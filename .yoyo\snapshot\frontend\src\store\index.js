import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import auth from './modules/auth'
import images from './modules/images'
import users from './modules/users'
import stats from './modules/stats'

export default createStore({
  state: {
    // 标注进度状态
    annotationProgress: {
      currentStep: 0, // 0: 新建标注, 1: 图像标注, 2: 病例信息填写
      imageId: null,   // 当前标注的图像ID
      formData: null,  // 表单数据
      lastUpdated: null // 最后更新时间
    }
  },
  getters: {
    isAuthenticated: state => state.auth.isAuthenticated,
    // 获取标注进度
    getAnnotationProgress: state => state.annotationProgress,
    // 检查是否有未完成的标注
    hasUnfinishedAnnotation: state => {
      return state.annotationProgress.imageId !== null && 
             state.annotationProgress.currentStep > 0;
    }
  },
  mutations: {
    // 保存标注进度
    saveAnnotationProgress(state, { step, imageId, formData }) {
      // 对formData进行处理，避免存储过大的对象
      let simplifiedFormData = null;
      if (formData) {
        // 过滤掉大型对象属性或代理对象，只保留简单数据
        simplifiedFormData = {};
        Object.keys(formData).forEach(key => {
          // 检查是否为代理对象或复杂对象
          const value = formData[key];
          if (typeof value !== 'object' || value === null) {
            simplifiedFormData[key] = value;
          } else if (Array.isArray(value)) {
            // 对于数组，保留其字符串值
            simplifiedFormData[key] = value.map(item => 
              typeof item === 'string' ? item : JSON.stringify(item)
            );
          } else {
            // 对于对象，转换为字符串
            try {
              simplifiedFormData[key] = JSON.stringify(value);
            } catch(e) {
              console.warn('无法序列化表单数据字段:', key);
            }
          }
        });
      }
      
      state.annotationProgress = {
        currentStep: step,
        imageId: imageId,
        formData: simplifiedFormData,
        lastUpdated: new Date().toISOString()
      };
      console.log('保存标注进度:', state.annotationProgress);
    },
    // 清除标注进度
    clearAnnotationProgress(state) {
      state.annotationProgress = {
        currentStep: 0,
        imageId: null,
        formData: null,
        lastUpdated: null
      };
      console.log('清除标注进度');
    }
  },
  actions: {
    // 保存当前标注进度
    saveProgress({ commit }, { step, imageId, formData }) {
      commit('saveAnnotationProgress', { step, imageId, formData });
    },
    // 完成标注并清除进度
    completeAnnotation({ commit }) {
      commit('clearAnnotationProgress');
    }
  },
  modules: {
    auth,
    images,
    users,
    stats
  },
  plugins: [
    createPersistedState({
      paths: ['auth', 'annotationProgress.currentStep', 'annotationProgress.imageId', 'annotationProgress.lastUpdated'],
      storage: {
        getItem: key => {
          try {
            return localStorage.getItem(key);
          } catch (err) {
            console.error('localStorage getItem error:', err);
            return null;
          }
        },
        setItem: (key, value) => {
          try {
            // 如果数据过大，先尝试清理一些旧数据
            if (value && value.length > 1000000) {
              console.warn('数据过大，尝试清理localStorage');
              localStorage.removeItem('vuex');
            }
            localStorage.setItem(key, value);
          } catch (err) {
            console.error('localStorage setItem error:', err);
            // 如果失败，尝试清理localStorage后再保存核心数据
            try {
              for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key !== 'user' && key !== 'token') {
                  localStorage.removeItem(key);
                }
              }
              localStorage.setItem(key, value);
            } catch (e) {
              console.error('无法保存数据到localStorage，即使在清理后:', e);
            }
          }
        },
        removeItem: key => {
          try {
            localStorage.removeItem(key);
          } catch (err) {
            console.error('localStorage removeItem error:', err);
          }
        }
      }
    })
  ]
}) 