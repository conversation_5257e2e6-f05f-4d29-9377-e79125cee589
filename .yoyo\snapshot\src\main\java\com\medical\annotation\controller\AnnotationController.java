package com.medical.annotation.controller;

import com.medical.annotation.model.User;
import com.medical.annotation.model.Tag;
import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.service.ImageMetadataService;
import com.medical.annotation.service.UserService;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.util.TypeConverter;
import com.medical.annotation.util.FileNameGenerator;
import com.medical.annotation.util.BasePathConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/medical/api/annotations")
public class AnnotationController {

    @Autowired
    private ImageMetadataService imageMetadataService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private TagRepository tagRepository;
    
    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    @Autowired
    private ImagePairRepository imagePairRepository;
    
    @Autowired
    private com.medical.annotation.service.FileService fileService;
    
    @Autowired
    private BasePathConfig basePathConfig;
    
    private String processedDir;
    
    @PostConstruct
    public void init() {
        processedDir = basePathConfig.getProcessedDir();
    }
    
    /**
     * 保存标注图像
     */
    @PostMapping("/{imageId}")
    public ResponseEntity<?> saveAnnotation(
            @PathVariable("imageId") Long imageId,
            @RequestParam("file") MultipartFile file) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            // 调用已有的保存标注图像方法
            String savedPath = imageMetadataService.saveAnnotatedImage(imageId, file, currentUser.getId());
            
            Map<String, String> response = new HashMap<>();
            response.put("path", savedPath);
            response.put("message", "标注图像保存成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 根据图片ID和已保存的标注信息自动生成标注图片
     */
    @PostMapping("/generate/{imageId}")
    @Transactional
    public ResponseEntity<?> generateAndSaveAnnotatedImage(@PathVariable("imageId") Long imageId) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            // 获取图像元数据
            Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findById(imageId);
            if (!metadataOpt.isPresent()) {
                throw new Exception("图像元数据不存在");
            }
            
            ImageMetadata metadata = metadataOpt.get();
            
            // 获取该图像的所有标注信息
            List<Tag> tags = tagRepository.findByMetadataId(imageId);
            if (tags.isEmpty()) {
                throw new Exception("没有找到标注信息");
            }
            
            // 从ImagePair中获取原始图像路径
            List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageId);
            if (imagePairs.isEmpty()) {
                throw new Exception("未找到关联的图像对信息");
            }
            
            String originalImagePath = imagePairs.get(0).getImageOnePath();
            if (originalImagePath == null || originalImagePath.isEmpty()) {
                throw new Exception("原始图像路径不存在");
            }
            
            File originalFile = new File(originalImagePath);
            if (!originalFile.exists()) {
                throw new Exception("原始图像文件不存在: " + originalImagePath);
            }
            
            BufferedImage originalImage = ImageIO.read(originalFile);
            
            // 在原始图像上绘制标注
            BufferedImage annotatedImage = drawAnnotations(originalImage, tags);
            
            // 生成唯一标注图像文件名（只生成一次）
            String originalFilename = metadata.getFilename();
            String filenameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf('.'));
            String uniqueAnnotatedFileName = FileNameGenerator.generateUniqueFileName("annotated_" + filenameWithoutExt + fileExtension);
            // 构造Web访问URL
            String annotatedImageWebPath = "/medical/images/processed/" + uniqueAnnotatedFileName;
            // 实际保存图片
            String processedDir = fileService.getProcessedDirectoryPath();
            File processedDirFile = new File(processedDir);
            if (!processedDirFile.exists()) {
                processedDirFile.mkdirs();
            }
            String annotatedFilePath = processedDir + File.separator + uniqueAnnotatedFileName;
            File annotatedFile = new File(annotatedFilePath);
            
            // 保存标注后的图像
            try {
                System.out.println("[AnnotationController] 保存标注图片到: " + annotatedFilePath);
                ImageIO.write(annotatedImage, "png", annotatedFile);
                System.out.println("[AnnotationController] 保存后文件是否存在: " + annotatedFile.exists() + ", 大小: " + (annotatedFile.exists() ? annotatedFile.length() : 0));
            } catch (Exception e) {
                System.err.println("[AnnotationController] 保存标注图片失败: " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
            
            // 用annotatedImageWebPath写入image_two_path
            ImagePair imagePair = imagePairs.get(0);
            imagePair.setImageTwoPath(annotatedImageWebPath);
            imagePairRepository.save(imagePair);
            
            // 返回前端的路径和数据库一致
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "标注图像已保存");
            response.put("annotated_image_path", annotatedImageWebPath);
            response.put("image_pair_id", imagePair.getId());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 在原始图像上绘制标注
     */
    private BufferedImage drawAnnotations(BufferedImage original, List<Tag> tags) {
        BufferedImage image = new BufferedImage(
            original.getWidth(), 
            original.getHeight(), 
            BufferedImage.TYPE_INT_ARGB
        );
        
        // 绘制原始图像
        Graphics2D g2d = image.createGraphics();
        g2d.drawImage(original, 0, 0, null);
        
        // 设置绘图属性
        g2d.setColor(Color.RED);
        g2d.setStroke(new BasicStroke(3));
        
        // 绘制每个标注
        for (Tag tag : tags) {
            int x = (int)(tag.getX() * original.getWidth());
            int y = (int)(tag.getY() * original.getHeight());
            int width = (int)(tag.getWidth() * original.getWidth());
            int height = (int)(tag.getHeight() * original.getHeight());
            
            // 绘制矩形框
            g2d.drawRect(x, y, width, height);
            
            // 绘制标签文本
            g2d.setColor(Color.WHITE);
            g2d.fillRect(x, y - 20, tag.getTag().length() * 10, 20);
            g2d.setColor(Color.BLACK);
            g2d.drawString(tag.getTag(), x + 5, y - 5);
            g2d.setColor(Color.RED);
        }
        
        g2d.dispose();
        return image;
    }
    
    /**
     * 更新已有标注图像
     */
    @PutMapping("/{imageId}/{imagePairId}")
    public ResponseEntity<?> updateAnnotation(
            @PathVariable("imageId") Long imageId,
            @PathVariable("imagePairId") Long imagePairId,
            @RequestParam("file") MultipartFile file) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            // 调用保存标注图像方法，实际上是覆盖原有图像
            String savedPath = imageMetadataService.saveAnnotatedImage(imageId, file, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("path", savedPath);
            response.put("imagePairId", imagePairId);
            response.put("message", "标注图像更新成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 删除标注图像
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteAnnotation(@PathVariable("id") Long id) {
        try {
            // 在实际系统中，需要先查询图像对信息，然后再删除图像文件
            // 这里简化处理，假设id是图像路径
            String path = processedDir + "/annotated_" + id + ".png";
            imageMetadataService.deleteAnnotatedImage(path);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "标注图像删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 根据ID获取标注详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getAnnotation(@PathVariable("id") Long id) {
        try {
            Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findById(id);
            if (!metadataOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            ImageMetadata metadata = metadataOpt.get();
            
            // 获取标注图片路径
            List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(id);
            String annotatedImagePath = null;
            if (!imagePairs.isEmpty() && imagePairs.get(0).getImageTwoPath() != null) {
                annotatedImagePath = imagePairs.get(0).getImageTwoPath();
            }
            
            // 获取标注数据和标签
            List<Tag> tags = tagRepository.findByMetadataId(id);
            
            // 获取上传者信息
            String uploaderName = "";
            if (metadata.getUploadedBy() != null) {
                uploaderName = metadata.getUploadedBy().getName();
            }
            
            // 构造返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("id", metadata.getId());
            result.put("caseNumber", metadata.getCaseNumber());
            result.put("uploadedByName", uploaderName);
            result.put("lesionLocation", metadata.getLesionLocation());
            result.put("diagnosisCategory", metadata.getDiagnosisCategory());
            result.put("status", metadata.getStatus());
            result.put("uploadTime", metadata.getCreatedAt());
            result.put("reviewDate", metadata.getReviewDate());
            result.put("originalImagePath", metadata.getPath());
            result.put("annotatedImagePath", annotatedImagePath);
            result.put("tags", tags);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @PostMapping("/annotations/{id}/raw")
    public ResponseEntity<?> rawAnnotation(@PathVariable String id, @RequestBody Map<String, Object> requestData) {
        try {
            // 例如：替换这里的硬编码路径
            String path = processedDir + "/annotated_" + id + ".png";
            
            // 这里需要实际的方法实现
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("处理失败: " + e.getMessage());
        }
    }
} 