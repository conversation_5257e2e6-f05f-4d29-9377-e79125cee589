# Spring Boot Docker环境配置文件
# 用于Docker容器化部署

# 服务器配置
server.port=8085
server.servlet.context-path=/

# 数据库配置
spring.datasource.url=********************************************************************************************************************************
spring.datasource.username=hemangioma_user
spring.datasource.password=hemangioma_pass
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.max-lifetime=1200000

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB
spring.servlet.multipart.file-size-threshold=2KB

# 静态资源配置
spring.web.resources.static-locations=classpath:/static/,file:/app/medical_images/
spring.mvc.static-path-pattern=/static/**

# 日志配置
logging.level.root=INFO
logging.level.com.medical=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# 日志文件配置
logging.file.name=/app/logs/application.log
logging.file.max-size=100MB
logging.file.max-history=30
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# Actuator配置
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.health.db.enabled=true

# 跨域配置
spring.web.cors.allowed-origins=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=false

# AI服务配置
ai.service.url=http://ai-service:8086
ai.service.timeout=300000
ai.service.retry.max-attempts=3
ai.service.retry.delay=1000

# 文件存储配置
file.upload.path=/app/medical_images/
file.upload.original-path=/app/medical_images/original/
file.upload.processed-path=/app/medical_images/processed/
file.upload.annotated-path=/app/medical_images/annotated/

# 缓存配置
spring.cache.type=simple
spring.cache.cache-names=hemangioma-types,system-config

# 安全配置
spring.security.user.name=admin
spring.security.user.password=admin123
spring.security.user.roles=ADMIN

# JSON配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-dates-as-timestamps=false

# 国际化配置
spring.messages.basename=messages
spring.messages.encoding=UTF-8
spring.messages.cache-duration=3600

# 线程池配置
spring.task.execution.pool.core-size=8
spring.task.execution.pool.max-size=16
spring.task.execution.pool.queue-capacity=100
spring.task.execution.thread-name-prefix=hemangioma-task-

# 数据库连接健康检查
spring.datasource.hikari.connection-test-query=SELECT 1
