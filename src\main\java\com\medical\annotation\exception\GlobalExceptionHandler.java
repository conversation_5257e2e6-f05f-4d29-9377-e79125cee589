package com.medical.annotation.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<Map<String, String>> handleUserNotFoundException(UserNotFoundException e) {
        System.err.println("用户不存在异常: " + e.getMessage());
        
        Map<String, String> response = new HashMap<>();
        response.put("message", e.getMessage());
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }
    
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<Map<String, String>> handleAuthenticationException(AuthenticationException e) {
        System.err.println("认证异常: " + e.getMessage());
        
        Map<String, String> response = new HashMap<>();
        response.put("message", e.getMessage());
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }
    
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<Map<String, String>> handleBadCredentialsException(BadCredentialsException e) {
        System.err.println("身份验证失败: " + e.getMessage());
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "用户名或密码不正确");
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }
    
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<Map<String, String>> handleNoHandlerFoundException(NoHandlerFoundException e) {
        System.err.println("资源不存在: " + e.getMessage());
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "请求的资源不存在: " + e.getRequestURL());
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }
    
    /**
     * 处理内容类型冲突异常，尤其是当尝试在预设图片内容类型的响应中返回错误消息时
     */
    @ExceptionHandler(HttpMessageNotWritableException.class)
    public ResponseEntity<?> handleHttpMessageNotWritableException(HttpMessageNotWritableException e, HttpServletRequest request, HttpServletResponse response) {
        System.err.println("内容类型冲突: " + e.getMessage());
        e.printStackTrace();
        
        // 检查是否是图片请求
        String requestPath = request.getRequestURI();
        boolean isImageRequest = requestPath.contains("/images/") || requestPath.contains("/image/");
        
        if (isImageRequest) {
            System.out.println("检测到图片请求处理错误，重置Content-Type以返回文本错误信息");
            
            // 显式设置内容类型为文本，覆盖之前的图片内容类型
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("未找到请求的图片或处理图片时出错");
        }
        
        // 其他情况下的常规处理
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("message", "响应内容类型冲突，请检查请求的资源类型");
        
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .contentType(MediaType.APPLICATION_JSON)
                .body(errorResponse);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, String>> handleAllExceptions(Exception e, HttpServletRequest request) {
        // 记录异常
        System.err.println("全局异常处理器捕获到异常: " + e.getMessage());
        e.printStackTrace();
        
        // 检查是否是图片请求
        String requestPath = request.getRequestURI();
        boolean isImageRequest = requestPath.contains("/images/") || requestPath.contains("/image/");
        
        // 确保使用UTF-8编码处理错误消息
        String errorMessage = e.getMessage();
        if (errorMessage != null) {
            try {
                // 防止编码问题导致的乱码
                byte[] bytes = errorMessage.getBytes(StandardCharsets.UTF_8);
                errorMessage = new String(bytes, StandardCharsets.UTF_8);
                System.out.println("错误消息(UTF-8编码): " + errorMessage);
            } catch (Exception ex) {
                errorMessage = "处理错误信息时出错";
            }
        } else {
            errorMessage = "未知错误";
        }
        
        Map<String, String> response = new HashMap<>();
        response.put("message", errorMessage);
        
        if (isImageRequest) {
            // 对图片请求的特殊处理，确保设置正确的内容类型
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response);
        }
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
} 