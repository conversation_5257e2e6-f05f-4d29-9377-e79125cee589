package com.medical.annotation.model;

import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.util.ImagePathUtil;
import com.medical.annotation.util.SpringUtils;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@Entity
@Table(name = "image_metadata")
public class ImageMetadata {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "formatted_id", length = 9, unique = true)
    private String formattedId;

    @Column(name = "filename", nullable = false)
    private String filename;

    @Column(name = "original_name", nullable = false)
    private String originalName;

    @Column(name = "mimetype", nullable = false)
    private String mimetype;

    @Column(name = "size", nullable = false)
    private Integer size;

    @Column(name = "width")
    private Integer width;

    @Column(name = "height")
    private Integer height;

    @ManyToOne
    @JoinColumn(name = "uploaded_by")
    private User uploadedBy;

    @ManyToOne
    @JoinColumn(name = "team_id")
    private Team team;

    @Column(name = "patient_name")
    private String patientName;

    @Column(name = "patient_age")
    private Integer patientAge;

    @Column(name = "patient_gender")
    private String patientGender;

    @Column(name = "case_number", unique = true)
    private String caseNumber;

    @Column(name = "lesion_location")
    private String lesionLocation;

    @Column(name = "lesion_size")
    private String lesionSize;

    @Column(name = "lesion_color")
    private String lesionColor;

    @Column(name = "border_clarity")
    private String borderClarity;

    @Column(name = "blood_flow")
    private String bloodFlow;

    @Column(name = "diagnosis_category")
    private String diagnosisCategory;

    @Column(name = "disease_stage")
    private String diseaseStage;

    @Column(name = "morphological_features", columnDefinition = "TEXT")
    private String morphologicalFeatures;

    @Column(columnDefinition = "TEXT")
    private String symptoms;

    @Column(name = "symptom_details", columnDefinition = "TEXT")
    private String symptomDetails;

    @Column(columnDefinition = "TEXT")
    private String complications;

    @Column(name = "complication_details", columnDefinition = "TEXT")
    private String complicationDetails;

    @Column(name = "diagnosis_icd_code")
    private String diagnosisIcdCode;

    @Column(name = "treatment_priority")
    private String treatmentPriority;

    @Column(name = "recommended_treatment", columnDefinition = "TEXT")
    private String recommendedTreatment;

    @Column(name = "treatment_plan", columnDefinition = "TEXT")
    private String treatmentPlan;

    @Column(columnDefinition = "TEXT")
    private String contraindications;

    @Column(name = "follow_up_schedule")
    private String followUpSchedule;

    @Column(name = "prognosis_rating")
    private Integer prognosisRating;

    @Column(name = "patient_education", columnDefinition = "TEXT")
    private String patientEducation;

    @Column(name = "acquisition_date")
    private LocalDate acquisitionDate;

    @Column(name = "study_description", columnDefinition = "TEXT")
    private String studyDescription;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Status status = Status.DRAFT;

    @ManyToOne
    @JoinColumn(name = "reviewed_by")
    private User reviewedBy;

    @Column(name = "review_notes", columnDefinition = "TEXT")
    private String reviewNotes;

    @Column(name = "review_date")
    private LocalDateTime reviewDate;

    @Column(name = "submitted_at")
    private LocalDateTime submittedAt;

    @ManyToOne
    @JoinColumn(name = "submitted_by")
    private User submittedBy;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        // 使用中国时区创建时间戳
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        createdAt = chinaTime.toLocalDateTime();
        updatedAt = chinaTime.toLocalDateTime();
    }

    @PreUpdate
    protected void onUpdate() {
        // 使用中国时区更新时间戳
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        updatedAt = chinaTime.toLocalDateTime();
    }

    public enum Status {
        DRAFT,
        SUBMITTED,
        REVIEWED,
        REJECTED,
        APPROVED
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFormattedId() {
        return formattedId;
    }

    public void setFormattedId(String formattedId) {
        this.formattedId = formattedId;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getMimetype() {
        return mimetype;
    }

    public void setMimetype(String mimetype) {
        this.mimetype = mimetype;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public User getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(User uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public Team getTeam() {
        return team;
    }

    public void setTeam(Team team) {
        this.team = team;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public Integer getPatientAge() {
        return patientAge;
    }

    public void setPatientAge(Integer patientAge) {
        this.patientAge = patientAge;
    }

    public String getPatientGender() {
        return patientGender;
    }

    public void setPatientGender(String patientGender) {
        this.patientGender = patientGender;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public String getLesionLocation() {
        return lesionLocation;
    }

    public void setLesionLocation(String lesionLocation) {
        this.lesionLocation = lesionLocation;
    }

    public String getLesionSize() {
        return lesionSize;
    }

    public void setLesionSize(String lesionSize) {
        this.lesionSize = lesionSize;
    }

    public String getLesionColor() {
        return lesionColor;
    }

    public void setLesionColor(String lesionColor) {
        this.lesionColor = lesionColor;
    }

    public String getBorderClarity() {
        return borderClarity;
    }

    public void setBorderClarity(String borderClarity) {
        this.borderClarity = borderClarity;
    }

    public String getBloodFlow() {
        return bloodFlow;
    }

    public void setBloodFlow(String bloodFlow) {
        this.bloodFlow = bloodFlow;
    }

    public String getDiagnosisCategory() {
        return diagnosisCategory;
    }

    public void setDiagnosisCategory(String diagnosisCategory) {
        this.diagnosisCategory = diagnosisCategory;
    }

    public String getDiseaseStage() {
        return diseaseStage;
    }

    public void setDiseaseStage(String diseaseStage) {
        this.diseaseStage = diseaseStage;
    }

    public String getMorphologicalFeatures() {
        return morphologicalFeatures;
    }

    public void setMorphologicalFeatures(String morphologicalFeatures) {
        this.morphologicalFeatures = morphologicalFeatures;
    }

    public String getSymptoms() {
        return symptoms;
    }

    public void setSymptoms(String symptoms) {
        this.symptoms = symptoms;
    }

    public String getSymptomDetails() {
        return symptomDetails;
    }

    public void setSymptomDetails(String symptomDetails) {
        this.symptomDetails = symptomDetails;
    }

    public String getComplications() {
        return complications;
    }

    public void setComplications(String complications) {
        this.complications = complications;
    }

    public String getComplicationDetails() {
        return complicationDetails;
    }

    public void setComplicationDetails(String complicationDetails) {
        this.complicationDetails = complicationDetails;
    }

    public String getDiagnosisIcdCode() {
        return diagnosisIcdCode;
    }

    public void setDiagnosisIcdCode(String diagnosisIcdCode) {
        this.diagnosisIcdCode = diagnosisIcdCode;
    }

    public String getTreatmentPriority() {
        return treatmentPriority;
    }

    public void setTreatmentPriority(String treatmentPriority) {
        this.treatmentPriority = treatmentPriority;
    }

    public String getRecommendedTreatment() {
        return recommendedTreatment;
    }

    public void setRecommendedTreatment(String recommendedTreatment) {
        this.recommendedTreatment = recommendedTreatment;
    }

    public String getTreatmentPlan() {
        return treatmentPlan;
    }

    public void setTreatmentPlan(String treatmentPlan) {
        this.treatmentPlan = treatmentPlan;
    }

    public String getContraindications() {
        return contraindications;
    }

    public void setContraindications(String contraindications) {
        this.contraindications = contraindications;
    }

    public String getFollowUpSchedule() {
        return followUpSchedule;
    }

    public void setFollowUpSchedule(String followUpSchedule) {
        this.followUpSchedule = followUpSchedule;
    }

    public Integer getPrognosisRating() {
        return prognosisRating;
    }

    public void setPrognosisRating(Integer prognosisRating) {
        this.prognosisRating = prognosisRating;
    }

    public String getPatientEducation() {
        return patientEducation;
    }

    public void setPatientEducation(String patientEducation) {
        this.patientEducation = patientEducation;
    }

    public LocalDate getAcquisitionDate() {
        return acquisitionDate;
    }

    public void setAcquisitionDate(LocalDate acquisitionDate) {
        this.acquisitionDate = acquisitionDate;
    }

    public String getStudyDescription() {
        return studyDescription;
    }

    public void setStudyDescription(String studyDescription) {
        this.studyDescription = studyDescription;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public User getReviewedBy() {
        return reviewedBy;
    }

    public void setReviewedBy(User reviewedBy) {
        this.reviewedBy = reviewedBy;
    }

    public String getReviewNotes() {
        return reviewNotes;
    }

    public void setReviewNotes(String reviewNotes) {
        this.reviewNotes = reviewNotes;
    }

    public LocalDateTime getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(LocalDateTime reviewDate) {
        this.reviewDate = reviewDate;
    }

    public LocalDateTime getSubmittedAt() {
        return submittedAt;
    }

    public void setSubmittedAt(LocalDateTime submittedAt) {
        this.submittedAt = submittedAt;
    }

    public User getSubmittedBy() {
        return submittedBy;
    }

    public void setSubmittedBy(User submittedBy) {
        this.submittedBy = submittedBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 兼容方法，用于替代已删除的path字段
    public String getPath() {
        return ImagePathUtil.getPath(this);
    }

    public void setPath(String path) {
        ImagePathUtil.setPath(this, path);
    }

    // 兼容方法，用于替代已删除的image_two_path字段
    public String getImageTwoPath() {
        return ImagePathUtil.getImageTwoPath(this);
    }

    public void setImageTwoPath(String imageTwoPath) {
        ImagePathUtil.setImageTwoPath(this, imageTwoPath);
    }

    // 兼容方法，用于替代已删除的modality字段
    private String modality;

    public String getModality() {
        return modality;
    }

    public void setModality(String modality) {
        this.modality = modality;
    }

    // 兼容方法，用于替代已删除的uploaded_by_custom_id和reviewed_by_custom_id字段
    private String uploadedByCustomId;
    private String reviewedByCustomId;

    public String getUploadedByCustomId() {
        return uploadedByCustomId;
    }

    public void setUploadedByCustomId(String uploadedByCustomId) {
        this.uploadedByCustomId = uploadedByCustomId;
    }

    public String getReviewedByCustomId() {
        return reviewedByCustomId;
    }

    public void setReviewedByCustomId(String reviewedByCustomId) {
        this.reviewedByCustomId = reviewedByCustomId;
    }
    
    // 临时字段，用于前端显示
    @Transient
    private String uploadedByName;
    
    @Transient
    private String reviewedByName;
    
    public String getUploadedByName() {
        if (uploadedByName == null && uploadedBy != null) {
            return uploadedBy.getName();
        }
        return uploadedByName;
    }
    
    public void setUploadedByName(String uploadedByName) {
        this.uploadedByName = uploadedByName;
    }
    
    public String getReviewedByName() {
        if (reviewedByName == null && reviewedBy != null) {
            return reviewedBy.getName();
        }
        return reviewedByName;
    }
    
    public void setReviewedByName(String reviewedByName) {
        this.reviewedByName = reviewedByName;
    }
} 