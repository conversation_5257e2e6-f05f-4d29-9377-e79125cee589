"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[381],{3381:(e,a,t)=>{t.r(a),t.d(a,{default:()=>le});t(28706),t(44114),t(62010);var s=t(61431),n={class:"container"},i={class:"row"},r={class:"col-md-3"},l={class:"list-group mb-4"},c=["onClick"],o={class:"col-md-9"},u={key:0,class:"card"},g={class:"card-header d-flex justify-content-between align-items-center"},d={class:"input-group",style:{"max-width":"300px"}},p={class:"card-body"},m={key:0,class:"text-center my-5"},k={key:1,class:"alert alert-danger"},v={key:2},b={class:"mb-3"},h={class:"table table-striped table-hover"},f={class:"btn-group btn-group-sm"},L=["title","onClick"],C=["onClick"],y={key:0,"aria-label":"用户分页"},E={class:"pagination justify-content-center"},w=["onClick"],A={key:1,class:"card"},P={class:"card-header d-flex justify-content-between align-items-center"},U={class:"input-group",style:{"max-width":"300px"}},I={class:"card-body"},T={class:"mb-3"},S={key:0,class:"text-center my-5"},R={key:1,class:"alert alert-danger"},D={key:2},X={class:"table-responsive"},_={class:"table table-striped table-hover"},x=["src"],$={class:"btn-group btn-group-sm"},N=["onClick"],V=["onClick"],Q={key:0,"aria-label":"图像分页"},F={class:"pagination justify-content-center"},W=["onClick"],z={key:2,class:"card"},O={class:"card-body"},q={class:"mb-3"},J={class:"mb-3"},M={class:"form-check form-switch mb-3"},K={class:"form-check form-switch mb-3"},j=["disabled"],B={key:0},H={key:1},G={key:3,class:"card"},Y={class:"card-body"};function Z(e,a,t,Z,ee,ae){return(0,s.uX)(),(0,s.CE)("div",n,[a[39]||(a[39]=(0,s.Lk)("h2",{class:"mt-4 mb-4"},"管理员控制台",-1)),(0,s.Lk)("div",i,[(0,s.Lk)("div",r,[(0,s.Lk)("div",l,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ee.tabs,(function(e,a){return(0,s.uX)(),(0,s.CE)("button",{key:a,class:(0,s.C4)(["list-group-item list-group-item-action",ee.activeTab===e.id?"active":""]),onClick:function(a){return ee.activeTab=e.id}},[(0,s.Lk)("i",{class:(0,s.C4)(e.icon)},null,2),(0,s.eW)(" "+(0,s.v_)(e.name),1)],10,c)})),128))])]),(0,s.Lk)("div",o,["users"===ee.activeTab?((0,s.uX)(),(0,s.CE)("div",u,[(0,s.Lk)("div",g,[a[18]||(a[18]=(0,s.Lk)("h5",{class:"mb-0"},"用户管理",-1)),(0,s.Lk)("div",d,[(0,s.bo)((0,s.Lk)("input",{"onUpdate:modelValue":a[0]||(a[0]=function(e){return ee.userSearchQuery=e}),type:"text",class:"form-control",placeholder:"搜索用户..."},null,512),[[s.Jo,ee.userSearchQuery]]),(0,s.Lk)("button",{class:"btn btn-outline-secondary",type:"button",onClick:a[1]||(a[1]=function(){return ae.searchUsers&&ae.searchUsers.apply(ae,arguments)})},a[17]||(a[17]=[(0,s.Lk)("i",{class:"bi bi-search"},null,-1)]))])]),(0,s.Lk)("div",p,[ee.loadingUsers?((0,s.uX)(),(0,s.CE)("div",m,a[19]||(a[19]=[(0,s.Lk)("div",{class:"spinner-border",role:"status"},[(0,s.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):ee.userError?((0,s.uX)(),(0,s.CE)("div",k,(0,s.v_)(ee.userError),1)):((0,s.uX)(),(0,s.CE)("div",v,[(0,s.Lk)("div",b,[(0,s.Lk)("button",{class:"btn btn-warning",onClick:a[2]||(a[2]=function(){return ae.goToReviewerApplications&&ae.goToReviewerApplications.apply(ae,arguments)})},a[20]||(a[20]=[(0,s.Lk)("i",{class:"bi bi-person-check"},null,-1),(0,s.eW)(" 查看权限升级申请 ")]))]),(0,s.Lk)("table",h,[a[22]||(a[22]=(0,s.Lk)("thead",null,[(0,s.Lk)("tr",null,[(0,s.Lk)("th",{scope:"col"},"ID"),(0,s.Lk)("th",{scope:"col"},"用户名"),(0,s.Lk)("th",{scope:"col"},"邮箱"),(0,s.Lk)("th",{scope:"col"},"角色"),(0,s.Lk)("th",{scope:"col"},"状态"),(0,s.Lk)("th",{scope:"col"},"注册时间"),(0,s.Lk)("th",{scope:"col"},"操作")])],-1)),(0,s.Lk)("tbody",null,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ee.users,(function(e){return(0,s.uX)(),(0,s.CE)("tr",{key:e.id},[(0,s.Lk)("td",null,(0,s.v_)(e.id),1),(0,s.Lk)("td",null,(0,s.v_)(e.username),1),(0,s.Lk)("td",null,(0,s.v_)(e.email),1),(0,s.Lk)("td",null,[(0,s.Lk)("span",{class:(0,s.C4)(["badge","ADMIN"===e.role?"bg-danger":"REVIEWER"===e.role?"bg-primary":"bg-secondary"])},(0,s.v_)(e.role),3)]),(0,s.Lk)("td",null,[(0,s.Lk)("span",{class:(0,s.C4)(["badge",e.active?"bg-success":"bg-danger"])},(0,s.v_)(e.active?"已激活":"已禁用"),3)]),(0,s.Lk)("td",null,(0,s.v_)(ae.formatDate(e.createdAt)),1),(0,s.Lk)("td",null,[(0,s.Lk)("div",f,[(0,s.Lk)("button",{class:"btn btn-outline-primary",title:e.active?"禁用用户":"激活用户",onClick:function(a){return ae.toggleUserStatus(e)}},[(0,s.Lk)("i",{class:(0,s.C4)(e.active?"bi bi-x-circle":"bi bi-check-circle")},null,2)],8,L),(0,s.Lk)("button",{class:"btn btn-outline-secondary",title:"修改角色",onClick:function(a){return ae.editUserRole(e)}},a[21]||(a[21]=[(0,s.Lk)("i",{class:"bi bi-pencil"},null,-1)]),8,C)])])])})),128))])]),ee.userTotalPages>1?((0,s.uX)(),(0,s.CE)("nav",y,[(0,s.Lk)("ul",E,[(0,s.Lk)("li",{class:(0,s.C4)(["page-item",0===ee.userCurrentPage?"disabled":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:a[3]||(a[3]=(0,s.D$)((function(e){return ae.changePage(ee.userCurrentPage-1,"users")}),["prevent"]))},"上一页")],2),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ee.userTotalPages,(function(e){return(0,s.uX)(),(0,s.CE)("li",{key:e,class:(0,s.C4)(["page-item",ee.userCurrentPage===e-1?"active":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:(0,s.D$)((function(a){return ae.changePage(e-1,"users")}),["prevent"])},(0,s.v_)(e),9,w)],2)})),128)),(0,s.Lk)("li",{class:(0,s.C4)(["page-item",ee.userCurrentPage===ee.userTotalPages-1?"disabled":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:a[4]||(a[4]=(0,s.D$)((function(e){return ae.changePage(ee.userCurrentPage+1,"users")}),["prevent"]))},"下一页")],2)])])):(0,s.Q3)("",!0)]))])])):(0,s.Q3)("",!0),"images"===ee.activeTab?((0,s.uX)(),(0,s.CE)("div",A,[(0,s.Lk)("div",P,[a[24]||(a[24]=(0,s.Lk)("h5",{class:"mb-0"},"图像管理",-1)),(0,s.Lk)("div",U,[(0,s.bo)((0,s.Lk)("input",{"onUpdate:modelValue":a[5]||(a[5]=function(e){return ee.imageSearchQuery=e}),type:"text",class:"form-control",placeholder:"搜索图像..."},null,512),[[s.Jo,ee.imageSearchQuery]]),(0,s.Lk)("button",{class:"btn btn-outline-secondary",type:"button",onClick:a[6]||(a[6]=function(){return ae.searchImages&&ae.searchImages.apply(ae,arguments)})},a[23]||(a[23]=[(0,s.Lk)("i",{class:"bi bi-search"},null,-1)]))])]),(0,s.Lk)("div",I,[(0,s.Lk)("div",T,[(0,s.bo)((0,s.Lk)("select",{"onUpdate:modelValue":a[7]||(a[7]=function(e){return ee.imageStatusFilter=e}),class:"form-select w-auto",onChange:a[8]||(a[8]=function(e){return ae.fetchImages()})},a[25]||(a[25]=[(0,s.Fv)('<option value="" data-v-537c82f4>所有状态</option><option value="UPLOADED" data-v-537c82f4>已上传</option><option value="ANNOTATED" data-v-537c82f4>已标注</option><option value="REVIEWED" data-v-537c82f4>已审核</option><option value="APPROVED" data-v-537c82f4>已批准</option><option value="REJECTED" data-v-537c82f4>已拒绝</option>',6)]),544),[[s.u1,ee.imageStatusFilter]])]),ee.loadingImages?((0,s.uX)(),(0,s.CE)("div",S,a[26]||(a[26]=[(0,s.Lk)("div",{class:"spinner-border",role:"status"},[(0,s.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):ee.imageError?((0,s.uX)(),(0,s.CE)("div",R,(0,s.v_)(ee.imageError),1)):((0,s.uX)(),(0,s.CE)("div",D,[(0,s.Lk)("div",X,[(0,s.Lk)("table",_,[a[29]||(a[29]=(0,s.Lk)("thead",null,[(0,s.Lk)("tr",null,[(0,s.Lk)("th",{scope:"col"},"ID"),(0,s.Lk)("th",{scope:"col"},"缩略图"),(0,s.Lk)("th",{scope:"col"},"名称"),(0,s.Lk)("th",{scope:"col"},"上传者"),(0,s.Lk)("th",{scope:"col"},"状态"),(0,s.Lk)("th",{scope:"col"},"上传时间"),(0,s.Lk)("th",{scope:"col"},"操作")])],-1)),(0,s.Lk)("tbody",null,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ee.images,(function(t){return(0,s.uX)(),(0,s.CE)("tr",{key:t.id},[(0,s.Lk)("td",null,(0,s.v_)(t.id),1),(0,s.Lk)("td",null,[(0,s.Lk)("img",{src:"".concat(ee.apiUrl,"/api/images/").concat(t.id,"/thumbnail"),class:"admin-thumbnail",alt:"缩略图"},null,8,x)]),(0,s.Lk)("td",null,(0,s.v_)(t.name),1),(0,s.Lk)("td",null,(0,s.v_)(t.uploaderName),1),(0,s.Lk)("td",null,[(0,s.Lk)("span",{class:(0,s.C4)(["badge","APPROVED"===t.status?"bg-success":"REJECTED"===t.status?"bg-danger":"ANNOTATED"===t.status?"bg-primary":"REVIEWED"===t.status?"bg-info":"bg-secondary"])},(0,s.v_)(ae.getStatusText(t.status)),3)]),(0,s.Lk)("td",null,(0,s.v_)(ae.formatDate(t.uploadTime)),1),(0,s.Lk)("td",null,[(0,s.Lk)("div",$,[(0,s.Lk)("button",{class:"btn btn-outline-info",title:"查看详情",onClick:function(a){return e.$router.push("/images/".concat(t.id))}},a[27]||(a[27]=[(0,s.Lk)("i",{class:"bi bi-eye"},null,-1)]),8,N),(0,s.Lk)("button",{class:"btn btn-outline-danger",title:"删除图像",onClick:function(e){return ae.deleteImage(t.id)}},a[28]||(a[28]=[(0,s.Lk)("i",{class:"bi bi-trash"},null,-1)]),8,V)])])])})),128))])])]),ee.imageTotalPages>1?((0,s.uX)(),(0,s.CE)("nav",Q,[(0,s.Lk)("ul",F,[(0,s.Lk)("li",{class:(0,s.C4)(["page-item",0===ee.imageCurrentPage?"disabled":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:a[9]||(a[9]=(0,s.D$)((function(e){return ae.changePage(ee.imageCurrentPage-1,"images")}),["prevent"]))},"上一页")],2),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ee.imageTotalPages,(function(e){return(0,s.uX)(),(0,s.CE)("li",{key:e,class:(0,s.C4)(["page-item",ee.imageCurrentPage===e-1?"active":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:(0,s.D$)((function(a){return ae.changePage(e-1,"images")}),["prevent"])},(0,s.v_)(e),9,W)],2)})),128)),(0,s.Lk)("li",{class:(0,s.C4)(["page-item",ee.imageCurrentPage===ee.imageTotalPages-1?"disabled":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:a[10]||(a[10]=(0,s.D$)((function(e){return ae.changePage(ee.imageCurrentPage+1,"images")}),["prevent"]))},"下一页")],2)])])):(0,s.Q3)("",!0)]))])])):(0,s.Q3)("",!0),"settings"===ee.activeTab?((0,s.uX)(),(0,s.CE)("div",z,[a[35]||(a[35]=(0,s.Lk)("div",{class:"card-header"},[(0,s.Lk)("h5",{class:"mb-0"},"系统设置")],-1)),(0,s.Lk)("div",O,[(0,s.Lk)("form",{onSubmit:a[15]||(a[15]=(0,s.D$)((function(){return ae.saveSettings&&ae.saveSettings.apply(ae,arguments)}),["prevent"]))},[(0,s.Lk)("div",q,[a[30]||(a[30]=(0,s.Lk)("label",{for:"siteName",class:"form-label"},"系统名称",-1)),(0,s.bo)((0,s.Lk)("input",{id:"siteName","onUpdate:modelValue":a[11]||(a[11]=function(e){return ee.settings.siteName=e}),type:"text",class:"form-control"},null,512),[[s.Jo,ee.settings.siteName]])]),(0,s.Lk)("div",J,[a[31]||(a[31]=(0,s.Lk)("label",{for:"maxImageSize",class:"form-label"},"最大图像大小(MB)",-1)),(0,s.bo)((0,s.Lk)("input",{id:"maxImageSize","onUpdate:modelValue":a[12]||(a[12]=function(e){return ee.settings.maxImageSizeMB=e}),type:"number",class:"form-control"},null,512),[[s.Jo,ee.settings.maxImageSizeMB]])]),(0,s.Lk)("div",M,[(0,s.bo)((0,s.Lk)("input",{id:"allowRegistration","onUpdate:modelValue":a[13]||(a[13]=function(e){return ee.settings.allowPublicRegistration=e}),class:"form-check-input",type:"checkbox"},null,512),[[s.lH,ee.settings.allowPublicRegistration]]),a[32]||(a[32]=(0,s.Lk)("label",{class:"form-check-label",for:"allowRegistration"},"允许公开注册",-1))]),(0,s.Lk)("div",K,[(0,s.bo)((0,s.Lk)("input",{id:"reviewRequired","onUpdate:modelValue":a[14]||(a[14]=function(e){return ee.settings.requireReview=e}),class:"form-check-input",type:"checkbox"},null,512),[[s.lH,ee.settings.requireReview]]),a[33]||(a[33]=(0,s.Lk)("label",{class:"form-check-label",for:"reviewRequired"},"标注需要审核",-1))]),(0,s.Lk)("button",{type:"submit",class:"btn btn-primary",disabled:ee.savingSettings},[ee.savingSettings?((0,s.uX)(),(0,s.CE)("span",B,a[34]||(a[34]=[(0,s.Lk)("span",{class:"spinner-border spinner-border-sm",role:"status","aria-hidden":"true"},null,-1),(0,s.eW)(" 保存中... ")]))):((0,s.uX)(),(0,s.CE)("span",H,"保存设置"))],8,j)],32)])])):(0,s.Q3)("",!0),"reviewerApplications"===ee.activeTab?((0,s.uX)(),(0,s.CE)("div",G,[a[38]||(a[38]=(0,s.Lk)("div",{class:"card-header"},[(0,s.Lk)("h5",{class:"mb-0"},"审核医生申请")],-1)),(0,s.Lk)("div",Y,[a[37]||(a[37]=(0,s.Lk)("p",null,"您可以在这里管理标注医生提交的审核医生申请。",-1)),(0,s.Lk)("button",{class:"btn btn-primary",onClick:a[16]||(a[16]=function(){return ae.goToReviewerApplications&&ae.goToReviewerApplications.apply(ae,arguments)})},a[36]||(a[36]=[(0,s.Lk)("i",{class:"bi bi-list-check"},null,-1),(0,s.eW)(" 查看申请列表 ")]))])])):(0,s.Q3)("",!0)])])])}var ee=t(24059),ae=t(698),te=(t(74423),t(23288),t(72505)),se=t.n(te);const ne={name:"Admin",data:function(){return{apiUrl:"",activeTab:"users",tabs:[{id:"users",name:"用户管理",icon:"bi bi-people"},{id:"images",name:"图像管理",icon:"bi bi-images"},{id:"settings",name:"系统设置",icon:"bi bi-gear"},{id:"reviewerApplications",name:"审核医生申请",icon:"bi bi-person-check"}],users:[],loadingUsers:!1,userError:null,userCurrentPage:0,userTotalPages:0,userSearchQuery:"",images:[],loadingImages:!1,imageError:null,imageCurrentPage:0,imageTotalPages:0,imageSearchQuery:"",imageStatusFilter:"",settings:{siteName:"血管瘤辅助标注系统",maxImageSizeMB:10,allowPublicRegistration:!0,requireReview:!0},savingSettings:!1}},watch:{activeTab:function(e){"users"===e?this.fetchUsers():"images"===e?this.fetchImages():"settings"===e?this.fetchSettings():"reviewerApplications"===e&&console.log("加载审核医生申请数据")}},mounted:function(){this.fetchUsers(),this.fetchImages(),this.fetchSettings()},methods:{fetchUsers:function(){var e=arguments,a=this;return(0,ae.A)((0,ee.A)().m((function t(){var s,n,i,r,l;return(0,ee.A)().w((function(t){while(1)switch(t.n){case 0:return s=e.length>0&&void 0!==e[0]?e[0]:0,a.loadingUsers=!0,a.userError=null,t.p=1,n={page:s,size:10,query:a.userSearchQuery},t.n=2,se().get("".concat(a.apiUrl,"/api/admin/users"),{params:n});case 2:i=t.v,a.users=i.data.content,a.userCurrentPage=i.data.number,a.userTotalPages=i.data.totalPages,t.n=4;break;case 3:t.p=3,l=t.v,a.userError="加载用户列表失败: "+((null===(r=l.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||l.message);case 4:return t.p=4,a.loadingUsers=!1,t.f(4);case 5:return t.a(2)}}),t,null,[[1,3,4,5]])})))()},fetchImages:function(){var e=arguments,a=this;return(0,ae.A)((0,ee.A)().m((function t(){var s,n,i,r,l;return(0,ee.A)().w((function(t){while(1)switch(t.n){case 0:return s=e.length>0&&void 0!==e[0]?e[0]:0,a.loadingImages=!0,a.imageError=null,t.p=1,n={page:s,size:10,query:a.imageSearchQuery,status:a.imageStatusFilter},t.n=2,se().get("".concat(a.apiUrl,"/api/admin/images"),{params:n});case 2:i=t.v,a.images=i.data.content,a.imageCurrentPage=i.data.number,a.imageTotalPages=i.data.totalPages,t.n=4;break;case 3:t.p=3,l=t.v,a.imageError="加载图像列表失败: "+((null===(r=l.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||l.message);case 4:return t.p=4,a.loadingImages=!1,t.f(4);case 5:return t.a(2)}}),t,null,[[1,3,4,5]])})))()},fetchSettings:function(){var e=this;return(0,ae.A)((0,ee.A)().m((function a(){var t,s,n;return(0,ee.A)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,se().get("".concat(e.apiUrl,"/api/admin/settings"));case 1:t=a.v,e.settings=t.data,a.n=3;break;case 2:a.p=2,n=a.v,e.$store.dispatch("showAlert",{type:"error",message:"加载系统设置失败: "+((null===(s=n.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.message)||n.message)});case 3:return a.a(2)}}),a,null,[[0,2]])})))()},saveSettings:function(){var e=this;return(0,ae.A)((0,ee.A)().m((function a(){var t,s;return(0,ee.A)().w((function(a){while(1)switch(a.n){case 0:return e.savingSettings=!0,a.p=1,a.n=2,se().post("".concat(e.apiUrl,"/api/admin/settings"),e.settings);case 2:e.$store.dispatch("showAlert",{type:"success",message:"系统设置保存成功"}),a.n=4;break;case 3:a.p=3,s=a.v,e.$store.dispatch("showAlert",{type:"error",message:"保存系统设置失败: "+((null===(t=s.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||s.message)});case 4:return a.p=4,e.savingSettings=!1,a.f(4);case 5:return a.a(2)}}),a,null,[[1,3,4,5]])})))()},changePage:function(e,a){"users"===a?e>=0&&e<this.userTotalPages&&this.fetchUsers(e):"images"===a&&e>=0&&e<this.imageTotalPages&&this.fetchImages(e)},searchUsers:function(){this.fetchUsers(0)},searchImages:function(){this.fetchImages(0)},toggleUserStatus:function(e){var a=this;return(0,ae.A)((0,ee.A)().m((function t(){var s,n;return(0,ee.A)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,t.n=1,se().post("".concat(a.apiUrl,"/api/admin/users/").concat(e.id,"/toggle-status"));case 1:a.$store.dispatch("showAlert",{type:"success",message:"用户 ".concat(e.username," 已").concat(e.active?"禁用":"激活")}),a.fetchUsers(a.userCurrentPage),t.n=3;break;case 2:t.p=2,n=t.v,a.$store.dispatch("showAlert",{type:"error",message:"更改用户状态失败: "+((null===(s=n.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.message)||n.message)});case 3:return t.a(2)}}),t,null,[[0,2]])})))()},editUserRole:function(e){var a=this;return(0,ae.A)((0,ee.A)().m((function t(){var s,n,i;return(0,ee.A)().w((function(t){while(1)switch(t.n){case 0:if(s=prompt("请为用户 ".concat(e.username," 选择新角色 (USER, REVIEWER, ADMIN)"),e.role),!s||!["USER","REVIEWER","ADMIN"].includes(s.toUpperCase())){t.n=5;break}return t.p=1,t.n=2,se().post("".concat(a.apiUrl,"/api/admin/users/").concat(e.id,"/change-role"),{role:s.toUpperCase()});case 2:a.$store.dispatch("showAlert",{type:"success",message:"用户 ".concat(e.username," 的角色已更改为 ").concat(s.toUpperCase())}),a.fetchUsers(a.userCurrentPage),t.n=4;break;case 3:t.p=3,i=t.v,a.$store.dispatch("showAlert",{type:"error",message:"更改用户角色失败: "+((null===(n=i.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||i.message)});case 4:t.n=6;break;case 5:s&&a.$store.dispatch("showAlert",{type:"error",message:"无效的角色值"});case 6:return t.a(2)}}),t,null,[[1,3]])})))()},deleteImage:function(e){var a=this;return(0,ae.A)((0,ee.A)().m((function t(){var s,n;return(0,ee.A)().w((function(t){while(1)switch(t.n){case 0:if(!confirm("确定要删除这张图像吗？此操作不可撤销。")){t.n=4;break}return t.p=1,t.n=2,se()["delete"]("".concat(a.apiUrl,"/api/admin/images/").concat(e));case 2:a.$store.dispatch("showAlert",{type:"success",message:"图像已成功删除"}),a.fetchImages(a.imageCurrentPage),t.n=4;break;case 3:t.p=3,n=t.v,a.$store.dispatch("showAlert",{type:"error",message:"删除图像失败: "+((null===(s=n.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.message)||n.message)});case 4:return t.a(2)}}),t,null,[[1,3]])})))()},formatDate:function(e){if(!e)return"";var a=new Date(e);return a.toLocaleString("zh-CN")},getStatusText:function(e){var a={UPLOADED:"已上传",ANNOTATED:"已标注",REVIEWED:"已审核",APPROVED:"已批准",REJECTED:"已拒绝"};return a[e]||e},goToReviewerApplications:function(){this.$router.push("/admin/reviewer-applications")}}};var ie=t(66262);const re=(0,ie.A)(ne,[["render",Z],["__scopeId","data-v-537c82f4"]]),le=re}}]);
//# sourceMappingURL=381.cd9492de.js.map