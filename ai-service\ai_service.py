from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
import numpy as np
from PIL import Image
import io
import cv2
import time
import json
import uvicorn
import os
import ollama
import httpx
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import logging
import uuid
from ultralytics import YOLO
from pathlib import Path
import re # Added for JSON fixing

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ai-service")

app = FastAPI(title="血管瘤AI分析服务", description="提供YOLO目标检测与LLM诊断建议生成功能")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，或者可以指定: ["http://localhost:8080", "http://************"]
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 全局变量存储模型
yolo_model = None
color_model = None
part_model = None

# 类别名称 - 根据您的模型调整
CLASS_NAMES = ["IH", "RICH", "PICH", "NICH", "KHE", "TA", "PG", "MVM", "VM", "AVM", "LM", "GVM", "CLVM", "CAVM", "LVM"]
COLOR_CLASS_NAMES = []
PART_CLASS_NAMES = []

# 血管瘤分类映射 - 完整的三大类分类体系
HEMANGIOMA_CATEGORIES = {
    "真性血管肿瘤": {
        "IH": "婴幼儿血管瘤",
        "RICH": "先天性快速消退型血管瘤",
        "PICH": "先天性部分消退型血管瘤",
        "NICH": "先天性不消退型血管瘤",
        "KHE": "卡波西型血管内皮细胞瘤",
        "TA": "丛状血管瘤",
        "PG": "化脓性肉芽肿",
        "SCH": "梭形细胞血管瘤",
        "EHE": "上皮样血管内皮瘤",
        "RHE": "网状血管内皮瘤",
        "PHE": "假肌源性血管内皮瘤",
        "POHE": "多形性血管内皮瘤",
        "AS": "血管肉瘤",
        "EAS": "上皮样血管肉瘤",
        "KS": "卡波西肉瘤"
    },
    "血管畸形": {
        "MVM": "微静脉畸形",
        "VM": "静脉畸形",
        "AVM": "动静脉畸形",
        "LM": "淋巴管畸形",
        "GVM": "球细胞静脉畸形",
        "CLVM": "毛细血管-淋巴管-静脉畸形",
        "CAVM": "毛细血管-动静脉畸形",
        "LVM": "淋巴管-静脉畸形"
    },
    "血管假瘤/易混淆病变": {
        "HPC": "血管外皮细胞瘤",
        "GT": "血管球瘤",
        "AL": "血管平滑肌瘤",
        "AF": "血管纤维瘤",
        "THH": "靶样含铁血黄素沉积性血管瘤",
        "HH": "鞋钉样血管瘤"
    }
}

# 新增中文名称映射 - 兼容旧版本
CLASS_NAMES_CHINESE = {
    "IH": "婴幼儿血管瘤",
    "RICH": "先天性快速消退型血管瘤",
    "PICH": "先天性部分消退型血管瘤",
    "NICH": "先天性不消退型血管瘤",
    "KHE": "卡波西型血管内皮细胞瘤",
    "KH": "角化型血管瘤",  # 保留兼容
    "TA": "丛状血管瘤",
    "PG": "化脓性肉芽肿",
    "MVM": "微静脉畸形",
    "VM": "静脉畸形",
    "AVM": "动静脉畸形",
    "LM": "淋巴管畸形",
    "GVM": "球细胞静脉畸形",
    "CLVM": "毛细血管-淋巴管-静脉畸形",
    "CAVM": "毛细血管-动静脉畸形",
    "LVM": "淋巴管-静脉畸形"
}

# 模型配置
MODEL_PATH = "F:/xueguan2/resources/models/best.onnx"  # 使用绝对路径
COLOR_MODEL_PATH = "F:/xueguan2/resources/data/Color/best_color.pt"
PART_MODEL_PATH = "F:/xueguan2/resources/data/Part/best_part(2).pt"
CONFIDENCE_THRESHOLD = 0.25  # 置信度阈值
IOU_THRESHOLD = 0.45  # NMS IOU阈值

# 分类标签路径
COLOR_CLASSES_PATH = "F:/xueguan2/resources/data/Color/colors_classes_CN.txt"
PART_CLASSES_PATH = "F:/xueguan2/resources/data/Part/Part_classes_CN.txt"


# LLM服务配置
# The Ollama library automatically connects to the default endpoint (http://localhost:11434)
# To configure a different host, set the OLLAMA_HOST environment variable.
# Example: export OLLAMA_HOST="http://*************:11434"
LLM_MODEL_NAME = os.getenv("LLM_MODEL_NAME", "deepseek-r1:8b")
JAVA_CALLBACK_URL = os.getenv("JAVA_CALLBACK_URL", "http://localhost:8085/medical/api/hemangioma-diagnoses/update-recommendation")

# 处理后图片保存路径
PROCESSED_DIR = "medical_images/processed"

# 确保保存目录存在
os.makedirs(PROCESSED_DIR, exist_ok=True)

class DetectionBox(BaseModel):
    class_id: int
    class_name: str
    confidence: float
    bbox: List[int]
    width: int
    height: int
    orig_width: int
    orig_height: int

class PredictedClass(BaseModel):
    name: str
    confidence: float

class DetectionResult(BaseModel):
    detected: bool
    confidence: float = 0.0
    boxes: List[DetectionBox] = []
    detection_boxes: List[DetectionBox] = [] # 新增字段以兼容Java端
    processing_time: float
    image_dimensions: Dict[str, int]
    processed_image_path: str = ""
    recommendation: str = ""
    detected_type: str = ""
    predicted_color: Optional[PredictedClass] = None
    predicted_part: Optional[PredictedClass] = None

def get_vessel_texture_label(value: Optional[str]) -> str:
    if not value: return "未提供"
    labels = {
        "soft": "软乎乎的（质软）",
        "elastic": "有点硬但有弹性（质韧）",
        "hard": "硬邦邦的（质硬）",
        "cystic": "像水气球一样（囊性 / 有波动感）",
        "compressible": "一按就扁，松手又鼓起来（可压缩性）"
    }
    return labels.get(value, "未提供")

def call_llm_service(
    patient_age: Optional[int], 
    gender: Optional[str], 
    origin_type: Optional[str], 
    vessel_texture: Optional[str], 
    confidence: float,
    detected_types: str,
    predicted_color: Optional[PredictedClass],
    predicted_part: Optional[PredictedClass]
) -> str:
    """调用大模型服务获取诊断建议"""
    logger.info("开始调用大模型服务 (后台)...")
    
    # 将检测到的类型缩写转换为带中文解释的完整字符串
    if detected_types:
        type_abbrs = detected_types.split('+')
        type_full_names = [f"{abbr} ({CLASS_NAMES_CHINESE.get(abbr, '未知类型')})" for abbr in type_abbrs]
        detected_types_with_explanation = ", ".join(type_full_names)
    else:
        detected_types_with_explanation = "未检测到特定亚型"

    # 1. 定义上下文信息
    context_info = {
        "诊断病症": "血管瘤",
        "AI初步识别亚型": detected_types_with_explanation,
        "AI识别病灶颜色": predicted_color.name if predicted_color else "未识别",
        "AI识别病灶部位": predicted_part.name if predicted_part else "未识别",
        "患者年龄": f"{patient_age} 岁" if patient_age is not None else "未知",
        "患者性别": gender if gender else "未知",
        "病变类型": origin_type if origin_type else "未知",
        "血管质地": get_vessel_texture_label(vessel_texture),
        "AI初步识别置信度": f"{confidence * 100:.1f}%" if confidence > 0 else "N/A"
    }

    # 2. 定义JSON模板
    json_template = {
      "treatment_suggestion": "",
      "precautions": "",
      "disclaimer": "本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
    }

    # 3. 构建提示
    prompt = f"""你是一名资深的血管瘤医学专家。请基于以下患者信息，生成一份个性化健康管理方案。

# 患者信息:
{json.dumps(context_info, indent=2, ensure_ascii=False)}

# 任务:
严格按照以下JSON格式填充内容并返回。不要添加任何额外的解释或说明文字，只返回JSON对象。
请确保返回的是有效的JSON格式，所有键名必须与模板完全一致，不要修改键名。

{json.dumps(json_template, indent=2, ensure_ascii=False)}
"""

    messages = [{'role': 'user', 'content': prompt}]

    try:
        logger.info(f"向大模型 {LLM_MODEL_NAME} 发送请求...")
        
        response = ollama.chat(
            model=LLM_MODEL_NAME,
            messages=messages,
            stream=False,
            options={"temperature": 0.0}
        )
        
        model_output = response['message']['content']
        logger.info(f"大模型返回内容长度: {len(model_output)} 字符")
        logger.info(f"大模型返回内容前100字符: {model_output[:100]}")
        
        # 增强的JSON提取逻辑
        try:
            # 找到第一个 '{' 和最后一个 '}' 来提取JSON块
            json_start = model_output.find('{')
            json_end = model_output.rfind('}') + 1
            
            if json_start != -1 and json_end > 0:
                json_string = model_output[json_start:json_end]
                # 验证提取的字符串是否为有效的JSON
                try:
                    # 先尝试直接解析
                    parsed_json = json.loads(json_string)
                    logger.info("后台任务成功从大模型响应中提取并验证了JSON。")
                    
                    # 确保所有必要的字段都存在
                    required_fields = ["treatment_suggestion", "precautions", "disclaimer"]
                    missing_fields = [field for field in required_fields if field not in parsed_json]
                    
                    if missing_fields:
                        logger.warning(f"JSON中缺少必要字段: {missing_fields}")
                        # 添加缺失的字段
                        for field in missing_fields:
                            parsed_json[field] = "由于AI返回格式错误，无法提供详细建议。请咨询医生获取专业意见。"
                        
                        # 返回修复后的JSON
                        return json.dumps(parsed_json, ensure_ascii=False)
                    else:
                        # 所有字段都存在，返回原始JSON
                        return json_string
                        
                except json.JSONDecodeError:
                    # 如果JSON格式不正确，尝试修复常见错误
                    logger.warning("提取的JSON格式不正确，尝试修复...")
                    
                    # 1. 修复dietary_advice字段的常见错误
                    # 查找类似 ": "":" 或 "": "" 这样的模式，这通常是dietary_advice字段的错误
                    fixed_json = re.sub(r'":\s*"":', '"dietary_advice":', json_string)
                    fixed_json = re.sub(r'""\s*:', '"dietary_advice":', fixed_json)
                    fixed_json = re.sub(r'":\s*""\s*:', '"dietary_advice":', fixed_json)
                    
                    # 2. 修复可能的多余逗号
                    fixed_json = re.sub(r',\s*}', '}', fixed_json)
                    
                    # 3. 修复可能的缺失引号
                    fixed_json = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', fixed_json)
                    
                    # 尝试解析修复后的JSON
                    try:
                        parsed_json = json.loads(fixed_json)
                        logger.info("成功修复并验证JSON格式。")
                        
                        # 确保所有必要的字段都存在
                        required_fields = ["treatment_suggestion", "precautions", "disclaimer"]
                        missing_fields = [field for field in required_fields if field not in parsed_json]
                        
                        if missing_fields:
                            logger.warning(f"修复后的JSON仍缺少必要字段: {missing_fields}")
                            # 添加缺失的字段
                            for field in missing_fields:
                                parsed_json[field] = "由于AI返回格式错误，无法提供详细建议。请咨询医生获取专业意见。"
                            
                            # 返回修复后的JSON
                            return json.dumps(parsed_json, ensure_ascii=False)
                        else:
                            # 所有字段都存在，返回修复后的JSON
                            return json.dumps(parsed_json, ensure_ascii=False)
                            
                    except json.JSONDecodeError as e:
                        # 如果修复失败，创建一个完整的新JSON
                        logger.error(f"无法修复JSON格式: {e}，使用默认模板。")
                        template = {
                            "treatment_suggestion": "由于AI返回格式错误，无法提供详细治疗建议。请咨询医生获取专业意见。",
                            "precautions": "如有任何不适，请立即就医。",
                            "disclaimer": "本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
                        }
                        return json.dumps(template, ensure_ascii=False)
            else:
                # 如果找不到 '{' 或 '}'，则认为格式无效
                logger.error("在模型输出中未找到有效的JSON对象。")
                template = {
                    "treatment_suggestion": "由于AI返回格式错误，无法提供详细治疗建议。请咨询医生获取专业意见。",
                    "precautions": "如有任何不适，请立即就医。",
                    "disclaimer": "本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
                }
                return json.dumps(template, ensure_ascii=False)

        except Exception as e:
            logger.error(f"处理大模型返回的JSON时发生未知错误: {e}")
            # 返回一个包含错误信息的JSON字符串
            template = {
                "treatment_suggestion": "由于AI返回格式错误，无法提供详细治疗建议。请咨询医生获取专业意见。",
                "precautions": "如有任何不适，请立即就医。",
                "disclaimer": "本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
            }
            return json.dumps(template, ensure_ascii=False)

    except Exception as e:
        error_message = str(e)
        logger.error(f"后台任务调用大模型服务失败: {error_message}")
        if "connection refused" in error_message.lower():
             logger.error("无法连接到Ollama服务。请确保Ollama正在运行。")
             return json.dumps({"error": "无法连接到Ollama服务，请确保其正在运行。"})
        return json.dumps({"error": f"调用大模型服务失败: {error_message}"})

def load_class_names(file_path: str) -> List[str]:
    """从文本文件加载类别名称"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f if line.strip()]
        logger.info(f"成功从 {file_path} 加载 {len(names)} 个类别。")
        return names
    except FileNotFoundError:
        logger.error(f"类别文件未找到: {file_path}")
        return []

def initialize_model():
    """初始化所有AI模型"""
    global yolo_model, color_model, part_model
    global COLOR_CLASS_NAMES, PART_CLASS_NAMES
    
    try:
        if os.path.exists(MODEL_PATH):
            logger.info(f"开始加载目标检测模型: {MODEL_PATH}")
            yolo_model = YOLO(MODEL_PATH)
            logger.info("目标检测模型加载成功。")
        else:
            logger.error(f"目标检测模型文件未找到: {MODEL_PATH}")

        if os.path.exists(COLOR_MODEL_PATH):
            logger.info(f"开始加载颜色分类模型: {COLOR_MODEL_PATH}")
            color_model = YOLO(COLOR_MODEL_PATH)
            logger.info("颜色分类模型加载成功。")
            COLOR_CLASS_NAMES = load_class_names(COLOR_CLASSES_PATH)
        else:
            logger.error(f"颜色分类模型文件未找到: {COLOR_MODEL_PATH}")

        if os.path.exists(PART_MODEL_PATH):
            logger.info(f"开始加载部位分类模型: {PART_MODEL_PATH}")
            part_model = YOLO(PART_MODEL_PATH)
            logger.info("部位分类模型加载成功。")
            PART_CLASS_NAMES = load_class_names(PART_CLASSES_PATH)
        else:
            logger.error(f"部位分类模型文件未找到: {PART_MODEL_PATH}")

    except Exception as e:
        logger.error(f"模型初始化过程中发生错误: {e}", exc_info=True)
        return False
    
    return True

def predict_classification(model, image: np.ndarray, class_names: List[str]) -> Optional[PredictedClass]:
    """
    使用目标检测模型对给定的图像进行“分类”预测。
    它会找到置信度最高的目标，并返回其类别和置信度。
    这适用于将目标检测模型用作分类器的场景。
    返回一个包含类别名称和置信度的 PredictedClass 对象，如果失败或未检测到目标则返回 None。
    """
    if model is None or not class_names:
        logger.warning("分类模型或类别列表未初始化，跳过分类。")
        return None
    
    try:
        results = model(image, verbose=False)
        
        # 提取第一个结果（因为我们只处理单张图片）
        result = results[0]
        
        # 检查是否有检测结果
        if len(result.boxes) == 0:
            logger.warning("目标检测模型未在该图像上找到任何目标，无法进行分类。")
            return None

        # 找到置信度最高的检测结果
        highest_confidence = -1.0
        best_prediction = None

        for box in result.boxes:
            confidence = box.conf[0].item()
            if confidence > highest_confidence:
                highest_confidence = confidence
                class_id = int(box.cls[0].item())
                
                if class_id < len(class_names):
                    class_name = class_names[class_id]
                    best_prediction = PredictedClass(name=class_name, confidence=highest_confidence)
                else:
                    logger.error(f"预测索引 {class_id} 超出类别列表范围 (大小: {len(class_names)})。")
        
        if best_prediction:
            logger.info(f"模拟分类预测结果 (最高置信度目标): {best_prediction.name}, 置信度: {best_prediction.confidence:.4f}")
        else:
            logger.warning("未找到任何有效的预测目标。")

        return best_prediction
            
    except Exception as e:
        logger.error(f"使用目标检测模型进行分类预测时出错: {e}", exc_info=True)
        return None


def save_processed_image(image, original_filename=None):
    """
    保存处理后的图像到指定目录，并返回Web可访问的路径。
    """
    try:
        if original_filename:
            # 保留原始文件名但去除扩展名
            original_stem = Path(original_filename).stem
        else:
            # 如果没有原始文件名，创建一个基于时间的唯一标识
            original_stem = f"processed_{uuid.uuid4().hex[:12]}"

        # 创建一个更具描述性的唯一文件名
        timestamp = int(time.time())
        unique_id = uuid.uuid4().hex[:8]
        processed_filename = f"{original_stem}_processed_{timestamp}_{unique_id}.jpg"
        
        # 构建完整的文件系统保存路径
        save_path = os.path.join(PROCESSED_DIR, processed_filename)
        
        # 保存图像
        cv2.imwrite(save_path, image)
        logger.info(f"处理后的图像已保存: {save_path}")

        # 构建Web访问路径，并确保使用正斜杠
        web_path = f"/medical/images/processed/{processed_filename}"
        
        # 显式地将任何反斜杠替换为正斜杠，以确保URL的兼容性
        web_path = web_path.replace('\\\\', '/')

        return web_path
    except Exception as e:
        logger.error(f"保存处理后图像时发生错误: {e}")
        return None

async def update_java_backend(diagnosis_id: int, recommendation: str, predicted_color: Optional[PredictedClass] = None, predicted_part: Optional[PredictedClass] = None):
    """
    使用HTTPX异步回调Java后端的接口，更新诊断建议和分类结果
    """
    payload = {
        "diagnosisId": diagnosis_id, 
        "recommendation": recommendation
    }
    
    # 添加颜色和部位信息
    if predicted_color:
        payload["predictedColor"] = {
            "name": predicted_color.name,
            "confidence": predicted_color.confidence
        }
    
    if predicted_part:
        payload["predictedPart"] = {
            "name": predicted_part.name,
            "confidence": predicted_part.confidence
        }
        
    try:
        async with httpx.AsyncClient() as client:
            logger.info(f"准备回调Java后端: {JAVA_CALLBACK_URL}, ID: {diagnosis_id}")
            # 输出更详细的请求信息
            logger.info(f"请求参数: diagnosisId={diagnosis_id}, recommendation长度={len(recommendation)}")
            logger.info(f"请求payload摘要: {recommendation[:100]}...")
            if predicted_color:
                logger.info(f"预测颜色: {predicted_color.name}, 置信度: {predicted_color.confidence:.4f}")
            if predicted_part:
                logger.info(f"预测部位: {predicted_part.name}, 置信度: {predicted_part.confidence:.4f}")
            
            response = await client.post(JAVA_CALLBACK_URL, json=payload, timeout=60)
            response.raise_for_status()
            logger.info(f"成功回调Java后端，更新了ID为 {diagnosis_id} 的记录, 响应状态: {response.status_code}")
            logger.info(f"响应内容: {response.text}")
    except httpx.RequestError as e:
        # 输出更详细的错误信息
        logger.error(f"回调Java后端失败 (URL: {JAVA_CALLBACK_URL}): {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        # 如果是连接错误，提供更多信息
        if isinstance(e, httpx.ConnectError):
            logger.error(f"连接错误，请检查Java后端是否正在运行，以及网络连接是否正常")
    except Exception as e:
        logger.error(f"回调Java后端时发生未知错误: {str(e)}")
        logger.exception("详细错误信息:")

def run_llm_and_update_in_background(
    diagnosis_id: int,
    patient_age: Optional[int], 
    gender: Optional[str], 
    origin_type: Optional[str], 
    vessel_texture: Optional[str], 
    confidence: float,
    detected_types: str,
    predicted_color: Optional[PredictedClass],
    predicted_part: Optional[PredictedClass]
):
    """
    后台任务的封装：1. 调用LLM  2. 调用Java回调
    """
    logger.info(f"后台任务开始，处理诊断ID: {diagnosis_id}")
    # 1. 调用LLM获取建议
    recommendation = call_llm_service(
        patient_age, gender, origin_type, vessel_texture, confidence, detected_types, predicted_color, predicted_part
    )
    # 2. 异步调用Java后端进行更新
    import asyncio
    asyncio.run(update_java_backend(diagnosis_id, recommendation, predicted_color, predicted_part))
    logger.info(f"后台任务结束，处理诊断ID: {diagnosis_id}")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    logger.info("AI服务正在启动...")
    
    if not initialize_model():
        logger.error("模型初始化失败，应用无法启动")
        raise RuntimeError("模型初始化失败，应用无法启动")
    logger.info("AI服务启动完成，等待请求...")

@app.get("/")
async def root():
    """API根路径，返回服务状态"""
    return {
        "status": "运行中",
        "service": "血管瘤AI检测服务",
        "model": MODEL_PATH,
        "endpoints": {
            "/detect": "上传图像进行血管瘤检测",
            "/health": "检查服务健康状态"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查端点，返回各个模型的状态"""
    return {
        "status": "healthy",
        "model_loaded": yolo_model is not None,
        "color_model_loaded": color_model is not None,
        "part_model_loaded": part_model is not None,
        "color_classes_loaded": len(COLOR_CLASS_NAMES) > 0,
        "part_classes_loaded": len(PART_CLASS_NAMES) > 0,
    }

@app.post("/detect", response_model=DetectionResult)
async def detect_objects(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    diagnosis_id: int = Form(...),
    patient_age: Optional[int] = Form(None),
    gender: Optional[str] = Form(None),
    origin_type: Optional[str] = Form(None),
    vessel_texture: Optional[str] = Form(None)
):
    """
    上传图像进行血管瘤检测，并根据提供的信息生成诊断建议
    """
    logger.info(f"收到检测请求，文件名: {file.filename}, 诊断ID: {diagnosis_id}")
    logger.info(f"患者信息 - 年龄: {patient_age}, 性别: {gender}, 类型: {origin_type}, 触感: {vessel_texture}")
    
    if not yolo_model:
        logger.error("模型未加载")
        raise HTTPException(status_code=500, detail="模型未加载")
    
    try:
        # 读取上传的图像
        image_data = await file.read()
        logger.info(f"读取图像数据，大小: {len(image_data)} 字节")
        
        # 转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            logger.error("无法解码图像")
            raise HTTPException(status_code=400, detail="无法解码图像")
        
        # 记录图像尺寸
        height, width = image.shape[:2]
        logger.info(f"图像尺寸: {width} x {height}")
        
        # 保存原始图像用于调试
        debug_path = os.path.join(PROCESSED_DIR, f"debug_original_{int(time.time())}.jpg")
        cv2.imwrite(debug_path, image)
        logger.info(f"原始图像已保存用于调试: {debug_path}")
        
        # 使用YOLO模型进行预测
        start_time = time.time()
        logger.info("开始使用YOLO模型进行预测...")
        
        try:
            # 使用ultralytics库的YOLO模型进行预测
            results = yolo_model(image, conf=CONFIDENCE_THRESHOLD, iou=IOU_THRESHOLD)
            logger.info("YOLO预测完成")
            
            # 处理预测结果
            detections: List[DetectionBox] = []
            detected_tag_names = set()
            
            # 对每个检测结果
            for result in results:
                # 获取边界框、置信度和类别
                boxes = result.boxes
                
                if len(boxes) > 0:
                    logger.info(f"检测到 {len(boxes)} 个目标")
                    
                    # 遍历所有检测框
                    for i, box in enumerate(boxes):
                        # 获取边界框坐标 (xyxy格式)
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        
                        # 获取置信度
                        confidence = float(box.conf[0].cpu().numpy())
                        
                        # 获取类别ID和名称
                        class_id = int(box.cls[0].cpu().numpy())
                        class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
                        detected_tag_names.add(class_name)
                        
                        # 计算宽度和高度
                        box_width = int(x2 - x1)
                        box_height = int(y2 - y1)
                        
                        # 创建检测结果对象
                        detection = DetectionBox(
                            class_id=class_id,
                            class_name=class_name,
                            confidence=confidence,
                            bbox=[int(x1), int(y1), int(x2), int(y2)],
                            width=box_width,
                            height=box_height,
                            orig_width=width,
                            orig_height=height
                        )
                        
                        detections.append(detection)
                        logger.info(f"检测 #{i+1}: 类别={class_name}, 置信度={confidence:.4f}, 位置={[int(x1), int(y1), int(x2), int(y2)]}")
                else:
                    logger.info("未检测到任何目标")
            
        except Exception as e:
            logger.exception(f"YOLO预测过程中出错: {str(e)}")
            raise HTTPException(status_code=500, detail=f"YOLO预测失败: {str(e)}")
        
        # 计算处理时间
        processing_time = time.time() - start_time
        logger.info(f"处理完成，耗时: {processing_time:.3f}秒")
        
        # 确定是否检测到血管瘤
        detected = len(detections) > 0
        max_confidence = max([d.confidence for d in detections]) if detected else 0.0
        detected_type_str = "+".join(sorted(list(detected_tag_names)))
        
        # 在图像上绘制检测框
        processed_image_path = ""
        if detected:
            logger.info("在图像上绘制检测框...")
            # 使用YOLO结果自带的绘制功能
            result_plotted = results[0].plot()  # BGR格式图像
            
            # 保存处理后的图像
            processed_image_path = save_processed_image(result_plotted, file.filename)
            logger.info(f"处理后图像Web路径: {processed_image_path}")
            
            # 注册后台任务来调用LLM并更新Java后端
            background_tasks.add_task(
                run_llm_and_update_in_background,
                diagnosis_id=diagnosis_id,
                patient_age=patient_age,
                gender=gender,
                origin_type=origin_type,
                vessel_texture=vessel_texture,
                confidence=max_confidence,
                detected_types=detected_type_str,
                predicted_color=None, # 暂时设置为None，因为这里没有预测结果
                predicted_part=None # 暂时设置为None，因为这里没有预测结果
            )
            logger.info(f"已为诊断ID {diagnosis_id} 注册后台LLM任务。")

        else:
            # 即使没有检测到，也保存一个处理后的图像（原始图像）
            processed_image_path = save_processed_image(image, file.filename)
            logger.info(f"未检测到目标，保存原始图像: {processed_image_path}")
        
        # 立即返回初步检测结果，recommendation字段为空
        return DetectionResult(
            detected=detected,
            confidence=float(max_confidence),
            boxes=[d.dict() for d in detections],
            processing_time=processing_time,
            image_dimensions={"width": width, "height": height},
            processed_image_path=processed_image_path,
            recommendation="", # 注意：这里返回空字符串
            detected_type=detected_type_str
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.exception(f"处理请求时发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

@app.post("/diagnose")
async def diagnose(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    diagnosis_id: Optional[int] = Form(None),  # 添加诊断ID参数
    patient_age: Optional[int] = Form(None),
    gender: Optional[str] = Form(None),
    origin_type: Optional[str] = Form(None),
    vessel_texture: Optional[str] = Form(None)
):
    """
    接收图像和患者信息，执行YOLO检测和LLM诊断，并立即返回所有结果。
    这是一个同步阻塞的端点，可能会比较慢。
    """
    logger.info("接收到 /diagnose 请求")
    if diagnosis_id:
        logger.info(f"诊断ID: {diagnosis_id}")
    start_time = time.time()

    # 1. 图像预处理
    contents = await file.read()
    try:
        # 使用OpenCV读取图像，以确保格式与旧端点一致 (BGR)
        nparr = np.frombuffer(contents, np.uint8)
        image_np = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image_np is None:
            raise HTTPException(status_code=400, detail="无法解码图像")
        orig_height, orig_width = image_np.shape[:2]
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"无法处理图像文件: {e}")

    # 2. 目标检测
    logger.info("开始进行目标检测...")
    detection_results = yolo_model.predict(
        source=image_np,
        conf=CONFIDENCE_THRESHOLD,
        iou=IOU_THRESHOLD,
        verbose=False
    )
    logger.info("目标检测完成。")

    # 3. 并行进行颜色和部位分类
    logger.info("开始进行颜色和部位分类...")
    predicted_color = predict_classification(color_model, image_np, COLOR_CLASS_NAMES)
    predicted_part = predict_classification(part_model, image_np, PART_CLASS_NAMES)
    logger.info("颜色和部位分类完成。")

    # 4. 处理检测结果
    detection_result = detection_results[0]
    boxes_data = []
    detected_class_names = set()
    highest_confidence = 0.0

    # 处理检测框
    boxes = detection_result.boxes
    if len(boxes) > 0:
        for box in boxes:
            # 处理每个检测到的对象
            class_id = int(box.cls[0].item())
            class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"未知类型_{class_id}"
            confidence = float(box.conf[0].item())
            
            # 记录检测到的类别
            detected_class_names.add(class_name)

            # 更新最高置信度
            if confidence > highest_confidence:
                highest_confidence = confidence

            # 获取边界框坐标
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
            width = int(x2 - x1)
            height = int(y2 - y1)

            # 添加到结果列表
            boxes_data.append({
            "class_id": class_id,
            "class_name": class_name,
                "confidence": confidence,
            "bbox": [int(x1), int(y1), int(x2), int(y2)],
                "width": width,
                "height": height,
            "orig_width": orig_width,
            "orig_height": orig_height
            })
    
    # 5. 保存处理后的图像
    detected = len(boxes_data) > 0
    detected_type_str = "+".join(sorted(list(detected_class_names))) if detected_class_names else ""
    
    # 处理结果图像
    processed_image_path = ""
    if detected:
        result_plotted = detection_results[0].plot()
        processed_image_path = save_processed_image(result_plotted, file.filename)
    
    # 6. 调用大模型进行分析
    logger.info("开始调用大模型服务...")
    if diagnosis_id is not None:
        # 如果提供了诊断ID，则启动后台任务
        background_tasks.add_task(
            run_llm_and_update_in_background,
            diagnosis_id,
            patient_age,
            gender,
            origin_type,
            vessel_texture,
            highest_confidence,
            detected_type_str,
            predicted_color,
            predicted_part
    )
        logger.info(f"已添加后台任务，稍后将回调Java后端更新诊断ID: {diagnosis_id}")
    
    # 7. 构建返回结果
    response_data = {
        "detected": detected,
        "confidence": highest_confidence,
        "boxes": boxes_data,
        "processing_time": time.time() - start_time,
        "image_dimensions": {"width": orig_width, "height": orig_height},
        "processed_image_path": processed_image_path,
        "detected_type": detected_type_str,
        "predicted_color": {"name": predicted_color.name, "confidence": predicted_color.confidence} if predicted_color else None,
        "predicted_part": {"name": predicted_part.name, "confidence": predicted_part.confidence} if predicted_part else None,
    }
    
    logger.info(f"处理后的图像已保存: {processed_image_path}")
    return response_data


@app.post("/diagnose-yolo")
async def diagnose_yolo_only(
    file: UploadFile = File(...),
    only_yolo: Optional[str] = Form(None),
    patient_age: Optional[int] = Form(None),
    gender: Optional[str] = Form(None),
    origin_type: Optional[str] = Form(None),
    vessel_texture: Optional[str] = Form(None)
):
    """
    仅执行YOLO目标检测，并返回结果。不调用LLM。
    这是一个轻量级的检测端点。
    """
    logger.info("接收到 /diagnose-yolo 请求")
    start_time = time.time()

    # 1. 图像预处理
    contents = await file.read()
    try:
        # 使用OpenCV读取图像，确保颜色格式(BGR)正确
        nparr = np.frombuffer(contents, np.uint8)
        image_np = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image_np is None:
            raise HTTPException(status_code=400, detail="无法解码图像")
        orig_height, orig_width = image_np.shape[:2]
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"无法处理图像文件: {e}")

    # 2. 目标检测
    logger.info("开始进行目标检测...")
    detection_results = yolo_model.predict(
        source=image_np,
        conf=CONFIDENCE_THRESHOLD,
        iou=IOU_THRESHOLD,
        verbose=False
    )
    logger.info("目标检测完成。")

    # 3. 并行进行颜色和部位分类
    logger.info("开始进行颜色和部位分类...")
    predicted_color = predict_classification(color_model, image_np, COLOR_CLASS_NAMES)
    predicted_part = predict_classification(part_model, image_np, PART_CLASS_NAMES)
    logger.info("颜色和部位分类完成。")
    
    # 4. 处理检测结果
    detection_result = detection_results[0]
    boxes_data = []
    detected_class_names = set()
    highest_confidence = 0.0

    # 使用更稳健的方式遍历检测框
    for box in detection_result.boxes:
        confidence = box.conf[0].item()
        # 预测时已使用阈值过滤，此处无需再次检查
        # if confidence < CONFIDENCE_THRESHOLD:
        #     continue
            
        class_id = int(box.cls[0].item())
        class_name = CLASS_NAMES[class_id]
        detected_class_names.add(class_name)

        # 更新最高置信度
        if confidence > highest_confidence:
            highest_confidence = confidence

        # 获取坐标
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()

        # 计算宽度和高度
        box_width = int(x2 - x1)
        box_height = int(y2 - y1)

        # 添加检测框信息
        box_info = {
            "class_id": class_id,
            "class_name": class_name,
            "confidence": float(confidence),
            "bbox": [int(x1), int(y1), int(x2), int(y2)],
            "width": box_width,
            "height": box_height,
            "orig_width": orig_width,
            "orig_height": orig_height
        }
        boxes_data.append(box_info)

    detected_types_list = sorted(list(set(item.get("class_name") for item in boxes_data)))
    detected_types_str = "+".join(detected_types_list)
    
    # 5. 保存带标注框的图像
    processed_image_path = ""
    if detection_result.plot is not None:
        # .plot()返回的图像是BGR格式，因为输入是BGR。cv2.imwrite期望BGR，所以直接保存。
        processed_image_bgr = detection_result.plot(conf=True, line_width=1, font_size=5, pil=False)
        processed_image_path = save_processed_image(processed_image_bgr, file.filename)

    # 6. 准备最终响应
    return DetectionResult(
        detected=bool(boxes_data),
        confidence=highest_confidence,
        boxes=boxes_data,
        detection_boxes=boxes_data,  # 添加一个别名以匹配Java端
        processing_time=time.time() - start_time,
        image_dimensions={"width": orig_width, "height": orig_height},
        processed_image_path=processed_image_path,
        detected_type=detected_types_str,
        predicted_color=predicted_color,
        predicted_part=predicted_part
    )

@app.post("/update-annotation")
async def update_annotation(
    file: UploadFile = File(...),
    annotations: str = Form(...),  # JSON字符串，包含标注框数据
    diagnosis_id: int = Form(...),
    original_image_path: Optional[str] = Form(None)  # 原始图像的路径，用于查找原图
):
    """
    接收用户修改的标注框数据，重新生成带有标注的图像，并更新数据库中的图像路径。
    当用户在标注界面修改了标注框后，离开页面时自动调用此接口。
    
    参数:
    - file: 原始图像文件
    - annotations: JSON字符串，包含标注框数据列表，格式为:
      [{"class_id": 0, "class_name": "IH", "bbox": [x1,y1,x2,y2], "confidence": 0.9}, ...]
    - diagnosis_id: 诊断记录ID，用于更新数据库
    - original_image_path: 原始已处理图像的路径(可选)
    
    返回:
    - 包含新生成图像路径的响应对象
    """
    logger.info(f"收到标注框更新请求，诊断ID: {diagnosis_id}")
    
    try:
        # 1. 读取图像
        image_data = await file.read()
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise HTTPException(status_code=400, detail="无法解码图像")
        
        height, width = image.shape[:2]
        logger.info(f"图像尺寸: {width} x {height}")
        
        # 2. 解析标注数据
        try:
            annotations_data = json.loads(annotations)
            logger.info(f"收到 {len(annotations_data)} 个标注框")
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="标注数据格式不正确")
        
        # 3. 在图像上绘制标注框
        for anno in annotations_data:
            class_name = anno.get("class_name", "未知")
            confidence = anno.get("confidence", 0.0)
            bbox = anno.get("bbox", [0, 0, 0, 0])
            
            # 确保bbox是有效的
            if len(bbox) != 4:
                continue
                
            # 绘制边框
            x1, y1, x2, y2 = [int(coord) for coord in bbox]
            color = (0, 255, 0)  # 绿色
            cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
            
            # 添加标签文本
            label = f"{class_name} {confidence:.2f}"
            cv2.putText(image, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
        
        # 4. 保存处理后的图像
        processed_image_path = save_processed_image(image, file.filename)
        logger.info(f"更新的图像已保存: {processed_image_path}")
        
        # 5. 尝试更新数据库中的图像路径
        # 注意：这里需要一个后端API来更新数据库
        # 这部分需要根据您的数据库结构和访问方式进行实现
        try:
            # 调用Java后端API更新图像路径
            async with httpx.AsyncClient() as client:
                # 尝试多个可能的URL路径
                base_urls = [
                    "http://localhost:8085/medical/api/hemangioma-diagnoses/update-image-path",
                    "http://localhost:8085/api/hemangioma-diagnoses/update-image-path"
                ]
                
                payload = {
                    "diagnosisId": diagnosis_id,
                    "processedImagePath": processed_image_path
                }
                
                # 添加重试逻辑
                success = False
                last_error = None
                
                for base_url in base_urls:
                    try:
                        logger.info(f"尝试更新图像路径，URL: {base_url}")
                        response = await client.post(
                            base_url, 
                            json=payload, 
                            timeout=10,
                            headers={"Content-Type": "application/json"}
                        )
                        response.raise_for_status()
                        logger.info(f"成功更新数据库中的图像路径，诊断ID: {diagnosis_id}, URL: {base_url}")
                        success = True
                        break
                    except Exception as e:
                        logger.warning(f"URL {base_url} 更新失败: {e}")
                        last_error = e
                        continue
                
                if not success:
                    logger.error(f"所有URL尝试均失败，最后的错误: {last_error}")
                    raise last_error
        except Exception as e:
            logger.error(f"更新数据库中的图像路径失败: {e}")
            # 继续执行，不因为数据库更新失败而中断整个流程
        
        # 6. 返回结果
        return {
            "status": "success",
            "processed_image_path": processed_image_path,
            "message": "标注框已更新，图像已重新生成"
        }
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.exception(f"处理标注框更新请求时发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

async def main():
    """使用 uvicorn 启动 FastAPI 应用"""
    # 配置uvicorn
    config = uvicorn.Config(
        "ai_service:app", 
        host="0.0.0.0", 
        port=8086, 
        log_level="info",
        reload=True  # 开启热重载
    )
    server = uvicorn.Server(config)
    
    # 启动应用前初始化模型
    logger.info("准备启动AI服务...")
    if not initialize_model():
        logger.error("模型初始化失败，服务无法启动")
        return # 提前退出
        
    logger.info("模型初始化成功，启动Web服务...")
    await server.serve()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())