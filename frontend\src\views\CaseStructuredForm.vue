<template>
  <div class="structured-form-container">
    <div class="page-header">
      <div class="header-content">
        <h2>病例信息填写</h2>
      </div>
      <div class="header-actions">
        <!-- 删除顶部的按钮，只保留底部的按钮 -->
      </div>
    </div>

    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>

    <div v-else class="form-content">
      <div class="form-note">
        <el-alert
          title="注意：带有 * 标记的字段为必填项，其他字段为选填项。治疗与注意事项中的四个字段必须填写。"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
      <el-form ref="caseForm" :model="formData" label-position="top" :rules="rules">
        
        <!-- 基础信息 -->
        <div class="form-section">
          <h3 class="section-title">基础信息</h3>
          <div class="adaptive-fields-container">
            <el-form-item label="患者年龄" prop="patientAge">
              <el-input-number v-model="formData.patientAge" :min="0" :max="120" />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="formData.gender">
                <el-radio label="男">男</el-radio>
                <el-radio label="女">女</el-radio>
              </el-radio-group>
            </el-form-item>
             <el-form-item label="类型" prop="originType">
              <el-radio-group v-model="formData.originType">
                <el-radio label="先天性">先天性</el-radio>
                <el-radio label="后天性">后天性</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="病变部位" prop="bodyPart">
              <el-select v-model="formData.bodyPart" placeholder="请选择病变部位" filterable>
                <el-option
                  v-for="item in partOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="颜色" prop="color">
              <el-select v-model="formData.color" placeholder="请选择病灶颜色" filterable>
                <el-option
                  v-for="item in colorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="血管质地" prop="vesselTexture">
              <el-select v-model="formData.vesselTexture" placeholder="请选择血管质地">
                <el-option label="质软" value="soft" />
                <el-option label="质韧" value="elastic" />
                <el-option label="质硬" value="hard" />
                <el-option label="囊性" value="cystic" />
                <el-option label="可压缩" value="compressible" />
              </el-select>
            </el-form-item>
          </div>
        </div>
        
        <!-- 治疗与注意事项 -->
        <div class="form-section">
          <h3 class="section-title">
            治疗与注意事项 <span class="required-indicator">*</span>
            <el-tag v-if="isLoadingRecommendation" type="warning" size="small">
              <i class="el-icon-loading"></i> 正在获取AI建议...
            </el-tag>
          </h3>
          <div class="form-grid">
            <el-form-item label="治疗建议 *" prop="treatmentSuggestion">
              <el-input v-model="formData.treatmentSuggestion" type="textarea" :rows="5" placeholder="医生填写的最终治疗方案"></el-input>
            </el-form-item>
            <el-form-item label="注意事项 *" prop="precautions">
              <el-input v-model="formData.precautions" type="textarea" :rows="5" placeholder="需要患者注意的事项（如破溃出血处理、日常注意等）"></el-input>
            </el-form-item>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="saveData('REVIEWED')" :loading="savingDraft">保存草稿</el-button>
          <el-button type="success" @click="submitForm" :loading="submitting">提交完成</el-button>
          <el-button v-if="saveError" type="warning" @click="retrySave">重试保存</el-button>
        </div>
        
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import api from '@/utils/api';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import { formatDate } from '../utils/formatters';
import { getImageUrl } from '../utils/imageHelper';
import { ElMessage } from 'element-plus';
import axios from 'axios';

export default {
  name: 'CaseStructuredForm',
  components: {
    ConfirmDialog
  },
  data() {
    return {
      loading: true,
      imageId: null,
      diagnosisId: null,
      saveDialogVisible: false,
      pollingInterval: null,
      isLoadingRecommendation: false,
      savingDraft: false,
      submitting: false,
      saveError: false,
      partOptions: [
        { value: '唇部', label: '唇部' },
        { value: '脑部', label: '脑部' },
        { value: '颈部', label: '颈部' },
        { value: '脸部', label: '脸部' },
        { value: '掌部', label: '掌部' },
        { value: '手臂', label: '手臂' },
        { value: '胸部', label: '胸部' },
        { value: '腹部', label: '腹部' },
        { value: '腿部', label: '腿部' },
        { value: '阴部', label: '阴部' },
        { value: '背部', label: '背部' },
        { value: '耳部', label: '耳部' },
        { value: '枕部', label: '枕部' },
        { value: '眼部', label: '眼部' },
        { value: '脚底', label: '脚底' },
        { value: '脚背', label: '脚背' },
        { value: '肩部', label: '肩部' },
        { value: '舌部', label: '舌部' },
        { value: '屁股', label: '屁股' },
        { value: '口腔', label: '口腔' },
        { value: '鼻部', label: '鼻部' }
      ],
      colorOptions: [
        { value: '褐色', label: '褐色' },
        { value: '黑色', label: '黑色' },
        { value: '红色', label: '红色' },
        { value: '白色', label: '白色' },
        { value: '正常', label: '正常' },
        { value: '玫红', label: '玫红' }
      ],
      formData: {
        patientAge: null,
        gender: '',
        originType: '',
        bodyPart: '',
        color: '',
        vesselTexture: '',
        treatmentSuggestion: '',
        precautions: ''
      },
      rules: {
        patientAge: [{ required: false, message: '请输入患者年龄', trigger: 'blur' }],
        gender: [{ required: false, message: '请选择性别', trigger: 'change' }],
        originType: [{ required: false, message: '请选择类型', trigger: 'change' }],
        bodyPart: [{ required: false, message: '请输入病变部位', trigger: 'blur' }],
        color: [{ required: false, message: '请输入颜色', trigger: 'blur' }],
        // 治疗与注意事项必填字段
        treatmentSuggestion: [{ required: true, message: '请填写治疗建议', trigger: 'blur' }],
        precautions: [{ required: true, message: '请填写注意事项', trigger: 'blur' }]
      },
      availableEndpoints: []
    };
  },
  computed: {
    ...mapGetters(['getAnnotationProgress'])
  },
  created() {
    // 检查URL中是否有诊断ID参数 (同时检查query和params，增强兼容性)
    const diagnosisId = this.$route.query.diagnosisId || this.$route.params.id || this.$route.query.imageId;
    if (diagnosisId) {
      console.log('从URL获取到诊断/图像ID:', diagnosisId);
      this.diagnosisId = diagnosisId;
      this.imageId = diagnosisId;
      // 将数据加载推迟到下一个Tick，以避免与应用初始化（如身份验证）产生竞态条件
      this.$nextTick(() => {
        // 先检查可用的API端点
        this.checkAvailableEndpoints(diagnosisId).then(() => {
          this.loadCaseData(diagnosisId);
        });
      });
    } else {
      this.loading = false;
      this.$message.error("缺少诊断ID，无法加载病例数据。");
    }
  },
  methods: {
    ...mapActions(['saveProgress', 'completeAnnotation']),
    formatDate,
    
    // 检查可用的API端点
    async checkAvailableEndpoints(diagnosisId) {
      console.log('检查可用的API端点...');
      const endpoints = [
        `/medical/api/hemangioma-diagnoses/${diagnosisId}`,
        `/medical/api/images/${diagnosisId}/structured-form`
      ];
      
      // 存储可用的端点
      this.availableEndpoints = [];
      
      for (const endpoint of endpoints) {
        try {
          // 使用OPTIONS请求检查端点是否支持
          const response = await axios.options(endpoint);
          console.log(`端点 ${endpoint} 可用，支持的方法:`, response.headers['allow'] || 'GET, POST, PUT');
          this.availableEndpoints.push({
            url: endpoint,
            methods: (response.headers['allow'] || 'GET, POST, PUT').split(', ')
          });
        } catch (error) {
          // 如果OPTIONS请求失败，尝试HEAD请求
          try {
            const headResponse = await axios.head(endpoint);
            console.log(`端点 ${endpoint} 可用 (通过HEAD请求)`);
            this.availableEndpoints.push({
              url: endpoint,
              methods: ['GET']  // 假设至少支持GET
            });
          } catch (headError) {
            console.log(`端点 ${endpoint} 不可用:`, error.message);
          }
        }
      }
      
      console.log('可用的API端点:', this.availableEndpoints);
    },
    async loadCaseData(diagnosisId) {
      this.loading = true;
      console.log('开始加载病例数据, 诊断ID:', diagnosisId);
      
      // 添加时间戳以避免缓存问题
      const timestamp = Date.now();
      let success = false;
      
      // 准备认证头
      const headers = {};
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const token = localStorage.getItem('token');
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      if (user && (user.id || user.customId)) {
        headers['X-User-Id'] = user.id || user.customId;
        headers['X-User-Role'] = user.role || 'USER';
      }
      
      // 尝试不同的API路径获取数据
      const apiPaths = [
        `/medical/api/hemangioma-diagnoses/${diagnosisId}`,
        `/medical/api/diagnoses/${diagnosisId}`,
        `/api/hemangioma-diagnoses/${diagnosisId}`,
        `/hemangioma-diagnoses/${diagnosisId}`,
        `/api/diagnoses/${diagnosisId}`
        // 移除不存在的API路径，避免404错误
        // `/medical/api/images/${diagnosisId}/structured-form`
      ];
      
      for (const path of apiPaths) {
        if (success) break;
        
        try {
          console.log(`尝试从路径获取数据: ${path}?t=${timestamp}`);
          const response = await axios.get(`${path}?t=${timestamp}`, { headers });
          
          if (response && response.data) {
            const data = response.data;
            console.log('成功获取诊断数据:', data);
            this.diagnosisId = data.id || diagnosisId;
            
            // 更新表单数据
            const newFormData = {
              // 先设置默认值
              patientAge: null,
              gender: '',
              originType: '',
              bodyPart: '',
              color: '',
              vesselTexture: '',
              treatmentSuggestion: '',
              precautions: ''
            };
            
            // 然后用API返回的数据覆盖默认值
            Object.keys(newFormData).forEach(key => {
              if (data[key] !== undefined) {
                newFormData[key] = data[key];
              }
            });
            
            // 特殊处理可能的不同命名
            if (data.patient_age !== undefined) newFormData.patientAge = data.patient_age;
            if (data.vessel_texture !== undefined) newFormData.vesselTexture = data.vessel_texture;
            if (data.treatment_suggestion !== undefined) newFormData.treatmentSuggestion = data.treatment_suggestion;
            if (data.precautions !== undefined) newFormData.precautions = data.precautions;
            
            // 替换整个表单数据对象，确保Vue能检测到变化
            this.formData = newFormData;
            
            // 强制组件重新渲染
            this.$nextTick(() => {
              console.log('表单数据已更新，强制重新渲染:', this.formData);
              this.$forceUpdate();
            });
            
            this.$message.success("病例数据已加载，请核对并完善。");
            success = true;
            
            // 如果缺少大模型生成的建议字段，开始轮询获取
            if (!data.treatmentSuggestion || !data.precautions) {
              this.isLoadingRecommendation = true;
              this.startPolling(diagnosisId);
            }
            
            break; // 成功获取数据后跳出循环
          }
        } catch (error) {
          console.error(`从路径 ${path} 加载失败:`, error);
        }
      }
      
      // 如果所有API路径都失败，尝试使用api.get方法
      if (!success) {
        try {
          console.log('所有直接路径均失败，尝试使用api.get方法');
          const response = await api.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);
          if (response && response.data) {
            const data = response.data;
            console.log('使用api.get成功获取数据:', data);
            
            // 更新表单数据（同上）
            const newFormData = { ...this.formData };
            
            // 从API响应中提取数据
            Object.keys(newFormData).forEach(key => {
              if (data[key] !== undefined) {
                newFormData[key] = data[key];
              }
            });
            
            // 特殊处理可能的不同命名
            if (data.patient_age !== undefined) newFormData.patientAge = data.patient_age;
            if (data.vessel_texture !== undefined) newFormData.vesselTexture = data.vessel_texture;
            if (data.treatment_suggestion !== undefined) newFormData.treatmentSuggestion = data.treatment_suggestion;
            if (data.precautions !== undefined) newFormData.precautions = data.precautions;
            
            this.formData = newFormData;
            this.$forceUpdate();
            this.$message.success("病例数据已加载，请核对并完善。");
            success = true;
          }
        } catch (apiError) {
          console.error('使用api.get加载失败:', apiError);
        }
      }
      
      // 如果所有尝试都失败，检查是否有本地备份
      if (!success) {
        try {
          const backupKey = `formData_backup_${diagnosisId}`;
          const backupData = localStorage.getItem(backupKey);
          
          if (backupData) {
            console.log('找到本地备份数据，尝试恢复');
            const parsedBackup = JSON.parse(backupData);
            this.formData = parsedBackup;
            this.$forceUpdate();
            this.$message.warning('从本地备份恢复了表单数据，请注意保存');
            success = true;
          }
        } catch (backupError) {
          console.error('恢复本地备份失败:', backupError);
        }
      }
      
      // 如果所有尝试都失败，提供默认值
      if (!success) {
        console.error('所有数据获取尝试均失败，提供默认值');
        this.provideDefaultValues();
        this.$message.error('无法从服务器加载数据，已提供默认值，请手动填写。');
      }
      
      this.loading = false;
    },
    async submitForm() {
      try {
        // 表单验证
        await this.$refs.caseForm.validate();
        
        // 额外检查治疗与注意事项的必填字段
        const requiredFields = [
          'treatmentSuggestion', 
          'precautions'
        ];
        
        const missingFields = requiredFields.filter(field => !this.formData[field]);
        
        if (missingFields.length > 0) {
          // 构建错误消息
          const fieldNames = {
            treatmentSuggestion: '治疗建议',
            precautions: '注意事项'
          };
          
          const missingFieldNames = missingFields.map(field => fieldNames[field]);
          this.$message.warning(`请填写必填项: ${missingFieldNames.join('、')}`);
          return;
        }

        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const userRole = user ? user.role : null;

        let finalStatus;
        if (userRole === 'ADMIN' || userRole === 'REVIEWER') {
          finalStatus = 'APPROVED'; // 已通过
        } else {
          finalStatus = 'SUBMITTED'; // 待审核
        }
        
        await this.saveData(finalStatus);
      } catch (error) {
        this.$message.warning('表单验证失败，请检查必填项。');
        return;
      }
    },
    async saveData(status) {
      if (!this.diagnosisId) {
        this.$message.error("未找到诊断记录ID，无法保存。");
        return;
      }

      if (status === 'SUBMITTED' || status === 'APPROVED') {
        this.submitting = true;
      } else {
        this.savingDraft = true;
      }
      this.saveError = false;
      
      console.log(`开始保存数据，诊断ID: ${this.diagnosisId}, 状态: ${status}`);
      const loading = this.$loading({ lock: true, text: '正在保存...' });
      
      const headers = { 
        'Content-Type': 'application/json', 
        'Accept': 'application/json' 
      };
      const token = localStorage.getItem('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user && (user.id || user.customId)) {
        headers['X-User-Id'] = user.id || user.customId;
        headers['X-User-Role'] = user.role || 'USER';
      }

      const formDataWithAction = {
        ...this.formData,
        status: status
      };

      const url = `/medical/api/hemangioma-diagnoses/${this.diagnosisId}`;
      
      try {
        console.log(`尝试使用 PUT 方法保存到端点: ${url}`);
        const response = await axios.put(url, formDataWithAction, { headers });
        console.log(`成功保存数据, 响应:`, response.data);

        loading.close();
        this.savingDraft = false;
        this.submitting = false;

        localStorage.removeItem(`formData_backup_${this.diagnosisId}`);
        
        let message;
        if (status === 'APPROVED') {
          message = "病例信息已直接审核通过！";
        } else if (status === 'SUBMITTED') {
          message = "病例信息已提交，等待审核！";
        } else { // REVIEWED
          message = "信息已保存并标记为已标注！";
        }
        this.$message.success(message);

        setTimeout(() => {
          this.$router.push('/app/dashboard');
        }, 1500);

      } catch (error) {
        console.error(`保存到端点 ${url} 失败:`, error.response ? error.response.data : error.message);
        
        loading.close();
        this.savingDraft = false;
        this.submitting = false;
        this.saveError = true;
        
        const errorMessage = error.response?.data?.error || error.message || "未知错误";
        this.$message.error(`保存失败: ${errorMessage}`);
        
        try {
          localStorage.setItem(`formData_backup_${this.diagnosisId}`, JSON.stringify(this.formData));
          this.$message.warning('已在本地备份您的表单数据，可点击"重试保存"按钮再次尝试。');
        } catch (e) {
          console.error("本地备份也失败:", e);
        }
      }
    },
    returnToAnnotation() {
      this.$router.back();
    },
    // 重试保存
    retrySave() {
      this.saveData('REVIEWED');
    },
    // 获取用于显示的图像URL，支持离线模式
    getImageUrl(path) {
      // 检查是否为离线模式URL
      if (path && path.startsWith('blob:')) {
        return path;
      }
      
      // 检查localStorage中是否有离线图片
      const imageId = this.imageId;
      if (imageId) {
        const offlineImage = localStorage.getItem(`offline_image_${imageId}`);
        if (offlineImage) {
          console.log('表单页面：使用本地存储的离线图片');
          return offlineImage;
        }
      }
      
      // 使用原始URL
      return getImageUrl(path);
    },
    // 加载图像数据
    fetchImageData() {
      if (!this.imageId) {
        this.$message.error('未指定图像ID，无法加载数据');
        return;
      }
      
      // 检查是否为测试模式或离线模式
      const isTestMode = this.$route.query.testMode === 'true';
      const offlineMode = localStorage.getItem('offlineMode') === 'true';
      
      if (isTestMode || offlineMode) {
        console.log('使用测试模式或离线模式加载数据');
        
        // 尝试从localStorage获取离线图片
        const offlineImage = localStorage.getItem(`offline_image_${this.imageId}`);
        
        // 创建模拟数据
        this.imageData = {
          id: parseInt(this.imageId),
          original_name: `离线图片_${this.imageId}.jpg`,
          created_at: new Date().toISOString(),
          path: offlineImage || ''
        };
        
        // 如果有之前保存的进度，恢复已填写的表单数据
        const savedProgress = this.getAnnotationProgress;
        if (savedProgress && savedProgress.formData) {
          this.formData = { ...this.formData, ...savedProgress.formData };
          this.$message.info('已恢复之前填写的表单数据');
        }
        
        this.loaded = true;
        return;
      }
      
      // 正常API请求
      this.loading = true;
      api.images.getOne(this.imageId)
        .then(response => {
          this.imageData = response.data;
          this.loading = false;
          this.loaded = true;
          
          // 从后端返回的独立字段中映射数据到表单
          this.mapImageDataToForm(this.imageData);
        })
        .catch(error => {
          console.error('加载图像数据失败', error);
          
          // 如果API失败但是离线模式启用，使用模拟数据
          if (localStorage.getItem('offlineMode') === 'true') {
            console.warn('API失败但启用了离线模式，使用模拟数据');
            
            const offlineImage = localStorage.getItem(`offline_image_${this.imageId}`);
            
            if (offlineImage) {
              this.imageData = {
                id: parseInt(this.imageId),
                original_name: `离线图片_${this.imageId}.jpg`,
                created_at: new Date().toISOString(),
                path: offlineImage
              };
              
              this.$message.warning('API请求失败，使用离线模式显示表单');
              this.loaded = true;
            } else {
              this.error = '无法获取图像数据';
              this.$message.error('加载失败，无法获取图像数据');
            }
          } else {
            this.error = error.response?.data || error.message;
            this.$message.error('加载失败: ' + this.error);
          }
          
          this.loading = false;
        });
    },
    // 深度合并表单数据，避免结构不一致问题
    mergeFormData(savedData) {
      const result = { ...this.formData };
      
      // 遍历保存的数据，将其合并到默认表单结构中
      for (const key in savedData) {
        if (Object.prototype.hasOwnProperty.call(savedData, key)) {
          if (typeof savedData[key] === 'object' && savedData[key] !== null && 
              typeof result[key] === 'object' && result[key] !== null) {
            // 递归合并嵌套对象
            result[key] = this.mergeFormData(savedData[key]);
          } else {
            // 直接替换值
            result[key] = savedData[key];
          }
        }
      }
      
      return result;
    },
    // 表单提交
    handleSubmit() {
      this.$refs.caseForm.validate(valid => {
        if (!valid) {
          this.$message.warning('表单验证失败，请检查必填项');
          return;
        }
        
        this.saving = true;
        
        // 检查是否为离线模式
        const offlineMode = localStorage.getItem('offlineMode') === 'true';
        
        if (offlineMode) {
          console.log('离线模式：模拟保存表单数据');
          
          // 存储到localStorage
          try {
            localStorage.setItem(`offline_form_${this.imageId}`, JSON.stringify(this.formData));
            
            // 更新进度
            this.completeAnnotation();
            
            this.$message.success('表单已保存（离线模式）');
            setTimeout(() => {
              this.$router.push('/app/dashboard');
            }, 1000);
          } catch (e) {
            console.error('离线保存表单失败', e);
            this.$message.error('保存失败: ' + e.message);
          }
          
          this.saving = false;
          return;
        }
        
        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '提交数据中...',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, 0.7)'
        });
        
        // 添加action参数，指定为提交操作
        const formDataWithAction = {
          ...this.formData,
          action: 'submit', // 指定为提交操作，状态将设为SUBMITTED(已提交)
          timestamp: new Date().toISOString() // 添加当前时间戳，使后端可以使用前端时间
        };
        
        // 添加用户信息，确保后端能正确识别角色
        try {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const user = JSON.parse(userStr);
            // 直接在表单数据中包含用户角色信息
            if (user.role) {
              formDataWithAction.userRole = user.role;
              console.log('添加用户角色到表单数据:', user.role);
            }
            if (user.id) {
              formDataWithAction.userId = user.id;
            }
            if (user.customId) {
              formDataWithAction.userCustomId = user.customId;
            }
          }
        } catch (e) {
          console.error('获取用户信息失败:', e);
        }
        
        console.log('提交表单数据:', formDataWithAction);
        
        // 正常API请求（已改为简单请求，不带自定义头和 _role 参数）
        api.images.saveStructuredFormData(this.imageId, formDataWithAction)
          .then(response => {
            loading.close();
            this.$message.success('表单提交成功');
            // 完成标注流程，清除进度
            this.completeAnnotation();
            // 延迟跳转到首页
            setTimeout(() => {
              this.$router.push('/app/dashboard');
            }, 1000);
          })
          .catch(error => {
            loading.close();
            console.error('提交表单失败', error);
            
            const errorMessage = error.response?.data || error.message || '未知错误';
            this.$message.error('提交失败: ' + errorMessage);
            
            // 如果API失败，使用离线模式
            if (!offlineMode) {
              this.$confirm('提交到服务器失败，是否启用离线模式?', '提交失败', {
                confirmButtonText: '启用离线模式',
                cancelButtonText: '再试一次',
                type: 'warning'
              }).then(() => {
                localStorage.setItem('offlineMode', 'true');
                this.handleSubmit(); // 递归调用，使用离线模式
              }).catch(() => {
                this.$message.info('请稍后重试');
              });
            }
          })
          .finally(() => {
            this.saving = false;
          });
      });
    },
    prefilFormFromAnnotations() {
      // 这里可以根据标注数据预填其他表单字段
      // 例如根据标注位置自动推断病变部位等
    },
    // 保存表单数据到进度中
    saveFormProgress() {
      this.saveProgress({
        step: 2, // 病例信息填写步骤
        imageId: this.imageId,
        formData: this.formData
      });
    },
    // 保存并退出
    saveAndExit() {
      // 保存当前表单数据
      this.saveFormProgress();
      
      // 显示确认对话框
      this.saveDialogVisible = true;
    },
    // 处理保留填写进度
    handleSaveProgress() {
      // 保存会话状态，防止401错误导致重定向到登录页面
      sessionStorage.setItem('isNavigatingAfterSave', 'true');
      sessionStorage.setItem('allowFormOperation', 'true');
      
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '保存数据中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      });
      
      try {
        // 添加action参数，指定为保存操作
        const formDataWithAction = {
          ...this.formData,
          action: 'save', // 指定为保存操作，状态将设为REVIEWED(已标注)
          timestamp: new Date().toISOString() // 添加当前时间戳，使后端可以使用前端时间
        };
        
        // 调用API将数据保存到数据库（已改为简单请求，不带自定义头和 _role 参数）
        api.images.saveStructuredFormData(this.imageId, formDataWithAction)
          .then(response => {
            this.$message.success('表单数据已保存到数据库');
            sessionStorage.setItem('navigatingFromForm', 'true');
            try {
              this.$router.push('/app/dashboard');
            } catch (e) {
              console.error('路由导航错误，尝试使用window.location', e);
              window.location.href = '/app/dashboard';
            }
          })
          .catch(async error => {
            console.error('保存表单数据失败:', error);
            this.$message.error('保存失败: ' + (error.response?.data || error.message || '未知错误'));
            // 1. 本地保存表单数据
            try {
              localStorage.setItem(`offline_form_${this.imageId}`, JSON.stringify(this.formData));
              this.$message.warning('已将表单数据保存到本地，稍后可继续填写');
            } catch (e) {
              console.error('本地保存表单失败', e);
            }
            // 2. 允许用户选择是否继续
            try {
              await this.$confirm('保存到服务器失败，是否仅本地保存并返回工作台？', '保存失败', {
                confirmButtonText: '继续',
                cancelButtonText: '留在当前页面',
                type: 'warning'
              });
              // 用户选择继续，跳转
              this.$router.push('/app/dashboard');
            } catch (e) {
              // 用户选择留在当前页面
              this.$message.info('请稍后重试或检查网络');
            }
          })
          .finally(() => {
            loading.close();
          });
      } catch (error) {
        console.error('处理保存进度时出错:', error);
        this.$message.error('保存失败: ' + error.message);
        loading.close();
        
        // 如果出错，也尝试重定向到工作台
        window.location.href = '/app/dashboard';
      }
    },
    // 处理丢弃填写进度
    handleDiscardProgress() {
      this.completeAnnotation();
      this.$message.info('已清除填写进度');
      this.$router.push('/cases/new');
    },
    // 返回上一步（图像标注页面）
    returnToAnnotation() {
      // 保存当前表单数据
      this.saveFormProgress();
      
      // 返回图像标注页面
      this.$router.push({
        path: '/case/' + this.diagnosisId + '/annotate-and-form',
        query: { imageId: this.imageId }
      });
    },
    // 添加用户认证检查方法
    checkUserAuthentication() {
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user) {
        // 尝试从会话存储恢复用户信息
        const preservedUser = sessionStorage.getItem('preservedUser');
        if (preservedUser) {
          console.log('尝试使用保存的用户信息恢复会话');
          localStorage.setItem('user', preservedUser);
          sessionStorage.removeItem('preservedUser');
        }
      }
    },
    // 从后端返回的独立字段中映射数据到表单
    mapImageDataToForm(imageData) {
      console.log('原始图像数据:', imageData);
      
      if (!imageData) {
        console.warn('未找到图像数据，无法映射到表单');
        return;
      }
      
      // 处理下划线命名的字段 - 创建兼容对象
      const data = { ...imageData };
      
      // 可能的下划线风格字段映射
      const fieldMappings = {
        'lesion_location': 'lesionLocation',
        'patient_age': 'patientAge',
        'disease_stage': 'diseaseStage',
        'morphological_features': 'morphologicalFeatures',
        'blood_flow': 'bloodFlow',
        'symptom_details': 'symptomDetails',
        'complication_details': 'complicationDetails',
        'diagnosis_category': 'diagnosisCategory', 
        'diagnosis_icd_code': 'diagnosisIcdCode',
        'treatment_priority': 'treatmentPriority',
        'treatment_plan': 'treatmentPlan',
        'recommended_treatment': 'recommendedTreatment',
        'follow_up_schedule': 'followUpSchedule',
        'prognosis_rating': 'prognosisRating',
        'patient_education': 'patientEducation'
      };
      
      // 创建兼容对象，支持两种字段命名风格
      Object.keys(fieldMappings).forEach(key => {
        if (data[key] !== undefined && data[fieldMappings[key]] === undefined) {
          data[fieldMappings[key]] = data[key];
          console.log(`字段映射: ${key} -> ${fieldMappings[key]}`);
        }
      });
      
      // 基础信息映射
      if (data.lesionLocation) {
        // 如果是单个字符串，尝试按路径拆分
        if (typeof data.lesionLocation === 'string') {
          const locationParts = data.lesionLocation.split('/').filter(p => p.trim());
          if (locationParts.length > 0) {
            this.formData.bodyPart = locationParts;
            console.log('设置病变部位(字符串分割):', this.formData.bodyPart);
          }
        } else if (Array.isArray(data.lesionLocation)) {
          this.formData.bodyPart = data.lesionLocation;
          console.log('设置病变部位(数组):', this.formData.bodyPart);
        }
      }
      
      // 尝试从lesion_location字段读取(下划线格式)
      if (!this.formData.bodyPart || this.formData.bodyPart.length === 0) {
        if (imageData.lesion_location) {
          if (typeof imageData.lesion_location === 'string') {
            const locationParts = imageData.lesion_location.split('/').filter(p => p.trim());
            if (locationParts.length > 0) {
              this.formData.bodyPart = locationParts;
              console.log('设置病变部位(下划线字段):', this.formData.bodyPart);
            }
          }
        }
      }
      
      if (data.patientAge !== null && data.patientAge !== undefined) {
        this.formData.patientAge = parseInt(data.patientAge);
        console.log('设置患者年龄:', this.formData.patientAge);
      }
      
      if (data.diseaseStage) {
        this.formData.stage = data.diseaseStage;
        console.log('设置病程阶段:', this.formData.stage);
      }
      
      // 形态与临床特征映射
      if (data.morphologicalFeatures) {
        const morphFeatures = data.morphologicalFeatures || '';
        // 如果包含冒号，前面部分是形态特征，后面是描述
        const colonIndex = morphFeatures.indexOf(':');
        
        if (colonIndex > -1) {
          const features = morphFeatures.substring(0, colonIndex).split('/');
          this.formData.morphology = features;
          this.formData.morphologyDesc = morphFeatures.substring(colonIndex + 1).trim();
        } else {
          // 如果没有冒号，全部作为形态特征
          this.formData.morphology = morphFeatures.split('/').filter(p => p.trim() !== '');
        }
        
        console.log('设置形态特征:', this.formData.morphology);
        console.log('设置形态描述:', this.formData.morphologyDesc);
      }
      
      if (data.bloodFlow) {
        this.formData.bloodFlow = data.bloodFlow;
        console.log('设置血流信号:', this.formData.bloodFlow);
      }
      
      if (data.symptoms) {
        const symptomsData = data.symptoms || '';
        const colonIndex = symptomsData.indexOf(':');
        
        if (colonIndex > -1) {
          const symptoms = symptomsData.substring(0, colonIndex).split('/');
          this.formData.symptoms = symptoms;
          this.formData.symptomDescription = symptomsData.substring(colonIndex + 1).trim();
        } else {
          this.formData.symptoms = symptomsData.split('/').filter(s => s.trim() !== '');
        }
        
        console.log('设置症状:', this.formData.symptoms);
      }
      
      if (data.symptomDetails) {
        this.formData.symptomDescription = data.symptomDetails;
        console.log('设置症状详情:', this.formData.symptomDescription);
      }
      
      if (data.complications) {
        const complications = data.complications || '';
        this.formData.complications = complications.split('/').filter(c => c.trim() !== '');
        console.log('设置并发症:', this.formData.complications);
      }
      
      if (data.complicationDetails) {
        this.formData.complicationDetails = data.complicationDetails;
        console.log('设置并发症详情:', this.formData.complicationDetails);
      }
      
      // 诊断与治疗建议映射
      if (data.diagnosisCategory) {
        this.formData.diagnosis = data.diagnosisCategory;
        console.log('设置诊断结论:', this.formData.diagnosis);
      }
      
      if (data.diagnosisIcdCode) {
        this.formData.diagnosisCode = data.diagnosisIcdCode;
        console.log('设置ICD编码:', this.formData.diagnosisCode);
      }
      
      if (data.treatmentPriority) {
        this.formData.treatmentPriority = data.treatmentPriority;
        console.log('设置治疗优先级:', this.formData.treatmentPriority);
      }
      
      if (data.treatmentPlan) {
        const treatmentPlan = data.treatmentPlan || '';
        this.formData.treatmentPlan = treatmentPlan.split('/').filter(p => p.trim() !== '');
        console.log('设置治疗方案:', this.formData.treatmentPlan);
      }
      
      if (data.recommendedTreatment) {
        this.formData.treatmentSuggestion = data.recommendedTreatment;
        console.log('设置治疗详情:', this.formData.treatmentSuggestion);
      }
      
      if (data.contraindications) {
        this.formData.contraindications = data.contraindications;
        console.log('设置禁忌症:', this.formData.contraindications);
      }
      
      // 随访与预后映射
      if (data.followUpSchedule) {
        const followUp = data.followUpSchedule;
        
        // 检查是否是标准选项之一
        const standardOptions = ['1个月', '3个月', '6个月'];
        if (standardOptions.includes(followUp)) {
          this.formData.followUpPeriod = followUp;
        } else {
          this.formData.followUpPeriod = 'custom';
          this.formData.customFollowUp = followUp;
        }
        
        console.log('设置随访周期:', this.formData.followUpPeriod, this.formData.customFollowUp);
      }
      
      if (data.prognosisRating !== null && data.prognosisRating !== undefined) {
        this.formData.prognosisRating = parseInt(data.prognosisRating);
        console.log('设置预后评估:', this.formData.prognosisRating);
      }
      
      if (data.patientEducation) {
        const education = data.patientEducation || '';
        const colonIndex = education.indexOf(':');
        
        if (colonIndex > -1) {
          const eduItems = education.substring(0, colonIndex).split('/');
          this.formData.patientEducation = eduItems;
          this.formData.lifestyleAdjustment = education.substring(colonIndex + 1).trim();
        } else {
          this.formData.patientEducation = education.split('/').filter(e => e.trim() !== '');
        }
        
        console.log('设置患者教育:', this.formData.patientEducation);
        console.log('设置教育详情:', this.formData.lifestyleAdjustment);
      }
      
      // 转换字符串数组（如"[]"）为实际数组
      this.convertStringArrays();
      
      this.$message.success('已从数据库加载保存的表单数据');
    },
    // 处理字符串形式的数组
    convertStringArrays() {
      const arrayFields = ['morphology', 'symptoms', 'complications', 'treatmentPlan', 'patientEducation', 'bodyPart'];
      
      arrayFields.forEach(field => {
        const value = this.formData[field];
        
        // 跳过null或undefined值
        if (value === null || value === undefined) {
          return;
        }
        
        // 处理字符串形式的数组 "[item1,item2]"
        if (typeof value === 'string') {
          try {
            // 检查是否是JSON数组字符串
            if (value.trim().startsWith('[') && value.trim().endsWith(']')) {
            const arrayValue = JSON.parse(value);
            if (Array.isArray(arrayValue)) {
              this.formData[field] = arrayValue;
              console.log(`转换字段 ${field} 从字符串到数组:`, arrayValue);
            }
            }
            // 如果是逗号分隔的字符串，也转换为数组
            else if (value.includes(',')) {
              const arrayValue = value.split(',').map(item => item.trim());
              this.formData[field] = arrayValue;
              console.log(`转换字段 ${field} 从逗号分隔字符串到数组:`, arrayValue);
            }
          } catch (e) {
            console.error(`无法解析字符串数组 ${field}:`, value, e);
            // 确保字段至少是一个空数组，而不是无效的字符串
            if (!Array.isArray(this.formData[field])) {
              this.formData[field] = [];
          }
        }
      }
        // 确保字段是数组类型
        else if (!Array.isArray(this.formData[field])) {
          console.warn(`字段 ${field} 不是数组类型，设置为空数组`);
          this.formData[field] = [];
        }
      });
      
      // 输出处理后的完整表单数据用于调试
      console.log('处理后的表单数据:', JSON.stringify(this.formData, null, 2));
    },
    // 确保级联选择器数据正确显示
    ensureCascaderDataValid() {
      // 添加一个延时，确保在组件完全挂载后执行
      setTimeout(() => {
        console.log('检查级联选择器数据:', this.formData.bodyPart);
        
        // 如果bodyPart已有值但级联选择器没有正确显示
        if (this.formData.bodyPart && this.formData.bodyPart.length > 0) {
          // 检查级联选择器引用是否存在
          const cascader = this.$refs.bodyPartCascader;
          if (cascader) {
            if (!cascader.modelValue || cascader.modelValue.length === 0) {
            // 尝试重新设置值
            this.$nextTick(() => {
                try {
              // 深拷贝防止引用问题
              const locationValue = JSON.parse(JSON.stringify(this.formData.bodyPart));
              // 先清空再设置
              this.formData.bodyPart = [];
              this.$nextTick(() => {
                this.formData.bodyPart = locationValue;
                console.log('重新设置级联选择器值:', locationValue);
              });
                } catch (error) {
                  console.error('重设级联选择器值时出错:', error);
                }
            });
            }
          } else {
            console.log('级联选择器引用不存在，跳过重设值');
          }
        }
      }, 500);
    },
    // 开始轮询获取大模型生成的建议
    startPolling(diagnosisId) {
      console.log('开始轮询获取诊断建议，ID:', diagnosisId);
      this.pollingInterval = setInterval(() => {
        this.pollForResults(diagnosisId);
      }, 5000); // 每5秒轮询一次

      // 设置一个超时，比如5分钟后停止轮询，防止无限循环
      setTimeout(() => {
        if (this.pollingInterval) {
          clearInterval(this.pollingInterval);
          this.pollingInterval = null;
          if(this.isLoadingRecommendation) {
            this.$message.warning('获取诊断建议超时，将使用默认值。');
            this.isLoadingRecommendation = false;
            
            // 轮询超时，提供默认值
            this.provideDefaultValues();
          }
        }
      }, 60000); // 改为1分钟，加快测试速度
    },
    // 轮询获取诊断结果
    async pollForResults(diagnosisId) {
      try {
        console.log(`开始轮询端点: /medical/api/hemangioma-diagnoses/${diagnosisId}`);
        // 修正：直接使用 api.get，而不是 api.default.get
        const response = await api.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);
        
        if (!response || !response.data) {
          console.log("轮询返回空数据");
          return;
        }
        
        const data = response.data;
        console.log("轮询成功，获取到数据:", data);
        
        // 检查返回的数据是否包含AI建议
        const hasTreatmentSuggestion = data && (data.treatmentSuggestion || data.treatment_suggestion);
        const hasPrecautions = data && (data.precautions || data.precautions);
        
        if (hasTreatmentSuggestion || hasPrecautions) {
          // 更新模型数据，支持不同的命名风格，并确保不会设置为undefined
          if (hasTreatmentSuggestion) {
          this.formData.treatmentSuggestion = data.treatmentSuggestion || data.treatment_suggestion || this.formData.treatmentSuggestion;
          }
          
          if (hasPrecautions) {
          this.formData.precautions = data.precautions || data.precautions || this.formData.precautions;
          }
          
          // 如果所有必要的字段都已填充，停止轮询
          if (this.formData.treatmentSuggestion && 
              this.formData.precautions) {
            
            console.log("所有AI建议字段已填充，停止轮询");
          clearInterval(this.pollingInterval);
          this.pollingInterval = null;
            this.isLoadingRecommendation = false;
            this.$message.success('已获取AI生成的诊断建议');
          }
        }
      } catch (error) {
        console.error("轮询过程中出错:", error);
      }
    },
    async tryNextPath(nextIndex) {
        try {
            await this.$router.push({
                name: 'CaseStructuredForm',
                params: { caseId: this.$route.params.caseId, stepIndex: nextIndex }
            });
            this.currentStepIndex = nextIndex;
        } catch (error) {
            console.error('Navigation failed:', error);
            this.$message.error('表单步骤跳转失败');
        }
    },
    // 提供默认值的辅助方法
    provideDefaultValues() {
      console.log('无法从API获取数据，提供默认值');
      
      // 设置默认的治疗建议
      if (!this.formData.treatmentSuggestion) {
        this.formData.treatmentSuggestion = '根据患者情况，建议采用观察等待策略，定期复查。如有明显生长或症状加重，可考虑口服药物治疗。';
      }
      
      // 设置默认的注意事项
      if (!this.formData.precautions) {
        this.formData.precautions = '保持患处清洁干燥，避免外伤和摩擦。避免剧烈运动导致的碰撞。注意保暖，避免患处受凉。';
      }
      
      this.$message.warning('使用默认建议值，请根据实际情况修改');
    },
  },
  beforeDestroy() {
    // 组件销毁前清理轮询
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      console.log('清理轮询定时器');
    }
  },
};
</script>

<style scoped>
.structured-form-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 4px;
}

.form-note {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-content h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.form-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #EBEEF5;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
  color: #409EFF;
  padding-bottom: 8px;
  border-bottom: 1px dashed #EBEEF5;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

/* 自适应字段容器 - 灵活布局 */
.adaptive-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.adaptive-fields-container .el-form-item {
  flex: 1 1 150px; /* 灵活增长，最小宽度150px */
  min-width: 150px;
  max-width: calc(100% / 3 - 10px); /* 在较小屏幕上最多3个一行 */
  margin-bottom: 10px;
}

/* 大屏幕上最多6个一行 */
@media (min-width: 1200px) {
  .adaptive-fields-container .el-form-item {
    max-width: calc(100% / 6 - 13px);
  }
}

/* 中等屏幕上最多4个一行 */
@media (min-width: 900px) and (max-width: 1199px) {
  .adaptive-fields-container .el-form-item {
    max-width: calc(100% / 4 - 12px);
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 25px;
}

.form-actions button {
  min-width: 120px;
}

.required-indicator {
  color: #f56c6c;
  font-size: 12px;
  margin-left: 5px;
}
</style>
