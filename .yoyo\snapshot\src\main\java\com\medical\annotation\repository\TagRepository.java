package com.medical.annotation.repository;

import com.medical.annotation.model.Tag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface TagRepository extends JpaRepository<Tag, Long> {
    
    // 根据图像元数据ID查找标签
    List<Tag> findByMetadataId(Long metadataId);
    
    // 根据创建者ID查找标签
    List<Tag> findByCreatedBy(Integer createdBy);
    
    // 根据图像元数据ID和创建者ID查找标签
    List<Tag> findByMetadataIdAndCreatedBy(Long metadataId, Integer createdBy);
    
    // 根据图像元数据ID删除所有标签
    @Transactional
    void deleteByMetadataId(Long metadataId);
    
    // 为向后兼容添加重载方法
    default List<Tag> findByMetadataId(Integer metadataId) {
        return findByMetadataId(metadataId.longValue());
    }
    
    default List<Tag> findByMetadataIdAndCreatedBy(Integer metadataId, Integer createdBy) {
        return findByMetadataIdAndCreatedBy(metadataId.longValue(), createdBy);
    }
    
    @Transactional
    default void deleteByMetadataId(Integer metadataId) {
        deleteByMetadataId(metadataId.longValue());
    }
} 