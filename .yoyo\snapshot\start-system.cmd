@echo off
echo 启动血管瘤辅助系统...
echo.

REM 设置后端端口为8085
set BACKEND_PORT=8085

echo 检查后端服务是否已启动...
netstat -ano | findstr ":%BACKEND_PORT%" > nul
if %errorlevel% equ 0 (
    echo 后端服务已在端口%BACKEND_PORT%上运行
) else (
    echo 启动后端服务...
    start /b cmd /c "cd /d %~dp0 && mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dserver.port=%BACKEND_PORT%""
    echo 等待后端服务启动...
    timeout /t 10 /nobreak > nul
)

echo 检查前端开发服务器是否已启动...
netstat -ano | findstr ":8080" > nul
if %errorlevel% equ 0 (
    echo 前端开发服务器已在端口8080上运行
) else (
    echo 启动前端开发服务器...
    start /b cmd /c "cd /d %~dp0frontend && npm run serve"
    echo 等待前端服务器启动...
    timeout /t 5 /nobreak > nul
)

echo.
echo 系统启动完成！
echo 前端地址: http://localhost:8080
echo 后端地址: http://localhost:%BACKEND_PORT%
echo.
echo 请使用浏览器访问前端地址进行测试...
echo 如需退出，请关闭此窗口并手动停止相应的服务。

timeout /t 5 /nobreak > nul
start http://localhost:8080 