<template>
  <div v-if="hasAccess">
    <slot></slot>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { hasPermission } from '../../utils/permissions'

export default {
  name: 'PermissionControl',
  props: {
    // 需要的权限，可以是单个权限字符串或权限数组
    permission: {
      type: [String, Array],
      default: null
    },
    // 需要的角色，可以是单个角色字符串或角色数组
    role: {
      type: [String, Array],
      default: null
    },
    // 权限逻辑：'and'表示需要所有权限，'or'表示只需要其中一个
    logic: {
      type: String,
      default: 'or',
      validator: (value) => ['and', 'or'].includes(value)
    }
  },
  setup(props) {
    const store = useStore()
    
    // 获取当前用户角色
    const userRole = computed(() => store.getters.getUserRole)
    
    // 计算是否有访问权限
    const hasAccess = computed(() => {
      // 如果没有指定权限或角色，默认显示
      if (!props.permission && !props.role) {
        return true
      }
      
      // 如果未登录，不显示
      if (!userRole.value) {
        return false
      }
      
      // 检查角色
      if (props.role) {
        const roles = Array.isArray(props.role) ? props.role : [props.role]
        if (!roles.includes(userRole.value)) {
          return false
        }
      }
      
      // 检查权限
      if (props.permission) {
        const permissions = Array.isArray(props.permission) 
          ? props.permission 
          : [props.permission]
        
        if (props.logic === 'and') {
          // 必须拥有所有权限
          return permissions.every(perm => 
            hasPermission(userRole.value, perm)
          )
        } else {
          // 拥有任一权限即可
          return permissions.some(perm => 
            hasPermission(userRole.value, perm)
          )
        }
      }
      
      return true
    })
    
    return {
      hasAccess
    }
  }
}
</script> 