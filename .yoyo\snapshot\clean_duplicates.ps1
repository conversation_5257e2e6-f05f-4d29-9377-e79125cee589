# 清理重复标注图片的PowerShell脚本
# 该脚本查找同一metadataId的多个标注文件，只保留最新的一个

# 配置项
$processedDir = "F:\血管瘤辅助系统\medical_images\processed"
$dryRun = $false  # 设为$true只显示要删除的文件但不实际删除

# 切换到处理目录
Set-Location $processedDir

# 获取所有标注图片
Write-Host "正在扫描标注图片..."
$files = Get-ChildItem -Path . -Filter "annotated_*.jpg"
Write-Host "找到 $($files.Count) 个标注图片"

# 按metadataId分组
Write-Host "正在按ID分组..."
$groupedFiles = @{}

foreach ($file in $files) {
    # 从文件名中提取metadataId (annotated_ID_TIMESTAMP.jpg)
    if ($file.Name -match "annotated_(\d+)_(\d+)\.jpg") {
        $metadataId = $matches[1]
        $timestamp = $matches[2]
        
        if (-not $groupedFiles.ContainsKey($metadataId)) {
            $groupedFiles[$metadataId] = @()
        }
        
        $groupedFiles[$metadataId] += @{ 
            "FileName" = $file.Name
            "FullPath" = $file.FullName
            "Timestamp" = [long]$timestamp
        }
    }
}

# 处理每组文件，保留最新的一个
$totalToDelete = 0
$totalSize = 0

foreach ($metadataId in $groupedFiles.Keys) {
    $files = $groupedFiles[$metadataId]
    
    if ($files.Count -gt 1) {
        # 按时间戳排序，保留最新的
        $sortedFiles = $files | Sort-Object -Property Timestamp -Descending
        $latest = $sortedFiles[0]
        $toDelete = $sortedFiles[1..($sortedFiles.Count-1)]
        
        Write-Host "图像ID: $metadataId - 找到 $($files.Count) 个版本，保留最新的: $($latest.FileName)"
        
        foreach ($file in $toDelete) {
            $fileSize = (Get-Item $file.FullPath).Length
            $totalSize += $fileSize
            $totalToDelete++
            
            if ($dryRun) {
                Write-Host "  [DRY RUN] 将删除: $($file.FileName) ($([math]::Round($fileSize/1KB, 2)) KB)"
            } else {
                Write-Host "  删除: $($file.FileName) ($([math]::Round($fileSize/1KB, 2)) KB)"
                Remove-Item $file.FullPath -Force
            }
        }
    }
}

# 显示统计信息
$mode = if ($dryRun) { "[DRY RUN]" } else { "" }
Write-Host "完成！$mode 删除了 $totalToDelete 个重复文件，节省了 $([math]::Round($totalSize/1MB, 2)) MB 空间" 