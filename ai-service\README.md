# 血管瘤AI检测服务

这个服务使用YOLO v11模型通过ONNX Runtime提供血管瘤检测功能。

## 系统架构

整个系统由三部分组成：

1. **前端应用** - 端口8080
   - 提供用户界面
   - 处理用户交互

2. **后端API服务** - 端口8085
   - Spring Boot应用
   - 处理业务逻辑和数据存储
   - 通过HTTP调用AI服务

3. **AI服务** - 端口8086
   - 使用FastAPI和ONNX Runtime
   - 提供血管瘤检测功能
   - 可独立扩展和优化

## 安装要求

### AI服务依赖

- Python 3.8+
- 依赖包：见`ai_requirements.txt`

```bash
pip install -r ai_requirements.txt
```

## 启动服务

### 启动AI服务

Windows:
```
start_ai_service.bat
```

Linux/Mac:
```bash
python ai_service.py
```

服务将在端口8086上运行。

### 配置Nginx反向代理

已更新的`nginx.conf`配置了三个服务的反向代理：
- `/` -> 前端服务 (8080)
- `/medical/api/` -> 后端API服务 (8085)
- `/ai/` -> AI服务 (8086)

## API使用

### 直接调用AI服务

**检测图像中的血管瘤:**
```
POST http://localhost:8086/detect
```

参数:
- `file`: 图像文件 (multipart/form-data)

响应:
```json
{
  "detected": true,
  "confidence": 0.92,
  "boxes": [
    {
      "class_id": 0,
      "class_name": "血管瘤",
      "confidence": 0.92,
      "bbox": [120, 50, 250, 180],
      "width": 130,
      "height": 130
    }
  ],
  "processing_time": 0.156,
  "image_dimensions": {
    "width": 640,
    "height": 480
  }
}
```

### 通过Nginx反向代理调用

**检测图像中的血管瘤:**
```
POST http://localhost/ai/detect
```

### 从Java后端调用

已创建`HemangiomaDiagnosisService`类，可以从Spring Boot应用调用AI服务：

```java
@Autowired
private HemangiomaDiagnosisService diagnosisService;

// 在控制器中使用
@PostMapping("/detect-hemangioma")
public ResponseEntity<?> detectHemangioma(@RequestParam("image") MultipartFile imageFile) {
    try {
        Map<String, Object> result = diagnosisService.detectHemangioma(imageFile);
        return ResponseEntity.ok(result);
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("处理失败: " + e.getMessage());
    }
}
```

## 健康检查

```
GET http://localhost:8086/health
```

响应:
```json
{
  "status": "healthy",
  "model_loaded": true
}
```

## 自定义模型

如需使用其他YOLO模型，请修改以下内容：

1. 替换`best.onnx`文件
2. 根据需要更新`CLASS_NAMES`列表
3. 根据模型输出格式调整`postprocess_results`函数 