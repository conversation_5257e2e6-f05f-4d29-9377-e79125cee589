<template>
  <div class="annotation-container">
    <div class="page-header">
      <h2>图像标注</h2>
      <el-button type="primary" size="small" @click="refreshData">
        <el-icon><Refresh /></el-icon> 刷新数据
      </el-button>
    </div>

    <div class="main-content">
      <!-- 标注工具栏 -->
      <div class="toolbar">
        <div class="tool-section">
          <h3>标签分类</h3>
          <!-- 第一级：主要分类 -->
          <el-select
            v-model="selectedMainCategory"
            placeholder="请选择主要分类"
            style="width: 100%; margin-bottom: 10px;"
            @change="onMainCategoryChange"
          >
            <el-option label="真性血管肿瘤" value="真性血管肿瘤"></el-option>
            <el-option label="血管畸形" value="血管畸形"></el-option>
            <el-option label="血管假瘤/易混淆病变" value="血管假瘤/易混淆病变"></el-option>
          </el-select>

          <!-- 第二级：具体类型 -->
          <el-select
            v-model="selectedTag"
            placeholder="请选择具体类型"
            style="width: 100%"
            :disabled="!selectedMainCategory"
          >
            <el-option
              v-for="tag in availableSubCategories"
              :key="tag.value"
              :label="tag.label"
              :value="tag.value"
            ></el-option>
          </el-select>
        </div>
        
        <div class="tool-section">
          <h3>标注工具</h3>
          <el-radio-group v-model="currentTool" style="display: block; margin-top: 10px;">
            <el-radio label="rectangle">矩形框</el-radio>
          </el-radio-group>
        </div>

        <div class="annotations-list">
          <h3>已添加标注 
            <el-button type="text" size="small" @click="reloadAnnotations" title="刷新标注">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </h3>
          <div v-if="filteredAnnotations.length === 0" class="empty-annotations">
            <p>暂无标注，请在图片上绘制矩形框添加标注</p>
          </div>
          <el-table v-else :data="filteredAnnotations" style="width: 100%">
            <el-table-column label="编号" width="60">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="tag" label="标签" width="90" />
            <el-table-column label="坐标" width="120">
              <template #default="scope">
                <div class="coordinates">
                  <small>X: {{ Math.round(scope.row.x) }}</small><br>
                  <small>Y: {{ Math.round(scope.row.y) }}</small>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="尺寸" width="120">
              <template #default="scope">
                <div class="dimensions">
                  <small>W: {{ Math.round(scope.row.width) }}</small><br>
                  <small>H: {{ Math.round(scope.row.height) }}</small>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button type="danger" size="small" @click="deleteAnnotation(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 图像标注区域 -->
      <div class="annotation-area">
        <div class="image-wrapper">
          <div ref="imageContainerInner" class="image-container">
            <!-- 固定显示的图片 -->
            <img 
              v-if="currentImage" 
              ref="annotationImage" 
              :src="getImageUrl(currentImage.url)"
              class="annotation-image"
              alt="医学影像" 
              @load="handleImageLoad"
            />
            
            <!-- 透明覆盖层，用于标注，与图片大小一致 -->
            <div 
              v-if="currentImage"
              class="annotation-overlay"
              :style="{
                width: imageWidth + 'px',
                height: imageHeight + 'px'
              }"
              @mousedown="startDrawing"
              @mousemove="onDrawing"
              @mouseup="endDrawing"
              @mouseleave="cancelDrawing"
            ></div>
            
            <!-- 标注框 -->
            <div 
              v-for="(box, index) in filteredAnnotations" 
              :key="index" 
              class="annotation-box"
              :style="{
                left: box.x + 'px',
                top: box.y + 'px',
                width: box.width + 'px',
                height: box.height + 'px',
                borderColor: getTagColor(box.tag),
                cursor: isEditingBox && editingBoxId === box.id ? 'move' : 'default'
              }"
              @mousedown.stop="startEditBox($event, box.id)"
            >
              <span class="annotation-label" :style="{ backgroundColor: getTagColor(box.tag) }">
                {{ box.tag }}
              </span>
              
              <!-- 四个角的调整大小的手柄 -->
              <div class="resize-handle top-left" @mousedown.stop="startResizeBox($event, box.id, 'top-left')"></div>
              <div class="resize-handle top-right" @mousedown.stop="startResizeBox($event, box.id, 'top-right')"></div>
              <div class="resize-handle bottom-left" @mousedown.stop="startResizeBox($event, box.id, 'bottom-left')"></div>
              <div class="resize-handle bottom-right" @mousedown.stop="startResizeBox($event, box.id, 'bottom-right')"></div>
            </div>
            
            <!-- 正在绘制的框 -->
            <div 
              v-if="isDrawing" 
              class="drawing-box"
              :style="{
                left: Math.min(drawStart.x, drawCurrent.x) + 'px',
                top: Math.min(drawStart.y, drawCurrent.y) + 'px',
                width: Math.abs(drawCurrent.x - drawStart.x) + 'px',
                height: Math.abs(drawCurrent.y - drawStart.y) + 'px'
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <div class="form-section">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api'
import { getImageUrl, convertToAbsoluteCoordinates, UserIdConsistencyFixer } from '../utils/imageHelper'
import { Refresh } from '@element-plus/icons-vue'
import axios from 'axios'
import { mapGetters } from 'vuex'
import { ElMessage } from 'element-plus'

// 配置对象，用于集中管理API路径和存储键名等
const CONFIG = {
  STORAGE_KEYS: {
    userInfo: 'user',
    token: 'token',
    authValid: 'authValid',
    isNavigatingAfterSave: 'isNavigatingAfterSave',
    pendingDiagnosis: 'pendingDiagnosisId',
    formDataPrefix: 'formData_backup_'
  },
  ROUTES: {
    structuredForm: '/app/cases/structured-form'
  },
  UI: {
    navigationDelay: 100
  }
};

export default {
  name: 'CaseDetailForm',
  components: {
    Refresh
  },
  data() {
    return {
      imageId: this.$route.params.id || this.$route.query.imageId || null,
      currentImage: null, // 初始化 currentImage
      selectedMainCategory: '', // 主要分类
      selectedTag: '', // 具体类型
      currentTool: 'rectangle', // 添加并初始化工具
      // 血管瘤分类数据
      hemangiomaCategories: {
        '真性血管肿瘤': [
          { label: '婴幼儿血管瘤', value: '婴幼儿血管瘤' },
          { label: '先天性快速消退型血管瘤', value: '先天性快速消退型血管瘤' },
          { label: '先天性部分消退型血管瘤', value: '先天性部分消退型血管瘤' },
          { label: '先天性不消退型血管瘤', value: '先天性不消退型血管瘤' },
          { label: '卡波西型血管内皮细胞瘤', value: '卡波西型血管内皮细胞瘤' },
          { label: '丛状血管瘤', value: '丛状血管瘤' },
          { label: '化脓性肉芽肿', value: '化脓性肉芽肿' },
          { label: '梭形细胞血管瘤', value: '梭形细胞血管瘤' },
          { label: '上皮样血管内皮瘤', value: '上皮样血管内皮瘤' },
          { label: '网状血管内皮瘤', value: '网状血管内皮瘤' },
          { label: '假肌源性血管内皮瘤', value: '假肌源性血管内皮瘤' },
          { label: '多形性血管内皮瘤', value: '多形性血管内皮瘤' },
          { label: '血管肉瘤', value: '血管肉瘤' },
          { label: '上皮样血管肉瘤', value: '上皮样血管肉瘤' },
          { label: '卡波西肉瘤', value: '卡波西肉瘤' }
        ],
        '血管畸形': [
          { label: '微静脉畸形', value: '微静脉畸形' },
          { label: '静脉畸形', value: '静脉畸形' },
          { label: '动静脉畸形', value: '动静脉畸形' },
          { label: '淋巴管畸形', value: '淋巴管畸形' },
          { label: '球细胞静脉畸形', value: '球细胞静脉畸形' },
          { label: '毛细血管-淋巴管-静脉畸形', value: '毛细血管-淋巴管-静脉畸形' },
          { label: '毛细血管-动静脉畸形', value: '毛细血管-动静脉畸形' },
          { label: '淋巴管-静脉畸形', value: '淋巴管-静脉畸形' }
        ],
        '血管假瘤/易混淆病变': [
          { label: '血管外皮细胞瘤', value: '血管外皮细胞瘤' },
          { label: '血管球瘤', value: '血管球瘤' },
          { label: '血管平滑肌瘤', value: '血管平滑肌瘤' },
          { label: '血管纤维瘤', value: '血管纤维瘤' },
          { label: '靶样含铁血黄素沉积性血管瘤', value: '靶样含铁血黄素沉积性血管瘤' },
          { label: '鞋钉样血管瘤', value: '鞋钉样血管瘤' }
        ]
      },
      formData: {
        // 表单数据结构
        diseasePart: '',
        diseaseType: '',
        patientAge: '',
        patientGender: '',
        diagnosis: '',
        treatmentPlan: '',
        notes: ''
      },
      dbAnnotations: [], // 存储从数据库获取的标注数据
      uploadedImages: [],
      currentImageIndex: 0,
      annotations: [],
      imageWidth: 0,
      imageHeight: 0,
      drawingBox: null,
      isDrawing: false,
      selectedAnnotation: null,
      isMoving: false,
      isResizing: false,
      resizeHandle: null,
      moveStartX: 0,
      moveStartY: 0,
      drawStart: { x: 0, y: 0 },
      drawCurrent: { x: 0, y: 0 },
      // annotationTool: 'rectangle', // 移除旧的、冲突的属性
      showAnnotationForm: false,
      currentAnnotation: null,
      zoomLevel: 1,
      panOffset: { x: 0, y: 0 },
      isPanning: false,
      lastPanPoint: { x: 0, y: 0 },
      annotationsLoaded: false,
      shouldSaveOriginalImage: false,
      imageLoaded: false,
      isSavingAnnotations: false,
      originalWidth: 0,
      originalHeight: 0,
      scaleX: 1,
      scaleY: 1,
      isSaving: false, // 添加保存状态变量
      processedAnnotations: false, // 添加标注处理状态
      // 添加延迟保存的计时器ID
      saveImageTimer: null,
    };
  },
  computed: {
    // 根据选择的主要分类返回可用的子分类
    availableSubCategories() {
      if (!this.selectedMainCategory) {
        return [];
      }
      return this.hemangiomaCategories[this.selectedMainCategory] || [];
    },
    filteredAnnotations() {
      return this.annotations.filter(a => a.imageIndex === this.currentImageIndex)
    }
  },
  watch: {
    // 关键修复：当标注数据和图像都加载完成后，处理标注
    dbAnnotations: {
      handler(newAnnotations) {
        if (this.imageLoaded && newAnnotations && newAnnotations.length > 0 && !this.processedAnnotations) {
          console.log('监听到标注数据变化且图像已加载，开始处理标注');
          this.processLoadedAnnotations();
        }
      },
      deep: true,
      immediate: true
    },
    // 关键修复：当图像加载完成后，如果标注数据已存在，处理标注
    imageLoaded: {
      handler(newVal) {
        if (newVal && this.dbAnnotations && this.dbAnnotations.length > 0 && !this.processedAnnotations) {
          console.log('监听到图像加载完成且标注数据已存在，开始处理标注');
          this.processLoadedAnnotations();
        }
      },
      immediate: true
    }
  },
  created() {
    // 注册窗口大小改变事件监听
    window.addEventListener('resize', this.handleResize);
  },
  mounted() {
    console.log('CaseDetailForm mounted - 查询参数:', this.$route.query, '路由参数:', this.$route.params);

    // 获取图像ID，优先从route params中获取，然后是query参数
    let imageId = this.$route.params.id || this.$route.query.imageId || this.$route.query.diagnosisId || '';
    console.log('原始图像ID:', imageId, '类型:', typeof imageId);

    // 加载AI检测结果并自动设置分类
    this.loadAIDetectionResults();
    
    // 确保imageId是字符串类型
    imageId = String(imageId);
    
    // 检查是否有有效的图像ID
    if (!imageId || imageId === 'undefined' || imageId === 'null') {
      this.$message.warning('未提供有效的图像ID，请选择一个图像进行标注');
      return;
    }
    
    // 保存图像ID到组件实例
    this.imageId = imageId;
    console.log('处理后的图像ID:', this.imageId, '类型:', typeof this.imageId);
    
    // 显示加载提示
    const loading = this.$loading({
      lock: true,
      text: '正在加载图像和标注数据...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 统一加载流程
      // 标记为应用内操作
      sessionStorage.setItem('isAppOperation', 'true');
    
    // 添加重试机制，确保能正确加载图像和标注数据
    let retryCount = 0;
    const maxRetries = 3;
    
    const loadDataWithRetry = () => {
      console.log(`尝试加载页面数据，第 ${retryCount + 1} 次尝试`);
      
      // 异步加载数据
      this.loadPageData(imageId)
        .then(() => {
          console.log('页面数据加载成功');
          // 成功后加载关联的标注
          return this.loadAnnotations(imageId);
        })
        .then(() => {
          console.log('标注数据也加载成功');
          loading.close();
        })
        .catch(error => {
          console.error(`第 ${retryCount + 1} 次加载数据失败:`, error);
          
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`${retryCount * 2}秒后重试...`);
            
            // 延迟重试，每次增加等待时间
            setTimeout(loadDataWithRetry, retryCount * 2000);
          } else {
            console.error('达到最大重试次数，放弃加载');
            this.$message.error('无法加载页面数据，请返回重试');
            loading.close();
          }
        });
    };
    
    // 开始加载数据
    loadDataWithRetry();
    
    // 添加事件监听器
    document.addEventListener('mousemove', this.handleGlobalMouseMove);
    document.addEventListener('mouseup', this.handleGlobalMouseUp);
    
    // 响应窗口大小变化，更新图像尺寸
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    // 移除窗口大小改变事件监听，避免内存泄漏
    window.removeEventListener('resize', this.handleResize);
    
    // 清除导航标记
    sessionStorage.removeItem('isNavigatingAfterSave');
    sessionStorage.removeItem('returningToWorkbench');
    // 保留isAppOperation，以维持应用内操作状态
  },
  methods: {
    // 处理主分类变化
    onMainCategoryChange() {
      // 清空子分类选择
      this.selectedTag = '';
    },

    // 加载AI检测结果并自动设置分类
    async loadAIDetectionResults() {
      try {
        const diagnosisId = this.imageId;
        if (!diagnosisId) {
          console.log('没有诊断ID，跳过AI检测结果加载');
          return;
        }

        console.log('开始加载AI检测结果，诊断ID:', diagnosisId);

        // 获取血管瘤诊断数据
        const response = await axios.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);
        const diagnosisData = response.data;

        console.log('获取到的诊断数据:', diagnosisData);

        if (diagnosisData && diagnosisData.detectedType) {
          // 将AI检测到的类型转换为完整的中文名称
          const detectedType = diagnosisData.detectedType;
          console.log('AI检测到的类型:', detectedType);

          // 自动设置分类
          this.autoSetCategoryFromAI(detectedType);
        }
      } catch (error) {
        console.error('加载AI检测结果失败:', error);
        // 不显示错误消息，因为这是自动加载，失败了用户可以手动选择
      }
    },

    // 根据AI检测结果自动设置分类
    autoSetCategoryFromAI(detectedType) {
      console.log('开始自动设置分类，检测类型:', detectedType);

      // AI检测结果可能是缩写形式，需要转换为完整名称
      const typeMapping = {
        'IH': '婴幼儿血管瘤',
        'RICH': '先天性快速消退型血管瘤',
        'PICH': '先天性部分消退型血管瘤',
        'NICH': '先天性不消退型血管瘤',
        'KHE': '卡波西型血管内皮细胞瘤',
        'TA': '丛状血管瘤',
        'PG': '化脓性肉芽肿',
        'MVM': '微静脉畸形',
        'VM': '静脉畸形',
        'AVM': '动静脉畸形',
        'LM': '淋巴管畸形',
        'GVM': '球细胞静脉畸形',
        'CLVM': '毛细血管-淋巴管-静脉畸形',
        'CAVM': '毛细血管-动静脉畸形',
        'LVM': '淋巴管-静脉畸形'
      };

      // 处理多个检测类型（用+分隔）
      const types = detectedType.split('+');
      const firstType = types[0].trim();

      // 获取完整的中文名称
      const fullTypeName = typeMapping[firstType] || firstType;
      console.log('转换后的完整类型名称:', fullTypeName);

      // 查找该类型属于哪个主分类
      let targetMainCategory = '';
      let targetSubCategory = fullTypeName;

      for (const [mainCategory, subCategories] of Object.entries(this.hemangiomaCategories)) {
        const found = subCategories.find(item => item.value === fullTypeName);
        if (found) {
          targetMainCategory = mainCategory;
          targetSubCategory = found.value;
          break;
        }
      }

      if (targetMainCategory) {
        console.log('自动设置分类:', targetMainCategory, '->', targetSubCategory);

        // 设置主分类
        this.selectedMainCategory = targetMainCategory;

        // 等待Vue更新DOM后设置子分类
        this.$nextTick(() => {
          this.selectedTag = targetSubCategory;

          // 显示成功消息
          this.$message.success(`已根据AI检测结果自动设置分类：${targetSubCategory}`);
        });
      } else {
        console.log('未找到匹配的分类，使用默认设置');
        // 如果没找到匹配的，设置为第一个分类作为默认
        this.selectedMainCategory = '真性血管肿瘤';
        this.$nextTick(() => {
          this.selectedTag = '婴幼儿血管瘤';
        });
      }
    },
    // 处理长ID，将时间戳ID转换为合适的整数
    processLongId(id) {
      if (!id) return null;
      
      const idStr = id.toString();
      // 如果ID长度超过9（INT范围限制），截取后9位
      if (idStr.length > 9) {
        console.log(`处理长ID: ${idStr} -> ${idStr.substring(idStr.length - 9)}`);
        return idStr.substring(idStr.length - 9);
      }
      return idStr;
    },

    // 添加防止缓存的参数
    getImageUrl(url) {
      if (!url) return '';
      
      // 如果URL已经包含完整路径(http)，直接使用
      if (url.startsWith('http')) {
        return url;
      }
      
      // 尝试不同的URL格式
      const originalUrl = url;
      let finalUrl = url;
      
      // 如果是相对路径，加上基础URL
      if (url.startsWith('/')) {
        // 获取当前域名作为基础URL
        const baseUrl = window.location.origin;
        finalUrl = baseUrl + url;
      }
      
      // 添加时间戳和随机数参数，防止缓存
      const cacheBuster = `t=${Date.now()}&r=${Math.random()}`;
      if (finalUrl.includes('?')) {
        finalUrl = `${finalUrl}&${cacheBuster}`;
      } else {
        finalUrl = `${finalUrl}?${cacheBuster}`;
      }
      
      console.log(`处理图像URL: ${originalUrl} -> ${finalUrl}`);
      return finalUrl;
    },
    
    // 修改加载图像数据的方法，添加防止缓存的头信息
    async loadImageData() {
      if (!this.imageId) {
        console.error('No image ID provided');
        return;
      }
      
      try {
        console.log(`Loading image data for ID: ${this.imageId}`);
        
        // 添加时间戳和随机数，防止缓存
        const timestamp = Date.now();
        const random = Math.random();
        
        const response = await api.get(`/images/${this.imageId}?t=${timestamp}&r=${random}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });
        
        if (response.data) {
          this.currentImage = response.data;
          console.log('Image data loaded:', this.currentImage);
          
          // 加载关联的标注
          this.loadAnnotations();
        } else {
          console.error('Failed to load image data: Response data is empty');
        }
      } catch (error) {
        console.error('Failed to load image data:', error);
      }
    },
    
    // 修改加载标注的方法，解决刷新后标注框不显示的问题
    async loadAnnotations(imageId) {
      if (!imageId) {
        console.error('无法加载标注：缺少图像ID');
        return Promise.reject('缺少图像ID');
      }
      
      const id = imageId || this.imageId;
      console.log('加载图像ID为', id, '的标注数据');
      
      // 最大重试次数
      const maxRetries = 3;
      let retries = 0;
      
      const tryLoadAnnotations = async () => {
        try {
          // 添加时间戳和随机数避免缓存
          const timestamp = new Date().getTime();
          const random = Math.random();
          
          // 获取当前用户信息
          const user = JSON.parse(localStorage.getItem('user') || '{}');
          const userId = user.customId || user.id || '';
      
          // 检查是否是血管瘤诊断ID
          const isDiagnosisId = this.$route.query.diagnosisId === id;
          
          // 构建API URL
          let apiUrl = `/medical/api/tags/image/${id}?t=${timestamp}&r=${random}&mode=view&_role=REVIEWER&userId=${userId}`;
      
          // 如果是血管瘤诊断ID，使用特殊的API端点
      if (isDiagnosisId) {
            apiUrl = `/medical/api/hemangioma-diagnoses/${id}/tags?t=${timestamp}&r=${random}`;
          }
          
          console.log(`尝试加载标注数据 (第${retries + 1}次尝试)，URL: ${apiUrl}`);
          
          // 尝试从API加载标注数据
          const response = await fetch(apiUrl);
          
          if (!response.ok) {
            throw new Error(`获取标注失败: ${response.status}`);
          }
          
          const data = await response.json();
          console.log(`获取到 ${data.length} 个标注数据:`, data);
            
            // 保存原始标注数据
          this.dbAnnotations = data || [];
            
          // 如果图像已加载，立即转换标注
              if (this.imageWidth > 0 && this.imageHeight > 0) {
            this.processLoadedAnnotations();
              } else {
            // 否则标记为待处理
                this.annotationsLoaded = true;
              }
          
          return data;
        } catch (error) {
          console.error(`加载标注数据失败 (第${retries + 1}次尝试):`, error);
          
          // 如果还有重试次数，则等待后重试
          if (retries < maxRetries) {
            retries++;
            const waitTime = retries * 1000; // 每次重试增加等待时间
            console.log(`等待 ${waitTime}ms 后重试...`);
            
            return new Promise((resolve, reject) => {
              setTimeout(async () => {
                try {
                  const result = await tryLoadAnnotations();
                  resolve(result);
                } catch (retryError) {
                  reject(retryError);
                }
              }, waitTime);
            });
          }
          
          // 尝试备用API路径
          try {
            console.log('尝试使用备用API路径...');
            const timestamp = new Date().getTime();
        const random = Math.random();
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            const userId = user.customId || user.id || '';
            
            // 检查是否是血管瘤诊断ID
            const isDiagnosisId = this.$route.query.diagnosisId === id;
            
            // 构建备用API URL
            let backupUrl = `/api/tags/image/${id}?t=${timestamp}&r=${random}&userId=${userId}`;
            
            // 如果是血管瘤诊断ID，使用特殊的API端点
            if (isDiagnosisId) {
              backupUrl = `/api/hemangioma-diagnoses/${id}/tags?t=${timestamp}&r=${random}`;
            }
            
            console.log(`尝试备用API路径: ${backupUrl}`);
            const response = await fetch(backupUrl);
            
            if (!response.ok) {
              throw new Error(`备用API也失败: ${response.status}`);
            }
            
            const data = await response.json();
            console.log(`通过备用API获取到 ${data.length} 个标注数据:`, data);
            
            // 保存原始标注数据
            this.dbAnnotations = data || [];
            
            // 如果图像已加载，立即转换标注
            if (this.imageWidth > 0 && this.imageHeight > 0) {
                this.processLoadedAnnotations();
            } else {
              // 否则标记为待处理
              this.annotationsLoaded = true;
            }
            
            return data;
          } catch (backupError) {
            console.error('所有API尝试都失败:', backupError);
            this.$message.error('加载标注数据失败，您可以创建新的标注');
            return Promise.reject(backupError);
          }
        }
      };
      
      // 开始尝试加载
      return tryLoadAnnotations();
    },
    
    // 转换血管瘤标签数据为前端标注格式
    convertHemangiomaTags(tags) {
      console.log('转换血管瘤标签数据:', tags);
      
      if (!tags || !tags.length || !this.imageWidth || !this.imageHeight) {
        console.warn('无法转换标签数据: 缺少必要信息', {
          tagsLength: tags ? tags.length : 0,
          imageWidth: this.imageWidth,
          imageHeight: this.imageHeight
        });
        return;
      }
      
      const convertedAnnotations = tags.map((tag, index) => {
        // 判断是否是相对坐标（0-1范围内）
        const isRelativeCoordinates = 
          tag.x <= 1 && tag.x >= 0 && 
          tag.y <= 1 && tag.y >= 0 && 
          tag.width <= 1 && tag.width >= 0 && 
          tag.height <= 1 && tag.height >= 0;
        
        // 根据坐标类型计算实际像素值
        const x = isRelativeCoordinates ? tag.x * this.imageWidth : tag.x;
        const y = isRelativeCoordinates ? tag.y * this.imageHeight : tag.y;
        const width = isRelativeCoordinates ? tag.width * this.imageWidth : tag.width;
        const height = isRelativeCoordinates ? tag.height * this.imageHeight : tag.height;
        
        return {
          id: `${index + 1}`,
          serverId: tag.id || null,
          tag: tag.tag || tag.tagName || 'IH-婴幼儿血管瘤',
          x: x,
          y: y,
          width: width,
          height: height,
          confidence: tag.confidence || null,
          type: 'rectangle'
        };
      });
      
      console.log('转换后的标注数据:', convertedAnnotations);
      this.annotations = convertedAnnotations;
    },
    
    // 转换普通标签数据为前端标注格式
    convertTags(tags) {
      console.log('转换普通标签数据:', tags);
          
      if (!tags || !tags.length || !this.imageWidth || !this.imageHeight) {
        console.warn('无法转换标签数据: 缺少必要信息');
        return;
          }
      
      const convertedAnnotations = tags.map((tag, index) => {
        // 判断是否是相对坐标（0-1范围内）
        const isRelativeCoordinates = 
          tag.x <= 1 && tag.x >= 0 && 
          tag.y <= 1 && tag.y >= 0 && 
          tag.width <= 1 && tag.width >= 0 && 
          tag.height <= 1 && tag.height >= 0;
        
        // 根据坐标类型计算实际像素值
        const x = isRelativeCoordinates ? tag.x * this.imageWidth : tag.x;
        const y = isRelativeCoordinates ? tag.y * this.imageHeight : tag.y;
        const width = isRelativeCoordinates ? tag.width * this.imageWidth : tag.width;
        const height = isRelativeCoordinates ? tag.height * this.imageHeight : tag.height;
        
        return {
          id: tag.id || `${index + 1}`,
          tag: tag.tag || tag.tagName || 'IH-婴幼儿血管瘤',
          x: x,
          y: y,
          width: width,
          height: height,
          confidence: tag.confidence || null,
          type: 'rectangle'
        };
      });
      
      console.log('转换后的标注数据:', convertedAnnotations);
      this.annotations = convertedAnnotations;
    },
    
    // 添加手动刷新方法
    async refreshData() {
      // 清除本地缓存
      this.dbAnnotations = [];
      this.annotations = [];
      this.annotationsLoaded = false;
      
      // 重新加载数据
      await this.loadImageData();
      
      // 显示提示
      this.$message({
        message: '数据已刷新',
        type: 'success',
        duration: 2000
      });
    },

    // 从image_pairs表加载图像
    loadImageFromPairs(imageId) {
      console.log('🔍 开始从image_pairs表加载图像，ID:', imageId);
      
      // 获取当前用户信息
      const user = JSON.parse(localStorage.getItem('user')) || {};
      const userId = user.customId || user.id;
      
      if (!userId) {
        console.warn('未找到用户ID，可能导致权限问题');
          }
      
      // 显式添加用户ID作为查询参数
      const params = { userId };
      
      // 添加时间戳避免缓存
      params.t = new Date().getTime();
      
      // 先从image_pairs表查询相关记录
      api.imagePairs.getByMetadataId(imageId)
        .then(response => {
          console.log('image_pairs查询响应:', response);
          const data = response.data;
          
          if (data && data.length > 0) {
            const imagePair = data[0];
            console.log('image_pairs第一条记录:', imagePair);
            this.imagePairId = imagePair.id;
            
            // 使用处理后的图像路径
            const processedImagePath = imagePair.imageOnePath || imagePair.image_one_path;
            console.log('获取到图像路径:', processedImagePath);
            
            // 如果路径仍为空，尝试其他字段
            if (!processedImagePath && imagePair.image_one_url) {
              console.log('尝试使用image_one_url:', imagePair.image_one_url);
              this.processedFilePath = imagePair.image_one_url;
            } else if (!processedImagePath && imagePair.url) {
              console.log('尝试使用url字段:', imagePair.url);
              this.processedFilePath = imagePair.url;
            } else {
              this.processedFilePath = processedImagePath;
            }
            
            // 如果仍然没有找到路径，直接从image_metadata表获取
            if (!this.processedFilePath) {
              console.log('image_pairs中未找到图像路径，尝试从image_metadata表获取');
              this.tryLoadImagePathFromMetadata(imageId, (path) => {
                if (path) {
                  console.log('从image_metadata表获取到图像路径:', path);
                  this.processedFilePath = path;
                  this.updateImagePairPath(imageId, path);
                  
                  // 更新上传图像数组
                  this.uploadedImages = [{
                    id: imageId,
                    url: path,
                    filename: `图像 #${imageId}`
                  }];
                  
                  this.currentImage = this.uploadedImages[0];
                  this.imageLoaded = true;
                }
              });
              return;  // 中断当前流程，等待回调处理
            }
            
            // 更新上传图像数组
            this.uploadedImages = [{
              id: imageId,
              url: this.processedFilePath,
              filename: `图像 #${imageId}`
            }];
            
            this.currentImage = this.uploadedImages[0];
            this.currentImageIndex = 0;
            this.currentImageId = imageId;
            
            // 标记图像已加载
            this.imageLoaded = true;
            
            // 加载标注框
            this.loadAnnotations(imageId);
          } else {
            console.log('image_pairs未找到数据，尝试从image_metadata表直接获取路径');
            this.tryLoadImagePathFromMetadata(imageId, (path) => {
              if (path) {
                console.log('从image_metadata表获取到图像路径:', path);
                this.processedFilePath = path;
                this.createImageRecord(path, imageId);
              } else {
                this.$message.error('未能找到图像路径，无法加载图像');
              }
            });
          }
        })
        .catch(error => {
          console.error('加载图像对失败:', error);
          // 尝试直接从image_metadata获取
          this.tryLoadImagePathFromMetadata(imageId);
        });
    },
    
    // 作为备用，尝试从image_metadata加载
    tryLoadFromMetadata(imageId) {
      console.log('从image_metadata表尝试加载图像ID:', imageId);
      
      api.images.getOne(imageId)
        .then(response => {
          console.log('从image_metadata表获取的完整响应:', response);
          const imageData = response.data;
          console.log('image_metadata表获取的图像数据:', imageData);
          
          if (!imageData || !imageData.path) {
            console.error('image_metadata表中图像路径不存在，无法加载图像');
            this.$message.error('图像路径不存在，请检查数据库');
            return;
          }
          
          // 准备图像数据
          this.uploadedImages = [{
            id: imageData.id,
            url: imageData.path,
            filename: imageData.original_name || `图像 #${imageData.id}`
          }];
          
          this.currentImage = this.uploadedImages[0];
          this.currentImageIndex = 0;
          
          // 保存处理后的图像路径
          this.processedFilePath = imageData.path;
          
          // 创建ImagePair记录 - 直接使用fetch创建，避免API调用
          this.createImagePairDirectly(imageData.id, imageData.path);
          
          // 加载标注数据
          this.loadAnnotationsFromDatabase();
          
          // 标记图像已成功加载
          this.imageLoaded = true;
          
          console.log('从image_metadata加载成功并创建image_pairs记录');
        })
        .catch(error => {
          console.error('从image_metadata加载图像失败:', error);
          this.loadingError = true;
          this.loadingErrorMessage = '无法加载图像，请检查数据库记录';
          
          // 如果有物理路径，尝试创建新的图像记录
          if (this.processedFilePath && this.currentImageId) {
            console.log('尝试使用现有路径创建图像记录');
            this.createImageRecord(this.processedFilePath, this.currentImageId);
          } else {
            this.$message.error('无法找到图像数据，请返回上一步重新上传');
          }
        });
    },
    
    // 直接创建ImagePair记录，不使用API
    createImagePairDirectly(metadataId, imagePath) {
      console.log('直接创建ImagePair记录:', { metadataId, path: imagePath });
      
      // 准备数据
      const data = {
        metadataId: metadataId.toString(),
        imageOnePath: imagePath,
        description: `图像${metadataId}`
      };
      
      // 直接使用fetch API，完全绕过axios和api.js
      fetch('/api/image-pairs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data),
        credentials: 'include'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(result => {
          console.log('成功创建ImagePair记录:', result);
          if (result && result.id) {
            this.imagePairId = result.id;
          }
          this.$message.success('图像关联信息创建成功');
        })
        .catch(error => {
          console.error('创建ImagePair记录失败:', error);
          this.$message.warning('图像关联创建失败，但您仍可继续使用');
        });
    },
    
    // 保留原有方法但不再主动调用
    loadImageById(imageId) {
      // 重定向到新的加载方法
      this.loadImageFromPairs(imageId);
    },
    
    // 创建新的图像记录
    createImageRecord(imagePath, metadataId) {
      console.log('正在创建新的图像记录:', { path: imagePath, id: metadataId });
      
      // 获取当前用户
      const user = JSON.parse(localStorage.getItem('user')) || { id: 1 };
      
      // 提取文件名
      const filename = imagePath.substring(imagePath.lastIndexOf('/') + 1);
      
      // 创建元数据
      const metadata = {
        filename: filename,
        original_name: filename,
        path: imagePath,
        mimetype: 'image/jpeg',
        size: 0,
        width: 800,
        height: 600,
        status: 'DRAFT',
        uploaded_by: user.id
      };
      
      // 保存元数据
      api.images.update(metadataId, metadata)
        .then(response => {
          console.log('成功创建/更新图像元数据:', response.data);
          
          // 创建ImagePair记录
          this.saveOriginalImage();
          
          // 重新加载页面
          this.loadImageById(metadataId);
        })
        .catch(error => {
          console.error('创建图像元数据失败:', error);
          this.$message.error('无法创建图像记录，请检查数据库连接');
        });
    },
    
    // 根据路径加载图像
    loadImageByPath(imagePath) {
      // 模拟图像加载过程
      this.uploadedImages = [{
        id: this.currentImageId || Date.now().toString(),
        url: imagePath,
        filename: imagePath.substring(imagePath.lastIndexOf('/') + 1)
      }];
      
      this.currentImage = this.uploadedImages[0];
      this.currentImageIndex = 0;
      
      // 如果没有图像ID，使用当前时间戳作为ID
      if (!this.currentImageId) {
        this.currentImageId = this.uploadedImages[0].id;
        console.log('生成临时图像ID:', this.currentImageId);
      }
      
      // 确保ImagePair记录存在
      this.checkExistingImagePair();
      
      // 标记图像已成功加载
      this.imageLoaded = true;
    },
    
    // 检查并确保图像对记录存在
    checkExistingImagePair() {
      if (!this.currentImageId || !this.processedFilePath) {
        console.log('缺少图像ID或物理路径，不检查ImagePair记录');
        return;
      }
      
      console.log('检查图像ID对应的ImagePair记录:', this.currentImageId);
      
      api.imagePairs.getByMetadataId(this.currentImageId)
        .then(response => {
          if (response.data && response.data.length > 0) {
            // 找到现有记录
            const imagePair = response.data[0];
            this.imagePairId = imagePair.id;
            console.log('找到现有ImagePair记录:', imagePair);
            
            // 如果记录中的路径与当前路径不一致，更新记录
            if (imagePair.imageOnePath !== this.processedFilePath) {
              console.log('更新ImagePair中的路径信息');
              this.saveOriginalImage();
            }
          } else {
            // 没有找到记录，创建新记录
            console.log('未找到ImagePair记录，创建新记录');
            this.saveOriginalImage();
          }
        })
        .catch(error => {
          console.error('检查ImagePair记录失败:', error);
          // 尝试创建新记录
          console.log('尝试创建新的ImagePair记录');
          this.saveOriginalImage();
        });
    },
    
    saveOriginalImage() {
      // 如果没有图像ID，则不保存
      if (!this.currentImageId) {
        console.log('缺少图像ID，不保存到ImagePair表');
        return;
      }
      
      let imagePath = '';
      
      // 首选：从currentImage中获取URL（已经从数据库加载）
      if (this.currentImage && this.currentImage.url) {
        imagePath = this.currentImage.url;
        console.log('使用从数据库加载的图像路径:', imagePath);
      } 
      // 次选：使用本地保存的processedFilePath
      else if (this.processedFilePath) {
        imagePath = this.processedFilePath;
        console.log('使用本地保存的处理后图像路径:', imagePath);
      }
      // 如果都没有，无法保存
      else {
        console.error('无法找到有效的图像路径，不保存到ImagePair表');
        return;
      }
      
      // 确保图像元数据存在
      this.ensureImageMetadataExists(this.currentImageId, imagePath, () => {
        // 获取当前用户
        const user = JSON.parse(localStorage.getItem('user')) || { id: 1 };
        
        try {
          // 准备请求数据 - 极简格式，避免任何不必要的字段
          const data = {
            metadataId: this.currentImageId.toString(), // 使用字符串格式
            imageOnePath: imagePath,
            description: `图像${this.currentImageId}` // 简化描述
          };
          
          console.log('保存到image_pairs的数据:', data);
          
          // 使用fetch API直接发送请求 - 绕过所有中间件和缓存
          const directRequest = async () => {
            try {
              // 查询image_pairs是否存在
              console.log('直接查询ImagePair是否存在:', this.currentImageId);
              const checkResponse = await fetch(`/api/image-pairs/metadata/${this.currentImageId}`, {
                method: 'GET',
                headers: {
                  'Accept': 'application/json',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache'
                },
                credentials: 'include'
              });
              
              // 解析响应
              const pairsData = await checkResponse.json();
              let existingPairId = null;
              let exists = false;
              
              if (pairsData && pairsData.length > 0 && pairsData[0].id) {
                exists = true;
                existingPairId = pairsData[0].id;
                console.log('找到现有ImagePair:', existingPairId);
              }
              
              // 如果存在，添加ID字段
              if (exists && existingPairId) {
                data.id = existingPairId;
              }
              
              // 发送创建/更新请求
              const saveResponse = await fetch('/api/image-pairs', {
                method: 'POST', // 始终使用POST
                headers: {
                  'Content-Type': 'application/json',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache'
                },
                body: JSON.stringify(data),
                credentials: 'include'
              });
              
              if (!saveResponse.ok) {
                throw new Error(`保存失败: ${saveResponse.status}`);
              }
              
              const result = await saveResponse.json();
              console.log('保存ImagePair成功:', result);
              
              // 更新本地ID
              if (result && result.id) {
                this.imagePairId = result.id;
              }
              
              this.$message.success('图像关联信息保存成功');
              
            } catch (error) {
              console.error('保存ImagePair失败:', error);
              this.$message.error('保存失败: ' + error.message);
            }
          };
          
          // 执行直接请求
          directRequest();
          
        } catch (err) {
          console.error('准备ImagePair数据时出错:', err);
          this.$message.error('保存图像对失败: ' + err.message);
        }
      });
    },
    
    // 确保图像元数据存在
    ensureImageMetadataExists(imageId, imagePath, callback) {
      // 检查图像元数据是否存在
      api.images.getOne(imageId)
        .then(() => {
          // 元数据存在，执行回调
          callback();
        })
        .catch(() => {
          // 元数据不存在，创建新的
          console.log('图像元数据不存在，创建新记录');
          this.createImageRecord(imagePath, imageId);
        });
    },
    
    // 窗口大小变化时更新图像尺寸
    handleResize() {
      // 窗口大小变化时，需要延迟一点以确保DOM已更新
      this.$nextTick(() => {
        const image = this.$refs.annotationImage;
        if (image) {
          // 获取新的图像显示尺寸
          const rect = image.getBoundingClientRect();
          const newWidth = rect.width;
          const newHeight = rect.height;
          
          // 如果尺寸有变化，更新并重新计算标注框位置
          if (newWidth !== this.imageWidth || newHeight !== this.imageHeight) {
            console.log(`窗口大小改变，图像尺寸从 ${this.imageWidth}x${this.imageHeight} 变为 ${newWidth}x${newHeight}`);
            this.imageWidth = newWidth;
            this.imageHeight = newHeight;
            
            // 更新缩放比例
            this.scaleX = this.imageWidth / this.originalWidth;
            this.scaleY = this.imageHeight / this.originalHeight;
            
            // 重新计算标注框位置
            this.recalculateAnnotationPositions();
          }
        }
      });
    },
    
    // 添加空的initTools方法以修复错误
    initTools() {
      console.log('初始化标注工具');
      // 这个方法被调用但原本不存在，添加一个空实现
    },
    
    // 图像加载完成后的处理
    handleImageLoad(event) {
      console.log('图像加载完成');
      
      // 获取图像实际尺寸
      const img = event.target;
      const rect = img.getBoundingClientRect();

        this.imageWidth = rect.width;
        this.imageHeight = rect.height;
      this.originalWidth = img.naturalWidth;
      this.originalHeight = img.naturalHeight;
      
      console.log('图像尺寸:', {
        width: this.imageWidth,
        height: this.imageHeight,
        originalWidth: this.originalWidth,
        originalHeight: this.originalHeight
      });
        
        // 标记图像已加载
        this.imageLoaded = true;
        
      // 如果有待处理的标注数据，现在可以处理了
      if (this.annotationsLoaded && this.dbAnnotations.length > 0) {
        console.log('图像加载完成，处理待转换的标注数据');
            this.processLoadedAnnotations();
      }
      
      // 发出图像加载完成事件
      this.$emit('image-loaded', {
        width: this.imageWidth,
        height: this.imageHeight
      });
    },
    
    // 重新计算标注位置
    recalculateAnnotationPositions() {
      if (!this.annotations || !this.annotations.length) return;
      
      console.log('重新计算标注位置，图像尺寸:', this.imageWidth, 'x', this.imageHeight);
      
      // 确保图像尺寸有效
      if (this.imageWidth <= 0 || this.imageHeight <= 0) {
        console.error('图像尺寸无效，无法重新计算标注位置');
        return;
      }
      
      this.annotations = this.annotations.map(tag => {
        // 根据标注格式进行不同处理
        if (tag.isYoloFormat) {
          // YOLO格式 - 使用归一化的中心点坐标
          const centerX = tag.normalizedX; // 中心点X
          const centerY = tag.normalizedY; // 中心点Y
          const width = tag.normalizedWidth;
          const height = tag.normalizedHeight;
          
          // 计算左上角坐标
          const pixelX = Math.round((centerX - width/2) * this.imageWidth);
          const pixelY = Math.round((centerY - height/2) * this.imageHeight);
          const pixelWidth = Math.round(width * this.imageWidth);
          const pixelHeight = Math.round(height * this.imageHeight);
          
          console.log(`重新计算YOLO标注 ${tag.id}: 中心点(${centerX.toFixed(4)}, ${centerY.toFixed(4)}) -> 左上角(${pixelX}, ${pixelY}), 尺寸: ${pixelWidth} x ${pixelHeight}`);
          
          // 返回更新后的标注对象
          return {
            ...tag,
            x: pixelX,
            y: pixelY,
            width: pixelWidth,
            height: pixelHeight
          };
        } 
        // 普通左上角坐标系统
        else if (tag.normalizedX !== undefined) {
          // 使用归一化坐标重新计算
          const newX = Math.round(tag.normalizedX * this.imageWidth);
          const newY = Math.round(tag.normalizedY * this.imageHeight);
          const newWidth = Math.round(tag.normalizedWidth * this.imageWidth);
          const newHeight = Math.round(tag.normalizedHeight * this.imageHeight);
          
          console.log(`重新计算普通标注 ${tag.id}: 归一化(${tag.normalizedX.toFixed(4)}, ${tag.normalizedY.toFixed(4)}) -> 像素(${newX}, ${newY}), 尺寸: ${newWidth} x ${newHeight}`);
          
          return {
            ...tag,
            x: newX,
            y: newY,
            width: newWidth,
            height: newHeight
          };
        } 
        // 处理历史数据：如果坐标值在0-1范围内，视为归一化坐标
        else if (tag.x <= 1 && tag.y <= 1 && tag.width <= 1 && tag.height <= 1) {
          const newX = Math.round(tag.x * this.imageWidth);
          const newY = Math.round(tag.y * this.imageHeight);
          const newWidth = Math.round(tag.width * this.imageWidth);
          const newHeight = Math.round(tag.height * this.imageHeight);
          
          console.log(`重新计算旧式归一化标注 ${tag.id}: (${tag.x.toFixed(4)}, ${tag.y.toFixed(4)}) -> (${newX}, ${newY}), 尺寸: ${newWidth} x ${newHeight}`);
          
          // 保存归一化坐标，便于后续更新
          return {
            ...tag,
            x: newX,
            y: newY,
            width: newWidth,
            height: newHeight,
            normalizedX: tag.x,
            normalizedY: tag.y,
            normalizedWidth: tag.width,
            normalizedHeight: tag.height
          };
        } else {
          // 如果是像素坐标，按比例缩放
          const scaleX = this.imageWidth / this.originalWidth || 1;
          const scaleY = this.imageHeight / this.originalHeight || 1;
          
          if (Math.abs(scaleX - 1) < 0.01 && Math.abs(scaleY - 1) < 0.01) {
            // 如果缩放比例接近1，则不做调整
            return tag;
          }
          
          const newX = Math.round(tag.x * scaleX);
          const newY = Math.round(tag.y * scaleY);
          const newWidth = Math.round(tag.width * scaleX);
          const newHeight = Math.round(tag.height * scaleY);
          
          console.log(`按比例缩放标注 ${tag.id}: 比例(${scaleX.toFixed(2)}, ${scaleY.toFixed(2)}), (${tag.x}, ${tag.y}) -> (${newX}, ${newY}), 尺寸: ${newWidth} x ${newHeight}`);
          
          return {
            ...tag,
            x: newX,
            y: newY,
            width: newWidth,
            height: newHeight
          };
        }
      });
      
      console.log('标注位置重新计算完成，共', this.annotations.length, '个标注');
    },
    selectTool(tool) {
      this.currentTool = tool
    },
    startDrawing(event) {
      if (this.currentTool !== 'rectangle') return;

      this.isDrawing = true;
      const rect = this.$refs.imageContainerInner.getBoundingClientRect();
      this.drawStart.x = event.clientX - rect.left;
      this.drawStart.y = event.clientY - rect.top;
      this.drawCurrent.x = this.drawStart.x;
      this.drawCurrent.y = this.drawStart.y;
    },

    onDrawing(event) {
      if (!this.isDrawing) return;

      const rect = this.$refs.imageContainerInner.getBoundingClientRect();
      this.drawCurrent.x = event.clientX - rect.left;
      this.drawCurrent.y = event.clientY - rect.top;
    },

    endDrawing() {
      if (!this.isDrawing) return

      this.isDrawing = false

      // 添加标注
      if (Math.abs(this.drawCurrent.x - this.drawStart.x) > 10 && 
          Math.abs(this.drawCurrent.y - this.drawStart.y) > 10) {
        
        const x = Math.min(this.drawStart.x, this.drawCurrent.x)
        const y = Math.min(this.drawStart.y, this.drawCurrent.y)
        const width = Math.abs(this.drawCurrent.x - this.drawStart.x)
        const height = Math.abs(this.drawCurrent.y - this.drawStart.y)
        
        // 计算归一化坐标 - 存储归一化坐标，以便适应不同大小的显示
        const normalizedX = x / this.imageWidth
        const normalizedY = y / this.imageHeight
        const normalizedWidth = width / this.imageWidth
        const normalizedHeight = height / this.imageHeight
        
        // 创建标注对象
        const annotation = {
          id: Date.now(),
          imageIndex: this.currentImageIndex,
          tag: this.selectedTag,
          type: 'rectangle',
          x: x,
          y: y,
          width: width,
          height: height,
          // 保存归一化坐标，用于窗口大小变化时重新计算
          normalizedX: normalizedX,
          normalizedY: normalizedY,
          normalizedWidth: normalizedWidth,
          normalizedHeight: normalizedHeight
        }
        
        // 添加到本地数组
        this.annotations.push(annotation)
        
        // 保存到数据库
        this.saveAnnotationToDatabase(annotation)
      }
    },
    
    cancelDrawing() {
      if (this.isDrawing) {
        this.isDrawing = false;
      }
    },
    // 开始编辑已有的标注框（移动整个框）
    startEditBox(event, boxId) {
      event.preventDefault()
      
      // 查找要编辑的标注框
      const boxToEdit = this.annotations.find(box => box.id === boxId)
      if (!boxToEdit) return
      
      // 保存原始框的信息，用于计算偏移量
      this.originalBox = {...boxToEdit}
      
      const rect = this.$refs.imageContainerInner.getBoundingClientRect()
      this.editStartPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
      
      this.isEditingBox = true
      this.editingBoxId = boxId
      this.isDrawing = false // 确保不在绘制新框
    },
    // 开始调整标注框大小
    startResizeBox(event, boxId, handle) {
      event.preventDefault()
      
      // 查找要调整大小的标注框
      const boxToResize = this.annotations.find(box => box.id === boxId)
      if (!boxToResize) return
      
      // 保存原始框的信息
      this.originalBox = {...boxToResize}
      
      const rect = this.$refs.imageContainerInner.getBoundingClientRect()
      this.editStartPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
      
      this.isResizingBox = true
      this.editingBoxId = boxId
      this.resizeHandle = handle
      this.isDrawing = false // 确保不在绘制新框
    },
    // 处理拖动框或调整大小
    handleGlobalMouseMove(event) {
      // 处理绘制新框
      if (this.isDrawing) {
        const rect = this.$refs.imageContainerInner.getBoundingClientRect()
        const x = event.clientX - rect.left
        const y = event.clientY - rect.top
        
        // 约束坐标在图片范围内
        const boundedX = Math.max(0, Math.min(this.imageWidth, x))
        const boundedY = Math.max(0, Math.min(this.imageHeight, y))
        
        this.drawCurrent = { x: boundedX, y: boundedY }
        return
      }
      
      // 处理移动整个框
      if (this.isEditingBox) {
        const rect = this.$refs.imageContainerInner.getBoundingClientRect()
        const currentX = event.clientX - rect.left
        const currentY = event.clientY - rect.top
        
        // 计算移动的偏移量
        const deltaX = currentX - this.editStartPos.x
        const deltaY = currentY - this.editStartPos.y
        
        // 找到正在编辑的框
        const boxIndex = this.annotations.findIndex(box => box.id === this.editingBoxId)
        if (boxIndex === -1) return
        
        // 计算新位置，确保在图片范围内
        let newX = this.originalBox.x + deltaX
        let newY = this.originalBox.y + deltaY
        
        // 防止框移出图片边界
        newX = Math.max(0, Math.min(newX, this.imageWidth - this.originalBox.width))
        newY = Math.max(0, Math.min(newY, this.imageHeight - this.originalBox.height))
        
        // 更新框位置
        this.annotations[boxIndex].x = newX
        this.annotations[boxIndex].y = newY
        return
      }
      
      // 处理调整框大小
      if (this.isResizingBox) {
        const rect = this.$refs.imageContainerInner.getBoundingClientRect()
        const currentX = event.clientX - rect.left
        const currentY = event.clientY - rect.top
        
        // 约束坐标在图片范围内
        const boundedX = Math.max(0, Math.min(this.imageWidth, currentX))
        const boundedY = Math.max(0, Math.min(this.imageHeight, currentY))
        
        // 找到正在调整的框
        const boxIndex = this.annotations.findIndex(box => box.id === this.editingBoxId)
        if (boxIndex === -1) return
        
        const box = this.annotations[boxIndex]
        const original = this.originalBox
        
        // 根据拖动的角落调整大小
        switch (this.resizeHandle) {
          case 'top-left':
            // 调整左上角
            box.width = original.x + original.width - boundedX
            box.height = original.y + original.height - boundedY
            box.x = boundedX
            box.y = boundedY
            // 确保宽度和高度为正值
            if (box.width < 10) {
              box.width = 10
              box.x = original.x + original.width - 10
            }
            if (box.height < 10) {
              box.height = 10
              box.y = original.y + original.height - 10
            }
            break;
            
          case 'top-right':
            // 调整右上角
            box.width = boundedX - original.x
            box.height = original.y + original.height - boundedY
            box.y = boundedY
            // 确保宽度和高度为正值
            if (box.width < 10) box.width = 10
            if (box.height < 10) {
              box.height = 10
              box.y = original.y + original.height - 10
            }
            break;
            
          case 'bottom-left':
            // 调整左下角
            box.width = original.x + original.width - boundedX
            box.height = boundedY - original.y
            box.x = boundedX
            // 确保宽度和高度为正值
            if (box.width < 10) {
              box.width = 10
              box.x = original.x + original.width - 10
            }
            if (box.height < 10) box.height = 10
            break;
            
          case 'bottom-right':
            // 调整右下角
            box.width = boundedX - original.x
            box.height = boundedY - original.y
            // 确保宽度和高度为正值
            if (box.width < 10) box.width = 10
            if (box.height < 10) box.height = 10
            break;
        }
      }
    },
    handleGlobalMouseUp() {
      if (this.isDrawing) {
        this.endDrawing()
      } else if (this.isEditingBox || this.isResizingBox) {
        // 结束框的编辑或调整大小
        
        // 找到正在编辑的标注框
        const editedAnnotation = this.annotations.find(box => box.id === this.editingBoxId)
        if (editedAnnotation) {
          // 如果原始框存在，比较是否有变更
          if (this.originalBox) {
            const hasChanged = 
              editedAnnotation.x !== this.originalBox.x || 
              editedAnnotation.y !== this.originalBox.y || 
              editedAnnotation.width !== this.originalBox.width || 
              editedAnnotation.height !== this.originalBox.height
            
            if (hasChanged) {
              console.log('标注框已修改，更新数据库:', {
                原位置: `(${this.originalBox.x}, ${this.originalBox.y})`,
                原尺寸: `${this.originalBox.width} x ${this.originalBox.height}`,
                新位置: `(${editedAnnotation.x}, ${editedAnnotation.y})`,
                新尺寸: `${editedAnnotation.width} x ${editedAnnotation.height}`
              })
              
              // 更新归一化坐标 - 确保在窗口大小变化时标注框保持一致
              editedAnnotation.normalizedX = editedAnnotation.x / this.imageWidth
              editedAnnotation.normalizedY = editedAnnotation.y / this.imageHeight
              editedAnnotation.normalizedWidth = editedAnnotation.width / this.imageWidth
              editedAnnotation.normalizedHeight = editedAnnotation.height / this.imageHeight
              
              // 将修改后的标注框更新到数据库
              this.updateAnnotationInDatabase(editedAnnotation)
            }
          }
        }
        
        // 重置状态
        this.isEditingBox = false
        this.isResizingBox = false
        this.editingBoxId = null
        this.resizeHandle = null
        this.originalBox = null
      }
    },
    endDrawing() {
      if (!this.isDrawing) return

      this.isDrawing = false

      // 添加标注
      if (Math.abs(this.drawCurrent.x - this.drawStart.x) > 10 && 
          Math.abs(this.drawCurrent.y - this.drawStart.y) > 10) {
        
        const x = Math.min(this.drawStart.x, this.drawCurrent.x)
        const y = Math.min(this.drawStart.y, this.drawCurrent.y)
        const width = Math.abs(this.drawCurrent.x - this.drawStart.x)
        const height = Math.abs(this.drawCurrent.y - this.drawStart.y)
        
        // 计算归一化坐标 - 存储归一化坐标，以便适应不同大小的显示
        const normalizedX = x / this.imageWidth
        const normalizedY = y / this.imageHeight
        const normalizedWidth = width / this.imageWidth
        const normalizedHeight = height / this.imageHeight
        
        // 创建标注对象
        const annotation = {
          id: Date.now(),
          imageIndex: this.currentImageIndex,
          tag: this.selectedTag,
          type: 'rectangle',
          x: x,
          y: y,
          width: width,
          height: height,
          // 保存归一化坐标，用于窗口大小变化时重新计算
          normalizedX: normalizedX,
          normalizedY: normalizedY,
          normalizedWidth: normalizedWidth,
          normalizedHeight: normalizedHeight
        }
        
        // 添加到本地数组
        this.annotations.push(annotation)
        
        // 保存到数据库
        this.saveAnnotationToDatabase(annotation)
      }
    },
    async saveAnnotationToDatabase(annotation) {
      console.log('保存标注到数据库', annotation);
      
      // 检查是否有imageId
      if (!this.imageId) {
        console.error('缺少图像ID，无法保存标注');
        return;
      }
      
      // 确保图像尺寸有效
      if (!this.imageWidth || !this.imageHeight) {
        console.error('图像尺寸无效，无法计算归一化坐标');
        return;
      }
      
      try {
        // 声明归一化坐标变量
        let normalizedX, normalizedY, normalizedWidth, normalizedHeight;
        
        // 根据标注类型计算归一化坐标
        if (annotation.isYoloFormat) {
          // 如果是YOLO格式的标注，使用中心点坐标系统
          // 先将左上角坐标转换为中心点坐标
          normalizedWidth = annotation.width / this.imageWidth;
          normalizedHeight = annotation.height / this.imageHeight;
          normalizedX = (annotation.x / this.imageWidth) + (normalizedWidth / 2);
          normalizedY = (annotation.y / this.imageHeight) + (normalizedHeight / 2);
          
          console.log('保存YOLO格式标注，转换为中心点坐标：', {
            左上角像素: `(${annotation.x}, ${annotation.y})`,
            像素宽高: `${annotation.width} x ${annotation.height}`,
            归一化中心点: `(${normalizedX.toFixed(4)}, ${normalizedY.toFixed(4)})`,
            归一化宽高: `${normalizedWidth.toFixed(4)} x ${normalizedHeight.toFixed(4)}`
          });
        } else {
          // 普通左上角坐标系统
          normalizedX = annotation.x / this.imageWidth;
          normalizedY = annotation.y / this.imageHeight;
          normalizedWidth = annotation.width / this.imageWidth;
          normalizedHeight = annotation.height / this.imageHeight;
          
          console.log('保存普通格式标注，使用左上角坐标：', {
            像素坐标: `(${annotation.x}, ${annotation.y})`,
            像素宽高: `${annotation.width} x ${annotation.height}`,
            归一化坐标: `(${normalizedX.toFixed(4)}, ${normalizedY.toFixed(4)})`,
            归一化宽高: `${normalizedWidth.toFixed(4)} x ${normalizedHeight.toFixed(4)}`
          });
        }
        
        // 限制归一化坐标在0-1范围内
        normalizedX = Math.max(0, Math.min(1, normalizedX));
        normalizedY = Math.max(0, Math.min(1, normalizedY));
        normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth));
        normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight));
        
        // 创建要保存的标注数据
        const tagData = {
          metadata_id: parseInt(this.imageId, 10),
          tag: annotation.tag,
          x: Number(normalizedX.toFixed(6)),
          y: Number(normalizedY.toFixed(6)),
          width: Number(normalizedWidth.toFixed(6)),
          height: Number(normalizedHeight.toFixed(6)),
          // 记录坐标系统类型，方便后续处理
          coord_system: annotation.isYoloFormat ? 'yolo_center' : 'top_left'
        };
        
        console.log('保存标注数据:', tagData);
        
        // 使用API保存标注
        const response = await api.tags.create(tagData);
        console.log('标注保存成功:', response.data);
        
        // 更新annotation对象，添加数据库ID和正确的归一化坐标
        annotation.dbId = response.data.id;
        annotation.normalizedX = normalizedX;
        annotation.normalizedY = normalizedY;
        annotation.normalizedWidth = normalizedWidth;
        annotation.normalizedHeight = normalizedHeight;
        
        this.$message.success('标注保存成功');
        return response.data;
      } catch (error) {
        console.error('保存标注失败:', error);
        this.$message.error('保存标注失败: ' + (error.message || '未知错误'));
        throw error;
      }
    },
    saveAnnotationToDatabase(annotation) {
      // 强制输出调试信息
      console.clear(); // 清除之前的控制台输出
      console.log('%c==================== 开始保存标注到数据库 ====================', 'background: #222; color: #bada55; font-size: 16px;');
      
      // 获取当前用户信息
      const user = JSON.parse(localStorage.getItem('user'))
      if (!user || !user.id) {
        this.$message.error('未检测到有效用户信息(缺少ID)，无法保存标注')
        console.error('未检测到有效用户信息(缺少ID)，无法保存标注', user)
        return
      }
      
      // 获取当前图像的metadata_id
      const currentImage = this.uploadedImages[this.currentImageIndex]
      if (!currentImage || !currentImage.id) {
        this.$message.error('图像信息不完整，无法保存标注')
        console.error('图像信息不完整，无法保存标注, currentImage:', currentImage)
        return
      }
      
      // 打印调试信息（强制显示在控制台）
      const debugInfo = {
        图像ID: currentImage.id,
        图像ID类型: typeof currentImage.id,
        标签类型: annotation.tag,
        坐标: `(${annotation.x}, ${annotation.y})`,
        尺寸: `${annotation.width} x ${annotation.height}`,
        用户ID: user.id || user.customId,
        用户ID类型: typeof (user.id || user.customId),
        用户角色: user.role
      };
      console.log('%c标注数据:', 'color: #47B881; font-weight: bold;', debugInfo);
      
      // 使用已保存的归一化坐标(如果存在)，否则计算归一化坐标
      let normalizedX, normalizedY, normalizedWidth, normalizedHeight;
      
      if (annotation.normalizedX !== undefined) {
        // 使用已保存的归一化坐标
        normalizedX = annotation.normalizedX;
        normalizedY = annotation.normalizedY;
        normalizedWidth = annotation.normalizedWidth;
        normalizedHeight = annotation.normalizedHeight;
        console.log('使用已保存的归一化坐标:', {
          x: normalizedX, y: normalizedY, 
          width: normalizedWidth, height: normalizedHeight
        });
      } else {
        // 确保imageWidth和imageHeight不为0
        if (!this.imageWidth || !this.imageHeight) {
          this.$message.error('图片尺寸无效，无法计算标注坐标')
          return
        }
        
        // 归一化计算 - 将像素坐标转换为0-1范围
        normalizedX = annotation.x / this.imageWidth;
        normalizedY = annotation.y / this.imageHeight;
        normalizedWidth = annotation.width / this.imageWidth;
        normalizedHeight = annotation.height / this.imageHeight;
        console.log('计算新的归一化坐标:', {
          x: normalizedX, y: normalizedY, 
          width: normalizedWidth, height: normalizedHeight
        });
      }
      
      // 验证并修正坐标范围
      // x, y必须在[0,1]之间
      normalizedX = Math.max(0, Math.min(1, normalizedX))
      normalizedY = Math.max(0, Math.min(1, normalizedY))
      
      // width, height必须在(0,1]之间
      normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth))
      normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight))
      
      // 保留4位小数
      normalizedX = Number(normalizedX.toFixed(4))
      normalizedY = Number(normalizedY.toFixed(4))
      normalizedWidth = Number(normalizedWidth.toFixed(4))
      normalizedHeight = Number(normalizedHeight.toFixed(4))
      
      // 构建要发送到后端的数据
      const tagData = {
        tag: annotation.tag,
        tagName: annotation.tag,
        x: normalizedX,
        y: normalizedY,
        width: normalizedWidth,
        height: normalizedHeight,
        metadata_id: parseInt(currentImage.id, 10),
        created_by: parseInt(user.id, 10)
      }
      
      // 确认数据类型正确
      console.log('发送标注数据:', tagData, {
        metadata_id类型: typeof tagData.metadata_id,
        created_by类型: typeof tagData.created_by
      })
      
      // 显示保存中提示
      const loadingInstance = this.$loading({
        lock: true,
        text: '正在保存标注...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      // 获取用户认证信息 - 如果有token则添加到请求头
      const token = localStorage.getItem('token') || '';
      const userId = user.id || user.customId || '';
      const userRole = user.role || 'USER';
      
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };
      
      // 如果有token，添加认证头
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      // 添加用户ID和角色头
      headers['X-User-Id'] = userId;
      headers['X-User-Role'] = userRole;
      
      console.log('使用认证头信息:', {
        Authorization: token ? 'Bearer [已设置]' : '[未设置]',
        'X-User-Id': userId,
        'X-User-Role': userRole
      });
      
      // 使用fetch API直接发送请求
      fetch('/medical/api/tags', {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(tagData),
        credentials: 'include'
      })
      .then(response => {
        console.log('保存标注响应状态:', response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        loadingInstance.close();
        console.log('标注保存成功:', data);
        this.$message.success('标注保存成功');
        
        // 更新标注数据，添加数据库ID
        annotation.dbId = data.id;
        
        // 添加/更新归一化坐标，方便窗口大小变化时重新计算
        annotation.normalizedX = normalizedX;
        annotation.normalizedY = normalizedY;
        annotation.normalizedWidth = normalizedWidth;
        annotation.normalizedHeight = normalizedHeight;
        
        // 刷新标注列表
        this.loadAnnotationsFromDatabase().then(() => {
          this.processLoadedAnnotations();
        });
      })
      .catch(error => {
        console.error('保存标注失败:', error);
        
        // 尝试备用路径
        console.log('尝试备用API路径...');
        
        fetch('/api/tags', {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(tagData),
          credentials: 'include'
        })
        .then(response => {
          if (!response.ok) {
            throw new Error(`备用路径也失败! status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          loadingInstance.close();
          console.log('通过备用路径保存标注成功:', data);
          this.$message.success('标注保存成功');
          
          // 更新标注数据，添加数据库ID
          annotation.dbId = data.id;
          
          // 添加归一化坐标
          annotation.normalizedX = normalizedX;
          annotation.normalizedY = normalizedY;
          annotation.normalizedWidth = normalizedWidth;
          annotation.normalizedHeight = normalizedHeight;
          
          // 刷新标注列表
          this.loadAnnotationsFromDatabase().then(() => {
            this.processLoadedAnnotations();
          });
        })
        .catch(backupError => {
          loadingInstance.close();
          console.error('所有尝试都失败:', backupError);
          
          // 即使保存失败，也添加到本地标注列表
          this.$message.warning('无法保存到服务器，但已添加到本地标注列表');
          
          // 添加归一化坐标
          annotation.normalizedX = normalizedX;
          annotation.normalizedY = normalizedY;
          annotation.normalizedWidth = normalizedWidth;
          annotation.normalizedHeight = normalizedHeight;
        });
      });
    },
    getTagColor(tag) {
      // 根据标签类型返回不同的颜色
      const colorMap = {
        // 真性血管肿瘤 - 红色系
        '婴幼儿血管瘤': '#ff5252',
        '先天性快速消退型血管瘤': '#ff7252',
        '先天性部分消退型血管瘤': '#ff9252',
        '先天性不消退型血管瘤': '#ffb252',
        '卡波西型血管内皮细胞瘤': '#e91e63',
        '丛状血管瘤': '#f44336',
        '化脓性肉芽肿': '#ff6b6b',
        '梭形细胞血管瘤': '#ff8a80',
        '上皮样血管内皮瘤': '#ffab91',
        '网状血管内皮瘤': '#ffcc02',
        '假肌源性血管内皮瘤': '#ff9800',
        '多形性血管内皮瘤': '#ff5722',
        '血管肉瘤': '#d32f2f',
        '上皮样血管肉瘤': '#c62828',
        '卡波西肉瘤': '#b71c1c',

        // 血管畸形 - 蓝色系
        '微静脉畸形': '#2196f3',
        '静脉畸形': '#03a9f4',
        '动静脉畸形': '#00bcd4',
        '淋巴管畸形': '#4fc3f7',
        '球细胞静脉畸形': '#29b6f6',
        '毛细血管-淋巴管-静脉畸形': '#42a5f5',
        '毛细血管-动静脉畸形': '#1e88e5',
        '淋巴管-静脉畸形': '#1976d2',

        // 血管假瘤/易混淆病变 - 绿色系
        '血管外皮细胞瘤': '#4caf50',
        '血管球瘤': '#66bb6a',
        '血管平滑肌瘤': '#81c784',
        '血管纤维瘤': '#a5d6a7',
        '靶样含铁血黄素沉积性血管瘤': '#c8e6c9',
        '鞋钉样血管瘤': '#388e3c',

        // 兼容旧版本
        'IH-婴幼儿血管瘤': '#ff5252',
        'RICH-先天性快速消退型血管瘤': '#ff7252',
        'PICH-先天性部分消退型血管瘤': '#ff9252',
        'NICH-先天性不消退型血管瘤': '#ffb252',
        'KHE-卡泊西型血管内皮细胞瘤': '#e91e63',
        'KH-角化型血管瘤': '#8bc34a',
        'PG-肉芽肿性血管瘤': '#cddc39',
        'MVM-微静脉畸形': '#2196f3',
        'VM-静脉畸形': '#03a9f4',
        'AVM-动静脉畸形': '#00bcd4',
        '血管瘤': '#ff5252',
        '淋巴管瘤': '#2196f3',
        '混合型': '#9c27b0',
        '其他': '#607d8b'
      };

      return colorMap[tag] || '#ff5252'; // 默认红色
    },
    deleteAnnotation(id) {
      // 记录当前标注数量，用于后续检查
      const beforeCount = this.annotations.length;
      console.log(`删除标注开始，当前标注数量: ${beforeCount}, 要删除的ID: ${id}`);
      
      const index = this.annotations.findIndex(item => item.id === id);
      if (index !== -1) {
        const annotation = this.annotations[index];
        
        // 显示删除中的加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在删除标注...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        
        // 从本地数组中删除
        this.annotations.splice(index, 1);
        console.log(`已从本地数组中删除标注，剩余标注数量: ${this.annotations.length}`);
        
        // 如果有数据库ID，则从数据库中也删除
        if (annotation.dbId) {
          console.log('正在从数据库删除标注，ID:', annotation.dbId);
          
          // 保存原始标注，用于恢复
          const originalAnnotation = {...annotation};
          
          api.tags.delete(annotation.dbId)
            .then(() => {
              console.log('标注已从数据库中删除');
              this.$message.success('标注已删除');
              loading.close();
              
              // 删除成功后，检查标注数量是否正确
              console.log(`删除成功后标注数量: ${this.annotations.length}`);
            })
            .catch(error => {
              console.error('删除标注失败', error);
              
              // 详细记录错误信息
              if (error.response) {
                console.error('服务器响应:', error.response.status, error.response.data);
                
                // 根据不同的错误状态码提供不同的错误信息
                if (error.response.status === 403) {
                  this.$message.error('您没有权限删除此标注');
                } else if (error.response.status === 404) {
                  this.$message.error('标注不存在或已被删除');
                } else {
                  this.$message.error(`删除失败: ${error.response.data || '服务器错误'}`);
                }
              } else if (error.request) {
                this.$message.error('网络错误，无法连接到服务器');
              } else {
                this.$message.error('删除标注失败: ' + error.message);
              }
              
              loading.close();
              
              // 恢复本地删除的标注 - 使用索引确保不会重复添加
              console.log(`恢复标注到索引 ${index}，恢复前标注数量: ${this.annotations.length}`);
              this.annotations.splice(index, 0, originalAnnotation);
              console.log(`恢复后标注数量: ${this.annotations.length}`);
              
              // 检查是否有重复标注
              this.checkForDuplicateAnnotations();
            });
        } else {
          // 如果没有数据库ID，只需要本地删除
          this.$message.success('标注已删除');
          loading.close();
          console.log(`本地标注删除完成，当前标注数量: ${this.annotations.length}`);
        }
      } else {
        console.error(`未找到ID为 ${id} 的标注`);
        this.$message.warning('未找到要删除的标注');
      }
    },
    
    // 检查并移除重复的标注
    checkForDuplicateAnnotations() {
      console.log('检查是否有重复标注...');
      
      // 使用Map记录已存在的标注ID
      const idMap = new Map();
      const duplicates = [];
      
      // 查找重复项
      this.annotations.forEach((annotation, index) => {
        if (idMap.has(annotation.id)) {
          duplicates.push(index);
        } else {
          idMap.set(annotation.id, index);
        }
      });
      
      // 如果找到重复项，从后向前删除（避免索引变化）
      if (duplicates.length > 0) {
        console.warn(`发现 ${duplicates.length} 个重复标注，将被移除`);
        for (let i = duplicates.length - 1; i >= 0; i--) {
          this.annotations.splice(duplicates[i], 1);
        }
        this.$message.warning(`已自动移除 ${duplicates.length} 个重复标注`);
      }
    },
    async saveAndNext() {
      return new Promise(async (resolve, reject) => {
        try {
          // 设置保存状态
          this.isSaving = true;
          
          // 输出调试信息
          console.log('[saveAndNext] 开始保存标注并跳转');
          console.log('[saveAndNext] 当前标注数量:', this.annotations.length);
          
          // 显示确认对话框
          try {
            await this.$confirm('是否继续填写表单？', '确认操作', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'info'
            });
            console.log('[saveAndNext] 用户确认继续');
          } catch (e) {
            // 用户取消了操作
            console.log('[saveAndNext] 用户取消了操作');
            this.isSaving = false; // 重置保存状态
            resolve(false);
            return;
          }
          
          console.log('[导航跟踪] 准备跳转到表单页，当前URL:', window.location.href);
          
          // 获取当前用户信息，确保认证有效
          const user = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.userInfo) || '{}');
          const userId = user.id || user.customId || '';
          const userRole = user.role || 'USER';
          const token = localStorage.getItem(CONFIG.STORAGE_KEYS.token);
          
          // 主动更新认证状态标记
          sessionStorage.setItem(CONFIG.STORAGE_KEYS.authValid, 'true');
          sessionStorage.setItem(CONFIG.STORAGE_KEYS.isNavigatingAfterSave, 'true');
          
          console.log('[认证状态] 用户ID:', userId, '角色:', userRole);
          
          // 确保诊断ID存在，尝试多种方式获取
          let diagnosisId = this.diagnosisId;
          
          // 如果diagnosisId不存在，尝试从路由参数获取
          if (!diagnosisId) {
            diagnosisId = this.$route.query.diagnosisId || this.$route.params.id;
          }
          
          // 如果仍然不存在，尝试从当前图像获取
          if (!diagnosisId && this.imageId) {
            diagnosisId = this.imageId;
            console.log('[诊断ID] 使用imageId作为诊断ID:', diagnosisId);
          }
          
          // 获取当前图像信息，用于保存标注
          const currentImage = this.uploadedImages && this.uploadedImages.length > 0 ? 
                              this.uploadedImages[this.currentImageIndex] : null;
          
          if (!currentImage || !currentImage.id) {
            console.error('[saveAndNext] 无法获取当前图像信息:', {
              hasUploadedImages: !!(this.uploadedImages && this.uploadedImages.length > 0),
              currentImageIndex: this.currentImageIndex
            });
            this.$message.warning('无法获取图像信息，将尝试使用备用方法保存标注');
          } else {
            console.log('[saveAndNext] 使用图像信息:', {
              id: currentImage.id,
              url: currentImage.url
            });
          }
          
          // 使用图像ID
          const imageId = currentImage?.id || diagnosisId || this.imageId;
          
          if (!imageId) {
            this.$message.error('无法获取有效的图像ID，无法保存标注');
            this.isSaving = false;
            resolve(false);
            return;
          }
          
          console.log('[saveAndNext] 使用的图像ID:', imageId);
          
          // 只有在有标注数据时才尝试保存
          if (this.annotations && this.annotations.length > 0) {
            try {
              // 显示保存中的加载状态
              const loading = this.$loading({
                lock: true,
                text: '保存标注并生成图像...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              
              // 添加重试机制
              const maxRetries = 3;
              let currentRetry = 0;
              let lastError = null;
              let saveSuccess = false;
              let errorMessage = '';
              
              while (currentRetry < maxRetries && !saveSuccess) {
                try {
                  console.log(`[saveAndNext] 尝试保存标注，第 ${currentRetry + 1} 次尝试`);
                  
                  // 修改为调用立即保存方法
                  await this.saveAnnotatedImageAfterEdit(imageId, true);
                  console.log('[saveAndNext] 标注图像保存成功');
                  
                  // 标记为成功
                  saveSuccess = true;
                  
                  // 在本地存储中保存标注数据作为备份
                  localStorage.setItem('annotations', JSON.stringify(this.annotations));
                  this.$message.success('标注已保存');
                  
                } catch (error) {
                  lastError = error;
                  console.error(`[saveAndNext] 第 ${currentRetry + 1} 次保存标注失败:`, error);
                  
                  // 获取详细错误信息用于显示
                  if (error.response) {
                    errorMessage = `服务器错误 (${error.response.status}): ${
                      typeof error.response.data === 'string' 
                        ? error.response.data 
                        : (error.response.data?.error || error.response.data?.message || JSON.stringify(error.response.data))
                    }`;
                  } else if (error.request) {
                    errorMessage = '服务器未响应';
                  } else {
                    errorMessage = error.message || '未知错误';
                  }
                  
                  // 增加重试间隔
                  await new Promise(resolve => setTimeout(resolve, 1000 * (currentRetry + 1)));
                  currentRetry++;
                }
              }
              
              // 关闭加载状态
              loading.close();
              
              if (!saveSuccess) {
                console.error('[saveAndNext] 所有保存尝试均失败');
                this.$message.error(`保存标注失败: ${errorMessage}`);
                
                // 询问用户是否要继续
                try {
                  await this.$confirm('保存标注失败，是否仍要继续到表单页面？', '提示', {
                    confirmButtonText: '继续',
                    cancelButtonText: '留在当前页面',
                    type: 'warning'
                  });
                  console.log('[saveAndNext] 用户选择继续到表单页面');
                  // 如果用户选择继续，我们会继续导航
                } catch (e) {
                  console.log('[saveAndNext] 用户选择留在当前页面');
                  this.isSaving = false;
                  resolve(false);
                  return;
                }
              }
            } catch (error) {
              console.error('[saveAndNext] 保存标注图像过程中出错:', error);
              this.$message.error('保存标注过程中出错: ' + (error.message || '未知错误'));
              
              // 询问用户是否要继续
              try {
                await this.$confirm('保存标注失败，是否仍要继续到表单页面？', '提示', {
                  confirmButtonText: '继续',
                  cancelButtonText: '留在当前页面',
                  type: 'warning'
                });
              } catch (e) {
                this.isSaving = false;
                resolve(false);
                return;
              }
            }
          } else {
            console.log('[saveAndNext] 没有标注数据，跳过保存步骤');
          }
          
          // 添加一个小延迟，确保状态保存后再导航
          await new Promise(resolve => setTimeout(resolve, CONFIG.UI.navigationDelay));
          
          // 如果仍然不存在，尝试从当前图像对象获取
          if (!diagnosisId && this.currentImageIndex >= 0 && this.uploadedImages && this.uploadedImages.length > 0) {
            const currentImage = this.uploadedImages[this.currentImageIndex];
            if (currentImage && currentImage.id) {
              diagnosisId = currentImage.id;
              console.log('[诊断ID] 使用当前图像ID作为诊断ID:', diagnosisId);
            }
          }
          
          // 最终确认是否有有效的诊断ID
          if (!diagnosisId) {
            this.$message.error('未找到诊断ID，无法继续');
            console.error('[导航错误] 未找到有效的诊断ID');
            console.log('[诊断状态]', {
              this_diagnosisId: this.diagnosisId,
              route_query: this.$route.query.diagnosisId,
              route_params: this.$route.params.id,
              imageId: this.imageId,
              currentImageIndex: this.currentImageIndex,
              hasUploadedImages: !!(this.uploadedImages && this.uploadedImages.length > 0)
            });
            this.isSaving = false;
            resolve(false);
            return;
          }
          
          console.log('[诊断ID] 最终使用的诊断ID:', diagnosisId);
          
          // 使用router进行导航，不使用window.location
          const route = `${CONFIG.ROUTES.structuredForm}?diagnosisId=${diagnosisId}`;
          console.log('[导航] 即将导航到:', route);
          
          try {
            await this.$router.push(route);
            console.log('[导航] 导航完成');
            resolve(true);
          } catch (routerError) {
            console.error('[导航] 路由导航失败:', routerError);
            
            // 如果路由导航失败，尝试备用方案
            try {
              await this.$router.replace(route);
              console.log('[导航] 备用导航完成');
              resolve(true);
            } catch (backupError) {
              console.error('[导航] 备用导航也失败:', backupError);
              this.$message.error('导航失败，请手动访问结构化表单页面');
              
              // 最后的备用方案，刷新页面
              this.isSaving = false;
              sessionStorage.setItem(CONFIG.STORAGE_KEYS.pendingDiagnosis, diagnosisId);
              setTimeout(() => {
                window.location.href = route;
              }, 200);
              resolve(false);
            }
          }
        } catch (error) {
          console.error('[saveAndNext] 操作失败:', error);
          this.$message.error('操作失败，请重试');
          reject(error);
        } finally {
          // 确保总是重置保存状态
          setTimeout(() => {
            this.isSaving = false;
          }, 1000);
        }
      });
    },
    // 新增：确认是否继续的辅助方法
    async confirmContinue() {
      try {
        await this.$confirm('保存标注失败，是否仍要继续到表单页面？', '提示', {
          confirmButtonText: '继续',
          cancelButtonText: '留在当前页面',
          type: 'warning'
        });
        return true;
      } catch (e) {
        return false;
      }
    },
    // 新增：通用的保存标注方法
    async saveAnnotations(loadingText = '保存中...') {
      // 获取当前图像
      const currentImage = this.uploadedImages[this.currentImageIndex]
      if (!currentImage || !currentImage.id) {
        this.$message.error('无效的图像，无法保存标注')
        return false
      }
      
      // 防止重复提交
      if (this.isSavingAnnotations) {
        console.warn('正在保存标注，请勿重复操作')
        return false
      }
      
      this.isSavingAnnotations = true
      
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: loadingText,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      try {
        // 调用API保存标注图像
        console.log('保存标注，图像ID:', currentImage.id)
        
        // 调用saveAnnotatedImage API（这个API会自动生成标注图像）
        try {
          const response = await api.tags.saveAnnotatedImage(currentImage.id)
          console.log('标注图像保存成功:', response.data)
        } catch (annotationError) {
          console.error('保存标注图像失败:', annotationError)
          
          // 如果是服务器错误，可以尝试更新原有的图像
          if (annotationError.response && annotationError.response.status >= 500) {
            try {
              console.log('尝试使用另一种方法更新标注图像')
              await api.tags.updateImageAfterAnnotation({ metadata_id: currentImage.id })
              console.log('更新标注图像成功')
            } catch (updateError) {
              console.error('更新标注图像也失败:', updateError)
              throw updateError
            }
          } else {
            throw annotationError
          }
        }
        
        // 更新图像状态为已标注，添加重试机制
        try {
          // 获取当前中国时间
          const now = new Date()
          const chinaTime = new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
            timeZone: 'Asia/Shanghai'
          }).format(now)
          
          // 转换为ISO-8601格式
          const isoTime = now.toISOString()
          console.log('当前中国时间:', chinaTime, '，ISO时间:', isoTime)
          
          // 传递时间戳参数，避免重复请求
          try {
            await api.images.markAsAnnotated(currentImage.id, isoTime)
            console.log('图像状态已成功更新为"已标注"(REVIEWED)')
          } catch (markError) {
            console.error('标记为已标注失败:', markError)
            // 如果失败是因为特定错误，不再重试
            if (markError.response && markError.response.data && 
                markError.response.data.includes('重复')) {
              console.log('检测到重复标记消息，不再重试')
            } else {
              // 尝试使用另一种方法更新状态
              await api.images.updateStatus(currentImage.id, 'REVIEWED')
              console.log('使用updateStatus成功更新状态为"已标注"')
            }
          }
        } catch (statusError) {
          console.error('所有更新图像状态的尝试都失败:', statusError)
          // 即使状态更新失败，我们仍然继续流程
        }
        
        // 显示成功消息
        this.$message.success('标注已保存')
        
        // 关闭加载状态并重置防重复标志
        loading.close()
        this.isSavingAnnotations = false
        return true
      } catch (error) {
        console.error('保存标注图片失败', error)
        
        // 获取详细错误信息
        let errorMessage = '保存标注失败'
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data)
          
          if (typeof error.response.data === 'string') {
            errorMessage += ': ' + error.response.data
          } else if (error.response.data && error.response.data.message) {
            errorMessage += ': ' + error.response.data.message
          } else {
            errorMessage += ' (状态码: ' + error.response.status + ')'
          }
        } else if (error.message) {
          errorMessage += ': ' + error.message
        }
        
        // 仅显示错误消息，不阻止流程继续
        this.$message.error(errorMessage)
        loading.close()
        this.isSavingAnnotations = false
        
        // 询问用户是否要继续，即使保存标注失败
        try {
          const result = await this.$confirm('保存标注失败，但已在本地保存标注数据。是否继续?', '警告', {
            confirmButtonText: '继续',
            cancelButtonText: '留在当前页面',
            type: 'warning'
          })
          
          // 如果用户选择继续，保存标注数据到本地存储
          if (result === 'confirm') {
            localStorage.setItem('annotations', JSON.stringify(this.annotations))
            localStorage.setItem('pendingImageId', currentImage.id.toString())
            return true
          }
          return false
        } catch (e) {
          // 用户选择留在当前页面
          return false
        }
      }
    },
    loadAnnotationsFromDatabase() {
      console.log('从数据库加载标注数据，图像ID:', this.imageId);
      
      return new Promise((resolve, reject) => {
        // 获取用户认证信息
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const token = localStorage.getItem('token') || '';
        const userId = user.id || user.customId || '';
        const userRole = user.role || 'USER';
        
        // 设置请求头
        const headers = {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json'
        };
        
        // 如果有token，添加认证头
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
        
        // 添加用户ID和角色头
        headers['X-User-Id'] = userId;
        headers['X-User-Role'] = userRole;
        
        console.log('加载标注数据 - 使用认证头:', {
          Authorization: token ? 'Bearer [已设置]' : '[未设置]',
          'X-User-Id': userId,
          'X-User-Role': userRole
        });
        
        // 添加时间戳和随机数，避免缓存问题
        const timestamp = Date.now();
        const random = Math.random();
        const cacheBuster = `t=${timestamp}&r=${random}`;
        
        // 使用fetch API加载标注数据
        const url = `/medical/api/tags/image/${this.imageId}?${cacheBuster}&userId=${userId}`;
        
        fetch(url, {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        })
          .then(response => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
          })
          .then(tags => {
            console.log('从数据库获取到标注数据:', tags);
            
            // 更新数据库标注数据
            this.dbAnnotations = tags.map(tag => ({
              id: tag.id,
              tag: tag.tagName || tag.tag, // 优先使用tagName字段，如果不存在则使用tag字段
              name: tag.tagName || tag.tag, // 同样优先使用tagName
              x: tag.x,
              y: tag.y,
              width: tag.width,
              height: tag.height,
              created_by: tag.created_by
            }));
            
            console.log('处理后的标注数据:', this.dbAnnotations);
            resolve(this.dbAnnotations);
          })
          .catch(error => {
            console.error('获取标注数据失败:', error);
            
            // 尝试备用API路径
            console.log('尝试备用API路径...');
            const backupUrl = `/api/tags/image/${this.imageId}?${cacheBuster}&userId=${userId}`;
            
            fetch(backupUrl, {
              method: 'GET',
              headers: headers,
              credentials: 'include'
            })
              .then(response => {
                if (!response.ok) {
                  throw new Error(`备用路径也失败! status: ${response.status}`);
                }
                return response.json();
              })
              .then(tags => {
                console.log('通过备用路径获取到标注数据:', tags);
                
                // 更新数据库标注数据
                this.dbAnnotations = tags.map(tag => ({
                  id: tag.id,
                  tag: tag.tagName || tag.tag,
                  name: tag.tagName || tag.tag,
                  x: tag.x,
                  y: tag.y,
                  width: tag.width,
                  height: tag.height,
                  created_by: tag.created_by
                }));
                
                console.log('处理后的标注数据:', this.dbAnnotations);
                resolve(this.dbAnnotations);
              })
              .catch(backupError => {
                console.error('所有尝试都失败:', backupError);
                
                // 如果都失败了，检查是否有本地保存的标注数据
                const localAnnotations = localStorage.getItem(`annotations_${this.imageId}`);
                if (localAnnotations) {
                  try {
                    const parsedAnnotations = JSON.parse(localAnnotations);
                    console.log('找到本地保存的标注数据:', parsedAnnotations);
                    this.dbAnnotations = parsedAnnotations;
                    resolve(this.dbAnnotations);
                  } catch (e) {
                    console.error('解析本地标注数据失败:', e);
                    reject(backupError);
                  }
                } else {
                  reject(backupError);
                }
              });
          });
      });
    },
    updateAnnotationInDatabase(annotation) {
      // 获取当前用户信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user || (!user.id && !user.customId)) {
        this.$message.error('未检测到有效用户信息，无法更新标注');
        return;
      }
      
      const userId = user.id || user.customId;
      const userRole = user.role || 'USER';
      
      // 如果没有数据库ID，则无法更新
      if (!annotation.dbId) {
        console.warn('标注没有数据库ID，无法更新:', annotation);
        return;
      }
      
      // 计算归一化坐标（将像素坐标转换为0-1范围）
      // 确保imageWidth和imageHeight不为0
      if (!this.imageWidth || !this.imageHeight) {
        this.$message.error('图片尺寸无效，无法计算标注坐标');
        return;
      }
      
      // 归一化计算
      let normalizedX, normalizedY, normalizedWidth, normalizedHeight;
      
      // 根据标注类型计算归一化坐标
      if (annotation.isYoloFormat) {
        // 如果是YOLO格式的标注，使用中心点坐标系统
        // 先将左上角坐标转换为中心点坐标
        normalizedWidth = annotation.width / this.imageWidth;
        normalizedHeight = annotation.height / this.imageHeight;
        normalizedX = (annotation.x / this.imageWidth) + (normalizedWidth / 2);
        normalizedY = (annotation.y / this.imageHeight) + (normalizedHeight / 2);
      } else {
        // 普通左上角坐标系统
        normalizedX = annotation.x / this.imageWidth;
        normalizedY = annotation.y / this.imageHeight;
        normalizedWidth = annotation.width / this.imageWidth;
        normalizedHeight = annotation.height / this.imageHeight;
      }
      
      // 验证并修正坐标范围
      // x, y必须在[0,1]之间
      normalizedX = Math.max(0, Math.min(1, normalizedX));
      normalizedY = Math.max(0, Math.min(1, normalizedY));
      
      // width, height必须在(0,1]之间
      normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth));
      normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight));
      
      // 保留4位小数
      normalizedX = Number(normalizedX.toFixed(4));
      normalizedY = Number(normalizedY.toFixed(4));
      normalizedWidth = Number(normalizedWidth.toFixed(4));
      normalizedHeight = Number(normalizedHeight.toFixed(4));
      
      // 构建要发送到后端的数据
      const tagData = {
        tag: annotation.tag,
        x: normalizedX,
        y: normalizedY,
        width: normalizedWidth,
        height: normalizedHeight,
        updated_by: userId,
        coord_system: annotation.isYoloFormat ? 'yolo_center' : 'top_left'
      };
      
      console.log('更新标注数据:', {
        id: annotation.dbId,
        归一化坐标: `(${normalizedX}, ${normalizedY})`,
        归一化尺寸: `${normalizedWidth} x ${normalizedHeight}`,
        坐标系统: annotation.isYoloFormat ? 'YOLO中心点' : '左上角'
      });
      
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '正在更新标注...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      // 设置请求头
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-User-Id': userId,
        'X-User-Role': userRole,
        'Cache-Control': 'no-cache'
      };
      
      // 添加时间戳，避免缓存问题
      const timestamp = Date.now();
      
      // 使用fetch API直接发送请求，避免可能的缓存问题
      fetch(`/medical/api/tags/${annotation.dbId}?t=${timestamp}&userId=${userId}`, {
        method: 'PUT',
        headers: headers,
        body: JSON.stringify(tagData),
        credentials: 'include'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          loading.close();
          console.log('标注已更新', data);
          this.$message.success('标注已更新');
          
          // 更新本地标注的归一化坐标
          annotation.normalizedX = normalizedX;
          annotation.normalizedY = normalizedY;
          annotation.normalizedWidth = normalizedWidth;
          annotation.normalizedHeight = normalizedHeight;
          
          // 安排延迟保存标注图像，而不是立即保存
          this.scheduleSaveAnnotatedImage(this.imageId);
        })
        .catch(error => {
          console.error('更新标注失败', error);
          loading.close();
          
          // 尝试备用API路径
          console.log('尝试备用API路径...');
          fetch(`/api/tags/${annotation.dbId}?t=${timestamp}&userId=${userId}`, {
            method: 'PUT',
            headers: headers,
            body: JSON.stringify(tagData),
            credentials: 'include'
          })
            .then(response => {
              if (!response.ok) {
                throw new Error(`备用路径也失败! status: ${response.status}`);
              }
              return response.json();
            })
            .then(data => {
              console.log('通过备用路径更新标注成功:', data);
              this.$message.success('标注已更新');
              
              // 更新本地标注的归一化坐标
              annotation.normalizedX = normalizedX;
              annotation.normalizedY = normalizedY;
              annotation.normalizedWidth = normalizedWidth;
              annotation.normalizedHeight = normalizedHeight;
              
              // 安排延迟保存标注图像，而不是立即保存
              this.scheduleSaveAnnotatedImage(this.imageId);
            })
            .catch(backupError => {
              console.error('所有尝试都失败:', backupError);
              this.$message.error('更新标注失败，请稍后重试');
            });
        });
    },
    
    // 新增：编辑后保存标注图像
    async saveAnnotatedImageAfterEdit(imageId, immediate = false) {
      if (!imageId) {
        console.error('保存标注图像失败：缺少图像ID');
        this.$message.error('保存标注图像失败：缺少图像ID');
        return;
      }
      
      // 如果不是立即保存且有计时器正在运行，则不执行保存
      if (!immediate && this.saveImageTimer) {
        console.log('已经有延迟保存计划，不重复保存');
        return;
      }
      
      // 清除计时器（如果存在）
      if (this.saveImageTimer) {
        clearTimeout(this.saveImageTimer);
        this.saveImageTimer = null;
      }
      
      console.log('编辑标注完成，正在保存标注图像，图像ID:', imageId);
      
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '正在保存标注图像...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      try {
        // 调用API保存标注图像
        const response = await api.tags.saveAnnotatedImage(imageId);
        console.log('标注图像保存成功:', response.data);
        
        // 如果返回了新的处理后图像路径，更新图像
        if (response.data && response.data.processedPath) {
          console.log('更新处理后的图像路径:', response.data.processedPath);
          // 添加时间戳避免缓存问题
          const timestamp = Date.now();
          const newImageUrl = `${response.data.processedPath}?t=${timestamp}`;
          
          // 更新当前图像的URL
          if (this.currentImage) {
            this.processedFilePath = newImageUrl;
          }
          
          this.$message.success('标注图像已保存');
        } else {
          this.$message.warning('标注图像已保存，但未返回新图像路径');
        }
        
        loading.close();
      } catch (error) {
        console.error('保存标注图像失败:', error);
        this.$message.error('保存标注图像失败，请稍后重试');
        loading.close();
      }
    },
    // 新增：处理标注数据的方法
    processLoadedAnnotations() {
      // 关键修复：添加"哨兵"判断，防止重复执行
      if (this.processedAnnotations) {
        console.warn('标注已处理过，跳过重复渲染，防止生成重复的标注框。');
        return;
      }
      
      // 确保有尺寸和标注数据
      if (this.imageWidth <= 0 || this.imageHeight <= 0) {
        console.warn('图像尺寸无效，无法处理标注', {
          w: this.imageWidth, h: this.imageHeight
        });
        return;
      }
      
      if (!this.dbAnnotations || this.dbAnnotations.length === 0) {
        console.log('没有标注数据需要处理');
        this.annotations = []; // 确保清空
        return;
      }
      
      console.log('开始处理已加载的标注数据，图像尺寸:', 
                 `显示: ${this.imageWidth}x${this.imageHeight}`,
                 `原始: ${this.originalWidth}x${this.originalHeight}`,
                 '标注数量:', this.dbAnnotations.length);
      
      const newAnnotations = [];
      
      // 转换数据库标注为本地格式
      this.dbAnnotations.forEach(dbTag => {
        const tagName = dbTag.tagName || dbTag.tag || 'IH-婴幼儿血管瘤';
        
        // 确保坐标值是数值类型
        const normCenterX = parseFloat(dbTag.x) || 0;
        const normCenterY = parseFloat(dbTag.y) || 0;
        const normWidth = parseFloat(dbTag.width) || 0;
        const normHeight = parseFloat(dbTag.height) || 0;
        
        // 检查坐标是否有效
        if (normWidth <= 0 || normHeight <= 0) {
          console.warn(`标注 ${dbTag.id} 的尺寸无效，跳过该标注`);
          return;
        }

        // 1. 将归一化尺寸转换为像素尺寸
        const pixelWidth = normWidth * this.imageWidth;
        const pixelHeight = normHeight * this.imageHeight;

        // 2. 将归一化中心点转换为像素中心点
        const pixelCenterX = normCenterX * this.imageWidth;
        const pixelCenterY = normCenterY * this.imageHeight;

        // 3. 从像素中心点计算左上角坐标
        const finalX = pixelCenterX - (pixelWidth / 2);
        const finalY = pixelCenterY - (pixelHeight / 2);
        
        console.log(`处理标注 ${dbTag.id}: 中心点(${pixelCenterX.toFixed(2)}, ${pixelCenterY.toFixed(2)}) -> 左上角(${finalX.toFixed(2)}, ${finalY.toFixed(2)})`);

        const annotation = {
          id: dbTag.id,
          dbId: dbTag.id,
          tag: tagName,
          x: finalX,
          y: finalY,
          width: pixelWidth,
          height: pixelHeight,
          // 保存原始的归一化YOLO坐标
          normalizedX: normCenterX,
          normalizedY: normCenterY,
          normalizedWidth: normWidth,
          normalizedHeight: normHeight,
          imageIndex: this.currentImageIndex,
          type: 'rectangle',
          isYoloFormat: true // 添加标志位
        };
        
        newAnnotations.push(annotation);
      });
      
      this.annotations = newAnnotations;
      this.processedAnnotations = true;
      this.annotationsLoaded = true;
    },
    goBack() {
      // 提示用户可能丢失未保存的标注
      console.log('[导航跟踪] 点击返回按钮，当前URL:', window.location.href);
      
      this.$confirm('返回上传页面可能会丢失当前未保存的标注，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 设置返回工作台标记，防止401错误时跳转到登录页
        sessionStorage.setItem('returningToWorkbench', 'true');
        console.log('[导航跟踪] 用户确认返回，准备导航到: /app/cases/new');
        
        // 检查是否有图像对ID和已标注的图像
        if (this.imagePairId) {
          // 显示加载状态
          const loading = this.$loading({
            lock: true,
            text: '正在处理...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 使用新API直接删除标注图片并清空路径
          api.imagePairs.deleteAnnotatedImage(this.imagePairId)
            .then(response => {
              console.log('标注图片删除结果:', response.data);
              loading.close();
              // 用户确认后，返回到上传图片页面
              console.log('[导航跟踪] 导航到: /app/cases/new (API调用后)');
              
              // 记录导航前的信息
              const beforeUrl = window.location.href;
              
              this.$router.push('/app/cases/new').then(() => {
                console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);
              }).catch(err => {
                console.error('[导航跟踪] 导航失败:', err);
                // 备用方案，使用window.location
                window.location.href = '/app/cases/new';
              });
            })
            .catch(error => {
              console.error('删除标注图片失败', error);
              loading.close();
              // 即使失败也返回上一页
              this.$message.error('删除标注图片失败，但仍将返回上一页');
              
              console.log('[导航跟踪] 导航到: /app/cases/new (API调用失败后)');
              const beforeUrl = window.location.href;
              
              this.$router.push('/app/cases/new').then(() => {
                console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);
              }).catch(err => {
                console.error('[导航跟踪] 导航失败:', err);
                // 备用方案，使用window.location
                window.location.href = '/app/cases/new';
              });
            });
        } else {
          // 如果没有图像对ID，直接返回上一页
          console.log('[导航跟踪] 导航到: /app/cases/new (无图像对ID)');
          const beforeUrl = window.location.href;
          
          this.$router.push('/app/cases/new').then(() => {
            console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);
          }).catch(err => {
            console.error('[导航跟踪] 导航失败:', err);
            // 备用方案，使用window.location
            window.location.href = '/app/cases/new';
          });
        }
      }).catch(() => {
        // 用户取消，不做任何操作
        console.log('[导航跟踪] 用户取消返回操作');
      });
    },
    tryLoadImagePathFromMetadata(imageId, callback) {
      console.log('从image_metadata表获取图像路径:', imageId);
      
      api.images.getOne(imageId)
        .then(response => {
          if (response.data && response.data.path) {
            console.log('成功从image_metadata获取图像路径:', response.data.path);
            callback(response.data.path);
          } else {
            console.error('image_metadata中图像路径不存在');
            callback(null);
          }
        })
        .catch(error => {
          console.error('从image_metadata获取图像路径失败:', error);
          callback(null);
        });
    },
    updateImagePairPath(metadataId, path) {
      if (!this.imagePairId || !path) {
        console.error('缺少imagePairId或path，无法更新');
        return;
      }
      
      console.log('更新image_pairs表中的图像路径:', {
        imagePairId: this.imagePairId,
        metadataId: metadataId,
        path: path
      });
      
      // 构建要更新的数据
      const updateData = {
        id: this.imagePairId,
        metadataId: metadataId.toString(),
        imageOnePath: path
      };
      
      // 使用API更新image_pairs表
      fetch(`/api/image-pairs`, {
        method: 'POST',  // 使用POST方法
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(updateData),
        credentials: 'include'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(result => {
          console.log('成功更新image_pairs中的图像路径:', result);
        })
        .catch(error => {
          console.error('更新image_pairs中的图像路径失败:', error);
        });
    },
    // 加载图像标注
    loadAnnotations(imageId) {
      if (!imageId) {
        console.error('loadAnnotations: 图像ID无效，无法加载标注');
        return;
      }
      
      // 重置标注加载状态
      this.annotationsLoaded = false;
      
      console.log('开始加载图像标注，ID:', imageId);
      
      // 获取当前用户
      const user = JSON.parse(localStorage.getItem('user')) || {};
      const userId = user.customId || user.id;
      
      if (!userId) {
        console.warn('未找到用户ID，标注加载可能存在权限问题');
      }
      
      // 显示加载中提示
      this.$message.info('正在加载标注数据...');
      
      // 添加时间戳避免缓存问题
      const timestamp = new Date().getTime();
      console.log(`请求时间戳: ${timestamp}, 使用edit模式加载标注`);
      
      // 使用API工具类获取标注，添加edit模式参数
      api.tags.getByImageId(imageId, 'edit')
        .then(response => {
          console.log('成功获取图像标注数据:', response.data);
          
          // 确保返回的数据是数组
          this.dbAnnotations = response.data || [];
          
          // 如果返回的不是数组，记录错误并转换为空数组
          if (!Array.isArray(this.dbAnnotations)) {
            console.error('从服务器获取的标注数据不是数组格式:', this.dbAnnotations);
            this.dbAnnotations = [];
          }
          
          // 显示获取到的标注数量
          console.log(`成功获取到 ${this.dbAnnotations.length} 个标注`);
          if (this.dbAnnotations.length > 0) {
            this.$message.success(`已加载 ${this.dbAnnotations.length} 个标注`);
          } else {
            this.$message.info('没有找到标注数据，您可以添加新标注');
          }
          
          // 如果图像已加载并且有尺寸，立即处理标注
          if (this.imageWidth > 0 && this.imageHeight > 0) {
            this.processLoadedAnnotations();
          } else {
            console.log('图像尺寸尚未加载，将在图像加载后处理标注');
            // 标注数据已存储在this.dbAnnotations中，将在图像加载后处理
          }
        })
        .catch(error => {
          console.error('加载标注数据失败:', error);
          this.dbAnnotations = [];
          this.annotationsLoaded = true; // 即使加载失败也标记为已处理，防止无限循环
          this.$message.error('加载标注失败，但您仍可以添加新标注');
        });
    },
    
    // 刷新认证信息
    refreshAuthentication() {
      // 尝试刷新用户认证信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user.id || user.customId) {
        console.log('尝试刷新用户认证信息');
        // 这里可以添加刷新token的逻辑，如果系统支持
      }
    },
    // 添加一个新方法用于尝试从localStorage加载离线数据
    tryLoadOfflineData(imageId) {
      console.log('尝试从localStorage加载离线数据...');
      
      // 尝试从localStorage获取图像数据
      const offlineImageData = localStorage.getItem(`offline_image_${imageId}`);
      if (offlineImageData) {
        console.log('找到离线图像数据');
        
        // 创建临时图像对象
        this.imageData = {
          id: imageId,
          path: offlineImageData
        };
        
        // 创建临时图像对
        this.imagePair = {
          id: Date.now(),
          metadataId: imageId,
          imageOnePath: offlineImageData
        };
        
        // 加载图像
        this.loadImage();
      } else {
        console.warn('未找到离线图像数据');
      }
    },
    // 提交表单
    submitForm() {
      // 先验证表单
      this.$refs.structuredForm.validate(valid => {
        if (valid) {
          console.log('表单验证通过，准备提交数据');
          
          // 先获取最新的标注数据
          this.loadAnnotationsFromDatabase().then(() => {
            // 显示加载提示
            const loading = this.$loading({
              lock: true,
              text: '正在保存表单数据...',
              spinner: 'el-icon-loading',
              background: 'rgba(255, 255, 255, 0.7)'
            });
            
            // 准备表单数据
            const formData = { ...this.formData };
            
            // 添加标注数据到表单
            formData.annotations = this.dbAnnotations;
            
            console.log('提交表单数据:', formData);
            
            // 使用API保存表单数据
            api.images.saveStructuredFormData(this.imageId, formData)
              .then(response => {
                console.log('表单数据保存成功:', response.data);
                loading.close();
                this.$message.success('表单数据保存成功');
                
                // 跳转到病例列表页面
                this.$router.push('/app/cases');
              })
              .catch(error => {
                console.error('表单数据保存失败:', error);
                loading.close();
                this.$message.error('表单数据保存失败: ' + (error.message || '未知错误'));
              });
          }).catch(error => {
            console.error('获取标注数据失败:', error);
            this.$message.warning('无法获取最新标注数据，将只保存表单信息');
            
            // 即使获取标注失败，也继续保存表单
            this.submitFormOnly();
          });
        } else {
          console.error('表单验证失败');
          this.$message.error('表单验证失败，请检查输入');
          return false;
        }
      });
    },
    
    // 仅提交表单数据
    submitFormOnly() {
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在保存表单数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      });
      
      // 准备表单数据
      const formData = { ...this.formData };
      
      console.log('提交表单数据:', formData);
      
      // 使用API保存表单数据
      api.images.saveStructuredFormData(this.imageId, formData)
        .then(response => {
          console.log('表单数据保存成功:', response.data);
          loading.close();
          this.$message.success('表单数据保存成功');
          
          // 跳转到病例列表页面
          this.$router.push('/app/cases');
        })
        .catch(error => {
          console.error('表单数据保存失败:', error);
          loading.close();
          this.$message.error('表单数据保存失败: ' + (error.message || '未知错误'));
        });
    },
    // 加载表单数据
    loadFormData() {
      console.log('加载表单数据，图像ID:', this.imageId);
      
      // 使用API获取图像元数据
      api.images.getOne(this.imageId)
        .then(response => {
          console.log('获取图像元数据成功:', response.data);
          const imageData = response.data;
          
          // 如果有结构化表单数据，填充到表单中
          if (imageData.structuredForm) {
            this.formData = {
              ...this.formData,
              ...imageData.structuredForm
            };
            console.log('已加载结构化表单数据:', this.formData);
          } else {
            console.log('图像没有结构化表单数据');
          }
        })
        .catch(error => {
          console.error('加载表单数据失败:', error);
          this.$message.warning('加载表单数据失败，将使用空表单');
        });
    },
    // 异步加载页面数据，解决时序问题
    async loadPageData(diagnosisId) {
      if (!diagnosisId) {
        return Promise.reject('未提供诊断ID');
      }

      console.log(`开始为ID: ${diagnosisId} 加载页面核心数据`);
      try {
        // 首先尝试从血管瘤诊断API获取数据
      try {
        const response = await axios.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);
        const data = response.data;
          console.log('从血管瘤诊断API获取到的核心数据:', data);

        if (data && data.imagePath) {
          const imageUrl = data.imagePath;
          
          this.uploadedImages = [{
            id: diagnosisId,
            url: imageUrl,
            filename: `诊断图像 #${diagnosisId}`
            }];
            this.currentImage = this.uploadedImages[0];
            this.processedFilePath = imageUrl;
            
            console.log('图片信息设置成功，URL:', imageUrl);
            return Promise.resolve();
          }
        } catch (diagnosisError) {
          console.log('从血管瘤诊断API获取数据失败，尝试普通图像API:', diagnosisError.message);
        }
        
        // 如果血管瘤诊断API失败，尝试从普通图像API获取数据
        console.log('尝试从普通图像API获取数据，ID:', diagnosisId);
        const imageResponse = await axios.get(`/api/images/${diagnosisId}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });
        
        const imageData = imageResponse.data;
        console.log('从普通图像API获取到的数据:', imageData);
        
        if (imageData && (imageData.path || imageData.url)) {
          const imageUrl = imageData.path || imageData.url;
          
          this.uploadedImages = [{
            id: diagnosisId,
            url: imageUrl,
            filename: imageData.original_name || `图像 #${diagnosisId}`
          }];
          this.currentImage = this.uploadedImages[0];
          this.processedFilePath = imageUrl;
          
          console.log('图片信息设置成功，URL:', imageUrl);
          return Promise.resolve();
        } else {
          throw new Error('响应数据中缺少图像路径');
        }
      } catch (error) {
        console.error(`加载页面数据失败 (ID: ${diagnosisId}):`, error);
        this.$message.error(`加载图像信息失败: ${error.message}`);
        return Promise.reject(error);
      }
    },
    // 重新加载标注数据
    reloadAnnotations() {
      console.log('手动刷新标注数据');
      
      // 清除当前标注
      this.annotations = [];
      this.dbAnnotations = [];
      this.annotationsLoaded = false;
      
      // 显示加载中提示
      this.$message.info('正在重新加载标注数据...');
      
      // 重新加载标注
      this.loadAnnotationsFromDatabase()
        .then(() => {
          // 如果图像已加载，处理标注
          if (this.imageWidth > 0 && this.imageHeight > 0) {
            this.processLoadedAnnotations();
          }
          
          // 显示成功消息
          this.$message.success(`已加载 ${this.dbAnnotations.length} 个标注`);
        })
        .catch(error => {
          console.error('重新加载标注失败:', error);
          this.$message.error('重新加载标注失败，请刷新页面重试');
        });
    },
    // 添加一个直接的按钮点击处理方法，便于调试
    handleNextButtonClick(event) {
      console.log('下一步按钮被点击', event);
      // 防止事件冒泡
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }
      
      // 添加额外的调试信息
      console.log('当前组件状态:', {
        imageId: this.imageId,
        hasAnnotations: this.annotations.length > 0,
        isSaving: this.isSaving,
        currentImageIndex: this.currentImageIndex,
        hasUploadedImages: !!(this.uploadedImages && this.uploadedImages.length > 0)
      });
      
      // 设置超时，如果正常流程在5秒内没有完成，则使用备用导航
      const navigationTimeout = setTimeout(() => {
        console.log('[handleNextButtonClick] 导航超时，使用备用导航');
        this.directNavigateToForm();
      }, 5000);
      
      // 调用原有的保存方法
      this.saveAndNext().finally(() => {
        // 清除超时
        clearTimeout(navigationTimeout);
      });
    },
    // 添加直接导航方法，确保即使其他方法失败也能导航到下一页
    directNavigateToForm() {
      try {
        // 获取诊断ID
        const diagnosisId = this.diagnosisId || this.$route.query.diagnosisId || this.$route.params.id || this.imageId;
        
        if (!diagnosisId) {
          console.error('[directNavigateToForm] 无法获取诊断ID');
          this.$message.error('无法获取诊断ID，无法导航');
          return false;
        }
        
        // 构建导航URL
        const route = `${CONFIG.ROUTES.structuredForm}?diagnosisId=${diagnosisId}`;
        console.log('[directNavigateToForm] 直接导航到:', route);
        
        // 使用window.location直接导航
        window.location.href = route;
        return true;
      } catch (error) {
        console.error('[directNavigateToForm] 导航失败:', error);
        return false;
      }
    },
    // 新增：安排延迟保存标注图像
    scheduleSaveAnnotatedImage(imageId) {
      console.log('安排延迟保存标注图像，10秒后执行...');
      
      // 清除之前的计时器（如果存在）
      if (this.saveImageTimer) {
        console.log('清除之前的保存计时器');
        clearTimeout(this.saveImageTimer);
      }
      
      // 设置新的计时器，10秒后执行
      this.saveImageTimer = setTimeout(() => {
        console.log('10秒延迟后执行保存标注图像');
        this.saveAnnotatedImageAfterEdit(imageId);
        this.saveImageTimer = null;
      }, 10000); // 10秒延迟
    },
    // 添加saveAndExit方法，立即保存标注图像并退出
    async saveAndExit() {
      if (this.annotations.length === 0) {
        // 如果没有标注，直接退出
        this.$router.push('/app/cases');
        return;
      }
      
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '保存中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      const currentImage = this.uploadedImages[this.currentImageIndex];
      if (!currentImage || !currentImage.id) {
        this.$message.error('图像信息不完整，无法保存标注');
        loading.close();
        return;
      }
      
      try {
        console.log('saveAndExit: 使用真实图像ID保存标注:', currentImage.id);
        
        // 立即保存标注图像，不使用延迟
        await this.saveAnnotatedImageAfterEdit(currentImage.id, true);
        
        localStorage.setItem('annotations', JSON.stringify(this.annotations));
        this.$message.success('标注已保存');
        
        loading.close();
        
        // 退出到病例列表
        localStorage.setItem('isSaveAndExit', 'true');
        this.$router.push('/app/cases');
      } catch (error) {
        console.error('保存标注失败:', error);
        this.$message.error('保存标注失败: ' + (error.message || '未知错误'));
        loading.close();
      }
    },
  }
}
</script>

<style scoped>
/* Add all styles from CaseDetailForm.vue here */
.annotation-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #ddd;
}

.main-content {
  display: flex;
  flex: 1;
}

.toolbar {
  width: 300px;
  padding: 20px;
  border-right: 1px solid #ddd;
  overflow-y: auto;
}

.tool-section {
  margin-bottom: 20px;
}

.annotations-list {
  margin-top: 20px;
}

.annotation-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.image-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.image-container {
  position: relative;
}

.annotation-image {
  max-width: 500px;
  max-height: 500px;
  display: block;
}

.annotation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  cursor: crosshair;
}

.annotation-box {
  position: absolute;
  border: 2px solid;
  box-sizing: border-box;
}

.annotation-label {
  position: absolute;
  top: -20px;
  left: 0;
  color: white;
  padding: 2px 5px;
  font-size: 12px;
  white-space: nowrap;
}

.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: white;
  border: 1px solid black;
}

.top-left { top: -5px; left: -5px; cursor: nwse-resize; }
.top-right { top: -5px; right: -5px; cursor: nesw-resize; }
.bottom-left { bottom: -5px; left: -5px; cursor: nesw-resize; }
.bottom-right { bottom: -5px; right: -5px; cursor: nwse-resize; }

.drawing-box {
  position: absolute;
  border: 2px dashed #007bff;
  box-sizing: border-box;
}

.navigation-controls {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  border-top: 1px solid #ddd;
}

.form-section {
  padding: 20px;
}
</style> 