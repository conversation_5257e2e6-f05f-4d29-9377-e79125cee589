<template>
  <div class="login-page login-background">
    <div class="login-container">
      <!-- 左侧信息区域 -->
      <div class="login-info">
        <h1 class="system-name">血管瘤人工智能<br/>辅助治疗系统</h1>
        <p class="system-slogan">即刻体验智能诊断与专业医疗支持</p>
        <div class="feature-list">
          <div class="feature-item">AI智能诊断平台</div>
          <div class="feature-item">精准血管瘤识别</div>
          <div class="feature-item">专业医疗数据平台</div>
          <div class="feature-item">智慧医疗辅助系统</div>
        </div>
        <div class="learn-more">
          <a href="#" class="learn-more-link">了解更多 ›</a>
        </div>
      </div>
      
      <!-- 右侧登录框 -->
      <div class="login-form-container">
        <div class="login-form-box">
          <div class="login-tabs">
            <div class="tab-item active">账号密码登录</div>
          </div>
          
          <div class="login-form">
        <div v-if="error" class="alert alert-danger">
          {{ error }}
        </div>
        <div v-if="success" class="alert alert-success">
          {{ success }}
        </div>
        
        <form @submit.prevent="handleLogin">
              <div class="form-group">
                <div class="input-with-icon">
                  <i class="icon-user"></i>
            <input 
              id="email" 
              v-model="email" 
              type="email" 
                    placeholder="请输入邮箱地址"
              required
              :disabled="loading"
            >
          </div>
              </div>
              
              <div class="form-group">
                <div class="input-with-icon">
                  <i class="icon-lock"></i>
            <input 
              id="password" 
              v-model="password" 
              type="password" 
                    placeholder="请输入密码"
              required
              :disabled="loading"
            >
          </div>
              </div>
              
              <div class="form-options">
                <div class="remember-me">
            <input 
              id="remember" 
              v-model="remember" 
              type="checkbox" 
              :disabled="loading"
            >
                  <label for="remember">下次自动登录</label>
                </div>
                <router-link to="/forgot-password" class="forgot-password">忘记密码?</router-link>
          </div>
              
              <div class="form-actions">
            <button 
              type="submit" 
                  class="login-btn" 
              :disabled="loading"
            >
                  <span v-if="loading" class="spinner"></span>
                  <span>{{ loading ? '登录中...' : '登 录' }}</span>
            </button>
          </div>
        </form>
        
            <div class="register-link">
              <span>没有账号?</span>
              <router-link to="/register" class="register-btn">立即注册</router-link>
        </div>
        
        <!-- 如果是从标注页面重定向过来的，显示提示 -->
            <div v-if="imageId" class="redirect-notice">
          检测到您之前正在标注图片(ID: {{ imageId }})，登录后将自动跳转回表单页面。
        </div>
      </div>
          
          <div class="login-footer">
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import api from '@/utils/api'  // 导入API模块

export default {
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    const email = ref('')
    const password = ref('')
    const remember = ref(false)
    const loading = ref(false)
    const error = ref('')
    const success = ref('')
    const imageId = ref(null)
    
    // 检查是否是从"保存并退出"操作过来
    onMounted(() => {
      // 仅在非应用内导航或无特殊标记时才执行清理
      const isAppOperation = sessionStorage.getItem('isAppOperation') === 'true';
      const preservedUser = sessionStorage.getItem('preservedUser');

      if (!isAppOperation && !preservedUser) {
        console.log("执行用户数据清理逻辑");
      cleanInvalidUserData();
      }
      // 清理标记
      sessionStorage.removeItem('isAppOperation');
      
      // 先尝试恢复用户会话，这对于从标注页面退出很重要
      if (preservedUser) {
        console.log('检测到保存的用户会话，尝试恢复');
        localStorage.setItem('user', preservedUser);
        sessionStorage.removeItem('preservedUser');
        
        // 检查是否应该重定向到工作台
        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/app/dashboard';
        console.log('恢复用户会话后将重定向到:', redirectPath);
        
        // 延迟导航以确保状态更新
        setTimeout(() => {
          router.push(redirectPath);
        }, 100);
        return;
      }
      
      // 首先检查是否有保存并退出的标记
      const isSaveAndExit = localStorage.getItem('isSaveAndExit') === 'true';
      if (isSaveAndExit) {
        console.log('登录页面检测到保存并退出标记，将自动重定向到工作台');
        // 清除标记
        localStorage.removeItem('isSaveAndExit');
        
        // 直接跳转到工作台
        router.push('/app/dashboard');
        return;
      }
      
      // 从query参数中提取imageId
      if (route.query.imageId) {
        imageId.value = route.query.imageId
        console.log(`检测到从标注页面跳转，图片ID: ${imageId.value}`)
      }
    })
    
    const handleLogin = async () => {
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        // 在登录之前清除所有与Dashboard相关的缓存
        console.log('登录前清除所有Dashboard相关缓存...');
        localStorage.removeItem('dashboardStats');
        localStorage.removeItem('tempApiData');
        localStorage.removeItem('dashboardApiResponse');
        
        // 查找和清除所有包含dashboard或stats的缓存项
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('dashboard') || key.includes('stats'))) {
            keysToRemove.push(key);
          }
        }
        
        // 删除找到的缓存项
        keysToRemove.forEach(key => {
          console.log('清除缓存项:', key);
          localStorage.removeItem(key);
        });
        
        // 执行登录
        const userData = await store.dispatch('login', {
          email: email.value,
          password: password.value
        })
        
        // 检查返回值是否为错误信息或非对象（表示登录失败）
        if (typeof userData === 'string' || !userData || typeof userData !== 'object') {
          // 将英文错误信息翻译成中文
          if (typeof userData === 'string') {
            if (userData === 'Invalid credentials') {
              error.value = '用户名或密码错误';
            } else {
              error.value = userData;
            }
          } else {
            error.value = '登录失败，请检查邮箱和密码';
          }
          return;
        }
        
        // 登录成功后处理用户信息
        console.log('登录成功，检查用户信息:', userData);
        
        // 执行全局权限修复函数
        if (window.fixUserPermission) {
          const result = window.fixUserPermission();
          console.log('登录后执行权限修复:', result);
        }
        
        // 标记需要刷新工作台
        sessionStorage.setItem('forceRefreshDashboard', 'true');
        
        // 登录成功
        if (imageId.value) {
          // 如果有imageId，说明是从标注页面跳转过来的
          success.value = '登录成功，即将返回标注页面...';
          
          // 先恢复会话标识以防止再次重定向
          sessionStorage.setItem('isNavigatingAfterSave', 'true');
          
          // 保存当前进度到Vuex
          store.dispatch('saveProgress', {
            step: 2, // 病例信息填写步骤
            imageId: Number(imageId.value),
            formData: null
          })
          
          setTimeout(() => {
            // 直接跳转到结构化表单页面
            router.push({
              path: '/cases/structured-form',
              query: { imageId: imageId.value, direct: 1 }
            })
          }, 1500)
        } else {
          // 查看是否有保存的重定向路径
          const redirectPath = sessionStorage.getItem('redirectPath');
          const redirectAfterLogin = sessionStorage.getItem('redirectAfterLogin');
          
          // 清除重定向路径
          sessionStorage.removeItem('redirectPath');
          sessionStorage.removeItem('redirectAfterLogin');
          
          // 标记来自登录页面
          sessionStorage.setItem('fromLoginPage', 'true');
          
          // 普通登录流程
          success.value = '登录成功，正在跳转到工作台...';
            setTimeout(() => {
            const finalRedirectPath = redirectPath || redirectAfterLogin || '/app/dashboard';
            window.location.href = finalRedirectPath;
        }, 1500)
        }
      } catch (err) {
        console.error('Login error:', err)
        error.value = typeof err === 'string' ? err : '登录失败，请检查邮箱和密码'
      } finally {
        loading.value = false
      }
    }
    
    // 添加清理不完整用户数据的方法
    const cleanInvalidUserData = () => {
      try {
        // 检查是否是从标注表单页面导航过来的
        const isNavigatingFromForm = sessionStorage.getItem('navigatingFromForm') === 'true';
        const isNavigatingAfterSave = sessionStorage.getItem('isNavigatingAfterSave') === 'true';
        
        // 如果是从表单页面导航过来的，不清除用户数据
        if (isNavigatingFromForm || isNavigatingAfterSave) {
          console.log('检测到从表单页面导航，保留用户数据');
          return;
        }
        
        const userStr = localStorage.getItem('user');
        if (!userStr) return; // 没有用户数据，不需要清理
        
        const user = JSON.parse(userStr);
        console.log('检查存储的用户数据:', user);
        
        // 检查是否有必要的用户字段
        const requiredFields = ['id', 'name', 'email', 'role'];
        const missingFields = requiredFields.filter(field => !user[field]);
        
        if (missingFields.length > 0) {
          console.warn(`存储的用户数据缺少必要字段: ${missingFields.join(', ')}`);
          console.log('清除不完整的用户数据');
          localStorage.removeItem('user');
          
          // 防止循环引用导致问题，也清除从会话中保存的用户
          sessionStorage.removeItem('preservedUser');
        } else {
          console.log('存储的用户数据完整，包含必要字段');
        }
      } catch (error) {
        console.error('检查用户数据时出错:', error);
        // 出错时，清除可能损坏的用户数据
        localStorage.removeItem('user');
        sessionStorage.removeItem('preservedUser');
      }
    };
    
    return {
      email,
      password,
      remember,
      loading,
      error,
      success,
      imageId,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  width: 100%;
  overflow: hidden;
}

/* 确保不使用此类的背景色，让全局CSS接管 */
.login-page.login-background {
  /* background: linear-gradient(135deg, #0d47a1, #4a148c, #6a1b9a); */
}

.login-container {
  display: flex;
  max-width: 1000px;
  margin: 50px auto;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
}

/* 左侧信息区域样式 */
.login-info {
  flex: 1;
  padding: 60px 40px;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.system-name {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 20px;
  line-height: 1.3;
}

.system-slogan {
  font-size: 16px;
  margin-bottom: 40px;
  opacity: 0.9;
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 40px;
}

.feature-item {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  display: inline-block;
}

.learn-more {
  margin-top: auto;
}

.learn-more-link {
  color: white;
  text-decoration: none;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
}

.learn-more-link:hover {
  text-decoration: underline;
}

/* 右侧登录框样式 */
.login-form-container {
  width: 450px;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.login-form-box {
  padding: 40px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.login-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 25px;
  justify-content: center;
}

.tab-item {
  padding: 10px 0;
  margin-right: 0;
  font-size: 18px;
  color: #1890ff;
  font-weight: 500;
  position: relative;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1890ff;
}

.login-form {
  flex: 1;
}

.form-group {
  margin-bottom: 20px;
}

.input-with-icon {
  position: relative;
}

.input-with-icon i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #bfbfbf;
}

.input-with-icon input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s;
}

.input-with-icon input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.remember-me {
  display: flex;
  align-items: center;
}

.remember-me input {
  margin-right: 6px;
}

.forgot-password {
  color: #1890ff;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.form-actions {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-btn:hover {
  background-color: #40a9ff;
}

.login-btn:disabled {
  background-color: #91d5ff;
  cursor: not-allowed;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.register-link {
  text-align: center;
  margin-top: 16px;
  font-size: 14px;
}

.register-link span {
  color: #666;
  margin-right: 5px;
}

.register-link a {
  color: #1890ff;
  text-decoration: none;
  font-weight: 500;
}

.register-link a:hover {
  text-decoration: underline;
}

.redirect-notice {
  margin-top: 20px;
  padding: 10px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  color: #1890ff;
  font-size: 14px;
}

.login-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.home-link {
  color: #1890ff;
  text-decoration: none;
}

.home-link:hover {
  text-decoration: underline;
}

.copyright {
  color: #999;
}

.alert {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
}

.alert-danger {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.alert-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

/* 图标样式 */
.icon-user:before {
  content: "👤";
}

.icon-lock:before {
  content: "🔒";
}

/* 响应式调整 */
@media (max-width: 992px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-info {
    padding: 30px;
  }
  
  .login-form-container {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .login-container {
    max-width: 100%;
  }
  
  .login-form-box {
    padding: 20px;
  }
  
  .system-name {
    font-size: 24px;
  }
  
  .feature-list {
    gap: 10px;
  }
  
  .feature-item {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style> 