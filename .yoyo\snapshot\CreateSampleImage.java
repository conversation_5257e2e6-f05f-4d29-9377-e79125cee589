import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.File;
import javax.imageio.ImageIO;

public class CreateSampleImage {
    public static void main(String[] args) {
        try {
            // 创建一个800x600的图像
            int width = 800;
            int height = 600;
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            
            // 获取图形上下文
            Graphics2D g2d = image.createGraphics();
            
            // 填充背景色
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);
            
            // 画一个边框
            g2d.setColor(Color.RED);
            g2d.drawRect(10, 10, width-20, height-20);
            
            // 添加一些文字
            g2d.setColor(Color.BLACK);
            g2d.setFont(new Font("Arial", Font.BOLD, 36));
            g2d.drawString("血管瘤样例图像", 200, 100);
            
            // 添加当前日期和时间
            g2d.setFont(new Font("Arial", Font.PLAIN, 20));
            g2d.drawString("创建时间: " + new java.util.Date(), 200, 150);
            
            // 释放图形上下文
            g2d.dispose();
            
            // 保存图像到文件
            String filePath = "medical_images/sample.jpg";
            File outputfile = new File(filePath);
            ImageIO.write(image, "jpg", outputfile);
            
            // 同时保存到子目录
            String[] subDirs = {"original", "processed"};
            for (String dir : subDirs) {
                File subDirFile = new File("medical_images/" + dir);
                if (!subDirFile.exists()) {
                    subDirFile.mkdirs();
                }
                
                File subDirOutputFile = new File("medical_images/" + dir + "/sample.jpg");
                ImageIO.write(image, "jpg", subDirOutputFile);
                System.out.println("保存图像到: " + subDirOutputFile.getAbsolutePath());
            }
            
            System.out.println("已成功创建样例图像: " + outputfile.getAbsolutePath());
            System.out.println("图像大小: " + width + "x" + height);
            System.out.println("文件大小: " + outputfile.length() + " 字节");
            
        } catch (Exception e) {
            System.out.println("创建图像时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 