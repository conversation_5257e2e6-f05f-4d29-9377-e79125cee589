<!DOCTYPE html>
<html>
<head>
    <title>CORS测试</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>API CORS测试</h1>
    
    <div>
        <button id="testButton">测试API连接</button>
    </div>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;">
        结果将显示在这里
    </div>
    
    <script>
        document.getElementById('testButton').addEventListener('click', function() {
            const resultElement = document.getElementById('result');
            resultElement.innerHTML = '正在发送请求...';
            
            // 发送OPTIONS预检请求
            fetch('http://localhost:8082/medical/api/users/authenticate', {
                method: 'OPTIONS',
                headers: {
                    'Content-Type': 'application/json'
                },
                mode: 'cors',
                credentials: 'include'
            })
            .then(response => {
                resultElement.innerHTML += '<p>预检请求成功，状态码: ' + response.status + '</p>';
                
                // 测试实际的POST请求
                return fetch('http://localhost:8082/medical/api/users/authenticate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'doctor123'
                    }),
                    mode: 'cors',
                    credentials: 'include'
                });
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error('请求失败，状态码: ' + response.status);
                }
            })
            .then(data => {
                resultElement.innerHTML += '<p>请求成功，返回数据: ' + JSON.stringify(data) + '</p>';
            })
            .catch(error => {
                resultElement.innerHTML += '<p style="color:red">错误: ' + error.message + '</p>';
                console.error('API错误:', error);
            });
        });
    </script>
</body>
</html> 