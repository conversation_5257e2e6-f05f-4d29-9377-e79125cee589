# 血管瘤辅助标注系统 - 图片管理问题解决方案

## 问题描述

在血管瘤辅助标注系统中，存在两个主要问题：

1. 前端请求路径问题：前端请求API时缺少`/medical`前缀导致404错误
2. 标注图片累积问题：当用户返回上一步时未能删除已有标注图片，导致重新保存时产生大量重复图片

## 问题解决方案

### 1. API路径问题解决

- 修改前端`api.js`中的`deleteAnnotatedImage`方法，修正API路径：
  ```javascript
  deleteAnnotatedImage(id) {
    return apiClient.delete(`/annotated-image?image_pair_id=${id}`);
  }
  ```
  
- 确保使用了正确的apiClient实例，该实例已配置了`/medical`前缀

### 2. 图片重复问题解决

#### 2.1 修正路径解析逻辑

- 增强`FileService.deleteFileByWebPath`方法以支持多种路径格式：
  - 带完整前缀的路径：`/medical/images/processed/filename.jpg`
  - 不带前缀的路径：`/images/processed/filename.jpg`
  - 仅有文件名的情况：`filename.jpg`

- 扩大文件搜索范围，依次检查所有可能的目录：processed、annotate、temp、original

#### 2.2 添加详细日志

- 在`FileUploadController.deleteAnnotatedImage`方法中添加更详细的日志记录
- 添加`RequestLoggingFilter`记录所有API请求和响应，方便问题排查

#### 2.3 提供清理和检测工具

- `clean_duplicates_fix.ps1`：清理重复标注图片，按图像ID分组只保留最新版本
- `CheckMissingFiles.java`：检测数据库与文件系统的一致性，修复不一致记录

## 关键代码改进

1. **文件路径解析增强**:
```java
// 如果是直接文件名
String filename = webPath;
if (webPath.contains("/")) {
    filename = webPath.substring(webPath.lastIndexOf('/') + 1);
}

// 优先在processed目录查找，因为标注图片默认在这个目录
File processedFile = new File(getProcessedDirectoryPath(), filename);
if (processedFile.exists()) {
    physicalPath = processedFile.getAbsolutePath();
    System.out.println("在processed目录找到匹配文件: " + physicalPath);
} else {
    // 依次检查其他目录...
}
```

2. **更严格的删除逻辑**:
```java
// 无论物理文件是否删除成功，都清空图像对中的标注图片路径
String oldPath = imagePair.getImageTwoPath();
imagePair.setImageTwoPath(null);
imagePairRepository.save(imagePair);
System.out.println("已清空图像对的标注图片路径: ID=" + imagePairId + ", 旧路径=" + oldPath);
```

## 维护建议

1. **定期清理**：每周运行一次清理脚本删除重复图片
2. **路径标准化**：确保所有新代码在保存路径时使用统一格式
3. **日志监控**：定期检查系统日志，及时发现异常情况
4. **定期数据校验**：每月验证一次数据库与文件系统的一致性

## 结论

通过以上改进，我们解决了系统中存在的路径问题和图片重复问题，提高了系统稳定性和存储利用率。同时，添加的详细日志和监控工具将有助于未来的问题排查和系统维护。 