# 使用官方的OpenJDK 8镜像作为基础镜像
FROM openjdk:8-jdk-alpine

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache curl

# 复制Maven配置文件
COPY pom.xml .

# 复制Maven wrapper文件（如果存在）
COPY .mvn .mvn
COPY mvnw .

# 给Maven wrapper执行权限
RUN chmod +x mvnw

# 下载依赖（利用Docker缓存层）
RUN ./mvnw dependency:go-offline -B

# 复制源代码
COPY src ./src

# 构建应用
RUN ./mvnw clean package -DskipTests

# 创建必要的目录
RUN mkdir -p /app/medical_images/original \
    && mkdir -p /app/medical_images/processed \
    && mkdir -p /app/medical_images/annotated \
    && mkdir -p /app/logs

# 暴露端口
EXPOSE 8085

# 设置JVM参数
ENV JAVA_OPTS="-Xmx1024m -Xms512m -Djava.security.egd=file:/dev/./urandom"

# 启动应用
CMD ["sh", "-c", "java $JAVA_OPTS -jar target/annotation-*.jar"]

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8085/actuator/health || exit 1
