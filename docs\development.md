# 血管瘤AI智能诊断平台 - 开发指南

## 📋 目录
- [开发环境搭建](#开发环境搭建)
- [项目结构说明](#项目结构说明)
- [开发规范](#开发规范)
- [API接口说明](#api接口说明)
- [数据库设计](#数据库设计)
- [AI模型集成](#ai模型集成)
- [测试指南](#测试指南)
- [常见问题](#常见问题)

## 🛠 开发环境搭建

### 环境要求
- **Java**: OpenJDK 8 或更高版本
- **Node.js**: 16.x 或更高版本
- **Python**: 3.8 或更高版本
- **MySQL**: 8.0 或更高版本
- **Maven**: 3.6 或更高版本
- **Git**: 2.x 或更高版本

### 快速启动
1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd xueguan2
   ```

2. **数据库初始化**
   ```sql
   CREATE DATABASE hemangioma_diagnosis DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   mysql -u root -p hemangioma_diagnosis < mysql/init.sql
   ```

3. **配置文件设置**
   ```bash
   # 复制配置模板
   cp config/application-template.properties src/main/resources/application-dev.properties
   # 编辑配置文件，设置数据库连接等信息
   ```

4. **启动开发环境**
   ```bash
   # 使用脚本一键启动
   scripts/start-dev.bat
   
   # 或手动启动各服务
   # 后端
   mvn spring-boot:run -Dspring-boot.run.profiles=dev
   
   # AI服务
   cd ai-service
   pip install -r requirements.txt
   python ai_service.py
   
   # 前端
   cd frontend
   npm install
   npm run serve
   ```

## 📁 项目结构说明

### 后端结构 (Spring Boot)
```
src/main/java/com/medical/
├── controller/          # 控制器层
│   ├── AuthController.java
│   ├── HemangiomaDiagnosisController.java
│   └── UserController.java
├── service/            # 服务层
│   ├── impl/          # 服务实现
│   ├── AuthService.java
│   ├── HemangiomaDiagnosisService.java
│   └── UserService.java
├── entity/            # 实体类
│   ├── User.java
│   ├── HemangiomaDiagnosis.java
│   └── HemangiomaType.java
├── repository/        # 数据访问层
│   ├── UserRepository.java
│   └── HemangiomaDiagnosisRepository.java
├── dto/              # 数据传输对象
│   ├── request/      # 请求DTO
│   └── response/     # 响应DTO
├── config/           # 配置类
│   ├── SecurityConfig.java
│   ├── CorsConfig.java
│   └── WebConfig.java
└── util/             # 工具类
    ├── FileUtil.java
    └── ResponseUtil.java
```

### 前端结构 (Vue.js)
```
frontend/src/
├── components/        # 公共组件
│   ├── common/       # 通用组件
│   ├── forms/        # 表单组件
│   └── charts/       # 图表组件
├── views/            # 页面视图
│   ├── Login.vue
│   ├── Dashboard.vue
│   └── AnnotationAndForm.vue
├── router/           # 路由配置
│   └── index.js
├── store/            # Vuex状态管理
│   ├── modules/      # 模块化store
│   └── index.js
├── utils/            # 工具函数
│   ├── request.js    # HTTP请求封装
│   ├── auth.js       # 认证工具
│   └── constants.js  # 常量定义
├── assets/           # 静态资源
│   ├── images/
│   └── styles/
└── plugins/          # 插件配置
    └── element-plus.js
```

### AI服务结构 (Python)
```
ai-service/
├── ai_service.py     # 主服务文件
├── models/           # AI模型相关
│   ├── yolo_model.py
│   └── llm_service.py
├── utils/            # 工具函数
│   ├── image_processor.py
│   ├── type_mapper.py
│   └── logger.py
├── config/           # 配置文件
│   └── settings.py
└── tests/            # 测试文件
    └── test_ai_service.py
```

## 📝 开发规范

### 代码规范
1. **Java代码规范**
   - 遵循Google Java Style Guide
   - 使用驼峰命名法
   - 类名首字母大写，方法名和变量名首字母小写
   - 常量使用全大写，下划线分隔

2. **JavaScript代码规范**
   - 遵循ESLint标准配置
   - 使用驼峰命名法
   - 组件名使用PascalCase
   - 文件名使用kebab-case

3. **Python代码规范**
   - 遵循PEP 8规范
   - 使用下划线命名法
   - 类名使用PascalCase
   - 函数和变量名使用snake_case

### Git提交规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type类型：**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```
feat(ai): 添加YOLO模型集成功能

- 集成Ultralytics YOLO模型
- 添加血管瘤检测接口
- 实现检测结果可视化

Closes #123
```

### 分支管理
- **main**: 主分支，用于生产环境
- **develop**: 开发分支，用于集成测试
- **feature/xxx**: 功能分支，用于新功能开发
- **hotfix/xxx**: 热修复分支，用于紧急修复
- **release/xxx**: 发布分支，用于版本发布

## 🔌 API接口说明

### 认证接口
```http
POST /medical/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### 血管瘤诊断接口
```http
POST /medical/api/hemangioma-diagnoses
Content-Type: multipart/form-data

file: [图像文件]
userId: 1
```

### AI检测接口
```http
POST /ai-api/detect
Content-Type: multipart/form-data

file: [图像文件]
confidence: 0.5
```

### LLM诊断建议接口
```http
POST /ai-api/diagnose
Content-Type: application/json

{
  "detectionResults": [...],
  "imageInfo": {...}
}
```

## 🗄 数据库设计

### 核心表结构

#### 用户表 (users)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键 |
| username | varchar(50) | 用户名 |
| password | varchar(255) | 密码 |
| role | varchar(20) | 角色 |
| status | tinyint | 状态 |

#### 血管瘤诊断记录表 (hemangioma_diagnoses)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键 |
| user_id | bigint | 用户ID |
| image_path | varchar(500) | 图像路径 |
| ai_detection_result | json | AI检测结果 |
| llm_recommendation | json | LLM诊断建议 |
| processing_status | varchar(20) | 处理状态 |

## 🤖 AI模型集成

### YOLO模型集成
1. **模型加载**
   ```python
   from ultralytics import YOLO
   model = YOLO('path/to/model.pt')
   ```

2. **图像检测**
   ```python
   results = model(image_path, conf=0.5)
   ```

3. **结果处理**
   ```python
   detections = []
   for result in results:
       for box in result.boxes:
           detection = {
               'class': int(box.cls),
               'confidence': float(box.conf),
               'bbox': box.xyxy.tolist()
           }
           detections.append(detection)
   ```

### LLM服务集成
1. **Ollama客户端**
   ```python
   import requests
   
   def call_llm(prompt):
       response = requests.post(
           'http://localhost:11434/api/generate',
           json={
               'model': 'deepseek-r1:8b',
               'prompt': prompt,
               'stream': False
           }
       )
       return response.json()
   ```

## 🧪 测试指南

### 单元测试
```bash
# Java单元测试
mvn test

# JavaScript单元测试
cd frontend
npm run test:unit

# Python单元测试
cd ai-service
python -m pytest tests/
```

### 集成测试
```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
mvn verify
```

### API测试
使用Postman或curl进行API测试：
```bash
# 健康检查
curl http://localhost:8085/actuator/health

# AI服务检查
curl http://localhost:8086/health
```

## ❓ 常见问题

### Q1: 启动时端口被占用怎么办？
**A**: 检查端口占用情况并关闭相关进程：
```bash
# Windows
netstat -ano | findstr :8080
taskkill /PID <PID> /F

# Linux/Mac
lsof -i :8080
kill -9 <PID>
```

### Q2: AI模型加载失败怎么办？
**A**: 检查模型文件路径和权限：
```bash
# 检查文件是否存在
ls -la resources/models/

# 检查Python依赖
pip list | grep ultralytics
```

### Q3: 数据库连接失败怎么办？
**A**: 检查数据库服务和配置：
```bash
# 检查MySQL服务
systemctl status mysql

# 测试连接
mysql -u root -p -h localhost
```

### Q4: 前端构建失败怎么办？
**A**: 清理缓存并重新安装依赖：
```bash
cd frontend
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

## 📞 技术支持

如果遇到其他问题，请：
1. 查看项目日志文件
2. 检查GitHub Issues
3. 联系开发团队

---

**更新时间**: 2024-01-01  
**版本**: V2.0
