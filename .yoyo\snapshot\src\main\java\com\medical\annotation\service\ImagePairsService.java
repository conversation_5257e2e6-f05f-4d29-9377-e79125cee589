package com.medical.annotation.service;

import com.medical.annotation.model.ImagePair;
import com.medical.annotation.repository.ImagePairRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ImagePairsService {

    @Autowired
    private ImagePairRepository imagePairRepository;

    /**
     * 根据元数据ID查找图像对
     * @param metadataId 图像元数据ID
     * @return 图像对列表
     */
    public List<ImagePair> findByMetadataId(Long metadataId) {
        return imagePairRepository.findByMetadataId(metadataId);
    }

    /**
     * 创建新的图像对
     * @param imagePair 图像对信息
     * @return 保存的图像对
     */
    @Transactional
    public ImagePair createImagePair(ImagePair imagePair) {
        imagePair.setCreatedAt(LocalDateTime.now());
        return imagePairRepository.save(imagePair);
    }

    /**
     * 更新图像对信息
     * @param id 图像对ID
     * @param updatedImagePair 更新的图像对信息
     * @return 更新后的图像对
     */
    @Transactional
    public ImagePair updateImagePair(Long id, ImagePair updatedImagePair) throws Exception {
        Optional<ImagePair> imagePairOpt = imagePairRepository.findById(id);
        if (!imagePairOpt.isPresent()) {
            throw new Exception("图像对不存在");
        }
        
        ImagePair imagePair = imagePairOpt.get();
        
        // 更新字段
        if (updatedImagePair.getImageOnePath() != null) {
            imagePair.setImageOnePath(updatedImagePair.getImageOnePath());
        }
        
        if (updatedImagePair.getImageTwoPath() != null) {
            imagePair.setImageTwoPath(updatedImagePair.getImageTwoPath());
        }
        
        if (updatedImagePair.getDescription() != null) {
            imagePair.setDescription(updatedImagePair.getDescription());
        }
        
        return imagePairRepository.save(imagePair);
    }

    /**
     * 删除图像对
     * @param id 图像对ID
     */
    @Transactional
    public void deleteImagePair(Long id) throws Exception {
        if (!imagePairRepository.existsById(id)) {
            throw new Exception("图像对不存在");
        }
        imagePairRepository.deleteById(id);
    }

    /**
     * 根据元数据ID删除图像对
     * @param metadataId 元数据ID
     */
    @Transactional
    public void deleteByMetadataId(Long metadataId) {
        imagePairRepository.deleteByMetadataId(metadataId);
    }
} 