"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[35],{7035:(t,n,e)=>{e.r(n),e.d(n,{default:()=>x});e(2010);var a=e(641),i=e(33),o=e(3751),r={class:"container"},s={key:0,class:"text-center my-5"},l={key:1,class:"alert alert-danger"},c={key:2,class:"row"},u={class:"col-md-8"},d={class:"image-container position-relative"},h=["src"],p={class:"mt-3 d-flex justify-content-between"},g={class:"col-md-4"},f={class:"card"},v={class:"card-body"},m={class:"btn-group mb-3"},k=["onClick"];function y(t,n,e,y,A,w){return(0,a.uX)(),(0,a.CE)("div",r,[n[19]||(n[19]=(0,a.Lk)("h2",{class:"mt-4 mb-3"},"图像详情",-1)),A.loading?((0,a.uX)(),(0,a.CE)("div",s,n[9]||(n[9]=[(0,a.Lk)("div",{class:"spinner-border",role:"status"},[(0,a.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):A.error?((0,a.uX)(),(0,a.CE)("div",l,(0,i.v_)(A.error),1)):((0,a.uX)(),(0,a.CE)("div",c,[(0,a.Lk)("div",u,[(0,a.Lk)("div",d,[(0,a.Lk)("img",{src:w.imageUrl,class:"img-fluid",alt:"血管瘤图像",ref:"imageElement",onLoad:n[0]||(n[0]=function(){return w.initializeCanvas&&w.initializeCanvas.apply(w,arguments)})},null,40,h),(0,a.Lk)("canvas",{ref:"annotationCanvas",class:"annotation-canvas position-absolute top-0 start-0",onMousedown:n[1]||(n[1]=function(){return w.startDrawing&&w.startDrawing.apply(w,arguments)}),onMousemove:n[2]||(n[2]=function(){return w.draw&&w.draw.apply(w,arguments)}),onMouseup:n[3]||(n[3]=function(){return w.endDrawing&&w.endDrawing.apply(w,arguments)}),onMouseleave:n[4]||(n[4]=function(){return w.endDrawing&&w.endDrawing.apply(w,arguments)})},null,544)]),(0,a.Lk)("div",p,[(0,a.Lk)("div",null,[(0,a.Lk)("button",{class:"btn btn-outline-primary me-2",onClick:n[5]||(n[5]=function(){return w.clearAnnotations&&w.clearAnnotations.apply(w,arguments)})},"清除标注"),(0,a.Lk)("button",{class:"btn btn-primary",onClick:n[6]||(n[6]=function(){return w.saveAnnotations&&w.saveAnnotations.apply(w,arguments)})},"保存标注")]),(0,a.Lk)("div",null,[(0,a.Lk)("button",{class:"btn btn-secondary",onClick:n[7]||(n[7]=function(n){return t.$router.go(-1)})},"返回")])])]),(0,a.Lk)("div",g,[(0,a.Lk)("div",f,[n[18]||(n[18]=(0,a.Lk)("div",{class:"card-header"},[(0,a.Lk)("h5",{class:"card-title mb-0"},"图像信息")],-1)),(0,a.Lk)("div",v,[(0,a.Lk)("p",null,[n[10]||(n[10]=(0,a.Lk)("strong",null,"名称：",-1)),(0,a.eW)(" "+(0,i.v_)(A.image.name),1)]),(0,a.Lk)("p",null,[n[11]||(n[11]=(0,a.Lk)("strong",null,"上传者：",-1)),(0,a.eW)(" "+(0,i.v_)(A.image.uploaderName),1)]),(0,a.Lk)("p",null,[n[12]||(n[12]=(0,a.Lk)("strong",null,"上传时间：",-1)),(0,a.eW)(" "+(0,i.v_)(w.formatDate(A.image.uploadTime)),1)]),(0,a.Lk)("p",null,[n[13]||(n[13]=(0,a.Lk)("strong",null,"状态：",-1)),n[14]||(n[14]=(0,a.eW)()),(0,a.Lk)("span",{class:(0,i.C4)(w.statusClass)},(0,i.v_)(w.statusText),3)]),n[15]||(n[15]=(0,a.Lk)("hr",null,null,-1)),n[16]||(n[16]=(0,a.Lk)("h6",null,"标注工具",-1)),(0,a.Lk)("div",m,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(A.drawingTools,(function(t){return(0,a.uX)(),(0,a.CE)("button",{key:t.type,class:(0,i.C4)(["btn",A.currentTool===t.type?"btn-primary":"btn-outline-primary"]),onClick:function(n){return w.selectTool(t.type)}},[(0,a.Lk)("i",{class:(0,i.C4)(t.icon)},null,2),(0,a.eW)(" "+(0,i.v_)(t.label),1)],10,k)})),128))]),n[17]||(n[17]=(0,a.Lk)("h6",null,"注释",-1)),(0,a.bo)((0,a.Lk)("textarea",{"onUpdate:modelValue":n[8]||(n[8]=function(t){return A.notes=t}),class:"form-control mb-3",rows:"3",placeholder:"添加标注说明..."},null,512),[[o.Jo,A.notes]])])])])]))])}var A=e(4048),w=e(388),L=(e(8706),e(1629),e(4114),e(739),e(3288),e(8111),e(7588),e(3110),e(9432),e(6099),e(3500),e(2505)),E=e.n(L);const b={name:"ImageDetail",data:function(){return{image:{},loading:!0,error:null,annotations:[],currentAnnotation:null,isDrawing:!1,currentTool:"freehand",notes:"",drawingTools:[{type:"freehand",label:"自由绘制",icon:"bi bi-pencil"},{type:"rectangle",label:"矩形",icon:"bi bi-square"},{type:"circle",label:"圆形",icon:"bi bi-circle"}]}},computed:{imageId:function(){return this.$route.params.id},imageUrl:function(){return"".concat({NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL,"/api/images/").concat(this.imageId,"/file")},statusText:function(){var t={UPLOADED:"已上传",ANNOTATED:"已标注",REVIEWED:"已审核",APPROVED:"已批准",REJECTED:"已拒绝"};return t[this.image.status]||this.image.status},statusClass:function(){var t={UPLOADED:"text-secondary",ANNOTATED:"text-primary",REVIEWED:"text-info",APPROVED:"text-success",REJECTED:"text-danger"};return t[this.image.status]||""}},methods:{fetchImageData:function(){var t=this;return(0,w.A)((0,A.A)().mark((function n(){var e,a;return(0,A.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.loading=!0,n.prev=1,n.next=4,E().get("".concat({NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL,"/api/images/").concat(t.imageId));case 4:e=n.sent,t.image=e.data,t.image.annotationData&&(t.annotations=JSON.parse(t.image.annotationData),t.notes=t.image.notes||""),n.next=12;break;case 9:n.prev=9,n.t0=n["catch"](1),t.error="加载图像详情失败: "+((null===(a=n.t0.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||n.t0.message);case 12:return n.prev=12,t.loading=!1,n.finish(12);case 15:case"end":return n.stop()}}),n,null,[[1,9,12,15]])})))()},formatDate:function(t){if(!t)return"";var n=new Date(t);return n.toLocaleString("zh-CN")},initializeCanvas:function(){var t=this.$refs.imageElement,n=this.$refs.annotationCanvas;t&&n&&(n.width=t.clientWidth,n.height=t.clientHeight,this.redrawAnnotations())},redrawAnnotations:function(){var t=this,n=this.$refs.annotationCanvas;if(n){var e=n.getContext("2d");e.clearRect(0,0,n.width,n.height),this.annotations.forEach((function(n){t.drawAnnotation(e,n)}))}},drawAnnotation:function(t,n){if(t.strokeStyle="#FF0000",t.lineWidth=2,"freehand"===n.type)t.beginPath(),n.points.forEach((function(n,e){0===e?t.moveTo(n.x,n.y):t.lineTo(n.x,n.y)})),t.stroke();else if("rectangle"===n.type){var e=n.end.x-n.start.x,a=n.end.y-n.start.y;t.strokeRect(n.start.x,n.start.y,e,a)}else if("circle"===n.type){var i=Math.sqrt(Math.pow(n.end.x-n.start.x,2)+Math.pow(n.end.y-n.start.y,2));t.beginPath(),t.arc(n.start.x,n.start.y,i,0,2*Math.PI),t.stroke()}},startDrawing:function(t){this.isDrawing=!0;var n=this.$refs.annotationCanvas,e=n.getBoundingClientRect(),a=t.clientX-e.left,i=t.clientY-e.top;"freehand"===this.currentTool?this.currentAnnotation={type:"freehand",points:[{x:a,y:i}]}:"rectangle"!==this.currentTool&&"circle"!==this.currentTool||(this.currentAnnotation={type:this.currentTool,start:{x:a,y:i},end:{x:a,y:i}})},draw:function(t){if(this.isDrawing&&this.currentAnnotation){var n=this.$refs.annotationCanvas,e=n.getContext("2d"),a=n.getBoundingClientRect(),i=t.clientX-a.left,o=t.clientY-a.top;if("freehand"===this.currentTool){this.currentAnnotation.points.push({x:i,y:o}),e.strokeStyle="#FF0000",e.lineWidth=2,e.beginPath();var r=this.currentAnnotation.points,s=r[r.length-2],l=r[r.length-1];e.moveTo(s.x,s.y),e.lineTo(l.x,l.y),e.stroke()}else this.currentAnnotation.end={x:i,y:o},this.redrawAnnotations(),this.drawAnnotation(e,this.currentAnnotation)}},endDrawing:function(){this.isDrawing&&this.currentAnnotation&&(this.annotations.push(this.currentAnnotation),this.currentAnnotation=null,this.isDrawing=!1)},clearAnnotations:function(){if(confirm("确定要清除所有标注吗？")){this.annotations=[];var t=this.$refs.annotationCanvas,n=t.getContext("2d");n.clearRect(0,0,t.width,t.height)}},saveAnnotations:function(){var t=this;return(0,w.A)((0,A.A)().mark((function n(){var e,a;return(0,A.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,e=JSON.stringify(t.annotations),n.next=4,E().post("".concat({NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL,"/api/images/").concat(t.imageId,"/annotate"),{annotationData:e,notes:t.notes});case 4:t.$store.dispatch("showAlert",{type:"success",message:"标注保存成功"}),n.next=10;break;case 7:n.prev=7,n.t0=n["catch"](0),t.$store.dispatch("showAlert",{type:"error",message:"保存标注失败: "+((null===(a=n.t0.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||n.t0.message)});case 10:case"end":return n.stop()}}),n,null,[[0,7]])})))()},selectTool:function(t){this.currentTool=t}},mounted:function(){this.fetchImageData(),window.addEventListener("resize",this.initializeCanvas)},beforeUnmount:function(){window.removeEventListener("resize",this.initializeCanvas)}};var C=e(6262);const D=(0,C.A)(b,[["render",y],["__scopeId","data-v-8341efda"]]),x=D}}]);