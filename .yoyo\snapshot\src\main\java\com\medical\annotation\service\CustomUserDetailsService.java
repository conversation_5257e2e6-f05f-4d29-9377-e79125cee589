package com.medical.annotation.service;

import com.medical.annotation.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    private UserService userService;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        try {
            // 通过邮箱查找用户
            Optional<User> userOpt = userService.getUserByEmail(email);
            
            if (!userOpt.isPresent()) {
                throw new UsernameNotFoundException("用户不存在: " + email);
            }
            
            User user = userOpt.get();
            
            // 创建Spring Security的权限列表
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getRole().name()));
            
            // 返回Spring Security的User对象
            return new org.springframework.security.core.userdetails.User(
                user.getEmail(),
                user.getPassword(),
                authorities
            );
        } catch (Exception e) {
            throw new UsernameNotFoundException("加载用户信息失败", e);
        }
    }
} 