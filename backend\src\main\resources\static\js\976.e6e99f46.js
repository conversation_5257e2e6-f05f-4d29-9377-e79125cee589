"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[976],{30976:(e,a,n)=>{n.r(a),n.d(a,{default:()=>H});var s=n(61431),r={class:"forgot-password-page forgot-password-background"},t={class:"forgot-password-container"},o={class:"forgot-password-form-container"},l={class:"forgot-password-form-box"},i={class:"forgot-password-form"},u={key:0,class:"alert alert-danger"},d={key:1,class:"alert alert-success"},c={key:2},v={class:"form-group"},p={class:"input-with-icon"},f=["disabled"],k={class:"form-actions"},w=["disabled"],g={key:0,class:"spinner"},m={key:3},b={class:"form-group"},h={class:"input-with-icon"},L=["disabled"],y={class:"form-actions"},C=["disabled"],E={key:0,class:"spinner"},_={class:"resend-code"},P={key:0},S={key:4},R={class:"form-group"},X={class:"input-with-icon"},V=["disabled"],A={key:0,class:"field-error"},K={key:1,class:"password-strength"},Q={class:"strength-indicator"},q={class:"form-group"},$={class:"input-with-icon"},D=["disabled"],I={class:"form-actions"},J=["disabled"],U={key:0,class:"spinner"},x={class:"login-link"};function B(e,a,n,B,F,z){var T=(0,s.g2)("router-link");return(0,s.uX)(),(0,s.CE)("div",r,[(0,s.Lk)("div",t,[(0,s.Lk)("div",o,[(0,s.Lk)("div",l,[a[20]||(a[20]=(0,s.Lk)("div",{class:"forgot-password-tabs"},[(0,s.Lk)("div",{class:"tab-item active"},"密码找回")],-1)),(0,s.Lk)("div",i,[B.error?((0,s.uX)(),(0,s.CE)("div",u,(0,s.v_)(B.error),1)):(0,s.Q3)("",!0),B.success?((0,s.uX)(),(0,s.CE)("div",d,(0,s.v_)(B.success),1)):(0,s.Q3)("",!0),1===B.step?((0,s.uX)(),(0,s.CE)("div",c,[a[10]||(a[10]=(0,s.Lk)("p",{class:"form-description"},"请输入您注册时使用的电子邮箱，我们将向该邮箱发送验证码",-1)),(0,s.Lk)("form",{onSubmit:a[1]||(a[1]=(0,s.D$)((function(){return B.handleSendVerificationCode&&B.handleSendVerificationCode.apply(B,arguments)}),["prevent"]))},[(0,s.Lk)("div",v,[(0,s.Lk)("div",p,[a[9]||(a[9]=(0,s.Lk)("i",{class:"icon-email"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"email","onUpdate:modelValue":a[0]||(a[0]=function(e){return B.email=e}),type:"email",placeholder:"请输入邮箱地址",required:"",disabled:B.loading},null,8,f),[[s.Jo,B.email]])])]),(0,s.Lk)("div",k,[(0,s.Lk)("button",{type:"submit",class:"submit-btn",disabled:B.loading},[B.loading?((0,s.uX)(),(0,s.CE)("span",g)):(0,s.Q3)("",!0),(0,s.Lk)("span",null,(0,s.v_)(B.loading?"发送中...":"发送验证邮件"),1)],8,w)])],32)])):(0,s.Q3)("",!0),2===B.step?((0,s.uX)(),(0,s.CE)("div",m,[a[12]||(a[12]=(0,s.Lk)("p",{class:"form-description"},"验证邮件已发送，请查收邮箱并输入验证码",-1)),(0,s.Lk)("form",{onSubmit:a[4]||(a[4]=(0,s.D$)((function(){return B.handleVerifyCode&&B.handleVerifyCode.apply(B,arguments)}),["prevent"]))},[(0,s.Lk)("div",b,[(0,s.Lk)("div",h,[a[11]||(a[11]=(0,s.Lk)("i",{class:"icon-lock"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"verificationCode","onUpdate:modelValue":a[2]||(a[2]=function(e){return B.verificationCode=e}),type:"text",placeholder:"请输入验证码",required:"",disabled:B.loading},null,8,L),[[s.Jo,B.verificationCode]])])]),(0,s.Lk)("div",y,[(0,s.Lk)("button",{type:"submit",class:"submit-btn",disabled:B.loading},[B.loading?((0,s.uX)(),(0,s.CE)("span",E)):(0,s.Q3)("",!0),(0,s.Lk)("span",null,(0,s.v_)(B.loading?"验证中...":"验证"),1)],8,C)]),(0,s.Lk)("div",_,[B.countdown>0?((0,s.uX)(),(0,s.CE)("span",P,(0,s.v_)(B.countdown)+"秒后可重新发送",1)):((0,s.uX)(),(0,s.CE)("a",{key:1,href:"#",onClick:a[3]||(a[3]=(0,s.D$)((function(){return B.handleSendVerificationCode&&B.handleSendVerificationCode.apply(B,arguments)}),["prevent"]))},"重新发送验证码"))])],32)])):(0,s.Q3)("",!0),3===B.step?((0,s.uX)(),(0,s.CE)("div",S,[a[17]||(a[17]=(0,s.Lk)("p",{class:"form-description"},"请设置新密码",-1)),(0,s.Lk)("form",{onSubmit:a[8]||(a[8]=(0,s.D$)((function(){return B.handleResetPassword&&B.handleResetPassword.apply(B,arguments)}),["prevent"]))},[(0,s.Lk)("div",R,[(0,s.Lk)("div",X,[a[13]||(a[13]=(0,s.Lk)("i",{class:"icon-lock"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"newPassword","onUpdate:modelValue":a[5]||(a[5]=function(e){return B.newPassword=e}),type:"password",placeholder:"请设置新密码",required:"",disabled:B.loading,onBlur:a[6]||(a[6]=function(){return B.validatePassword&&B.validatePassword.apply(B,arguments)})},null,40,V),[[s.Jo,B.newPassword]])]),B.passwordError?((0,s.uX)(),(0,s.CE)("div",A,(0,s.v_)(B.passwordError),1)):(0,s.Q3)("",!0),B.passwordStrength&&!B.passwordError?((0,s.uX)(),(0,s.CE)("div",K,[a[14]||(a[14]=(0,s.Lk)("div",{class:"strength-label"},"密码强度：",-1)),(0,s.Lk)("div",Q,[(0,s.Lk)("div",{class:(0,s.C4)(["strength-bar",{weak:"weak"===B.passwordStrength,medium:"medium"===B.passwordStrength,strong:"strong"===B.passwordStrength}])},null,2)]),(0,s.Lk)("div",{class:(0,s.C4)(["strength-text",B.passwordStrength])},(0,s.v_)("weak"===B.passwordStrength?"弱":"medium"===B.passwordStrength?"中":"strong"===B.passwordStrength?"强":""),3)])):(0,s.Q3)("",!0),a[15]||(a[15]=(0,s.Lk)("div",{class:"password-hint"}," 密码长度至少8位，必须包含大写字母、小写字母、数字和特殊符号中的至少三种 ",-1))]),(0,s.Lk)("div",q,[(0,s.Lk)("div",$,[a[16]||(a[16]=(0,s.Lk)("i",{class:"icon-lock"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"confirmPassword","onUpdate:modelValue":a[7]||(a[7]=function(e){return B.confirmPassword=e}),type:"password",placeholder:"请确认新密码",required:"",disabled:B.loading},null,8,D),[[s.Jo,B.confirmPassword]])])]),(0,s.Lk)("div",I,[(0,s.Lk)("button",{type:"submit",class:"submit-btn",disabled:B.loading||!!B.passwordError},[B.loading?((0,s.uX)(),(0,s.CE)("span",U)):(0,s.Q3)("",!0),(0,s.Lk)("span",null,(0,s.v_)(B.loading?"重置中...":"重置密码"),1)],8,J)])],32)])):(0,s.Q3)("",!0),(0,s.Lk)("div",x,[a[19]||(a[19]=(0,s.Lk)("span",null,"记起密码了?",-1)),(0,s.bF)(T,{to:"/login",class:"login-btn"},{default:(0,s.k6)((function(){return a[18]||(a[18]=[(0,s.eW)("返回登录")])})),_:1,__:[18]})])])])])])])}var F=n(24059),z=n(698),T=(n(2008),n(74423),n(44114),n(26099),n(27495),n(90906),n(76031),n(60455)),W=n(36149);const Z={name:"ForgotPassword",setup:function(){var e=(0,T.rd)(),a=(0,s.KR)(""),n=(0,s.KR)(""),r=(0,s.KR)(""),t=(0,s.KR)(""),o=(0,s.KR)(!1),l=(0,s.KR)(""),i=(0,s.KR)(""),u=(0,s.KR)(1),d=(0,s.KR)(0),c=null,v=(0,s.KR)(""),p=(0,s.KR)("");(0,s.hi)((function(){c&&clearInterval(c)}));var f=function(){d.value=60,c=setInterval((function(){d.value--,d.value<=0&&clearInterval(c)}),1e3)},k=function(){var e=r.value;if(e.length<8)return v.value="密码长度必须不少于8位",p.value="weak",!1;var a=/[A-Z]/.test(e),n=/[a-z]/.test(e),s=/[0-9]/.test(e),t=/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e),o=[a,n,s,t].filter(Boolean).length,l=["123456","password","abcdef","12345678","qwerty","111111"];return l.includes(e.toLowerCase())?(v.value="请勿使用常见的简单密码",p.value="weak",!1):o<3?(v.value="密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种",p.value="weak",!1):(3===o&&e.length<10?p.value="medium":(4===o||3===o&&e.length>=10)&&(p.value="strong"),v.value="",!0)};(0,s.wB)(r,(function(){r.value?k():(v.value="",p.value="")}));var w=function(){var e=(0,z.A)((0,F.A)().m((function e(){var n;return(0,F.A)().w((function(e){while(1)switch(e.n){case 0:if(a.value){e.n=1;break}return l.value="请输入邮箱地址",e.a(2);case 1:return o.value=!0,l.value="",i.value="",e.p=2,e.n=3,W["default"].users.sendResetPasswordEmail(a.value);case 3:i.value="验证邮件已发送，请注意查收",u.value=2,f(),e.n=5;break;case 4:e.p=4,n=e.v,console.error("发送验证码错误:",n),l.value="string"===typeof n?n:"发送验证码失败，请检查邮箱是否正确";case 5:return e.p=5,o.value=!1,e.f(5);case 6:return e.a(2)}}),e,null,[[2,4,5,6]])})));return function(){return e.apply(this,arguments)}}(),g=function(){var e=(0,z.A)((0,F.A)().m((function e(){var s;return(0,F.A)().w((function(e){while(1)switch(e.n){case 0:if(n.value){e.n=1;break}return l.value="请输入验证码",e.a(2);case 1:return o.value=!0,l.value="",i.value="",e.p=2,e.n=3,W["default"].users.verifyResetCode(a.value,n.value);case 3:i.value="验证成功，请设置新密码",u.value=3,e.n=5;break;case 4:e.p=4,s=e.v,console.error("验证码验证错误:",s),l.value="string"===typeof s?s:"验证码错误或已过期";case 5:return e.p=5,o.value=!1,e.f(5);case 6:return e.a(2)}}),e,null,[[2,4,5,6]])})));return function(){return e.apply(this,arguments)}}(),m=function(){var s=(0,z.A)((0,F.A)().m((function s(){var u;return(0,F.A)().w((function(s){while(1)switch(s.n){case 0:if(r.value){s.n=1;break}return l.value="请输入新密码",s.a(2);case 1:if(k()){s.n=2;break}return s.a(2);case 2:if(r.value===t.value){s.n=3;break}return l.value="两次输入的密码不一致",s.a(2);case 3:return o.value=!0,l.value="",i.value="",s.p=4,s.n=5,W["default"].users.resetPassword(a.value,n.value,r.value);case 5:i.value="密码重置成功，即将跳转到登录页面",setTimeout((function(){e.push("/login")}),3e3),s.n=7;break;case 6:s.p=6,u=s.v,console.error("重置密码错误:",u),l.value="string"===typeof u?u:"重置密码失败，请重试";case 7:return s.p=7,o.value=!1,s.f(7);case 8:return s.a(2)}}),s,null,[[4,6,7,8]])})));return function(){return s.apply(this,arguments)}}();return{email:a,verificationCode:n,newPassword:r,confirmPassword:t,loading:o,error:l,success:i,step:u,countdown:d,passwordError:v,passwordStrength:p,handleSendVerificationCode:w,handleVerifyCode:g,handleResetPassword:m,validatePassword:k}}};var j=n(66262);const G=(0,j.A)(Z,[["render",B],["__scopeId","data-v-0220b3a4"]]),H=G}}]);
//# sourceMappingURL=976.e6e99f46.js.map