package com.medical.annotation.controller;

import com.medical.annotation.model.ReviewerApplication;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.service.ReviewerApplicationService;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/users")
public class UserApiController {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ReviewerApplicationService reviewerApplicationService;
    
    // 获取所有用户（仅返回基本信息，不包括密码）
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        // 使用UserService确保所有用户都有customId
        List<User> users = userService.getAllUsers();
        // 清空密码字段
        users.forEach(user -> user.setPassword(null));
        return ResponseEntity.ok(users);
    }
    
    // 通过自定义ID获取用户
    @GetMapping("/custom/{customId}")
    public ResponseEntity<User> getUserByCustomId(@PathVariable String customId) {
        Optional<User> user = userService.getUserByCustomId(customId);
        if (user.isPresent()) {
            User returnUser = user.get();
            returnUser.setPassword(null);
            return ResponseEntity.ok(returnUser);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    // 获取单个用户
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Integer id) {
        Optional<User> user = userRepository.findById(id);
        if (user.isPresent()) {
            User returnUser = user.get();
            returnUser.setPassword(null);
            return ResponseEntity.ok(returnUser);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    // 创建用户
    @PostMapping
    public ResponseEntity<?> createUser(@RequestBody User user) {
        try {
            // 使用UserService创建用户，确保生成customId
            User savedUser = userService.createUser(user);
            savedUser.setPassword(null);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedUser);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    // 更新用户信息
    @PutMapping("/{id}")
    public ResponseEntity<?> updateUser(@PathVariable Integer id, @RequestBody User user) {
        if (!userRepository.existsById(id)) {
            return ResponseEntity.notFound().build();
        }
        
        // 获取当前用户数据
        Optional<User> currentUser = userRepository.findById(id);
        if (!currentUser.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        User existingUser = currentUser.get();
        
        // 如果更新了邮箱，检查是否与其他用户冲突
        if (!existingUser.getEmail().equals(user.getEmail()) && 
            userRepository.existsByEmail(user.getEmail())) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Email already exists");
            return ResponseEntity.badRequest().body(error);
        }
        
        // 更新用户数据
        user.setId(id);
        
        // 保留现有的customId
        user.setCustomId(existingUser.getCustomId());
        
        // 如果密码字段为空，表示不修改密码
        if (user.getPassword() == null || user.getPassword().isEmpty()) {
            user.setPassword(existingUser.getPassword());
        } else {
            // 加密新密码
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        
        User updatedUser = userRepository.save(user);
        updatedUser.setPassword(null);
        
        return ResponseEntity.ok(updatedUser);
    }
    
    // 删除用户
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteUser(@PathVariable Integer id) {
        if (!userRepository.existsById(id)) {
            return ResponseEntity.notFound().build();
        }
        
        userRepository.deleteById(id);
        return ResponseEntity.ok().build();
    }
    
    // 重置密码
    @PostMapping("/{id}/reset-password")
    public ResponseEntity<?> resetPassword(@PathVariable Integer id, @RequestBody Map<String, String> passwordData) {
        String newPassword = passwordData.get("password");
        
        if (newPassword == null || newPassword.isEmpty()) {
            return ResponseEntity.badRequest().body("New password is required");
        }
        
        Optional<User> userOpt = userRepository.findById(id);
        if (!userOpt.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        User user = userOpt.get();
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Password reset successfully");
        return ResponseEntity.ok(response);
    }
    
    // 用户验证
    @PostMapping("/authenticate")
    public ResponseEntity<?> authenticate(@RequestBody Map<String, String> credentials) {
        String email = credentials.get("email");
        String password = credentials.get("password");
        
        if (email == null || password == null) {
            return ResponseEntity.badRequest().body("Email and password are required");
        }
        
        Optional<User> user = userRepository.findByEmail(email);
        if (!user.isPresent()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials");
        }
        
        // 尝试使用密码编码器验证
        if (passwordEncoder.matches(password, user.get().getPassword())) {
            User authenticatedUser = user.get();
            authenticatedUser.setPassword(null); // 不返回密码
            return ResponseEntity.ok(authenticatedUser);
        } 
        // 备选方案：如果数据库中存储的是明文密码，直接比较
        else if (password.equals(user.get().getPassword())) {
            User authenticatedUser = user.get();
            authenticatedUser.setPassword(null); // 不返回密码
            
            // 将用户密码升级为加密格式（可选）
            try {
                User userToUpdate = user.get();
                userToUpdate.setPassword(passwordEncoder.encode(password));
                userRepository.save(userToUpdate);
                System.out.println("已将用户 " + email + " 的密码升级为加密格式");
            } catch (Exception e) {
                System.err.println("升级密码失败: " + e.getMessage());
            }
            
            return ResponseEntity.ok(authenticatedUser);
        }
        else {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials");
        }
    }
    
    // 获取当前登录用户信息
    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser() {
        try {
            // 从Spring Security上下文中获取当前用户的电子邮件
            org.springframework.security.core.Authentication auth = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            String email = auth.getName();
            if (email.equals("anonymousUser")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
            }
            
            Optional<User> userOpt = userRepository.findByEmail(email);
            if (!userOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("用户不存在");
            }
            
            User user = userOpt.get();
            user.setPassword(null); // 不返回密码
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 申请成为审核医生
    @PostMapping("/me/reviewer-application")
    public ResponseEntity<?> applyForReviewer(@RequestBody Map<String, String> requestBody) {
        try {
            String reason = requestBody.get("reason");
            
            // 获取当前用户
            org.springframework.security.core.Authentication auth = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            String email = auth.getName();
            if (email.equals("anonymousUser")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
            }
            
            Optional<User> userOpt = userRepository.findByEmail(email);
            if (!userOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("用户不存在");
            }
            
            Integer userId = userOpt.get().getId();
            
            // 创建申请
            ReviewerApplication application = reviewerApplicationService.createApplication(userId, reason);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(application);
        } catch (IllegalArgumentException e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 获取所有待处理的审核医生申请（管理员用）
    @GetMapping("/reviewer-applications")
    public ResponseEntity<?> getPendingReviewerApplications() {
        try {
            List<ReviewerApplication> applications = reviewerApplicationService.getPendingApplications();
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 处理审核医生申请（批准或拒绝）
    @PostMapping("/reviewer-applications/{userId}")
    public ResponseEntity<?> processReviewerApplication(
            @PathVariable Integer userId, 
            @RequestBody Map<String, Object> requestBody) {
        try {
            Boolean approved = (Boolean) requestBody.get("approved");
            String remarks = (String) requestBody.getOrDefault("remarks", "");
            
            // 获取处理人ID
            org.springframework.security.core.Authentication auth = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            String email = auth.getName();
            if (email.equals("anonymousUser")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
            }
            
            Optional<User> processorOpt = userRepository.findByEmail(email);
            if (!processorOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("处理人不存在");
            }
            
            Integer processorId = processorOpt.get().getId();
            
            // 获取用户的待处理申请
            List<ReviewerApplication> applications = reviewerApplicationService.getUserApplications(userId);
            Optional<ReviewerApplication> pendingApplication = applications.stream()
                .filter(app -> app.getStatus().equals("PENDING"))
                .findFirst();
            
            if (!pendingApplication.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("未找到该用户的待处理申请");
            }
            
            // 处理申请
            String status = approved ? "APPROVED" : "REJECTED";
            ReviewerApplication processedApplication = reviewerApplicationService.processApplication(
                pendingApplication.get().getId(), status, remarks, processorId);
            
            return ResponseEntity.ok(processedApplication);
        } catch (IllegalArgumentException e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
} 