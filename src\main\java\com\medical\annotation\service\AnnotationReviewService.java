package com.medical.annotation.service;

import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.model.HemangiomaDiagnosis.Status;
import com.medical.annotation.model.ReviewerApplication;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.HemangiomaDiagnosisRepository;
import com.medical.annotation.repository.ReviewerApplicationRepository;
import com.medical.annotation.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 标注审核服务
 * 处理标注审核相关的业务逻辑
 */
@Service
public class AnnotationReviewService {

    private static final Logger logger = LoggerFactory.getLogger(AnnotationReviewService.class);

    @Autowired
    private HemangiomaDiagnosisRepository hemangiomaDiagnosisRepository;

    @Autowired
    private ReviewerApplicationRepository reviewerApplicationRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 获取待审核的标注列表
     * 根据用户角色和团队关系进行权限过滤：
     * 1. 管理员和审核医生都只能查看无团队的标注和自己团队内的标注
     * 2. 标注医生无权查看待审核标注
     *
     * @param currentUser 当前用户
     * @param pageable 分页参数
     * @return 符合条件的待审核标注列表
     */
    public Page<HemangiomaDiagnosis> getPendingReviews(User currentUser, Pageable pageable) {
        logger.info("用户 {} (ID: {}) 正在获取待审核列表", currentUser.getName(), currentUser.getId());

        Specification<HemangiomaDiagnosis> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("status"), Status.SUBMITTED));
            
            if (!currentUser.getRole().equals("SUPER_ADMIN")) {
                // 非超级管理员只能看到其所在团队的提交
                Integer teamId = currentUser.getTeam() != null ? currentUser.getTeam().getId() : null;
                if (teamId != null) {
                    logger.info("用户属于团队 ID: {}, 限制查询范围", teamId);
                    predicates.add(cb.equal(root.get("user").get("team").get("id"), teamId));
            } else {
                    // 如果没有团队，理论上看不到任何提交，除非是自己的
                     logger.warn("用户 {} (ID: {}) 不属于任何团队，但尝试获取审核列表", currentUser.getName(), currentUser.getId());
                     predicates.add(cb.equal(root.get("user").get("id"), -1)); // 返回空
            }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        
        return hemangiomaDiagnosisRepository.findAll(spec, pageable);
    }

    /**
     * 判断用户是否有权限审核特定标注
     * @param currentUser 当前用户
     * @param diagnosis 标注元数据
     * @return 是否有权限审核
     */
    public boolean canReview(User currentUser, HemangiomaDiagnosis diagnosis) {
        if (currentUser.getRole().equals("SUPER_ADMIN")) {
            return true;
        }
        if (currentUser.getRole().equals("REVIEWER")) {
            User author = diagnosis.getUser();
            return author != null && author.getTeam() != null && currentUser.getTeam() != null &&
                   author.getTeam().getId().equals(currentUser.getTeam().getId());
        }
            return false;
    }

    /**
     * 审核标注
     * @param diagnosisId 标注ID
     * @param approved 是否批准
     * @param reviewNotes 审核备注
     * @param reviewer 审核人
     * @return 审核后的标注元数据
     */
    @Transactional
    public HemangiomaDiagnosis reviewAnnotation(Integer diagnosisId, boolean approved, String reviewNotes, User reviewer) {
        logger.info("用户 {} 正在审核诊断ID: {}, 结果: {}", reviewer.getName(), diagnosisId, approved ? "通过" : "拒绝");

        Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(diagnosisId);
        if (diagnosisOpt.isEmpty()) {
            logger.error("审核失败: 未找到诊断记录 ID: {}", diagnosisId);
            throw new IllegalArgumentException("未找到诊断记录 ID: " + diagnosisId);
        }
        
        HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
        
        if (!canReview(reviewer, diagnosis)) {
             logger.warn("审核失败: 用户 {} 无权审核该诊断记录", reviewer.getName());
             throw new SecurityException("用户无权审核该诊断记录");
        }
        
        diagnosis.setStatus(approved ? Status.APPROVED : Status.REJECTED);
        // 未来可以增加一个字段来存储 reviewNotes
        
        return hemangiomaDiagnosisRepository.save(diagnosis);
    }

    /**
     * 自动完成审核（用于ADMIN和REVIEWER角色自己提交的标注）
     * @param diagnosis 标注元数据
     * @param user 提交人
     * @return 更新后的标注元数据
     */
    @Transactional
    public HemangiomaDiagnosis autoApprove(HemangiomaDiagnosis diagnosis, User user) {
        if (user.getRole().equals("REVIEWER") || user.getRole().equals("SUPER_ADMIN")) {
            logger.info("用户 {} (角色: {}) 提交的诊断 (ID: {}) 被自动批准", user.getName(), user.getRole(), diagnosis.getId());
            diagnosis.setStatus(Status.APPROVED);
        } else {
             diagnosis.setStatus(Status.SUBMITTED);
        }
        return hemangiomaDiagnosisRepository.save(diagnosis);
    }
} 