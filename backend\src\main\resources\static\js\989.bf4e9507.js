"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[989],{7989:(e,a,r)=>{r.r(a),r.d(a,{default:()=>I});r(62010);var s=r(61431),l={class:"register-page register-background"},n={class:"register-container"},i={class:"register-form-container"},t={class:"register-form-box"},o={class:"register-form"},u={key:0,class:"alert alert-danger"},d={class:"error-content"},c={class:"error-message"},v={key:1,class:"alert alert-success"},p={class:"form-group"},k={class:"input-with-icon"},g=["disabled"],m={class:"form-group"},f={class:"input-with-icon"},w=["disabled"],h={key:0,class:"field-error"},L={class:"form-group"},b={class:"input-with-icon"},y=["disabled"],E={key:0,class:"field-error"},R={key:1,class:"password-strength"},_={class:"strength-indicator"},C={class:"form-group"},K={class:"input-with-icon"},S=["disabled"],A={class:"form-group"},P={class:"input-with-icon"},x=["disabled"],X={class:"form-group"},q={class:"input-with-icon"},B=["disabled"],J={class:"form-actions"},Q=["disabled"],U={key:0,class:"spinner"},V={class:"login-link"};function z(e,a,r,z,Z,$){var D=(0,s.g2)("router-link");return(0,s.uX)(),(0,s.CE)("div",l,[(0,s.Lk)("div",n,[(0,s.Lk)("div",i,[(0,s.Lk)("div",t,[a[20]||(a[20]=(0,s.Lk)("div",{class:"register-tabs"},[(0,s.Lk)("div",{class:"tab-item active"},"用户注册")],-1)),(0,s.Lk)("div",o,[z.error?((0,s.uX)(),(0,s.CE)("div",u,[(0,s.Lk)("div",d,[a[9]||(a[9]=(0,s.Lk)("div",{class:"error-icon"},"⚠️",-1)),(0,s.Lk)("div",c,(0,s.v_)(z.error),1)])])):(0,s.Q3)("",!0),z.success?((0,s.uX)(),(0,s.CE)("div",v,(0,s.v_)(z.success),1)):(0,s.Q3)("",!0),(0,s.Lk)("form",{onSubmit:a[8]||(a[8]=(0,s.D$)((function(){return z.handleRegister&&z.handleRegister.apply(z,arguments)}),["prevent"]))},[(0,s.Lk)("div",p,[(0,s.Lk)("div",k,[a[10]||(a[10]=(0,s.Lk)("i",{class:"icon-user"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"name","onUpdate:modelValue":a[0]||(a[0]=function(e){return z.name=e}),type:"text",placeholder:"请输入您的姓名",required:"",disabled:z.loading},null,8,g),[[s.Jo,z.name]])])]),(0,s.Lk)("div",m,[(0,s.Lk)("div",f,[a[11]||(a[11]=(0,s.Lk)("i",{class:"icon-email"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"email","onUpdate:modelValue":a[1]||(a[1]=function(e){return z.email=e}),type:"email",placeholder:"请输入邮箱地址",required:"",disabled:z.loading,onBlur:a[2]||(a[2]=function(){return z.validateEmail&&z.validateEmail.apply(z,arguments)})},null,40,w),[[s.Jo,z.email]])]),z.emailError?((0,s.uX)(),(0,s.CE)("div",h,(0,s.v_)(z.emailError),1)):(0,s.Q3)("",!0)]),(0,s.Lk)("div",L,[(0,s.Lk)("div",b,[a[12]||(a[12]=(0,s.Lk)("i",{class:"icon-lock"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"password","onUpdate:modelValue":a[3]||(a[3]=function(e){return z.password=e}),type:"password",placeholder:"请设置密码",required:"",disabled:z.loading,onBlur:a[4]||(a[4]=function(){return z.validatePassword&&z.validatePassword.apply(z,arguments)})},null,40,y),[[s.Jo,z.password]])]),z.passwordError?((0,s.uX)(),(0,s.CE)("div",E,(0,s.v_)(z.passwordError),1)):(0,s.Q3)("",!0),z.passwordStrength&&!z.passwordError?((0,s.uX)(),(0,s.CE)("div",R,[a[13]||(a[13]=(0,s.Lk)("div",{class:"strength-label"},"密码强度：",-1)),(0,s.Lk)("div",_,[(0,s.Lk)("div",{class:(0,s.C4)(["strength-bar",{weak:"weak"===z.passwordStrength,medium:"medium"===z.passwordStrength,strong:"strong"===z.passwordStrength}])},null,2)]),(0,s.Lk)("div",{class:(0,s.C4)(["strength-text",z.passwordStrength])},(0,s.v_)("weak"===z.passwordStrength?"弱":"medium"===z.passwordStrength?"中":"strong"===z.passwordStrength?"强":""),3)])):(0,s.Q3)("",!0),a[14]||(a[14]=(0,s.Lk)("div",{class:"password-hint"}," 密码长度至少8位，必须包含大写字母、小写字母、数字和特殊符号中的至少三种 ",-1))]),(0,s.Lk)("div",C,[(0,s.Lk)("div",K,[a[15]||(a[15]=(0,s.Lk)("i",{class:"icon-lock"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"confirmPassword","onUpdate:modelValue":a[5]||(a[5]=function(e){return z.confirmPassword=e}),type:"password",placeholder:"请再次输入密码",required:"",disabled:z.loading},null,8,S),[[s.Jo,z.confirmPassword]])])]),(0,s.Lk)("div",A,[(0,s.Lk)("div",P,[a[16]||(a[16]=(0,s.Lk)("i",{class:"icon-hospital"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"hospital","onUpdate:modelValue":a[6]||(a[6]=function(e){return z.hospital=e}),type:"text",placeholder:"请输入所在医院",required:"",disabled:z.loading},null,8,x),[[s.Jo,z.hospital]])])]),(0,s.Lk)("div",X,[(0,s.Lk)("div",q,[a[17]||(a[17]=(0,s.Lk)("i",{class:"icon-department"},null,-1)),(0,s.bo)((0,s.Lk)("input",{id:"department","onUpdate:modelValue":a[7]||(a[7]=function(e){return z.department=e}),type:"text",placeholder:"请输入所在科室",disabled:z.loading},null,8,B),[[s.Jo,z.department]])])]),(0,s.Lk)("div",J,[(0,s.Lk)("button",{type:"submit",class:"register-btn",disabled:z.loading||!!z.emailError||!!z.passwordError},[z.loading?((0,s.uX)(),(0,s.CE)("span",U)):(0,s.Q3)("",!0),(0,s.Lk)("span",null,(0,s.v_)(z.loading?"注册中...":"立即注册"),1)],8,Q)])],32),(0,s.Lk)("div",V,[a[19]||(a[19]=(0,s.Lk)("span",null,"已有账号?",-1)),(0,s.bF)(D,{to:"/login",class:"login-btn"},{default:(0,s.k6)((function(){return a[18]||(a[18]=[(0,s.eW)("立即登录")])})),_:1,__:[18]})])]),a[21]||(a[21]=(0,s.Lk)("div",{class:"register-footer"},[(0,s.Lk)("span")],-1))])])])])}var Z=r(24059),$=r(698),D=(r(2008),r(74423),r(44114),r(26099),r(27495),r(90906),r(21699),r(42762),r(76031),r(66278)),O=r(60455);const T={name:"Register",setup:function(){var e=(0,D.Pj)(),a=(0,O.rd)(),r=(0,s.KR)(""),l=(0,s.KR)(""),n=(0,s.KR)(""),i=(0,s.KR)(""),t=(0,s.KR)(""),o=(0,s.KR)(""),u=(0,s.KR)(!1),d=(0,s.KR)(""),c=(0,s.KR)(""),v=(0,s.KR)(""),p=(0,s.KR)(""),k=(0,s.KR)(""),g=function(){if(!l.value.trim())return v.value="邮箱地址不能为空",!1;var e=/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;return e.test(l.value)?(v.value="",!0):(v.value="请输入有效的邮箱地址",!1)},m=function(){var e=n.value;if(e.length<8)return p.value="密码长度必须不少于8位",k.value="weak",!1;var a=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/[0-9]/.test(e),l=/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e),i=[a,r,s,l].filter(Boolean).length,t=["123456","password","abcdef","12345678","qwerty","111111"];return t.includes(e.toLowerCase())?(p.value="请勿使用常见的简单密码",k.value="weak",!1):i<3?(p.value="密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种",k.value="weak",!1):(3===i&&e.length<10?k.value="medium":(4===i||3===i&&e.length>=10)&&(k.value="strong"),p.value="",!0)};(0,s.wB)(l,(function(){v.value&&(v.value=""),d.value&&(d.value.includes("邮箱已被注册")||d.value.includes("邮箱")||d.value.includes("已存在"))&&(d.value="")})),(0,s.wB)(n,(function(){n.value?m():(p.value="",k.value="")})),(0,s.wB)(i,(function(){i.value&&n.value!==i.value?d.value="两次输入的密码不一致":d.value=""}));var f=function(){var s=(0,$.A)((0,Z.A)().m((function s(){var v,p,f;return(0,Z.A)().w((function(s){while(1)switch(s.n){case 0:if(g()){s.n=1;break}return s.a(2);case 1:if(m()){s.n=2;break}return s.a(2);case 2:if(n.value===i.value){s.n=3;break}return d.value="两次输入的密码不一致",s.a(2);case 3:return u.value=!0,d.value="",c.value="",s.p=4,s.n=5,e.dispatch("register",{name:r.value,email:l.value,password:n.value,hospital:t.value,department:o.value,role:"DOCTOR"});case 5:if(v=s.v,"string"!==typeof v||!(v.includes("邮箱已被注册")||v.includes("email already exists")||v.includes("已存在")||v.includes("already registered"))){s.n=6;break}return d.value="该邮箱已被注册，请更换邮箱",s.a(2);case 6:c.value="注册成功，请登录",r.value="",l.value="",n.value="",i.value="",t.value="",o.value="",k.value="",setTimeout((function(){a.push("/login")}),3e3),s.n=8;break;case 7:s.p=7,f=s.v,console.error("Registration error:",f),p=function(e){if(!e)return!1;var a="string"===typeof e?e:e.message||"";return a.includes("邮箱已被注册")||a.includes("email already exists")||a.includes("已存在")||a.includes("already registered")},f.response&&f.response.data&&p(f.response.data)||f.message&&p(f.message)?d.value="该邮箱已被注册，请更换邮箱":d.value="注册失败，请检查填写信息";case 8:return s.p=8,u.value=!1,s.f(8);case 9:return s.a(2)}}),s,null,[[4,7,8,9]])})));return function(){return s.apply(this,arguments)}}();return{name:r,email:l,password:n,confirmPassword:i,hospital:t,department:o,loading:u,error:d,success:c,emailError:v,passwordError:p,passwordStrength:k,validateEmail:g,validatePassword:m,handleRegister:f}}};var j=r(66262);const F=(0,j.A)(T,[["render",z],["__scopeId","data-v-b9aadf68"]]),I=F}}]);
//# sourceMappingURL=989.bf4e9507.js.map