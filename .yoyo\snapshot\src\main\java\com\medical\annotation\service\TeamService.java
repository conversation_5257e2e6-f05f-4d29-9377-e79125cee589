package com.medical.annotation.service;

import com.medical.annotation.model.Team;
import com.medical.annotation.model.TeamMemberLog;
import com.medical.annotation.model.User;
import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.repository.TeamMemberLogRepository;
import com.medical.annotation.repository.TeamRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.ImageMetadataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class TeamService {

    @Autowired
    private TeamRepository teamRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TeamMemberLogRepository teamMemberLogRepository;
    
    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    /**
     * 创建新团队
     * @param team 团队信息
     * @param creatorId 创建者ID
     * @return 创建的团队
     */
    @Transactional
    public Team createTeam(Team team, Integer creatorId) throws Exception {
        // 验证团队名是否已存在
        if (teamRepository.existsByName(team.getName())) {
            throw new Exception("团队名称已存在");
        }
        
        // 验证创建者是否存在
        Optional<User> creatorOpt = userRepository.findById(creatorId);
        if (!creatorOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        User creator = creatorOpt.get();
        // 移除角色限制，允许任何用户创建团队
        // if (creator.getRole() != User.Role.ADMIN && creator.getRole() != User.Role.REVIEWER) {
        //     throw new Exception("无权创建团队");
        // }
        
        // 设置创建者和时间
        team.setCreatedBy(creator);
        team.setCreatedAt(LocalDateTime.now());
        
        // 保存团队
        Team savedTeam = teamRepository.save(team);
        
        try {
            // 将创建者自动添加为团队成员
            creator.setTeam(savedTeam);
            userRepository.save(creator);
            
            // 记录团队成员添加日志
            addTeamMemberLog(creator.getId(), savedTeam.getId(), creator.getId());
            
            System.out.println("自动将创建者 " + creator.getName() + " (ID: " + creator.getId() + ") 添加为团队 " + savedTeam.getName() + " (ID: " + savedTeam.getId() + ") 的成员");
        } catch (Exception e) {
            System.err.println("添加创建者到团队失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return savedTeam;
    }
    
    /**
     * 添加团队成员日志
     */
    private void addTeamMemberLog(Integer userId, Integer teamId, Integer performedBy) {
        addTeamMemberLog(userId, teamId, performedBy, "ADD");
    }

    /**
     * 添加团队成员日志，支持指定动作
     */
    private void addTeamMemberLog(Integer userId, Integer teamId, Integer performedBy, String action) {
        try {
            // 获取用户信息以提取customId
            Optional<User> userOpt = userRepository.findById(userId);
            if (!userOpt.isPresent()) {
                System.err.println("添加团队成员日志失败: 用户不存在 (ID=" + userId + ")");
                return;
            }
            
            User user = userOpt.get();
            String customId = user.getCustomId();
            
            if (customId == null || customId.isEmpty()) {
                System.err.println("添加团队成员日志失败: 用户缺少customId (ID=" + userId + ")");
                // 使用数字ID作为备用，增加容错性
                customId = "USER_" + userId.toString();
                System.out.println("使用备用customId: " + customId);
            }
            
            try {
            // 创建并保存团队成员日志
                TeamMemberLog log = new TeamMemberLog(customId, userId, teamId, action, performedBy);
            teamMemberLogRepository.save(log);
            
                System.out.println("添加了团队成员日志: 用户ID=" + userId + ", customId=" + customId + ", 团队ID=" + teamId + ", 操作=" + action + ", 操作者ID=" + performedBy);
            } catch (Exception e) {
                System.err.println("保存团队成员日志时出错: " + e.getMessage());
                e.printStackTrace();
                
                // 尝试不带customId保存
                try {
                    TeamMemberLog fallbackLog = new TeamMemberLog(userId, teamId, action, performedBy);
                    fallbackLog.setCustomId("USER_" + userId.toString());
                    teamMemberLogRepository.save(fallbackLog);
                    System.out.println("使用备用方法添加了团队成员日志");
                } catch (Exception ex) {
                    System.err.println("备用方法添加团队成员日志也失败: " + ex.getMessage());
                    ex.printStackTrace();
                }
            }
        } catch (Exception e) {
            System.err.println("添加团队成员日志失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 更新团队信息
     * @param team 更新的团队信息
     * @param userId 操作用户ID
     * @return 更新后的团队
     */
    @Transactional
    public Team updateTeam(Team team, Integer userId) throws Exception {
        // 验证团队是否存在
        Optional<Team> existingTeamOpt = teamRepository.findById(team.getId());
        if (!existingTeamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        
        // 验证用户是否有权限（只有管理员、团队创建者或审核医生可以更新团队）
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        User user = userOpt.get();
        Team existingTeam = existingTeamOpt.get();
        
        if (user.getRole() != User.Role.ADMIN && 
            !existingTeam.getCreatedBy().getId().equals(userId) && 
            user.getRole() != User.Role.REVIEWER) {
            throw new Exception("无权更新团队");
        }
        
        // 更新团队信息
        existingTeam.setName(team.getName());
        existingTeam.setDescription(team.getDescription());
        existingTeam.setUpdatedAt(LocalDateTime.now());
        
        return teamRepository.save(existingTeam);
    }
    
    /**
     * 删除团队
     * @param teamId 团队ID
     * @param userId 操作用户ID
     */
    @Transactional
    public void deleteTeam(Integer teamId, Integer userId) throws Exception {
        // 验证团队是否存在
        Optional<Team> teamOpt = teamRepository.findById(teamId);
        if (!teamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        
        // 验证用户是否有权限（只有管理员或团队创建者可以删除团队）
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        User user = userOpt.get();
        Team team = teamOpt.get();
        
        if (user.getRole() != User.Role.ADMIN && 
            !team.getCreatedBy().getId().equals(userId)) {
            throw new Exception("无权删除团队");
        }
        
        // 验证团队是否有成员
        List<User> teamMembers = userRepository.findByTeam_Id(teamId);
        if (!teamMembers.isEmpty()) {
            throw new Exception("团队中仍有成员，请先移除所有成员");
        }
        
        // 删除团队
        teamRepository.delete(team);
    }
    
    /**
     * 将用户添加到团队
     * @param teamId 团队ID
     * @param userId 要添加的用户ID
     * @param operatorId 操作者ID
     */
    @Transactional
    public void addUserToTeam(Integer teamId, Integer userId, Integer operatorId) throws Exception {
        // 验证团队是否存在
        Optional<Team> teamOpt = teamRepository.findById(teamId);
        if (!teamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        
        // 验证要添加的用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        // 验证操作者是否有权限
        Optional<User> operatorOpt = userRepository.findById(operatorId);
        if (!operatorOpt.isPresent()) {
            throw new Exception("操作者不存在");
        }
        
        User operator = operatorOpt.get();
        Team team = teamOpt.get();
        
        if (operator.getRole() != User.Role.ADMIN && 
            !team.getCreatedBy().getId().equals(operatorId) &&
            operator.getRole() != User.Role.REVIEWER) {
            throw new Exception("无权添加团队成员");
        }
        
        // 将用户添加到团队
        User user = userOpt.get();
        user.setTeam(team);
        userRepository.save(user);
        
        // 记录团队成员添加日志
        addTeamMemberLog(userId, teamId, operatorId);
    }
    
    /**
     * 将用户从团队中移除
     * @param teamId 团队ID
     * @param userId 要移除的用户ID
     * @param operatorId 操作者ID
     */
    @Transactional
    public void removeUserFromTeam(Integer teamId, Integer userId, Integer operatorId) throws Exception {
        // 验证团队是否存在
        Optional<Team> teamOpt = teamRepository.findById(teamId);
        if (!teamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        
        // 验证要移除的用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        // 验证操作者是否有权限
        Optional<User> operatorOpt = userRepository.findById(operatorId);
        if (!operatorOpt.isPresent()) {
            throw new Exception("操作者不存在");
        }
        
        User operator = operatorOpt.get();
        Team team = teamOpt.get();
        User user = userOpt.get();
        
        if (operator.getRole() != User.Role.ADMIN && 
            !team.getCreatedBy().getId().equals(operatorId) &&
            operator.getRole() != User.Role.REVIEWER) {
            throw new Exception("无权移除团队成员");
        }
        
        // 验证用户是否在该团队中
        if (user.getTeam() == null || !user.getTeam().getId().equals(teamId)) {
            throw new Exception("用户不在该团队中");
        }
        
        // 将用户从团队中移除
        user.setTeam(null);
        userRepository.save(user);
        
        // 记录团队成员移除日志
        addTeamMemberLog(userId, teamId, operatorId, "REMOVE");
    }
    
    /**
     * 获取团队信息
     * @param teamId 团队ID
     * @return 团队信息
     */
    public Team getTeamById(Integer teamId) throws Exception {
        Optional<Team> teamOpt = teamRepository.findById(teamId);
        if (!teamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        return teamOpt.get();
    }
    
    /**
     * 获取团队成员
     * @param teamId 团队ID
     * @return 团队成员列表
     */
    public List<User> getTeamMembers(Integer teamId) throws Exception {
        // 验证团队是否存在
        if (!teamRepository.existsById(teamId)) {
            throw new Exception("团队不存在");
        }
        
        return userRepository.findByTeam_Id(teamId);
    }
    
    /**
     * 获取所有团队
     * @return 团队列表
     */
    public List<Team> getAllTeams() {
        return teamRepository.findAll();
    }
    
    /**
     * 搜索团队
     * @param keyword 关键词
     * @return 团队列表
     */
    public List<Team> searchTeams(String keyword) {
        return teamRepository.findByNameContainingIgnoreCase(keyword);
    }
    
    /**
     * 获取团队中已通过审核的标注
     * @param teamId 团队ID
     * @return 已通过标注列表
     */
    public List<ImageMetadata> getTeamApprovedAnnotations(Integer teamId) throws Exception {
        // 验证团队是否存在
        if (!teamRepository.existsById(teamId)) {
            throw new Exception("团队不存在");
        }
        
        List<ImageMetadata> annotations = imageMetadataRepository.findByStatusAndTeam_Id(ImageMetadata.Status.APPROVED, teamId);
        
        // 确保每个标注都有必要的显示信息
        for (ImageMetadata annotation : annotations) {
            // 设置标注医生姓名
            User uploader = annotation.getUploadedBy();
            if (uploader != null) {
                annotation.setUploadedByName(uploader.getName()); 
            }
            
            // 确保每个标注都有病例号，如果没有则生成默认值
            if (annotation.getCaseNumber() == null || annotation.getCaseNumber().trim().isEmpty()) {
                annotation.setCaseNumber("CASE-" + annotation.getId());
            }
        }
        
        return annotations;
    }
} 