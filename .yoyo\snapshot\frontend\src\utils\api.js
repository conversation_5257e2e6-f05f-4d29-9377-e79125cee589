import axios from 'axios';

// 创建一个直接指向后端API的客户端
const apiClient = axios.create({
  baseURL: '/api',  // 使用/api作为前缀，会被代理配置重写
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000,
  withCredentials: true, // 启用跨域Cookie
  
  // 增强版循环引用处理
  transformResponse: [
    function(data) {
      if (typeof data === 'string') {
        try {
          // 更安全的JSON解析，处理循环引用和编码问题
          let obj;
          
          try {
            obj = JSON.parse(data);
          } catch (parseError) {
            // 尝试处理可能的UTF-8 BOM标记
            if (data.charCodeAt(0) === 0xFEFF) {
              console.log('检测到UTF-8 BOM标记，尝试移除后解析');
              obj = JSON.parse(data.slice(1));
            } else {
              throw parseError;
            }
          }
          
          // 用于处理循环引用检测的WeakMap
          const seenObjects = new WeakMap();
          
          // 深度优先处理，移除循环引用
          function sanitizeObject(obj, path = '') {
            // 基本类型直接返回
            if (obj === null || obj === undefined || typeof obj !== 'object') {
              return obj;
            }
            
            // 检测循环引用
            if (seenObjects.has(obj)) {
              console.warn(`检测到循环引用: ${path} -> 引用路径: ${seenObjects.get(obj)}`);
              // 返回一个简化版，只保留ID信息
              if (obj.id) {
                return { id: obj.id, _isCircularRef: true };
              }
              return { _isCircularRef: true };
            }
            
            // 标记当前对象为已处理，记录路径
            seenObjects.set(obj, path);
            
            // 数组特殊处理
            if (Array.isArray(obj)) {
              return obj.map((item, index) => 
                sanitizeObject(item, `${path}[${index}]`));
            }
            
            // 对象处理
            const result = {};
            for (const key in obj) {
              if (Object.prototype.hasOwnProperty.call(obj, key)) {
                // 某些特殊字段采用特殊处理
                if (key === 'createdBy' && obj.id) {
                  // 对于createdBy字段，如果已经有ID了，只保留ID和名称
                  if (obj[key] && typeof obj[key] === 'object') {
                    result[key] = {
                      id: obj[key].id,
                      name: obj[key].name,
                      _simplified: true
                    };
                  } else {
                    result[key] = obj[key];
                  }
                } else {
                  // 普通字段正常递归处理
                  result[key] = sanitizeObject(obj[key], path ? `${path}.${key}` : key);
                }
              }
            }
            
            return result;
          }
          
          return sanitizeObject(obj);
        } catch(e) {
          console.error('转换响应数据时出错:', e);
          return data;
        }
      }
      return data;
    }
  ]
});

// 配置请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 使用通用函数规范化URL路径
    config.url = normalizePath(config.url);
    console.log(`[API] ${config.method.toUpperCase()} ${config.url}`);
    
    // 从localStorage获取用户信息，添加到请求头
    let user;
    try {
      // 首先尝试从localStorage获取
      const userStr = localStorage.getItem('user');
      if (userStr) {
        user = JSON.parse(userStr);
      }
      
      // 如果localStorage中没有用户信息，尝试从sessionStorage获取
      if (!user || !user.id) {
        const sessionUserStr = sessionStorage.getItem('preservedUser');
        if (sessionUserStr) {
          console.log('[API] 从会话存储恢复用户信息');
          user = JSON.parse(sessionUserStr);
          // 同步回localStorage，保持一致性
          localStorage.setItem('user', sessionUserStr);
        }
      }
      
      // 如果用户对象存在但不完整，尝试恢复更多信息
      if (user && (!user.email || !user.role)) {
        const userProfileStr = localStorage.getItem('userProfile');
        if (userProfileStr) {
          const userProfile = JSON.parse(userProfileStr);
          // 合并缺失的信息
          user = { ...user, ...userProfile };
          console.log('[API] 从用户配置文件合并额外信息');
        }
      }
    } catch (e) {
      console.error('解析用户信息失败:', e);
    }
    
    if (user) {
      // 使用标准Authorization头而非自定义头，避免CORS问题
      const userId = user.customId || user.id || '';
      config.headers['Authorization'] = `Bearer user_${userId}`;
      
      // 添加更多认证信息到Authorization头，统一格式
      let authParts = [`user_${userId}`];
      
      // 添加邮箱信息（如果存在）
      if (user.email) {
        try {
          const emailBase64 = btoa(encodeURIComponent(user.email));
          authParts.push(emailBase64);
        } catch (e) {
          console.warn('邮箱编码失败:', e);
        }
      }
      
      // 添加角色信息（如果存在）
      if (user.role) {
        authParts.push(`role_${user.role}`);
      }
      
      // 添加团队信息（如果存在）
      if (user.team && user.team.id) {
        authParts.push(`team_${user.team.id}`);
      }
      
      // 组合所有信息到一个Authorization头
      config.headers['Authorization'] = authParts.join(':');
      
      // 确保用户角色始终添加到URL参数
      if (user.role) {
        const separator = config.url.includes('?') ? '&' : '?';
        config.url += `${separator}_role=${user.role}`;
      }
      
      // 对于团队相关请求，添加团队ID到URL参数
      if (user.team && user.team.id && 
         (config.url.includes('/teams/') || 
          config.url.includes('/team-applications') || 
          config.url.includes('/images/team-'))) {
        const separator = config.url.includes('?') ? '&' : '?';
        config.url += `${separator}_teamId=${user.team.id}`;
      }
      
      // 为标注相关的请求添加特殊处理
      if (config.url.includes('/annotations') || 
          config.url.includes('/tags') || 
          config.url.includes('/structured-form')) {
        // 添加时间戳，避免缓存问题
        const timestamp = Date.now();
        const separator = config.url.includes('?') ? '&' : '?';
        config.url += `${separator}_t=${timestamp}`;
      }
    } else {
      console.warn('[API] 未找到用户信息，请求可能未认证');
    }
    
    return config;
  },
  error => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 配置响应拦截器
apiClient.interceptors.response.use(response => {
  console.log(`[API响应] ${response.status} ${response.config.url}`, {
    数据: response.data
  });
  return response;
}, error => {
  console.error('[API响应错误]', {
    请求URL: error.config ? error.config.url : '未知',
    请求方法: error.config ? error.config.method : '未知',
    状态码: error.response ? error.response.status : '网络错误',
    响应数据: error.response ? error.response.data : null
  });
  return Promise.reject(error);
});

// 创建一个使用代理的客户端，避免CORS问题
// 这会使用当前域名和端口，通过服务器代理访问
const fileApiClient = axios.create({
  baseURL: '/api',  // 使用/api作为前缀，会被代理配置重写
  headers: {
    'Content-Type': 'multipart/form-data', // 文件上传
    'Accept': 'application/json'
  },
  timeout: 15000,
  withCredentials: true
});

// 创建一个专门用于调试的客户端，便于跟踪请求与响应
const debugApiClient = axios.create({
  baseURL: '/api',  // 使用/api作为前缀，会被代理配置重写
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Debug': 'true'  // 添加调试标记
  },
  timeout: 10000,
  withCredentials: true
});

// 调试客户端拦截器
debugApiClient.interceptors.request.use(
  config => {
    // 使用通用函数规范化URL路径
    config.url = normalizePath(config.url);
    console.log(`[Debug API] 请求: ${config.method.toUpperCase()} ${config.baseURL}${config.url}`);
    
    // 添加用户认证信息，与apiClient保持一致
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        if (user) {
          // 使用标准Authorization头
          config.headers['Authorization'] = `Bearer user_${user.customId || user.id || ''}`;
          
          // 将额外信息编码到Authorization头中
          if (user.email) {
            const emailBase64 = btoa(user.email);
            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
          }
          
          if (user.role) {
            // 将角色信息添加到请求URL参数中
            const separator = config.url.includes('?') ? '&' : '?';
            config.url += `${separator}_role=${user.role}`;
          }
        }
      }
    } catch (e) {
      console.error('[Debug API] 读取用户信息失败:', e);
    }
    
    console.log('请求头:', config.headers);
    return config;
  },
  error => {
    console.error('[Debug API] 请求错误:', error);
    return Promise.reject(error);
  }
);

debugApiClient.interceptors.response.use(
  response => {
    console.log(`[Debug API] 响应状态: ${response.status}`);
    console.log('响应数据:', response.data);
    return response;
  },
  error => {
    console.error('[Debug API] 响应错误:', error.response || error);
    return Promise.reject(error);
  }
);

// 文件API拦截器
fileApiClient.interceptors.request.use(
  config => {
    // 使用通用函数规范化URL路径
    config.url = normalizePath(config.url);
    console.log(`[FileAPI] ${config.method.toUpperCase()} ${config.url}`);
    
    // 添加用户信息到请求头 - 修复认证问题
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
    if (user) {
          // 不使用可能导致CORS预检失败的自定义头
          config.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;
          if (user.email) {
            // 使用标准Authorization头部格式而非自定义头
            const emailBase64 = btoa(user.email);
            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
          }
        }
      }
    } catch (e) {
      console.error('[FileAPI] 读取用户信息失败:', e);
    }
    
    return config;
  },
  error => {
    console.error('[FileAPI Request Error]', error);
    return Promise.reject(error);
  }
);

fileApiClient.interceptors.response.use(
  response => {
    console.log(`[FileAPI] Response: ${response.status}`);
    return response;
  },
  error => {
    if (error.response) {
      console.error(`[FileAPI] Error: ${error.response.status}`, error.response.data);
    } else {
      console.error('[FileAPI] Network Error:', error.message);
    }
    return Promise.reject(error);
  }
);

// 添加响应拦截器
apiClient.interceptors.response.use(
  response => {
    console.log(`[API] Response: ${response.status}`);
    // 添加更详细的日志，帮助调试数据结构问题
    try {
      const url = response.config.url;
      
      // 特殊处理团队成员API
      if (url.includes('/teams/') && url.includes('/members')) {
        console.log(`[API Debug] 团队成员API返回数据:`, 
          typeof response.data, 
          Array.isArray(response.data) ? '是数组' : '不是数组',
          response.data ? '非空' : '为空');
        
        // 修复非数组的团队成员响应
        if (response.data && !Array.isArray(response.data)) {
          console.warn('[API Debug] 团队成员API返回非数组数据，尝试修复');
          
          // 如果响应是对象但不是数组，尝试将其转换为数组
          if (typeof response.data === 'object') {
            // 检查已知的团队成员响应格式
            const extractedArray = response.data.members || response.data.data || response.data.content || response.data.users;
            if (Array.isArray(extractedArray)) {
              console.log('[API Debug] 从对象中提取到数组数据');
              response.data = extractedArray;
            } else if (response.data.content && typeof response.data.content === 'object') {
              // 处理嵌套内容对象
              const nestedArray = response.data.content.members || response.data.content.users;
              if (Array.isArray(nestedArray)) {
                console.log('[API Debug] 从嵌套对象中提取到数组数据');
                response.data = nestedArray;
              } else {
                // 如果无法提取数组，将对象放入数组中
                console.log('[API Debug] 将对象放入数组中');
                response.data = [response.data];
              }
            } else {
              // 检查是否有普通对象结构的团队成员
              const hasUserFields = response.data.id && (response.data.name || response.data.email);
              if (hasUserFields) {
                console.log('[API Debug] 检测到单个团队成员对象');
                response.data = [response.data];
              } else {
                // 尝试从对象键值中查找成员
                const membersFromKeys = Object.values(response.data).filter(v => 
                  typeof v === 'object' && v !== null && (v.id || v.userId || v.user_id)
                );
                if (membersFromKeys.length > 0) {
                  console.log('[API Debug] 从对象键值中提取成员');
                  response.data = membersFromKeys;
                } else {
                  // 最后尝试：将对象放入数组
                  console.log('[API Debug] 将对象放入数组中');
                  response.data = [response.data];
                }
              }
            }
          } else {
            // 如果不是对象也不是数组，返回空数组
            console.warn('[API Debug] 无法解析团队成员数据，返回空数组');
            response.data = [];
          }
        }
        
        // 确保数据始终是数组
        if (!response.data) {
          console.warn('[API Debug] 团队成员API返回空数据，初始化为空数组');
          response.data = [];
        }
        
        // 处理团队成员的用户ID和名称格式化
        if (Array.isArray(response.data)) {
          response.data = response.data.map(member => {
            // 如果没有id属性但有user_id，规范化数据结构
            if (!member.id && member.user_id) {
              member.id = member.user_id;
            }
            // 如果没有name属性但有username或user对象，规范化数据结构
            if (!member.name) {
              if (member.username) {
                member.name = member.username;
              } else if (member.user && member.user.name) {
                member.name = member.user.name;
              }
            }
            return member;
          });
        }
        
        console.log('[API Debug] 处理后的团队成员数据，成员数量:', 
          Array.isArray(response.data) ? response.data.length : 0);
      }
      
      // 用户API日志
      if (url.includes('/users')) {
        console.log(`[API Debug] 用户API返回数据结构:`, 
          typeof response.data, 
          Array.isArray(response.data) ? '是数组' : '不是数组',
          response.data ? '非空' : '为空');
          
        // 如果是对象且不是数组，打印其结构
        if (response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
          console.log('[API Debug] 对象键:', Object.keys(response.data));
        }
      }
      
      // 图像API日志 - 添加专门处理病例列表
      if (url.includes('/images/') || url.includes('/api/images')) {
        console.log(`[API Debug] 图像API返回数据:`, 
          typeof response.data, 
          Array.isArray(response.data) ? `是数组[${response.data.length}]` : '不是数组',
          response.data ? '非空' : '为空');
          
        // 如果响应是空的，初始化为空数组
        if (!response.data && (url.includes('my-images') || url.includes('team-images'))) {
          console.warn('[API Debug] 病例列表返回空数据，初始化为空数组');
          response.data = [];
        }
        
        // 确保病例列表是数组格式
        if (response.data && !Array.isArray(response.data) && 
           (url.includes('my-images') || url.includes('team-images'))) {
          console.warn('[API Debug] 病例列表返回非数组数据，尝试修复');
          if (typeof response.data === 'object') {
            // 检查多种可能的嵌套数组字段
            const extractedArray = response.data.content || response.data.images || 
                                  response.data.data || response.data.records || 
                                  response.data.items || response.data.list;
            
            if (Array.isArray(extractedArray)) {
              console.log('[API Debug] 从对象中提取到病例数组数据');
              response.data = extractedArray;
            } else if (response.data.page && typeof response.data.page === 'object') {
              // 处理分页结构
              const pagedArray = response.data.page.content || response.data.page.data || response.data.page.list;
              if (Array.isArray(pagedArray)) {
                console.log('[API Debug] 从分页对象中提取病例数组');
                response.data = pagedArray;
              } else {
                console.log('[API Debug] 将单个病例对象放入数组');
                response.data = [response.data];
              }
            } else if (Object.keys(response.data).length > 0 && 
                     (response.data.id || response.data.imageId || response.data.metadata_id)) {
              // 如果是单个病例对象
              console.log('[API Debug] 检测到单个病例对象，转换为数组');
              response.data = [response.data];
            } else {
              // 尝试从对象值中提取病例列表
              const imagesFromValues = Object.values(response.data).filter(v => 
                Array.isArray(v) && v.length > 0 && 
                typeof v[0] === 'object' && (v[0].id || v[0].imageId || v[0].metadata_id)
              );
              
              if (imagesFromValues.length > 0) {
                console.log('[API Debug] 从对象值中提取病例数组');
                response.data = imagesFromValues[0]; // 使用第一个找到的数组
              } else {
                console.log('[API Debug] 将对象放入数组');
                response.data = [response.data];
              }
            }
          }
        }
        
        // 为病例添加缺失的必要字段
        if (Array.isArray(response.data) && 
           (url.includes('my-images') || url.includes('team-images'))) {
          response.data = response.data.map(image => {
            // 确保每个病例都有ID
            if (!image.id && image.imageId) {
              image.id = image.imageId;
            } else if (!image.id && image.metadata_id) {
              image.id = image.metadata_id;
            }
            
            // 确保每个病例都有状态
            if (!image.status && image.imageStatus) {
              image.status = image.imageStatus;
            }
            
            // 确保每个病例都有标题
            if (!image.title && image.name) {
              image.title = image.name;
            }
            
            return image;
          });
          
          console.log(`[API Debug] 处理后的病例列表数量: ${response.data.length}`);
        }
      }
    } catch (e) {
      console.error('[API Debug] 日志错误:', e);
    }
    return response;
  },
  error => {
    if (error.response) {
      console.error(`[API] Error: ${error.response.status}`, error.response.data);
      
      // 如果是未授权错误，尝试自动重定向到登录页面
      if (error.response.status === 401) {
        console.warn('未授权，可能需要重新登录');
        
        // 检查是否是团队申请相关的API请求
        const isTeamApplicationRequest = error.config.url && (
          error.config.url.includes('/team-applications') ||
          error.config.url.includes('/teams/') && error.config.url.includes('/applications')
        );
        
        // 检查是否是标注相关的API请求
        const isAnnotationRelatedRequest = error.config.url && (
          error.config.url.includes('/annotations') || 
          error.config.url.includes('/tags') || 
          error.config.url.includes('/image-after-annotation') ||
          error.config.url.includes('/images/') ||
          error.config.url.includes('/structured-form')
        );
        
        // 检查是否在标注流程中
        const isInAnnotationFlow = isAnnotationRelatedRequest || 
                                  window.location.pathname.includes('/annotations') ||
                                  window.location.pathname.includes('/cases/structured-form') ||
                                  window.location.pathname.includes('/cases/form') ||
                                  sessionStorage.getItem('isNavigatingAfterSave');
        
        // 检查是否有特殊标记表示正在执行保存后跳转
        const isNavigatingAfterSave = sessionStorage.getItem('isNavigatingAfterSave');
        const skipAuthCheck = localStorage.getItem('skipAuthCheck');
        
        // 检查是否在团队管理相关页面
        const isTeamManagementPage = 
          window.location.pathname.includes('/teams') || 
          window.location.pathname.includes('/team-management');
          
        // 检查是否是病例列表相关请求
        const isCasesRelatedRequest = error.config.url && (
          error.config.url.includes('/images/my-images') || 
          error.config.url.includes('/images/team-images')
        );
        
        // 检查是否在病例相关页面
        const isCasePage = window.location.pathname.includes('/cases');
        
        // 如果是团队申请相关操作或病例列表操作
        if (isTeamApplicationRequest || isTeamManagementPage || isCasesRelatedRequest || isCasePage) {
          console.log('重要操作检测到认证问题，显示用户提示而非重定向');
          
          // 尝试自动修复
          const autoFixAttempted = attemptAutoFix();
          
          // 如果是病例列表 API，返回一个空数组以避免UI崩溃
          if (isCasesRelatedRequest) {
            console.log('病例列表API认证失败，返回空数组');
            const emptyResponse = {
              status: 200,
              statusText: 'OK (Mock)',
              data: []
            };
            return Promise.resolve(emptyResponse);
          }
          
          if (autoFixAttempted) {
            // 重试请求，用标准格式重发
            return retryRequestWithStandardAuth(error.config);
          }
          
          // 返回特殊错误，供UI层友好提示
          return Promise.reject({
            teamAuthError: true, 
            needsRelogin: true,
            message: '会话已过期，请重新登录后再操作'
          });
        }
        
        // 如果是标注流程或其他特殊情况，不执行强制重定向
        if (isNavigatingAfterSave || skipAuthCheck || isInAnnotationFlow) {
          console.log('检测到标注流程相关请求或特殊设置，不执行自动重定向');
          
          // 显示友好提示
          if (window.$message) {
            window.$message.warning('您的登录已过期，但可以继续当前操作');
          }
          
          // 尝试自动恢复会话
          const preservedUser = sessionStorage.getItem('preservedUser');
          if (preservedUser) {
            console.log('尝试使用保存的用户信息恢复会话');
            localStorage.setItem('user', preservedUser);
            
            // 重新发送请求
            const config = error.config;
            // 避免循环重试
            config._retry = true;
            
            // 添加新的认证信息
            const user = JSON.parse(preservedUser);
            if (user) {
              config.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;
              if (user.email) {
                const emailBase64 = btoa(user.email);
                config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
              }
            }
            
            return apiClient(config);
          }
          
          // 返回特殊错误，但不中断流程
          return Promise.reject(new Error('会话已过期，请重新登录后再操作'));
        }
        
        // 对于其他请求，执行标准的重定向逻辑
        // 清除本地存储的用户信息
        localStorage.removeItem('user');
        
        // 如果不在登录页面，重定向到登录页面
        if (window.location.pathname !== '/login') {
          // 保存当前URL以便登录后返回
          const currentPath = window.location.pathname + window.location.search;
          sessionStorage.setItem('redirectAfterLogin', currentPath);
          
          window.location.href = '/login';
        }
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应 - 网络错误或后端服务未启动
      console.error('[API] 网络错误:', error.message);
      
      // 特殊处理病例列表和团队列表，避免UI崩溃
      const url = error.config?.url || '';
      if (url.includes('/images/my-images') || url.includes('/images/team-images') ||
          url.includes('/teams') && !url.includes('/applications')) {
        console.log('关键列表API网络错误，返回空数组以避免UI崩溃');
        const emptyResponse = {
          status: 200,
          statusText: 'OK (Mock due to network error)',
          data: []
        };
        return Promise.resolve(emptyResponse);
      }
    } else {
      console.error('[API] 请求配置错误:', error.message);
    }
    return Promise.reject(error);
  }
);

// 添加自动修复会话的函数
function attemptAutoFix() {
  try {
    // 检查是否有保存的用户信息
    const userStr = localStorage.getItem('user');
    if (!userStr) return false;
    
    const user = JSON.parse(userStr);
    
    // 尝试从Session Storage获取更多认证信息
    const sessionAuth = sessionStorage.getItem('authData');
    if (sessionAuth) {
      const authData = JSON.parse(sessionAuth);
      // 将认证信息合并到用户对象
      if (authData.token) {
        user.token = authData.token;
        // 保存更新后的用户信息
        localStorage.setItem('user', JSON.stringify(user));
        console.log('已从会话存储恢复认证令牌');
        return true;
      }
    }
    
    // 尝试使用Email和ID重新确认身份
    if (user.email && (user.id || user.customId)) {
      // 至少确认有这些基本信息
      return true;
    }
    
    return false;
  } catch (e) {
    console.error('自动修复尝试失败:', e);
    return false;
  }
}

// 添加会话恢复功能 - 在页面刷新时调用
function recoverSessionAfterRefresh() {
  try {
    console.log('检查页面是否需要恢复会话...');
    
    // 检查是否有保存的用户信息
    const userStr = localStorage.getItem('user');
    if (!userStr) {
      console.log('本地存储中没有用户信息，无需恢复会话');
      return false;
    }
    
    const user = JSON.parse(userStr);
    if (!user || (!user.id && !user.customId)) {
      console.log('用户信息不完整，无法恢复会话');
      return false;
    }
    
    // 在页面加载完成后发送认证恢复请求
    const userId = user.customId || user.id;
    const params = {
      _t: new Date().getTime(),
      _refresh: true
    };
    
    if (user.role) {
      params._role = user.role;
    }
    
    // 构建认证头
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
    
    if (userId) {
      headers['Authorization'] = `Bearer user_${userId}`;
      if (user.email) {
        try {
          const emailBase64 = btoa(encodeURIComponent(user.email));
          headers['Authorization'] = `${headers['Authorization']}:${emailBase64}`;
        } catch (e) {
          console.warn('编码邮箱失败:', e);
        }
      }
    }
    
    // 发送简单的会话检查请求
    console.log('发送会话恢复请求...');
    axios.get('/medical/api/users/session-check', {
      params,
      headers,
      withCredentials: true
    })
      .then(response => {
        console.log('会话恢复成功:', response.data);
        // 可以在这里更新用户信息
        if (response.data && response.data.user) {
          localStorage.setItem('user', JSON.stringify(response.data.user));
          console.log('用户信息已更新');
        }
        return true;
      })
      .catch(error => {
        console.warn('会话恢复失败:', error);
        return false;
      });
      
    return true;
  } catch (e) {
    console.error('会话恢复过程出错:', e);
    return false;
  }
}

// 页面加载时尝试恢复会话
if (typeof window !== 'undefined') {
  window.addEventListener('DOMContentLoaded', () => {
    recoverSessionAfterRefresh();
  });
}

// 添加一个工具函数，用于规范化API路径，避免重复前缀
function normalizePath(url) {
  if (!url) return url;
  
  // 保存原始URL，用于比较和记录
  const originalUrl = url;

  // 先去除开头的重复斜杠
  let normalizedUrl = url;
  while (normalizedUrl.startsWith('//')) {
    normalizedUrl = normalizedUrl.substring(1);
  }
  
  // 检查各种重复模式
  const patterns = [
    { find: 'medical/api/medical/api/', replace: 'medical/api/' },
    { find: 'api/medical/api/', replace: 'medical/api/' },
    { find: 'medical/api/api/', replace: 'medical/api/' },
    { find: 'medical/medical/', replace: 'medical/' },
    { find: 'api/api/', replace: 'api/' }
  ];
  
  // 应用所有模式
  let foundPatternMatch = false;
  for (const pattern of patterns) {
    if (normalizedUrl.includes(pattern.find)) {
      normalizedUrl = normalizedUrl.replace(new RegExp(pattern.find, 'g'), pattern.replace);
      console.log(`[normalizePath] 路径修正: 将 ${pattern.find} 替换为 ${pattern.replace}`);
      foundPatternMatch = true;
    }
  }
  
  // 确保以斜杠开头
  if (!normalizedUrl.startsWith('/')) {
    normalizedUrl = '/' + normalizedUrl;
  }
  
  // 特殊处理：确保/api/teams等关键路径保持正确格式
  // 如果URL路径已经包含/api前缀，不要使用apiClient.baseURL再添加一次/api
  if (normalizedUrl.startsWith('/api/') && apiClient && apiClient.defaults.baseURL === '/api') {
    // 这种情况下，我们需要修改URL，移除apiClient的baseURL以避免重复
    normalizedUrl = normalizedUrl.replace(/^\/api\//, '/');
    console.log(`[normalizePath] 检测到路径已有/api前缀，移除重复: ${normalizedUrl}`);
  }
  
  // 记录修正结果
  if (originalUrl !== normalizedUrl) {
    console.log(`[normalizePath] URL路径已规范化: ${originalUrl} -> ${normalizedUrl}`);
  }
  
  return normalizedUrl;
}

// 使用标准认证格式重新发送请求
function retryRequestWithStandardAuth(config) {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return Promise.reject(new Error('无可用认证信息'));
    
    const user = JSON.parse(userStr);
    const newConfig = {...config};
    
    // 重置头部，使用标准Authorization头
    newConfig.headers = {...config.headers};
    newConfig.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;
    if (user.email) {
      const emailBase64 = btoa(user.email);
      newConfig.headers['Authorization'] = `${newConfig.headers['Authorization']}:${emailBase64}`;
    }
    
    // 移除所有可能导致CORS问题的自定义头
    delete newConfig.headers['X-User-Id'];
    delete newConfig.headers['X-User-Email']; 
    delete newConfig.headers['X-Authentication-Email'];
    delete newConfig.headers['X-User-Role'];
    delete newConfig.headers['X-User-Team-Id'];
    
    // 修复URL路径，避免重复前缀
    newConfig.url = normalizePath(newConfig.url);
    
    // 避免循环重试
    newConfig._retry = true;
    
    console.log('使用标准认证头重试请求，URL:', newConfig.url);
    return apiClient(newConfig);
  } catch (e) {
    console.error('重试请求失败:', e);
    return Promise.reject(e);
  }
}

// 添加一个工具函数来处理userId可能是customId的情况
const getUserIdParam = (userId) => {
  // 如果是对象，尝试提取customId或id属性
  if (userId && typeof userId === 'object') {
    return userId.customId || userId.id || null;
  }

  // 如果是字符串或数字，直接返回
  return userId;
};

// API功能
const api = {
  // 认证相关
  auth: {
    // 登录
    login(credentials) {
      return apiClient.post('/users/authenticate', credentials);
    },
    // 注册
    register(userData) {
      return apiClient.post('/users', userData);
    }
  },
  
  // 统计数据相关
  stats: {
    // 获取仪表盘统计数据
    getDashboard(userId) {
      // 验证用户ID是否有效
      if (!userId) {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        userId = user.customId || user.id;
        console.log('从localStorage中获取用户ID:', userId);
        
        // 如果仍然无法获取，返回拒绝的Promise
      if (!userId) {
        console.error('获取仪表盘统计数据时用户ID为空');
        return Promise.reject(new Error('用户ID不能为空'));
      }
      }

      // 添加时间戳，避免缓存问题
      const timestamp = Date.now();
      
      // 只使用无认证限制的API端点，避免403错误
      // 将时间戳作为URL参数，避免自定义头
      let url = `/stats-v2/dashboard-unrestricted/${userId}?t=${timestamp}`;
      
      // 添加用户信息到URL参数中，而非自定义头部
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user.role) {
        // 将角色信息添加到URL参数，避免使用自定义头部
        url += `&_role=${user.role}`;
      }
      
      console.log(`获取用户[${userId}]的仪表盘统计数据, 完整URL: ${apiClient.defaults.baseURL}${url}`);
      console.log('发送仪表盘数据请求:', url);
      
      // 设置请求头，只保留必要的标准头部，避免CORS预检问题
      const config = {
        headers: {
          'X-User-Id': userId
          // 移除所有可能导致CORS预检问题的非标准头部
          // 'X-Request-Time': timestamp  这个头会导致CORS错误
        },
        timeout: 15000,  // 增加超时时间到15秒
        withCredentials: true  // 确保发送认证信息
      };
      
      // 检查是否有token，如果有则添加到请求头
      const token = localStorage.getItem('token');
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }
      
      // 使用apiClient发送请求，错误处理不再回退到旧API
      return apiClient.get(url, config)
        .catch(error => {
          console.error(`获取用户[${userId}]的仪表盘统计数据失败:`, error.message || error);
          
          if (error.response) {
            console.error('服务器响应状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
          } else if (error.request) {
            console.error('请求已发送但未收到响应，可能是网络问题');
          }
          
          // 直接返回空数据，避免UI崩溃
          return {
            data: {
              totalCount: 0,
              draftCount: 0,
              reviewedCount: 0,
              submittedCount: 0,
              approvedCount: 0,
              rejectedCount: 0,
              dataSource: 'error_fallback',
              userId: userId,  // 记录实际使用的用户ID
              error: error.message || '获取统计数据失败'
            }
          };
        });
    }
  },
  
  // 用户相关
  users: {
    // 获取当前用户信息
    getCurrentUser() {
      return apiClient.get('/users/me');
    },
    // 更新当前用户信息
    updateCurrentUser(userData) {
      return apiClient.put('/users/me', userData);
    },
    // 修改密码
    changePassword(oldPassword, newPassword) {
      return apiClient.post('/users/me/change-password', { oldPassword, newPassword });
    },
    // 获取当前用户的审核医生申请状态
    getReviewerApplicationStatus() {
      return apiClient.get('/reviewer-applications/me');
    },
    // 申请成为审核医生
    applyForReviewer(reason) {
      return apiClient.post('/reviewer-applications', { reason });
    },
    // 获取所有用户
    getAll() {
      return apiClient.get('/users');
    },
    // 获取单个用户
    getUser(id) {
      return apiClient.get(`/users/${id}`);
    },
    // 创建用户
    createUser(userData) {
      return apiClient.post('/users', userData);
    },
    // 更新用户
    updateUser(id, userData) {
      return apiClient.put(`/users/${id}`, userData);
    },
    // 删除用户
    deleteUser(id) {
      return apiClient.delete(`/users/${id}`);
    },
    // 重置密码
    resetPassword(userId, newPassword) {
      return apiClient.post(`/users/${userId}/reset-password`, { newPassword });
    },
    // 获取所有审核医生
    getAllReviewers() {
      return apiClient.get('/users/reviewers');
    },
    // 获取所有待处理的审核医生申请
    getPendingReviewerApplications() {
      return apiClient.get('/reviewer-applications/pending');
    },
    // 处理审核医生申请
    processReviewerApplication(applicationId, data) {
      return apiClient.put(`/reviewer-applications/${applicationId}`, data);
    },
    // 获取无团队的用户
    getUsersWithoutTeam() {
      return apiClient.get('/users/without-team');
    }
  },
  
  // 团队相关
  teams: {
    // 创建团队
    create(teamData) {
      return apiClient.post('/api/teams', teamData);
    },
    // 获取所有团队
    getAll() {
      return apiClient.get('/api/teams');
    },
    // 获取单个团队信息
    getOne(id) {
      return apiClient.get(`/api/teams/${id}`);
    },
    // 加入团队/部门
    joinTeam(teamId, userId) {
      return apiClient.post(`/api/teams/${teamId}/members`, { userId });
    },
    // 申请加入团队
    applyToJoinTeam(teamId, reason) {
      // 从localStorage获取用户信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = user.customId || user.id;
      
      console.log('申请加入团队，使用用户ID:', userId, '类型:', typeof userId);
      
      // 检查是否获取到了有效的userId
      if (!userId) {
        console.error('无法获取有效的用户ID，可能用户未登录或会话已过期');
        // 返回一个被拒绝的Promise，这样调用者可以捕获错误
        return Promise.reject(new Error('无法获取用户ID，请重新登录'));
      }
      
      // 构建请求数据，只包含必要的参数，不包含userId
      const requestData = {
        teamId: parseInt(teamId, 10) || teamId, // 尝试将字符串转为数字
        reason: reason,
        status: 'PENDING' // 明确设置状态为"申请中"
      };
      
      console.log('团队申请请求数据:', JSON.stringify(requestData));
      
      // 使用apiClient发送请求，让请求拦截器自动添加认证信息
      return apiClient.post('/api/team-applications', requestData);
    },
    // 获取团队的所有申请记录
    getTeamApplications(teamId, status) {
      let url = `/api/team-applications/team/${teamId}`;
      if (status) {
        url += `?status=${status}`;
      }
      return apiClient.get(url);
    },
    // 处理团队申请（批准或拒绝）
    processTeamApplication(applicationId, action, reason) {
      return apiClient.put(`/api/team-applications/${applicationId}`, {
        action: action, // 'APPROVE' 或 'REJECT'
        reason: reason || ''
      });
    },
    // 获取用户的团队申请（增强版，支持customId）
    getUserApplications(userId) {
      const idParam = getUserIdParam(userId);
      return apiClient.get(`/api/team-applications/user/${idParam}`);
    },
    // 获取团队成员
    getTeamMembers(teamId) {
      return apiClient.get(`/api/teams/${teamId}/members`);
    },
    // 获取团队已通过标注
    getTeamAnnotations(teamId) {
      return apiClient.get(`/api/teams/${teamId}/annotations/approved`);
    },
    // 获取单个标注详情
    getAnnotationDetail(annotationId) {
      return apiClient.get(`/api/annotations/${annotationId}`);
    },
    // 移除团队成员
    removeTeamMember(teamId, userId) {
      return apiClient.delete(`/api/teams/${teamId}/members/${userId}`);
    },
    // 添加检查用户是否已经是团队成员的方法
    checkUserInTeam(userId, teamId) {
      return apiClient.get(`/api/teams/${teamId}/members/check/${userId}`)
        .catch(error => {
          // 处理404等错误，返回一个默认响应
          if (error.response) {
            return { data: { isMember: false } };
          }
          throw error;
        });
    }
  },
  
  // 审核医生申请相关
  reviewer: {
    // 获取已处理的申请
    getProcessedApplications() {
      return apiClient.get('/api/reviewer-applications/processed');
    }
  },
  
  // 图像相关
  images: {
    // 获取图像列表
    getAll() {
      return apiClient.get('/images');
    },
    // 获取单张图像
    getOne(id) {
      return apiClient.get(`/images/${id}`);
    },
    // 上传图像
    upload(file) {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      
      // 获取用户信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user.id) {
        formData.append('user_id', user.id);
      }
      
      // 添加文件名和描述
      if (file.name) {
        formData.append('name', file.name);
      }
      
      // 确保timeoutDuration和retryCount有有效值
      const timeoutDuration = 15000;
      const retryCount = 3;
      
      console.log('上传文件中...', {
        文件名: file.name,
        文件大小: file.size,
        文件类型: file.type,
        用户ID: user.id
      });
      
      // 使用fileApiClient发送请求，增加超时时间
      const config = {
        timeout: timeoutDuration,
      };
      
      return fileApiClient.post('/images/upload', formData, config);
    },
    // 保存图像到指定路径
    saveToPath(formData) {
      return fileApiClient.post('/images/save-to-path', formData);
    },
    
    // 保存标注后的图像
    saveAnnotatedImage(imageId, processedImageFile) {
      const formData = new FormData();
      formData.append('file', processedImageFile);
      
      return fileApiClient.post(`/images/save-annotated-image/${imageId}`, formData);
    },
    
    // 删除图像
    delete(id) {
      return apiClient.delete(`/api/images/${id}`);
    },
    
    // 更新图像元数据
    update(id, metadata) {
      return apiClient.put(`/api/images/${id}`, metadata);
    },
    
    // 提交图像到审核流程
    submitForReview(id) {
      return apiClient.post(`/api/images/${id}/submit`);
    },
    
    // 审核图像
    reviewImage(id, approved, reviewNotes) {
      return apiClient.post(`/api/images/${id}/review`, { approved, reviewNotes });
    },
    
    // 保存标注图像
    saveAnnotatedImage(id, file) {
      const formData = new FormData();
      formData.append('file', file);
      
      return fileApiClient.post(`/api/images/${id}/annotate`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    
    // 删除标注图像
    deleteAnnotatedImage(path) {
      return apiClient.delete('/api/images/annotated', { params: { path } });
    },
    
    // 获取当前用户上传的所有图像
    getUserImages(status = null, customUserId = null) {
      // 优先使用传入的用户ID，否则从localStorage获取
      let userId;
      if (customUserId) {
        userId = customUserId;
        console.log('getUserImages: 使用传入的用户ID:', userId);
      } else {
        // 从localStorage获取用户信息
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        userId = user.customId || user.id;
        console.log('getUserImages: 从localStorage获取用户ID:', userId);
      }
      
      // 构建查询参数，明确指定用户ID
      let params = { userId };
      if (status) {
        params.status = status;
      }
      
      // 明确传递用户ID作为查询参数
      console.log('获取用户图像，用户ID:', userId, '状态:', status || '全部');
      
      // 使用完整的直接路径，完全避免前缀问题
      // 不依赖interceptor添加前缀，而是直接指定完整路径
      const directUrl = '/medical/api/images/my-images';
      
      // 添加调试信息，确认请求的完整URL
      console.log('getUserImages使用完整路径:', directUrl, '参数:', params);
      
      try {
        // 使用axios直接请求，避免拦截器可能添加的前缀
        return axios.get(directUrl, { params });
      } catch (error) {
        console.error('获取用户图像失败:', error);
        return Promise.reject(error);
      }
    },
    
    // 获取团队内的所有图像
    getTeamImages(customUserId = null) {
      // 优先使用传入的用户ID，否则从localStorage获取
      let userId;
      if (customUserId) {
        userId = customUserId;
        console.log('getTeamImages: 使用传入的用户ID:', userId);
      } else {
        // 从localStorage获取用户信息
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        userId = user.customId || user.id;
        console.log('getTeamImages: 从localStorage获取用户ID:', userId);
      }
      
      // 构建查询参数，明确指定用户ID
      let params = { userId };
      
      console.log('获取团队图像，用户ID:', userId);
      
      // 使用完整的直接路径，完全避免前缀问题
      const directUrl = '/medical/api/images/team-images';
      
      // 添加调试信息
      console.log('getTeamImages使用完整路径:', directUrl, '参数:', params);
      
      try {
        // 使用axios直接请求，避免拦截器可能添加的前缀
        return axios.get(directUrl, { params });
      } catch (error) {
        console.error('获取团队图像失败:', error);
        return Promise.reject(error);
      }
    },
    
    // 获取待审核的图像
    getPendingReviewImages() {
      return apiClient.get('/api/images/pending-review');
    },
    
    // 获取已审核的图像
    getReviewedImages() {
      return apiClient.get('/api/images/reviewed');
    },
    
    // 保存结构化表单数据（改为简单请求：POST + application/x-www-form-urlencoded，不带自定义头）
    saveStructuredFormData(imageId, formData) {
      // 将对象转为 x-www-form-urlencoded 字符串
      const params = new URLSearchParams();
      Object.keys(formData).forEach(key => {
        let value = formData[key];
        // 数组转为逗号分隔字符串
        if (Array.isArray(value)) {
          value = value.join(',');
        }
        params.append(key, value == null ? '' : value);
      });
      return apiClient.post(`/api/images/${imageId}/structured-form`, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
    },
    
    // 更新结构化表单数据 - 实际上与保存是同一个API
    updateStructuredFormData(id, formData) {
      console.log('API调用: 更新结构化表单数据，ID:', id, '数据:', formData);
      return apiClient.put(`/api/images/${id}/structured-form`, formData);
    },
    
    // 将图像标记为已标注状态
    markAsAnnotated(id, timestamp) {
      console.log('API调用: 将图像标记为已标注，ID:', id, timestamp ? '时间戳:' + timestamp : '');
      
      // 构建查询参数
      const params = {};
      if (timestamp) {
        params.reviewTimestamp = timestamp;
      }
      
      // 添加防重复提交标志
      const requestKey = `mark_annotated_${id}_${new Date().getTime()}`;
      if (window.pendingRequests && window.pendingRequests[requestKey]) {
        console.warn('已有相同请求正在处理中，避免重复提交');
        return window.pendingRequests[requestKey];
      }
      
      // 创建请求并保存引用
      const request = apiClient.put(`/api/images/${id}/mark-annotated`, null, { params })
        .then(response => {
          // 请求成功，移除引用
          if (window.pendingRequests) {
            delete window.pendingRequests[requestKey];
          }
          return response;
        })
        .catch(error => {
          // 请求失败，移除引用
          if (window.pendingRequests) {
            delete window.pendingRequests[requestKey];
          }
          throw error;
        });
      
      // 初始化pendingRequests对象（如果不存在）
      if (!window.pendingRequests) {
        window.pendingRequests = {};
      }
      
      // 存储请求引用
      window.pendingRequests[requestKey] = request;
      
      return request;
    },
    
    // 更新图像状态
    updateStatus(id, status) {
      console.log('API调用: 更新图像状态，ID:', id, '状态:', status);
      return apiClient.put(`/api/images/${id}/status`, null, { params: { status } });
    },
    
    // 新增：获取经过筛选的图像列表（专用于病例列表页面）
    getFilteredImages(status = null) {
      // 从localStorage获取用户信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = user.customId || user.id;
      
      if (!userId) {
        console.error('获取筛选图像列表失败：未找到用户ID');
        return Promise.reject(new Error('用户未登录或ID不可用'));
      }
      
      // 检查是否有特殊标记表示这是从Dashboard直接导航过来的
      const isDirectNavigation = localStorage.getItem('directNavigation') === 'true';
      if (isDirectNavigation) {
        // 清除标记
        localStorage.removeItem('directNavigation');
        
        // 检查是否有保存的状态
        const savedStatus = localStorage.getItem('lastSelectedStatus');
        if (savedStatus && !status) {
          status = savedStatus;
          console.log('使用保存的状态值:', status);
        }
      }
      
      // 构建查询参数
      let params = { userId };
      if (status) {
        params.status = status;
      }
      
      console.log('获取筛选图像列表，用户ID:', userId, '状态:', status || '全部');
      
      // 使用固定的直接路径
      const directUrl = '/medical/api/images/my-images';
      
      // 添加日志
      console.log('getFilteredImages使用路径:', directUrl, '参数:', params);
      
      try {
        // 使用axios直接请求，避免拦截器问题
        return axios.get(directUrl, { params })
          .then(response => {
            console.log('获取筛选图像列表成功，数量:', response.data ? response.data.length : 0);
            return response;
          });
      } catch (error) {
        console.error('获取筛选图像列表失败:', error);
        return Promise.reject(error);
      }
    }
  },
  
  // 标签相关
  tags: {
    // 按图像ID获取标签
    getByImageId(imageId) {
      console.log(`获取图像标注，图像ID: ${imageId}`);
      
      // 获取用户信息用于认证
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      
      // 添加查询参数
      const params = { _t: new Date().getTime() };
      
      // 添加角色信息（如果有）
      if (user.role) {
        params._role = user.role;
      }
      
      // 如果有用户ID，添加到参数
      if (user.customId || user.id) {
        params.userId = user.customId || user.id;
      }
      
      // 使用正确的路径，匹配后端Controller
      const directUrl = `/api/tags/image/${imageId}`;
      
      const config = {
        params,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      };
      
      // 添加Authorization头
      if (user.id || user.customId) {
        const userId = user.customId || user.id;
        config.headers['Authorization'] = `Bearer user_${userId}`;
        if (user.email) {
          try {
            const emailBase64 = btoa(encodeURIComponent(user.email));
            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
          } catch (e) {
            console.warn('编码邮箱失败:', e);
          }
        }
      }
      
      // 实现带重试的请求函数
      const makeRequest = (retryCount = 0, maxRetries = 3) => {
        return axios.get(directUrl, config)
          .then(response => {
            console.log(`成功获取图像标注，数量: ${response.data ? response.data.length : 0}`);
            return response;
          })
          .catch(error => {
            console.error(`获取图像标注失败，尝试次数: ${retryCount + 1}`, error);
            
            // 如果是认证错误且还有重试次数，则重试
            if (error.response && 
                (error.response.status === 403 || error.response.status === 401) && 
                retryCount < maxRetries) {
              console.log(`认证错误，${maxRetries - retryCount}秒后重试...`);
              
              // 等待一秒后重试
              return new Promise(resolve => {
                setTimeout(() => {
                  // 更新参数，添加刷新标记
                  config.params._refresh = true;
                  config.params._retry = retryCount + 1;
                  
                  // 递归调用自己，增加重试计数
                  resolve(makeRequest(retryCount + 1, maxRetries));
                }, 1000);
              });
            }
            
            // 如果已达到最大重试次数或不是认证错误，则拒绝Promise
            return Promise.reject(error);
          });
      };
      
      // 执行请求，带重试功能
      return makeRequest();
    },
    // 按标签ID获取标签
    getById(id) {
      return apiClient.get(`/api/tags/${id}`);
    },
    // 创建标签
    create(tagData) {
      // 获取用户信息用于认证
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      
      // 使用正确的路径，匹配后端Controller
      const directUrl = '/api/tags';
      console.log('创建标签使用路径:', directUrl, '数据:', tagData);
      
      // 添加查询参数
      const params = { _t: new Date().getTime() };
      
      // 添加角色信息（如果有）
      if (user.role) {
        params._role = user.role;
      }

      // 如果有用户ID，添加到参数
      if (user.customId || user.id) {
        params.userId = user.customId || user.id;
      }
      
      // 请求配置
      const config = {
        params,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      };

      // 添加Authorization头
      if (user.id || user.customId) {
        const userId = user.customId || user.id;
        config.headers['Authorization'] = `Bearer user_${userId}`;
        if (user.email) {
          try {
            const emailBase64 = btoa(encodeURIComponent(user.email));
            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
          } catch (e) {
            console.warn('编码邮箱失败:', e);
          }
        }
      }
      
      // 使用axios直接请求，避免路径拦截器问题
      return axios.post(directUrl, tagData, config);
    },
    // 更新标签
    update(id, tagData) {
      console.log(`更新标注数据, ID: ${id}`, tagData);
      // 添加时间戳，防止缓存
      const requestURL = `/api/tags/${id}?t=${new Date().getTime()}`;
      console.log(`发送PUT请求到: ${requestURL}`);
      
      // 尝试直接使用fetch API发送请求
      return new Promise((resolve, reject) => {
        // 先使用apiClient尝试
        apiClient.put(requestURL, tagData)
          .then(response => {
            console.log(`使用apiClient更新标注成功, ID: ${id}`, response.data);
            resolve(response);
          })
          .catch(error => {
            console.error(`使用apiClient更新标注失败, ID: ${id}`, error);
            console.log('尝试使用fetch API作为备选方案');
            
            // 作为备选，使用fetch API
            fetch(requestURL, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
              },
              body: JSON.stringify(tagData),
              credentials: 'include'
            })
              .then(response => {
                if (!response.ok) {
                  throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
              })
              .then(data => {
                console.log(`使用fetch更新标注成功, ID: ${id}`, data);
                resolve({ data });
              })
              .catch(fetchError => {
                console.error(`使用fetch更新标注也失败, ID: ${id}`, fetchError);
                reject(error); // 返回原始错误
              });
          });
      });
    },
    // 删除标签
    delete(id) {
      return apiClient.delete(`/api/tags/${id}`);
    },
    // 删除一个图像的所有标签
    deleteByImageId(imageId) {
      return apiClient.delete(`/api/tags/image/${imageId}`);
    },
    // 按用户ID获取标签
    getByUserId(userId) {
      return apiClient.get(`/api/tags/user/${userId}`);
    },
    
    // 添加: 保存标注后的图像 - 使用已存在的后端API
    saveAnnotatedImage(imageId) {
      // 获取用户信息用于认证
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      
      // 构建请求数据，确保格式正确
      const requestData = {
        metadata_id: imageId
      };
      
      // 添加查询参数
      const params = { _t: new Date().getTime() };
      
      // 添加角色信息（如果有）
      if (user.role) {
        params._role = user.role;
      }
      
      // 如果有用户ID，添加到参数
      if (user.customId || user.id) {
        params.userId = user.customId || user.id;
      }
      
      console.log('保存标注后图像，使用imageId:', imageId);
      
      // 使用正确的路径，匹配后端Controller
      const directUrl = '/api/tags/save-image-after-annotation';
      
      // 请求配置
      const config = {
        params,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      };
      
      // 添加Authorization头
      if (user.id || user.customId) {
        const userId = user.customId || user.id;
        config.headers['Authorization'] = `Bearer user_${userId}`;
        if (user.email) {
          try {
            const emailBase64 = btoa(encodeURIComponent(user.email));
            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
          } catch (e) {
            console.warn('编码邮箱失败:', e);
          }
        }
      }
      
      // 添加重试机制
      return new Promise((resolve, reject) => {
        axios.post(directUrl, requestData, config)
          .then(response => {
            console.log('标注图像保存成功:', response.data);
            resolve(response);
          })
          .catch(error => {
            console.error('保存标注图像失败:', error);
            
            // 如果是认证错误，尝试一次自动恢复会话
            if (error.response && (error.response.status === 401 || error.response.status === 403)) {
              console.log('检测到认证错误，尝试恢复会话...');
              
              // 通过刷新用户信息恢复会话
              const refreshConfig = { ...config };
              // 添加额外的会话恢复参数
              refreshConfig.params._refresh = true;
              
              // 重试请求
              setTimeout(() => {
                axios.post(directUrl, requestData, refreshConfig)
                  .then(retryResponse => {
                    console.log('会话恢复成功，标注图像已保存:', retryResponse.data);
                    resolve(retryResponse);
                  })
                  .catch(retryError => {
                    console.error('尝试恢复会话后仍然失败:', retryError);
                    reject(retryError);
                  });
              }, 500);
            } else {
              reject(error);
            }
          });
      });
    }
  },
  
  // 标注图像操作
  annotations: {
    // 获取标注详情
    getAnnotationDetail(id) {
      return apiClient.get(`/api/annotations/${id}`);
    },
    // 保存标注图像
    saveAnnotatedImage(imageId) {
      return apiClient.post(`/api/annotations/${imageId}`);
    },
    // 更新已有标注图像
    updateAnnotatedImage(imageId, imagePairId) {
      return apiClient.put(`/api/annotations/${imageId}/${imagePairId}`);
    },
    // 将标注图像保存到本地
    // 注意：这不是一个API调用，而是在前端使用canvas数据保存图像
    saveAnnotatedImageToLocal(imageId, imageData) {
      // 从Base64字符串创建一个blob
      const byteCharacters = atob(imageData.split(',')[1]);
      const byteArrays = [];
      for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
      }
      const blob = new Blob([new Uint8Array(byteArrays)], { type: 'image/png' });
      
      // 创建下载链接并模拟点击
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `annotated_${imageId}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 根据已保存的标注信息自动生成标注图片
    generateAnnotatedImage(imageId) {
      console.log(`调用自动生成标注图片API，图像ID: ${imageId}`);
      return apiClient.post(`/api/annotations/generate/${imageId}`);
    }
  },
  
  // 图像对（图像与标注）
  imagePairs: {
    // 获取所有图像对
    getAll() {
      return apiClient.get('/api/image-pairs');
    },
    // 按元数据ID获取图像对
    getByMetadataId: (metadataId) => {
      // 在请求前添加认证调试信息
      console.log('获取图像对 - 认证调试:');
      let user;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
          console.log('用户信息:', {
            id: user.id,
            customId: user.customId,
            email: user.email,
            role: user.role
          });
        } else {
          console.warn('本地存储中没有用户信息');
        }
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }

      // 处理长整型ID（如时间戳ID）
      if (metadataId && metadataId.toString().length > 9) {
        const fullId = metadataId.toString();
        console.log(`使用完整ID参数请求: ${fullId}`);
        // 使用查询参数API而非路径参数
        return apiClient.get(`/api/image-pairs/by-metadata?id=${fullId}`);
      }
      
      // 直接使用正确的路径，绕过normalizePath
      // 使用完整的直接路径，避免任何路径拼接问题
      const directPath = `/medical/api/image-pairs/metadata/${metadataId}`;
      console.log('使用直接路径:', directPath);
      
      // 添加自定义请求头和查询参数，确保认证信息被传递
      const config = {};
      if (user) {
        // 添加查询参数
        config.params = {
          _role: user.role || 'USER',
          userId: user.customId || user.id,
          _t: new Date().getTime()  // 时间戳避免缓存
        };
        
        console.log('添加查询参数:', config.params);
      }
      
      // 使用axios直接请求，而不是apiClient，完全避免拦截器
      return axios.get(directPath, config);
    },
    // 获取单个图像对
    getOne(id) {
      return apiClient.get(`/api/image-pairs/${id}`);
    },
    // 创建图像对
    create(imagePairData) {
      return apiClient.post('/api/image-pairs', imagePairData);
    },
    // 更新图像对
    update(id, imagePairData) {
      return apiClient.put(`/api/image-pairs/${id}`, imagePairData);
    },
    // 删除图像对
    delete(id) {
      return apiClient.delete(`/api/image-pairs/${id}`);
    },
    // 删除一个元数据相关的所有图像对
    deleteByMetadataId(metadataId) {
      return apiClient.delete(`/api/image-pairs/metadata/${metadataId}`);
    },
    // 删除标注图像 - 使用正确的注解控制器路径
    deleteAnnotatedImage(id) {
      return apiClient.delete(`/api/annotations/${id}`);
    }
  },
  
  // 团队申请相关API（新增）
  teamApplications: {
    // 获取所有待处理的申请（管理员视图）
    getPendingApplications() {
      return apiClient.get('/api/team-applications/pending');
    },
    // 获取已处理的申请（可选状态过滤）
    getProcessedApplications(status) {
      if (status) {
        return apiClient.get(`/api/team-applications/processed?status=${status}`);
      } else {
        return apiClient.get('/api/team-applications/processed');
      }
    },
    // 处理申请
    processApplication(applicationId, data) {
      return apiClient.put(`/api/team-applications/${applicationId}`, data);
    },
    // 获取团队的申请
    getTeamApplications(teamId, status) {
      let url = `/api/team-applications/team/${teamId}`;
      if (status) {
        url += `?status=${status}`;
      }
      return apiClient.get(url);
    },
    // 获取用户的申请
    getUserApplications(userId) {
      return apiClient.get(`/api/team-applications/user/${userId}`);
    },
    // 申请加入团队
    applyToJoinTeam(teamId, reason) {
      return apiClient.post(`/api/team-applications`, { teamId, reason });
    }
  },
  
  // 图像日志相关
  imageLogs: {
    // 记录图像操作日志
    logOperation(logData) {
      console.log('记录图像操作日志:', logData);
      return apiClient.post('/api/image-logs/log', logData);
    },
    
    // 获取时区信息
    getTimezoneInfo() {
      return apiClient.get('/api/image-logs/timezone-info');
    }
  }
};

// 处理长整型ID的函数，将其转换为合适的整数
function processLongId(id) {
  if (!id) return null;
  
  const idStr = id.toString();
  // 如果ID长度超过9（INT范围限制），使用完整ID但添加特殊参数
  if (idStr.length > 9) {
    console.log(`处理长ID: ${idStr} - 使用完整ID和查询参数`);
    // 使用原始ID，但添加长ID标记，传递给新的API端点
    return idStr;
  }
  return idStr;
}

export default api;