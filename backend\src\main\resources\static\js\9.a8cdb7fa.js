"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[9],{84009:(e,t,n)=>{n.r(t),n.d(t,{default:()=>m});var a=n(61431),r={class:"reviewer-applications-container"},i={class:"page-header"},o={class:"header-actions"},s={key:0,class:"loading-container"},c={key:1,class:"empty-state"},l={key:2},u={key:0,class:"loading-container"},d={key:1,class:"empty-state"},p={key:2};function f(e,t,n,f,h,g){var b=(0,a.g2)("el-button"),v=(0,a.g2)("el-skeleton"),m=(0,a.g2)("el-empty"),k=(0,a.g2)("el-table-column"),w=(0,a.g2)("el-table"),A=(0,a.g2)("el-tab-pane"),_=(0,a.g2)("el-tag"),y=(0,a.g2)("el-tabs");return(0,a.uX)(),(0,a.CE)("div",r,[(0,a.Lk)("div",i,[t[2]||(t[2]=(0,a.Lk)("h1",null,"审核医生申请管理",-1)),(0,a.Lk)("div",o,[(0,a.bF)(b,{type:"primary",onClick:g.goBack},{default:(0,a.k6)((function(){return t[1]||(t[1]=[(0,a.Lk)("i",{class:"bi bi-arrow-left"},null,-1),(0,a.eW)(" 返回 ")])})),_:1,__:[1]},8,["onClick"])])]),(0,a.bF)(y,{modelValue:h.activeTab,"onUpdate:modelValue":t[0]||(t[0]=function(e){return h.activeTab=e})},{default:(0,a.k6)((function(){return[(0,a.bF)(A,{label:"待处理申请",name:"pending"},{default:(0,a.k6)((function(){return[h.loading?((0,a.uX)(),(0,a.CE)("div",s,[(0,a.bF)(v,{rows:5,animated:""})])):0===h.pendingApplications.length?((0,a.uX)(),(0,a.CE)("div",c,[(0,a.bF)(m,{description:"暂无待处理的申请"})])):((0,a.uX)(),(0,a.CE)("div",l,[(0,a.bF)(w,{data:h.pendingApplications,border:"",style:{width:"100%"}},{default:(0,a.k6)((function(){return[(0,a.bF)(k,{prop:"id",label:"申请ID",width:"100"}),(0,a.bF)(k,{label:"申请人",width:"150"},{default:(0,a.k6)((function(e){return[(0,a.eW)((0,a.v_)(g.getUserName(e.row.userId)),1)]})),_:1}),(0,a.bF)(k,{prop:"reason",label:"申请理由","show-overflow-tooltip":""}),(0,a.bF)(k,{prop:"createdAt",label:"申请时间",width:"180"},{default:(0,a.k6)((function(e){return[(0,a.eW)((0,a.v_)(g.formatDate(e.row.createdAt)),1)]})),_:1}),(0,a.bF)(k,{label:"操作",width:"200"},{default:(0,a.k6)((function(e){return[(0,a.bF)(b,{type:"success",size:"small",onClick:function(t){return g.handleApprove(e.row)}},{default:(0,a.k6)((function(){return t[3]||(t[3]=[(0,a.eW)(" 批准 ")])})),_:2,__:[3]},1032,["onClick"]),(0,a.bF)(b,{type:"danger",size:"small",onClick:function(t){return g.handleReject(e.row)}},{default:(0,a.k6)((function(){return t[4]||(t[4]=[(0,a.eW)(" 拒绝 ")])})),_:2,__:[4]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])]))]})),_:1}),(0,a.bF)(A,{label:"已处理申请",name:"processed"},{default:(0,a.k6)((function(){return[h.loading?((0,a.uX)(),(0,a.CE)("div",u,[(0,a.bF)(v,{rows:5,animated:""})])):0===h.processedApplications.length?((0,a.uX)(),(0,a.CE)("div",d,[(0,a.bF)(m,{description:"暂无已处理的申请"})])):((0,a.uX)(),(0,a.CE)("div",p,[(0,a.bF)(w,{data:h.processedApplications,border:"",style:{width:"100%"}},{default:(0,a.k6)((function(){return[(0,a.bF)(k,{prop:"id",label:"申请ID",width:"100"}),(0,a.bF)(k,{label:"申请人",width:"150"},{default:(0,a.k6)((function(e){return[(0,a.eW)((0,a.v_)(g.getUserName(e.row.userId)),1)]})),_:1}),(0,a.bF)(k,{prop:"reason",label:"申请理由","show-overflow-tooltip":""}),(0,a.bF)(k,{prop:"status",label:"状态",width:"120"},{default:(0,a.k6)((function(e){return[(0,a.bF)(_,{type:"APPROVED"===e.row.status?"success":"danger"},{default:(0,a.k6)((function(){return[(0,a.eW)((0,a.v_)("APPROVED"===e.row.status?"已批准":"已拒绝"),1)]})),_:2},1032,["type"])]})),_:1}),(0,a.bF)(k,{prop:"remarks",label:"备注","show-overflow-tooltip":""}),(0,a.bF)(k,{prop:"processedAt",label:"处理时间",width:"180"},{default:(0,a.k6)((function(e){return[(0,a.eW)((0,a.v_)(g.formatDate(e.row.processedAt)),1)]})),_:1})]})),_:1},8,["data"])]))]})),_:1})]})),_:1},8,["modelValue"])])}n(50113),n(51629),n(64346),n(44114),n(23288),n(62010),n(18111),n(20116),n(7588),n(79432),n(26099),n(23500);var h=n(36149);const g={name:"ReviewerApplications",data:function(){return{activeTab:"pending",loading:!1,pendingApplications:[],processedApplications:[],users:{}}},created:function(){this.fetchApplications()},methods:{fetchApplications:function(){var e=this;this.loading=!0,h["default"].users.getPendingReviewerApplications().then((function(t){return e.pendingApplications=t.data||[],h["default"].reviewer.getProcessedApplications()})).then((function(t){return e.processedApplications=t.data||[],e.fetchUsers()}))["catch"]((function(t){console.error("获取申请列表失败:",t),e.$message.error("获取申请列表失败")}))["finally"]((function(){e.loading=!1}))},fetchUsers:function(){var e=this;return h["default"].users.getAll().then((function(t){if(t&&t.data){var n=Array.isArray(t.data)?t.data:[];n.forEach((function(t){e.$set(e.users,t.id,t)})),console.log("成功获取所有用户信息:",Object.keys(e.users).length)}}))["catch"]((function(e){console.error("获取所有用户信息失败:",e)}))},getUserName:function(e){var t=this;if(this.users[e]&&this.users[e].name)return this.users[e].name;var n={1:"管理员",2:"张医生",3:"李审核",4:"王医生",5:"赵医生",6:"钱医生",7:"孙医生",8:"周医生",9:"吴医生",10:"郑医生"};return n[e]?n[e]:(h["default"].users.getUser(e).then((function(n){n&&n.data&&t.$set(t.users,e,n.data)}))["catch"]((function(t){console.error("获取用户 ".concat(e," 信息失败:"),t)})),"用户 ".concat(e))},formatDate:function(e){if(!e)return"";var t=new Date(e);return t.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},handleApprove:function(e){var t=this;this.$confirm("确定批准该用户成为审核医生吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((function(){t.processApplication(e.userId,!0)}))["catch"]((function(){}))},handleReject:function(e){var t=this;this.$prompt("请输入拒绝理由","拒绝申请",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"请输入拒绝理由..."}).then((function(n){var a=n.value;t.processApplication(e.userId,!1,a)}))["catch"]((function(){}))},processApplication:function(e,t){var n=this,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";this.loading=!0;var r=this.pendingApplications.find((function(t){return t.userId===e}));if(!r)return this.$message.error("未找到该用户的申请记录"),void(this.loading=!1);var i={status:t?"APPROVED":"REJECTED",remarks:a||""};h["default"].users.processReviewerApplication(r.id,i).then((function(){n.$message.success(t?"已批准申请":"已拒绝申请"),n.fetchApplications()}))["catch"]((function(e){console.error("处理申请失败:",e),e.response&&e.response.data&&e.response.data.message?n.$message.error(e.response.data.message):n.$message.error("处理申请失败")}))["finally"]((function(){n.loading=!1}))},goBack:function(){this.$router.push("/app/users")}}};var b=n(66262);const v=(0,b.A)(g,[["render",f],["__scopeId","data-v-e3a894ca"]]),m=v}}]);
//# sourceMappingURL=9.a8cdb7fa.js.map