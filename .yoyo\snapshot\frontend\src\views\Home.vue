<template>
  <div class="container mt-4">
    <div class="jumbotron bg-light p-5 rounded">
      <h1 class="display-4">欢迎使用血管瘤辅助系统</h1>
      <p class="lead">该系统提供血管瘤图像的上传、标注、审核和管理功能，帮助医生更高效地进行诊断和治疗。</p>
      <hr class="my-4">
      <p>如需上传和标注图像，请先登录系统。</p>
      <div>
        <router-link v-if="!isAuthenticated" to="/login" class="btn btn-primary btn-lg me-2">立即登录</router-link>
        <router-link v-if="isAuthenticated" to="/images/upload" class="btn btn-success btn-lg">上传图像</router-link>
      </div>
    </div>

    <div v-if="isAuthenticated" class="row mt-4">
      <div class="col-md-3">
        <div class="card text-white bg-primary mb-3">
          <div class="card-header">待处理图像</div>
          <div class="card-body">
            <h5 class="card-title">{{ stats.draftCount }}</h5>
            <p class="card-text">尚未提交审核的图像</p>
            <router-link to="/images?status=draft" class="btn btn-light btn-sm">查看</router-link>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-white bg-warning mb-3">
          <div class="card-header">待审核图像</div>
          <div class="card-body">
            <h5 class="card-title">{{ stats.submittedCount }}</h5>
            <p class="card-text">已提交等待审核的图像</p>
            <router-link to="/images?status=submitted" class="btn btn-light btn-sm">查看</router-link>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-white bg-success mb-3">
          <div class="card-header">已通过审核</div>
          <div class="card-body">
            <h5 class="card-title">{{ stats.approvedCount }}</h5>
            <p class="card-text">审核通过的图像</p>
            <router-link to="/images?status=approved" class="btn btn-light btn-sm">查看</router-link>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-white bg-danger mb-3">
          <div class="card-header">未通过审核</div>
          <div class="card-body">
            <h5 class="card-title">{{ stats.rejectedCount }}</h5>
            <p class="card-text">审核未通过的图像</p>
            <router-link to="/images?status=rejected" class="btn btn-light btn-sm">查看</router-link>
          </div>
        </div>
      </div>
    </div>

    <div v-if="isAuthenticated" class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header bg-info text-white">
            <h5 class="mb-0">最新动态</h5>
          </div>
          <div class="card-body">
            <p>系统中共有 <span class="badge bg-primary">{{ stats.totalCount }}</span> 张图像</p>
            <div id="recentActivity">
              <div v-if="loading" class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
              </div>
              <div v-else-if="error" class="alert alert-danger">
                {{ error }}
              </div>
              <div v-else-if="images.length === 0" class="text-center">
                暂无图像数据
              </div>
              <div v-else>
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>文件名</th>
                      <th>患者信息</th>
                      <th>状态</th>
                      <th>上传时间</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="image in recentImages" :key="image.id">
                      <td>{{ image.id }}</td>
                      <td>{{ image.filename }}</td>
                      <td>
                        {{ image.patientName || '未知' }}, 
                        {{ image.patientAge || '?' }}岁, 
                        {{ image.patientGender || '未知' }}
                      </td>
                      <td>
                        <span :class="getStatusBadgeClass(image.status)">
                          {{ getStatusText(image.status) }}
                        </span>
                      </td>
                      <td>{{ formatDate(image.createdAt) }}</td>
                      <td>
                        <router-link :to="`/images/${image.id}`" class="btn btn-sm btn-info">
                          查看
                        </router-link>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'Home',
  setup() {
    const store = useStore()
    const isAuthenticated = computed(() => store.getters.isAuthenticated)
    const images = computed(() => store.getters.getAllImages)
    const stats = computed(() => store.getters.getStats)
    const loading = computed(() => store.getters.isStatsLoading)
    const error = computed(() => store.getters.getStatsError)
    
    // 只显示最近10条记录
    const recentImages = computed(() => images.value.slice(0, 10))
    
    onMounted(async () => {
      if (isAuthenticated.value) {
        try {
          await store.dispatch('fetchStats')
          await store.dispatch('fetchImages')
        } catch (error) {
          console.error('Failed to load data:', error)
        }
      }
    })
    
    // 根据状态获取对应的样式类
    const getStatusBadgeClass = (status) => {
      switch (status) {
        case 'DRAFT': return 'badge bg-primary'
        case 'SUBMITTED': return 'badge bg-warning'
        case 'APPROVED': return 'badge bg-success'
        case 'REJECTED': return 'badge bg-danger'
        default: return 'badge bg-secondary'
      }
    }
    
    // 根据状态获取中文文本
    const getStatusText = (status) => {
      switch (status) {
        case 'DRAFT': return '草稿'
        case 'SUBMITTED': return '待审核'
        case 'APPROVED': return '已通过'
        case 'REJECTED': return '未通过'
        default: return '未知'
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    return {
      isAuthenticated,
      images,
      recentImages,
      stats,
      loading,
      error,
      getStatusBadgeClass,
      getStatusText,
      formatDate
    }
  }
}
</script> 