const { defineConfig } = require('@vue/cli-service')
const webpack = require('webpack')
const path = require('path')

/**
 * 中央API配置 - 在这里更改服务器地址将同时影响开发环境和生产环境
 * 
 * 可以通过.env文件或环境变量覆盖这些值:
 * VUE_APP_API_URL - 服务器地址和端口
 * VUE_APP_CONTEXT_PATH - 上下文路径
 * VUE_APP_API_PREFIX - API前缀
 */
const API_TARGET_URL = process.env.VUE_APP_API_URL || 'http://192.168.2.50:8085'
const API_CONTEXT_PATH = process.env.VUE_APP_CONTEXT_PATH || '/medical'
const API_PREFIX = process.env.VUE_APP_API_PREFIX || '/api'

// 将这些值写入配置文件，在前端代码创建api.config.js时可以读取
process.env.VUE_APP_API_URL = '';  // 设置为空，因为API会通过同域请求
process.env.VUE_APP_CONTEXT_PATH = API_CONTEXT_PATH;
process.env.VUE_APP_API_PREFIX = API_PREFIX;

module.exports = {
  // 确保 publicPath 设置为根路径 '/'
  // 这是解决资源路径问题的最关键配置
  publicPath: '/',

  lintOnSave: false,
  configureWebpack: {
    // 使用自定义入口点
    entry: {
      app: './webpack-entry.js'
    },
    plugins: [
      new webpack.DefinePlugin({
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
        // 全局注入tryNextPath函数
        'tryNextPath': function(paths, index) {
          if (!paths || index >= paths.length) return '';
          return paths[index];
        }
      }),
      // 提供Node.js核心模块的polyfill
      new webpack.ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer'],
        tryNextPath: [(path.resolve(__dirname, 'src/element-plus-unified-fix.js')), 'tryNextPath']
      })
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        // 解决Element Plus模块问题
        'element-plus/es': path.resolve(__dirname, 'node_modules/element-plus/lib'),
        // 修复vue解析
        'vue$': 'vue/dist/vue.esm-bundler.js',
      },
      fallback: {
        // Node.js核心模块polyfills
        "http": require.resolve("stream-http"),
        "https": require.resolve("https-browserify"),
        "url": require.resolve("url"),
        "stream": require.resolve("stream-browserify"),
        "buffer": require.resolve("buffer/"),
        "process": require.resolve("process/browser"),
        "util": require.resolve("util/"),
        "path": require.resolve("path-browserify"),
        "crypto": require.resolve("crypto-browserify")
      },
      extensions: ['.js', '.vue', '.json', '.mjs', '.cjs']
    },
    module: {
      rules: [
        // 处理.mjs文件
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: 'javascript/auto'
        }
      ]
    }
  },
  devServer: {
    // 允许任何域名访问，解决ngrok内网穿透时的Host header问题
    allowedHosts: 'all',
    // 兼容旧版webpack-dev-server的配置
    historyApiFallback: true,
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws',
    },
    proxy: {
      [API_CONTEXT_PATH + API_PREFIX]: {
        target: API_TARGET_URL,
        changeOrigin: true
      },
      [API_CONTEXT_PATH]: {
        target: API_TARGET_URL,
        changeOrigin: true
      }
      }
  }
} 