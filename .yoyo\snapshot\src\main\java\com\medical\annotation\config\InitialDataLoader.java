package com.medical.annotation.config;

import com.medical.annotation.model.User;
import com.medical.annotation.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

@Configuration
public class InitialDataLoader {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Bean
    public CommandLineRunner loadData() {
        return args -> {
            // 如果没有任何用户，则创建默认管理员用户
            if (userRepository.count() == 0) {
                User adminUser = new User();
                adminUser.setEmail("<EMAIL>");
                adminUser.setName("管理员");
                adminUser.setPassword(passwordEncoder.encode("admin123"));
                adminUser.setRole(User.Role.ADMIN);
                adminUser.setHospital("系统默认");
                adminUser.setDepartment("管理部门");
                
                userRepository.save(adminUser);
                
                System.out.println("初始管理员用户已创建: <EMAIL> / admin123");
            }
        };
    }
} 