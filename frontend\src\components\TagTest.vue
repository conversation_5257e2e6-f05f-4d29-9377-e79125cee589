<template>
  <div class="tag-test">
    <h2>标注测试工具</h2>
    <p>这个组件用于测试直接向后端发送标注请求</p>
    
    <div class="input-area">
      <div class="form-group">
        <label>图像ID (metadata_id)</label>
        <input type="number" v-model="metadata_id" placeholder="输入图像ID" />
      </div>
      
      <div class="form-group">
        <label>标签名称 (tag)</label>
        <input type="text" v-model="tag" placeholder="输入标签名称" />
      </div>
      
      <div class="form-group">
        <label>创建者ID (created_by)</label>
        <input type="number" v-model="created_by" placeholder="输入创建者ID" />
      </div>
      
      <div class="form-group">
        <label>X坐标 (x)</label>
        <input type="number" v-model="x" step="0.01" placeholder="输入归一化X坐标" />
      </div>
      
      <div class="form-group">
        <label>Y坐标 (y)</label>
        <input type="number" v-model="y" step="0.01" placeholder="输入归一化Y坐标" />
      </div>
      
      <div class="form-group">
        <label>宽度 (width)</label>
        <input type="number" v-model="width" step="0.01" placeholder="输入归一化宽度" />
      </div>
      
      <div class="form-group">
        <label>高度 (height)</label>
        <input type="number" v-model="height" step="0.01" placeholder="输入归一化高度" />
      </div>
    </div>
    
    <div class="action-area">
      <button @click="testFetch">使用Fetch API测试</button>
      <button @click="testAxios">使用Axios测试</button>
      <button @click="testXHR">使用XHR测试</button>
    </div>
    
    <div class="result-area">
      <h3>测试结果</h3>
      <div v-if="result">
        <pre>{{ result }}</pre>
      </div>
      <div v-if="error" class="error">
        <h4>错误信息</h4>
        <pre>{{ error }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'TagTest',
  data() {
    return {
      metadata_id: 1,
      tag: '血管瘤',
      created_by: 1,
      x: 0.5,
      y: 0.5,
      width: 0.2,
      height: 0.2,
      result: null,
      error: null
    };
  },
  methods: {
    async testFetch() {
      this.result = null;
      this.error = null;
      
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        
        // 构建请求数据
        const data = {
          metadata_id: parseInt(this.metadata_id),
          tag: this.tag,
          created_by: parseInt(this.created_by),
          x: parseFloat(this.x),
          y: parseFloat(this.y),
          width: parseFloat(this.width),
          height: parseFloat(this.height)
        };
        
        // 添加认证信息
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        };
        
        if (user.id || user.customId) {
          const userId = user.customId || user.id;
          headers['Authorization'] = `Bearer user_${userId}`;
        }
        
        // 记录请求详情
        console.log('发送请求到: /medical/api/api', data);
        
        // 显示确认框
        if (!confirm(`即将发送请求到 /medical/api/api\n数据: ${JSON.stringify(data, null, 2)}\n确认发送?`)) {
          this.result = "用户取消了请求";
          return;
        }
        
        // 发送请求
        const response = await fetch('/medical/api/api', {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(data),
          credentials: 'include'
        });
        
        // 检查响应状态
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        // 解析响应
        const result = await response.json();
        
        // 显示结果
        this.result = JSON.stringify(result, null, 2);
        alert('请求成功，查看结果!');
      } catch (error) {
        console.error('测试失败:', error);
        this.error = error.toString();
        alert('请求失败: ' + error.toString());
      }
    },
    
    async testAxios() {
      this.result = null;
      this.error = null;
      
      try {
        // 构建请求数据
        const data = {
          metadata_id: parseInt(this.metadata_id),
          tag: this.tag,
          created_by: parseInt(this.created_by),
          x: parseFloat(this.x),
          y: parseFloat(this.y),
          width: parseFloat(this.width),
          height: parseFloat(this.height)
        };
        
        // 记录请求详情
        console.log('使用Axios发送请求到: /medical/api/api', data);
        
        // 显示确认框
        if (!confirm(`即将使用Axios发送请求到 /medical/api/api\n数据: ${JSON.stringify(data, null, 2)}\n确认发送?`)) {
          this.result = "用户取消了请求";
          return;
        }
        
        // 发送请求
        const response = await axios.post('/medical/api/api', data, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          withCredentials: true
        });
        
        // 显示结果
        this.result = JSON.stringify(response.data, null, 2);
        alert('请求成功，查看结果!');
      } catch (error) {
        console.error('Axios测试失败:', error);
        this.error = error.toString();
        if (error.response) {
          this.error += '\n\nResponse data: ' + JSON.stringify(error.response.data, null, 2);
        }
        alert('请求失败: ' + error.toString());
      }
    },
    
    testXHR() {
      this.result = null;
      this.error = null;
      
      try {
        // 构建请求数据
        const data = {
          metadata_id: parseInt(this.metadata_id),
          tag: this.tag,
          created_by: parseInt(this.created_by),
          x: parseFloat(this.x),
          y: parseFloat(this.y),
          width: parseFloat(this.width),
          height: parseFloat(this.height)
        };
        
        // 记录请求详情
        console.log('使用XHR发送请求到: /medical/api/api', data);
        
        // 显示确认框
        if (!confirm(`即将使用XHR发送请求到 /medical/api/api\n数据: ${JSON.stringify(data, null, 2)}\n确认发送?`)) {
          this.result = "用户取消了请求";
          return;
        }
        
        // 创建XHR请求
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/medical/api/api', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('Accept', 'application/json');
        xhr.withCredentials = true;
        
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            this.result = JSON.stringify(JSON.parse(xhr.responseText), null, 2);
            alert('请求成功，查看结果!');
          } else {
            this.error = `XHR Error: ${xhr.status} ${xhr.statusText}\n${xhr.responseText}`;
            alert('请求失败: ' + xhr.status + ' ' + xhr.statusText);
          }
        };
        
        xhr.onerror = () => {
          this.error = 'XHR请求失败';
          alert('XHR请求失败');
        };
        
        // 发送请求
        xhr.send(JSON.stringify(data));
      } catch (error) {
        console.error('XHR测试失败:', error);
        this.error = error.toString();
        alert('请求失败: ' + error.toString());
      }
    }
  }
};
</script>

<style scoped>
.tag-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.input-area {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

label {
  font-weight: bold;
  margin-bottom: 5px;
}

input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.action-area {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

button {
  padding: 10px 15px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #45a049;
}

.result-area {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  background-color: #eee;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
}

.error {
  margin-top: 10px;
  color: #d32f2f;
}
</style> 