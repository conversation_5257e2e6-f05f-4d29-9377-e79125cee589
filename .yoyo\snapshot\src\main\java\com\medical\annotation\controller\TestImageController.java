package com.medical.annotation.controller;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Controller
@RequestMapping("/test")
public class TestImageController {
    
    @GetMapping("/image")
    @ResponseBody
    public ResponseEntity<Resource> getTestImage() {
        try {
            System.out.println("测试获取图片");
            
            // 使用固定路径获取指定的测试图片
            String imagePath = "F:/血管瘤辅助系统/medical_images/temp/temp_4e7e5728-0607-4491-add1-ec7241539c17_IH_009_017.jpg";
            File file = new File(imagePath);
            
            System.out.println("测试图片路径: " + file.getAbsolutePath());
            System.out.println("文件是否存在: " + file.exists() + ", 大小: " + (file.exists() ? file.length() : 0));
            
            if (file.exists()) {
                // 尝试直接读取字节数据
                byte[] data = Files.readAllBytes(file.toPath());
                System.out.println("成功读取图片数据，大小: " + data.length + " 字节");
                
                ByteArrayResource resource = new ByteArrayResource(data);
                
                return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_JPEG)
                        .contentLength(data.length)
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"test.jpg\"")
                        .body(resource);
            } else {
                System.out.println("文件不存在: " + imagePath);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.err.println("测试获取图片时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }
    
    @GetMapping("/simple")
    @ResponseBody
    public String getSimpleResponse() {
        return "测试成功 - 简单文本响应";
    }
} 