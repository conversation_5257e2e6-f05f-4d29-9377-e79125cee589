package com.medical.annotation.controller;

import com.medical.annotation.model.ReviewerApplication;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.service.ReviewerApplicationService;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/users")
public class UserApiController {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ReviewerApplicationService reviewerApplicationService;
    
    // 获取所有用户（仅返回基本信息，不包括密码）
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        // 使用UserService确保所有用户都有customId
        List<User> users = userService.getAllUsers();
        // 清空密码字段
        users.forEach(user -> user.setPassword(null));
        return ResponseEntity.ok(users);
    }
    
    // 获取单个用户
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Integer id) {
        Optional<User> user = userRepository.findById(id);
        if (user.isPresent()) {
            User returnUser = user.get();
            returnUser.setPassword(null);
            return ResponseEntity.ok(returnUser);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    // 创建用户
    @PostMapping
    public ResponseEntity<?> createUser(@RequestBody User user) {
        try {
            // 使用UserService创建用户，确保生成customId
            User savedUser = userService.createUser(user);
            savedUser.setPassword(null);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedUser);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            // 检查是否是邮箱已存在的错误
            if (e.getMessage() != null && e.getMessage().contains("邮箱已被使用")) {
                error.put("message", "该邮箱已被注册，请更换邮箱");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
            }
            // 其他错误
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    // 更新用户信息
    @PutMapping("/{id}")
    public ResponseEntity<?> updateUser(@PathVariable Integer id, @RequestBody Map<String, Object> userData) {
        try {
            System.out.println("收到更新用户请求: ID=" + id + ", 请求体=" + userData);
            
            // 从请求体中获取字段
            String customId = userData.containsKey("customId") ? (String) userData.get("customId") : null;
            String email = userData.containsKey("email") ? (String) userData.get("email") : null;
            String name = userData.containsKey("name") ? (String) userData.get("name") : null;
            String hospital = userData.containsKey("hospital") ? (String) userData.get("hospital") : null;
            String department = userData.containsKey("department") ? (String) userData.get("department") : null;
            
            System.out.println("请求体详情:");
            System.out.println("- ID: " + id);
            System.out.println("- CustomID: " + customId);
            System.out.println("- Email: " + email);
            System.out.println("- Name: " + name);
            System.out.println("- Hospital: " + hospital);
            System.out.println("- Department: " + department);
            
            // 创建User对象并设置字段
            User user = new User();
            user.setId(id);
            user.setCustomId(customId);
            user.setEmail(email);
            user.setName(name);
            user.setHospital(hospital);
            user.setDepartment(department);
            
            User updatedUser = userService.updateUser(user);
            updatedUser.setPassword(null);
            
            System.out.println("用户更新成功: " + updatedUser);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            System.out.println("用户更新失败: " + e.getMessage());
            e.printStackTrace();
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    // 测试更新用户信息 - 调试端点
    @PostMapping("/debug/update/{id}")
    public ResponseEntity<?> debugUpdateUser(@PathVariable Integer id, @RequestBody Map<String, Object> userData) {
        try {
            System.out.println("收到调试更新用户请求: ID=" + id + ", 请求体=" + userData);
            
            User user = new User();
            user.setId(id);
            
            if (userData.containsKey("customId")) {
                user.setCustomId((String) userData.get("customId"));
            }
            
            if (userData.containsKey("email")) {
                user.setEmail((String) userData.get("email"));
            }
            
            if (userData.containsKey("name")) {
                user.setName((String) userData.get("name"));
            }
            
            if (userData.containsKey("hospital")) {
                user.setHospital((String) userData.get("hospital"));
            }
            
            if (userData.containsKey("department")) {
                user.setDepartment((String) userData.get("department"));
            }
            
            User updatedUser = userService.updateUser(user);
            updatedUser.setPassword(null);
            
            System.out.println("调试用户更新成功: " + updatedUser);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            System.out.println("调试用户更新失败: " + e.getMessage());
            e.printStackTrace();
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    // 删除用户
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteUser(@PathVariable Integer id) {
        if (!userRepository.existsById(id)) {
            return ResponseEntity.notFound().build();
        }
        
        userRepository.deleteById(id);
        return ResponseEntity.ok().build();
    }
    
    // 重置密码
    @PostMapping("/{id}/reset-password")
    public ResponseEntity<?> resetPassword(@PathVariable Integer id, @RequestBody Map<String, String> passwordData) {
        String newPassword = passwordData.get("password");
        
        if (newPassword == null || newPassword.isEmpty()) {
            return ResponseEntity.badRequest().body("New password is required");
        }
        
        Optional<User> userOpt = userRepository.findById(id);
        if (!userOpt.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        User user = userOpt.get();
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Password reset successfully");
        return ResponseEntity.ok(response);
    }
    
    // 用户验证
    @PostMapping("/authenticate")
    public ResponseEntity<?> authenticate(@RequestBody Map<String, String> credentials) {
        String email = credentials.get("email");
        String password = credentials.get("password");
        
        if (email == null || password == null) {
            return ResponseEntity.badRequest().body("Email and password are required");
        }
        
        Optional<User> user = userRepository.findByEmail(email);
        if (!user.isPresent()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials");
        }
        
        // 尝试使用密码编码器验证
        if (passwordEncoder.matches(password, user.get().getPassword())) {
            User authenticatedUser = user.get();
            authenticatedUser.setPassword(null); // 不返回密码
            return ResponseEntity.ok(authenticatedUser);
        } 
        // 备选方案：如果数据库中存储的是明文密码，直接比较
        else if (password.equals(user.get().getPassword())) {
            User authenticatedUser = user.get();
            authenticatedUser.setPassword(null); // 不返回密码
            
            // 将用户密码升级为加密格式（可选）
            try {
                User userToUpdate = user.get();
                userToUpdate.setPassword(passwordEncoder.encode(password));
                userRepository.save(userToUpdate);
                System.out.println("已将用户 " + email + " 的密码升级为加密格式");
            } catch (Exception e) {
                System.err.println("升级密码失败: " + e.getMessage());
            }
            
            return ResponseEntity.ok(authenticatedUser);
        }
        else {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials");
        }
    }
    
    // 获取当前登录用户信息
    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser() {
        try {
            // 从Spring Security上下文中获取当前用户的电子邮件
            org.springframework.security.core.Authentication auth = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            String email = auth.getName();
            if (email.equals("anonymousUser")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
            }
            
            Optional<User> userOpt = userRepository.findByEmail(email);
            if (!userOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("用户不存在");
            }
            
            User user = userOpt.get();
            user.setPassword(null); // 不返回密码
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 忘记密码 - 发送验证码
    @PostMapping("/forgot-password")
    public ResponseEntity<?> forgotPassword(@RequestBody Map<String, String> request) {
        try {
            String email = request.get("email");
            if (email == null || email.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("message", "邮箱地址不能为空"));
            }
            
            // 检查邮箱是否存在
            Optional<User> userOpt = userRepository.findByEmail(email);
            if (!userOpt.isPresent()) {
                return ResponseEntity.badRequest().body(Map.of("message", "该邮箱未注册"));
            }
            
            // 生成验证码并保存
            String verificationCode = userService.generatePasswordResetCode(email);
            
            // 发送验证码邮件
            userService.sendPasswordResetEmail(email, verificationCode);
            
            return ResponseEntity.ok(Map.of("message", "验证码已发送到您的邮箱"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("message", e.getMessage()));
        }
    }
    
    // 验证重置密码的验证码
    @PostMapping("/verify-reset-code")
    public ResponseEntity<?> verifyResetCode(@RequestBody Map<String, String> request) {
        try {
            String email = request.get("email");
            String code = request.get("code");
            
            if (email == null || email.isEmpty() || code == null || code.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("message", "邮箱和验证码不能为空"));
            }
            
            // 验证验证码
            boolean isValid = userService.verifyPasswordResetCode(email, code);
            if (!isValid) {
                return ResponseEntity.badRequest().body(Map.of("message", "验证码错误或已过期"));
            }
            
            return ResponseEntity.ok(Map.of("message", "验证成功"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("message", e.getMessage()));
        }
    }
    
    // 重置密码
    @PostMapping("/reset-password")
    public ResponseEntity<?> resetPassword(@RequestBody Map<String, String> request) {
        try {
            String email = request.get("email");
            String code = request.get("code");
            String newPassword = request.get("newPassword");
            
            if (email == null || email.isEmpty() || code == null || code.isEmpty() || newPassword == null || newPassword.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("message", "邮箱、验证码和新密码不能为空"));
            }
            
            // 验证验证码
            boolean isValid = userService.verifyPasswordResetCode(email, code);
            if (!isValid) {
                return ResponseEntity.badRequest().body(Map.of("message", "验证码错误或已过期"));
            }
            
            try {
                // 重置密码
                userService.resetPassword(email, newPassword);
                return ResponseEntity.ok(Map.of("message", "密码重置成功"));
            } catch (Exception e) {
                // 捕获密码复杂度验证错误
                return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("message", e.getMessage()));
        }
    }
    
    // 申请成为审核医生
    @PostMapping("/me/reviewer-application")
    public ResponseEntity<?> applyForReviewer(@RequestBody Map<String, String> requestBody) {
        try {
            String reason = requestBody.get("reason");
            
            // 获取当前用户
            org.springframework.security.core.Authentication auth = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            String email = auth.getName();
            if (email.equals("anonymousUser")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
            }
            
            Optional<User> userOpt = userRepository.findByEmail(email);
            if (!userOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("用户不存在");
            }
            
            Integer userId = userOpt.get().getId();
            
            // 创建申请
            ReviewerApplication application = reviewerApplicationService.createApplication(userId, reason);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(application);
        } catch (IllegalArgumentException e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 获取所有待处理的审核医生申请（管理员用）
    @GetMapping("/reviewer-applications")
    public ResponseEntity<?> getPendingReviewerApplications() {
        try {
            List<ReviewerApplication> applications = reviewerApplicationService.getPendingApplications();
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 处理审核医生申请（批准或拒绝）
    @PostMapping("/reviewer-applications/{userId}")
    public ResponseEntity<?> processReviewerApplication(
            @PathVariable Integer userId, 
            @RequestBody Map<String, Object> requestBody) {
        try {
            Boolean approved = (Boolean) requestBody.get("approved");
            String remarks = (String) requestBody.getOrDefault("remarks", "");
            
            // 获取处理人ID
            org.springframework.security.core.Authentication auth = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            String email = auth.getName();
            if (email.equals("anonymousUser")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
            }
            
            Optional<User> processorOpt = userRepository.findByEmail(email);
            if (!processorOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("处理人不存在");
            }
            
            Integer processorId = processorOpt.get().getId();
            
            // 获取用户的待处理申请
            List<ReviewerApplication> applications = reviewerApplicationService.getUserApplications(userId);
            Optional<ReviewerApplication> pendingApplication = applications.stream()
                .filter(app -> app.getStatus().equals("PENDING"))
                .findFirst();
            
            if (!pendingApplication.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("未找到该用户的待处理申请");
            }
            
            // 处理申请
            String status = approved ? "APPROVED" : "REJECTED";
            ReviewerApplication processedApplication = reviewerApplicationService.processApplication(
                pendingApplication.get().getId(), status, remarks, processorId);
            
            return ResponseEntity.ok(processedApplication);
        } catch (IllegalArgumentException e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    // 上传用户头像
    @PostMapping("/{id}/avatar")
    public ResponseEntity<?> uploadAvatar(@PathVariable Integer id, @RequestParam("file") MultipartFile file) {
        try {
            // 检查用户是否存在
            Optional<User> userOpt = userRepository.findById(id);
            if (!userOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            User user = userOpt.get();
            
            // 检查文件是否为空
            if (file.isEmpty()) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "请选择一个文件");
                return ResponseEntity.badRequest().body(error);
            }
            
            // 检查文件类型
            String contentType = file.getContentType();
            if (contentType == null || (!contentType.startsWith("image/"))) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "只允许上传图片文件");
                return ResponseEntity.badRequest().body(error);
            }
            
            // 获取文件扩展名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            
            // 生成唯一文件名
            String fileName = "avatar_" + user.getId() + "_" + System.currentTimeMillis() + extension;
            
            // 确保目录存在
            File directory = new File("medical_images/head_portrait");
            if (!directory.exists()) {
                directory.mkdirs();
            }
            
            // 保存文件
            Path filePath = Paths.get("medical_images/head_portrait/" + fileName);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            
            // 更新用户头像路径 - 修改为正确的医院系统资源映射路径
            user.setAvatarPath("/medical/images/head_portrait/" + fileName);
            userRepository.save(user);
            
            // 返回头像URL
            Map<String, String> response = new HashMap<>();
            response.put("avatarUrl", user.getAvatarPath());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, String> error = new HashMap<>();
            error.put("error", "上传头像失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    // 获取用户头像
    @GetMapping("/{id}/avatar")
    public ResponseEntity<?> getUserAvatar(@PathVariable Integer id) {
        try {
            Optional<User> userOpt = userRepository.findById(id);
            if (!userOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            User user = userOpt.get();
            String avatarPath = user.getAvatarPath();
            
            if (avatarPath == null || avatarPath.isEmpty()) {
                Map<String, String> response = new HashMap<>();
                response.put("avatarUrl", null);
                return ResponseEntity.ok(response);
            }
            
            Map<String, String> response = new HashMap<>();
            response.put("avatarUrl", avatarPath);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, String> error = new HashMap<>();
            error.put("error", "获取头像失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    /**
     * 修改用户密码
     */
    @PostMapping("/{id}/change-password")
    public ResponseEntity<?> changeUserPassword(@PathVariable Integer id, @RequestBody Map<String, String> passwordData) {
        String oldPassword = passwordData.get("oldPassword");
        String newPassword = passwordData.get("newPassword");
        
        if (oldPassword == null || oldPassword.isEmpty() || newPassword == null || newPassword.isEmpty()) {
            Map<String, String> error = new HashMap<>();
            error.put("message", "原密码和新密码不能为空");
            return ResponseEntity.badRequest().body(error);
        }
        
        try {
            // 验证密码复杂度
            String validationMessage = userService.validatePasswordComplexity(newPassword);
            if (validationMessage != null) {
                Map<String, String> error = new HashMap<>();
                error.put("message", validationMessage);
                return ResponseEntity.badRequest().body(error);
            }
            
            // 修改密码
            User user = userService.changePassword(id, oldPassword, newPassword);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "密码修改成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
} 