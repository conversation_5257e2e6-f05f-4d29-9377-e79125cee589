import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import axios from 'axios'
// 导入API配置
import { API_BASE_URL, API_CONTEXT_PATH, DASHBOARD_STATS_URL } from './config/api.config'

// 全局引入 Element Plus
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 单独引入 Element Plus 组件的方法，确保正确注册
import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'

// 引入自定义权限指令
import { registerPermissionDirective } from './directives/permission'

// 引入组件暴露模块
import './expose-components'

// 引入清理工具
// import { cleanInvalidImagePairs } from './utils/cleanupHelper'

// 移除Bootstrap引入
// import 'bootstrap/dist/css/bootstrap.min.css'
// import 'bootstrap-icons/font/bootstrap-icons.css'
// import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// 引入全局样式
import './assets/css/style.css'

// 添加用户权限修复工具
window.fixUserPermission = function() {
  try {
    // 获取当前用户
    const userStr = localStorage.getItem('user');
    if (!userStr) {
      return {success: false, message: '未找到已登录用户'};
    }
    
    const user = JSON.parse(userStr);
    
    // 检查用户角色
    if (!user.role) {
      // 根据用户ID判断角色
      if (user.customId && user.customId.startsWith('1')) {
        user.role = 'ADMIN';
      } else if (user.customId && user.customId.startsWith('3')) {
        user.role = 'REVIEWER';
      } else {
        user.role = 'DOCTOR';
      }
      
      // 保存修复后的用户数据
      localStorage.setItem('user', JSON.stringify(user));
      
      // 更新store
      store.commit('setUser', user);
      
      return {success: true, message: `已修复用户角色为: ${user.role}`};
    } else {
      // 特殊检查张医生账号
      if (user.customId === '200000001' && user.role !== 'DOCTOR') {
        user.role = 'DOCTOR';
        localStorage.setItem('user', JSON.stringify(user));
        // 更新store
        store.commit('setUser', user);
        return {success: true, message: '已修复张医生账号的角色为DOCTOR'};
      }
      
      // 特殊检查李审核账号
      if (user.customId === '300000001' && (user.role !== 'REVIEWER' || user.name !== '李审核')) {
        user.role = 'REVIEWER';
        user.name = '李审核';
        localStorage.setItem('user', JSON.stringify(user));
        // 更新store
        store.commit('setUser', user);
        return {success: true, message: '已修复李审核账号的信息'};
      }
      
      return {success: true, message: `用户角色已存在: ${user.role}`};
    }
  } catch (error) {
    return {success: false, message: `修复失败: ${error.message}`};
  }
};

// 替换localStorage监听为空函数
const originalSetItem = localStorage.setItem;
const originalRemoveItem = localStorage.removeItem;

localStorage.setItem = function(key, value) {
  originalSetItem.call(this, key, value);
};

localStorage.removeItem = function(key) {
  originalRemoveItem.call(this, key);
};

// 设置axios全局默认值
// axios.defaults.baseURL = '/api'; // 不使用baseURL，避免路径重复
axios.defaults.withCredentials = true; // 允许跨域请求发送cookies
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.timeout = 10000; // 10秒超时

// 移除所有日志输出的请求拦截器
axios.interceptors.request.use(
  config => {
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 移除所有日志输出的响应拦截器
axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    return Promise.reject(error);
  }
);

const app = createApp(App)

// 禁用全局错误处理器中的日志输出
app.config.errorHandler = function(err, vm, info) {
  // 错误处理逻辑保留，但不输出日志
};

// 禁用全局未捕获的Promise异常处理中的日志输出
window.addEventListener('unhandledrejection', event => {
  // 异常处理逻辑保留，但不输出日志
});

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局配置
app.config.globalProperties.$axios = axios

// 正确注册Element Plus的消息组件
app.config.globalProperties.$message = ElMessage
app.config.globalProperties.$notify = ElNotification
app.config.globalProperties.$msgbox = ElMessageBox
app.config.globalProperties.$alert = ElMessageBox.alert
app.config.globalProperties.$confirm = ElMessageBox.confirm
app.config.globalProperties.$prompt = ElMessageBox.prompt
app.config.globalProperties.$loading = ElLoading.service

// 注册权限指令
registerPermissionDirective(app)

// 注册Element Plus
app.use(store)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})

// 将消息组件暴露给全局，以便API响应拦截器可以使用
window.$message = ElMessage

// 添加全局Dashboard统计数据刷新函数（移除日志输出）
window.refreshDashboardStats = function() {
  // 获取当前用户ID
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) {
      return;
    }
    
    const user = JSON.parse(userStr);
    const userId = user.customId || user.id;
    
    if (!userId) {
      return;
    }
    
    // 使用XHR同步请求确保数据立即可用
    const xhr = new XMLHttpRequest();
    xhr.open('GET', `${DASHBOARD_STATS_URL}/${userId}?t=${Date.now()}`, false); // 使用API配置
    xhr.send();
    
    if (xhr.status === 200) {
      const data = JSON.parse(xhr.responseText);
      
      // 缓存到全局变量
      window.dashboardStats = {
        totalCount: parseInt(data.totalCount || 0),
        draftCount: parseInt(data.draftCount || 0),
        reviewedCount: parseInt(data.reviewedCount || 0),
        submittedCount: parseInt(data.submittedCount || 0),
        approvedCount: parseInt(data.approvedCount || 0),
        rejectedCount: parseInt(data.rejectedCount || 0),
        dataSource: "global_refresh",
        timestamp: Date.now(),
        userId: userId
      };
      
      // 保存到localStorage
      localStorage.setItem('dashboardStats', JSON.stringify(window.dashboardStats));
      
      // 触发统计刷新事件
      const event = new CustomEvent('dashboard-stats-updated', {
        detail: window.dashboardStats
      });
      window.dispatchEvent(event);
    }
  } catch (error) {
    // 错误处理，但不输出日志
  }
};

// 添加禁用日志输出的函数
const disableConsoleOutput = () => {
  if (typeof window !== 'undefined') {
    window.console = {
      ...console,
      log: function() {},
      info: function() {},
      warn: function() {},
      error: function() {},
      debug: function() {}
    };
  }
};

// 初始化应用
app.mount('#app');

// 禁用控制台输出
disableConsoleOutput();

// 在应用挂载后执行数据强制更新
setTimeout(() => {
  console.log('🔄 应用挂载后尝试强制更新统计数据');
  
  try {
    // 1. 尝试从localStorage获取统计数据
    const statsData = JSON.parse(localStorage.getItem('dashboardStats') || '{}');
    if (statsData && statsData.totalCount) {
      console.log('找到统计数据:', statsData);
      
      // 2. 直接更新DOM
      const statElements = document.querySelectorAll('.stat-value');
      if (statElements && statElements.length >= 5) {
        statElements[0].textContent = statsData.totalCount || 0;
        statElements[1].textContent = statsData.draftCount || 0;
        statElements[2].textContent = statsData.reviewedCount || 0;
        statElements[3].textContent = statsData.submittedCount || 0;
        statElements[4].textContent = statsData.approvedCount || 0;
        console.log('📊 统计数据DOM元素已强制更新!');
      } else {
        console.log('未找到统计卡片DOM元素:', statElements ? statElements.length : 0);
        
        // 如果找不到元素，可能是还没渲染，再等待一小段时间
        setTimeout(() => {
          const retryElements = document.querySelectorAll('.stat-value');
          if (retryElements && retryElements.length >= 5) {
            retryElements[0].textContent = statsData.totalCount || 0;
            retryElements[1].textContent = statsData.draftCount || 0;
            retryElements[2].textContent = statsData.reviewedCount || 0;
            retryElements[3].textContent = statsData.submittedCount || 0;
            retryElements[4].textContent = statsData.approvedCount || 0;
            console.log('📊 第二次尝试：统计数据DOM元素已强制更新!');
          }
        }, 300);
      }
    } else {
      console.log('localStorage中没有找到统计数据，尝试直接获取');
      
      // 3. 如果没有localStorage数据，获取当前用户ID后请求
      const axios = require('axios').default;
      
      // 从localStorage获取当前用户ID
      try {
        const userStr = localStorage.getItem('user') || '{}';
        const user = JSON.parse(userStr);
        const userId = user.customId || user.id;
        
        if (!userId) {
          console.log('找不到有效的用户ID，跳过数据获取');
          return;
        }
        
        console.log('尝试获取用户', userId, '的统计数据');
        
        // 使用无认证限制的API端点
        axios.get(`http://192.168.2.43:8085/medical/api/stats-v2/dashboard-unrestricted/${userId}?t=${Date.now()}`)
          .then(response => {
            console.log('直接获取统计数据成功:', response.data);
            
            // 更新DOM
            const statElements = document.querySelectorAll('.stat-value');
            if (statElements && statElements.length >= 5) {
              statElements[0].textContent = response.data.totalCount || 0;
              statElements[1].textContent = response.data.draftCount || 0;
              statElements[2].textContent = response.data.reviewedCount || 0;
              statElements[3].textContent = response.data.submittedCount || 0;
              statElements[4].textContent = response.data.approvedCount || 0;
              console.log('📊 通过API获取的统计数据已更新到DOM!');
            }
          })
          .catch(error => {
            console.error('直接获取统计数据失败:', error);
          });
      } catch (error) {
        console.error('获取用户ID失败:', error);
      }
    }
  } catch (error) {
    console.error('强制更新统计数据失败:', error);
  }
}, 500);

// 在应用挂载后执行数据库清理
// console.log('应用启动，执行数据库清理...');
// cleanInvalidImagePairs(); 

// 添加window.location变更监听
const originalPushState = history.pushState;
const originalReplaceState = history.replaceState;

// 重写pushState
history.pushState = function() {
  console.log('[URL变更] history.pushState 调用:', {
    状态: arguments[0],
    标题: arguments[1],
    URL: arguments[2],
    当前时间: new Date().toLocaleTimeString(),
    调用方法: '直接调用'
  });
  
  // 获取调用堆栈
  const stack = new Error().stack;
  console.log('[URL变更] pushState调用堆栈:', stack);
  
  return originalPushState.apply(this, arguments);
};

// 重写replaceState
history.replaceState = function() {
  console.log('[URL变更] history.replaceState 调用:', {
    状态: arguments[0],
    标题: arguments[1],
    URL: arguments[2],
    当前时间: new Date().toLocaleTimeString(),
    调用方法: '直接调用'
  });
  
  // 获取调用堆栈
  const stack = new Error().stack;
  console.log('[URL变更] replaceState调用堆栈:', stack);
  
  return originalReplaceState.apply(this, arguments);
};

// 监听popstate事件
window.addEventListener('popstate', function(event) {
  console.log('[URL变更] popstate事件触发:', {
    状态: event.state,
    当前URL: window.location.href,
    当前时间: new Date().toLocaleTimeString(),
    调用方法: 'popstate事件'
  });
});

// 创建全局事件总线用于组件间通信
import mitt from 'mitt';
const eventBus = mitt();

// 全局事件总线，用于组件间通信
window.eventBus = eventBus; 