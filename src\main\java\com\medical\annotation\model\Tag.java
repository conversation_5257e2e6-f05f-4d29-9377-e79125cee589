package com.medical.annotation.model;

import com.fasterxml.jackson.annotation.JsonBackReference;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@Entity
@Table(name = "tags")
public class Tag {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "tag_name")
    private String tagName;
    
    // 老版本兼容：用于存储tag名称的字段
    @Column(name = "tag")
    private String tag;
    
    @Column(name = "x")
    private Double x;
    
    @Column(name = "y")
    private Double y;
    
    @Column(name = "width")
    private Double width;
    
    @Column(name = "height")
    private Double height;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "confidence", nullable = true)
    private Double confidence;
    
    // 老版本兼容：直接存储hemangioma_id，不需要关联关系
    @Column(name = "hemangioma_id")
    private Long hemangioma_id;
    
    // 老版本兼容：创建者ID
    @Column(name = "created_by")
    private Long createdBy;
    
    // 添加metadata_id字段
    @Column(name = "metadata_id")
    private Integer metadata_id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hemangioma_diagnosis_id")
    @JsonBackReference
    private HemangiomaDiagnosis hemangiomaDiagnosis;
    
    // 构造函数
    public Tag() {
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        this.createdAt = chinaTime.toLocalDateTime();
        this.updatedAt = chinaTime.toLocalDateTime();
    }
    
    @PrePersist
    protected void onCreate() {
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        if (createdAt == null) {
            createdAt = chinaTime.toLocalDateTime();
        }
        updatedAt = chinaTime.toLocalDateTime();
    }
    
    @PreUpdate
    protected void onUpdate() {
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        updatedAt = chinaTime.toLocalDateTime();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTagName() {
        return tagName;
    }
    
    public void setTagName(String tagName) {
        this.tagName = tagName;
        // 同步设置tag字段，保持向后兼容
        this.tag = tagName;
    }
    
    // 为老版本兼容添加tag的getter和setter
    public String getTag() {
        return tag;
    }
    
    public void setTag(String tag) {
        this.tag = tag;
        // 同步设置tagName字段
        this.tagName = tag;
    }
    
    public Double getX() {
        return x;
    }
    
    public void setX(Double x) {
        this.x = x;
    }
    
    public Double getY() {
        return y;
    }
    
    public void setY(Double y) {
        this.y = y;
    }
    
    public Double getWidth() {
        return width;
    }
    
    public void setWidth(Double width) {
        this.width = width;
    }
    
    public Double getHeight() {
        return height;
    }
    
    public void setHeight(Double height) {
        this.height = height;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Double getConfidence() {
        return confidence;
    }

    public void setConfidence(Double confidence) {
        this.confidence = confidence;
    }

    public HemangiomaDiagnosis getHemangiomaDiagnosis() {
        return hemangiomaDiagnosis;
    }

    public void setHemangiomaDiagnosis(HemangiomaDiagnosis hemangiomaDiagnosis) {
        this.hemangiomaDiagnosis = hemangiomaDiagnosis;
        // 同步设置hemangioma_id字段，保持向后兼容
        if (hemangiomaDiagnosis != null) {
            this.hemangioma_id = hemangiomaDiagnosis.getId().longValue();
        }
    }
    
    // 为老版本兼容添加hemangioma_id的getter和setter
    public Long getHemangioma_id() {
        return hemangioma_id;
    }
    
    public void setHemangioma_id(Long hemangioma_id) {
        this.hemangioma_id = hemangioma_id;
    }
    
    // 为老版本兼容添加createdBy的getter和setter
    public Long getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }
    
    // 添加metadata_id的getter和setter
    public Integer getMetadata_id() {
        return metadata_id;
    }
    
    public void setMetadata_id(Integer metadata_id) {
        this.metadata_id = metadata_id;
    }
} 