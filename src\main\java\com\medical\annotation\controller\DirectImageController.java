package com.medical.annotation.controller;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import com.medical.annotation.service.FileService;
import com.medical.annotation.util.ImageUtilService;

@RestController
public class DirectImageController {

    @Autowired
    private FileService fileService;
    
    @Autowired
    private ImageUtilService imageUtilService;

    /**
     * 直接从文件系统提供图片，绕过复杂的路径处理和数据库查询
     */
    @GetMapping("/direct-image/{filename:.+}")
    public ResponseEntity<Resource> getDirectImage(@PathVariable String filename) {
        try {
            System.out.println("直接获取图片: " + filename);
            
            // 固定路径，直接从已知的位置读取文件
            String imagePath = fileService.getTempDirectoryPath() + File.separator + filename;
            File file = new File(imagePath);
            
            System.out.println("检查文件路径: " + file.getAbsolutePath());
            System.out.println("文件是否存在: " + file.exists() + ", 大小: " + (file.exists() ? file.length() : 0));
            
            if (file.exists()) {
                byte[] data = Files.readAllBytes(file.toPath());
                System.out.println("成功读取图片数据，大小: " + data.length + " 字节");
                
                ByteArrayResource resource = new ByteArrayResource(data);
                String contentType;
                try {
                    contentType = Files.probeContentType(file.toPath());
                    if (contentType == null) {
                        contentType = "image/jpeg";  // 默认为JPEG
                    }
                } catch (Exception e) {
                    contentType = "image/jpeg";  // 出错时默认为JPEG
                }
                
                System.out.println("返回图片，内容类型: " + contentType);
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .contentLength(data.length)
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                        .body(resource);
            } else {
                System.out.println("文件不存在: " + imagePath);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.err.println("获取图片时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 测试用的图片获取接口，返回临时目录中的第一个JPEG图片
     */
    @GetMapping("/test/image")
    public ResponseEntity<Resource> getTestImage() {
        try {
            System.out.println("测试获取图片");
            
            // 使用FileService获取临时目录路径，而不是硬编码路径
            String tempDir = fileService.getTempDirectoryPath();
            File dir = new File(tempDir);
            
            // 获取临时目录下的第一个jpg图片作为测试图片
            File[] imageFiles = dir.listFiles((d, name) -> name.toLowerCase().endsWith(".jpg"));
            
            if (imageFiles == null || imageFiles.length == 0) {
                System.out.println("临时目录中没有找到jpg图片: " + tempDir);
                return ResponseEntity.notFound().build();
            }
            
            File file = imageFiles[0]; // 使用找到的第一个图片
            System.out.println("测试图片路径: " + file.getAbsolutePath());
            System.out.println("文件是否存在: " + file.exists() + ", 大小: " + (file.exists() ? file.length() : 0));
            
            if (file.exists()) {
                // 尝试直接读取字节数据
                byte[] data = Files.readAllBytes(file.toPath());
                System.out.println("成功读取图片数据，大小: " + data.length + " 字节");
                
                ByteArrayResource resource = new ByteArrayResource(data);
                
                return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_JPEG)
                        .contentLength(data.length)
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"test.jpg\"")
                        .body(resource);
            } else {
                System.out.println("文件不存在: " + file.getAbsolutePath());
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.err.println("测试获取图片时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 简单的健康检查接口
     */
    @GetMapping("/test/simple")
    public String getSimpleResponse() {
        return "测试成功 - 简单文本响应";
    }
    
    /**
     * 从系统路径加载图片（用于访问非标准位置的图片）
     */
    @GetMapping("/image/system-path")
    public ResponseEntity<Resource> getImageBySystemPath(@RequestParam("path") String imagePath) {
        try {
            System.out.println("从系统路径加载图片: " + imagePath);
            
            // 进行路径安全检查 - 防止任意文件访问
            if (imagePath.contains("..") || !imagePath.matches(".*\\.(jpg|jpeg|png|gif|bmp)$")) {
                return ResponseEntity.badRequest().body(null);
            }
            
            File file = new File(imagePath);
            if (!file.exists() || !file.isFile()) {
                return ResponseEntity.notFound().build();
            }
            
            // 使用FileSystemResource避免将整个文件加载到内存
            FileSystemResource resource = new FileSystemResource(file);
            
            // 尝试确定内容类型
            String contentType;
            try {
                contentType = Files.probeContentType(file.toPath());
                if (contentType == null) {
                    // 根据文件扩展名猜测内容类型
                    String fileName = file.getName().toLowerCase();
                    if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
                        contentType = "image/jpeg";
                    } else if (fileName.endsWith(".png")) {
                        contentType = "image/png";
                    } else if (fileName.endsWith(".gif")) {
                        contentType = "image/gif";
                    } else if (fileName.endsWith(".bmp")) {
                        contentType = "image/bmp";
                    } else {
                        contentType = "application/octet-stream";
                    }
                }
            } catch (Exception e) {
                contentType = "application/octet-stream";
            }
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .contentLength(file.length())
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                    .body(resource);
        } catch (Exception e) {
            System.err.println("从系统路径加载图片失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }
} 