/**
 * 格式化日期
 * @param {string|Date} date - 日期字符串或Date对象
 * @param {boolean} withTime - 是否包含时间
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, withTime = false) {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  
  const dateStr = `${year}-${month}-${day}`;
  
  if (withTime) {
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');
    
    return `${dateStr} ${hours}:${minutes}:${seconds}`;
  }
  
  return dateStr;
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
}

/**
 * 将数值转换为百分比格式
 * @param {number} value - 0-1之间的小数值
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的百分比
 */
export function toPercentage(value, decimals = 1) {
  if (typeof value !== 'number') return '';
  
  return (value * 100).toFixed(decimals) + '%';
}

/**
 * 截断文本
 * @param {string} text - 原始文本
 * @param {number} maxLength - 最大长度
 * @returns {string} 截断后的文本
 */
export function truncateText(text, maxLength = 30) {
  if (!text || typeof text !== 'string' || text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + '...';
} 