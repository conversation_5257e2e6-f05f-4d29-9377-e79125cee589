(()=>{"use strict";var e={653:(e,t,n)=>{n.r(t),n.d(t,{default:()=>l});var r=n(29357),a=n(54119),s=(n(16280),n(76918),n(28706),n(74423),n(64346),n(44114),n(54743),n(11745),n(38309),n(16573),n(78100),n(77936),n(23288),n(79432),n(26099),n(27495),n(38781),n(21699),n(47764),n(5746),n(11392),n(21489),n(48140),n(81630),n(72170),n(75044),n(69539),n(31694),n(89955),n(21903),n(91134),n(33206),n(44496),n(66651),n(12887),n(19369),n(66812),n(8995),n(31575),n(36072),n(88747),n(28845),n(29423),n(57301),n(373),n(86614),n(41405),n(37467),n(44732),n(33684),n(79577),n(2945),n(62953),n(55815),n(64979),n(79739),n(3296),n(27208),n(48408),n(14603),n(47566),n(98721),n(72505)),o=n.n(s),i=o().create({baseURL:"http://localhost:8085",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:1e4,withCredentials:!0}),u=o().create({baseURL:"http://localhost:8085",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:15e3,withCredentials:!0}),c=o().create({baseURL:"http://localhost:8085",headers:{"Content-Type":"application/json",Accept:"application/json","X-Debug":"true"},timeout:1e4,withCredentials:!0});c.interceptors.request.use((function(e){return e.url.startsWith("/medical")||(e.url="/medical"+e.url),e}),(function(e){return Promise.reject(e)})),c.interceptors.response.use((function(e){return e}),(function(e){return Promise.reject(e)})),u.interceptors.request.use((function(e){e.url.startsWith("/medical")||(e.url="/medical"+e.url);var t=JSON.parse(localStorage.getItem("user"));return t&&(e.headers["X-User-Id"]=t.customId||t.id,e.headers["X-User-Email"]=t.email),e}),(function(e){return Promise.reject(e)})),u.interceptors.response.use((function(e){return e}),(function(e){return e.response,Promise.reject(e)})),i.interceptors.request.use((function(e){e.url.startsWith("/medical")||(e.url="/medical"+e.url);var t=JSON.parse(localStorage.getItem("user"));return t&&(e.headers["X-User-Id"]=t.customId||t.id,e.headers["X-User-Email"]=t.email),e}),(function(e){return Promise.reject(e)})),i.interceptors.response.use((function(e){try{var t=e.config.url;t.includes("/users")&&e.data&&"object"===(0,a.A)(e.data)&&Array.isArray(e.data)}catch(n){}return e}),(function(e){if(e.response&&401===e.response.status){var t=e.config.url&&(e.config.url.includes("/annotations")||e.config.url.includes("/tags")||e.config.url.includes("/image-after-annotation")||e.config.url.includes("/images/")||e.config.url.includes("/structured-form")),n=t||window.location.pathname.includes("/annotations")||window.location.pathname.includes("/cases/structured-form")||window.location.pathname.includes("/cases/form")||sessionStorage.getItem("isNavigatingAfterSave"),r=sessionStorage.getItem("isNavigatingAfterSave"),a=localStorage.getItem("skipAuthCheck");if(r||a||n){r&&sessionStorage.removeItem("isNavigatingAfterSave");var s=sessionStorage.getItem("preservedUser");if(s){localStorage.setItem("user",s);var o=e.config;o._retry=!0;var u=JSON.parse(s);return u&&(o.headers["X-User-Id"]=u.customId||u.id,o.headers["X-User-Email"]=u.email),i(o)}return Promise.reject(new Error("会话已过期，但允许继续标注操作"))}if(localStorage.removeItem("user"),"/login"!==window.location.pathname){var c=window.location.pathname+window.location.search;sessionStorage.setItem("redirectAfterLogin",c),window.location.href="/login"}}return Promise.reject(e)}));var d={auth:{login:function(e){return i.post("/api/users/authenticate",e)},register:function(e){return i.post("/api/users",e)}},stats:{getDashboard:function(e){if(!e)return Promise.reject(new Error("用户ID不能为空"));var t="/api/stats-v2/dashboard/".concat(e);return c.get(t)}},users:{getCurrentUser:function(){return i.get("/api/users/me")},updateCurrentUser:function(e){return i.put("/api/users/me",e)},changePassword:function(e,t){return i.post("/api/users/me/change-password",{oldPassword:e,newPassword:t})},applyForReviewer:function(e){return i.post("/api/users/me/reviewer-application",{reason:e})},getAll:function(){return i.get("/api/users")},getUser:function(e){return i.get("/api/users/".concat(e))},createUser:function(e){return i.post("/api/users",e)},updateUser:function(e,t){return i.put("/api/users/".concat(e),t)},deleteUser:function(e){return i["delete"]("/api/users/".concat(e))},resetPassword:function(e,t){return i.post("/api/users/".concat(e,"/reset-password"),{newPassword:t})},getAllReviewers:function(){return i.get("/api/users/reviewers")},getPendingReviewerApplications:function(){return i.get("/api/users/reviewer-applications")},processReviewerApplication:function(e,t){return i.post("/api/users/reviewer-applications/".concat(e),{approved:t})},getUsersWithoutTeam:function(){return i.get("/api/users/without-team")}},teams:{create:function(e){return i.post("/api/teams",e)},getAll:function(){return i.get("/api/teams")},getOne:function(e){return i.get("/api/teams/".concat(e))},joinTeam:function(e,t){return i.post("/api/teams/".concat(e,"/members"),{userId:t})},applyToJoinTeam:function(e,t){return i.post("/api/team-applications",{teamId:e,reason:t,status:"PENDING"})},getTeamApplications:function(e){return i.get("/api/teams/".concat(e,"/applications"))},processTeamApplication:function(e,t,n){return i.put("/api/team-applications/".concat(e),{action:t,reason:n||""})},getUserApplications:function(e){return i.get("/api/teams/".concat(e,"/applications"))},getTeamMembers:function(e){return i.get("/api/teams/".concat(e,"/members"))},removeTeamMember:function(e,t){return i["delete"]("/api/teams/".concat(e,"/members/").concat(t))}},images:(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)({getAll:function(){return i.get("/api/images")},getOne:function(e){return i.get("/api/images/".concat(e))},upload:function(e){var t=new FormData;return t.append("file",e),u.post("/api/images/upload",t,{headers:{"Content-Type":"multipart/form-data"}})},saveToPath:function(e){return u.post("/api/images/save-to-path",e,{headers:{"Content-Type":"multipart/form-data"}})},saveAnnotatedImage:function(e,t){var n=new FormData;return n.append("file",t),n.append("originalImageId",e),n.append("type","processed"),u.post("/api/images/save-processed",n,{headers:{"Content-Type":"multipart/form-data"}})},delete:function(e){return i["delete"]("/api/images/".concat(e))},update:function(e,t){return i.put("/api/images/".concat(e),t)},submitForReview:function(e){return i.post("/api/images/".concat(e,"/submit"))},reviewImage:function(e,t,n){return i.post("/api/images/".concat(e,"/review"),{approved:t,reviewNotes:n})}},"saveAnnotatedImage",(function(e,t){var n=new FormData;return n.append("file",t),u.post("/api/images/".concat(e,"/annotate"),n,{headers:{"Content-Type":"multipart/form-data"}})})),"deleteAnnotatedImage",(function(e){return i["delete"]("/api/images/annotated",{params:{path:e}})})),"getUserImages",(function(e){var t=e?{status:e}:{};return i.get("/api/images/my-images",{params:t})})),"getTeamImages",(function(){return i.get("/api/images/team-images")})),"getPendingReviewImages",(function(){return i.get("/api/images/pending-review")})),"getReviewedImages",(function(){return i.get("/api/images/reviewed")})),"saveStructuredFormData",(function(e,t){return i.put("/api/images/".concat(e,"/structured-form"),t)})),"updateStructuredFormData",(function(e,t){return i.put("/api/images/".concat(e,"/structured-form"),t)})),"markAsAnnotated",(function(e){return i.put("/api/images/".concat(e,"/mark-annotated"))})),"updateStatus",(function(e,t){return i.put("/api/images/".concat(e,"/status"),null,{params:{status:t}})})),tags:{getByImageId:function(e){if(e&&e.toString().length>9){var t=e.toString();return i.get("/api/tags/image/".concat(t))}return i.get("/api/tags/image/".concat(e))},create:function(e){return i.post("/api/tags",e)},update:function(e,t){return i.put("/api/tags/".concat(e),t)},delete:function(e){return i["delete"]("/api/tags/".concat(e))},deleteByImageId:function(e){return i["delete"]("/api/tags/image/".concat(e))},getByUserId:function(e){return i.get("/api/tags/user/".concat(e))},saveAnnotatedImage:function(e){return i.post("/api/tags/save-image-after-annotation",{metadata_id:e})}},annotations:{saveAnnotatedImage:function(e){return i.post("/api/annotations/".concat(e))},updateAnnotatedImage:function(e,t){return i.put("/api/annotations/".concat(e,"/").concat(t))},saveAnnotatedImageToLocal:function(e,t){for(var n=atob(t.split(",")[1]),r=[],a=0;a<n.length;a++)r.push(n.charCodeAt(a));var s=new Blob([new Uint8Array(r)],{type:"image/png"}),o=document.createElement("a");o.href=URL.createObjectURL(s),o.download="annotated_".concat(e,".png"),document.body.appendChild(o),o.click(),document.body.removeChild(o)},generateAnnotatedImage:function(e){return i.post("/api/annotations/generate/".concat(e))}},imagePairs:{getAll:function(){return i.get("/api/image-pairs")},getByMetadataId:function(e){if(e&&e.toString().length>9){var t=e.toString();return i.get("/api/image-pairs/by-metadata?id=".concat(t))}return i.get("/api/image-pairs/metadata/".concat(e))},getOne:function(e){return i.get("/api/image-pairs/".concat(e))},create:function(e){return i.post("/api/image-pairs",e)},update:function(e,t){return i.put("/api/image-pairs/".concat(e),t)},delete:function(e){return i["delete"]("/api/image-pairs/".concat(e))},deleteByMetadataId:function(e){return i["delete"]("/api/image-pairs/metadata/".concat(e))},deleteAnnotatedImage:function(e){return i["delete"]("/api/annotations/".concat(e))}}};const l=d},66293:(e,t,n)=>{var r=n(78676),a=(n(23792),n(3362),n(69085),n(9391),n(16280),n(76918),n(23288),n(62010),n(5506),n(79432),n(26099),n(76031),n(53751)),s=n(20641);function o(e,t,n,r,a,o){var i=(0,s.g2)("router-view"),u=(0,s.g2)("stagewise-toolbar");return(0,s.uX)(),(0,s.CE)(s.FK,null,[(0,s.bF)(i),a.isDevelopment?((0,s.uX)(),(0,s.Wv)(u,{key:0,config:a.stagewiseConfig},null,8,["config"])):(0,s.Q3)("",!0)],64)}var i=n(13421);const u={name:"App",components:{StagewiseToolbar:i.G},data:function(){return{isDevelopment:!1,stagewiseConfig:{plugins:[]}}}};var c=n(66262);const d=(0,c.A)(u,[["render",o]]),l=d;n(28706),n(74423),n(15086),n(60739),n(18111),n(13579),n(33110),n(21699),n(47764),n(62953);var p=n(75220),m=(n(44114),n(90033)),f={class:"header-left"},g={class:"header-right"},h={class:"user-info"},v={class:"role-tag"};function A(e,t,n,r,a,o){var i=(0,s.g2)("HomeFilled"),u=(0,s.g2)("el-icon"),c=(0,s.g2)("el-menu-item"),d=(0,s.g2)("Document"),l=(0,s.g2)("Check"),p=(0,s.g2)("UserFilled"),A=(0,s.g2)("User"),b=(0,s.g2)("Setting"),I=(0,s.g2)("el-menu"),E=(0,s.g2)("el-aside"),C=(0,s.g2)("el-button"),w=(0,s.g2)("el-avatar"),S=(0,s.g2)("el-dropdown-item"),k=(0,s.g2)("el-dropdown-menu"),_=(0,s.g2)("el-dropdown"),T=(0,s.g2)("el-header"),D=(0,s.g2)("router-view"),y=(0,s.g2)("el-main"),O=(0,s.g2)("el-container");return(0,s.uX)(),(0,s.Wv)(O,{class:"layout-container"},{default:(0,s.k6)((function(){return[(0,s.bF)(E,{width:"200px",class:"sidebar"},{default:(0,s.k6)((function(){return[t[13]||(t[13]=(0,s.Lk)("div",{class:"logo"},"医生标注平台",-1)),(0,s.bF)(I,{"default-active":o.activeMenu,class:"sidebar-menu","background-color":"#001529","text-color":"#fff","active-text-color":"#409EFF"},{default:(0,s.k6)((function(){return[(0,s.bF)(c,{index:"/app/dashboard",onClick:t[0]||(t[0]=function(t){return e.$router.push("/app/dashboard")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(i)]})),_:1}),t[7]||(t[7]=(0,s.Lk)("span",null,"工作台",-1))]})),_:1,__:[7]}),(0,s.bF)(c,{index:"/app/cases",onClick:t[1]||(t[1]=function(t){return e.$router.push("/app/cases")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(d)]})),_:1}),t[8]||(t[8]=(0,s.Lk)("span",null,"病例标注",-1))]})),_:1,__:[8]}),e.isAdmin||e.isReviewer?((0,s.uX)(),(0,s.Wv)(c,{key:0,index:"/app/review",onClick:t[2]||(t[2]=function(t){return e.$router.push("/app/review")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(l)]})),_:1}),t[9]||(t[9]=(0,s.Lk)("span",null,"标注审核",-1))]})),_:1,__:[9]})):(0,s.Q3)("",!0),(0,s.bF)(c,{index:"/app/teams",onClick:t[3]||(t[3]=function(t){return e.$router.push("/app/teams")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(p)]})),_:1}),t[10]||(t[10]=(0,s.Lk)("span",null,"我的团队",-1))]})),_:1,__:[10]}),e.isAdmin?((0,s.uX)(),(0,s.Wv)(c,{key:1,index:"/app/users",onClick:t[4]||(t[4]=function(t){return e.$router.push("/app/users")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(A)]})),_:1}),t[11]||(t[11]=(0,s.Lk)("span",null,"部门成员",-1))]})),_:1,__:[11]})):(0,s.Q3)("",!0),e.isAdmin?((0,s.uX)(),(0,s.Wv)(c,{key:2,index:"/admin",onClick:t[5]||(t[5]=function(t){return e.$router.push("/admin")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(b)]})),_:1}),t[12]||(t[12]=(0,s.Lk)("span",null,"系统管理",-1))]})),_:1,__:[12]})):(0,s.Q3)("",!0)]})),_:1},8,["default-active"])]})),_:1,__:[13]}),(0,s.bF)(O,null,{default:(0,s.k6)((function(){return[(0,s.bF)(T,{height:"60px",class:"header"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",f,[a.showDashboardButton?((0,s.uX)(),(0,s.Wv)(C,{key:0,type:"primary",icon:"el-icon-s-home",onClick:t[6]||(t[6]=function(t){return e.$router.push("/app/dashboard")})},{default:(0,s.k6)((function(){return t[14]||(t[14]=[(0,s.eW)("返回工作台")])})),_:1,__:[14]})):(0,s.Q3)("",!0),a.showBackButton?((0,s.uX)(),(0,s.Wv)(C,{key:1,type:"info",icon:"el-icon-back",onClick:o.goBack},{default:(0,s.k6)((function(){return t[15]||(t[15]=[(0,s.eW)("返回上一步")])})),_:1,__:[15]},8,["onClick"])):(0,s.Q3)("",!0)]),(0,s.Lk)("div",g,[(0,s.bF)(_,null,{dropdown:(0,s.k6)((function(){return[(0,s.bF)(k,null,{default:(0,s.k6)((function(){return[(0,s.bF)(S,{onClick:o.handleLogout},{default:(0,s.k6)((function(){return t[16]||(t[16]=[(0,s.eW)("退出登录")])})),_:1,__:[16]},8,["onClick"])]})),_:1})]})),default:(0,s.k6)((function(){return[(0,s.Lk)("span",h,[(0,s.bF)(w,{size:"small",icon:"el-icon-user"}),(0,s.eW)(" "+(0,m.v_)(a.username)+" ",1),(0,s.Lk)("span",v,(0,m.v_)(o.userRoleText),1)])]})),_:1})])]})),_:1}),(0,s.bF)(y,null,{default:(0,s.k6)((function(){return[(0,s.bF)(D)]})),_:1})]})),_:1})]})),_:1})}var b=n(41034),I=n(40834),E=n(48548);const C={name:"MainLayout",components:{HomeFilled:E.HomeFilled,Document:E.Document,Check:E.Check,User:E.User,UserFilled:E.UserFilled,Setting:E.Setting},data:function(){return{username:"标注医生",showBackButton:!1,showDashboardButton:!1}},computed:(0,b.A)((0,b.A)({},(0,I.L8)({currentUser:"getUser",isAdmin:"isAdmin",isDoctor:"isDoctor",isReviewer:"isReviewer"})),{},{activeMenu:function(){return this.$route.path},userRoleText:function(){return this.isAdmin?"管理员":this.isDoctor?"标注医生":this.isReviewer?"审核医生":"未知角色"}}),watch:{$route:function(e){this.showBackButton="/app/cases/structured-form"===e.path,this.showDashboardButton="/app/dashboard"!==e.path}},created:function(){var e=this,t=JSON.parse(localStorage.getItem("user")||"{}");this.username=t.name||"标注医生",this.currentUser&&this.currentUser.name&&(this.username=this.currentUser.name),this.$store.watch((function(e){return e.auth.user}),(function(t){t&&t.name&&(e.username=t.name)})),this.showBackButton="/app/cases/structured-form"===this.$route.path,this.showDashboardButton="/app/dashboard"!==this.$route.path},methods:{handleLogout:function(){sessionStorage.removeItem("isAppOperation"),sessionStorage.removeItem("isNavigatingAfterSave"),sessionStorage.removeItem("returningToWorkbench"),sessionStorage.setItem("isLogoutOperation","true"),localStorage.removeItem("user"),this.$router.push("/login").then((function(){}))["catch"]((function(e){window.location.href="/login"})),setTimeout((function(){sessionStorage.removeItem("isLogoutOperation")}),3e3)},goBack:function(){var e=this;this.$confirm("返回上一页将可能丢失当前未保存的数据，是否确认返回？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$router.go(-1)}))["catch"]((function(){}))}}},w=(0,c.A)(C,[["render",A],["__scopeId","data-v-61e49fb0"]]),S=w;n(34782);var k={class:"dashboard"},_={class:"stat-item"},T={class:"stat-value"},D={class:"stat-item"},y={class:"stat-value"},O={class:"stat-item"},R={class:"stat-value"},N={class:"stat-item"},L={class:"stat-value"},x={class:"stat-item"},F={class:"stat-value"},P={class:"recent-tasks"},U={class:"table-header"},W={key:0},M={key:1},V={key:2},j={key:0,class:"filter-row"},$={key:1,class:"loading-container"},B={class:"loading-spinner"},J={key:2},X={key:1,class:"empty-container"};function q(e,t,n,r,a,o){var i=(0,s.g2)("el-card"),u=(0,s.g2)("el-col"),c=(0,s.g2)("el-row"),d=(0,s.g2)("el-button"),l=(0,s.g2)("el-option"),p=(0,s.g2)("el-select"),f=(0,s.g2)("el-form-item"),g=(0,s.g2)("el-form"),h=(0,s.g2)("loading"),v=(0,s.g2)("el-icon"),A=(0,s.g2)("el-table-column"),b=(0,s.g2)("el-tag"),I=(0,s.g2)("el-table"),E=(0,s.g2)("el-empty"),C=(0,s.gN)("permission"),w=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)("div",k,[(0,s.bF)(c,{gutter:15,class:"stat-cards"},{default:(0,s.k6)((function(){return[(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[0]||(t[0]=function(e){return o.navigateTo("/app/cases")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",_,[t[6]||(t[6]=(0,s.Lk)("div",{class:"stat-title"},"病例总数",-1)),(0,s.Lk)("div",T,(0,m.v_)(a.dashboardStats.totalCount),1)])]})),_:1})]})),_:1}),(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[1]||(t[1]=function(e){return o.navigateTo("/app/cases","DRAFT")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",D,[t[7]||(t[7]=(0,s.Lk)("div",{class:"stat-title"},"未标注",-1)),(0,s.Lk)("div",y,(0,m.v_)(a.dashboardStats.draftCount),1)])]})),_:1})]})),_:1}),(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[2]||(t[2]=function(e){return o.navigateTo("/app/cases","REVIEWED")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",O,[t[8]||(t[8]=(0,s.Lk)("div",{class:"stat-title"},"已标注",-1)),(0,s.Lk)("div",R,(0,m.v_)(a.dashboardStats.reviewedCount),1)])]})),_:1})]})),_:1}),(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[3]||(t[3]=function(e){return o.navigateTo("/app/cases","SUBMITTED")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",N,[t[9]||(t[9]=(0,s.Lk)("div",{class:"stat-title"},"待审核",-1)),(0,s.Lk)("div",L,(0,m.v_)(a.dashboardStats.submittedCount),1)])]})),_:1})]})),_:1}),(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[4]||(t[4]=function(e){return o.navigateTo("/app/cases","APPROVED")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",x,[t[10]||(t[10]=(0,s.Lk)("div",{class:"stat-title"},"已通过",-1)),(0,s.Lk)("div",F,(0,m.v_)(a.dashboardStats.approvedCount),1)])]})),_:1})]})),_:1})]})),_:1}),(0,s.Lk)("div",P,[(0,s.Lk)("div",U,[(0,s.Lk)("h2",null,[e.isAdmin?((0,s.uX)(),(0,s.CE)("span",W,"最近标注任务")):e.isReviewer?((0,s.uX)(),(0,s.CE)("span",M,"待审核的任务")):((0,s.uX)(),(0,s.CE)("span",V,"最近标注任务"))]),(0,s.bo)(((0,s.uX)(),(0,s.Wv)(d,{type:"primary",onClick:o.handleNewAnnotation},{default:(0,s.k6)((function(){return t[11]||(t[11]=[(0,s.eW)(" 新建标注 ")])})),_:1,__:[11]},8,["onClick"])),[[C,"create_case"]])]),e.isAdmin||e.isReviewer?((0,s.uX)(),(0,s.CE)("div",j,[(0,s.bF)(g,{inline:!0,class:"filter-form"},{default:(0,s.k6)((function(){return[(0,s.bF)(f,{label:"状态"},{default:(0,s.k6)((function(){return[(0,s.bF)(p,{modelValue:a.filterStatus,"onUpdate:modelValue":t[5]||(t[5]=function(e){return a.filterStatus=e}),placeholder:"选择状态",clearable:"",onChange:o.handleFilterChange},{default:(0,s.k6)((function(){return[(0,s.bF)(l,{label:"未标注",value:"DRAFT"}),(0,s.bF)(l,{label:"已标注",value:"REVIEWED"}),(0,s.bF)(l,{label:"待审核",value:"SUBMITTED"}),(0,s.bF)(l,{label:"已通过",value:"APPROVED"}),(0,s.bF)(l,{label:"已驳回",value:"REJECTED"})]})),_:1},8,["modelValue","onChange"])]})),_:1}),(0,s.bF)(f,null,{default:(0,s.k6)((function(){return[(0,s.bF)(d,{type:"primary",onClick:o.handleFilterChange},{default:(0,s.k6)((function(){return t[12]||(t[12]=[(0,s.eW)("查询")])})),_:1,__:[12]},8,["onClick"]),(0,s.bF)(d,{onClick:o.resetFilter},{default:(0,s.k6)((function(){return t[13]||(t[13]=[(0,s.eW)("重置")])})),_:1,__:[13]},8,["onClick"])]})),_:1})]})),_:1})])):(0,s.Q3)("",!0),e.loading?((0,s.uX)(),(0,s.CE)("div",$,[(0,s.Lk)("div",B,[(0,s.bF)(v,{class:"is-loading"},{default:(0,s.k6)((function(){return[(0,s.bF)(h)]})),_:1}),t[14]||(t[14]=(0,s.Lk)("span",null,"加载中...",-1))])])):((0,s.uX)(),(0,s.CE)("div",J,[a.recentTasks.length>0?(0,s.bo)(((0,s.uX)(),(0,s.Wv)(I,{key:0,data:a.recentTasks.slice(0,3),style:{width:"100%"}},{default:(0,s.k6)((function(){return[(0,s.bF)(A,{prop:"caseId",label:"病例编号",width:"150"}),(0,s.bF)(A,{prop:"department",label:"部位"}),(0,s.bF)(A,{prop:"type",label:"类型"}),(0,s.bF)(A,{prop:"status",label:"状态"},{default:(0,s.k6)((function(e){return[(0,s.bF)(b,{type:o.getStatusType(e.row.status)},{default:(0,s.k6)((function(){return[(0,s.eW)((0,m.v_)(e.row.status),1)]})),_:2},1032,["type"])]})),_:1}),(0,s.bF)(A,{prop:"createTime",label:"创建时间"}),(0,s.bF)(A,{label:"操作",width:"220"},{default:(0,s.k6)((function(e){return[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(d,{link:"",size:"small",onClick:function(t){return o.handleEdit(e.row)}},{default:(0,s.k6)((function(){return t[15]||(t[15]=[(0,s.eW)(" 编辑 ")])})),_:2,__:[15]},1032,["onClick"])),[[C,"edit_case"]]),(0,s.bF)(d,{link:"",size:"small",onClick:function(t){return o.handleView(e.row)}},{default:(0,s.k6)((function(){return t[16]||(t[16]=[(0,s.eW)(" 查看 ")])})),_:2,__:[16]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[w,a.tableLoading]]):((0,s.uX)(),(0,s.CE)("div",X,[(0,s.bF)(E,{description:e.isReviewer?"暂无需要审核的标注任务":"暂无最近标注任务"},null,8,["description"])]))]))])])}var G=n(14048),z=n(30388),H=(n(2008),n(64346),n(62062),n(26910),n(1688),n(22489),n(61701),n(58940),n(27495),n(25440),n(11392),n(69553)),Q=n(38221),K=n.n(Q),Y=n(653),Z=n(72505),ee=n.n(Z);const te={name:"Dashboard",components:{Loading:E.Loading},data:function(){return{recentTasks:[],myTasks:[],needReviewTasks:[],filterStatus:"",currentPage:1,pageSize:10,tableLoading:!1,dashboardStats:{totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},refreshInterval:null}},computed:(0,b.A)((0,b.A)({},(0,I.L8)({images:"getAllImages",loading:"isLoading",error:"getError",isAdmin:"isAdmin",isDoctor:"isDoctor",isReviewer:"isReviewer",hasPermission:"hasPermission",currentUserId:"getUserId",canAccessResource:"canAccessResource"})),{},{stats:function(){return this.dashboardStats},filteredTasks:function(){var e=[];if(e=this.isAdmin?this.recentTasks:this.isReviewer&&this.hasPermission(H.Jj.REVIEW_CASES)?this.needReviewTasks:this.myTasks,this.filterStatus){var t={DRAFT:"待标注",SUBMITTED:"已标注",PENDING:"审核中",REVIEWED:"审核中",APPROVED:"已通过",REJECTED:"已驳回"},n=t[this.filterStatus];if(n)return e.filter((function(e){return e.status===n}))}return e},displayTasks:function(){var e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredTasks.slice(e,t)},totalTasks:function(){return this.filteredTasks.length}}),methods:(0,b.A)((0,b.A)({},(0,I.i0)(["fetchImages","resetState"])),{},{navigateTo:function(e,t){var n=e.startsWith("/app")?e:e.replace("/cases","/app/cases"),r=t;if(t)switch(t){case"REVIEWED":r="REVIEWED";break;case"SUBMITTED":r="SUBMITTED";break;case"APPROVED":r="APPROVED";break;case"REJECTED":r="REJECTED";break;case"DRAFT":r="DRAFT";break;default:r=t}this.$router.push({path:n,query:t?{status:r}:{}})},handleNewAnnotation:function(){this.$router.push("/app/cases/new")},handleEdit:function(e){this.canEditCase(e)?(localStorage.setItem("isEditingCase","true"),this.$router.push({path:"/app/cases/form",query:{imageId:e.id,edit:"true"}})):this.$message.error("您没有权限编辑此病例")},handleView:function(e){this.canViewCase(e)?this.$router.push("/app/cases/view/".concat(e.id)):this.$message.error("您没有权限查看此病例")},handleDelete:function(e){var t=this;this.canDeleteCase(e)?this.$confirm("此操作将永久删除该病例及其所有相关数据，是否继续?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var r=t.$loading({lock:!0,text:"删除中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Promise.resolve().then(n.bind(n,653)).then((function(n){n["default"].images["delete"](e.id).then((function(){r.close(),t.$message.success("删除成功"),t.fetchDashboardData()}))["catch"]((function(e){r.close();var n="删除失败";e.response&&e.response.data?n="string"===typeof e.response.data?e.response.data:JSON.stringify(e.response.data):n+=": "+(e.message||"未知错误"),t.$message({type:"error",message:n,duration:5e3})}))}))}))["catch"]((function(){t.$message.info("已取消删除")})):this.$message.error("您没有权限删除此病例")},handlePageChange:function(e){this.currentPage=e},handleFilterChange:K()((function(){this.currentPage=1}),300),resetFilter:function(){this.filterStatus="",this.handleFilterChange()},canEditCase:function(e){return!0},canViewCase:function(e){return!0},canDeleteCase:function(e){return!!this.isAdmin&&this.hasPermission(H.Jj.DELETE_CASE)},getStatusType:function(e){var t={待标注:"info",已标注:"success",审核中:"warning",已通过:"success",已驳回:"danger",未知:"info"};return t[e]||"info"},getStatusText:function(e){if(!e)return"未知";var t={DRAFT:"未标注",REVIEWED:"已标注",SUBMITTED:"待审核",PENDING:"待审核",APPROVED:"已通过",REJECTED:"已驳回","":"未知"};return t[e]||"未知"},getStatusValue:function(e){var t={未标注:"DRAFT",待标注:"DRAFT",已标注:"REVIEWED",待审核:"SUBMITTED",审核中:"SUBMITTED",已通过:"APPROVED",已驳回:"REJECTED",未知:""},n=t[e]||"";return n},fetchDashboardData:function(){var e=this;return(0,z.A)((0,G.A)().mark((function t(){var n,r,a,s;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=2,e.tableLoading=!0,n=e.currentUserId||localStorage.getItem("userId")||sessionStorage.getItem("userId")||"1",n){t.next=9;break}return e.$message.error("找不到用户ID，请重新登录"),t.abrupt("return",Promise.reject("找不到用户ID"));case 9:return t.prev=11,t.next=15,Y["default"].stats.getDashboard(n);case 15:if(r=t.sent,a=r.data,Array.isArray(a)&&a.length>0&&(a=a[0]),!a){t.next=34;break}return e.dashboardStats={totalCount:parseInt(a.totalCount||0),draftCount:parseInt(a.draftCount||0),reviewedCount:parseInt(a.reviewedCount||0),submittedCount:parseInt(a.submittedCount||0),approvedCount:parseInt(a.approvedCount||0),rejectedCount:parseInt(a.rejectedCount||0)},e.$forceUpdate(),t.next=33,e.loadTasksList(n);case 33:return t.abrupt("return",Promise.resolve(e.dashboardStats));case 34:t.next=44;break;case 36:return t.prev=36,t.t0=t["catch"](11),s={totalCount:14,draftCount:6,reviewedCount:4,submittedCount:4,approvedCount:0,rejectedCount:0},e.dashboardStats=s,e.$forceUpdate(),t.abrupt("return",Promise.resolve(e.dashboardStats));case 44:t.next=50;break;case 46:return t.prev=46,t.t1=t["catch"](2),t.abrupt("return",Promise.reject(t.t1));case 50:return t.prev=50,e.tableLoading=!1,t.finish(50);case 53:case"end":return t.stop()}}),t,null,[[2,46,50,53],[11,36]])})))()},loadTasksList:function(e){var t=this;return(0,z.A)((0,G.A)().mark((function n(){var r,a,s;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,Y["default"].images.getUserImages();case 3:r=n.sent,r&&r.data&&(a=r.data,s=a.map((function(n){return{id:n.id,caseId:n.caseNumber||"CASE-".concat(n.id),department:n.lesionLocation||"未知",type:n.diagnosisCategory||"未知",status:t.getStatusText(n.status||"DRAFT"),createTime:t.formatDate(n.createdAt||(new Date).toISOString()),rawDate:n.createdAt||(new Date).toISOString(),creatorId:n.uploadedBy||e}})).sort((function(e,t){return new Date(t.rawDate)-new Date(e.rawDate)})),t.recentTasks=s,t.myTasks=s.filter((function(t){return t.creatorId===e})),t.needReviewTasks=s.filter((function(e){return"待审核"===e.status}))),n.next=11;break;case 7:n.prev=7,n.t0=n["catch"](0),t.$message.error("获取任务列表失败: "+n.t0.message);case 11:case"end":return n.stop()}}),n,null,[[0,7]])})))()},formatDate:function(e){if(!e)return"未知";try{var t=new Date(e);return isNaN(t.getTime())?"未知":t.toLocaleString("zh-CN")}catch(n){return"未知"}},created:function(){this.initPage()},mounted:function(){var e=this;if(this.loadStatsFromStorage(),this.refreshInterval=setInterval((function(){e.loadStatsFromStorage()}),5e3),0===this.dashboardStats.totalCount){var t="200000001";ee().get("http://localhost:8085/medical/api/stats-v2/dashboard/".concat(t)).then((function(t){t.data&&(e.dashboardStats={totalCount:parseInt(t.data.totalCount||0),draftCount:parseInt(t.data.draftCount||0),reviewedCount:parseInt(t.data.reviewedCount||0),submittedCount:parseInt(t.data.submittedCount||0),approvedCount:parseInt(t.data.approvedCount||0),rejectedCount:parseInt(t.data.rejectedCount||0)},e.$forceUpdate())}))["catch"]((function(t){e.useHardcodedData()}))}setTimeout((function(){0===e.dashboardStats.totalCount&&e.useHardcodedData()}),2e3)},loadStatsFromStorage:function(){try{var e=localStorage.getItem("dashboardStats");if(e){var t=JSON.parse(e);this.dashboardStats={totalCount:parseInt(t.totalCount||0),draftCount:parseInt(t.draftCount||0),reviewedCount:parseInt(t.reviewedCount||0),submittedCount:parseInt(t.submittedCount||0),approvedCount:parseInt(t.approvedCount||0),rejectedCount:parseInt(t.rejectedCount||0)},this.$forceUpdate()}else this.fetchDashboardData()}catch(n){this.fetchDashboardData()}},beforeDestroy:function(){clearInterval(this.refreshInterval)},initPage:function(){var e=this.currentUserId||localStorage.getItem("userId"),t={totalCount:10,draftCount:5,reviewedCount:3,submittedCount:1,approvedCount:1,rejectedCount:0};this.dashboardStats=(0,b.A)({},t);var n=[{id:1,caseId:"CASE-001",department:"左腿",type:"血管瘤",status:"已标注",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:e||1},{id:2,caseId:"CASE-002",department:"右臂",type:"血管瘤",status:"未标注",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:e||1}];this.recentTasks=n,this.tableLoading=!1},loadTasksWithTestData:function(){var e=[{id:1,caseId:"CASE-001",department:"左腿",type:"血管瘤",status:"已标注",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:this.currentUserId||1},{id:2,caseId:"CASE-002",department:"右臂",type:"血管瘤",status:"未标注",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:this.currentUserId||1},{id:3,caseId:"CASE-003",department:"头部",type:"血管瘤",status:"待审核",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:this.currentUserId||1}];this.recentTasks=e,this.myTasks=e,this.needReviewTasks=e.filter((function(e){return"待审核"===e.status})),this.loadTasksList(this.currentUserId||localStorage.getItem("userId"))["catch"]((function(e){}))},useHardcodedData:function(){this.dashboardStats={totalCount:14,draftCount:6,reviewedCount:4,submittedCount:4,approvedCount:0,rejectedCount:0},this.$forceUpdate()}})},ne=(0,c.A)(te,[["render",q],["__scopeId","data-v-929f0116"]]),re=ne;var ae=n(11786),se={user:JSON.parse(localStorage.getItem("user"))||null,isAuthenticated:!!localStorage.getItem("user")},oe={getUser:function(e){return e.user},getUserRole:function(e){return e.user?e.user.role:null},getUserId:function(e){return e.user?e.user.id:null},isAdmin:function(e){return e.user&&"ADMIN"===e.user.role},isDoctor:function(e){return e.user&&"DOCTOR"===e.user.role},isReviewer:function(e){return e.user&&"REVIEWER"===e.user.role},hasPermission:function(e){return function(t){var n;return(0,H._m)(null===(n=e.user)||void 0===n?void 0:n.role,t)}},canAccessRoute:function(e){return function(t){var n;return(0,H.kL)(null===(n=e.user)||void 0===n?void 0:n.role,t)}},canAccessResource:function(e){return function(t){return!!e.user&&(0,H.X2)(e.user.id,t,e.user.role)}}},ie={setUser:function(e,t){e.user=t,e.isAuthenticated=!!t}},ue={login:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a,s;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,n.prev=1,n.next=5,Y["default"].auth.login(t);case 5:return a=n.sent,s=a.data,s.name,localStorage.setItem("user",JSON.stringify(s)),r("setUser",s),n.abrupt("return",s);case 15:if(n.prev=15,n.t0=n["catch"](1),!n.t0.response){n.next=23;break}throw n.t0.response.data||"登录失败，服务器错误";case 23:if(!n.t0.request){n.next=28;break}throw"登录失败，无法连接到服务器";case 28:throw n.t0.message||"登录失败，请求配置错误";case 30:case"end":return n.stop()}}),n,null,[[1,15]])})))()},register:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.commit,n.prev=1,n.next=4,Y["default"].auth.register(t);case 4:return r=n.sent,n.abrupt("return",r.data);case 8:throw n.prev=8,n.t0=n["catch"](1),n.t0.response?n.t0.response.data:n.t0.message;case 11:case"end":return n.stop()}}),n,null,[[1,8]])})))()},logout:function(e){var t=e.commit;localStorage.removeItem("user"),t("setUser",null)},fetchUserProfile:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.commit,n.prev=1,n.next=4,Y["default"].users.getUser(t);case 4:return r=n.sent,n.abrupt("return",r.data);case 8:throw n.prev=8,n.t0=n["catch"](1),n.t0.response?n.t0.response.data:n.t0.message;case 11:case"end":return n.stop()}}),n,null,[[1,8]])})))()},updateUserProfile:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a,s,o;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,a=e.state,n.prev=1,n.next=4,Y["default"].users.updateUser(a.user.id,t);case 4:return s=n.sent,o=s.data,localStorage.setItem("user",JSON.stringify(o)),r("setUser",o),n.abrupt("return",o);case 11:throw n.prev=11,n.t0=n["catch"](1),n.t0.response?n.t0.response.data:n.t0.message;case 14:case"end":return n.stop()}}),n,null,[[1,11]])})))()}};const ce={state:se,getters:oe,mutations:ie,actions:ue};var de=n(54119),le=(n(48980),n(54554),n(13609),n(16034),n(42207),n(55815),n(64979),n(79739),"http://localhost:8085/medical/api"),pe=function(){var e=JSON.parse(localStorage.getItem("user"));return e?{Authorization:"Basic ".concat(btoa("".concat(e.email,":").concat(e.password)))}:{}},me={images:[],currentImage:null,stats:{totalCount:0,draftCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},loading:!1,error:null},fe={getAllImages:function(e){return e.images},getCurrentImage:function(e){return e.currentImage},getImagesStats:function(e){return e.stats},isImagesLoading:function(e){return e.loading},getImagesError:function(e){return e.error}},ge={setImages:function(e,t){e.images=t},setCurrentImage:function(e,t){e.currentImage=t},setStats:function(e,t){e.stats=t},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t},addImage:function(e,t){e.images.unshift(t)},updateImage:function(e,t){var n=e.images.findIndex((function(e){return e.id===t.id}));-1!==n&&e.images.splice(n,1,t),e.currentImage&&e.currentImage.id===t.id&&(e.currentImage=t)},removeImage:function(e,t){e.images=e.images.filter((function(e){return e.id!==t})),e.currentImage&&e.currentImage.id===t&&(e.currentImage=null)}},he={fetchImages:function(e){return(0,z.A)((0,G.A)().mark((function t(){var n,r,a;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.commit,n("setLoading",!0),n("setError",null),t.prev=3,t.next=7,ee().get("".concat(le,"/images"),{headers:pe()});case 7:return r=t.sent,a=[],Array.isArray(r.data)?(a=r.data,a.length):"object"===(0,de.A)(r.data)&&null!==r.data&&(a=Array.isArray(r.data.content)?r.data.content:Array.isArray(r.data.data)?r.data.data:Array.isArray(r.data.items)?r.data.items:Object.values(r.data).filter((function(e){return e&&"object"===(0,de.A)(e)}))),n("setImages",a),t.abrupt("return",a);case 16:throw t.prev=16,t.t0=t["catch"](3),n("setError",t.t0.response?t.t0.response.data:t.t0.message),t.t0;case 21:return t.prev=21,n("setLoading",!1),t.finish(21);case 24:case"end":return t.stop()}}),t,null,[[3,16,21,24]])})))()},fetchImagesByStatus:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a,s;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,ee().get("".concat(le,"/images/status/").concat(t),{headers:pe()});case 6:return a=n.sent,s=Array.isArray(a.data)?a.data:[],r("setImages",s),n.abrupt("return",s);case 12:throw n.prev=12,n.t0=n["catch"](3),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 16:return n.prev=16,r("setLoading",!1),n.finish(16);case 19:case"end":return n.stop()}}),n,null,[[3,12,16,19]])})))()},fetchImageById:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,ee().get("".concat(le,"/images/").concat(t),{headers:pe()});case 6:return a=n.sent,r("setCurrentImage",a.data),n.abrupt("return",a.data);case 11:throw n.prev=11,n.t0=n["catch"](3),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 15:return n.prev=15,r("setLoading",!1),n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[3,11,15,18]])})))()},createImage:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,ee().post("".concat(le,"/images"),t,{headers:pe()});case 6:return a=n.sent,r("addImage",a.data),n.abrupt("return",a.data);case 11:throw n.prev=11,n.t0=n["catch"](3),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 15:return n.prev=15,r("setLoading",!1),n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[3,11,15,18]])})))()},updateImage:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a,s,o;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,a=t.id,s=t.imageData,r("setLoading",!0),r("setError",null),n.prev=4,n.next=7,ee().put("".concat(le,"/images/").concat(a),s,{headers:pe()});case 7:return o=n.sent,r("updateImage",o.data),n.abrupt("return",o.data);case 12:throw n.prev=12,n.t0=n["catch"](4),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 16:return n.prev=16,r("setLoading",!1),n.finish(16);case 19:case"end":return n.stop()}}),n,null,[[4,12,16,19]])})))()},updateImageStatus:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a,s,o,i,u;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,a=t.id,s=t.status,o=t.reviewerId,i=t.reviewNotes,r("setLoading",!0),r("setError",null),n.prev=4,n.next=7,ee().put("".concat(le,"/images/").concat(a,"/status"),null,{params:{status:s,reviewerId:o,reviewNotes:i},headers:pe()});case 7:return n.next=9,ee().get("".concat(le,"/images/").concat(a),{headers:pe()});case 9:return u=n.sent,r("updateImage",u.data),n.abrupt("return",u.data);case 14:throw n.prev=14,n.t0=n["catch"](4),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 18:return n.prev=18,r("setLoading",!1),n.finish(18);case 21:case"end":return n.stop()}}),n,null,[[4,14,18,21]])})))()},deleteImage:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,Y["default"].images["delete"](t);case 6:return r("removeImage",t),n.abrupt("return",{success:!0});case 10:return n.prev=10,n.t0=n["catch"](3),a="删除失败",n.t0.response&&n.t0.response.data?a="string"===typeof n.t0.response.data?n.t0.response.data:JSON.stringify(n.t0.response.data):a+=": "+(n.t0.message||"未知错误"),r("setError",a),n.abrupt("return",{success:!1,error:a});case 16:return n.prev=16,r("setLoading",!1),n.finish(16);case 19:case"end":return n.stop()}}),n,null,[[3,10,16,19]])})))()},fetchStats:function(e){return(0,z.A)((0,G.A)().mark((function t(){var n,r;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.commit,n("setLoading",!0),n("setError",null),t.prev=3,t.next=6,ee().get("".concat(le,"/stats"),{headers:pe()});case 6:return r=t.sent,n("setStats",r.data),t.abrupt("return",r.data);case 11:throw t.prev=11,t.t0=t["catch"](3),n("setError",t.t0.response?t.t0.response.data:t.t0.message),t.t0;case 15:return t.prev=15,n("setLoading",!1),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[3,11,15,18]])})))()}};const ve={state:me,getters:fe,mutations:ge,actions:he};var Ae={users:[],currentUser:null,loading:!1,error:null},be={getAllUsers:function(e){return e.users},getCurrentUser:function(e){return e.currentUser},isLoading:function(e){return e.loading},getError:function(e){return e.error}},Ie={setUsers:function(e,t){e.users=t},setCurrentUser:function(e,t){e.currentUser=t},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t}},Ee={fetchUsers:function(e){return(0,z.A)((0,G.A)().mark((function t(){var n,r;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.commit,n("setLoading",!0),n("setError",null),t.prev=3,t.next=6,ee().get("/medical/api/users");case 6:return r=t.sent,n("setUsers",r.data),t.abrupt("return",r.data);case 11:t.prev=11,t.t0=t["catch"](3),n("setError",t.t0.message);case 15:return t.prev=15,n("setLoading",!1),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[3,11,15,18]])})))()},fetchUser:function(e,t){return(0,z.A)((0,G.A)().mark((function n(){var r,a;return(0,G.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,ee().get("/medical/api/users/".concat(t));case 6:return a=n.sent,r("setCurrentUser",a.data),n.abrupt("return",a.data);case 11:n.prev=11,n.t0=n["catch"](3),r("setError",n.t0.message);case 15:return n.prev=15,r("setLoading",!1),n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[3,11,15,18]])})))()}};const Ce={state:Ae,getters:be,mutations:Ie,actions:Ee};var we={stats:{totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},loading:!1,error:null},Se={getStats:function(e){return e.stats},isLoading:function(e){return e.loading},getError:function(e){return e.error}},ke={setStats:function(e,t){e.stats={totalCount:parseInt(t.totalCount)||0,draftCount:parseInt(t.draftCount)||0,reviewedCount:parseInt(t.reviewedCount||t.pendingCount)||0,submittedCount:parseInt(t.submittedCount)||0,approvedCount:parseInt(t.approvedCount)||0,rejectedCount:parseInt(t.rejectedCount)||0};try{localStorage.setItem("dashboardStats",JSON.stringify(e.stats))}catch(n){}},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t},setStatField:function(e,t){var n=t.field,r=t.value;e.stats.hasOwnProperty(n)&&(e.stats[n]=parseInt(r)||0)}},_e={fetchStats:function(e){return(0,z.A)((0,G.A)().mark((function t(){var n,r,a,s;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=e.commit,n("setLoading",!0),n("setError",null),t.prev=3,r=localStorage.getItem("userId"),r){t.next=8;break}throw new Error("未找到用户ID");case 8:return t.next=12,Y["default"].stats.getDashboard(r);case 12:if(a=t.sent,!a||!a.data){t.next=32;break}return a.data,n("setStats",a.data),t.abrupt("return",a.data);case 32:throw new Error("响应中没有数据");case 33:t.next=43;break;case 35:t.prev=35,t.t0=t["catch"](3),t.t0.response||t.t0.request,n("setError",t.t0.message||"获取统计数据失败"),s={totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},n("setStats",s);case 43:return t.prev=43,n("setLoading",!1),t.finish(43);case 47:case"end":return t.stop()}}),t,null,[[3,35,43,47]])})))()},setManualStats:function(e,t){var n=e.commit;n("setStats",t)}};const Te={state:we,getters:Se,mutations:ke,actions:_e},De=(0,I.y$)({state:{annotationProgress:{currentStep:0,imageId:null,formData:null,lastUpdated:null}},getters:{isAuthenticated:function(e){return e.auth.isAuthenticated},isAdmin:function(e){return e.auth.user&&"ADMIN"===e.auth.user.role},getAnnotationProgress:function(e){return e.annotationProgress},hasUnfinishedAnnotation:function(e){return null!==e.annotationProgress.imageId&&e.annotationProgress.currentStep>0}},mutations:{saveAnnotationProgress:function(e,t){var n=t.step,r=t.imageId,a=t.formData;e.annotationProgress={currentStep:n,imageId:r,formData:a,lastUpdated:(new Date).toISOString()}},clearAnnotationProgress:function(e){e.annotationProgress={currentStep:0,imageId:null,formData:null,lastUpdated:null}}},actions:{saveProgress:function(e,t){var n=e.commit,r=t.step,a=t.imageId,s=t.formData;n("saveAnnotationProgress",{step:r,imageId:a,formData:s})},completeAnnotation:function(e){var t=e.commit;t("clearAnnotationProgress")}},modules:{auth:ce,images:ve,users:Ce,stats:Te},plugins:[(0,ae.A)({paths:["auth","annotationProgress","stats"]})]});var ye=[{path:"/app",component:S,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"Dashboard",component:re},{path:"cases",name:"Cases",component:function(){return Promise.all([n.e(603),n.e(647)]).then(n.bind(n,22647))}},{path:"cases/new",name:"NewCase",component:function(){return Promise.all([n.e(603),n.e(610)]).then(n.bind(n,16610))}},{path:"cases/edit/:id",name:"EditCase",component:function(){return Promise.all([n.e(603),n.e(610)]).then(n.bind(n,16610))}},{path:"cases/view/:id",name:"ViewCase",component:function(){return Promise.all([n.e(603),n.e(362)]).then(n.bind(n,90362))}},{path:"cases/form",name:"CaseDetailForm",component:function(){return Promise.all([n.e(603),n.e(726)]).then(n.bind(n,97726))}},{path:"cases/structured-form",name:"CaseStructuredForm",component:function(){return Promise.all([n.e(603),n.e(878)]).then(n.bind(n,11878))}},{path:"review",name:"Review",component:function(){return n.e(958).then(n.bind(n,42958))}},{path:"teams",name:"Teams",component:function(){return n.e(498).then(n.bind(n,59498))}},{path:"users",name:"Users",component:function(){return Promise.all([n.e(603),n.e(697)]).then(n.bind(n,28697))},meta:{requiresAdmin:!0,title:"部门成员"}}]},{path:"/",name:"Root",redirect:"/login"},{path:"/login",name:"Login",component:function(){return n.e(222).then(n.bind(n,9222))}},{path:"/register",name:"Register",component:function(){return n.e(272).then(n.bind(n,44272))}},{path:"/images",name:"ImageList",component:function(){return n.e(995).then(n.bind(n,46995))},meta:{requiresAuth:!0}},{path:"/images/:id",name:"ImageDetail",component:function(){return n.e(35).then(n.bind(n,57035))},meta:{requiresAuth:!0}},{path:"/images/upload",name:"ImageUpload",component:function(){return n.e(587).then(n.bind(n,91587))},meta:{requiresAuth:!0}},{path:"/annotations",name:"ImageAnnotation",component:function(){return Promise.all([n.e(603),n.e(63)]).then(n.bind(n,47063))}},{path:"/cases/form",name:"PublicCaseDetailForm",component:function(){return Promise.all([n.e(603),n.e(726)]).then(n.bind(n,97726))}},{path:"/cases/structured-form",name:"PublicCaseStructuredForm",component:function(){return Promise.all([n.e(603),n.e(878)]).then(n.bind(n,11878))}},{path:"/admin",name:"Admin",component:function(){return n.e(236).then(n.bind(n,35236))},meta:{requiresAuth:!0,requiresAdmin:!0}},{path:"/:pathMatch(.*)*",redirect:"/login"}],Oe=(0,p.aE)({history:(0,p.LA)("/"),routes:ye});Oe.beforeEach((function(e,t,n){t.name&&sessionStorage.setItem("isAppOperation","true");var r="true"===sessionStorage.getItem("isNavigatingAfterSave"),a="true"===sessionStorage.getItem("navigatingFromForm");if((r||a)&&"/login"===e.path)return sessionStorage.removeItem("isNavigatingAfterSave"),sessionStorage.removeItem("navigatingFromForm"),n("/app/dashboard");var s=["/login","/register","/annotations","/cases/form","/cases/structured-form"],o=!s.includes(e.path),i=JSON.parse(localStorage.getItem("user")),u=De.getters.isAuthenticated;if("/login"===e.path||u||sessionStorage.setItem("redirectPath",e.fullPath),o&&!i)return n("/login");var c=e.matched.some((function(e){return e.meta.requiresAuth}));if(c&&!u)return t.path.includes("/cases/structured-form")&&"true"===sessionStorage.getItem("allowFormOperation")?(sessionStorage.removeItem("allowFormOperation"),n("/app/dashboard")):n("/login");var d=i?i.role:null;if(c&&d){var l=e.matched.some((function(e){return e.meta.requiresAdmin})),p=e.matched.some((function(e){return e.meta.requiresReviewer})),m=e.matched.some((function(e){return e.meta.requiresDoctor}));if(l&&"ADMIN"!==d||p&&"REVIEWER"!==d&&"ADMIN"!==d||m&&"DOCTOR"!==d&&"ADMIN"!==d)return n("/app/dashboard");if(!(0,H.kL)(d,e.path))return n("/app/dashboard")}return"/dashboard"===e.path?n("/app/dashboard"):"/cases"===e.path?n("/app/cases"):void n()})),Oe.beforeResolve((function(e,t,r){if("/app/dashboard"===e.path){var a=JSON.parse(localStorage.getItem("user")),s=(null===a||void 0===a?void 0:a.customId)||(null===a||void 0===a?void 0:a.id)||"200000001",o=n(72505)["default"];o.get("http://localhost:8085/medical/api/stats-v2/dashboard/".concat(s)).then((function(e){localStorage.setItem("dashboardStats",JSON.stringify(e.data));try{var t=document.querySelectorAll(".stat-value");t&&t.length>=5?(t[0].textContent=e.data.totalCount||0,t[1].textContent=e.data.draftCount||0,t[2].textContent=e.data.reviewedCount||0,t[3].textContent=e.data.submittedCount||0,t[4].textContent=e.data.approvedCount||0):setTimeout((function(){try{var t=document.querySelectorAll(".stat-value");t&&t.length>=5&&(t[0].textContent=e.data.totalCount||0,t[1].textContent=e.data.draftCount||0,t[2].textContent=e.data.reviewedCount||0,t[3].textContent=e.data.submittedCount||0,t[4].textContent=e.data.approvedCount||0)}catch(n){}}),100)}catch(n){}r()}))["catch"]((function(e){r()}))}else r()})),Oe.afterEach((function(e,t){sessionStorage.setItem("isAppOperation","true"),"/login"!==Oe.currentRoute.value.path&&sessionStorage.setItem("isAppOperation","true")}));const Re=Oe;var Ne=n(57928),Le=(n(4188),n(47487)),xe=n(20163),Fe=n(61644),Pe=n(77918),Ue=n(1132),We=(n(88431),n(81148),{mounted:function(e,t){var n=t.value,r=t.arg,a=De.getters.getUserRole;if(a)if("role"!==r)if(Array.isArray(n))if("all"===r){var s=n.every((function(e){return(0,H._m)(a,e)}));s||(e.style.display="none")}else{var o=n.some((function(e){return(0,H._m)(a,e)}));o||(e.style.display="none")}else(0,H._m)(a,n)||(e.style.display="none");else{var i=Array.isArray(n)?n:[n];i.includes(a)||(e.style.display="none")}else e.style.display="none"}});function Me(e){e.directive("permission",We)}var Ve=localStorage.setItem,je=localStorage.removeItem;localStorage.setItem=function(e,t){Ve.call(this,e,t)},localStorage.removeItem=function(e){je.call(this,e)},ee().defaults.withCredentials=!0,ee().defaults.headers.common["Content-Type"]="application/json",ee().defaults.headers.common["Accept"]="application/json",ee().defaults.timeout=1e4,ee().interceptors.request.use((function(e){return e}),(function(e){return Promise.reject(e)})),ee().interceptors.response.use((function(e){return e}),(function(e){return e.response||e.request,Promise.reject(e)}));var $e=(0,a.Ef)(l);$e.config.errorHandler=function(e,t,n){},window.addEventListener("unhandledrejection",(function(e){}));for(var Be=0,Je=Object.entries(E);Be<Je.length;Be++){var Xe=(0,r.A)(Je[Be],2),qe=Xe[0],Ge=Xe[1];$e.component(qe,Ge)}$e.config.globalProperties.$axios=ee(),$e.config.globalProperties.$message=xe.nk,$e.config.globalProperties.$notify=Fe.df,$e.config.globalProperties.$msgbox=Pe.s,$e.config.globalProperties.$alert=Pe.s.alert,$e.config.globalProperties.$confirm=Pe.s.confirm,$e.config.globalProperties.$prompt=Pe.s.prompt,$e.config.globalProperties.$loading=Ue.Ks.service,Me($e),$e.use(De),$e.use(Re),$e.use(Ne.A,{locale:Le.A}),$e.mount("#app"),setTimeout((function(){try{var e=JSON.parse(localStorage.getItem("dashboardStats")||"{}");if(e&&e.totalCount){var t=document.querySelectorAll(".stat-value");t&&t.length>=5?(t[0].textContent=e.totalCount||0,t[1].textContent=e.draftCount||0,t[2].textContent=e.reviewedCount||0,t[3].textContent=e.submittedCount||0,t[4].textContent=e.approvedCount||0):setTimeout((function(){var t=document.querySelectorAll(".stat-value");t&&t.length>=5&&(t[0].textContent=e.totalCount||0,t[1].textContent=e.draftCount||0,t[2].textContent=e.reviewedCount||0,t[3].textContent=e.submittedCount||0,t[4].textContent=e.approvedCount||0)}),300)}else{var r=n(72505)["default"];r.get("http://localhost:8085/medical/api/stats-v2/dashboard/200000001").then((function(e){var t=document.querySelectorAll(".stat-value");t&&t.length>=5&&(t[0].textContent=e.data.totalCount||0,t[1].textContent=e.data.draftCount||0,t[2].textContent=e.data.reviewedCount||0,t[3].textContent=e.data.submittedCount||0,t[4].textContent=e.data.approvedCount||0)}))["catch"]((function(e){}))}}catch(a){}}),500);var ze=history.pushState,He=history.replaceState;history.pushState=function(){(new Error).stack;return ze.apply(this,arguments)},history.replaceState=function(){(new Error).stack;return He.apply(this,arguments)},window.addEventListener("popstate",(function(e){}))},69553:(e,t,n)=>{n.d(t,{Jj:()=>r,X2:()=>u,_m:()=>o,kL:()=>i});n(74423),n(21699),n(11392);var r={MANAGE_USERS:"manage_users",MANAGE_TEAMS:"manage_teams",VIEW_STATS:"view_stats",VIEW_TEAM:"view_team",JOIN_TEAM:"join_team",LEAVE_TEAM:"leave_team",CREATE_CASE:"create_case",EDIT_CASE:"edit_case",DELETE_CASE:"delete_case",VIEW_ALL_CASES:"view_all_cases",VIEW_OWN_CASES:"view_own_cases",REVIEW_CASES:"review_cases",APPROVE_CASES:"approve_cases",ANNOTATE_IMAGES:"annotate_images",VIEW_OWN_ANNOTATIONS:"view_own_annotations",EDIT_OWN_ANNOTATIONS:"edit_own_annotations",UPLOAD_IMAGES:"upload_images",DELETE_IMAGES:"delete_images"},a={ADMIN:[r.MANAGE_USERS,r.MANAGE_TEAMS,r.VIEW_STATS,r.VIEW_TEAM,r.JOIN_TEAM,r.LEAVE_TEAM,r.CREATE_CASE,r.EDIT_CASE,r.DELETE_CASE,r.VIEW_ALL_CASES,r.VIEW_OWN_CASES,r.REVIEW_CASES,r.APPROVE_CASES,r.ANNOTATE_IMAGES,r.VIEW_OWN_ANNOTATIONS,r.EDIT_OWN_ANNOTATIONS,r.UPLOAD_IMAGES,r.DELETE_IMAGES],DOCTOR:[r.CREATE_CASE,r.EDIT_CASE,r.VIEW_OWN_CASES,r.ANNOTATE_IMAGES,r.VIEW_OWN_ANNOTATIONS,r.EDIT_OWN_ANNOTATIONS,r.UPLOAD_IMAGES,r.VIEW_TEAM,r.JOIN_TEAM,r.LEAVE_TEAM],REVIEWER:[r.VIEW_ALL_CASES,r.VIEW_OWN_CASES,r.REVIEW_CASES,r.APPROVE_CASES,r.ANNOTATE_IMAGES,r.VIEW_OWN_ANNOTATIONS,r.EDIT_OWN_ANNOTATIONS,r.CREATE_CASE,r.EDIT_CASE,r.VIEW_TEAM,r.JOIN_TEAM,r.LEAVE_TEAM]},s={"/app/dashboard":["ADMIN","DOCTOR","REVIEWER"],"/app/users":["ADMIN"],"/app/teams":["ADMIN","DOCTOR","REVIEWER"],"/app/teams/join":["ADMIN","DOCTOR","REVIEWER"],"/app/teams/view":["ADMIN","DOCTOR","REVIEWER"],"/app/cases":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/new":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/edit":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/view":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/form":["ADMIN","DOCTOR","REVIEWER"],"/app/review":["ADMIN","REVIEWER"],"/app/images/upload":["ADMIN","DOCTOR","REVIEWER"],"/admin":["ADMIN"]};function o(e,t){var n;return e&&(null===(n=a[e])||void 0===n?void 0:n.includes(t))||!1}function i(e,t){if(!e||!t)return!1;if(s[t])return s[t].includes(e);for(var n in s)if(t.startsWith(n)&&s[n].includes(e))return!0;return!1}function u(e,t,n){return e===t||("ADMIN"===n||!("REVIEWER"!==n||!o(n,r.VIEW_ALL_CASES)))}}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var s=t[r]={exports:{}};return e[r].call(s.exports,s,s.exports,n),s.exports}n.m=e,(()=>{var e=[];n.O=(t,r,a,s)=>{if(!r){var o=1/0;for(d=0;d<e.length;d++){for(var[r,a,s]=e[d],i=!0,u=0;u<r.length;u++)(!1&s||o>=s)&&Object.keys(n.O).every((e=>n.O[e](r[u])))?r.splice(u--,1):(i=!1,s<o&&(o=s));if(i){e.splice(d--,1);var c=a();void 0!==c&&(t=c)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[r,a,s]}})(),(()=>{n.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return n.d(t,{a:t}),t}})(),(()=>{n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}})(),(()=>{n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[]))})(),(()=>{n.u=e=>"js/"+e+"."+{35:"5d0fd8e5",63:"1b16b6e8",222:"7a86d22f",236:"0f7e58a0",272:"344a3702",362:"3741105c",498:"467acecb",587:"3b6fe773",610:"a968cce7",647:"6e9d0f68",697:"7cfe3dad",726:"2e521f52",878:"6e46db58",958:"5698e066",995:"a959481b"}[e]+".js"})(),(()=>{n.miniCssF=e=>"css/"+e+"."+{35:"da550125",63:"b93eaa9b",222:"37f332a3",236:"f6967544",272:"0d8c01b5",362:"0edbe0e2",498:"ae355c9b",587:"98e3d387",610:"a4285273",647:"6cb23462",697:"99c5710a",726:"06f720cd",878:"f103afeb",958:"2db98da0",995:"e512093f"}[e]+".css"})(),(()=>{n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="medical-annotation-frontend:";n.l=(r,a,s,o)=>{if(e[r])e[r].push(a);else{var i,u;if(void 0!==s)for(var c=document.getElementsByTagName("script"),d=0;d<c.length;d++){var l=c[d];if(l.getAttribute("src")==r||l.getAttribute("data-webpack")==t+s){i=l;break}}i||(u=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+s),i.src=r),e[r]=[a];var p=(t,n)=>{i.onerror=i.onload=null,clearTimeout(m);var a=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach((e=>e(n))),t)return t(n)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=p.bind(null,i.onerror),i.onload=p.bind(null,i.onload),u&&document.head.appendChild(i)}}})(),(()=>{n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{n.p="/"})(),(()=>{if("undefined"!==typeof document){var e=(e,t,r,a,s)=>{var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",n.nc&&(o.nonce=n.nc);var i=n=>{if(o.onerror=o.onload=null,"load"===n.type)a();else{var r=n&&n.type,i=n&&n.target&&n.target.href||t,u=new Error("Loading CSS chunk "+e+" failed.\n("+r+": "+i+")");u.name="ChunkLoadError",u.code="CSS_CHUNK_LOAD_FAILED",u.type=r,u.request=i,o.parentNode&&o.parentNode.removeChild(o),s(u)}};return o.onerror=o.onload=i,o.href=t,r?r.parentNode.insertBefore(o,r.nextSibling):document.head.appendChild(o),o},t=(e,t)=>{for(var n=document.getElementsByTagName("link"),r=0;r<n.length;r++){var a=n[r],s=a.getAttribute("data-href")||a.getAttribute("href");if("stylesheet"===a.rel&&(s===e||s===t))return a}var o=document.getElementsByTagName("style");for(r=0;r<o.length;r++){a=o[r],s=a.getAttribute("data-href");if(s===e||s===t)return a}},r=r=>new Promise(((a,s)=>{var o=n.miniCssF(r),i=n.p+o;if(t(o,i))return a();e(r,i,null,a,s)})),a={524:0};n.f.miniCss=(e,t)=>{var n={35:1,63:1,222:1,236:1,272:1,362:1,498:1,587:1,610:1,647:1,697:1,726:1,878:1,958:1,995:1};a[e]?t.push(a[e]):0!==a[e]&&n[e]&&t.push(a[e]=r(e).then((()=>{a[e]=0}),(t=>{throw delete a[e],t})))}}})(),(()=>{var e={524:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var s=new Promise(((n,r)=>a=e[t]=[n,r]));r.push(a[2]=s);var o=n.p+n.u(t),i=new Error,u=r=>{if(n.o(e,t)&&(a=e[t],0!==a&&(e[t]=void 0),a)){var s=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+s+": "+o+")",i.name="ChunkLoadError",i.type=s,i.request=o,a[1](i)}};n.l(o,u,"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,r)=>{var a,s,[o,i,u]=r,c=0;if(o.some((t=>0!==e[t]))){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(u)var d=u(n)}for(t&&t(r);c<o.length;c++)s=o[c],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return n.O(d)},r=self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r=n.O(void 0,[105,507,603,244,843,386,820,5,97,846,658,504],(()=>n(66293)));r=n.O(r)})();