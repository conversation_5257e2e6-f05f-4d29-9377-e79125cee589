@echo off
chcp 65001
echo USE medical_annotations; > fix_path.sql
echo -- 修复特定图像的路径 >> fix_path.sql
echo UPDATE image_pairs >> fix_path.sql
echo SET image_one_path = 'F:/血管瘤辅助系统/medical_images/temp/temp_cdcda5f7-701c-444e-8d31-f20ecb6cdb96_IH_009_027.jpg' >> fix_path.sql
echo WHERE image_one_path LIKE '%%processed_cdcda5f7-701c-444e-8d31-f20ecb6cdb96_IH_009_027.jpg'; >> fix_path.sql
echo. >> fix_path.sql
echo SELECT id, image_one_path >> fix_path.sql
echo FROM image_pairs >> fix_path.sql
echo WHERE image_one_path LIKE '%%cdcda5f7-701c-444e-8d31-f20ecb6cdb96_IH_009_027.jpg'; >> fix_path.sql
echo. >> fix_path.sql

echo 正在运行SQL脚本修复图像路径...
mysql -u root -p medical_annotations < fix_path.sql
echo 完成！ 