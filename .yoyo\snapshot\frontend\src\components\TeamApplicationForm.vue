<template>
  <div class="team-application-form">
    <el-form ref="applicationForm" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="申请团队" prop="teamId">
        <el-select v-model="formData.teamId" placeholder="请选择申请加入的团队">
          <el-option 
            v-for="team in teamList" 
            :key="team.id" 
            :label="team.name" 
            :value="team.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请原因" prop="reason">
        <el-input 
          type="textarea" 
          v-model="formData.reason" 
          placeholder="请简要说明申请加入团队的原因"
          :rows="4"
          maxlength="500"
          show-word-limit>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitApplication" :loading="isSubmitting">提交申请</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import api from '@/utils/api';
import { ElMessage } from 'element-plus';

export default {
  name: 'TeamApplicationForm',
  props: {
    preSelectedTeamId: {
      type: Number,
      default: null
    }
  },
  emits: ['submit-success'],
  
  setup(props, { emit }) {
    const formData = reactive({
      teamId: props.preSelectedTeamId || null,
      reason: ''
    });
    
    const rules = {
      teamId: [{ required: true, message: '请选择团队', trigger: 'change' }],
      reason: [
        { required: true, message: '请填写申请原因', trigger: 'blur' },
        { min: 10, message: '申请原因至少需要10个字符', trigger: 'blur' }
      ]
    };
    
    const teamList = ref([]);
    const applicationForm = ref(null);
    const isSubmitting = ref(false);
    
    // 获取团队列表
    const getTeamList = async () => {
      try {
        const response = await api.teams.getAll();
        teamList.value = response.data;
      } catch (error) {
        console.error('获取团队列表失败:', error);
        ElMessage.error('获取团队列表失败，请稍后重试');
      }
    };
    
    // 提交申请
    const submitApplication = () => {
      applicationForm.value.validate(async (valid) => {
        if (valid) {
          try {
            isSubmitting.value = true;
            // 调用申请加入团队的API
            const response = await api.teams.applyToJoinTeam(
              formData.teamId,
              formData.reason
            );
            
            ElMessage.success('申请提交成功，请等待管理员审核');
            emit('submit-success', response.data);
            resetForm();
          } catch (error) {
            console.error('提交申请失败:', error);
            const errorMsg = error.response?.data?.message || '提交申请失败，请稍后重试';
            ElMessage.error(errorMsg);
          } finally {
            isSubmitting.value = false;
          }
        }
      });
    };
    
    // 重置表单
    const resetForm = () => {
      applicationForm.value.resetFields();
    };
    
    onMounted(() => {
      getTeamList();
    });
    
    return {
      formData,
      rules,
      teamList,
      applicationForm,
      isSubmitting,
      submitApplication,
      resetForm
    };
  }
};
</script>

<style scoped>
.team-application-form {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 