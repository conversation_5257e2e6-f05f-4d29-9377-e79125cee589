package com.medical.annotation.controller;

import com.medical.annotation.model.Team;
import com.medical.annotation.model.User;
import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.service.TeamService;
import com.medical.annotation.service.UserService;
import com.medical.annotation.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/teams")
public class TeamController {

    @Autowired
    private TeamService teamService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 创建团队
     */
    @PostMapping
    public ResponseEntity<?> createTeam(@RequestBody Team team) {
        try {
            Integer userId = null;
            
            // 尝试从SecurityContext获取当前用户
            try {
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                if (auth != null && auth.getName() != null && !auth.getName().equals("anonymousUser")) {
                    User currentUser = userService.getUserByEmail(auth.getName()).orElse(null);
                    if (currentUser != null) {
                        userId = currentUser.getId();
                    }
                }
            } catch (Exception e) {
                System.out.println("无法从认证上下文获取用户: " + e.getMessage());
            }
            
            // 如果无法获取用户ID，使用默认管理员ID
            if (userId == null) {
                // 尝试查找系统中的第一个管理员用户
                List<User> admins = userRepository.findByRole(User.Role.ADMIN);
                if (!admins.isEmpty()) {
                    userId = admins.get(0).getId();
                } else {
                    // 如果没有管理员，使用ID为1的用户作为备选
                    userId = 1;
                }
            }
            
            Team createdTeam = teamService.createTeam(team, userId);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(createdTeam);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 更新团队信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateTeam(@PathVariable("id") Integer id, @RequestBody Team team) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            team.setId(id);
            Team updatedTeam = teamService.updateTeam(team, currentUser.getId());
            
            return ResponseEntity.ok(updatedTeam);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 删除团队
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteTeam(@PathVariable("id") Integer id) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            teamService.deleteTeam(id, currentUser.getId());
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "团队已成功删除");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取团队信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getTeam(@PathVariable("id") Integer id) {
        try {
            Team team = teamService.getTeamById(id);
            return ResponseEntity.ok(team);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取所有团队
     */
    @GetMapping
    public ResponseEntity<?> getAllTeams(@RequestParam(value = "keyword", required = false) String keyword) {
        try {
            List<Team> teams;
            if (keyword != null && !keyword.isEmpty()) {
                teams = teamService.searchTeams(keyword);
            } else {
                teams = teamService.getAllTeams();
            }
            
            // 转换为简单格式
            List<Map<String, Object>> result = teams.stream().map(team -> {
                Map<String, Object> teamMap = new HashMap<>();
                teamMap.put("id", team.getId());
                teamMap.put("name", team.getName());
                teamMap.put("description", team.getDescription());
                return teamMap;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取团队成员
     */
    @GetMapping("/{id}/members")
    public ResponseEntity<?> getTeamMembers(@PathVariable("id") Integer id) {
        try {
            List<User> members = teamService.getTeamMembers(id);
            return ResponseEntity.ok(members);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 添加团队成员（新接口，支持通过请求体传递用户ID）
     */
    @PostMapping("/{id}/members")
    public ResponseEntity<?> addTeamMemberByBody(
            @PathVariable("id") Integer id,
            @RequestBody Map<String, Object> requestBody) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            // 从请求体中获取userId
            Object userIdObj = requestBody.get("userId");
            if (userIdObj == null) {
                throw new Exception("用户ID不能为空");
            }
            
            Integer userId;
            try {
                userId = Integer.valueOf(userIdObj.toString());
            } catch (NumberFormatException e) {
                throw new Exception("无效的用户ID格式");
            }
            
            teamService.addUserToTeam(id, userId, currentUser.getId());
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "用户已成功添加到团队");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 移除团队成员
     */
    @DeleteMapping("/{id}/members/{userId}")
    public ResponseEntity<?> removeTeamMember(@PathVariable("id") Integer id, @PathVariable("userId") Integer userId) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            teamService.removeUserFromTeam(id, userId, currentUser.getId());
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "用户已成功从团队移除");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 搜索团队
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchTeams(@RequestParam("query") String query) {
        try {
            if (query == null || query.trim().isEmpty()) {
                return ResponseEntity.ok(teamService.getAllTeams());
            }
            
            List<Team> teams = teamService.searchTeams(query.trim());
            
            // 转换为前端需要的格式
            List<Map<String, Object>> result = teams.stream().map(team -> {
                Map<String, Object> teamMap = new HashMap<>();
                teamMap.put("id", team.getId());
                teamMap.put("name", team.getName());
                teamMap.put("code", team.getId().toString()); // 使用ID作为团队代码
                teamMap.put("description", team.getDescription());
                teamMap.put("memberCount", userRepository.countByTeam_Id(team.getId()));
                return teamMap;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取团队已通过的标注
     */
    @GetMapping("/{id}/annotations/approved")
    public ResponseEntity<?> getTeamApprovedAnnotations(@PathVariable("id") Integer id) {
        try {
            List<ImageMetadata> approvedAnnotations = teamService.getTeamApprovedAnnotations(id);
            
            System.out.println("获取到已通过标注数量: " + approvedAnnotations.size());
            
            // 转换为包含必要信息的格式
            List<Map<String, Object>> result = approvedAnnotations.stream().map(annotation -> {
                Map<String, Object> item = new HashMap<>();
                item.put("id", annotation.getId());
                
                // 确保病例号不为空
                String caseNumber = annotation.getCaseNumber();
                if (caseNumber == null || caseNumber.trim().isEmpty()) {
                    caseNumber = "CASE-" + annotation.getId();
                }
                item.put("caseNumber", caseNumber);
                
                System.out.println("标注ID: " + annotation.getId() + ", 病例号: " + caseNumber + ", 上传者: " + annotation.getUploadedByName());
                
                item.put("uploadedByName", annotation.getUploadedByName());
                item.put("lesionLocation", annotation.getLesionLocation());
                item.put("diagnosisCategory", annotation.getDiagnosisCategory());
                item.put("formattedReviewDate", annotation.getReviewDate() != null ? 
                        annotation.getReviewDate().toString() : "");
                item.put("imagePath", annotation.getPath());
                item.put("imageTwoPath", annotation.getImageTwoPath());
                
                return item;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
} 