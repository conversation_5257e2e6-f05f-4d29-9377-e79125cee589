/**
 * Element Plus 统一修复模块
 * 
 * 此文件合并了所有Element Plus相关的修复功能：
 * - 解决ES模块加载问题，特别是.mjs文件找不到的问题
 * - 修复缺少的模块路径
 * - 提供通用的tryNextPath函数
 * - 模拟缺失的类型和辅助函数
 */

// 创建通用的tryNextPath函数处理模块路径解析
export function tryNextPath(paths, index) {
  if (!paths || index >= paths.length) {
    console.warn('模块路径解析失败', paths, index);
    return '';
  }
  return paths[index];
}

// 全局注入tryNextPath函数
if (typeof window !== 'undefined') {
  window.tryNextPath = tryNextPath;
}

if (typeof global !== 'undefined') {
  global.tryNextPath = tryNextPath;
}

// 修复Element Plus类型相关的模块
export const isString = (val) => typeof val === 'string';
export const isNumber = (val) => typeof val === 'number';
export const isBoolean = (val) => typeof val === 'boolean';
export const isObject = (val) => val !== null && typeof val === 'object';
export const isFunction = (val) => typeof val === 'function';
export const isArray = Array.isArray;
export const isDate = (val) => val instanceof Date;
export const isPromise = (val) => isObject(val) && isFunction(val.then) && isFunction(val.catch);

// 模拟常用的缺失模块
export const Size = {
  LARGE: 'large',
  DEFAULT: 'default',
  SMALL: 'small',
};

// 模拟运行时props帮助函数
export const buildProp = (prop, key) => prop;
export const buildProps = (props) => props;
export const definePropType = (type) => type;
export const mutable = (val) => val;

// 导出空的token对象，用于替换缺失的模块
export const TOKEN = Symbol('token');
export const TOOLTIP_INJECTION_KEY = Symbol('tooltip');

// 修复缺少的模块路径
const fixElementPlusImports = () => {
  try {
    if (typeof window !== 'undefined') {
      // 创建虚拟的utils模块以避免缺失
      window.__EP_UTILS_TYPES__ = {
        isString,
        isNumber,
        isBoolean,
        isObject,
        isFunction,
      };
      
      // 修复可能缺失的token和hooks
      window.__EP_VIRTUAL_MODULES__ = {};
      
      // 定义一个模块导入拦截器
      const originalImport = window.__webpack_require__ || (() => {});
      if (window.__webpack_require__) {
        window.__webpack_require__ = function(moduleId) {
          try {
            return originalImport(moduleId);
          } catch (e) {
            // 如果是Element Plus内部模块导入错误
            if (e.message && e.message.includes('Cannot find module')) {
              // 返回空对象避免崩溃
              console.warn('Element Plus模块加载失败:', e.message);
              return {};
            }
            throw e;
          }
        };
      }
    }
  } catch (e) {
    console.warn('Element Plus兼容性修复应用失败', e);
  }
};

// 应用修复
fixElementPlusImports();

// 导出统一的修复工具
export default {
  tryNextPath,
  isString,
  isNumber,
  isBoolean,
  isObject,
  isFunction,
  isArray,
  isDate,
  isPromise,
  Size,
  buildProp,
  buildProps,
  definePropType,
  mutable,
  TOKEN,
  TOOLTIP_INJECTION_KEY
}; 