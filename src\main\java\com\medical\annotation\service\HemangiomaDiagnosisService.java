package com.medical.annotation.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@Service
public class HemangiomaDiagnosisService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${ai.service.url:http://localhost:8086}")
    private String aiServiceUrl;

    public HemangiomaDiagnosisService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 调用AI服务进行血管瘤检测
     * @param imageFile 要检测的图像文件
     * @return 检测结果
     */
    public Map<String, Object> detectHemangioma(MultipartFile imageFile) throws IOException {
        // 构建请求URL
        String detectUrl = aiServiceUrl + "/detect";
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", imageFile.getResource());
        
        // 创建请求实体
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.postForEntity(detectUrl, requestEntity, String.class);
        
        // 解析响应
        return objectMapper.readValue(response.getBody(), Map.class);
    }
    
    /**
     * 检查AI服务健康状态
     * @return 健康状态信息
     */
    public boolean isAiServiceHealthy() {
        try {
            String healthUrl = aiServiceUrl + "/health";
            ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            return false;
        }
    }
} 