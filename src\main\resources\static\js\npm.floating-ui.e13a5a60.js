"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[97],{4967:(t,e,n)=>{n.d(e,{UE:()=>Ht,ll:()=>Ct,rD:()=>Wt,__:()=>St,UU:()=>Bt,cY:()=>Pt,BN:()=>Ft});const o=Math.min,i=Math.max,r=Math.round,l=Math.floor,c=t=>({x:t,y:t}),s={left:"right",right:"left",bottom:"top",top:"bottom"},a={start:"end",end:"start"};function f(t,e,n){return i(t,o(e,n))}function u(t,e){return"function"===typeof t?t(e):t}function d(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function p(t){return"y"===t?"height":"width"}function g(t){return["top","bottom"].includes(d(t))?"y":"x"}function y(t){return m(g(t))}function w(t,e,n){void 0===n&&(n=!1);const o=h(t),i=y(t),r=p(i);let l="x"===i?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return e.reference[r]>e.floating[r]&&(l=T(l)),[l,T(l)]}function x(t){const e=T(t);return[v(t),e,v(e)]}function v(t){return t.replace(/start|end/g,(t=>a[t]))}function b(t,e,n){const o=["left","right"],i=["right","left"],r=["top","bottom"],l=["bottom","top"];switch(t){case"top":case"bottom":return n?e?i:o:e?o:i;case"left":case"right":return e?r:l;default:return[]}}function R(t,e,n,o){const i=h(t);let r=b(d(t),"start"===n,o);return i&&(r=r.map((t=>t+"-"+i)),e&&(r=r.concat(r.map(v)))),r}function T(t){return t.replace(/left|right|bottom|top/g,(t=>s[t]))}function L(t){return{top:0,right:0,bottom:0,left:0,...t}}function E(t){return"number"!==typeof t?L(t):{top:t,right:t,bottom:t,left:t}}function A(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}}function D(t,e,n){let{reference:o,floating:i}=t;const r=g(e),l=y(e),c=p(l),s=d(e),a="y"===r,f=o.x+o.width/2-i.width/2,u=o.y+o.height/2-i.height/2,m=o[c]/2-i[c]/2;let w;switch(s){case"top":w={x:f,y:o.y-i.height};break;case"bottom":w={x:f,y:o.y+o.height};break;case"right":w={x:o.x+o.width,y:u};break;case"left":w={x:o.x-i.width,y:u};break;default:w={x:o.x,y:o.y}}switch(h(e)){case"start":w[l]-=m*(n&&a?-1:1);break;case"end":w[l]+=m*(n&&a?-1:1);break}return w}const O=async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:r=[],platform:l}=n,c=r.filter(Boolean),s=await(null==l.isRTL?void 0:l.isRTL(e));let a=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:f,y:u}=D(a,o,s),d=o,h={},m=0;for(let p=0;p<c.length;p++){const{name:n,fn:r}=c[p],{x:g,y,data:w,reset:x}=await r({x:f,y:u,initialPlacement:o,placement:d,strategy:i,middlewareData:h,rects:a,platform:l,elements:{reference:t,floating:e}});f=null!=g?g:f,u=null!=y?y:u,h={...h,[n]:{...h[n],...w}},x&&m<=50&&(m++,"object"===typeof x&&(x.placement&&(d=x.placement),x.rects&&(a=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),({x:f,y:u}=D(a,d,s))),p=-1)}return{x:f,y:u,placement:d,strategy:i,middlewareData:h}};async function k(t,e){var n;void 0===e&&(e={});const{x:o,y:i,platform:r,rects:l,elements:c,strategy:s}=t,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:h=!1,padding:m=0}=u(e,t),p=E(m),g="floating"===d?"reference":"floating",y=c[h?g:d],w=A(await r.getClippingRect({element:null==(n=await(null==r.isElement?void 0:r.isElement(y)))||n?y:y.contextElement||await(null==r.getDocumentElement?void 0:r.getDocumentElement(c.floating)),boundary:a,rootBoundary:f,strategy:s})),x="floating"===d?{x:o,y:i,width:l.floating.width,height:l.floating.height}:l.reference,v=await(null==r.getOffsetParent?void 0:r.getOffsetParent(c.floating)),b=await(null==r.isElement?void 0:r.isElement(v))&&await(null==r.getScale?void 0:r.getScale(v))||{x:1,y:1},R=A(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:x,offsetParent:v,strategy:s}):x);return{top:(w.top-R.top+p.top)/b.y,bottom:(R.bottom-w.bottom+p.bottom)/b.y,left:(w.left-R.left+p.left)/b.x,right:(R.right-w.right+p.right)/b.x}}const C=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:i,placement:r,rects:l,platform:c,elements:s,middlewareData:a}=e,{element:d,padding:m=0}=u(t,e)||{};if(null==d)return{};const g=E(m),w={x:n,y:i},x=y(r),v=p(x),b=await c.getDimensions(d),R="y"===x,T=R?"top":"left",L=R?"bottom":"right",A=R?"clientHeight":"clientWidth",D=l.reference[v]+l.reference[x]-w[x]-l.floating[v],O=w[x]-l.reference[x],k=await(null==c.getOffsetParent?void 0:c.getOffsetParent(d));let C=k?k[A]:0;C&&await(null==c.isElement?void 0:c.isElement(k))||(C=s.floating[A]||l.floating[v]);const S=D/2-O/2,P=C/2-b[v]/2-1,F=o(g[T],P),B=o(g[L],P),H=F,W=C-b[v]-B,V=C/2-b[v]/2+S,M=f(H,V,W),_=!a.arrow&&null!=h(r)&&V!==M&&l.reference[v]/2-(V<H?F:B)-b[v]/2<0,N=_?V<H?V-H:V-W:0;return{[x]:w[x]+N,data:{[x]:M,centerOffset:V-M-N,..._&&{alignmentOffset:N}},reset:_}}});const S=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:i,middlewareData:r,rects:l,initialPlacement:c,platform:s,elements:a}=e,{mainAxis:f=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:v=!0,...b}=u(t,e);if(null!=(n=r.arrow)&&n.alignmentOffset)return{};const L=d(i),E=g(c),A=d(c)===c,D=await(null==s.isRTL?void 0:s.isRTL(a.floating)),O=m||(A||!v?[T(c)]:x(c)),C="none"!==y;!m&&C&&O.push(...R(c,v,y,D));const S=[c,...O],P=await k(e,b),F=[];let B=(null==(o=r.flip)?void 0:o.overflows)||[];if(f&&F.push(P[L]),h){const t=w(i,l,D);F.push(P[t[0]],P[t[1]])}if(B=[...B,{placement:i,overflows:F}],!F.every((t=>t<=0))){var H,W;const t=((null==(H=r.flip)?void 0:H.index)||0)+1,e=S[t];if(e){var V;const n="alignment"===h&&E!==g(e),o=(null==(V=B[0])?void 0:V.overflows[0])>0;if(!n||o)return{data:{index:t,overflows:B},reset:{placement:e}}}let n=null==(W=B.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:W.placement;if(!n)switch(p){case"bestFit":{var M;const t=null==(M=B.filter((t=>{if(C){const e=g(t.placement);return e===E||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:M[0];t&&(n=t);break}case"initialPlacement":n=c;break}if(i!==n)return{reset:{placement:n}}}return{}}}};async function P(t,e){const{placement:n,platform:o,elements:i}=t,r=await(null==o.isRTL?void 0:o.isRTL(i.floating)),l=d(n),c=h(n),s="y"===g(n),a=["left","top"].includes(l)?-1:1,f=r&&s?-1:1,m=u(e,t);let{mainAxis:p,crossAxis:y,alignmentAxis:w}="number"===typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return c&&"number"===typeof w&&(y="end"===c?-1*w:w),s?{x:y*f,y:p*a}:{x:p*a,y:y*f}}const F=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:i,y:r,placement:l,middlewareData:c}=e,s=await P(e,t);return l===(null==(n=c.offset)?void 0:n.placement)&&null!=(o=c.arrow)&&o.alignmentOffset?{}:{x:i+s.x,y:r+s.y,data:{...s,placement:l}}}}},B=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:i}=e,{mainAxis:r=!0,crossAxis:l=!1,limiter:c={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...s}=u(t,e),a={x:n,y:o},h=await k(e,s),p=g(d(i)),y=m(p);let w=a[y],x=a[p];if(r){const t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=w+h[t],o=w-h[e];w=f(n,w,o)}if(l){const t="y"===p?"top":"left",e="y"===p?"bottom":"right",n=x+h[t],o=x-h[e];x=f(n,x,o)}const v=c.fn({...e,[y]:w,[p]:x});return{...v,data:{x:v.x-n,y:v.y-o,enabled:{[y]:r,[p]:l}}}}}};function H(){return"undefined"!==typeof window}function W(t){return _(t)?(t.nodeName||"").toLowerCase():"#document"}function V(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function M(t){var e;return null==(e=(_(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function _(t){return!!H()&&(t instanceof Node||t instanceof V(t).Node)}function N(t){return!!H()&&(t instanceof Element||t instanceof V(t).Element)}function z(t){return!!H()&&(t instanceof HTMLElement||t instanceof V(t).HTMLElement)}function I(t){return!(!H()||"undefined"===typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof V(t).ShadowRoot)}function U(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=J(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function Y(t){return["table","td","th"].includes(W(t))}function j(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(n){return!1}}))}function q(t){const e=$(),n=N(t)?J(t):t;return["transform","translate","scale","rotate","perspective"].some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function X(t){let e=Q(t);while(z(e)&&!G(e)){if(q(e))return e;if(j(e))return null;e=Q(e)}return null}function $(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function G(t){return["html","body","#document"].includes(W(t))}function J(t){return V(t).getComputedStyle(t)}function K(t){return N(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function Q(t){if("html"===W(t))return t;const e=t.assignedSlot||t.parentNode||I(t)&&t.host||M(t);return I(e)?e.host:e}function Z(t){const e=Q(t);return G(e)?t.ownerDocument?t.ownerDocument.body:t.body:z(e)&&U(e)?e:Z(e)}function tt(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=Z(t),r=i===(null==(o=t.ownerDocument)?void 0:o.body),l=V(i);if(r){const t=et(l);return e.concat(l,l.visualViewport||[],U(i)?i:[],t&&n?tt(t):[])}return e.concat(i,tt(i,[],n))}function et(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function nt(t){const e=J(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=z(t),l=i?t.offsetWidth:n,c=i?t.offsetHeight:o,s=r(n)!==l||r(o)!==c;return s&&(n=l,o=c),{width:n,height:o,$:s}}function ot(t){return N(t)?t:t.contextElement}function it(t){const e=ot(t);if(!z(e))return c(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:l}=nt(e);let s=(l?r(n.width):n.width)/o,a=(l?r(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}const rt=c(0);function lt(t){const e=V(t);return $()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:rt}function ct(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==V(t))&&e}function st(t,e,n,o){void 0===e&&(e=!1),void 0===n&&(n=!1);const i=t.getBoundingClientRect(),r=ot(t);let l=c(1);e&&(o?N(o)&&(l=it(o)):l=it(t));const s=ct(r,n,o)?lt(r):c(0);let a=(i.left+s.x)/l.x,f=(i.top+s.y)/l.y,u=i.width/l.x,d=i.height/l.y;if(r){const t=V(r),e=o&&N(o)?V(o):o;let n=t,i=et(n);while(i&&o&&e!==n){const t=it(i),e=i.getBoundingClientRect(),o=J(i),r=e.left+(i.clientLeft+parseFloat(o.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(o.paddingTop))*t.y;a*=t.x,f*=t.y,u*=t.x,d*=t.y,a+=r,f+=l,n=V(i),i=et(n)}}return A({width:u,height:d,x:a,y:f})}function at(t,e){const n=K(t).scrollLeft;return e?e.left+n:st(M(t)).left+n}function ft(t,e,n){void 0===n&&(n=!1);const o=t.getBoundingClientRect(),i=o.left+e.scrollLeft-(n?0:at(t,o)),r=o.top+e.scrollTop;return{x:i,y:r}}function ut(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r="fixed"===i,l=M(o),s=!!e&&j(e.floating);if(o===l||s&&r)return n;let a={scrollLeft:0,scrollTop:0},f=c(1);const u=c(0),d=z(o);if((d||!d&&!r)&&(("body"!==W(o)||U(l))&&(a=K(o)),z(o))){const t=st(o);f=it(o),u.x=t.x+o.clientLeft,u.y=t.y+o.clientTop}const h=!l||d||r?c(0):ft(l,a,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-a.scrollLeft*f.x+u.x+h.x,y:n.y*f.y-a.scrollTop*f.y+u.y+h.y}}function dt(t){return Array.from(t.getClientRects())}function ht(t){const e=M(t),n=K(t),o=t.ownerDocument.body,r=i(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),l=i(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let c=-n.scrollLeft+at(t);const s=-n.scrollTop;return"rtl"===J(o).direction&&(c+=i(e.clientWidth,o.clientWidth)-r),{width:r,height:l,x:c,y:s}}function mt(t,e){const n=V(t),o=M(t),i=n.visualViewport;let r=o.clientWidth,l=o.clientHeight,c=0,s=0;if(i){r=i.width,l=i.height;const t=$();(!t||t&&"fixed"===e)&&(c=i.offsetLeft,s=i.offsetTop)}return{width:r,height:l,x:c,y:s}}function pt(t,e){const n=st(t,!0,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=z(t)?it(t):c(1),l=t.clientWidth*r.x,s=t.clientHeight*r.y,a=i*r.x,f=o*r.y;return{width:l,height:s,x:a,y:f}}function gt(t,e,n){let o;if("viewport"===e)o=mt(t,n);else if("document"===e)o=ht(M(t));else if(N(e))o=pt(e,n);else{const n=lt(t);o={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return A(o)}function yt(t,e){const n=Q(t);return!(n===e||!N(n)||G(n))&&("fixed"===J(n).position||yt(n,e))}function wt(t,e){const n=e.get(t);if(n)return n;let o=tt(t,[],!1).filter((t=>N(t)&&"body"!==W(t))),i=null;const r="fixed"===J(t).position;let l=r?Q(t):t;while(N(l)&&!G(l)){const e=J(l),n=q(l);n||"fixed"!==e.position||(i=null);const c=r?!n&&!i:!n&&"static"===e.position&&!!i&&["absolute","fixed"].includes(i.position)||U(l)&&!n&&yt(t,l);c?o=o.filter((t=>t!==l)):i=e,l=Q(l)}return e.set(t,o),o}function xt(t){let{element:e,boundary:n,rootBoundary:r,strategy:l}=t;const c="clippingAncestors"===n?j(e)?[]:wt(e,this._c):[].concat(n),s=[...c,r],a=s[0],f=s.reduce(((t,n)=>{const r=gt(e,n,l);return t.top=i(r.top,t.top),t.right=o(r.right,t.right),t.bottom=o(r.bottom,t.bottom),t.left=i(r.left,t.left),t}),gt(e,a,l));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function vt(t){const{width:e,height:n}=nt(t);return{width:e,height:n}}function bt(t,e,n){const o=z(e),i=M(e),r="fixed"===n,l=st(t,!0,r,e);let s={scrollLeft:0,scrollTop:0};const a=c(0);function f(){a.x=at(i)}if(o||!o&&!r)if(("body"!==W(e)||U(i))&&(s=K(e)),o){const t=st(e,!0,r,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else i&&f();r&&!o&&i&&f();const u=!i||o||r?c(0):ft(i,s),d=l.left+s.scrollLeft-a.x-u.x,h=l.top+s.scrollTop-a.y-u.y;return{x:d,y:h,width:l.width,height:l.height}}function Rt(t){return"static"===J(t).position}function Tt(t,e){if(!z(t)||"fixed"===J(t).position)return null;if(e)return e(t);let n=t.offsetParent;return M(t)===n&&(n=n.ownerDocument.body),n}function Lt(t,e){const n=V(t);if(j(t))return n;if(!z(t)){let e=Q(t);while(e&&!G(e)){if(N(e)&&!Rt(e))return e;e=Q(e)}return n}let o=Tt(t,e);while(o&&Y(o)&&Rt(o))o=Tt(o,e);return o&&G(o)&&Rt(o)&&!q(o)?n:o||X(t)||n}const Et=async function(t){const e=this.getOffsetParent||Lt,n=this.getDimensions,o=await n(t.floating);return{reference:bt(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function At(t){return"rtl"===J(t).direction}const Dt={convertOffsetParentRelativeRectToViewportRelativeRect:ut,getDocumentElement:M,getClippingRect:xt,getOffsetParent:Lt,getElementRects:Et,getClientRects:dt,getDimensions:vt,getScale:it,isElement:N,isRTL:At};function Ot(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function kt(t,e){let n,r=null;const c=M(t);function s(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}function a(f,u){void 0===f&&(f=!1),void 0===u&&(u=1),s();const d=t.getBoundingClientRect(),{left:h,top:m,width:p,height:g}=d;if(f||e(),!p||!g)return;const y=l(m),w=l(c.clientWidth-(h+p)),x=l(c.clientHeight-(m+g)),v=l(h),b=-y+"px "+-w+"px "+-x+"px "+-v+"px",R={rootMargin:b,threshold:i(0,o(1,u))||1};let T=!0;function L(e){const o=e[0].intersectionRatio;if(o!==u){if(!T)return a();o?a(!1,o):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}1!==o||Ot(d,t.getBoundingClientRect())||a(),T=!1}try{r=new IntersectionObserver(L,{...R,root:c.ownerDocument})}catch(E){r=new IntersectionObserver(L,R)}r.observe(t)}return a(!0),s}function Ct(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:l="function"===typeof ResizeObserver,layoutShift:c="function"===typeof IntersectionObserver,animationFrame:s=!1}=o,a=ot(t),f=i||r?[...a?tt(a):[],...tt(e)]:[];f.forEach((t=>{i&&t.addEventListener("scroll",n,{passive:!0}),r&&t.addEventListener("resize",n)}));const u=a&&c?kt(a,n):null;let d,h=-1,m=null;l&&(m=new ResizeObserver((t=>{let[o]=t;o&&o.target===a&&m&&(m.unobserve(e),cancelAnimationFrame(h),h=requestAnimationFrame((()=>{var t;null==(t=m)||t.observe(e)}))),n()})),a&&!s&&m.observe(a),m.observe(e));let p=s?st(t):null;function g(){const e=st(t);p&&!Ot(p,e)&&n(),p=e,d=requestAnimationFrame(g)}return s&&g(),n(),()=>{var t;f.forEach((t=>{i&&t.removeEventListener("scroll",n),r&&t.removeEventListener("resize",n)})),null==u||u(),null==(t=m)||t.disconnect(),m=null,s&&cancelAnimationFrame(d)}}const St=k,Pt=F,Ft=B,Bt=S,Ht=C,Wt=(t,e,n)=>{const o=new Map,i={platform:Dt,...n},r={...i.platform,_c:o};return O(t,e,{...i,platform:r})}}}]);