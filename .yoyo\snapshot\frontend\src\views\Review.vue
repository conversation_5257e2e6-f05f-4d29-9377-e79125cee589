<template>
  <div class="review-container">
    <div class="page-header">
      <h2>标注审核</h2>
    </div>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="待审核" name="pending">
        <el-table :data="pendingReviews" style="width: 100%" v-loading="loading.pending">
          <el-table-column prop="caseId" label="病例编号" width="180" />
          <el-table-column prop="patientInfo" label="患者信息" width="180" />
          <el-table-column prop="department" label="部位" />
          <el-table-column prop="type" label="类型" />
          <el-table-column prop="createTime" label="标注时间" />
          <el-table-column prop="annotator" label="标注人" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleReview(scope.row)">
                审核
              </el-button>
              <el-button type="info" size="small" @click="handleView(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          layout="prev, pager, next"
          :total="pagination.pending.total"
          :page-size="pagination.pending.pageSize"
          @current-change="(page) => handlePageChange(page, 'pending')"
          class="pagination"
        />
      </el-tab-pane>

      <el-tab-pane label="已审核" name="reviewed">
        <el-table :data="reviewedCases" style="width: 100%" v-loading="loading.reviewed">
          <el-table-column prop="caseId" label="病例编号" width="180" />
          <el-table-column prop="patientInfo" label="患者信息" width="180" />
          <el-table-column prop="department" label="部位" />
          <el-table-column prop="type" label="类型" />
          <el-table-column prop="reviewTime" label="审核时间" />
          <el-table-column prop="reviewer" label="审核人" />
          <el-table-column prop="status" label="审核结果">
            <template #default="scope">
              <el-tag :type="scope.row.status === '已通过' ? 'success' : 'danger'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="info" size="small" @click="handleView(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          layout="prev, pager, next"
          :total="pagination.reviewed.total"
          :page-size="pagination.reviewed.pageSize"
          @current-change="(page) => handlePageChange(page, 'reviewed')"
          class="pagination"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 审核对话框 -->
    <el-dialog
      title="标注审核"
      :visible.sync="reviewDialog.visible"
      width="500px"
      @close="closeReviewDialog"
    >
      <div v-if="reviewDialog.case" class="review-dialog-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="病例编号">{{ reviewDialog.case.caseId }}</el-descriptions-item>
          <el-descriptions-item label="患者信息">{{ reviewDialog.case.patientInfo }}</el-descriptions-item>
          <el-descriptions-item label="部位">{{ reviewDialog.case.department }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ reviewDialog.case.type }}</el-descriptions-item>
          <el-descriptions-item label="标注人">{{ reviewDialog.case.annotator }}</el-descriptions-item>
          <el-descriptions-item label="标注时间">{{ reviewDialog.case.createTime }}</el-descriptions-item>
        </el-descriptions>

        <el-form :model="reviewDialog.form" label-width="80px" class="review-form">
          <el-form-item label="审核结果">
            <el-radio-group v-model="reviewDialog.form.result">
              <el-radio label="approve">通过</el-radio>
              <el-radio label="reject">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="reviewDialog.form.comment"
              :rows="3"
              placeholder="请输入审核意见（选填）"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeReviewDialog">取消</el-button>
          <el-button type="primary" @click="submitReview">提交审核</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import api from '../utils/api'

export default {
  name: 'Review',
  data() {
    return {
      activeTab: 'pending',
      loading: {
        pending: false,
        reviewed: false
      },
      pendingReviews: [],
      reviewedCases: [],
      pagination: {
        pending: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        reviewed: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        }
      },
      reviewDialog: {
        visible: false,
        case: null,
        form: {
          result: 'approve',
          comment: ''
        }
      }
    }
  },
  created() {
    this.fetchPendingReviews()
  },
  watch: {
    activeTab(val) {
      if (val === 'pending') {
        this.fetchPendingReviews()
      } else if (val === 'reviewed') {
        this.fetchReviewedCases()
      }
    }
  },
  methods: {
    fetchPendingReviews() {
      this.loading.pending = true
      // TODO: 实际API调用获取待审核列表
      setTimeout(() => {
        this.pendingReviews = []
        this.pagination.pending.total = 0
        this.loading.pending = false
      }, 500)
    },
    fetchReviewedCases() {
      this.loading.reviewed = true
      // TODO: 实际API调用获取已审核列表
      setTimeout(() => {
        this.reviewedCases = []
        this.pagination.reviewed.total = 0
        this.loading.reviewed = false
      }, 500)
    },
    handlePageChange(page, type) {
      this.pagination[type].currentPage = page
      if (type === 'pending') {
        this.fetchPendingReviews()
      } else {
        this.fetchReviewedCases()
      }
    },
    handleReview(row) {
      this.reviewDialog.visible = true
      this.reviewDialog.case = row
      this.reviewDialog.form = {
        result: 'approve',
        comment: ''
      }
    },
    handleView(row) {
      this.$router.push(`/cases/view/${row.id}`)
    },
    closeReviewDialog() {
      this.reviewDialog.visible = false
      this.reviewDialog.case = null
      this.reviewDialog.form = {
        result: 'approve',
        comment: ''
      }
    },
    submitReview() {
      if (!this.reviewDialog.case) {
        this.$message.warning('没有选择审核的病例');
        return;
      }
      
      const caseId = this.reviewDialog.case.id;
      const isApproved = this.reviewDialog.form.result === 'approve';
      const reviewNotes = this.reviewDialog.form.comment;
      
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '提交审核结果...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      // 使用新API更新状态
      const updatePromise = isApproved 
        ? api.images.markAsApproved(caseId, reviewNotes) 
        : api.images.markAsRejected(caseId, reviewNotes);
      
      console.log(`开始将图像ID ${caseId} 状态更新为${isApproved ? '"已通过"(APPROVED)' : '"已驳回"(REJECTED)'}`);
      
      updatePromise
        .then(() => {
          console.log(`成功将图像ID ${caseId} 状态更新为${isApproved ? '"已通过"(APPROVED)' : '"已驳回"(REJECTED)'}`);
          this.$message.success(`审核${isApproved ? '通过' : '驳回'}成功`);
          this.closeReviewDialog();
          
          // 重新加载数据
          if (this.activeTab === 'pending') {
            this.fetchPendingReviews();
          } else {
            this.fetchReviewedCases();
          }
        })
        .catch(error => {
          console.error('审核提交失败', error);
          this.$message.error('审核提交失败: ' + (error.response?.data || error.message || '未知错误'));
        })
        .finally(() => {
          loading.close();
        });
    }
  }
}
</script>

<style scoped>
.review-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.review-dialog-content {
  padding: 10px;
}

.review-form {
  margin-top: 20px;
}
</style> 