{"version": 3, "file": "js/763.c78ee3c3.js", "mappings": "iMACOA,MAAM,a,GAGJA,MAAM,mB,GAJfC,IAAA,EASWD,MAAM,gB,GAEJA,MAAM,6B,GACJA,MAAM,c,GAEJA,MAAM,c,GASRA,MAAM,c,GAEJA,MAAM,c,GAQRA,MAAM,c,GAEJA,MAAM,c,GAQRA,MAAM,c,GAEJA,MAAM,c,GAaVA,MAAM,c,GAEJA,MAAM,c,GACJA,MAAM,e,GA7DvBC,IAAA,EAsE0CD,MAAM,sB,GAtEhDC,IAAA,EA2E4BD,MAAM,2B,EA3ElC,Q,GA6EuBA,MAAM,mB,GAUhBA,MAAM,gB,GAvFnBC,IAAA,EAqG6CD,MAAM,qB,GAGlCA,MAAM,iB,GAxGvBC,IAAA,EA0GqDC,MAAA,sC,GA1GrDD,IAAA,EA6G2BC,MAAA,sC,GAKZF,MAAM,0B,EAlHrB,Q,GAuHeA,MAAM,kB,GAvHrBC,IAAA,EA0HuDC,MAAA,mB,GA1HvDD,IAAA,EA2H6BC,MAAA,mB,GAkBdF,MAAM,6B,GAEFA,MAAM,wB,GA/IzBC,IAAA,EAiJsEC,MAAA,sC,GAjJtED,IAAA,EAwJiBD,MAAM,0B,GAxJvBC,IAAA,EA4JiBD,MAAM,8B,GA5JvBC,IAAA,G,GAAAA,IAAA,EAiKiBD,MAAM,0B,GAjKvBC,IAAA,EAqKiBD,MAAM,8B,GArKvBC,IAAA,G,GA2KiBD,MAAM,c,GAYRA,MAAM,kB,qdAtLnBG,EAAAA,EAAAA,IA+LM,MA/LNC,EA+LM,gBA9LJC,EAAAA,EAAAA,IAA2C,MAAvCL,MAAM,sBAAqB,WAAO,KAEtCK,EAAAA,EAAAA,IA2LM,MA3LNC,EA2LM,gBAzLJD,EAAAA,EAAAA,IAA4C,OAAvCL,MAAM,iBAAgB,eAAW,IAGLO,EAAAC,iBAAiBC,WATxDC,EAAAA,EAAAA,IAAA,SASiE,WAA3DP,EAAAA,EAAAA,IAyFM,MAzFNQ,EAyFM,EAvFJN,EAAAA,EAAAA,IA4CM,MA5CNO,EA4CM,EA3CJP,EAAAA,EAAAA,IASM,MATNQ,EASM,cARJR,EAAAA,EAAAA,IAAkC,OAA7BL,MAAM,cAAa,QAAI,KAC5BK,EAAAA,EAAAA,IAMM,MANNS,EAMM,EALJC,EAAAA,EAAAA,IAIEC,EAAA,CAnBhBC,WAgByBV,EAAAW,SAASC,WAhBlC,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAgByBd,EAAAW,SAASC,WAAUE,CAAA,GAC5BC,KAAK,SACLC,YAAY,S,4BAKlBlB,EAAAA,EAAAA,IAQM,MARNmB,EAQM,cAPJnB,EAAAA,EAAAA,IAAgC,OAA3BL,MAAM,cAAa,MAAE,KAC1BK,EAAAA,EAAAA,IAKM,MALNoB,EAKM,EAJJV,EAAAA,EAAAA,IAGiBW,EAAA,CA7B/BT,WA0BuCV,EAAAW,SAASS,cA1BhD,sBAAAP,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA0BuCd,EAAAW,SAASS,cAAaN,CAAA,I,CA1B7D,SAAAO,EAAAA,EAAAA,KA2BgB,iBAAgC,EAAhCb,EAAAA,EAAAA,IAAgCc,EAAA,CAAtBC,MAAM,KAAG,CA3BnC,SAAAF,EAAAA,EAAAA,KA2BoC,kBAACR,EAAA,KAAAA,EAAA,KA3BrCW,EAAAA,EAAAA,IA2BoC,M,IA3BpCC,EAAA,EAAAC,GAAA,OA4BgBlB,EAAAA,EAAAA,IAAgCc,EAAA,CAAtBC,MAAM,KAAG,CA5BnC,SAAAF,EAAAA,EAAAA,KA4BoC,kBAACR,EAAA,KAAAA,EAAA,KA5BrCW,EAAAA,EAAAA,IA4BoC,M,IA5BpCC,EAAA,EAAAC,GAAA,M,IAAAD,EAAA,G,uBAiCU3B,EAAAA,EAAAA,IAQM,MARN6B,EAQM,gBAPJ7B,EAAAA,EAAAA,IAAgC,OAA3BL,MAAM,cAAa,MAAE,KAC1BK,EAAAA,EAAAA,IAKM,MALN8B,EAKM,EAJJpB,EAAAA,EAAAA,IAGiBW,EAAA,CAvC/BT,WAoCuCV,EAAAW,SAASkB,WApChD,sBAAAhB,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAoCuCd,EAAAW,SAASkB,WAAUf,CAAA,I,CApC1D,SAAAO,EAAAA,EAAAA,KAqCgB,iBAAoC,EAApCb,EAAAA,EAAAA,IAAoCc,EAAA,CAA1BC,MAAM,OAAK,CArCrC,SAAAF,EAAAA,EAAAA,KAqCsC,kBAAGR,EAAA,KAAAA,EAAA,KArCzCW,EAAAA,EAAAA,IAqCsC,Q,IArCtCC,EAAA,EAAAC,GAAA,OAsCgBlB,EAAAA,EAAAA,IAAoCc,EAAA,CAA1BC,MAAM,OAAK,CAtCrC,SAAAF,EAAAA,EAAAA,KAsCsC,kBAAGR,EAAA,KAAAA,EAAA,KAtCzCW,EAAAA,EAAAA,IAsCsC,Q,IAtCtCC,EAAA,EAAAC,GAAA,M,IAAAD,EAAA,G,uBA2CU3B,EAAAA,EAAAA,IAWM,MAXNgC,EAWM,gBAVJhC,EAAAA,EAAAA,IAAkC,OAA7BL,MAAM,cAAa,QAAI,KAC5BK,EAAAA,EAAAA,IAQM,MARNiC,EAQM,EAPJvB,EAAAA,EAAAA,IAMYwB,EAAA,CApD1BtB,WA8CkCV,EAAAW,SAASsB,cA9C3C,sBAAApB,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA8CkCd,EAAAW,SAASsB,cAAanB,CAAA,GAAEE,YAAY,W,CA9CtE,SAAAK,EAAAA,EAAAA,KA+CgB,iBAAqC,EAArCb,EAAAA,EAAAA,IAAqC0B,EAAA,CAA1BX,MAAM,KAAKY,MAAM,UAC5B3B,EAAAA,EAAAA,IAAwC0B,EAAA,CAA7BX,MAAM,KAAKY,MAAM,aAC5B3B,EAAAA,EAAAA,IAAqC0B,EAAA,CAA1BX,MAAM,KAAKY,MAAM,UAC5B3B,EAAAA,EAAAA,IAAuC0B,EAAA,CAA5BX,MAAM,KAAKY,MAAM,YAC5B3B,EAAAA,EAAAA,IAA8C0B,EAAA,CAAnCX,MAAM,MAAMY,MAAM,iB,IAnD7CV,EAAA,G,yBA0DQ3B,EAAAA,EAAAA,IA0BM,MA1BNsC,EA0BM,gBAzBJtC,EAAAA,EAAAA,IAAqC,OAAhCL,MAAM,cAAa,WAAO,KAC/BK,EAAAA,EAAAA,IAuBM,MAvBNuC,EAuBM,EAtBJvC,EAAAA,EAAAA,IAqBM,MArBNwC,EAqBM,EApBJ9B,EAAAA,EAAAA,IAmBY+B,GAAA,CAlBVC,KAAA,GACAC,OAAO,IACN,eAAa,EACb,YAAWC,EAAAC,iBACXC,MAAO,EACP,kBAAgB,G,CApEjC,SAAAvB,EAAAA,EAAAA,KAkFa,iBASO,CArBQrB,EAAA6C,eAG6C,WAEzDjD,EAAAA,EAAAA,IAKM,MALNkD,EAKM,EAJJhD,EAAAA,EAAAA,IAA0D,OAApDiD,IAAK/C,EAAA6C,aAAcpD,MAAM,gBAAgBuD,IAAI,Q,OA5ErEC,IA6EkBnD,EAAAA,EAAAA,IAEM,MAFNoD,EAEM,EADJ1C,EAAAA,EAAAA,IAAgF2C,GAAA,CAArEpC,KAAK,UAAUqC,KAAK,QAASC,SA9E5DC,EAAAA,EAAAA,IA8EwEZ,EAAAa,WAAU,W,CA9ElF,SAAAlC,EAAAA,EAAAA,KA8EoF,kBAAIR,EAAA,MAAAA,EAAA,MA9ExFW,EAAAA,EAAAA,IA8EoF,S,IA9EpFC,EAAA,EAAAC,GAAA,M,sBAsEwC,WAAxB9B,EAAAA,EAAAA,IAIM,MAJN4D,EAIM,EAHJhD,EAAAA,EAAAA,IAA6DiD,EAAA,CAApDhE,MAAM,eAAa,CAvE9C,SAAA4B,EAAAA,EAAAA,KAuE+C,iBAAsB,EAAtBb,EAAAA,EAAAA,IAAsBkD,G,IAvErEjC,EAAA,I,eAwEkB3B,EAAAA,EAAAA,IAAiC,aAxEnD0B,EAAAA,EAAAA,IAwEuB,cAAS1B,EAAAA,EAAAA,IAAa,UAAT,UAAI,mBACtBA,EAAAA,EAAAA,IAA6D,OAAxDL,MAAM,cAAa,mCAA+B,O,IAzEzEgC,EAAA,G,wBAuFQ3B,EAAAA,EAAAA,IAUM,MAVN6D,EAUM,EATJnD,EAAAA,EAAAA,IAOY2C,GAAA,CANVpC,KAAK,UACJ6C,SAAU5D,EAAA6D,eAAiB7D,EAAA8D,aAC3BT,QAAOX,EAAAqB,kB,CA3FpB,SAAA1C,EAAAA,EAAAA,KAwGkB,iBAEP,CAbgBrB,EAAA6D,eAAY,WAA3BG,EAAAA,EAAAA,IAAuDP,EAAA,CA7FnE/D,IAAA,aAAA2B,EAAAA,EAAAA,KA6FyC,iBAAgB,EAAhBb,EAAAA,EAAAA,IAAgByD,I,IA7FzDxC,EAAA,MAAAtB,EAAAA,EAAAA,IAAA,QA8FYL,EAAAA,EAAAA,IAAmD,aAAAoE,EAAAA,EAAAA,IAA1ClE,EAAA6D,aAAe,SAAW,QAAd,G,IA9FjCpC,EAAA,G,2BAgGUjB,EAAAA,EAAAA,IAA4C2C,GAAA,CAAhCE,QAAOX,EAAAyB,WAAS,CAhGtC,SAAA9C,EAAAA,EAAAA,KAgGwC,kBAAER,EAAA,MAAAA,EAAA,MAhG1CW,EAAAA,EAAAA,IAgGwC,O,IAhGxCC,EAAA,EAAAC,GAAA,M,oBAqGiB1B,EAAAC,iBAAiBC,YAAS,WAArCN,EAAAA,EAAAA,IAyFM,MAzFNwE,EAyFM,EAxFJ5D,EAAAA,EAAAA,IAuFU6D,GAAA,MAtFGC,QAAMjD,EAAAA,EAAAA,KACf,iBAMM,EANNvB,EAAAA,EAAAA,IAMM,MANNyE,EAMM,gBALJzE,EAAAA,EAAAA,IAAyC,QAAnCL,MAAM,gBAAe,WAAO,IACtBO,EAAAC,iBAAiBuE,WAAQ,WAArC5E,EAAAA,EAAAA,IAEO,OAFP6E,EAAkF,iBAElF,WACA7E,EAAAA,EAAAA,IAAsE,OAAtE8E,EAAwD,c,IA7GtE,SAAArD,EAAAA,EAAAA,KAkHU,iBAEM,EAFNvB,EAAAA,EAAAA,IAEM,MAFN6E,EAEM,EADJ7E,EAAAA,EAAAA,IAA6E,OAAvEiD,IAAK/C,EAAAC,iBAAiB2E,YAAanF,MAAM,eAAeuD,IAAI,U,OAnH9E6B,MAuHU/E,EAAAA,EAAAA,IAmBM,MAnBNgF,EAmBM,EAlBJtE,EAAAA,EAAAA,IAiBkBuE,GAAA,CAjBAC,OAAQ,EAAGC,OAAA,I,CAxHzC,SAAA5D,EAAAA,EAAAA,KAyHc,iBAGuB,EAHvBb,EAAAA,EAAAA,IAGuB0E,GAAA,CAHD3D,MAAM,QAAM,CAzHhD,SAAAF,EAAAA,EAAAA,KA8J+C,iBACC,CArCpBrB,EAAAC,iBAAiBuE,WAAQ,WAArC5E,EAAAA,EAAAA,IAA0E,OAA1EuF,EAA+D,WAAI,WACnEvF,EAAAA,EAAAA,IAAgD,OAAhDwF,EAAqC,S,IA3HrD3D,EAAA,KA6HcjB,EAAAA,EAAAA,IAEuB0E,GAAA,CAFD3D,MAAM,OAAK,CA7H/C,SAAAF,EAAAA,EAAAA,KA8HgB,iBAAmD,EA9HnEG,EAAAA,EAAAA,KAAA0C,EAAAA,EAAAA,IA8HmBmB,KAAKC,MAAoC,IAA9BtF,EAAAC,iBAAiBsF,aAAoB,KACrD,G,IA/Hd9D,EAAA,IAgI0CzB,EAAAC,iBAAiBuE,WAAQ,WAArDR,EAAAA,EAAAA,IAEuBkB,GAAA,CAlIrCxF,IAAA,EAgIqE6B,MAAM,U,CAhI3E,SAAAF,EAAAA,EAAAA,KAiIgB,iBAAmC,EAjInDG,EAAAA,EAAAA,KAAA0C,EAAAA,EAAAA,IAiImBlE,EAAAC,iBAAiBuF,cAAY,G,IAjIhD/D,EAAA,MAAAtB,EAAAA,EAAAA,IAAA,OAmI0CH,EAAAC,iBAAiBwF,iBAAc,WAA3DzB,EAAAA,EAAAA,IAEuBkB,GAAA,CArIrCxF,IAAA,EAmI2E6B,MAAM,Q,CAnIjF,SAAAF,EAAAA,EAAAA,KAoIgB,iBAAkD,EAAlDvB,EAAAA,EAAAA,IAAkD,aAAAoE,EAAAA,EAAAA,IAAzClE,EAAAC,iBAAiBwF,gBAAc,G,IApIxDhE,EAAA,MAAAtB,EAAAA,EAAAA,IAAA,OAsI0CH,EAAAC,iBAAiByF,gBAAa,WAA1D1B,EAAAA,EAAAA,IAEuBkB,GAAA,CAxIrCxF,IAAA,EAsI0E6B,MAAM,Q,CAtIhF,SAAAF,EAAAA,EAAAA,KAuIgB,iBAAiD,EAAjDvB,EAAAA,EAAAA,IAAiD,aAAAoE,EAAAA,EAAAA,IAAxClE,EAAAC,iBAAiByF,eAAa,G,IAvIvDjE,EAAA,MAAAtB,EAAAA,EAAAA,IAAA,O,IAAAsB,EAAA,OA6IU3B,EAAAA,EAAAA,IAuCM,MAvCN6F,EAuCM,EAtCJnF,EAAAA,EAAAA,IAOaoF,GAAA,MArJzB,SAAAvE,EAAAA,EAAAA,KA+Ic,iBAKM,EALNvB,EAAAA,EAAAA,IAKM,MALN+F,EAKM,gBAJJ/F,EAAAA,EAAAA,IAAmB,YAAb,UAAM,IACAE,EAAAC,iBAAiB6F,0BAAuB,WAApDlG,EAAAA,EAAAA,IAEO,OAFPmG,EAEOlF,EAAA,MAAAA,EAAA,MADLf,EAAAA,EAAAA,IAA+B,KAA5BL,MAAM,mBAAiB,UAlJ5C+B,EAAAA,EAAAA,IAkJiD,sBAlJjDrB,EAAAA,EAAAA,IAAA,S,IAAAsB,EAAA,IAwJsDzB,EAAAC,iBAAiB+F,sBAAmB,WAA9EpG,EAAAA,EAAAA,IAGM,MAHNqG,EAGM,gBAFJnG,EAAAA,EAAAA,IAAgB,UAAZ,WAAO,KACXA,EAAAA,EAAAA,IAAiD,UAAAoE,EAAAA,EAAAA,IAA3ClE,EAAAC,iBAAiB+F,qBAAmB,mBAE5CpG,EAAAA,EAAAA,IAGM,MAHNsG,EAGM,CAFelG,EAAAC,iBAAiB6F,0BAAuB,WAA3D9B,EAAAA,EAAAA,IAAkFmC,GAAA,CA7JhGzG,IAAA,EA6J4E0G,KAAM,EAAGC,SAAA,Q,WACvEzG,EAAAA,EAAAA,IAAoB,IA9JlC0G,EA8JwB,cAG8BtG,EAAAC,iBAAiBsG,cAAW,WAAtE3G,EAAAA,EAAAA,IAGM,MAHN4G,EAGM,gBAFJ1G,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAAyC,UAAAoE,EAAAA,EAAAA,IAAnClE,EAAAC,iBAAiBsG,aAAW,mBAEpC3G,EAAAA,EAAAA,IAGM,MAHN6G,EAGM,CAFezG,EAAAC,iBAAiB6F,0BAAuB,WAA3D9B,EAAAA,EAAAA,IAAkFmC,GAAA,CAtKhGzG,IAAA,EAsK4E0G,KAAM,EAAGC,SAAA,Q,WACvEzG,EAAAA,EAAAA,IAAoB,IAvKlC8G,EAuKwB,eAIZ5G,EAAAA,EAAAA,IAQM,MARN6G,EAQM,EAPJnG,EAAAA,EAAAA,IAMEoG,GAAA,CALAC,MAAM,WACN9F,KAAK,OACL+F,YAAY,iDACZ,eACCC,UAAU,SAMjBjH,EAAAA,EAAAA,IAKM,MALNkH,EAKM,EAJJxG,EAAAA,EAAAA,IAA8D2C,GAAA,CAAnDpC,KAAK,UAAWsC,QAAOX,EAAAuE,c,CAxL9C,SAAA5F,EAAAA,EAAAA,KAwL4D,kBAAER,EAAA,MAAAA,EAAA,MAxL9DW,EAAAA,EAAAA,IAwL4D,O,IAxL5DC,EAAA,EAAAC,GAAA,M,gBAyLYlB,EAAAA,EAAAA,IAA4D2C,GAAA,CAAjDpC,KAAK,UAAWsC,QAAOX,EAAAwE,Y,CAzL9C,SAAA7F,EAAAA,EAAAA,KAyL0D,kBAAER,EAAA,MAAAA,EAAA,MAzL5DW,EAAAA,EAAAA,IAyL0D,O,IAzL1DC,EAAA,EAAAC,GAAA,M,gBA0LYlB,EAAAA,EAAAA,IAA+D2C,GAAA,CAApDpC,KAAK,UAAWsC,QAAOX,EAAAyE,e,CA1L9C,SAAA9F,EAAAA,EAAAA,KA0L6D,kBAAER,EAAA,MAAAA,EAAA,MA1L/DW,EAAAA,EAAAA,IA0L6D,O,IA1L7DC,EAAA,EAAAC,GAAA,M,gBA2LYlB,EAAAA,EAAAA,IAAiD2C,GAAA,CAArCE,QAAOX,EAAA0E,gBAAc,CA3L7C,SAAA/F,EAAAA,EAAAA,KA2L+C,kBAAER,EAAA,MAAAA,EAAA,MA3LjDW,EAAAA,EAAAA,IA2L+C,O,IA3L/CC,EAAA,EAAAC,GAAA,M,qBAAAD,EAAA,QAAAtB,EAAAA,EAAAA,IAAA,U,+JAyMA,SACEkH,KAAM,sBACNC,WAAY,CACV,qBAAsBC,EAAAA,aACtB,eAAgBC,EAAAA,SAElBC,KAAI,WACF,MAAO,CACL9G,SAAU,CACRC,WAAY,KACZQ,cAAe,IACfS,WAAY,MACZI,cAAe,IAEjByF,aAAc,CACZ,CAAEvF,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,OAExBoG,YAAa,CACX,CAAExF,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,MACtB,CAAEY,MAAO,KAAMZ,MAAO,OAExBuC,aAAc,KACdjB,aAAc,KACdgB,cAAc,EACd5D,kBAAkB2H,EAAAA,EAAAA,IAAS,CACzB1H,WAAW,EACXsE,UAAU,EACVgB,aAAc,GACdD,WAAY,EACZX,YAAa,GACbkB,yBAAyB,EACzBE,oBAAqB,GACrBO,YAAa,GACbsB,sBAAuB,GACvBC,WAAY,GACZrC,eAAgB,GAChBC,cAAe,KAEjBI,yBAAyB,EACzBiC,oBAAqB,CAAC,IAAK,KAC3BC,gBAAiB,KACjBC,eAAgB,GAChBC,WAAW,EAEf,EACAC,SAAU,CACRC,gBAAe,WACb,IAAM7C,EAAa8C,KAAKpI,iBAAiBsF,WACzC,OAAIA,EAAa,GAAY,UACzBA,EAAa,GAAY,UACtB,SACT,GAEF+C,QAAO,WACLC,QAAQC,IAAI,wBACd,EACAC,QAAO,WACLF,QAAQC,IAAI,gCACZE,SAAS7B,MAAQ,UAGjBwB,KAAKM,WAAU,WAEb,GADAJ,QAAQC,IAAI,oBACRI,OAAOC,OACT,IACED,OAAOC,OAAOC,YAAY,CACxB/H,KAAM,cACNgI,KAAM,wBACL,IACL,CAAE,MAAOC,GACPT,QAAQU,MAAM,sBAAuBD,EACvC,CAEJ,GACF,EACAE,QAAS,CACPvG,iBAAgB,SAACwG,GAAM,IAAAC,EAAA,KACrB,IAAKD,EAGH,OAFAd,KAAKvE,aAAe,UACpBuE,KAAKxF,aAAe,MAKtB,GAAIsG,EAAKE,IAAIjG,KAAO,SAIlB,OAHAkG,EAAAA,GAAUL,MAAM,kBAChBZ,KAAKvE,aAAe,UACpBuE,KAAKxF,aAAe,MAItBwF,KAAKvE,aAAeqF,EAAKE,IAGzB,IAAME,EAAS,IAAIC,WACnBD,EAAOE,OAAS,SAACT,GACfI,EAAKvG,aAAemG,EAAEU,OAAOC,MAC/B,EACAJ,EAAOK,cAAcT,EAAKE,IAC5B,EACAtF,iBAAgB,WAAG,IA2Bb8F,EA3BaC,EAAA,KAoBjB,GAlBAzB,KAAKxE,cAAe,EACpBwE,KAAKJ,eAAiB,GACtBI,KAAKpI,iBAAmB,CACtBC,WAAW,EACXsE,UAAU,EACVe,WAAY,EACZC,aAAc,GACdQ,oBAAqB,GACrBO,YAAa,GACbsB,sBAAuB,GACvBjD,YAAa,GACbmF,GAAI,KACJjE,yBAAyB,EACzBL,eAAgB,GAChBC,cAAe,KAIZ2C,KAAKvE,aAGR,OAFAuE,KAAKJ,eAAiB,gBACtBI,KAAKxE,cAAe,GAMtB,IAEE,GAAIwE,KAAK2B,QAAU3B,KAAK2B,OAAOC,QAAQC,UACrCL,EAAgBxB,KAAK2B,OAAOC,QAAQC,cAGjC,CACH,IAAMC,EAAUC,aAAaC,QAAQ,QACrC,GAAIF,EAAS,CACX,IAAMG,EAAUC,KAAKC,MAAML,GAE3BN,EAAgBS,EAAQP,IAGnBF,GAAiBS,EAAQG,UAC5BlC,QAAQC,IAAI,wBAAyB8B,EAAQG,SAEjD,CACF,CACF,CAAE,MAAOxB,GACPV,QAAQU,MAAM,YAAaA,EAC7B,CAGA,IAAKY,EAGH,OAFAxB,KAAKxE,cAAe,OACpByF,EAAAA,GAAUL,MAAM,mBAKlB,IAAMtI,EAAW,IAAI+J,SACrB/J,EAASgK,OAAO,OAAQtC,KAAKvE,cAGzBuE,KAAK1H,SAASC,YAAYD,EAASgK,OAAO,cAAetC,KAAK1H,SAASC,WAAWgK,YAClFvC,KAAK1H,SAASS,eAAeT,EAASgK,OAAO,SAAUtC,KAAK1H,SAASS,eACrEiH,KAAK1H,SAASkB,YAAYlB,EAASgK,OAAO,cAAetC,KAAK1H,SAASkB,YACvEwG,KAAK1H,SAASsB,eAAetB,EAASgK,OAAO,iBAAkBtC,KAAK1H,SAASsB,eAGjFtB,EAASgK,OAAO,UAAWd,EAAce,YAEzCrC,QAAQC,IAAI,eAAgBqB,GAG5BgB,IAAAA,KAAW,wDAAyDlK,EAAU,CAC5EmK,QAAS,CACP,eAAgB,sBAChB,YAAajB,EAAce,YAE7BG,QAAS,MAEVC,MAAK,SAAAC,GACJ1C,QAAQC,IAAI,YAAayC,EAASxD,MAGlCqC,EAAK7J,iBAAiBC,WAAY,EAClC4J,EAAK7J,iBAAiB8J,GAAKkB,EAASxD,KAAKsC,GAGzC,IAAMvE,EAAeyF,EAASxD,KAAKjC,aAUnC,GATAsE,EAAK7J,iBAAiBuE,SAAWgB,GAAgBA,EAAa0F,OAAS,EAGvEpB,EAAK7J,iBAAiBsF,WAAa0F,EAASxD,KAAKlC,YAAc,EAG/DuE,EAAK7J,iBAAiBuF,aAAeA,GAAgB,GAGjDyF,EAASxD,KAAK0D,mBAAoB,CACpC,IAAMC,EAAUxC,OAAOyC,SAASC,OAChCxB,EAAK7J,iBAAiB2E,YAAcwG,EAAUH,EAASxD,KAAK0D,kBAC9D,CAGArB,EAAK7J,iBAAiBwF,eAAiBwF,EAASxD,KAAK8D,OAAS,GAC9DzB,EAAK7J,iBAAiByF,cAAgBuF,EAASxD,KAAK+D,UAAY,GAG3DP,EAASxD,KAAKzB,qBAMjB8D,EAAK7J,iBAAiB+F,oBAAsBiF,EAASxD,KAAKzB,oBAE1D8D,EAAK7J,iBAAiBsG,YAAc0E,EAASxD,KAAKlB,aAAe0E,EAASxD,KAAKI,sBAE/EiC,EAAK7J,iBAAiB4H,sBAAwBoD,EAASxD,KAAKI,wBAR5DiC,EAAK7J,iBAAiB6F,yBAA0B,EAChDgE,EAAK2B,aAAaR,EAASxD,KAAKsC,KAWlCD,EAAKjG,cAAe,CACtB,IAAC,UACM,SAAAoF,GACLV,QAAQU,MAAM,UAAWA,GACzBa,EAAK7B,eAAa,eAAAyD,OAAmBzC,EAAM0C,SAC3C7B,EAAKjG,cAAe,CACtB,GACF,EAEA4H,aAAY,SAACG,GAAa,IAAAC,EAAA,KACtBtD,QAAQC,IAAI,iBAAkBoD,GAC9BvD,KAAKL,gBAAkB8D,aAAY,WAC/BD,EAAKE,eAAeH,EACxB,GAAG,KAGHI,YAAW,WACHH,EAAK7D,kBACLiE,cAAcJ,EAAK7D,iBACnB6D,EAAK7D,gBAAkB,KACpB6D,EAAK5L,iBAAiB6F,0BACrB+F,EAAK5L,iBAAiB6F,yBAA0B,EAChDwD,EAAAA,GAAU4C,KAAK,+BAG3B,GAAG,KACP,EAEAH,eAAc,SAACH,GAAa,IAAAO,EAAA,KAC1BtB,IAAAA,IAAU,qCAADa,OAAsCE,IAC5CZ,MAAK,SAAAC,GACJ,IAAMxD,EAAOwD,EAASxD,KACtBc,QAAQC,IAAI,YAAaf,GAMrBA,EAAKzB,sBAAwBmG,EAAKlM,iBAAiB+F,sBACrDmG,EAAKlM,iBAAiB+F,oBAAsByB,EAAKzB,qBAK/CyB,EAAKlB,cAAgB4F,EAAKlM,iBAAiBsG,cAC7C4F,EAAKlM,iBAAiBsG,YAAckB,EAAKlB,aAMzCkB,EAAKzB,qBACLyB,EAAKlB,aAED4F,EAAKnE,kBACPiE,cAAcE,EAAKnE,iBACnBmE,EAAKnE,gBAAkB,KACvBmE,EAAKlM,iBAAiB6F,yBAA0B,EAGtD,IAAC,UACM,SAAAmD,GACLV,QAAQU,MAAM,cAAeA,EAE/B,GACJ,EAEAmD,aAAY,WACV/D,KAAKpI,iBAAiBC,WAAY,CACpC,EAEAiE,UAAS,WACPkE,KAAK1H,SAASC,WAAa,GAC3ByH,KAAK1H,SAASS,cAAgB,GAC9BiH,KAAK1H,SAASkB,WAAa,GAC3BwG,KAAK1H,SAASsB,cAAgB,GAE9BoG,KAAKvE,aAAe,KACpBuE,KAAKxF,aAAe,IACtB,EACAoE,aAAY,WACV,IAAM2E,EAAcvD,KAAKpI,iBAAiB8J,GACrC6B,EAMLvD,KAAKgE,QAAQC,KAAK,CAChBC,KAAM,aAAFb,OAAeE,EAAW,sBAC9BY,MAAO,CACLC,KAAM,OACNC,KAAM,oBATRpD,EAAAA,GAAUL,MAAM,gBAYpB,EAEM/B,WAAU,WAAG,IAAAyF,EAAA,YAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAA,OAAAJ,EAAAA,EAAAA,KAAAK,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,UACZT,EAAK1M,iBAAiB8J,GAAI,CAAFoD,EAAAC,EAAA,QACK,OAAhC9D,EAAAA,GAAUL,MAAM,iBAAgBkE,EAAAE,EAAA,UAejC,OAXKL,EAAc,CAClBpM,WAAY+L,EAAKhM,SAASC,WAC1B0M,OAAQX,EAAKhM,SAASS,cACtBS,WAAY8K,EAAKhM,SAASkB,WAC1BI,cAAe0K,EAAKhM,SAASsB,cAC7BsJ,MAAOoB,EAAK1M,iBAAiBwF,eAAiBkH,EAAK1M,iBAAiBwF,eAAe4B,KAAO,GAC1FmE,SAAUmB,EAAK1M,iBAAiByF,cAAgBiH,EAAK1M,iBAAiByF,cAAc2B,KAAO,GAC3FkG,YAAaZ,EAAK1M,iBAAiBsN,YACnCvH,oBAAqB2G,EAAK1M,iBAAiB+F,oBAC3CO,YAAaoG,EAAK1M,iBAAiBsG,aAAeoG,EAAK1M,iBAAiB4H,sBACxE2F,OAAQ,YACTL,EAAAM,EAAA,EAAAN,EAAAC,EAAA,EAGwBvC,IAAAA,IAAU,qCAADa,OAAsCiB,EAAK1M,iBAAiB8J,IAAMiD,GAAY,OAAjGG,EAAAO,EAEbf,EAAKzE,WAAY,EAGjB8D,YAAW,WAETW,EAAKN,QAAQC,KAAK,iBACpB,GAAG,MAAOa,EAAAC,EAAA,eAAAD,EAAAM,EAAA,EAAAR,EAAAE,EAAAO,EAEVnF,QAAQU,MAAM,UAASgE,GACvB3D,EAAAA,GAAUL,MAAM,gBAAe,cAAAkE,EAAAE,EAAA,MAAAN,EAAA,iBA/BhBH,EAiCnB,EAEAe,mBAAkB,WAChB,IAEE,GAAItF,KAAK2B,QAAU3B,KAAK2B,OAAOC,QAAQ2D,YACrC,OAAOvF,KAAK2B,OAAOC,QAAQ2D,YAI3B,IAAMzD,EAAUC,aAAaC,QAAQ,QACrC,GAAIF,EAAS,CACX,IAAMG,EAAUC,KAAKC,MAAML,GAC3B,OAAOG,EAAQuD,MAAQvD,EAAQwD,QACjC,CAEJ,CAAE,MAAO7E,GAEP,OADAV,QAAQU,MAAM,YAAaA,GACpB,IACT,CACA,OAAO,IACT,EACA9B,cAAa,WAAG,IAAA4G,EAAA,KAERnC,EAAcvD,KAAKpI,iBAAiB8J,GAC1C,GAAK6B,EAAL,CAMA,IAAMkC,EAAWzF,KAAKsF,qBACtBpF,QAAQC,IAAI,UAAWsF,GAGvB,IAAIN,EAAS,YAITM,IAAaA,EAASE,cAAcC,SAAS,UAAYH,EAASE,cAAcC,SAAS,eAC3FT,EAAS,WACO,OAIlB3C,IAAAA,IAAU,qCAADa,OAAsCE,EAAW,WAAW,KAAM,CACzEsC,OAAQ,CACNV,OAAQA,KAGXxC,MAAK,SAAAC,GACJ1C,QAAQC,IAAI,UAAWyC,EAASxD,MAIhCsG,EAAK3G,iBACL2G,EAAK5J,WACP,IAAC,UACM,SAAA8E,GACLV,QAAQU,MAAM,UAAWA,GACzBK,EAAAA,GAAUL,MAAM,WAADyC,OAAYzC,EAAM0C,UAGjCoC,EAAK3G,iBACL2G,EAAK5J,WACP,GArCA,MAFEmF,EAAAA,GAAUL,MAAM,gBAwCpB,EACA1F,WAAU,SAACyF,GACLA,GACFA,EAAEmF,kBAEJ9F,KAAKvE,aAAe,KACpBuE,KAAKxF,aAAe,IACtB,EAEAuL,sBAAqB,SAACjM,GACpB,IAAMkM,EAAa,CACjB,KAAQ,KACR,QAAW,KACX,KAAQ,KACR,OAAU,KACV,aAAgB,OAElB,OAAOA,EAAWlM,IAAUA,CAC9B,EACAiF,eAAc,WAEZiB,KAAKlE,YACLkE,KAAKH,WAAY,EAGjBoG,OAAOC,OAAOlG,KAAKpI,iBAAkB,CACnCC,WAAW,EACXsE,UAAU,EACVsB,yBAAyB,EACzBiE,GAAI,KACJnF,YAAa,GACbY,aAAc,GACdD,WAAY,EACZE,eAAgB,GAChBC,cAAe,GACfzD,cAAe,GACf+D,oBAAqB,GACrBO,YAAa,GACbgH,YAAa,IAEjB,I,eCrqBJ,MAAMiB,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,I", "sources": ["webpack://medical-annotation-frontend/./src/views/HemangiomaDiagnosis.vue", "webpack://medical-annotation-frontend/./src/views/HemangiomaDiagnosis.vue?b123"], "sourcesContent": ["<template>\n  <div class=\"container\">\n    <h2 class=\"blood-vessel-title\">血管瘤智能诊断</h2>\n    \n    <div class=\"diagnosis-panel\">\n      <!-- 上传图像提示 -->\n      <div class=\"panel-heading\">上传图像进行血管瘤检测</div>\n      \n      <!-- 表单区域 -->\n      <div class=\"form-content\" v-if=\"!diagnosisResults.processed\">\n        <!-- 所有基本信息字段放在一个自适应容器中 -->\n        <div class=\"adaptive-fields-container\">\n          <div class=\"field-item\">\n            <div class=\"form-label\">患者年龄</div>\n            <div class=\"form-field\">\n              <el-input \n                v-model=\"formData.patientAge\" \n                type=\"number\" \n                placeholder=\"请输入年龄\"\n              />\n            </div>\n          </div>\n          \n          <div class=\"field-item\">\n            <div class=\"form-label\">性别</div>\n            <div class=\"form-field\">\n              <el-radio-group v-model=\"formData.patientGender\">\n                <el-radio label=\"男\">男</el-radio>\n                <el-radio label=\"女\">女</el-radio>\n              </el-radio-group>\n            </div>\n          </div>\n          \n          <div class=\"field-item\">\n            <div class=\"form-label\">类型</div>\n            <div class=\"form-field\">\n              <el-radio-group v-model=\"formData.originType\">\n                <el-radio label=\"先天性\">先天性</el-radio>\n                <el-radio label=\"后天性\">后天性</el-radio>\n              </el-radio-group>\n            </div>\n          </div>\n\n          <div class=\"field-item\">\n            <div class=\"form-label\">血管质地</div>\n            <div class=\"form-field\">\n              <el-select v-model=\"formData.vesselTexture\" placeholder=\"请选择血管质地\">\n                <el-option label=\"质软\" value=\"soft\" />\n                <el-option label=\"质韧\" value=\"elastic\" />\n                <el-option label=\"质硬\" value=\"hard\" />\n                <el-option label=\"囊性\" value=\"cystic\" />\n                <el-option label=\"可压缩\" value=\"compressible\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n\n        <!-- 上传图像 -->\n        <div class=\"form-group\">\n          <div class=\"form-label\">上传血管瘤图像</div>\n          <div class=\"form-field\">\n            <div class=\"upload-area\">\n              <el-upload\n                drag\n                action=\"#\"\n                :auto-upload=\"false\"\n                :on-change=\"handleFileChange\"\n                :limit=\"1\"\n                :show-file-list=\"false\"\n              >\n                <div v-if=\"!imagePreview\" class=\"upload-placeholder\">\n                  <el-icon class=\"upload-icon\"><i-ep-upload-filled /></el-icon>\n                  <div>拖拽文件到此处或 <em>点击上传</em></div>\n                  <div class=\"upload-tip\">支持JPG、PNG、BMP等常见图像格式，最大文件大小10MB</div>\n                </div>\n                <div v-else class=\"image-preview-container\">\n                  <img :src=\"imagePreview\" class=\"preview-image\" alt=\"图像预览\">\n                  <div class=\"preview-overlay\">\n                    <el-button type=\"primary\" size=\"small\" @click.stop=\"resetImage\">重新上传</el-button>\n                  </div>\n                </div>\n              </el-upload>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 操作按钮 -->\n        <div class=\"form-buttons\">\n          <el-button \n            type=\"primary\" \n            :disabled=\"isProcessing || !selectedFile\" \n            @click=\"processDiagnosis\"\n          >\n            <el-icon v-if=\"isProcessing\"><i-ep-loading /></el-icon>\n            <span>{{ isProcessing ? '处理中...' : '开始诊断' }}</span>\n          </el-button>\n          <el-button @click=\"resetForm\">重置</el-button>\n        </div>\n      </div>\n      \n      <!-- 诊断结果区域，只在处理完成后显示 -->\n      <div v-if=\"diagnosisResults.processed\" class=\"diagnosis-results\">\n        <el-card>\n          <template #header>\n            <div class=\"result-header\">\n              <span class=\"result-title\">血管瘤诊断结果</span>\n              <span v-if=\"diagnosisResults.detected\" style=\"color: #F56C6C; padding: 6px 12px;\">\n                检测到疑似血管瘤\n              </span>\n              <span v-else style=\"color: #67C23A; padding: 6px 12px;\">未检测到血管瘤</span>\n            </div>\n          </template>\n\n          <!-- 结果图像 -->\n          <div class=\"result-image-container\">\n            <img :src=\"diagnosisResults.resultImage\" class=\"result-image\" alt=\"诊断结果图像\" />\n          </div>\n\n          <!-- 基本检测信息 -->\n          <div class=\"detection-info\">\n            <el-descriptions :column=\"2\" border>\n              <el-descriptions-item label=\"检测状态\">\n                <span v-if=\"diagnosisResults.detected\" style=\"color: #F56C6C;\">已检测到</span>\n                <span v-else style=\"color: #67C23A;\">未检测到</span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"置信度\">\n                {{ Math.round(diagnosisResults.confidence * 100) }}%\n              </el-descriptions-item>\n              <el-descriptions-item v-if=\"diagnosisResults.detected\" label=\"检测到的类型\">\n                {{ diagnosisResults.detectedType }}\n              </el-descriptions-item>\n              <el-descriptions-item v-if=\"diagnosisResults.predictedColor\" label=\"病灶颜色\">\n                <span>{{ diagnosisResults.predictedColor }}</span>\n              </el-descriptions-item>\n              <el-descriptions-item v-if=\"diagnosisResults.predictedPart\" label=\"病灶部位\">\n                <span>{{ diagnosisResults.predictedPart }}</span>\n              </el-descriptions-item>\n            </el-descriptions>\n          </div>\n\n          <!-- 大模型生成的诊断建议 -->\n          <div class=\"diagnosis-recommendations\">\n            <el-divider>\n              <div class=\"recommendation-title\">\n                <span>AI诊断建议</span>\n                <span v-if=\"diagnosisResults.isLoadingRecommendation\" style=\"font-size: 14px; color: #909399;\">\n                  <i class=\"el-icon-loading\"></i> 正在生成详细建议...\n                </span>\n              </div>\n            </el-divider>\n\n            <!-- 治疗建议 -->\n            <div class=\"recommendation-section\" v-if=\"diagnosisResults.treatmentSuggestion\">\n              <h3>治疗与注意事项</h3>\n              <p>{{ diagnosisResults.treatmentSuggestion }}</p>\n            </div>\n            <div class=\"recommendation-placeholder\" v-else>\n              <el-skeleton v-if=\"diagnosisResults.isLoadingRecommendation\" :rows=\"1\" animated />\n              <p v-else>暂无治疗建议</p>\n            </div>\n\n            <div class=\"recommendation-section\" v-if=\"diagnosisResults.precautions\">\n              <h3>注意事项</h3>\n              <p>{{ diagnosisResults.precautions }}</p>\n            </div>\n            <div class=\"recommendation-placeholder\" v-else>\n              <el-skeleton v-if=\"diagnosisResults.isLoadingRecommendation\" :rows=\"1\" animated />\n              <p v-else>暂无注意事项</p>\n            </div>\n\n            <!-- 免责声明 -->\n            <div class=\"disclaimer\">\n              <el-alert\n                title=\"AI生成免责声明\"\n                type=\"info\"\n                description=\"本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。\"\n                show-icon\n                :closable=\"false\"\n              />\n            </div>\n          </div>\n\n          <!-- 操作按钮 -->\n          <div class=\"result-actions\">\n            <el-button type=\"primary\" @click=\"modifyReport\">修改</el-button>\n            <el-button type=\"success\" @click=\"saveReport\">保存</el-button>\n            <el-button type=\"warning\" @click=\"confirmReport\">确认</el-button>\n            <el-button @click=\"resetDiagnosis\">返回</el-button>\n          </div>\n        </el-card>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { UploadFilled, Loading } from '@element-plus/icons-vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport axios from 'axios';\nimport { reactive, ref } from 'vue';\n\nexport default {\n  name: 'HemangiomaDiagnosis',\n  components: {\n    'i-ep-upload-filled': UploadFilled,\n    'i-ep-loading': Loading\n  },\n  data() {\n    return {\n      formData: {\n        patientAge: null,\n        patientGender: '男',\n        originType: '先天性',\n        vesselTexture: '',\n      },\n      colorOptions: [\n        { value: '褐色', label: '褐色' },\n        { value: '黑色', label: '黑色' },\n        { value: '红色', label: '红色' },\n        { value: '白色', label: '白色' },\n        { value: '正常', label: '正常' },\n        { value: '玫红', label: '玫红' }\n      ],\n      partOptions: [\n        { value: '唇部', label: '唇部' },\n        { value: '脑部', label: '脑部' },\n        { value: '颈部', label: '颈部' },\n        { value: '脸部', label: '脸部' },\n        { value: '掌部', label: '掌部' },\n        { value: '手臂', label: '手臂' },\n        { value: '胸部', label: '胸部' },\n        { value: '腹部', label: '腹部' },\n        { value: '腿部', label: '腿部' },\n        { value: '阴部', label: '阴部' },\n        { value: '背部', label: '背部' },\n        { value: '耳部', label: '耳部' },\n        { value: '枕部', label: '枕部' },\n        { value: '眼部', label: '眼部' },\n        { value: '脚底', label: '脚底' },\n        { value: '脚背', label: '脚背' },\n        { value: '肩部', label: '肩部' },\n        { value: '舌部', label: '舌部' },\n        { value: '屁股', label: '屁股' },\n        { value: '口腔', label: '口腔' },\n        { value: '鼻部', label: '鼻部' }\n      ],\n      selectedFile: null,\n      imagePreview: null,\n      isProcessing: false,\n      diagnosisResults: reactive({\n        processed: false,\n        detected: false,\n        detectedType: '',\n        confidence: 0,\n        resultImage: '',\n        isLoadingRecommendation: false,\n        treatmentSuggestion: '',\n        precautions: '', // 新字段：注意事项\n        emergencyInstructions: '', // 保留兼容旧版API\n        disclaimer: '',\n        predictedColor: '', // 新增：预测颜色\n        predictedPart: '',  // 新增：预测部位\n      }),\n      isLoadingRecommendation: false,\n      activeCollapseItems: ['1', '2'],\n      pollingInterval: null,\n      diagnosisError: '',\n      isEditing: false // 新增：控制是否处于编辑模式\n    };\n  },\n  computed: {\n    confidenceColor() {\n      const confidence = this.diagnosisResults.confidence;\n      if (confidence > 0.8) return '#F56C6C';\n      if (confidence > 0.5) return '#E6A23C';\n      return '#67C23A';\n    }\n  },\n  created() {\n    console.log('[血管瘤诊断] 组件created钩子触发');\n  },\n  mounted() {\n    console.log('[血管瘤诊断] 组件mounted钩子触发，DOM已挂载');\n    document.title = '血管瘤智能诊断';\n    \n    // 通知父组件此页面已加载\n    this.$nextTick(() => {\n      console.log('[血管瘤诊断] 组件完全渲染完成');\n      if (window.parent) {\n        try {\n          window.parent.postMessage({\n            type: 'PAGE_LOADED',\n            page: 'hemangioma-diagnosis'\n          }, '*');\n        } catch (e) {\n          console.error('[血管瘤诊断] 发送页面加载消息失败:', e);\n        }\n      }\n    });\n  },\n  methods: {\n    handleFileChange(file) {\n      if (!file) {\n        this.selectedFile = null;\n        this.imagePreview = null;\n        return;\n      }\n      \n      // 验证文件大小（10MB最大）\n      if (file.raw.size > 10 * 1024 * 1024) {\n        ElMessage.error('文件大小超过限制(10MB)');\n        this.selectedFile = null;\n        this.imagePreview = null;\n        return;\n      }\n      \n      this.selectedFile = file.raw;\n      \n      // 创建图片预览\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        this.imagePreview = e.target.result;\n      };\n      reader.readAsDataURL(file.raw);\n    },\n    processDiagnosis() {\n      // 设置加载状态\n      this.isProcessing = true;\n      this.diagnosisError = '';\n      this.diagnosisResults = {\n        processed: false,\n        detected: false,\n        confidence: 0,\n        detectedType: '',\n        treatmentSuggestion: '',\n        precautions: '', // 新字段：注意事项\n        emergencyInstructions: '', // 保留兼容旧版API\n        resultImage: '',\n        id: null,\n        isLoadingRecommendation: false, // 追踪是否正在加载治疗建议\n        predictedColor: '', // 新增：预测颜色\n        predictedPart: '',  // 新增：预测部位\n      };\n\n      // 1. 验证是否已上传图像和选择血管质地\n      if (!this.selectedFile) {\n        this.diagnosisError = '请上传血管瘤图像';\n        this.isProcessing = false;\n        return;\n      }\n\n      // 获取当前登录用户ID\n      let currentUserId;\n      try {\n        // 尝试从Vuex store获取\n        if (this.$store && this.$store.getters.getUserId) {\n          currentUserId = this.$store.getters.getUserId;\n        } \n        // 或尝试从localStorage获取完整用户信息\n        else {\n          const userStr = localStorage.getItem('user');\n          if (userStr) {\n            const userObj = JSON.parse(userStr);\n            // 确保使用数字ID，而不是自定义ID\n            currentUserId = userObj.id;\n            \n            // 如果没有数字ID但有自定义ID，则记录日志但不使用自定义ID\n            if (!currentUserId && userObj.customId) {\n              console.log('警告: 用户没有数字ID，仅有自定义ID:', userObj.customId);\n            }\n          }\n        }\n      } catch (error) {\n        console.error('获取用户ID失败:', error);\n      }\n\n      // 如果没有获取到用户ID，则提示用户登录\n      if (!currentUserId) {\n        this.isProcessing = false;\n        ElMessage.error('未检测到登录用户，请先登录系统');\n        return;\n      }\n\n      // 2. 上传图像并保存诊断记录\n      const formData = new FormData();\n      formData.append('file', this.selectedFile);\n      \n      // 添加表单数据\n      if (this.formData.patientAge) formData.append('patient_age', this.formData.patientAge.toString());\n      if (this.formData.patientGender) formData.append('gender', this.formData.patientGender);\n      if (this.formData.originType) formData.append('origin_type', this.formData.originType);\n      if (this.formData.vesselTexture) formData.append('vessel_texture', this.formData.vesselTexture);\n      \n      // 确保用户ID作为数字添加到表单中\n      formData.append('user_id', currentUserId.toString());\n\n      console.log('发送诊断请求，用户ID:', currentUserId);\n\n      // 发送请求\n      axios.post('/medical/api/hemangioma-diagnoses/upload-and-diagnose', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'X-User-ID': currentUserId.toString()\n        },\n        timeout: 60000 // 增加超时时间到60秒\n      })\n      .then(response => {\n        console.log('诊断数据上传成功:', response.data);\n        \n        // 3. 处理响应，显示诊断结果\n        this.diagnosisResults.processed = true;\n        this.diagnosisResults.id = response.data.id;\n        \n        // 检查是否检测到血管瘤（通过detectedType字段判断）\n        const detectedType = response.data.detectedType;\n        this.diagnosisResults.detected = detectedType && detectedType.length > 0;\n        \n        // 设置置信度\n        this.diagnosisResults.confidence = response.data.confidence || 0;\n        \n        // 设置检测到的类型\n        this.diagnosisResults.detectedType = detectedType || '';\n        \n        // 设置结果图像 - 优先使用处理后的图像路径\n        if (response.data.processedImagePath) {\n          const baseUrl = window.location.origin; // 使用当前域名作为基础URL\n          this.diagnosisResults.resultImage = baseUrl + response.data.processedImagePath;\n        }\n\n        // 新增：填充颜色和部位信息\n        this.diagnosisResults.predictedColor = response.data.color || '';\n        this.diagnosisResults.predictedPart = response.data.bodyPart || '';\n\n        // 检查是否已有治疗建议（通常初次返回时没有，需要轮询获取）\n        if (!response.data.treatmentSuggestion) {\n          // 如果没有治疗建议，标记为正在加载并开始轮询\n          this.diagnosisResults.isLoadingRecommendation = true;\n          this.startPolling(response.data.id);\n        } else {\n          // 如果已经有治疗建议，直接显示\n          this.diagnosisResults.treatmentSuggestion = response.data.treatmentSuggestion;\n          // 优先使用precautions字段，如果不存在则回退到emergencyInstructions\n          this.diagnosisResults.precautions = response.data.precautions || response.data.emergencyInstructions;\n          // 兼容旧版本\n          this.diagnosisResults.emergencyInstructions = response.data.emergencyInstructions;\n        }\n        \n        // 重置处理状态\n        this.isProcessing = false;\n      })\n      .catch(error => {\n        console.error('诊断处理失败:', error);\n        this.diagnosisError = `诊断处理失败，请重试: ${error.message}`;\n        this.isProcessing = false;\n      });\n    },\n\n    startPolling(diagnosisId) {\n        console.log('开始轮询获取诊断建议，ID:', diagnosisId);\n        this.pollingInterval = setInterval(() => {\n            this.pollForResults(diagnosisId);\n        }, 3000); // 每3秒轮询一次\n\n        // 设置一个超时，比如3分钟后停止轮询，防止无限循环\n        setTimeout(() => {\n            if (this.pollingInterval) {\n                clearInterval(this.pollingInterval);\n                this.pollingInterval = null;\n                if(this.diagnosisResults.isLoadingRecommendation) {\n                    this.diagnosisResults.isLoadingRecommendation = false;\n                    ElMessage.info('已获取部分诊断建议，更多详细内容稍后可能会继续更新。');\n                }\n            }\n        }, 180000); // 3分钟后停止轮询\n    },\n\n    pollForResults(diagnosisId) {\n      axios.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`)\n        .then(response => {\n          const data = response.data;\n          console.log('轮询获取诊断结果:', data);\n          \n          // 检查是否有新的治疗建议内容\n          let hasUpdates = false;\n          \n          // 检查并更新治疗建议\n          if (data.treatmentSuggestion && !this.diagnosisResults.treatmentSuggestion) {\n            this.diagnosisResults.treatmentSuggestion = data.treatmentSuggestion;\n            hasUpdates = true;\n          }\n          \n          // 检查并更新注意事项\n          if (data.precautions && !this.diagnosisResults.precautions) {\n            this.diagnosisResults.precautions = data.precautions;\n            hasUpdates = true;\n          }\n          \n          // 如果已经获取到所有建议，停止轮询\n          if (\n            data.treatmentSuggestion && \n            data.precautions\n          ) {\n            if (this.pollingInterval) {\n              clearInterval(this.pollingInterval);\n              this.pollingInterval = null;\n              this.diagnosisResults.isLoadingRecommendation = false;\n            }\n          }\n        })\n        .catch(error => {\n          console.error('轮询获取诊断结果失败:', error);\n          // 轮询错误不需要显示给用户，继续尝试\n        });\n    },\n    \n    backToUpload() {\n      this.diagnosisResults.processed = false;\n    },\n    \n    resetForm() {\n      this.formData.patientAge = '';\n      this.formData.patientGender = '';\n      this.formData.originType = '';\n      this.formData.vesselTexture = '';\n      \n      this.selectedFile = null;\n      this.imagePreview = null;\n    },\n    modifyReport() {\n      const diagnosisId = this.diagnosisResults.id;\n      if (!diagnosisId) {\n        ElMessage.error('无法修改报告：缺少诊断ID');\n        return;\n      }\n      \n      // 使用 Vue Router 进行页面跳转\n      this.$router.push({\n        path: `/app/case/${diagnosisId}/annotate-and-form`,\n        query: {\n          mode: 'edit', // 明确告知目标页面是编辑模式\n          from: 'diagnosis-page' // 可选：标记来源页面\n        }\n      });\n    },\n\n    async saveReport() {\n      if (!this.diagnosisResults.id) {\n        ElMessage.error('诊断ID丢失，无法保存报告');\n        return;\n      }\n      \n      const updatedData = {\n        patientAge: this.formData.patientAge,\n        gender: this.formData.patientGender,\n        originType: this.formData.originType,\n        vesselTexture: this.formData.vesselTexture,\n        color: this.diagnosisResults.predictedColor ? this.diagnosisResults.predictedColor.name : '',\n        bodyPart: this.diagnosisResults.predictedPart ? this.diagnosisResults.predictedPart.name : '',\n        reviewNotes: this.diagnosisResults.reviewNotes,\n        treatmentSuggestion: this.diagnosisResults.treatmentSuggestion,\n        precautions: this.diagnosisResults.precautions || this.diagnosisResults.emergencyInstructions,\n        status: 'REVIEWED' // 明确设置状态为\"已标注\"\n      };\n\n      try {\n        const response = await axios.put(`/medical/api/hemangioma-diagnoses/${this.diagnosisResults.id}`, updatedData);\n        // 移除成功提示消息\n        this.isEditing = false;\n        \n        // 跳转到主页面或诊断列表页面\n        setTimeout(() => {\n          // 使用Vue Router跳转到病例列表页面\n          this.$router.push('/app/dashboard');\n        }, 1500); // 延迟1.5秒后跳转，让用户看到成功消息\n      } catch (error) {\n        console.error('保存报告失败:', error);\n        ElMessage.error('保存报告失败，请稍后重试');\n      }\n    },\n    // 获取当前用户角色\n    getCurrentUserRole() {\n      try {\n        // 尝试从Vuex store获取\n        if (this.$store && this.$store.getters.getUserRole) {\n          return this.$store.getters.getUserRole;\n        } \n        // 或尝试从localStorage获取完整用户信息\n        else {\n          const userStr = localStorage.getItem('user');\n          if (userStr) {\n            const userObj = JSON.parse(userStr);\n            return userObj.role || userObj.userRole;\n          }\n        }\n      } catch (error) {\n        console.error('获取用户角色失败:', error);\n        return null;\n      }\n      return null;\n    },\n    confirmReport() {\n      // 确认报告并重置诊断页面到初始状态\n      const diagnosisId = this.diagnosisResults.id;\n      if (!diagnosisId) {\n        ElMessage.error('无法确认报告：缺少诊断ID');\n        return;\n      }\n\n      // 获取当前用户角色\n      const userRole = this.getCurrentUserRole();\n      console.log('当前用户角色:', userRole);\n      \n      // 根据用户角色设置不同状态\n      let status = 'SUBMITTED'; // 默认为待审核状态\n      let statusMessage = '待审核';\n      \n      // 如果是管理员或审核医生，则设置为已完成状态\n      if (userRole && (userRole.toLowerCase().includes('admin') || userRole.toLowerCase().includes('reviewer'))) {\n        status = 'APPROVED'; // 已完成状态\n        statusMessage = '已完成';\n      }\n      \n      // 更新诊断状态\n      axios.put(`/medical/api/hemangioma-diagnoses/${diagnosisId}/status`, null, {\n        params: {\n          status: status\n        }\n      })\n      .then(response => {\n        console.log('更新状态成功:', response.data);\n        // 移除成功提示消息\n        \n        // 重置页面到初始状态\n        this.resetDiagnosis();\n        this.resetForm();\n      })\n      .catch(error => {\n        console.error('更新状态失败:', error);\n        ElMessage.error(`更新状态失败: ${error.message}`);\n        \n        // 即使失败也重置页面\n        this.resetDiagnosis();\n        this.resetForm();\n      });\n    },\n    resetImage(e) {\n      if (e) {\n        e.stopPropagation(); // 防止触发上传事件\n      }\n      this.selectedFile = null;\n      this.imagePreview = null;\n    },\n    // 获取血管质地标签\n    getVesselTextureLabel(value) {\n      const textureMap = {\n        'soft': '质软',\n        'elastic': '质韧',\n        'hard': '质硬',\n        'cystic': '囊性',\n        'compressible': '可压缩'\n      };\n      return textureMap[value] || value;\n    },\n    resetDiagnosis() {\n      // 恢复所有状态到初始值\n      this.resetForm();\n      this.isEditing = false;\n\n      // 重置诊断结果\n      Object.assign(this.diagnosisResults, {\n        processed: false,\n        detected: false,\n        isLoadingRecommendation: false,\n        id: null,\n        resultImage: '',\n        detectedType: '',\n        confidence: 0,\n        predictedColor: '',\n        predictedPart: '',\n        vesselTexture: '',\n        treatmentSuggestion: '',\n        precautions: '',\n        reviewNotes: ''\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 15px;\n}\n\n.blood-vessel-title {\n  font-size: 22px;\n  margin-bottom: 15px;\n}\n\n.diagnosis-panel {\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  overflow: hidden;\n  background: #fff;\n}\n\n.panel-heading {\n  padding: 12px 15px;\n  border-bottom: 1px solid #e4e7ed;\n  background-color: #f5f7fa;\n  font-size: 16px;\n}\n\n.form-content {\n  padding: 15px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n/* 移除旧的布局类 */\n.form-row, .horizontal-groups {\n  /* 保留以兼容其他地方使用 */\n}\n\n/* 自适应字段容器 - 灵活布局 */\n.adaptive-fields-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 15px;\n}\n\n.field-item {\n  flex: 1 1 150px; /* 灵活增长，最小宽度150px */\n  min-width: 150px;\n  max-width: calc(100% / 3 - 10px); /* 在较小屏幕上最多3个一行 */\n}\n\n/* 大屏幕上最多6个一行 */\n@media (min-width: 1200px) {\n  .field-item {\n    max-width: calc(100% / 6 - 13px);\n  }\n}\n\n/* 中等屏幕上最多4个一行 */\n@media (min-width: 900px) and (max-width: 1199px) {\n  .field-item {\n    max-width: calc(100% / 4 - 12px);\n  }\n}\n\n/* 确保表单字段有合适的高度 */\n.form-field {\n  width: 100%;\n}\n\n.form-field .el-select {\n  width: 100%;\n}\n\n.form-label {\n  font-weight: bold;\n  margin-bottom: 6px;\n}\n\n.upload-area {\n  border: 1px dashed #d9d9d9;\n  border-radius: 6px;\n  padding: 20px;\n  text-align: center;\n  cursor: pointer;\n}\n\n.upload-icon {\n  font-size: 48px;\n  color: #8c939d;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #606266;\n  margin-top: 10px;\n}\n\n.image-preview-container {\n  display: flex;\n  justify-content: center;\n  position: relative;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 300px;\n}\n\n.preview-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(0,0,0,0.3);\n  opacity: 0;\n  transition: opacity 0.3s;\n}\n\n.image-preview-container:hover .preview-overlay {\n  opacity: 1;\n}\n\n.form-buttons {\n  margin-top: 20px;\n  text-align: center;\n}\n\n@media (max-width: 768px) {\n  .form-row {\n    flex-direction: column;\n  }\n}\n\n.diagnosis-results {\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.diagnosis-recommendations {\n  margin: 20px 0;\n}\n\n.result-image-container {\n  margin: 20px auto;\n  text-align: center;\n  max-width: 600px;\n}\n\n.result-image {\n  max-width: 100%;\n  max-height: 500px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  object-fit: contain;\n}\n\n.placeholder-image {\n  height: 300px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #f5f7fa;\n  color: #909399;\n}\n\n.recommendation-box {\n  background-color: #f9f9f9;\n  border-left: 4px solid #409EFF;\n  padding: 15px;\n  border-radius: 4px;\n}\n\n.recommendation-box p {\n  margin: 0;\n  color: #333;\n}\n\n.recommendation-loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  font-size: 16px;\n  color: #606266;\n}\n\n.recommendation-loading .el-icon {\n  margin-right: 8px;\n  font-size: 20px;\n}\n\n.recommendation-collapse {\n  margin-top: 10px;\n}\n\n.recommendation-collapse p {\n  padding: 0 10px;\n  line-height: 1.6;\n  color: #606266;\n}\n\n.disclaimer {\n  margin: 20px 0;\n  padding: 10px;\n  background-color: #f7f7f9;\n  border-radius: 4px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.disclaimer p {\n  margin: 0;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.result-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.result-title {\n  font-size: 18px;\n  font-weight: bold;\n}\n\n.detection-info {\n  margin-bottom: 20px;\n}\n\n.recommendation-title {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.recommendation-section {\n  margin: 15px 0;\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n}\n\n.recommendation-section h3 {\n  margin-top: 0;\n  color: #409EFF;\n  font-size: 16px;\n}\n\n.recommendation-placeholder {\n  margin: 15px 0;\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  min-height: 80px;\n}\n\n.result-actions {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  margin-top: 30px;\n}\n\n@media (max-width: 768px) {\n  .result-actions {\n    flex-direction: column;\n  }\n}\n</style> ", "import { render } from \"./HemangiomaDiagnosis.vue?vue&type=template&id=3c2b4d8f&scoped=true\"\nimport script from \"./HemangiomaDiagnosis.vue?vue&type=script&lang=js\"\nexport * from \"./HemangiomaDiagnosis.vue?vue&type=script&lang=js\"\n\nimport \"./HemangiomaDiagnosis.vue?vue&type=style&index=0&id=3c2b4d8f&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-3c2b4d8f\"]])\n\nexport default __exports__"], "names": ["class", "key", "style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "$data", "diagnosisResults", "processed", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_input", "modelValue", "formData", "patientAge", "_cache", "$event", "type", "placeholder", "_hoisted_7", "_hoisted_8", "_component_el_radio_group", "patientGender", "_withCtx", "_component_el_radio", "label", "_createTextVNode", "_", "__", "_hoisted_9", "_hoisted_10", "originType", "_hoisted_11", "_hoisted_12", "_component_el_select", "vesselTexture", "_component_el_option", "value", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_component_el_upload", "drag", "action", "$options", "handleFileChange", "limit", "imagePreview", "_hoisted_17", "src", "alt", "_hoisted_18", "_hoisted_19", "_component_el_button", "size", "onClick", "_withModifiers", "resetImage", "_hoisted_16", "_component_el_icon", "_component_i_ep_upload_filled", "_hoisted_20", "disabled", "isProcessing", "selectedFile", "processDiagnosis", "_createBlock", "_component_i_ep_loading", "_toDisplayString", "resetForm", "_hoisted_21", "_component_el_card", "header", "_hoisted_22", "detected", "_hoisted_23", "_hoisted_24", "_hoisted_25", "resultImage", "_hoisted_26", "_hoisted_27", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "_hoisted_28", "_hoisted_29", "Math", "round", "confidence", "detectedType", "predictedColor", "predictedPart", "_hoisted_30", "_component_el_divider", "_hoisted_31", "isLoadingRecommendation", "_hoisted_32", "treatmentSuggestion", "_hoisted_33", "_hoisted_34", "_component_el_skeleton", "rows", "animated", "_hoisted_35", "precautions", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_component_el_alert", "title", "description", "closable", "_hoisted_40", "modifyReport", "saveReport", "confirmReport", "resetDiagnosis", "name", "components", "UploadFilled", "Loading", "data", "colorOptions", "partOptions", "reactive", "emergencyInstructions", "disclaimer", "activeCollapseItems", "pollingInterval", "diagnosisError", "isEditing", "computed", "confidenceColor", "this", "created", "console", "log", "mounted", "document", "$nextTick", "window", "parent", "postMessage", "page", "e", "error", "methods", "file", "_this", "raw", "ElMessage", "reader", "FileReader", "onload", "target", "result", "readAsDataURL", "currentUserId", "_this2", "id", "$store", "getters", "getUserId", "userStr", "localStorage", "getItem", "userObj", "JSON", "parse", "customId", "FormData", "append", "toString", "axios", "headers", "timeout", "then", "response", "length", "processedImagePath", "baseUrl", "location", "origin", "color", "bodyPart", "startPolling", "concat", "message", "diagnosisId", "_this3", "setInterval", "pollForResults", "setTimeout", "clearInterval", "info", "_this4", "backToUpload", "$router", "push", "path", "query", "mode", "from", "_this5", "_asyncToGenerator", "_regenerator", "m", "_callee", "updatedData", "_t", "w", "_context", "n", "a", "gender", "reviewNotes", "status", "p", "v", "getCurrentUserRole", "getUserRole", "role", "userRole", "_this6", "toLowerCase", "includes", "params", "stopPropagation", "getVesselTextureLabel", "textureMap", "Object", "assign", "__exports__", "render"], "sourceRoot": ""}