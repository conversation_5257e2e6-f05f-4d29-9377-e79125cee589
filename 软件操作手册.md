# 血管瘤人工智能辅助治疗系统
# 软件操作手册

**软件名称：血管瘤人工智能辅助治疗系统**
**版本号：V1.0**
**编写日期：2025-XX-XX**
**编写单位：[您的单位名称]**

---
## 文档修订历史

| 版本号 | 修订日期 | 修订人 | 修订描述 |
| :--- | :--- | :--- | :--- |
| V1.0 | 2025-XX-XX | [您的姓名] | 初稿创建，覆盖系统所有核心功能模块。 |

---
## 目录
*(注：此处为目录占位符，最终生成PDF时可自动生成)*
1.  引言
    1.1. 编写目的
    1.2. 软件简介
    1.3. 主要特点
    1.4. 目标用户
    1.5. 文档约定
2.  系统概述
    2.1. 系统设计理念与目标
    2.2. 用户角色与核心职责
    2.3. 系统核心工作流程
3.  系统环境要求与准备
    3.1. 硬件环境要求
    3.2. 软件环境要求
    3.3. 网络环境建议
4.  系统访问与登录
    4.1. 访问系统
    4.2. 用户注册详解
    4.3. 用户登录详解
    4.4. 密码找回
    4.5. 安全退出
5.  核心功能模块详解
    5.1. 病例管理与标注模块 (医生角色)
    5.2. 病例审核模块 (审核员/管理员角色)
    5.3. 后台管理模块 (管理员角色)
6.  故障与排除 (FAQ)
7.  术语表
8.  附录A：场景演练
9.  附录B：常见错误与提示信息
10. 联系我们

---

## 1. 引言

### 1.1 编写目的
本文档是 **"血管瘤人工智能辅助治疗系统"** (以下简称"本系统")的官方操作指南。其编写的核心目的在于提供一份全面、详尽、清晰的用户操作说明，确保每一位使用者都能准确、高效地利用本系统的各项功能。

通过本手册，用户可以：
-   了解系统的设计目标、核心价值和主要功能构成。
-   熟悉系统的运行环境要求和访问方式。
-   掌握从注册、登录到完成核心业务操作的全过程。
-   在遇到操作疑问或常见问题时，能够快速找到解决方案。

本手册力求图文并茂、步骤清晰，是您使用本系统不可或缺的参考资料。

### 1.2 软件简介
本系统是一套基于B/S（浏览器/服务器）架构，集成了人工智能（AI）辅助诊断与分析功能的专业医疗影像管理与协作平台。我们专注于血管瘤及脉管畸形这一特定医疗领域，旨在为临床医生和医学研究者提供一站式的数字化解决方案。

系统将传统病历管理、复杂的医学影像分析与前沿的人工智能技术相结合，构建了一个高效、智能、协作的线上工作环境。

### 1.3 主要特点
-   **AI智能诊断**：集成先进的深度学习模型，可对上传的医学影像进行自动化分析，智能识别血管瘤特征，为医生提供量化的、客观的初步诊断建议，有效提升诊断效率和一致性。
-   **专业的病例管理**：提供结构化的病例数据存储方案，能够系统地管理患者信息、既往史、临床表现、影像资料、诊断记录等全方位信息，形成完整的电子健康档案。
-   **强大的在线图像标注**：内建专业、易用的在线图像标注工具集，支持医生在浏览器端直接对医学影像进行精确的病灶区域框选和分类标签，操作直观、便捷。
-   **高效的团队协作**：支持多位医生组成线上医疗团队，共同对病例进行管理、标注、讨论和审核。系统的流程化设计促进了医疗团队内部的沟通、协作与质量控制。
-   **清晰的角色与权限管理**：预设了医生、审核员、管理员等多种用户角色，每种角色拥有严格定义的操作权限，确保了医疗数据的安全、保密和操作流程的规范性。

### 1.4 目标用户
本手册主要面向以下用户群体：
-   **临床医生**：特别是皮肤科、儿科、血管外科等相关科室的医生，是本系统的主要使用者。
-   **医学研究人员**：需要进行血管瘤相关数据收集、整理和分析的研究者。
-   **科室或医院管理员**：负责管理本科室或本单位的用户、数据和团队。
-   **医学生或实习医生**：在指导下使用本系统进行学习和实践。

### 1.5 文档约定
为方便阅读，本手册采用以下书写约定：
-   **【按钮/菜单】**：使用加粗和方括号表示界面上可以点击的按钮、菜单项或链接。例如：【登 录】按钮。
-   **"页面/模块"**：使用加粗和引号表示系统的功能模块或页面名称。例如："我的病例标注"页面。
-   *`输入内容`*：使用斜体和反引号表示需要用户输入的文本内容。
-   **[截图 X-X: 描述]**：作为截图的占位符和图注，描述了该截图应展示的界面内容。

---

## 2. 系统概述

### 2.1 系统设计理念与目标
本系统的设计核心是"技术赋能医疗"，旨在利用现代信息技术与人工智能，优化传统血管瘤诊疗流程。我们的目标是：
1.  **效率提升**: 将医生从重复、繁琐的阅片和记录工作中解放出来。
2.  **精度辅助**: 为医生的诊断提供客观数据参考，辅助决策。
3.  **数据资产化**: 将零散的病例资料转化为结构化、可用于研究的宝贵数据资产。
4.  **协作无界**: 打破物理空间限制，实现高效的远程团队协作和会诊。

### 2.2 用户角色与核心职责
本系统预设了三种核心用户角色，各角色职责分明，权限逐级递增。

| 角色 | 核心职责 | 详细权限说明 |
| :--- | :--- | :--- |
| **医生 (DOCTOR)** | 病例的创建者和执行者 | - 基础权限：注册、登录、退出、修改个人信息。<br>- 病例管理：创建新病例、上传图像、编辑和保存草稿。<br>- 核心操作：对图像进行病灶标注、填写详细的病例信息表单。<br>- 流程操作：将完成的病例提交审核。<br>- 权限申请：可向管理员申请升级为"审核员"角色。 |
| **审核员 (REVIEWER)** | 诊断的审核与质量控制者 | - **包含"医生"角色的全部权限。**<br>- 审核工作：查看并批阅其他医生提交的"待审核"病例。<br>- 审核决策：可对病例进行【通过】或【驳回】操作，并填写审核意见。<br>- 团队管理：可以创建医疗团队，并邀请其他用户加入。 |
| **管理员 (ADMIN)** | 系统的最高管理者 | - **包含"审核员"角色的全部权限。**<br>- 用户管理：管理系统中所有用户账号的增、删、改、查，可直接分配用户角色。<br>- 权限审批：处理"医生"角色提交的权限升级申请。<br>- 团队管理：管理系统中的所有团队。<br>- 全局数据管理：可查看和管理系统中的所有病例及图像文件。 |

### 2.3 系统核心工作流程
本系统的核心业务围绕病例的"创建 -> 标注 -> 审核"这一闭环流程展开。

```mermaid
graph TD
    A(开始) --> B{用户登录};
    B --> C[角色: 医生];
    C --> D[访问"我的病例标注"页面];
    D --> E[点击【新建标注】];
    E --> F[上传图像 & 填写表单];
    F --> G[绘制标注 & 选择标签];
    G --> H{保存或提交?};
    H -- 保存 --> I[病例存为"草稿"状态];
    I --> D;
    H -- 提交 --> J[病例变为"待审核"状态];
    J --> K[流转至审核列表];
    K --> L{角色: 审核员/管理员};
    L --> M[访问"标注审核"页面];
    M --> N[选择病例并【批阅】];
    N --> O{审核通过?};
    O -- 是 --> P[病例变为"已通过"状态];
    P --> Q(流程结束);
    O -- 否 --> R[填写驳回意见];
    R --> S[病例变为"已驳回"状态];
    S --> D;
```
*(上图为系统核心工作流程示意图)*

---

## 3. 系统环境要求与准备

### 3.1 硬件环境要求
为了保证流畅、高效地使用本系统，我们建议您的计算机硬件配置不低于以下标准：
-   **处理器 (CPU)**: Intel Core i5 或 AMD Ryzen 5 系列及以上。
-   **内存 (RAM)**: 推荐 8 GB 及以上。更大的内存有助于在处理高分辨率图像时获得更流畅的体验。
-   **显卡 (GPU)**: 无特殊要求，主流集成显卡即可满足日常使用。
-   **显示器分辨率**: 为了获得最佳的视觉效果和操作空间，推荐使用分辨率为 1920x1080 (全高清) 或更高的显示器。
-   **输入设备**: 标准键盘和鼠标。

### 3.2 软件环境要求
-   **操作系统**:
    -   Windows: 推荐 Windows 10 或 Windows 11。
    -   macOS: 推荐 macOS 11 Big Sur 或更高版本。
    -   Linux: 主流发行版，如 Ubuntu 20.04 LTS 或更高版本。
-   **浏览器**: 本系统基于现代Web技术构建，为获得最佳的兼容性、性能和安全性，请务必使用以下浏览器的**最新稳定版本**：
    -   **Google Chrome (谷歌浏览器)** (强烈推荐)
    -   Microsoft Edge (基于Chromium内核)
    -   Mozilla Firefox (火狐浏览器)
    -   Safari (仅限macOS)
    
> **重要说明**：请勿使用 Internet Explorer (IE) 或其他过时的浏览器，否则可能导致功能异常或无法使用。

### 3.3 网络环境建议
-   **网络连接**: 系统所有功能均需在联网环境下使用，请确保您的计算机已连接到互联网。
-   **网络速度**: 由于系统涉及医学影像的上传和加载，为保证操作流畅，推荐使用下行速度不低于 20Mbps 的宽带连接。网络速度越快，图像加载时间越短，体验越好。

---

## 4. 系统访问与登录
本章节将以最详尽的步骤，分解说明用户如何从零开始进入并使用本系统，涵盖了从访问、注册、登录到安全退出的全过程。每一个步骤都将描述到原子级别，以确保用户对系统的每一个响应都有清晰的预期。

### 4.1 访问系统
本系统为B/S（浏览器/服务器）架构的应用程序，用户无需在本地计算机上安装任何客户端软件，仅需通过标准的网页浏览器即可访问。

**操作场景**: 任何用户在开始一切工作前的第一个步骤。

1.  **前置条件**:
    -   用户的计算机已根据 **"3. 系统环境要求与准备"** 章节完成配置。
    -   计算机已成功连接到互联网。
    -   用户已从系统管理员处获得了本系统的唯一访问地址（URL）。

2.  **操作步骤**:
    1.  **启动浏览器**: 在您的计算机操作系统中，找到并双击打开一个符合要求的浏览器程序图标，例如 **Google Chrome**。
    2.  **定位地址栏**: 在浏览器窗口的顶部，找到用于输入网址的地址栏。
    3.  **输入系统地址**: 使用键盘，在地址栏中准确无误地输入由系统管理员提供的系统访问URL。
        -   地址格式示例: `http://<服务器IP地址或域名>:<端口号>`
    4.  **确认访问**: 按下键盘上的 **【Enter】** 键。

3.  **系统响应与预期结果**:
    -   按下回车键后，浏览器将向指定地址的服务器发送页面请求。
    -   页面开始加载，您可能会短暂地看到一个空白页面。
    -   数秒内（取决于网络速度），页面加载完成，您将看到本系统的 **"用户登录"** 页面。页面的标题通常为 **"血管瘤人工智能辅助治疗系统"**，中央区域会有一个清晰的登录表单。
    -   至此，您已成功访问本系统。

**[截图 4.1-1: 浏览器窗口特写，地址栏中清晰地显示了系统的URL，页面内容区已完全加载出登录界面的所有元素。]**

### 4.2 用户注册详解
对于初次使用的用户，必须先注册一个个人专属账号，这是使用系统所有功能的前提。

**操作场景**: 新加入的医生、研究员或管理员首次使用本系统。

#### 4.2.1 步骤一：导航至注册页面
**[截图 4.2.1-1: 系统登录页面的完整视图，用红色箭头或高亮框醒目地标示出位于登录框下方的【立即注册】文本链接。]**

1.  **前置条件**: 您已成功访问本系统，当前停留在 **"用户登录"** 页面。
2.  **界面定位**: 将您的目光聚焦在登录表单的下方区域。您会找到一个内容为 **【立即注册】** 的蓝色可点击文本链接。
3.  **用户操作**:
    -   将鼠标光标移动到 **【立即注册】** 文本链接上。
    -   光标的样式会从默认的箭头变为一只小手形状，表示此链接可以点击。
    -   单击鼠标左键。
4.  **系统响应**:
    -   **成功**: 页面内容会发生即时切换，从"登录"视图平滑过渡到 **"用户注册"** 视图。您会看到一组用于信息录入的全新空白表单，页面标题或主提示文字会变为"用户注册"或类似内容。
    -   **失败**: 此操作无典型失败场景。如果链接无法点击，请检查浏览器是否禁用了JavaScript，或尝试刷新页面后重试。

#### 4.2.2 步骤二：逐项填写注册信息
进入注册页面后，您需要准确、完整地填写表单中的每一个字段。所有带红色星号（*）的字段均为必填项。

**[截图 4.2.2-1: 空白的用户注册表单完整界面，每个输入框旁都有清晰的中文标签，且必填项旁有红色星号标识。]**

---
##### ********* 字段：姓名**
-   **功能描述**: 此姓名将作为您在系统内的身份标识，会出现在您的个人资料、操作日志、病例报告等所有与您身份相关的位置。
-   **界面元素**: 一个标准的单行文本输入框，左侧有"姓名"标签。
-   **操作**:
    1.  用鼠标左键单击该输入框，框内会出现闪烁的光标。
    2.  通过键盘输入您的真实姓名。
-   **填写要求**:
    -   必填项。
    -   建议使用您在医疗机构中常用的职业姓名，以便于同事和管理员进行识别和管理。
    -   长度限制通常为2至20个中英文字符。
-   **示例**: *`张三`*
-   **客户端校验**: 当您离开此输入框时，如果内容为空，框体边缘可能会变为红色，并在一旁出现"姓名不能为空"的提示。

---
##### **4.2.2.2 字段：邮箱地址**
-   **功能描述**: 此邮箱极为重要，它将作为您登录系统的**唯一账号**，同时也是接收系统通知（如审核结果）和找回密码的唯一渠道。
-   **界面元素**: 一个标准的单行文本输入框，左侧有"邮箱地址"标签，通常包含一个"@"符号图标作为提示。
-   **操作**: 在输入框内，输入一个您本人拥有、常用且能正常收发邮件的电子邮箱地址。
-   **填写要求**:
    -   必填项。
    -   必须是真实、有效的邮箱格式，例如 `<EMAIL>`。
-   **示例**: *`<EMAIL>`*
-   **客户端校验**:
    -   若输入为空，会提示"邮箱地址不能为空"。
    -   若输入的格式不正确（例如缺少"@"符号或域名），会提示"请输入有效的邮箱地址"。
-   **重要提示**: 请务必确保此邮箱的安全性，并牢记其密码，因为它是您账户所有权的最终凭证。

---
##### **4.2.2.3 字段：设置密码**
-   **功能描述**: 为您的系统账户设置一个安全的登录密码。
-   **界面元素**: 一个密码类型的输入框。为保护您的隐私，您输入的字符将不会明文显示，而是以实心圆点（●）或星号（*）代替。
-   **操作**: 在输入框内，输入您构思好的账户密码。
-   **填写要求**:
    -   必填项。
    -   为保障账户数据安全，密码长度必须不少于8位。
    -   强烈建议使用包含大写字母、小写字母、数字和特殊符号（如 `!@#$%^&*`）的组合密码。
-   **安全警告**: 严禁使用过于简单的、可被轻易猜到的密码（如`123456`、`abcdef`、`password`）。请勿使用与其他网站或应用相同的密码，以防范"撞库攻击"带来的风险。一个强密码是您数据安全的第一道、也是最重要的防线。

---
##### **4.2.2.4 字段：再次输入密码**
-   **功能描述**: 防止您因误操作输错密码，要求您再次输入以进行确认。
-   **界面元素**: 同样是一个密码类型的输入框。
-   **操作**: 在此输入框内，重新输入一遍您刚才在"设置密码"框中输入的密码。
-   **填写要求**:
    -   必填项。
    -   内容必须与上一个"设置密码"框中输入的内容**完全一致**。
-   **客户端校验**: 系统会实时或在提交时自动比对两个密码框的内容。如果两次输入不匹配，会给出明确的错误提示，例如"两次输入的密码不一致"。

---
##### **4.2.2.5 字段：所在医院**
-   **功能描述**: 用于记录您当前所属的医疗机构或单位。此信息有助于团队管理和数据统计。
-   **界面元素**: 标准的单行文本输入框。
-   **操作**: 在输入框内，输入您当前就职的医院完整官方名称。
-   **示例**: *`XX省第一人民医院`*

---
##### **4.2.2.6 字段：所在科室**
-   **功能描述**: 用于记录您所属的具体科室。
-   **界面元素**: 标准的单行文本输入框。
-   **操作**: 在输入框内，输入您当前所在的具体科室名称。
-   **示例**: *`皮肤科`* or *`血管瘤与脉管畸形中心`*

---
#### 4.2.3 步骤三：提交注册申请并处理反馈
1.  **前置条件**: 您已完成上述所有必填表单字段的填写，并确认所有信息准确无误。
2.  **界面定位**: 在注册表单的最下方，有一个视觉上非常显著的 **【立即注册】** 按钮。
3.  **用户操作**: 使用鼠标单击 **【立即注册】** 按钮。

4.  **预期系统反馈（包含所有场景）**:
    -   **场景一：注册成功 (最理想情况)**
        -   **系统行为**:
            1.  前端JavaScript对所有字段进行最终格式校验。
            2.  校验通过后，将您的注册信息加密并通过网络发送到服务器。
            3.  服务器创建账户成功后，会向浏览器返回一个成功的信号。
        -   **界面变化**:
            1.  浏览器界面会弹出一个内容为"注册成功！"的模态对话框或短暂的顶部提示条。
            2.  在您手动关闭提示或数秒后，页面将**自动跳转**回本系统的"用户登录"页面。
        -   **后续操作**: 您现在可以使用刚刚注册的邮箱和密码，在登录页面进行首次登录。

    -   **场景二：注册失败（前端客户端校验）**
        -   **系统行为**: 如果您填写的信息不符合基本格式（例如邮箱格式错误，两次密码不一致，或有必填项为空），在您点击按钮的瞬间，前端JavaScript会立刻进行校验并**阻止**向服务器发送任何请求。
        -   **界面变化**:
            1.  在不符合要求的输入框下方或旁边，会立即出现红色的错误提示文字，例如"请输入有效的邮箱地址"或"两次输入的密码不一致"。
            2.  【立即注册】按钮可能会短暂地左右晃动，以一种视觉上的方式提醒您存在错误。
        -   **后续操作**: 您需要仔细阅读错误提示，定位到错误的输入框，修正其中的内容，直到所有红色提示消失。然后再次点击【立即注册】按钮。
        **[截图 4.2.3-1: 注册表单，其中"邮箱地址"和"再次输入密码"字段下方出现了红色的错误提示文字，框体边缘也变为红色。]**

    -   **场景三：注册失败（后端服务器校验）**
        -   **系统行为**: 如果您填写的信息格式完全正确，但业务逻辑上存在冲突（最常见的情况是：该邮箱已被其他用户注册），信息会成功发送到服务器。服务器进行业务逻辑校验后，发现冲突，并向浏览器返回一个明确的错误信息。
        -   **界面变化**: 页面顶部或表单附近会显示一个来自服务器的错误提示条，内容通常为："该邮箱已被注册，请尝试登录或更换邮箱"。
        -   **后续操作**: 您需要根据服务器的提示，更换一个新的、未被注册过的邮箱地址，或者如果您想起自己可能已注册过，可以直接前往登录页面尝试登录。

### 4.3 用户登录详解
**操作场景**：已经成功注册并拥有账户的用户，访问系统以开始日常工作。

**[截图 4.3-1: 系统登录页面，页面视觉中心是"邮箱地址"和"密码"两个输入框，以及一个蓝色的"登 录"按钮。]**

#### 4.3.1 步骤一：输入登录凭据
1.  **前置条件**：您已经拥有一个本系统的账户，并已成功打开了系统的登录页面。
2.  **界面元素与操作**:
    -   **邮箱地址输入框**:
        -   **定位**: 找到标有"邮箱地址"的输入框。
        -   **操作**: 用鼠标左键单击该输入框，当光标闪烁时，通过键盘输入您注册时使用的**完整**电子邮箱地址。
    -   **密码输入框**:
        -   **定位**: 找到标有"密码"的输入框。
        -   **操作**: 用鼠标左键单击该输入框，当光标闪烁时，通过键盘输入您的账户密码。请特别注意密码的大小写是否正确，以及键盘的大写锁定键（Caps Lock）是否意外开启。

#### 4.3.2 步骤二（可选）：选择"下次自动登录"
-   **功能描述**: 勾选此项后，系统会在您的浏览器中保存一个安全的身份凭证。在凭证有效期内（例如7天），您使用同一台计算机的同一浏览器再次访问本系统时，将无需重复输入密码，系统会自动识别您的身份并直接进入主界面。
-   **界面元素**: 在登录按钮附近，有一个标签为"下次自动登录"或"保持登录"的小复选框。
-   **操作**: 如果您当前是在个人且受信任的计算机上操作，为了方便，可以用鼠标左键单击该复选框，框内出现一个"✓"符号表示已成功选中。
-   **安全警告**: 为了您的账户和敏感医疗数据的安全，请**绝对不要**在任何公共计算机（如图书馆、网吧、酒店商务中心）或他人的计算机上使用此功能。否则可能导致您的账户信息泄露或被未授权使用。

#### 4.3.3 步骤三：执行登录
1.  **前置条件**: 您已确保"邮箱地址"和"密码"均已正确填写。
2.  **界面定位**: 在输入框下方，找到那个唯一的、颜色突出的 **【登 录】** 按钮。
3.  **用户操作**: 使用鼠标左键单击 **【登 录】** 按钮。
4.  **预期系统反馈**:
    -   **场景一：登录成功**
        -   **系统行为**: 前端将您输入的凭据发送至服务器。服务器验证通过后，会生成一个具有时效性的身份令牌（Token）并返回给前端。前端浏览器会自动将此令牌存储起来，作为您后续所有操作的"通行证"。
        -   **界面变化**: 页面将自动跳转至系统的主功能界面。对于"医生"角色的用户，默认进入的通常是 **"我的病例标注"** 工作台页面。您会看到一个全新的、功能丰富的界面布局，包括左侧的导航菜单栏和页面右上角显示的您的用户名。
        -   **后续操作**: 您可以正式开始使用系统的各项功能。
        **[截图 4.3.3-1: 登录成功后的系统主界面，左侧是功能导航栏，顶部是标题栏，右上角显示用户名，主内容区是工作台。]**

    -   **场景二：登录失败**
        -   **系统行为**: 服务器验证后发现您的邮箱或密码不匹配，会返回一个认证失败的错误信号。
        -   **界面变化**: 页面**不会**发生跳转，仍停留在登录页。在【登 录】按钮的上方或表单附近，会出现一条红色的错误提示信息，内容通常为 **"用户名或密码错误"**。您之前输入的邮箱和密码通常会被保留，以便您进行修改。
        -   **后续操作**:
            1.  请仔细重新检查您输入的邮箱和密码是否准确无误，特别注意：
                -   键盘的**大小写锁定**是否开启？
                -   输入法是否处于英文半角状态？
                -   邮箱地址是否完整，有没有多余的空格？
            2.  修正输入后，再次点击【登 录】按钮尝试。
            3.  如果多次尝试均失败，您可能需要使用 **"4.4 密码找回"** 功能来重置您的密码。
        **[截图 4.3.3-2: 登录页面，在登录按钮上方显示了一条红色的"用户名或密码错误"的提示信息。]**

### 4.4 密码找回
**操作场景**：用户因长时间未使用或其他原因，忘记了自己的账户登录密码，导致无法登录系统。

**操作全流程详解**:
1.  **定位入口**: 在系统登录页面的密码输入框附近，或登录按钮下方，找到并用鼠标左键点击内容为 **【忘记密码?】** 的文本链接。
2.  **进入重置流程**: 系统将跳转至一个标题为 **"密码找回"** 或 **"重置密码"** 的新页面。
3.  **输入验证邮箱**: 该页面会有一个输入框，要求您输入注册时使用的电子邮箱地址。请在此输入框中准确输入您的邮箱。
4.  **请求验证邮件**: 点击页面上的 **【发送验证邮件】** 或 **【获取验证码】** 按钮。
5.  **系统响应**: 系统会提示"验证邮件已发送，请注意查收"。同时，后台服务会向您填写的邮箱地址发送一封包含有时效性的密码重置链接或数字验证码的邮件。
6.  **查收邮件**:
    -   登录您自己的个人电子邮箱（例如QQ邮箱、163邮箱等）。
    -   在收件箱中找到一封来自本系统（发件人名称通常为"血管瘤人工智能辅助治疗系统"）的标题为"密码重置"的邮件。
    -   > **提示**：如果收件箱中没有找到，请务必检查该邮件是否被误判进入了 **"垃圾邮件"**、**"广告邮件"** 或 **"订阅邮件"** 文件夹。
7.  **执行重置**:
    -   **情况A：邮件中是重置链接**：直接用鼠标左键点击邮件正文中的密码重置链接。
    -   **情况B：邮件中是验证码**：复制该数字验证码，返回到系统的密码找回页面，在指定的验证码输入框中粘贴或输入该验证码，然后点击【下一步】。
8.  **设置新密码**: 浏览器会自动打开一个新的密码设置页面。该页面通常包含两个输入框："新密码"和"确认新密码"。请在这两个框中输入您想要设置的全新密码，然后点击 **【确认修改】** 或 **【重置密码】** 按钮。
9.  **完成与重新登录**: 页面会提示"密码重置成功"，并自动跳转回系统的登录页面。此时，您必须使用您的邮箱和刚刚设置的 **新密码** 进行登录。旧密码已永久失效。

### 4.5 安全退出
**操作场景**：在您完成当天的工作，或需要中途离开您的工作电脑时，或在公共计算机上完成操作后，都必须执行安全退出。这是一个至关重要的安全操作，能有效防止他人未经授权访问您的账户和其中敏感的医疗数据。

**[截图 4.5-1: 系统任何功能页面的右上角，清晰地显示着当前登录用户的用户名（例如"张三"），旁边可能还有一个小的下拉箭头图标。]**

**操作全流程详解**:
1.  **定位用户菜单触发器**: 在您登录系统后的任何功能页面，将您的目光移至界面的右上角。您会看到一个显示您用户名的区域。
2.  **触发下拉菜单**: 使用鼠标，将光标移动到该用户名区域上，并单击鼠标左键。
3.  **系统响应 - 菜单出现**: 单击后，一个包含若干选项的下拉式菜单会从该区域正下方优雅地展开。
    **[截图 4.5-2: 用户名下方的下拉菜单已完全展开，可以清晰地看到【个人中心】、【修改密码】和【退出登录】等选项。]**
4.  **定位并选择退出选项**: 在这个刚刚出现的下拉菜单中，找到内容为 **【退出登录】** 的菜单项，并用鼠标左键单击它。
5.  **执行退出与系统最终反馈**:
    -   **系统行为**: 在您点击的瞬间，前端浏览器会立刻向服务器发送一个退出登录的请求。服务器收到请求后，会立即销毁您本次登录的会话（Session）状态和身份令牌（Token）。
    -   **界面变化**: 浏览器页面会被强制刷新，并**立即跳转**回本系统的"用户登录"页面。
    -   **状态确认**: 回到登录页面，即表示您已成功、彻底地从系统中退出了。此时，如果您尝试通过浏览器的"后退"按钮返回之前的工作页面，系统将因检测不到有效的登录状态而拒绝访问，并很可能再次将您强制重定向回登录页。
- **最佳实践**: 养成每次使用完系统后都执行【退出登录】的习惯，是保障信息安全的强制性、必须遵守的最佳实践。

---

## 5. 核心功能模块详解

### 5.1 病例管理与标注模块 (医生角色)
这是"医生"角色用户日常工作中使用频率最高、也是本系统核心价值所在的功能模块。它覆盖了从创建新病例、上传影像、进行专业标注、填写结构化信息到最终提交审核的完整工作流程。

#### 5.1.1 "我的病例标注"工作台
用户以"医生"或更高权限角色成功登录后，系统通常会默认直接进入此页面。如果登录后进入了其他页面，用户也可以通过点击界面左侧主导航栏中的 **【我的病例标注】** 菜单项来手动进入。

**[截图 5.1.1-1: 系统主界面，左侧的垂直导航菜单栏中，【我的病例标注】这一项被高亮或有明显的选中状态。]**

##### 5.1.1.1 工作台界面元素详解
"我的病例标注"工作台是所有病例管理工作的起点和信息枢纽。整个界面在逻辑上可以划分为几个关键区域。

**[截图 5.1.1-2: "我的病例标注"工作台的完整界面截图。截图应包含A、B、C三个区域的全部内容。]**

-   **A区 - 操作入口区**:
    -   **【新建标注】按钮**:
        -   **位置**: 通常位于页面的右上角，是一个颜色突出（例如蓝色）、带有"+"号图标或明确"新建"字样的醒目按钮。
        -   **功能**: 这是所有新病例创建工作的**唯一入口**。无论何时您需要为一个新患者创建病例，都应从点击此按钮开始。
        -   **交互**: 鼠标左键单击后，系统将导航至全新的"图像标注与表单填写"界面。

-   **B区 - 状态筛选与快速过滤区**:
    -   **位置**: 位于页面主列表区域的上方，通常表现为一组可以点击的标签页（Tabs）。
    -   **功能**: 用于根据病例当前所处的流程阶段，对下方列表中的大量病例进行快速过滤显示。这能极大地帮助您集中处理同类任务，例如，您可以只查看所有被驳回需要修改的病例。
    -   **各状态标签详解与操作**:
        -   **【全部】**:
            -   **描述**: 这是工作台的默认视图，显示所有与您的账户相关的病例，不过滤任何状态。
            -   **操作**: 点击【全部】标签，下方列表将刷新，展示您所有创建或被分配的病例。
        -   **【未标注 (草稿)】**:
            -   **描述**: 显示您已经创建，但工作尚未全部完成，还未提交给上级审核的病例。这些是您的"正在进行中"的项目。
            -   **操作**: 点击【未标注】标签，可以快速找到所有未完成的工作，以便继续编辑。
        -   **【待审核】**:
            -   **描述**: 显示您已经确认完成并已提交，正在等待审核员（Reviewer）或管理员（Admin）进行批阅的病例。
            -   **操作**: 点击【待审核】标签，可以追踪您已提交工作的状态。此状态下的病例通常是只读的，您无法再对其进行编辑。
        -   **【已驳回】**:
            -   **描述**: 显示审核员审核后，认为其中存在某些问题（如标注不准、信息缺失等）并退回给您进行修改的病例。
            -   **操作**: 点击【已驳回】标签，列表中的病例是您需要**优先处理**的工作。您需要仔细查看审核员的驳回意见，点击【编辑】进入修改，完成后再次提交。
        -   **【已通过】**:
            -   **描述**: 显示已经由审核员审核通过，整个诊断流程已圆满结束的病例。这些病例可以视为已成功归档的成果。
            -   **操作**: 点击【已通过】标签，可以回顾您已成功完成的工作。此状态下的病例仅供查看，无法进行任何修改。

-   **C区 - 病例核心列表区**:
    -   **位置**: 占据了页面的主体区域，是工作台最核心的内容展示区。
    -   **功能**: 以结构化的表格（Table）形式，清晰、有序地展示了符合当前筛选条件的所有病例信息。
    -   **列表核心列项详解**:
        -   **病例编号**: 系统为每一个成功创建的病例自动生成的全局唯一标识符（ID），是精确追踪和定位病例的内部索引。
        -   **患者信息**: 通常显示患者的脱敏姓名或ID，是识别不同病例最直接的业务依据。
        -   **部位/类型**: 提取自病例详细信息表单中的核心诊断信息（例如病变部位、诊断标签等）的摘要，方便医生在不进入详情页的情况下快速浏览和识别。
        -   **当前状态**: 以带有不同颜色和文字的徽章（Badge）形式，直观地显示该病例目前正处于工作流的哪个阶段（如"待审核"、"已驳回"等），与B区的状态筛选器功能完全对应。
        -   **创建时间**: 精确到秒地记录了您创建该病例的日期和时间，便于工作的追溯和排序。
        -   **操作栏**: 这是表格每行的最右侧，也是与单个病例进行交互的核心区域。系统会根据该行病例的"当前状态"，动态地、智能化地提供当前允许执行的操作按钮。
            -   **当状态为"未标注"或"已驳回"时**:
                -   **【编辑】**: 点击后进入"图像标注与表单填写"界面，对该病例的所有信息进行修改和完善。
                -   **【删除】**: 点击后会弹出二次确认对话框，确认后将**永久删除**此病例及其所有相关数据。此操作不可逆，请务必谨慎。
            -   **当状态为"待审核"或"已通过"时**:
                -   **【查看】**: 点击后进入该病例的详情页面，但所有内容均为只读模式，您只能查看而不能进行任何修改。

#### 5.1.2 "图像标注与表单填写"界面
此界面是整个系统的核心工作区，所有关键的数据生产——包括AI模型的训练数据来源（图像标注）和结构化的临床数据（表单信息）——都在此完成。用户在"我的病例标注"工作台点击【新建标注】按钮，或点击一个"未标注"/"已驳回"状态病例的【编辑】按钮后，系统便会导航至此界面。

##### 5.1.2.1 界面整体布局详解
该界面经过精心设计，采用上下分区的布局，以优化医生的工作流，减少不必要的视线移动和滚动操作。

**[截图 5.1.2-1: "图像标注与表单填写"界面的完整布局截图。上方是左右分栏的图像标注区，下方是内容详尽的病例信息表单区。]**

-   **上半部分 - 图像标注区**:
    -   这是一个水平分栏的区域，专门用于所有与图像相关的操作。
    -   **左侧为"工具与标注列表区"**: 包含了进行标注所需的全部工具（如标签选择、绘图工具）以及当前图像上所有已添加标注的实时列表。
    -   **右侧为"图像显示与操作区"**: 一个宽敞的画布，用于显示待标注的医学影像，并在此区域内完成所有交互式的绘制、移动和缩放操作。

-   **下半部分 - 病例信息表单区**:
    -   这是一个内容详尽的垂直长表单，包含了需要医生填写的、关于此病例的全部结构化信息。表单内容非常丰富，涵盖了患者基本信息、病史、临床检查、诊断意见等多个维度。我们将在 **5.1.4** 节详细介绍其每一个字段。

##### 5.1.2.2 左侧工具栏区域详解

**[截图 5.1.2-2: 左侧工具栏区域的高亮特写。]**

-   **A - 【标签分类】下拉菜单**:
    -   **功能**: 这是进行标注前**必须**操作的关键控件。它用于为您的每一次框选操作预设一个医学上准确的分类。绘制时，系统会自动将当前选中的标签赋给新画的框。
    -   **操作**:
        1.  鼠标左键单击该下拉菜单。
        2.  一个包含所有预设诊断标签（如`IH-婴幼儿血管瘤`, `VM-静脉畸形`等）的列表会向下展开。
        3.  从列表中选择一个最符合当前病灶特征的诊断标签并单击。
        4.  您选择的标签会显示在下拉菜单框中，表示当前标签已激活。

-   **B - 【标注工具】区域**:
    -   **功能**: 提供绘制标注所需的工具。
    -   **【矩形框】工具**: 目前系统主要提供此工具。它被设计为默认选中状态（通常背景色会与其他工具不同），用于框选出矩形的病灶区域。

-   **C - "已添加标注"列表**:
    -   **功能**: 这是一个实时更新的列表，同步显示您在右侧图像上绘制的每一个标注框的详细信息。
    -   **列表内容**: 每一行代表一个标注框，通常包含该框的**标签名**、**坐标**等摘要信息。
    -   **交互**:
        -   **高亮联动**: 当您的鼠标悬停在列表中的某一行上时，右侧图像上对应的那个标注框会高亮显示，反之亦然。这有助于您快速定位和识别。
        -   **【删除】按钮**: 每一行记录的最右侧都有一个【删除】按钮（通常是垃圾桶图标）。点击它，可以精确地删除指定的某一个标注框，而无需影响其他标注。

##### 5.1.2.3 右侧图像显示与操作区详解

**[截图 5.1.2-3: 右侧图像显示与操作区的特写，一张医学影像已加载在画布中央。]**

-   **功能**: 这是所有交互式标注操作发生的主舞台。
-   **画布 (Canvas)**: 用于显示您上传的高清医学影像。
-   **交互模式**:
    -   **绘图模式**: 当您选中了【矩形框】工具后，鼠标光标在此区域内会变为十字形（`crosshair`），表示已进入绘图模式，可以开始绘制。
    -   **编辑模式**: 当您将鼠标移动到已绘制的标注框上时，光标会根据位置变为移动（四向箭头）或缩放（双向箭头）的图标，表示可以对现有标注进行编辑。

---
#### 5.1.3 核心工作流：从零到完成一个完整病例的标注与信息填写

##### 5.1.3.1 步骤一：上传待标注的医学影像
1.  **定位上传区域**:
    -   **场景**: 如果您是新建一个病例，右侧的图像显示区会处于空白状态，中央通常会有一个带有"+"号图标或文字提示（如【点击上传】或【拖拽文件到此处】）的明显区域。
    -   **[截图 5.1.3-1: 空白的图像操作区，中央有醒目的上传提示。]**

2.  **用户操作**:
    -   **方式一（点击上传）**: 用鼠标左键单击此上传提示区域。
    -   **方式二（拖拽上传）**: 从您计算机的文件管理器中，用鼠标左键按住想要上传的图像文件不放，将其拖拽到浏览器的这个上传区域内，然后松开鼠标。

3.  **系统响应（针对点击上传）**:
    -   浏览器会立即弹出一个标准的操作系统**文件选择对话框**。
    -   **[截图 5.1.3-2: 弹出的文件选择对话框，显示用户本地文件夹内容。]**

4.  **用户操作（在文件选择对话框中）**:
    1.  在弹出的对话框中，浏览您本地计算机的文件夹。
    2.  找到并用鼠标左键单击选中您需要标注的医学影像文件（系统支持 `.jpg`, `.jpeg`, `.png` 等常见图像格式）。
    3.  点击对话框右下角的 **【打开】** 按钮。

5.  **系统反馈（上传与加载）**:
    -   **成功**:
        1.  文件选择对话框关闭。
        2.  图像开始上传，您可能会看到一个加载进度条或旋转的菊花图标。
        3.  上传成功后，图像会自动在右侧的图像显示区加载并完整地显示出来。
        4.  至此，图像已准备就绪，可以开始标注。
        **[截图 5.1.3-3: 图像已成功上传并清晰地显示在右侧画布中。]**
    -   **失败**:
        -   **文件格式不支持**: 如果您选择了系统不支持的文件格式（如 `.txt`, `.docx`），系统会弹出一个错误提示："上传文件格式不支持"。
        -   **文件过大**: 如果文件大小超过了系统设置的上限，会提示："上传文件过大"。
        -   **网络错误**: 如果上传过程中网络中断，可能会提示上传失败。

##### 5.1.3.2 步骤二：选择诊断标签
> **重要说明**: 此步骤虽然简单，但至关重要。**请务必在绘制每一个标注框之前，先在左侧工具栏选择好正确的标签**。因为系统在您完成绘制的瞬间，就会将当前选中的标签与新生成的框进行绑定。

1.  **操作**: 在界面左上角的 **【标签分类】** 下拉菜单处用鼠标左键单击。
2.  **系统反馈**: 菜单会向下展开，显示所有预设的、可用的诊断标签列表。
    **[截图 5.1.3-4: "标签分类"下拉菜单被完全展开，列表中清晰地列出了所有可选的血管瘤分类名称。]**
3.  **操作**: 从列表中用鼠标左键单击选择一个最符合您将要标注的那个病灶特征的诊断标签。例如，我们选择 `IH-婴幼儿血管瘤`。
4.  **系统反馈**:
    -   下拉菜单收起。
    -   您选择的标签名称（`IH-婴幼儿血管瘤`）会显示在下拉菜单框中。
    -   这表示当前标签已激活，后续所有绘制的标注框都将被自动标记为此类型。

##### 5.1.3.3 步骤三：使用矩形框工具进行标注
1.  **确认工具**: 确保左侧工具栏中的 **【矩形框】** 工具已被选中（它通常是默认选中的，背景色会与其他工具不同）。
2.  **定位病灶起点**: 将鼠标光标移动到右侧图像上，找到您想要标注的病灶区域的任意一个角点（例如左上角）作为起点。此时，鼠标光标会变为一个精确的十字形（`+`），表示已进入绘图模式。
3.  **开始绘制**: 在您选定的起点位置，**按住鼠标左键不放**。
4.  **拖动绘制**:
    -   保持按住左键的状态，向病灶的对角方向（例如右下角）拖动鼠标。
    -   随着您的拖动，一个半透明的、带有虚线边框的矩形会跟随着您的光标实时显示其大小和位置。
    -   请仔细调整拖动的终点，确保这个半透明的矩形框能够恰好、完整地覆盖整个病灶区域，不多也不少。
5.  **完成绘制**: 当您确认矩形框的位置和大小都准确无误后，**松开鼠标左键**。
6.  **系统反馈（这是关键）**:
    -   在您松开鼠标的瞬间，一个带有颜色的、清晰的实线矩形框便固定在图像上，取代了之前的半透明虚线框。
    -   矩形框的左上角会自动显示一个小小的、带有背景色的标签牌，上面的文字正是您在第二步中选择的标签名称（例如 `IH-婴幼儿血管瘤`）。
    -   **同时**，在界面左侧的"已添加标注"列表中，会**立刻新增一条记录**，代表这个刚刚被创建的标注。这条记录包含了标签名、坐标等信息。
    **[截图 5.1.3-5: 图像上已经绘制好一个带标签的矩形框，并且左侧的"已添加标注"列表中也同步新增了一条标注信息，两者有高亮联动效果。]**
7.  **重复操作**: 如果同一张图像上存在多个独立的病灶需要标注，您可以重复 **第二步**（如果需要，先为下一个病灶选择一个新的标签）和 **第三步**（绘制一个新的框），为图像上的每一个病灶都进行准确的标注。

##### 5.1.3.4 步骤四（可选）：编辑已绘制的标注框
在完成绘制后，您随时可以对已有的标注框进行微调。

-   **移动标注框**:
    1.  将鼠标光标移动到已绘制的标注框**内部**（非边缘或角落）。
    2.  光标会从十字形变为可拖动的四向箭头或手形。
    3.  此时按住鼠标左键不放，并拖动鼠标，整个标注框就会跟着您的鼠标一起移动。
    4.  移动到正确位置后，松开鼠标左键即可。

-   **调整标注框大小**:
    1.  将鼠标光标移动到已绘制的标注框的任意一条**边**或一个**角**上。
    2.  光标会变为相应的双向箭头（水平、垂直或对角线）。
    3.  此时按住鼠标左键不放，并向内或向外拖动，即可调整框体的大小。
    4.  调整到满意的大小后，松开鼠标左键。

---
#### 5.1.4 步骤五：填写详细的病例信息表单
在完成所有图像的标注工作后，您需要将页面向下滚动，细致地填写位于图像区下方的病例信息表单。

**[截图 5.1.4-1: 屏幕下方详细的病例信息表单区域的特写，其中部分字段已被填写作为示例。]**

1.  **定位表单**: 将页面向下滚动，找到标题为"病例信息表单"的区域。
2.  **逐项填写**: 根据您对患者的实际诊断情况，以及病历资料，依次、完整地填写表单中的每一个项目。

##### 5.1.4.1 表单核心字段示例与填写要求
-   **患者年龄**:
    -   **要求**: 必须输入数字。
    -   **示例**: *`3`* (表示3岁)
-   **性别**:
    -   **要求**: 通常是一个【男】/【女】的单选框或下拉选择框。
    -   **示例**: 选择"女"。
-   **病变部位**:
    -   **要求**: 文本输入框，用于描述病灶所在的具体解剖位置。
    -   **示例**: *`左侧面颊部`*
-   **诊断摘要**:
    -   **要求**: 一个多行文本输入区域，用于您对患者的病情进行总结性的描述。
    -   **最佳实践**: 请使用专业、简洁、规范的医学术语进行描述，这将作为重要的归档信息。
-   **治疗建议**:
    -   **要求**: 一个多行文本输入区域，用于您给出专业的治疗方案或建议。
> **重要提示**: 表单中所有带有红色星号（*）标记的字段均为必填项。在您提交审核之前，系统会检查并确保所有这些字段都已被填写。

---
#### 5.1.5 步骤六：保存草稿或提交审核
在完成所有图像标注和信息填写工作后，您可以在整个页面的最底部找到两个功能完全不同的关键操作按钮。

**[截图 5.1.5-1: 页面最底部的操作栏，左侧是灰色的【保存草稿】按钮，右侧是蓝色的【提交审核】按钮，两者并排显示。]**

##### 5.1.5.1 功能：【保存草稿】
-   **适用场景**:
    -   工作量较大，尚未全部完成，需要中途暂停。
    -   需要临时离开电脑，希望保存当前进度，稍后再回来继续。
    -   对某些信息尚不确定，想先保存下来，待核实后再提交。
-   **操作**: 用鼠标左键单击灰色的 **【保存草稿】** 按钮。
-   **系统反馈**:
    -   系统会**静默地**将在当前页面上的所有工作成果（包括已上传的图像、所有绘制的标注框、所有已填写的表单内容）完整地保存到数据库。
    -   此病例的状态将被记为"未标注"（即草稿状态）。
    -   页面顶部可能会短暂地弹出一个绿色的提示条，显示"保存成功"。
    -   您可以安全地关闭页面或离开。之后，您可以在"我的病例标注"工作台的"未标注"列表中找到它，点击【编辑】即可从上次离开的地方无缝继续工作。

##### 5.1.5.2 功能：【提交审核】
-   **适用场景**: 您已反复确认，当前病例的所有图像标注都已精确无误，所有表单信息都已准确完整地填写，确信这份病例已经达到可以送交上级医生进行审核的标准。
-   **操作**: 用鼠标左键单击蓝色的 **【提交审核】** 按钮。
-   **系统反馈与确认流程**:
    1.  **数据完整性校验**: 系统会首先进行一次最终的数据完整性校验。如果发现有必填项（带*号的）未填写，会**阻止提交**，并弹出一个错误提示，同时自动将页面滚动到第一个未填写的字段处，用红色边框高亮该输入框，提醒您补充。
    2.  **二次确认对话框**: 如果所有数据校验通过，系统会弹出一个模态确认对话框，标题通常为"确认提交"，内容为："您确定要提交审核吗？提交后病例将进入锁定状态，您将无法再进行任何修改。"
    **[截图 5.1.5-2: 屏幕中央弹出的二次确认对话框。]**
    3.  **用户确认操作**: 在此对话框中，点击 **【确定】** 按钮。如果您想取消提交，可以点击【取消】。
    4.  **最终系统反馈**:
        -   点击【确定】后，系统会将此病例的状态从"未标注"更新为"待审核"，并将其正式推送至审核人员的工作列表中。
        -   页面通常会自动**跳转**回"我的病例标注"工作台页面。
        -   在工作台列表中，您会看到刚刚操作的那个病例，其"当前状态"徽章已变为"待审核"，并且其"操作栏"下的【编辑】和【删除】按钮已消失，仅剩下一个只读的【查看】按钮。
        -   至此，一个病例的创建和提交流程圆满完成。

### 5.2 病例审核模块 (审核员/管理员角色)
此模块是本系统为确保医疗诊断质量和数据准确性而设立的关键环节。它主要供拥有"审核员 (REVIEWER)"或"管理员 (ADMIN)"权限的用户使用，用于对医生提交的病例进行专业的质量控制和最终确认。

**操作场景**: 审核员或管理员需要对医生提交的"待审核"病例进行审查和决策。

#### 5.2.1 访问"标注审核"工作台
1.  **前置条件**: 您已使用拥有"审核员"或"管理员"权限的账号成功登录本系统。
2.  **界面定位**: 将您的目光移至系统界面的左侧主导航栏。
3.  **用户操作**: 在导航栏中，找到并用鼠标左键单击内容为 **【标注审核】** 的菜单项。该菜单项通常会有一个相关的图标。
4.  **系统响应**: 页面内容会立即切换到 **"标注审核"工作台**。此工作台会以列表形式，集中展示所有当前状态为"待审核"的病例，等待您的处理。

**[截图 5.2.1-1: "标注审核"工作台页面完整视图。列表中清晰地显示了多条状态为"待审核"的病例，以及顶部可能的筛选/搜索区域。]**

##### 5.2.1.1 "标注审核"工作台界面元素详解
"标注审核"工作台的界面布局与"我的病例标注"工作台（详见 **5.1.1.1** 节）类似，但其核心功能和交互逻辑是围绕"审核"这一行为展开的。

-   **A区 - 审核病例列表**:
    -   **功能**: 这是工作台的核心区域，以表格形式展示所有需要您进行审核的病例。列表通常会显示病例的摘要信息，如病例编号、患者信息、提交医生、提交时间、当前状态等。
    -   **列表列项**: 通常包括但不限于：
        -   **病例编号**: 唯一标识符。
        -   **患者信息**: 患者姓名或ID。
        -   **提交医生**: 提交此病例的医生姓名。
        -   **提交时间**: 病例提交审核的日期和时间。
        -   **当前状态**: 统一显示为"待审核"。
        -   **操作栏**: 每一行病例的最右侧。对于"待审核"状态的病例，这里通常会显示一个唯一的按钮：**【批阅】**。

-   **B区 - 顶部筛选/搜索栏 (可选)**:
    -   **功能**: 如果病例数量较多，可能会提供搜索框或筛选器，帮助审核员快速定位特定病例，例如按医生姓名、病例编号等进行搜索。

#### 5.2.2 步骤二：批阅病例
1.  **前置条件**: 您已成功访问"标注审核"工作台，且列表中有待批阅的病例。
2.  **定位目标病例**: 在"标注审核"工作台的病例列表中，仔细浏览并找到您想要开始审查和批阅的特定病例。
3.  **用户操作**: 将鼠标光标移动到目标病例所在行的最右侧，找到并用鼠标左键单击 **【批阅】** 按钮。
4.  **系统响应**: 系统会加载并跳转至该病例的**详情审核页面**。此页面布局与医生进行标注时的页面（详见 **5.1.2** 节）非常相似，但有一个显著的区别：所有图像标注和表单信息都将处于**只读状态（通常显示为灰色或不可编辑）**，无法进行任何修改。在页面的最下方，会增加一个专门用于审核操作的区域。

**[截图 5.2.2-1: 病例审核详情页的完整视图。上方是提交的图像标注区和病例信息表单区，所有内容均以灰色或不可编辑状态显示。页面最下方有一个独立的"审核操作"区域。]**

#### 5.2.3 步骤三：执行审核操作并做出决策
在病例详情审核页面，您的核心任务是全面审查医生提交的各项内容，并最终决定"通过"或"驳回"。

**审核流程与要点**:
1.  **全面审查内容**:
    -   **图像标注审查**: 仔细检查图像上的每一个标注框。
        -   **准确性**: 标注的矩形框是否精确地覆盖了病灶区域，有无偏差或遗漏？
        -   **标签恰当性**: 标注的标签（如`IH-婴幼儿血管瘤`）是否与实际病灶的医学特征完全吻合？
        -   **数量**: 是否所有需要标注的病灶都已进行了标注？
    -   **表单信息审查**: 逐项核对病例信息表单中的所有内容。
        -   **完整性**: 所有必填项是否都已填写？
        -   **准确性与合理性**: 患者年龄、性别、病变部位、诊断摘要、治疗建议等信息是否准确、逻辑合理，有无明显的医学错误或矛盾？
        -   **规范性**: 文本描述是否使用了专业、规范的医学术语？

2.  **参考AI诊断结果 (可选)**:
    -   如果界面上提供了AI模型的初步诊断结果（如AI检测的类型、置信度等），您可以将其作为辅助参考。
    -   > **重要提示**: AI结果仅供参考，最终的诊断和审核判断必须基于审核员的专业医学知识和经验。

3.  **定位审核操作区**: 审查完成后，将页面滚动到最下方，找到专门的"审核操作"区域。

**[截图 5.2.3-1: 页面底部的"审核操作"区域特写。区域内包含一个多行文本框用于填写"审核意见"，以及并排的【通过】和【驳回】两个按钮。]**

##### 5.2.3.1 功能：审核意见
-   **功能描述**: 这是一个多行文本输入框，供审核员输入专业的评语、具体的修改建议，或者当病例通过时提供简要的确认信息。
-   **用户操作**: 单击输入框，通过键盘输入您的意见。
-   **最佳实践**: 无论最终决策是"通过"还是"驳回"，都强烈建议填写清晰、具体、有建设性的审核意见。这对于知识传递、医生水平提升和流程追溯都非常有帮助。对于驳回的病例，意见应明确指出需要修改的具体内容和原因。

##### 5.2.3.2 功能：【通过】
-   **适用场景**: 您对该病例的图像标注和所有表单信息进行了全面审查，认为其内容均准确无误，完全符合诊断标准和系统规范，可以正式归档。
-   **操作**: 确保已填写必要的"审核意见"后，用鼠标左键单击蓝色的 **【通过】** 按钮。
-   **系统反馈**:
    -   **成功提示**: 系统可能会弹出一个短暂的提示："病例已通过审核"。
    -   **状态更新**: 该病例的状态将永久性地从"待审核"变为"已通过"。
    -   **列表移除**: 该病例将自动从您"标注审核"工作台的"待审核"列表中移除，因为其流程已结束。
    -   **后续影响**: 原提交医生在"我的病例标注"工作台中，会看到该病例的状态已更新为"已通过"，且只能进行【查看】操作。

##### 5.2.3.3 功能：【驳回】
-   **适用场景**: 您在审查病例时，发现其中存在需要修改的错误或不足之处，例如标注不够精确、表单信息缺失、诊断意见存在疑问、或不符合既定的操作规范等。需要退回给原提交医生进行修改。
-   **操作**: 确保已在"审核意见"框中填写了**详细且明确的驳回原因和修改建议**后，用鼠标左键单击红色的 **【驳回】** 按钮。
-   **系统反馈**:
    -   **成功提示**: 系统可能会弹出一个短暂的提示："病例已驳回"。
    -   **状态更新**: 该病例的状态将从"待审核"变为"已驳回"。
    -   **列表变化**: 该病例将从"标注审核"工作台的"待审核"列表中移除。
    -   **后续影响**: 该病例将自动退回到原提交医生的"我的病例标注"列表中，其状态更新为"已驳回"。原提交医生会在病例详情页中看到您的驳回意见，并在修改后可以再次提交审核。

---

### 5.3 后台管理模块 (管理员角色)
后台管理模块是系统的最高权限区域，仅供拥有"管理员 (ADMIN)"角色的用户访问和操作。它提供了对系统用户、团队以及所有核心数据的全局管理能力，确保系统的稳定运行和数据安全。

**操作场景**: 系统管理员进行日常的用户维护、团队管理或数据核查。

#### 5.3.1 访问后台管理控制台
1.  **前置条件**: 您已使用拥有"管理员"权限的账号成功登录本系统。
2.  **界面定位**: 将您的目光移至系统界面的左侧主导航栏。
3.  **用户操作**: 在导航栏中，找到并用鼠标左键单击内容为 **【管理员控制台】** 的菜单项（或类似的命名，如【系统管理】）。
4.  **系统响应**: 页面内容会立即切换到 **"管理员控制台"** 页面。此页面通常包含多个标签页（Tabs），分别对应不同的管理子模块，例如"用户管理"、"团队管理"、"图像管理"等。

**[截图 5.3.1-1: "管理员控制台"页面完整视图。顶部有多个标签页（如"用户管理"、"团队管理"），当前显示的是用户管理列表。]**

#### 5.3.2 用户管理
此子模块是管理员维护系统用户账号的核心区域，能够对系统中所有用户的基本信息、角色和状态进行全面管理。

1.  **访问**: 在"管理员控制台"页面，点击 **【用户管理】** 标签页。
2.  **界面元素与功能**:
    -   **用户列表**: 以表格形式展示系统中所有注册用户的详细信息，包括用户ID、姓名、邮箱、当前角色、注册时间、状态（启用/禁用）等。
    -   **搜索框**: 通常位于用户列表上方，允许管理员通过用户的姓名、邮箱或ID进行快速搜索，以定位特定用户。
    -   **操作栏**: 用户列表的每一行末尾会提供针对该用户的管理操作按钮，例如：
        -   **【编辑】**: 点击后弹出用户编辑对话框，可修改用户的姓名、医院、科室等基本信息。
        -   **【修改角色】**: 这是一个关键功能。管理员可直接在此更改用户的角色，例如将某位普通"医生"提升为"审核员"，或将"审核员"降级为"医生"。通常会通过一个下拉菜单或单选框选择新角色。
        -   **【禁用/激活】**: 管理员可以临时冻结或解冻某个用户的登录权限。被禁用的用户将无法登录系统。这在处理异常账户时非常有用。
        -   **【删除】**: 极端操作，点击后会弹出二次确认对话框，确认后将**永久删除**用户账户及其所有相关数据。操作不可逆，需极其谨慎。
    -   **权限审批入口**: 在某些设计中，可能会有一个独立的区域或按钮，用于处理医生角色提交的"申请成为审核员"的申请。管理员可在此查看申请列表，并选择【批准】或【拒绝】这些申请。

**[截图 5.3.2-1: 用户管理界面的详细视图，展示了用户列表、搜索框、以及用户行末尾的编辑、修改角色、禁用/激活等按钮。]**

#### 5.3.3 团队管理
此子模块允许管理员创建、修改、解散医疗团队，并管理团队内部的成员。

1.  **访问**: 在"管理员控制台"页面，点击 **【团队管理】** 标签页。
2.  **界面元素与功能**:
    -   **团队列表**: 以表格形式展示系统中所有已创建的医疗团队信息，包括团队ID、团队名称、描述、创建者、成员数量等。
    -   **【创建团队】按钮**: 通常位于团队列表上方，点击后弹出"创建新团队"对话框，管理员可在此输入团队名称和描述来建立一个新团队。
    -   **搜索框**: 用于按团队名称或描述进行快速搜索。
    -   **操作栏**: 团队列表的每一行末尾会提供针对该团队的管理操作按钮，例如：
        -   **【编辑】**: 点击后弹出"编辑团队信息"对话框，可修改团队的名称、描述。
        -   **【管理成员】**: 这是一个重要的入口。点击后会进入该团队的成员管理页面，管理员可在此将系统中的现有用户添加到团队，或将团队中的成员移除。通常以列表形式显示成员，并提供添加/移除按钮。
        -   **【解散团队】**: 极端操作，点击后会弹出二次确认对话框，确认后将**永久删除**该团队及其所有关联关系。操作不可逆，请务必谨慎。

**[截图 5.3.3-1: 团队管理界面的详细视图，展示了团队列表、【创建团队】按钮、以及团队行末尾的编辑、管理成员、解散团队等按钮。]**

#### 5.3.4 图像管理
此子模块允许管理员查看并管理系统中所有用户上传的原始医学图像、处理后的图像以及标注后的图像文件。这对于系统维护和存储空间管理非常重要。

1.  **访问**: 在"管理员控制台"页面，点击 **【图像管理】** 标签页。
2.  **界面元素与功能**:
    -   **图像列表**: 以缩略图或列表形式展示系统中所有已上传的图像文件，并显示其关键元数据，如文件名、上传者、上传时间、文件大小、图像尺寸等。
    -   **筛选/搜索**: 提供多种筛选条件（如按上传者、图像类型、时间范围）和搜索功能（按文件名），方便管理员快速定位特定图像。
    -   **【查看详情】**: 点击图像缩略图或详情按钮，可查看图像的原始大小、元数据，甚至可能关联到该图像的所有标注信息。
    -   **【删除】**: 极端操作，点击后会弹出二次确认对话框，确认后将**永久删除**该图像文件及其在系统中的所有相关记录。此操作不可逆，请务必谨慎。

**[截图 5.3.4-1: 图像管理界面的详细视图，展示了图像缩略图列表、筛选/搜索功能、以及每张图像对应的管理操作。]**

---

## 6. 故障与排除 (FAQ)
本章节提供了用户在使用"血管瘤人工智能辅助治疗系统"过程中可能会遇到的一些常见问题、它们可能的原因以及详细的解决方案。当您遇到操作上的困惑或系统提示错误信息时，建议首先查阅本章节。

-   **Q1: 无法登录系统，提示"用户名或密码错误"，该怎么办？**
    -   **可能的原因**:
        1.  您输入的邮箱地址或密码有误。这可能是最常见的原因，通常是由于打字失误、忘记密码或密码被更改。
        2.  密码输入时键盘大写锁定（Caps Lock）键意外开启，导致大小写不匹配。
        3.  您使用的输入法是中文全角状态，导致输入了非英文字符或全角标点。
        4.  邮箱地址前后有多余的不可见空格。
        5.  您的账户可能因多次错误尝试被暂时锁定，或被系统管理员禁用。
    -   **建议解决方案**:
        1.  **仔细检查输入内容**: 在重新尝试登录前，请务必仔细核对您输入的邮箱地址和密码。建议先在一个文本编辑器（如记事本）中输入密码，确认无误后再复制粘贴到密码输入框中。
        2.  **检查键盘状态**: 确保键盘上的Caps Lock键处于关闭状态。同时，确认输入法为英文半角模式。
        3.  **尝试找回密码**: 如果您确认输入无误但仍无法登录，很可能是您记错了密码。请立即使用登录页面上的 **【忘记密码?】** 功能进行密码重置（详见 **4.4 密码找回**）。
        4.  **联系系统管理员**: 如果以上所有方法都无法解决问题，请及时联系您的系统管理员。他们可以帮助您检查账户状态、重置密码，或提供进一步的技术支持。

-   **Q2: 注册时提示"该邮箱已被注册"怎么办？**
    -   **可能的原因**:
        1.  您之前可能已经使用此邮箱地址在本系统中注册过账号，但您忘记了。
        2.  其他人（例如您的同事或家人）可能使用您的邮箱地址或类似的地址进行了注册。
    -   **建议解决方案**:
        1.  **尝试直接登录**: 请先回忆一下您是否曾注册过，并直接前往登录页面，使用该邮箱地址尝试登录。
        2.  **使用新邮箱注册**: 如果您确定不是自己注册的，或者您需要使用一个全新的身份，请更换一个您本人拥有且尚未在本系统注册过的电子邮箱地址来完成新用户的注册流程。

-   **Q3: 标注时画错了框，或者框选区域不准确，如何删除或修改？**
    -   **可能的原因**: 在图像标注过程中，由于鼠标操作失误或对病灶范围判断不准，导致绘制的标注框有误。
    -   **建议解决方案**:
        -   **删除错误的标注框**: 您完全不需要为此烦恼或重新开始整个标注。在"图像标注与表单填写"界面的左侧，有一个 **"已添加标注"列表**（详见 **5.1.2.2 节 C区**）。您画的每一个标注框都在那里有一条对应的记录。找到您画错或不想要的那个标注，点击该记录最右侧的 **【删除】** 图标按钮（通常是一个垃圾桶的标志）。点击后，那个错误的标注框就会立刻从图像上被移除。
        -   **微调已有的标注框**: 如果您想调整已经画好的标注框的大小或位置，可以参考 **5.1.3.4 步骤四：编辑已绘制的标注框** 的详细说明。系统支持直接拖动框体移动位置，或拖动边缘/角落调整大小。

-   **Q4: 我想微调一下已经画好的标注框的大小或位置，可以吗？**
    -   **可能的原因**: 绘制的标注框不够精确，需要进行细节上的调整。
    -   **建议解决方案**: 是的，可以。本系统提供了灵活的编辑功能。请参考 **5.1.3.4 步骤四：编辑已绘制的标注框** 的详细说明。简而言之，将鼠标移动到框体内部拖动可移动位置，移动到边缘或角落拖动可调整大小。

-   **Q5: 病例的"状态"都有什么含义？为什么我的病例状态变了就不能编辑了？**
    -   **可能的原因**: 对病例在工作流中的不同状态含义不了解，以及不同状态下权限的变化。
    -   **建议解决方案**: 病例的状态代表了它在整个工作流中的当前位置，不同状态对应不同的可操作权限。具体含义如下（详见 **5.1.1.1 节 B区：状态筛选器**）：
        -   **未标注**: 这是您的草稿状态。您可以对其进行任何编辑、修改，甚至删除。当您点击【保存草稿】时，病例会保持此状态。
        -   **待审核**: 您已确认完成并点击【提交审核】后的状态。此时病例正在等待审核员的批阅。为确保审核流程的严谨性，在此状态下，您不能再对病例进行任何修改（只能【查看】）。
        -   **已驳回**: 审核员认为病例存在问题并将其退回给您的状态。您可以根据审核意见，再次点击【编辑】按钮进行修改，修改完成后可以再次【提交审核】。
        -   **已通过**: 审核流程圆满结束，该病例已由审核员审核通过并归档。此状态下的病例是最终成果，通常您只能查看，无法再做任何修改，以保证数据的最终性。

-   **Q6: 为什么我的导航栏里没有"标注审核"和"管理员控制台"这两个菜单？**
    -   **可能的原因**: 您当前登录的用户账号权限不足以访问这些功能模块。
    -   **建议解决方案**: 这是由系统的权限控制机制决定的。这两个功能模块分别需要"审核员 (REVIEWER)"和"管理员 (ADMIN)"角色才能访问。如果您当前是以普通"医生 (DOCTOR)"角色登录的，那么看不到这些菜单是正常的。如果您确实因为工作需要这些权限，可以向系统管理员提出申请（请参考 **Q7**）。

-   **Q7: 我想成为审核医生，应该如何操作？**
    -   **可能的原因**: 普通医生用户希望承担更多职责，参与病例审核工作。
    -   **建议解决方案**: 本系统设计了权限升级申请的功能。通常您可以在"个人中心"、"用户管理"或类似的个人设置页面中找到一个 **【申请成为审核员】** 的按钮或入口。点击后提交您的申请，然后等待系统管理员的审批即可。一旦管理员批准了您的申请，您的账户角色将被提升，并获得相应的"标注审核"权限。

-   **Q8: 上传图像时失败或长时间没有反应，可能是什么原因？**
    -   **可能的原因**:
        1.  **网络连接问题**: 您的计算机网络连接不稳定或已中断。
        2.  **图像文件格式不兼容**: 您上传的图像文件不是系统支持的常见格式（如`.jpg`, `.jpeg`, `.png`）。
        3.  **图像文件过大**: 您上传的单张图像文件大小超过了系统配置的上传上限。
        4.  **服务器端问题**: 后端服务器异常或存储空间不足。
    -   **建议解决方案**:
        1.  **检查网络**: 确保您的计算机网络连接正常，可以尝试访问其他网站测试网络连通性。如果网络不稳定，请尝试切换网络环境。
        2.  **检查文件格式**: 确认图像文件格式。如果不是支持的格式，请使用图像编辑工具将其转换为`.jpg`或`.png`格式。
        3.  **压缩图像文件**: 如果图像文件过大（通常超过20MB），请尝试使用图像处理软件（如Photoshop、在线图片压缩工具）在不严重影响清晰度的前提下适当压缩图像文件大小，然后再重新上传。
        4.  **联系管理员**: 如果排查了以上所有问题仍无法解决，请将具体的错误提示和操作过程告知系统管理员。

---

## 7. 术语表
本章节旨在提供"血管瘤人工智能辅助治疗系统"操作手册中所有专业术语、缩写和特定概念的清晰定义。这将帮助用户更好地理解文档内容，并确保在团队协作和交流中，大家对核心概念的理解保持一致。

| 术语/缩写 | 中文全称/解释 | 详细说明与系统上下文关联 |
| :--- | :--- | :--- |
| **AI** | Artificial Intelligence (人工智能) | 在本系统中，特指用于辅助诊断的深度学习模型，能够对医学影像进行智能分析，识别病灶并提供初步诊断建议。 |
| **B/S架构** | Browser/Server (浏览器/服务器架构) | 一种软件系统架构模式。用户通过标准网页浏览器（客户端）访问应用程序，而无需在本地安装额外的软件。所有业务逻辑和数据存储都在服务器端完成。本系统采用此架构，确保了便捷性和跨平台兼容性。 |
| **UI** | User Interface (用户界面) | 指用户与软件进行交互的图形化界面。本系统的UI设计注重直观性和易用性，确保医生能够高效完成标注和管理任务。 |
| **FAQ** | Frequently Asked Questions (常见问题解答) | 文档中专门用于回答用户在使用软件过程中可能遇到的常见操作疑问和技术问题的章节。 |
| **病例** | 病例 | 在本系统中，指一个完整的、结构化的患者诊断记录单元。它包含患者的基本信息、医学影像文件、医生绘制的标注、填写的详细表单（诊断摘要、治疗建议等），以及最终的审核结果。是系统核心的数据实体。 |
| **标注** | 图像标注 | 指医生在医学影像（如图片）上，使用系统提供的绘图工具（如矩形框、圆形等），精确地圈定病灶区域，并为其赋予特定的医学分类标签（如`IH-婴幼儿血管瘤`）的过程。这是AI模型学习和系统诊断结果可视化的基础。 |
| **标签** | 诊断标签 / 分类标签 | 指用于分类和识别病灶类型的医学术语。在标注过程中，医生会从预设的标签列表中选择，例如"婴幼儿血管瘤"、"静脉畸形"等。这些标签是系统进行统计、检索和AI诊断的关键数据。 |
| **工作台** | 工作台 / Workbench | 指为特定用户角色（如医生、审核员）设计的主功能聚合页面。它集中显示了该角色最常需要处理的任务列表、状态概览和快速操作入口，旨在提供一个高效、便捷的日常工作环境。例如"我的病例标注"工作台和"标注审核"工作台。 |
| **角色** | 用户角色 | 系统中用于定义用户权限和可访问功能集合的身份标识。本系统主要有：医生 (DOCTOR)、审核员 (REVIEWER) 和管理员 (ADMIN)。不同角色对应不同的操作权限，确保数据安全和流程规范。 |
| **权限** | 操作权限 | 指特定用户角色被系统允许执行的特定操作集合。例如，只有审核员才能进行"批阅"操作，只有管理员才能进行"用户管理"操作。 |
| **IH** | Infantile Hemangioma (婴幼儿血管瘤) | 一种常见的血管瘤分类的医学缩写。在系统标注标签中出现。 |
| **RICH** | Rapidly Involuting Congenital Hemangioma (先天性快速消退型血管瘤) | 一种先天性血管瘤分类的医学缩写。在系统标注标签中出现。 |
| **PICH** | Partially Involuting Congenital Hemangioma (先天性部分消退型血管瘤) | 一种先天性血管瘤分类的医学缩写。在系统标注标签中出现。 |
| **NICH** | Non-Involuting Congenital Hemangioma (先天性不消退型血管瘤) | 一种先天性血管瘤分类的医学缩写。在系统标注标签中出现。 |
| **KHE** | Kaposiform Hemangioendothelioma (卡泊西型血管内皮细胞瘤) | 一种血管内皮细胞瘤的医学缩写。在系统标注标签中出现。 |
| **VM** | Venous Malformation (静脉畸形) | 一种脉管畸形分类的医学缩写。在系统标注标签中出现。 |
| **AVM** | Arteriovenous Malformation (动静脉畸形) | 一种脉管畸形分类的医学缩写。在系统标注标签中出现。 |

---

## 8. 附录A：场景演练
本章节旨在通过一个完整、端到端的操作场景，指导用户亲身体验"血管瘤人工智能辅助治疗系统"的核心功能。通过本演练，用户将直观了解从创建新病例到最终提交审核的每一个环节，加深对系统操作流程的理解。

### 8.1 演练目标
完成一个模拟的"左侧面颊部婴幼儿血管瘤"病例的创建、图像上传与专业标注、详细信息表单填写，并最终成功提交审核的全过程。此演练假设用户已拥有"医生"角色。

### 8.2 演练步骤
1.  **步骤一：登录系统**
    -   **操作**: 参照 **4.3 用户登录详解** 章节，使用您分配到的"医生"账号（邮箱和密码）成功登录本系统。请确保登录后进入"我的病例标注"工作台。
    -   **预期结果**: 页面成功跳转至"我的病例标注"工作台主界面。

2.  **步骤二：新建病例**
    -   **前置条件**: 您已位于"我的病例标注"工作台页面。
    -   **操作**: 在工作台页面的右上角区域，找到并用鼠标左键单击醒目的 **【新建标注】** 按钮。
    -   **预期结果**: 系统页面将跳转至"图像标注与表单填写"界面，上方图像显示区将为空白，下方病例信息表单为空白，等待您进行数据录入。

3.  **步骤三：上传待标注的医学影像**
    -   **前置条件**: 您已位于"图像标注与表单填写"界面。
    -   **操作**: 参照 **5.1.3.1 步骤一：上传待标注的医学影像** 章节，在右侧图像显示区的上传提示区域单击，并在弹出的文件选择对话框中，选择一张模拟的患者左侧面颊部的血管瘤照片文件（例如 `patient_face_hemangioma.jpg`）。点击【打开】上传。
    -   **预期结果**: 图像文件成功上传，并完整显示在右侧的图像画布区域中。同时，系统可能会在左侧工具栏的"已添加标注"列表中显示AI的初步检测结果（如果有集成AI功能）。

4.  **步骤四：选择诊断标签**
    -   **前置条件**: 图像已成功加载，且您已准备进行首次标注。
    -   **操作**: 参照 **5.1.3.2 步骤二：选择诊断标签** 章节，在左侧工具栏的 **【标签分类】** 下拉菜单处用鼠标左键单击，然后从展开的列表中选择最符合当前图像病灶特征的标签，例如选择 `IH-婴幼儿血管瘤`。
    -   **预期结果**: 您选择的标签（`IH-婴幼儿血管瘤`）显示在下拉菜单框中，表示当前标注工具已绑定此标签。

5.  **步骤五：使用矩形框工具进行标注**
    -   **前置条件**: 已选择正确的诊断标签。
    -   **操作**: 参照 **5.1.3.3 步骤三：使用矩形框工具进行标注** 章节，将鼠标光标移动到右侧图像画布中病灶的左上角，按住鼠标左键不放，并拖动鼠标至病灶的右下角，精确地框出照片中的血管瘤区域。松开鼠标左键完成绘制。
    -   **预期结果**: 一个带有 `IH-婴幼儿血管瘤` 标签的清晰矩形框将固定显示在图像上。同时，左侧的"已添加标注"列表中会新增一条对应的记录。如果您愿意，可以重复此步骤，对图像上的其他病灶进行标注。

6.  **步骤六：填写详细的病例信息表单**
    -   **前置条件**: 已完成图像标注工作。
    -   **操作**: 将页面向下滚动，参照 **5.1.4 步骤五：填写详细的病例信息表单** 章节，根据模拟的病例信息，在下方的表单中逐项、完整、准确地填写所有必填字段（带红色星号*的项目）。
        -   **患者年龄**: *`1`* (输入数字1，表示1岁)
        -   **性别**: 选择"女"
        -   **病变部位**: *`左侧面颊部`* (输入文本)
        -   **诊断摘要**: *`患儿1岁，女性。左侧面颊部可见一约2cm x 3cm红色斑块，边界清晰，表面皮温略高，初步诊断为婴幼儿血管瘤（增殖期）。`* (输入详细诊断描述)
        -   **治疗建议**: *`建议密切观察，必要时进行口服普萘洛尔治疗。`* (输入具体治疗建议)
        -   ... (确保填写所有其他必填项)
    -   **预期结果**: 所有必填字段均已填充有效信息，不再有任何红色错误提示。

7.  **步骤七：提交审核**
    -   **前置条件**: 所有图像标注和表单信息均已准确无误地完成。
    -   **操作**: 参照 **5.1.5.2 功能：【提交审核】** 章节，将页面滚动到最底部，找到并用鼠标左键单击蓝色的 **【提交审核】** 按钮。系统会弹出一个二次确认对话框，请在对话框中点击 **【确定】** 按钮。
    -   **预期结果**: 系统将显示"提交成功"的提示，并自动跳转回"我的病例标注"工作台页面。在列表中，您会看到刚刚创建的病例其"当前状态"已从"未标注"变为"待审核"，并且其操作按钮已变为只读的【查看】。至此，一个完整的病例创建、标注和提交流程演练圆满完成。

---

## 9. 附录B：常见错误与提示信息
本附录汇总了"血管瘤人工智能辅助治疗系统"在用户操作过程中，系统可能出现的各类错误提示信息及其详尽的解释与推荐的解决方案。当您在操作界面上看到任何系统提示时，都可以在此快速查找并获得帮助。

| 错误/提示信息 | 可能的原因 | 建议解决方案 |
| :--- | :--- | :--- |
| **"用户名或密码错误"** | 1.  您输入的邮箱地址或密码不正确（打字失误、忘记密码或密码被他人更改）。<br>2.  密码输入时键盘大写锁定（Caps Lock）键意外开启，导致大小写不匹配。<br>3.  您使用的输入法处于中文全角状态，导致输入了非预期的全角字符或标点。<br>4.  邮箱地址或密码输入框中，有多余的不可见空格。<br>5.  您的账户可能因多次错误尝试被暂时锁定，或已被系统管理员禁用。 | 1.  **仔细检查输入内容**: 在重新尝试登录前，请务必仔细核对您输入的邮箱地址和密码。建议先在一个文本编辑器（如记事本）中输入密码，确认无误后再复制粘贴到密码输入框中，以避免输入错误。
2.  **检查键盘状态**: 确保键盘上的Caps Lock键处于关闭状态。同时，切换输入法至英文半角模式。
3.  **使用找回密码功能**: 如果您确认输入无误但仍无法登录，强烈建议使用登录页面上的 **【忘记密码?】** 功能进行密码重置（详见 **4.4 密码找回** 章节）。
4.  **联系系统管理员**: 如果以上所有方法都无法解决问题，请立即联系您的系统管理员，寻求进一步的帮助，他们可以协助您检查账户状态或进行手动重置。 |
| **"该邮箱已被注册"** | 1.  您之前可能已经使用此邮箱地址在本系统中注册过账号，但您忘记了。<br>2.  其他人（例如您的同事或家人）可能无意中使用了您的邮箱地址或一个非常相似的地址进行了注册。 | 1.  **尝试直接登录**: 请先回忆一下您是否曾注册过该邮箱。如果是，请直接前往登录页面，使用该邮箱地址和您记忆中的密码尝试登录。
2.  **更换新邮箱注册**: 如果您确定自己从未注册过，或者您希望使用一个新的身份，请更换一个您本人拥有且尚未在本系统注册过的电子邮箱地址来完成新用户的注册流程。 |
| **"两次输入的密码不一致"** | 在用户注册或修改密码时，"设置密码"和"再次输入密码"这两个密码输入框中的内容未能完全匹配。 | 1.  **重新输入**: 请确保两次在密码输入框中输入的内容**完全一致**，包括大小写、数字和特殊符号。
2.  **仔细核对**: 您可以在第一个密码框输入后，仔细检查，然后以同样的方式再次输入到确认框。 |
| **"XX为必填项"** | 在您提交表单（如注册、病例信息填写）时，某个被系统标记为"必须填写"（通常有红色星号*）的字段为空或未填写。 | 1.  **定位错误字段**: 根据系统提示，找到并补充所有带有红色星号*的必填字段。系统通常会自动将页面滚动到第一个未填写的字段并进行高亮。
2.  **重新提交**: 确保所有必填项都已填充有效信息后，再次点击提交按钮。 |
| **"上传文件格式不支持"** | 您尝试上传的图像文件不是系统允许的图像格式（如系统可能只支持.jpg, .jpeg, .png等）。 | 1.  **检查文件扩展名**: 核对您要上传的图像文件的扩展名，确保它在系统支持的格式列表中。
2.  **转换文件格式**: 如果文件格式不支持，请使用图像编辑工具（如Windows画图、Photoshop、在线图片转换工具）将其转换为系统支持的格式（如`.jpg`或`.png`），然后重新上传。 |
| **"上传文件过大"** | 您上传的单张图像文件大小超过了系统配置的上传上限（例如，系统可能限制单张图片最大为20MB）。 | 1.  **检查文件大小**: 查看您要上传的图像文件的实际大小。
2.  **压缩图像文件**: 如果文件大小超过了系统允许的上限，请使用图像处理软件或在线图片压缩工具，在不严重影响图像清晰度的前提下，适当压缩图像文件的大小，然后再重新上传。 |

---

## 10. 附录C：角色权限矩阵详表
本附录通过一个详细的矩阵表格，清晰地列出了"血管瘤人工智能辅助治疗系统"中各个用户角色（医生、审核员、管理员）在不同功能模块及子模块下所拥有的具体操作权限。这将帮助用户明确自己在系统中的职责范围，并理解权限控制的逻辑。

| 功能模块 | 子模块 | 具体操作权限 | 医生 (DOCTOR) | 审核员 (REVIEWER) | 管理员 (ADMIN) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **账户管理** | 注册 | 注册新账户 | ✅ | ✅ | ✅ |
| | 登录 | 登录系统 | ✅ | ✅ | ✅ |
| | 退出 | 退出系统 | ✅ | ✅ | ✅ |
| | 密码找回 | 重置自己的密码 | ✅ | ✅ | ✅ |
| | 个人信息 | 查看自己的个人信息 | ✅ | ✅ | ✅ |
| | | 修改自己的个人信息 | ✅ | ✅ | ✅ |
| **病例管理** | 病例列表 | 查看自己创建的所有病例 | ✅ | ✅ | ✅ |
| | | 查看所有用户的病例 (仅限管理员/审核员在其团队内) | ❌ | ✅ (团队内) | ✅ |
| | 创建 | 新建病例记录 | ✅ | ✅ | ✅ |
| | 编辑 | 编辑自己处于"草稿"状态的病例 | ✅ | ✅ | ✅ |
| | | 编辑自己处于"已驳回"状态的病例 | ✅ | ✅ | ✅ |
| | | 编辑他人病例 (需特定授权或仅限管理员) | ❌ | ❌ | ✅ |
| | 删除 | 删除自己处于"草稿"状态的病例 | ✅ | ✅ | ✅ |
| | | 删除任何状态的任何病例 (仅限管理员) | ❌ | ❌ | ✅ |
| | 提交 | 提交病例至审核流程 | ✅ | ✅ | ✅ |
| **图像标注** | 图像上传 | 上传医学影像文件 | ✅ | ✅ | ✅ |
| | 图像查看 | 查看已上传的医学影像 | ✅ | ✅ | ✅ |
| | 标注操作 | 添加新的标注框 | ✅ | ✅ | ✅ |
| | | 修改自己已绘制的标注 | ✅ | ✅ | ✅ |
| | | 删除自己已绘制的标注 | ✅ | ✅ | ✅ |
| **病例审核** | 审核列表 | 查看所有待审核病例列表 | ❌ | ✅ | ✅ |
| | 审核操作 | 批阅具体病例详情 | ❌ | ✅ | ✅ |
| | | 通过审核，将病例状态置为"已通过" | ❌ | ✅ | ✅ |
| | | 驳回审核，将病例状态置为"已驳回" | ❌ | ✅ | ✅ |
| **团队管理** | 团队列表 | 查看自己所属的团队信息 | ✅ | ✅ | ✅ |
| | | 查看系统内所有团队信息 (仅限管理员) | ❌ | ❌ | ✅ |
| | 创建团队 | 创建新的医疗团队 | ❌ | ✅ | ✅ |
| | 成员管理 | 邀请成员加入自己管理的团队 | ❌ | ✅ | ✅ |
| | | 从自己管理的团队移除成员 | ❌ | ✅ | ✅ |
| | | 编辑团队信息 (名称、描述等) | ❌ | ✅ | ✅ |
| | | 解散团队 (仅限管理员/团队创建者) | ❌ | ❌ | ✅ |
| | 团队所有权转让 | 转让团队所有权给其他用户 (仅限管理员/团队所有者) | ❌ | ❌ | ✅ |
| **后台管理** | 用户管理 | 查看用户列表 | ❌ | ❌ | ✅ |
| | | 修改用户基本信息 | ❌ | ❌ | ✅ |
| | | 修改用户角色 | ❌ | ❌ | ✅ |
| | | 禁用/激活用户账户 | ❌ | ❌ | ✅ |
| | 权限审批 | 查看权限申请列表 (如医生申请成为审核员) | ❌ | ❌ | ✅ |
| | | 批准/拒绝权限申请 | ❌ | ❌ | ✅ |
| | 图像管理 | 查看所有上传图像列表 | ❌ | ❌ | ✅ |
| | | 删除任何图像文件 | ❌ | ❌ | ✅ |

---

## 11. 附录D：系统模块交互简述
本附录将以简洁明了的方式，概述"血管瘤人工智能辅助治疗系统"的整体技术架构和核心模块之间的交互关系。这将帮助读者从技术层面理解系统的高效运作原理。

### 11.1 系统整体架构概览
本系统采用先进的B/S（浏览器/服务器）前后端分离架构，并整合了独立运行的AI服务模块，确保了系统的灵活性、可扩展性和高可用性。

```mermaid
graph TD
    A[用户] --> B{浏览器 (前端)};
    B --> C[应用服务器 (后端)];
    C --> D[数据库 (MySQL)];
    C --> E[AI服务模块 (Python)];

    subgraph Frontend
        B
    end

    subgraph Backend
        C
        D
    end

    subgraph AI Service
        E
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bfb,stroke:#333,stroke-width:2px
    style D fill:#fbb,stroke:#333,stroke-width:2px
    style E fill:#ffb,stroke:#333,stroke-width:2px
```
*（上图为系统整体架构示意图）*

### 11.2 核心模块职责与交互流程

#### 11.2.1 前端模块 (浏览器)
-   **主要技术**: 基于主流的 **Vue.js** 框架进行开发，结合 **Element Plus** 组件库构建美观、响应式的用户界面（UI）。
-   **核心职责**: 负责用户交互、页面渲染、数据展示和用户输入收集。所有用户在界面上的操作，如点击按钮、填写表单、绘制标注，都由前端代码处理。
-   **与后端交互**: 当用户需要获取或提交数据时（如登录、创建病例、保存标注），前端会通过 **RESTful API**（HTTP请求）向后端发送请求，接收并解析后端返回的数据，然后更新界面。

#### 11.2.2 后端模块 (应用服务器)
-   **主要技术**: 使用 **Java** 语言和 **Spring Boot** 框架进行构建，提供了一系列符合RESTful风格的API接口。
-   **核心职责**: 作为整个系统的中枢，负责处理前端发来的所有业务请求，执行核心业务逻辑（如用户认证、权限管理、病例管理、数据校验），协调数据存储和AI服务调用。
-   **与数据库交互**: 通过 **JPA/Hibernate** 等持久化框架，与MySQL数据库进行数据存取操作，确保数据的安全、一致和可靠。
-   **与AI服务交互**: 当需要进行医学影像分析或生成智能诊断建议时，后端会作为客户端，通过 **HTTP/RPC** 调用独立的AI服务模块。

#### 11.2.3 数据库模块 (MySQL)
-   **主要技术**: 采用 **MySQL** 关系型数据库管理系统。
-   **核心职责**: 负责所有系统数据的持久化存储，包括但不限于用户信息、用户角色、医疗团队信息、病例详情、原始医学影像的元数据、医生绘制的标注数据，以及AI生成的诊断建议等。通过其强大的事务处理能力和数据完整性约束，确保系统数据的安全、完整和可靠。
-   **与后端交互**: 仅与后端模块进行数据交互，前端不直接访问数据库。

#### 11.2.4 AI服务模块 (Python)
-   **主要技术**: 使用 **Python** 语言开发，并集成先进的深度学习框架（如 **PyTorch/TensorFlow**）和模型库（如 **YOLO** 用于目标检测，以及大型语言模型用于生成文本建议）。ONNX格式的模型用于推理优化。
-   **核心职责**: 作为一个独立运行的微服务，专注于处理所有与人工智能相关的计算密集型任务。包括对医学影像进行智能识别、病灶区域检测、分类，并根据病例数据生成详细的诊断摘要和治疗建议。
-   **与后端交互**: 接收后端模块发送的图像文件和相关元数据，执行AI推理，并将结构化的分析结果（如标注框坐标、病灶类型、置信度）和文本建议（诊断总结、治疗建议等）返回给后端模块。AI服务在处理完成后，可能还会通过回调机制更新数据库中对应的病例记录。

### 11.3 数据流与处理流程示例

#### 11.3.1 用户上传图像并进行标注的数据流
1.  **前端**: 用户在"图像标注与表单填写"界面通过点击【上传】按钮或拖拽操作，将本地医学影像文件发送至后端服务器。
2.  **后端**: 接收到前端上传的图像文件后，将其保存到指定的文件存储路径（如 `medical_images/original`），并在数据库中创建相应的图像元数据记录。
3.  **后端 -> AI服务**: 后端会立即将新上传的图像文件发送给AI服务模块，请求进行初步的YOLO目标检测。
4.  **AI服务**: 接收图像，利用预训练的YOLO模型对图像进行快速分析，识别出潜在的血管瘤病灶区域，并返回标注框的坐标、置信度及类别信息给后端。
5.  **后端**: 接收AI服务的YOLO检测结果，将其中的标注数据保存到数据库的标签（Tag）表中，并更新病例记录中的AI检测结果字段。同时，可能会异步触发大型语言模型（LLM）的调用，生成更详细的诊断建议。
6.  **前端**: 接收到后端返回的包含AI检测结果（如果存在）的病例数据后，在图像显示区渲染图像，并根据数据库中已保存的标注数据（包括医生手动标注和AI自动生成的）在图像上绘制标注框。
7.  **前端**: 用户在图像上进行手动标注操作，每当绘制或修改一个标注框，前端会实时更新"已添加标注"列表，并通过API将这些标注数据发送回后端。
8.  **后端**: 接收前端发送的标注数据，将其更新或保存到数据库的标签（Tag）表中。

#### 11.3.2 用户提交病例进行审核的数据流
1.  **前端**: 医生完成所有标注和表单填写后，点击【提交审核】按钮，将病例的完整数据发送至后端。
2.  **后端**: 接收病例数据，进行严格的业务逻辑校验（如必填项检查、数据格式合法性），并将病例的状态更新为"待审核"，保存到数据库。
3.  **后端**: 同时，该病例将出现在拥有"审核员"或"管理员"权限用户的"标注审核"工作台中。
4.  **前端**: 审核员在"标注审核"工作台选择一个"待审核"病例并点击【批阅】，前端向后端请求该病例的详细数据（包括原始图像、所有标注和表单信息）。
5.  **后端**: 从数据库中检索并返回该病例的完整详细数据给前端。
6.  **前端**: 审核员在详情页查看病例，进行审查。点击【通过】或【驳回】按钮，并将审核意见发送给后端。
7.  **后端**: 接收审核结果和意见，更新数据库中病例的状态（"已通过"或"已驳回"），并将审核意见保存到对应的字段。
8.  **后端**: 如果病例被驳回，系统会通知原提交医生（例如通过消息提醒），使其可以在"我的病例标注"工作台中看到被驳回的病例状态，并根据审核意见进行修改后再次提交。

---

## 12. 联系我们
如果您在使用"血管瘤人工智能辅助治疗系统"的过程中遇到本手册未能解答的疑难问题，或者有任何功能改进的宝贵建议，我们非常欢迎您通过以下官方渠道与我们取得联系。我们的技术支持团队将竭诚为您提供帮助。

-   **技术支持邮箱**: `<EMAIL>` (此邮箱专门用于接收用户遇到的技术问题、操作障碍以及系统bug反馈。请在邮件中尽可能详细地描述您的问题，包含截图和操作步骤，以便我们更快地定位和解决。)

-   **联系电话**: `XXX-XXXXXXXX` (您可以在工作时间拨打此电话，与我们的技术支持人员进行实时沟通，解决紧急问题。)

-   **系统管理员**: [请在此处填写您的系统管理员姓名或部门联系方式] (如果您是企业或机构用户，建议优先联系您的内部系统管理员，他们可能能够提供更直接的支持，或协助您向我们的技术支持团队进行反馈。)