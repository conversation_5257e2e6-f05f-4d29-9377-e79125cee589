"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[282],{90282:(e,n,i)=>{i.r(n),i.d(n,{default:()=>g});var t=i(61431),a={class:"review-container"},l={key:0,class:"review-dialog-content"},o={class:"dialog-footer"};function r(e,n,i,r,u,c){var s=(0,t.g2)("el-table-column"),d=(0,t.g2)("el-button"),g=(0,t.g2)("el-table"),f=(0,t.g2)("el-pagination"),p=(0,t.g2)("el-tab-pane"),v=(0,t.g2)("el-tag"),b=(0,t.g2)("el-tabs"),w=(0,t.g2)("el-descriptions-item"),m=(0,t.g2)("el-descriptions"),h=(0,t.g2)("el-radio"),_=(0,t.g2)("el-radio-group"),k=(0,t.g2)("el-form-item"),F=(0,t.g2)("el-input"),D=(0,t.g2)("el-form"),C=(0,t.g2)("el-dialog"),R=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",a,[n[13]||(n[13]=(0,t.Lk)("div",{class:"page-header"},[(0,t.Lk)("h2",null,"标注审核")],-1)),(0,t.bF)(b,{modelValue:u.activeTab,"onUpdate:modelValue":n[2]||(n[2]=function(e){return u.activeTab=e})},{default:(0,t.k6)((function(){return[(0,t.bF)(p,{label:"待审核",name:"pending"},{default:(0,t.k6)((function(){return[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(g,{data:u.pendingReviews,style:{width:"100%"}},{default:(0,t.k6)((function(){return[(0,t.bF)(s,{prop:"caseId",label:"病例编号",width:"180"}),(0,t.bF)(s,{prop:"patientInfo",label:"患者信息",width:"180"}),(0,t.bF)(s,{prop:"department",label:"部位"}),(0,t.bF)(s,{prop:"type",label:"类型"}),(0,t.bF)(s,{prop:"createTime",label:"标注时间"}),(0,t.bF)(s,{prop:"annotator",label:"标注人"}),(0,t.bF)(s,{label:"操作",width:"200"},{default:(0,t.k6)((function(e){return[(0,t.bF)(d,{type:"primary",size:"small",onClick:function(n){return c.handleReview(e.row)}},{default:(0,t.k6)((function(){return n[6]||(n[6]=[(0,t.eW)(" 审核 ")])})),_:2,__:[6]},1032,["onClick"]),(0,t.bF)(d,{type:"info",size:"small",onClick:function(n){return c.handleView(e.row)}},{default:(0,t.k6)((function(){return n[7]||(n[7]=[(0,t.eW)(" 查看 ")])})),_:2,__:[7]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[R,u.loading.pending]]),(0,t.bF)(f,{background:"",layout:"prev, pager, next",total:u.pagination.pending.total,"page-size":u.pagination.pending.pageSize,class:"pagination",onCurrentChange:n[0]||(n[0]=function(e){return c.handlePageChange(e,"pending")})},null,8,["total","page-size"])]})),_:1}),(0,t.bF)(p,{label:"已审核",name:"reviewed"},{default:(0,t.k6)((function(){return[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(g,{data:u.reviewedCases,style:{width:"100%"}},{default:(0,t.k6)((function(){return[(0,t.bF)(s,{prop:"caseId",label:"病例编号",width:"180"}),(0,t.bF)(s,{prop:"patientInfo",label:"患者信息",width:"180"}),(0,t.bF)(s,{prop:"department",label:"部位"}),(0,t.bF)(s,{prop:"type",label:"类型"}),(0,t.bF)(s,{prop:"reviewTime",label:"审核时间"}),(0,t.bF)(s,{prop:"reviewer",label:"审核人"}),(0,t.bF)(s,{prop:"status",label:"审核结果"},{default:(0,t.k6)((function(e){return[(0,t.bF)(v,{type:"已通过"===e.row.status?"success":"danger"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,t.v_)(e.row.status),1)]})),_:2},1032,["type"])]})),_:1}),(0,t.bF)(s,{label:"操作",width:"120"},{default:(0,t.k6)((function(e){return[(0,t.bF)(d,{type:"info",size:"small",onClick:function(n){return c.handleView(e.row)}},{default:(0,t.k6)((function(){return n[8]||(n[8]=[(0,t.eW)(" 查看 ")])})),_:2,__:[8]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[R,u.loading.reviewed]]),(0,t.bF)(f,{background:"",layout:"prev, pager, next",total:u.pagination.reviewed.total,"page-size":u.pagination.reviewed.pageSize,class:"pagination",onCurrentChange:n[1]||(n[1]=function(e){return c.handlePageChange(e,"reviewed")})},null,8,["total","page-size"])]})),_:1})]})),_:1},8,["modelValue"]),(0,t.bF)(C,{visible:u.reviewDialog.visible,"onUpdate:visible":n[5]||(n[5]=function(e){return u.reviewDialog.visible=e}),title:"标注审核",width:"500px",onClose:c.closeReviewDialog},{footer:(0,t.k6)((function(){return[(0,t.Lk)("span",o,[(0,t.bF)(d,{onClick:c.closeReviewDialog},{default:(0,t.k6)((function(){return n[11]||(n[11]=[(0,t.eW)("取消")])})),_:1,__:[11]},8,["onClick"]),(0,t.bF)(d,{type:"primary",onClick:c.submitReview},{default:(0,t.k6)((function(){return n[12]||(n[12]=[(0,t.eW)("提交审核")])})),_:1,__:[12]},8,["onClick"])])]})),default:(0,t.k6)((function(){return[u.reviewDialog["case"]?((0,t.uX)(),(0,t.CE)("div",l,[(0,t.bF)(m,{column:1,border:""},{default:(0,t.k6)((function(){return[(0,t.bF)(w,{label:"病例编号"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,t.v_)(u.reviewDialog["case"].caseId),1)]})),_:1}),(0,t.bF)(w,{label:"患者信息"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,t.v_)(u.reviewDialog["case"].patientInfo),1)]})),_:1}),(0,t.bF)(w,{label:"部位"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,t.v_)(u.reviewDialog["case"].department),1)]})),_:1}),(0,t.bF)(w,{label:"类型"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,t.v_)(u.reviewDialog["case"].type),1)]})),_:1}),(0,t.bF)(w,{label:"标注人"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,t.v_)(u.reviewDialog["case"].annotator),1)]})),_:1}),(0,t.bF)(w,{label:"标注时间"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,t.v_)(u.reviewDialog["case"].createTime),1)]})),_:1})]})),_:1}),(0,t.bF)(D,{model:u.reviewDialog.form,"label-width":"80px",class:"review-form"},{default:(0,t.k6)((function(){return[(0,t.bF)(k,{label:"审核结果"},{default:(0,t.k6)((function(){return[(0,t.bF)(_,{modelValue:u.reviewDialog.form.result,"onUpdate:modelValue":n[3]||(n[3]=function(e){return u.reviewDialog.form.result=e})},{default:(0,t.k6)((function(){return[(0,t.bF)(h,{label:"approve"},{default:(0,t.k6)((function(){return n[9]||(n[9]=[(0,t.eW)("通过")])})),_:1,__:[9]}),(0,t.bF)(h,{label:"reject"},{default:(0,t.k6)((function(){return n[10]||(n[10]=[(0,t.eW)("驳回")])})),_:1,__:[10]})]})),_:1},8,["modelValue"])]})),_:1}),(0,t.bF)(k,{label:"备注"},{default:(0,t.k6)((function(){return[(0,t.bF)(F,{modelValue:u.reviewDialog.form.comment,"onUpdate:modelValue":n[4]||(n[4]=function(e){return u.reviewDialog.form.comment=e}),type:"textarea",rows:3,placeholder:"请输入审核意见（选填）"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model"])])):(0,t.Q3)("",!0)]})),_:1},8,["visible","onClose"])])}i(28706),i(44114),i(26099),i(76031);var u=i(36149);const c={name:"Review",data:function(){return{activeTab:"pending",loading:{pending:!1,reviewed:!1},pendingReviews:[],reviewedCases:[],pagination:{pending:{pageSize:10,currentPage:1,total:0},reviewed:{pageSize:10,currentPage:1,total:0}},reviewDialog:{visible:!1,case:null,form:{result:"approve",comment:""}}}},watch:{activeTab:function(e){"pending"===e?this.fetchPendingReviews():"reviewed"===e&&this.fetchReviewedCases()}},created:function(){this.fetchPendingReviews()},methods:{fetchPendingReviews:function(){var e=this;this.loading.pending=!0,setTimeout((function(){e.pendingReviews=[],e.pagination.pending.total=0,e.loading.pending=!1}),500)},fetchReviewedCases:function(){var e=this;this.loading.reviewed=!0,setTimeout((function(){e.reviewedCases=[],e.pagination.reviewed.total=0,e.loading.reviewed=!1}),500)},handlePageChange:function(e,n){this.pagination[n].currentPage=e,"pending"===n?this.fetchPendingReviews():this.fetchReviewedCases()},handleReview:function(e){this.reviewDialog.visible=!0,this.reviewDialog["case"]=e,this.reviewDialog.form={result:"approve",comment:""}},handleView:function(e){this.$router.push("/cases/view/".concat(e.id))},closeReviewDialog:function(){this.reviewDialog.visible=!1,this.reviewDialog["case"]=null,this.reviewDialog.form={result:"approve",comment:""}},submitReview:function(){var e=this;if(this.reviewDialog["case"]){var n=this.reviewDialog["case"].id,i="approve"===this.reviewDialog.form.result,t=this.reviewDialog.form.comment,a=this.$loading({lock:!0,text:"提交审核结果...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),l=i?u["default"].images.markAsApproved(n,t):u["default"].images.markAsRejected(n,t);console.log("开始将图像ID ".concat(n," 状态更新为").concat(i?'"已通过"(APPROVED)':'"已驳回"(REJECTED)')),l.then((function(){console.log("成功将图像ID ".concat(n," 状态更新为").concat(i?'"已通过"(APPROVED)':'"已驳回"(REJECTED)')),e.$message.success("审核".concat(i?"通过":"驳回","成功")),e.closeReviewDialog(),"pending"===e.activeTab?e.fetchPendingReviews():e.fetchReviewedCases()}))["catch"]((function(n){var i;console.error("审核提交失败",n),e.$message.error("审核提交失败: "+((null===(i=n.response)||void 0===i?void 0:i.data)||n.message||"未知错误"))}))["finally"]((function(){a.close()}))}else this.$message.warning("没有选择审核的病例")}}};var s=i(66262);const d=(0,s.A)(c,[["render",r],["__scopeId","data-v-5de22d39"]]),g=d}}]);
//# sourceMappingURL=282.8b1cf9e0.js.map