import api from '@/utils/api'

// 团队申请模块
const state = {
  // 待处理的申请数量
  pendingCount: 0,
  // 上次更新时间
  lastUpdated: null
}

const getters = {
  // 获取待处理的申请数量
  getPendingApplicationsCount: state => state.pendingCount,
  
  // 判断是否有待处理的申请
  hasPendingApplications: state => state.pendingCount > 0,
  
  // 获取上次更新时间
  getLastUpdated: state => state.lastUpdated
}

const mutations = {
  // 设置待处理的申请数量
  setPendingCount(state, count) {
    state.pendingCount = count
    state.lastUpdated = new Date().toISOString()
  },
  
  // 增加待处理的申请数量
  incrementPendingCount(state) {
    state.pendingCount++
    state.lastUpdated = new Date().toISOString()
  },
  
  // 减少待处理的申请数量
  decrementPendingCount(state) {
    if (state.pendingCount > 0) {
      state.pendingCount--
      state.lastUpdated = new Date().toISOString()
    }
  },
  
  // 重置待处理的申请数量
  resetPendingCount(state) {
    state.pendingCount = 0
    state.lastUpdated = new Date().toISOString()
  }
}

const actions = {
  // 获取待处理的申请数量
  async fetchPendingApplicationsCount({ commit, rootState }) {
    try {
      // 获取当前用户的团队ID
      const user = rootState.auth.user || JSON.parse(localStorage.getItem('user') || '{}')
      const teamId = user.team?.id
      
      if (!teamId) {
        console.log('用户不属于任何团队，无需获取团队申请数量')
        commit('resetPendingCount')
        return
      }
      
      // 调用API获取团队待处理申请
      const response = await api.teams.getTeamApplications(teamId, 'PENDING')
      
      if (response && response.data) {
        // 设置待处理的申请数量
        commit('setPendingCount', response.data.length)
        console.log(`团队${teamId}有${response.data.length}条待处理申请`)
      } else {
        commit('resetPendingCount')
      }
    } catch (error) {
      console.error('获取团队申请数量失败:', error)
      // 错误情况下，重置计数器以避免显示错误信息
      commit('resetPendingCount')
    }
  },
  
  // 处理团队申请后更新计数
  processApplication({ commit }, { action }) {
    if (action === 'APPROVED' || action === 'REJECTED') {
      commit('decrementPendingCount')
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
} 