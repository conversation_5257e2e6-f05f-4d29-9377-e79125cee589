"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[995],{46995:(e,t,a)=>{a.r(t),a.d(t,{default:()=>Q});var n=a(20641),s=a(53751),r=a(90033),c={class:"container mt-4"},l={class:"d-flex justify-content-between align-items-center mb-4"},i={class:"card mb-4"},u={class:"card-body"},o={class:"row"},d={class:"col-md-3 mb-2"},f={class:"col-md-3 mb-2"},p={class:"col-md-3 mb-2"},k={class:"col-md-3 mb-2"},g={class:"d-flex justify-content-end mt-2"},m={key:0,class:"loader-container"},v={key:1,class:"alert alert-danger"},b={key:2,class:"text-center py-5"},L={key:3,class:"row"},y={class:"card h-100"},h={class:"image-container"},C=["src","alt"],x={class:"card-body"},E={class:"card-title"},A={class:"card-text"},w={class:"text-muted"},D={class:"card-text"},F={class:"badge bg-secondary ms-2"},_={class:"card-text"},P={class:"card-footer bg-transparent"},I={class:"d-flex justify-content-between"},T=["onClick"],B=["onClick"],R={key:4,class:"mt-4"},S={class:"pagination justify-content-center"},X=["onClick"];function W(e,t,a,W,q,U){var V=(0,n.g2)("router-link");return(0,n.uX)(),(0,n.CE)("div",c,[(0,n.Lk)("div",l,[t[9]||(t[9]=(0,n.Lk)("h2",null,"图像管理",-1)),(0,n.Lk)("div",null,[(0,n.bF)(V,{to:"/images/upload",class:"btn btn-success"},{default:(0,n.k6)((function(){return t[8]||(t[8]=[(0,n.Lk)("i",{class:"bi bi-plus-circle me-1"},null,-1),(0,n.eW)(" 上传新图像 ")])})),_:1,__:[8]})])]),(0,n.Lk)("div",i,[(0,n.Lk)("div",u,[(0,n.Lk)("div",o,[(0,n.Lk)("div",d,[t[11]||(t[11]=(0,n.Lk)("label",{for:"statusFilter",class:"form-label"},"状态",-1)),(0,n.bo)((0,n.Lk)("select",{id:"statusFilter",class:"form-select","onUpdate:modelValue":t[0]||(t[0]=function(e){return W.filters.status=e})},t[10]||(t[10]=[(0,n.Fv)('<option value="" data-v-6cfc50f8>全部</option><option value="DRAFT" data-v-6cfc50f8>草稿</option><option value="SUBMITTED" data-v-6cfc50f8>待审核</option><option value="APPROVED" data-v-6cfc50f8>已通过</option><option value="REJECTED" data-v-6cfc50f8>未通过</option>',5)]),512),[[s.u1,W.filters.status]])]),(0,n.Lk)("div",f,[t[12]||(t[12]=(0,n.Lk)("label",{for:"patientFilter",class:"form-label"},"患者名称",-1)),(0,n.bo)((0,n.Lk)("input",{type:"text",id:"patientFilter",class:"form-control","onUpdate:modelValue":t[1]||(t[1]=function(e){return W.filters.patientName=e})},null,512),[[s.Jo,W.filters.patientName]])]),(0,n.Lk)("div",p,[t[13]||(t[13]=(0,n.Lk)("label",{for:"diagnosticFilter",class:"form-label"},"诊断类别",-1)),(0,n.bo)((0,n.Lk)("input",{type:"text",id:"diagnosticFilter",class:"form-control","onUpdate:modelValue":t[2]||(t[2]=function(e){return W.filters.diagnosisCategory=e})},null,512),[[s.Jo,W.filters.diagnosisCategory]])]),(0,n.Lk)("div",k,[t[15]||(t[15]=(0,n.Lk)("label",{for:"sortBy",class:"form-label"},"排序方式",-1)),(0,n.bo)((0,n.Lk)("select",{id:"sortBy",class:"form-select","onUpdate:modelValue":t[3]||(t[3]=function(e){return W.sortBy=e})},t[14]||(t[14]=[(0,n.Lk)("option",{value:"createdAt"},"上传时间",-1),(0,n.Lk)("option",{value:"patientName"},"患者姓名",-1),(0,n.Lk)("option",{value:"status"},"状态",-1)]),512),[[s.u1,W.sortBy]])])]),(0,n.Lk)("div",g,[(0,n.Lk)("button",{class:"btn btn-primary me-2",onClick:t[4]||(t[4]=function(){return W.applyFilters&&W.applyFilters.apply(W,arguments)})},t[16]||(t[16]=[(0,n.Lk)("i",{class:"bi bi-funnel me-1"},null,-1),(0,n.eW)(" 筛选 ")])),(0,n.Lk)("button",{class:"btn btn-outline-secondary",onClick:t[5]||(t[5]=function(){return W.resetFilters&&W.resetFilters.apply(W,arguments)})},t[17]||(t[17]=[(0,n.Lk)("i",{class:"bi bi-arrow-repeat me-1"},null,-1),(0,n.eW)(" 重置 ")]))])])]),W.loading?((0,n.uX)(),(0,n.CE)("div",m,t[18]||(t[18]=[(0,n.Lk)("div",{class:"spinner-border text-primary",role:"status"},[(0,n.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):W.error?((0,n.uX)(),(0,n.CE)("div",v,(0,r.v_)(W.error),1)):0===W.images.length?((0,n.uX)(),(0,n.CE)("div",b,t[19]||(t[19]=[(0,n.Lk)("div",{class:"mb-3"},[(0,n.Lk)("i",{class:"bi bi-images",style:{"font-size":"3rem"}})],-1),(0,n.Lk)("h4",null,"暂无图像数据",-1),(0,n.Lk)("p",{class:"text-muted"},'点击上方"上传新图像"按钮添加图像',-1)]))):((0,n.uX)(),(0,n.CE)("div",L,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(W.images,(function(e){return(0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"col-md-4 mb-4"},[(0,n.Lk)("div",y,[(0,n.Lk)("div",h,[(0,n.Lk)("img",{src:e.path,alt:e.filename,class:"card-img-top"},null,8,C)]),(0,n.Lk)("div",x,[(0,n.Lk)("h5",E,(0,r.v_)(e.filename),1),(0,n.Lk)("p",A,[(0,n.Lk)("small",w," 患者: "+(0,r.v_)(e.patientName||"未知")+", "+(0,r.v_)(e.patientAge||"?")+"岁, "+(0,r.v_)(e.patientGender||"未知"),1)]),(0,n.Lk)("p",D,[(0,n.Lk)("span",{class:(0,r.C4)(W.getStatusBadgeClass(e.status))},(0,r.v_)(W.getStatusText(e.status)),3),(0,n.Lk)("span",F,(0,r.v_)(e.mimetype),1)]),(0,n.Lk)("p",_,[(0,n.Lk)("small",null," 上传时间: "+(0,r.v_)(W.formatDate(e.createdAt)),1)])]),(0,n.Lk)("div",P,[(0,n.Lk)("div",I,[(0,n.bF)(V,{to:"/images/".concat(e.id),class:"btn btn-primary"},{default:(0,n.k6)((function(){return t[20]||(t[20]=[(0,n.Lk)("i",{class:"bi bi-eye me-1"},null,-1),(0,n.eW)(" 查看 ")])})),_:2,__:[20]},1032,["to"]),(0,n.Lk)("div",null,["DRAFT"===e.status?((0,n.uX)(),(0,n.CE)("button",{key:0,class:"btn btn-warning me-2",onClick:function(t){return W.submitImage(e.id)}},t[21]||(t[21]=[(0,n.Lk)("i",{class:"bi bi-send me-1"},null,-1),(0,n.eW)(" 提交 ")]),8,T)):(0,n.Q3)("",!0),(0,n.Lk)("button",{class:"btn btn-danger",onClick:function(t){return W.confirmDelete(e.id)}},t[22]||(t[22]=[(0,n.Lk)("i",{class:"bi bi-trash me-1"},null,-1),(0,n.eW)(" 删除 ")]),8,B)])])])])])})),128))])),W.images.length>0?((0,n.uX)(),(0,n.CE)("nav",R,[(0,n.Lk)("ul",S,[(0,n.Lk)("li",{class:(0,r.C4)(["page-item",{disabled:1===W.currentPage}])},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:t[6]||(t[6]=(0,s.D$)((function(e){return W.changePage(W.currentPage-1)}),["prevent"]))},"上一页")],2),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(W.totalPages,(function(e){return(0,n.uX)(),(0,n.CE)("li",{class:(0,r.C4)(["page-item",{active:e===W.currentPage}]),key:e},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:(0,s.D$)((function(t){return W.changePage(e)}),["prevent"])},(0,r.v_)(e),9,X)],2)})),128)),(0,n.Lk)("li",{class:(0,r.C4)(["page-item",{disabled:W.currentPage===W.totalPages}])},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:t[7]||(t[7]=(0,s.D$)((function(e){return W.changePage(W.currentPage+1)}),["prevent"]))},"下一页")],2)])])):(0,n.Q3)("",!0)])}var q=a(41034),U=a(14048),V=a(30388),K=(a(23288),a(27495),a(25440),a(50953)),N=a(40834),j=a(75220),J=a(1132),M=a(20163);const O={name:"ImageList",setup:function(){var e=(0,N.Pj)(),t=(0,j.lq)(),a=(0,j.rd)(),s=(0,n.EW)((function(){return e.getters.getAllImages})),r=(0,n.EW)((function(){return e.getters.isLoading})),c=(0,n.EW)((function(){return e.getters.getError})),l=(0,K.KR)(1),i=(0,K.KR)(12),u=(0,n.EW)((function(){return Math.ceil(s.value.length/i.value)})),o=(0,K.KR)({status:t.query.status||"",patientName:"",diagnosisCategory:""}),d=(0,K.KR)("createdAt");(0,n.wB)((function(){return t.query.status}),(function(e){e!==o.value.status&&(o.value.status=e||"",f())})),(0,n.sV)((0,V.A)((0,U.A)().mark((function a(){return(0,U.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,!t.query.status){a.next=6;break}return a.next=4,e.dispatch("fetchImagesByStatus",t.query.status);case 4:a.next=8;break;case 6:return a.next=8,e.dispatch("fetchImages");case 8:a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](0);case 13:case"end":return a.stop()}}),a,null,[[0,10]])}))));var f=function(){var n=(0,V.A)((0,U.A)().mark((function n(){var s;return(0,U.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,!o.value.status){n.next=7;break}return n.next=4,e.dispatch("fetchImagesByStatus",o.value.status);case 4:a.replace({query:(0,q.A)((0,q.A)({},t.query),{},{status:o.value.status})})["catch"]((function(){})),n.next=12;break;case 7:return n.next=9,e.dispatch("fetchImages");case 9:s=(0,q.A)({},t.query),delete s.status,a.replace({query:s})["catch"]((function(){}));case 12:l.value=1,n.next=18;break;case 15:n.prev=15,n.t0=n["catch"](0);case 18:case"end":return n.stop()}}),n,null,[[0,15]])})));return function(){return n.apply(this,arguments)}}(),p=function(){o.value={status:"",patientName:"",diagnosisCategory:""},d.value="createdAt",f()},k=function(e){e>=1&&e<=u.value&&(l.value=e)},g=function(){var t=(0,V.A)((0,U.A)().mark((function t(a){return(0,U.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!confirm("确定要提交此图像进行审核吗？")){t.next=9;break}return t.prev=1,t.next=4,e.dispatch("updateImageStatus",{id:a,status:"SUBMITTED"});case 4:t.next=9;break;case 6:t.prev=6,t.t0=t["catch"](1);case 9:case"end":return t.stop()}}),t,null,[[1,6]])})));return function(e){return t.apply(this,arguments)}}(),m=function(){var t=(0,V.A)((0,U.A)().mark((function t(a){var n,s;return(0,U.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!confirm("确定要删除此图像吗？此操作不可撤销！")){t.next=14;break}return t.prev=1,n=J.Ks.service({lock:!0,text:"删除中...",background:"rgba(0, 0, 0, 0.7)"}),t.next=5,e.dispatch("deleteImage",a);case 5:s=t.sent,n.close(),s.success?M.nk.success("删除成功"):(0,M.nk)({type:"error",message:s.error||"删除失败",duration:5e3}),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](1),M.nk.error("删除失败: "+(t.t0.message||"未知错误"));case 14:case"end":return t.stop()}}),t,null,[[1,10]])})));return function(e){return t.apply(this,arguments)}}(),v=function(e){switch(e){case"DRAFT":return"badge bg-primary";case"SUBMITTED":return"badge bg-warning";case"APPROVED":return"badge bg-success";case"REJECTED":return"badge bg-danger";default:return"badge bg-secondary"}},b=function(e){switch(e){case"DRAFT":return"草稿";case"SUBMITTED":return"待审核";case"APPROVED":return"已通过";case"REJECTED":return"未通过";default:return"未知"}},L=function(e){if(!e)return"未知";var t=new Date(e);return t.toLocaleString("zh-CN")};return{images:s,loading:r,error:c,currentPage:l,totalPages:u,filters:o,sortBy:d,applyFilters:f,resetFilters:p,changePage:k,submitImage:g,confirmDelete:m,getStatusBadgeClass:v,getStatusText:b,formatDate:L}}};var $=a(66262);const z=(0,$.A)(O,[["render",W],["__scopeId","data-v-6cfc50f8"]]),Q=z}}]);