package com.medical.annotation.util;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;

import com.medical.annotation.model.Tag;

/**
 * 图像处理工具类
 */
public class ImageUtil {

    /**
     * 在图像上绘制标注框
     * @param imagePath 原始图像路径
     * @param x 标注框左上角X坐标
     * @param y 标注框左上角Y坐标
     * @param width 标注框宽度
     * @param height 标注框高度
     * @param label 标注标签
     * @return 带标注的图像
     * @throws IOException 如果图像处理失败
     */
    public static BufferedImage drawAnnotation(String imagePath, int x, int y, int width, int height, String label) throws IOException {
        // 读取原始图像
        BufferedImage originalImage = ImageIO.read(new File(imagePath));
        
        // 创建图形上下文
        Graphics2D g = originalImage.createGraphics();
        
        // 设置绘制属性
        g.setColor(Color.RED);
        g.setStroke(new BasicStroke(2));
        
        // 绘制矩形框
        g.drawRect(x, y, width, height);
        
        // 绘制标签
        if (label != null && !label.isEmpty()) {
            g.setFont(new Font("Arial", Font.BOLD, 12));
            g.drawString(label, x, y - 5);
        }
        
        g.dispose();
        
        return originalImage;
    }
    
    /**
     * 在图像上绘制标注框
     * 
     * @param originalImage 原始图像
     * @param tags 标注列表
     * @return 绘制好标注框的图像
     */
    public static BufferedImage drawAnnotations(BufferedImage originalImage, List<Tag> tags) {
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();
        
        // 创建新的图像，使用原始尺寸
        BufferedImage annotatedImage = new BufferedImage(
                width, height, BufferedImage.TYPE_INT_RGB);
        
        // 复制原始图像
        Graphics2D g2d = annotatedImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);
        
        // 设置绘图属性
        g2d.setColor(Color.RED);
        g2d.setStroke(new BasicStroke(2.0f));
        g2d.setFont(new Font("Arial", Font.BOLD, 14));
        
        // 为每个标注绘制边界框
        for (Tag tag : tags) {
            if (tag.getX() == null || tag.getY() == null || 
                tag.getWidth() == null || tag.getHeight() == null) {
                continue; // 跳过无效标注
            }
            
            // 获取归一化坐标
            double normalizedX = tag.getX();
            double normalizedY = tag.getY();
            double normalizedWidth = tag.getWidth();
            double normalizedHeight = tag.getHeight();
            String label = tag.getTag() != null ? tag.getTag() : "标注";
            
            // 转换为像素坐标 - 归一化坐标表示矩形中心点坐标和宽高比例
            // 计算左上角坐标
            int pixelX = (int)(normalizedX * width);
            int pixelY = (int)(normalizedY * height);
            int pixelW = (int)(normalizedWidth * width);
            int pixelH = (int)(normalizedHeight * height);
            
            // 绘制边界框
            g2d.drawRect(pixelX, pixelY, pixelW, pixelH);
            
            // 绘制标签文本
            g2d.drawString(label, pixelX + 5, pixelY + 15);
        }
        
        g2d.dispose();
        return annotatedImage;
    }
    
    /**
     * 绘制多个标注到图像上
     * @param imagePath 图像路径
     * @param annotations 标注数据，每个标注是一个5元素数组 [x, y, width, height, label]
     * @return 带有标注的图像
     */
    public static BufferedImage drawMultipleAnnotations(String imagePath, Object[][] annotations) throws IOException {
        // 加载原始图像
        BufferedImage originalImage;
        try {
            originalImage = ImageIO.read(new File(imagePath));
            if (originalImage == null) {
                throw new IOException("无法读取图像：" + imagePath);
            }
        } catch (IOException e) {
            System.err.println("读取图像失败: " + e.getMessage());
            // 创建一个空白图像作为后备
            originalImage = new BufferedImage(800, 600, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = originalImage.createGraphics();
            g.setColor(Color.WHITE);
            g.fillRect(0, 0, 800, 600);
            g.setColor(Color.RED);
            g.drawString("图像读取失败: " + imagePath, 50, 300);
            g.dispose();
        }
        
        // 创建一个可编辑的副本
        BufferedImage annotatedImage = new BufferedImage(
                originalImage.getWidth(),
                originalImage.getHeight(),
                BufferedImage.TYPE_INT_RGB
        );
        
        // 复制原始图像内容
        Graphics2D g2d = annotatedImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);
        
        // 设置标注样式
        g2d.setColor(Color.RED);
        g2d.setStroke(new BasicStroke(2));
        
        // 使用支持中文的字体
        Font font = new Font("Microsoft YaHei", Font.BOLD, 16);
        g2d.setFont(font);
        
        // 设置渲染提示以改善文字显示质量
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // 绘制每个标注
        for (Object[] annotation : annotations) {
            int x = (int) annotation[0];
            int y = (int) annotation[1];
            int width = (int) annotation[2];
            int height = (int) annotation[3];
            String label = (String) annotation[4];
            
            System.out.println("绘制标签: " + label + " 位于 (" + x + "," + y + ")");
            
            // 画矩形框
            g2d.setColor(Color.RED);
            g2d.drawRect(x, y, width, height);
            
            // 画标签文本背景
            FontMetrics fm = g2d.getFontMetrics();
            int textWidth = fm.stringWidth(label);
            int textHeight = fm.getHeight();
            g2d.setColor(new Color(255, 0, 0, 200)); // 更不透明的红色
            g2d.fillRect(x, y - textHeight - 2, textWidth + 6, textHeight);
            
            // 画标签文本
            g2d.setColor(Color.WHITE);
            g2d.drawString(label, x + 3, y - 5);
        }
        
        g2d.dispose();
        return annotatedImage;
    }
    
    /**
     * 调整图像大小
     * @param originalImage 原始图像
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 调整大小后的图像
     */
    public static BufferedImage resizeImage(BufferedImage originalImage, int maxWidth, int maxHeight) {
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        
        // 如果原图已经在限制范围内，直接返回
        if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
            return originalImage;
        }
        
        // 计算新的尺寸，保持宽高比
        int newWidth, newHeight;
        
        if (originalWidth > originalHeight) {
            // 宽度优先
            newWidth = maxWidth;
            newHeight = (int) (originalHeight * ((double) maxWidth / originalWidth));
        } else {
            // 高度优先
            newHeight = maxHeight;
            newWidth = (int) (originalWidth * ((double) maxHeight / originalHeight));
        }
        
        // 创建新的缩放图片
        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, originalImage.getType());
        Graphics2D g = resizedImage.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g.dispose();
        
        return resizedImage;
    }
    
    /**
     * 解码Base64编码的图像数据
     * @param base64Data Base64编码的图像数据
     * @return 解码后的BufferedImage
     */
    public static BufferedImage decodeBase64Image(String base64Data) {
        try {
            // 处理包含mime类型的Base64字符串
            if (base64Data.contains(",")) {
                base64Data = base64Data.split(",")[1];
            }
            
            // 解码Base64数据
            byte[] imageBytes = java.util.Base64.getDecoder().decode(base64Data);
            
            // 从字节数组创建图像
            java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(imageBytes);
            return ImageIO.read(bis);
        } catch (Exception e) {
            System.err.println("解码Base64图像失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 保存图像到指定路径
     * @param image 要保存的图像
     * @param outputPath 输出路径
     * @param formatName 图像格式（jpg, png等）
     * @return 是否保存成功
     */
    public static boolean saveImage(BufferedImage image, String outputPath, String formatName) {
        try {
            File outputFile = new File(outputPath);
            
            // 确保父目录存在
            File parentDir = outputFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 保存图像
            return ImageIO.write(image, formatName, outputFile);
        } catch (Exception e) {
            System.err.println("保存图像失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
} 