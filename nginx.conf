# nginx.conf

# 运行 Nginx 的用户，在 Windows 上通常不需要设置
# user  nobody;

# 工作进程数，通常设置为CPU核心数
worker_processes  1;

# 错误日志存放路径
error_log  logs/error.log;

events {
    # 每个工作进程的最大连接数
    worker_connections  1024;
}

http {
    # 引入MIME类型定义
    include       mime.types;
    default_type  application/octet-stream;

    # 日志格式
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    # 访问日志存放路径
    access_log  logs/access.log  main;

    # 开启高效文件传输模式
    sendfile        on;
    keepalive_timeout  65;

    # === 反向代理服务器配置 ===
    server {
        # 监听 80 端口，这是标准的HTTP端口
        listen       80;
        server_name  localhost;

        # 规则一：处理前端应用的请求
        # 所有不匹配下面 /medical/api/ 规则的请求，都默认转发给前端开发服务器
        location / {
            proxy_pass http://localhost:8080; # 您的前端服务地址

            # 设置必要的头信息，确保WebSocket（用于热更新）等功能正常工作
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 规则二：处理后端API的请求
        # 所有以 /medical/api/ 开头的请求，都转发给后端的Java服务器
        location /medical/api/ {
            proxy_pass http://localhost:8085; # 您的后端服务地址

            # 设置必要的头信息
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 规则三：处理AI服务的请求
        # 所有以 /ai/ 开头的请求，都转发给AI服务
        location /ai/ {
            proxy_pass http://localhost:8086/; # 注意这里的尾部斜杠，会去掉/ai前缀

            # 设置必要的头信息
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            # 增加超时时间，因为AI处理可能需要更长时间
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            # 允许上传大文件
            client_max_body_size 10M;
        }

        # 自定义错误页面 (可选)
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
} 