<template>
  <div class="annotation-review-container">
    <AnnotationReviewList />
  </div>
</template>

<script>
import AnnotationReviewList from '@/components/AnnotationReviewList.vue';

export default {
  name: 'AnnotationReview',
  components: {
    AnnotationReviewList
  }
};
</script>

<style scoped>
.annotation-review-container {
  padding: 0;
  height: 100%;
  background-color: #f5f7fa;
}

/* 在SimpleLayout中时填满整个页面 */
:deep(.simple-layout .annotation-review-container) {
  padding: 0;
}
</style> 