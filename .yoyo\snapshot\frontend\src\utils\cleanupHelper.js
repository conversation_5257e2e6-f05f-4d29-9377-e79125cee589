import api from './api';

/**
 * 清理数据库中的无效图像对记录
 * 主要针对metadata_id为1的无效记录和特定ID的问题记录
 */
export const cleanInvalidImagePairs = () => {
  console.log('[清理工具] 开始清理无效数据');
  
  // 1. 尝试删除metadata_id为1的无效图像对
  api.imagePairs.getByMetadataId(1)
    .then(response => {
      const invalidPairs = response.data;
      if (invalidPairs && invalidPairs.length > 0) {
        console.log('[清理工具] 发现metadata_id为1的无效图像对:', invalidPairs.length, '条记录');
        
        // 遍历删除每一条记录，确保可靠删除
        invalidPairs.forEach(pair => {
          console.log(`[清理工具] 尝试删除ID为${pair.id}的无效图像对`);
          api.imagePairs.delete(pair.id)
            .then(() => {
              console.log(`[清理工具] 成功删除ID为${pair.id}的无效图像对`);
            })
            .catch(err => {
              console.error(`[清理工具] 删除ID为${pair.id}的无效图像对失败:`, err);
            });
        });
        
        // 同时也调用批量删除API
        api.imagePairs.deleteByMetadataId(1)
          .then(() => {
            console.log('[清理工具] 成功批量清理metadata_id为1的无效图像对记录');
          })
          .catch(error => {
            console.error('[清理工具] 批量清理无效图像对失败:', error);
          });
      } else {
        console.log('[清理工具] 未发现metadata_id为1的无效图像对');
      }
    })
    .catch(error => {
      console.error('[清理工具] 检查无效图像对时出错:', error);
    });
  
  // 2. 特别处理可能有问题的记录（如ID为147和148的记录）
  const specialIds = [147, 148];
  specialIds.forEach(id => {
    api.imagePairs.getOne(id)
      .then(response => {
        if (response.data) {
          const pair = response.data;
          
          // 检查记录是否确实是需要删除的无效记录（带有乱码路径）
          const path = pair.imageOnePath || '';
          const hasEncodingIssues = path.includes('琛') || path.includes('绯') || !path.includes('\\');
          
          if (hasEncodingIssues) {
            console.log(`[清理工具] 发现ID为${id}的乱码记录，准备删除`);
            
            api.imagePairs.delete(id)
              .then(() => {
                console.log(`[清理工具] 成功删除ID为${id}的乱码记录`);
              })
              .catch(err => {
                console.error(`[清理工具] 删除ID为${id}的乱码记录失败:`, err);
              });
          }
        }
      })
      .catch(err => {
        // 404错误是正常的，说明记录不存在
        if (err.response && err.response.status !== 404) {
          console.error(`[清理工具] 检查ID为${id}的记录时出错:`, err);
        }
      });
  });
  
  // 3. 查找所有带有乱码路径的记录
  api.imagePairs.getAll()
    .then(response => {
      if (response.data && Array.isArray(response.data)) {
        const pairs = response.data;
        const invalidPairs = pairs.filter(pair => {
          const path = pair.imageOnePath || '';
          return path.includes('琛') || path.includes('绯') || 
                 (path.length > 0 && !path.includes('\\') && !path.includes('/'));
        });
        
        if (invalidPairs.length > 0) {
          console.log(`[清理工具] 发现${invalidPairs.length}条带有乱码路径的记录`);
          
          invalidPairs.forEach(pair => {
            console.log(`[清理工具] 尝试删除乱码记录，ID: ${pair.id}, 路径: ${pair.imageOnePath}`);
            api.imagePairs.delete(pair.id)
              .then(() => {
                console.log(`[清理工具] 成功删除乱码记录，ID: ${pair.id}`);
              })
              .catch(err => {
                console.error(`[清理工具] 删除乱码记录失败，ID: ${pair.id}`, err);
              });
          });
        } else {
          console.log('[清理工具] 未发现带有乱码路径的记录');
        }
      }
    })
    .catch(error => {
      console.error('[清理工具] 获取所有图像对记录失败:', error);
    });
};

export default {
  cleanInvalidImagePairs
}; 