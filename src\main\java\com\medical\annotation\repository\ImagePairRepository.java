package com.medical.annotation.repository;

import com.medical.annotation.model.ImagePair;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ImagePairRepository extends JpaRepository<ImagePair, Long> {
    List<ImagePair> findByDiagnosisId(Integer diagnosisId);
    void deleteByDiagnosisId(Integer diagnosisId);
    List<ImagePair> findByImageOnePathContaining(String pathFragment);
} 