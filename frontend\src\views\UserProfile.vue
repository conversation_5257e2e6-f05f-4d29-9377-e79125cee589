<template>
  <div class="profile-container">
    <!-- 顶部紫色渐变背景区域 -->
    <div class="profile-header">
      <div class="avatar-container" @click="triggerFileInput">
        <img :src="avatarUrl || require('@/assets/images/default-avatar.png')" alt="用户头像" class="avatar" />
        <div class="avatar-overlay">
          <i class="el-icon-camera"></i>
          <span>更换头像</span>
        </div>
      </div>
      <div class="user-id">ID {{ userCustomId || userId }}</div>
      <input 
        type="file" 
        ref="fileInput" 
        style="display: none" 
        accept="image/*" 
        @change="handleFileChange"
      />
    </div>
    
    <!-- 底部白色背景区域 -->
    <div class="profile-content">
      <div class="info-section">
        <div class="info-item">
          <div class="icon"><i class="el-icon-user"></i></div>
          <div class="item-content">
            <div class="label">个人资料</div>
            <div class="value">{{ userName }}</div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
        
        <div class="info-item">
          <div class="icon"><i class="el-icon-message"></i></div>
          <div class="item-content">
            <div class="label">邮箱</div>
            <div class="value">{{ userEmail }}</div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
        
        <div class="info-item">
          <div class="icon"><i class="el-icon-office-building"></i></div>
          <div class="item-content">
            <div class="label">医院</div>
            <div class="value">{{ userHospital }}</div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
        
        <div class="info-item">
          <div class="icon"><i class="el-icon-first-aid-kit"></i></div>
          <div class="item-content">
            <div class="label">科室</div>
            <div class="value">{{ userDepartment }}</div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
        
        <div class="info-item">
          <div class="icon"><i class="el-icon-s-custom"></i></div>
          <div class="item-content">
            <div class="label">角色</div>
            <div class="value">{{ userRole }}</div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮区域 -->
      <div class="action-section">
        <el-button type="primary" icon="el-icon-key" @click="goToChangePasswordByName">修改密码</el-button>
        <el-button type="success" icon="el-icon-edit" @click="goToEditProfile" style="margin-left: 15px;">编辑资料</el-button>
        <el-button @click="goBack" style="margin-left: 15px;">返回工作台</el-button>
      </div>
      

      
      <!-- 编辑资料对话框已移至独立页面 EditProfile.vue -->
    </div>
  </div>
</template>

<script>
import { API_BASE_URL } from '@/config/api.config';
import api from '@/utils/api';

export default {
  name: 'UserProfile',
  data() {
    return {
      userId: '',
      userCustomId: '',  // 添加自定义ID字段
      userName: '',
      userEmail: '',
      userHospital: '',
      userDepartment: '',
      userRole: '',
      userRoleCode: '', // 添加角色代码
      avatarUrl: null,
      isUploading: false,
      isAdmin: false // 是否为管理员
    }
  },
  created() {
    // 从localStorage获取用户信息
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      this.userId = user.id || '未设置';
      this.userCustomId = user.customId || ''; // 获取用户自定义ID
      this.userName = user.name || '未设置';
      this.userEmail = user.email || '未设置';
      this.userHospital = user.hospital || '未设置';
      this.userDepartment = user.department || '未设置';
      this.userRoleCode = user.role || ''; // 保存原始角色代码
      
      // 判断是否为管理员
      this.isAdmin = user.role === 'ADMIN';
      
      // 转换角色显示
      switch(user.role) {
        case 'ADMIN':
          this.userRole = '管理员';
          break;
        case 'DOCTOR':
          this.userRole = '标注医生';
          break;
        case 'REVIEWER':
          this.userRole = '审核医生';
          break;
        default:
          this.userRole = '未知角色';
      }
      
      // 如果没有customId，尝试从API获取完整用户信息
      if (!this.userCustomId && this.userId && this.userId !== '未设置') {
        this.fetchUserDetails();
      }
      
      // 获取用户头像
      this.fetchUserAvatar();
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },
  methods: {
    goBack() {
      this.$router.push('/app/dashboard');
    },
    triggerFileInput() {
      // 触发文件选择
      this.$refs.fileInput.click();
    },
    handleFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        this.$message.error('请选择图片文件');
        return;
      }
      
      // 检查文件大小（限制为2MB）
      if (file.size > 2 * 1024 * 1024) {
        this.$message.error('图片大小不能超过2MB');
        return;
      }
      
      // 上传头像
      this.uploadAvatar(file);
    },
    async uploadAvatar(file) {
      try {
        this.isUploading = true;
        
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        
        // 调用API上传头像
        const response = await api.users.uploadAvatar(this.userId, formData);
        
        // 更新头像URL
        this.avatarUrl = API_BASE_URL + response.data.avatarUrl;
        
        this.$message.success('头像上传成功');
      } catch (error) {
        console.error('上传头像失败:', error);
        this.$message.error('上传头像失败: ' + (error.response?.data?.error || error.message || '未知错误'));
      } finally {
        this.isUploading = false;
      }
    },
    async fetchUserAvatar() {
      try {
        // 调用API获取头像URL
        const response = await api.users.getUserAvatar(this.userId);
        
        if (response.data.avatarUrl) {
          this.avatarUrl = API_BASE_URL + response.data.avatarUrl;
        }
      } catch (error) {
        console.error('获取头像失败:', error);
      }
    },
    
    // 获取用户详细信息，包括customId
    async fetchUserDetails() {
      try {
        const response = await api.users.getUser(this.userId);
        const userData = response.data;
        
        if (userData) {
          // 更新customId
          this.userCustomId = userData.customId || '';
          
          // 可选：更新其他可能的用户信息
          if (userData.name) this.userName = userData.name;
          if (userData.email) this.userEmail = userData.email;
          if (userData.hospital) this.userHospital = userData.hospital;
          if (userData.department) this.userDepartment = userData.department;
          
          // 更新本地存储的用户信息
          const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
          storedUser.customId = userData.customId;
          localStorage.setItem('user', JSON.stringify(storedUser));
        }
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
      }
    },
    
    // 通过名称跳转到修改密码页面
    goToChangePasswordByName() {
      console.log('正在通过路由名称跳转到修改密码页面...');
      // 在控制台打印所有可用的路由名称，用于调试
      console.log('可用路由:', Object.keys(this.$router.options.routes).map(i => this.$router.options.routes[i].name).filter(n => n));
      
      this.$router.push({ 
        name: 'ChangePassword'
      }).catch(err => {
        console.error('通过名称跳转失败:', err);
        console.log('尝试通过路径跳转...');
        
        // 尝试使用绝对路径跳转
        window.location.href = '#/app/change-password';
      });
    },
    
    // 跳转到编辑资料页面
    goToEditProfile() {
      console.log('正在跳转到编辑资料页面...');
      
      this.$router.push({ 
        name: 'EditProfile'
      }).catch(err => {
        console.error('通过名称跳转失败:', err);
        console.log('尝试通过路径跳转...');
        
        // 尝试使用绝对路径跳转
        window.location.href = '#/app/edit-profile';
      });
    },
    
    // 旧的修改密码跳转方法，保留作参考
    goToChangePassword() {
      console.log('正在跳转到修改密码页面(旧方法)...');
      this.$router.push('/app/change-password').catch(err => {
        console.error('跳转失败:', err);
      });
    },
    
    // 编辑资料功能已移至独立页面 EditProfile.vue
  }
}
</script>

<style scoped>
.profile-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.profile-header {
  background: linear-gradient(135deg, #8a6bff, #6b4fff);
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.avatar-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.7);
  margin-bottom: 10px;
  position: relative;
  cursor: pointer;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  color: white;
  font-size: 12px;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay i {
  font-size: 20px;
  margin-bottom: 5px;
}

.user-id {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 5px;
}

.profile-content {
  flex: 1;
  background-color: white;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  margin-top: -20px;
  padding: 20px;
}

.info-section {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
}

.info-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.info-item .icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #6b4fff;
}

.info-item .item-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.info-item .label {
  width: 80px;
  font-weight: 500;
  color: #333;
}

.info-item .value {
  flex: 1;
  color: #666;
}

.info-item .el-icon-arrow-right {
  color: #ccc;
}

.action-section {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

/* 表单提示样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

.dialog-footer {
  text-align: center;
}
</style> 