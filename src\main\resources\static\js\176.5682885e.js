"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[176],{176:(e,r,t)=>{t.r(r),t.d(r,{default:()=>f});t(2010);var o=t(641),n=t(33),l={class:"users-container"},a={class:"page-header"},s={class:"dialog-footer"},u={class:"dialog-footer"};function i(e,r,t,i,d,m){var c=(0,o.g2)("el-button"),f=(0,o.g2)("el-table-column"),p=(0,o.g2)("el-tag"),g=(0,o.g2)("el-switch"),b=(0,o.g2)("el-popconfirm"),F=(0,o.g2)("el-table"),w=(0,o.g2)("el-pagination"),h=(0,o.g2)("el-input"),v=(0,o.g2)("el-form-item"),k=(0,o.g2)("el-option"),_=(0,o.g2)("el-select"),V=(0,o.g2)("el-form"),P=(0,o.g2)("el-dialog"),C=(0,o.gN)("loading");return(0,o.uX)(),(0,o.CE)("div",l,[(0,o.Lk)("div",a,[r[14]||(r[14]=(0,o.Lk)("h2",null,"用户管理",-1)),(0,o.bF)(c,{type:"primary",onClick:m.handleAddUser},{default:(0,o.k6)((function(){return r[13]||(r[13]=[(0,o.eW)("添加用户")])})),_:1,__:[13]},8,["onClick"])]),(0,o.bo)(((0,o.uX)(),(0,o.Wv)(F,{data:d.users,style:{width:"100%"}},{default:(0,o.k6)((function(){return[(0,o.bF)(f,{prop:"id",label:"ID",width:"80"}),(0,o.bF)(f,{prop:"username",label:"用户名",width:"150"}),(0,o.bF)(f,{prop:"name",label:"姓名",width:"150"}),(0,o.bF)(f,{prop:"role",label:"角色"},{default:(0,o.k6)((function(e){return[(0,o.bF)(p,{type:m.getRoleType(e.row.role)},{default:(0,o.k6)((function(){return[(0,o.eW)((0,n.v_)(m.getRoleName(e.row.role)),1)]})),_:2},1032,["type"])]})),_:1}),(0,o.bF)(f,{prop:"department",label:"部门"}),(0,o.bF)(f,{prop:"email",label:"邮箱"}),(0,o.bF)(f,{prop:"createTime",label:"创建时间"}),(0,o.bF)(f,{label:"状态"},{default:(0,o.k6)((function(e){return[(0,o.bF)(g,{modelValue:e.row.active,"onUpdate:modelValue":function(r){return e.row.active=r},onChange:function(r){return m.handleStatusChange(e.row,r)},"active-color":"#13ce66","inactive-color":"#ff4949"},null,8,["modelValue","onUpdate:modelValue","onChange"])]})),_:1}),(0,o.bF)(f,{label:"操作",width:"180"},{default:(0,o.k6)((function(e){return[(0,o.bF)(c,{type:"text",size:"small",onClick:function(r){return m.handleEditUser(e.row)}},{default:(0,o.k6)((function(){return r[15]||(r[15]=[(0,o.eW)("编辑")])})),_:2,__:[15]},1032,["onClick"]),(0,o.bF)(c,{type:"text",size:"small",onClick:function(r){return m.handleResetPassword(e.row)}},{default:(0,o.k6)((function(){return r[16]||(r[16]=[(0,o.eW)(" 重置密码 ")])})),_:2,__:[16]},1032,["onClick"]),(0,o.bF)(b,{title:"确定要删除该用户吗？",onConfirm:function(r){return m.handleDeleteUser(e.row)}},{reference:(0,o.k6)((function(){return[(0,o.bF)(c,{type:"text",size:"small",style:{color:"#F56C6C"}},{default:(0,o.k6)((function(){return r[17]||(r[17]=[(0,o.eW)("删除")])})),_:1,__:[17]})]})),_:2},1032,["onConfirm"])]})),_:1})]})),_:1},8,["data"])),[[C,d.loading]]),(0,o.bF)(w,{background:"",layout:"prev, pager, next",total:d.total,"page-size":d.pageSize,onCurrentChange:m.handlePageChange,class:"pagination"},null,8,["total","page-size","onCurrentChange"]),(0,o.bF)(P,{title:d.userForm.id?"编辑用户":"添加用户",visible:d.dialogVisible,width:"500px",onClose:m.closeDialog},{footer:(0,o.k6)((function(){return[(0,o.Lk)("span",s,[(0,o.bF)(c,{onClick:r[8]||(r[8]=function(e){return d.dialogVisible=!1})},{default:(0,o.k6)((function(){return r[18]||(r[18]=[(0,o.eW)("取消")])})),_:1,__:[18]}),(0,o.bF)(c,{type:"primary",onClick:m.submitForm},{default:(0,o.k6)((function(){return r[19]||(r[19]=[(0,o.eW)("确 定")])})),_:1,__:[19]},8,["onClick"])])]})),default:(0,o.k6)((function(){return[(0,o.bF)(V,{model:d.userForm,rules:d.userRules,ref:"userFormRef","label-width":"80px",style:{padding:"0 20px"}},{default:(0,o.k6)((function(){return[(0,o.bF)(v,{label:"用户名",prop:"username"},{default:(0,o.k6)((function(){return[(0,o.bF)(h,{modelValue:d.userForm.username,"onUpdate:modelValue":r[0]||(r[0]=function(e){return d.userForm.username=e}),disabled:d.userForm.id},null,8,["modelValue","disabled"])]})),_:1}),(0,o.bF)(v,{label:"姓名",prop:"name"},{default:(0,o.k6)((function(){return[(0,o.bF)(h,{modelValue:d.userForm.name,"onUpdate:modelValue":r[1]||(r[1]=function(e){return d.userForm.name=e})},null,8,["modelValue"])]})),_:1}),(0,o.bF)(v,{label:"角色",prop:"role"},{default:(0,o.k6)((function(){return[(0,o.bF)(_,{modelValue:d.userForm.role,"onUpdate:modelValue":r[2]||(r[2]=function(e){return d.userForm.role=e}),placeholder:"请选择角色"},{default:(0,o.k6)((function(){return[(0,o.bF)(k,{label:"管理员",value:"ADMIN"}),(0,o.bF)(k,{label:"标注医生",value:"DOCTOR"}),(0,o.bF)(k,{label:"审核医生",value:"REVIEWER"})]})),_:1},8,["modelValue"])]})),_:1}),(0,o.bF)(v,{label:"部门",prop:"department"},{default:(0,o.k6)((function(){return[(0,o.bF)(h,{modelValue:d.userForm.department,"onUpdate:modelValue":r[3]||(r[3]=function(e){return d.userForm.department=e})},null,8,["modelValue"])]})),_:1}),(0,o.bF)(v,{label:"邮箱",prop:"email"},{default:(0,o.k6)((function(){return[(0,o.bF)(h,{modelValue:d.userForm.email,"onUpdate:modelValue":r[4]||(r[4]=function(e){return d.userForm.email=e})},null,8,["modelValue"])]})),_:1}),d.userForm.id?(0,o.Q3)("",!0):((0,o.uX)(),(0,o.Wv)(v,{key:0,label:"密码",prop:"password"},{default:(0,o.k6)((function(){return[(0,o.bF)(h,{modelValue:d.userForm.password,"onUpdate:modelValue":r[5]||(r[5]=function(e){return d.userForm.password=e}),type:"password"},null,8,["modelValue"])]})),_:1})),d.userForm.id?(0,o.Q3)("",!0):((0,o.uX)(),(0,o.Wv)(v,{key:1,label:"确认密码",prop:"confirmPassword"},{default:(0,o.k6)((function(){return[(0,o.bF)(h,{modelValue:d.userForm.confirmPassword,"onUpdate:modelValue":r[6]||(r[6]=function(e){return d.userForm.confirmPassword=e}),type:"password"},null,8,["modelValue"])]})),_:1})),(0,o.bF)(v,{label:"状态"},{default:(0,o.k6)((function(){return[(0,o.bF)(g,{modelValue:d.userForm.active,"onUpdate:modelValue":r[7]||(r[7]=function(e){return d.userForm.active=e})},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["title","visible","onClose"]),(0,o.bF)(P,{title:"重置密码",visible:d.resetPasswordDialog.visible,width:"400px",onClose:r[12]||(r[12]=function(e){return d.resetPasswordDialog.visible=!1})},{footer:(0,o.k6)((function(){return[(0,o.Lk)("span",u,[(0,o.bF)(c,{onClick:r[11]||(r[11]=function(e){return d.resetPasswordDialog.visible=!1})},{default:(0,o.k6)((function(){return r[20]||(r[20]=[(0,o.eW)("取消")])})),_:1,__:[20]}),(0,o.bF)(c,{type:"primary",onClick:m.submitResetPassword},{default:(0,o.k6)((function(){return r[21]||(r[21]=[(0,o.eW)("确定")])})),_:1,__:[21]},8,["onClick"])])]})),default:(0,o.k6)((function(){return[(0,o.bF)(V,{model:d.resetPasswordDialog.form,rules:d.resetPasswordRules,ref:"resetPasswordForm","label-width":"100px"},{default:(0,o.k6)((function(){return[(0,o.bF)(v,{label:"新密码",prop:"password"},{default:(0,o.k6)((function(){return[(0,o.bF)(h,{modelValue:d.resetPasswordDialog.form.password,"onUpdate:modelValue":r[9]||(r[9]=function(e){return d.resetPasswordDialog.form.password=e}),type:"password",placeholder:"请输入新密码"},null,8,["modelValue"])]})),_:1}),(0,o.bF)(v,{label:"确认新密码",prop:"confirmPassword"},{default:(0,o.k6)((function(){return[(0,o.bF)(h,{modelValue:d.resetPasswordDialog.form.confirmPassword,"onUpdate:modelValue":r[10]||(r[10]=function(e){return d.resetPasswordDialog.form.confirmPassword=e}),type:"password",placeholder:"请确认新密码"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["visible"])])}t(6280),t(6918),t(8706),t(6031);const d={name:"Users",data:function(){var e=this,r=function(r,t,o){t!==e.userForm.password?o(new Error("两次输入密码不一致!")):o()},t=function(r,t,o){t!==e.resetPasswordDialog.form.password?o(new Error("两次输入密码不一致!")):o()};return{loading:!1,users:[],total:0,currentPage:1,pageSize:10,dialogVisible:!1,userForm:{id:null,username:"",name:"",role:"",department:"",email:"",password:"",confirmPassword:"",active:!0},userRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:r,trigger:"blur"}]},resetPasswordDialog:{visible:!1,userId:null,form:{password:"",confirmPassword:""}},resetPasswordRules:{password:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:t,trigger:"blur"}]}}},created:function(){this.fetchUsers()},methods:{getRoleType:function(e){var r={ADMIN:"danger",DOCTOR:"primary",REVIEWER:"success"};return r[e]||"info"},getRoleName:function(e){var r={ADMIN:"管理员",DOCTOR:"标注医生",REVIEWER:"审核医生"};return r[e]||"未知"},handlePageChange:function(e){this.currentPage=e,this.fetchUsers()},fetchUsers:function(){var e=this;this.loading=!0,setTimeout((function(){e.users=[],e.total=0,e.loading=!1}),500)},handleAddUser:function(){this.userForm={id:null,username:"",name:"",role:"",department:"",email:"",password:"",confirmPassword:"",active:!0},this.dialogVisible=!0},handleEditUser:function(e){this.userForm={id:e.id,username:e.username,name:e.name,role:e.role,department:e.department,email:e.email,active:e.active},this.dialogVisible=!0},handleDeleteUser:function(e){this.$message.success("已删除用户: ".concat(e.name)),this.fetchUsers()},handleStatusChange:function(e,r){this.$message.success("已".concat(r?"启用":"禁用","用户: ").concat(e.name)),e.active=!r},handleResetPassword:function(e){this.resetPasswordDialog.visible=!0,this.resetPasswordDialog.userId=e.id,this.resetPasswordDialog.form={password:"",confirmPassword:""}},closeDialog:function(){this.dialogVisible=!1,this.$refs.userFormRef&&this.$refs.userFormRef.resetFields()},submitForm:function(){var e=this;this.$refs.userFormRef.validate((function(r){if(!r)return!1;e.userForm.id?e.$message.success("用户更新成功"):e.$message.success("用户添加成功"),e.dialogVisible=!1,e.fetchUsers()}))},submitResetPassword:function(){var e=this;this.$refs.resetPasswordForm.validate((function(r){if(!r)return!1;e.$message.success("密码重置成功"),e.resetPasswordDialog.visible=!1}))}}};var m=t(6262);const c=(0,m.A)(d,[["render",i],["__scopeId","data-v-c668e5ac"]]),f=c},4599:(e,r,t)=>{var o=t(6518),n=t(4576),l=t(9472),a=l(n.setTimeout,!0);o({global:!0,bind:!0,forced:n.setTimeout!==a},{setTimeout:a})},5575:(e,r,t)=>{var o=t(6518),n=t(4576),l=t(9472),a=l(n.setInterval,!0);o({global:!0,bind:!0,forced:n.setInterval!==a},{setInterval:a})},6031:(e,r,t)=>{t(5575),t(4599)},9472:(e,r,t)=>{var o=t(4576),n=t(8745),l=t(4901),a=t(4215),s=t(2839),u=t(7680),i=t(2812),d=o.Function,m=/MSIE .\./.test(s)||"BUN"===a&&function(){var e=o.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}();e.exports=function(e,r){var t=r?2:1;return m?function(o,a){var s=i(arguments.length,1)>t,m=l(o)?o:d(o),c=s?u(arguments,t):[],f=s?function(){n(m,this,c)}:m;return r?e(f,a):e(f)}:e}}}]);