import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/components/layout/MainLayout.vue'
import SimpleLayout from '@/components/layout/SimpleLayout.vue'
import Dashboard from '@/views/Dashboard.vue'
import store from '../store'
import { canAccessRoute } from '../utils/permissions'
import { preloadDashboardData } from '@/views/Dashboard.vue'
import { API_BASE_URL, DASHBOARD_STATS_URL } from '../config/api.config'

const routes = [
  {
    path: '/app',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/app/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { 
          keepAlive: true,
          title: '工作台'
        }
      },
      {
        path: 'cases',
        name: 'Cases',
        component: () => import('@/views/Cases.vue')
      },
      {
        path: 'cases/new',
        name: 'NewCase',
        component: () => import('@/views/CaseForm.vue')
      },
      {
        path: 'cases/edit/:id',
        name: 'EditCase',
        component: () => import('@/views/CaseForm.vue')
      },
      {
        path: 'cases/view/:id',
        name: 'ViewCase',
        component: () => import('@/views/CaseView.vue')
      },
      {
        path: 'cases/form',
        name: 'CaseDetailForm',
        component: () => import('@/views/CaseDetailForm.vue')
      },
      {
        path: 'cases/structured-form',
        name: 'CaseStructuredForm',
        component: () => import('@/views/CaseStructuredForm.vue')
      },
      {
        path: 'review',
        name: 'Review',
        component: () => import('@/views/Review.vue')
      },
      {
        path: 'teams',
        name: 'Teams',
        component: () => import('@/views/Teams.vue')
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/Users.vue'),
        meta: { 
          requiresAdmin: true,
          title: '部门成员'
        }
      },
      {
        path: 'annotation-reviews',
        name: 'AnnotationReviews',
        component: () => import('@/views/AnnotationReview.vue'),
        meta: {
          requiresAuth: true,
          title: '标注审核',
          // 允许审核医生和管理员访问
          accessRoles: ['ADMIN', 'REVIEWER']
        }
      }
    ]
  },
  {
    path: '/',
    name: 'Root',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue')
  },
  {
    path: '/images',
    name: 'ImageList',
    component: () => import('../views/ImageList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/images/:id',
    name: 'ImageDetail',
    component: () => import('../views/ImageDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/images/upload',
    name: 'ImageUpload',
    component: () => import('../views/ImageUpload.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/annotations',
    name: 'ImageAnnotation',
    component: () => import('../views/ImageAnnotationPage.vue')
  },
  {
    path: '/cases/form',
    name: 'PublicCaseDetailForm',
    component: () => import('@/views/CaseDetailForm.vue')
  },
  {
    path: '/cases/structured-form',
    name: 'PublicCaseStructuredForm',
    component: () => import('@/views/CaseStructuredForm.vue')
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('../views/Admin.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/reviewer-applications',
    name: 'ReviewerApplications',
    component: () => import('../views/admin/ReviewerApplications.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/standalone-review',
    name: 'StandaloneReview',
    component: () => import('@/views/StandaloneReview.vue'),
    meta: {
      requiresAuth: true,
      title: '标注审核',
      accessRoles: ['ADMIN', 'REVIEWER']
    }
  },
  {
    path: '/review-standalone',
    name: 'ReviewStandalone',
    component: SimpleLayout,
    children: [
      {
        path: '',
        component: () => import('@/views/AnnotationReview.vue')
      }
    ],
    meta: {
      requiresAuth: true,
      title: '标注审核',
      accessRoles: ['ADMIN', 'REVIEWER']
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

router.beforeEach((to, from, next) => {
  // 记录导航详情
  console.log('[路由导航] 导航开始:', {
    从: from.fullPath,
    到: to.fullPath,
    时间: new Date().toLocaleTimeString(),
    query参数: to.query,
    来源页面: document.referrer
  });
  
  // 设置应用内操作标记
  if (from.name) { // 如果from.name存在，说明是应用内导航
    sessionStorage.setItem('isAppOperation', 'true');
  }
  
  // 检查是否从表单页面保存后进行导航
  const isNavigatingAfterSave = sessionStorage.getItem('isNavigatingAfterSave') === 'true';
  const isNavigatingFromForm = sessionStorage.getItem('navigatingFromForm') === 'true';
  
  // 如果是从表单保存后导航，且目标是登录页面，则重定向到工作台
  if ((isNavigatingAfterSave || isNavigatingFromForm) && to.path === '/login') {
    console.log('检测到从表单页面保存后导航到登录页面，重定向到工作台');
    sessionStorage.removeItem('isNavigatingAfterSave');
    sessionStorage.removeItem('navigatingFromForm');
    return next('/app/dashboard');
  }
  
  // 公开页面列表
  const publicPages = ['/login', '/register', '/annotations', '/cases/form', '/cases/structured-form']
  const authRequired = !publicPages.includes(to.path)
  
  // 获取用户信息
  const user = JSON.parse(localStorage.getItem('user'))
  const isAuthenticated = store.getters.isAuthenticated
  
  // 保存当前路由，用于登录后重定向
  if (to.path !== '/login' && !isAuthenticated) {
    sessionStorage.setItem('redirectPath', to.fullPath);
  }
  
  // 如果即将访问工作台页面，提前预加载数据
  if (to.path === '/app/dashboard' || to.path === '/app' || to.path === '/app/') {
    preloadDashboardData();
    
    // 添加：如果当前已登录，立即调用全局统计数据刷新函数
    if (user && window.refreshDashboardStats) {
      console.log('[路由导航] 检测到前往工作台，预先刷新统计数据');
      setTimeout(() => {
        window.refreshDashboardStats();
      }, 0);
    }
  }
  
  // 1. 检查是否需要登录
  if (authRequired && !user) {
    return next('/login')
  }
  
  // 2. 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  if (requiresAuth && !isAuthenticated) {
    // 如果当前是在表单页面且设置了特殊标记
    if (from.path.includes('/cases/structured-form') && sessionStorage.getItem('allowFormOperation') === 'true') {
      console.log('表单操作中，即使未认证也允许完成此次导航');
      sessionStorage.removeItem('allowFormOperation');  // 使用后移除标记
      return next('/app/dashboard');  // 导航到工作台
    }
    
    return next('/login')
  }
  
  // 3. 检查路由是否需要特定权限
  const userRole = user ? user.role : null
  if (requiresAuth && userRole) {
    // 首先检查路由元数据中的角色要求
    const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)
    const requiresReviewer = to.matched.some(record => record.meta.requiresReviewer)
    const requiresDoctor = to.matched.some(record => record.meta.requiresDoctor)
    
    if (
      (requiresAdmin && userRole !== 'ADMIN') ||
      (requiresReviewer && userRole !== 'REVIEWER' && userRole !== 'ADMIN') ||
      (requiresDoctor && userRole !== 'DOCTOR' && userRole !== 'ADMIN')
    ) {
      // 没有权限，跳转到默认页面
      return next('/app/dashboard')
    }
    
    // 然后使用权限配置进行进一步检查
    if (!canAccessRoute(userRole, to.path)) {
      console.warn(`User with role ${userRole} does not have permission to access ${to.path}`)
      return next('/app/dashboard')
    }
  }
  
  // 4. 特定路由重定向
  if (to.path === '/dashboard') {
    return next('/app/dashboard')
  } else if (to.path === '/cases') {
    return next('/app/cases')
  }
  
  // 5. 通过所有检查，允许访问
  next()
})

// 添加全局后置钩子
router.afterEach((to, from) => {
  // 记录导航完成详情
  console.log('[路由导航] 导航完成:', {
    从: from.fullPath,
    到: to.fullPath,
    时间: new Date().toLocaleTimeString(),
    当前URL: window.location.href,
    导航状态: {
      isAppOperation: sessionStorage.getItem('isAppOperation'),
      isNavigatingAfterSave: sessionStorage.getItem('isNavigatingAfterSave'),
      returningToWorkbench: sessionStorage.getItem('returningToWorkbench'),
      isLogoutOperation: sessionStorage.getItem('isLogoutOperation')
    }
  });
  
  // 设置应用内操作标记
  sessionStorage.setItem('isAppOperation', 'true');
  
  // 确保不在登录页面时，设置应用内操作状态
  if (router.currentRoute.value.path !== '/login') {
    sessionStorage.setItem('isAppOperation', 'true');
  }
  
  // 添加进入Dashboard页面时的数据刷新逻辑
  if (to.path === '/app/dashboard' || to.path === '/app/' || to.path === '/app') {
    console.log('[路由导航] 检测到进入工作台页面，立即执行统计数据获取');
    
    try {
      // 获取当前用户ID
      const userStr = localStorage.getItem('user');
      if (!userStr) return;
      
      const user = JSON.parse(userStr);
      const userId = user.customId || user.id;
      if (!userId) return;
      
      // 使用同步XHR请求立即获取数据
      const xhr = new XMLHttpRequest();
      xhr.open('GET', `${DASHBOARD_STATS_URL}/${userId}?t=${Date.now()}`, false); // 同步请求
      xhr.send();
      
      if (xhr.status === 200) {
        console.log('[路由导航] 统计数据获取成功');
        const data = JSON.parse(xhr.responseText);
        
        // 缓存数据到全局变量和localStorage
        window.dashboardStats = {
          totalCount: parseInt(data.totalCount || 0),
          draftCount: parseInt(data.draftCount || 0),
          reviewedCount: parseInt(data.reviewedCount || 0),
          submittedCount: parseInt(data.submittedCount || 0),
          approvedCount: parseInt(data.approvedCount || 0),
          rejectedCount: parseInt(data.rejectedCount || 0),
          dataSource: "navigation_xhr",
          timestamp: Date.now()
        };
        
        // 保存到localStorage以便下次使用
        localStorage.setItem('dashboardStats', JSON.stringify(window.dashboardStats));
        
        // 如果DOM已加载，直接更新统计卡片
        setTimeout(() => {
          const statElements = document.querySelectorAll('.stat-value');
          if (statElements && statElements.length >= 5) {
            statElements[0].textContent = data.totalCount || 0;
            statElements[1].textContent = data.draftCount || 0;
            statElements[2].textContent = data.reviewedCount || 0;
            statElements[3].textContent = data.submittedCount || 0;
            statElements[4].textContent = data.approvedCount || 0;
            console.log('[路由导航] 统计数据DOM已更新');
          }
        }, 100); // 短暂延迟确保DOM加载
      }
    } catch (e) {
      console.error('[路由导航] 获取统计数据失败:', e);
    }
  }
})

export default router 