# 标签更新失败问题修复指南

## 问题描述

在标签更新API中，当尝试更新标签ID=15时，出现400错误：
> "Image metadata not found for ID: 1"

## 问题原因

分析代码发现，在`TagApiController.java`文件的`updateTag`方法中，硬编码了一个固定的`metadataId = 1`，无论更新哪个标签都去验证ID=1的元数据是否存在。

但实际上，每个标签关联自己的`metadata_id`（例如ID=15的标签实际关联的是`metadata_id=19`），所以应该使用标签自身的`metadata_id`来验证。

## 错误代码位置

文件：`src/main/java/com/medical/annotation/controller/TagApiController.java`

问题代码片段（约第800行左右）：
```java
try {
    // 使用固定图片ID
    Integer metadataId = 1;  // 这里错误地使用了固定值1
    
    // 验证图像元数据存在
    if (!imageMetadataRepository.existsById(TypeConverter.toLong(metadataId))) {
        System.out.println("图像元数据不存在: metadataId=" + metadataId);
        return ResponseEntity.badRequest().body("Image metadata not found for ID: " + metadataId);
    }
}
```

## 修复方法

修改为使用标签自身的`metadataId`：

```java
try {
    // 使用标签实际关联的metadata_id
    Long metadataId = existingTag.getMetadataId();  // 使用标签自身的metadata_id
    
    // 验证图像元数据存在
    if (!imageMetadataRepository.existsById(metadataId)) {
        System.out.println("图像元数据不存在: metadataId=" + metadataId);
        return ResponseEntity.badRequest().body("Image metadata not found for ID: " + metadataId);
    }
}
```

## 修复步骤

1. 打开文件：`src/main/java/com/medical/annotation/controller/TagApiController.java`
2. 找到`@PutMapping("/{id}")`注解下的`updateTag`方法
3. 定位到`// 使用固定图片ID`注释下方的代码行
4. 将`Integer metadataId = 1;`替换为`Long metadataId = existingTag.getMetadataId();`
5. 保存文件
6. 重新编译和部署应用程序

## 验证方法

修复后，尝试更新ID=15的标签，操作应该成功，不再出现400错误。 