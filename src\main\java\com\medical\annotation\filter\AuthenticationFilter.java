package com.medical.annotation.filter;

import com.medical.annotation.model.User;
import com.medical.annotation.service.UserService;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

public class AuthenticationFilter extends OncePerRequestFilter {
    
    private final UserService userService;
    
    public AuthenticationFilter(UserService userService) {
        this.userService = userService;
    }
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) 
            throws ServletException, IOException {
        
        // 白名单：放行最近标注接口
        String path = request.getRequestURI();
        if (path.startsWith("/medical/api/images/recent-annotations/")) {
            System.out.println("[认证过滤器] 白名单放行: " + path);
            filterChain.doFilter(request, response);
            System.out.println("[认证过滤器] 白名单放行后: " + path);
            return;
        }
        System.out.println("[认证过滤器] 进入主认证流程: " + path);
        
        // 支持标准Authorization头和自定义头
        String userId = extractUserId(request);
        String email = extractEmail(request);
        
        // URL解码Email (如果包含%符号)
        if (email != null && email.contains("%")) {
            try {
                String decodedEmail = java.net.URLDecoder.decode(email, "UTF-8");
                System.out.println("直接解码Email: " + email + " → " + decodedEmail);
                email = decodedEmail;
            } catch (Exception e) {
                System.err.println("Email解码失败: " + e.getMessage());
            }
        }
        
        // 调试信息
        System.out.println("认证过滤器 - 请求路径: " + request.getRequestURI());
        System.out.println("提取的用户ID: " + userId + (userId != null && userId.contains(",") ? " (包含逗号，已处理)" : ""));
        System.out.println("提取的Email: " + email);
        
        // 尝试通过提取的信息获取用户
        User user = null;
        
        try {
            // 首先通过ID查找
            if (userId != null) {
                System.out.println("尝试通过ID查找用户: " + userId);
                
                // 尝试判断userId是否为数字ID
                try {
                    int numericId = Integer.parseInt(userId);
                    // 如果是数字ID，直接通过数字ID查找
                    try {
                        User userById = userService.getUserById(numericId);
                        user = userById;
                        System.out.println("通过数字ID找到用户: " + user.getName());
                    } catch (Exception e) {
                        System.out.println("通过数字ID " + numericId + " 未找到用户，尝试通过自定义ID查找: " + e.getMessage());
                    }
                } catch (NumberFormatException e) {
                    // 如果不是数字ID，继续尝试通过自定义ID查找
                    System.out.println("ID不是数字格式，尝试通过自定义ID查找");
                }
                
                // 如果通过数字ID未找到，或者ID不是数字，尝试通过自定义ID查找
                if (user == null) {
                    user = userService.getUserByCustomId(userId).orElse(null);
                    if (user != null) {
                        System.out.println("通过自定义ID找到用户: " + user.getName());
                    }
                }
            }
            
            // 如果通过ID找不到，尝试通过电子邮件查找
            if (user == null && email != null) {
                System.out.println("尝试通过Email查找用户: " + email);
                user = userService.getUserByEmail(email).orElse(null);
                if (user != null) {
                    System.out.println("通过Email找到用户: " + user.getName());
                }
            }
            
            // 如果成功获取用户，创建认证令牌
            if (user != null) {
                List<GrantedAuthority> authorities = new ArrayList<>();
                authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getRole().name()));
                
                // 设置认证信息到上下文
                Authentication authentication = new UsernamePasswordAuthenticationToken(
                    user.getEmail(), null, authorities);
                SecurityContextHolder.getContext().setAuthentication(authentication);
                
                System.out.println("用户成功认证: " + user.getEmail() + ", 角色: " + user.getRole());
            } else {
                System.out.println("未找到有效用户，继续作为匿名用户");
            }
        } catch (Exception e) {
            System.err.println("认证过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        filterChain.doFilter(request, response);
        System.out.println("[认证过滤器] 主认证流程后: " + path);
    }
    
    /**
     * 从请求中提取用户ID
     */
    private String extractUserId(HttpServletRequest request) {
        // 尝试从Authorization头中提取
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer user_")) {
            // 解析Bearer user_[userId]格式
            String userId = authHeader.substring("Bearer user_".length());
            // 如果包含冒号，说明是复合格式，需要只取第一部分
            if (userId.contains(":")) {
                userId = userId.split(":")[0];
            }
            return userId;
        }
        
        // 如果没有从Authorization头中找到，尝试从X-User-Id头中获取
        String userId = request.getHeader("X-User-Id");
        if (userId != null && !userId.isEmpty()) {
            // 处理可能包含逗号的复合ID格式（如"300000001, 3"）
            if (userId.contains(",")) {
                // 分割并返回去掉空格的第一个有效ID
                String[] idParts = userId.split(",");
                for (String idPart : idParts) {
                    String trimmedId = idPart.trim();
                    if (!trimmedId.isEmpty()) {
                        System.out.println("从复合ID中提取第一个有效ID: " + trimmedId);
                        return trimmedId;
                    }
                }
            }
            return userId;
        }
        
        // 如果都找不到，尝试从URL参数中获取
        return request.getParameter("userId");
    }
    
    /**
     * 从请求中提取电子邮件
     */
    private String extractEmail(HttpServletRequest request) {
        String email = null;
        
        // 尝试从Authorization头中提取
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.contains(":")) {
            try {
                // 提取并解码base64编码的Email部分
                String[] parts = authHeader.split(":");
                if (parts.length > 1) {
                    String base64Email = parts[1];
                    byte[] decoded = Base64.getDecoder().decode(base64Email);
                    email = new String(decoded, "UTF-8");
                    System.out.println("从Authorization头提取Email: " + email);
                    return email;
                }
            } catch (Exception e) {
                System.err.println("解析Authorization头中的Email失败: " + e.getMessage());
            }
        }
        
        // 尝试从X-Authentication-Email或X-User-Email头中获取
        email = request.getHeader("X-Authentication-Email");
        if (email != null && !email.isEmpty()) {
            System.out.println("从X-Authentication-Email头提取Email: " + email);
            return decodeEmailIfNeeded(email);
        }
        
        email = request.getHeader("X-User-Email");
        if (email != null && !email.isEmpty()) {
            System.out.println("从X-User-Email头提取Email: " + email);
            return decodeEmailIfNeeded(email);
        }
        
        // 最后尝试从请求参数中获取
        email = request.getParameter("email");
        if (email != null && !email.isEmpty()) {
            System.out.println("从请求参数提取Email: " + email);
            return decodeEmailIfNeeded(email);
        }
        
        return null;
    }
    
    /**
     * 如果需要，对Email进行URL解码
     * @param email 可能编码过的Email
     * @return 解码后的Email
     */
    private String decodeEmailIfNeeded(String email) {
        if (email != null && email.contains("%")) {
            try {
                String decodedEmail = java.net.URLDecoder.decode(email, "UTF-8");
                System.out.println("解码Email: " + email + " → " + decodedEmail);
                return decodedEmail;
            } catch (Exception e) {
                System.err.println("Email解码失败: " + e.getMessage());
            }
        }
        return email;
    }
} 