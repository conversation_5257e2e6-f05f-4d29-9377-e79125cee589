package com.medical.annotation.exception;

public class UserNotFoundException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    public UserNotFoundException(String message) {
        super(message);
    }
    
    public UserNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
    // 创建常用实例的工厂方法
    public static UserNotFoundException forCustomId(String customId) {
        return new UserNotFoundException("用户不存在 (自定义ID: " + customId + ")");
    }
    
    public static UserNotFoundException forId(Integer id) {
        return new UserNotFoundException("用户不存在 (ID: " + id + ")");
    }
    
    public static UserNotFoundException forEmail(String email) {
        return new UserNotFoundException("用户不存在 (邮箱: " + email + ")");
    }
} 