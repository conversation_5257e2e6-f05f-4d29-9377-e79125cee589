<template>
  <div class="container">
    <h2 class="mt-4 mb-3">图像详情</h2>
    
    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>
    
    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>
    
    <div v-else class="row">
      <div class="col-md-8">
        <div class="image-container position-relative">
          <img ref="imageElement" :src="imageUrl" class="img-fluid" alt="血管瘤图像" @load="initializeCanvas" />
          <canvas 
            ref="annotationCanvas" 
            class="annotation-canvas position-absolute top-0 start-0" 
            @mousedown="startDrawing" 
            @mousemove="draw" 
            @mouseup="endDrawing"
            @mouseleave="endDrawing">
          </canvas>
        </div>
        <div class="mt-3 d-flex justify-content-between">
          <div>
            <button class="btn btn-outline-primary me-2" @click="clearAnnotations">清除标注</button>
            <button class="btn btn-primary" @click="saveAnnotations">保存标注</button>
          </div>
          <div>
            <button class="btn btn-secondary" @click="$router.go(-1)">返回</button>
          </div>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">图像信息</h5>
          </div>
          <div class="card-body">
            <p><strong>名称：</strong> {{ image.name }}</p>
            <p><strong>上传者：</strong> {{ image.uploaderName }}</p>
            <p><strong>上传时间：</strong> {{ formatDate(image.uploadTime) }}</p>
            <p><strong>状态：</strong> <span :class="statusClass">{{ statusText }}</span></p>
            
            <hr />
            
            <h6>标注工具</h6>
            <div class="btn-group mb-3">
              <button 
                v-for="tool in drawingTools" 
                :key="tool.type"
                :class="['btn', currentTool === tool.type ? 'btn-primary' : 'btn-outline-primary']" 
                @click="selectTool(tool.type)">
                <i :class="tool.icon"></i> {{ tool.label }}
              </button>
            </div>
            
            <h6>注释</h6>
            <textarea v-model="notes" class="form-control mb-3" rows="3" placeholder="添加标注说明..."></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ImageDetail',
  data() {
    return {
      image: {},
      loading: true,
      error: null,
      annotations: [],
      currentAnnotation: null,
      isDrawing: false,
      currentTool: 'freehand',
      notes: '',
      drawingTools: [
        { type: 'freehand', label: '自由绘制', icon: 'bi bi-pencil' },
        { type: 'rectangle', label: '矩形', icon: 'bi bi-square' },
        { type: 'circle', label: '圆形', icon: 'bi bi-circle' }
      ]
    };
  },
  computed: {
    imageId() {
      return this.$route.params.id;
    },
    imageUrl() {
      return `${process.env.VUE_APP_API_URL}/api/images/${this.imageId}/file`;
    },
    statusText() {
      const statusMap = {
        'UPLOADED': '已上传',
        'ANNOTATED': '已标注',
        'REVIEWED': '已审核',
        'APPROVED': '已批准',
        'REJECTED': '已拒绝'
      };
      return statusMap[this.image.status] || this.image.status;
    },
    statusClass() {
      const classMap = {
        'UPLOADED': 'text-secondary',
        'ANNOTATED': 'text-primary',
        'REVIEWED': 'text-info',
        'APPROVED': 'text-success',
        'REJECTED': 'text-danger'
      };
      return classMap[this.image.status] || '';
    }
  },
  mounted() {
    this.fetchImageData();
    // 当窗口大小改变时，重新调整画布大小
    window.addEventListener('resize', this.initializeCanvas);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.initializeCanvas);
  },
  methods: {
    async fetchImageData() {
      this.loading = true;
      try {
        const response = await axios.get(`${process.env.VUE_APP_API_URL}/api/images/${this.imageId}`);
        this.image = response.data;
        
        // 如果有标注数据，加载它
        if (this.image.annotationData) {
          this.annotations = JSON.parse(this.image.annotationData);
          this.notes = this.image.notes || '';
        }
      } catch (err) {
        this.error = '加载图像详情失败: ' + (err.response?.data?.message || err.message);
      } finally {
        this.loading = false;
      }
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    },
    initializeCanvas() {
      const img = this.$refs.imageElement;
      const canvas = this.$refs.annotationCanvas;
      
      if (img && canvas) {
        // 设置canvas大小与图像一致
        canvas.width = img.clientWidth;
        canvas.height = img.clientHeight;
        
        // 绘制已有的标注
        this.redrawAnnotations();
      }
    },
    redrawAnnotations() {
      const canvas = this.$refs.annotationCanvas;
      if (!canvas) return;
      
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // 绘制所有已保存的标注
      this.annotations.forEach(annotation => {
        this.drawAnnotation(ctx, annotation);
      });
    },
    drawAnnotation(ctx, annotation) {
      ctx.strokeStyle = '#FF0000';
      ctx.lineWidth = 2;
      
      if (annotation.type === 'freehand') {
        ctx.beginPath();
        annotation.points.forEach((point, index) => {
          if (index === 0) {
            ctx.moveTo(point.x, point.y);
          } else {
            ctx.lineTo(point.x, point.y);
          }
        });
        ctx.stroke();
      } else if (annotation.type === 'rectangle') {
        const width = annotation.end.x - annotation.start.x;
        const height = annotation.end.y - annotation.start.y;
        ctx.strokeRect(annotation.start.x, annotation.start.y, width, height);
      } else if (annotation.type === 'circle') {
        const radius = Math.sqrt(
          Math.pow(annotation.end.x - annotation.start.x, 2) + 
          Math.pow(annotation.end.y - annotation.start.y, 2)
        );
        ctx.beginPath();
        ctx.arc(annotation.start.x, annotation.start.y, radius, 0, 2 * Math.PI);
        ctx.stroke();
      }
    },
    startDrawing(event) {
      this.isDrawing = true;
      const canvas = this.$refs.annotationCanvas;
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      if (this.currentTool === 'freehand') {
        this.currentAnnotation = {
          type: 'freehand',
          points: [{ x, y }]
        };
      } else if (this.currentTool === 'rectangle' || this.currentTool === 'circle') {
        this.currentAnnotation = {
          type: this.currentTool,
          start: { x, y },
          end: { x, y }
        };
      }
    },
    draw(event) {
      if (!this.isDrawing || !this.currentAnnotation) return;
      
      const canvas = this.$refs.annotationCanvas;
      const ctx = canvas.getContext('2d');
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      if (this.currentTool === 'freehand') {
        this.currentAnnotation.points.push({ x, y });
        
        // 绘制当前线段
        ctx.strokeStyle = '#FF0000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        const points = this.currentAnnotation.points;
        const lastPoint = points[points.length - 2];
        const newPoint = points[points.length - 1];
        ctx.moveTo(lastPoint.x, lastPoint.y);
        ctx.lineTo(newPoint.x, newPoint.y);
        ctx.stroke();
      } else {
        // 矩形或圆形，需要重新绘制所有内容
        this.currentAnnotation.end = { x, y };
        this.redrawAnnotations();
        this.drawAnnotation(ctx, this.currentAnnotation);
      }
    },
    endDrawing() {
      if (this.isDrawing && this.currentAnnotation) {
        this.annotations.push(this.currentAnnotation);
        this.currentAnnotation = null;
        this.isDrawing = false;
      }
    },
    clearAnnotations() {
      if (confirm('确定要清除所有标注吗？')) {
        this.annotations = [];
        const canvas = this.$refs.annotationCanvas;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    },
    async saveAnnotations() {
      try {
        const annotationData = JSON.stringify(this.annotations);
        await axios.post(`${process.env.VUE_APP_API_URL}/api/images/${this.imageId}/annotate`, {
          annotationData,
          notes: this.notes
        });
        this.$store.dispatch('showAlert', { type: 'success', message: '标注保存成功' });
      } catch (err) {
        this.$store.dispatch('showAlert', { 
          type: 'error', 
          message: '保存标注失败: ' + (err.response?.data?.message || err.message)
        });
      }
    },
    selectTool(tool) {
      this.currentTool = tool;
    }
  }
};
</script>

<style scoped>
.image-container {
  max-height: 70vh;
  overflow: hidden;
}

.annotation-canvas {
  pointer-events: all;
  z-index: 10;
}
</style> 