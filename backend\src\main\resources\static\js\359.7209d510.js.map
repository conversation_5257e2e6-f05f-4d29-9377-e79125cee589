{"version": 3, "file": "js/359.7209d510.js", "mappings": "gMACOA,MAAM,+B,GACJA,MAAM,mB,GAiBJA,MAAM,wB,GACJA,MAAM,kB,GAKJA,MAAM,c,GAzBrBC,IAAA,EA0B0BD,MAAM,sB,GA1BhCC,IAAA,EA6B4BD,MAAM,uB,GAKfA,MAAM,c,GACJA,MAAM,mB,EAnC3B,a,GAgDmBA,MAAM,c,GACJA,MAAM,mB,EAjD3B,a,GA8DmBA,MAAM,gB,GACJA,MAAM,e,EA/D3B,a,GA2EmBA,MAAM,gB,EA3EzB,a,GAAAC,IAAA,EAiFuCD,MAAM,W,GAM5BA,MAAM,iB,GAvFvBC,IAAA,EA6FgCD,MAAM,mB,wEA5FpCE,EAAAA,EAAAA,IAuGM,MAvGNC,EAuGM,EAtGJC,EAAAA,EAAAA,IAqGM,MArGNC,EAqGM,gBAvGVC,EAAAA,EAAAA,IAAA,2jBAmBMF,EAAAA,EAAAA,IAmFM,MAnFNG,EAmFM,EAlFJH,EAAAA,EAAAA,IAiFM,MAjFNI,EAiFM,gBAhFJJ,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,cAAY,EACrBI,EAAAA,EAAAA,IAAyC,OAApCJ,MAAM,mBAAkB,YAAM,KAGrCI,EAAAA,EAAAA,IAuEE,MAvEFK,EAuEE,CAtEOC,EAAAC,QAAK,WAAhBT,EAAAA,EAAAA,IAEM,MAFNU,GAEMC,EAAAA,EAAAA,IADDH,EAAAC,OAAK,KA3BlBG,EAAAA,EAAAA,IAAA,OA6BmBJ,EAAAK,UAAO,WAAlBb,EAAAA,EAAAA,IAEM,MAFNc,GAEMH,EAAAA,EAAAA,IADDH,EAAAK,SAAO,KA9BpBD,EAAAA,EAAAA,IAAA,QAiCQV,EAAAA,EAAAA,IAoDO,QApDAa,SAAMC,EAAA,KAAAA,EAAA,IAjCrBC,EAAAA,EAAAA,KAAA,kBAiC+BT,EAAAU,aAAAV,EAAAU,YAAAC,MAAAX,EAAAY,UAAW,kB,EAC5BlB,EAAAA,EAAAA,IAYM,MAZNmB,EAYM,EAXJnB,EAAAA,EAAAA,IAUA,MAVAoB,EAUA,cATEpB,EAAAA,EAAAA,IAAyB,KAAtBJ,MAAM,aAAW,oBAC1BI,EAAAA,EAAAA,IAOC,SANCqB,GAAG,QAtCjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAuCuBhB,EAAAiB,MAAKD,CAAA,GACdE,KAAK,QACCC,YAAY,UAClBC,SAAA,GACCC,SAAUrB,EAAAsB,S,OA3CzBC,GAAA,OAuCuBvB,EAAAiB,cASTvB,EAAAA,EAAAA,IAYM,MAZN8B,EAYM,EAXJ9B,EAAAA,EAAAA,IAUA,MAVA+B,EAUA,cATE/B,EAAAA,EAAAA,IAAyB,KAAtBJ,MAAM,aAAW,oBAC1BI,EAAAA,EAAAA,IAOC,SANCqB,GAAG,WApDjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAqDuBhB,EAAA0B,SAAQV,CAAA,GACjBE,KAAK,WACCC,YAAY,QAClBC,SAAA,GACCC,SAAUrB,EAAAsB,S,OAzDzBK,GAAA,OAqDuB3B,EAAA0B,iBASThC,EAAAA,EAAAA,IAWE,MAXFkC,EAWE,EAVAlC,EAAAA,EAAAA,IAQM,MARNmC,EAQM,WAPVnC,EAAAA,EAAAA,IAKC,SAJCqB,GAAG,WAjEjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAkEuBhB,EAAA8B,SAAQd,CAAA,GACjBE,KAAK,WACJG,SAAUrB,EAAAsB,S,OApEzBS,GAAA,OAkEuB/B,EAAA8B,YAAQ,aAIbpC,EAAAA,EAAAA,IAAoC,SAA7BsC,IAAI,YAAW,UAAM,OAE9BC,EAAAA,EAAAA,IAA8EC,EAAA,CAAjEC,GAAG,mBAAmB7C,MAAM,mB,CAxEzD,SAAA8C,EAAAA,EAAAA,KAwE2E,kBAAK5B,EAAA,KAAAA,EAAA,KAxEhF6B,EAAAA,EAAAA,IAwE2E,U,IAxE3EC,EAAA,EAAAC,GAAA,SA2Ec7C,EAAAA,EAAAA,IASE,MATF8C,EASE,EARJ9C,EAAAA,EAAAA,IAOS,UANPwB,KAAK,SACD5B,MAAM,YACT+B,SAAUrB,EAAAsB,S,CAEKtB,EAAAsB,UAAO,WAAnB9B,EAAAA,EAAAA,IAA4C,OAA5CiD,KAjFlBrC,EAAAA,EAAAA,IAAA,QAkFkBV,EAAAA,EAAAA,IAA6C,aAAAS,EAAAA,EAAAA,IAApCH,EAAAsB,QAAU,SAAW,OAAd,MAlFlCoB,MAAA,KAuFYhD,EAAAA,EAAAA,IAGE,MAHFiD,EAGE,cAFAjD,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXuC,EAAAA,EAAAA,IAAmEC,EAAA,CAAtDC,GAAG,YAAY7C,MAAM,gB,CAzFhD,SAAA8C,EAAAA,EAAAA,KAyF+D,kBAAI5B,EAAA,KAAAA,EAAA,KAzFnE6B,EAAAA,EAAAA,IAyF+D,S,IAzF/DC,EAAA,EAAAC,GAAA,QA6FuBvC,EAAA4C,UAAO,WAAlBpD,EAAAA,EAAAA,IAEE,MAFFqD,EAA4C,sBAC7B1C,EAAAA,EAAAA,IAAGH,EAAA4C,SAAU,oBAChC,KA/FRxC,EAAAA,EAAAA,IAAA,wBAkGUV,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,gBAAc,EACvBI,EAAAA,EAAAA,IAAa,qB,uLAczB,SACEoD,KAAM,QACNC,MAAK,WACH,IAAMC,GAAQC,EAAAA,EAAAA,MACRC,GAASC,EAAAA,EAAAA,MACTC,GAAQC,EAAAA,EAAAA,MAERpC,GAAQqC,EAAAA,EAAAA,IAAI,IACZ5B,GAAW4B,EAAAA,EAAAA,IAAI,IACfxB,GAAWwB,EAAAA,EAAAA,KAAI,GACfhC,GAAUgC,EAAAA,EAAAA,KAAI,GACdrD,GAAQqD,EAAAA,EAAAA,IAAI,IACZjD,GAAUiD,EAAAA,EAAAA,IAAI,IACdV,GAAUU,EAAAA,EAAAA,IAAI,OAGpBC,EAAAA,EAAAA,KAAU,WAER,IAAMC,EAA8D,SAA7CC,eAAeC,QAAQ,kBACxCC,EAAgBF,eAAeC,QAAQ,iBAU7C,GARKF,GAAmBG,IACtBC,QAAQC,IAAI,cACdC,KAGAL,eAAeM,WAAW,kBAGtBJ,EAAe,CACjBC,QAAQC,IAAI,mBACZG,aAAaC,QAAQ,OAAQN,GAC7BF,eAAeM,WAAW,iBAG1B,IAAMG,EAAeT,eAAeC,QAAQ,uBAAyB,iBAOrE,OANAE,QAAQC,IAAI,gBAAiBK,QAG7BC,YAAW,WACTjB,EAAOkB,KAAKF,EACd,GAAG,IAEL,CAGA,IAAMG,EAA0D,SAA1CL,aAAaN,QAAQ,iBAC3C,GAAIW,EAOF,OANAT,QAAQC,IAAI,6BAEZG,aAAaD,WAAW,sBAGxBb,EAAOkB,KAAK,kBAKVhB,EAAMkB,MAAM1B,UACdA,EAAQ2B,MAAQnB,EAAMkB,MAAM1B,QAC5BgB,QAAQC,IAAI,oBAADW,OAAqB5B,EAAQ2B,QAE5C,IAEA,IAAM7D,EAAU,eAAA+D,GAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAC,IAAA,IAAAC,EAAAC,EAAAxF,EAAAyF,EAAAC,EAAAf,EAAAgB,EAAAC,EAAA,OAAAR,EAAAA,EAAAA,KAAAS,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAchB,IAbFhE,EAAQiD,OAAQ,EAChBtE,EAAMsE,MAAQ,GACdlE,EAAQkE,MAAQ,GAACc,EAAAE,EAAA,EAIf3B,QAAQC,IAAI,2BACZG,aAAaD,WAAW,kBACxBC,aAAaD,WAAW,eACxBC,aAAaD,WAAW,wBAGlBe,EAAe,GACZC,EAAI,EAAGA,EAAIf,aAAawB,OAAQT,IACjCxF,EAAMyE,aAAazE,IAAIwF,GACzBxF,IAAQA,EAAIkG,SAAS,cAAgBlG,EAAIkG,SAAS,WACpDX,EAAaV,KAAK7E,GAUtB,OALAuF,EAAaY,SAAQ,SAAAnG,GACnBqE,QAAQC,IAAI,SAAUtE,GACtByE,aAAaD,WAAWxE,EAC1B,IAEA8F,EAAAC,EAAA,EACuBtC,EAAM2C,SAAS,QAAS,CAC7C1E,MAAOA,EAAMsD,MACb7C,SAAUA,EAAS6C,QACpB,OAHY,GAAPS,EAAOK,EAAAO,EAMW,kBAAbZ,GAA0BA,GAAgC,YAApBa,EAAAA,EAAAA,GAAOb,GAAqB,CAAAK,EAAAC,EAAA,QAU3E,OANIrF,EAAMsE,MAFc,kBAAbS,EACQ,wBAAbA,EACY,WAEAA,EAGF,gBAChBK,EAAAS,EAAA,UAKFlC,QAAQC,IAAI,eAAgBmB,GAGxBe,OAAOC,oBACHf,EAASc,OAAOC,oBACtBpC,QAAQC,IAAI,aAAcoB,IAI5BxB,eAAeQ,QAAQ,wBAAyB,QAG5CrB,EAAQ2B,OAEVlE,EAAQkE,MAAQ,mBAGhBd,eAAeQ,QAAQ,wBAAyB,QAGhDjB,EAAM2C,SAAS,eAAgB,CAC7BM,KAAM,EACNrD,QAASsD,OAAOtD,EAAQ2B,OACxB4B,SAAU,OAGZhC,YAAW,WAETjB,EAAOkB,KAAK,CACVgC,KAAM,yBACN9B,MAAO,CAAE1B,QAASA,EAAQ2B,MAAO8B,OAAQ,IAE7C,GAAG,QAGGnC,EAAeT,eAAeC,QAAQ,gBACtCwB,EAAqBzB,eAAeC,QAAQ,sBAGlDD,eAAeM,WAAW,gBAC1BN,eAAeM,WAAW,sBAG1BN,eAAeQ,QAAQ,gBAAiB,QAGxC5D,EAAQkE,MAAQ,mBACdJ,YAAW,WACX,IAAMmC,EAAoBpC,GAAgBgB,GAAsB,iBAChEa,OAAOQ,SAASC,KAAOF,CAC3B,GAAG,OACHjB,EAAAC,EAAA,eAAAD,EAAAE,EAAA,EAAAJ,EAAAE,EAAAO,EAEAhC,QAAQ3D,MAAM,eAAckF,GAC5BlF,EAAMsE,MAAuB,kBAAfY,EAAsBA,EAAU,gBAAc,OAExC,OAFwCE,EAAAE,EAAA,EAE5DjE,EAAQiD,OAAQ,EAAIc,EAAAoB,EAAA,iBAAApB,EAAAS,EAAA,MAAAjB,EAAA,sBAExB,kBA3GgB,OAAAJ,EAAA9D,MAAA,KAAAC,UAAA,KA8GVkD,EAAuB,WAC3B,IAEE,IAAM4C,EAAwE,SAAjDjD,eAAeC,QAAQ,sBAC9CiD,EAA4E,SAApDlD,eAAeC,QAAQ,yBAGrD,GAAIgD,GAAwBC,EAE1B,YADA/C,QAAQC,IAAI,qBAId,IAAM+C,EAAU5C,aAAaN,QAAQ,QACrC,IAAKkD,EAAS,OAEd,IAAMC,EAAOC,KAAKC,MAAMH,GACxBhD,QAAQC,IAAI,aAAcgD,GAG1B,IAAMG,EAAiB,CAAC,KAAM,OAAQ,QAAS,QACzCC,EAAgBD,EAAeE,QAAO,SAAAC,GAAI,OAAMN,EAAKM,EAAM,IAE7DF,EAAczB,OAAS,GACzB5B,QAAQwD,KAAK,kBAAD5C,OAAmByC,EAAcI,KAAK,QAClDzD,QAAQC,IAAI,cACZG,aAAaD,WAAW,QAGxBN,eAAeM,WAAW,kBAE1BH,QAAQC,IAAI,mBAEhB,CAAE,MAAO5D,GACP2D,QAAQ3D,MAAM,aAAcA,GAE5B+D,aAAaD,WAAW,QACxBN,eAAeM,WAAW,gBAC5B,CACF,EAEA,MAAO,CACL9C,MAAAA,EACAS,SAAAA,EACAI,SAAAA,EACAR,QAAAA,EACArB,MAAAA,EACAI,QAAAA,EACAuC,QAAAA,EACAlC,YAAAA,EAEJ,G,eC1UF,MAAM4G,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/Login.vue", "webpack://medical-annotation-frontend/./src/views/Login.vue?240b"], "sourcesContent": ["<template>\n  <div class=\"login-page login-background\">\n    <div class=\"login-container\">\n      <!-- 左侧信息区域 -->\n      <div class=\"login-info\">\n        <h1 class=\"system-name\">血管瘤人工智能<br/>辅助治疗系统</h1>\n        <p class=\"system-slogan\">即刻体验智能诊断与专业医疗支持</p>\n        <div class=\"feature-list\">\n          <div class=\"feature-item\">AI智能诊断平台</div>\n          <div class=\"feature-item\">精准血管瘤识别</div>\n          <div class=\"feature-item\">专业医疗数据平台</div>\n          <div class=\"feature-item\">智慧医疗辅助系统</div>\n        </div>\n        <div class=\"learn-more\">\n          <a href=\"#\" class=\"learn-more-link\">了解更多 ›</a>\n        </div>\n      </div>\n      \n      <!-- 右侧登录框 -->\n      <div class=\"login-form-container\">\n        <div class=\"login-form-box\">\n          <div class=\"login-tabs\">\n            <div class=\"tab-item active\">账号密码登录</div>\n          </div>\n          \n          <div class=\"login-form\">\n        <div v-if=\"error\" class=\"alert alert-danger\">\n          {{ error }}\n        </div>\n        <div v-if=\"success\" class=\"alert alert-success\">\n          {{ success }}\n        </div>\n        \n        <form @submit.prevent=\"handleLogin\">\n              <div class=\"form-group\">\n                <div class=\"input-with-icon\">\n                  <i class=\"icon-user\"></i>\n            <input \n              id=\"email\" \n              v-model=\"email\" \n              type=\"email\" \n                    placeholder=\"请输入邮箱地址\"\n              required\n              :disabled=\"loading\"\n            >\n          </div>\n              </div>\n              \n              <div class=\"form-group\">\n                <div class=\"input-with-icon\">\n                  <i class=\"icon-lock\"></i>\n            <input \n              id=\"password\" \n              v-model=\"password\" \n              type=\"password\" \n                    placeholder=\"请输入密码\"\n              required\n              :disabled=\"loading\"\n            >\n          </div>\n              </div>\n              \n              <div class=\"form-options\">\n                <div class=\"remember-me\">\n            <input \n              id=\"remember\" \n              v-model=\"remember\" \n              type=\"checkbox\" \n              :disabled=\"loading\"\n            >\n                  <label for=\"remember\">下次自动登录</label>\n                </div>\n                <router-link to=\"/forgot-password\" class=\"forgot-password\">忘记密码?</router-link>\n          </div>\n              \n              <div class=\"form-actions\">\n            <button \n              type=\"submit\" \n                  class=\"login-btn\" \n              :disabled=\"loading\"\n            >\n                  <span v-if=\"loading\" class=\"spinner\"></span>\n                  <span>{{ loading ? '登录中...' : '登 录' }}</span>\n            </button>\n          </div>\n        </form>\n        \n            <div class=\"register-link\">\n              <span>没有账号?</span>\n              <router-link to=\"/register\" class=\"register-btn\">立即注册</router-link>\n        </div>\n        \n        <!-- 如果是从标注页面重定向过来的，显示提示 -->\n            <div v-if=\"imageId\" class=\"redirect-notice\">\n          检测到您之前正在标注图片(ID: {{ imageId }})，登录后将自动跳转回表单页面。\n        </div>\n      </div>\n          \n          <div class=\"login-footer\">\n            <span></span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted } from 'vue'\nimport { useStore } from 'vuex'\nimport { useRouter, useRoute } from 'vue-router'\nimport api from '@/utils/api'  // 导入API模块\n\nexport default {\n  name: 'Login',\n  setup() {\n    const store = useStore()\n    const router = useRouter()\n    const route = useRoute()\n    \n    const email = ref('')\n    const password = ref('')\n    const remember = ref(false)\n    const loading = ref(false)\n    const error = ref('')\n    const success = ref('')\n    const imageId = ref(null)\n    \n    // 检查是否是从\"保存并退出\"操作过来\n    onMounted(() => {\n      // 仅在非应用内导航或无特殊标记时才执行清理\n      const isAppOperation = sessionStorage.getItem('isAppOperation') === 'true';\n      const preservedUser = sessionStorage.getItem('preservedUser');\n\n      if (!isAppOperation && !preservedUser) {\n        console.log(\"执行用户数据清理逻辑\");\n      cleanInvalidUserData();\n      }\n      // 清理标记\n      sessionStorage.removeItem('isAppOperation');\n      \n      // 先尝试恢复用户会话，这对于从标注页面退出很重要\n      if (preservedUser) {\n        console.log('检测到保存的用户会话，尝试恢复');\n        localStorage.setItem('user', preservedUser);\n        sessionStorage.removeItem('preservedUser');\n        \n        // 检查是否应该重定向到工作台\n        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/app/dashboard';\n        console.log('恢复用户会话后将重定向到:', redirectPath);\n        \n        // 延迟导航以确保状态更新\n        setTimeout(() => {\n          router.push(redirectPath);\n        }, 100);\n        return;\n      }\n      \n      // 首先检查是否有保存并退出的标记\n      const isSaveAndExit = localStorage.getItem('isSaveAndExit') === 'true';\n      if (isSaveAndExit) {\n        console.log('登录页面检测到保存并退出标记，将自动重定向到工作台');\n        // 清除标记\n        localStorage.removeItem('isSaveAndExit');\n        \n        // 直接跳转到工作台\n        router.push('/app/dashboard');\n        return;\n      }\n      \n      // 从query参数中提取imageId\n      if (route.query.imageId) {\n        imageId.value = route.query.imageId\n        console.log(`检测到从标注页面跳转，图片ID: ${imageId.value}`)\n      }\n    })\n    \n    const handleLogin = async () => {\n      loading.value = true\n      error.value = ''\n      success.value = ''\n      \n      try {\n        // 在登录之前清除所有与Dashboard相关的缓存\n        console.log('登录前清除所有Dashboard相关缓存...');\n        localStorage.removeItem('dashboardStats');\n        localStorage.removeItem('tempApiData');\n        localStorage.removeItem('dashboardApiResponse');\n        \n        // 查找和清除所有包含dashboard或stats的缓存项\n        const keysToRemove = [];\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          if (key && (key.includes('dashboard') || key.includes('stats'))) {\n            keysToRemove.push(key);\n          }\n        }\n        \n        // 删除找到的缓存项\n        keysToRemove.forEach(key => {\n          console.log('清除缓存项:', key);\n          localStorage.removeItem(key);\n        });\n        \n        // 执行登录\n        const userData = await store.dispatch('login', {\n          email: email.value,\n          password: password.value\n        })\n        \n        // 检查返回值是否为错误信息或非对象（表示登录失败）\n        if (typeof userData === 'string' || !userData || typeof userData !== 'object') {\n          // 将英文错误信息翻译成中文\n          if (typeof userData === 'string') {\n            if (userData === 'Invalid credentials') {\n              error.value = '用户名或密码错误';\n            } else {\n              error.value = userData;\n            }\n          } else {\n            error.value = '登录失败，请检查邮箱和密码';\n          }\n          return;\n        }\n        \n        // 登录成功后处理用户信息\n        console.log('登录成功，检查用户信息:', userData);\n        \n        // 执行全局权限修复函数\n        if (window.fixUserPermission) {\n          const result = window.fixUserPermission();\n          console.log('登录后执行权限修复:', result);\n        }\n        \n        // 标记需要刷新工作台\n        sessionStorage.setItem('forceRefreshDashboard', 'true');\n        \n        // 登录成功\n        if (imageId.value) {\n          // 如果有imageId，说明是从标注页面跳转过来的\n          success.value = '登录成功，即将返回标注页面...';\n          \n          // 先恢复会话标识以防止再次重定向\n          sessionStorage.setItem('isNavigatingAfterSave', 'true');\n          \n          // 保存当前进度到Vuex\n          store.dispatch('saveProgress', {\n            step: 2, // 病例信息填写步骤\n            imageId: Number(imageId.value),\n            formData: null\n          })\n          \n          setTimeout(() => {\n            // 直接跳转到结构化表单页面\n            router.push({\n              path: '/cases/structured-form',\n              query: { imageId: imageId.value, direct: 1 }\n            })\n          }, 1500)\n        } else {\n          // 查看是否有保存的重定向路径\n          const redirectPath = sessionStorage.getItem('redirectPath');\n          const redirectAfterLogin = sessionStorage.getItem('redirectAfterLogin');\n          \n          // 清除重定向路径\n          sessionStorage.removeItem('redirectPath');\n          sessionStorage.removeItem('redirectAfterLogin');\n          \n          // 标记来自登录页面\n          sessionStorage.setItem('fromLoginPage', 'true');\n          \n          // 普通登录流程\n          success.value = '登录成功，正在跳转到工作台...';\n            setTimeout(() => {\n            const finalRedirectPath = redirectPath || redirectAfterLogin || '/app/dashboard';\n            window.location.href = finalRedirectPath;\n        }, 1500)\n        }\n      } catch (err) {\n        console.error('Login error:', err)\n        error.value = typeof err === 'string' ? err : '登录失败，请检查邮箱和密码'\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    // 添加清理不完整用户数据的方法\n    const cleanInvalidUserData = () => {\n      try {\n        // 检查是否是从标注表单页面导航过来的\n        const isNavigatingFromForm = sessionStorage.getItem('navigatingFromForm') === 'true';\n        const isNavigatingAfterSave = sessionStorage.getItem('isNavigatingAfterSave') === 'true';\n        \n        // 如果是从表单页面导航过来的，不清除用户数据\n        if (isNavigatingFromForm || isNavigatingAfterSave) {\n          console.log('检测到从表单页面导航，保留用户数据');\n          return;\n        }\n        \n        const userStr = localStorage.getItem('user');\n        if (!userStr) return; // 没有用户数据，不需要清理\n        \n        const user = JSON.parse(userStr);\n        console.log('检查存储的用户数据:', user);\n        \n        // 检查是否有必要的用户字段\n        const requiredFields = ['id', 'name', 'email', 'role'];\n        const missingFields = requiredFields.filter(field => !user[field]);\n        \n        if (missingFields.length > 0) {\n          console.warn(`存储的用户数据缺少必要字段: ${missingFields.join(', ')}`);\n          console.log('清除不完整的用户数据');\n          localStorage.removeItem('user');\n          \n          // 防止循环引用导致问题，也清除从会话中保存的用户\n          sessionStorage.removeItem('preservedUser');\n        } else {\n          console.log('存储的用户数据完整，包含必要字段');\n        }\n      } catch (error) {\n        console.error('检查用户数据时出错:', error);\n        // 出错时，清除可能损坏的用户数据\n        localStorage.removeItem('user');\n        sessionStorage.removeItem('preservedUser');\n      }\n    };\n    \n    return {\n      email,\n      password,\n      remember,\n      loading,\n      error,\n      success,\n      imageId,\n      handleLogin\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  margin: 0;\n  width: 100%;\n  overflow: hidden;\n}\n\n/* 确保不使用此类的背景色，让全局CSS接管 */\n.login-page.login-background {\n  /* background: linear-gradient(135deg, #0d47a1, #4a148c, #6a1b9a); */\n}\n\n.login-container {\n  display: flex;\n  max-width: 1000px;\n  margin: 50px auto;\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);\n  border-radius: 8px;\n  overflow: hidden;\n  background-color: white;\n}\n\n/* 左侧信息区域样式 */\n.login-info {\n  flex: 1;\n  padding: 60px 40px;\n  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);\n  color: white;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.system-name {\n  font-size: 32px;\n  font-weight: 600;\n  margin-bottom: 20px;\n  line-height: 1.3;\n}\n\n.system-slogan {\n  font-size: 16px;\n  margin-bottom: 40px;\n  opacity: 0.9;\n}\n\n.feature-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 40px;\n}\n\n.feature-item {\n  background-color: rgba(255, 255, 255, 0.2);\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 14px;\n  display: inline-block;\n}\n\n.learn-more {\n  margin-top: auto;\n}\n\n.learn-more-link {\n  color: white;\n  text-decoration: none;\n  font-size: 14px;\n  display: inline-flex;\n  align-items: center;\n}\n\n.learn-more-link:hover {\n  text-decoration: underline;\n}\n\n/* 右侧登录框样式 */\n.login-form-container {\n  width: 450px;\n  background-color: white;\n  display: flex;\n  flex-direction: column;\n}\n\n.login-form-box {\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.login-tabs {\n  display: flex;\n  border-bottom: 1px solid #eee;\n  margin-bottom: 25px;\n  justify-content: center;\n}\n\n.tab-item {\n  padding: 10px 0;\n  margin-right: 0;\n  font-size: 18px;\n  color: #1890ff;\n  font-weight: 500;\n  position: relative;\n}\n\n.tab-item.active:after {\n  content: '';\n  position: absolute;\n  bottom: -1px;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background-color: #1890ff;\n}\n\n.login-form {\n  flex: 1;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.input-with-icon {\n  position: relative;\n}\n\n.input-with-icon i {\n  position: absolute;\n  left: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #bfbfbf;\n}\n\n.input-with-icon input {\n  width: 100%;\n  padding: 12px 12px 12px 40px;\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  font-size: 14px;\n  transition: all 0.3s;\n}\n\n.input-with-icon input:focus {\n  border-color: #40a9ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n  outline: none;\n}\n\n.form-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  font-size: 14px;\n}\n\n.remember-me {\n  display: flex;\n  align-items: center;\n}\n\n.remember-me input {\n  margin-right: 6px;\n}\n\n.forgot-password {\n  color: #1890ff;\n  text-decoration: none;\n}\n\n.forgot-password:hover {\n  text-decoration: underline;\n}\n\n.form-actions {\n  margin-bottom: 20px;\n}\n\n.login-btn {\n  width: 100%;\n  background-color: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 24px;\n  padding: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.3s;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.login-btn:hover {\n  background-color: #40a9ff;\n}\n\n.login-btn:disabled {\n  background-color: #91d5ff;\n  cursor: not-allowed;\n}\n\n.spinner {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top-color: #fff;\n  animation: spin 0.8s linear infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n.register-link {\n  text-align: center;\n  margin-top: 16px;\n  font-size: 14px;\n}\n\n.register-link span {\n  color: #666;\n  margin-right: 5px;\n}\n\n.register-link a {\n  color: #1890ff;\n  text-decoration: none;\n  font-weight: 500;\n}\n\n.register-link a:hover {\n  text-decoration: underline;\n}\n\n.redirect-notice {\n  margin-top: 20px;\n  padding: 10px;\n  background-color: #e6f7ff;\n  border: 1px solid #91d5ff;\n  border-radius: 6px;\n  color: #1890ff;\n  font-size: 14px;\n}\n\n.login-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: #999;\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #eee;\n}\n\n.home-link {\n  color: #1890ff;\n  text-decoration: none;\n}\n\n.home-link:hover {\n  text-decoration: underline;\n}\n\n.copyright {\n  color: #999;\n}\n\n.alert {\n  padding: 12px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n  font-size: 14px;\n}\n\n.alert-danger {\n  background-color: #fff2f0;\n  border: 1px solid #ffccc7;\n  color: #ff4d4f;\n}\n\n.alert-success {\n  background-color: #f6ffed;\n  border: 1px solid #b7eb8f;\n  color: #52c41a;\n}\n\n/* 图标样式 */\n.icon-user:before {\n  content: \"👤\";\n}\n\n.icon-lock:before {\n  content: \"🔒\";\n}\n\n/* 响应式调整 */\n@media (max-width: 992px) {\n  .login-container {\n    flex-direction: column;\n  }\n  \n  .login-info {\n    padding: 30px;\n  }\n  \n  .login-form-container {\n    width: 100%;\n  }\n}\n\n@media (max-width: 576px) {\n  .login-container {\n    max-width: 100%;\n  }\n  \n  .login-form-box {\n    padding: 20px;\n  }\n  \n  .system-name {\n    font-size: 24px;\n  }\n  \n  .feature-list {\n    gap: 10px;\n  }\n  \n  .feature-item {\n    padding: 6px 12px;\n    font-size: 12px;\n  }\n}\n</style> ", "import { render } from \"./Login.vue?vue&type=template&id=bdd56362&scoped=true\"\nimport script from \"./Login.vue?vue&type=script&lang=js\"\nexport * from \"./Login.vue?vue&type=script&lang=js\"\n\nimport \"./Login.vue?vue&type=style&index=0&id=bdd56362&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-bdd56362\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createStaticVNode", "_hoisted_3", "_hoisted_4", "_hoisted_5", "$setup", "error", "_hoisted_6", "_toDisplayString", "_createCommentVNode", "success", "_hoisted_7", "onSubmit", "_cache", "_withModifiers", "handleLogin", "apply", "arguments", "_hoisted_8", "_hoisted_9", "id", "$event", "email", "type", "placeholder", "required", "disabled", "loading", "_hoisted_10", "_hoisted_11", "_hoisted_12", "password", "_hoisted_13", "_hoisted_14", "_hoisted_15", "remember", "_hoisted_16", "for", "_createVNode", "_component_router_link", "to", "_withCtx", "_createTextVNode", "_", "__", "_hoisted_17", "_hoisted_19", "_hoisted_18", "_hoisted_20", "imageId", "_hoisted_21", "name", "setup", "store", "useStore", "router", "useRouter", "route", "useRoute", "ref", "onMounted", "isAppOperation", "sessionStorage", "getItem", "preservedUser", "console", "log", "cleanInvalidUserData", "removeItem", "localStorage", "setItem", "redirectPath", "setTimeout", "push", "isSaveAndExit", "query", "value", "concat", "_ref", "_asyncToGenerator", "_regenerator", "m", "_callee", "keysToRemove", "i", "userData", "result", "redirectAfterLogin", "_t", "w", "_context", "n", "p", "length", "includes", "for<PERSON>ach", "dispatch", "v", "_typeof", "a", "window", "fixUserPermission", "step", "Number", "formData", "path", "direct", "finalRedirectPath", "location", "href", "f", "isNavigatingFromForm", "isNavigatingAfterSave", "userStr", "user", "JSON", "parse", "requiredFields", "missingFields", "filter", "field", "warn", "join", "__exports__", "render"], "sourceRoot": ""}