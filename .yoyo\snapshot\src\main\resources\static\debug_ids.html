<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库ID调试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card-title {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
        }
        .col {
            flex: 1;
            padding: 10px;
            min-width: 300px;
        }
        .image-list {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #eee;
            padding: 10px;
        }
        .image-item {
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #eee;
            border-radius: 4px;
            cursor: pointer;
        }
        .image-item:hover {
            background-color: #f5f5f5;
        }
        .image-item.selected {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .detail-panel {
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-top: 15px;
        }
        pre {
            background-color: #f8f9fa; 
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .refresh-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 15px;
        }
        .refresh-btn:hover {
            background-color: #45a049;
        }
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .alert-info {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
        }
        .alert-danger {
            background-color: #ffebee;
            border: 1px solid #ffcdd2;
            color: #b71c1c;
        }
    </style>
</head>
<body>
    <h1>数据库ID调试工具</h1>
    
    <div class="alert alert-info">
        <p>这个工具可以帮助您查看数据库中所有图像的ID以及相关的ImagePair记录，便于调试前端与后端ID不匹配的问题。</p>
    </div>
    
    <button id="refresh-btn" class="refresh-btn">刷新数据</button>
    
    <div class="row">
        <div class="col">
            <div class="card">
                <h2 class="card-title">图像列表</h2>
                <div id="image-list" class="image-list">
                    <p>加载中...</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <h2 class="card-title">详细信息</h2>
                <div id="detail-panel" class="detail-panel">
                    <p>选择左侧列表中的图像查看详细信息</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载完成后获取数据
        document.addEventListener('DOMContentLoaded', fetchImageIds);
        
        // 刷新按钮点击事件
        document.getElementById('refresh-btn').addEventListener('click', fetchImageIds);
        
        // 获取所有图像ID数据
        function fetchImageIds() {
            const imageList = document.getElementById('image-list');
            imageList.innerHTML = '<p>正在加载数据...</p>';
            
            fetch('/medical/api/debug/image-ids')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    displayImageList(data);
                })
                .catch(error => {
                    imageList.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
                    console.error('获取数据失败:', error);
                });
        }
        
        // 显示图像列表
        function displayImageList(images) {
            const imageList = document.getElementById('image-list');
            
            if (!images || images.length === 0) {
                imageList.innerHTML = '<p>没有找到图像数据</p>';
                return;
            }
            
            // 按ID排序
            images.sort((a, b) => a.id - b.id);
            
            let html = '';
            
            images.forEach(image => {
                html += `
                    <div class="image-item" data-id="${image.id}" onclick="showDetails(${JSON.stringify(image).replace(/"/g, '&quot;')})">
                        <strong>ID: ${image.id}</strong> (${image.formatted_id})<br>
                        <small>${image.filename}</small>
                    </div>
                `;
            });
            
            imageList.innerHTML = html;
        }
        
        // 显示详细信息
        function showDetails(image) {
            const detailPanel = document.getElementById('detail-panel');
            
            // 取消之前的选中状态
            const items = document.querySelectorAll('.image-item.selected');
            items.forEach(item => item.classList.remove('selected'));
            
            // 设置当前选中状态
            const currentItem = document.querySelector(`.image-item[data-id="${image.id}"]`);
            if (currentItem) {
                currentItem.classList.add('selected');
            }
            
            // 准备显示数据
            const imageDetails = {
                "ID": image.id,
                "格式化ID (前面补零)": image.formatted_id,
                "文件名": image.filename,
                "路径": image.path,
                "关联的ImagePair记录": image.pairs.length > 0 ? image.pairs : "无"
            };
            
            // 处理路径便于测试访问
            const webPath = image.path;
            const webPathHtml = webPath ? `<a href="${webPath}" target="_blank">在新窗口打开图像</a>` : '无路径';
            
            // 构建HTML
            let html = `
                <h3>图像元数据 (ImageMetadata)</h3>
                <pre>${JSON.stringify(imageDetails, null, 2)}</pre>
                <div>${webPathHtml}</div>
                
                <h3>关联的图像对 (ImagePair)</h3>
            `;
            
            if (image.pairs && image.pairs.length > 0) {
                html += `<pre>${JSON.stringify(image.pairs, null, 2)}</pre>`;
                
                // 添加测试链接
                html += `
                    <h3>测试链接</h3>
                    <ul>
                        <li><a href="/medical/api/images/${image.formatted_id}" target="_blank">测试ID: ${image.formatted_id} (有前导零)</a></li>
                        <li><a href="/medical/api/images/${image.id}" target="_blank">测试ID: ${image.id} (无前导零)</a></li>
                        <li><a href="/medical/api/image-pairs/metadata/${image.formatted_id}" target="_blank">测试ImagePair: ${image.formatted_id}</a></li>
                    </ul>
                `;
            } else {
                html += `<p>没有关联的ImagePair记录</p>`;
            }
            
            detailPanel.innerHTML = html;
        }
    </script>
</body>
</html> 