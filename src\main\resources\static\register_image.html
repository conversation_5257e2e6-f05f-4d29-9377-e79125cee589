<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图像注册工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], 
        input[type="file"],
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>图像注册工具</h1>
    
    <div class="form-container">
        <div class="form-group">
            <label for="upload-type">注册类型：</label>
            <select id="upload-type">
                <option value="upload">上传新图像</option>
                <option value="register">注册已有图像</option>
            </select>
        </div>
        
        <div id="upload-form" class="form-group">
            <label for="image-file">选择图像文件：</label>
            <input type="file" id="image-file" accept="image/*">
        </div>
        
        <div id="register-form" class="form-group" style="display: none;">
            <label for="image-path">已有图像路径：</label>
            <input type="text" id="image-path" placeholder="例如: /medical/images/temp/temp_example.jpg">
            
            <label for="image-filename">图像文件名：</label>
            <input type="text" id="image-filename" placeholder="例如: example.jpg">
        </div>
        
        <div class="form-group">
            <label for="user-id">用户ID：</label>
            <input type="text" id="user-id" value="1">
        </div>
        
        <div class="form-group">
            <button id="submit-btn">提交</button>
        </div>
    </div>
    
    <div id="result" class="result" style="display: none;">
        <h3>处理结果：</h3>
        <pre id="result-content"></pre>
    </div>
    
    <script>
        // 切换表单显示
        document.getElementById('upload-type').addEventListener('change', function() {
            const uploadForm = document.getElementById('upload-form');
            const registerForm = document.getElementById('register-form');
            
            if (this.value === 'upload') {
                uploadForm.style.display = 'block';
                registerForm.style.display = 'none';
            } else {
                uploadForm.style.display = 'none';
                registerForm.style.display = 'block';
            }
        });
        
        // 提交处理
        document.getElementById('submit-btn').addEventListener('click', async function() {
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('result-content');
            const uploadType = document.getElementById('upload-type').value;
            const userId = document.getElementById('user-id').value;
            
            resultDiv.style.display = 'block';
            resultContent.innerHTML = '处理中...';
            
            try {
                let response;
                
                if (uploadType === 'upload') {
                    // 处理上传新图像
                    const imageFile = document.getElementById('image-file').files[0];
                    if (!imageFile) {
                        throw new Error('请选择图像文件');
                    }
                    
                    const formData = new FormData();
                    formData.append('file', imageFile);
                    formData.append('user_id', userId);
                    
                    response = await fetch('/medical/api/images/save-to-path', {
                        method: 'POST',
                        body: formData
                    });
                } else {
                    // 处理注册已有图像
                    const imagePath = document.getElementById('image-path').value;
                    const imageFilename = document.getElementById('image-filename').value;
                    
                    if (!imagePath || !imageFilename) {
                        throw new Error('请填写图像路径和文件名');
                    }
                    
                    response = await fetch('/medical/api/images/register-medical', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            filename: imageFilename,
                            image_path: imagePath,
                            user_id: parseInt(userId)
                        })
                    });
                }
                
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();
                resultContent.innerHTML = `<span class="success">成功!</span>\n\n${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultContent.innerHTML = `<span class="error">错误:</span> ${error.message}`;
                console.error('处理失败:', error);
            }
        });
    </script>
</body>
</html> 