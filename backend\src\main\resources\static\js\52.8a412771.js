(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[52],{659:(e,t,a)=>{var n=a(51873),s=Object.prototype,r=s.hasOwnProperty,o=s.toString,i=n?n.toStringTag:void 0;function l(e){var t=r.call(e,i),a=e[i];try{e[i]=void 0;var n=!0}catch(l){}var s=o.call(e);return n&&(t?e[i]=a:delete e[i]),s}e.exports=l},9325:(e,t,a)=>{var n=a(34840),s="object"==typeof self&&self&&self.Object===Object&&self,r=n||s||Function("return this")();e.exports=r},10124:(e,t,a)=>{var n=a(9325),s=function(){return n.Date.now()};e.exports=s},23052:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>$});var n=a(61431),s={class:"cases-container"},r={class:"page-header"},o={key:0},i={key:1},l={key:2},c={class:"filter-row"},u={key:0,class:"text-center my-5"},g={key:2,class:"text-center my-5"},d={key:0},f={key:1},h={key:2};function p(e,t,a,p,m,v){var b=(0,n.g2)("el-option"),y=(0,n.g2)("el-select"),E=(0,n.g2)("el-form-item"),I=(0,n.g2)("el-button"),C=(0,n.g2)("el-form"),S=(0,n.g2)("el-table-column"),A=(0,n.g2)("status-badge"),k=(0,n.g2)("el-table"),D=(0,n.g2)("el-pagination"),T=(0,n.gN)("loading");return(0,n.uX)(),(0,n.CE)("div",s,[(0,n.Lk)("div",r,[(0,n.Lk)("h2",null,[e.isAdmin?((0,n.uX)(),(0,n.CE)("span",o,"病例标注管理")):e.isReviewer?((0,n.uX)(),(0,n.CE)("span",i,"待审核病例")):((0,n.uX)(),(0,n.CE)("span",l,"我的病例标注"))])]),(0,n.Lk)("div",c,[(0,n.bF)(C,{inline:!0,class:"filter-form"},{default:(0,n.k6)((function(){return[(0,n.bF)(E,{label:"状态"},{default:(0,n.k6)((function(){return[(0,n.bF)(y,{modelValue:m.filterStatus,"onUpdate:modelValue":t[0]||(t[0]=function(e){return m.filterStatus=e}),placeholder:"选择状态",clearable:"",onChange:v.handleFilterChange},{default:(0,n.k6)((function(){return[(0,n.bF)(b,{label:"未标注",value:"DRAFT"}),(0,n.bF)(b,{label:"已标注",value:"REVIEWED"}),(0,n.bF)(b,{label:"待审核",value:"SUBMITTED"}),(0,n.bF)(b,{label:"已通过",value:"APPROVED"}),(0,n.bF)(b,{label:"已驳回",value:"REJECTED"})]})),_:1},8,["modelValue","onChange"])]})),_:1}),(0,n.bF)(E,null,{default:(0,n.k6)((function(){return[(0,n.bF)(I,{type:"primary",onClick:v.handleFilterChange},{default:(0,n.k6)((function(){return t[1]||(t[1]=[(0,n.eW)("查询")])})),_:1,__:[1]},8,["onClick"]),(0,n.bF)(I,{onClick:v.resetFilter},{default:(0,n.k6)((function(){return t[2]||(t[2]=[(0,n.eW)("重置")])})),_:1,__:[2]},8,["onClick"])]})),_:1})]})),_:1})]),e.loading?((0,n.uX)(),(0,n.CE)("div",u,t[3]||(t[3]=[(0,n.Lk)("div",{class:"spinner-border",role:"status"},[(0,n.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):(0,n.bo)(((0,n.uX)(),(0,n.Wv)(k,{key:1,data:m.casesList,style:{width:"100%"}},{default:(0,n.k6)((function(){return[(0,n.bF)(S,{prop:"caseId",label:"病例编号",width:"180"}),(0,n.bF)(S,{prop:"department",label:"部位"}),(0,n.bF)(S,{prop:"type",label:"类型"}),(0,n.bF)(S,{prop:"status",label:"状态"},{default:(0,n.k6)((function(e){return[(0,n.bF)(A,{status:v.getStatusValue(e.row.status)},null,8,["status"])]})),_:1}),(0,n.bF)(S,{prop:"createTime",label:"创建时间"}),(0,n.bF)(S,{label:"操作",width:"220"},{default:(0,n.k6)((function(e){return[(0,n.bF)(I,{link:"",size:"small",onClick:function(t){return v.handleEdit(e.row)}},{default:(0,n.k6)((function(){return t[4]||(t[4]=[(0,n.eW)(" 编辑 ")])})),_:2,__:[4]},1032,["onClick"]),(0,n.bF)(I,{link:"",size:"small",onClick:function(t){return v.handleView(e.row)}},{default:(0,n.k6)((function(){return t[5]||(t[5]=[(0,n.eW)(" 查看 ")])})),_:2,__:[5]},1032,["onClick"]),v.canDeleteCase(e.row)?((0,n.uX)(),(0,n.Wv)(I,{key:0,link:"",type:"danger",size:"small",onClick:function(t){return v.handleDelete(e.row)}},{default:(0,n.k6)((function(){return t[6]||(t[6]=[(0,n.eW)(" 删除 ")])})),_:2,__:[6]},1032,["onClick"])):(0,n.Q3)("",!0)]})),_:1})]})),_:1},8,["data"])),[[T,m.tableLoading]]),e.loading||0!==m.casesList.length?(0,n.Q3)("",!0):((0,n.uX)(),(0,n.CE)("div",g,[m.allImages&&m.allImages.length>0&&m.filterStatus?((0,n.uX)(),(0,n.CE)("p",d," 没有符合条件的病例，当前筛选状态: "+(0,n.v_)(v.getStatusText(m.filterStatus)),1)):m.allImages&&m.allImages.length>0?((0,n.uX)(),(0,n.CE)("p",f," 暂无病例匹配当前筛选条件 ")):((0,n.uX)(),(0,n.CE)("p",h," 暂无病例标注数据 "))])),m.totalItems>0?((0,n.uX)(),(0,n.Wv)(D,{key:3,background:"",layout:"total, sizes, prev, pager, next, jumper",total:m.totalItems,"page-size":m.pageSize,"page-sizes":[10,20,50,100],"current-page":m.currentPage,onCurrentChange:v.handlePageChange,onSizeChange:v.handleSizeChange},null,8,["total","page-size","current-page","onCurrentChange","onSizeChange"])):(0,n.Q3)("",!0)])}var m=a(24059),v=a(59158),b=a(698),y=a(88844),E=(a(28706),a(2008),a(51629),a(74423),a(64346),a(62062),a(44114),a(34782),a(26910),a(60739),a(23288),a(18111),a(22489),a(7588),a(61701),a(33110),a(79432),a(26099),a(58940),a(21699),a(47764),a(42762),a(23500),a(62953),a(66278)),I=(a(44633),a(38221)),C=a.n(I),S=a(36149),A=a(72505),k=a.n(A),D={class:"status-badge"};function T(e,t,a,s,r,o){var i=(0,n.g2)("el-tag");return(0,n.uX)(),(0,n.CE)("div",D,[(0,n.bF)(i,{type:o.tagType,effect:o.effect,size:"medium"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(o.displayText),1)]})),_:1},8,["type","effect"])])}const _={name:"StatusBadge",props:{status:{type:String,required:!0,validator:function(e){return["DRAFT","REVIEWED","SUBMITTED","APPROVED","REJECTED"].includes(e)}},plain:{type:Boolean,default:!1}},computed:{tagType:function(){var e={DRAFT:"info",REVIEWED:"warning",SUBMITTED:"primary",APPROVED:"success",REJECTED:"danger"};return e[this.status]||"info"},displayText:function(){var e={DRAFT:"未标注",REVIEWED:"已标注",SUBMITTED:"待审核",APPROVED:"已通过",REJECTED:"已驳回"};return console.log("StatusBadge 显示状态:",this.status,"映射为:",e[this.status]||this.status),e[this.status]||this.status},effect:function(){return this.plain?"plain":"light"}}};var R=a(66262);const w=(0,R.A)(_,[["render",T],["__scopeId","data-v-3bce608d"]]),F=w,P={name:"CasesList",components:{StatusBadge:F},data:function(){return{casesList:[],pageSize:10,currentPage:1,totalItems:0,filterStatus:"",tableLoading:!1,myImages:[],needReviewImages:[],allImages:[]}},computed:(0,y.A)((0,y.A)({},(0,E.L8)({images:"getAllImages",loading:"isImagesLoading",error:"getImagesError",isAdmin:"isAdmin",isDoctor:"isDoctor",isReviewer:"isReviewer",hasPermission:"hasPermission",currentUserId:"getUserId",canAccessResource:"canAccessResource"})),{},{filteredImages:function(){var e=this.filterStatus||this.$route.query.status;console.log("过滤状态:",e);var t=this.allImages;return e?(console.log("根据状态 ".concat(e," 过滤 ").concat(t.length," 条记录")),t.filter((function(t){var a=t.status||"DRAFT";return console.log("图像 ".concat(t.id," 状态: ").concat(a,", 目标状态: ").concat(e,", 匹配: ").concat(a===e)),a===e}))):t}}),methods:(0,y.A)((0,y.A)({},(0,E.i0)(["fetchImages"])),{},{getStatusType:function(e){var t={未标注:"info",已标注:"success",待审核:"warning",已通过:"success",已驳回:"danger"};return t[e]||"info"},getStatusValue:function(e){var t={未标注:"DRAFT",已标注:"REVIEWED",待审核:"SUBMITTED",已通过:"APPROVED",已驳回:"REJECTED"};return t[e]||"DRAFT"},getStatusText:function(e){var t={DRAFT:"未标注",REVIEWED:"已标注",SUBMITTED:"待审核",APPROVED:"已通过",REJECTED:"已驳回"};return console.log("转换状态显示:",e,"→",t[e]||e),t[e]||e},handleEdit:function(e){if(!this.$store.getters.hasPermission("edit_case"))return console.log("用户没有编辑权限，阻止编辑操作"),void this.$message.error("您没有编辑病例的权限");var t=["DRAFT","REVIEWED","REJECTED"],a=this.getStatusValue(e.status);if(console.log("===== 编辑按钮点击 ====="),console.log("病例ID:",e.id),console.log("病例状态:",e.status,"转换为API状态:",a),console.log("可编辑状态列表:",t),console.log("状态检查结果:",t.includes(a)),console.log("当前用户角色:",this.$store.getters.getUserRole),console.log("是否有编辑权限:",this.$store.getters.hasPermission("edit_case")),console.log("========================"),t.includes(a)){console.log("开始编辑病例:",e.id,"状态:",a);var n="/app/case/".concat(e.id,"/annotate-and-form");console.log("使用完整路径导航:",n),sessionStorage.setItem("isAppOperation","true"),this.$router.push(n),console.log("路由导航已触发，目标路径:",n)}else this.$message.error("此病例已提交或已完成审核，无法编辑。")},handleView:function(e){this.canViewCase(e)?this.$router.push("/app/cases/view/".concat(e.id)):this.$message.error("您没有权限查看此病例")},handleDelete:function(e){var t=this;this.canDeleteCase(e)?this.$confirm("此操作将永久删除该病例及其所有相关数据，是否继续?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n=t.$loading({lock:!0,text:"删除中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Promise.resolve().then(a.bind(a,36149)).then((function(a){a["default"].images["delete"](e.id).then((function(){console.log("图像及相关数据已删除"),n.close(),t.$message.success("删除成功"),t.fetchCases()}))["catch"]((function(e){n.close();var a="删除失败";e.response&&e.response.data?a="string"===typeof e.response.data?e.response.data:JSON.stringify(e.response.data):a+=": "+(e.message||"未知错误"),console.error("删除失败:",a),t.$message({type:"error",message:a,duration:5e3})}))}))}))["catch"]((function(){t.$message.info("已取消删除")})):this.$message.error("您没有权限删除此病例")},handlePageChange:function(e){this.currentPage=e,this.processCases()},handleSizeChange:function(e){this.pageSize=e,this.currentPage=1,this.processCases()},handleFilterChange:C()((function(){this.currentPage=1,this.processCases(),this.$router.push({query:(0,y.A)((0,y.A)({},this.$route.query),{},{status:this.filterStatus||void 0,page:1!==this.currentPage?this.currentPage:void 0})})["catch"]((function(){}))}),300),resetFilter:function(){this.filterStatus="",this.handleFilterChange()},canEditCase:function(e){return!0},canViewCase:function(e){return!0},canDeleteCase:function(e){if(this.isAdmin)return!0;var t=this.getStatusValue(e.status);return"APPROVED"!==t},fetchCases:function(){var e=this;return(0,b.A)((0,m.A)().m((function t(){var a,n,s,r,o,i,l,c,u,g,d;return(0,m.A)().w((function(t){while(1)switch(t.n){case 0:t.p=0,console.log("获取病例列表，检查查询参数:",e.$route.query),e.tableLoading=!0,a=parseInt(e.$route.query.page)||1,n=e.$route.query.status,e.currentPage=a,n&&(e.filterStatus=n,console.log("从URL参数设置过滤状态:",e.filterStatus)),s=null;try{r=localStorage.getItem("user"),r&&(o=JSON.parse(r),s=o.customId||o.id,console.log("从localStorage获取用户ID:",s))}catch(f){console.error("解析用户信息失败:",f)}if(s&&(k().defaults.headers.common["X-User-Id"]=s),l=e.$route.path.includes("/team")||"team"===e.$route.query.view,!l){t.n=2;break}return console.log("获取团队图像"),t.n=1,S["default"].images.getTeamImages();case 1:i=t.v,t.n=4;break;case 2:return console.log("获取个人图像"),t.n=3,S["default"].images.getUserImages(e.filterStatus);case 3:i=t.v;case 4:if(i&&i.data&&Array.isArray(i.data)){t.n=10;break}if(console.error("图像数据格式不正确:",i),l){t.n=9;break}return console.log("个人图像获取失败，尝试获取团队图像"),t.p=5,t.n=6,S["default"].images.getTeamImages();case 6:if(c=t.v,!(c&&c.data&&Array.isArray(c.data))){t.n=7;break}return e.allImages=(0,v.A)(c.data),console.log("成功获取团队图像数量:",e.allImages.length),e.processCases(),e.tableLoading=!1,t.a(2);case 7:t.n=9;break;case 8:t.p=8,g=t.v,console.error("获取团队图像失败:",g);case 9:return e.allImages=[],e.myImages=[],e.needReviewImages=[],e.tableLoading=!1,t.a(2);case 10:if(e.allImages=(0,v.A)(i.data),console.log("所有图像数量:",e.allImages.length),0!==e.allImages.length||!e.filterStatus){t.n=12;break}return console.log("当前状态下没有图像，尝试获取所有图像"),t.n=11,S["default"].images.getUserImages();case 11:u=t.v,u&&u.data&&Array.isArray(u.data)&&(e.allImages=(0,v.A)(u.data),console.log("获取到所有图像数量:",e.allImages.length));case 12:e.processCases(),t.n=14;break;case 13:t.p=13,d=t.v,console.error("获取病例列表失败:",d),e.$message.error("获取病例列表失败: "+d.message);case 14:return t.p=14,e.tableLoading=!1,t.f(14);case 15:return t.a(2)}}),t,null,[[5,8],[0,13,14,15]])})))()},processCases:function(){var e=this,t=this.filteredImages;if(console.log("处理 ".concat(t.length," 条病例记录")),console.log("接口返回数据",t),0===t.length)return this.totalItems=0,void(this.casesList=[]);var a=(0,v.A)(t).sort((function(e,t){var a=e.createdAt||e.created_at,n=t.createdAt||t.created_at;return a?n?new Date(n)-new Date(a):-1:1}));this.totalItems=a.length;var n=(this.currentPage-1)*this.pageSize,s=n+this.pageSize;console.log("详细检查创建时间字段:"),a.slice(n,s).forEach((function(t,a){console.log("记录 ".concat(a+1,":"),{id:t.id,createdAt:t.createdAt,created_at:t.created_at,formattedDate:e.formatDate(t.createdAt||t.created_at),rawObject:t})}));a.slice(n,s).map((function(e){return e.id}));this.casesList=a.slice(n,s).map((function(t){var a=e.formatDate(t.createdAt||t.created_at),n=t.diagnosisCategory||t.diagnosis_category||"未知";return{id:t.id,caseId:t.caseNumber||"CASE-".concat(t.id),department:t.lesionLocation||"未知",type:n,status:e.getStatusText(t.status||"DRAFT"),createTime:a,rawDate:t.createdAt||t.created_at,creatorId:t.uploadedBy,hasAnnotation:!(!t.image_two_path||""===t.image_two_path.trim())}}))},formatDate:function(e){if(console.log("格式化日期:",e),!e)return console.log('日期为空，返回"未知"'),"未知";try{var t=new Date(e);return console.log("转换后的日期对象:",t),isNaN(t.getTime())?(console.log('无效日期，返回"未知"'),"未知"):t.toLocaleString("zh-CN")}catch(a){return console.error("日期格式化错误:",a),"未知"}},getFilteredCases:function(){var e=this,t=this.filteredImages;if(!t||0===t.length)return this.casesList=[],void(this.totalItems=0);var a=(this.currentPage-1)*this.pageSize,n=a+this.pageSize;this.casesList=t.slice(a,n).map((function(t){return{id:t.id,caseId:t.case_number||t.id,department:t.lesion_location||"未知",type:t.diagnosis_category||"未指定",status:e.getStatusText(t.status||"DRAFT"),uploadedBy:t.uploaded_by,createTime:e.formatDate(t.created_at),originalRecord:t}})),this.totalItems=t.length}}),watch:{"$route.query":{handler:function(e,t){if(!t)return console.log("初始化路由监听，设置初始参数"),e.status&&(this.filterStatus=e.status,console.log("初始过滤状态:",this.filterStatus)),void(e.page&&(this.currentPage=parseInt(e.page)||1,console.log("初始页码:",this.currentPage)));e.status===t.status&&e.page===t.page||(console.log("路由参数变化，旧状态:",t.status,"新状态:",e.status),e.status!==t.status&&(this.filterStatus=e.status||"",console.log("更新过滤状态为:",this.filterStatus)),e.page!==t.page&&(this.currentPage=parseInt(e.page)||1,console.log("更新当前页码为:",this.currentPage)),this.fetchCases())},deep:!0,immediate:!0}},created:function(){this.fetchCases()}},x=(0,R.A)(P,[["render",p],["__scopeId","data-v-1f0e4e82"]]),$=x},23805:e=>{function t(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}e.exports=t},31800:e=>{var t=/\s/;function a(e){var a=e.length;while(a--&&t.test(e.charAt(a)));return a}e.exports=a},34840:(e,t,a)=>{var n="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g;e.exports=n},38221:(e,t,a)=>{var n=a(23805),s=a(10124),r=a(99374),o="Expected a function",i=Math.max,l=Math.min;function c(e,t,a){var c,u,g,d,f,h,p=0,m=!1,v=!1,b=!0;if("function"!=typeof e)throw new TypeError(o);function y(t){var a=c,n=u;return c=u=void 0,p=t,d=e.apply(n,a),d}function E(e){return p=e,f=setTimeout(S,t),m?y(e):d}function I(e){var a=e-h,n=e-p,s=t-a;return v?l(s,g-n):s}function C(e){var a=e-h,n=e-p;return void 0===h||a>=t||a<0||v&&n>=g}function S(){var e=s();if(C(e))return A(e);f=setTimeout(S,I(e))}function A(e){return f=void 0,b&&c?y(e):(c=u=void 0,d)}function k(){void 0!==f&&clearTimeout(f),p=0,c=h=u=f=void 0}function D(){return void 0===f?d:A(s())}function T(){var e=s(),a=C(e);if(c=arguments,u=this,h=e,a){if(void 0===f)return E(h);if(v)return clearTimeout(f),f=setTimeout(S,t),y(h)}return void 0===f&&(f=setTimeout(S,t)),d}return t=r(t)||0,n(a)&&(m=!!a.leading,v="maxWait"in a,g=v?i(r(a.maxWait)||0,t):g,b="trailing"in a?!!a.trailing:b),T.cancel=k,T.flush=D,T}e.exports=c},40346:e=>{function t(e){return null!=e&&"object"==typeof e}e.exports=t},44394:(e,t,a)=>{var n=a(72552),s=a(40346),r="[object Symbol]";function o(e){return"symbol"==typeof e||s(e)&&n(e)==r}e.exports=o},51873:(e,t,a)=>{var n=a(9325),s=n.Symbol;e.exports=s},54128:(e,t,a)=>{var n=a(31800),s=/^\s+/;function r(e){return e?e.slice(0,n(e)+1).replace(s,""):e}e.exports=r},59350:e=>{var t=Object.prototype,a=t.toString;function n(e){return a.call(e)}e.exports=n},72552:(e,t,a)=>{var n=a(51873),s=a(659),r=a(59350),o="[object Null]",i="[object Undefined]",l=n?n.toStringTag:void 0;function c(e){return null==e?void 0===e?i:o:l&&l in Object(e)?s(e):r(e)}e.exports=c},99374:(e,t,a)=>{var n=a(54128),s=a(23805),r=a(44394),o=NaN,i=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,c=/^0o[0-7]+$/i,u=parseInt;function g(e){if("number"==typeof e)return e;if(r(e))return o;if(s(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=s(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var a=l.test(e);return a||c.test(e)?u(e.slice(2),a?2:8):i.test(e)?o:+e}e.exports=g}}]);
//# sourceMappingURL=52.8a412771.js.map