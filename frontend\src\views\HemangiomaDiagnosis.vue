<template>
  <div class="container">
    <h2 class="blood-vessel-title">血管瘤智能诊断</h2>
    
    <div class="diagnosis-panel">
      <!-- 上传图像提示 -->
      <div class="panel-heading">上传图像进行血管瘤检测</div>
      
      <!-- 表单区域 -->
      <div class="form-content" v-if="!diagnosisResults.processed">
        <!-- 所有基本信息字段放在一个自适应容器中 -->
        <div class="adaptive-fields-container">
          <div class="field-item">
            <div class="form-label">患者年龄</div>
            <div class="form-field">
              <el-input 
                v-model="formData.patientAge" 
                type="number" 
                placeholder="请输入年龄"
              />
            </div>
          </div>
          
          <div class="field-item">
            <div class="form-label">性别</div>
            <div class="form-field">
              <el-radio-group v-model="formData.patientGender">
                <el-radio label="男">男</el-radio>
                <el-radio label="女">女</el-radio>
              </el-radio-group>
            </div>
          </div>
          
          <div class="field-item">
            <div class="form-label">类型</div>
            <div class="form-field">
              <el-radio-group v-model="formData.originType">
                <el-radio label="先天性">先天性</el-radio>
                <el-radio label="后天性">后天性</el-radio>
              </el-radio-group>
            </div>
          </div>

          <div class="field-item">
            <div class="form-label">血管质地</div>
            <div class="form-field">
              <el-select v-model="formData.vesselTexture" placeholder="请选择血管质地">
                <el-option label="质软" value="soft" />
                <el-option label="质韧" value="elastic" />
                <el-option label="质硬" value="hard" />
                <el-option label="囊性" value="cystic" />
                <el-option label="可压缩" value="compressible" />
              </el-select>
            </div>
          </div>
        </div>

        <!-- 上传图像 -->
        <div class="form-group">
          <div class="form-label">上传血管瘤图像</div>
          <div class="form-field">
            <div class="upload-area">
              <el-upload
                drag
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :limit="1"
                :show-file-list="false"
              >
                <div v-if="!imagePreview" class="upload-placeholder">
                  <el-icon class="upload-icon"><i-ep-upload-filled /></el-icon>
                  <div>拖拽文件到此处或 <em>点击上传</em></div>
                  <div class="upload-tip">支持JPG、PNG、BMP等常见图像格式，最大文件大小10MB</div>
                </div>
                <div v-else class="image-preview-container">
                  <img :src="imagePreview" class="preview-image" alt="图像预览">
                  <div class="preview-overlay">
                    <el-button type="primary" size="small" @click.stop="resetImage">重新上传</el-button>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="form-buttons">
          <el-button 
            type="primary" 
            :disabled="isProcessing || !selectedFile" 
            @click="processDiagnosis"
          >
            <el-icon v-if="isProcessing"><i-ep-loading /></el-icon>
            <span>{{ isProcessing ? '处理中...' : '开始诊断' }}</span>
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </div>
      </div>
      
      <!-- 诊断结果区域，只在处理完成后显示 -->
      <div v-if="diagnosisResults.processed" class="diagnosis-results">
        <el-card>
          <template #header>
            <div class="result-header">
              <span class="result-title">血管瘤诊断结果</span>
              <span v-if="diagnosisResults.detected" style="color: #F56C6C; padding: 6px 12px;">
                检测到疑似血管瘤
              </span>
              <span v-else style="color: #67C23A; padding: 6px 12px;">未检测到血管瘤</span>
            </div>
          </template>

          <!-- 结果图像 -->
          <div class="result-image-container">
            <img :src="diagnosisResults.resultImage" class="result-image" alt="诊断结果图像" />
          </div>

          <!-- 基本检测信息 -->
          <div class="detection-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="检测状态">
                <span v-if="diagnosisResults.detected" style="color: #F56C6C;">已检测到</span>
                <span v-else style="color: #67C23A;">未检测到</span>
              </el-descriptions-item>
              <el-descriptions-item label="置信度">
                {{ Math.round(diagnosisResults.confidence * 100) }}%
              </el-descriptions-item>
              <el-descriptions-item v-if="diagnosisResults.detected" label="检测到的类型">
                {{ diagnosisResults.detectedType }}
              </el-descriptions-item>
              <el-descriptions-item v-if="diagnosisResults.predictedColor" label="病灶颜色">
                <span>{{ diagnosisResults.predictedColor }}</span>
              </el-descriptions-item>
              <el-descriptions-item v-if="diagnosisResults.predictedPart" label="病灶部位">
                <span>{{ diagnosisResults.predictedPart }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 大模型生成的诊断建议 -->
          <div class="diagnosis-recommendations">
            <el-divider>
              <div class="recommendation-title">
                <span>AI诊断建议</span>
                <span v-if="diagnosisResults.isLoadingRecommendation" style="font-size: 14px; color: #909399;">
                  <i class="el-icon-loading"></i> 正在生成详细建议...
                </span>
              </div>
            </el-divider>

            <!-- 治疗建议 -->
            <div class="recommendation-section" v-if="diagnosisResults.treatmentSuggestion">
              <h3>治疗与注意事项</h3>
              <p>{{ diagnosisResults.treatmentSuggestion }}</p>
            </div>
            <div class="recommendation-placeholder" v-else>
              <el-skeleton v-if="diagnosisResults.isLoadingRecommendation" :rows="1" animated />
              <p v-else>暂无治疗建议</p>
            </div>

            <div class="recommendation-section" v-if="diagnosisResults.precautions">
              <h3>注意事项</h3>
              <p>{{ diagnosisResults.precautions }}</p>
            </div>
            <div class="recommendation-placeholder" v-else>
              <el-skeleton v-if="diagnosisResults.isLoadingRecommendation" :rows="1" animated />
              <p v-else>暂无注意事项</p>
            </div>

            <!-- 免责声明 -->
            <div class="disclaimer">
              <el-alert
                title="AI生成免责声明"
                type="info"
                description="本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
                show-icon
                :closable="false"
              />
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="result-actions">
            <el-button type="primary" @click="modifyReport">修改</el-button>
            <el-button type="success" @click="saveReport">保存</el-button>
            <el-button type="warning" @click="confirmReport">确认</el-button>
            <el-button @click="resetDiagnosis">返回</el-button>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { UploadFilled, Loading } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
import { reactive, ref } from 'vue';

export default {
  name: 'HemangiomaDiagnosis',
  components: {
    'i-ep-upload-filled': UploadFilled,
    'i-ep-loading': Loading
  },
  data() {
    return {
      formData: {
        patientAge: null,
        patientGender: '男',
        originType: '先天性',
        vesselTexture: '',
      },
      colorOptions: [
        { value: '褐色', label: '褐色' },
        { value: '黑色', label: '黑色' },
        { value: '红色', label: '红色' },
        { value: '白色', label: '白色' },
        { value: '正常', label: '正常' },
        { value: '玫红', label: '玫红' }
      ],
      partOptions: [
        { value: '唇部', label: '唇部' },
        { value: '脑部', label: '脑部' },
        { value: '颈部', label: '颈部' },
        { value: '脸部', label: '脸部' },
        { value: '掌部', label: '掌部' },
        { value: '手臂', label: '手臂' },
        { value: '胸部', label: '胸部' },
        { value: '腹部', label: '腹部' },
        { value: '腿部', label: '腿部' },
        { value: '阴部', label: '阴部' },
        { value: '背部', label: '背部' },
        { value: '耳部', label: '耳部' },
        { value: '枕部', label: '枕部' },
        { value: '眼部', label: '眼部' },
        { value: '脚底', label: '脚底' },
        { value: '脚背', label: '脚背' },
        { value: '肩部', label: '肩部' },
        { value: '舌部', label: '舌部' },
        { value: '屁股', label: '屁股' },
        { value: '口腔', label: '口腔' },
        { value: '鼻部', label: '鼻部' }
      ],
      selectedFile: null,
      imagePreview: null,
      isProcessing: false,
      diagnosisResults: reactive({
        processed: false,
        detected: false,
        detectedType: '',
        confidence: 0,
        resultImage: '',
        isLoadingRecommendation: false,
        treatmentSuggestion: '',
        precautions: '', // 新字段：注意事项
        emergencyInstructions: '', // 保留兼容旧版API
        disclaimer: '',
        predictedColor: '', // 新增：预测颜色
        predictedPart: '',  // 新增：预测部位
      }),
      isLoadingRecommendation: false,
      activeCollapseItems: ['1', '2'],
      pollingInterval: null,
      diagnosisError: '',
      isEditing: false // 新增：控制是否处于编辑模式
    };
  },
  computed: {
    confidenceColor() {
      const confidence = this.diagnosisResults.confidence;
      if (confidence > 0.8) return '#F56C6C';
      if (confidence > 0.5) return '#E6A23C';
      return '#67C23A';
    }
  },
  created() {
    console.log('[血管瘤诊断] 组件created钩子触发');
  },
  mounted() {
    console.log('[血管瘤诊断] 组件mounted钩子触发，DOM已挂载');
    document.title = '血管瘤智能诊断';
    
    // 通知父组件此页面已加载
    this.$nextTick(() => {
      console.log('[血管瘤诊断] 组件完全渲染完成');
      if (window.parent) {
        try {
          window.parent.postMessage({
            type: 'PAGE_LOADED',
            page: 'hemangioma-diagnosis'
          }, '*');
        } catch (e) {
          console.error('[血管瘤诊断] 发送页面加载消息失败:', e);
        }
      }
    });
  },
  methods: {
    handleFileChange(file) {
      if (!file) {
        this.selectedFile = null;
        this.imagePreview = null;
        return;
      }
      
      // 验证文件大小（10MB最大）
      if (file.raw.size > 10 * 1024 * 1024) {
        ElMessage.error('文件大小超过限制(10MB)');
        this.selectedFile = null;
        this.imagePreview = null;
        return;
      }
      
      this.selectedFile = file.raw;
      
      // 创建图片预览
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imagePreview = e.target.result;
      };
      reader.readAsDataURL(file.raw);
    },
    processDiagnosis() {
      // 设置加载状态
      this.isProcessing = true;
      this.diagnosisError = '';
      this.diagnosisResults = {
        processed: false,
        detected: false,
        confidence: 0,
        detectedType: '',
        treatmentSuggestion: '',
        precautions: '', // 新字段：注意事项
        emergencyInstructions: '', // 保留兼容旧版API
        resultImage: '',
        id: null,
        isLoadingRecommendation: false, // 追踪是否正在加载治疗建议
        predictedColor: '', // 新增：预测颜色
        predictedPart: '',  // 新增：预测部位
      };

      // 1. 验证是否已上传图像和选择血管质地
      if (!this.selectedFile) {
        this.diagnosisError = '请上传血管瘤图像';
        this.isProcessing = false;
        return;
      }

      // 获取当前登录用户ID
      let currentUserId;
      try {
        // 尝试从Vuex store获取
        if (this.$store && this.$store.getters.getUserId) {
          currentUserId = this.$store.getters.getUserId;
        } 
        // 或尝试从localStorage获取完整用户信息
        else {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const userObj = JSON.parse(userStr);
            // 确保使用数字ID，而不是自定义ID
            currentUserId = userObj.id;
            
            // 如果没有数字ID但有自定义ID，则记录日志但不使用自定义ID
            if (!currentUserId && userObj.customId) {
              console.log('警告: 用户没有数字ID，仅有自定义ID:', userObj.customId);
            }
          }
        }
      } catch (error) {
        console.error('获取用户ID失败:', error);
      }

      // 如果没有获取到用户ID，则提示用户登录
      if (!currentUserId) {
        this.isProcessing = false;
        ElMessage.error('未检测到登录用户，请先登录系统');
        return;
      }

      // 2. 上传图像并保存诊断记录
      const formData = new FormData();
      formData.append('file', this.selectedFile);
      
      // 添加表单数据
      if (this.formData.patientAge) formData.append('patient_age', this.formData.patientAge.toString());
      if (this.formData.patientGender) formData.append('gender', this.formData.patientGender);
      if (this.formData.originType) formData.append('origin_type', this.formData.originType);
      if (this.formData.vesselTexture) formData.append('vessel_texture', this.formData.vesselTexture);
      
      // 确保用户ID作为数字添加到表单中
      formData.append('user_id', currentUserId.toString());

      console.log('发送诊断请求，用户ID:', currentUserId);

      // 发送请求
      axios.post('/medical/api/hemangioma-diagnoses/upload-and-diagnose', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-User-ID': currentUserId.toString()
        },
        timeout: 60000 // 增加超时时间到60秒
      })
      .then(response => {
        console.log('诊断数据上传成功:', response.data);
        
        // 3. 处理响应，显示诊断结果
        this.diagnosisResults.processed = true;
        this.diagnosisResults.id = response.data.id;
        
        // 检查是否检测到血管瘤（通过detectedType字段判断）
        const detectedType = response.data.detectedType;
        this.diagnosisResults.detected = detectedType && detectedType.length > 0;
        
        // 设置置信度
        this.diagnosisResults.confidence = response.data.confidence || 0;
        
        // 设置检测到的类型
        this.diagnosisResults.detectedType = detectedType || '';
        
        // 设置结果图像 - 优先使用处理后的图像路径
        if (response.data.processedImagePath) {
          const baseUrl = window.location.origin; // 使用当前域名作为基础URL
          this.diagnosisResults.resultImage = baseUrl + response.data.processedImagePath;
        }

        // 新增：填充颜色和部位信息
        this.diagnosisResults.predictedColor = response.data.color || '';
        this.diagnosisResults.predictedPart = response.data.bodyPart || '';

        // 检查是否已有治疗建议（通常初次返回时没有，需要轮询获取）
        if (!response.data.treatmentSuggestion) {
          // 如果没有治疗建议，标记为正在加载并开始轮询
          this.diagnosisResults.isLoadingRecommendation = true;
          this.startPolling(response.data.id);
        } else {
          // 如果已经有治疗建议，直接显示
          this.diagnosisResults.treatmentSuggestion = response.data.treatmentSuggestion;
          // 优先使用precautions字段，如果不存在则回退到emergencyInstructions
          this.diagnosisResults.precautions = response.data.precautions || response.data.emergencyInstructions;
          // 兼容旧版本
          this.diagnosisResults.emergencyInstructions = response.data.emergencyInstructions;
        }
        
        // 重置处理状态
        this.isProcessing = false;
      })
      .catch(error => {
        console.error('诊断处理失败:', error);
        this.diagnosisError = `诊断处理失败，请重试: ${error.message}`;
        this.isProcessing = false;
      });
    },

    startPolling(diagnosisId) {
        console.log('开始轮询获取诊断建议，ID:', diagnosisId);
        this.pollingInterval = setInterval(() => {
            this.pollForResults(diagnosisId);
        }, 3000); // 每3秒轮询一次

        // 设置一个超时，比如3分钟后停止轮询，防止无限循环
        setTimeout(() => {
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
                this.pollingInterval = null;
                if(this.diagnosisResults.isLoadingRecommendation) {
                    this.diagnosisResults.isLoadingRecommendation = false;
                    ElMessage.info('已获取部分诊断建议，更多详细内容稍后可能会继续更新。');
                }
            }
        }, 180000); // 3分钟后停止轮询
    },

    pollForResults(diagnosisId) {
      axios.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`)
        .then(response => {
          const data = response.data;
          console.log('轮询获取诊断结果:', data);
          
          // 检查是否有新的治疗建议内容
          let hasUpdates = false;
          
          // 检查并更新治疗建议
          if (data.treatmentSuggestion && !this.diagnosisResults.treatmentSuggestion) {
            this.diagnosisResults.treatmentSuggestion = data.treatmentSuggestion;
            hasUpdates = true;
          }
          
          // 检查并更新注意事项
          if (data.precautions && !this.diagnosisResults.precautions) {
            this.diagnosisResults.precautions = data.precautions;
            hasUpdates = true;
          }
          
          // 如果已经获取到所有建议，停止轮询
          if (
            data.treatmentSuggestion && 
            data.precautions
          ) {
            if (this.pollingInterval) {
              clearInterval(this.pollingInterval);
              this.pollingInterval = null;
              this.diagnosisResults.isLoadingRecommendation = false;
            }
          }
        })
        .catch(error => {
          console.error('轮询获取诊断结果失败:', error);
          // 轮询错误不需要显示给用户，继续尝试
        });
    },
    
    backToUpload() {
      this.diagnosisResults.processed = false;
    },
    
    resetForm() {
      this.formData.patientAge = '';
      this.formData.patientGender = '';
      this.formData.originType = '';
      this.formData.vesselTexture = '';
      
      this.selectedFile = null;
      this.imagePreview = null;
    },
    modifyReport() {
      const diagnosisId = this.diagnosisResults.id;
      if (!diagnosisId) {
        ElMessage.error('无法修改报告：缺少诊断ID');
        return;
      }
      
      // 使用 Vue Router 进行页面跳转
      this.$router.push({
        path: `/app/case/${diagnosisId}/annotate-and-form`,
        query: {
          mode: 'edit', // 明确告知目标页面是编辑模式
          from: 'diagnosis-page' // 可选：标记来源页面
        }
      });
    },

    async saveReport() {
      if (!this.diagnosisResults.id) {
        ElMessage.error('诊断ID丢失，无法保存报告');
        return;
      }
      
      const updatedData = {
        patientAge: this.formData.patientAge,
        gender: this.formData.patientGender,
        originType: this.formData.originType,
        vesselTexture: this.formData.vesselTexture,
        color: this.diagnosisResults.predictedColor ? this.diagnosisResults.predictedColor.name : '',
        bodyPart: this.diagnosisResults.predictedPart ? this.diagnosisResults.predictedPart.name : '',
        reviewNotes: this.diagnosisResults.reviewNotes,
        treatmentSuggestion: this.diagnosisResults.treatmentSuggestion,
        precautions: this.diagnosisResults.precautions || this.diagnosisResults.emergencyInstructions,
        status: 'REVIEWED' // 明确设置状态为"已标注"
      };

      try {
        const response = await axios.put(`/medical/api/hemangioma-diagnoses/${this.diagnosisResults.id}`, updatedData);
        // 移除成功提示消息
        this.isEditing = false;
        
        // 跳转到主页面或诊断列表页面
        setTimeout(() => {
          // 使用Vue Router跳转到病例列表页面
          this.$router.push('/app/dashboard');
        }, 1500); // 延迟1.5秒后跳转，让用户看到成功消息
      } catch (error) {
        console.error('保存报告失败:', error);
        ElMessage.error('保存报告失败，请稍后重试');
      }
    },
    // 获取当前用户角色
    getCurrentUserRole() {
      try {
        // 尝试从Vuex store获取
        if (this.$store && this.$store.getters.getUserRole) {
          return this.$store.getters.getUserRole;
        } 
        // 或尝试从localStorage获取完整用户信息
        else {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const userObj = JSON.parse(userStr);
            return userObj.role || userObj.userRole;
          }
        }
      } catch (error) {
        console.error('获取用户角色失败:', error);
        return null;
      }
      return null;
    },
    confirmReport() {
      // 确认报告并重置诊断页面到初始状态
      const diagnosisId = this.diagnosisResults.id;
      if (!diagnosisId) {
        ElMessage.error('无法确认报告：缺少诊断ID');
        return;
      }

      // 获取当前用户角色
      const userRole = this.getCurrentUserRole();
      console.log('当前用户角色:', userRole);
      
      // 根据用户角色设置不同状态
      let status = 'SUBMITTED'; // 默认为待审核状态
      let statusMessage = '待审核';
      
      // 如果是管理员或审核医生，则设置为已完成状态
      if (userRole && (userRole.toLowerCase().includes('admin') || userRole.toLowerCase().includes('reviewer'))) {
        status = 'APPROVED'; // 已完成状态
        statusMessage = '已完成';
      }
      
      // 更新诊断状态
      axios.put(`/medical/api/hemangioma-diagnoses/${diagnosisId}/status`, null, {
        params: {
          status: status
        }
      })
      .then(response => {
        console.log('更新状态成功:', response.data);
        // 移除成功提示消息
        
        // 重置页面到初始状态
        this.resetDiagnosis();
        this.resetForm();
      })
      .catch(error => {
        console.error('更新状态失败:', error);
        ElMessage.error(`更新状态失败: ${error.message}`);
        
        // 即使失败也重置页面
        this.resetDiagnosis();
        this.resetForm();
      });
    },
    resetImage(e) {
      if (e) {
        e.stopPropagation(); // 防止触发上传事件
      }
      this.selectedFile = null;
      this.imagePreview = null;
    },
    // 获取血管质地标签
    getVesselTextureLabel(value) {
      const textureMap = {
        'soft': '质软',
        'elastic': '质韧',
        'hard': '质硬',
        'cystic': '囊性',
        'compressible': '可压缩'
      };
      return textureMap[value] || value;
    },
    resetDiagnosis() {
      // 恢复所有状态到初始值
      this.resetForm();
      this.isEditing = false;

      // 重置诊断结果
      Object.assign(this.diagnosisResults, {
        processed: false,
        detected: false,
        isLoadingRecommendation: false,
        id: null,
        resultImage: '',
        detectedType: '',
        confidence: 0,
        predictedColor: '',
        predictedPart: '',
        vesselTexture: '',
        treatmentSuggestion: '',
        precautions: '',
        reviewNotes: ''
      });
    }
  }
};
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 15px;
}

.blood-vessel-title {
  font-size: 22px;
  margin-bottom: 15px;
}

.diagnosis-panel {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.panel-heading {
  padding: 12px 15px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  font-size: 16px;
}

.form-content {
  padding: 15px;
  max-width: 800px;
  margin: 0 auto;
}

/* 移除旧的布局类 */
.form-row, .horizontal-groups {
  /* 保留以兼容其他地方使用 */
}

/* 自适应字段容器 - 灵活布局 */
.adaptive-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.field-item {
  flex: 1 1 150px; /* 灵活增长，最小宽度150px */
  min-width: 150px;
  max-width: calc(100% / 3 - 10px); /* 在较小屏幕上最多3个一行 */
}

/* 大屏幕上最多6个一行 */
@media (min-width: 1200px) {
  .field-item {
    max-width: calc(100% / 6 - 13px);
  }
}

/* 中等屏幕上最多4个一行 */
@media (min-width: 900px) and (max-width: 1199px) {
  .field-item {
    max-width: calc(100% / 4 - 12px);
  }
}

/* 确保表单字段有合适的高度 */
.form-field {
  width: 100%;
}

.form-field .el-select {
  width: 100%;
}

.form-label {
  font-weight: bold;
  margin-bottom: 6px;
}

.upload-area {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
}

.upload-icon {
  font-size: 48px;
  color: #8c939d;
}

.upload-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 10px;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0,0,0,0.3);
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview-container:hover .preview-overlay {
  opacity: 1;
}

.form-buttons {
  margin-top: 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
}

.diagnosis-results {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.diagnosis-recommendations {
  margin: 20px 0;
}

.result-image-container {
  margin: 20px auto;
  text-align: center;
  max-width: 600px;
}

.result-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  object-fit: contain;
}

.placeholder-image {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
}

.recommendation-box {
  background-color: #f9f9f9;
  border-left: 4px solid #409EFF;
  padding: 15px;
  border-radius: 4px;
}

.recommendation-box p {
  margin: 0;
  color: #333;
}

.recommendation-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-size: 16px;
  color: #606266;
}

.recommendation-loading .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.recommendation-collapse {
  margin-top: 10px;
}

.recommendation-collapse p {
  padding: 0 10px;
  line-height: 1.6;
  color: #606266;
}

.disclaimer {
  margin: 20px 0;
  padding: 10px;
  background-color: #f7f7f9;
  border-radius: 4px;
  font-size: 12px;
  color: #909399;
}

.disclaimer p {
  margin: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-title {
  font-size: 18px;
  font-weight: bold;
}

.detection-info {
  margin-bottom: 20px;
}

.recommendation-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.recommendation-section {
  margin: 15px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.recommendation-section h3 {
  margin-top: 0;
  color: #409EFF;
  font-size: 16px;
}

.recommendation-placeholder {
  margin: 15px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  min-height: 80px;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .result-actions {
    flex-direction: column;
  }
}
</style> 