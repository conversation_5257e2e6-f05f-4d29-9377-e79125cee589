"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[726],{37620:(t,e,a)=>{a.d(e,{VG:()=>i});a(54119),a(28706),a(74423),a(8921),a(15086),a(26099),a(27495),a(99449),a(21699),a(71761),a(90744),a(11392);var n="http://localhost:8085";function i(t){if(!t)return"";var e=String(t);if(e.startsWith("http://")||e.startsWith("https://"))return e;if(e.startsWith("data:"))return e;if(e.includes("medical_images")){var a=/medical_images[\/\\]([^\/\\]+)(?:[\/\\](.*))?/,i=e.match(a);if(i){var r=i[1],s=i[2]||"",o="/medical/images/".concat(r,"/").concat(s),d=n+o;return d}}if(e.startsWith("/medical/"))return n+e;if(e.match(/^[a-zA-Z]:\\/)){var h=e.split(/[\/\\]/).pop(),c="";e.includes("\\original\\")||e.includes("/original/")?c="original":e.includes("\\processed\\")||e.includes("/processed/")?c="processed":e.includes("\\annotate\\")||e.includes("/annotate/")?c="annotate":(e.includes("\\temp\\")||e.includes("/temp/"))&&(c="temp");var u="/medical/images/".concat(c,"/").concat(h);return n+u}var g=e.startsWith("/")?e:"/"+e;g.startsWith("/medical")||(g="/medical"+g);try{var l=g.lastIndexOf("/");if(-1!==l){var m=g.substring(0,l+1),f=g.substring(l+1);g=m+encodeURIComponent(f)}}catch(p){}return n+g}},97726:(t,e,a)=>{a.r(e),a.d(e,{default:()=>F});var n=a(20641),i=a(90033),r=a(53751),s={class:"annotation-container"},o={class:"main-content"},d={class:"toolbar"},h={class:"tool-section"},c={class:"tool-section"},u={key:0,class:"annotations-list"},g={class:"annotation-area"},l={class:"image-wrapper"},m={class:"image-container",ref:"imageContainerInner"},f=["src"],p=["onMousedown"],I=["onMousedown"],x=["onMousedown"],v=["onMousedown"],w=["onMousedown"],b={class:"navigation-controls"};function y(t,e,a,y,k,M){var P=(0,n.g2)("el-option"),$=(0,n.g2)("el-select"),B=(0,n.g2)("el-radio"),C=(0,n.g2)("el-radio-group"),S=(0,n.g2)("el-table-column"),A=(0,n.g2)("el-button"),F=(0,n.g2)("el-table");return(0,n.uX)(),(0,n.CE)("div",s,[e[13]||(e[13]=(0,n.Lk)("div",{class:"page-header"},[(0,n.Lk)("h2",null,"图像标注")],-1)),(0,n.Lk)("div",o,[(0,n.Lk)("div",d,[(0,n.Lk)("div",h,[e[6]||(e[6]=(0,n.Lk)("h3",null,"标签分类",-1)),(0,n.bF)($,{modelValue:k.selectedTag,"onUpdate:modelValue":e[0]||(e[0]=function(t){return k.selectedTag=t}),placeholder:"请选择标签分类",style:{width:"100%"}},{default:(0,n.k6)((function(){return[(0,n.bF)(P,{label:"血管瘤",value:"血管瘤"}),(0,n.bF)(P,{label:"淋巴管瘤",value:"淋巴管瘤"}),(0,n.bF)(P,{label:"混合型",value:"混合型"}),(0,n.bF)(P,{label:"其他",value:"其他"})]})),_:1},8,["modelValue"])]),(0,n.Lk)("div",c,[e[8]||(e[8]=(0,n.Lk)("h3",null,"标注工具",-1)),(0,n.bF)(C,{modelValue:k.currentTool,"onUpdate:modelValue":e[1]||(e[1]=function(t){return k.currentTool=t}),style:{display:"block","margin-top":"10px"}},{default:(0,n.k6)((function(){return[(0,n.bF)(B,{label:"rectangle"},{default:(0,n.k6)((function(){return e[7]||(e[7]=[(0,n.eW)("矩形框")])})),_:1,__:[7]})]})),_:1},8,["modelValue"])]),k.annotations.length>0?((0,n.uX)(),(0,n.CE)("div",u,[e[10]||(e[10]=(0,n.Lk)("h3",null,"已添加标注",-1)),(0,n.bF)(F,{data:M.filteredAnnotations,style:{width:"100%"}},{default:(0,n.k6)((function(){return[(0,n.bF)(S,{label:"编号",width:"60"},{default:(0,n.k6)((function(t){return[(0,n.eW)((0,i.v_)(t.$index+1),1)]})),_:1}),(0,n.bF)(S,{prop:"tag",label:"标签",width:"90"}),(0,n.bF)(S,{label:"操作",width:"80"},{default:(0,n.k6)((function(t){return[(0,n.bF)(A,{type:"danger",size:"small",onClick:function(e){return M.deleteAnnotation(t.row.id)}},{default:(0,n.k6)((function(){return e[9]||(e[9]=[(0,n.eW)("删除")])})),_:2,__:[9]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])])):(0,n.Q3)("",!0)]),(0,n.Lk)("div",g,[(0,n.Lk)("div",l,[(0,n.Lk)("div",m,[k.currentImage?((0,n.uX)(),(0,n.CE)("img",{key:0,src:M.getImageUrl(k.currentImage.url),ref:"annotationImage",class:"annotation-image",onLoad:e[2]||(e[2]=function(){return M.handleImageLoad&&M.handleImageLoad.apply(M,arguments)}),alt:"医学影像"},null,40,f)):(0,n.Q3)("",!0),k.currentImage?((0,n.uX)(),(0,n.CE)("div",{key:1,class:"annotation-overlay",style:(0,i.Tr)({width:k.imageWidth+"px",height:k.imageHeight+"px"}),onMousedown:e[3]||(e[3]=function(){return M.startDrawing&&M.startDrawing.apply(M,arguments)}),onMousemove:e[4]||(e[4]=function(){return M.drawing&&M.drawing.apply(M,arguments)}),onMouseup:e[5]||(e[5]=function(){return M.endDrawing&&M.endDrawing.apply(M,arguments)})},null,36)):(0,n.Q3)("",!0),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(M.filteredAnnotations,(function(t,e){return(0,n.uX)(),(0,n.CE)("div",{key:e,class:"annotation-box",style:(0,i.Tr)({left:t.x+"px",top:t.y+"px",width:t.width+"px",height:t.height+"px",borderColor:M.getTagColor(t.tag),cursor:k.isEditingBox&&k.editingBoxId===t.id?"move":"default"}),onMousedown:(0,r.D$)((function(e){return M.startEditBox(e,t.id)}),["stop"])},[(0,n.Lk)("span",{class:"annotation-label",style:(0,i.Tr)({backgroundColor:M.getTagColor(t.tag)})},(0,i.v_)(t.tag),5),(0,n.Lk)("div",{class:"resize-handle top-left",onMousedown:(0,r.D$)((function(e){return M.startResizeBox(e,t.id,"top-left")}),["stop"])},null,40,I),(0,n.Lk)("div",{class:"resize-handle top-right",onMousedown:(0,r.D$)((function(e){return M.startResizeBox(e,t.id,"top-right")}),["stop"])},null,40,x),(0,n.Lk)("div",{class:"resize-handle bottom-left",onMousedown:(0,r.D$)((function(e){return M.startResizeBox(e,t.id,"bottom-left")}),["stop"])},null,40,v),(0,n.Lk)("div",{class:"resize-handle bottom-right",onMousedown:(0,r.D$)((function(e){return M.startResizeBox(e,t.id,"bottom-right")}),["stop"])},null,40,w)],44,p)})),128)),k.isDrawing?((0,n.uX)(),(0,n.CE)("div",{key:2,class:"drawing-box",style:(0,i.Tr)({left:Math.min(k.drawStart.x,k.drawCurrent.x)+"px",top:Math.min(k.drawStart.y,k.drawCurrent.y)+"px",width:Math.abs(k.drawCurrent.x-k.drawStart.x)+"px",height:Math.abs(k.drawCurrent.y-k.drawStart.y)+"px"})},null,4)):(0,n.Q3)("",!0)],512)]),(0,n.Lk)("div",b,[(0,n.Lk)("div",null,[(0,n.bF)(A,{type:"info",onClick:M.goBack},{default:(0,n.k6)((function(){return e[11]||(e[11]=[(0,n.eW)("返回上传图片")])})),_:1,__:[11]},8,["onClick"])]),(0,n.Lk)("div",null,[(0,n.bF)(A,{type:"primary",onClick:M.saveAndNext,style:{"margin-right":"10px"}},{default:(0,n.k6)((function(){return e[12]||(e[12]=[(0,n.eW)("保存并填写表单")])})),_:1,__:[12]},8,["onClick"])])])])])])}a(54119);var k=a(41034),M=a(14048),P=a(30388),$=(a(16280),a(76918),a(28706),a(2008),a(50113),a(48980),a(51629),a(64346),a(8921),a(44114),a(54554),a(59089),a(60739),a(23288),a(18111),a(22489),a(20116),a(7588),a(33110),a(2892),a(9868),a(79432),a(26099),a(38781),a(47764),a(23500),a(62953),a(76031),a(653)),B=a(37620);const C={name:"CaseDetailForm",data:function(){return{uploadedImages:[],currentImageIndex:0,currentImage:null,selectedTag:"血管瘤",currentTool:"rectangle",annotations:[],isDrawing:!1,drawStart:{x:0,y:0},drawCurrent:{x:0,y:0},imageWidth:0,imageHeight:0,isEditingBox:!1,isResizingBox:!1,editingBoxId:null,resizeHandle:null,editStartPos:{x:0,y:0},originalBox:null,imagePairId:null,dbAnnotations:[],annotationsLoaded:!1,processedFilePath:null,shouldSaveOriginalImage:!1,imageLoaded:!1}},computed:{filteredAnnotations:function(){var t=this;return this.annotations.filter((function(e){return e.imageIndex===t.currentImageIndex}))}},created:function(){var t=this.$route.query.id||this.$route.query.imageId||localStorage.getItem("lastUploadedImageId");if(t)this.currentImageId=t,sessionStorage.setItem("isAppOperation","true"),this.loadImageFromPairs(t);else{var e=this.$route.query.path||this.$route.query.filePath||localStorage.getItem("processedFilePath");e?(this.processedFilePath=e,this.loadImageByPath(e)):this.$message.error("未提供图像ID，请先上传图像")}window.addEventListener("resize",this.handleResize)},mounted:function(){sessionStorage.setItem("isAppOperation","true"),this.currentImage&&(this.loadAnnotationsFromDatabase(),this.saveOriginalImage()),window.addEventListener("mousemove",this.handleGlobalMouseMove),window.addEventListener("mouseup",this.handleGlobalMouseUp),this.shouldSaveOriginalImage&&this.imageLoaded&&this.processedFilePath&&this.saveOriginalImage()},unmounted:function(){window.removeEventListener("mousemove",this.handleGlobalMouseMove),window.removeEventListener("mouseup",this.handleGlobalMouseUp),window.removeEventListener("resize",this.handleResize),sessionStorage.removeItem("isNavigatingAfterSave"),sessionStorage.removeItem("returningToWorkbench")},methods:{processLongId:function(t){if(!t)return null;var e=t.toString();return e.length>9?e.substring(e.length-9):e},loadImageFromPairs:function(t){var e=this;if(!t)return this.loadingError=!0,void(this.loadingErrorMessage="图像ID无效");fetch("/api/image-pairs/metadata/".concat(t),{method:"GET",headers:{Accept:"application/json"},credentials:"include"}).then((function(t){if(!t.ok)throw new Error("HTTP error! status: ".concat(t.status));return t.json()})).then((function(a){if(a&&a.length>0){var n=a[0];e.imagePairId=n.id;var i=n.imageOnePath||n.image_one_path;if(!i&&n.image_one_url?e.processedFilePath=n.image_one_url:!i&&n.url?e.processedFilePath=n.url:e.processedFilePath=i,!e.processedFilePath)return void e.tryLoadImagePathFromMetadata(t,(function(a){a&&(e.processedFilePath=a,e.updateImagePairPath(t,a),e.uploadedImages=[{id:t,url:a,filename:"图像 #".concat(t)}],e.currentImage=e.uploadedImages[0],e.imageLoaded=!0)}));e.uploadedImages=[{id:t,url:e.processedFilePath,filename:"图像 #".concat(t)}];(0,B.VG)(e.processedFilePath);e.currentImage=e.uploadedImages[0],e.currentImageIndex=0,e.loadAnnotationsFromDatabase(),e.imageLoaded=!0}else e.tryLoadFromMetadata(t)}))["catch"]((function(a){e.tryLoadFromMetadata(t)}))},tryLoadFromMetadata:function(t){var e=this;$["default"].images.getOne(t).then((function(t){var a=t.data;a&&a.path?(e.uploadedImages=[{id:a.id,url:a.path,filename:a.original_name||"图像 #".concat(a.id)}],e.currentImage=e.uploadedImages[0],e.currentImageIndex=0,e.processedFilePath=a.path,e.createImagePairDirectly(a.id,a.path),e.loadAnnotationsFromDatabase(),e.imageLoaded=!0):e.$message.error("图像路径不存在，请检查数据库")}))["catch"]((function(t){e.loadingError=!0,e.loadingErrorMessage="无法加载图像，请检查数据库记录",e.processedFilePath&&e.currentImageId?e.createImageRecord(e.processedFilePath,e.currentImageId):e.$message.error("无法找到图像数据，请返回上一步重新上传")}))},createImagePairDirectly:function(t,e){var a=this,n={metadataId:t.toString(),imageOnePath:e,description:"图像".concat(t)};fetch("/api/image-pairs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n),credentials:"include"}).then((function(t){if(!t.ok)throw new Error("HTTP error! status: ".concat(t.status));return t.json()})).then((function(t){t&&t.id&&(a.imagePairId=t.id),a.$message.success("图像关联信息创建成功")}))["catch"]((function(t){a.$message.warning("图像关联创建失败，但您仍可继续使用")}))},loadImageById:function(t){this.loadImageFromPairs(t)},createImageRecord:function(t,e){var a=this,n=JSON.parse(localStorage.getItem("user"))||{id:1},i=t.substring(t.lastIndexOf("/")+1),r={filename:i,original_name:i,path:t,mimetype:"image/jpeg",size:0,width:800,height:600,status:"DRAFT",uploaded_by:n.id};$["default"].images.update(e,r).then((function(t){a.saveOriginalImage(),a.loadImageById(e)}))["catch"]((function(t){a.$message.error("无法创建图像记录，请检查数据库连接")}))},loadImageByPath:function(t){this.uploadedImages=[{id:this.currentImageId||Date.now().toString(),url:t,filename:t.substring(t.lastIndexOf("/")+1)}],this.currentImage=this.uploadedImages[0],this.currentImageIndex=0,this.currentImageId||(this.currentImageId=this.uploadedImages[0].id),this.checkExistingImagePair(),this.imageLoaded=!0},checkExistingImagePair:function(){var t=this;this.currentImageId&&this.processedFilePath&&$["default"].imagePairs.getByMetadataId(this.currentImageId).then((function(e){if(e.data&&e.data.length>0){var a=e.data[0];t.imagePairId=a.id,a.imageOnePath!==t.processedFilePath&&t.saveOriginalImage()}else t.saveOriginalImage()}))["catch"]((function(e){t.saveOriginalImage()}))},saveOriginalImage:function(){var t=this;if(this.currentImageId){var e="";if(this.currentImage&&this.currentImage.url)e=this.currentImage.url;else{if(!this.processedFilePath)return;e=this.processedFilePath}this.ensureImageMetadataExists(this.currentImageId,e,(function(){JSON.parse(localStorage.getItem("user"));try{var a={metadataId:t.currentImageId.toString(),imageOnePath:e,description:"图像".concat(t.currentImageId)},n=function(){var e=(0,P.A)((0,M.A)().mark((function e(){var n,i,r,s,o,d;return(0,M.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=4,fetch("/api/image-pairs/metadata/".concat(t.currentImageId),{method:"GET",headers:{Accept:"application/json","Cache-Control":"no-cache",Pragma:"no-cache"},credentials:"include"});case 4:return n=e.sent,e.next=7,n.json();case 7:return i=e.sent,r=null,s=!1,i&&i.length>0&&i[0].id&&(s=!0,r=i[0].id),s&&r&&(a.id=r),e.next=14,fetch("/api/image-pairs",{method:"POST",headers:{"Content-Type":"application/json","Cache-Control":"no-cache",Pragma:"no-cache"},body:JSON.stringify(a),credentials:"include"});case 14:if(o=e.sent,o.ok){e.next=17;break}throw new Error("保存失败: ".concat(o.status));case 17:return e.next=19,o.json();case 19:d=e.sent,d&&d.id&&(t.imagePairId=d.id),t.$message.success("图像关联信息保存成功"),e.next=29;break;case 25:e.prev=25,e.t0=e["catch"](0),t.$message.error("保存失败: "+e.t0.message);case 29:case"end":return e.stop()}}),e,null,[[0,25]])})));return function(){return e.apply(this,arguments)}}();n()}catch(i){t.$message.error("保存图像对失败: "+i.message)}}))}},ensureImageMetadataExists:function(t,e,a){var n=this;$["default"].images.getOne(t).then((function(){a()}))["catch"]((function(){n.createImageRecord(e,t)}))},handleResize:function(){if(this.$refs.annotationImage){var t=this.$refs.annotationImage,e=t.getBoundingClientRect();this.imageWidth=e.width,this.imageHeight=e.height}},initTools:function(){},getImageUrl:function(t){var e=(0,B.VG)(t);return e},handleImageLoad:function(t){var e=this;this.currentImage&&this.getImageUrl(this.currentImage.url);if(this.$refs.annotationImage){var a=this.$refs.annotationImage,n=a.getBoundingClientRect();this.imageWidth=n.width,this.imageHeight=n.height,this.$nextTick((function(){e.imageLoaded=!0,e.dbAnnotations.length>0&&!e.annotationsLoaded&&e.processLoadedAnnotations()}))}},selectTool:function(t){this.currentTool=t},startDrawing:function(t){if(!this.isEditingBox&&!this.isResizingBox&&"rectangle"===this.currentTool){var e=this.$refs.imageContainerInner.getBoundingClientRect(),a=t.clientX-e.left,n=t.clientY-e.top;a<0||a>this.imageWidth||n<0||n>this.imageHeight||(this.isDrawing=!0,this.drawStart={x:a,y:n},this.drawCurrent={x:a,y:n})}},drawing:function(t){if(this.isDrawing){var e=this.$refs.imageContainerInner.getBoundingClientRect(),a=t.clientX-e.left,n=t.clientY-e.top,i=Math.max(0,Math.min(this.imageWidth,a)),r=Math.max(0,Math.min(this.imageHeight,n));this.drawCurrent={x:i,y:r}}},startEditBox:function(t,e){t.preventDefault();var a=this.annotations.find((function(t){return t.id===e}));if(a){this.originalBox=(0,k.A)({},a);var n=this.$refs.imageContainerInner.getBoundingClientRect();this.editStartPos={x:t.clientX-n.left,y:t.clientY-n.top},this.isEditingBox=!0,this.editingBoxId=e,this.isDrawing=!1}},startResizeBox:function(t,e,a){t.preventDefault();var n=this.annotations.find((function(t){return t.id===e}));if(n){this.originalBox=(0,k.A)({},n);var i=this.$refs.imageContainerInner.getBoundingClientRect();this.editStartPos={x:t.clientX-i.left,y:t.clientY-i.top},this.isResizingBox=!0,this.editingBoxId=e,this.resizeHandle=a,this.isDrawing=!1}},handleGlobalMouseMove:function(t){var e=this;if(this.isDrawing){var a=this.$refs.imageContainerInner.getBoundingClientRect(),n=t.clientX-a.left,i=t.clientY-a.top,r=Math.max(0,Math.min(this.imageWidth,n)),s=Math.max(0,Math.min(this.imageHeight,i));this.drawCurrent={x:r,y:s}}else{if(this.isEditingBox){var o=this.$refs.imageContainerInner.getBoundingClientRect(),d=t.clientX-o.left,h=t.clientY-o.top,c=d-this.editStartPos.x,u=h-this.editStartPos.y,g=this.annotations.findIndex((function(t){return t.id===e.editingBoxId}));if(-1===g)return;var l=this.originalBox.x+c,m=this.originalBox.y+u;return l=Math.max(0,Math.min(l,this.imageWidth-this.originalBox.width)),m=Math.max(0,Math.min(m,this.imageHeight-this.originalBox.height)),this.annotations[g].x=l,void(this.annotations[g].y=m)}if(this.isResizingBox){var f=this.$refs.imageContainerInner.getBoundingClientRect(),p=t.clientX-f.left,I=t.clientY-f.top,x=Math.max(0,Math.min(this.imageWidth,p)),v=Math.max(0,Math.min(this.imageHeight,I)),w=this.annotations.findIndex((function(t){return t.id===e.editingBoxId}));if(-1===w)return;var b=this.annotations[w],y=this.originalBox;switch(this.resizeHandle){case"top-left":b.width=y.x+y.width-x,b.height=y.y+y.height-v,b.x=x,b.y=v,b.width<10&&(b.width=10,b.x=y.x+y.width-10),b.height<10&&(b.height=10,b.y=y.y+y.height-10);break;case"top-right":b.width=x-y.x,b.height=y.y+y.height-v,b.y=v,b.width<10&&(b.width=10),b.height<10&&(b.height=10,b.y=y.y+y.height-10);break;case"bottom-left":b.width=y.x+y.width-x,b.height=v-y.y,b.x=x,b.width<10&&(b.width=10,b.x=y.x+y.width-10),b.height<10&&(b.height=10);break;case"bottom-right":b.width=x-y.x,b.height=v-y.y,b.width<10&&(b.width=10),b.height<10&&(b.height=10);break}}}},handleGlobalMouseUp:function(){var t=this;if(this.isDrawing)this.endDrawing();else if(this.isEditingBox||this.isResizingBox){var e=this.annotations.find((function(e){return e.id===t.editingBoxId}));if(e&&this.originalBox){var a=e.x!==this.originalBox.x||e.y!==this.originalBox.y||e.width!==this.originalBox.width||e.height!==this.originalBox.height;a&&this.updateAnnotationInDatabase(e)}this.isEditingBox=!1,this.isResizingBox=!1,this.editingBoxId=null,this.resizeHandle=null,this.originalBox=null}},endDrawing:function(){if(this.isDrawing&&(this.isDrawing=!1,Math.abs(this.drawCurrent.x-this.drawStart.x)>10&&Math.abs(this.drawCurrent.y-this.drawStart.y)>10)){var t=Math.min(this.drawStart.x,this.drawCurrent.x),e=Math.min(this.drawStart.y,this.drawCurrent.y),a=Math.abs(this.drawCurrent.x-this.drawStart.x),n=Math.abs(this.drawCurrent.y-this.drawStart.y),i={id:Date.now(),imageIndex:this.currentImageIndex,tag:this.selectedTag,type:"rectangle",x:t,y:e,width:a,height:n};this.annotations.push(i),this.saveAnnotationToDatabase(i)}},saveAnnotationToDatabase:function(t){var e=this,a=JSON.parse(localStorage.getItem("user"));if(a){var n=this.uploadedImages[this.currentImageIndex];if(n&&n.id)if(this.imageWidth&&this.imageHeight){var i=t.x/this.imageWidth,r=t.y/this.imageHeight,s=t.width/this.imageWidth,o=t.height/this.imageHeight;i=Math.max(0,Math.min(1,i)),r=Math.max(0,Math.min(1,r)),s=Math.max(.001,Math.min(1,s)),o=Math.max(.001,Math.min(1,o)),i=Number(i.toFixed(4)),r=Number(r.toFixed(4)),s=Number(s.toFixed(4)),o=Number(o.toFixed(4));var d={metadata_id:n.id,tag:t.tag,x:i,y:r,width:s,height:o,created_by:a.id};$["default"].tags.create(d).then((function(a){var n=e.annotations.findIndex((function(e){return e.id===t.id}));-1!==n&&(e.annotations[n].dbId=a.data.id),e.$message.success("标注已保存")}))["catch"]((function(t){t.response?401===t.response.status?e.$message.error("未授权，请先登录"):400===t.response.status?e.$message.error(t.response.data||"提交的标注数据无效"):e.$message.error("保存标注到数据库失败"):e.$message.error("保存标注到数据库失败，网络错误")}))}else this.$message.error("图片尺寸无效，无法计算标注坐标");else this.$message.error("图像信息不完整，无法保存标注")}else this.$message.error("未检测到用户信息，无法保存标注")},getTagColor:function(t){var e={血管瘤:"#f56c6c",淋巴管瘤:"#409eff",混合型:"#67c23a",其他:"#909399"};return e[t]||"#409eff"},deleteAnnotation:function(t){var e=this,a=this.annotations.findIndex((function(e){return e.id===t}));if(-1!==a){var n=this.annotations[a];this.annotations.splice(a,1),n.dbId&&$["default"].tags["delete"](n.dbId).then((function(){e.$message.success("标注已删除")}))["catch"]((function(t){e.$message.error("从数据库删除标注失败"),e.annotations.splice(a,0,n)}))}},saveAndNext:function(){var t=this;return(0,P.A)((0,M.A)().mark((function e(){var a,n,i,r,s;return(0,M.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,sessionStorage.setItem("isNavigatingAfterSave","true"),a=!0,e.prev=4,n=t.saveAnnotations("保存中，准备跳转到表单页面..."),i=new Promise((function(t,e){return setTimeout((function(){return e(new Error("请求超时"))}),8e3)})),e.next=9,Promise.race([n,i]);case 9:a=e.sent,e.next=17;break;case 12:e.prev=12,e.t0=e["catch"](4),t.$message.warning("保存标注过程中出现问题，将继续跳转到表单页面"),a=!0;case 17:if(e.t1=a,e.t1){e.next=22;break}return e.next=21,t.confirmContinue();case 21:e.t1=e.sent;case 22:if(!e.t1){e.next=29;break}if(s=null===(r=t.currentImage)||void 0===r?void 0:r.id,s){e.next=27;break}return t.$message.error("找不到图像ID，无法继续"),e.abrupt("return");case 27:t.$router.push({path:"/cases/structured-form",query:{imageId:s,direct:"1"}}).then((function(){}))["catch"]((function(t){window.location.href="/cases/structured-form?imageId=".concat(s,"&direct=1")}));case 29:e.next=35;break;case 31:e.prev=31,e.t2=e["catch"](0),t.$message.error("出现错误："+(e.t2.message||"未知错误"));case 35:case"end":return e.stop()}}),e,null,[[0,31],[4,12]])})))()},confirmContinue:function(){var t=this;return(0,P.A)((0,M.A)().mark((function e(){return(0,M.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$confirm("保存标注失败，是否仍要继续到表单页面？","提示",{confirmButtonText:"继续",cancelButtonText:"留在当前页面",type:"warning"});case 3:return e.abrupt("return",!0);case 6:return e.prev=6,e.t0=e["catch"](0),e.abrupt("return",!1);case 9:case"end":return e.stop()}}),e,null,[[0,6]])})))()},saveAnnotations:function(){var t=arguments,e=this;return(0,P.A)((0,M.A)().mark((function a(){var n,i,r,s,o,d,h,c;return(0,M.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=t.length>0&&void 0!==t[0]?t[0]:"保存中...",0!==e.annotations.length){a.next=4;break}return e.$message.warning("请至少添加一个标注框后再保存"),a.abrupt("return",!1);case 4:if(i=e.$loading({lock:!0,text:n,spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),r=e.uploadedImages[e.currentImageIndex],r&&r.id){a.next=10;break}return e.$message.error("图像信息不完整，无法保存标注"),i.close(),a.abrupt("return",!1);case 10:return a.prev=11,s=$["default"].tags.saveAnnotatedImage(r.id),o=new Promise((function(t,e){return setTimeout((function(){return e(new Error("API请求超时，服务器响应过慢"))}),5e3)})),a.next=17,Promise.race([s,o]);case 17:return d=a.sent,d.data&&d.data.annotated_image_path,localStorage.setItem("annotations",JSON.stringify(e.annotations)),a.prev=21,a.next=25,$["default"].images.markAsAnnotated(r.id);case 25:a.next=41;break;case 28:return a.prev=28,a.t0=a["catch"](21),a.prev=31,a.next=35,$["default"].images.updateStatus(r.id,"REVIEWED");case 35:a.next=41;break;case 38:a.prev=38,a.t1=a["catch"](31);case 41:return e.$message.success("标注已保存"),i.close(),a.abrupt("return",!0);case 46:return a.prev=46,a.t2=a["catch"](11),h="保存标注失败",a.t2.response?"string"===typeof a.t2.response.data?h+=": "+a.t2.response.data:a.t2.response.data&&a.t2.response.data.message?h+=": "+a.t2.response.data.message:h+=" (状态码: "+a.t2.response.status+")":a.t2.message&&(h+=": "+a.t2.message),e.$message.error(h),i.close(),a.prev=53,a.next=56,e.$confirm("保存标注失败，但已在本地保存标注数据。是否继续?","警告",{confirmButtonText:"继续",cancelButtonText:"留在当前页面",type:"warning"});case 56:if(c=a.sent,"confirm"!==c){a.next=61;break}return localStorage.setItem("annotations",JSON.stringify(e.annotations)),localStorage.setItem("pendingImageId",r.id.toString()),a.abrupt("return",!0);case 61:return a.abrupt("return",!1);case 64:return a.prev=64,a.t3=a["catch"](53),a.abrupt("return",!1);case 67:case"end":return a.stop()}}),a,null,[[11,46],[21,28],[31,38],[53,64]])})))()},loadAnnotationsFromDatabase:function(){var t=this,e=JSON.parse(localStorage.getItem("user"));e&&this.uploadedImages.forEach((function(e,a){if(e&&e.id){var n=e.id;String(n).length,$["default"].tags.getByImageId(n).then((function(e){t.dbAnnotations=e.data||[],Array.isArray(t.dbAnnotations)||(t.dbAnnotations=[]),t.imageWidth>0&&t.imageHeight>0&&t.processLoadedAnnotations()}))["catch"]((function(e){e.response&&(401===e.response.status?t.$message.error("请先登录后再进行标注"):404!==e.response.status&&400!==e.response.status||t.$message.warning("无法加载图像标注，但您仍可以添加新标注"))}))}}))},updateAnnotationInDatabase:function(t){var e=this,a=JSON.parse(localStorage.getItem("user"));if(a){if(t.dbId)if(this.imageWidth&&this.imageHeight){var n=t.x/this.imageWidth,i=t.y/this.imageHeight,r=t.width/this.imageWidth,s=t.height/this.imageHeight;n=Math.max(0,Math.min(1,n)),i=Math.max(0,Math.min(1,i)),r=Math.max(.001,Math.min(1,r)),s=Math.max(.001,Math.min(1,s)),n=Number(n.toFixed(4)),i=Number(i.toFixed(4)),r=Number(r.toFixed(4)),s=Number(s.toFixed(4));var o={tag:t.tag,x:n,y:i,width:r,height:s,updated_by:a.id};$["default"].tags.update(t.dbId,o).then((function(t){e.$message.success("标注已更新")}))["catch"]((function(t){t.response?401===t.response.status?e.$message.error("未授权，请先登录"):400===t.response.status?e.$message.error(t.response.data||"提交的标注数据无效"):e.$message.error("更新标注失败"):e.$message.error("更新标注失败，网络错误")}))}else this.$message.error("图片尺寸无效，无法计算标注坐标")}else this.$message.error("未检测到用户信息，无法更新标注")},processLoadedAnnotations:function(){var t=this;this.imageWidth<=0||this.imageHeight<=0||this.dbAnnotations&&0!==this.dbAnnotations.length&&(this.annotations=[],this.dbAnnotations.forEach((function(e){var a={id:Date.now()+Math.random(),dbId:e.id,imageIndex:t.currentImageIndex,tag:e.tag,type:"rectangle",x:e.x*t.imageWidth,y:e.y*t.imageHeight,width:e.width*t.imageWidth,height:e.height*t.imageHeight};t.annotations.push(a)})),this.annotationsLoaded=!0)},goBack:function(){var t=this;this.$confirm("返回上传页面可能会丢失当前未保存的标注，是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(sessionStorage.setItem("returningToWorkbench","true"),t.imagePairId){var e=t.$loading({lock:!0,text:"正在处理...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});$["default"].imagePairs.deleteAnnotatedImage(t.imagePairId).then((function(a){e.close();window.location.href;t.$router.push("/app/cases/new").then((function(){}))["catch"]((function(t){window.location.href="/app/cases/new"}))}))["catch"]((function(a){e.close(),t.$message.error("删除标注图片失败，但仍将返回上一页");window.location.href;t.$router.push("/app/cases/new").then((function(){}))["catch"]((function(t){window.location.href="/app/cases/new"}))}))}else{window.location.href;t.$router.push("/app/cases/new").then((function(){}))["catch"]((function(t){window.location.href="/app/cases/new"}))}}))["catch"]((function(){}))},tryLoadImagePathFromMetadata:function(t,e){$["default"].images.getOne(t).then((function(t){t.data&&t.data.path?e(t.data.path):e(null)}))["catch"]((function(t){e(null)}))},updateImagePairPath:function(t,e){if(this.imagePairId&&e){var a={id:this.imagePairId,metadataId:t.toString(),imageOnePath:e};fetch("/api/image-pairs",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(a),credentials:"include"}).then((function(t){if(!t.ok)throw new Error("HTTP error! status: ".concat(t.status));return t.json()})).then((function(t){}))["catch"]((function(t){}))}}},watch:{imageLoaded:function(t){t&&this.shouldSaveOriginalImage&&this.processedFilePath&&this.saveOriginalImage()}}};var S=a(66262);const A=(0,S.A)(C,[["render",y],["__scopeId","data-v-1896179a"]]),F=A}}]);