package com.medical.annotation.repository;

import com.medical.annotation.model.User;
import com.medical.annotation.model.User.Role;
import com.medical.annotation.model.User.ReviewerApplicationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Integer> {
    // 基础查询
    Optional<User> findByEmail(String email);
    boolean existsByEmail(String email);
    
    // 自定义ID相关查询
    Optional<User> findByCustomId(String customId);
    boolean existsByCustomId(String customId);
    
    // 添加一个使用原生SQL和正确校对规则的方法
    @Query(value = "SELECT * FROM users WHERE custom_id = ?1 COLLATE utf8mb4_0900_ai_ci", nativeQuery = true)
    Optional<User> findByCustomIdWithCollation(String customId);
    
    // 团队相关查询
    List<User> findByTeam_Id(Integer teamId);
    List<User> findByTeam_IdAndRole(Integer teamId, Role role);
    List<User> findByTeamIsNull();
    
    // 计数查询
    long countByTeam_Id(Integer teamId);
    
    // 审核医生申请状态相关查询
    List<User> findByReviewerApplicationStatus(ReviewerApplicationStatus status);
    List<User> findByRole(Role role);
} 