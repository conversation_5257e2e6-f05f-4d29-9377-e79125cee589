<template>
  <div class="edit-profile-container">
    <div class="page-header">
      <div class="back-link" @click="goBack">
        <i class="el-icon-arrow-left"></i> 返回
      </div>
      <h1 class="page-title">编辑个人资料</h1>
    </div>
    
    <div class="form-container">
      <el-form :model="profileForm" ref="profileForm" :rules="profileRules" label-width="100px" class="profile-form">
        <el-form-item label="ID" prop="customId">
          <el-input v-model="profileForm.customId" placeholder="用户ID (9-20位数字或字母)"></el-input>
          <div class="form-tip">ID长度必须为9-20位，只能包含数字和拼音字母</div>
        </el-form-item>
        
        <el-form-item label="姓名" prop="name">
          <el-input v-model="profileForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="profileForm.email" placeholder="请输入邮箱"></el-input>
          <div class="form-tip">请输入有效的邮箱地址</div>
        </el-form-item>
        
        <el-form-item label="医院" prop="hospital">
          <el-input v-model="profileForm.hospital" placeholder="请输入医院名称"></el-input>
        </el-form-item>
        
        <el-form-item label="科室" prop="department">
          <el-input v-model="profileForm.department" placeholder="请输入科室名称"></el-input>
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="profileForm.role" placeholder="请选择角色" disabled>
            <el-option label="管理员" value="ADMIN"></el-option>
            <el-option label="标注医生" value="DOCTOR"></el-option>
            <el-option label="审核医生" value="REVIEWER"></el-option>
          </el-select>
          <div class="form-tip">角色不可修改，请联系管理员</div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="isSubmitting" @click="updateProfile">保存修改</el-button>
          <el-button @click="goBack" style="margin-left: 15px;">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api';

export default {
  name: 'EditProfile',
  data() {
    // 邮箱格式验证器
    const validateEmail = (rule, value, callback) => {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(value)) {
        callback(new Error('请输入有效的邮箱地址'));
      } else {
        callback();
      }
    };
    
    return {
      userId: '',
      isAdmin: false,
      isSubmitting: false,
      originalEmail: '', // 存储原始邮箱，用于比较是否修改
      profileForm: {
        customId: '',
        name: '',
        email: '',
        hospital: '',
        department: '',
        role: ''
      },
      profileRules: {
        customId: [
          { required: true, message: '请输入用户ID', trigger: 'blur' },
          { min: 9, max: 20, message: 'ID长度必须为9-20位', trigger: 'blur' },
          { 
            validator: (rule, value, callback) => {
              if (!/^[a-zA-Z0-9]+$/.test(value)) {
                callback(new Error('ID只能包含数字和拼音字母'));
              } else {
                callback();
              }
            }, 
            trigger: 'blur' 
          }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ],
        hospital: [
          { required: true, message: '请输入医院名称', trigger: 'blur' }
        ],
        department: [
          { required: true, message: '请输入科室名称', trigger: 'blur' }
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    // 从localStorage获取用户信息
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      this.userId = user.id || '';
      
      // 判断是否为管理员
      this.isAdmin = user.role === 'ADMIN';
      
      // 如果有用户ID，获取用户详细信息
      if (this.userId) {
        this.fetchUserDetails();
      } else {
        this.$message.error('未找到用户信息');
        this.goBack();
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      this.$message.error('获取用户信息失败');
      this.goBack();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.push('/app/profile');
    },
    
    // 获取用户详细信息
    async fetchUserDetails() {
      try {
        const response = await api.users.getUser(this.userId);
        const userData = response.data;
        
                  if (userData) {
            this.profileForm = {
              customId: userData.customId || '',
              name: userData.name || '',
              email: userData.email || '',
              hospital: userData.hospital || '',
              department: userData.department || '',
              role: userData.role || ''
            };
            
            // 保存原始邮箱，用于后续比较
            this.originalEmail = userData.email || '';
          }
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        this.$message.error('获取用户详细信息失败');
      }
    },
    
    // 更新个人资料
    updateProfile() {
      this.$refs.profileForm.validate(async (valid) => {
        if (!valid) {
          return;
        }
        
        // 检查邮箱是否被修改
        const isEmailChanged = this.originalEmail !== this.profileForm.email;
        
        // 如果邮箱被修改，显示确认对话框
        if (isEmailChanged) {
          try {
            await this.$confirm(
              `您将邮箱从 "${this.originalEmail}" 修改为 "${this.profileForm.email}"，确定要修改吗？`,
              '修改邮箱确认',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            );
            // 用户点击了确定，继续保存
          } catch (e) {
            // 用户点击了取消，不继续保存
            return;
          }
        }
        
        this.isSubmitting = true;
        
        try {
          // 准备更新数据
          const userData = {
            name: this.profileForm.name,
            hospital: this.profileForm.hospital,
            department: this.profileForm.department,
            customId: this.profileForm.customId,
            email: this.profileForm.email
            // 角色不可修改
          };
          
          const response = await api.users.updateUser(this.userId, userData);
          
          // 更新本地存储的用户信息
          const updatedUser = response.data;
          const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
          
          storedUser.name = updatedUser.name;
          storedUser.hospital = updatedUser.hospital;
          storedUser.department = updatedUser.department;
          
          // 更新ID和邮箱
          if (updatedUser.customId) {
            storedUser.customId = updatedUser.customId;
          }
          if (updatedUser.email) {
            storedUser.email = updatedUser.email;
          }
          
          localStorage.setItem('user', JSON.stringify(storedUser));
          
          // 更新成功后，更新原始邮箱
          if (updatedUser.email) {
            this.originalEmail = updatedUser.email;
          }
          
          this.$message.success('资料更新成功');
          
          // 延迟返回个人资料页面
          setTimeout(() => {
            this.goBack();
          }, 1500);
        } catch (error) {
          console.error('更新资料失败:', error);
          this.$message.error('更新资料失败: ' + (error.response?.data?.message || error.message || '未知错误'));
        } finally {
          this.isSubmitting = false;
        }
      });
    }
  }
}
</script>

<style scoped>
.edit-profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  position: relative;
}

.back-link {
  color: #409EFF;
  cursor: pointer;
  margin-bottom: 10px;
  display: inline-block;
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin: 10px 0;
  text-align: center;
}

.form-container {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.profile-form {
  max-width: 500px;
  margin: 0 auto;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

.el-form-item {
  margin-bottom: 25px;
}
</style> 