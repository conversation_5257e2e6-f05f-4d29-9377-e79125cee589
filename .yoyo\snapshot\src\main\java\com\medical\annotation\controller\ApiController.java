package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.model.Tag;
import com.medical.annotation.model.User;
import com.medical.annotation.model.Team;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.TeamRepository;
import com.medical.annotation.service.FileService;
import com.medical.annotation.util.DatabaseUtil;
import com.medical.annotation.util.ImagePathUtil;
import com.medical.annotation.util.FileNameGenerator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.security.access.prepost.PreAuthorize;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.io.File;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.lang.StringBuilder;
import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import javax.imageio.ImageIO;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.logging.Logger;

@RestController
@RequestMapping("/api")
public class ApiController {

    private static final Logger logger = Logger.getLogger(ApiController.class.getName());

    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TagRepository tagRepository;
    
    @Autowired
    private ImagePairRepository imagePairRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TeamRepository teamRepository;
    
    @Autowired
    private FileService fileService;

    // 获取所有图像
    @GetMapping("/images")
    public ResponseEntity<List<Map<String, Object>>> getAllImages() {
        System.out.println("接收到获取所有图像请求");
        
        List<ImageMetadata> images = imageMetadataRepository.findAll();
        System.out.println("从数据库获取到 " + images.size() + " 条图像记录");
        
        // 检查空值字段
        for (int i = 0; i < Math.min(5, images.size()); i++) {
            ImageMetadata img = images.get(i);
            System.out.println("图像ID: " + img.getId() + 
                ", 格式化ID: " + img.getFormattedId() + 
                ", 状态: " + img.getStatus() + 
                ", 患者名称: " + img.getPatientName() + 
                ", 文件名: " + img.getFilename());
        }
        
        // 按创建时间降序排序，最新的在前面
        images.sort((a, b) -> {
            if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
            if (a.getCreatedAt() == null) return 1;
            if (b.getCreatedAt() == null) return -1;
            return b.getCreatedAt().compareTo(a.getCreatedAt());
        });
        
        // 转换为Map，将id替换为formatted_id
        List<Map<String, Object>> result = new ArrayList<>();
        for (ImageMetadata image : images) {
            Map<String, Object> imageMap = new HashMap<>();
            // 使用formatted_id替换id
            imageMap.put("id", image.getFormattedId() != null ? image.getFormattedId() : String.valueOf(image.getId()));
            imageMap.put("filename", image.getFilename());
            imageMap.put("original_name", image.getOriginalName());
            imageMap.put("mimetype", image.getMimetype());
            imageMap.put("size", image.getSize());
            imageMap.put("width", image.getWidth());
            imageMap.put("height", image.getHeight());
            imageMap.put("status", image.getStatus() != null ? image.getStatus().toString() : null);
            imageMap.put("created_at", image.getCreatedAt());
            imageMap.put("createdAt", image.getCreatedAt());
            
            // 添加上传者信息
            if (image.getUploadedBy() != null) {
                imageMap.put("uploaded_by", image.getUploadedBy().getId());
                imageMap.put("uploader_name", image.getUploadedBy().getName());
            }
            
            // 添加病例号等信息
            imageMap.put("case_number", image.getCaseNumber());
            imageMap.put("patient_name", image.getPatientName());
            imageMap.put("lesion_location", image.getLesionLocation());
            imageMap.put("border_clarity", image.getBorderClarity());
            imageMap.put("blood_flow", image.getBloodFlow());
            imageMap.put("complications", image.getComplications());
            
            result.add(imageMap);
        }
        
        System.out.println("返回排序后的图像列表，使用formatted_id作为id");
        return ResponseEntity.ok(result);
    }

    // 获取特定图像
    @GetMapping("/images/{id}")
    public ResponseEntity<?> getImageById(@PathVariable String id) {
        System.out.println("获取图像信息, 原始请求ID: " + id);
        
        // 处理9位格式化ID（前导零），尝试查找格式化ID对应的图像
        if (id.length() == 9 && id.startsWith("0")) {
            // 尝试通过formattedId查找图像
            Optional<ImageMetadata> imageByFormattedId = imageMetadataRepository.findByFormattedId(id);
            if (imageByFormattedId.isPresent()) {
                System.out.println("通过格式化ID找到图像: " + id);
                ImageMetadata image = imageByFormattedId.get();
                return ResponseEntity.ok(image);
            }
            
            // 如果格式化ID不存在，尝试去掉前导零后查找
            try {
                String idWithoutLeadingZeros = id.replaceFirst("^0+", "");
                if (!idWithoutLeadingZeros.isEmpty()) {
                    Long numericId = Long.parseLong(idWithoutLeadingZeros);
                    Optional<ImageMetadata> image = imageMetadataRepository.findById(numericId);
                    if (image.isPresent()) {
                        System.out.println("通过去掉前导零后的ID找到图像: " + numericId);
                        return ResponseEntity.ok(image.get());
                    }
                }
            } catch (NumberFormatException e) {
                System.out.println("ID格式错误，无法转换为数字: " + id);
            }
        }
        
        // 尝试直接按ID查找
        try {
            Long numericId = Long.parseLong(id);
            Optional<ImageMetadata> image = imageMetadataRepository.findById(numericId);
            if (image.isPresent()) {
                System.out.println("通过数字ID找到图像: " + numericId);
                return ResponseEntity.ok(image.get());
            }
        } catch (NumberFormatException e) {
            System.out.println("ID不是有效的数字: " + id);
            // ID不是数字，继续尝试其他查找方式
        }
        
        System.out.println("未找到ID为 " + id + " 的图像，原始请求ID: " + id);
        return ResponseEntity.notFound().build();
    }
    
    /**
     * 构建图像响应对象
     */
    private ResponseEntity<Map<String, Object>> buildImageResponse(ImageMetadata image) {
        // 构建响应
        Map<String, Object> response = new HashMap<>();
        
        // 添加基本元数据信息 - 使用formatted_id作为id字段
        String displayId = image.getFormattedId() != null ? image.getFormattedId() : String.valueOf(image.getId());
        response.put("id", displayId);
        response.put("real_id", image.getId()); // 保留一个真实ID的字段，以防需要
        response.put("filename", image.getFilename());
        response.put("original_name", image.getOriginalName());
        response.put("mimetype", image.getMimetype());
        response.put("size", image.getSize());
        response.put("width", image.getWidth());
        response.put("height", image.getHeight());
        response.put("status", image.getStatus() != null ? image.getStatus().toString() : null);
        
        // 添加患者信息（如果有）
        if (image.getPatientName() != null) {
            response.put("patient_name", image.getPatientName());
            response.put("patient_age", image.getPatientAge());
            response.put("patient_gender", image.getPatientGender());
        }
        
        // 添加诊断信息（如果有）
        if (image.getDiagnosisCategory() != null) {
            response.put("diagnosis_category", image.getDiagnosisCategory());
            response.put("diagnosis_icd_code", image.getDiagnosisIcdCode());
        }
        
        // 添加上传者信息
        if (image.getUploadedBy() != null) {
            Map<String, Object> uploaderInfo = new HashMap<>();
            uploaderInfo.put("id", image.getUploadedBy().getId());
            uploaderInfo.put("name", image.getUploadedBy().getName());
            uploaderInfo.put("email", image.getUploadedBy().getEmail());
            response.put("uploader", uploaderInfo);
        } else {
            // 仅保留ID信息
            response.put("uploader_id", "未知");
        }
        
        // 获取标注图像路径（如果有）
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(image.getId());
        if (!imagePairs.isEmpty() && imagePairs.get(0).getImageTwoPath() != null && !imagePairs.get(0).getImageTwoPath().isEmpty()) {
            response.put("annotated_image_path", imagePairs.get(0).getImageTwoPath());
        }
        
        // 添加创建时间
        if (image.getCreatedAt() != null) {
            response.put("created_at", image.getCreatedAt().toString());
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 将文件路径转换为URL路径
     */
    private String convertFilePathToUrl(String filePath) {
        // 这里可能需要根据实际环境配置进行调整
        // 例如，如果文件存储在特定目录，需要映射到相应的URL
        return filePath;
    }

    // 按状态获取图像
    @GetMapping("/images/status/{status}")
    public ResponseEntity<List<ImageMetadata>> getImagesByStatus(@PathVariable String status) {
        try {
            ImageMetadata.Status imageStatus = ImageMetadata.Status.valueOf(status.toUpperCase());
            List<ImageMetadata> images = imageMetadataRepository.findByStatus(imageStatus);
            return ResponseEntity.ok(images);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    // 更新图像状态
    @PutMapping("/images/{id}/status")
    public ResponseEntity<?> updateImageStatus(
            @PathVariable Long id, 
            @RequestParam String status,
            @RequestParam(required = false) Integer reviewerId,
            @RequestParam(required = false) String reviewNotes) {
        
        Optional<ImageMetadata> optionalImage = imageMetadataRepository.findById(id);
        if (!optionalImage.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        ImageMetadata image = optionalImage.get();
        try {
            ImageMetadata.Status newStatus = ImageMetadata.Status.valueOf(status.toUpperCase());
            image.setStatus(newStatus);
            
            if (reviewerId != null) {
                Optional<User> reviewer = userRepository.findById(reviewerId);
                reviewer.ifPresent(image::setReviewedBy);
            }
            
            if (reviewNotes != null && !reviewNotes.isEmpty()) {
                image.setReviewNotes(reviewNotes);
            }
            
            image.setReviewDate(LocalDateTime.now());
            imageMetadataRepository.save(image);
            
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Invalid status value");
        }
    }
    
    // 将图像标记为已标注（REVIEWED）
    @PutMapping("/images/{id}/mark-annotated")
    public ResponseEntity<?> markAsAnnotated(
            @PathVariable Long id,
            @RequestParam(required = false) String reviewTimestamp) {
        
        try {
        Optional<ImageMetadata> optionalImage = imageMetadataRepository.findById(id);
        if (!optionalImage.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        ImageMetadata image = optionalImage.get();
            
            // 先查找是否已存在ImagePair记录
            List<ImagePair> existingPairs = imagePairRepository.findByMetadataId(image.getId());
            
            ImagePair imagePair;
            if (existingPairs.isEmpty()) {
                // 不存在记录时，创建一条新的记录
                System.out.println("未找到ImagePair记录，为图像ID " + id + " 创建一条新记录");
                imagePair = new ImagePair();
                imagePair.setMetadataId(image.getId());
                imagePair.setDescription("标记为已标注时创建的记录");
            } else {
                // 存在记录时，检查是否有多条记录
                if (existingPairs.size() > 1) {
                    System.out.println("警告：发现重复的ImagePair记录，共 " + existingPairs.size() + " 条，进行清理...");
                    
                    // 按创建时间降序排序，保留最新的一条
                    existingPairs.sort((a, b) -> {
                        if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
                        if (a.getCreatedAt() == null) return 1;
                        if (b.getCreatedAt() == null) return -1;
                        return b.getCreatedAt().compareTo(a.getCreatedAt());
                    });
                    
                    // 获取最新的记录
                    imagePair = existingPairs.get(0);
                    System.out.println("保留最新记录，ID: " + imagePair.getId() + ", 创建时间: " + imagePair.getCreatedAt());
                    
                    // 删除其他记录
                    for (int i = 1; i < existingPairs.size(); i++) {
                        ImagePair pair = existingPairs.get(i);
                        System.out.println("删除重复记录，ID: " + pair.getId() + ", 创建时间: " + pair.getCreatedAt());
                        imagePairRepository.deleteById(pair.getId());
                    }
                } else {
                    // 只有一条记录，直接使用
                    imagePair = existingPairs.get(0);
                    System.out.println("使用现有ImagePair记录: ID=" + imagePair.getId());
                }
            }
            
            // 获取原始图像路径
            String originalPath = imagePair.getImageOnePath();
            if (originalPath == null || originalPath.isEmpty()) {
                // 如果原始路径为空，尝试使用图像元数据中的路径
                originalPath = image.getPath();
                if (originalPath == null || originalPath.isEmpty()) {
                    return ResponseEntity.badRequest().body("未设置原始图像路径");
                }
                imagePair.setImageOnePath(originalPath);
            }
            
            // 不再生成和保存标注图片，也不再写入image_two_path
            // 只返回状态变更结果
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "图像已成功标记为已标注",
                "image_pair_id", imagePair.getId()
            ));
        } catch (Exception e) {
            System.err.println("标记图像为已标注状态失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("标记失败: " + e.getMessage());
        }
    }
    
    // 创建新图像记录
    @PostMapping("/images")
    public ResponseEntity<?> createImage(@RequestBody ImageMetadata imageMetadata) {
        try {
            ImageMetadata savedImage = imageMetadataRepository.save(imageMetadata);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedImage);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to create image: " + e.getMessage());
        }
    }
    
    // 更新图像
    @PutMapping("/images/{id}")
    public ResponseEntity<?> updateImage(@PathVariable Long id, @RequestBody ImageMetadata imageMetadata) {
        if (!imageMetadataRepository.existsById(id)) {
            return ResponseEntity.notFound().build();
        }
        
        imageMetadata.setId(id);
        ImageMetadata updatedImage = imageMetadataRepository.save(imageMetadata);
        return ResponseEntity.ok(updatedImage);
    }
    
    // 保存结构化表单数据
    @PutMapping("/images/{id}/structured-form")
    public ResponseEntity<?> saveStructuredFormData(@PathVariable Long id, @RequestBody Map<String, Object> formData) {
        System.out.println("接收到保存结构化表单请求，图像ID: " + id);
        
        // 添加调试信息，打印关键字段
        String formAction = formData.containsKey("action") ? formData.get("action").toString() : "undefined";
        boolean hasTimestamp = formData.containsKey("timestamp");
        String timestamp = hasTimestamp ? formData.get("timestamp").toString() : "not provided";
        
        System.out.println("表单动作: " + formAction + ", 是否包含时间戳: " + hasTimestamp + 
                           (hasTimestamp ? ", 时间戳值: " + timestamp : ""));
        System.out.println("表单数据: " + formData);
        
        Optional<ImageMetadata> optionalImage = imageMetadataRepository.findById(id);
        if (!optionalImage.isPresent()) {
            System.err.println("找不到图像ID: " + id);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("找不到图像ID: " + id);
        }
        
        try {
            ImageMetadata image = optionalImage.get();
            
            // 处理基础信息
            if (formData.containsKey("location")) {
                Object locationObj = formData.get("location");
                String locationStr = locationObj != null ? locationObj.toString() : "";
                System.out.println("设置lesionLocation: " + locationStr);
                image.setLesionLocation(locationStr);
            }
            
            if (formData.containsKey("patientAge")) {
                try {
                    Object ageObj = formData.get("patientAge");
                    if (ageObj != null) {
                        Integer age = null;
                        if (ageObj instanceof Integer) {
                            age = (Integer) ageObj;
                        } else if (ageObj instanceof String) {
                            age = Integer.parseInt((String) ageObj);
                        } else if (ageObj instanceof Double) {
                            age = ((Double) ageObj).intValue();
                        }
                        
                        if (age != null) {
                            System.out.println("设置patientAge: " + age);
                            image.setPatientAge(age);
                        }
                    }
                } catch (NumberFormatException e) {
                    System.err.println("无效的年龄值: " + formData.get("patientAge") + ", 错误: " + e.getMessage());
                }
            }
            
            if (formData.containsKey("stage")) {
                Object stageObj = formData.get("stage");
                String stageStr = stageObj != null ? stageObj.toString() : "";
                System.out.println("设置diseaseStage: " + stageStr);
                image.setDiseaseStage(stageStr);
            }
            
            // 形态与临床特征
            if (formData.containsKey("morphology")) {
                Object morphologyObj = formData.get("morphology");
                String morphologyStr = morphologyObj != null ? morphologyObj.toString() : "";
                
                String morphologyDesc = "";
                if (formData.containsKey("morphologyDesc")) {
                    Object descObj = formData.get("morphologyDesc");
                    morphologyDesc = descObj != null ? descObj.toString() : "";
                }
                
                String fullMorphology = morphologyStr;
                if (!morphologyDesc.isEmpty()) {
                    fullMorphology += ": " + morphologyDesc;
                }
                
                System.out.println("设置morphologicalFeatures: " + fullMorphology);
                image.setMorphologicalFeatures(fullMorphology);
            }
            
            if (formData.containsKey("bloodFlow")) {
                Object bloodFlowObj = formData.get("bloodFlow");
                String bloodFlowStr = bloodFlowObj != null ? bloodFlowObj.toString() : "";
                System.out.println("设置bloodFlow: " + bloodFlowStr);
                image.setBloodFlow(bloodFlowStr);
            }
            
            if (formData.containsKey("symptoms")) {
                Object symptomsObj = formData.get("symptoms");
                String symptomsStr = symptomsObj != null ? symptomsObj.toString() : "";
                System.out.println("设置symptoms: " + symptomsStr);
                image.setSymptoms(symptomsStr);
                
                if (formData.containsKey("symptomsDesc")) {
                    Object descObj = formData.get("symptomsDesc");
                    String symptomsDesc = descObj != null ? descObj.toString() : "";
                    System.out.println("设置symptomDetails: " + symptomsDesc);
                    image.setSymptomDetails(symptomsDesc);
                }
            }
            
            if (formData.containsKey("complications")) {
                Object complicationsObj = formData.get("complications");
                String complicationsStr = complicationsObj != null ? complicationsObj.toString() : "";
                System.out.println("设置complications: " + complicationsStr);
                image.setComplications(complicationsStr);
                
                if (formData.containsKey("complicationsDesc")) {
                    Object descObj = formData.get("complicationsDesc");
                    String complicationsDesc = descObj != null ? descObj.toString() : "";
                    System.out.println("设置complicationDetails: " + complicationsDesc);
                    image.setComplicationDetails(complicationsDesc);
                }
            }
            
            // 诊断与治疗建议
            if (formData.containsKey("diagnosis")) {
                Object diagnosisObj = formData.get("diagnosis");
                String diagnosisStr = diagnosisObj != null ? diagnosisObj.toString() : "";
                System.out.println("设置diagnosisCategory: " + diagnosisStr);
                image.setDiagnosisCategory(diagnosisStr);
            }
            
            if (formData.containsKey("diagnosisCode")) {
                Object codeObj = formData.get("diagnosisCode");
                String codeStr = codeObj != null ? codeObj.toString() : "";
                System.out.println("设置diagnosisIcdCode: " + codeStr);
                image.setDiagnosisIcdCode(codeStr);
            }
            
            if (formData.containsKey("treatmentPriority")) {
                Object priorityObj = formData.get("treatmentPriority");
                String priorityStr = priorityObj != null ? priorityObj.toString() : "";
                System.out.println("设置treatmentPriority: " + priorityStr);
                image.setTreatmentPriority(priorityStr);
            }
            
            if (formData.containsKey("treatmentPlan")) {
                Object planObj = formData.get("treatmentPlan");
                String planStr = planObj != null ? planObj.toString() : "";
                System.out.println("设置treatmentPlan: " + planStr);
                image.setTreatmentPlan(planStr);
                
                if (formData.containsKey("treatmentDetail")) {
                    Object detailObj = formData.get("treatmentDetail");
                    String detailStr = detailObj != null ? detailObj.toString() : "";
                    System.out.println("设置recommendedTreatment: " + detailStr);
                    image.setRecommendedTreatment(detailStr);
                }
            }
            
            if (formData.containsKey("contraindications")) {
                Object contraObj = formData.get("contraindications");
                String contraStr = contraObj != null ? contraObj.toString() : "";
                System.out.println("设置contraindications: " + contraStr);
                image.setContraindications(contraStr);
            }
            
            // 随访与预后
            if (formData.containsKey("followUpPeriod")) {
                Object periodObj = formData.get("followUpPeriod");
                String periodStr = periodObj != null ? periodObj.toString() : "";
                
                if ("custom".equals(periodStr) && formData.containsKey("customFollowUp")) {
                    Object customObj = formData.get("customFollowUp");
                    String customStr = customObj != null ? customObj.toString() : "";
                    if (!customStr.isEmpty()) {
                        periodStr = customStr;
                    }
                }
                
                System.out.println("设置followUpSchedule: " + periodStr);
                image.setFollowUpSchedule(periodStr);
            }
            
            if (formData.containsKey("prognosisRating")) {
                try {
                    Object ratingObj = formData.get("prognosisRating");
                    if (ratingObj != null) {
                        Integer rating = null;
                        if (ratingObj instanceof Integer) {
                            rating = (Integer) ratingObj;
                        } else if (ratingObj instanceof String) {
                            rating = Integer.parseInt((String) ratingObj);
                        } else if (ratingObj instanceof Double) {
                            rating = ((Double) ratingObj).intValue();
                        }
                        
                        if (rating != null) {
                            System.out.println("设置prognosisRating: " + rating);
                            image.setPrognosisRating(rating);
                        }
                    }
                } catch (NumberFormatException e) {
                    System.err.println("无效的评分值: " + formData.get("prognosisRating") + ", 错误: " + e.getMessage());
                }
            }
            
            if (formData.containsKey("patientEducation")) {
                Object educationObj = formData.get("patientEducation");
                String educationStr = educationObj != null ? educationObj.toString() : "";
                
                String educationDetail = "";
                if (formData.containsKey("patientEducationDetail")) {
                    Object detailObj = formData.get("patientEducationDetail");
                    educationDetail = detailObj != null ? detailObj.toString() : "";
                }
                
                String fullEducation = educationStr;
                if (!educationDetail.isEmpty()) {
                    fullEducation += ": " + educationDetail;
                }
                
                System.out.println("设置patientEducation: " + fullEducation);
                image.setPatientEducation(fullEducation);
            }
            
            // 根据action参数决定状态
            String action = "save"; // 默认为保存
            if (formData.containsKey("action")) {
                action = formData.get("action").toString();
            }
            
            // 获取当前请求
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String userIdHeader = request.getHeader("X-User-Id");
            
            System.out.println("提交操作 - X-User-Id头: " + userIdHeader);
            // 检查所有请求头，帮助调试
            java.util.Enumeration<String> headerNames = request.getHeaderNames();
            System.out.println("所有请求头:");
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                System.out.println("  " + headerName + ": " + request.getHeader(headerName));
            }
            
            // 获取Authorization头
            String authHeader = request.getHeader("Authorization");
            
            // 优先从X-User-Role头获取角色信息
            String userRoleHeader = request.getHeader("X-User-Role");
            if (userRoleHeader != null && !userRoleHeader.isEmpty()) {
                System.out.println("检测到X-User-Role头: " + userRoleHeader);
            }
            
            // 获取用户角色
            User currentUser = null;
            
            // 优先从请求体中获取用户角色信息
            String userRoleFromBody = null;
            if (formData.containsKey("userRole")) {
                userRoleFromBody = formData.get("userRole").toString();
                System.out.println("从请求体中获取用户角色: " + userRoleFromBody);
            }
            
            // 从请求体中获取用户ID
            String userIdFromBody = null;
            if (formData.containsKey("userId")) {
                Object userIdObj = formData.get("userId");
                if (userIdObj != null) {
                    userIdFromBody = userIdObj.toString();
                    System.out.println("从请求体中获取用户ID: " + userIdFromBody);
                }
            }
            
            String userCustomIdFromBody = null;
            if (formData.containsKey("userCustomId")) {
                Object customIdObj = formData.get("userCustomId");
                if (customIdObj != null) {
                    userCustomIdFromBody = customIdObj.toString();
                    System.out.println("从请求体中获取用户自定义ID: " + userCustomIdFromBody);
                }
            }
            
            // 如果表单中包含用户角色信息，优先使用它
            if (userRoleFromBody != null && !userRoleFromBody.isEmpty()) {
                // 尝试根据用户ID或自定义ID查找用户
                String userId = userIdFromBody != null ? userIdFromBody : 
                               (userCustomIdFromBody != null ? userCustomIdFromBody : 
                               (userIdHeader != null ? userIdHeader : null));
                               
                if (userId != null) {
                    Optional<User> userOpt;
                    if (userId.matches("\\d+")) {
                        userOpt = userRepository.findById(Integer.parseInt(userId));
                    } else {
                        userOpt = userRepository.findByCustomId(userId);
                    }
                    
                    if (userOpt.isPresent()) {
                        // 创建一个临时用户对象，带有请求体中的角色
                        currentUser = new User();
                        currentUser.setId(userOpt.get().getId());
                        currentUser.setName(userOpt.get().getName());
                        currentUser.setCustomId(userOpt.get().getCustomId());
                        
                        // 根据角色设置正确的枚举值
                        if ("REVIEWER".equals(userRoleFromBody)) {
                            currentUser.setRole(User.Role.REVIEWER);
                        } else if ("ADMIN".equals(userRoleFromBody)) {
                            currentUser.setRole(User.Role.ADMIN);
                        } else if ("DOCTOR".equals(userRoleFromBody)) {
                            currentUser.setRole(User.Role.DOCTOR);
                        } else {
                            // 默认使用数据库中的角色
                            currentUser.setRole(userOpt.get().getRole());
                        }
                        
                        System.out.println("基于请求体中的角色信息创建临时用户对象，角色为: " + currentUser.getRole());
                    }
                }
            }
            
            // 如果请求体中没有角色信息，尝试从X-User-Role头获取
            if (currentUser == null && userRoleHeader != null && !userRoleHeader.isEmpty()) {
                // 尝试根据userIdHeader找到用户
                if (userIdHeader != null && !userIdHeader.isEmpty()) {
                    Optional<User> userOpt;
                    if (userIdHeader.matches("\\d+")) {
                        userOpt = userRepository.findById(Integer.parseInt(userIdHeader));
                    } else {
                        userOpt = userRepository.findByCustomId(userIdHeader);
                    }
                    
                    if (userOpt.isPresent()) {
                        // 创建一个临时用户对象，带有X-User-Role指定的角色
                        currentUser = new User();
                        currentUser.setId(userOpt.get().getId());
                        currentUser.setName(userOpt.get().getName());
                        currentUser.setCustomId(userOpt.get().getCustomId());
                        
                        // 根据角色头设置正确的角色
                        if ("REVIEWER".equals(userRoleHeader)) {
                            currentUser.setRole(User.Role.REVIEWER);
                        } else if ("ADMIN".equals(userRoleHeader)) {
                            currentUser.setRole(User.Role.ADMIN);
                        } else if ("DOCTOR".equals(userRoleHeader)) {
                            currentUser.setRole(User.Role.DOCTOR);
                        } else {
                            // 默认使用数据库中的角色
                            currentUser.setRole(userOpt.get().getRole());
                        }
                        
                        System.out.println("基于X-User-Role头创建临时用户对象，角色为: " + currentUser.getRole());
                    }
                }
            }
            
            // 如果X-User-Role头没有提供用户信息，尝试从Authorization头获取
            if (currentUser == null && authHeader != null && !authHeader.isEmpty()) {
                System.out.println("检测到Authorization头: " + authHeader);
                if (authHeader.startsWith("Bearer ")) {
                    String token = authHeader.substring("Bearer ".length());
                    System.out.println("提取的Bearer令牌: " + token);
                    
                    // 如果X-User-Id为空，尝试从令牌中提取
                    if (userIdHeader == null || userIdHeader.isEmpty()) {
                        if (token.contains("user_")) {
                            String userId = token.substring(token.indexOf("user_") + "user_".length());
                            if (userId.contains(":")) {
                                userId = userId.split(":")[0];
                            }
                            System.out.println("从令牌中提取的用户ID: " + userId);
                            userIdHeader = userId;
                        }
                    }
                    
                    // 检查令牌中是否包含角色信息
                    if (token.contains("role_")) {
                        String roleStr = null;
                        // 提取角色信息
                        int roleIndex = token.indexOf("role_");
                        if (roleIndex != -1) {
                            roleStr = token.substring(roleIndex + "role_".length());
                            if (roleStr.contains(":")) {
                                roleStr = roleStr.split(":")[0];
                            }
                            System.out.println("从令牌中提取的角色信息: " + roleStr);
                            
                            // 直接检查是否为审核医生或管理员角色
                            if ("REVIEWER".equals(roleStr) || "ADMIN".equals(roleStr)) {
                                System.out.println("令牌中检测到审核医生或管理员角色: " + roleStr);
                                // 如果角色已确认，但还没有用户对象，尝试创建临时用户对象
                                if (currentUser == null && userIdHeader != null && !userIdHeader.isEmpty()) {
                                    Optional<User> userOpt;
                                    if (userIdHeader.matches("\\d+")) {
                                        userOpt = userRepository.findById(Integer.parseInt(userIdHeader));
                                    } else {
                                        userOpt = userRepository.findByCustomId(userIdHeader);
                                    }
                                    
                                    if (userOpt.isPresent()) {
                                        currentUser = userOpt.get();
                                        // 确保角色信息与令牌一致 - 这里不修改数据库中的值，只用于此次判断
                                        if ("REVIEWER".equals(roleStr) && currentUser.getRole() != User.Role.REVIEWER) {
                                            System.out.println("警告：用户在数据库中的角色(" + currentUser.getRole() + 
                                                             ")与令牌中的角色(REVIEWER)不一致，但将使用令牌中的角色进行此次操作");
                                            // 临时设置角色为REVIEWER，不保存到数据库
                                            currentUser = new User();
                                            currentUser.setId(userOpt.get().getId());
                                            currentUser.setName(userOpt.get().getName());
                                            currentUser.setCustomId(userOpt.get().getCustomId());
                                            currentUser.setRole(User.Role.REVIEWER);
                                        } else if ("ADMIN".equals(roleStr) && currentUser.getRole() != User.Role.ADMIN) {
                                            System.out.println("警告：用户在数据库中的角色(" + currentUser.getRole() + 
                                                             ")与令牌中的角色(ADMIN)不一致，但将使用令牌中的角色进行此次操作");
                                            // 临时设置角色为ADMIN，不保存到数据库
                                            currentUser = new User();
                                            currentUser.setId(userOpt.get().getId());
                                            currentUser.setName(userOpt.get().getName());
                                            currentUser.setCustomId(userOpt.get().getCustomId());
                                            currentUser.setRole(User.Role.ADMIN);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 从URL参数中检查角色信息
            String roleParam = request.getParameter("_role");
            if (roleParam != null && !roleParam.isEmpty()) {
                System.out.println("从URL参数中检测到角色信息: " + roleParam);
                // 直接检查是否为审核医生或管理员角色
                if ("REVIEWER".equals(roleParam) || "ADMIN".equals(roleParam)) {
                    System.out.println("URL参数中检测到审核医生或管理员角色: " + roleParam);
                    
                    // 如果角色已确认，但还没有用户对象，尝试查找用户
                    if (currentUser == null && userIdHeader != null && !userIdHeader.isEmpty()) {
                        Optional<User> userOpt;
                        if (userIdHeader.matches("\\d+")) {
                            userOpt = userRepository.findById(Integer.parseInt(userIdHeader));
                        } else {
                            userOpt = userRepository.findByCustomId(userIdHeader);
                        }
                        
                        if (userOpt.isPresent()) {
                            // 临时创建具有正确角色的用户对象
                            currentUser = new User();
                            currentUser.setId(userOpt.get().getId());
                            currentUser.setName(userOpt.get().getName());
                            currentUser.setCustomId(userOpt.get().getCustomId());
                            currentUser.setRole("REVIEWER".equals(roleParam) ? User.Role.REVIEWER : User.Role.ADMIN);
                            System.out.println("基于URL参数创建了临时用户对象，角色为: " + currentUser.getRole());
                        }
                    }
                }
            }
            
            // 特殊处理李审核账号 - 保证这个账号总是被识别为审核医生
            if (currentUser == null && (
                (userIdHeader != null && "300000001".equals(userIdHeader)) || 
                (userCustomIdFromBody != null && "300000001".equals(userCustomIdFromBody)) ||
                (userIdFromBody != null && "3".equals(userIdFromBody)) // 数据库ID
            )) {
                System.out.println("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                System.out.println("检测到特殊用户ID 300000001 (李审核)，确保识别为审核医生");
                System.out.println("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                
                // 尝试查找李审核账号
                Optional<User> userOpt = userRepository.findByCustomId("300000001");
                if (userOpt.isPresent()) {
                    currentUser = userOpt.get();
                    // 确保角色设置为REVIEWER
                    if (currentUser.getRole() != User.Role.REVIEWER) {
                        System.out.println("警告：李审核账号角色不是REVIEWER，临时设置为REVIEWER");
                        // 创建一个临时用户对象，不修改数据库
                        User tempUser = new User();
                        tempUser.setId(currentUser.getId());
                        tempUser.setName(currentUser.getName());
                        tempUser.setCustomId(currentUser.getCustomId());
                        tempUser.setRole(User.Role.REVIEWER);
                        currentUser = tempUser;
                    }
                    
                    System.out.println("成功识别李审核账号，角色被设置为: " + currentUser.getRole());
                } else {
                    // 如果找不到，创建一个临时用户对象
                    currentUser = new User();
                    currentUser.setId(3); // 假设ID为3
                    currentUser.setName("李审核");
                    currentUser.setCustomId("300000001");
                    currentUser.setRole(User.Role.REVIEWER);
                    System.out.println("创建了临时李审核用户对象，角色为REVIEWER");
                }
            }
            
            // 设置状态
            if ("submit".equals(action)) {
                // 根据用户角色设置不同的状态
                if (currentUser != null && (currentUser.getRole() == User.Role.REVIEWER || currentUser.getRole() == User.Role.ADMIN)) {
                    // 审核医生或管理员提交 - 设置为已完成状态
                    image.setStatus(ImageMetadata.Status.APPROVED);
                    System.out.println("================= 审核状态处理 =================");
                    System.out.println("审核医生或管理员提交，更新状态为: APPROVED (已完成)");
                    System.out.println("用户角色: " + currentUser.getRole() + ", 用户ID: " + currentUser.getId() + ", 自定义ID: " + currentUser.getCustomId());
                    System.out.println("用户名: " + currentUser.getName());
                    System.out.println("============================================");
                    
                    // 同时设置审核人为当前用户
                    image.setReviewedBy(currentUser);
                    
                    // 使用前端传递的时间戳（如果有）
                    if (formData.containsKey("timestamp")) {
                        try {
                            String timestampStr = formData.get("timestamp").toString();
                            // 解析ISO-8601格式的时间戳
                            LocalDateTime reviewDate = LocalDateTime.parse(timestampStr);
                            image.setReviewDate(reviewDate);
                            System.out.println("使用前端传递的时间戳: " + timestampStr);
                        } catch (Exception e) {
                            System.err.println("解析前端时间戳失败，使用中国时区的当前时间: " + e.getMessage());
                            // 使用中国时区的当前时间
                            ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
                            LocalDateTime reviewDate = chinaTime.toLocalDateTime();
                            image.setReviewDate(reviewDate);
                            System.out.println("设置审核时间(中国时区): " + reviewDate);
                        }
                    } else {
                        // 使用中国时区的当前时间
                        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
                        LocalDateTime reviewDate = chinaTime.toLocalDateTime();
                        image.setReviewDate(reviewDate);
                        System.out.println("设置审核时间(中国时区): " + reviewDate);
                    }
                    
                    System.out.println("设置审核人: " + currentUser.getName());
                } else {
                    // 普通医生提交 - 设置为待审核状态
            image.setStatus(ImageMetadata.Status.SUBMITTED);
                    System.out.println("普通医生提交，更新状态为: SUBMITTED (已提交/待审核)");
                    if (currentUser != null) {
                        System.out.println("用户角色: " + currentUser.getRole() + ", 用户ID: " + currentUser.getId() + ", 自定义ID: " + currentUser.getCustomId());
                    } else {
                        System.out.println("警告：未能识别当前用户，使用默认状态");
                    }
                }
            } else {
                // 保存操作 - 设置为已标注
                image.setStatus(ImageMetadata.Status.REVIEWED);
                System.out.println("用户点击保存并退出，更新状态为: REVIEWED (已标注)");
            }
            
            // 设置提交时间
            // 使用前端传递的时间戳（如果有）
            if (formData.containsKey("timestamp")) {
                try {
                    String timestampStr = formData.get("timestamp").toString();
                    // 解析ISO-8601格式的时间戳
                    LocalDateTime submittedAt = LocalDateTime.parse(timestampStr);
                    image.setSubmittedAt(submittedAt);
                    System.out.println("使用前端传递的时间戳作为提交时间: " + timestampStr);
                } catch (Exception e) {
                    System.err.println("解析前端时间戳失败，使用中国时区的当前时间: " + e.getMessage());
                    // 使用中国时区的当前时间
                    ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
                    LocalDateTime now = chinaTime.toLocalDateTime();
            image.setSubmittedAt(now);
                    System.out.println("设置提交时间(中国时区): " + now);
                }
            } else {
                // 使用中国时区的当前时间
                ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
                LocalDateTime now = chinaTime.toLocalDateTime();
                image.setSubmittedAt(now);
                System.out.println("设置提交时间(中国时区): " + now);
            }
            
            // 设置提交人(如果有当前用户信息)
            if (userIdHeader != null && !userIdHeader.isEmpty()) {
                try {
                    // 尝试解析用户ID并查找用户
                    User submittedByUser = null;
                    if (userIdHeader.matches("\\d+")) {
                        // 如果是数字ID，按ID查找
                        Integer userId = Integer.parseInt(userIdHeader);
                        Optional<User> userOpt = userRepository.findById(userId);
                        if (userOpt.isPresent()) {
                            submittedByUser = userOpt.get();
                        }
                    } else {
                        // 如果不是数字ID，尝试按自定义ID查找
                        Optional<User> userOpt = userRepository.findByCustomId(userIdHeader);
                        if (userOpt.isPresent()) {
                            submittedByUser = userOpt.get();
                        }
                    }
                    
                    if (submittedByUser != null) {
                        image.setSubmittedBy(submittedByUser);
                        System.out.println("设置提交人: " + submittedByUser.getName());
                    } else {
                        // 如果找不到提交用户，使用上传用户作为提交用户
                        if (image.getUploadedBy() != null) {
                            image.setSubmittedBy(image.getUploadedBy());
                            System.out.println("找不到提交用户，使用上传用户作为提交用户: " + 
                                (image.getUploadedBy().getName() != null ? image.getUploadedBy().getName() : "ID: " + image.getUploadedBy().getId()));
                        } else {
                            // 尝试查找默认用户作为提交人
                            Optional<User> defaultUser = userRepository.findById(1);
                            if (defaultUser.isPresent()) {
                                image.setSubmittedBy(defaultUser.get());
                                System.out.println("使用默认用户作为提交人: " + defaultUser.get().getName());
                            } else {
                                System.err.println("警告: 无法设置提交人，没有默认用户");
                            }
                        }
                    }
                } catch (Exception e) {
                    // 如果解析用户ID失败，使用上传用户作为提交用户
                    if (image.getUploadedBy() != null) {
                        image.setSubmittedBy(image.getUploadedBy());
                        System.out.println("解析用户ID失败，使用上传用户作为提交用户: " + 
                            (image.getUploadedBy().getName() != null ? image.getUploadedBy().getName() : "ID: " + image.getUploadedBy().getId()));
                    } else {
                        // 尝试查找默认用户作为提交人
                        Optional<User> defaultUser = userRepository.findById(1);
                        if (defaultUser.isPresent()) {
                            image.setSubmittedBy(defaultUser.get());
                            System.out.println("使用默认用户作为提交人: " + defaultUser.get().getName());
                        } else {
                            System.err.println("警告: 无法设置提交人，没有默认用户");
                        }
                    }
                    System.err.println("解析用户ID失败: " + e.getMessage());
                }
            } else {
                // 如果没有用户ID头，使用上传用户作为提交用户
                if (image.getUploadedBy() != null) {
                    image.setSubmittedBy(image.getUploadedBy());
                    System.out.println("没有用户ID头，使用上传用户作为提交用户: " + 
                        (image.getUploadedBy().getName() != null ? image.getUploadedBy().getName() : "ID: " + image.getUploadedBy().getId()));
                } else {
                    // 尝试查找默认用户作为提交人
                    Optional<User> defaultUser = userRepository.findById(1);
                    if (defaultUser.isPresent()) {
                        image.setSubmittedBy(defaultUser.get());
                        System.out.println("使用默认用户作为提交人: " + defaultUser.get().getName());
                    } else {
                        System.err.println("警告: 无法设置提交人，没有默认用户");
                    }
                }
            }
            
            // 保存到数据库
            ImageMetadata updatedImage = imageMetadataRepository.save(image);
            System.out.println("表单数据保存成功，更新的图像ID: " + updatedImage.getId());
            System.out.println("状态: " + updatedImage.getStatus() + 
                              ", 提交时间: " + updatedImage.getSubmittedAt() + 
                              ", 提交人: " + (updatedImage.getSubmittedBy() != null ? 
                                  updatedImage.getSubmittedBy().getName() : "未设置"));
            
            // 返回成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("id", updatedImage.getId());
            response.put("message", "结构化表单数据保存成功");
            response.put("status", updatedImage.getStatus().toString());
            response.put("submittedAt", updatedImage.getSubmittedAt());
            if (updatedImage.getSubmittedBy() != null) {
                response.put("submittedBy", updatedImage.getSubmittedBy().getId());
                response.put("submittedByName", updatedImage.getSubmittedBy().getName());
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("保存结构化表单数据失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("保存结构化表单数据失败: " + e.getMessage());
        }
    }
    
    // 删除图像
    @DeleteMapping("/images/{id}")
    public ResponseEntity<?> deleteImage(@PathVariable Long id) {
        Optional<ImageMetadata> optionalImage = imageMetadataRepository.findById(id);
        if (!optionalImage.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        try {
            // 先删除关联的标签，然后删除图像元数据
            tagRepository.deleteByMetadataId(id);
            imageMetadataRepository.deleteById(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            e.printStackTrace();
            
            // 检查错误类型并提供更具体的错误信息
            String errorMessage = "删除图像失败 (ID: " + id + "): ";
            String rootCause = getRootCauseMessage(e);
            
            // 检查是否是外键约束违反错误
            if (rootCause.contains("foreign key constraint") || rootCause.contains("a foreign key constraint fails")) {
                errorMessage += "此图像有关联的数据记录，请先删除关联数据。";
                errorMessage += " 技术细节: " + rootCause;
            } else {
                errorMessage += rootCause;
            }
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMessage);
        }
    }
    
    /**
     * 获取异常的根本原因消息
     */
    private String getRootCauseMessage(Throwable throwable) {
        Throwable cause = throwable;
        String message = throwable.getMessage();
        while (cause.getCause() != null) {
            cause = cause.getCause();
            if (cause.getMessage() != null && !cause.getMessage().isEmpty()) {
                message = cause.getMessage();
            }
        }
        return message;
    }
    
    // 获取统计数据
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        Map<String, Object> stats = new HashMap<>();
        
        long totalCount = imageMetadataRepository.count();
        long draftCount = imageMetadataRepository.findByStatus(ImageMetadata.Status.DRAFT).size();
        long submittedCount = imageMetadataRepository.findByStatus(ImageMetadata.Status.SUBMITTED).size();
        long approvedCount = imageMetadataRepository.findByStatus(ImageMetadata.Status.APPROVED).size();
        long rejectedCount = imageMetadataRepository.findByStatus(ImageMetadata.Status.REJECTED).size();
        
        stats.put("totalCount", totalCount);
        stats.put("draftCount", draftCount);
        stats.put("submittedCount", submittedCount);
        stats.put("approvedCount", approvedCount);
        stats.put("rejectedCount", rejectedCount);
        
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/images/my-images")
    public ResponseEntity<List<Map<String, Object>>> getUserImages(
            HttpServletRequest request,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String userId) {
        System.out.println("接收到获取用户图像请求，状态过滤: " + status + ", 用户ID参数: " + userId);
        
        // 如果前端直接传递了userId参数，优先使用
        if (userId != null && !userId.isEmpty()) {
            System.out.println("使用前端传递的用户ID参数: " + userId);
        } else {
            // 否则尝试从请求头中获取用户信息
        String userIdHeader = request.getHeader("X-User-Id");
        String userEmail = request.getHeader("X-User-Email");
        
        // 尝试从请求头获取用户ID
        if (userIdHeader != null && !userIdHeader.isEmpty()) {
            userId = userIdHeader;
            System.out.println("从请求头获取到用户ID: " + userId);
        } else {
            // 尝试从认证信息获取
            String authHeader = request.getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer user_")) {
                    String userInfo = authHeader.substring("Bearer user_".length());
                    if (userInfo.contains(":")) {
                        userId = userInfo.split(":")[0];
                    } else {
                        userId = userInfo;
                    }
                    System.out.println("从Authorization头获取到用户ID: " + userId);
                } else if (authHeader != null && authHeader.startsWith("Basic ")) {
                try {
                    String base64Credentials = authHeader.substring("Basic ".length());
                    byte[] credDecoded = java.util.Base64.getDecoder().decode(base64Credentials);
                    String credentials = new String(credDecoded, java.nio.charset.StandardCharsets.UTF_8);
                    // 凭据格式是 "用户名:密码"
                    final String[] values = credentials.split(":", 2);
                    String username = values[0];
                    
                    // 查找用户ID
                    Optional<User> user = userRepository.findByEmail(username);
                    if (user.isPresent()) {
                        userId = user.get().getCustomId();
                        System.out.println("通过认证头获取到用户CustomID: " + userId);
                    }
                } catch (Exception e) {
                    System.err.println("解析认证头失败: " + e.getMessage());
                }
            }
            }
        }
        
        // 如果无法识别用户，返回空列表
        if (userId == null || userId.isEmpty()) {
            System.err.println("无法识别用户ID，返回空列表");
            return ResponseEntity.ok(new ArrayList<>());
        }
        
        try {
            List<ImageMetadata> images = new ArrayList<>();
            
            // 先通过custom_id查找用户的实际ID
            Optional<User> userOpt = userRepository.findByCustomId(userId);
            if (!userOpt.isPresent()) {
                System.err.println("找不到custom_id为 " + userId + " 的用户，返回空列表");
                return ResponseEntity.ok(new ArrayList<>());
            }
            
            // 获取用户的实际ID
            Integer userNumericId = userOpt.get().getId();
            System.out.println("找到用户: " + userOpt.get().getName() + "，数据库ID: " + userNumericId);
            
            // 使用用户实际ID构建查询条件
            String condition = "uploaded_by = ?";
            Object[] params = new Object[] { userNumericId };
            System.out.println("使用用户数据库ID条件查询: " + condition + ", 参数: " + userNumericId);
            
            // 根据状态参数构建SQL查询
            String sql = "SELECT * FROM image_metadata WHERE " + condition;
            
            if (status != null && !status.isEmpty() && !status.equals("ALL")) {
                switch (status) {
                    case "SUBMITTED":
                        sql = "SELECT im.* FROM image_metadata im " +
                              "JOIN image_pairs ip ON im.id = ip.metadata_id " +
                              "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                              "AND ip.image_two_path IS NOT NULL";
                        break;
                    case "PENDING":
                        sql = "SELECT im.* FROM image_metadata im " +
                              "JOIN image_pairs ip ON im.id = ip.metadata_id " +
                              "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                              "AND ip.image_two_path IS NOT NULL " +
                              "AND im.reviewed_by IS NULL";
                        break;
                    case "APPROVED":
                        sql = "SELECT im.* FROM image_metadata im " +
                              "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                              "AND im.reviewed_by IS NOT NULL";
                        break;
                    case "DRAFT":
                        sql = "SELECT im.* FROM image_metadata im " +
                              "LEFT JOIN image_pairs ip ON im.id = ip.metadata_id " +
                              "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                              "AND (ip.image_two_path IS NULL OR ip.image_two_path = '')";
                        break;
                }
            }
            
            System.out.println("执行SQL查询: " + sql);
            System.out.println("参数: userId = " + userNumericId);
            
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql, params);
            System.out.println("查询结果行数: " + rows.size());
            
            // 转换为Map，将id替换为formatted_id
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> row : rows) {
                Map<String, Object> map = new HashMap<>();
                
                // 设置ID
                if (row.get("id") != null) {
                    Long id = ((Number) row.get("id")).longValue();
                    map.put("id", id);
                }
                
                // 设置基本信息
                map.put("filename", row.get("filename"));
                map.put("originalName", row.get("original_name"));
                map.put("mimetype", row.get("mimetype"));
                
                // 设置患者信息
                map.put("patientName", row.get("patient_name"));
                map.put("caseNumber", row.get("case_number"));
                map.put("lesionLocation", row.get("lesion_location"));
                map.put("diagnosisCategory", row.get("diagnosis_category"));
                
                // 状态处理
                String statusStr = (String) row.get("status");
                map.put("status", statusStr);
                
                // 直接从数据库行获取创建时间
                Object createdAt = row.get("created_at");
                if (createdAt != null) {
                    map.put("created_at", createdAt);
                    map.put("createdAt", createdAt);
                    System.out.println("图像ID " + map.get("id") + " 的创建时间: " + createdAt);
                } else {
                    System.out.println("警告: 图像ID " + map.get("id") + " 的创建时间为null");
                }
                
                // 其他字段
                map.put("uploadedBy", row.get("uploaded_by"));
                map.put("reviewedBy", row.get("reviewed_by"));
                
                result.add(map);
            }
            
            // 打印结果中的创建时间字段
            for (int i = 0; i < Math.min(3, result.size()); i++) {
                Map<String, Object> item = result.get(i);
                System.out.println("结果中的第 " + (i+1) + " 条记录:");
                System.out.println("  ID: " + item.get("id"));
                System.out.println("  created_at: " + item.get("created_at"));
                System.out.println("  createdAt: " + item.get("createdAt"));
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            System.err.println("获取用户图像失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ArrayList<>());
        }
    }

    // 获取用户仪表盘统计数据 - 新V2版本，支持团队数据
    @GetMapping("/stats-v2/dashboard/{userId}")
    public ResponseEntity<Map<String, Object>> getDashboardStatsV2(@PathVariable String userId) {
        System.out.println("接收到V2仪表盘统计请求，用户ID: " + userId);
        
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 1. 确认用户ID是否存在
            Optional<User> userOptional = userRepository.findByCustomId(userId);
            if (!userOptional.isPresent()) {
                System.err.println("找不到ID为 " + userId + " 的用户，返回默认统计数据");
                return ResponseEntity.ok(getDefaultStats());
            }
            
            User user = userOptional.get();
            System.out.println("找到用户: " + user.getName() + ", 角色: " + user.getRole());
            
            // 2. 如果用户是普通医生，获取个人统计数据
            if (user.getRole() == User.Role.DOCTOR) {
                // 查询用户自己上传的病例统计
                stats = getUserPersonalStats(userId);
                System.out.println("返回医生个人统计数据: " + stats);
            } 
            // 3. 如果用户是审核医生或管理员，获取团队统计数据
            else if (user.getRole() == User.Role.REVIEWER || user.getRole() == User.Role.ADMIN) {
                // 查询用户所在团队的病例统计
                if (user.getTeam() != null) {
                    stats = getTeamStats(user.getTeam().getId());
                    System.out.println("返回用户所在团队统计数据: " + stats);
                } else {
                    // 如果没有团队，返回全局统计数据
                    stats = getGlobalStats();
                    System.out.println("返回全局统计数据: " + stats);
                }
            } else {
                // 其他角色返回默认统计
                stats = getDefaultStats();
                System.out.println("返回默认统计数据: " + stats);
            }
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            System.err.println("获取V2仪表盘统计数据失败: " + e.getMessage());
            e.printStackTrace();
            
            // 返回默认统计数据
            return ResponseEntity.ok(getDefaultStats());
        }
    }
    
    // 获取用户的个人统计
    private Map<String, Object> getUserPersonalStats(String userId) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 根据custom_id查找用户
            System.out.println("获取用户个人统计数据，用户ID: " + userId);
            Optional<User> userOpt = userRepository.findByCustomId(userId);
            
            if (!userOpt.isPresent()) {
                System.out.println("未找到用户ID为 " + userId + " 的用户");
                return getDefaultStats();
            }
            
            User user = userOpt.get();
            Integer userNumericId = user.getId();
            System.out.println("找到用户: " + user.getName() + "，数据库ID: " + userNumericId);
            
            // 使用用户实际数据库ID进行查询，而不是custom_id
            String condition = "uploaded_by = ?";
            System.out.println("使用用户数据库ID条件查询: " + condition + ", 参数: " + userNumericId);
            
            // 总数
            String countSql = "SELECT COUNT(*) FROM image_metadata im " +
                    "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " ";
            Integer totalCount = jdbcTemplate.queryForObject(countSql, Integer.class, userNumericId);
            System.out.println("用户上传的图像总数: " + totalCount);
            
            // 各状态数量
            String draftCountSql = "SELECT COUNT(*) FROM image_metadata im " +
                    "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                    "AND im.status = 'DRAFT'";
            Integer draftCount = jdbcTemplate.queryForObject(draftCountSql, Integer.class, userNumericId);
            
            String submittedCountSql = "SELECT COUNT(*) FROM image_metadata im " +
                    "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                    "AND im.status = 'SUBMITTED'";
            Integer submittedCount = jdbcTemplate.queryForObject(submittedCountSql, Integer.class, userNumericId);
            
            String reviewedCountSql = "SELECT COUNT(*) FROM image_metadata im " +
                    "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                    "AND im.status = 'REVIEWED'";
            Integer reviewedCount = jdbcTemplate.queryForObject(reviewedCountSql, Integer.class, userNumericId);
            
            String rejectedCountSql = "SELECT COUNT(*) FROM image_metadata im " +
                    "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                    "AND im.status = 'REJECTED'";
            Integer rejectedCount = jdbcTemplate.queryForObject(rejectedCountSql, Integer.class, userNumericId);
            
            String approvedCountSql = "SELECT COUNT(*) FROM image_metadata im " +
                    "WHERE " + condition.replace("uploaded_by", "im.uploaded_by") + " " +
                    "AND im.status = 'APPROVED'";
            Integer approvedCount = jdbcTemplate.queryForObject(approvedCountSql, Integer.class, userNumericId);
            
            // 构建响应数据
            stats.put("totalCount", totalCount);
            stats.put("draftCount", draftCount);
            stats.put("submittedCount", submittedCount); 
            stats.put("reviewedCount", reviewedCount);
            stats.put("rejectedCount", rejectedCount);
            stats.put("approvedCount", approvedCount);
            stats.put("userId", userId);
                stats.put("userName", user.getName());
            stats.put("dataSource", "personal");
            
            // 添加团队信息
            if (user.getTeam() != null) {
                stats.put("teamId", user.getTeam().getId());
                stats.put("teamName", user.getTeam().getName());
            }
            
            System.out.println("返回用户个人统计数据: " + stats);
            return stats;
            
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("获取用户统计数据出错: " + e.getMessage());
            return getDefaultStats();
        }
    }
    
    // 获取团队统计
    private Map<String, Object> getTeamStats(Integer teamId) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 查询团队的病例总数
            String totalSql = "SELECT COUNT(*) FROM image_metadata WHERE team_id = ?";
            Integer totalCount = jdbcTemplate.queryForObject(totalSql, Integer.class, teamId);
            totalCount = (totalCount != null) ? totalCount : 0;
            
            // 查询各状态的记录数
            String draftSql = "SELECT COUNT(*) FROM image_metadata WHERE team_id = ? AND status = 'DRAFT'";
            Integer draftCount = jdbcTemplate.queryForObject(draftSql, Integer.class, teamId);
            draftCount = (draftCount != null) ? draftCount : 0;
            
            String reviewedSql = "SELECT COUNT(*) FROM image_metadata WHERE team_id = ? AND status = 'REVIEWED'";
            Integer reviewedCount = jdbcTemplate.queryForObject(reviewedSql, Integer.class, teamId);
            reviewedCount = (reviewedCount != null) ? reviewedCount : 0;
            
            String submittedSql = "SELECT COUNT(*) FROM image_metadata WHERE team_id = ? AND status = 'SUBMITTED'";
            Integer submittedCount = jdbcTemplate.queryForObject(submittedSql, Integer.class, teamId);
            submittedCount = (submittedCount != null) ? submittedCount : 0;
            
            String approvedSql = "SELECT COUNT(*) FROM image_metadata WHERE team_id = ? AND status = 'APPROVED'";
            Integer approvedCount = jdbcTemplate.queryForObject(approvedSql, Integer.class, teamId);
            approvedCount = (approvedCount != null) ? approvedCount : 0;
            
            String rejectedSql = "SELECT COUNT(*) FROM image_metadata WHERE team_id = ? AND status = 'REJECTED'";
            Integer rejectedCount = jdbcTemplate.queryForObject(rejectedSql, Integer.class, teamId);
            rejectedCount = (rejectedCount != null) ? rejectedCount : 0;
            
            stats.put("totalCount", totalCount);
            stats.put("draftCount", draftCount);
            stats.put("reviewedCount", reviewedCount);
            stats.put("submittedCount", submittedCount);
            stats.put("approvedCount", approvedCount);
            stats.put("rejectedCount", rejectedCount);
            stats.put("dataSource", "team");
            stats.put("teamId", teamId);
            
            // 获取团队名称
            try {
                Optional<Team> teamOpt = teamRepository.findById(teamId);
                if (teamOpt.isPresent()) {
                    stats.put("teamName", teamOpt.get().getName());
                }
            } catch (Exception e) {
                System.err.println("获取团队名称失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("获取团队统计失败: " + e.getMessage());
            e.printStackTrace();
            return getDefaultStats();
        }
        
        return stats;
    }
    
    // 获取全局统计数据
    private Map<String, Object> getGlobalStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 查询病例总数
            String totalSql = "SELECT COUNT(*) FROM image_metadata";
            Integer totalCount = jdbcTemplate.queryForObject(totalSql, Integer.class);
            totalCount = (totalCount != null) ? totalCount : 0;
            
            // 查询各状态的记录数
            String draftSql = "SELECT COUNT(*) FROM image_metadata WHERE status = 'DRAFT'";
            Integer draftCount = jdbcTemplate.queryForObject(draftSql, Integer.class);
            draftCount = (draftCount != null) ? draftCount : 0;
            
            String reviewedSql = "SELECT COUNT(*) FROM image_metadata WHERE status = 'REVIEWED'";
            Integer reviewedCount = jdbcTemplate.queryForObject(reviewedSql, Integer.class);
            reviewedCount = (reviewedCount != null) ? reviewedCount : 0;
            
            String submittedSql = "SELECT COUNT(*) FROM image_metadata WHERE status = 'SUBMITTED'";
            Integer submittedCount = jdbcTemplate.queryForObject(submittedSql, Integer.class);
            submittedCount = (submittedCount != null) ? submittedCount : 0;
            
            String approvedSql = "SELECT COUNT(*) FROM image_metadata WHERE status = 'APPROVED'";
            Integer approvedCount = jdbcTemplate.queryForObject(approvedSql, Integer.class);
            approvedCount = (approvedCount != null) ? approvedCount : 0;
            
            String rejectedSql = "SELECT COUNT(*) FROM image_metadata WHERE status = 'REJECTED'";
            Integer rejectedCount = jdbcTemplate.queryForObject(rejectedSql, Integer.class);
            rejectedCount = (rejectedCount != null) ? rejectedCount : 0;
            
            stats.put("totalCount", totalCount);
            stats.put("draftCount", draftCount);
            stats.put("reviewedCount", reviewedCount);
            stats.put("submittedCount", submittedCount);
            stats.put("approvedCount", approvedCount);
            stats.put("rejectedCount", rejectedCount);
            stats.put("dataSource", "global");
        } catch (Exception e) {
            System.err.println("获取全局统计失败: " + e.getMessage());
            e.printStackTrace();
            return getDefaultStats();
        }
        
        return stats;
    }
    
    // 默认统计数据
    private Map<String, Object> getDefaultStats() {
        Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", 0);
            stats.put("draftCount", 0);
            stats.put("reviewedCount", 0);
            stats.put("submittedCount", 0);
            stats.put("approvedCount", 0);
            stats.put("rejectedCount", 0);
        stats.put("dataSource", "default");
        stats.put("isDefault", true);
        return stats;
    }
    
    // 调试用：显示所有已上传图像的ID
    @GetMapping("/debug/image-ids")
    public ResponseEntity<List<Map<String, Object>>> getAllImageIds() {
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            // 修改SQL查询，不再使用uploaded_by_custom_id
            // 改为关联查询users表获取custom_id
            String sql = "SELECT im.*, u.custom_id as uploader_custom_id " +
                        "FROM image_metadata im " +
                        "LEFT JOIN users u ON im.uploaded_by = u.id";
            
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
            
            for (Map<String, Object> row : rows) {
                Map<String, Object> item = new HashMap<>();
                
                Long id = ((Number) row.get("id")).longValue();
                String formattedId = (String) row.get("formatted_id");
                String filename = (String) row.get("filename");
                String status = (String) row.get("status");
                
                // 使用关联查询得到的uploader_custom_id
                String uploaderId = (String) row.get("uploader_custom_id");
                
                item.put("id", id);
                item.put("formatted_id", formattedId);
                item.put("filename", filename);
                item.put("status", status);
                item.put("uploader_id", uploaderId);
                
                result.add(item);
        }
        
        return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            result.add(error);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 测试格式化ID功能 - 查询图像
     */
    @GetMapping("/test/find-image-by-formatted-id/{formattedId}")
    public ResponseEntity<?> testFindImageByFormattedId(@PathVariable String formattedId) {
        System.out.println("测试按格式化ID查询图像: " + formattedId);
        
        // 尝试按格式化ID查找
        Optional<ImageMetadata> imageByFormatted = imageMetadataRepository.findByFormattedId(formattedId);
        if (imageByFormatted.isPresent()) {
            System.out.println("成功通过格式化ID找到图像: " + formattedId);
            ImageMetadata image = imageByFormatted.get();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "通过格式化ID找到图像");
            response.put("image", image);
            response.put("id", image.getId());
            response.put("formattedId", image.getFormattedId());
            response.put("path", image.getPath());
            
            return ResponseEntity.ok(response);
        }
        
        // 尝试转换为普通ID并查找
        try {
            // 处理前导零
            String idWithoutLeadingZeros = formattedId.replaceFirst("^0+", "");
            if (idWithoutLeadingZeros.isEmpty()) {
                idWithoutLeadingZeros = "0";
            }
            long numericId = Long.parseLong(idWithoutLeadingZeros);
            Optional<ImageMetadata> imageById = imageMetadataRepository.findById(numericId);
            
            if (imageById.isPresent()) {
                System.out.println("通过转换后的普通ID找到图像: " + numericId);
                ImageMetadata image = imageById.get();
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "通过普通ID找到图像");
                response.put("image", image);
                response.put("id", image.getId());
                response.put("formattedId", image.getFormattedId());
                response.put("path", image.getPath());
                
                return ResponseEntity.ok(response);
            }
        } catch (NumberFormatException e) {
            System.out.println("格式化ID不能转换为数字: " + formattedId);
        }
        
        // 查找所有图像并返回基本信息
        List<ImageMetadata> allImages = imageMetadataRepository.findAll();
        List<Map<String, Object>> simplifiedImages = allImages.stream()
            .map(img -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", img.getId());
                map.put("formattedId", img.getFormattedId());
                map.put("filename", img.getFilename());
                return map;
            })
            .collect(Collectors.toList());
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "找不到指定格式化ID的图像");
        response.put("formattedId", formattedId);
        response.put("allImages", simplifiedImages);
        
        return ResponseEntity.ok(response);
    }

    // 获取用户所在团队的所有图像
    @GetMapping("/images/team-images")
    public ResponseEntity<List<ImageMetadata>> getTeamImages(
            HttpServletRequest request,
            @RequestParam(required = false) String userId) {
        
        try {
            // 获取用户所在团队的所有图像
        if (userId == null || userId.isEmpty()) {
                // 尝试从请求中获取用户ID
                userId = (String) request.getAttribute("userId");
                if (userId == null || userId.isEmpty()) {
                    return ResponseEntity.badRequest().body(null);
                }
            }
            
            // 先查找用户
            User user = userRepository.findByCustomId(userId).orElse(null);
            if (user == null) {
                return ResponseEntity.badRequest().body(null);
            }
            
            // 获取用户所在团队
            Team team = user.getTeam();
            if (team == null) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            
            // 查询团队所有成员上传的图像
            Integer teamId = team.getId();
            List<User> teamMembers = userRepository.findByTeam_Id(teamId);
            
            // 构建查询条件
            if (teamMembers.isEmpty()) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            
            // 使用JPA查询关联表获取团队图像
            // 构建查询条件：查找团队成员上传的图像
            List<Integer> memberIds = teamMembers.stream()
                    .map(User::getId)
                    .collect(Collectors.toList());
            
            // 使用JDBC查询直接获取图像并转换为对象
            // 修改：构建关联查询获取图像
            StringBuilder sqlBuilder = new StringBuilder(
                    "SELECT im.* FROM image_metadata im " +
                    "JOIN users u ON im.uploaded_by = u.id " +
                    "WHERE u.team_id = ?");
            
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sqlBuilder.toString(), teamId);
            
            // 转换为对象
            List<ImageMetadata> images = new ArrayList<>();
            for (Map<String, Object> row : rows) {
                ImageMetadata img = convertRowToImageMetadata(row);
                images.add(img);
            }
            
            // 按创建时间降序排序
            images.sort((a, b) -> {
                if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
                if (a.getCreatedAt() == null) return 1;
                if (b.getCreatedAt() == null) return -1;
                return b.getCreatedAt().compareTo(a.getCreatedAt());
            });
            
            return ResponseEntity.ok(images);
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    // 辅助方法：将数据库行转换为ImageMetadata对象
    private ImageMetadata convertRowToImageMetadata(Map<String, Object> row) {
        ImageMetadata metadata = new ImageMetadata();
        
        // 设置基本属性
        metadata.setId(((Number) row.get("id")).longValue());
        
        if (row.get("formatted_id") != null) {
            metadata.setFormattedId((String) row.get("formatted_id"));
        }
        
        metadata.setFilename((String) row.get("filename"));
        metadata.setOriginalName((String) row.get("original_name"));
        metadata.setMimetype((String) row.get("mimetype"));
        metadata.setSize(((Number) row.get("size")).intValue());
        
        if (row.get("width") != null) {
            metadata.setWidth(((Number) row.get("width")).intValue());
        }
        
        if (row.get("height") != null) {
            metadata.setHeight(((Number) row.get("height")).intValue());
        }
        
        // 状态
        if (row.get("status") != null) {
            metadata.setStatus(ImageMetadata.Status.valueOf((String) row.get("status")));
        }
        
        // 时间字段
        if (row.get("created_at") != null) {
            metadata.setCreatedAt((LocalDateTime) row.get("created_at"));
        }
        
        if (row.get("updated_at") != null) {
            metadata.setUpdatedAt((LocalDateTime) row.get("updated_at"));
        }
        
        // 用户关联字段 - 不再使用custom_id字段
        // 上传者ID通过uploaded_by获取
        if (row.get("uploaded_by") != null) {
            Integer uploaderId = ((Number) row.get("uploaded_by")).intValue();
            User uploader = userRepository.findById(uploaderId).orElse(null);
            metadata.setUploadedBy(uploader);
        }
        
        // 其他用户相关字段同样处理...
        
        return metadata;
    }

    /**
     * 仪表盘统计数据 - 无认证限制版本
     * 适用于前端在没有完全认证的情况下获取统计数据
     */
    @GetMapping("/stats-v2/dashboard-unrestricted/{userId}")
    public ResponseEntity<Map<String, Object>> getDashboardStatsUnrestricted(@PathVariable String userId) {
        System.out.println("接收到无认证限制的仪表盘统计请求，用户ID: " + userId);
        
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 查找用户，但忽略未找到的错误
            Optional<User> userOptional = userRepository.findByCustomId(userId);
            
            if (userOptional.isPresent()) {
                User user = userOptional.get();
                System.out.println("找到用户: " + user.getName() + ", 角色: " + user.getRole());
                
                // 对所有用户都返回个人统计数据，确保体验一致
                stats = getUserPersonalStats(userId);
                System.out.println("返回用户个人统计数据: " + stats);
            } else {
                // 用户未找到，返回默认统计
                System.out.println("未找到用户 " + userId + "，返回默认统计");
                stats = getDefaultStats();
                stats.put("message", "User not found");
            }
            
            // 确保统计数据中至少包含基本字段
            ensureBasicStats(stats);
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            System.err.println("获取无认证限制的仪表盘统计失败: " + e.getMessage());
            e.printStackTrace();
            
            // 返回带有错误信息的默认统计数据
            Map<String, Object> errorStats = getDefaultStats();
            errorStats.put("error", e.getMessage());
            return ResponseEntity.ok(errorStats);
        }
    }
    
    /**
     * 确保统计数据包含所有必要字段
     */
    private void ensureBasicStats(Map<String, Object> stats) {
        if (!stats.containsKey("totalCount")) stats.put("totalCount", 0);
        if (!stats.containsKey("draftCount")) stats.put("draftCount", 0);
        if (!stats.containsKey("reviewedCount")) stats.put("reviewedCount", 0);
        if (!stats.containsKey("submittedCount")) stats.put("submittedCount", 0);
        if (!stats.containsKey("approvedCount")) stats.put("approvedCount", 0);
        if (!stats.containsKey("rejectedCount")) stats.put("rejectedCount", 0);
        if (!stats.containsKey("dataSource")) stats.put("dataSource", "default");
    }

    // 新增：POST 方式保存结构化表单数据，兼容 application/x-www-form-urlencoded
    @PostMapping("/images/{id}/structured-form")
    public ResponseEntity<?> saveStructuredFormDataPost(@PathVariable Long id, @RequestParam Map<String, String> formData) {
        // 转为 Map<String, Object> 以复用原有逻辑
        Map<String, Object> formDataObj = new HashMap<>(formData);
        return saveStructuredFormData(id, formDataObj);
    }

    // 新增：专门兼容 /medical/api/images/{id}/structured-form 的 POST 请求
    @PostMapping("/medical/api/images/{id}/structured-form")
    public ResponseEntity<?> saveStructuredFormDataMedicalPost(@PathVariable Long id, @RequestParam Map<String, String> formData) {
        Map<String, Object> formDataObj = new HashMap<>(formData);
        return saveStructuredFormData(id, formDataObj);
    }

    @GetMapping("/images/recent-annotations/{userId}")
    public ResponseEntity<List<Map<String, Object>>> getRecentAnnotations(@PathVariable String userId) {
        try {
            Optional<User> userOpt = userRepository.findByCustomId(userId);
            if (!userOpt.isPresent()) {
                return ResponseEntity.ok(new ArrayList<>());
            }
            Integer dbUserId = userOpt.get().getId();
            List<ImageMetadata> recentAnnotations = imageMetadataRepository.findTop5ByUploadedBy_IdOrderByUpdatedAtDesc(dbUserId);

            List<Map<String, Object>> result = new ArrayList<>();
            for (ImageMetadata img : recentAnnotations) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", img.getId());
                map.put("formattedId", img.getFormattedId());
                map.put("lesionLocation", img.getLesionLocation());
                map.put("status", img.getStatus());
                map.put("createdAt", img.getCreatedAt());
                map.put("updatedAt", img.getUpdatedAt());

                // 查询该病例最新的tag
                List<Tag> tags = tagRepository.findByMetadataId(img.getId());
                if (!tags.isEmpty()) {
                    tags.sort((a, b) -> {
                        if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
                        if (a.getCreatedAt() == null) return 1;
                        if (b.getCreatedAt() == null) return -1;
                        return b.getCreatedAt().compareTo(a.getCreatedAt());
                    });
                    map.put("latestTag", tags.get(0).getTag());
                } else {
                    map.put("latestTag", "未知");
                }
                result.add(map);
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 