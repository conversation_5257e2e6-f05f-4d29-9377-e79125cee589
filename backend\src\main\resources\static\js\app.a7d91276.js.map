{"version": 3, "file": "js/app.a7d91276.js", "mappings": "kOAOsB,qBAAXA,QAAwD,qBAAvBA,OAAOC,cACjDD,OAAOC,YAAcA,EAAAA,a,iBCHD,qBAAXD,SACTA,OAAOE,QAAUF,OAAOE,SAAW,CAAC,EACpCF,OAAOE,QAAQC,SAAU,EACzBH,OAAOE,QAAQE,IAAMJ,OAAOE,QAAQE,KAAO,CAAC,EAC5CJ,OAAOK,OAASL,OAAOK,QAAUC,EAAAA,OAAAA,IAYb,qBAAXC,EAAAA,GAA0BA,EAAAA,EAAOC,MAAQD,EAAAA,EAAOC,KAAKC,UAC9DF,EAAAA,EAAOC,KAAKC,QAAUC,OAAOC,Q,uFCrB7BC,EAAAA,EAAAA,IAAeC,E,CAIjB,SACEC,KAAM,O,eCCR,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,I,2FC6DaC,MAAM,gB,GAgBNA,MAAM,gB,GAEDA,MAAM,a,uhBAvFtBL,EAAAA,EAAAA,IAoHeM,EAAA,CApHDD,MAAM,oBAAkB,CADxC,SAAAE,EAAAA,EAAAA,KAEI,iBAgEW,EAhEXC,EAAAA,EAAAA,IAgEWC,EAAA,CAhEDC,MAAM,QAAQL,MAAM,W,CAFlC,SAAAE,EAAAA,EAAAA,KAGM,iBAA+B,gBAA/BI,EAAAA,EAAAA,IAA+B,OAA1BN,MAAM,QAAO,WAAO,KACzBG,EAAAA,EAAAA,IA6DUI,EAAA,CA5DP,iBAAgBC,EAAAC,WACjBT,MAAM,eACN,mBAAiB,UACjB,aAAW,OACX,oBAAkB,W,CAT1B,SAAAE,EAAAA,EAAAA,KAYQ,iBAGe,EAHfC,EAAAA,EAAAA,IAGeO,EAAA,CAHDC,MAAM,iBAAkBC,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEC,EAAAC,QAAQC,KAAK,iBAAD,I,CAZjE,SAAAf,EAAAA,EAAAA,KAaU,iBAAiC,EAAjCC,EAAAA,EAAAA,IAAiCe,EAAA,MAb3C,SAAAhB,EAAAA,EAAAA,KAamB,iBAAc,EAAdC,EAAAA,EAAAA,IAAcgB,G,IAbjCC,EAAA,I,aAcUd,EAAAA,EAAAA,IAAgB,YAAV,OAAG,I,IAdnBc,EAAA,EAAAC,GAAA,OAkBQlB,EAAAA,EAAAA,IAMeO,EAAA,CALbC,MAAM,aACLC,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEC,EAAAC,QAAQC,KAAK,aAAD,I,CApB9B,SAAAf,EAAAA,EAAAA,KAsBU,iBAA+B,EAA/BC,EAAAA,EAAAA,IAA+Be,EAAA,MAtBzC,SAAAhB,EAAAA,EAAAA,KAsBmB,iBAAY,EAAZC,EAAAA,EAAAA,IAAYmB,G,IAtB/BF,EAAA,I,aAuBUd,EAAAA,EAAAA,IAAiB,YAAX,QAAI,I,IAvBpBc,EAAA,EAAAC,GAAA,OA2BQlB,EAAAA,EAAAA,IAMeO,EAAA,CALbC,MAAM,4BACLC,QAAOJ,EAAAe,+B,CA7BlB,SAAArB,EAAAA,EAAAA,KA+BU,iBAA8B,EAA9BC,EAAAA,EAAAA,IAA8Be,EAAA,MA/BxC,SAAAhB,EAAAA,EAAAA,KA+BmB,iBAAW,EAAXC,EAAAA,EAAAA,IAAWqB,G,IA/B9BJ,EAAA,I,aAgCUd,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,I,IAhCrBc,EAAA,EAAAC,GAAA,K,eAqCgBN,EAAAU,SAAWV,EAAAW,aAAU,WAD7B/B,EAAAA,EAAAA,IAOee,EAAA,CA3CvBiB,IAAA,EAsCUhB,MAAM,0BACLC,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEC,EAAAC,QAAQC,KAAK,0BAAD,I,CAvC9B,SAAAf,EAAAA,EAAAA,KAyCU,iBAA4B,EAA5BC,EAAAA,EAAAA,IAA4Be,EAAA,MAzCtC,SAAAhB,EAAAA,EAAAA,KAyCmB,iBAAS,EAATC,EAAAA,EAAAA,IAASyB,G,IAzC5BR,EAAA,I,aA0CUd,EAAAA,EAAAA,IAAiB,YAAX,QAAI,I,IA1CpBc,EAAA,EAAAC,GAAA,QAAAQ,EAAAA,EAAAA,IAAA,QA8CQ1B,EAAAA,EAAAA,IAQeO,EAAA,CAPbC,MAAM,aACLC,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEC,EAAAC,QAAQC,KAAK,aAAD,GACpBjB,MAAM,kB,CAjDhB,SAAAE,EAAAA,EAAAA,KAmDU,iBAAiC,EAAjCC,EAAAA,EAAAA,IAAiCe,EAAA,MAnD3C,SAAAhB,EAAAA,EAAAA,KAmDmB,iBAAc,EAAdC,EAAAA,EAAAA,IAAc2B,G,IAnDjCV,EAAA,I,eAoDUd,EAAAA,EAAAA,IAAiB,YAAX,QAAI,I,IApDpBc,EAAA,EAAAC,GAAA,OA0DgBN,EAAAU,UAAO,WADf9B,EAAAA,EAAAA,IAOee,EAAA,CAhEvBiB,IAAA,EA2DUhB,MAAM,aACLC,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEC,EAAAC,QAAQC,KAAK,aAAD,I,CA5D9B,SAAAf,EAAAA,EAAAA,KA8DU,iBAA2B,EAA3BC,EAAAA,EAAAA,IAA2Be,EAAA,MA9DrC,SAAAhB,EAAAA,EAAAA,KA8DmB,iBAAQ,EAARC,EAAAA,EAAAA,IAAQ4B,G,IA9D3BX,EAAA,I,eA+DUd,EAAAA,EAAAA,IAAiB,YAAX,QAAI,I,IA/DpBc,EAAA,EAAAC,GAAA,SAAAQ,EAAAA,EAAAA,IAAA,O,IAAAT,EAAA,G,0BAAAA,EAAA,EAAAC,GAAA,QAoEIlB,EAAAA,EAAAA,IAgDeF,EAAA,MApHnB,SAAAC,EAAAA,EAAAA,KAqEM,iBA0CY,EA1CZC,EAAAA,EAAAA,IA0CY6B,EAAA,CA1CDC,OAAO,OAAOjC,MAAM,U,CArErC,SAAAE,EAAAA,EAAAA,KAsEQ,iBAeM,EAfNI,EAAAA,EAAAA,IAeM,MAfN4B,EAeM,CAbIC,EAAAC,sBAAmB,WAD3BzC,EAAAA,EAAAA,IAMY0C,EAAA,CA7EtBV,IAAA,EAyEYW,KAAK,UACLC,KAAK,iBACJ3B,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEC,EAAAC,QAAQC,KAAK,iBAAD,I,CA3EhC,SAAAf,EAAAA,EAAAA,KA2EoD,kBAE1CW,EAAA,MAAAA,EAAA,MA7EV2B,EAAAA,EAAAA,IA2EoD,Y,IA3EpDpB,EAAA,EAAAC,GAAA,SAAAQ,EAAAA,EAAAA,IAAA,OA+EkBM,EAAAM,iBAAc,WADtB9C,EAAAA,EAAAA,IAMY0C,EAAA,CApFtBV,IAAA,EAgFYW,KAAK,OACLC,KAAK,eACJ3B,QAAOJ,EAAAkC,Q,CAlFpB,SAAAxC,EAAAA,EAAAA,KAkF4B,kBAElBW,EAAA,MAAAA,EAAA,MApFV2B,EAAAA,EAAAA,IAkF4B,Y,IAlF5BpB,EAAA,EAAAC,GAAA,M,iBAAAQ,EAAAA,EAAAA,IAAA,UAsFQvB,EAAAA,EAAAA,IAwBM,MAxBNqC,EAwBM,EAvBJxC,EAAAA,EAAAA,IAsBcyC,EAAA,MAhBDC,UAAQ3C,EAAAA,EAAAA,KACjB,iBAamB,EAbnBC,EAAAA,EAAAA,IAamB2C,EAAA,MA3GjC,SAAA5C,EAAAA,EAAAA,KA+FgB,iBAImB,EAJnBC,EAAAA,EAAAA,IAImB4C,EAAA,MAnGnC,SAAA7C,EAAAA,EAAAA,KAgGkB,iBAEc,EAFdC,EAAAA,EAAAA,IAEc6C,EAAA,CAFDC,GAAG,eAAeC,MAAA,4C,CAhGjD,SAAAhD,EAAAA,EAAAA,KAgGgG,kBAE9EW,EAAA,MAAAA,EAAA,MAlGlB2B,EAAAA,EAAAA,IAgGgG,W,IAhGhGpB,EAAA,EAAAC,GAAA,O,IAAAD,EAAA,IAoGwCL,EAAAoC,WAAahB,EAAAiB,wBAAqB,WAA1DzD,EAAAA,EAAAA,IAEmBoD,EAAA,CAtGnCpB,IAAA,EAoG6Ef,QAAOJ,EAAA6C,wB,CApGpF,SAAAnD,EAAAA,EAAAA,KAoG4G,kBAE5FW,EAAA,MAAAA,EAAA,MAtGhB2B,EAAAA,EAAAA,IAoG4G,a,IApG5GpB,EAAA,EAAAC,GAAA,M,iBAAAQ,EAAAA,EAAAA,IAAA,OAuGwCd,EAAAoC,UAAYhB,EAAAiB,wBAAqB,WAAzDzD,EAAAA,EAAAA,IAEmBoD,EAAA,CAzGnCpB,IAAA,EAuG2E2B,SAAA,I,CAvG3E,SAAApD,EAAAA,EAAAA,KAuGoF,kBAEpEW,EAAA,MAAAA,EAAA,MAzGhB2B,EAAAA,EAAAA,IAuGoF,c,IAvGpFpB,EAAA,EAAAC,GAAA,SAAAQ,EAAAA,EAAAA,IAAA,QA0GgB1B,EAAAA,EAAAA,IAA+D4C,EAAA,CAA5CnC,QAAOJ,EAAA+C,cAAY,CA1GtD,SAAArD,EAAAA,EAAAA,KA0GwD,kBAAIW,EAAA,MAAAA,EAAA,MA1G5D2B,EAAAA,EAAAA,IA0GwD,S,IA1GxDpB,EAAA,EAAAC,GAAA,M,mBAAAD,EAAA,I,IAAA,SAAAlB,EAAAA,EAAAA,KAwFY,iBAIO,EAJPI,EAAAA,EAAAA,IAIO,OAJPkD,EAIO,EAHLrD,EAAAA,EAAAA,IAAwDsD,EAAA,CAA7CC,KAAK,QAAQnB,KAAK,kBAzF3CC,EAAAA,EAAAA,IAyFsE,KACxDmB,EAAAA,EAAAA,IAAGxB,EAAAyB,UAAW,IACd,IAAAtD,EAAAA,EAAAA,IAAmE,QAA5DN,OA3FrB6D,EAAAA,EAAAA,IAAA,YA2FyCrD,EAAAsD,iB,QAAkBtD,EAAAuD,cAAY,K,IA3FvE3C,EAAA,M,IAAAA,EAAA,KAiHMjB,EAAAA,EAAAA,IAEU6D,EAAA,MAnHhB,SAAA9D,EAAAA,EAAAA,KAkHQ,iBAA2B,EAA3BC,EAAAA,EAAAA,IAA2BP,G,IAlHnCwB,EAAA,I,IAAAA,EAAA,I,IAAAA,EAAA,G,+CAoIA,SACEvB,KAAM,aACNoE,WAAY,CACVC,WAAAA,EAAAA,WACAC,SAAAA,EAAAA,SACAC,MAAAA,EAAAA,MACAC,KAAAA,EAAAA,KACAC,WAAAA,EAAAA,WACAC,QAAAA,EAAAA,SAEFC,KAAI,WACF,MAAO,CACLZ,SAAU,OACVnB,gBAAgB,EAChBL,qBAAqB,EACrBgB,uBAAuB,EACvBqB,UAAW,KAEf,EACAC,UAAQC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,IACHC,EAAAA,EAAAA,IAAW,CACZC,YAAa,UACbpD,QAAS,UACT0B,SAAU,WACVzB,WAAY,aAEZoD,uBAAwB,0CACxBC,yBAA0B,kDAC1B,IAEFtE,WAAU,WACR,OAAOuE,KAAKC,OAAOC,IACrB,EAEAnB,aAAY,WAEV,GAAIiB,KAAKvD,QAAS,MAAO,MACzB,GAAIuD,KAAK7B,SAAU,MAAO,OAC1B,GAAI6B,KAAKtD,WAAY,MAAO,OAG5B,IACE,IAAMyD,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACxD,GAAIJ,GAAQA,EAAKK,KAEf,OAAOL,EAAKK,MACV,IAAK,QAAS,MAAO,MACrB,IAAK,SAAU,MAAO,OACtB,IAAK,WAAY,MAAO,OACxB,QAAS,MAAO,OAGtB,CAAE,MAAOC,GACPC,QAAQC,MAAM,wBAAyBF,EACzC,CAEA,MAAO,MACT,EAEA3B,aAAY,WACV,GAAIkB,KAAKvD,QAAS,MAAO,iBACzB,GAAIuD,KAAKtD,WAAY,MAAO,oBAC5B,GAAIsD,KAAK7B,SAAU,MAAO,kBAG1B,IACE,IAAMgC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACxD,GAAIJ,GAAQA,EAAKK,KACf,OAAOL,EAAKK,MACV,IAAK,QAAS,MAAO,iBACrB,IAAK,WAAY,MAAO,oBACxB,IAAK,SAAU,MAAO,kBACtB,QAAS,MAAO,GAGtB,CAAE,MAAOC,GACPC,QAAQC,MAAM,0BAA2BF,EAC3C,CAEA,MAAO,EACT,IAEFG,MAAO,CACLX,OAAM,SAAChC,GAQL,GANA+B,KAAKvC,eAA6B,+BAAZQ,EAAGiC,KAGzBF,KAAK5C,oBAAkC,mBAAZa,EAAGiC,KAGd,mBAAZjC,EAAGiC,MAAyC,SAAZjC,EAAGiC,MAA+B,UAAZjC,EAAGiC,KAAkB,CAE7E,IAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDM,EAAgBV,EAAKW,UAAYX,EAAKY,GAGtCC,EAAaV,aAAaC,QAAQ,oBACpCS,GAAcA,IAAeH,IAC/BH,QAAQO,IAAI,0BAA2BD,EAAY,IAAKH,GAGpD9G,OAAOmH,wBACTR,QAAQO,IAAI,6BACZE,YAAW,kBAAMpH,OAAOmH,uBAAuB,GAAE,KAKjDL,GACFP,aAAac,QAAQ,mBAAoBP,EAE7C,CACF,EAGAjC,SAAQ,SAACyC,EAAQC,GACXD,IAAWC,GAAUD,GAAUC,IACjCZ,QAAQO,IAAI,sBAAuBK,EAAQ,IAAKD,GAGvB,mBAArBrB,KAAKC,OAAOC,MAAkD,SAArBF,KAAKC,OAAOC,MAAwC,UAArBF,KAAKC,OAAOC,MAClFnG,OAAOmH,wBACTR,QAAQO,IAAI,8BACZE,YAAW,kBAAMpH,OAAOmH,uBAAuB,GAAE,IAIzD,GAEFK,QAAO,WAELvB,KAAKwB,eAGLxB,KAAKyB,4BACP,EACAC,QAAO,WAEL1B,KAAK2B,iCACP,EACAC,cAAa,WAEX5B,KAAK6B,gCACP,EACAC,QAAS,CAEPN,aAAY,WAAG,IAAAO,EAAA,KACb,IACE,IAAM5B,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACpDJ,GAAQA,EAAKtF,OACfmF,KAAKpB,SAAWuB,EAAKtF,MAInBmF,KAAK7B,UACP6D,EAAAA,WAAIC,MAAMC,+BACPC,MAAK,SAAAC,GACAA,EAAS5C,MAAiC,YAAzB4C,EAAS5C,KAAK6C,SACjCN,EAAK3D,uBAAwB,EAEjC,IAAC,UACM,SAAAuC,GAAI,OAAKD,QAAQC,MAAM,gBAAiBA,EAAM,GAE3D,CAAE,MAAOA,GACPD,QAAQC,MAAM,YAAaA,EAC7B,CACF,EAGAc,2BAA0B,WACxBzB,KAAKsC,OAAOC,SAAS,iDACvB,EAGAZ,gCAA+B,WAAG,IAAAa,EAAA,KAChCxC,KAAKP,UAAYgD,aAAY,WAC3BD,EAAKf,4BACP,GAAG,IACL,EAGAI,+BAA8B,WACxB7B,KAAKP,WACPiD,cAAc1C,KAAKP,UAEvB,EACAlB,aAAY,WACVmC,QAAQO,IAAI,uBAAwBlH,OAAO4I,SAASC,MAGpDC,eAAeC,WAAW,kBAC1BD,eAAeC,WAAW,yBAC1BD,eAAeC,WAAW,wBAG1BD,eAAezB,QAAQ,oBAAqB,QAC5CV,QAAQO,IAAI,2BAGZX,aAAawC,WAAW,QAGxBpC,QAAQO,IAAI,iBACZjB,KAAKhE,QAAQC,KAAK,UAAUkG,MAAK,WAC/BzB,QAAQO,IAAI,kBACd,IAAE,UAAO,SAAAR,GACPC,QAAQC,MAAM,eAAgBF,GAE9B1G,OAAO4I,SAASC,KAAO,QACzB,IAGAzB,YAAW,WACTT,QAAQO,IAAI,iBACZ4B,eAAeC,WAAW,oBAC5B,GAAG,IACL,EACApF,OAAM,WAAG,IAAAqF,EAAA,KAEP/C,KAAKgD,SAAS,6BAA8B,KAAM,CAChDC,kBAAmB,KACnBC,iBAAkB,KAClB5F,KAAM,YACL6E,MAAK,WAENY,EAAK/G,QAAQmH,IAAI,EACnB,IAAE,UAAO,WACP,GAEJ,EAEAC,yBAAwB,WAAG,IAAAC,EAAA,KAEpBrD,KAAK7B,UAKV6D,EAAAA,WAAIC,MAAMC,+BACPC,MAAK,SAAAC,GACJ1B,QAAQO,IAAI,YAAamB,EAAS5C,MAClC6D,EAAKjF,sBAAwBgE,EAAS5C,MAAQ4C,EAAS5C,KAAK8D,OAAS,GACnElB,EAAS5C,KAAK+D,MAAK,SAAAC,GAAE,MAAoB,YAAfA,EAAInB,MAAoB,GACtD,IAAC,UACM,SAAA1B,GACLD,QAAQC,MAAM,cAAeA,EAC/B,GACJ,EAGAtC,uBAAsB,WAAG,IAAAoF,EAAA,KACvBzD,KAAK0D,QAAQ,UAAW,WAAY,CAClCT,kBAAmB,OACnBC,iBAAkB,KAClBS,UAAW,WACXC,iBAAkB,yBACjBzB,MAAK,SAAA0B,GAAe,IAAZC,EAAID,EAAJC,MACJA,GAA0B,KAAjBA,EAAMC,OAMpB/B,EAAAA,WAAIC,MAAM+B,iBAAiBF,GACxB3B,MAAK,WACJsB,EAAKQ,SAASC,QAAQ,kBACtBT,EAAKrF,uBAAwB,CAC/B,IAAC,UACM,SAAAuC,GACDA,EAAMyB,UAAYzB,EAAMyB,SAAS5C,MAAQmB,EAAMyB,SAAS5C,KAAK2E,QAC/DV,EAAKQ,SAAStD,MAAMA,EAAMyB,SAAS5C,KAAK2E,SAExCV,EAAKQ,SAAStD,MAAM,eAExB,IAhBA8C,EAAKQ,SAASG,QAAQ,WAiB1B,IAAE,UAAO,WACP,GAEJ,EAGA7H,8BAA6B,WAC3BmE,QAAQO,IAAI,qBACZ,IACEjB,KAAKhE,QAAQC,KAAK,6BAA6BkG,MAAK,WAClDzB,QAAQO,IAAI,oBACd,IAAE,UAAO,SAAAR,GACPC,QAAQC,MAAM,qBAAsBF,EACtC,GACF,CAAE,MAAO4D,GACP3D,QAAQC,MAAM,aAAc0D,EAC9B,CACF,EACAC,kBAAiB,WACf5D,QAAQO,IAAI,oBAEZlH,OAAO4I,SAASC,KAAO,cACzB,ICvaE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,KAEpE,I,yHCREjI,EAAAA,EAAAA,IAIeM,EAAA,CAJDD,MAAM,iBAAe,CADrC,SAAAE,EAAAA,EAAAA,KAEI,iBAEU,EAFVC,EAAAA,EAAAA,IAEU6D,EAAA,MAJd,SAAA9D,EAAAA,EAAAA,KAGM,iBAA2B,EAA3BC,EAAAA,EAAAA,IAA2BP,G,IAHjCwB,EAAA,I,IAAAA,EAAA,G,CASA,SACEvB,KAAM,gBCHF,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,I,OCROG,MAAM,a,GAIAA,MAAM,kB,GASJA,MAAM,a,GAEJA,MAAM,c,GAMRA,MAAM,a,GAEJA,MAAM,c,GAMRA,MAAM,a,GAEJA,MAAM,c,GAMRA,MAAM,a,GAEJA,MAAM,c,GAOdA,MAAM,iB,GAQEA,MAAM,kB,GAUNA,MAAM,kB,2MAhErBuJ,EAAAA,EAAAA,IAyEM,MAzENrH,EAyEM,EAvEJ/B,EAAAA,EAAAA,IAyCSqJ,EAAA,CAzCAC,OAAQ,GAAIzJ,MAAM,c,CAH/B,SAAAE,EAAAA,EAAAA,KAIM,iBAOS,EAPTC,EAAAA,EAAAA,IAOSuJ,EAAA,CAPAC,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAI7G,MAAA,0B,CAJ3D,SAAAhD,EAAAA,EAAAA,KAKQ,iBAKM,EALNI,EAAAA,EAAAA,IAKM,MALNqC,EAKM,cAJJrC,EAAAA,EAAAA,IAA+B,MAA3B4C,MAAA,cAAmB,OAAG,KAC1B/C,EAAAA,EAAAA,IAEYkC,EAAA,CAFDC,KAAK,UAAUoB,KAAK,QAAS9C,QAAOJ,EAAAwJ,e,CAPzD,SAAA9J,EAAAA,EAAAA,KAQY,iBAA8B,EAA9BC,EAAAA,EAAAA,IAA8Be,EAAA,MAR1C,SAAAhB,EAAAA,EAAAA,KAQqB,iBAAW,EAAXC,EAAAA,EAAAA,IAAW8J,G,IARhC7I,EAAA,I,aAAAoB,EAAAA,EAAAA,IAQ0C,W,IAR1CpB,EAAA,EAAAC,GAAA,K,qBAAAD,EAAA,KAYMjB,EAAAA,EAAAA,IAOSuJ,EAAA,CAPAC,GAAI,GAAKC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EAAIC,GAAI,G,CAZpD,SAAA7J,EAAAA,EAAAA,KAaQ,iBAKU,EALVC,EAAAA,EAAAA,IAKU+J,EAAA,CALDC,OAAO,QAAQnK,MAAM,iBAAkBY,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEN,EAAA4J,WAAW,aAAD,I,CAbzE,SAAAlK,EAAAA,EAAAA,KAcU,iBAGM,EAHNI,EAAAA,EAAAA,IAGM,MAHNkD,EAGM,cAFJlD,EAAAA,EAAAA,IAAkC,OAA7BN,MAAM,cAAa,QAAI,KAC5BM,EAAAA,EAAAA,IAA6D,MAA7D+J,GAA6D1G,EAAAA,EAAAA,IAAlCxB,EAAAmI,eAAeC,YAAU,K,IAhBhEnJ,EAAA,I,IAAAA,EAAA,KAoBMjB,EAAAA,EAAAA,IAOSuJ,EAAA,CAPAC,GAAI,GAAKC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EAAIC,GAAI,G,CApBpD,SAAA7J,EAAAA,EAAAA,KAqBQ,iBAKU,EALVC,EAAAA,EAAAA,IAKU+J,EAAA,CALDC,OAAO,QAAQnK,MAAM,iBAAkBY,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEN,EAAA4J,WAAW,aAAc,WAAf,I,CArBzE,SAAAlK,EAAAA,EAAAA,KAsBU,iBAGM,EAHNI,EAAAA,EAAAA,IAGM,MAHNkK,EAGM,cAFJlK,EAAAA,EAAAA,IAAiC,OAA5BN,MAAM,cAAa,OAAG,KAC3BM,EAAAA,EAAAA,IAAgE,MAAhEmK,GAAgE9G,EAAAA,EAAAA,IAArCxB,EAAAmI,eAAeI,eAAa,K,IAxBnEtJ,EAAA,I,IAAAA,EAAA,KA4BMjB,EAAAA,EAAAA,IAOSuJ,EAAA,CAPAC,GAAI,GAAKC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EAAIC,GAAI,G,CA5BpD,SAAA7J,EAAAA,EAAAA,KA6BQ,iBAKU,EALVC,EAAAA,EAAAA,IAKU+J,EAAA,CALDC,OAAO,QAAQnK,MAAM,iBAAkBY,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEN,EAAA4J,WAAW,aAAc,YAAf,I,CA7BzE,SAAAlK,EAAAA,EAAAA,KA8BU,iBAGM,EAHNI,EAAAA,EAAAA,IAGM,MAHNqK,EAGM,gBAFJrK,EAAAA,EAAAA,IAAiC,OAA5BN,MAAM,cAAa,OAAG,KAC3BM,EAAAA,EAAAA,IAAiE,MAAjEsK,GAAiEjH,EAAAA,EAAAA,IAAtCxB,EAAAmI,eAAeO,gBAAc,K,IAhCpEzJ,EAAA,I,IAAAA,EAAA,KAoCMjB,EAAAA,EAAAA,IAOSuJ,EAAA,CAPAC,GAAI,GAAKC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EAAIC,GAAI,G,CApCpD,SAAA7J,EAAAA,EAAAA,KAqCQ,iBAKU,EALVC,EAAAA,EAAAA,IAKU+J,EAAA,CALDC,OAAO,QAAQnK,MAAM,iBAAkBY,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEN,EAAA4J,WAAW,aAAc,WAAf,I,CArCzE,SAAAlK,EAAAA,EAAAA,KAsCU,iBAGM,EAHNI,EAAAA,EAAAA,IAGM,MAHNwK,EAGM,gBAFJxK,EAAAA,EAAAA,IAAiC,OAA5BN,MAAM,cAAa,OAAG,KAC3BM,EAAAA,EAAAA,IAAgE,MAAhEyK,GAAgEpH,EAAAA,EAAAA,IAArCxB,EAAAmI,eAAeU,eAAa,K,IAxCnE5J,EAAA,I,IAAAA,EAAA,I,IAAAA,EAAA,KA+CId,EAAAA,EAAAA,IA0BM,MA1BN2K,EA0BM,gBAzBJ3K,EAAAA,EAAAA,IAEM,OAFDN,MAAM,gBAAc,EACvBM,EAAAA,EAAAA,IAA0B,YAAtBA,EAAAA,EAAAA,IAAiB,YAAX,YAAI,KAGhBH,EAAAA,EAAAA,IAoBSqJ,EAAA,CApBAC,OAAQ,IAAE,CApDzB,SAAAvJ,EAAAA,EAAAA,KAqDQ,iBAQS,EARTC,EAAAA,EAAAA,IAQSuJ,EAAA,CARAC,GAAI,GAAKC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EAAIC,GAAI,G,CArDtD,SAAA7J,EAAAA,EAAAA,KAsDU,iBAMU,EANVC,EAAAA,EAAAA,IAMU+J,EAAA,CANDC,OAAO,QAAQnK,MAAM,cAAeY,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEN,EAAA4J,WAAW,4BAAD,I,CAtDxE,SAAAlK,EAAAA,EAAAA,KAuDY,iBAIM,EAJNI,EAAAA,EAAAA,IAIM,MAJN4K,EAIM,EAHJ/K,EAAAA,EAAAA,IAAkDe,EAAA,CAAzClB,MAAM,eAAa,CAxD1C,SAAAE,EAAAA,EAAAA,KAwD2C,iBAAW,EAAXC,EAAAA,EAAAA,IAAW8J,G,IAxDtD7I,EAAA,I,eAyDcd,EAAAA,EAAAA,IAAqC,OAAhCN,MAAM,gBAAe,SAAK,mBAC/BM,EAAAA,EAAAA,IAA0C,OAArCN,MAAM,eAAc,eAAW,M,IA1DlDoB,EAAA,I,IAAAA,EAAA,KA+DQjB,EAAAA,EAAAA,IAQSuJ,EAAA,CARAC,GAAI,GAAKC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EAAIC,GAAI,G,CA/DtD,SAAA7J,EAAAA,EAAAA,KAgEU,iBAMU,EANVC,EAAAA,EAAAA,IAMU+J,EAAA,CANDC,OAAO,QAAQnK,MAAM,cAAeY,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEN,EAAA4J,WAAW,aAAD,I,CAhExE,SAAAlK,EAAAA,EAAAA,KAiEY,iBAIA,EAJAI,EAAAA,EAAAA,IAIA,MAJA6K,EAIA,EAHEhL,EAAAA,EAAAA,IAAqDe,EAAA,CAA5ClB,MAAM,eAAa,CAlE1C,SAAAE,EAAAA,EAAAA,KAkE2C,iBAAc,EAAdC,EAAAA,EAAAA,IAAc2B,G,IAlEzDV,EAAA,I,eAmEcd,EAAAA,EAAAA,IAAoC,OAA/BN,MAAM,gBAAe,QAAI,mBAC9BM,EAAAA,EAAAA,IAAuC,OAAlCN,MAAM,eAAc,YAAQ,M,IApE/CoB,EAAA,I,IAAAA,EAAA,I,IAAAA,EAAA,O,oCAoFIgK,EAAmB,CACrBb,WAAY,EACZG,cAAe,EACfG,eAAgB,EAChBG,cAAe,EACfK,cAAe,GA6CjB,SACExL,KAAM,YACNoE,WAAY,CACVqH,QAAAA,EAAAA,QACAC,QAAAA,EAAAA,QACAC,KAAAA,EAAAA,KACAlH,WAAAA,EAAAA,YAEFE,KAAI,WACF,MAAO,CACL8F,gBAAc3F,EAAAA,EAAAA,GAAA,GAAOyG,GAEzB,EACA1G,UAAQC,EAAAA,EAAAA,GAAA,IACHC,EAAAA,EAAAA,IAAW,CACZiB,cAAe,eAGnBD,MAAO,CACLX,OAAM,SAAChC,EAAIwI,GACO,mBAAZxI,EAAGiC,OACLQ,QAAQO,IAAI,8BACZjB,KAAKgF,gBAET,GAKF0B,iBAAgB,SAACzI,EAAIwI,EAAME,GAEzBjG,QAAQO,IAAI,0BACZ0F,GAAK,SAAAC,GAEHlG,QAAQO,IAAI,qBACZ2F,EAAG5B,eACL,GACF,EAEAzD,QAAO,WACLb,QAAQO,IAAI,sCACZjB,KAAKsF,eAAiB,CAAEC,WAAY,EAAGG,cAAe,EAAGG,eAAgB,EAAGG,cAAe,EAAGK,cAAe,EAC/G,EAEA3E,QAAO,WACLhB,QAAQO,IAAI,sCAKZlH,OAAO8M,iBAAiB,QAAS7G,KAAK8G,iBACtCpG,QAAQO,IAAI,YACd,EAEA8F,UAAS,WACPrG,QAAQO,IAAI,6BAGd,EAEAW,cAAa,WACXlB,QAAQO,IAAI,6CAEZlH,OAAOiN,oBAAoB,QAAShH,KAAK8G,iBACzCpG,QAAQO,IAAI,YACd,EAIAa,SAAOnC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,IACFsH,EAAAA,EAAAA,IAAW,CAAC,gBAAc,IAE7B7B,WAAU,SAAClF,EAAMmC,GACf,IAAM6E,EAAQ7E,EAAS,CAAEA,OAAAA,GAAW,CAAC,EACrC/B,aAAac,QAAQ,qBAAsBiB,GAAU,OACrDrC,KAAKhE,QAAQC,KAAK,CAAEiE,KAAAA,EAAMgH,MAAAA,GAC5B,EAEAC,WAAU,SAACC,GACT,OAAKA,EACE,IAAIC,KAAKD,GAAME,iBADJ,EAEpB,EAEAR,gBAAe,WACbpG,QAAQO,IAAI,mBACZjB,KAAKgF,eACP,EAEMA,cAAa,WAAG,IAAAjD,EAAA,YAAAwF,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAzH,EAAA0H,EAAAC,EAAA1F,EAAA5C,EAAAuI,EAAA,OAAAP,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OACpBxH,QAAQO,IAAI,eAIZ,IAEE0G,EAAS5F,EAAKlB,cAGT8G,IACGC,EAAUtH,aAAaC,QAAQ,QACjCqH,IACIzH,EAAOC,KAAKC,MAAMuH,GAExBD,EAASxH,EAAKY,IAGpB,CAAE,MAAOsD,GACP3D,QAAQC,MAAM,YAAa0D,EAC7B,IAEKsD,EAAQ,CAAFM,EAAAC,EAAA,QAE4B,OADrCxH,QAAQC,MAAM,uBACdoB,EAAKkC,SAAStD,MAAM,kBAAiBsH,EAAAE,EAAA,UAQU,OAJjDzH,QAAQO,IAAI,gBAAiB0G,GAEvBE,EAAU9F,EAAKqG,SAAS,CAAEC,MAAM,EAAMC,KAAM,gBAAgBL,EAAAM,EAAA,EAE1DT,GAAMU,EAAAA,EAAAA,IAA4Bb,GAAOM,EAAAC,EAAA,EACxBO,MAAMX,EAAK,CAChCY,OAAQ,MACRC,QAAS,CAAE,gBAAiB,YAC5BC,YAAa,YACb,OAJW,GAAPxG,EAAO6F,EAAAY,EAMRzG,EAAS0G,GAAI,CAAFb,EAAAC,EAAA,cAAQ,IAAIa,MAAM,eAADC,OAAgB5G,EAASC,SAAS,cAAA4F,EAAAC,EAAA,EAEhD9F,EAAS6G,OAAM,OAA5BzJ,EAAGyI,EAAAY,EACTnI,QAAQO,IAAI,kBAAmBzB,GAE/BuC,EAAKuD,eAAiB,CACpBC,WAAY2D,SAAS1J,EAAK+F,YAAc,GACxCG,cAAewD,SAAS1J,EAAKkG,eAAiB,GAC9CG,eAAgBqD,SAAS1J,EAAKqG,gBAAkB,GAChDG,cAAekD,SAAS1J,EAAKwG,eAAiB,GAC9CK,cAAe6C,SAAS1J,EAAK6G,eAAiB,IAEhDD,GAAezG,EAAAA,EAAAA,GAAA,GAASoC,EAAKuD,gBAC7BvD,EAAKoH,eAAclB,EAAAC,EAAA,eAAAD,EAAAM,EAAA,EAAAR,EAAAE,EAAAY,EAGnBnI,QAAQC,MAAM,YAAWoH,GACzBhG,EAAKkC,SAAStD,MAAM,SAADqI,OAAUjB,EAAM5D,UAAU,OAE9B,OAF8B8D,EAAAM,EAAA,EAE7CV,EAAQuB,QAAOnB,EAAAoB,EAAA,iBAAApB,EAAAE,EAAA,MAAAT,EAAA,qBA1DGH,EA4DtB,KCnRE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,I,yDCLM+B,EAAQ,CACZnJ,KAAMoJ,EAAAA,EAAaC,eAAe,SAAW,KAC7CC,kBAAmBF,EAAAA,EAAaC,eAAe,QAC/CvH,MAAO,GACPyH,mBAAoB,KACpB7B,SAAS,EACTlH,MAAO,MAGHgJ,GAAU,CACdC,QAAS,SAAAN,GAAK,OAAIA,EAAMnJ,IAAI,EAE5B0J,YAAa,SAAAP,GAAK,OAAIA,EAAMnJ,KAAOmJ,EAAMnJ,KAAKK,KAAO,IAAI,EAEzDsJ,UAAW,SAAAR,GAAK,OAAIA,EAAMnJ,KAAOmJ,EAAMnJ,KAAKY,GAAK,IAAI,EAErDtE,QAAS,SAAA6M,GAAK,OAAIA,EAAMnJ,MAA4B,UAApBmJ,EAAMnJ,KAAKK,IAAgB,EAE3DrC,SAAU,SAAAmL,GAAK,OAAIA,EAAMnJ,MAA4B,WAApBmJ,EAAMnJ,KAAKK,IAAiB,EAE7D9D,WAAY,SAAA4M,GAAK,OAAIA,EAAMnJ,MAA4B,aAApBmJ,EAAMnJ,KAAKK,IAAmB,EAEjEuJ,cAAe,SAAAT,GAAK,OAAI,SAAAU,GAAc,IAAAC,EACpC,OAAOF,EAAAA,EAAAA,IAAwB,QAAXE,EAACX,EAAMnJ,YAAI,IAAA8J,OAAA,EAAVA,EAAYzJ,KAAMwJ,EACzC,CAAC,EAEDE,eAAgB,SAAAZ,GAAK,OAAI,SAAAa,GAAS,IAAAC,EAChC,OAAOF,EAAAA,EAAAA,IAAyB,QAAXE,EAACd,EAAMnJ,YAAI,IAAAiK,OAAA,EAAVA,EAAY5J,KAAM2J,EAC1C,CAAC,EAEDE,kBAAmB,SAAAf,GAAK,OAAI,SAAAgB,GAC1B,QAAKhB,EAAMnJ,OACJkK,EAAAA,EAAAA,IAAkBf,EAAMnJ,KAAKY,GAAIuJ,EAAiBhB,EAAMnJ,KAAKK,KACtE,CAAC,EAED+J,YAAa,SAAAjB,GAAK,OAAIA,EAAMrH,KAAK,EACjCuI,sBAAuB,SAAAlB,GAAK,OAAIA,EAAMI,kBAAkB,EACxDe,eAAgB,SAAAnB,GAAK,OAAIA,EAAMzB,OAAO,EACtC6C,cAAe,SAAApB,GAAK,OAAIA,EAAM3I,KAAK,GAG/BgK,GAAY,CAChBC,QAAO,SAACtB,EAAOnJ,GACbmJ,EAAMnJ,KAAOA,EACbmJ,EAAMG,kBAAoBtJ,CAC5B,EAEA0K,SAAQ,SAACvB,EAAOrH,GACdqH,EAAMrH,MAAQA,CAChB,EACA6I,sBAAqB,SAACxB,EAAOnJ,GAC3BmJ,EAAMI,mBAAqBvJ,CAC7B,EACA4K,WAAU,SAACzB,EAAOjH,GAChBiH,EAAMzB,QAAUxF,CAClB,EACA2I,SAAQ,SAAC1B,EAAO3I,GACd2I,EAAM3I,MAAQA,CAChB,GAGIsK,GAAU,CAERC,MAAK,SAAArH,EAAa+E,GAAa,OAAArB,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAyD,EAAA/I,EAAAgJ,EAAAjL,EAAA4H,EAAA,OAAAP,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAMjC,OANUiD,EAAMtH,EAANsH,OACZE,EAAAA,EAAmBC,MAAMH,GAAQlD,EAAAM,EAAA,EAG/B7H,QAAQO,IAAI,QAAS2H,GAErBX,EAAAC,EAAA,EACuBlG,EAAAA,WAAIuJ,KAAKL,MAAMtC,GAAY,OAqBlD,GArBMxG,EAAQ6F,EAAAY,EAGdnI,QAAQO,IAAI,aAAcmB,GAGpBgJ,EAAUhJ,EAAS5C,KACzBkB,QAAQO,IAAI,eAAgBmK,GAGtBjL,EAAO,CACXY,GAAIqK,EAAQrK,GACZD,SAAUsK,EAAQtK,SAClBjG,KAAMuQ,EAAQvQ,KACd2Q,MAAOJ,EAAQI,MACfhL,KAAM4K,EAAQ5K,KACdiL,WAAYL,EAAQK,WACpBC,SAAUN,EAAQM,UAIhBN,EAAQO,KACV,IACExL,EAAKwL,KAAO,CACV5K,GAAIqK,EAAQO,KAAK5K,GACjBlG,KAAMuQ,EAAQO,KAAK9Q,KACnB+Q,YAAaR,EAAQO,KAAKC,YAE9B,CAAE,MAAOvH,GACP3D,QAAQmL,KAAK,eAAgBxH,GAC7BlE,EAAKwL,KAAO,IACd,MAEAxL,EAAKwL,KAAO,KAoCU,OAjCxBjL,QAAQO,IAAI,YAAad,GACzBO,QAAQO,IAAI,eAAgBd,EAAKY,GAAI,MAAOZ,EAAKtF,KAAM,MAAOsF,EAAKK,KAAM,YAAaL,EAAKW,UAGtFX,EAAKtF,OACR6F,QAAQmL,KAAK,oBAET1L,EAAKqL,OACPrL,EAAKtF,KAAOsF,EAAKqL,MAAMM,MAAM,KAAK,GAClCpL,QAAQO,IAAI,iBAAkBd,EAAKtF,OAEnCsF,EAAKtF,KAAO,SAKXsF,EAAKK,OACRE,QAAQC,MAAM,eAEVR,EAAKW,UAAYX,EAAKW,SAASiL,WAAW,KAC5C5L,EAAKK,KAAO,QACHL,EAAKW,UAAYX,EAAKW,SAASiL,WAAW,KACnD5L,EAAKK,KAAO,WAEZL,EAAKK,KAAO,SAEdE,QAAQO,IAAI,iBAAkBd,EAAKK,OAIrC+I,EAAAA,EAAayC,cAAc,OAAQ7L,GAGnCgL,EAAO,UAAWhL,GAAM8H,EAAAE,EAAA,EAEjBhI,GAAI,OAEqB,OAFrB8H,EAAAM,EAAA,EAAAR,EAAAE,EAAAY,EAEXnI,QAAQC,MAAM,UAASoH,GAASE,EAAAE,EAAA,EACzBkD,EAAAA,EAAmB1K,MAAMwK,EAAMpD,IAAQ,OAEf,OAFeE,EAAAM,EAAA,EAE9C8C,EAAAA,EAAmBY,IAAId,GAAQlD,EAAAoB,EAAA,iBAAApB,EAAAE,EAAA,MAAAT,EAAA,qBAnFEH,EAqFrC,EAGM2E,SAAQ,SAAAC,EAAaC,GAAU,OAAA7E,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAA4E,IAAA,IAAAlB,EAAA/I,EAAAkK,EAAA,OAAA9E,EAAAA,EAAAA,KAAAQ,GAAA,SAAAuE,GAAA,eAAAA,EAAArE,GAAA,OACF,OADlBiD,EAAMgB,EAANhB,OACfE,EAAAA,EAAmBC,MAAMH,GAAQoB,EAAAhE,EAAA,EAAAgE,EAAArE,EAAA,EAGRlG,EAAAA,WAAIuJ,KAAKW,SAASE,GAAS,OAApC,OAARhK,EAAQmK,EAAA1D,EAAA0D,EAAApE,EAAA,EACP/F,EAAS5C,MAAI,cAAA+M,EAAAhE,EAAA,EAAA+D,EAAAC,EAAA1D,EAAA0D,EAAApE,EAAA,EAEbkD,EAAAA,EAAmB1K,MAAMwK,EAAMmB,IAAQ,OAEf,OAFeC,EAAAhE,EAAA,EAE9C8C,EAAAA,EAAmBY,IAAId,GAAQoB,EAAAlD,EAAA,iBAAAkD,EAAApE,EAAA,MAAAkE,EAAA,qBATE9E,EAWrC,EAGAiF,OAAM,SAAAC,GAAa,IAAVtB,EAAMsB,EAANtB,OACP5B,EAAAA,EAAamD,kBAAkB,QAC/BvB,EAAO,UAAW,KACpB,EAGMwB,iBAAgB,SAAAC,EAAajF,GAAQ,OAAAJ,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAoF,IAAA,IAAA1B,EAAA/I,EAAA0K,EAAA,OAAAtF,EAAAA,EAAAA,KAAAQ,GAAA,SAAA+E,GAAA,eAAAA,EAAA7E,GAAA,OACR,OADViD,EAAMyB,EAANzB,OACvBE,EAAAA,EAAmBC,MAAMH,GAAQ4B,EAAAxE,EAAA,EAAAwE,EAAA7E,EAAA,EAGRlG,EAAAA,WAAIC,MAAM2H,QAAQjC,GAAO,OACD,OADzCvF,EAAQ2K,EAAAlE,EACdsC,EAAO,wBAAyB/I,EAAS5C,MAAMuN,EAAA5E,EAAA,EACxC/F,EAAS5C,MAAI,cAAAuN,EAAAxE,EAAA,EAAAuE,EAAAC,EAAAlE,EAAAkE,EAAA5E,EAAA,EAEbkD,EAAAA,EAAmB1K,MAAMwK,EAAM2B,IAAQ,OAEf,OAFeC,EAAAxE,EAAA,EAE9C8C,EAAAA,EAAmBY,IAAId,GAAQ4B,EAAA1D,EAAA,iBAAA0D,EAAA5E,EAAA,MAAA0E,EAAA,qBAVQtF,EAY3C,EAGMyF,kBAAiB,SAAAC,EAAoBb,GAAU,OAAA7E,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAyF,IAAA,IAAA/B,EAAA7B,EAAAlH,EAAA+K,EAAAC,EAAA,OAAA5F,EAAAA,EAAAA,KAAAQ,GAAA,SAAAqF,GAAA,eAAAA,EAAAnF,GAAA,OAClB,OADTiD,EAAM8B,EAAN9B,OAAQ7B,EAAK2D,EAAL3D,MAChC+B,EAAAA,EAAmBC,MAAMH,GAAQkC,EAAA9E,EAAA,EAAA8E,EAAAnF,EAAA,EAGRlG,EAAAA,WAAIC,MAAMqL,WAAWhE,EAAMnJ,KAAKY,GAAIqL,GAAS,OAOrC,OAPzBhK,EAAQiL,EAAAxE,EACRsE,EAAc/K,EAAS5C,KAG7B+J,EAAAA,EAAayC,cAAc,OAAQmB,GAGnChC,EAAO,UAAWgC,GAAaE,EAAAlF,EAAA,EAExBgF,GAAW,cAAAE,EAAA9E,EAAA,EAAA6E,EAAAC,EAAAxE,EAAAwE,EAAAlF,EAAA,EAEXkD,EAAAA,EAAmB1K,MAAMwK,EAAMiC,IAAQ,OAEf,OAFeC,EAAA9E,EAAA,EAE9C8C,EAAAA,EAAmBY,IAAId,GAAQkC,EAAAhE,EAAA,iBAAAgE,EAAAlF,EAAA,MAAA+E,EAAA,qBAjBkB3F,EAmBrD,EAIMgG,WAAU,SAAAC,GAAa,OAAAjG,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAgG,IAAA,IAAAtC,EAAA/I,EAAAsL,EAAA,OAAAlG,EAAAA,EAAAA,KAAAQ,GAAA,SAAA2F,GAAA,eAAAA,EAAAzF,GAAA,OACM,OADhBiD,EAAMqC,EAANrC,OACjBE,EAAAA,EAAmBC,MAAMH,GAAQwC,EAAApF,EAAA,EAAAoF,EAAAzF,EAAA,EAGRlG,EAAAA,WAAIC,MAAM2L,SAAQ,OACP,OAD5BxL,EAAQuL,EAAA9E,EACdsC,EAAO,WAAY/I,EAAS5C,MAAMmO,EAAAxF,EAAA,EAC3B/F,EAAS5C,MAAI,cAAAmO,EAAApF,EAAA,EAAAmF,EAAAC,EAAA9E,EAAA8E,EAAAxF,EAAA,EAEbkD,EAAAA,EAAmB1K,MAAMwK,EAAMuC,IAAQ,OAEf,OAFeC,EAAApF,EAAA,EAE9C8C,EAAAA,EAAmBY,IAAId,GAAQwC,EAAAtE,EAAA,iBAAAsE,EAAAxF,EAAA,MAAAsF,EAAA,qBAVNlG,EAY7B,GAGF,UACE+B,MAAAA,EACAK,QAAAA,GACAgB,UAAAA,GACAM,QAAAA,I,gBClOI4C,I,2GAAgB,WACpB,IAAM1N,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAOJ,EAAO,CAAE2N,cAAe,SAAF9E,OAAW+E,KAAK,GAAD/E,OAAI7I,EAAKqL,MAAK,KAAAxC,OAAI7I,EAAK6N,aAAkB,CAAC,CACxF,GAEM1E,GAAQ,CACZ2E,OAAQ,GACRC,aAAc,KACdrG,SAAS,EACTlH,MAAO,MAGHgJ,GAAU,CACdwE,aAAc,SAAA7E,GAAK,OAAIA,EAAM2E,MAAM,EACnCG,gBAAiB,SAAA9E,GAAK,OAAIA,EAAM4E,YAAY,EAC5CG,gBAAiB,SAAA/E,GAAK,OAAIA,EAAMzB,OAAO,EACvCyG,eAAgB,SAAAhF,GAAK,OAAIA,EAAM3I,KAAK,GAGhCgK,GAAY,CAChB4D,UAAS,SAACjF,EAAO2E,GACf3E,EAAM2E,OAASA,CACjB,EACAO,gBAAe,SAAClF,EAAOmF,GACrBnF,EAAM4E,aAAeO,CACvB,EACA1D,WAAU,SAACzB,EAAOjH,GAChBiH,EAAMzB,QAAUxF,CAClB,EACA2I,SAAQ,SAAC1B,EAAO3I,GACd2I,EAAM3I,MAAQA,CAChB,EACA+N,SAAQ,SAACpF,EAAOmF,GACdnF,EAAM2E,OAAOU,QAAQF,EACvB,EACAG,YAAW,SAACtF,EAAOuF,GACjB,IAAMlT,EAAQ2N,EAAM2E,OAAOa,WAAU,SAAAC,GAAG,OAAIA,EAAIhO,KAAO8N,EAAa9N,EAAE,KACvD,IAAXpF,GACF2N,EAAM2E,OAAOe,OAAOrT,EAAO,EAAGkT,GAE5BvF,EAAM4E,cAAgB5E,EAAM4E,aAAanN,KAAO8N,EAAa9N,KAC/DuI,EAAM4E,aAAeW,EAEzB,EACAI,YAAW,SAAC3F,EAAO4F,GACjB5F,EAAM2E,OAAS3E,EAAM2E,OAAOkB,QAAO,SAAAJ,GAAG,OAAIA,EAAIhO,KAAOmO,CAAO,IACxD5F,EAAM4E,cAAgB5E,EAAM4E,aAAanN,KAAOmO,IAClD5F,EAAM4E,aAAe,KAEzB,GAGIjD,GAAU,CAERmE,YAAW,SAAAvL,GAAa,OAAA0D,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAyD,EAAA/I,EAAA6L,EAAAlG,EAAA,OAAAP,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAII,OAJdiD,EAAMtH,EAANsH,OAClBE,EAAAA,EAAmBC,MAAMH,GAAQlD,EAAAM,EAAA,EAG/B7H,QAAQO,IAAI,mBAAkBgH,EAAAC,EAAA,EACPmH,IAAAA,IAAU,GAADrG,OAAIsG,EAAAA,GAAO,WAAW,CAAE3G,QAASkF,OAAkB,OA4BxD,OA5BrBzL,EAAQ6F,EAAAY,EACdnI,QAAQO,IAAI,gBAAiBmB,EAASC,QAGlC4L,EAAS,GACTsB,MAAMC,QAAQpN,EAAS5C,OACzBkB,QAAQO,IAAI,cAAemB,EAAS5C,KAAK8D,QACzC2K,EAAS7L,EAAS5C,KAEdyO,EAAO3K,OAAS,GAClB5C,QAAQO,IAAI,QAASgN,EAAOwB,MAAM,EAAG,KAEL,YAAzBC,EAAAA,GAAAA,GAAOtN,EAAS5C,OAAuC,OAAlB4C,EAAS5C,OACvDkB,QAAQO,IAAI,mBAGVgN,EADEsB,MAAMC,QAAQpN,EAAS5C,KAAKmQ,SACrBvN,EAAS5C,KAAKmQ,QACdJ,MAAMC,QAAQpN,EAAS5C,KAAKA,MAC5B4C,EAAS5C,KAAKA,KACd+P,MAAMC,QAAQpN,EAAS5C,KAAKoQ,OAC5BxN,EAAS5C,KAAKoQ,MAGdnV,OAAOoV,OAAOzN,EAAS5C,MAAM2P,QAAO,SAAAW,GAAI,OAAIA,GAAwB,YAAhBJ,EAAAA,GAAAA,GAAOI,EAAiB,KAIzFpP,QAAQO,IAAI,cAAegN,EAAO3K,QAClC6H,EAAO,YAAa8C,GAAOhG,EAAAE,EAAA,EACpB8F,GAAM,OAG2B,MAH3BhG,EAAAM,EAAA,EAAAR,EAAAE,EAAAY,EAEbnI,QAAQC,MAAM,YAAWoH,GACzBsD,EAAAA,EAAmB1K,MAAMwK,EAAMpD,GAASA,EAAA,OAGT,OAHSE,EAAAM,EAAA,EAGxC8C,EAAAA,EAAmBY,IAAId,GAAQlD,EAAAoB,EAAA,iBAAApB,EAAAE,EAAA,MAAAT,EAAA,qBAxCLH,EA0C9B,EAGMwI,oBAAmB,SAAA5D,EAAa9J,GAAQ,OAAAkF,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAA4E,IAAA,IAAAlB,EAAA/I,EAAA6L,EAAA3B,EAAA,OAAA9E,EAAAA,EAAAA,KAAAQ,GAAA,SAAAuE,GAAA,eAAAA,EAAArE,GAAA,OACX,OADPiD,EAAMgB,EAANhB,OAC1BE,EAAAA,EAAmBC,MAAMH,GAAQoB,EAAAhE,EAAA,EAAAgE,EAAArE,EAAA,EAGRmH,IAAAA,IAAU,GAADrG,OAAIsG,EAAAA,GAAO,mBAAAtG,OAAkB3G,GAAU,CAAEsG,QAASkF,OAAkB,OAGzE,OAHrBzL,EAAQmK,EAAA1D,EAERoF,EAASsB,MAAMC,QAAQpN,EAAS5C,MAAQ4C,EAAS5C,KAAO,GAC9D2L,EAAO,YAAa8C,GAAO1B,EAAApE,EAAA,EACpB8F,GAAM,OAE2B,MAF3B1B,EAAAhE,EAAA,EAAA+D,EAAAC,EAAA1D,EAEbwC,EAAAA,EAAmB1K,MAAMwK,EAAMmB,GAASA,EAAA,OAGT,OAHSC,EAAAhE,EAAA,EAGxC8C,EAAAA,EAAmBY,IAAId,GAAQoB,EAAAlD,EAAA,iBAAAkD,EAAApE,EAAA,MAAAkE,EAAA,qBAbW9E,EAe9C,EAGMyI,eAAc,SAAAvD,EAAa1L,GAAI,OAAAwG,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAoF,IAAA,IAAA1B,EAAA/I,EAAA0K,EAAA,OAAAtF,EAAAA,EAAAA,KAAAQ,GAAA,SAAA+E,GAAA,eAAAA,EAAA7E,GAAA,OACF,OADZiD,EAAMsB,EAANtB,OACrBE,EAAAA,EAAmBC,MAAMH,GAAQ4B,EAAAxE,EAAA,EAAAwE,EAAA7E,EAAA,EAGRmH,IAAAA,IAAU,GAADrG,OAAIsG,EAAAA,GAAO,YAAAtG,OAAWjI,GAAM,CAAE4H,QAASkF,OAAkB,OACjD,OADlCzL,EAAQ2K,EAAAlE,EACdsC,EAAO,kBAAmB/I,EAAS5C,MAAKuN,EAAA5E,EAAA,EACjC/F,EAAS5C,MAAI,OAEoB,MAFpBuN,EAAAxE,EAAA,EAAAuE,EAAAC,EAAAlE,EAEpBwC,EAAAA,EAAmB1K,MAAMwK,EAAM2B,GAASA,EAAA,OAGT,OAHSC,EAAAxE,EAAA,EAGxC8C,EAAAA,EAAmBY,IAAId,GAAQ4B,EAAA1D,EAAA,iBAAA0D,EAAA5E,EAAA,MAAA0E,EAAA,qBAXEtF,EAarC,EAGM0I,YAAW,SAAArD,EAAasD,GAAW,OAAA3I,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAyF,IAAA,IAAA/B,EAAA/I,EAAAgL,EAAA,OAAA5F,EAAAA,EAAAA,KAAAQ,GAAA,SAAAqF,GAAA,eAAAA,EAAAnF,GAAA,OACN,OADfiD,EAAMyB,EAANzB,OAClBE,EAAAA,EAAmBC,MAAMH,GAAQkC,EAAA9E,EAAA,EAAA8E,EAAAnF,EAAA,EAGRmH,IAAAA,KAAW,GAADrG,OAAIsG,EAAAA,GAAO,WAAWY,EAAW,CAAEvH,QAASkF,OAAkB,OAC9D,OAD3BzL,EAAQiL,EAAAxE,EACdsC,EAAO,WAAY/I,EAAS5C,MAAK6N,EAAAlF,EAAA,EAC1B/F,EAAS5C,MAAI,OAEoB,MAFpB6N,EAAA9E,EAAA,EAAA6E,EAAAC,EAAAxE,EAEpBwC,EAAAA,EAAmB1K,MAAMwK,EAAMiC,GAASA,EAAA,OAGT,OAHSC,EAAA9E,EAAA,EAGxC8C,EAAAA,EAAmBY,IAAId,GAAQkC,EAAAhE,EAAA,iBAAAgE,EAAAlF,EAAA,MAAA+E,EAAA,qBAXM3F,EAazC,EAGMqH,YAAW,SAAA3B,EAAAO,GAAgC,OAAAjG,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAgG,IAAA,IAAAtC,EAAApK,EAAAmP,EAAA9N,EAAAsL,EAAA,OAAAlG,EAAAA,EAAAA,KAAAQ,GAAA,SAAA2F,GAAA,eAAAA,EAAAzF,GAAA,OACd,OADfiD,EAAM8B,EAAN9B,OAAYpK,EAAEyM,EAAFzM,GAAImP,EAAS1C,EAAT0C,UAClC7E,EAAAA,EAAmBC,MAAMH,GAAQwC,EAAApF,EAAA,EAAAoF,EAAAzF,EAAA,EAGRmH,IAAAA,IAAU,GAADrG,OAAIsG,EAAAA,GAAO,YAAAtG,OAAWjI,GAAMmP,EAAW,CAAEvH,QAASkF,OAAkB,OAChE,OAD9BzL,EAAQuL,EAAA9E,EACdsC,EAAO,cAAe/I,EAAS5C,MAAKmO,EAAAxF,EAAA,EAC7B/F,EAAS5C,MAAI,OAEoB,MAFpBmO,EAAApF,EAAA,EAAAmF,EAAAC,EAAA9E,EAEpBwC,EAAAA,EAAmB1K,MAAMwK,EAAMuC,GAASA,EAAA,OAGT,OAHSC,EAAApF,EAAA,EAGxC8C,EAAAA,EAAmBY,IAAId,GAAQwC,EAAAtE,EAAA,iBAAAsE,EAAAxF,EAAA,MAAAsF,EAAA,qBAXclG,EAajD,EAGM4I,kBAAiB,SAAAC,EAAAC,GAAsD,OAAA9I,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAA6I,IAAA,IAAAnF,EAAApK,EAAAsB,EAAAkO,EAAAC,EAAApO,EAAAqO,EAAA,OAAAjJ,EAAAA,EAAAA,KAAAQ,GAAA,SAAA0I,GAAA,eAAAA,EAAAxI,GAAA,OAC1C,OADTiD,EAAMiF,EAANjF,OAAYpK,EAAEsP,EAAFtP,GAAIsB,EAAMgO,EAANhO,OAAQkO,EAAUF,EAAVE,WAAYC,EAAWH,EAAXG,YAC5DnF,EAAAA,EAAmBC,MAAMH,GAAQuF,EAAAnI,EAAA,EAAAmI,EAAAxI,EAAA,EAGzBmH,IAAAA,IAAU,GAADrG,OACVsG,EAAAA,GAAO,YAAAtG,OAAWjI,EAAE,WACvB,KACA,CACE4P,OAAQ,CAAEtO,OAAAA,EAAQkO,WAAAA,EAAYC,YAAAA,GAC9B7H,QAASkF,OAEZ,cAAA6C,EAAAxI,EAAA,EAGsBmH,IAAAA,IAAU,GAADrG,OAAIsG,EAAAA,GAAO,YAAAtG,OAAWjI,GAAM,CAAE4H,QAASkF,OAAkB,OACrD,OAD9BzL,EAAQsO,EAAA7H,EACdsC,EAAO,cAAe/I,EAAS5C,MAAKkR,EAAAvI,EAAA,EAC7B/F,EAAS5C,MAAI,OAEoB,MAFpBkR,EAAAnI,EAAA,EAAAkI,EAAAC,EAAA7H,EAEpBwC,EAAAA,EAAmB1K,MAAMwK,EAAMsF,GAASA,EAAA,OAGT,OAHSC,EAAAnI,EAAA,EAGxC8C,EAAAA,EAAmBY,IAAId,GAAQuF,EAAArH,EAAA,iBAAAqH,EAAAvI,EAAA,MAAAmI,EAAA,qBArB0C/I,EAuB7E,EAGMqJ,YAAW,SAAAC,EAAa9P,GAAI,OAAAwG,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAqJ,IAAA,IAAA3F,EAAA4F,EAAAC,EAAA,OAAAxJ,EAAAA,EAAAA,KAAAQ,GAAA,SAAAiJ,GAAA,eAAAA,EAAA/I,GAAA,OACC,OADfiD,EAAM0F,EAAN1F,OAClBE,EAAAA,EAAmBC,MAAMH,GAAQ8F,EAAA1I,EAAA,EAAA0I,EAAA/I,EAAA,EAIzBlG,EAAAA,WAAIiM,OAAM,UAAQlN,GAAG,OACF,OAAzBoK,EAAO,cAAepK,GAAGkQ,EAAA9I,EAAA,EAClB,CAAEjE,SAAS,IAAM,OAaS,OAbT+M,EAAA1I,EAAA,EAAAyI,EAAAC,EAAApI,EAEpBkI,EAAe,OAEfC,EAAM5O,UAAY4O,EAAM5O,SAAS5C,KAEnCuR,EAA8C,kBAAxBC,EAAM5O,SAAS5C,KACjCwR,EAAM5O,SAAS5C,KACfY,KAAK8Q,UAAUF,EAAM5O,SAAS5C,MAElCuR,GAAgB,MAAQC,EAAM7M,SAAW,QAG3CgH,EAAO,WAAY4F,GAAcE,EAAA9I,EAAA,EAC1B,CAAEjE,SAAS,EAAOvD,MAAOoQ,IAAc,OAEf,OAFeE,EAAA1I,EAAA,EAE9C8C,EAAAA,EAAmBY,IAAId,GAAQ8F,EAAA5H,EAAA,iBAAA4H,EAAA9I,EAAA,MAAA2I,EAAA,qBAvBDvJ,EAyBlC,EAGM4J,WAAU,SAAAC,GAAkC,OAAA7J,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAA4J,IAAA,IAAA9O,EAAA,OAAAiF,EAAAA,EAAAA,KAAAQ,GAAA,SAAAsJ,GAAA,eAAAA,EAAApJ,GAAA,OAAJ,OAA3B3F,EAAQ6O,EAAR7O,SAAmB6O,EAATG,UAAiBH,EAANjG,OAAMmG,EAAAnJ,EAAA,EAErC5F,EAAS,aAAc,KAAM,CAAEiP,MAAM,KAAO,GAAAH,EAAA,IAFH9J,EAGlD,GAGF,UACE+B,MAAAA,GACAK,QAAAA,GACAgB,UAAAA,GACAM,QAAAA,ICzOF,IAAM3B,GAAQ,CACZ,EAGIK,GAAU,CAEdY,YAAa,SAACjB,EAAOK,EAAS4H,GAAS,OAAKA,EAAUhG,KAAKtJ,KAAK,EAChEwP,eAAgB,SAACnI,EAAOK,EAAS4H,GAAS,OAAKA,EAAUhG,KAAK7B,kBAAkB,EAChFe,eAAgB,SAACnB,EAAOK,EAAS4H,GAAS,OAAKA,EAAUhG,KAAK1D,OAAO,EACrE6C,cAAe,SAACpB,EAAOK,EAAS4H,GAAS,OAAKA,EAAUhG,KAAK5K,KAAK,GAG9DgK,GAAY,CAChB,EAGIM,GAAU,CAERsC,WAAU,SAAA1J,GAAe,OAAA0D,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAnF,EAAA,OAAAiF,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAAJ,OAAR3F,EAAQsB,EAARtB,SAAQ0F,EAAAE,EAAA,EAClB5F,EAAS,kBAAmB,KAAM,CAAEiP,MAAM,KAAO,GAAA9J,EAAA,IAD3BH,EAE/B,EAGMmK,UAAS,SAAAvF,EAAexE,GAAQ,OAAAJ,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAA4E,IAAA,IAAA9J,EAAA,OAAAiF,EAAAA,EAAAA,KAAAQ,GAAA,SAAAuE,GAAA,eAAAA,EAAArE,GAAA,OAAZ,OAAR3F,EAAQ4J,EAAR5J,SAAQgK,EAAApE,EAAA,EACjB5F,EAAS,wBAAyBoF,EAAQ,CAAE6J,MAAM,KAAO,GAAAnF,EAAA,IAD5B9E,EAEtC,GAGF,UACE+B,MAAAA,GACAK,QAAAA,GACAgB,UAAAA,GACAM,QAAAA,IC9BF,IAAM3B,GAAQ,CACZqI,MAAO,CACLpM,WAAY,EACZqM,WAAY,EACZlM,cAAe,EACfG,eAAgB,EAChBG,cAAe,EACfK,cAAe,GAEjBwB,SAAS,EACTlH,MAAO,MAGHgJ,GAAU,CACdkI,SAAU,SAAAvI,GAAK,OAAIA,EAAMqI,KAAK,EAC9BG,eAAgB,SAAAxI,GAAK,OAAIA,EAAMzB,OAAO,EACtCkK,cAAe,SAAAzI,GAAK,OAAIA,EAAM3I,KAAK,EAEnCqR,eAAgB,SAAA1I,GAAK,OAAIA,EAAMqI,KAAK,GAGhChH,GAAY,CAChBsH,SAAQ,SAAC3I,EAAOqI,GACdjR,QAAQO,IAAI,0BAA2B0Q,GAGvCrI,EAAMqI,MAAQ,CACZpM,WAAY2D,SAASyI,EAAMpM,aAAe,EAC1CqM,WAAY1I,SAASyI,EAAMC,aAAe,EAC1ClM,cAAewD,SAASyI,EAAMjM,eAAiBiM,EAAMO,eAAiB,EACtErM,eAAgBqD,SAASyI,EAAM9L,iBAAmB,EAClDG,cAAekD,SAASyI,EAAM3L,gBAAkB,EAChDK,cAAe6C,SAASyI,EAAMtL,gBAAkB,GAGlD3F,QAAQO,IAAI,kBAAmBqI,EAAMqI,OAGrCpI,EAAAA,EAAayC,cAAc,iBAAkB1C,EAAMqI,MACrD,EACA5G,WAAU,SAACzB,EAAOjH,GAChBiH,EAAMzB,QAAUxF,CAClB,EACA2I,SAAQ,SAAC1B,EAAO3I,GACd2I,EAAM3I,MAAQA,CAChB,EAEAwR,aAAY,SAAC7I,EAAKzF,GAAoB,IAAhBuO,EAAKvO,EAALuO,MAAOtO,EAAKD,EAALC,MACvBwF,EAAMqI,MAAMU,eAAeD,IAC7B9I,EAAMqI,MAAMS,GAASlJ,SAASpF,IAAU,EACxCpD,QAAQO,IAAI,UAAD+H,OAAWoJ,EAAK,OAAApJ,OAAMM,EAAMqI,MAAMS,MAE7C1R,QAAQC,MAAM,eAADqI,OAAgBoJ,GAEjC,GAGInH,GAAU,CAERkG,WAAU,SAAAhF,GAAa,OAAA5E,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAyD,EAAAxD,EAAAxH,EAAAiC,EAAA5C,EAAA8S,EAAAvK,EAAA,OAAAP,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAWxB,GAXciD,EAAMgB,EAANhB,OACjBE,EAAAA,EAAmBC,MAAMH,GAAQlD,EAAAM,EAAA,EAG/B7H,QAAQO,IAAI,2CAGR0G,EAAS,KACPxH,EAAOoJ,EAAAA,EAAaC,eAAe,QACrCrJ,IACFwH,EAASxH,EAAKY,IAGX4G,EAAQ,CAAFM,EAAAC,EAAA,cACH,IAAIa,MAAM,yBAAwB,OAIV,OAFhCrI,QAAQO,IAAI,WAAY0G,GAExBjH,QAAQO,IAAI,oBAAoBgH,EAAAC,EAAA,EACTlG,EAAAA,WAAI2P,MAAMY,aAAa5K,GAAO,OAId,GAJjCvF,EAAQ6F,EAAAY,EACdnI,QAAQO,IAAI,WAAYmB,GACxB1B,QAAQO,IAAI,YAAamB,EAASC,QAClC3B,QAAQO,IAAI,cAAYyO,EAAAA,GAAAA,GAAStN,EAAS5C,OAC1CkB,QAAQO,IAAI,WAAYmB,EAAS5C,OAE7B4C,IAAYA,EAAS5C,KAAI,CAAAyI,EAAAC,EAAA,QAaU,OAX/B1I,EAAO4C,EAAS5C,KACtBkB,QAAQO,IAAI,WACZP,QAAQO,IAAI,gBAAiBzB,EAAK+F,YAAUmK,EAAAA,GAAAA,GAASlQ,EAAK+F,aAC1D7E,QAAQO,IAAI,gBAAiBzB,EAAKoS,YAAUlC,EAAAA,GAAAA,GAASlQ,EAAKoS,aAC1DlR,QAAQO,IAAI,mBAAoBzB,EAAKkG,eAAagK,EAAAA,GAAAA,GAASlQ,EAAKkG,gBAChEhF,QAAQO,IAAI,oBAAqBzB,EAAKqG,gBAAc6J,EAAAA,GAAAA,GAASlQ,EAAKqG,iBAClEnF,QAAQO,IAAI,mBAAoBzB,EAAKwG,eAAa0J,EAAAA,GAAAA,GAASlQ,EAAKwG,gBAChEtF,QAAQO,IAAI,mBAAoBzB,EAAK6G,eAAaqJ,EAAAA,GAAAA,GAASlQ,EAAK6G,gBAEhE3F,QAAQO,IAAI,yBACZkK,EAAO,WAAY/I,EAAS5C,MAC5BkB,QAAQO,IAAI,yBAAyBgH,EAAAE,EAAA,EAC9B/F,EAAS5C,MAAI,aAEd,IAAIuJ,MAAM,WAAU,OAAAd,EAAAC,EAAA,eAAAD,EAAAM,EAAA,EAAAR,EAAAE,EAAAY,EAG5BnI,QAAQC,MAAM,YAAWoH,GAGzBsD,EAAAA,EAAmB1K,MAAMwK,EAAMpD,GAGzBuK,EAAe,CACnB/M,WAAY,EACZqM,WAAY,EACZlM,cAAe,EACfG,eAAgB,EAChBG,cAAe,EACfK,cAAe,GAGjB3F,QAAQO,IAAI,iBAAkBqR,GAC9BnH,EAAO,WAAYmH,GAAc,OAGF,OAHErK,EAAAM,EAAA,EAEjC7H,QAAQO,IAAI,2CACZoK,EAAAA,EAAmBY,IAAId,GAAQlD,EAAAoB,EAAA,iBAAApB,EAAAE,EAAA,MAAAT,EAAA,qBA/DNH,EAiE7B,EAGAiL,eAAc,SAAA/F,EAAagG,GAAW,IAArBtH,EAAMsB,EAANtB,OACfzK,QAAQO,IAAI,YAAawR,GACzBtH,EAAO,WAAYsH,EACrB,GAGF,UACEnJ,MAAAA,GACAK,QAAAA,GACAgB,UAAAA,GACAM,QAAAA,I,wDC3II3B,GAAQ,CACZoJ,KAAM,EACNxD,QAAS,KACTyD,SAAU,KACVC,UAAU,EACVC,UAAW,KACXC,YAAa,MAGTnJ,GAAU,CACdoJ,sBAAuB,SAAAzJ,GACrB,MAAO,CACLoJ,KAAMpJ,EAAMoJ,KACZxD,QAAS5F,EAAM4F,QACfyD,SAAUrJ,EAAMqJ,SAChBC,SAAUtJ,EAAMsJ,SAChBC,UAAWvJ,EAAMuJ,UACjBC,YAAaxJ,EAAMwJ,YAEvB,EACAE,wBAAyB,SAAA1J,GACvB,OAAOA,EAAMoJ,KAAO,GAAuB,OAAlBpJ,EAAM4F,OACjC,EACA+D,eAAgB,SAAA3J,GAAK,OAAIA,EAAMoJ,IAAI,EACnCQ,WAAY,SAAA5J,GAAK,OAAIA,EAAM4F,OAAO,EAClCiE,YAAa,SAAA7J,GAAK,OAAIA,EAAMqJ,QAAQ,EACpCS,WAAY,SAAA9J,GAAK,OAAIA,EAAMsJ,QAAQ,EACnCS,aAAc,SAAA/J,GAAK,OAAIA,EAAMuJ,SAAS,EACtCS,0BAA2B,SAAAhK,GACzB,MAAO,CACLqJ,SAAUrJ,EAAMqJ,SAEpB,GAGI1H,GAAU,CACdsI,aAAY,SAAA1P,EAAa2P,GAAU,IAApBrI,EAAMtH,EAANsH,OACbA,EAAO,eAAgBqI,EACzB,EACAC,mBAAkB,SAAAtH,GAAa,IAAVhB,EAAMgB,EAANhB,OACnBA,EAAO,sBACT,EACAuI,eAAc,SAAAjH,EAAakG,GAAU,IAApBxH,EAAMsB,EAANtB,OACfA,EAAO,mBAAoBwH,EAC7B,EAEAgB,uBAAsB,SAAA/G,EAAAK,GAA0C,IAAvC9B,EAAMyB,EAANzB,OAAYuH,EAAIzF,EAAJyF,KAAMxD,EAAOjC,EAAPiC,QAASyD,EAAQ1F,EAAR0F,SAClDxH,EAAO,eAAgB,CAAEuH,KAAAA,EAAMxD,QAAAA,EAASyD,SAAAA,GAC1C,EACAiB,wBAAuB,SAAApG,GAAa,IAAVrC,EAAMqC,EAANrC,OACxBA,EAAO,sBACT,GAGIR,GAAY,CAChBkJ,aAAY,SAACvK,EAAOkK,GAKlB,GAJAlK,EAAMoJ,KAAOc,EAASd,MAAQpJ,EAAMoJ,KACpCpJ,EAAM4F,QAAUsE,EAAStE,SAAW5F,EAAM4F,QAGtCsE,EAASb,SAAU,CAGrB,IAAImB,EAAqB,KACrBN,EAASb,WAEXmB,EAAqB,CAAC,EACtBrZ,OAAOsZ,KAAKP,EAASb,UAAUqB,SAAQ,SAAArX,GAErC,IAAMmH,EAAQ0P,EAASb,SAAShW,GAChC,GAAqB,YAAjB+S,EAAAA,GAAAA,GAAO5L,IAAgC,OAAVA,EAC/BgQ,EAAmBnX,GAAOmH,OACrB,GAAIyL,MAAMC,QAAQ1L,GAEvBgQ,EAAmBnX,GAAOmH,EAAMmQ,KAAI,SAAAnE,GAAI,MACtB,kBAATA,EAAoBA,EAAO1P,KAAK8Q,UAAUpB,EAAK,SAIxD,IACEgE,EAAmBnX,GAAOyD,KAAK8Q,UAAUpN,EAC3C,CAAE,MAAMO,GACN3D,QAAQmL,KAAK,eAAgBlP,EAC/B,CAEJ,KAGF2M,EAAMqJ,SAAWmB,GAAsBN,EAASb,QAClD,CAaA,GAViC,qBAAtBa,EAASZ,WAClBtJ,EAAMsJ,SAAWY,EAASZ,UAIxBY,EAASX,YACXvJ,EAAMuJ,UAAYW,EAASX,YAIxBW,EAASZ,UAAoD,SAAxCtS,aAAaC,QAAQ,iBAA8BiT,EAAStE,QACpF,IACE3F,EAAAA,EAAayC,cAAc,oBAADhD,OAAqBwK,EAAStE,SAAW,CACjEwD,KAAMpJ,EAAMoJ,KACZC,SAAUrJ,EAAMqJ,SAChBC,SAAUtJ,EAAMsJ,SAChBC,UAAWvJ,EAAMuJ,WAErB,CAAE,MAAOxO,GACP3D,QAAQC,MAAM,WAAY0D,EAC5B,CAGFiF,EAAMwJ,aAAc,IAAIzL,MAAO6M,cAC/BxT,QAAQO,IAAI,UAAWqI,EACzB,EAEA6K,iBAAgB,SAAC7K,EAAOqJ,GAKtB,GAJArJ,EAAMqJ,SAAWA,EACjBrJ,EAAMwJ,aAAc,IAAIzL,MAAO6M,eAG1B5K,EAAMsJ,UAAoD,SAAxCtS,aAAaC,QAAQ,iBAA8B+I,EAAM4F,QAC9E,IACE3F,EAAAA,EAAayC,cAAc,gBAADhD,OAAiBM,EAAM4F,SAAWyD,GAG5DpJ,EAAAA,EAAayC,cAAc,oBAADhD,OAAqBM,EAAM4F,SAAW,CAC9DwD,KAAMpJ,EAAMoJ,KACZC,SAAUA,EACVC,SAAUtJ,EAAMsJ,SAChBC,UAAWvJ,EAAMuJ,WAErB,CAAE,MAAOxO,GACP3D,QAAQC,MAAM,aAAc0D,EAC9B,CAEJ,EAEA+P,oBAAmB,SAAC9K,GAElB,GAAIA,EAAM4F,QACR,IACE3F,EAAAA,EAAamD,kBAAkB,oBAAD1D,OAAqBM,EAAM4F,SAC3D,CAAE,MAAO7K,GACP3D,QAAQC,MAAM,WAAY0D,EAC5B,CAIFiF,EAAMoJ,KAAO,EACbpJ,EAAM4F,QAAU,KAChB5F,EAAMqJ,SAAW,KACjBrJ,EAAMsJ,UAAW,EACjBtJ,EAAMuJ,UAAY,KAClBvJ,EAAMwJ,aAAc,IAAIzL,MAAO6M,cAC/BxT,QAAQO,IAAI,SACd,GAGF,UACEqI,MAAAA,GACAK,QAAAA,GACAsB,QAAAA,GACAN,UAAAA,ICrKF,IAAMrB,GAAQ,CAEZ4I,aAAc,EAEdY,YAAa,MAGTnJ,GAAU,CAEd0K,4BAA6B,SAAA/K,GAAK,OAAIA,EAAM4I,YAAY,EAGxDpS,uBAAwB,SAAAwJ,GAAK,OAAIA,EAAM4I,aAAe,CAAC,EAGvDoC,eAAgB,SAAAhL,GAAK,OAAIA,EAAMwJ,WAAW,GAGtCnI,GAAY,CAEhB4J,gBAAe,SAACjL,EAAOkL,GACrBlL,EAAM4I,aAAesC,EACrBlL,EAAMwJ,aAAc,IAAIzL,MAAO6M,aACjC,EAGAO,sBAAqB,SAACnL,GACpBA,EAAM4I,eACN5I,EAAMwJ,aAAc,IAAIzL,MAAO6M,aACjC,EAGAQ,sBAAqB,SAACpL,GAChBA,EAAM4I,aAAe,IACvB5I,EAAM4I,eACN5I,EAAMwJ,aAAc,IAAIzL,MAAO6M,cAEnC,EAGAS,kBAAiB,SAACrL,GAChBA,EAAM4I,aAAe,EACrB5I,EAAMwJ,aAAc,IAAIzL,MAAO6M,aACjC,GAGIjJ,GAAU,CAER2J,8BAA6B,SAAA/Q,GAAwB,OAAA0D,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAyD,EAAAoG,EAAAsD,EAAA1U,EAAA2U,EAAA1S,EAAA2F,EAAA,OAAAP,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAI3B,GAJMiD,EAAMtH,EAANsH,OAAQoG,EAAS1N,EAAT0N,UAAStJ,EAAAM,EAAA,EAG7CpI,EAAOoR,EAAUhG,KAAKpL,MAAQC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACzEuU,EAAkB,QAAZD,EAAG1U,EAAKwL,YAAI,IAAAkJ,OAAA,EAATA,EAAW9T,GAErB+T,EAAQ,CAAF7M,EAAAC,EAAA,QAEkB,OAD3BxH,QAAQO,IAAI,wBACZkK,EAAO,qBAAoBlD,EAAAE,EAAA,iBAAAF,EAAAC,EAAA,EAKNlG,EAAAA,WAAI+S,MAAMC,oBAAoBF,EAAQ,WAAU,OAAjE1S,EAAQ6F,EAAAY,EAEVzG,GAAYA,EAAS5C,MAEvB2L,EAAO,kBAAmB/I,EAAS5C,KAAK8D,QACxC5C,QAAQO,IAAI,KAAD+H,OAAM8L,EAAM,KAAA9L,OAAI5G,EAAS5C,KAAK8D,OAAM,YAE/C6H,EAAO,qBACRlD,EAAAC,EAAA,eAAAD,EAAAM,EAAA,EAAAR,EAAAE,EAAAY,EAEDnI,QAAQC,MAAM,cAAaoH,GAE3BoD,EAAO,qBAAoB,cAAAlD,EAAAE,EAAA,MAAAT,EAAA,iBAzB4BH,EA2B3D,EAGA0N,mBAAkB,SAAA9I,EAAAM,GAAyB,IAAtBtB,EAAMgB,EAANhB,OAAY+J,EAAMzI,EAANyI,OAChB,aAAXA,GAAoC,aAAXA,GAC3B/J,EAAO,wBAEX,GAGF,UACEgK,YAAY,EACZ7L,MAAAA,GACAK,QAAAA,GACAsB,QAAAA,GACAN,UAAAA,IClFF,IAAeyK,EAAAA,EAAAA,IAAY,CACzB9L,MAAO,CACL,EAEFK,QAAS,CACPF,gBAAiB,SAAAH,GAAK,OAAIA,EAAMiC,KAAK9B,eAAe,GAEtDkB,UAAW,CAETgJ,uBAAsB,SAACrK,EAAO+L,GAC5B,EAEFzB,wBAAuB,SAACtK,GACtB,GAGJ2B,QAAS,CAEPsI,aAAY,SAAA1P,EAAewR,GAAS,IAArB9S,EAAQsB,EAARtB,SACb,OAAOA,EAAS,oCAAqC8S,EACvD,EAEA5B,mBAAkB,SAAAtH,GAAe,IAAZ5J,EAAQ4J,EAAR5J,SACnB,OAAOA,EAAS,qCAClB,GAEF+S,QAAS,CACP/J,KAAAA,GACA0C,OAAAA,GACAhM,MAAAA,GACA0P,MAAAA,GACA4D,WAAAA,GACAC,iBAAAA,IAEFC,QAAS,EACPC,EAAAA,EAAAA,GAAqB,CACnBC,MAAO,CAAC,OAAQ,aAAc,oBAC9BC,QAAS,CACPrV,QAAS,SAAA5D,GACP,IACE,OAAO2D,aAAaC,QAAQ5D,EAC9B,CAAE,MAAO8D,GAEP,OADAC,QAAQC,MAAM,8BAA+BF,GACtC,IACT,CACF,EACAW,QAAS,SAACzE,EAAKmH,GACb,IAEMA,GAASA,EAAMR,OAAS,MAC1B5C,QAAQmL,KAAK,yBACbvL,aAAawC,WAAW,SAE1BxC,aAAac,QAAQzE,EAAKmH,EAC5B,CAAE,MAAOrD,GACPC,QAAQC,MAAM,8BAA+BF,GAE7C,IACE,IAAK,IAAIoV,EAAI,EAAGA,EAAIvV,aAAagD,OAAQuS,IAAK,CAC5C,IAAMlZ,EAAM2D,aAAa3D,IAAIkZ,GACjB,SAARlZ,GAA0B,UAARA,GACpB2D,aAAawC,WAAWnG,EAE5B,CACA2D,aAAac,QAAQzE,EAAKmH,EAC5B,CAAE,MAAOO,GACP3D,QAAQC,MAAM,8BAA+B0D,EAC/C,CACF,CACF,EACAvB,WAAY,SAAAnG,GACV,IACE2D,aAAawC,WAAWnG,EAC1B,CAAE,MAAO8D,GACPC,QAAQC,MAAM,iCAAkCF,EAClD,CACF,QC7ER,IAAMqV,GAAY,WAAH,OAAS,8BAAgC,EAElDC,GAAsB,WAAH,OAAS,8BAA0C,EAEtEC,GAAS,CACb,CACE9V,KAAM,OACN+V,UAAWC,EACXC,KAAM,CAAEC,cAAc,GACtBC,SAAU,CACR,CACEnW,KAAM,GACNoW,SAAU,kBAEZ,CACEpW,KAAM,YACNrF,KAAM,YACNob,UAAWM,EACXJ,KAAM,CACJK,WAAW,EACXC,MAAO,QAGX,CACEvW,KAAM,QACNrF,KAAM,QACNob,UAAW,WAAF,OAAQ,6BAA2B,GAI9C,CACE/V,KAAM,iBACNrF,KAAM,WACNob,UAAW,WAAF,OAAQ,8BAA8B,GAajD,CACE/V,KAAM,6BACNrF,KAAM,oBACNob,UAAW,WAAF,OAAQ,8BAAuC,EACxDS,OAAO,EACPL,SAAU,CACR,CACEnW,KAAM,GACNrF,KAAM,eACNob,UAAW,WAAF,OAAQ,8BAAwC,EAC7DS,OAAO,KAIT,CACExW,KAAM,SACNrF,KAAM,SACNob,UAAW,WAAF,OAAQ,8BAA4B,GAE/C,CACE/V,KAAM,QACNrF,KAAM,QACNob,UAAW,WAAF,OAAQ,qDAA2B,GAE9C,CACE/V,KAAM,qBACNrF,KAAM,mBACNob,UAAW,WAAF,OAAQ,qDAAoD,EACrEE,KAAM,CACJC,cAAc,EACdK,MAAO,SACPE,YAAa,CAAC,QAAS,cAG3B,CACEzW,KAAM,QACNrF,KAAM,QACNob,UAAW,WAAF,OAAQ,8BAA2B,EAC5CE,KAAM,CACJS,eAAe,EACfH,MAAO,SAGX,CACEvW,KAAM,uBACNrF,KAAM,sBACNob,UAAWF,GACXI,KAAM,CACJC,cAAc,EACdK,MAAO,QACPE,YAAa,CAAC,QAAS,SAAU,cAGrC,CACEzW,KAAM,qBACNrF,KAAM,oBACNob,UAAW,WAAF,OAAQ,8BAAsC,EACvDE,KAAM,CACJC,cAAc,EACdK,MAAO,OAEPE,YAAa,CAAC,QAAS,cAG3B,CACEzW,KAAM,UACNrF,KAAM,cACNob,UAAW,WAAF,OAAQ,4BAAiC,EAClDE,KAAM,CACJC,cAAc,EACdK,MAAO,OACPE,YAAa,CAAC,QAAS,SAAU,gBAKzC,CACEzW,KAAM,IACNrF,KAAM,OACNyb,SAAU,UAEZ,CACEpW,KAAM,SACNrF,KAAM,QACNob,UAAW,WAAF,OAAQ,8BAA2B,GAE9C,CACE/V,KAAM,mBACNrF,KAAM,iBACNob,UAAW,WAAF,OAAQ,8BAAoC,GAEvD,CACE/V,KAAM,YACNrF,KAAM,WACNob,UAAW,WAAF,OAAQ,6BAA+B,GAElD,CACE/V,KAAM,UACNrF,KAAM,YACNob,UAAW,WAAF,OAAQ,4BAAgC,EACjDE,KAAM,CAAEC,cAAc,IAExB,CACElW,KAAM,cACNrF,KAAM,cACNob,UAAW,WAAF,OAAQ,8BAAkC,EACnDE,KAAM,CAAEC,cAAc,IAExB,CACElW,KAAM,eACNrF,KAAM,kBACNob,UAAW,WAAF,OAAQ,8BAAuC,GAE1D,CACE/V,KAAM,yBACNrF,KAAM,2BACNob,UAAW,WAAF,OAAQ,8BAAwC,GAE3D,CACE/V,KAAM,YACNrF,KAAM,UACNob,UAAW,WAAF,OAAQ,8BAAmC,GAEtD,CACE/V,KAAM,SACNrF,KAAM,QACNob,UAAW,WAAF,OAAQ,6BAA4B,EAC7CE,KAAM,CAAEC,cAAc,EAAMQ,eAAe,IAE7C,CACE1W,KAAM,+BACNrF,KAAM,uBACNob,UAAW,WAAF,OAAQ,4BAAiD,EAClEE,KAAM,CAAEC,cAAc,EAAMQ,eAAe,IAE7C,CACE1W,KAAM,yBACN+V,UAAWY,EACXR,SAAU,CACR,CACEnW,KAAM,GACNrF,KAAM,mBACNob,UAAW,WAAF,OAAQ,8BAAsC,EACvDS,OAAO,IAGXP,KAAM,CACJC,cAAc,EACdK,MAAO,OACPE,YAAa,CAAC,QAAS,cAG3B,CACEzW,KAAM,aACNrF,KAAM,QACNob,UAAWH,GACXK,KAAM,CACJC,cAAc,EACdK,MAAO,WAGX,CACEvW,KAAM,yBACNrF,KAAM,eACNob,UAAW,WAAF,OAAQ,8BAAwC,EACzDS,OAAO,GAET,CACExW,KAAM,mBACNoW,SAAU,WAKRQ,IAASC,EAAAA,EAAAA,IAAa,CAE1BC,SAASC,EAAAA,EAAAA,IAAiBhd,KAC1B+b,OAAAA,KAGFc,GAAOI,YAAW,SAACjZ,EAAIwI,EAAME,GAE3BjG,QAAQO,IAAI,eAAgB,CAC1BkW,EAAG1Q,EAAK2Q,SACRC,EAAGpZ,EAAGmZ,SACNE,IAAI,IAAIjQ,MAAOkQ,qBACfC,QAASvZ,EAAGiJ,MACZuQ,KAAMC,SAASC,WAIblR,EAAK5L,MACPgI,eAAezB,QAAQ,iBAAkB,QAI3C,IAAMwW,EAA4E,SAApD/U,eAAetC,QAAQ,yBAC/CsX,EAAwE,SAAjDhV,eAAetC,QAAQ,sBAGpD,IAAKqX,GAAyBC,IAAqC,WAAZ5Z,EAAGiC,KAIxD,OAHAQ,QAAQO,IAAI,8BACZ4B,eAAeC,WAAW,yBAC1BD,eAAeC,WAAW,sBACnB6D,EAAK,kBAId,IAAMmR,EAAc,CAAC,SAAU,YAAa,YAAa,oBACnDC,GAAgBD,EAAYE,SAAS/Z,EAAGiC,QAAUjC,EAAGga,QAAQ1U,MAAK,SAAA2U,GAAM,OAAIJ,EAAYE,SAASE,EAAOhY,KAAK,IAG7GC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SACvCkJ,EAAkB0O,GAAMxO,QAAQF,gBAuBtC,GApBgB,WAAZxL,EAAGiC,MAAsBuJ,GAC3B5G,eAAezB,QAAQ,eAAgBnD,EAAGmZ,UAI5B,mBAAZnZ,EAAGiC,MAAyC,SAAZjC,EAAGiC,MAA+B,UAAZjC,EAAGiC,MAKvDC,GAAQpG,OAAOmH,uBACjBR,QAAQO,IAAI,4BASZ8W,IAAiB5X,EACnB,OAAOwG,EAAK,UAId,IAAMyP,EAAenY,EAAGga,QAAQ1U,MAAK,SAAA2U,GAAM,OAAIA,EAAO/B,KAAKC,YAAY,IACvE,GAAIA,IAAiB3M,EAEnB,OAAIhD,EAAKvG,KAAK8X,SAAS,2BAA8E,SAAjDnV,eAAetC,QAAQ,uBACzEG,QAAQO,IAAI,wBACZ4B,eAAeC,WAAW,sBACnB6D,EAAK,mBAGPA,EAAK,UAId,IAAMyR,EAAWjY,EAAOA,EAAKK,KAAO,KACpC,GAAI4V,GAAgBgC,EAAU,CAE5B1X,QAAQO,IAAI,eAAgB,CAC1BoX,KAAMpa,EAAGiC,KACToY,KAAMF,EACNG,OAAQta,EAAGkY,KAAKQ,aAAe,UAC/B6B,QAASva,EAAGga,QAAQ1U,MAAK,SAAA2U,GAAM,OAAIA,EAAO/B,KAAKS,aAAa,IAC5D6B,SAAUxa,EAAGga,QAAQ1U,MAAK,SAAA2U,GAAM,OAAIA,EAAO/B,KAAKuC,gBAAgB,IAChEC,SAAU1a,EAAGga,QAAQ1U,MAAK,SAAA2U,GAAM,OAAIA,EAAO/B,KAAKyC,cAAc,MAIhE,IAAMhC,EAAgB3Y,EAAGga,QAAQ1U,MAAK,SAAA2U,GAAM,OAAIA,EAAO/B,KAAKS,aAAa,IACnE8B,EAAmBza,EAAGga,QAAQ1U,MAAK,SAAA2U,GAAM,OAAIA,EAAO/B,KAAKuC,gBAAgB,IACzEE,EAAiB3a,EAAGga,QAAQ1U,MAAK,SAAA2U,GAAM,OAAIA,EAAO/B,KAAKyC,cAAc,IAE3E,GACGhC,GAA8B,UAAbwB,GACjBM,GAAiC,aAAbN,GAAwC,UAAbA,GAC/CQ,GAA+B,WAAbR,GAAsC,UAAbA,EAI5C,OADA1X,QAAQmL,KAAK,uBAAD7C,OAAwBoP,EAAQ,UAAApP,OAAS/K,EAAGiC,KAAI,cACrDyG,EAAK,kBAId,IAAMkS,GAAY3O,EAAAA,EAAAA,IAAekO,EAAUna,EAAGiC,MAG9C,GAFAQ,QAAQO,IAAI,uBAAD+H,OAAwBoP,EAAQ,KAAApP,OAAI6P,EAAY,KAAO,KAAI,OAAA7P,OAAM/K,EAAGiC,QAE1E2Y,EAEH,OADAnY,QAAQmL,KAAK,yBAAD7C,OAA0BoP,EAAQ,UAAApP,OAAS/K,EAAGiC,KAAI,cACvDyG,EAAK,iBAEhB,CAGA,MAAgB,eAAZ1I,EAAGiC,KACEyG,EAAK,kBACS,WAAZ1I,EAAGiC,KACLyG,EAAK,mBAIdA,GACF,IAGAmQ,GAAOgC,WAAU,SAAC7a,EAAIwI,GAwBpB,GAtBA/F,QAAQO,IAAI,eAAgB,CAC1BkW,EAAG1Q,EAAK2Q,SACRC,EAAGpZ,EAAGmZ,SACNE,IAAI,IAAIjQ,MAAOkQ,qBACfwB,MAAOhf,OAAO4I,SAASC,KACvBoW,KAAM,CACJC,eAAgBpW,eAAetC,QAAQ,kBACvCqX,sBAAuB/U,eAAetC,QAAQ,yBAC9C2Y,qBAAsBrW,eAAetC,QAAQ,wBAC7C4Y,kBAAmBtW,eAAetC,QAAQ,wBAK9CsC,eAAezB,QAAQ,iBAAkB,QAGF,WAAnC0V,GAAOsC,aAAatV,MAAM5D,MAC5B2C,eAAezB,QAAQ,iBAAkB,QAI3B,mBAAZnD,EAAGiC,MAAyC,UAAZjC,EAAGiC,MAAgC,SAAZjC,EAAGiC,KAAiB,CAC7EQ,QAAQO,IAAI,gCAEZ,IAEE,IAAM2G,EAAUtH,aAAaC,QAAQ,QACrC,IAAKqH,EAAS,OAEd,IAAMzH,EAAOC,KAAKC,MAAMuH,GAElBD,EAASxH,EAAKY,GACpB,IAAK4G,EAAQ,OAGb,IAAM0R,EAAM,IAAIC,eAIhB,GAHAD,EAAIE,KAAK,MAAO,GAAFvQ,OAAKwQ,EAAAA,GAAmB,KAAAxQ,OAAIrB,EAAM,OAAAqB,OAAM3B,KAAKoS,MAAK,2CAA0C,GAC1GJ,EAAIK,OAEe,MAAfL,EAAIhX,OAAgB,CACtB3B,QAAQO,IAAI,mBACZ,IAAMzB,EAAOY,KAAKC,MAAMgZ,EAAIM,cAG5B5f,OAAOuL,eAAiB,CACtBC,WAAY2D,SAAS1J,EAAK+F,YAAc,GACxCqM,WAAY1I,SAAS1J,EAAKoS,YAAc,GACxClM,cAAewD,SAAS1J,EAAKkG,eAAiB,GAC9CG,eAAgBqD,SAAS1J,EAAKqG,gBAAkB,GAChDG,cAAekD,SAAS1J,EAAKwG,eAAiB,GAC9CK,cAAe6C,SAAS1J,EAAK6G,eAAiB,GAC9CuT,WAAY,iBACZC,UAAWxS,KAAKoS,OAIlBnZ,aAAac,QAAQ,iBAAkBhB,KAAK8Q,UAAUnX,OAAOuL,iBAG7DnE,YAAW,WACT,IAAM2Y,EAAepC,SAASqC,iBAAiB,eAC3CD,GAAgBA,EAAaxW,QAAU,IACzCwW,EAAa,GAAGE,YAAcxa,EAAK+F,YAAc,EACjDuU,EAAa,GAAGE,YAAcxa,EAAKoS,YAAc,EACjDkI,EAAa,GAAGE,YAAcxa,EAAKkG,eAAiB,EACpDoU,EAAa,GAAGE,YAAcxa,EAAKqG,gBAAkB,EACrDiU,EAAa,GAAGE,YAAcxa,EAAKwG,eAAiB,EACpDtF,QAAQO,IAAI,qBAEhB,GAAG,IACL,CACF,CAAE,MAAOoD,GACP3D,QAAQC,MAAM,mBAAoB0D,EACpC,CACF,CACF,IAEA,Y,uFC3aa2F,I,kBAAa,CACxBtI,QAAO,SAACuY,EAAIC,GACV,IAAQpW,EAAeoW,EAAfpW,MAAOqW,EAAQD,EAARC,IACT/B,EAAWD,GAAMxO,QAAQE,YAS/B,GANAnJ,QAAQO,IAAI,UAAW,CACrBmZ,IAAKtW,EACLuW,GAAIF,EACJ7B,KAAMF,IAGHA,EAML,GAAY,SAAR+B,EASJ,GAAI5K,MAAMC,QAAQ1L,GAEhB,GAAY,QAARqW,EAAe,CAEjB,IAAMG,EAAoBxW,EAAMyW,OAAM,SAAAvQ,GAAU,OAC9CwQ,EAAAA,EAAAA,IAAgBpC,EAAUpO,EAAW,IAElCsQ,GACHG,GAAeR,EAEnB,KAAO,CAEL,IAAMS,EAAmB5W,EAAMP,MAAK,SAAAyG,GAAU,OAC5CwQ,EAAAA,EAAAA,IAAgBpC,EAAUpO,EAAW,IAElC0Q,GACHD,GAAeR,EAEnB,KACK,CAEL,IAAMlQ,GAAgByQ,EAAAA,EAAAA,IAAgBpC,EAAUtU,GAChDpD,QAAQO,IAAI,WAAD+H,OAAYlF,EAAK,QAAAkF,OAAOe,EAAgB,MAAQ,QACtDA,GACH0Q,GAAeR,EAEnB,KAnCA,CACE,IAAMU,EAAQpL,MAAMC,QAAQ1L,GAASA,EAAQ,CAACA,GACzC6W,EAAM3C,SAASI,IAClBqC,GAAeR,EAGnB,MAXEQ,GAAeR,EAyCnB,IAIF,SAASQ,GAAeR,GAEtBA,EAAG/b,MAAM0c,QAAU,OAGnBX,EAAGY,aAAa,WAAY,YAG5BZ,EAAG/b,MAAM4c,cAAgB,OAGzBb,EAAGY,aAAa,2BAA4B,QAG5CZ,EAAGc,UAAUC,IAAI,sBACnB,CAGO,SAASC,GAA4BzX,GAC1CA,EAAI0X,UAAU,aAAclR,GAC9B,C,gBClFAjQ,OAAOohB,cAAgBphB,OAAOohB,eAAiB,CAAC,EAChDphB,OAAOohB,cAAcC,qBAAuBA,GAAAA,EAG1CA,GAAAA,E,4BCmB+BC,GAAAA,GAAUnX,QACZmX,GAAAA,GAAU1a,MACX0a,GAAAA,GAAUC,KACPD,GAAAA,GAAUjX,QAG3CiX,GAAAA,GAAUnX,QAAU,WAAO,EAC3BmX,GAAAA,GAAU1a,MAAQ,WAAO,EACzB0a,GAAAA,GAAUC,KAAO,WAAO,EACxBD,GAAAA,GAAUjX,QAAU,WAAO,EAkB3BrK,OAAOwhB,kBAAoB,WACzB,IAEE,IAAM3T,EAAUtH,aAAaC,QAAQ,QACrC,IAAKqH,EACH,MAAO,CAAC1D,SAAS,EAAOC,QAAS,YAGnC,IAAMhE,EAAOC,KAAKC,MAAMuH,GAGxB,GAAKzH,EAAKK,KAiBH,CAEL,IAAIgb,EAAe,KAWnB,OATIrb,EAAKW,UAAYX,EAAKW,SAASiL,WAAW,KAC5CyP,EAAe,QACNrb,EAAKW,UAAYX,EAAKW,SAASiL,WAAW,KACnDyP,EAAe,WACNrb,EAAKW,UAAYX,EAAKW,SAASiL,WAAW,OACnDyP,EAAe,UAIbA,GAAgBrb,EAAKK,OAASgb,GAChCrb,EAAKK,KAAOgb,EACZlb,aAAac,QAAQ,OAAQhB,KAAK8Q,UAAU/Q,IAE5CgY,GAAMhN,OAAO,UAAWhL,GACjB,CAAC+D,SAAS,EAAMC,QAAS,YAAF6E,OAAcwS,KAGvC,CAACtX,SAAS,EAAMC,QAAS,YAAF6E,OAAc7I,EAAKK,MACnD,CAvBE,OAdIL,EAAKW,UAAYX,EAAKW,SAASiL,WAAW,KAC5C5L,EAAKK,KAAO,QACHL,EAAKW,UAAYX,EAAKW,SAASiL,WAAW,KACnD5L,EAAKK,KAAO,WAEZL,EAAKK,KAAO,SAIdF,aAAac,QAAQ,OAAQhB,KAAK8Q,UAAU/Q,IAG5CgY,GAAMhN,OAAO,UAAWhL,GAEjB,CAAC+D,SAAS,EAAMC,QAAS,aAAF6E,OAAe7I,EAAKK,MAwBtD,CAAE,MAAOG,GACP,MAAO,CAACuD,SAAS,EAAOC,QAAS,SAAF6E,OAAWrI,EAAMwD,SAClD,CACF,EAGA,IAAMsX,GAAkBnb,aAAac,QAC/Bsa,GAAqBpb,aAAawC,WAExCxC,aAAac,QAAU,SAASzE,EAAKmH,GACnC2X,GAAgBE,KAAK3b,KAAMrD,EAAKmH,EAClC,EAEAxD,aAAawC,WAAa,SAASnG,GACjC+e,GAAmBC,KAAK3b,KAAMrD,EAChC,EAIA0S,IAAAA,SAAeuM,iBAAkB,EACjCvM,IAAAA,SAAe1G,QAAQkT,OAAO,gBAAkB,mBAChDxM,IAAAA,SAAe1G,QAAQkT,OAAO,UAAY,mBAC1CxM,IAAAA,SAAeyM,QAAU,IAGzBzM,IAAAA,aAAmB0M,QAAQC,KACzB,SAAAC,GACE,OAAOA,CACT,IACA,SAAAtb,GACE,OAAOub,QAAQC,OAAOxb,EACxB,IAIF0O,IAAAA,aAAmBjN,SAAS4Z,KAC1B,SAAA5Z,GACE,OAAOA,CACT,IACA,SAAAzB,GACE,OAAOub,QAAQC,OAAOxb,EACxB,IAGF,IAAM6C,IAAM4Y,EAAAA,EAAAA,IAAUC,GAGtB7Y,GAAIyY,OAAOK,aAAe,SAAS7b,EAAKmG,EAAI0U,GAC1C,EAIFvhB,OAAO8M,iBAAiB,sBAAsB,SAAA0V,GAC5C,IAIF,IAAK,IAALC,GAAA,EAAAC,GAA+BhiB,OAAOiiB,QAAQC,GAAoBH,GAAAC,GAAAnZ,OAAAkZ,KAAE,CAA/D,IAAAI,IAAAC,EAAAA,EAAAA,GAAAJ,GAAAD,IAAA,GAAO7f,GAAGigB,GAAA,GAAE3G,GAAS2G,GAAA,GACxBpZ,GAAIyS,UAAUtZ,GAAKsZ,GACrB,CAGAzS,GAAIyY,OAAOa,iBAAiBC,OAAS1N,IAGrC7L,GAAIyY,OAAOa,iBAAiB7Y,SAAWoX,GAAAA,GACvC7X,GAAIyY,OAAOa,iBAAiBE,QAAUC,GAAAA,GACtCzZ,GAAIyY,OAAOa,iBAAiBI,QAAUC,GAAAA,EACtC3Z,GAAIyY,OAAOa,iBAAiBM,OAASD,GAAAA,EAAaE,MAClD7Z,GAAIyY,OAAOa,iBAAiB9Z,SAAWma,GAAAA,EAAaG,QACpD9Z,GAAIyY,OAAOa,iBAAiBpZ,QAAUyZ,GAAAA,EAAaI,OACnD/Z,GAAIyY,OAAOa,iBAAiB1U,SAAWoV,GAAAA,GAAUC,QAGjDxC,GAA4BzX,IAG5BA,GAAIwY,IAAI7D,IACR3U,GAAIwY,IAAIlF,IACRtT,GAAIwY,IAAI0B,GAAAA,EAAa,CACnBC,OAAQC,GAAAA,IAIV7jB,OAAOkK,SAAWoX,GAAAA,GAGlBthB,OAAOmH,uBAAqBqG,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAG,SAAAC,IAAA,IAAAE,EAAAzH,EAAAwH,EAAAG,EAAA1F,EAAA2F,EAAA,OAAAP,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAGiB,GAF9CxH,QAAQO,IAAI,2CAA2CgH,EAAAM,EAAA,EAE/CX,EAAUtH,aAAaC,QAAQ,QAChCqH,EAAS,CAAFK,EAAAC,EAAA,eAAAD,EAAAE,EAAA,UAGU,GAFhBhI,EAAOC,KAAKC,MAAMuH,GAElBD,EAASxH,EAAKY,GACf4G,EAAQ,CAAFM,EAAAC,EAAA,eAAAD,EAAAE,EAAA,UAIuB,OAD5BL,GAAMU,EAAAA,EAAAA,IAA4Bb,GACxCjH,QAAQO,IAAI,gBAAiB6G,GAAKG,EAAAC,EAAA,EAEXmH,IAAAA,IAAUvH,EAAK,CACpCa,QAAS,CAAE,gBAAiB,cAC5B,OAFIvG,EAAQ6F,EAAAY,EAIVzG,EAAS5C,OACX2Y,GAAMhN,OAAO,oBAAqB/I,EAAS5C,MAC3CkB,QAAQO,IAAI,sBACbgH,EAAAC,EAAA,eAAAD,EAAAM,EAAA,EAAAR,EAAAE,EAAAY,EAEDnI,QAAQC,MAAM,eAAcoH,GAAS,cAAAE,EAAAE,EAAA,MAAAT,EAAA,kBAKzC,IAAMmW,GAAuB,WACL,qBAAX9jB,SACTA,OAAO2G,SAAOf,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTe,SAAO,IACVO,IAAK,WAAY,EACjBqa,KAAM,WAAY,EAClBzP,KAAM,WAAY,EAClBlL,MAAO,WAAY,EACnBmd,MAAO,WAAY,IAGzB,EAGAta,GAAIua,MAAM,QAGVF,KAGA1c,YAAW,WACTT,QAAQO,IAAI,sBAEZ,IAEE,IAAMwR,EAAYrS,KAAKC,MAAMC,aAAaC,QAAQ,mBAAqB,MACvE,GAAIkS,GAAaA,EAAUlN,WAAY,CACrC7E,QAAQO,IAAI,UAAWwR,GAGvB,IAAMqH,EAAepC,SAASqC,iBAAiB,eAC3CD,GAAgBA,EAAaxW,QAAU,GACzCwW,EAAa,GAAGE,YAAcvH,EAAUlN,YAAc,EACtDuU,EAAa,GAAGE,YAAcvH,EAAUb,YAAc,EACtDkI,EAAa,GAAGE,YAAcvH,EAAU/M,eAAiB,EACzDoU,EAAa,GAAGE,YAAcvH,EAAU5M,gBAAkB,EAC1DiU,EAAa,GAAGE,YAAcvH,EAAUzM,eAAiB,EACzDtF,QAAQO,IAAI,wBAEZP,QAAQO,IAAI,gBAAiB6Y,EAAeA,EAAaxW,OAAS,GAGlEnC,YAAW,WACT,IAAM6c,EAAgBtG,SAASqC,iBAAiB,eAC5CiE,GAAiBA,EAAc1a,QAAU,IAC3C0a,EAAc,GAAGhE,YAAcvH,EAAUlN,YAAc,EACvDyY,EAAc,GAAGhE,YAAcvH,EAAUb,YAAc,EACvDoM,EAAc,GAAGhE,YAAcvH,EAAU/M,eAAiB,EAC1DsY,EAAc,GAAGhE,YAAcvH,EAAU5M,gBAAkB,EAC3DmY,EAAc,GAAGhE,YAAcvH,EAAUzM,eAAiB,EAC1DtF,QAAQO,IAAI,4BAEhB,GAAG,KAEP,KAAO,CACLP,QAAQO,IAAI,gCAGZ,IAAMoO,EAAQhV,EAAAA,OAAAA,WAGd,IACE,IAAMuN,EAAUtH,aAAaC,QAAQ,SAAW,KAC1CJ,EAAOC,KAAKC,MAAMuH,GAElBD,EAASxH,EAAKY,GAEpB,IAAK4G,EAEH,YADAjH,QAAQO,IAAI,qBAIdP,QAAQO,IAAI,SAAU0G,EAAQ,SAG9B0H,EAAM4O,IAAI,GAADjV,OAAIkV,EAAAA,IAAYlV,OAAGmV,EAAAA,GAAgB,4BAAAnV,OAA2BrB,EAAM,OAAAqB,OAAM3B,KAAKoS,MAAK,OAAAzQ,OAAMoV,KAAKC,SAAQ,0CAA0C,CACxJ1V,QAAS,CACP,gBAAiB,qBACjB,OAAU,WACV,QAAW,OAGZxG,MAAK,SAAAC,GACJ1B,QAAQO,IAAI,cAAemB,EAAS5C,MAGpC,IAAMsa,EAAepC,SAASqC,iBAAiB,eAC3CD,GAAgBA,EAAaxW,QAAU,IACzCwW,EAAa,GAAGE,YAAc5X,EAAS5C,KAAK+F,YAAc,EAC1DuU,EAAa,GAAGE,YAAc5X,EAAS5C,KAAKoS,YAAc,EAC1DkI,EAAa,GAAGE,YAAc5X,EAAS5C,KAAKkG,eAAiB,EAC7DoU,EAAa,GAAGE,YAAc5X,EAAS5C,KAAKqG,gBAAkB,EAC9DiU,EAAa,GAAGE,YAAc5X,EAAS5C,KAAKwG,eAAiB,EAC7DtF,QAAQO,IAAI,2BAEhB,IAAE,UACK,SAAAN,GACLD,QAAQC,MAAM,cAAeA,EAC/B,GACJ,CAAE,MAAOA,GACPD,QAAQC,MAAM,YAAaA,EAC7B,CACF,CACF,CAAE,MAAOA,GACPD,QAAQC,MAAM,cAAeA,EAC/B,CACF,GAAG,KAOH,IAAM2d,GAAoBtH,QAAQuH,UAC5BC,GAAuBxH,QAAQyH,aAGrCzH,QAAQuH,UAAY,WAClB7d,QAAQO,IAAI,gCAAiC,CAC3Cyd,GAAIC,UAAU,GACdC,GAAID,UAAU,GACdE,IAAKF,UAAU,GACfG,MAAM,IAAIzX,MAAOkQ,qBACjBwH,KAAM,SAIR,IAAMC,GAAQ,IAAIjW,OAAQiW,MAG1B,OAFAte,QAAQO,IAAI,yBAA0B+d,GAE/BV,GAAkBW,MAAMjf,KAAM2e,UACvC,EAGA3H,QAAQyH,aAAe,WACrB/d,QAAQO,IAAI,mCAAoC,CAC9Cyd,GAAIC,UAAU,GACdC,GAAID,UAAU,GACdE,IAAKF,UAAU,GACfG,MAAM,IAAIzX,MAAOkQ,qBACjBwH,KAAM,SAIR,IAAMC,GAAQ,IAAIjW,OAAQiW,MAG1B,OAFAte,QAAQO,IAAI,4BAA6B+d,GAElCR,GAAqBS,MAAMjf,KAAM2e,UAC1C,EAGA5kB,OAAO8M,iBAAiB,YAAY,SAAS0V,GAC3C7b,QAAQO,IAAI,wBAAyB,CACnCyd,GAAInC,EAAMjT,MACVyP,MAAOhf,OAAO4I,SAASC,KACvBkc,MAAM,IAAIzX,MAAOkQ,qBACjBwH,KAAM,cAEV,IAIA,IAAMG,IAAWC,EAAAA,GAAAA,KAGjBplB,OAAOmlB,SAAWA,GAGlB1b,GAAIyY,OAAOa,iBAAiBsC,uBAAyB,YACnDC,EAAAA,GAAAA,MACA7b,GAAIyY,OAAOa,iBAAiBwC,2BAA4B,CAC1D,EAGA5H,SAAS7Q,iBAAiB,oBAAoB,WAC5C1F,YAAW,WACT,GAAIpH,OAAO4I,SAASC,KAAKoV,SAAS,eAC9Bje,OAAO4I,SAASC,KAAKoV,SAAS,SACU,SAAxC1X,aAAaC,QAAQ,eACvB,KACE8e,EAAAA,GAAAA,MACA3e,QAAQO,IAAI,cACd,CAAE,MAAOoD,GACP3D,QAAQC,MAAM,WAAY0D,EAC5B,CAEJ,GAAG,IACL,G,wFC3ZA1H,IAAA,G,GAAAA,IAAA,EAiBwCuB,MAAA,wB,GAM7BlD,MAAM,W,GAvBjB2B,IAAA,G,GAAAA,IAAA,G,GAAAA,IAAA,G,GAAAA,IAAA,G,GAAAA,IAAA,G,GAoJW3B,MAAM,wB,GApJjB2B,IAAA,EAmKgB3B,MAAM,0B,GACXA,MAAM,e,GApKjB2B,IAAA,EA8KqC3B,MAAM,qB,GAE9BA,MAAM,W,GAGJA,MAAM,mB,GAaAA,MAAM,e,GAhM3B2B,IAAA,EAuMwB3B,MAAM,Y,GAQjBA,MAAM,W,GA+CNA,MAAM,W,GAeNA,MAAM,W,GAUNA,MAAM,W,GAvRnB2B,IAAA,EAgSgE3B,MAAM,mB,GAhStE2B,IAAA,EAsS+I3B,MAAM,gB,GAMxIA,MAAM,kB,GAuCLA,MAAM,iB,0dAlVlBuJ,EAAAA,EAAAA,IAwVM,OAxVDvJ,OADP6D,EAAAA,EAAAA,IAAA,CACa,yBAAwB,mBAA6B0gB,EAAAC,iB,CAElDD,EAAAE,kB,WAgKZlb,EAAAA,EAAAA,IA6JM,MA7JN0B,EA6JM,EA5JJ3K,EAAAA,EAAAA,IAIM,MAJN4K,EAIM,EAHJ/K,EAAAA,EAAAA,IAEYkC,EAAA,CAFDC,KAAK,UAAUoB,KAAK,SAAS1D,MAAM,cAAeY,QAAO2jB,EAAAG,Y,CArK5E,SAAAxkB,EAAAA,EAAAA,KAsKU,iBAAsC,EAtKhDsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAsKa4gB,EAAAC,aAAe,OAAS,UAAZ,G,IAtKzBpjB,EAAA,G,gCA0KMd,EAAAA,EAAAA,IAEM,OAFDN,MAAM,gBAAc,EACvBM,EAAAA,EAAAA,IAAa,UAAT,UAAI,IAGCikB,EAAAI,qBAAkB,WAA7Bpb,EAAAA,EAAAA,IAiJM,MAjJN4B,EAiJM,EA/IJ7K,EAAAA,EAAAA,IA4BM,MA5BNskB,EA4BM,gBA3BJtkB,EAAAA,EAAAA,IAAe,UAAX,UAAM,KAEVA,EAAAA,EAAAA,IAwBM,MAxBNukB,EAwBM,CAtBIN,EAAAI,qBAAuBJ,EAAAI,mBAAmBG,gBAAkBP,EAAAI,mBAAmBI,oBAAsBR,EAAAI,mBAAmB9M,aAAS,WADzIlY,EAAAA,EAAAA,IAkBWqlB,EAAA,CAtMvBrjB,IAAA,EAsLesjB,IAAKV,EAAAW,gBAAgBX,EAAAI,mBAAmBG,gBAAkBP,EAAAI,mBAAmBI,oBAAsBR,EAAAI,mBAAmB9M,WACtH,mBAAkB,GACnBsN,IAAI,UACJjiB,MAAA,sEACC,UAAS,KACT,sBAAoB,EACpB,gBAAe,EAChB,0B,CAEWyC,OAAKzF,EAAAA,EAAAA,KACd,iBAIM,EAJNI,EAAAA,EAAAA,IAIM,MAJN8kB,EAIM,gBAHJ9kB,EAAAA,EAAAA,IAAuC,KAApCN,MAAM,2BAAyB,yBAClCM,EAAAA,EAAAA,IAAa,SAAV,UAAM,KACTA,EAAAA,EAAAA,IAAmI,aAA5H,QAAIqD,EAAAA,EAAAA,IAAG4gB,EAAAI,mBAAmBG,gBAAkBP,EAAAI,mBAAmBI,oBAAsBR,EAAAI,mBAAmB9M,WAAS,K,IAnM1IzW,EAAA,G,yBAuMYmI,EAAAA,EAAAA,IAGM,MAHN8b,EAGMxkB,EAAA,MAAAA,EAAA,MAFJP,EAAAA,EAAAA,IAAuC,KAApCN,MAAM,2BAAyB,UAClCM,EAAAA,EAAAA,IAAa,SAAV,UAAM,YAMfA,EAAAA,EAAAA,IA4CM,MA5CNglB,EA4CM,gBA3CJhlB,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRH,EAAAA,EAAAA,IAyCkBolB,EAAA,CAzCAC,OAAQ,EAAGC,OAAA,I,CAjNvC,SAAAvlB,EAAAA,EAAAA,KAkNY,iBAAqH,EAArHC,EAAAA,EAAAA,IAAqHulB,EAAA,CAA/FC,MAAM,MAAI,CAlN5C,SAAAzlB,EAAAA,EAAAA,KAkN6C,iBAA6D,EAlN1GsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAkNgD4gB,EAAAI,mBAAmBiB,aAAerB,EAAAI,mBAAmB5e,IAAE,G,IAlNvG3E,EAAA,KAmNYjB,EAAAA,EAAAA,IAIuBulB,EAAA,CAJDC,MAAM,QAAM,CAnN9C,SAAAzlB,EAAAA,EAAAA,KAoNc,iBAES,EAFTC,EAAAA,EAAAA,IAES0lB,EAAA,CAFAvjB,KAAMiiB,EAAAuB,iBAAiBvB,EAAAI,mBAAmBtd,S,CApNjE,SAAAnH,EAAAA,EAAAA,KAqNgB,iBAAqD,EArNrEsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAqNmB4gB,EAAAwB,qBAAqBxB,EAAAI,mBAAmBtd,SAAM,G,IArNjEjG,EAAA,G,gBAAAA,EAAA,KAwNYjB,EAAAA,EAAAA,IAIuBulB,EAAA,CAJDC,MAAM,QAAM,CAxN9C,SAAAzlB,EAAAA,EAAAA,KAyNc,eAAA8lB,EAAA,MAE6F,EA3N3GxjB,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAyNkB4gB,EAAAI,mBAAmBxf,MAAQof,EAAAI,mBAAmBxf,KAAKtF,MAA2B0kB,EAAAI,mBAAmBsB,aAAe1B,EAAAI,mBAAmBsB,YAAYpmB,MAA0B0kB,EAAA2B,mBAAyC,QAAvBF,EAAAzB,EAAAI,mBAAmBxf,YAAI,IAAA6gB,OAAA,EAAvBA,EAAyBjgB,KAAMwe,EAAAI,mBAAmBsB,cAAW,S,IAzN1Q7kB,EAAA,KA6NYjB,EAAAA,EAAAA,IAgBuBulB,EAAA,CAhBDC,MAAM,QAAM,CA7N9C,SAAAzlB,EAAAA,EAAAA,KAwSE,iBAUF,CApF8BqkB,EAAAI,mBAAmBxf,MAAQof,EAAAI,mBAAmBxf,KAAKwL,MAAQ4T,EAAAI,mBAAmBxf,KAAKwL,KAAK9Q,OAAI,WAC1GF,EAAAA,EAAAA,IAAuEkmB,EAAA,CA/NvFlkB,IAAA,EA+NwBW,KAAK,W,CA/N7B,SAAApC,EAAAA,EAAAA,KA+NuC,iBAAuC,EA/N9EsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IA+N0C4gB,EAAAI,mBAAmBxf,KAAKwL,KAAK9Q,MAAI,G,IA/N3EuB,EAAA,KAiOmCmjB,EAAAI,mBAAmBxf,MAAQof,EAAAI,mBAAmBxf,KAAKghB,UAAO,WAC7ExmB,EAAAA,EAAAA,IAAsFkmB,EAAA,CAlOtGlkB,IAAA,EAkOwBW,KAAK,W,CAlO7B,SAAApC,EAAAA,EAAAA,KAkOuC,iBAAsD,EAlO7FsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAkO0C4gB,EAAA6B,gBAAgB7B,EAAAI,mBAAmBxf,KAAKghB,UAAO,G,IAlOzF/kB,EAAA,KAoOmCmjB,EAAAI,mBAAmBhU,MAAQ4T,EAAAI,mBAAmBhU,KAAK9Q,OAAI,WAC1EF,EAAAA,EAAAA,IAAkEkmB,EAAA,CArOlFlkB,IAAA,EAqOwBW,KAAK,W,CArO7B,SAAApC,EAAAA,EAAAA,KAqOuC,iBAAkC,EArOzEsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAqO0C4gB,EAAAI,mBAAmBhU,KAAK9Q,MAAI,G,IArOtEuB,EAAA,KAuOmCmjB,EAAAI,mBAAmBwB,UAAO,WAC7CxmB,EAAAA,EAAAA,IAAiFkmB,EAAA,CAxOjGlkB,IAAA,EAwOwBW,KAAK,W,CAxO7B,SAAApC,EAAAA,EAAAA,KAwOuC,iBAAiD,EAxOxFsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAwO0C4gB,EAAA6B,gBAAgB7B,EAAAI,mBAAmBwB,UAAO,G,IAxOpF/kB,EAAA,O,WA2OgBzB,EAAAA,EAAAA,IAAmCkmB,EAAA,CA3OnDlkB,IAAA,EA2OwBW,KAAK,W,CA3O7B,SAAApC,EAAAA,EAAAA,KA2OuC,kBAAGW,EAAA,MAAAA,EAAA,MA3O1C2B,EAAAA,EAAAA,IA2OuC,Q,IA3OvCpB,EAAA,EAAAC,GAAA,Q,IAAAD,EAAA,KA8OYjB,EAAAA,EAAAA,IAEuBulB,EAAA,CAFDC,MAAM,QAAM,CA9O9C,SAAAzlB,EAAAA,EAAAA,KA+Oc,iBAAkD,EA/OhEsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IA+OiB4gB,EAAA8B,eAAe9B,EAAAI,mBAAmB2B,YAAS,G,IA/O5DllB,EAAA,KAiPYjB,EAAAA,EAAAA,IAEuBulB,EAAA,CAFDC,MAAM,QAAM,CAjP9C,SAAAzlB,EAAAA,EAAAA,KAkPc,iBAAkD,EAlPhEsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAkPiB4gB,EAAA8B,eAAe9B,EAAAI,mBAAmB4B,YAAS,G,IAlP5DnlB,EAAA,KAoPYjB,EAAAA,EAAAA,IAEuBulB,EAAA,CAFDC,MAAM,QAAM,CApP9C,SAAAzlB,EAAAA,EAAAA,KAqPc,iBAA0C,EArPxDsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAqPiB4gB,EAAAI,mBAAmB6B,YAAc,KAAJ,G,IArP9CplB,EAAA,KAuPYjB,EAAAA,EAAAA,IAEuBulB,EAAA,CAFDC,MAAM,SAAO,CAvP/C,SAAAzlB,EAAAA,EAAAA,KAwPc,iBAA4C,EAxP1DsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAwPiB4gB,EAAAI,mBAAmB8B,cAAgB,KAAJ,G,IAxPhDrlB,EAAA,I,IAAAA,EAAA,OA8PQd,EAAAA,EAAAA,IAYM,MAZNomB,EAYM,gBAXJpmB,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRH,EAAAA,EAAAA,IASkBolB,EAAA,CATAC,OAAQ,EAAGC,OAAA,I,CAhQvC,SAAAvlB,EAAAA,EAAAA,KAiQY,iBAAmG,EAAnGC,EAAAA,EAAAA,IAAmGulB,EAAA,CAA7EC,MAAM,MAAI,CAjQ5C,SAAAzlB,EAAAA,EAAAA,KAiQ6C,iBAA2C,EAjQxFsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAiQgD4gB,EAAAI,mBAAmBgC,aAAe,KAAJ,G,IAjQ9EvlB,EAAA,KAkQYjB,EAAAA,EAAAA,IAAmGulB,EAAA,CAA7EC,MAAM,MAAI,CAlQ5C,SAAAzlB,EAAAA,EAAAA,KAkQ6C,iBAA0C,EAlQvFsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAkQgD4gB,EAAAI,mBAAmBiC,YAAc,KAAM,IAAC,G,IAlQxFxlB,EAAA,KAmQYjB,EAAAA,EAAAA,IAA8FulB,EAAA,CAAxEC,MAAM,MAAI,CAnQ5C,SAAAzlB,EAAAA,EAAAA,KAmQ6C,iBAAsC,EAnQnFsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAmQgD4gB,EAAAI,mBAAmBkC,QAAU,KAAJ,G,IAnQzEzlB,EAAA,KAoQYjB,EAAAA,EAAAA,IAAoGulB,EAAA,CAA9EC,MAAM,QAAM,CApQ9C,SAAAzlB,EAAAA,EAAAA,KAoQ+C,iBAA0C,EApQzFsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAoQkD4gB,EAAAI,mBAAmB6B,YAAc,KAAJ,G,IApQ/EplB,EAAA,KAqQYjB,EAAAA,EAAAA,IAAiJulB,EAAA,CAA3HC,MAAM,OAAQmB,KAAM,G,CArQtD,SAAA5mB,EAAAA,EAAAA,KAqQyD,iBAA6E,EArQtIsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAqQ4D4gB,EAAAI,mBAAmBoC,UAAYxC,EAAAI,mBAAmBqC,gBAAkB,KAAJ,G,IArQ5H5lB,EAAA,KAsQYjB,EAAAA,EAAAA,IAA6FulB,EAAA,CAAvEC,MAAM,MAAI,CAtQ5C,SAAAzlB,EAAAA,EAAAA,KAsQ6C,iBAAqC,EAtQlFsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAsQgD4gB,EAAAI,mBAAmBsC,OAAS,KAAJ,G,IAtQxE7lB,EAAA,KAuQYjB,EAAAA,EAAAA,IAAuGulB,EAAA,CAAjFC,MAAM,QAAM,CAvQ9C,SAAAzlB,EAAAA,EAAAA,KAuQ+C,iBAA6C,EAvQ5FsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAuQkD4gB,EAAAI,mBAAmBuC,eAAiB,KAAJ,G,IAvQlF9lB,EAAA,KAwQYjB,EAAAA,EAAAA,IAA4GulB,EAAA,CAAtFC,MAAM,QAAM,CAxQ9C,SAAAzlB,EAAAA,EAAAA,KAwQ+C,iBAAkD,EAxQjGsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAwQkD4gB,EAAAI,mBAAmBwC,oBAAsB,KAAJ,G,IAxQvF/lB,EAAA,I,IAAAA,EAAA,OA6QQd,EAAAA,EAAAA,IAOM,MAPN8mB,EAOM,gBANJ9mB,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRH,EAAAA,EAAAA,IAIkBolB,EAAA,CAJAC,OAAQ,EAAGC,OAAA,I,CA/QvC,SAAAvlB,EAAAA,EAAAA,KAgRY,iBAA2G,EAA3GC,EAAAA,EAAAA,IAA2GulB,EAAA,CAArFC,MAAM,QAAM,CAhR9C,SAAAzlB,EAAAA,EAAAA,KAgR+C,iBAAiD,EAhRhGsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAgRkD4gB,EAAAI,mBAAmB0C,mBAAqB,KAAJ,G,IAhRtFjmB,EAAA,KAiRYjB,EAAAA,EAAAA,IAA6GulB,EAAA,CAAvFC,MAAM,QAAM,CAjR9C,SAAAzlB,EAAAA,EAAAA,KAiR+C,iBAAmD,EAjRlGsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAiRkD4gB,EAAAI,mBAAmB2C,qBAAuB,KAAJ,G,IAjRxFlmB,EAAA,KAkRYjB,EAAAA,EAAAA,IAAqGulB,EAAA,CAA/EC,MAAM,QAAM,CAlR9C,SAAAzlB,EAAAA,EAAAA,KAkR+C,iBAA2C,EAlR1FsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAkRkD4gB,EAAAI,mBAAmB4C,aAAe,KAAJ,G,IAlRhFnmB,EAAA,I,IAAAA,EAAA,OAuRQd,EAAAA,EAAAA,IAkBM,MAlBNknB,EAkBM,gBAjBJlnB,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRH,EAAAA,EAAAA,IAMYsnB,EAAA,CA/RtBC,WA0RqBnD,EAAA/O,YA1RrB,sBAAA3U,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA0RqByjB,EAAA/O,YAAW1U,CAAA,GACpBwB,KAAK,WACJqlB,KAAM,EACPC,YAAY,aACXtkB,SAAwC,cAA9BihB,EAAAI,mBAAmBtd,Q,kCAES,cAA9Bkd,EAAAI,mBAAmBtd,SAAM,WAApCkC,EAAAA,EAAAA,IAKM,MALNse,EAKM,gBAJJvnB,EAAAA,EAAAA,IAA+B,KAA5BN,MAAM,mBAAiB,WAjStCwC,EAAAA,EAAAA,IAiS2C,KAC/BmB,EAAAA,EAAAA,IAAiC,aAA9B4gB,EAAAI,mBAAmBtd,OAAwB,oBAA+D,aAAzBkd,EAAAI,mBAAmBtd,OAAM,iDAlSzHxF,EAAAA,EAAAA,IAAA,QAsSqB0iB,EAAAI,mBAAmBnP,aAA8C,aAA9B+O,EAAAI,mBAAmBtd,QAAuD,aAA9Bkd,EAAAI,mBAAmBtd,QAtSvHxF,EAAAA,EAAAA,IAAA,SAsS6H,WAAnH0H,EAAAA,EAAAA,IAEM,MAFNue,EAEM,gBADJxnB,EAAAA,EAAAA,IAAsB,cAAd,SAAK,KAvSzBkC,EAAAA,EAAAA,IAuSkC,KAACmB,EAAAA,EAAAA,IAAG4gB,EAAAI,mBAAmBnP,aAAW,SAK5DlV,EAAAA,EAAAA,IAkBM,MAlBNynB,EAkBM,EAjBJ5nB,EAAAA,EAAAA,IAAmDkC,EAAA,CAAxC2lB,MAAA,GAAOpnB,QAAO2jB,EAAAG,Y,CA7SnC,SAAAxkB,EAAAA,EAAAA,KA6S+C,kBAAEW,EAAA,MAAAA,EAAA,MA7SjD2B,EAAAA,EAAAA,IA6S+C,O,IA7S/CpB,EAAA,EAAAC,GAAA,M,eA8SwD,cAA9BkjB,EAAAI,mBAAmBtd,SAAM,WAAzCkC,EAAAA,EAAAA,IAeW0e,EAAAA,GAAA,CA7TrBtmB,IAAA,KA+SYxB,EAAAA,EAAAA,IAMYkC,EAAA,CALVC,KAAK,UACL0lB,MAAA,GACCpnB,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEyjB,EAAA2D,kBAAkB3D,EAAAI,mBAAkB,I,CAlT1D,SAAAzkB,EAAAA,EAAAA,KAmTa,kBAEDW,EAAA,MAAAA,EAAA,MArTZ2B,EAAAA,EAAAA,IAmTa,S,IAnTbpB,EAAA,EAAAC,GAAA,QAsTYlB,EAAAA,EAAAA,IAMYkC,EAAA,CALVC,KAAK,SACL0lB,MAAA,GACCpnB,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEyjB,EAAA4D,iBAAiB5D,EAAAI,mBAAkB,I,CAzTzD,SAAAzkB,EAAAA,EAAAA,KA0Ta,kBAEDW,EAAA,MAAAA,EAAA,MA5TZ2B,EAAAA,EAAAA,IA0Ta,S,IA1TbpB,EAAA,EAAAC,GAAA,Q,MAAAQ,EAAAA,EAAAA,IAAA,aAAAA,EAAAA,EAAAA,IAAA,YAG+B,WAA3B0H,EAAAA,EAAAA,IA6JM,MAhKVrH,EAAA,gBAIM5B,EAAAA,EAAAA,IAAa,UAAT,QAAI,IAIAikB,EAAA1f,cAAW,WADnBlF,EAAAA,EAAAA,IAcWyoB,EAAA,CArBjBzmB,IAAA,EASQW,KAAK,OACJ+lB,UAAU,G,CAEA5M,OAAKvb,EAAAA,EAAAA,KACd,iBAA2D,EAA3DI,EAAAA,EAAAA,IAA2D,0BAbrEkC,EAAAA,EAAAA,IAagB,aAAOlC,EAAAA,EAAAA,IAAuC,eAAAqD,EAAAA,EAAAA,IAA5B4gB,EAAA1f,YAAYhF,MAAI,MACxCM,EAAAA,EAAAA,IAES0lB,EAAA,CAFDniB,KAAK,QAASpB,KAA2B,UAArBiiB,EAAA1f,YAAYW,KAAmB,SAAW,UAAWtC,MAAA,wB,CAd3F,SAAAhD,EAAAA,EAAAA,KAeY,iBAAmD,EAf/DsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAeoC,UAArB4gB,EAAA1f,YAAYW,KAAmB,MAAQ,QAAvB,G,IAf/BpE,EAAA,G,YAiBsBmjB,EAAA1f,YAAY8L,OAAI,WAA5BpH,EAAAA,EAAAA,IAEO,OAFP5G,EAEO,cAnBjBH,EAAAA,EAAAA,IAiBkE,WAClDrC,EAAAA,EAAAA,IAAwE0lB,EAAA,CAAhEniB,KAAK,QAAQpB,KAAK,W,CAlB1C,SAAApC,EAAAA,EAAAA,KAkBoD,iBAA2B,EAlB/EsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAkBuD4gB,EAAA1f,YAAY8L,KAAK9Q,MAAI,G,IAlB5EuB,EAAA,QAAAS,EAAAA,EAAAA,IAAA,O,IAAAT,EAAA,MAAAS,EAAAA,EAAAA,IAAA,QAuBMvB,EAAAA,EAAAA,IAkBM,MAlBNkD,EAkBM,EAjBJrD,EAAAA,EAAAA,IAOEsnB,EAAA,CA/BVC,WAyBmBnD,EAAA+D,YAzBnB,sBAAAznB,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAyBmByjB,EAAA+D,YAAWxnB,CAAA,GACpB8mB,YAAY,OACZ,cAAY,iBACZW,UAAA,GACAvoB,MAAM,eACLwoB,SAAQjE,EAAAkE,iB,mCAGXtoB,EAAAA,EAAAA,IAOYuoB,EAAA,CAxCpBhB,WAiC4BnD,EAAAoE,QAAQhY,KAjCpC,sBAAA9P,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAiC4ByjB,EAAAoE,QAAQhY,KAAI7P,CAAA,GAAE8mB,YAAY,QAAQW,UAAA,GAAWC,SAAQjE,EAAAkE,iB,CAjCjF,SAAAvoB,EAAAA,EAAAA,KAmCY,iBAAqB,gBADvBqJ,EAAAA,EAAAA,IAKE0e,EAAAA,GAAA,MAvCZW,EAAAA,EAAAA,IAmC2BrE,EAAAxK,OAnC3B,SAmCmBpJ,G,kBADThR,EAAAA,EAAAA,IAKEkpB,EAAA,CAHClnB,IAAKgP,EAAK5K,GACV4f,MAAOhV,EAAK9Q,KACZiJ,MAAO6H,EAAK5K,I,uCAtCzB3E,EAAA,G,qDA4CMzB,EAAAA,EAAAA,IAqGWmpB,EAAA,CAnGRtkB,KAAM+f,EAAAwE,YACN,aAAYxE,EAAA1X,QAAU,SAAW,WAClC3J,MAAA,eACAuiB,OAAA,GACAuD,OAAA,GACA,2BACC,iBAAgBzE,EAAA0E,gBAChBC,WAAW3E,EAAA4E,sB,CArDpB,SAAAjpB,EAAAA,EAAAA,KAwDQ,iBAAmD,EAAnDC,EAAAA,EAAAA,IAAmDipB,EAAA,CAAlCC,KAAK,KAAK1D,MAAM,KAAKtlB,MAAM,QAC5CF,EAAAA,EAAAA,IAIkBipB,EAAA,CAJDzD,MAAM,OAAOtlB,MAAM,O,CACvBipB,SAAOppB,EAAAA,EAAAA,KAChB,SAA2CqpB,GADpB,QA1DnC/mB,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IA2De4gB,EAAA8B,eAAekD,EAAMC,IAAIC,cAAW,G,IA3DnDroB,EAAA,KAgEQjB,EAAAA,EAAAA,IAkBkBipB,EAAA,CAlBDzD,MAAM,OAAOtlB,MAAM,O,CACvBipB,SAAOppB,EAAAA,EAAAA,KAOT,SAKnBqpB,GAZmC,OACXA,EAAMC,IAAIvD,aAA2C,YAAhCvR,EAAAA,EAAAA,GAAW6U,EAAMC,IAAIvD,eAAW,WAAjE1c,EAAAA,EAAAA,IAEO,OApEnBc,GAAA1G,EAAAA,EAAAA,IAmEiB4lB,EAAMC,IAAIvD,YAAYpmB,MAAQ,MAAJ,IAEd0pB,EAAMC,IAAIvD,cAAW,WAAtC1c,EAAAA,EAAAA,IAEO,OAvEnBiB,GAAA7G,EAAAA,EAAAA,IAsEiB4gB,EAAA2B,kBAAkBqD,EAAMC,IAAIvD,cAAgB,KAAOsD,EAAMC,IAAIvD,aAAW,IAE5DsD,EAAMC,IAAIE,YAAyC,YAA/BhV,EAAAA,EAAAA,GAAW6U,EAAMC,IAAIE,cAAU,WAApEngB,EAAAA,EAAAA,IAEO,OA1EnBkB,GAAA9G,EAAAA,EAAAA,IAyEiB4lB,EAAMC,IAAIE,WAAW7pB,MAAQ,MAAJ,IAEb0pB,EAAMC,IAAIE,aAAU,WAArCngB,EAAAA,EAAAA,IAEO,OA7EnBoB,GAAAhH,EAAAA,EAAAA,IA4EiB4gB,EAAA2B,kBAAkBqD,EAAMC,IAAIE,aAAe,KAAOH,EAAMC,IAAIE,YAAU,iBAE3EngB,EAAAA,EAAAA,IAEO,OAhFnBqB,EA8EyB,S,IA9EzBxJ,EAAA,KAqFQjB,EAAAA,EAAAA,IAiCkBipB,EAAA,CAjCDzD,MAAM,OAAOtlB,MAAM,O,CACvBipB,SAAOppB,EAAAA,EAAAA,KAChB,SA6BMqpB,GA9BiB,QACvBjpB,EAAAA,EAAAA,IA6BM,aApHlBuB,EAAAA,EAAAA,IAAA,OAiG8B0nB,EAAMC,IAAIrkB,MAAQokB,EAAMC,IAAIrkB,KAAKwL,MAAQ4Y,EAAMC,IAAIrkB,KAAKwL,KAAK9Q,OAAI,WAC/EF,EAAAA,EAAAA,IAA8DkmB,EAAA,CAlG9ElkB,IAAA,EAkGwBW,KAAK,W,CAlG7B,SAAApC,EAAAA,EAAAA,KAkGuC,iBAA8B,EAlGrEsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAkG0C4lB,EAAMC,IAAIrkB,KAAKwL,KAAK9Q,MAAI,G,IAlGlEuB,EAAA,G,OAoGmCmoB,EAAMC,IAAIrkB,MAAQokB,EAAMC,IAAIrkB,KAAKghB,UAAO,WAC3DxmB,EAAAA,EAAAA,IAA6EkmB,EAAA,CArG7FlkB,IAAA,EAqGwBW,KAAK,W,CArG7B,SAAApC,EAAAA,EAAAA,KAqGuC,iBAA6C,EArGpFsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAqG0C4gB,EAAA6B,gBAAgBmD,EAAMC,IAAIrkB,KAAKghB,UAAO,G,IArGhF/kB,EAAA,G,OAuGmCmoB,EAAMC,IAAI7Y,MAAQ4Y,EAAMC,IAAI7Y,KAAK9Q,OAAI,WACxDF,EAAAA,EAAAA,IAAyDkmB,EAAA,CAxGzElkB,IAAA,EAwGwBW,KAAK,W,CAxG7B,SAAApC,EAAAA,EAAAA,KAwGuC,iBAAyB,EAxGhEsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAwG0C4lB,EAAMC,IAAI7Y,KAAK9Q,MAAI,G,IAxG7DuB,EAAA,G,OA0GmCmoB,EAAMC,IAAIrD,UAAO,WACpCxmB,EAAAA,EAAAA,IAAwEkmB,EAAA,CA3GxFlkB,IAAA,EA2GwBW,KAAK,W,CA3G7B,SAAApC,EAAAA,EAAAA,KA2GuC,iBAAwC,EA3G/EsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IA2G0C4gB,EAAA6B,gBAAgBmD,EAAMC,IAAIrD,UAAO,G,IA3G3E/kB,EAAA,G,oBA8GgBzB,EAAAA,EAAAA,IAISkmB,EAAA,CAlHzBlkB,IAAA,EA8GwBW,KAAK,UAAUqnB,OAAO,Q,CA9G9C,SAAAzpB,EAAAA,EAAAA,KA+GkB,iBAEa,EAFbC,EAAAA,EAAAA,IAEaypB,EAAA,CAFDjV,QAAQ,kBAAkBkV,UAAU,O,CA/GlE,SAAA3pB,EAAAA,EAAAA,KAgHoB,kBAAwDW,EAAA,MAAAA,EAAA,MAAxDP,EAAAA,EAAAA,IAAwD,cAAlDA,EAAAA,EAAAA,IAAuC,KAApCN,MAAM,6BAhHnCwC,EAAAA,EAAAA,IAgHiE,UAAI,I,IAhHrEpB,EAAA,EAAAC,GAAA,O,IAAAD,EAAA,O,IAAAA,EAAA,KAyHQjB,EAAAA,EAAAA,IAA+DipB,EAAA,CAA9CC,KAAK,cAAc1D,MAAM,OAAOtlB,MAAM,SACvDF,EAAAA,EAAAA,IAA+CipB,EAAA,CAA9BC,KAAK,YAAY1D,MAAM,QAGxCxlB,EAAAA,EAAAA,IAMkBipB,EAAA,CANDzD,MAAM,KAAKtlB,MAAM,O,CACrBipB,SAAOppB,EAAAA,EAAAA,KAChB,SAESqpB,GAHc,QACvBppB,EAAAA,EAAAA,IAES0lB,EAAA,CAFAvjB,KAAMiiB,EAAAuB,iBAAiByD,EAAMC,IAAIniB,S,CA/HtD,SAAAnH,EAAAA,EAAAA,KAgIc,iBAA4C,EAhI1DsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IAgIiB4gB,EAAAwB,qBAAqBwD,EAAMC,IAAIniB,SAAM,G,IAhItDjG,EAAA,G,mBAAAA,EAAA,KAsIQjB,EAAAA,EAAAA,IAUkBipB,EAAA,CAVDU,MAAM,QAAQnE,MAAM,KAAKtlB,MAAM,O,CACnCipB,SAAOppB,EAAAA,EAAAA,KAChB,SAMYqpB,GAPW,QACvBppB,EAAAA,EAAAA,IAMYkC,EAAA,CALVqB,KAAK,QACJpB,KAA2B,aAArBinB,EAAMC,IAAIniB,OAAwB,OAAS,UACjDzG,SA3IfmpB,EAAAA,EAAAA,KAAA,SAAAjpB,GAAA,OA2I2ByjB,EAAA4E,qBAAqBI,EAAMC,IAAG,c,CA3IzD,SAAAtpB,EAAAA,EAAAA,KA6Ic,iBAAmD,EA7IjEsC,EAAAA,EAAAA,KAAAmB,EAAAA,EAAAA,IA6IsC,aAArB4lB,EAAMC,IAAIniB,OAAwB,KAAO,MAAzB,G,IA7IjCjG,EAAA,G,6BAAAA,EAAA,I,IAAAA,EAAA,G,4DA6CmBmjB,EAAA1X,YAuGbvM,EAAAA,EAAAA,IAWM,MAXNyK,EAWM,EAVJ5K,EAAAA,EAAAA,IASE6pB,EAAA,CARAC,WAAA,GACAC,OAAO,0CACN,aAAY,CAAC,GAAI,GAAI,GAAI,KACzB,YAAW3F,EAAA4F,SACXC,MAAO7F,EAAA6F,MACP,eAAc7F,EAAA8F,YACdC,aAAa/F,EAAAgG,iBACbC,gBAAgBjG,EAAAkG,qB,oFAsKvBtqB,EAAAA,EAAAA,IAqBYuqB,EAAA,CAxVhBhD,WAoUenD,EAAAoG,oBApUf,sBAAA9pB,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAoUeyjB,EAAAoG,oBAAmB7pB,CAAA,GAC5B2a,MAAM,OACNpb,MAAM,S,CAYKuqB,QAAM1qB,EAAAA,EAAAA,KACf,iBAGO,EAHPI,EAAAA,EAAAA,IAGO,OAHPuqB,EAGO,EAFL1qB,EAAAA,EAAAA,IAA8DkC,EAAA,CAAlDzB,QAAKC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAAEyjB,EAAAoG,qBAAsB,CAAH,I,CApVhD,SAAAzqB,EAAAA,EAAAA,KAoV0D,kBAAEW,EAAA,MAAAA,EAAA,MApV5D2B,EAAAA,EAAAA,IAoV0D,O,IApV1DpB,EAAA,EAAAC,GAAA,QAqVUlB,EAAAA,EAAAA,IAAyFkC,EAAA,CAA9EC,KAAK,SAAUuK,QAAS0X,EAAAuG,WAAalqB,QAAO2jB,EAAAwG,kB,CArVjE,SAAA7qB,EAAAA,EAAAA,KAqVmF,kBAAIW,EAAA,MAAAA,EAAA,MArVvF2B,EAAAA,EAAAA,IAqVmF,S,IArVnFpB,EAAA,EAAAC,GAAA,M,+BAAA,SAAAnB,EAAAA,EAAAA,KAwUM,iBASU,EATVC,EAAAA,EAAAA,IASU6qB,EAAA,MAjVhB,SAAA9qB,EAAAA,EAAAA,KAyUQ,iBAOe,EAPfC,EAAAA,EAAAA,IAOe8qB,EAAA,CAPDtF,MAAM,QAAM,CAzUlC,SAAAzlB,EAAAA,EAAAA,KA0UU,iBAKY,EALZC,EAAAA,EAAAA,IAKYsnB,EAAA,CA/UtBC,WA2UqBnD,EAAA/O,YA3UrB,sBAAA3U,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA2UqByjB,EAAA/O,YAAW1U,CAAA,GACpBwB,KAAK,WACLqlB,KAAK,IACLC,YAAY,c,2BA9UxBxmB,EAAA,I,IAAAA,EAAA,I,IAAAA,EAAA,G,kQAmWA,SACEvB,KAAM,uBAEN6b,MAAO,CACLwP,eAAgB,CACd5oB,KAAM6oB,QACN7B,SAAS,IAIb8B,MAAK,SAAC1P,GAEJ,IAAMqN,GAAcsC,EAAAA,EAAAA,IAAI,IAClBxe,GAAUwe,EAAAA,EAAAA,KAAI,GACdP,GAAaO,EAAAA,EAAAA,KAAI,GACjBhB,GAAcgB,EAAAA,EAAAA,IAAI,GAClBlB,GAAWkB,EAAAA,EAAAA,IAAI,IACfjB,GAAQiB,EAAAA,EAAAA,IAAI,GACZ/C,GAAc+C,EAAAA,EAAAA,IAAI,IAClBtR,GAAQsR,EAAAA,EAAAA,IAAI,IACZxmB,GAAcwmB,EAAAA,EAAAA,IAAI,MAElB1C,GAAU2C,EAAAA,EAAAA,IAAS,CACvB3a,KAAM,OAIFga,GAAsBU,EAAAA,EAAAA,KAAI,GAC1B1G,GAAqB0G,EAAAA,EAAAA,IAAI,MAGzB7V,GAAc6V,EAAAA,EAAAA,IAAI,IAGlB5G,GAAkB4G,EAAAA,EAAAA,KAAI,GAGtBlc,IADSoc,EAAAA,EAAAA,OACDC,EAAAA,EAAAA,OAGRhH,GAAe9f,EAAAA,EAAAA,KAAS,WAC5B,OAAOgX,EAAMwP,gBAAiC,uBAAf/b,EAAMjK,IACvC,IAGMumB,EAAc,eAAA5iB,GAAA0D,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAC,IAAA,IAAA0E,EAAAhK,EAAA2F,EAAA,OAAAP,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAGyB,GAHzBD,EAAAM,EAAA,EAGd6D,EAAW9L,aAAaC,QAAQ,SAClC6L,EAAU,CAAFnE,EAAAC,EAAA,QACVrI,EAAYiE,MAAQ1D,KAAKC,MAAM+L,GAASnE,EAAAC,EAAA,sBAAAD,EAAAC,EAAA,EAGjBlG,EAAAA,WAAI7B,KAAKsR,iBAAgB,OAA1CrP,EAAO6F,EAAAY,EACbhJ,EAAYiE,MAAQ1B,EAAS5C,KAC7Bc,aAAac,QAAQ,OAAQhB,KAAK8Q,UAAUrR,EAAYiE,QAAO,OAAAmE,EAAAC,EAAA,eAAAD,EAAAM,EAAA,EAAAR,EAAAE,EAAAY,EAGjEnI,QAAQC,MAAM,aAAYoH,GAC1BsT,EAAAA,GAAU1a,MAAM,kBAAiB,cAAAsH,EAAAE,EAAA,MAAAT,EAAA,kBAEpC,kBAhBmB,OAAA7D,EAAAob,MAAA,KAAAN,UAAA,KAmBd+H,EAAQ,eAAAva,GAAA5E,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAA4E,IAAA,IAAAjK,EAAAkK,EAAA,OAAA9E,EAAAA,EAAAA,KAAAQ,GAAA,SAAAuE,GAAA,eAAAA,EAAArE,GAAA,cAAAqE,EAAAhE,EAAA,EAAAgE,EAAArE,EAAA,EAESlG,EAAAA,WAAI+S,MAAM4R,cAAa,OAAxCvkB,EAAOmK,EAAA1D,EACbkM,EAAMjR,MAAQ1B,EAAS5C,KAAI+M,EAAArE,EAAA,eAAAqE,EAAAhE,EAAA,EAAA+D,EAAAC,EAAA1D,EAE3BnI,QAAQC,MAAM,WAAU2L,GACxB+O,EAAAA,GAAUjX,QAAQ,YAAW,cAAAmI,EAAApE,EAAA,MAAAkE,EAAA,kBAEhC,kBARa,OAAAF,EAAA8S,MAAA,KAAAN,UAAA,KAWR8E,EAAc,eAAAhX,GAAAlF,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAoF,IAAA,IAAA+Z,EAAAjW,EAAAvO,EAAA0K,EAAA,OAAAtF,EAAAA,EAAAA,KAAAQ,GAAA,SAAA+E,GAAA,eAAAA,EAAA7E,GAAA,OAmBpB,OAlBFL,EAAQ/D,OAAQ,EAAIiJ,EAAAxE,EAAA,EAEZqe,EAAWxmB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACtDoQ,EAAS,CACbkW,KAAMxB,EAAYvhB,MAAQ,EAC1BpF,KAAMymB,EAASrhB,MACfgjB,KAAM,kBACNC,OAAQzD,EAAYxf,MAAMC,OAC1B+Q,OAAQ6O,EAAQhY,MAII,UAAlBib,EAASpmB,OACXmQ,EAAOhJ,OAASif,EAAS7lB,IAAM6lB,EAAS9lB,UAG1CJ,QAAQO,IAAI,oBAAqB0P,GAEjC5D,EAAA7E,EAAA,EACuBmH,IAAAA,IAAU,4CAA6C,CAAEsB,OAAAA,IAAS,OAAnFvO,EAAO2K,EAAAlE,EAETzG,EAAS5C,MAAQ4C,EAAS5C,KAAKmQ,SACjCoU,EAAYjgB,MAAQ1B,EAAS5C,KAAKmQ,QAAQsE,KAAI,SAAAnE,GAE5C,IAAM3P,EAAO2P,EAAK3P,MAAQ,CAAC,EAC3B,GAAIA,IAASA,EAAKwL,MAAQxL,EAAKghB,QAAS,CAEtC,IAAMxV,EAAOoJ,EAAMjR,MAAMkjB,MAAK,SAAAC,GAAA,OAAKA,EAAElmB,KAAOZ,EAAKghB,OAAO,IACpDxV,IACFxL,EAAKwL,KAAOA,EAEhB,CAEA,OAAAhM,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACKmQ,GAAI,IAEP2U,YAAa3U,EAAKyR,UAClBN,YAAa9gB,EACbA,KAAMA,GAEV,IACAilB,EAAMthB,MAAQ1B,EAAS5C,KAAK0nB,gBAE5BnD,EAAYjgB,MAAQ,GACpBshB,EAAMthB,MAAQ,GAChBiJ,EAAA7E,EAAA,eAAA6E,EAAAxE,EAAA,EAAAuE,EAAAC,EAAAlE,EAEAnI,QAAQC,MAAM,aAAYmM,GAC1BuO,EAAAA,GAAU1a,MAAM,mBAChBojB,EAAYjgB,MAAQ,GACpBshB,EAAMthB,MAAQ,EAAC,OAEM,OAFNiJ,EAAAxE,EAAA,EAEfV,EAAQ/D,OAAQ,EAAKiJ,EAAA1D,EAAA,iBAAA0D,EAAA5E,EAAA,MAAA0E,EAAA,sBAExB,kBAvDmB,OAAAJ,EAAAwS,MAAA,KAAAN,UAAA,KA0Dd4G,EAAmB,SAAC7mB,GACxBymB,EAASrhB,MAAQpF,EACjB2mB,EAAYvhB,MAAQ,EACpB2f,GACF,EAEMgC,EAAsB,SAACoB,GAC3BxB,EAAYvhB,MAAQ+iB,EACpBpD,GACF,EAGM0D,EAAY,SAAC5R,GAEjB,IAAK1V,EAAYiE,MAAO,OAAO,EAG/B,GAA+B,UAA3BjE,EAAYiE,MAAMtD,KAAkB,OAAO,EAG/C,GAA+B,aAA3BX,EAAYiE,MAAMtD,KAAqB,OAAO,EAGlD,IAAK+U,EAAW5J,OAAS4J,EAAW4L,QAAS,OAAO,EAGpD,IAAMiG,EACH7R,EAAW5J,MAAQ4J,EAAW5J,KAAK5K,IAAOwU,EAAW4L,QAElDkG,EACHxnB,EAAYiE,MAAM6H,MAAQ9L,EAAYiE,MAAM6H,KAAK5K,IAAOlB,EAAYiE,MAAMqd,QAG7E,OAAOkG,GAAcA,IAAeD,CACtC,EAGMnD,EAAkB,SAAJrX,GAAiB,IAAV4X,EAAE5X,EAAF4X,IACzB,OAAKA,EAAI7Y,KAGF,GAFE,aAGX,EAGMwY,EAAmB,eAAAlX,GAAA1F,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAyF,EAAOqI,GAAU,IAAA+R,EAAAllB,EAAAjC,EAAAwL,EAAAyB,EAAAM,EAAA,OAAAlG,EAAAA,EAAAA,KAAAQ,GAAA,SAAAqF,GAAA,eAAAA,EAAAnF,GAAA,OAcT,GAbnCxH,QAAQO,IAAI,SAAUsU,GAAWlI,EAAA9E,EAAA,EAI/BoX,EAAmB7b,MAAQyR,EAG3BkK,EAAgB3b,OAAQ,EAGxB0M,EAAY1M,MAAQ,GAGdwjB,EAAc/R,EAAWxU,IAC3BumB,EAAa,CAAFja,EAAAnF,EAAA,QAKX,OALWmF,EAAA9E,EAAA,EAEX7H,QAAQO,IAAI,iBAAD+H,OAAkBse,IAC7Bzf,EAAQ/D,OAAQ,EAEhBuJ,EAAAnF,EAAA,EACuBmH,IAAAA,IAAU,qCAADrG,OAAsCse,IAAc,OAA9EllB,EAAOiL,EAAAxE,EAETzG,EAAS5C,OACXkB,QAAQO,IAAI,cAAemB,EAAS5C,MAG9BW,EAAOiC,EAAS5C,KAAKW,MAAQwf,EAAmB7b,MAAM3D,MAAQwf,EAAmB7b,MAAMmd,aAAe,CAAC,EAGzG9gB,IAASA,EAAKwL,MAAQxL,EAAKghB,UACvBxV,EAAOoJ,EAAMjR,MAAMkjB,MAAK,SAAAC,GAAA,OAAKA,EAAElmB,KAAOZ,EAAKghB,OAAO,IACpDxV,IACFxL,EAAKwL,KAAOA,IAKhBgU,EAAmB7b,OAAInE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClBggB,EAAmB7b,OACnB1B,EAAS5C,MAAI,IAEhBW,KAAMA,EACN8gB,YAAa9gB,IAGfO,QAAQO,IAAI,cAAe0e,EAAmB7b,QAChDuJ,EAAAnF,EAAA,eAAAmF,EAAA9E,EAAA,EAAA6E,EAAAC,EAAAxE,EAEAnI,QAAQC,MAAM,cAAayM,GAC3BiO,EAAAA,GAAU1a,MAAM,kBAAiB,OAEZ,OAFY0M,EAAA9E,EAAA,EAEjCV,EAAQ/D,OAAQ,EAAKuJ,EAAAhE,EAAA,UAAAgE,EAAAnF,EAAA,eAAAmF,EAAA9E,EAAA,EAAAmF,EAAAL,EAAAxE,EAIzBnI,QAAQC,MAAM,YAAW+M,GACzB2N,EAAAA,GAAU1a,MAAM,YAAW,cAAA0M,EAAAlF,EAAA,MAAA+E,EAAA,4BAE9B,gBA3DwBqa,GAAA,OAAAta,EAAAgS,MAAA,KAAAN,UAAA,KA8DnBe,EAAa,WACjBD,EAAgB3b,OAAQ,EACxB6b,EAAmB7b,MAAQ,IAC7B,EAGMof,EAAgB,eAAA1V,GAAAjG,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAgG,EAAO8H,GAAU,IAAAqR,EAAAjf,EAAAyQ,EAAAhW,EAAAolB,EAAAC,EAAAC,EAAAC,EAAAlX,EAAAO,EAAA4W,EAAA,OAAApgB,EAAAA,EAAAA,KAAAQ,GAAA,SAAA2F,GAAA,eAAAA,EAAAzF,GAAA,cAAAyF,EAAApF,EAAA,EAAAoF,EAAAzF,EAAA,EAGjCiV,EAAAA,EAAaG,QAAQ,WAEzB,OACA,CACEra,kBAAmB,KACnBC,iBAAkB,KAClB5F,KAAM,SAET,OAUuC,OAPxCwoB,EAAWhiB,OAAQ,EAGb8iB,EAAWxmB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACtDoH,EAASif,EAAS9lB,UAAY8lB,EAAS7lB,IAAM,GAC7CqX,EAAWwO,EAASpmB,MAAQ,WAElCE,QAAQO,IAAI,UAAW0G,EAAQyQ,GAASzK,EAAApF,EAAA,EAAAoF,EAAAzF,EAAA,EAIflG,EAAAA,WAAI6lB,QAAQC,cACjCvS,EAAWxU,GACX,CACEyP,YAAaA,EAAY1M,OAAS,SAErC,OALK1B,EAAOuL,EAAA9E,EAObnI,QAAQO,IAAI,UAAWmB,EAAS5C,MAGhC+V,EAAWlT,OAAS,WAEpBgZ,EAAAA,GAAUnX,QAAQ,mBAGlBwb,IACA+D,IAAiB9V,EAAAzF,EAAA,eAQW,OARXyF,EAAApF,EAAA,EAAAkI,EAAA9C,EAAA9E,EAEjBnI,QAAQC,MAAM,WAAU8P,GACxB/P,QAAQO,IAAI,QAA0B,QAAnBumB,EAAE/W,EAASrO,gBAAQ,IAAAolB,OAAA,EAAjBA,EAAmBhoB,MACxC6b,EAAAA,GAAU1a,MAAM,SAADqI,QAA2B,QAAjBye,EAAAhX,EAASrO,gBAAQ,IAAAqlB,GAAM,QAANA,EAAjBA,EAAmBjoB,YAAI,IAAAioB,OAAA,EAAvBA,EAAyBtjB,UAAWsM,EAAStM,UAEtEwJ,EAAApF,EAAA,EAEE7H,QAAQO,IAAI,eAAc0M,EAAAzF,EAAA,EACGmH,IAAAA,IAAU,qCAADrG,OAAsCuM,EAAWxU,IAAM,CAC3FsB,OAAQ,WACRmO,YAAaA,EAAY1M,OAAS,SAClC,OAHI4jB,EAAa/Z,EAAA9E,EAKnBnI,QAAQO,IAAI,UAAWymB,EAAeloB,MAGtC+V,EAAWlT,OAAS,WAEpBgZ,EAAAA,GAAUnX,QAAQ,yBAGlBwb,IACA+D,IAAiB9V,EAAAzF,EAAA,eAAAyF,EAAApF,EAAA,EAAAyI,EAAArD,EAAA9E,EAEjBnI,QAAQC,MAAM,WAAUqQ,GACxBqK,EAAAA,GAAU1a,MAAM,mBAAkB,OAAAgN,EAAAzF,EAAA,gBAAAyF,EAAApF,EAAA,EAAAqf,EAAAja,EAAA9E,EAIxB,WAAV+e,IACFlnB,QAAQC,MAAM,SAAQinB,GACtBvM,EAAAA,GAAU1a,OAAoB,QAAdgnB,EAAAC,EAAMxlB,gBAAQ,IAAAulB,OAAA,EAAdA,EAAgBnoB,OAAQ,WAC1C,QAEwB,OAFxBmO,EAAApF,EAAA,GAEAud,EAAWhiB,OAAQ,EAAK6J,EAAAtE,EAAA,mBAAAsE,EAAAxF,EAAA,MAAAsF,EAAA,oCAE3B,gBA9EqBsa,GAAA,OAAAva,EAAAyR,MAAA,KAAAN,UAAA,KAiFhBwE,EAAmB,SAAC5N,GACxBoK,EAAmB7b,MAAQyR,EAC3BoQ,EAAoB7hB,OAAQ,CAC9B,EAGMiiB,EAAe,eAAA3V,GAAA7I,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAA6I,IAAA,IAAAsW,EAAAjf,EAAAyQ,EAAAhW,EAAA4lB,EAAAC,EAAAP,EAAAQ,EAAAC,EAAAC,EAAAC,EAAA,OAAA7gB,EAAAA,EAAAA,KAAAQ,GAAA,SAAA0I,GAAA,eAAAA,EAAAxI,GAAA,UAClBsI,EAAY1M,MAAO,CAAF4M,EAAAxI,EAAA,QACQ,OAA5BmT,EAAAA,GAAUjX,QAAQ,WAAUsM,EAAAvI,EAAA,UAYY,OAZZuI,EAAAnI,EAAA,EAK5Bud,EAAWhiB,OAAQ,EAGb8iB,EAAWxmB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACtDoH,EAASif,EAAS9lB,UAAY8lB,EAAS7lB,IAAM,GAC7CqX,EAAWwO,EAASpmB,MAAQ,WAElCE,QAAQO,IAAI,UAAW0G,EAAQyQ,GAAS1H,EAAAnI,EAAA,EAAAmI,EAAAxI,EAAA,EAIflG,EAAAA,WAAI6lB,QAAQS,aACjC3I,EAAmB7b,MAAM/C,GACzB,CACEyP,YAAaA,EAAY1M,QAE5B,OALK1B,EAAOsO,EAAA7H,EAObnI,QAAQO,IAAI,UAAWmB,EAAS5C,MAGhCmmB,EAAoB7hB,OAAQ,EAG5B6b,EAAmB7b,MAAMzB,OAAS,WAElCgZ,EAAAA,GAAUnX,QAAQ,mBAGdub,EAAgB3b,OAClB4b,IAIF+D,IAAiB/S,EAAAxI,EAAA,eAUf,OAVewI,EAAAnI,EAAA,EAAA4f,EAAAzX,EAAA7H,EAEjBnI,QAAQC,MAAM,WAAUwnB,GACxBznB,QAAQO,IAAI,QAA0B,QAAnB+mB,EAAEG,EAAS/lB,gBAAQ,IAAA4lB,OAAA,EAAjBA,EAAmBxoB,MACxC6b,EAAAA,GAAU1a,MAAM,SAADqI,QAA2B,QAAjBif,EAAAE,EAAS/lB,gBAAQ,IAAA6lB,GAAM,QAANA,EAAjBA,EAAmBzoB,YAAI,IAAAyoB,OAAA,EAAvBA,EAAyB9jB,UAAWgkB,EAAShkB,UAEtEuM,EAAAnI,EAAA,EAEE7H,QAAQO,IAAI,eAEZyP,EAAAxI,EAAA,EAC6BlG,EAAAA,WAAI6lB,QAAQU,mBACvC5I,EAAmB7b,MAAM/C,GACzByP,EAAY1M,OACb,OAHK4jB,EAAahX,EAAA7H,EAKnBnI,QAAQO,IAAI,UAAWymB,GAGvB/B,EAAoB7hB,OAAQ,EAG5B6b,EAAmB7b,MAAMzB,OAAS,WAElCgZ,EAAAA,GAAUnX,QAAQ,yBAGdub,EAAgB3b,OAClB4b,IAIF+D,IAAiB/S,EAAAxI,EAAA,eAAAwI,EAAAnI,EAAA,EAAA6f,EAAA1X,EAAA7H,EAEjBnI,QAAQC,MAAM,WAAUynB,GACxB/M,EAAAA,GAAU1a,MAAM,mBAAkB,OAAA+P,EAAAxI,EAAA,gBAAAwI,EAAAnI,EAAA,EAAA8f,EAAA3X,EAAA7H,EAItCnI,QAAQC,MAAM,SAAQ0nB,GACtBhN,EAAAA,GAAU1a,OAAoB,QAAdunB,EAAAG,EAAMjmB,gBAAQ,IAAA8lB,OAAA,EAAdA,EAAgB1oB,OAAQ,UAAS,QAEzB,OAFyBkR,EAAAnI,EAAA,GAEjDud,EAAWhiB,OAAQ,EAAK4M,EAAArH,EAAA,mBAAAqH,EAAAvI,EAAA,MAAAmI,EAAA,oCAE3B,kBArFoB,OAAAF,EAAA6O,MAAA,KAAAN,UAAA,KAwFf0C,EAAiB,SAACmH,GACtB,IAAKA,EAAgB,MAAO,IAE5B,IACE,IAAMphB,EAAO,IAAIC,KAAKmhB,GAGtB,OAAIC,MAAMrhB,EAAKshB,WACN,IAGF,IAAIC,KAAKC,eAAe,QAAS,CACtCC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,YACPC,OAAO9hB,EACZ,CAAE,MAAOzG,GAEP,OADAD,QAAQmL,KAAK,WAAY2c,EAAgB7nB,GAClC,GACT,CACF,EAGM0S,EAAe,SAACkC,GAAe,IAAA4T,EAAAC,EACnC,IAAK7T,EAAY,OAAO,KAKxB,GAHA7U,QAAQO,IAAI,wBAAyBsU,GAGjCA,EAAWuK,eAEb,OADApf,QAAQO,IAAI,iCAAkCsU,EAAWuK,gBAClDvK,EAAWuK,eAIpB,GAAIvK,EAAW8T,WAAa9T,EAAW8T,UAAUvJ,eAE/C,OADApf,QAAQO,IAAI,yCAA0CsU,EAAW8T,UAAUvJ,gBACpEvK,EAAW8T,UAAUvJ,eAwB9B,IApBA,IAAMwJ,EAAgB,CAEpB/T,EAAWuK,eACXvK,EAAWgU,aAGS,QADpBJ,EACA5T,EAAW8T,iBAAS,IAAAF,OAAA,EAApBA,EAAsBrJ,eACF,QADgBsJ,EACpC7T,EAAW8T,iBAAS,IAAAD,OAAA,EAApBA,EAAsBG,aAGtBhU,EAAWiU,qBACXjU,EAAWkU,mBAGXlU,EAAWmU,WACXnU,EAAW1C,UACX0C,EAAWrV,MAIbsc,EAAA,EAAAmN,EAAmBL,EAAa9M,EAAAmN,EAAArmB,OAAAkZ,IAAE,CAA7B,IAAMtc,EAAGypB,EAAAnN,GACZ,GAAItc,EAEF,OADAQ,QAAQO,IAAI,UAAWf,GAChBA,CAEX,CAGA,OADAQ,QAAQO,IAAI,aACL,IACT,EAGMif,EAAkB,SAAChgB,GACvB,IAAKA,EAAM,MAAO,GAGlB,GAAIA,EAAK6L,WAAW,YAAc7L,EAAK6L,WAAW,YAChD,OAAO7L,EAIT,IAAI0pB,EAGJ,IAEE,IAAMC,EAAY9vB,OAAO8vB,WAAa,CAAC,EACvCD,EAAUC,EAAU3L,YACtB,CAAE,MAAO7Z,GACP3D,QAAQO,IAAI,2BACd,CAGK2oB,IAEHA,EAAU7vB,OAAO4I,SAASmnB,OAC1BppB,QAAQO,IAAI,uBAAwB2oB,IAItC,IAAIG,EAAiB7pB,EAAK6L,WAAW,KAAO7L,EAAG,IAAA8I,OAAQ9I,GAGjD0mB,EAAWxmB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACtDoH,EAASif,EAAS9lB,UAAY8lB,EAAS7lB,IAAM,GAG7CipB,EAAYD,EAAe/R,SAAS,KAAO,IAAM,IAGvD,OAFA+R,EAAa,GAAA/gB,OAAO+gB,GAAc/gB,OAAGghB,EAAS,WAAAhhB,OAAUrB,EAAM,OAAAqB,QAAM,IAAI3B,MAAOqhB,WAExE,GAAP1f,OAAU4gB,GAAO5gB,OAAG+gB,EACtB,GAGAE,EAAAA,EAAAA,KAAU,WACRvpB,QAAQO,IAAI,kCACZP,QAAQO,IAAI,QAAShH,cACrByG,QAAQO,IAAI,WAAYoO,IAAAA,SAAe6a,SAGvCnwB,OAAOowB,sBAAwB,CAC7B1G,gBAAAA,EACA2G,YAAa,WACX,IAAMxiB,EAAUtH,aAAaC,QAAQ,QACrC,OAAOqH,EAAUxH,KAAKC,MAAMuH,GAAW,IACzC,EACAyiB,eAAgB,WAAF,IAAAC,GAAA/iB,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAE,SAAAqJ,IAAA,IAAAyZ,EAAA3D,EAAAjf,EAAAvF,EAAA5C,EAAAgrB,EAAA,OAAAhjB,EAAAA,EAAAA,KAAAQ,GAAA,SAAAiJ,GAAA,eAAAA,EAAA/I,GAAA,OAMZ,OANY+I,EAAA1I,EAAA,EAGNqe,EAAWxmB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACtDoH,EAASif,EAAS9lB,UAAY8lB,EAAS7lB,GAE7CkQ,EAAA/I,EAAA,EACuBO,MAAM,+BAADO,OAAgCrB,EAAM,oBAAkB,OAAvE,OAAPvF,EAAO6O,EAAApI,EAAAoI,EAAA/I,EAAA,EACM9F,EAAS6G,OAAM,OAI0C,OAJtEzJ,EAAGyR,EAAApI,EACTnI,QAAQO,IAAI,aAAczB,GAG1BkB,QAAQO,IAAI,KAAD+H,OAAMxJ,EAAK0nB,cAAa,WAAAle,QAAsB,QAAZuhB,EAAA/qB,EAAKmQ,eAAO,IAAA4a,OAAA,EAAZA,EAAcjnB,SAAU,EAAC,QAAM2N,EAAA9I,EAAA,EACrE3I,GAAI,OAAAyR,EAAA1I,EAAA,EAAAiiB,EAAAvZ,EAAApI,EAEXnI,QAAQC,MAAM,aAAY6pB,GAAQ,cAAAvZ,EAAA9I,EAAA,MAAA2I,EAAA,kBAEtC,SAjBAuZ,IAAc,OAAAC,EAAArL,MAAA,KAAAN,UAAA,QAAd0L,CAAc,CAAE,IAoBlB5D,IACAC,IACAjD,GACF,IAGA,IAAMrC,EAAkB,SAACtM,GACvB,IAAKA,EAAQ,MAAO,MAGpB,IAAMnJ,EAAOoJ,EAAMjR,MAAMkjB,MAAK,SAAAC,GAAA,OAAKA,EAAElmB,KAAO+T,CAAM,IAClD,OAAInJ,EACKA,EAAK9Q,KAIP,KAAPmO,OAAY8L,EACd,EAGMoM,EAAoB,SAACuJ,GACzB,OAAKA,GAGD5qB,EAAYiE,OAAUjE,EAAYiE,MAAM/C,KAAO0pB,GAAY5qB,EAAYiE,MAAMhD,WAAa2pB,EAKvF,KAAPzhB,OAAYyhB,GAJH5qB,EAAYiE,MAAMjJ,KAJL,IASxB,EAGMkmB,EAAuB,SAAC1e,GAC5B,IAAKA,EAAQ,MAAO,KAEpB,IAAMqoB,EAAY,CAChB,MAAS,KACT,UAAa,MACb,SAAY,MACZ,SAAY,MACZ,SAAY,OAGd,OAAOA,EAAUroB,IAAWA,CAC9B,EAGMye,EAAmB,SAACze,GACxB,IAAKA,EAAQ,MAAO,OAEpB,IAAMsoB,EAAU,CACd,MAAS,OACT,UAAa,UACb,SAAY,UACZ,SAAY,SACZ,SAAY,WAGd,OAAOA,EAAQtoB,IAAW,MAC5B,EAEA,MAAO,CACL0hB,YAAAA,EACAlc,QAAAA,EACAie,WAAAA,EACAT,YAAAA,EACAF,SAAAA,EACAC,MAAAA,EACA9B,YAAAA,EACAK,QAAAA,EACA5O,MAAAA,EACA4Q,oBAAAA,EACAhG,mBAAAA,EACAF,gBAAAA,EACAgE,gBAAAA,EACA8B,iBAAAA,EACAE,oBAAAA,EACA0B,UAAAA,EACAhD,qBAAAA,EACAzE,WAAAA,EACAwD,kBAAAA,EACAC,iBAAAA,EACA4C,iBAAAA,EACA1E,eAAAA,EACA4C,gBAAAA,EACApkB,YAAAA,EACAuhB,gBAAAA,EACAF,kBAAAA,EACAhB,gBAAAA,EACA7M,aAAAA,EACAmM,aAAAA,EACAhP,YAAAA,EAEAuQ,qBAAAA,EACAD,iBAAAA,EAEJ,G,eCv/BF,MAAMhmB,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G,giBCNM6vB,EAAYvb,IAAAA,OAAa,CAC7B6a,QAASjwB,CAAAA,SAAAA,aAAAA,mBAAAA,OAAAA,gBAAAA,GAAAA,qBAAAA,WAAAA,SAAAA,KAAY4wB,sBAAwB,eAC7C/O,QAAS,IACTF,iBAAiB,IAInBgP,EAAUE,aAAa/O,QAAQC,KAC7B,SAAAC,GAME,GAJAA,EAAOnU,IAAMijB,EAAc9O,EAAOnU,KAClCpH,QAAQO,IAAI,SAAD+H,OAAUiT,EAAOvT,OAAOsiB,cAAa,KAAAhiB,OAAIiT,EAAOiO,SAAOlhB,OAAGiT,EAAOnU,MAGxEmU,EAAOnU,IAAIkQ,SAAS,cAAgBiE,EAAOnU,IAAIkQ,SAAS,kBAAmB,CAC7EtX,QAAQO,IAAI,0BAGZgb,EAAOtT,QAAU,CACf,eAAgB,mBAChB,OAAU,oBAIZ,IAAMqhB,EAAY/N,EAAOnU,IAAIkQ,SAAS,KAAO,IAAM,IACnDiE,EAAOnU,KAAO,GAAJkB,OAAOghB,EAAS,OAAAhhB,OAAM3B,KAAKoS,OAGrC,IACE,IAAM7R,EAAUtH,aAAaC,QAAQ,QACrC,GAAIqH,EAAS,CACX,IAAMzH,EAAOC,KAAKC,MAAMuH,GACxB,GAAIzH,IAASA,EAAKY,IAAMZ,EAAKW,UAAW,CACtC,IAAM6G,EAASxH,EAAKW,UAAYX,EAAKY,GACrCkb,EAAOtT,QAAQ,iBAAmB,eAAHK,OAAkBrB,GAGjD,IAAMsjB,EAAiBhP,EAAOnU,IAAIkQ,SAAS,KAAO,IAAM,IACxDiE,EAAOnU,KAAO,GAAJkB,OAAOiiB,EAAc,WAAAjiB,OAAUrB,EAC3C,CACF,CACF,CAAE,MAAOtD,GACP3D,QAAQmL,KAAK,kBAAmBxH,EAClC,CAKA,OAFA4X,EAAOtT,QAAQ,aAAehB,OAEvBsU,CACT,CAGA,IAAI9b,EACJ,IAEE,IAAMyH,EAAUtH,aAAaC,QAAQ,QAMrC,GALIqH,IACFzH,EAAOC,KAAKC,MAAMuH,KAIfzH,IAASA,EAAKY,GAAI,CACrB,IAAMmqB,EAAiBroB,eAAetC,QAAQ,iBAC1C2qB,IACFxqB,QAAQO,IAAI,qBACZd,EAAOC,KAAKC,MAAM6qB,GAElB5qB,aAAac,QAAQ,OAAQ8pB,GAEjC,CAGA,GAAI/qB,KAAUA,EAAKqL,QAAUrL,EAAKK,MAAO,CACvC,IAAM2qB,EAAiB7qB,aAAaC,QAAQ,eAC5C,GAAI4qB,EAAgB,CAClB,IAAMC,EAAchrB,KAAKC,MAAM8qB,GAE/BhrB,GAAIR,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQQ,GAASirB,GACrB1qB,QAAQO,IAAI,sBACd,CACF,CACF,CAAE,MAAOoD,GACP3D,QAAQC,MAAM,YAAa0D,EAC7B,CAEA,GAAIlE,EAAM,CAER,IAAMwH,EAASxH,EAAKY,IAAMZ,EAAKW,UAAY,GAS3C,GANAmb,EAAOtT,QAAQ,iBAAmB,eAAHK,OAAkBrB,GAGjDsU,EAAOtT,QAAQ,aAAehB,EAG1BxH,EAAKK,KAAM,CAEb,IAAMmQ,EAAS,IAAI0a,gBAAgBpP,EAAOtL,QAAU,CAAC,GAChDA,EAAO2a,IAAI,UACZ3a,EAAO4a,IAAI,QAASprB,EAAKK,MAE7Byb,EAAOtL,OAASA,CAClB,CAGA,IAAMA,EAAS,IAAI0a,gBAAgBpP,EAAOtL,QAAU,CAAC,GAOrD,GANKA,EAAO2a,IAAI,WACZ3a,EAAO4a,IAAI,SAAU5jB,GAEzBsU,EAAOtL,OAASA,EAGZxQ,EAAKwL,MAAQxL,EAAKwL,KAAK5K,KACvBkb,EAAOnU,IAAIkQ,SAAS,YACpBiE,EAAOnU,IAAIkQ,SAAS,uBACpBiE,EAAOnU,IAAIkQ,SAAS,kBAAmB,CACzC,IAAMrH,EAAS,IAAI0a,gBAAgBpP,EAAOtL,QACrCA,EAAO2a,IAAI,YACZ3a,EAAO4a,IAAI,UAAWprB,EAAKwL,KAAK5K,IAEpCkb,EAAOtL,OAASA,CAClB,CAEF,MACEjQ,QAAQmL,KAAK,yBAIXoQ,EAAOvT,QAA0C,YAAhCuT,EAAOvT,OAAO8iB,gBAEjCvP,EAAOtT,QAAQ,kCAAoC,8BACnDsT,EAAOtT,QAAQ,iCAAmC,0BAI/CsT,EAAOtT,QAAQ,mBAClBsT,EAAOtT,QAAQ,iBAAmB,YAIpC,IAAM8iB,EAAiB,MAAHziB,OAAS3B,KAAKoS,OA8BlC,OA7BIwC,EAAOnU,IAAIkQ,SAAS,KACtBiE,EAAOnU,KAAO,IAAJkB,OAAQyiB,GAElBxP,EAAOnU,KAAO,IAAJkB,OAAQyiB,GAIhBxP,EAAOyP,mBAAqBnc,MAAMC,QAAQyM,EAAOyP,mBACnDzP,EAAOyP,mBAAiBC,EAAAA,EAAAA,GAAO1P,EAAOyP,mBAEtCzP,EAAOyP,kBAAoB,CAAC,SAAAlsB,GAC1B,GAAoB,kBAATA,EACT,IACE,OAAOY,KAAKC,MAAMb,EACpB,CAAE,MAAO6E,GAEP,OADA3D,QAAQmL,KAAK,YAAaxH,GACnB7E,CACT,CAEF,OAAOA,CACT,GAIFkB,QAAQO,IAAI,aAAD+H,OAAciT,EAAOvT,OAAOsiB,cAAa,KAAAhiB,OAAIiT,EAAOiO,SAAOlhB,OAAGiT,EAAOnU,KAAO,CACrFuS,GAAI4B,EAAOtL,OACXib,GAAI3P,EAAOtT,UAGNsT,CACT,IACA,SAAAtb,GAEE,OADAD,QAAQC,MAAM,sBAAuBA,GAC9Bub,QAAQC,OAAOxb,EACxB,IAIFiqB,EAAUE,aAAa1oB,SAAS4Z,KAAI,SAAA5Z,GAIlC,OAHA1B,QAAQO,IAAI,WAAD+H,OAAY5G,EAASC,OAAM,KAAA2G,OAAI5G,EAAS6Z,OAAOnU,KAAO,CAC/D+jB,GAAIzpB,EAAS5C,OAER4C,CACT,IAAG,SAAAzB,GAOD,OANAD,QAAQC,MAAM,YAAa,CACzBmrB,MAAOnrB,EAAMsb,OAAStb,EAAMsb,OAAOnU,IAAM,KACzCikB,KAAMprB,EAAMsb,OAAStb,EAAMsb,OAAOvT,OAAS,KAC3CsjB,IAAKrrB,EAAMyB,SAAWzB,EAAMyB,SAASC,OAAS,OAC9C4pB,KAAMtrB,EAAMyB,SAAWzB,EAAMyB,SAAS5C,KAAO,OAExC0c,QAAQC,OAAOxb,EACxB,IAIA,IAAMurB,EAAgB7c,IAAAA,OAAa,CACjC6a,QAASjwB,CAAAA,SAAAA,aAAAA,mBAAAA,OAAAA,gBAAAA,GAAAA,qBAAAA,WAAAA,SAAAA,KAAY4wB,sBAAwB,eAC7CliB,QAAS,CACP,eAAgB,sBAChB,OAAU,oBAEZmT,QAAS,KACTF,iBAAiB,IAIbuQ,EAAiB9c,IAAAA,OAAa,CAClC6a,QAASjwB,CAAAA,SAAAA,aAAAA,mBAAAA,OAAAA,gBAAAA,GAAAA,qBAAAA,WAAAA,SAAAA,KAAY4wB,sBAAwB,eAC7CliB,QAAS,CACP,eAAgB,mBAChB,OAAU,mBACV,UAAW,QAEbmT,QAAS,IACTF,iBAAiB,IAucnB,SAASwQ,IACP,IAEE,IAAMxkB,EAAUtH,aAAaC,QAAQ,QACrC,IAAKqH,EAAS,OAAO,EAErB,IAAMzH,EAAOC,KAAKC,MAAMuH,GAGlBykB,EAAcxpB,eAAetC,QAAQ,YAC3C,GAAI8rB,EAAa,CACf,IAAMC,EAAWlsB,KAAKC,MAAMgsB,GAE5B,GAAIC,EAASC,MAKX,OAJApsB,EAAKosB,MAAQD,EAASC,MAEtBjsB,aAAac,QAAQ,OAAQhB,KAAK8Q,UAAU/Q,IAC5CO,QAAQO,IAAI,iBACL,CAEX,CAGA,SAAId,EAAKqL,QAAUrL,EAAKY,KAAMZ,EAAKW,SAMrC,CAAE,MAAOuD,GAEP,OADA3D,QAAQC,MAAM,YAAa0D,IACpB,CACT,CACF,CAGA,SAASmoB,IACP,IACE9rB,QAAQO,IAAI,mBAGZ,IAAM2G,EAAUtH,aAAaC,QAAQ,QACrC,IAAKqH,EAEH,OADAlH,QAAQO,IAAI,uBACL,EAGT,IAAMd,EAAOC,KAAKC,MAAMuH,GACxB,IAAKzH,IAAUA,EAAKY,KAAOZ,EAAKW,SAE9B,OADAJ,QAAQO,IAAI,mBACL,EAIT,IAAM0G,EAASxH,EAAKW,UAAYX,EAAKY,GAG/B+G,EAAM,wBACN6I,EAAS,CACb5I,IAAI,IAAIV,MAAOqhB,UACf+D,UAAU,GAIN9jB,EAAU,CACd,eAAgB,mBAChB,OAAU,mBACV,gBAAiB,sBAIfhB,IACFgB,EAAQ,iBAAmB,eAAHK,OAAkBrB,IAI5C,IAAM+kB,EAAcjyB,OAAOsZ,KAAKpD,GAC7BsD,KAAI,SAAAtX,GAAG,SAAAqM,OAAO2jB,mBAAmBhwB,GAAI,KAAAqM,OAAI2jB,mBAAmBhc,EAAOhU,IAAK,IACxEiwB,KAAK,KAGFC,EAAU,GAAH7jB,OAAMlB,EAAG,KAAAkB,OAAI0jB,GAI1B,OADAhsB,QAAQO,IAAI,kBACLwH,MAAMokB,EAAS,CACpBnkB,OAAQ,MACRC,QAASA,EACTC,YAAa,YAEZzG,MAAK,SAAAC,GACJ,IAAKA,EAAS0G,GACZ,MAAM,IAAIC,MAAM,aAADC,OAAc5G,EAASC,SAExC,OAAOD,EAAS6G,MAClB,IACC9G,MAAK,SAAA3C,GAOJ,OANAkB,QAAQO,IAAI,UAAWzB,GAEnBA,GAAQA,EAAKW,OACfG,aAAac,QAAQ,OAAQhB,KAAK8Q,UAAU1R,EAAKW,OACjDO,QAAQO,IAAI,aAEP,CACT,IAAE,UACK,SAAAN,GAGL,OAFAD,QAAQmL,KAAK,UAAWlL,IAEjB,CACT,GACJ,CAAE,MAAO0D,GAEP,OADA3D,QAAQC,MAAM,YAAa0D,IACpB,CACT,CACF,CAUA,SAAS0mB,EAAcjjB,GACrB,IAAKA,EAAK,OAAOA,EAGjB,IAAMglB,EAAchlB,EAGhBilB,EAAgBjlB,EACpB,MAAOilB,EAAchhB,WAAW,MAC9BghB,EAAgBA,EAAcC,UAAU,GAc1C,IAVA,IAAMC,EAAW,CACf,CAAEjG,KAAM,2BAA4BkG,QAAS,gBAC7C,CAAElG,KAAM,mBAAoBkG,QAAS,gBACrC,CAAElG,KAAM,mBAAoBkG,QAAS,gBACrC,CAAElG,KAAM,mBAAoBkG,QAAS,YACrC,CAAElG,KAAM,WAAYkG,QAAS,SAK/B1Q,EAAA,EAAA2Q,EAAsBF,EAAQzQ,EAAA2Q,EAAA7pB,OAAAkZ,IAAE,CAA3B,IAAM4Q,EAAOD,EAAA3Q,GACZuQ,EAAc/U,SAASoV,EAAQpG,QACjC+F,EAAgBA,EAAcG,QAAQ,IAAIG,OAAOD,EAAQpG,KAAM,KAAMoG,EAAQF,SAC7ExsB,QAAQO,IAAI,2BAAD+H,OAA4BokB,EAAQpG,KAAI,SAAAhe,OAAQokB,EAAQF,WAC/C,EAExB,CAoBA,OAjBKH,EAAchhB,WAAW,OAC5BghB,EAAgB,IAAMA,GAKpBA,EAAchhB,WAAW,UAAY6e,GAA4C,SAA/BA,EAAU0C,SAASpD,UAEvE6C,EAAgBA,EAAcG,QAAQ,WAAY,KAClDxsB,QAAQO,IAAI,uCAAD+H,OAAwC+jB,KAIjDD,IAAgBC,GAClBrsB,QAAQO,IAAI,8BAAD+H,OAA+B8jB,EAAW,QAAA9jB,OAAO+jB,IAGvDA,CACT,CAGA,SAASQ,EAA6BtR,GACpC,IACE,IAAMrU,EAAUtH,aAAaC,QAAQ,QACrC,IAAKqH,EAAS,OAAOsU,QAAQC,OAAO,IAAIpT,MAAM,YAE9C,IAAM5I,EAAOC,KAAKC,MAAMuH,GAClB4lB,GAAS7tB,EAAAA,EAAAA,GAAA,GAAOsc,GAKtB,GAFAuR,EAAU7kB,SAAOhJ,EAAAA,EAAAA,GAAA,GAAOsc,EAAOtT,SAC/B6kB,EAAU7kB,QAAQ,iBAAmB,eAAHK,OAAkB7I,EAAKW,UAAYX,EAAKY,IACtEZ,EAAKqL,MAAO,CACd,IAAMiiB,EAAc1f,KAAK5N,EAAKqL,OAC9BgiB,EAAU7kB,QAAQ,iBAAmB,GAAHK,OAAMwkB,EAAU7kB,QAAQ,iBAAgB,KAAAK,OAAIykB,EAChF,CAgBA,cAbOD,EAAU7kB,QAAQ,oBAClB6kB,EAAU7kB,QAAQ,uBAClB6kB,EAAU7kB,QAAQ,iCAClB6kB,EAAU7kB,QAAQ,sBAClB6kB,EAAU7kB,QAAQ,kBAGzB6kB,EAAU1lB,IAAMijB,EAAcyC,EAAU1lB,KAGxC0lB,EAAUE,QAAS,EAEnBhtB,QAAQO,IAAI,mBAAoBusB,EAAU1lB,KACnC8iB,EAAU4C,EACnB,CAAE,MAAOnpB,GAEP,OADA3D,QAAQC,MAAM,UAAW0D,GAClB6X,QAAQC,OAAO9X,EACxB,CACF,CAvpBA8nB,EAAerB,aAAa/O,QAAQC,KAClC,SAAAC,GAEEA,EAAOnU,IAAMijB,EAAc9O,EAAOnU,KAClCpH,QAAQO,IAAI,mBAAD+H,OAAoBiT,EAAOvT,OAAOsiB,cAAa,KAAAhiB,OAAIiT,EAAOiO,SAAOlhB,OAAGiT,EAAOnU,MAGtF,IACE,IAAMF,EAAUtH,aAAaC,QAAQ,QACrC,GAAIqH,EAAS,CACX,IAAMzH,EAAOC,KAAKC,MAAMuH,GACxB,GAAIzH,EAAM,CAKR,GAHA8b,EAAOtT,QAAQ,iBAAmB,eAAHK,OAAkB7I,EAAKW,UAAYX,EAAKY,IAAM,IAGzEZ,EAAKqL,MAAO,CACd,IAAMiiB,EAAc1f,KAAK5N,EAAKqL,OAC9ByQ,EAAOtT,QAAQ,iBAAmB,GAAHK,OAAMiT,EAAOtT,QAAQ,iBAAgB,KAAAK,OAAIykB,EAC1E,CAEA,GAAIttB,EAAKK,KAAM,CAEb,IAAMwpB,EAAY/N,EAAOnU,IAAIkQ,SAAS,KAAO,IAAM,IACnDiE,EAAOnU,KAAO,GAAJkB,OAAOghB,EAAS,UAAAhhB,OAAS7I,EAAKK,KAC1C,CACF,CACF,CACF,CAAE,MAAO6D,GACP3D,QAAQC,MAAM,wBAAyB0D,EACzC,CAGA,OADA3D,QAAQO,IAAI,OAAQgb,EAAOtT,SACpBsT,CACT,IACA,SAAAtb,GAEE,OADAD,QAAQC,MAAM,oBAAqBA,GAC5Bub,QAAQC,OAAOxb,EACxB,IAGFwrB,EAAerB,aAAa1oB,SAAS4Z,KACnC,SAAA5Z,GAGE,OAFA1B,QAAQO,IAAI,qBAAD+H,OAAsB5G,EAASC,SAC1C3B,QAAQO,IAAI,QAASmB,EAAS5C,MACvB4C,CACT,IACA,SAAAzB,GAEE,OADAD,QAAQC,MAAM,oBAAqBA,EAAMyB,UAAYzB,GAC9Cub,QAAQC,OAAOxb,EACxB,IAIFurB,EAAcpB,aAAa/O,QAAQC,KACjC,SAAAC,GAEEA,EAAOnU,IAAMijB,EAAc9O,EAAOnU,KAClCpH,QAAQO,IAAI,aAAD+H,OAAciT,EAAOvT,OAAOsiB,cAAa,KAAAhiB,OAAIiT,EAAOnU,MAG/D,IACE,IAAMF,EAAUtH,aAAaC,QAAQ,QACrC,GAAIqH,EAAS,CACX,IAAMzH,EAAOC,KAAKC,MAAMuH,GAC5B,GAAIzH,IAEE8b,EAAOtT,QAAQ,iBAAmB,eAAHK,OAAkB7I,EAAKW,UAAYX,EAAKY,IACnEZ,EAAKqL,OAAO,CAEd,IAAMiiB,EAAc1f,KAAK5N,EAAKqL,OAC9ByQ,EAAOtT,QAAQ,iBAAmB,GAAHK,OAAMiT,EAAOtT,QAAQ,iBAAgB,KAAAK,OAAIykB,EAC1E,CAEJ,CACF,CAAE,MAAOppB,GACP3D,QAAQC,MAAM,sBAAuB0D,EACvC,CAEA,OAAO4X,CACT,IACA,SAAAtb,GAEE,OADAD,QAAQC,MAAM,0BAA2BA,GAClCub,QAAQC,OAAOxb,EACxB,IAGFurB,EAAcpB,aAAa1oB,SAAS4Z,KAClC,SAAA5Z,GAEE,OADA1B,QAAQO,IAAI,uBAAD+H,OAAwB5G,EAASC,SACrCD,CACT,IACA,SAAAzB,GAME,OALIA,EAAMyB,SACR1B,QAAQC,MAAM,oBAADqI,OAAqBrI,EAAMyB,SAASC,QAAU1B,EAAMyB,SAAS5C,MAE1EkB,QAAQC,MAAM,2BAA4BA,EAAMwD,SAE3C+X,QAAQC,OAAOxb,EACxB,IAIFiqB,EAAUE,aAAa1oB,SAAS4Z,KAC9B,SAAA5Z,GACE1B,QAAQO,IAAI,mBAAD+H,OAAoB5G,EAASC,SAExC,IACE,IAAMyF,EAAM1F,EAAS6Z,OAAOnU,IAG5B,GAAIA,EAAIkQ,SAAS,YAAclQ,EAAIkQ,SAAS,YAAa,CAOvD,GANAtX,QAAQO,IAAI,4BAADyO,EAAAA,EAAAA,GACFtN,EAAS5C,MAChB+P,MAAMC,QAAQpN,EAAS5C,MAAQ,MAAQ,OACvC4C,EAAS5C,KAAO,KAAO,MAGrB4C,EAAS5C,OAAS+P,MAAMC,QAAQpN,EAAS5C,MAI3C,GAHAkB,QAAQmL,KAAK,mCAGgB,YAAzB6D,EAAAA,EAAAA,GAAOtN,EAAS5C,MAAmB,CAErC,IAAMmuB,EAAiBvrB,EAAS5C,KAAKouB,SAAWxrB,EAAS5C,KAAKA,MAAQ4C,EAAS5C,KAAKmQ,SAAWvN,EAAS5C,KAAKyC,MAC7G,GAAIsN,MAAMC,QAAQme,GAChBjtB,QAAQO,IAAI,2BACZmB,EAAS5C,KAAOmuB,OACX,GAAIvrB,EAAS5C,KAAKmQ,SAA4C,YAAjCD,EAAAA,EAAAA,GAAOtN,EAAS5C,KAAKmQ,SAAsB,CAE7E,IAAMke,EAAczrB,EAAS5C,KAAKmQ,QAAQie,SAAWxrB,EAAS5C,KAAKmQ,QAAQ1N,MACvEsN,MAAMC,QAAQqe,IAChBntB,QAAQO,IAAI,6BACZmB,EAAS5C,KAAOquB,IAGhBntB,QAAQO,IAAI,wBACZmB,EAAS5C,KAAO,CAAC4C,EAAS5C,MAE9B,KAAO,CAEL,IAAMsuB,EAAgB1rB,EAAS5C,KAAKuB,KAAOqB,EAAS5C,KAAK3E,MAAQuH,EAAS5C,KAAKgM,OAC/E,GAAIsiB,EACFptB,QAAQO,IAAI,2BACZmB,EAAS5C,KAAO,CAAC4C,EAAS5C,UACrB,CAEL,IAAMuuB,EAAkBtzB,OAAOoV,OAAOzN,EAAS5C,MAAM2P,QAAO,SAAAtG,GAAC,MAC9C,YAAb6G,EAAAA,EAAAA,GAAO7G,IAAwB,OAANA,IAAeA,EAAE9H,IAAM8H,EAAElB,QAAUkB,EAAEmlB,QAAQ,IAEpED,EAAgBzqB,OAAS,GAC3B5C,QAAQO,IAAI,0BACZmB,EAAS5C,KAAOuuB,IAGhBrtB,QAAQO,IAAI,wBACZmB,EAAS5C,KAAO,CAAC4C,EAAS5C,MAE9B,CACF,CACF,MAEEkB,QAAQmL,KAAK,gCACbzJ,EAAS5C,KAAO,GAKf4C,EAAS5C,OACZkB,QAAQmL,KAAK,oCACbzJ,EAAS5C,KAAO,IAId+P,MAAMC,QAAQpN,EAAS5C,QACzB4C,EAAS5C,KAAO4C,EAAS5C,KAAKyU,KAAI,SAAAga,GAahC,OAXKA,EAAOltB,IAAMktB,EAAOD,UACvBC,EAAOltB,GAAKktB,EAAOD,SAGhBC,EAAOpzB,OACNozB,EAAOrvB,SACTqvB,EAAOpzB,KAAOozB,EAAOrvB,SACZqvB,EAAO9tB,MAAQ8tB,EAAO9tB,KAAKtF,OACpCozB,EAAOpzB,KAAOozB,EAAO9tB,KAAKtF,OAGvBozB,CACT,KAGFvtB,QAAQO,IAAI,+BACVsO,MAAMC,QAAQpN,EAAS5C,MAAQ4C,EAAS5C,KAAK8D,OAAS,EAC1D,CAgBA,GAbIwE,EAAIkQ,SAAS,YACftX,QAAQO,IAAI,4BAADyO,EAAAA,EAAAA,GACFtN,EAAS5C,MAChB+P,MAAMC,QAAQpN,EAAS5C,MAAQ,MAAQ,OACvC4C,EAAS5C,KAAO,KAAO,MAGrB4C,EAAS5C,MAAiC,YAAzBkQ,EAAAA,EAAAA,GAAOtN,EAAS5C,QAAsB+P,MAAMC,QAAQpN,EAAS5C,OAChFkB,QAAQO,IAAI,mBAAoBxG,OAAOsZ,KAAK3R,EAAS5C,QAKrDsI,EAAIkQ,SAAS,aAAelQ,EAAIkQ,SAAS,eAAgB,CAa3D,GAZAtX,QAAQO,IAAI,0BAADyO,EAAAA,EAAAA,GACFtN,EAAS5C,MAChB+P,MAAMC,QAAQpN,EAAS5C,MAAQ,OAAHwJ,OAAU5G,EAAS5C,KAAK8D,OAAM,KAAM,OAChElB,EAAS5C,KAAO,KAAO,MAGpB4C,EAAS5C,OAASsI,EAAIkQ,SAAS,eAAgBlQ,EAAIkQ,SAAS,iBAC/DtX,QAAQmL,KAAK,iCACbzJ,EAAS5C,KAAO,IAId4C,EAAS5C,OAAS+P,MAAMC,QAAQpN,EAAS5C,QACzCsI,EAAIkQ,SAAS,cAAgBlQ,EAAIkQ,SAAS,kBAC5CtX,QAAQmL,KAAK,gCACgB,YAAzB6D,EAAAA,EAAAA,GAAOtN,EAAS5C,OAAmB,CAErC,IAAMmuB,EAAiBvrB,EAAS5C,KAAKmQ,SAAWvN,EAAS5C,KAAKyO,QACxC7L,EAAS5C,KAAKA,MAAQ4C,EAAS5C,KAAK0uB,SACpC9rB,EAAS5C,KAAKoQ,OAASxN,EAAS5C,KAAK2uB,KAE3D,GAAI5e,MAAMC,QAAQme,GAChBjtB,QAAQO,IAAI,6BACZmB,EAAS5C,KAAOmuB,OACX,GAAIvrB,EAAS5C,KAAKqnB,MAAsC,YAA9BnX,EAAAA,EAAAA,GAAOtN,EAAS5C,KAAKqnB,MAAmB,CAEvE,IAAMuH,EAAahsB,EAAS5C,KAAKqnB,KAAKlX,SAAWvN,EAAS5C,KAAKqnB,KAAKrnB,MAAQ4C,EAAS5C,KAAKqnB,KAAKsH,KAC3F5e,MAAMC,QAAQ4e,IAChB1tB,QAAQO,IAAI,4BACZmB,EAAS5C,KAAO4uB,IAEhB1tB,QAAQO,IAAI,2BACZmB,EAAS5C,KAAO,CAAC4C,EAAS5C,MAE9B,MAAO,GAAI/E,OAAOsZ,KAAK3R,EAAS5C,MAAM8D,OAAS,IACrClB,EAAS5C,KAAKuB,IAAMqB,EAAS5C,KAAK0P,SAAW9M,EAAS5C,KAAK6uB,aAEnE3tB,QAAQO,IAAI,+BACZmB,EAAS5C,KAAO,CAAC4C,EAAS5C,UACrB,CAEL,IAAM8uB,EAAmB7zB,OAAOoV,OAAOzN,EAAS5C,MAAM2P,QAAO,SAAAtG,GAAC,OAC5D0G,MAAMC,QAAQ3G,IAAMA,EAAEvF,OAAS,GACf,YAAhBoM,EAAAA,EAAAA,GAAO7G,EAAE,MAAoBA,EAAE,GAAG9H,IAAM8H,EAAE,GAAGqG,SAAWrG,EAAE,GAAGwlB,YAAY,IAGvEC,EAAiBhrB,OAAS,GAC5B5C,QAAQO,IAAI,2BACZmB,EAAS5C,KAAO8uB,EAAiB,KAEjC5tB,QAAQO,IAAI,uBACZmB,EAAS5C,KAAO,CAAC4C,EAAS5C,MAE9B,CACF,CAIE+P,MAAMC,QAAQpN,EAAS5C,QACvBsI,EAAIkQ,SAAS,cAAgBlQ,EAAIkQ,SAAS,kBAC5C5V,EAAS5C,KAAO4C,EAAS5C,KAAKyU,KAAI,SAAAxF,GAkBhC,OAhBKA,EAAM1N,IAAM0N,EAAMS,QACrBT,EAAM1N,GAAK0N,EAAMS,SACPT,EAAM1N,IAAM0N,EAAM4f,cAC5B5f,EAAM1N,GAAK0N,EAAM4f,cAId5f,EAAMpM,QAAUoM,EAAM8f,cACzB9f,EAAMpM,OAASoM,EAAM8f,cAIlB9f,EAAMgI,OAAShI,EAAM5T,OACxB4T,EAAMgI,MAAQhI,EAAM5T,MAGf4T,CACT,IAEA/N,QAAQO,IAAI,2BAAD+H,OAA4B5G,EAAS5C,KAAK8D,SAEzD,CACF,CAAE,MAAOe,GACP3D,QAAQC,MAAM,oBAAqB0D,EACrC,CACA,OAAOjC,CACT,IACA,SAAAzB,GACE,GAAIA,EAAMyB,UAIR,GAHA1B,QAAQC,MAAM,gBAADqI,OAAiBrI,EAAMyB,SAASC,QAAU1B,EAAMyB,SAAS5C,MAGxC,MAA1BmB,EAAMyB,SAASC,OAAgB,CACjC3B,QAAQmL,KAAK,gBAGb,IAAM2iB,EAA2B7tB,EAAMsb,OAAOnU,MAC5CnH,EAAMsb,OAAOnU,IAAIkQ,SAAS,uBAC1BrX,EAAMsb,OAAOnU,IAAIkQ,SAAS,YAAcrX,EAAMsb,OAAOnU,IAAIkQ,SAAS,kBAI9DyW,EAA6B9tB,EAAMsb,OAAOnU,MAC9CnH,EAAMsb,OAAOnU,IAAIkQ,SAAS,iBAC1BrX,EAAMsb,OAAOnU,IAAIkQ,SAAS,UAC1BrX,EAAMsb,OAAOnU,IAAIkQ,SAAS,4BAC1BrX,EAAMsb,OAAOnU,IAAIkQ,SAAS,aAC1BrX,EAAMsb,OAAOnU,IAAIkQ,SAAS,qBAItB0W,EAAqBD,GACD10B,OAAO4I,SAASgsB,SAAS3W,SAAS,iBAClCje,OAAO4I,SAASgsB,SAAS3W,SAAS,2BAClCje,OAAO4I,SAASgsB,SAAS3W,SAAS,gBAClCnV,eAAetC,QAAQ,yBAG3CqX,EAAwB/U,eAAetC,QAAQ,yBAC/CquB,EAAgBtuB,aAAaC,QAAQ,iBAGrCsuB,EAAa90B,OAAO4I,SAASgsB,SAAS3W,SAAS,UAG/C8W,EAAwBnuB,EAAMsb,OAAOnU,MACzCnH,EAAMsb,OAAOnU,IAAIkQ,SAAS,sBAC1BrX,EAAMsb,OAAOnU,IAAIkQ,SAAS,wBAItB+W,EAAah1B,OAAO4I,SAASgsB,SAAS3W,SAAS,UAGrD,GAAIwW,GAA4BK,GAAcC,GAAyBC,EAAY,CACjFruB,QAAQO,IAAI,2BAGZ,IAAM+tB,EAAmB5C,IAGzB,GAAI0C,EAAuB,CACzBpuB,QAAQO,IAAI,qBACZ,IAAMguB,EAAgB,CACpB5sB,OAAQ,IACR6sB,WAAY,YACZ1vB,KAAM,IAER,OAAO0c,QAAQiT,QAAQF,EACzB,CAEA,OAAID,EAEKzB,EAA6B5sB,EAAMsb,QAIrCC,QAAQC,OAAO,CACpBiT,eAAe,EACfC,cAAc,EACdlrB,QAAS,mBAEb,CAGA,GAAIyT,GAAyBgX,GAAiBF,EAAoB,CAChEhuB,QAAQO,IAAI,6BAGRlH,OAAOkK,UACTlK,OAAOkK,SAASG,QAAQ,qBAI1B,IAAMkrB,EAAgBzsB,eAAetC,QAAQ,iBAC7C,GAAI+uB,EAAe,CACjB5uB,QAAQO,IAAI,mBACZX,aAAac,QAAQ,OAAQkuB,GAG7B,IAAMrT,EAAStb,EAAMsb,OAErBA,EAAOyR,QAAS,EAGhB,IAAMvtB,EAAOC,KAAKC,MAAMivB,GACxB,GAAInvB,IACF8b,EAAOtT,QAAQ,iBAAmB,eAAHK,OAAkB7I,EAAKW,UAAYX,EAAKY,IACnEZ,EAAKqL,OAAO,CACd,IAAMiiB,EAAc1f,KAAK5N,EAAKqL,OAC9ByQ,EAAOtT,QAAQ,iBAAmB,GAAHK,OAAMiT,EAAOtT,QAAQ,iBAAgB,KAAAK,OAAIykB,EAC1E,CAGF,OAAO7C,EAAU3O,EACnB,CAGA,OAAOC,QAAQC,OAAO,IAAIpT,MAAM,mBAClC,CAOA,GAHAzI,aAAawC,WAAW,QAGS,WAA7B/I,OAAO4I,SAASgsB,SAAuB,CAEzC,IAAMY,EAAcx1B,OAAO4I,SAASgsB,SAAW50B,OAAO4I,SAASokB,OAC/DlkB,eAAezB,QAAQ,qBAAsBmuB,GAE7Cx1B,OAAO4I,SAASC,KAAO,QACzB,CACF,OACK,GAAIjC,EAAMob,QAAS,KAAAyT,EAExB9uB,QAAQC,MAAM,cAAeA,EAAMwD,SAGnC,IAAM2D,GAAkB,QAAZ0nB,EAAA7uB,EAAMsb,cAAM,IAAAuT,OAAA,EAAZA,EAAc1nB,MAAO,GACjC,GAAIA,EAAIkQ,SAAS,sBAAwBlQ,EAAIkQ,SAAS,wBAClDlQ,EAAIkQ,SAAS,YAAclQ,EAAIkQ,SAAS,iBAAkB,CAC5DtX,QAAQO,IAAI,4BACZ,IAAMguB,EAAgB,CACpB5sB,OAAQ,IACR6sB,WAAY,iCACZ1vB,KAAM,IAER,OAAO0c,QAAQiT,QAAQF,EACzB,CACF,MACEvuB,QAAQC,MAAM,gBAAiBA,EAAMwD,SAEvC,OAAO+X,QAAQC,OAAOxb,EACxB,IAyHoB,qBAAX5G,QACTA,OAAO8M,iBAAiB,oBAAoB,WAC1C2lB,GACF,IA+FF,IAAMiD,EAAiB,SAAC9nB,GAEtB,OAAIA,GAA4B,YAAlB+H,EAAAA,EAAAA,GAAO/H,GACZA,EAAO7G,UAAY6G,EAAO5G,IAAM,KAIlC4G,CACT,EAuBA,IAAM3F,EAAM,CAEVuJ,KAAM,CAEJL,MAAK,SAACtC,GACJ,OAAOgiB,EAAU8E,KAAK,sBAAuB9mB,EAC/C,EAEAsD,SAAQ,SAACE,GACP,OAAOwe,EAAU8E,KAAK,SAAUtjB,EAClC,GAIFuF,MAAO,CAELY,aAAY,SAAC5K,GAKX,GAHAjH,QAAQO,IAAI,oCAAqC0G,EAAQ,OAAF+H,EAAAA,EAAAA,GAAgB/H,KAGlEA,EAAQ,CACX,IAAMC,EAAUtH,aAAaC,QAAQ,QACrC,GAAIqH,EAAS,CACT,IAAMzH,EAAOC,KAAKC,MAAMuH,GAExBD,EAASxH,EAAKY,GACdL,QAAQO,IAAI,wBAAyB0G,EACzC,CACF,CAGA,IAAKA,EAEH,OADAjH,QAAQC,MAAM,oBACPub,QAAQC,OAAO,IAAIpT,MAAM,aAIlC,IAAM8Q,EAAYxS,KAAKoS,MACjB4E,EAASD,KAAKC,SAGhBvW,EAAM,uBAAHkB,OAA0BrB,EAAM,OAAAqB,OAAM6Q,EAAS,OAAA7Q,OAAMqV,EAAM,0CAElE3d,QAAQO,IAAI,QAAD+H,OAASrB,EAAM,sBAAAqB,OAAqB4hB,EAAU0C,SAASpD,SAAOlhB,OAAGlB,IAG5E,IAAMmU,EAAS,CACbtT,QAAS,CACP,YAAahB,GAEfmU,QAAS,KACTF,iBAAiB,GAInB,OAAOgP,EAAU3M,IAAInW,EAAKmU,GAAO,UACxB,SAAAtb,GAWL,OAVAD,QAAQC,MAAM,QAADqI,OAASrB,EAAM,gBAAgBhH,EAAMwD,SAAWxD,GAEzDA,EAAMyB,UACR1B,QAAQC,MAAM,YAAaA,EAAMyB,SAASC,QAC1C3B,QAAQC,MAAM,QAASA,EAAMyB,SAAS5C,OAC7BmB,EAAMob,SACfrb,QAAQC,MAAM,uBAIT,CACLnB,KAAM,CACJ+F,WAAY,EACZqM,WAAY,EACZlM,cAAe,EACfG,eAAgB,EAChBG,cAAe,EACfK,cAAe,EACfuT,WAAY,iBACZjS,OAAQA,EACRhH,MAAOA,EAAMwD,SAAW,YAG9B,GACJ,GAIFlC,OAAK0tB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CAEHle,eAAc,WACZ,OAAOmZ,EAAU3M,IAAI,YACvB,EAGA2R,cAAa,SAACC,GACZ,OAAOjF,EAAUkF,IAAI,iBAAkBD,EACzC,EAGAE,eAAc,SAACC,GACb,OAAOpF,EAAUkF,IAAI,kBAAmBE,EAC1C,EAGAC,aAAY,SAACtoB,EAAQgL,GACnB,OAAOiY,EAAU8E,KAAK,UAAD1mB,OAAWrB,EAAM,WAAWgL,EAAU,CACzDhK,QAAS,CACP,eAAgB,wBAGtB,EAGAzG,6BAA4B,WAC1B,OAAO0oB,EAAU3M,IAAI,4BACvB,EAEAja,iBAAgB,SAACksB,GACf,OAAOtF,EAAU8E,KAAK,yBAA0B,CAAEQ,OAAAA,GACpD,EAEAtiB,OAAM,WACJ,OAAOgd,EAAU3M,IAAI,SACvB,EAEArU,QAAO,SAAC7I,GACN,OAAO6pB,EAAU3M,IAAI,UAADjV,OAAWjI,GACjC,EAEAovB,WAAU,SAAC/jB,GACT,OAAOwe,EAAU8E,KAAK,SAAUtjB,EAClC,EAEAkB,WAAU,SAACvM,EAAIqL,GACb,OAAOwe,EAAUkF,IAAI,UAAD9mB,OAAWjI,GAAMqL,EACvC,EAEAgkB,WAAU,SAACrvB,GACT,OAAO6pB,EAAS,UAAQ,UAAD5hB,OAAWjI,GACpC,EAEAsvB,cAAa,SAAC1oB,EAAQ2oB,GACpB,OAAO1F,EAAU8E,KAAK,UAAD1mB,OAAWrB,EAAM,mBAAmB,CAAE2oB,YAAAA,GAC7D,EAEAC,gBAAe,WACb,OAAO3F,EAAU3M,IAAI,mBACvB,EAEAuS,+BAA8B,WAC5B,OAAO5F,EAAU3M,IAAI,iCACvB,EAEAwS,2BAA0B,SAACC,EAAelxB,GACxC,OAAOorB,EAAUkF,IAAI,0BAAD9mB,OAA2B0nB,GAAiBlxB,EAClE,EAEAmxB,oBAAmB,WACjB,OAAO/F,EAAU3M,IAAI,sBACvB,EAEA2S,uBAAsB,SAACplB,GACrB,OAAOof,EAAU8E,KAAK,yBAA0B,CAAElkB,MAAAA,GACpD,EAEAqlB,gBAAe,SAACrlB,EAAOslB,GACrB,OAAOlG,EAAU8E,KAAK,2BAA4B,CAAElkB,MAAAA,EAAOslB,KAAAA,GAC7D,GAAC,0BAEatlB,EAAOslB,EAAMR,GACzB,OAAO1F,EAAU8E,KAAK,wBAAyB,CAAElkB,MAAAA,EAAOslB,KAAAA,EAAMR,YAAAA,GAChE,IAAC,0BAGa3oB,GACZ,OAAOijB,EAAU3M,IAAI,UAADjV,OAAWrB,EAAM,WACvC,IAIFoN,MAAO,CAELgc,OAAM,SAACC,GACL,OAAOpG,EAAU8E,KAAK,SAAUsB,EAClC,EAEApjB,OAAM,WACJ,OAAOgd,EAAU3M,IAAI,SACvB,EAEAgT,OAAM,SAAClwB,GACL,OAAO6pB,EAAU3M,IAAI,UAADjV,OAAWjI,GACjC,EAEAmwB,SAAQ,SAACpc,EAAQnN,GACf,OAAOijB,EAAU8E,KAAK,UAAD1mB,OAAW8L,EAAM,YAAY,CAAEnN,OAAAA,GACtD,EAEAwpB,gBAAe,SAACrc,EAAQob,GAEtB,IAAM/vB,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDoH,EAASxH,EAAKW,UAAYX,EAAKY,GAKrC,GAHAL,QAAQO,IAAI,iBAAkB0G,EAAQ,OAAK+H,EAAAA,EAAAA,GAAS/H,KAG/CA,EAGH,OAFAjH,QAAQC,MAAM,6BAEPub,QAAQC,OAAO,IAAIpT,MAAM,mBAIlC,IAAMqoB,EAAc,CAClBtc,OAAQ5L,SAAS4L,EAAQ,KAAOA,EAChCob,OAAQA,EACR7tB,OAAQ,WAMV,OAHA3B,QAAQO,IAAI,YAAab,KAAK8Q,UAAUkgB,IAGjCxG,EAAU8E,KAAK,qBAAsB0B,EAC9C,EAEApc,oBAAmB,SAACF,EAAQzS,GAC1B,IAAIyF,EAAM,2BAAHkB,OAA8B8L,GAIrC,OAHIzS,IACFyF,GAAO,WAAJkB,OAAe3G,IAEbuoB,EAAU3M,IAAInW,EACvB,EAEAupB,uBAAsB,SAACX,EAAexb,EAAQgb,GAC5C,OAAOtF,EAAUkF,IAAI,sBAAD9mB,OAAuB0nB,GAAiB,CAC1Dxb,OAAQA,EACRgb,OAAQA,GAAU,IAEtB,EAEAoB,oBAAmB,SAAC3pB,GAClB,IAAM4pB,EAAU9B,EAAe9nB,GAC/B,OAAOijB,EAAU3M,IAAI,2BAADjV,OAA4BuoB,GAClD,EAEAC,eAAc,SAAC1c,GACb,OAAO8V,EAAU3M,IAAI,UAADjV,OAAW8L,EAAM,YACvC,EAEA2c,mBAAkB,SAAC3c,GACjB,OAAO8V,EAAU3M,IAAI,UAADjV,OAAW8L,EAAM,yBACvC,EAEA4c,oBAAmB,SAACC,GAClB,OAAO/G,EAAU3M,IAAI,gBAADjV,OAAiB2oB,GACvC,EAEAC,iBAAgB,SAAC9c,EAAQnN,GACvB,OAAOijB,EAAS,UAAQ,UAAD5hB,OAAW8L,EAAM,aAAA9L,OAAYrB,GACtD,EAEAkqB,gBAAe,SAAClqB,EAAQmN,GACtB,OAAO8V,EAAU3M,IAAI,UAADjV,OAAW8L,EAAM,mBAAA9L,OAAkBrB,IAAS,UACvD,SAAAhH,GAEL,GAAIA,EAAMyB,SACR,MAAO,CAAE5C,KAAM,CAAEsyB,UAAU,IAE7B,MAAMnxB,CACR,GACJ,EACAoxB,WAAU,SAACjd,GACT,IAAMlN,EAAUtH,aAAaC,QAAQ,QAC/BJ,EAAOyH,EAAUxH,KAAKC,MAAMuH,GAAW,KACvCe,EAAU,CAAC,EAIjB,OAHIxI,GAAQA,EAAKY,KACf4H,EAAQ,aAAexI,EAAKY,IAEvB6pB,EAAS,UAAQ,UAAD5hB,OAAW8L,GAAU,CAAEnM,QAAAA,GAChD,EACAqpB,kBAAiB,SAACld,EAAQmd,GACxB,IAAMrqB,EAAUtH,aAAaC,QAAQ,QAC/BJ,EAAOyH,EAAUxH,KAAKC,MAAMuH,GAAW,KACvCe,EAAU,CAAC,EAIjB,OAHIxI,GAAQA,EAAKY,KACf4H,EAAQ,aAAexI,EAAKY,IAEvB6pB,EAAU8E,KAAK,UAAD1mB,OAAW8L,EAAM,uBAAuB,CAAEmd,WAAAA,GAAc,CAAEtpB,QAAAA,GACjF,GAIFupB,SAAU,CAERC,yBAAwB,WACtB,OAAOvH,EAAU3M,IAAI,mCACvB,GAIFhQ,QAAMmkB,EAAA,CAEJxkB,OAAM,WACJ,OAAOgd,EAAU3M,IAAI,UACvB,EAEAgT,OAAM,SAAClwB,GACL,OAAO6pB,EAAU3M,IAAI,WAADjV,OAAYjI,GAClC,EAEAsxB,OAAM,SAACC,GAEL,IAAM3f,EAAW,IAAI4f,SACrB5f,EAAS6f,OAAO,OAAQF,GAGxB,IAAMnyB,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACpDJ,EAAKY,IACP4R,EAAS6f,OAAO,UAAWryB,EAAKY,IAI9BuxB,EAAKz3B,MACP8X,EAAS6f,OAAO,OAAQF,EAAKz3B,MAI/B,IAAM43B,EAAkB,KAGxB/xB,QAAQO,IAAI,WAAY,CACtByxB,IAAKJ,EAAKz3B,KACV83B,KAAML,EAAK5zB,KACXk0B,KAAMN,EAAKh1B,KACXu1B,KAAM1yB,EAAKY,KAIb,IAAMkb,EAAS,CACbH,QAAS2W,GAGX,OAAOvG,EAAcwD,KAAK,iBAAkB/c,EAAUsJ,EACxD,EAEA6W,WAAU,SAACngB,GACT,OAAOuZ,EAAcwD,KAAK,uBAAwB/c,EACpD,EAGAogB,mBAAkB,SAAC7jB,GAAoC,IAA3B8jB,EAAkBrU,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,KAE/C,GADAje,QAAQO,IAAI,sBAAD+H,OAAuBkG,KAC7BA,EAEH,OADAxO,QAAQC,MAAM,0BACPub,QAAQC,OAAO,IAAIpT,MAAM,WAIlC,GAAIiqB,EAAoB,CACtBtyB,QAAQO,IAAI,0BACZ,IAAM0R,EAAW,IAAI4f,SAGrB,OAFA5f,EAAS6f,OAAO,OAAQQ,GAEjB9G,EAAcwD,KAAK,WAAD1mB,OAAYkG,EAAO,aAAayD,EAAU,CACjEhK,QAAS,CACP,eAAgB,yBAElB,UAAO,SAAAhI,GAEP,OADAD,QAAQC,MAAM,sBAAuBA,GAC9Bub,QAAQC,OAAOxb,EACxB,GACF,CAIA,IAAMkZ,EAAYxS,KAAKoS,MACjB9I,EAAS,CACb0d,YAAanf,EACb+X,EAAGpN,GAML,OAHAnZ,QAAQO,IAAI,sBAAuB0P,GAG5BuL,QAAQgX,IAAI,CAEjBtI,EAAU3M,IAAI,kCAADjV,OAAmCkG,GAAW,CACzDyB,QAAMhR,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDgR,GAAM,IACTwiB,KAAM,SAERrX,QAAS,MACR3Z,MAAK,SAAAC,GAEN,OADA1B,QAAQO,IAAI,oCAAqCmB,EAAS5C,MACnD4C,CACT,IAAE,UAAO,SAAAzB,GAEP,OADAD,QAAQC,MAAM,oCAAqCA,GAC5Cub,QAAQC,OAAOxb,EACxB,IAGAiqB,EAAU3M,IAAI,qCAADjV,OAAsCkG,GAAW,CAC5DyB,OAAAA,EACAmL,QAAS,MACR3Z,MAAK,SAAAC,GAEN,OADA1B,QAAQO,IAAI,uCAAwCmB,EAAS5C,MACtD4C,CACT,IAAE,UAAO,SAAAzB,GAEP,OADAD,QAAQC,MAAM,uCAAwCA,GAC/Cub,QAAQC,OAAOxb,EACxB,IAGAiqB,EAAU8E,KAAK,6BAA8B,CAC3CrB,YAAanf,EACb2K,UAAWA,GACV,CACDiC,QAAS,MACR3Z,MAAK,SAAAC,GAEN,OADA1B,QAAQO,IAAI,kBAAmBmB,EAAS5C,MACjC4C,CACT,IAAE,UAAO,SAAAzB,GAEP,OADAD,QAAQC,MAAM,kBAAmBA,GAC1Bub,QAAQC,OAAOxb,EACxB,MACA,UAAO,SAAAyyB,GAGP,OAFA1yB,QAAQC,MAAM,qBAAsByyB,GAE7B,CACL5zB,KAAM,CACJ0E,SAAS,EACTC,QAAS,eACTxD,MAAOyyB,EAAOjvB,SAAW,cAG/B,GACF,EAEA,gBACOpD,GAEL,OADAL,QAAQO,IAAI,WAAD+H,OAAYjI,IAChB6pB,EAAS,UAAQ,yBAAD5hB,OAA0BjI,GACnD,EAGAsyB,OAAM,SAACtyB,EAAIuyB,GACT,OAAO1I,EAAUkF,IAAI,WAAD9mB,OAAYjI,GAAMuyB,EACxC,EAGAC,gBAAe,SAACxyB,GACd,OAAO6pB,EAAU8E,KAAK,WAAD1mB,OAAYjI,EAAE,WACrC,EAGAyyB,YAAW,SAACzyB,EAAI0yB,EAAUjjB,GACxB,OAAOoa,EAAU8E,KAAK,WAAD1mB,OAAYjI,EAAE,WAAW,CAAE0yB,SAAAA,EAAUjjB,YAAAA,GAC5D,IAACmf,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAAyC,EAAA,+BAGkBljB,GAEjB,GADAxO,QAAQO,IAAI,sBAAD+H,OAAuBkG,KAC7BA,EAEH,OADAxO,QAAQC,MAAM,0BACPub,QAAQC,OAAO,IAAIpT,MAAM,WAIlC,IAAM8Q,EAAYxS,KAAKoS,MACjB9I,EAAS,CACb0d,YAAanf,EACb+X,GAMF,OAHAvmB,QAAQO,IAAI,sBAAuB0P,GAG5Bia,EAAU3M,IAAI,kCAADjV,OAAmCkG,GAAW,CAChEyB,QAAMhR,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDgR,GAAM,IACTwiB,KAAM,WAER,UAAO,SAAAxyB,GAKP,OAJAD,QAAQC,MAAM,sCAAuCA,GAGrDD,QAAQO,IAAI,4CACL2pB,EAAU3M,IAAI,qCAADjV,OAAsCkG,GAAW,CAAEyB,OAAAA,GACzE,GACF,IAAC,iCAGoBzQ,GACnB,OAAO0qB,EAAS,UAAQ,oBAAqB,CAAEja,OAAQ,CAAEzQ,KAAAA,IAC3D,IAAC,4BAGiD,IAE5CyH,EAFQtF,EAAMsc,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,KAAM+U,EAAY/U,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,KAG1C,GAAI+U,EACF/rB,EAAS+rB,EACThzB,QAAQO,IAAI,4BAA6B0G,OACpC,CAEL,IAAMxH,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACxDoH,EAASxH,EAAKW,UAAYX,EAAKY,GAC/BL,QAAQO,IAAI,sCAAuC0G,EACrD,CAGA,IAAIgJ,EAAS,CAAEhJ,OAAAA,GACXtF,IACFsO,EAAOtO,OAASA,GAIlB3B,QAAQO,IAAI,eAAgB0G,EAAQ,MAAOtF,GAAU,MAIrD,IAAMsxB,EAAY,gCAGlBjzB,QAAQO,IAAI,uBAAwB0yB,EAAW,MAAOhjB,GAEtD,IAEE,OAAOtB,IAAAA,IAAUskB,EAAW,CAAEhjB,OAAAA,GAChC,CAAE,MAAOhQ,GAEP,OADAD,QAAQC,MAAM,YAAaA,GACpBub,QAAQC,OAAOxb,EACxB,CACF,IAAC,4BAGkC,IAE7BgH,EAFQ+rB,EAAY/U,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,KAG3B,GAAI+U,EACF/rB,EAAS+rB,EACThzB,QAAQO,IAAI,4BAA6B0G,OACpC,CAEL,IAAMxH,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACxDoH,EAASxH,EAAKW,UAAYX,EAAKY,GAC/BL,QAAQO,IAAI,sCAAuC0G,EACrD,CAGA,IAAIgJ,EAAS,CAAEhJ,OAAAA,GAEfjH,QAAQO,IAAI,eAAgB0G,GAG5B,IAAMgsB,EAAY,kCAGlBjzB,QAAQO,IAAI,uBAAwB0yB,EAAW,MAAOhjB,GAEtD,IAEE,OAAOtB,IAAAA,IAAUskB,EAAW,CAAEhjB,OAAAA,GAChC,CAAE,MAAOhQ,GAEP,OADAD,QAAQC,MAAM,YAAaA,GACpBub,QAAQC,OAAOxb,EACxB,CACF,IAAC,qCAIC,OAAOiqB,EAAU3M,IAAI,yBACvB,IAAC,gCAIC,OAAO2M,EAAU3M,IAAI,mBACvB,IAAC,mCAGsB/O,EAASyD,GAAU,IAAA5Q,EAAA,KACxCrB,QAAQO,IAAI,kBAAmBiO,GAG/B,IAAM6U,EAAcpR,EAASoR,YACzBA,IACFrjB,QAAQO,IAAI,YAAa8iB,EAAYzgB,OAAQ,cACtCqP,EAASoR,aAIlB,IAAMpT,EAAS,IAAI0a,gBAWnB,OAVA5wB,OAAOsZ,KAAKpB,GAAUqB,SAAQ,SAAArX,GAC5B,IAAImH,EAAQ6O,EAAShW,GAEjB4S,MAAMC,QAAQ1L,KAChBA,EAAQA,EAAM8oB,KAAK,MAErBjc,EAAO6hB,OAAO71B,EAAc,MAATmH,EAAgB,GAAKA,EAC1C,IAGO8mB,EAAU8E,KAAK,WAAD1mB,OAAYkG,EAAO,oBAAoByB,EAAQ,CAClEhI,QAAS,CACP,eAAgB,uCAGnBxG,MAAK,SAAAC,GAIJ,GAHA1B,QAAQO,IAAI,YAAamB,EAAS5C,MAG9BukB,GAAeA,EAAYzgB,OAAS,EAAG,CACzC5C,QAAQO,IAAI,eAGZ,IAAMd,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAGlDqzB,EAAqB7P,EAAY9P,KAAI,SAAAsB,GAEzC,IAAMse,EAAU,CACdxF,YAAanlB,SAASgG,EAAS,IAC/B4kB,IAAKve,EAAWue,KAAOve,EAAW1a,KAClCk5B,EAAGxe,EAAWwe,EACdC,EAAGze,EAAWye,EACd34B,MAAOka,EAAWla,MAClB4B,OAAQsY,EAAWtY,OACnBg3B,WAAY9zB,EAAKY,IAAM,GAMzB,OAHAL,QAAQO,IAAI,UAAW4yB,GAGhB9xB,EAAKmyB,QAAQL,EACtB,IAGA,OAAO3X,QAAQiY,IAAIP,GAChBzxB,MAAK,SAAAiyB,GAEJ,OADA1zB,QAAQO,IAAI,YAAamzB,GAClBhyB,CACT,IAAE,UACK,SAAAzB,GAGL,OAFAD,QAAQC,MAAM,YAAaA,GAEpByB,CACT,GACJ,CAEA,OAAOA,CACT,GACF,IAAC,oBAGOyxB,GAGN,OAFAnzB,QAAQO,IAAI,UAAW4yB,GAEhBjJ,EAAU8E,KAAK,QAASmE,EAAS,CACtClrB,QAAS,CACP,eAAgB,qBAGtB,IAAC,qCAGwB5H,EAAI4R,GAE3B,OADAjS,QAAQO,IAAI,uBAAwBF,EAAI,MAAO4R,GACxCiY,EAAUkF,IAAI,WAAD9mB,OAAYjI,EAAE,oBAAoB4R,EACxD,IAAC,4BAGe5R,EAAI8Y,GAClBnZ,QAAQO,IAAI,uBAAwBF,EAAI8Y,EAAY,OAASA,EAAY,IAGzE,IAAMlJ,EAAS,CAAC,EACZkJ,IACFlJ,EAAO0jB,gBAAkBxa,GAI3B,IAAMya,EAAa,kBAAHtrB,OAAqBjI,EAAE,KAAAiI,QAAI,IAAI3B,MAAOqhB,WACtD,GAAI3uB,OAAOw6B,iBAAmBx6B,OAAOw6B,gBAAgBD,GAEnD,OADA5zB,QAAQmL,KAAK,sBACN9R,OAAOw6B,gBAAgBD,GAIhC,IAAMvY,EAAU6O,EAAUkF,IAAI,WAAD9mB,OAAYjI,EAAE,mBAAmB,KAAM,CAAE4P,OAAAA,IACnExO,MAAK,SAAAC,GAKJ,OAHIrI,OAAOw6B,wBACFx6B,OAAOw6B,gBAAgBD,GAEzBlyB,CACT,IAAE,UACK,SAAAzB,GAKL,MAHI5G,OAAOw6B,wBACFx6B,OAAOw6B,gBAAgBD,GAE1B3zB,CACR,IAUF,OAPK5G,OAAOw6B,kBACVx6B,OAAOw6B,gBAAkB,CAAC,GAI5Bx6B,OAAOw6B,gBAAgBD,GAAcvY,EAE9BA,CACT,KAAC4T,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAAyC,EAAA,yBAGYrxB,EAAIsB,GAEf,OADA3B,QAAQO,IAAI,oBAAqBF,EAAI,MAAOsB,GACrCuoB,EAAUkF,IAAI,WAAD9mB,OAAYjI,EAAE,WAAW,KAAM,CAAE4P,OAAQ,CAAEtO,OAAAA,IACjE,IAAC,gCAGgC,IAAfA,EAAMsc,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,KAEnBxe,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDoH,EAASxH,EAAKW,UAAYX,EAAKY,GAErC,IAAK4G,EAEH,OADAjH,QAAQC,MAAM,sBACPub,QAAQC,OAAO,IAAIpT,MAAM,gBAIlC,IAAMyrB,EAAkE,SAA7Cl0B,aAAaC,QAAQ,oBAChD,GAAIi0B,EAAoB,CAEtBl0B,aAAawC,WAAW,oBAGxB,IAAM2xB,EAAcn0B,aAAaC,QAAQ,sBACrCk0B,IAAgBpyB,IAClBA,EAASoyB,EACT/zB,QAAQO,IAAI,YAAaoB,GAE7B,CAGA,IAAIsO,EAAS,CAAEhJ,OAAAA,GACXtF,IACFsO,EAAOtO,OAASA,GAGlB3B,QAAQO,IAAI,iBAAkB0G,EAAQ,MAAOtF,GAAU,MAGvD,IAAMsxB,EAAY,gCAGlBjzB,QAAQO,IAAI,yBAA0B0yB,EAAW,MAAOhjB,GAExD,IAEE,OAAOtB,IAAAA,IAAUskB,EAAW,CAAEhjB,OAAAA,IAC3BxO,MAAK,SAAAC,GAEJ,OADA1B,QAAQO,IAAI,iBAAkBmB,EAAS5C,KAAO4C,EAAS5C,KAAK8D,OAAS,GAC9DlB,CACT,GACJ,CAAE,MAAOzB,GAEP,OADAD,QAAQC,MAAM,cAAeA,GACtBub,QAAQC,OAAOxb,EACxB,CACF,IAAC,+BAEkBuO,GAEjB,OADAxO,QAAQO,IAAI,gBAAD+H,OAAiBkG,IACvBA,EAKE0b,EAAU3M,IAAI,qCAADjV,OAAsCkG,GAAW,CACnEyB,OAAQ,CACN0d,YAAanf,EACb+X,EAAG5f,KAAKoS,SAPHyC,QAAQC,OAAO,IAAIpT,MAAM,UAUpC,IAAC,+BAEkBmG,GAEjB,OADAxO,QAAQO,IAAI,gBAAD+H,OAAiBkG,IACvBA,EAKE0b,EAAU3M,IAAI,kCAADjV,OAAmCkG,GAAW,CAChEyB,OAAQ,CACNsW,EAAG5f,KAAKoS,MACR0Z,KAAM,UAER,UAAO,SAAAxyB,GAGP,OAFAD,QAAQC,MAAM,YAAaA,GAEpBiqB,EAAU3M,IAAI,8BAADjV,OAA+BkG,GAAW,CAC5DyB,OAAQ,CACN0d,YAAanf,EACb+X,EAAG5f,KAAKoS,QAGd,IAlBSyC,QAAQC,OAAO,IAAIpT,MAAM,UAmBpC,KAIF2rB,KAAM,CAEJC,aAAY,SAACzlB,GAAwB,IAAfikB,EAAIxU,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,OAE3B,OADAje,QAAQO,IAAI,cAAD+H,OAAekG,EAAO,UAAAlG,OAASmqB,IACnCvI,EAAU3M,IAAI,eAADjV,OAAgBkG,GAAW,CAAEyB,OAAQ,CAAEwiB,KAAAA,IAC7D,EAGAyB,QAAO,SAAC7zB,GACN,OAAO6pB,EAAU3M,IAAI,SAADjV,OAAUjI,GAChC,EAGAgwB,OAAM,SAAC8C,GACLnzB,QAAQO,IAAI,SAAU4yB,GAEtB,IAAMgB,GAASl1B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACVk0B,GAAO,IACVxF,YAAanlB,SAAS2qB,EAAQxF,YAAa,IAC3C4F,WAAY/qB,SAAS2qB,EAAQI,WAAY,MAE3C,OAAOrJ,EAAU8E,KAAK,QAASmF,EACjC,EAGAxB,OAAM,SAACtyB,EAAI8yB,GACT,OAAOjJ,EAAUkF,IAAI,SAAD9mB,OAAUjI,GAAM8yB,EACtC,EAEA,gBACO9yB,GAEL,IAAMZ,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDoQ,EAAS,CAAC,EAkBhB,OAfIxQ,GAAQA,EAAKY,KACf4P,EAAOhJ,OAASxH,EAAKY,IAInBZ,GAAQA,EAAKW,WACf6P,EAAO+iB,aAAevzB,EAAKW,UAG7BJ,QAAQO,IAAI,eAAgB,CAC1B6zB,KAAM/zB,EACN8xB,KAAMliB,EAAOhJ,OACbotB,MAAOpkB,EAAO+iB,eAGT9I,EAAS,UAAQ,SAAD5hB,OAAUjI,GAAM,CAAE4P,OAAAA,GAC3C,EAGAqkB,gBAAe,SAAC9lB,GACZ,OAAO0b,EAAS,UAAQ,eAAD5hB,OAAgBkG,GAC3C,EAGA+lB,YAAW,SAACttB,GACV,OAAOijB,EAAU3M,IAAI,cAADjV,OAAerB,GACrC,EAGAorB,mBAAkB,SAAC7jB,GAEjB,OADAxO,QAAQO,IAAI,gBAAD+H,OAAiBkG,IACvBA,EAKE0b,EAAU3M,IAAI,qCAADjV,OAAsCkG,GAAW,CACnEyB,OAAQ,CACN0d,YAAanf,EACb+X,EAAG5f,KAAKoS,SAPHyC,QAAQC,OAAO,IAAIpT,MAAM,UAUpC,EAGA2oB,oBAAmB,SAAC3wB,GAClB,OAAO6pB,EAAU3M,IAAI,gBAADjV,OAAiBjI,GACvC,GAIFgjB,YAAa,CAEX2N,oBAAmB,SAAC3wB,GAClB,OAAO6pB,EAAU3M,IAAI,gBAADjV,OAAiBjI,GACvC,EAGAgyB,mBAAkB,SAAC7jB,GAGjB,OAFAxO,QAAQO,IAAI,iBAELe,EAAI0yB,KAAK3B,mBAAmB7jB,EACrC,EAGAgmB,qBAAoB,SAAChmB,EAASimB,GAE5B,IAAMh1B,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDoQ,EAAS,CACb5I,IAAI,IAAIV,MAAOqhB,UACf/gB,OAAQxH,EAAKW,UAAYX,EAAKY,IAAM,IAOtC,OAJIZ,EAAKK,OACPmQ,EAAOykB,MAAQj1B,EAAKK,MAGfoqB,EAAUkF,IAAI,gBAAD9mB,OAAiBkG,EAAO,KAAAlG,OAAImsB,GAAe,KAAM,CAAExkB,OAAAA,GACzE,EAGA0kB,uBAAsB,SAACnmB,GAGrB,OAFAxO,QAAQO,IAAI,mBAELe,EAAI0yB,KAAK3B,mBAAmB7jB,EACrC,GAIFomB,WAAY,CAEV1nB,OAAM,WACJ,OAAOgd,EAAU3M,IAAI,eACvB,EAEAsX,gBAAiB,SAACC,GAGhB,IAAIr1B,EADJO,QAAQO,IAAI,iBAEZ,IACE,IAAM2G,EAAUtH,aAAaC,QAAQ,QACjCqH,GACFzH,EAAOC,KAAKC,MAAMuH,GAClBlH,QAAQO,IAAI,QAAS,CACnBF,GAAIZ,EAAKY,GACTD,SAAUX,EAAKW,SACf0K,MAAOrL,EAAKqL,MACZhL,KAAML,EAAKK,QAGbE,QAAQmL,KAAK,cAEjB,CAAE,MAAOxH,GACP3D,QAAQC,MAAM,YAAa0D,EAC7B,CAGA,GAAImxB,GAAcA,EAAWC,WAAWnyB,OAAS,EAAG,CAClD,IAAMoyB,EAASF,EAAWC,WAG1B,OAFA/0B,QAAQO,IAAI,eAAD+H,OAAgB0sB,IAEpB9K,EAAU3M,IAAI,mCAADjV,OAAoC0sB,GAC1D,CAGA,IAAMC,EAAa,eAAH3sB,OAAkBwsB,EAAU,gBAC5C90B,QAAQO,IAAI,gBAAiB00B,GAG7B,IAAM1Z,EAAS,CAAC,EAahB,OAZI9b,IAEF8b,EAAOtL,OAAS,CACdykB,MAAOj1B,EAAKK,MAAQ,OACpBmH,OAAQxH,EAAKW,UAAYX,EAAKY,GAC9BgH,IAAI,IAAIV,MAAOqhB,WAGjBhoB,QAAQO,IAAI,UAAWgb,EAAOtL,SAIzBtB,IAAAA,IAAUsmB,EAAY1Z,GAAO,UAC3B,SAAAtb,GAGL,OAFAD,QAAQC,MAAM,WAAYA,GAEnB,CAAEnB,KAAM,GACjB,GACJ,EAEAyxB,OAAM,SAAClwB,GACL,OAAO6pB,EAAU3M,IAAI,gBAADjV,OAAiBjI,GACvC,EAEAgwB,OAAM,SAAC6E,GACL,OAAOhL,EAAU8E,KAAK,eAAgBkG,EACxC,EAEAvC,OAAM,SAACtyB,EAAI60B,GACT,OAAOhL,EAAUkF,IAAI,gBAAD9mB,OAAiBjI,GAAM60B,EAC7C,EACA,gBACO70B,GACL,OAAO6pB,EAAS,UAAQ,gBAAD5hB,OAAiBjI,GAC1C,EAEA80B,mBAAkB,SAACL,GACjB,OAAO5K,EAAS,UAAQ,yBAAD5hB,OAA0BwsB,GACnD,EAEAM,qBAAoB,SAAC/0B,GACnB,OAAO6pB,EAAS,UAAQ,gBAAD5hB,OAAiBjI,GAC1C,EACAsxB,OAAM,SAAC1f,GAEL,OAAOuZ,EAAcwD,KAAK,iBAAkB/c,EAC9C,EACAmgB,WAAU,SAACngB,GAET,OAAOuZ,EAAcwD,KAAK,uBAAwB/c,EACpD,GAIF6C,iBAAkB,CAEhBugB,uBAAsB,WACpB,OAAOnL,EAAU3M,IAAI,6BACvB,EAEAkU,yBAAwB,SAAC9vB,GACvB,OAAIA,EACKuoB,EAAU3M,IAAI,uCAADjV,OAAwC3G,IAErDuoB,EAAU3M,IAAI,+BAEzB,EAEAhJ,mBAAkB,SAACyb,EAAelxB,GAEhC,OADAkB,QAAQO,IAAI,iBAAD+H,OAAkB0nB,EAAa,SAASlxB,GAC5CorB,EAAUkF,IAAI,sBAAD9mB,OAAuB0nB,GAAiBlxB,EAC9D,EAEAwV,oBAAmB,SAACF,EAAQzS,GAC1B,IAAIyF,EAAM,2BAAHkB,OAA8B8L,GAIrC,OAHIzS,IACFyF,GAAO,WAAJkB,OAAe3G,IAEbuoB,EAAU3M,IAAInW,EACvB,EAEAwpB,oBAAmB,SAAC3pB,GAClB,OAAOijB,EAAU3M,IAAI,2BAADjV,OAA4BrB,GAClD,EAEAwpB,gBAAe,SAACrc,EAAQob,GACtB,OAAOtF,EAAU8E,KAAK,qBAAsB,CAAE5a,OAAAA,EAAQob,OAAAA,GACxD,GAIF8F,UAAW,CAETC,aAAY,SAACC,GAEX,OADAx1B,QAAQO,IAAI,YAAai1B,GAClBtL,EAAU8E,KAAK,kBAAmBwG,EAC3C,EAGAC,gBAAe,WACb,OAAOvL,EAAU3M,IAAI,4BACvB,IAkBJ,S,2GC99DamY,EAAc,CAEzBC,aAAc,eACdC,aAAc,eACdC,WAAY,aAGZC,UAAW,YACXC,UAAW,YACXC,WAAY,aAGZC,YAAa,cACbC,UAAW,YACXC,YAAa,cACbC,eAAgB,iBAChBC,eAAgB,iBAGhBC,aAAc,eACdC,cAAe,gBAGfC,gBAAiB,kBACjBC,qBAAsB,uBACtBC,qBAAsB,uBAGtBC,cAAe,gBACfC,cAAe,iBAIJC,EAAmB,CAE9BC,MAAO,CACLpB,EAAYC,aACZD,EAAYE,aACZF,EAAYG,WACZH,EAAYI,UACZJ,EAAYK,UACZL,EAAYM,WACZN,EAAYO,YACZP,EAAYQ,UACZR,EAAYS,YACZT,EAAYU,eACZV,EAAYW,eACZX,EAAYY,aACZZ,EAAYa,cACZb,EAAYc,gBACZd,EAAYe,qBACZf,EAAYgB,qBACZhB,EAAYiB,cACZjB,EAAYkB,eAIdG,OAAQ,CACNrB,EAAYO,YACZP,EAAYQ,UACZR,EAAYW,eACZX,EAAYc,gBACZd,EAAYe,qBACZf,EAAYgB,qBACZhB,EAAYiB,cACZjB,EAAYI,UACZJ,EAAYK,UACZL,EAAYM,YAIdgB,SAAU,CACRtB,EAAYU,eACZV,EAAYW,eACZX,EAAYY,aACZZ,EAAYa,cACZb,EAAYc,gBACZd,EAAYe,qBACZf,EAAYgB,qBACZhB,EAAYO,YACZP,EAAYQ,UACZR,EAAYI,UACZJ,EAAYK,UACZL,EAAYM,aAKHiB,EAAoB,CAE/B,iBAAkB,CAAC,QAAS,SAAU,YAGtC,aAAc,CAAC,SAGf,aAAc,CAAC,QAAS,SAAU,YAClC,kBAAmB,CAAC,QAAS,SAAU,YACvC,kBAAmB,CAAC,QAAS,SAAU,YAGvC,aAAc,CAAC,QAAS,SAAU,YAClC,iBAAkB,CAAC,QAAS,SAAU,YACtC,kBAAmB,CAAC,QAAS,SAAU,YACvC,kBAAmB,CAAC,QAAS,SAAU,YACvC,kBAAmB,CAAC,QAAS,SAAU,YAGvC,YAAa,CAAC,QAAS,SAAU,YAGjC,cAAe,CAAC,QAAS,YAGzB,0BAA2B,CAAC,QAAS,YAGrC,qBAAsB,CAAC,QAAS,SAAU,YAG1C,4BAA6B,CAAC,QAAS,SAAU,YAGjD,eAAgB,CAAC,QAAS,SAAU,YAGpC,SAAU,CAAC,UAIN,SAAS5tB,EAAcqO,EAAUpO,GAAY,IAAA4tB,EAClD,OAAKxf,IAC4B,QAA1Bwf,EAAAL,EAAiBnf,UAAS,IAAAwf,OAAA,EAA1BA,EAA4B5f,SAAShO,MADtB,CAExB,CAGO,SAASE,EAAekO,EAAUyf,GACvC,IAAKzf,IAAayf,EAEhB,OADAn3B,QAAQO,IAAI,0BAAD+H,OAA2BoP,EAAQ,SAAApP,OAAQ6uB,KAC/C,EAMT,GAHAn3B,QAAQO,IAAI,iBAAD+H,OAAkBoP,EAAQ,cAAApP,OAAa6uB,IAG9CF,EAAkBE,GAAY,CAChC,IAAMhf,EAAY8e,EAAkBE,GAAW7f,SAASI,GAExD,OADA1X,QAAQO,IAAI,kBAAD+H,OAAmB6P,EAAY,OAAS,SAC5CA,CACT,CAGA,IAAK,IAAM1O,KAASwtB,EAClB,GAAIE,EAAU9rB,WAAW5B,GAAQ,CAC/B,IAAM0O,EAAY8e,EAAkBxtB,GAAO6N,SAASI,GAEpD,GADA1X,QAAQO,IAAI,eAAD+H,OAAgBmB,EAAK,MAAAnB,OAAK6P,EAAY,OAAS,SACtDA,EACF,OAAO,CAEX,CAIF,OADAnY,QAAQO,IAAI,+BACL,CACT,CAGO,SAASoJ,EAAkBxJ,EAAeyJ,EAAiB8N,GAEhE,OAAIvX,IAAkByJ,IAKL,UAAb8N,KAKa,aAAbA,IAA2BrO,EAAcqO,EAAUge,EAAYU,iBAMrE,C,6YClLO,SAAS98B,EAAY2b,EAAOha,GACjC,OAAKga,GAASha,GAASga,EAAMrS,QAC3B5C,QAAQmL,KAAK,WAAY8J,EAAOha,GACzB,IAEFga,EAAMha,EACf,CAGsB,qBAAX5B,SACTA,OAAOC,YAAcA,GAGD,qBAAXM,EAAAA,IACTA,EAAAA,EAAON,YAAcA,GAIhB,IAAM89B,EAAW,SAACC,GAAG,MAAoB,kBAARA,CAAgB,EAC3CC,EAAW,SAACD,GAAG,MAAoB,kBAARA,CAAgB,EAC3CE,EAAY,SAACF,GAAG,MAAoB,mBAARA,CAAiB,EAC7CG,EAAW,SAACH,GAAG,OAAa,OAARA,GAA+B,YAAfroB,EAAAA,EAAAA,GAAOqoB,EAAgB,EAC3DI,EAAa,SAACJ,GAAG,MAAoB,oBAARA,CAAkB,EAC/CvoB,EAAUD,MAAMC,QAChB4oB,EAAS,SAACL,GAAG,OAAKA,aAAe1wB,IAAI,EACrCgxB,EAAY,SAACN,GAAG,OAAKG,EAASH,IAAQI,EAAWJ,EAAI51B,OAASg2B,EAAWJ,EAAG,SAAO,EAGnFO,EAAO,CAClBC,MAAO,QACPC,QAAS,UACTC,MAAO,SAIIC,EAAY,SAACrU,EAAM1nB,GAAG,OAAK0nB,CAAI,EAC/BsU,EAAa,SAACjiB,GAAK,OAAKA,CAAK,EAC7BkiB,EAAiB,SAACt7B,GAAI,OAAKA,CAAI,EAC/Bu7B,EAAU,SAACd,GAAG,OAAKA,CAAG,EAGtBe,EAAQC,OAAO,SACfC,EAAwBD,OAAO,WAGtCE,EAAwB,WAC5B,IACE,GAAsB,qBAAXl/B,OAAwB,CAEjCA,OAAOm/B,mBAAqB,CAC1BpB,SAAAA,EACAE,SAAAA,EACAC,UAAAA,EACAC,SAAAA,EACAC,WAAAA,GAIFp+B,OAAOo/B,uBAAyB,CAAC,EAGjC,IAAMC,EAAiBr/B,OAAOs/B,qBAAwB,WAAO,EACzDt/B,OAAOs/B,sBACTt/B,OAAOs/B,oBAAsB,SAASC,GACpC,IACE,OAAOF,EAAeE,EACxB,CAAE,MAAOj1B,GAEP,GAAIA,EAAEF,SAAWE,EAAEF,QAAQ6T,SAAS,sBAGlC,OADAtX,QAAQmL,KAAK,sBAAuBxH,EAAEF,SAC/B,CAAC,EAEV,MAAME,CACR,CACF,EAEJ,CACF,CAAE,MAAOA,GACP3D,QAAQmL,KAAK,wBAAyBxH,EACxC,CACF,EAGA40B,IAGA,SACEj/B,YAAAA,EACA89B,SAAAA,EACAE,SAAAA,EACAC,UAAAA,EACAC,SAAAA,EACAC,WAAAA,EACA3oB,QAAAA,EACA4oB,OAAAA,EACAC,UAAAA,EACAC,KAAAA,EACAI,UAAAA,EACAC,WAAAA,EACAC,eAAAA,EACAC,QAAAA,EACAC,MAAAA,EACAE,sBAAAA,E,uFCjHWzvB,EAAe,CAE1ByC,cAAa,SAACrP,EAAK6C,GACjB,IAEE,OADAc,aAAac,QAAQzE,EAAKyD,KAAK8Q,UAAU1R,KAClC,CACT,CAAE,MAAO6E,GAEP,OADA3D,QAAQC,MAAM,gBAADqI,OAAiBrM,GAAO0H,IAC9B,CACT,CACF,EAGAmF,eAAc,SAAC7M,GAA0B,IAArB48B,EAAY5a,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,KACjC,IACE,IAAMnf,EAAOc,aAAaC,QAAQ5D,GAClC,OAAO6C,EAAOY,KAAKC,MAAMb,GAAQ+5B,CACnC,CAAE,MAAOl1B,GAEP,OADA3D,QAAQC,MAAM,gBAADqI,OAAiBrM,GAAO0H,GAC9Bk1B,CACT,CACF,EAGA7sB,kBAAiB,SAAC/P,GAChB,IAEE,OADA2D,aAAawC,WAAWnG,IACjB,CACT,CAAE,MAAO0H,GAEP,OADA3D,QAAQC,MAAM,iBAADqI,OAAkBrM,GAAO0H,IAC/B,CACT,CACF,GAIWgH,EAAqB,CAEhCC,MAAK,SAACH,GAAoE,IAA5DquB,EAAe7a,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,aAAc8a,EAAa9a,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,WAC5DxT,EAAOquB,GAAiB,GACpBC,GAAetuB,EAAOsuB,EAAe,KAC3C,EAGAxtB,IAAG,SAACd,GAAwC,IAAhCquB,EAAe7a,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,aAC5BxT,EAAOquB,GAAiB,EAC1B,EAGA74B,MAAK,SAACwK,EAAQxK,GAAmE,IAC3EoQ,EADe0oB,EAAa9a,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,WAAY6a,EAAe7a,UAAArb,OAAA,QAAA2vB,IAAAtU,UAAA,GAAAA,UAAA,GAAG,aAIjE,GAAIhe,EAAMyB,SAAU,CAElB,IAAMs3B,EAAe/4B,EAAMyB,SAAS5C,KAKlCuR,EAF0B,kBAAjB2oB,EAEMA,EACNA,GAAgBA,EAAav1B,QAEvBu1B,EAAav1B,QACnBu1B,GAAgBA,EAAa/4B,MAEvB+4B,EAAa/4B,MAGbA,EAAMyB,SAAS8sB,UAElC,MAEEne,EAFSpQ,EAAMwD,QAEAxD,EAAMwD,QAGN,OAYjB,OARAgH,EAAOsuB,EAAe1oB,GACtB5F,EAAOquB,GAAiB,GAExB94B,QAAQC,MAAM,QAASA,GACnBA,EAAMyB,UACR1B,QAAQC,MAAM,QAASA,EAAMyB,SAASC,OAAQ1B,EAAMyB,SAAS5C,MAGxDuR,CACT,E,2GCrFI4oB,EAAgB5/B,OAAO0O,MACvBmxB,EAAc7/B,OAAOuf,eAGd+F,EAAwB,WA8HnC,OA5HAtlB,OAAO8/B,0BAA2B,EAClCn5B,QAAQO,IAAI,cAAe,6FAG3BlH,OAAO0O,MAAQ,SAASX,EAAKgyB,GAC3B,GAAmB,kBAARhyB,IAAqBA,EAAIkQ,SAAS,cAAgBlQ,EAAIkQ,SAAS,aAAc,CAMtF,GALAtX,QAAQq5B,MAAM,oBAAqB,yEACnCr5B,QAAQO,IAAI,OAAQ6G,GACpBpH,QAAQO,IAAI,MAAO64B,GAGfA,GAAWA,EAAQE,KACrB,IACE,IAAMA,EAAO55B,KAAKC,MAAMy5B,EAAQE,MAChCt5B,QAAQO,IAAI,OAAQ+4B,EACtB,CAAE,MAAO31B,GACP3D,QAAQO,IAAI,cAAe64B,EAAQE,KACrC,CAGFt5B,QAAQu5B,UACV,CAGA,OAAON,EAAc1a,MAAMjf,KAAM2e,WAC9Bxc,MAAK,SAAAC,GAEJ,GAAmB,kBAAR0F,IAAqBA,EAAIkQ,SAAS,cAAgBlQ,EAAIkQ,SAAS,aAAc,CACtFtX,QAAQq5B,MAAM,oBAAqB,yEACnCr5B,QAAQO,IAAI,MAAOmB,EAASC,OAAQD,EAAS8sB,YAC7CxuB,QAAQO,IAAI,OAAQmB,EAASuG,SAG7B,IAAMuxB,EAAiB93B,EAAS+3B,QAChCD,EAAejxB,OAAO9G,MAAK,SAAA3C,GACzBkB,QAAQO,IAAI,QAASzB,GACrBkB,QAAQu5B,UACV,IAAE,UAAO,WACPv5B,QAAQO,IAAI,mBACZP,QAAQu5B,UACV,GACF,CACA,OAAO73B,CACT,IAAE,UACK,SAAAzB,GAML,KALmB,kBAARmH,IAAqBA,EAAIkQ,SAAS,cAAgBlQ,EAAIkQ,SAAS,eACxEtX,QAAQq5B,MAAM,oBAAqB,yEACnCr5B,QAAQC,MAAM,QAASA,GACvBD,QAAQu5B,YAEJt5B,CACR,GACJ,EAGA5G,OAAOuf,eAAiB,WACtB,IAAMD,EAAM,IAAIugB,EACVQ,EAAe/gB,EAAIE,KACnB8gB,EAAehhB,EAAIK,KAErB4gB,EAAa,GACbC,EAAgB,GA4DpB,OAzDAlhB,EAAIE,KAAO,SAAS7Q,EAAQZ,GAQ1B,OAPAwyB,EAAaxyB,EACbyyB,EAAgB7xB,EACG,kBAARZ,IAAqBA,EAAIkQ,SAAS,cAAgBlQ,EAAIkQ,SAAS,eACxEtX,QAAQq5B,MAAM,kBAAmB,yEACjCr5B,QAAQO,IAAI,OAAQ6G,GACpBpH,QAAQO,IAAI,MAAOyH,IAEd0xB,EAAanb,MAAMjf,KAAM2e,UAClC,EAEAtF,EAAIK,KAAO,SAASsgB,GAElB,GAA0B,kBAAfM,IAA4BA,EAAWtiB,SAAS,cAAgBsiB,EAAWtiB,SAAS,cACzFgiB,EACF,IACE,IAAMQ,EAAap6B,KAAKC,MAAM25B,GAC9Bt5B,QAAQO,IAAI,OAAQu5B,EACtB,CAAE,MAAOn2B,GACP3D,QAAQO,IAAI,cAAe+4B,EAC7B,CAGJ,OAAOK,EAAapb,MAAMjf,KAAM2e,UAClC,EAGAtF,EAAIxS,iBAAiB,QAAQ,WAC3B,GAA0B,kBAAfyzB,IAA4BA,EAAWtiB,SAAS,cAAgBsiB,EAAWtiB,SAAS,aAAc,CAE3G,GADAtX,QAAQO,IAAI,MAAOoY,EAAIhX,OAAQgX,EAAI6V,YAC/B7V,EAAIM,aACN,IACE,IAAM+f,EAAet5B,KAAKC,MAAMgZ,EAAIM,cACpCjZ,QAAQO,IAAI,QAASy4B,EACvB,CAAE,MAAOr1B,GACP3D,QAAQO,IAAI,eAAgBoY,EAAIM,aAClC,CAEFjZ,QAAQu5B,UACV,CACF,IAGA5gB,EAAIxS,iBAAiB,SAAS,WACF,kBAAfyzB,IAA4BA,EAAWtiB,SAAS,cAAgBsiB,EAAWtiB,SAAS,eAC7FtX,QAAQq5B,MAAM,kBAAmB,yEACjCr5B,QAAQC,MAAM,QAAS,CACrBmH,IAAKwyB,EACL5xB,OAAQ6xB,EACRl4B,OAAQgX,EAAIhX,OACZ6sB,WAAY7V,EAAI6V,WAChBvV,aAAcN,EAAIM,eAEpBjZ,QAAQu5B,WAEZ,IAEO5gB,CACT,GAEO,CACT,EAGaohB,EAAiB,SAAC5G,GAS7B,OARAnzB,QAAQq5B,MAAM,WAAY,yEAC1Br5B,QAAQO,IAAI,QAAS4yB,EAAQC,KAC7BpzB,QAAQO,IAAI,MAAO,IAAF+H,OAAM6qB,EAAQE,EAAC,MAAA/qB,OAAK6qB,EAAQG,EAAC,MAC9CtzB,QAAQO,IAAI,MAAO,GAAF+H,OAAK6qB,EAAQx4B,MAAK,OAAA2N,OAAM6qB,EAAQ52B,SACjDyD,QAAQO,IAAI,QAAS4yB,EAAQxF,aAC7B3tB,QAAQO,IAAI,OAAQ4yB,EAAQI,YAC5BvzB,QAAQu5B,WAED,CACLS,KAAM7G,EAAQC,IACd6G,OAAQ9G,EAAQE,EAChB6G,OAAQ/G,EAAQG,EAChB6G,GAAIhH,EAAQx4B,MACZy/B,GAAIjH,EAAQ52B,OACZ89B,KAAMlH,EAAQxF,YACd2M,MAAOnH,EAAQI,WAEnB,C,oDC5JAvzB,QAAQO,IAAI,oBAGZ5G,EAAQ,OAmBRA,EAAQ,M,oHCfF6jB,EAAe,GACfC,EAAmBlkB,WACnBghC,EAAahhC,OAGbqV,EAAU,GAAHtG,OAAMkV,GAAYlV,OAAGmV,GAAgBnV,OAAGiyB,GAG/CzhB,EAAsB,GAAHxQ,OAAMmV,EAAgB,2BAEzC3V,EAA8B,SAACb,GACnC,IAAMkS,EAAYxS,KAAKoS,MACjB4E,EAASD,KAAKC,SAEpB,MAAO,GAAPrV,OAAUwQ,EAAmB,KAAAxQ,OAAIrB,EAAM,OAAAqB,OAAM6Q,EAAS,OAAA7Q,OAAMqV,EAAM,yCACpE,EAGM6c,EAAc,WAClB,OAAO,CACT,C,GC3BIC,EAA2B,CAAC,EAGhC,SAAS9B,EAAoBC,GAE5B,IAAI8B,EAAeD,EAAyB7B,GAC5C,QAAqBrG,IAAjBmI,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASH,EAAyB7B,GAAY,CAGjD+B,QAAS,CAAC,GAOX,OAHAE,EAAoBjC,GAAU3d,KAAK2f,EAAOD,QAASC,EAAQA,EAAOD,QAAShC,GAGpEiC,EAAOD,OACf,CAGAhC,EAAoB5xB,EAAI8zB,E,MCzBxB,IAAIC,EAAW,GACfnC,EAAoBoC,EAAI,CAACC,EAAQC,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASlmB,EAAI,EAAGA,EAAI2lB,EAASl4B,OAAQuS,IAAK,CAGzC,IAFA,IAAK8lB,EAAUC,EAAIC,GAAYL,EAAS3lB,GACpCmmB,GAAY,EACPC,EAAI,EAAGA,EAAIN,EAASr4B,OAAQ24B,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAaphC,OAAOsZ,KAAKslB,EAAoBoC,GAAGlhB,OAAO5d,GAAS08B,EAAoBoC,EAAE9+B,GAAKg/B,EAASM,MAC9IN,EAAS3sB,OAAOitB,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbR,EAASxsB,OAAO6G,IAAK,GACrB,IAAIqmB,EAAIN,SACE3I,IAANiJ,IAAiBR,EAASQ,EAC/B,CACD,CACA,OAAOR,CAnBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIhmB,EAAI2lB,EAASl4B,OAAQuS,EAAI,GAAK2lB,EAAS3lB,EAAI,GAAG,GAAKgmB,EAAUhmB,IAAK2lB,EAAS3lB,GAAK2lB,EAAS3lB,EAAI,GACrG2lB,EAAS3lB,GAAK,CAAC8lB,EAAUC,EAAIC,EAqBjB,C,WCzBdxC,EAAoBnxB,EAAKozB,IACxB,IAAIa,EAASb,GAAUA,EAAOc,WAC7B,IAAOd,EAAO,WACd,IAAM,EAEP,OADAjC,EAAoBgD,EAAEF,EAAQ,CAAEh0B,EAAGg0B,IAC5BA,CAAM,C,WCLd9C,EAAoBgD,EAAI,CAAChB,EAASiB,KACjC,IAAI,IAAI3/B,KAAO2/B,EACXjD,EAAoBkD,EAAED,EAAY3/B,KAAS08B,EAAoBkD,EAAElB,EAAS1+B,IAC5ElC,OAAO+hC,eAAenB,EAAS1+B,EAAK,CAAE8/B,YAAY,EAAMxe,IAAKqe,EAAW3/B,IAE1E,C,WCND08B,EAAoBhwB,EAAI,CAAC,EAGzBgwB,EAAoBh1B,EAAKq4B,GACjBxgB,QAAQiY,IAAI15B,OAAOsZ,KAAKslB,EAAoBhwB,GAAGszB,QAAO,CAACC,EAAUjgC,KACvE08B,EAAoBhwB,EAAE1M,GAAK+/B,EAASE,GAC7BA,IACL,I,WCNJvD,EAAoBwD,EAAKH,GAEjB,MAAQA,EAAU,IAAM,CAAC,EAAI,WAAW,GAAK,WAAW,GAAK,WAAW,GAAK,WAAW,GAAK,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,K,WCF3XrD,EAAoByD,SAAYJ,GAExB,OAASA,EAAU,IAAM,CAAC,EAAI,WAAW,GAAK,WAAW,GAAK,WAAW,GAAK,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,M,WCH7XrD,EAAoB0D,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOh9B,MAAQ,IAAIi9B,SAAS,cAAb,EAChB,CAAE,MAAO54B,GACR,GAAsB,kBAAXtK,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,WCAxBs/B,EAAoBkD,EAAI,CAACW,EAAK7Y,IAAU5pB,OAAO0iC,UAAU9qB,eAAesJ,KAAKuhB,EAAK7Y,E,WCAlF,IAAI+Y,EAAa,CAAC,EACdC,EAAoB,+BAExBhE,EAAoBiE,EAAI,CAACx1B,EAAKy1B,EAAM5gC,EAAK+/B,KACxC,GAAGU,EAAWt1B,GAAQs1B,EAAWt1B,GAAK7L,KAAKshC,OAA3C,CACA,IAAIC,EAAQC,EACZ,QAAWxK,IAARt2B,EAEF,IADA,IAAI+gC,EAAUhmB,SAASimB,qBAAqB,UACpC9nB,EAAI,EAAGA,EAAI6nB,EAAQp6B,OAAQuS,IAAK,CACvC,IAAI+nB,EAAIF,EAAQ7nB,GAChB,GAAG+nB,EAAEC,aAAa,QAAU/1B,GAAO81B,EAAEC,aAAa,iBAAmBR,EAAoB1gC,EAAK,CAAE6gC,EAASI,EAAG,KAAO,CACpH,CAEGJ,IACHC,GAAa,EACbD,EAAS9lB,SAASomB,cAAc,UAEhCN,EAAOO,QAAU,QACjBP,EAAO1hB,QAAU,IACbud,EAAoB2E,IACvBR,EAAO3iB,aAAa,QAASwe,EAAoB2E,IAElDR,EAAO3iB,aAAa,eAAgBwiB,EAAoB1gC,GAExD6gC,EAAOvd,IAAMnY,GAEds1B,EAAWt1B,GAAO,CAACy1B,GACnB,IAAIU,EAAmB,CAACC,EAAM3hB,KAE7BihB,EAAOW,QAAUX,EAAOY,OAAS,KACjCC,aAAaviB,GACb,IAAIwiB,EAAUlB,EAAWt1B,GAIzB,UAHOs1B,EAAWt1B,GAClB01B,EAAOe,YAAcf,EAAOe,WAAWC,YAAYhB,GACnDc,GAAWA,EAAQtqB,SAAS4nB,GAAQA,EAAGrf,KACpC2hB,EAAM,OAAOA,EAAK3hB,EAAM,EAExBT,EAAU3a,WAAW88B,EAAiBQ,KAAK,UAAMxL,EAAW,CAAE31B,KAAM,UAAWohC,OAAQlB,IAAW,MACtGA,EAAOW,QAAUF,EAAiBQ,KAAK,KAAMjB,EAAOW,SACpDX,EAAOY,OAASH,EAAiBQ,KAAK,KAAMjB,EAAOY,QACnDX,GAAc/lB,SAASinB,KAAKC,YAAYpB,EApCkB,CAoCX,C,WCvChDnE,EAAoB6C,EAAKb,IACH,qBAAXtC,QAA0BA,OAAO8F,aAC1CpkC,OAAO+hC,eAAenB,EAAStC,OAAO8F,YAAa,CAAE/6B,MAAO,WAE7DrJ,OAAO+hC,eAAenB,EAAS,aAAc,CAAEv3B,OAAO,GAAO,C,WCL9Du1B,EAAoB9wB,EAAI,G,WCAxB,GAAwB,qBAAbmP,SAAX,CACA,IAAIonB,EAAmB,CAACpC,EAASqC,EAAUC,EAAQ7P,EAAShT,KAC3D,IAAI8iB,EAAUvnB,SAASomB,cAAc,QAErCmB,EAAQC,IAAM,aACdD,EAAQ3hC,KAAO,WACX+7B,EAAoB2E,KACvBiB,EAAQE,MAAQ9F,EAAoB2E,IAErC,IAAIoB,EAAkB7iB,IAGrB,GADA0iB,EAAQd,QAAUc,EAAQb,OAAS,KAChB,SAAf7hB,EAAMjf,KACT6xB,QACM,CACN,IAAIkQ,EAAY9iB,GAASA,EAAMjf,KAC3BgiC,EAAW/iB,GAASA,EAAMmiB,QAAUniB,EAAMmiB,OAAO97B,MAAQm8B,EACzDt+B,EAAM,IAAIsI,MAAM,qBAAuB2zB,EAAU,cAAgB2C,EAAY,KAAOC,EAAW,KACnG7+B,EAAI5F,KAAO,iBACX4F,EAAIqwB,KAAO,wBACXrwB,EAAInD,KAAO+hC,EACX5+B,EAAIsb,QAAUujB,EACVL,EAAQV,YAAYU,EAAQV,WAAWC,YAAYS,GACvD9iB,EAAO1b,EACR,GAWD,OATAw+B,EAAQd,QAAUc,EAAQb,OAASgB,EACnCH,EAAQr8B,KAAOm8B,EAGXC,EACHA,EAAOT,WAAWgB,aAAaN,EAASD,EAAOQ,aAE/C9nB,SAASinB,KAAKC,YAAYK,GAEpBA,CAAO,EAEXQ,EAAiB,CAAC78B,EAAMm8B,KAE3B,IADA,IAAIW,EAAmBhoB,SAASimB,qBAAqB,QAC7C9nB,EAAI,EAAGA,EAAI6pB,EAAiBp8B,OAAQuS,IAAK,CAChD,IAAIie,EAAM4L,EAAiB7pB,GACvB8pB,EAAW7L,EAAI+J,aAAa,cAAgB/J,EAAI+J,aAAa,QACjE,GAAe,eAAZ/J,EAAIoL,MAAyBS,IAAa/8B,GAAQ+8B,IAAaZ,GAAW,OAAOjL,CACrF,CACA,IAAI8L,EAAoBloB,SAASimB,qBAAqB,SACtD,IAAQ9nB,EAAI,EAAGA,EAAI+pB,EAAkBt8B,OAAQuS,IAAK,CAC7Cie,EAAM8L,EAAkB/pB,GACxB8pB,EAAW7L,EAAI+J,aAAa,aAChC,GAAG8B,IAAa/8B,GAAQ+8B,IAAaZ,EAAU,OAAOjL,CACvD,GAEG+L,EAAkBnD,GACd,IAAIxgB,SAAQ,CAACiT,EAAShT,KAC5B,IAAIvZ,EAAOy2B,EAAoByD,SAASJ,GACpCqC,EAAW1F,EAAoB9wB,EAAI3F,EACvC,GAAG68B,EAAe78B,EAAMm8B,GAAW,OAAO5P,IAC1C2P,EAAiBpC,EAASqC,EAAU,KAAM5P,EAAShT,EAAO,IAIxD2jB,EAAqB,CACxB,IAAK,GAGNzG,EAAoBhwB,EAAE02B,QAAU,CAACrD,EAASE,KACzC,IAAIoD,EAAY,CAAC,EAAI,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,GACvKF,EAAmBpD,GAAUE,EAAS3gC,KAAK6jC,EAAmBpD,IACzB,IAAhCoD,EAAmBpD,IAAkBsD,EAAUtD,IACtDE,EAAS3gC,KAAK6jC,EAAmBpD,GAAWmD,EAAenD,GAASv6B,MAAK,KACxE29B,EAAmBpD,GAAW,CAAC,IAC5Br4B,IAEH,aADOy7B,EAAmBpD,GACpBr4B,CAAC,IAET,CA1E0C,C,WCK3C,IAAI47B,EAAkB,CACrB,IAAK,GAGN5G,EAAoBhwB,EAAE4yB,EAAI,CAACS,EAASE,KAElC,IAAIsD,EAAqB7G,EAAoBkD,EAAE0D,EAAiBvD,GAAWuD,EAAgBvD,QAAWzJ,EACtG,GAA0B,IAAvBiN,EAGF,GAAGA,EACFtD,EAAS3gC,KAAKikC,EAAmB,SAEjC,GAAG,KAAOxD,EAAS,CAElB,IAAIyD,EAAU,IAAIjkB,SAAQ,CAACiT,EAAShT,IAAY+jB,EAAqBD,EAAgBvD,GAAW,CAACvN,EAAShT,KAC1GygB,EAAS3gC,KAAKikC,EAAmB,GAAKC,GAGtC,IAAIr4B,EAAMuxB,EAAoB9wB,EAAI8wB,EAAoBwD,EAAEH,GAEpD/7B,EAAQ,IAAIoI,MACZq3B,EAAgB7jB,IACnB,GAAG8c,EAAoBkD,EAAE0D,EAAiBvD,KACzCwD,EAAqBD,EAAgBvD,GACX,IAAvBwD,IAA0BD,EAAgBvD,QAAWzJ,GACrDiN,GAAoB,CACtB,IAAIb,EAAY9iB,IAAyB,SAAfA,EAAMjf,KAAkB,UAAYif,EAAMjf,MAChE+iC,EAAU9jB,GAASA,EAAMmiB,QAAUniB,EAAMmiB,OAAOze,IACpDtf,EAAMwD,QAAU,iBAAmBu4B,EAAU,cAAgB2C,EAAY,KAAOgB,EAAU,IAC1F1/B,EAAM9F,KAAO,iBACb8F,EAAMrD,KAAO+hC,EACb1+B,EAAMob,QAAUskB,EAChBH,EAAmB,GAAGv/B,EACvB,CACD,EAED04B,EAAoBiE,EAAEx1B,EAAKs4B,EAAc,SAAW1D,EAASA,EAC9D,MAAOuD,EAAgBvD,GAAW,CAEpC,EAWFrD,EAAoBoC,EAAEQ,EAAKS,GAA0C,IAA7BuD,EAAgBvD,GAGxD,IAAI4D,EAAuB,CAACC,EAA4B/gC,KACvD,IAGI85B,EAAUoD,GAHTf,EAAU6E,EAAaC,GAAWjhC,EAGhBqW,EAAI,EAC3B,GAAG8lB,EAASp4B,MAAMxC,GAAgC,IAAxBk/B,EAAgBl/B,KAAa,CACtD,IAAIu4B,KAAYkH,EACZnH,EAAoBkD,EAAEiE,EAAalH,KACrCD,EAAoB5xB,EAAE6xB,GAAYkH,EAAYlH,IAGhD,GAAGmH,EAAS,IAAI/E,EAAS+E,EAAQpH,EAClC,CAEA,IADGkH,GAA4BA,EAA2B/gC,GACrDqW,EAAI8lB,EAASr4B,OAAQuS,IACzB6mB,EAAUf,EAAS9lB,GAChBwjB,EAAoBkD,EAAE0D,EAAiBvD,IAAYuD,EAAgBvD,IACrEuD,EAAgBvD,GAAS,KAE1BuD,EAAgBvD,GAAW,EAE5B,OAAOrD,EAAoBoC,EAAEC,EAAO,EAGjCgF,EAAqBC,KAAK,2CAA6CA,KAAK,4CAA8C,GAC9HD,EAAmB1sB,QAAQssB,EAAqB7B,KAAK,KAAM,IAC3DiC,EAAmBzkC,KAAOqkC,EAAqB7B,KAAK,KAAMiC,EAAmBzkC,KAAKwiC,KAAKiC,G,KClFvF,IAAIE,EAAsBvH,EAAoBoC,OAAExI,EAAW,CAAC,MAAM,IAAOoG,EAAoB,SAC7FuH,EAAsBvH,EAAoBoC,EAAEmF,E", "sources": ["webpack://medical-annotation-frontend/./src/shims-webpack.js", "webpack://medical-annotation-frontend/./src/polyfills.js", "webpack://medical-annotation-frontend/./src/App.vue", "webpack://medical-annotation-frontend/./src/App.vue?7ccd", "webpack://medical-annotation-frontend/./src/components/layout/MainLayout.vue", "webpack://medical-annotation-frontend/./src/components/layout/MainLayout.vue?3fef", "webpack://medical-annotation-frontend/./src/components/layout/SimpleLayout.vue", "webpack://medical-annotation-frontend/./src/components/layout/SimpleLayout.vue?79a6", "webpack://medical-annotation-frontend/./src/views/Dashboard.vue", "webpack://medical-annotation-frontend/./src/views/Dashboard.vue?040e", "webpack://medical-annotation-frontend/./src/store/modules/auth.js", "webpack://medical-annotation-frontend/./src/store/modules/images.js", "webpack://medical-annotation-frontend/./src/store/modules/users.js", "webpack://medical-annotation-frontend/./src/store/modules/stats.js", "webpack://medical-annotation-frontend/./src/store/modules/annotation.js", "webpack://medical-annotation-frontend/./src/store/modules/teamApplications.js", "webpack://medical-annotation-frontend/./src/store/index.js", "webpack://medical-annotation-frontend/./src/router/index.js", "webpack://medical-annotation-frontend/./src/directives/permission.js", "webpack://medical-annotation-frontend/./src/expose-components.js", "webpack://medical-annotation-frontend/./src/main.js", "webpack://medical-annotation-frontend/./src/components/AnnotationReviewList.vue", "webpack://medical-annotation-frontend/./src/components/AnnotationReviewList.vue?f339", "webpack://medical-annotation-frontend/./src/utils/api.js", "webpack://medical-annotation-frontend/./src/utils/permissions.js", "webpack://medical-annotation-frontend/./src/element-plus-unified-fix.js", "webpack://medical-annotation-frontend/./src/utils/storeHelpers.js", "webpack://medical-annotation-frontend/./src/utils/debug.js", "webpack://medical-annotation-frontend/./webpack-entry.js", "webpack://medical-annotation-frontend/./src/config/api.config.js", "webpack://medical-annotation-frontend/webpack/bootstrap", "webpack://medical-annotation-frontend/webpack/runtime/chunk loaded", "webpack://medical-annotation-frontend/webpack/runtime/compat get default export", "webpack://medical-annotation-frontend/webpack/runtime/define property getters", "webpack://medical-annotation-frontend/webpack/runtime/ensure chunk", "webpack://medical-annotation-frontend/webpack/runtime/get javascript chunk filename", "webpack://medical-annotation-frontend/webpack/runtime/get mini-css chunk filename", "webpack://medical-annotation-frontend/webpack/runtime/global", "webpack://medical-annotation-frontend/webpack/runtime/hasOwnProperty shorthand", "webpack://medical-annotation-frontend/webpack/runtime/load script", "webpack://medical-annotation-frontend/webpack/runtime/make namespace object", "webpack://medical-annotation-frontend/webpack/runtime/publicPath", "webpack://medical-annotation-frontend/webpack/runtime/css loading", "webpack://medical-annotation-frontend/webpack/runtime/jsonp chunk loading", "webpack://medical-annotation-frontend/webpack/startup"], "sourcesContent": ["// 这个文件用于修复webpack特定的问题\n// 注意：此文件内容已被移至element-plus-unified-fix.js，保留此文件仅为保持向后兼容性\n\n// 导入统一修复文件中的tryNextPath函数\nimport { tryNextPath } from './element-plus-unified-fix.js';\n\n// 确保全局可用\nif (typeof window !== 'undefined' && typeof window.tryNextPath === 'undefined') {\n  window.tryNextPath = tryNextPath;\n}\n\n// 导出一个空对象\nexport default {}; ", "// 修复webpack中的模块解析问题\nimport 'core-js/stable';\nimport 'regenerator-runtime/runtime';\n\n// 全局处理Node.js内置模块\nif (typeof window !== 'undefined') {\n  window.process = window.process || {};\n  window.process.browser = true;\n  window.process.env = window.process.env || {};\n  window.Buffer = window.Buffer || require('buffer').Buffer;\n  \n  // 不再需要这个函数，它已经在element-plus-unified-fix.js中定义\n  // window.tryNextPath = function(pathArr, index) {\n  //   if (index >= pathArr.length) {\n  //     throw new Error('Failed to resolve module path');\n  //   }\n  //   return pathArr[index];\n  // };\n}\n\n// 修复util._extend弃用警告\nif (typeof global !== 'undefined' && global.util && global.util._extend) {\n  global.util._extend = Object.assign;\n}\n\n// 导出一个空对象，表明这是一个模块\nexport default {}; ", "<template>\n  <router-view />\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n}\n\n#app {\n  height: 100%;\n}\n\n/* 覆盖Element Plus的默认样式 */\n.el-menu {\n  border-right: none !important;\n}\n\n.el-menu-item.is-active {\n  background-color: #1890ff !important;\n}\n\n.el-card {\n  border-radius: 4px;\n  border: none;\n  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), \n              0 3px 6px 0 rgba(0, 0, 0, 0.12), \n              0 5px 12px 4px rgba(0, 0, 0, 0.09) !important;\n}\n\n.el-button--primary {\n  background-color: #1890ff !important;\n  border-color: #1890ff !important;\n}\n\n.el-button--primary:hover {\n  background-color: #40a9ff !important;\n  border-color: #40a9ff !important;\n}\n</style> ", "import { render } from \"./App.vue?vue&type=template&id=26afa77c\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=26afa77c&lang=css\"\n\nimport exportComponent from \"../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\n  <el-container class=\"layout-container\">\n    <el-aside width=\"200px\" class=\"sidebar\">\n      <div class=\"logo\">血管瘤诊断平台</div>\n      <el-menu\n        :default-active=\"activeMenu\"\n        class=\"sidebar-menu\"\n        background-color=\"#001529\"\n        text-color=\"#fff\"\n        active-text-color=\"#409EFF\"\n      >\n        <!-- 工作台 - 所有角色可见 -->\n        <el-menu-item index=\"/app/dashboard\" @click=\"$router.push('/app/dashboard')\">\n          <el-icon><HomeFilled /></el-icon>\n          <span>工作台</span>\n        </el-menu-item>\n        \n        <!-- 病例标注 - 所有角色可见 -->\n        <el-menu-item \n          index=\"/app/cases\" \n          @click=\"$router.push('/app/cases')\"\n        >\n          <el-icon><Document /></el-icon>\n          <span>病例标注</span>\n        </el-menu-item>\n        \n        <!-- 血管瘤诊断 - 所有角色可见 -->\n        <el-menu-item \n          index=\"/app/hemangioma-diagnosis\" \n          @click=\"navigateToHemangiomaDiagnosis\"\n        >\n          <el-icon><Picture /></el-icon>\n          <span>血管瘤诊断</span>\n        </el-menu-item>\n        \n        <!-- 标注审核 - 管理员和审核医生可见 -->\n        <el-menu-item \n          v-if=\"isAdmin || isReviewer\"\n          index=\"/app/annotation-reviews\" \n          @click=\"$router.push('/app/annotation-reviews')\"\n        >\n          <el-icon><Check /></el-icon>\n          <span>标注审核</span>\n        </el-menu-item>\n        \n        <!-- 团队 - 所有角色可见 -->\n        <el-menu-item \n          index=\"/app/teams\" \n          @click=\"$router.push('/app/teams')\"\n          class=\"team-menu-item\"\n        >\n          <el-icon><UserFilled /></el-icon>\n          <span>我的团队</span>\n          <!-- 移除团队申请数量徽章 -->\n        </el-menu-item>\n        \n        <!-- 部门成员 - 只有管理员可见 -->\n        <el-menu-item \n          v-if=\"isAdmin\"\n          index=\"/app/users\" \n          @click=\"$router.push('/app/users')\"\n        >\n          <el-icon><User /></el-icon>\n          <span>人员管理</span>\n        </el-menu-item>\n      </el-menu>\n    </el-aside>\n    \n    <el-container>\n      <el-header height=\"60px\" class=\"header\">\n        <div class=\"header-left\">\n          <el-button \n            v-if=\"showDashboardButton\" \n            type=\"primary\" \n            icon=\"el-icon-s-home\" \n            @click=\"$router.push('/app/dashboard')\">\n            返回工作台\n          </el-button>\n          <el-button \n            v-if=\"showBackButton\" \n            type=\"info\" \n            icon=\"el-icon-back\" \n            @click=\"goBack\">\n            返回上一步\n          </el-button>\n        </div>\n        <div class=\"header-right\">\n          <el-dropdown>\n            <span class=\"user-info\">\n              <el-avatar size=\"small\" icon=\"el-icon-user\"></el-avatar>\n              {{ username }}\n              <span :class=\"['role-tag', roleTagClass]\">{{ userRoleText }}</span>\n            </span>\n            <template #dropdown>\n              <el-dropdown-menu>\n                <el-dropdown-item>\n                  <router-link to=\"/app/profile\" style=\"text-decoration: none; color: inherit;\">\n                    个人中心\n                  </router-link>\n                </el-dropdown-item>\n                <el-dropdown-item v-if=\"isDoctor && !hasAppliedForReviewer\" @click=\"handleApplyForReviewer\">\n                  申请升级权限\n                </el-dropdown-item>\n                <el-dropdown-item v-if=\"isDoctor && hasAppliedForReviewer\" disabled>\n                  权限申请审核中\n                </el-dropdown-item>\n                <el-dropdown-item @click=\"handleLogout\">退出登录</el-dropdown-item>\n              </el-dropdown-menu>\n            </template>\n          </el-dropdown>\n        </div>\n      </el-header>\n      \n      <el-main>\n        <router-view></router-view>\n      </el-main>\n    </el-container>\n  </el-container>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { \n  HomeFilled, \n  Document, \n  Check, \n  User,\n  UserFilled,\n  Picture\n} from '@element-plus/icons-vue'\nimport api from '@/utils/api'\n\nexport default {\n  name: 'MainLayout',\n  components: {\n    HomeFilled,\n    Document,\n    Check,\n    User,\n    UserFilled,\n    Picture\n  },\n  data() {\n    return {\n      username: '标注医生',\n      showBackButton: false,\n      showDashboardButton: false,\n      hasAppliedForReviewer: false,\n      pollTimer: null // 用于存储定时器的ID\n    }\n  },\n  computed: {\n    ...mapGetters({\n      currentUser: 'getUser',\n      isAdmin: 'isAdmin',\n      isDoctor: 'isDoctor',\n      isReviewer: 'isReviewer',\n      // 添加团队申请相关的getter\n      hasPendingApplications: 'teamApplications/hasPendingApplications',\n      pendingApplicationsCount: 'teamApplications/getPendingApplicationsCount'\n    }),\n    // 当前激活的菜单项\n    activeMenu() {\n      return this.$route.path\n    },\n    // 用户角色文本\n    userRoleText() {\n      // 首先尝试使用Vuex中的角色标志\n      if (this.isAdmin) return '管理员'\n      if (this.isDoctor) return '标注医生'\n      if (this.isReviewer) return '审核医生'\n      \n      // 如果Vuex中的角色判断失效，直接从localStorage获取\n      try {\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        if (user && user.role) {\n          // 根据用户角色返回对应文本\n          switch(user.role) {\n            case 'ADMIN': return '管理员';\n            case 'DOCTOR': return '标注医生';\n            case 'REVIEWER': return '审核医生';\n            default: return '未知角色';\n          }\n        }\n      } catch (err) {\n        console.error('MainLayout: 获取用户角色失败:', err);\n      }\n      \n      return '未知角色'\n    },\n    // 根据角色返回不同的CSS类\n    roleTagClass() {\n      if (this.isAdmin) return 'role-tag-admin'\n      if (this.isReviewer) return 'role-tag-reviewer'\n      if (this.isDoctor) return 'role-tag-doctor'\n      \n      // 如果Vuex中的角色判断失效，直接从localStorage获取\n      try {\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        if (user && user.role) {\n          switch(user.role) {\n            case 'ADMIN': return 'role-tag-admin';\n            case 'REVIEWER': return 'role-tag-reviewer';\n            case 'DOCTOR': return 'role-tag-doctor';\n            default: return '';\n          }\n        }\n      } catch (err) {\n        console.error('MainLayout: 获取用户角色样式失败:', err);\n      }\n      \n      return ''\n    }\n  },\n  watch: {\n    $route(to) {\n      // Only show back button on specific routes\n      this.showBackButton = to.path === '/app/cases/structured-form';\n      \n      // Show dashboard button on all pages except dashboard\n      this.showDashboardButton = to.path !== '/app/dashboard';\n      \n      // 如果是进入Dashboard，检查用户是否变更\n      if (to.path === '/app/dashboard' || to.path === '/app' || to.path === '/app/') {\n        // 获取当前用户ID\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        const currentUserId = user.customId || user.id;\n        \n        // 如果存在lastUserId且与当前不同，说明用户已切换\n        const lastUserId = localStorage.getItem('lastActiveUserId');\n        if (lastUserId && lastUserId !== currentUserId) {\n          console.log('MainLayout: 检测到用户ID变更，从', lastUserId, '到', currentUserId);\n          \n          // 强制刷新统计数据\n          if (window.refreshDashboardStats) {\n            console.log('MainLayout: 用户变更，强制刷新统计数据');\n            setTimeout(() => window.refreshDashboardStats(), 0);\n          }\n        }\n        \n        // 更新最后活跃用户ID\n        if (currentUserId) {\n          localStorage.setItem('lastActiveUserId', currentUserId);\n        }\n      }\n    },\n    \n    // 监听用户名变化\n    username(newVal, oldVal) {\n      if (newVal !== oldVal && newVal && oldVal) {\n        console.log('MainLayout: 用户名变更，从', oldVal, '到', newVal);\n        \n        // 如果当前路径是Dashboard，强制刷新统计数据\n        if (this.$route.path === '/app/dashboard' || this.$route.path === '/app' || this.$route.path === '/app/') {\n          if (window.refreshDashboardStats) {\n            console.log('MainLayout: 用户名变更，立即刷新统计数据');\n            setTimeout(() => window.refreshDashboardStats(), 0);\n          }\n        }\n      }\n    }\n  },\n  created() {\n    // 获取当前用户信息\n    this.loadUserInfo()\n    \n    // 如果有团队，获取团队申请数量\n    this.fetchTeamApplicationsCount()\n  },\n  mounted() {\n    // 定时获取团队申请数量（每5分钟获取一次）\n    this.startPendingApplicationsPolling()\n  },\n  beforeUnmount() {\n    // 清除定时器\n    this.stopPendingApplicationsPolling()\n  },\n  methods: {\n    // 加载用户信息\n    loadUserInfo() {\n      try {\n        const user = JSON.parse(localStorage.getItem('user') || '{}')\n        if (user && user.name) {\n          this.username = user.name\n        }\n        \n        // 检查用户是否已申请成为审核医生\n        if (this.isDoctor) {\n          api.users.getReviewerApplicationStatus()\n            .then(response => {\n              if (response.data && response.data.status === 'PENDING') {\n                this.hasAppliedForReviewer = true\n              }\n            })\n            .catch(error => console.error('获取审核医生申请状态失败:', error))\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n      }\n    },\n    \n    // 获取团队申请数量\n    fetchTeamApplicationsCount() {\n      this.$store.dispatch('teamApplications/fetchPendingApplicationsCount')\n    },\n    \n    // 启动定时获取申请数量\n    startPendingApplicationsPolling() {\n      this.pollTimer = setInterval(() => {\n        this.fetchTeamApplicationsCount()\n      }, 5 * 60 * 1000) // 每5分钟更新一次\n    },\n    \n    // 停止定时获取\n    stopPendingApplicationsPolling() {\n      if (this.pollTimer) {\n        clearInterval(this.pollTimer)\n      }\n    },\n    handleLogout() {\n      console.log('[登出操作] 用户请求登出，当前URL:', window.location.href);\n      \n      // 清除所有导航状态标记\n      sessionStorage.removeItem('isAppOperation');\n      sessionStorage.removeItem('isNavigatingAfterSave');\n      sessionStorage.removeItem('returningToWorkbench');\n      \n      // 设置明确的注销标记，确保API拦截器可以识别这是登出操作\n      sessionStorage.setItem('isLogoutOperation', 'true');\n      console.log('[登出操作] 已设置登出标记，准备清除用户会话');\n      \n      // 清除用户会话\n      localStorage.removeItem('user');\n      \n      // 重定向到登录页\n      console.log('[登出操作] 导航到登录页');\n      this.$router.push('/login').then(() => {\n        console.log('[登出操作] 导航到登录页成功');\n      }).catch(err => {\n        console.error('[登出操作] 导航失败:', err);\n        // 备用导航方案\n        window.location.href = '/login';\n      });\n      \n      // 3秒后清除登出标记\n      setTimeout(() => {\n        console.log('[登出操作] 清除登出标记');\n        sessionStorage.removeItem('isLogoutOperation');\n      }, 3000);\n    },\n    goBack() {\n      // For all pages, ask if user wants to go back because they might lose data\n      this.$confirm('返回上一页将可能丢失当前未保存的数据，是否确认返回？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // Use browser history to go back, which is more reliable\n        this.$router.go(-1);\n      }).catch(() => {\n        // User cancelled, do nothing\n      });\n    },\n    // 检查是否已申请升级权限\n    checkReviewerApplication() {\n      // 只有标注医生需要检查\n      if (!this.isDoctor) {\n        return;\n      }\n      \n      // 从API获取当前用户的申请状态\n      api.users.getReviewerApplicationStatus()\n        .then(response => {\n          console.log('获取权限申请状态:', response.data);\n          this.hasAppliedForReviewer = response.data && response.data.length > 0 && \n            response.data.some(app => app.status === 'PENDING');\n        })\n        .catch(error => {\n          console.error('获取权限申请状态失败:', error);\n        });\n    },\n    \n    // 处理申请升级权限\n    handleApplyForReviewer() {\n      this.$prompt('请输入申请理由', '申请成为审核医生', {\n        confirmButtonText: '提交申请',\n        cancelButtonText: '取消',\n        inputType: 'textarea',\n        inputPlaceholder: '请详细说明您申请成为审核医生的理由...'\n      }).then(({ value }) => {\n        if (!value || value.trim() === '') {\n          this.$message.warning('申请理由不能为空');\n          return;\n        }\n        \n        // 提交申请\n        api.users.applyForReviewer(value)\n          .then(() => {\n            this.$message.success('申请已提交，请等待管理员审核');\n            this.hasAppliedForReviewer = true;\n          })\n          .catch(error => {\n            if (error.response && error.response.data && error.response.data.message) {\n              this.$message.error(error.response.data.message);\n            } else {\n              this.$message.error('申请提交失败，请稍后重试');\n            }\n          });\n      }).catch(() => {\n        // 用户取消输入，不做处理\n      });\n    },\n    \n    // 添加新的导航方法\n    navigateToHemangiomaDiagnosis() {\n      console.log('[导航] 准备导航到血管瘤诊断页面');\n      try {\n        this.$router.push('/app/hemangioma-diagnosis').then(() => {\n          console.log('[导航] 成功导航到血管瘤诊断页面');\n        }).catch(err => {\n          console.error('[导航] 导航到血管瘤诊断页面失败:', err);\n        });\n      } catch (e) {\n        console.error('[导航] 导航异常:', e);\n      }\n    },\n    navigateToProfile() {\n      console.log('[导航] 准备导航到个人中心页面');\n      // 使用正确的URL格式，不要包含/medical前缀\n      window.location.href = '/app/profile';\n    }\n  }\n}\n</script>\n\n<style>\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n}\n\n.layout-container {\n  height: 100vh;\n}\n\n.sidebar {\n  background-color: #001529;\n  height: 100%;\n  overflow-x: hidden;\n  z-index: 10;\n}\n\n.logo {\n  height: 64px;\n  line-height: 64px;\n  text-align: center;\n  font-size: 18px;\n  color: white;\n  font-weight: bold;\n  white-space: nowrap;\n  overflow: hidden;\n  margin-bottom: 16px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.sidebar-menu {\n  border-right: none !important;\n  width: 100%;\n}\n\n.header {\n  background-color: white;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);\n  z-index: 9;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n}\n\n.user-info .el-avatar {\n  margin-right: 8px;\n}\n\n.role-tag {\n  margin-left: 8px;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  color: white;\n}\n\n.role-tag-admin {\n  background-color: #f56c6c;\n}\n\n.role-tag-reviewer {\n  background-color: #e6a23c;\n}\n\n.role-tag-doctor {\n  background-color: #409eff;\n}\n\n/* 团队菜单项样式 */\n.team-menu-item {\n  position: relative;\n}\n\n/* 团队申请数量徽章样式 */\n.team-badge {\n  position: absolute !important;\n  top: 12px !important;\n  right: 20px !important;\n}\n\n.team-badge .el-badge__content {\n  height: 18px;\n  min-width: 18px;\n  line-height: 18px;\n  padding: 0 6px;\n  border-radius: 9px;\n  font-size: 12px;\n  font-weight: 500;\n  box-shadow: 0 0 0 1px #fff;\n  z-index: 11;\n}\n</style> ", "import { render } from \"./MainLayout.vue?vue&type=template&id=536c94e1\"\nimport script from \"./MainLayout.vue?vue&type=script&lang=js\"\nexport * from \"./MainLayout.vue?vue&type=script&lang=js\"\n\nimport \"./MainLayout.vue?vue&type=style&index=0&id=536c94e1&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\r\n  <el-container class=\"simple-layout\">\r\n    <el-main>\r\n      <router-view></router-view>\r\n    </el-main>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SimpleLayout'\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.simple-layout {\r\n  height: 100%;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.el-main {\r\n  padding: 0;\r\n  height: 100%;\r\n  overflow-y: auto;\r\n}\r\n</style> ", "import { render } from \"./SimpleLayout.vue?vue&type=template&id=47aa494e&scoped=true\"\nimport script from \"./SimpleLayout.vue?vue&type=script&lang=js\"\nexport * from \"./SimpleLayout.vue?vue&type=script&lang=js\"\n\nimport \"./SimpleLayout.vue?vue&type=style&index=0&id=47aa494e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-47aa494e\"]])\n\nexport default __exports__", "<template>\n  <div class=\"dashboard\">\n    <!-- 统计卡片 -->\n    <el-row :gutter=\"15\" class=\"stat-cards\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"24\" :xl=\"24\" style=\"margin-bottom: 15px;\">\n        <div class=\"header-actions\">\n          <h2 style=\"margin: 0;\">工作台</h2>\n          <el-button type=\"primary\" size=\"small\" @click=\"manualRefresh\">\n            <el-icon><Refresh /></el-icon> 刷新数据\n          </el-button>\n        </div>\n      </el-col>\n      <el-col :xs=\"12\" :sm=\"8\" :md=\"6\" :lg=\"6\" :xl=\"6\">\n        <el-card shadow=\"hover\" class=\"clickable-card\" @click=\"navigateTo('/app/cases')\">\n          <div class=\"stat-item\">\n            <div class=\"stat-title\">病例总数</div>\n            <div class=\"stat-value\">{{ dashboardStats.totalCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"12\" :sm=\"8\" :md=\"6\" :lg=\"6\" :xl=\"6\">\n        <el-card shadow=\"hover\" class=\"clickable-card\" @click=\"navigateTo('/app/cases', 'REVIEWED')\">\n          <div class=\"stat-item\">\n            <div class=\"stat-title\">已标注</div>\n            <div class=\"stat-value\">{{ dashboardStats.reviewedCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"12\" :sm=\"8\" :md=\"6\" :lg=\"6\" :xl=\"6\">\n        <el-card shadow=\"hover\" class=\"clickable-card\" @click=\"navigateTo('/app/cases', 'SUBMITTED')\">\n          <div class=\"stat-item\">\n            <div class=\"stat-title\">待审核</div>\n            <div class=\"stat-value\">{{ dashboardStats.submittedCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"12\" :sm=\"8\" :md=\"6\" :lg=\"6\" :xl=\"6\">\n        <el-card shadow=\"hover\" class=\"clickable-card\" @click=\"navigateTo('/app/cases', 'APPROVED')\">\n          <div class=\"stat-item\">\n            <div class=\"stat-title\">已通过</div>\n            <div class=\"stat-value\">{{ dashboardStats.approvedCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 快捷操作按钮 -->\n    <div class=\"quick-actions\">\n      <div class=\"table-header\">\n        <h2><span>快捷操作</span></h2>\n      </div>\n      \n      <el-row :gutter=\"20\">\n        <el-col :xs=\"24\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\n          <el-card shadow=\"hover\" class=\"action-card\" @click=\"navigateTo('/app/hemangioma-diagnosis')\">\n            <div class=\"action-content\">\n              <el-icon class=\"action-icon\"><Refresh /></el-icon>\n              <div class=\"action-title\">血管瘤诊断</div>\n              <div class=\"action-desc\">使用AI辅助诊断血管瘤</div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :xs=\"24\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\n          <el-card shadow=\"hover\" class=\"action-card\" @click=\"navigateTo('/app/teams')\">\n            <div class=\"action-content\">\n              <el-icon class=\"action-icon\"><UserFilled /></el-icon>\n              <div class=\"action-title\">团队标注</div>\n              <div class=\"action-desc\">查看团队标注信息</div>\n      </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters, mapActions } from 'vuex'\nimport axios from 'axios'\nimport { Loading, Refresh, Plus, UserFilled } from '@element-plus/icons-vue'\nimport { DASHBOARD_STATS_WITH_PARAMS } from '../config/api.config'\n\n// 全局缓存，保证统计数据可以即时显示\nlet globalStatsCache = {\n  totalCount: 0,\n  reviewedCount: 0,\n  submittedCount: 0,\n  approvedCount: 0,\n  rejectedCount: 0\n};\n\n// 恢复预加载函数，但修复为使用数字ID\nexport function preloadDashboardData() {\n  console.log('预加载Dashboard数据...');\n  try {\n    const userStr = localStorage.getItem('user') || '{}';\n    const user = JSON.parse(userStr);\n    // 只使用数字ID\n    const userId = user.id;\n    \n    if (!userId) {\n      console.warn('未获取到用户ID，跳过预加载');\n      return;\n    }\n    \n    const url = DASHBOARD_STATS_WITH_PARAMS(userId);\n    console.log('预加载仪表盘数据，URL:', url);\n    \n    axios.get(url, {\n      headers: {\n        'Cache-Control': 'no-cache',\n        'Pragma': 'no-cache',\n        'Expires': '0'\n      }\n    })\n    .then(response => {\n      if (response.data) {\n        globalStatsCache = {\n          totalCount: parseInt(response.data.totalCount || 0),\n          reviewedCount: parseInt(response.data.reviewedCount || 0),\n          submittedCount: parseInt(response.data.submittedCount || 0),\n          approvedCount: parseInt(response.data.approvedCount || 0),\n          rejectedCount: parseInt(response.data.rejectedCount || 0)\n        };\n        console.log('预加载数据更新完成');\n      }\n    })\n    .catch(err => console.error('预加载数据失败:', err));\n  } catch (e) {\n    console.error('预加载过程出错:', e);\n  }\n}\n\nexport default {\n  name: 'Dashboard',\n  components: {\n    Loading,\n    Refresh,\n    Plus,\n    UserFilled\n  },\n  data() {\n    return {\n      dashboardStats: { ...globalStatsCache }\n    }\n  },\n  computed: {\n    ...mapGetters({\n      currentUserId: 'getUserId'\n    }),\n  },\n  watch: {\n    $route(to, from) {\n      if (to.path === '/app/dashboard') {\n        console.log('【路由切换】切换到 dashboard，重新加载数据');\n        this.manualRefresh();\n      }\n    }\n  },\n\n  // --- Start of Lifecycle Hooks ---\n  \n  beforeRouteEnter(to, from, next) {\n    // 简化路由钩子，移除预加载调用\n    console.log('【路由前置】beforeRouteEnter');\n    next(vm => {\n      // 只调用一次刷新\n      console.log('【路由前置】进入组件，执行一次刷新');\n      vm.manualRefresh();\n    });\n  },\n\n  created() {\n    console.log('⭐⭐⭐ Dashboard组件 created: 组件被创建 ⭐⭐⭐');\n    this.dashboardStats = { totalCount: 0, reviewedCount: 0, submittedCount: 0, approvedCount: 0, rejectedCount: 0 };\n  },\n\n  mounted() {\n    console.log('⭐⭐⭐ Dashboard组件 mounted: 组件被挂载 ⭐⭐⭐');\n    // 移除这里的刷新调用，避免重复\n    // this.manualRefresh(); \n    \n    // 只保留页面获得焦点时的刷新，移除定时器\n    window.addEventListener('focus', this.handlePageFocus); // 页面获得焦点时刷新\n    console.log('焦点事件监听已设置');\n  },\n\n  activated() {\n    console.log('【keep-alive】Dashboard组件被激活');\n    // 组件被重新激活时不立即刷新，避免和beforeRouteEnter重复\n    // this.manualRefresh();\n  },\n\n  beforeUnmount() {\n    console.log('⭐⭐⭐ Dashboard组件 beforeUnmount: 组件即将卸载 ⭐⭐⭐');\n    // 清除所有事件监听，防止内存泄漏\n    window.removeEventListener('focus', this.handlePageFocus);\n    console.log('焦点事件监听已移除');\n  },\n\n  // --- End of Lifecycle Hooks ---\n\n  methods: {\n    ...mapActions(['resetState']),\n\n    navigateTo(path, status) {\n      const query = status ? { status } : {};\n      localStorage.setItem('lastSelectedStatus', status || 'ALL');\n      this.$router.push({ path, query });\n    },\n    \n    formatDate(date) {\n      if (!date) return '';\n      return new Date(date).toLocaleString();\n    },\n\n    handlePageFocus() {\n      console.log('页面获得焦点，强制获取最新数据');\n      this.manualRefresh();\n    },\n\n    async manualRefresh() {\n      console.log('【手动刷新】开始...');\n      \n      // 修改为只获取数字ID\n      let userId;\n      try {\n        // 先尝试从Vuex获取，这会返回数字ID\n        userId = this.currentUserId;\n        \n        // 如果Vuex中没有，则从localStorage获取\n        if (!userId) {\n          const userStr = localStorage.getItem('user');\n          if (userStr) {\n            const user = JSON.parse(userStr);\n            // 只使用id（数字ID）\n            userId = user.id;\n          }\n        }\n      } catch (e) {\n        console.error('解析用户信息失败:', e);\n      }\n      \n      if (!userId) {\n        console.error('无法获取有效的用户ID，可能用户未登录');\n        this.$message.error('无法获取用户ID，请重新登录');\n        return;\n      }\n      \n      console.log('【手动刷新】使用用户ID:', userId);\n\n      const loading = this.$loading({ lock: true, text: '正在获取最新数据...' });\n      try {\n        const url = DASHBOARD_STATS_WITH_PARAMS(userId);\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: { 'Cache-Control': 'no-cache' },\n          credentials: 'include'\n        });\n\n        if (!response.ok) throw new Error(`HTTP错误! 状态: ${response.status}`);\n        \n        const data = await response.json();\n        console.log('【手动刷新】获取到的统计数据:', data);\n        \n        this.dashboardStats = {\n          totalCount: parseInt(data.totalCount || 0),\n          reviewedCount: parseInt(data.reviewedCount || 0),\n          submittedCount: parseInt(data.submittedCount || 0),\n          approvedCount: parseInt(data.approvedCount || 0),\n          rejectedCount: parseInt(data.rejectedCount || 0)\n        };\n        globalStatsCache = { ...this.dashboardStats };\n        this.$forceUpdate();\n        \n      } catch (error) {\n        console.error('【手动刷新】失败:', error);\n        this.$message.error(`刷新失败: ${error.message}`);\n      } finally {\n        loading.close();\n      }\n    },\n  },\n}\n</script>\n\n<style scoped>\n.dashboard {\n  padding: 20px;\n}\n.stat-cards {\n  margin-bottom: 24px;\n  width: 100%;\n}\n.clickable-card {\n  cursor: pointer;\n  transition: transform 0.3s, box-shadow 0.3s;\n  height: 100%;\n  margin-bottom: 15px;\n}\n.clickable-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\n}\n.stat-item {\n  text-align: center;\n}\n.stat-title {\n  font-size: 16px;\n  color: #666;\n  margin-bottom: 10px;\n}\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n.quick-actions {\n  background: #fff;\n  padding: 24px;\n  border-radius: 4px;\n  margin-bottom: 20px;\n}\n.table-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n.table-header h2 {\n  margin: 0;\n  font-size: 18px;\n  color: #333;\n}\n.action-card {\n  cursor: pointer;\n  transition: transform 0.3s, box-shadow 0.3s;\n  height: 100%;\n  margin-bottom: 15px;\n}\n.action-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\n}\n.action-content {\n  text-align: center;\n  padding: 20px 0;\n}\n.action-icon {\n  font-size: 36px;\n  color: #409EFF;\n  margin-bottom: 10px;\n}\n.action-title {\n  font-size: 18px;\n  font-weight: bold;\n  margin-bottom: 5px;\n}\n.action-desc {\n  font-size: 14px;\n  color: #606266;\n}\n.header-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n</style> ", "import { render } from \"./Dashboard.vue?vue&type=template&id=dcc088ae&scoped=true\"\nimport script from \"./Dashboard.vue?vue&type=script&lang=js\"\nexport * from \"./Dashboard.vue?vue&type=script&lang=js\"\n\nimport \"./Dashboard.vue?vue&type=style&index=0&id=dcc088ae&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-dcc088ae\"]])\n\nexport default __exports__", "import api from '../../utils/api';\nimport { hasPermission, canAccessRoute, canAccessResource } from '../../utils/permissions';\nimport { storageUtils, asyncActionHandler } from '@/utils/storeHelpers';\n\nconst state = {\n  user: storageUtils.getFromStorage('user') || null,\n  isAuthenticated: !!storageUtils.getFromStorage('user'),\n  users: [], // 添加用户列表数据，整合users.js功能\n  currentUserDetails: null, // 添加当前查看的用户详情\n  loading: false,\n  error: null\n}\n\nconst getters = {\n  getUser: state => state.user,\n  // 获取用户角色\n  getUserRole: state => state.user ? state.user.role : null,\n  // 获取用户ID - 确保只返回数字ID\n  getUserId: state => state.user ? state.user.id : null,\n  // 判断是否为管理员\n  isAdmin: state => state.user && state.user.role === 'ADMIN',\n  // 判断是否为标注医生\n  isDoctor: state => state.user && state.user.role === 'DOCTOR',\n  // 判断是否为审核医生\n  isReviewer: state => state.user && state.user.role === 'REVIEWER',\n  // 检查是否有特定权限\n  hasPermission: state => permission => {\n    return hasPermission(state.user?.role, permission)\n  },\n  // 检查是否可以访问特定路由\n  canAccessRoute: state => route => {\n    return canAccessRoute(state.user?.role, route)\n  },\n  // 检查是否可以访问特定资源\n  canAccessResource: state => resourceOwnerId => {\n    if (!state.user) return false\n    return canAccessResource(state.user.id, resourceOwnerId, state.user.role)\n  },\n  // 添加用户列表的getter (从users.js)\n  getAllUsers: state => state.users,\n  getCurrentUserDetails: state => state.currentUserDetails,\n  isUsersLoading: state => state.loading,\n  getUsersError: state => state.error\n}\n\nconst mutations = {\n  setUser(state, user) {\n    state.user = user\n    state.isAuthenticated = !!user\n  },\n  // 添加用户列表相关的mutations (从users.js)\n  setUsers(state, users) {\n    state.users = users\n  },\n  setCurrentUserDetails(state, user) {\n    state.currentUserDetails = user\n  },\n  setLoading(state, status) {\n    state.loading = status\n  },\n  setError(state, error) {\n    state.error = error\n  }\n}\n\nconst actions = {\n  // 登录\n  async login({ commit }, credentials) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      console.log('登录请求:', credentials);\n      \n      // 移除硬编码的用户处理逻辑，完全依赖API返回的用户信息\n      const response = await api.auth.login(credentials);\n      \n      // 打印原始响应\n      console.log('登录API原始响应:', response);\n      \n      // 安全获取用户数据\n      const rawData = response.data;\n      console.log('登录成功，原始用户数据:', rawData);\n      \n      // 直接获取顶层属性，避免深层解析导致的循环引用问题\n      const user = {\n        id: rawData.id,\n        customId: rawData.customId,\n        name: rawData.name,\n        email: rawData.email,\n        role: rawData.role,\n        department: rawData.department,\n        hospital: rawData.hospital\n      };\n      \n      // 如果有team属性，只提取必要信息\n      if (rawData.team) {\n        try {\n          user.team = {\n            id: rawData.team.id,\n            name: rawData.team.name,\n            description: rawData.team.description\n          };\n        } catch (e) {\n          console.warn('提取team信息时出错:', e);\n          user.team = null;\n        }\n      } else {\n        user.team = null;\n      }\n      \n      console.log('处理后的用户数据:', user);\n      console.log('用户信息详情 - ID:', user.id, '姓名:', user.name, '角色:', user.role, 'customId:', user.customId);\n      \n      // 确保用户数据完整\n      if (!user.name) {\n        console.warn('警告: 登录响应中缺少用户姓名!');\n        // 尝试从email或其他属性中获取一个默认名称\n        if (user.email) {\n          user.name = user.email.split('@')[0];\n          console.log('使用邮箱用户名作为默认名称:', user.name);\n        } else {\n          user.name = '未命名用户';\n        }\n      }\n      \n      // 检查用户角色是否正确\n      if (!user.role) {\n        console.error('错误: 用户角色为空!');\n        // 根据用户ID判断角色 - 使用通用逻辑，不再硬编码特定ID\n        if (user.customId && user.customId.startsWith('1')) {\n          user.role = 'ADMIN';\n        } else if (user.customId && user.customId.startsWith('3')) {\n          user.role = 'REVIEWER';\n        } else {\n          user.role = 'DOCTOR';\n        }\n        console.log('已根据用户ID设置默认角色:', user.role);\n      }\n      \n      // 保存到本地存储\n      storageUtils.saveToStorage('user', user);\n      \n      // 更新状态\n      commit('setUser', user);\n      \n      return user;\n    } catch (error) {\n      console.error('登录错误详情:', error);\n      return asyncActionHandler.error(commit, error);\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 注册\n  async register({ commit }, userData) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      const response = await api.auth.register(userData);\n      return response.data;\n    } catch (error) {\n      return asyncActionHandler.error(commit, error);\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 注销\n  logout({ commit }) {\n    storageUtils.removeFromStorage('user');\n    commit('setUser', null);\n  },\n  \n  // 获取用户信息 - 重命名为fetchUserProfile以区分功能\n  async fetchUserProfile({ commit }, userId) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      const response = await api.users.getUser(userId);\n      commit('setCurrentUserDetails', response.data);\n      return response.data;\n    } catch (error) {\n      return asyncActionHandler.error(commit, error);\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 更新用户信息\n  async updateUserProfile({ commit, state }, userData) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      const response = await api.users.updateUser(state.user.id, userData);\n      const updatedUser = response.data;\n      \n      // 更新本地存储\n      storageUtils.saveToStorage('user', updatedUser);\n      \n      // 更新状态\n      commit('setUser', updatedUser);\n      \n      return updatedUser;\n    } catch (error) {\n      return asyncActionHandler.error(commit, error);\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 从users.js整合的功能\n  // 获取用户列表\n  async fetchUsers({ commit }) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      const response = await api.users.getAll();\n      commit('setUsers', response.data);\n      return response.data;\n    } catch (error) {\n      return asyncActionHandler.error(commit, error);\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  }\n}\n\nexport default {\n  state,\n  getters,\n  mutations,\n  actions\n} ", "import axios from 'axios'\nimport { API_URL } from '../../config/api.config'\nimport api from '@/utils/api'\nimport { asyncActionHandler } from '@/utils/storeHelpers'\n\n// 添加认证头\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user'))\n  return user ? { Authorization: `Basic ${btoa(`${user.email}:${user.password}`)}` } : {}\n}\n\nconst state = {\n  images: [],\n  currentImage: null,\n  loading: false,\n  error: null\n}\n\nconst getters = {\n  getAllImages: state => state.images,\n  getCurrentImage: state => state.currentImage,\n  isImagesLoading: state => state.loading,\n  getImagesError: state => state.error\n}\n\nconst mutations = {\n  setImages(state, images) {\n    state.images = images\n  },\n  setCurrentImage(state, image) {\n    state.currentImage = image\n  },\n  setLoading(state, status) {\n    state.loading = status\n  },\n  setError(state, error) {\n    state.error = error\n  },\n  addImage(state, image) {\n    state.images.unshift(image)\n  },\n  updateImage(state, updatedImage) {\n    const index = state.images.findIndex(img => img.id === updatedImage.id)\n    if (index !== -1) {\n      state.images.splice(index, 1, updatedImage)\n    }\n    if (state.currentImage && state.currentImage.id === updatedImage.id) {\n      state.currentImage = updatedImage\n    }\n  },\n  removeImage(state, imageId) {\n    state.images = state.images.filter(img => img.id !== imageId)\n    if (state.currentImage && state.currentImage.id === imageId) {\n      state.currentImage = null\n    }\n  }\n}\n\nconst actions = {\n  // 获取所有图像\n  async fetchImages({ commit }) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      console.log('开始从API获取图像数据...')\n      const response = await axios.get(`${API_URL}/images`, { headers: getAuthHeader() })\n      console.log('API响应成功, 状态码:', response.status)\n      \n      // 确保返回的数据是数组\n      let images = []\n      if (Array.isArray(response.data)) {\n        console.log('响应数据是数组，长度:', response.data.length)\n        images = response.data\n        // 记录前三条数据示例\n        if (images.length > 0) {\n          console.log('数据样例:', images.slice(0, 3))\n        }\n      } else if (typeof response.data === 'object' && response.data !== null) {\n        console.log('响应不是数组，尝试提取数组数据')\n        // 常见的REST API响应格式\n        if (Array.isArray(response.data.content)) {\n          images = response.data.content\n        } else if (Array.isArray(response.data.data)) {\n          images = response.data.data\n        } else if (Array.isArray(response.data.items)) {\n          images = response.data.items\n        } else {\n          // 如果都不是，尝试转换对象为数组\n          images = Object.values(response.data).filter(item => item && typeof item === 'object')\n        }\n      }\n      \n      console.log('处理后的图像数据数量:', images.length)\n      commit('setImages', images)\n      return images\n    } catch (error) {\n      console.error('获取图像数据失败:', error)\n      asyncActionHandler.error(commit, error);\n      throw error\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 按状态获取图像\n  async fetchImagesByStatus({ commit }, status) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      const response = await axios.get(`${API_URL}/images/status/${status}`, { headers: getAuthHeader() })\n      // 确保返回的数据是数组\n      const images = Array.isArray(response.data) ? response.data : []\n      commit('setImages', images)\n      return images\n    } catch (error) {\n      asyncActionHandler.error(commit, error);\n      throw error\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 获取单个图像详情\n  async fetchImageById({ commit }, id) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      const response = await axios.get(`${API_URL}/images/${id}`, { headers: getAuthHeader() })\n      commit('setCurrentImage', response.data)\n      return response.data\n    } catch (error) {\n      asyncActionHandler.error(commit, error);\n      throw error\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 创建新图像\n  async createImage({ commit }, imageData) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      const response = await axios.post(`${API_URL}/images`, imageData, { headers: getAuthHeader() })\n      commit('addImage', response.data)\n      return response.data\n    } catch (error) {\n      asyncActionHandler.error(commit, error);\n      throw error\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 更新图像\n  async updateImage({ commit }, { id, imageData }) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      const response = await axios.put(`${API_URL}/images/${id}`, imageData, { headers: getAuthHeader() })\n      commit('updateImage', response.data)\n      return response.data\n    } catch (error) {\n      asyncActionHandler.error(commit, error);\n      throw error\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 更新图像状态\n  async updateImageStatus({ commit }, { id, status, reviewerId, reviewNotes }) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      await axios.put(\n        `${API_URL}/images/${id}/status`, \n        null, \n        { \n          params: { status, reviewerId, reviewNotes },\n          headers: getAuthHeader() \n        }\n      )\n      \n      // 获取更新后的图像数据\n      const response = await axios.get(`${API_URL}/images/${id}`, { headers: getAuthHeader() })\n      commit('updateImage', response.data)\n      return response.data\n    } catch (error) {\n      asyncActionHandler.error(commit, error);\n      throw error\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 删除图像\n  async deleteImage({ commit }, id) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      // 使用封装的API客户端而不是直接使用axios\n      await api.images.delete(id)\n      commit('removeImage', id)\n      return { success: true }\n    } catch (error) {\n      let errorMessage = '删除失败';\n      \n      if (error.response && error.response.data) {\n        // 如果后端返回了详细的错误信息\n        errorMessage = typeof error.response.data === 'string' \n          ? error.response.data \n          : JSON.stringify(error.response.data);\n      } else {\n        errorMessage += ': ' + (error.message || '未知错误');\n      }\n      \n      commit('setError', errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 获取统计数据 - 使用stats模块而不是直接调用API\n  async fetchStats({ dispatch, rootState, commit }) {\n    // 委托给stats模块处理\n    return dispatch('fetchStats', null, { root: true });\n  }\n}\n\nexport default {\n  state,\n  getters,\n  mutations,\n  actions\n} ", "// 简化的Vuex用户模块 - 调用auth模块来保持向后兼容性\n\nconst state = {\n  // 保持状态为空，实际使用auth模块的状态\n}\n\nconst getters = {\n  // 所有getter重定向到auth模块\n  getAllUsers: (state, getters, rootState) => rootState.auth.users,\n  getCurrentUser: (state, getters, rootState) => rootState.auth.currentUserDetails,\n  isUsersLoading: (state, getters, rootState) => rootState.auth.loading,\n  getUsersError: (state, getters, rootState) => rootState.auth.error\n}\n\nconst mutations = {\n  // 不需要本地mutation，使用auth模块的mutation\n}\n\nconst actions = {\n  // 获取用户列表 - 调用auth模块\n  async fetchUsers({ dispatch }) {\n    return dispatch('auth/fetchUsers', null, { root: true });\n  },\n  \n  // 获取单个用户 - 调用auth模块\n  async fetchUser({ dispatch }, userId) {\n    return dispatch('auth/fetchUserProfile', userId, { root: true });\n  }\n}\n\nexport default {\n  state,\n  getters,\n  mutations,\n  actions\n} ", "// Vuex统计数据模块\nimport api from '@/utils/api'\nimport { storageUtils, asyncActionHandler } from '@/utils/storeHelpers'\n\nconst state = {\n  stats: {\n    totalCount: 0,\n    draftCount: 0,\n    reviewedCount: 0,\n    submittedCount: 0,\n    approvedCount: 0,\n    rejectedCount: 0\n  },\n  loading: false,\n  error: null\n}\n\nconst getters = {\n  getStats: state => state.stats,\n  isStatsLoading: state => state.loading,\n  getStatsError: state => state.error,\n  // 增加兼容images模块的getter\n  getImagesStats: state => state.stats\n}\n\nconst mutations = {\n  setStats(state, stats) {\n    console.log('Vuex setStats 被调用，原始数据:', stats);\n    \n    // 确保所有必要的字段都存在，并转换为数字\n    state.stats = {\n      totalCount: parseInt(stats.totalCount) || 0,\n      draftCount: parseInt(stats.draftCount) || 0,\n      reviewedCount: parseInt(stats.reviewedCount || stats.pendingCount) || 0,\n      submittedCount: parseInt(stats.submittedCount) || 0,\n      approvedCount: parseInt(stats.approvedCount) || 0,\n      rejectedCount: parseInt(stats.rejectedCount) || 0\n    };\n    \n    console.log('Vuex stats 更新为:', state.stats);\n    \n    // 使用新的存储工具保存到本地存储\n    storageUtils.saveToStorage('dashboardStats', state.stats);\n  },\n  setLoading(state, status) {\n    state.loading = status\n  },\n  setError(state, error) {\n    state.error = error\n  },\n  // 添加直接设置某个状态值的方法\n  setStatField(state, { field, value }) {\n    if (state.stats.hasOwnProperty(field)) {\n      state.stats[field] = parseInt(value) || 0;\n      console.log(`更新统计字段 ${field} = ${state.stats[field]}`);\n    } else {\n      console.error(`尝试设置未知统计字段: ${field}`);\n    }\n  }\n}\n\nconst actions = {\n  // 获取统计数据\n  async fetchStats({ commit }) {\n    asyncActionHandler.start(commit);\n    \n    try {\n      console.log('===== Vuex fetchStats Action 开始执行 =====');\n      \n      // 从'user'对象中获取ID，只使用数字ID (user.id)\n      let userId = null;\n      const user = storageUtils.getFromStorage('user');\n      if (user) {\n        userId = user.id;\n      }\n\n      if (!userId) {\n        throw new Error('未在localStorage中找到用户ID');\n      }\n      console.log('使用的用户ID:', userId);\n      \n      console.log('开始调用API获取仪表盘统计数据');\n      const response = await api.stats.getDashboard(userId);\n      console.log('API响应对象:', response);\n      console.log('API响应状态码:', response.status);\n      console.log('API响应数据类型:', typeof response.data);\n      console.log('API响应数据:', response.data);\n      \n      if (response && response.data) {\n        // 对每个字段进行详细日志记录\n        const data = response.data;\n        console.log('统计字段详情:');\n        console.log('- totalCount:', data.totalCount, typeof data.totalCount);\n        console.log('- draftCount:', data.draftCount, typeof data.draftCount);\n        console.log('- reviewedCount:', data.reviewedCount, typeof data.reviewedCount);\n        console.log('- submittedCount:', data.submittedCount, typeof data.submittedCount);\n        console.log('- approvedCount:', data.approvedCount, typeof data.approvedCount);\n        console.log('- rejectedCount:', data.rejectedCount, typeof data.rejectedCount);\n        \n        console.log('准备调用setStats mutation');\n        commit('setStats', response.data);\n        console.log('setStats mutation执行完毕');\n        return response.data;\n      } else {\n        throw new Error('响应中没有数据');\n      }\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      \n      // 使用通用错误处理\n      asyncActionHandler.error(commit, error);\n      \n      // 使用默认值\n      const defaultStats = {\n        totalCount: 0, \n        draftCount: 0,\n        reviewedCount: 0,\n        submittedCount: 0,\n        approvedCount: 0,\n        rejectedCount: 0\n      };\n      \n      console.log('由于错误，设置默认统计数据:', defaultStats);\n      commit('setStats', defaultStats);\n    } finally {\n      console.log('===== Vuex fetchStats Action 执行完毕 =====');\n      asyncActionHandler.end(commit);\n    }\n  },\n  \n  // 强制设置统计数据（用于手动更新）\n  setManualStats({ commit }, statsData) {\n    console.log('手动设置统计数据:', statsData);\n    commit('setStats', statsData);\n  }\n}\n\nexport default {\n  state,\n  getters,\n  mutations,\n  actions\n} ", "import { storageUtils } from '@/utils/storeHelpers';\n\nconst state = {\n  step: 0, // 0: 未开始, 1: 图像标注, 2: 病例信息填写\n  imageId: null,\n  formData: null,\n  testMode: false,\n  imagePath: null,  // 添加imagePath属性保存图片路径，支持离线模式\n  lastUpdated: null\n};\n\nconst getters = {\n  getAnnotationProgress: state => {\n    return {\n      step: state.step,\n      imageId: state.imageId,\n      formData: state.formData,\n      testMode: state.testMode,\n      imagePath: state.imagePath,\n      lastUpdated: state.lastUpdated\n    };\n  },\n  hasUnfinishedAnnotation: state => {\n    return state.step > 0 && state.imageId !== null;\n  },\n  getCurrentStep: state => state.step,\n  getImageId: state => state.imageId,\n  getFormData: state => state.formData,\n  isTestMode: state => state.testMode,\n  getImagePath: state => state.imagePath, // 获取图片路径的getter\n  getStructuredFormProgress: state => {\n    return {\n      formData: state.formData\n    };\n  }\n};\n\nconst actions = {\n  saveProgress({ commit }, progress) {\n    commit('SET_PROGRESS', progress);\n  },\n  completeAnnotation({ commit }) {\n    commit('COMPLETE_ANNOTATION');\n  },\n  updateFormData({ commit }, formData) {\n    commit('UPDATE_FORM_DATA', formData);\n  },\n  // 从index.js添加的兼容方法\n  saveAnnotationProgress({ commit }, { step, imageId, formData }) {\n    commit('SET_PROGRESS', { step, imageId, formData });\n  },\n  clearAnnotationProgress({ commit }) {\n    commit('COMPLETE_ANNOTATION');\n  }\n};\n\nconst mutations = {\n  SET_PROGRESS(state, progress) {\n    state.step = progress.step || state.step;\n    state.imageId = progress.imageId || state.imageId;\n    \n    // 保存表单数据\n    if (progress.formData) {\n      // 如果传入的是完整的formData对象，进行简化处理\n      // 这部分逻辑从index.js中的saveAnnotationProgress迁移而来\n      let simplifiedFormData = null;\n      if (progress.formData) {\n        // 过滤掉大型对象属性或代理对象，只保留简单数据\n        simplifiedFormData = {};\n        Object.keys(progress.formData).forEach(key => {\n          // 检查是否为代理对象或复杂对象\n          const value = progress.formData[key];\n          if (typeof value !== 'object' || value === null) {\n            simplifiedFormData[key] = value;\n          } else if (Array.isArray(value)) {\n            // 对于数组，保留其字符串值\n            simplifiedFormData[key] = value.map(item => \n              typeof item === 'string' ? item : JSON.stringify(item)\n            );\n          } else {\n            // 对于对象，转换为字符串\n            try {\n              simplifiedFormData[key] = JSON.stringify(value);\n            } catch(e) {\n              console.warn('无法序列化表单数据字段:', key);\n            }\n          }\n        });\n      }\n      \n      state.formData = simplifiedFormData || progress.formData;\n    }\n    \n    // 标记测试模式\n    if (typeof progress.testMode !== 'undefined') {\n      state.testMode = progress.testMode;\n    }\n    \n    // 保存图片路径\n    if (progress.imagePath) {\n      state.imagePath = progress.imagePath;\n    }\n    \n    // 离线模式也同步保存到localStorage\n    if ((progress.testMode || localStorage.getItem('offlineMode') === 'true') && progress.imageId) {\n      try {\n        storageUtils.saveToStorage(`offline_progress_${progress.imageId}`, {\n          step: state.step,\n          formData: state.formData,\n          testMode: state.testMode,\n          imagePath: state.imagePath\n        });\n      } catch (e) {\n        console.error('保存离线进度失败', e);\n      }\n    }\n    \n    state.lastUpdated = new Date().toISOString();\n    console.log('保存标注进度:', state);\n  },\n  \n  UPDATE_FORM_DATA(state, formData) {\n    state.formData = formData;\n    state.lastUpdated = new Date().toISOString();\n    \n    // 离线模式也同步保存表单数据\n    if ((state.testMode || localStorage.getItem('offlineMode') === 'true') && state.imageId) {\n      try {\n        storageUtils.saveToStorage(`offline_form_${state.imageId}`, formData);\n        \n        // 同步更新进度\n        storageUtils.saveToStorage(`offline_progress_${state.imageId}`, {\n          step: state.step,\n          formData: formData,\n          testMode: state.testMode,\n          imagePath: state.imagePath\n        });\n      } catch (e) {\n        console.error('保存离线表单数据失败', e);\n      }\n    }\n  },\n  \n  COMPLETE_ANNOTATION(state) {\n    // 清除本地存储中的进度\n    if (state.imageId) {\n      try {\n        storageUtils.removeFromStorage(`offline_progress_${state.imageId}`);\n      } catch (e) {\n        console.error('清除离线进度失败', e);\n      }\n    }\n    \n    // 重置状态\n    state.step = 0;\n    state.imageId = null;\n    state.formData = null;\n    state.testMode = false;\n    state.imagePath = null;\n    state.lastUpdated = new Date().toISOString();\n    console.log('清除标注进度');\n  }\n};\n\nexport default {\n  state,\n  getters,\n  actions,\n  mutations\n}; ", "import api from '@/utils/api'\r\n\r\n// 团队申请模块\r\nconst state = {\r\n  // 待处理的申请数量\r\n  pendingCount: 0,\r\n  // 上次更新时间\r\n  lastUpdated: null\r\n}\r\n\r\nconst getters = {\r\n  // 获取待处理的申请数量\r\n  getPendingApplicationsCount: state => state.pendingCount,\r\n  \r\n  // 判断是否有待处理的申请\r\n  hasPendingApplications: state => state.pendingCount > 0,\r\n  \r\n  // 获取上次更新时间\r\n  getLastUpdated: state => state.lastUpdated\r\n}\r\n\r\nconst mutations = {\r\n  // 设置待处理的申请数量\r\n  setPendingCount(state, count) {\r\n    state.pendingCount = count\r\n    state.lastUpdated = new Date().toISOString()\r\n  },\r\n  \r\n  // 增加待处理的申请数量\r\n  incrementPendingCount(state) {\r\n    state.pendingCount++\r\n    state.lastUpdated = new Date().toISOString()\r\n  },\r\n  \r\n  // 减少待处理的申请数量\r\n  decrementPendingCount(state) {\r\n    if (state.pendingCount > 0) {\r\n      state.pendingCount--\r\n      state.lastUpdated = new Date().toISOString()\r\n    }\r\n  },\r\n  \r\n  // 重置待处理的申请数量\r\n  resetPendingCount(state) {\r\n    state.pendingCount = 0\r\n    state.lastUpdated = new Date().toISOString()\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  // 获取待处理的申请数量\r\n  async fetchPendingApplicationsCount({ commit, rootState }) {\r\n    try {\r\n      // 获取当前用户的团队ID\r\n      const user = rootState.auth.user || JSON.parse(localStorage.getItem('user') || '{}')\r\n      const teamId = user.team?.id\r\n      \r\n      if (!teamId) {\r\n        console.log('用户不属于任何团队，无需获取团队申请数量')\r\n        commit('resetPendingCount')\r\n        return\r\n      }\r\n      \r\n      // 调用API获取团队待处理申请\r\n      const response = await api.teams.getTeamApplications(teamId, 'PENDING')\r\n      \r\n      if (response && response.data) {\r\n        // 设置待处理的申请数量\r\n        commit('setPendingCount', response.data.length)\r\n        console.log(`团队${teamId}有${response.data.length}条待处理申请`)\r\n      } else {\r\n        commit('resetPendingCount')\r\n      }\r\n    } catch (error) {\r\n      console.error('获取团队申请数量失败:', error)\r\n      // 错误情况下，重置计数器以避免显示错误信息\r\n      commit('resetPendingCount')\r\n    }\r\n  },\r\n  \r\n  // 处理团队申请后更新计数\r\n  processApplication({ commit }, { action }) {\r\n    if (action === 'APPROVED' || action === 'REJECTED') {\r\n      commit('decrementPendingCount')\r\n    }\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  getters,\r\n  actions,\r\n  mutations\r\n} ", "import { createStore } from 'vuex'\nimport createPersistedState from 'vuex-persistedstate'\nimport auth from './modules/auth'\nimport images from './modules/images'\nimport users from './modules/users'\nimport stats from './modules/stats'\nimport annotation from './modules/annotation'\nimport teamApplications from './modules/teamApplications' // 添加团队申请模块\nimport { storageUtils } from '@/utils/storeHelpers'\n\n// 创建空的状态对象，所有功能都已移至相应的模块\nexport default createStore({\n  state: {\n    // 已移至annotation模块\n  },\n  getters: {\n    isAuthenticated: state => state.auth.isAuthenticated,\n  },\n  mutations: {\n    // 为了保持向后兼容性，提供与旧代码相同的接口，但实际委托给annotation模块\n    saveAnnotationProgress(state, payload) {\n      // 不在此处实现，只是一个调用annotation模块的接口\n    },\n    clearAnnotationProgress(state) {\n      // 不在此处实现，只是一个调用annotation模块的接口\n    }\n  },\n  actions: {\n    // 保存当前标注进度 - 委托给annotation模块\n    saveProgress({ dispatch }, payload) {\n      return dispatch('annotation/saveAnnotationProgress', payload);\n    },\n    // 完成标注并清除进度 - 委托给annotation模块\n    completeAnnotation({ dispatch }) {\n      return dispatch('annotation/clearAnnotationProgress');\n    }\n  },\n  modules: {\n    auth,\n    images,\n    users,\n    stats,\n    annotation,\n    teamApplications // 添加团队申请模块到Vuex存储中\n  },\n  plugins: [\n    createPersistedState({\n      paths: ['auth', 'annotation', 'teamApplications'],\n      storage: {\n        getItem: key => {\n          try {\n            return localStorage.getItem(key);\n          } catch (err) {\n            console.error('localStorage getItem error:', err);\n            return null;\n          }\n        },\n        setItem: (key, value) => {\n          try {\n            // 如果数据过大，先尝试清理一些旧数据\n            if (value && value.length > 1000000) {\n              console.warn('数据过大，尝试清理localStorage');\n              localStorage.removeItem('vuex');\n            }\n            localStorage.setItem(key, value);\n          } catch (err) {\n            console.error('localStorage setItem error:', err);\n            // 如果失败，尝试清理localStorage后再保存核心数据\n            try {\n              for (let i = 0; i < localStorage.length; i++) {\n                const key = localStorage.key(i);\n                if (key !== 'user' && key !== 'token') {\n                  localStorage.removeItem(key);\n                }\n              }\n              localStorage.setItem(key, value);\n            } catch (e) {\n              console.error('无法保存数据到localStorage，即使在清理后:', e);\n            }\n          }\n        },\n        removeItem: key => {\n          try {\n            localStorage.removeItem(key);\n          } catch (err) {\n            console.error('localStorage removeItem error:', err);\n          }\n        }\n      }\n    })\n  ]\n}) ", "import { createRouter, createWebHistory } from 'vue-router'\nimport MainLayout from '@/components/layout/MainLayout.vue'\nimport SimpleLayout from '@/components/layout/SimpleLayout.vue'\nimport Dashboard from '@/views/Dashboard.vue'\nimport store from '../store'\nimport { canAccessRoute } from '../utils/permissions'\nimport { preloadDashboardData } from '@/views/Dashboard.vue'\nimport { API_BASE_URL, DASHBOARD_STATS_URL } from '../config/api.config'\n\n// 导入调试页面组件\nconst DebugView = () => import('../views/DebugView.vue');\n// 预先导入血管瘤诊断组件，避免懒加载问题\nconst HemangiomaDiagnosis = () => import('../views/HemangiomaDiagnosis.vue');\n\nconst routes = [\n  {\n    path: '/app',\n    component: MainLayout,\n    meta: { requiresAuth: true },\n    children: [\n      {\n        path: '',\n        redirect: '/app/dashboard'\n      },\n      {\n        path: 'dashboard',\n        name: 'Dashboard',\n        component: Dashboard,\n        meta: { \n          keepAlive: true,\n          title: '工作台'\n        }\n      },\n      {\n        path: 'cases',\n        name: 'Cases',\n        component: () => import('@/views/Cases.vue')\n      },\n\n\n      {\n        path: 'cases/view/:id',\n        name: 'ViewCase',\n        component: () => import('@/views/CaseView.vue')\n      },\n      // {\n      //   path: 'cases/form/:id',\n      //   name: 'CaseDetailForm',\n      //   component: () => import('@/views/CaseDetailForm.vue'),\n      //   props: true\n      // },\n      // {\n      //   path: 'cases/structured-form',\n      //   name: 'CaseStructuredForm',\n      //   component: () => import('@/views/CaseStructuredForm.vue')\n      // },\n      {\n        path: 'case/:id/annotate-and-form',\n        name: 'AnnotationAndForm',\n        component: () => import('@/views/AnnotationAndForm.vue'),\n        props: true,\n        children: [\n          {\n            path: '',\n            name: 'EmbeddedForm',\n            component: () => import('@/views/CaseStructuredForm.vue'),\n        props: true\n          }\n        ]\n      },\n      {\n        path: 'review',\n        name: 'Review',\n        component: () => import('@/views/Review.vue')\n      },\n      {\n        path: 'teams',\n        name: 'Teams',\n        component: () => import('@/views/Teams.vue')\n      },\n      {\n        path: 'teams/applications',\n        name: 'TeamApplications',\n        component: () => import('@/components/TeamApplicationManagement.vue'),\n        meta: {\n          requiresAuth: true,\n          title: '团队申请管理',\n          accessRoles: ['ADMIN', 'REVIEWER']\n        }\n      },\n      {\n        path: 'users',\n        name: 'Users',\n        component: () => import('@/views/Users.vue'),\n        meta: { \n          requiresAdmin: true,\n          title: '部门成员'\n        }\n      },\n      {\n        path: 'hemangioma-diagnosis',\n        name: 'HemangiomaDiagnosis',\n        component: HemangiomaDiagnosis,\n        meta: {\n          requiresAuth: true,\n          title: '血管瘤诊断',\n          accessRoles: ['ADMIN', 'DOCTOR', 'REVIEWER']\n        }\n      },\n      {\n        path: 'annotation-reviews',\n        name: 'AnnotationReviews',\n        component: () => import('@/views/AnnotationReview.vue'),\n        meta: {\n          requiresAuth: true,\n          title: '标注审核',\n          // 允许审核医生和管理员访问\n          accessRoles: ['ADMIN', 'REVIEWER']\n        }\n      },\n      {\n        path: 'profile',\n        name: 'UserProfile',\n        component: () => import('@/views/UserProfile.vue'),\n        meta: {\n          requiresAuth: true,\n          title: '个人中心',\n          accessRoles: ['ADMIN', 'DOCTOR', 'REVIEWER']\n        }\n      }\n    ]\n  },\n  {\n    path: '/',\n    name: 'Root',\n    redirect: '/login'\n  },\n  {\n    path: '/login',\n    name: 'Login',\n    component: () => import('@/views/Login.vue')\n  },\n  {\n    path: '/forgot-password',\n    name: 'ForgotPassword',\n    component: () => import('@/views/ForgotPassword.vue')\n  },\n  {\n    path: '/register',\n    name: 'Register',\n    component: () => import('../views/Register.vue')\n  },\n  {\n    path: '/images',\n    name: 'ImageList',\n    component: () => import('../views/ImageList.vue'),\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/images/:id',\n    name: 'ImageDetail',\n    component: () => import('../views/ImageDetail.vue'),\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/annotations',\n    name: 'ImageAnnotation',\n    component: () => import('@/views/AnnotationAndForm.vue')\n  },\n  {\n    path: '/cases/structured-form',\n    name: 'PublicCaseStructuredForm',\n    component: () => import('@/views/CaseStructuredForm.vue')\n  },\n  {\n    path: '/tag-test',\n    name: 'TagTest',\n    component: () => import('../components/TagTest.vue')\n  },\n  {\n    path: '/admin',\n    name: 'Admin',\n    component: () => import('../views/Admin.vue'),\n    meta: { requiresAuth: true, requiresAdmin: true }\n  },\n  {\n    path: '/admin/reviewer-applications',\n    name: 'ReviewerApplications',\n    component: () => import('../views/admin/ReviewerApplications.vue'),\n    meta: { requiresAuth: true, requiresAdmin: true }\n  },\n  {\n    path: '/standalone-review/:id',\n    component: SimpleLayout,\n    children: [\n      {\n        path: '',\n        name: 'ReviewStandalone',\n        component: () => import('@/views/AnnotationReview.vue'),\n        props: true\n      }\n    ],\n    meta: {\n      requiresAuth: true,\n      title: '标注审核',\n      accessRoles: ['ADMIN', 'REVIEWER']\n    }\n  },\n  {\n    path: '/app/debug',\n    name: 'debug',\n    component: DebugView,\n    meta: { \n      requiresAuth: true,\n      title: '标注调试工具'\n    }\n  },\n  {\n    path: '/combined-view/:caseId',\n    name: 'CombinedView',\n    component: () => import('@/views/CaseStructuredForm.vue'),\n    props: true\n  },\n  {\n    path: '/:pathMatch(.*)*',\n    redirect: '/login'\n  }\n]\n\n// 创建路由实例\nconst router = createRouter({\n  // 确保使用 history 模式\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n})\n\nrouter.beforeEach((to, from, next) => {\n  // 记录导航详情\n  console.log('[路由导航] 导航开始:', {\n    从: from.fullPath,\n    到: to.fullPath,\n    时间: new Date().toLocaleTimeString(),\n    query参数: to.query,\n    来源页面: document.referrer\n  });\n  \n  // 设置应用内操作标记\n  if (from.name) { // 如果from.name存在，说明是应用内导航\n    sessionStorage.setItem('isAppOperation', 'true');\n  }\n  \n  // 检查是否从表单页面保存后进行导航\n  const isNavigatingAfterSave = sessionStorage.getItem('isNavigatingAfterSave') === 'true';\n  const isNavigatingFromForm = sessionStorage.getItem('navigatingFromForm') === 'true';\n  \n  // 如果是从表单保存后导航，且目标是登录页面，则重定向到工作台\n  if ((isNavigatingAfterSave || isNavigatingFromForm) && to.path === '/login') {\n    console.log('检测到从表单页面保存后导航到登录页面，重定向到工作台');\n    sessionStorage.removeItem('isNavigatingAfterSave');\n    sessionStorage.removeItem('navigatingFromForm');\n    return next('/app/dashboard');\n  }\n  \n  // 公开页面列表 - 修复：移除需要认证的页面\n  const publicPages = ['/login', '/register', '/tag-test', '/forgot-password'];\n  const authRequired = !publicPages.includes(to.path) && !to.matched.some(record => publicPages.includes(record.path));\n  \n  // 获取用户信息\n  const user = JSON.parse(localStorage.getItem('user'))\n  const isAuthenticated = store.getters.isAuthenticated\n  \n  // 保存当前路由，用于登录后重定向\n  if (to.path !== '/login' && !isAuthenticated) {\n    sessionStorage.setItem('redirectPath', to.fullPath);\n  }\n  \n  // 如果即将访问工作台页面，提前预加载数据\n  if (to.path === '/app/dashboard' || to.path === '/app' || to.path === '/app/') {\n    // 移除独立调用，减少请求次数\n    // preloadDashboardData();\n    \n    // 添加：如果当前已登录，立即调用全局统计数据刷新函数\n    if (user && window.refreshDashboardStats) {\n      console.log('[路由导航] 检测到前往工作台，预先刷新统计数据');\n      // 移除这个全局刷新函数调用，减少冗余请求\n      // setTimeout(() => {\n      //  window.refreshDashboardStats();\n      // }, 0);\n    }\n  }\n  \n  // 1. 检查是否需要登录\n  if (authRequired && !user) {\n    return next('/login')\n  }\n  \n  // 2. 检查路由是否需要认证\n  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)\n  if (requiresAuth && !isAuthenticated) {\n    // 如果当前是在表单页面且设置了特殊标记\n    if (from.path.includes('/cases/structured-form') && sessionStorage.getItem('allowFormOperation') === 'true') {\n      console.log('表单操作中，即使未认证也允许完成此次导航');\n      sessionStorage.removeItem('allowFormOperation');  // 使用后移除标记\n      return next('/app/dashboard');  // 导航到工作台\n    }\n    \n    return next('/login')\n  }\n  \n  // 3. 检查路由是否需要特定权限\n  const userRole = user ? user.role : null\n  if (requiresAuth && userRole) {\n    // 添加调试日志\n    console.log('[路由导航] 权限检查:', {\n      目标路由: to.path,\n      用户角色: userRole,\n      路由权限要求: to.meta.accessRoles || '无特定角色要求',\n      是否需要管理员: to.matched.some(record => record.meta.requiresAdmin),\n      是否需要审核医生: to.matched.some(record => record.meta.requiresReviewer),\n      是否需要标注医生: to.matched.some(record => record.meta.requiresDoctor)\n    });\n\n    // 原有权限检查代码保持不变\n    const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)\n    const requiresReviewer = to.matched.some(record => record.meta.requiresReviewer)\n    const requiresDoctor = to.matched.some(record => record.meta.requiresDoctor)\n    \n    if (\n      (requiresAdmin && userRole !== 'ADMIN') ||\n      (requiresReviewer && userRole !== 'REVIEWER' && userRole !== 'ADMIN') ||\n      (requiresDoctor && userRole !== 'DOCTOR' && userRole !== 'ADMIN')\n    ) {\n      // 添加详细日志\n      console.warn(`[路由导航] 权限检查失败: 用户角色 ${userRole} 无权访问 ${to.path}, 重定向到工作台`);\n      return next('/app/dashboard')\n    }\n    \n    // 然后使用权限配置进行进一步检查\n    const hasAccess = canAccessRoute(userRole, to.path);\n    console.log(`[路由导航] 权限配置检查: 用户角色 ${userRole} ${hasAccess ? '有权' : '无权'}访问 ${to.path}`);\n    \n    if (!hasAccess) {\n      console.warn(`[路由导航] 权限配置检查失败: 用户角色 ${userRole} 无权访问 ${to.path}, 重定向到工作台`);\n      return next('/app/dashboard')\n    }\n  }\n  \n  // 4. 特定路由重定向\n  if (to.path === '/dashboard') {\n    return next('/app/dashboard')\n  } else if (to.path === '/cases') {\n    return next('/app/cases')\n  }\n  \n  // 5. 通过所有检查，允许访问\n  next()\n})\n\n// 添加全局后置钩子\nrouter.afterEach((to, from) => {\n  // 记录导航完成详情\n  console.log('[路由导航] 导航完成:', {\n    从: from.fullPath,\n    到: to.fullPath,\n    时间: new Date().toLocaleTimeString(),\n    当前URL: window.location.href,\n    导航状态: {\n      isAppOperation: sessionStorage.getItem('isAppOperation'),\n      isNavigatingAfterSave: sessionStorage.getItem('isNavigatingAfterSave'),\n      returningToWorkbench: sessionStorage.getItem('returningToWorkbench'),\n      isLogoutOperation: sessionStorage.getItem('isLogoutOperation')\n    }\n  });\n  \n  // 设置应用内操作标记\n  sessionStorage.setItem('isAppOperation', 'true');\n  \n  // 确保不在登录页面时，设置应用内操作状态\n  if (router.currentRoute.value.path !== '/login') {\n    sessionStorage.setItem('isAppOperation', 'true');\n  }\n  \n  // 添加进入Dashboard页面时的数据刷新逻辑\n  if (to.path === '/app/dashboard' || to.path === '/app/' || to.path === '/app') {\n    console.log('[路由导航] 检测到进入工作台页面，立即执行统计数据获取');\n    \n    try {\n      // 获取当前用户ID\n      const userStr = localStorage.getItem('user');\n      if (!userStr) return;\n      \n      const user = JSON.parse(userStr);\n      // 只使用数字ID\n      const userId = user.id;\n      if (!userId) return;\n      \n      // 使用同步XHR请求立即获取数据\n      const xhr = new XMLHttpRequest();\n      xhr.open('GET', `${DASHBOARD_STATS_URL}/${userId}?t=${Date.now()}&forcePersonalStats=true&view=personal`, false); // 同步请求\n      xhr.send();\n      \n      if (xhr.status === 200) {\n        console.log('[路由导航] 统计数据获取成功');\n        const data = JSON.parse(xhr.responseText);\n        \n        // 缓存数据到全局变量和localStorage\n        window.dashboardStats = {\n          totalCount: parseInt(data.totalCount || 0),\n          draftCount: parseInt(data.draftCount || 0),\n          reviewedCount: parseInt(data.reviewedCount || 0),\n          submittedCount: parseInt(data.submittedCount || 0),\n          approvedCount: parseInt(data.approvedCount || 0),\n          rejectedCount: parseInt(data.rejectedCount || 0),\n          dataSource: \"navigation_xhr\",\n          timestamp: Date.now()\n        };\n        \n        // 保存到localStorage以便下次使用\n        localStorage.setItem('dashboardStats', JSON.stringify(window.dashboardStats));\n        \n        // 如果DOM已加载，直接更新统计卡片\n        setTimeout(() => {\n          const statElements = document.querySelectorAll('.stat-value');\n          if (statElements && statElements.length >= 5) {\n            statElements[0].textContent = data.totalCount || 0;\n            statElements[1].textContent = data.draftCount || 0;\n            statElements[2].textContent = data.reviewedCount || 0;\n            statElements[3].textContent = data.submittedCount || 0;\n            statElements[4].textContent = data.approvedCount || 0;\n            console.log('[路由导航] 统计数据DOM已更新');\n          }\n        }, 100); // 短暂延迟确保DOM加载\n      }\n    } catch (e) {\n      console.error('[路由导航] 获取统计数据失败:', e);\n    }\n  }\n})\n\nexport default router ", "// 权限控制指令\n\nimport store from '../store'\nimport { hasPermission as checkPermission } from '../utils/permissions'\n\n/**\n * 使用方法：\n * 1. v-permission=\"'permission_name'\"  - 检查单个权限\n * 2. v-permission=\"['permission1', 'permission2']\" - 检查多个权限（OR关系）\n * 3. v-permission:all=\"['permission1', 'permission2']\" - 检查多个权限（AND关系）\n * 4. v-permission:role=\"'ADMIN'\" - 直接检查角色\n */\nexport const permission = {\n  mounted(el, binding) {\n    const { value, arg } = binding\n    const userRole = store.getters.getUserRole\n    \n    // 添加调试日志\n    console.log('权限指令检查:', {\n      权限值: value,\n      参数: arg,\n      用户角色: userRole\n    });\n    \n    if (!userRole) {\n      disableElement(el);\n      return\n    }\n    \n    // 直接检查角色\n    if (arg === 'role') {\n      const roles = Array.isArray(value) ? value : [value]\n      if (!roles.includes(userRole)) {\n        disableElement(el);\n      }\n      return\n    }\n    \n    // 检查权限\n    if (Array.isArray(value)) {\n      // 多个权限\n      if (arg === 'all') {\n        // AND逻辑 - 必须拥有所有权限\n        const hasAllPermissions = value.every(permission => \n          checkPermission(userRole, permission)\n        )\n        if (!hasAllPermissions) {\n          disableElement(el);\n        }\n      } else {\n        // OR逻辑 - 拥有其中一个权限即可\n        const hasAnyPermission = value.some(permission => \n          checkPermission(userRole, permission)\n        )\n        if (!hasAnyPermission) {\n          disableElement(el);\n        }\n      }\n    } else {\n      // 单个权限\n      const hasPermission = checkPermission(userRole, value);\n      console.log(`权限检查结果: ${value} -> ${hasPermission ? '有权限' : '无权限'}`);\n      if (!hasPermission) {\n        disableElement(el);\n      }\n    }\n  }\n}\n\n// 禁用元素的辅助函数\nfunction disableElement(el) {\n  // 隐藏元素\n  el.style.display = 'none';\n  \n  // 添加disabled属性\n  el.setAttribute('disabled', 'disabled');\n  \n  // 阻止点击事件\n  el.style.pointerEvents = 'none';\n  \n  // 添加自定义属性标记为禁用\n  el.setAttribute('data-permission-disabled', 'true');\n  \n  // 添加禁用样式\n  el.classList.add('permission-disabled');\n}\n\n// 注册全局权限检查函数\nexport function registerPermissionDirective(app) {\n  app.directive('permission', permission)\n} ", "/**\r\n * 将Vue组件暴露给全局使用\r\n * 这个文件用于将关键组件暴露给window对象，以便在独立页面中使用\r\n */\r\n\r\nimport AnnotationReviewList from '@/components/AnnotationReviewList.vue';\r\n\r\n// 创建全局组件对象\r\nwindow.VueComponents = window.VueComponents || {};\r\nwindow.VueComponents.AnnotationReviewList = AnnotationReviewList;\r\n\r\nexport default {\r\n  AnnotationReviewList\r\n}; ", "// 首先导入webpack修复模块 - 仍然保留引用以保持向后兼容\nimport './shims-webpack.js';\n\n// 导入polyfills解决模块问题\nimport './polyfills.js';\n\n// 导入Element Plus统一修复模块 - 保留这一个，移除其他重复的修复模块\nimport './element-plus-unified-fix.js';\n\n// 下面两行已经被统一修复模块替代，不再需要\n// import './element-plus-module-fix.js';\n// import './element-plus-fix.js';\n\nimport { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport axios from 'axios'\n// 导入API配置\nimport { API_BASE_URL, API_CONTEXT_PATH, DASHBOARD_STATS_URL, DASHBOARD_STATS_WITH_PARAMS } from './config/api.config'\n\n// 全局引入 Element Plus\nimport ElementPlus from 'element-plus'\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\nimport 'element-plus/dist/index.css'\nimport zhCn from 'element-plus/dist/locale/zh-cn.mjs'\n\n// 单独引入 Element Plus 组件的方法，确保正确注册\nimport { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'\n\n// 覆盖ElMessage方法，使其不显示任何提示\nconst originalElMessageSuccess = ElMessage.success;\nconst originalElMessageError = ElMessage.error;\nconst originalElMessageInfo = ElMessage.info;\nconst originalElMessageWarning = ElMessage.warning;\n\n// 替换为空函数\nElMessage.success = () => {};\nElMessage.error = () => {};\nElMessage.info = () => {};\nElMessage.warning = () => {};\n\n// 引入自定义权限指令\nimport { registerPermissionDirective } from './directives/permission'\n\n// 引入组件暴露模块\nimport './expose-components'\n\n// 引入全局样式\nimport './assets/css/style.css'\n\n// 引入背景样式\nimport '@/assets/css/background.css'\n\n// 引入调试工具\nimport { enableAnnotationDebug } from './utils/debug.js';\n\n// 添加用户权限修复工具\nwindow.fixUserPermission = function() {\n  try {\n    // 获取当前用户\n    const userStr = localStorage.getItem('user');\n    if (!userStr) {\n      return {success: false, message: '未找到已登录用户'};\n    }\n    \n    const user = JSON.parse(userStr);\n    \n    // 检查用户角色\n    if (!user.role) {\n      // 根据用户ID模式判断角色\n      if (user.customId && user.customId.startsWith('1')) {\n        user.role = 'ADMIN';\n      } else if (user.customId && user.customId.startsWith('3')) {\n        user.role = 'REVIEWER';\n      } else {\n        user.role = 'DOCTOR';\n      }\n      \n      // 保存修复后的用户数据\n      localStorage.setItem('user', JSON.stringify(user));\n      \n      // 更新store\n      store.commit('setUser', user);\n      \n      return {success: true, message: `已修复用户角色为: ${user.role}`};\n    } else {\n      // 检查角色是否与ID模式一致\n      let expectedRole = null;\n      \n      if (user.customId && user.customId.startsWith('1')) {\n        expectedRole = 'ADMIN';\n      } else if (user.customId && user.customId.startsWith('3')) {\n        expectedRole = 'REVIEWER';\n      } else if (user.customId && user.customId.startsWith('2')) {\n        expectedRole = 'DOCTOR';\n      }\n      \n      // 如果有期望角色且与当前角色不符，进行修复\n      if (expectedRole && user.role !== expectedRole) {\n        user.role = expectedRole;\n        localStorage.setItem('user', JSON.stringify(user));\n        // 更新store\n        store.commit('setUser', user);\n        return {success: true, message: `已将用户角色修复为${expectedRole}`};\n      }\n      \n      return {success: true, message: `用户角色已存在: ${user.role}`};\n    }\n  } catch (error) {\n    return {success: false, message: `修复失败: ${error.message}`};\n  }\n};\n\n// 替换localStorage监听为空函数\nconst originalSetItem = localStorage.setItem;\nconst originalRemoveItem = localStorage.removeItem;\n\nlocalStorage.setItem = function(key, value) {\n  originalSetItem.call(this, key, value);\n};\n\nlocalStorage.removeItem = function(key) {\n  originalRemoveItem.call(this, key);\n};\n\n// 设置axios全局默认值\n// axios.defaults.baseURL = '/api'; // 不使用baseURL，避免路径重复\naxios.defaults.withCredentials = true; // 允许跨域请求发送cookies\naxios.defaults.headers.common['Content-Type'] = 'application/json';\naxios.defaults.headers.common['Accept'] = 'application/json';\naxios.defaults.timeout = 10000; // 10秒超时\n\n// 移除所有日志输出的请求拦截器\naxios.interceptors.request.use(\n  config => {\n    return config;\n  },\n  error => {\n    return Promise.reject(error);\n  }\n);\n\n// 移除所有日志输出的响应拦截器\naxios.interceptors.response.use(\n  response => {\n    return response;\n  },\n  error => {\n    return Promise.reject(error);\n  }\n);\n\nconst app = createApp(App)\n\n// 禁用全局错误处理器中的日志输出\napp.config.errorHandler = function(err, vm, info) {\n  // 错误处理逻辑保留，但不输出日志\n};\n\n// 禁用全局未捕获的Promise异常处理中的日志输出\nwindow.addEventListener('unhandledrejection', event => {\n  // 异常处理逻辑保留，但不输出日志\n});\n\n// 注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\n// 全局配置\napp.config.globalProperties.$axios = axios\n\n// 正确注册Element Plus的消息组件\napp.config.globalProperties.$message = ElMessage\napp.config.globalProperties.$notify = ElNotification\napp.config.globalProperties.$msgbox = ElMessageBox\napp.config.globalProperties.$alert = ElMessageBox.alert\napp.config.globalProperties.$confirm = ElMessageBox.confirm\napp.config.globalProperties.$prompt = ElMessageBox.prompt\napp.config.globalProperties.$loading = ElLoading.service\n\n// 注册权限指令\nregisterPermissionDirective(app)\n\n// 注册Element Plus\napp.use(store)\napp.use(router)\napp.use(ElementPlus, {\n  locale: zhCn\n})\n\n// 将消息组件暴露给全局，以便API响应拦截器可以使用\nwindow.$message = ElMessage\n\n// 定义全局的仪表盘数据刷新函数\nwindow.refreshDashboardStats = async () => {\n  console.log('全局刷新函数 window.refreshDashboardStats 被调用');\n  try {\n    const userStr = localStorage.getItem('user');\n    if (!userStr) return;\n    const user = JSON.parse(userStr);\n    // 只使用数字ID\n    const userId = user.id;\n    if (!userId) return;\n\n    // 确保使用带个人统计参数的正确URL\n    const url = DASHBOARD_STATS_WITH_PARAMS(userId);\n    console.log('全局刷新函数调用的URL:', url);\n    \n    const response = await axios.get(url, {\n      headers: { 'Cache-Control': 'no-cache' }\n    });\n\n    if (response.data) {\n      store.commit('setDashboardStats', response.data);\n      console.log('全局统计数据已刷新并存入store');\n    }\n  } catch (error) {\n    console.error('全局刷新仪表盘数据失败:', error);\n  }\n};\n\n// 添加禁用日志输出的函数\nconst disableConsoleOutput = () => {\n  if (typeof window !== 'undefined') {\n    window.console = {\n      ...console,\n      log: function() {},\n      info: function() {},\n      warn: function() {},\n      error: function() {},\n      debug: function() {}\n    };\n  }\n};\n\n// 初始化应用\napp.mount('#app');\n\n// 禁用控制台输出\ndisableConsoleOutput();\n\n// 在应用挂载后执行数据强制更新\nsetTimeout(() => {\n  console.log('🔄 应用挂载后尝试强制更新统计数据');\n  \n  try {\n    // 1. 尝试从localStorage获取统计数据\n    const statsData = JSON.parse(localStorage.getItem('dashboardStats') || '{}');\n    if (statsData && statsData.totalCount) {\n      console.log('找到统计数据:', statsData);\n      \n      // 2. 直接更新DOM\n      const statElements = document.querySelectorAll('.stat-value');\n      if (statElements && statElements.length >= 5) {\n        statElements[0].textContent = statsData.totalCount || 0;\n        statElements[1].textContent = statsData.draftCount || 0;\n        statElements[2].textContent = statsData.reviewedCount || 0;\n        statElements[3].textContent = statsData.submittedCount || 0;\n        statElements[4].textContent = statsData.approvedCount || 0;\n        console.log('📊 统计数据DOM元素已强制更新!');\n      } else {\n        console.log('未找到统计卡片DOM元素:', statElements ? statElements.length : 0);\n        \n        // 如果找不到元素，可能是还没渲染，再等待一小段时间\n        setTimeout(() => {\n          const retryElements = document.querySelectorAll('.stat-value');\n          if (retryElements && retryElements.length >= 5) {\n            retryElements[0].textContent = statsData.totalCount || 0;\n            retryElements[1].textContent = statsData.draftCount || 0;\n            retryElements[2].textContent = statsData.reviewedCount || 0;\n            retryElements[3].textContent = statsData.submittedCount || 0;\n            retryElements[4].textContent = statsData.approvedCount || 0;\n            console.log('📊 第二次尝试：统计数据DOM元素已强制更新!');\n          }\n        }, 300);\n      }\n    } else {\n      console.log('localStorage中没有找到统计数据，尝试直接获取');\n      \n      // 3. 如果没有localStorage数据，获取当前用户ID后请求\n      const axios = require('axios').default;\n      \n      // 从localStorage获取当前用户ID\n      try {\n        const userStr = localStorage.getItem('user') || '{}';\n        const user = JSON.parse(userStr);\n        // 只使用数字ID\n        const userId = user.id;\n        \n        if (!userId) {\n          console.log('找不到有效的用户ID，跳过数据获取');\n          return;\n        }\n        \n        console.log('尝试获取用户', userId, '的统计数据');\n        \n        // 使用无认证限制的API端点\n        axios.get(`${API_BASE_URL}${API_CONTEXT_PATH}/api/stats-v2/dashboard/${userId}?t=${Date.now()}&r=${Math.random()}&forcePersonalStats=true&view=personal`, {\n          headers: {\n            'Cache-Control': 'no-cache, no-store',\n            'Pragma': 'no-cache',\n            'Expires': '0'\n          }\n        })\n          .then(response => {\n            console.log('直接获取统计数据成功:', response.data);\n            \n            // 更新DOM\n            const statElements = document.querySelectorAll('.stat-value');\n            if (statElements && statElements.length >= 5) {\n              statElements[0].textContent = response.data.totalCount || 0;\n              statElements[1].textContent = response.data.draftCount || 0;\n              statElements[2].textContent = response.data.reviewedCount || 0;\n              statElements[3].textContent = response.data.submittedCount || 0;\n              statElements[4].textContent = response.data.approvedCount || 0;\n              console.log('📊 通过API获取的统计数据已更新到DOM!');\n            }\n          })\n          .catch(error => {\n            console.error('直接获取统计数据失败:', error);\n          });\n      } catch (error) {\n        console.error('获取用户ID失败:', error);\n      }\n    }\n  } catch (error) {\n    console.error('强制更新统计数据失败:', error);\n  }\n}, 500);\n\n// 在应用挂载后执行数据库清理\n// console.log('应用启动，执行数据库清理...');\n// cleanInvalidImagePairs(); \n\n// 添加window.location变更监听\nconst originalPushState = history.pushState;\nconst originalReplaceState = history.replaceState;\n\n// 重写pushState\nhistory.pushState = function() {\n  console.log('[URL变更] history.pushState 调用:', {\n    状态: arguments[0],\n    标题: arguments[1],\n    URL: arguments[2],\n    当前时间: new Date().toLocaleTimeString(),\n    调用方法: '直接调用'\n  });\n  \n  // 获取调用堆栈\n  const stack = new Error().stack;\n  console.log('[URL变更] pushState调用堆栈:', stack);\n  \n  return originalPushState.apply(this, arguments);\n};\n\n// 重写replaceState\nhistory.replaceState = function() {\n  console.log('[URL变更] history.replaceState 调用:', {\n    状态: arguments[0],\n    标题: arguments[1],\n    URL: arguments[2],\n    当前时间: new Date().toLocaleTimeString(),\n    调用方法: '直接调用'\n  });\n  \n  // 获取调用堆栈\n  const stack = new Error().stack;\n  console.log('[URL变更] replaceState调用堆栈:', stack);\n  \n  return originalReplaceState.apply(this, arguments);\n};\n\n// 监听popstate事件\nwindow.addEventListener('popstate', function(event) {\n  console.log('[URL变更] popstate事件触发:', {\n    状态: event.state,\n    当前URL: window.location.href,\n    当前时间: new Date().toLocaleTimeString(),\n    调用方法: 'popstate事件'\n  });\n});\n\n// 创建全局事件总线用于组件间通信\nimport mitt from 'mitt';\nconst eventBus = mitt();\n\n// 全局事件总线，用于组件间通信\nwindow.eventBus = eventBus;\n\n// 启用标注调试模式 - 修复Vue未定义错误\napp.config.globalProperties.$enableAnnotationDebug = function() {\n  enableAnnotationDebug();\n  app.config.globalProperties.$isAnnotationDebugEnabled = true;\n};\n\n// 在进入应用后自动启用标注调试模式（可通过控制台禁用）\ndocument.addEventListener('DOMContentLoaded', () => {\n  setTimeout(() => {\n    if (window.location.href.includes('annotation') || \n        window.location.href.includes('case') ||\n        localStorage.getItem('enableDebug') === 'true') {\n      try {\n        enableAnnotationDebug();\n        console.log('标注调试模式已自动启用');\n      } catch (e) {\n        console.error('启用调试模式失败', e);\n      }\n    }\n  }, 1000);\n}); ", "<template>\n  <div class=\"annotation-review-list\" :class=\"{'standalone-mode': isStandalone}\">\n    <!-- 标注列表视图 -->\n    <div v-if=\"!isViewingDetail\">\n      <h2>标注审核</h2>\n      \n      <!-- 审核人信息和说明 -->\n      <el-alert\n        v-if=\"currentUser\"\n        type=\"info\"\n        :closable=\"false\"\n      >\n        <template #title>\n          <span>当前审核人: <strong>{{ currentUser.name }}</strong></span>\n          <el-tag size=\"small\" :type=\"currentUser.role === 'ADMIN' ? 'danger' : 'warning'\" style=\"margin-left: 10px\">\n            {{ currentUser.role === 'ADMIN' ? '管理员' : '审核医生' }}\n          </el-tag>\n          <span v-if=\"currentUser.team\" style=\"margin-left: 10px\">\n            团队: <el-tag size=\"small\" type=\"success\">{{ currentUser.team.name }}</el-tag>\n          </span>\n        </template>\n      </el-alert>\n      \n      <div class=\"filters\">\n        <el-input\n          v-model=\"searchQuery\"\n          placeholder=\"搜索标注\"\n          prefix-icon=\"el-icon-search\"\n          clearable\n          class=\"search-input\"\n          @change=\"loadAnnotations\"\n        />\n        \n        <el-select v-model=\"filters.team\" placeholder=\"按团队筛选\" clearable @change=\"loadAnnotations\">\n          <el-option\n            v-for=\"team in teams\"\n            :key=\"team.id\"\n            :label=\"team.name\"\n            :value=\"team.id\"\n          />\n        </el-select>\n      </div>\n      \n      <!-- 标注列表 -->\n      <el-table\n        v-loading=\"loading\"\n        :data=\"annotations\"\n        :empty-text=\"loading ? '加载中...' : '暂无待审核的标注'\"\n        style=\"width: 100%\"\n        border\n        stripe\n        highlight-current-row\n        :row-class-name=\"getRowClassName\"\n        @row-click=\"showAnnotationDetail\"\n      >\n        <!-- ID和提交时间 -->\n        <el-table-column prop=\"id\" label=\"ID\" width=\"70\" />\n        <el-table-column label=\"提交时间\" width=\"160\">\n          <template #default=\"scope\">\n            {{ formatDateTime(scope.row.submittedAt) }}\n          </template>\n        </el-table-column>\n        \n        <!-- 标注医生 -->\n        <el-table-column label=\"标注医生\" width=\"150\">\n          <template #default=\"scope\">\n            <span v-if=\"scope.row.submittedBy && typeof scope.row.submittedBy === 'object'\">\n              {{ scope.row.submittedBy.name || '未知' }}\n            </span>\n            <span v-else-if=\"scope.row.submittedBy\">\n              {{ getDoctorNameById(scope.row.submittedBy) || '医生' + scope.row.submittedBy }}\n            </span>\n            <span v-else-if=\"scope.row.uploadedBy && typeof scope.row.uploadedBy === 'object'\">\n              {{ scope.row.uploadedBy.name || '未知' }}\n            </span>\n            <span v-else-if=\"scope.row.uploadedBy\">\n              {{ getDoctorNameById(scope.row.uploadedBy) || '医生' + scope.row.uploadedBy }}\n            </span>\n            <span v-else>\n              未知\n            </span>\n          </template>\n        </el-table-column>\n        \n        <!-- 团队信息 -->\n        <el-table-column label=\"所属团队\" width=\"180\">\n          <template #default=\"scope\">\n            <div>\n              <!-- 调试信息 -->\n              <div v-if=\"false\" style=\"font-size: 10px; color: #999;\">\n                team: {{ scope.row.team ? '有' : '无' }}, \n                team_id: {{ scope.row.team_id || '无' }},\n                user: {{ scope.row.user ? '有' : '无' }},\n                user.team: {{ scope.row.user && scope.row.user.team ? '有' : '无' }}\n              </div>\n              \n              <!-- 显示逻辑 - 优先使用用户的团队信息 -->\n              <template v-if=\"scope.row.user && scope.row.user.team && scope.row.user.team.name\">\n                <el-tag type=\"success\">{{ scope.row.user.team.name }}</el-tag>\n              </template>\n              <template v-else-if=\"scope.row.user && scope.row.user.team_id\">\n                <el-tag type=\"success\">{{ getTeamNameById(scope.row.user.team_id) }}</el-tag>\n              </template>\n              <template v-else-if=\"scope.row.team && scope.row.team.name\">\n                <el-tag type=\"success\">{{ scope.row.team.name }}</el-tag>\n              </template>\n              <template v-else-if=\"scope.row.team_id\">\n                <el-tag type=\"success\">{{ getTeamNameById(scope.row.team_id) }}</el-tag>\n              </template>\n              <template v-else>\n                <el-tag type=\"warning\" effect=\"dark\">\n                  <el-tooltip content=\"无团队标注，所有审核医生可审核\" placement=\"top\">\n                    <span><i class=\"el-icon-warning-outline\"></i> 无团队</span>\n                  </el-tooltip>\n                </el-tag>\n              </template>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <!-- 病例信息 -->\n        <el-table-column prop=\"patientName\" label=\"患者姓名\" width=\"120\" />\n        <el-table-column prop=\"diagnosis\" label=\"诊断\" />\n        \n        <!-- 状态列 -->\n        <el-table-column label=\"状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.status)\">\n              {{ getStatusDisplayText(scope.row.status) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <!-- 操作列 -->\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\n          <template #default=\"scope\">\n            <el-button\n              size=\"small\"\n              :type=\"scope.row.status === 'APPROVED' ? 'info' : 'primary'\"\n              @click.stop=\"showAnnotationDetail(scope.row)\"\n            >\n              {{ scope.row.status === 'APPROVED' ? '查看' : '批阅' }}\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <!-- 分页控件 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :page-size=\"pageSize\"\n          :total=\"total\"\n          :current-page=\"currentPage\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </div>\n    \n    <!-- 标注详情视图 -->\n    <div v-else class=\"annotation-detail-view\">\n      <div class=\"top-actions\">\n        <el-button type=\"primary\" size=\"medium\" class=\"back-button\" @click=\"backToList\">\n          {{ isStandalone ? '返回列表' : '返回标注列表' }}\n        </el-button>\n      </div>\n      \n      <div class=\"detail-title\">\n        <h2>病例详情</h2>\n      </div>\n      \n      <div v-if=\"selectedAnnotation\" class=\"annotation-detail\">\n        <!-- 标注图像 - 移动到详情前面 -->\n        <div class=\"section\">\n          <h3>病变标注图像</h3>\n          <!-- 移除调试信息 -->\n          <div class=\"image-container\">\n            <el-image\n              v-if=\"selectedAnnotation && (selectedAnnotation.image_two_path || selectedAnnotation.processedImagePath || selectedAnnotation.imagePath)\"\n              :src=\"getFullImageUrl(selectedAnnotation.image_two_path || selectedAnnotation.processedImagePath || selectedAnnotation.imagePath)\"\n              :preview-src-list=\"[]\"\n              fit=\"contain\"\n              style=\"max-width: 350px; max-height: 350px; width: auto; height: auto;\"\n              :z-index=\"9999\"\n              :preview-teleported=\"false\"\n              :initial-index=\"0\"\n              hide-on-click-modal\n            >\n              <template #error>\n                <div class=\"image-error\">\n                  <i class=\"el-icon-picture-outline\"></i>\n                  <p>无法加载图像</p>\n                  <small>路径: {{ selectedAnnotation.image_two_path || selectedAnnotation.processedImagePath || selectedAnnotation.imagePath }}</small>\n                </div>\n              </template>\n            </el-image>\n            <div v-else class=\"no-image\">\n              <i class=\"el-icon-picture-outline\"></i>\n              <p>该标注无图像</p>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 基本信息 -->\n        <div class=\"section\">\n          <h3>基本信息</h3>\n          <el-descriptions :column=\"2\" border>\n            <el-descriptions-item label=\"ID\">{{ selectedAnnotation.formattedId || selectedAnnotation.id }}</el-descriptions-item>\n            <el-descriptions-item label=\"标注状态\">\n              <el-tag :type=\"getStatusTagType(selectedAnnotation.status)\">\n                {{ getStatusDisplayText(selectedAnnotation.status) }}\n              </el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"标注医生\">\n              {{ (selectedAnnotation.user && selectedAnnotation.user.name) || \n                (selectedAnnotation.submittedBy && selectedAnnotation.submittedBy.name) || \n                getDoctorNameById(selectedAnnotation.user?.id || selectedAnnotation.submittedBy) || '未知' }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"所属团队\">\n              <template v-if=\"selectedAnnotation.user && selectedAnnotation.user.team && selectedAnnotation.user.team.name\">\n                <el-tag type=\"success\">{{ selectedAnnotation.user.team.name }}</el-tag>\n              </template>\n              <template v-else-if=\"selectedAnnotation.user && selectedAnnotation.user.team_id\">\n                <el-tag type=\"success\">{{ getTeamNameById(selectedAnnotation.user.team_id) }}</el-tag>\n              </template>\n              <template v-else-if=\"selectedAnnotation.team && selectedAnnotation.team.name\">\n                <el-tag type=\"success\">{{ selectedAnnotation.team.name }}</el-tag>\n              </template>\n              <template v-else-if=\"selectedAnnotation.team_id\">\n                <el-tag type=\"success\">{{ getTeamNameById(selectedAnnotation.team_id) }}</el-tag>\n              </template>\n              <template v-else>\n                <el-tag type=\"warning\">无团队</el-tag>\n              </template>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"创建时间\">\n              {{ formatDateTime(selectedAnnotation.createdAt) }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"更新时间\">\n              {{ formatDateTime(selectedAnnotation.updatedAt) }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"病例编号\">\n              {{ selectedAnnotation.caseNumber || '-' }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"血管瘤类型\">\n              {{ selectedAnnotation.detectedType || '-' }}\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n        \n        <!-- 患者信息 -->\n        <div class=\"section\">\n          <h3>患者信息</h3>\n          <el-descriptions :column=\"3\" border>\n            <el-descriptions-item label=\"姓名\">{{ selectedAnnotation.patientName || '-' }}</el-descriptions-item>\n            <el-descriptions-item label=\"年龄\">{{ selectedAnnotation.patientAge || '-' }}岁</el-descriptions-item>\n            <el-descriptions-item label=\"性别\">{{ selectedAnnotation.gender || '-' }}</el-descriptions-item>\n            <el-descriptions-item label=\"病例编号\">{{ selectedAnnotation.caseNumber || '-' }}</el-descriptions-item>\n            <el-descriptions-item label=\"病灶位置\" :span=\"2\">{{ selectedAnnotation.bodyPart || selectedAnnotation.lesionLocation || '-' }}</el-descriptions-item>\n            <el-descriptions-item label=\"颜色\">{{ selectedAnnotation.color || '-' }}</el-descriptions-item>\n            <el-descriptions-item label=\"血管质地\">{{ selectedAnnotation.vesselTexture || '-' }}</el-descriptions-item>\n            <el-descriptions-item label=\"症状描述\">{{ selectedAnnotation.symptomDescription || '-' }}</el-descriptions-item>\n          </el-descriptions>\n        </div>\n        \n        <!-- 诊断信息 -->\n        <div class=\"section\">\n          <h3>诊断信息</h3>\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"诊断摘要\">{{ selectedAnnotation.diagnosticSummary || '-' }}</el-descriptions-item>\n            <el-descriptions-item label=\"治疗建议\">{{ selectedAnnotation.treatmentSuggestion || '-' }}</el-descriptions-item>\n            <el-descriptions-item label=\"注意事项\">{{ selectedAnnotation.precautions || '-' }}</el-descriptions-item>\n          </el-descriptions>\n        </div>\n        \n        <!-- 审核意见输入框 -->\n        <div class=\"section\">\n          <h3>审核意见</h3>\n          <el-input\n            v-model=\"reviewNotes\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入审核意见...\"\n            :disabled=\"selectedAnnotation.status !== 'SUBMITTED'\"\n          ></el-input>\n          <div v-if=\"selectedAnnotation.status !== 'SUBMITTED'\" class=\"approved-notice\">\n            <i class=\"el-icon-warning\"></i> \n            {{ selectedAnnotation.status === 'APPROVED' ? '该标注已通过审核，无法修改审核意见' : \n               selectedAnnotation.status === 'REJECTED' ? '该标注已被拒绝，无法修改审核意见' : \n               '该标注不是待审核状态，无法进行审核操作' }}\n          </div>\n          <div v-if=\"selectedAnnotation.reviewNotes && (selectedAnnotation.status === 'APPROVED' || selectedAnnotation.status === 'REJECTED')\" class=\"review-notes\">\n            <strong>审核备注:</strong> {{ selectedAnnotation.reviewNotes }}\n          </div>\n        </div>\n        \n        <!-- 审核操作按钮 -->\n        <div class=\"action-buttons\">\n          <el-button plain @click=\"backToList\">返回</el-button>\n          <template v-if=\"selectedAnnotation.status === 'SUBMITTED'\">\n            <el-button\n              type=\"success\"\n              plain\n              @click=\"approveAnnotation(selectedAnnotation)\"\n            >\n              批准\n            </el-button>\n            <el-button\n              type=\"danger\"\n              plain\n              @click=\"showRejectDialog(selectedAnnotation)\"\n            >\n              拒绝\n            </el-button>\n          </template>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 拒绝原因对话框 -->\n    <el-dialog\n      v-model=\"rejectDialogVisible\"\n      title=\"拒绝标注\"\n      width=\"400px\"\n    >\n      <el-form>\n        <el-form-item label=\"拒绝原因\">\n          <el-input\n            v-model=\"reviewNotes\"\n            type=\"textarea\"\n            rows=\"4\"\n            placeholder=\"请输入拒绝原因...\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"rejectDialogVisible = false\">取消</el-button>\n          <el-button type=\"danger\" :loading=\"submitting\" @click=\"rejectAnnotation\">确认拒绝</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport api from '@/utils/api'; // 修改为使用utils/api而不是@/api\nimport axios from 'axios';\nimport { useRouter, useRoute } from 'vue-router';\n\nexport default {\n  name: 'AnnotationReviewList',\n  \n  props: {\n    standaloneMode: {\n      type: Boolean,\n      default: false\n    }\n  },\n  \n  setup(props) {\n    // 状态数据\n    const annotations = ref([]);\n    const loading = ref(false);\n    const submitting = ref(false);\n    const currentPage = ref(1);\n    const pageSize = ref(20);\n    const total = ref(0);\n    const searchQuery = ref('');\n    const teams = ref([]);\n    const currentUser = ref(null);\n    \n    const filters = reactive({\n      team: null\n    });\n    \n    // 拒绝标注对话框状态\n    const rejectDialogVisible = ref(false);\n    const selectedAnnotation = ref(null);\n    \n    // 审核意见\n    const reviewNotes = ref('');\n    \n    // 详情视图状态\n    const isViewingDetail = ref(false);\n    \n    const router = useRouter();\n    const route = useRoute();\n    \n    // 检查是否在独立页面中\n    const isStandalone = computed(() => {\n      return props.standaloneMode || route.path === '/standalone-review';\n    });\n    \n    // 加载用户信息\n    const loadCurrentUser = async () => {\n      try {\n        // 获取当前用户信息\n        const userData = localStorage.getItem('user');\n        if (userData) {\n          currentUser.value = JSON.parse(userData);\n        } else {\n          // 如果本地没有用户信息，尝试从服务器获取当前登录用户信息\n          const response = await api.user.getCurrentUser();\n          currentUser.value = response.data;\n          localStorage.setItem('user', JSON.stringify(currentUser.value));\n        }\n      } catch (error) {\n        console.error('获取当前用户信息失败', error);\n        ElMessage.error('获取用户信息失败，请重新登录');\n      }\n    };\n    \n    // 加载团队信息\n    const loadTeams = async () => {\n      try {\n        const response = await api.teams.getAllTeams();\n        teams.value = response.data;\n      } catch (error) {\n        console.error('获取团队列表失败', error);\n        ElMessage.warning('获取团队列表失败');\n      }\n    };\n    \n    // 加载待审核标注\n    const loadAnnotations = async () => {\n      loading.value = true;\n      try {\n        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');\n        const params = {\n          page: currentPage.value - 1,\n          size: pageSize.value,\n          sort: 'created_at,desc', // 按创建时间降序排序\n          search: searchQuery.value.trim(),\n          teamId: filters.team\n        };\n\n        // 只有在非管理员时才添加userId，管理员可以看所有的\n        if (userInfo.role !== 'ADMIN') {\n          params.userId = userInfo.id || userInfo.customId;\n        }\n\n        console.log('加载待审核的血管瘤诊断记录，参数:', params);\n\n        // 直接请求新的API端点\n        const response = await axios.get('/medical/api/hemangioma-diagnoses/pending', { params });\n\n        if (response.data && response.data.content) {\n          annotations.value = response.data.content.map(item => {\n            // 确保用户对象完整，包含团队信息\n            const user = item.user || {};\n            if (user && !user.team && user.team_id) {\n              // 如果用户有team_id但没有team对象，尝试从teams中获取\n              const team = teams.value.find(t => t.id === user.team_id);\n              if (team) {\n                user.team = team;\n              }\n            }\n            \n            return {\n              ...item,\n              // 兼容旧的字段名，以防模板中还在使用\n              submittedAt: item.updatedAt, \n              submittedBy: user,\n              user: user\n            };\n          });\n          total.value = response.data.totalElements;\n        } else {\n          annotations.value = [];\n          total.value = 0;\n        }\n      } catch (error) {\n        console.error('加载待审核标注失败:', error);\n        ElMessage.error('加载待审核列表失败，请稍后重试');\n        annotations.value = [];\n        total.value = 0;\n      } finally {\n        loading.value = false;\n      }\n    };\n    \n    // 分页处理\n    const handleSizeChange = (size) => {\n      pageSize.value = size;\n      currentPage.value = 1; // 重置页码为第一页\n      loadAnnotations();\n    };\n    \n    const handleCurrentChange = (page) => {\n      currentPage.value = page;\n      loadAnnotations();\n    };\n    \n    // 判断是否可以审核\n    const canReview = (annotation) => {\n      // 从用户信息判断权限，而不是硬编码返回值\n      if (!currentUser.value) return false;\n      \n      // 管理员可以审核所有标注\n      if (currentUser.value.role === 'ADMIN') return true;\n      \n      // 非审核医生不可以审核\n      if (currentUser.value.role !== 'REVIEWER') return false;\n      \n      // 无团队标注，所有审核医生可以审核\n      if (!annotation.team && !annotation.team_id) return true;\n      \n      // 获取团队ID (考虑不同格式)\n      const annotationTeamId = \n        (annotation.team && annotation.team.id) || annotation.team_id;\n      \n      const userTeamId = \n        (currentUser.value.team && currentUser.value.team.id) || currentUser.value.team_id;\n      \n      // 有团队标注，只有同团队审核医生可以审核\n      return userTeamId && userTeamId === annotationTeamId;\n    };\n    \n    // 获取行的类名，用于样式\n    const getRowClassName = ({ row }) => {\n      if (!row.team) {\n        return 'no-team-row';\n      }\n      return '';\n    };\n    \n    // 查看标注详情\n    const showAnnotationDetail = async (annotation) => {\n      console.log('查看标注详情', annotation);\n      \n      try {\n        // 先显示基本信息\n        selectedAnnotation.value = annotation;\n        \n        // 切换到详情视图\n        isViewingDetail.value = true;\n        \n        // 重置审核意见\n        reviewNotes.value = '';\n        \n        // 然后加载完整的诊断记录\n        const diagnosisId = annotation.id;\n        if (diagnosisId) {\n          try {\n            console.log(`加载完整的诊断记录，ID: ${diagnosisId}`);\n            loading.value = true;\n            \n            // 获取完整的诊断记录\n            const response = await axios.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);\n            \n            if (response.data) {\n              console.log('获取到完整的诊断记录:', response.data);\n              \n              // 处理用户和团队信息\n              const user = response.data.user || selectedAnnotation.value.user || selectedAnnotation.value.submittedBy || {};\n              \n              // 如果用户有team_id但没有team对象，尝试从teams中获取\n              if (user && !user.team && user.team_id) {\n                const team = teams.value.find(t => t.id === user.team_id);\n                if (team) {\n                  user.team = team;\n                }\n              }\n              \n              // 合并数据，保留原有数据，添加新获取的详细信息\n              selectedAnnotation.value = {\n                ...selectedAnnotation.value,\n                ...response.data,\n                // 确保用户信息正确\n                user: user,\n                submittedBy: user\n              };\n              \n              console.log('合并后的完整诊断记录:', selectedAnnotation.value);\n            }\n          } catch (error) {\n            console.error('获取完整诊断记录失败:', error);\n            ElMessage.error('获取诊断详情失败，请稍后重试');\n          } finally {\n            loading.value = false;\n          }\n        }\n      } catch (error) {\n        console.error('查看标注详情失败:', error);\n        ElMessage.error('加载标注详情失败');\n      }\n    };\n    \n    // 返回列表\n    const backToList = () => {\n      isViewingDetail.value = false;\n      selectedAnnotation.value = null;\n    };\n    \n    // 批准标注\n    const approveAnnotation = async (annotation) => {\n      try {\n        // 确认对话框\n        await ElMessageBox.confirm(\n          `确认批准此标注？`,\n          '确认批准',\n          {\n            confirmButtonText: '确认',\n            cancelButtonText: '取消',\n            type: 'info'\n          }\n        );\n        \n        // 执行批准操作\n        submitting.value = true;\n        \n        // 获取用户信息\n        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');\n        const userId = userInfo.customId || userInfo.id || '';\n        const userRole = userInfo.role || 'REVIEWER';\n        \n        console.log('使用认证信息:', userId, userRole);\n        \n        try {\n          // 使用更新后的API服务\n          const response = await api.reviews.approveReview(\n            annotation.id, \n            {\n              reviewNotes: reviewNotes.value || '审核通过'\n            }\n          );\n          \n          console.log('审核成功响应:', response.data);\n          \n          // 更新本地状态\n          annotation.status = 'APPROVED';\n          \n          ElMessage.success('标注已批准，状态已更新为已通过');\n          \n          // 返回列表并重新加载\n          backToList();\n          loadAnnotations();\n        } catch (apiError) {\n          console.error('API调用失败:', apiError);\n          console.log('错误详情:', apiError.response?.data);\n          ElMessage.error(`审核失败: ${apiError.response?.data?.message || apiError.message}`);\n          \n          // 尝试使用备用方法 - 直接使用axios\n          try {\n            console.log('尝试使用备用方法...');\n            const backupResponse = await axios.put(`/medical/api/hemangioma-diagnoses/${annotation.id}`, {\n              status: 'APPROVED',\n              reviewNotes: reviewNotes.value || '审核通过'\n            });\n            \n            console.log('备用方法成功:', backupResponse.data);\n            \n            // 更新本地状态\n            annotation.status = 'APPROVED';\n            \n            ElMessage.success('标注已批准（备用方法），状态已更新为已通过');\n            \n            // 返回列表并重新加载\n            backToList();\n            loadAnnotations();\n          } catch (backupError) {\n            console.error('备用方法也失败:', backupError);\n            ElMessage.error('所有审核方法均失败，请稍后重试');\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批准标注失败', error);\n          ElMessage.error(error.response?.data || '批准标注失败');\n        }\n      } finally {\n        submitting.value = false;\n      }\n    };\n    \n    // 显示拒绝对话框\n    const showRejectDialog = (annotation) => {\n      selectedAnnotation.value = annotation;\n      rejectDialogVisible.value = true;\n    };\n    \n    // 拒绝标注\n    const rejectAnnotation = async () => {\n      if (!reviewNotes.value) {\n        ElMessage.warning('请输入拒绝原因');\n        return;\n      }\n      \n      try {\n        submitting.value = true;\n        \n        // 获取用户信息\n        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');\n        const userId = userInfo.customId || userInfo.id || '';\n        const userRole = userInfo.role || 'REVIEWER';\n        \n        console.log('使用认证信息:', userId, userRole);\n        \n        try {\n          // 使用更新后的API服务\n          const response = await api.reviews.rejectReview(\n            selectedAnnotation.value.id, \n            {\n              reviewNotes: reviewNotes.value\n            }\n          );\n          \n          console.log('拒绝成功响应:', response.data);\n          \n          // 关闭对话框\n          rejectDialogVisible.value = false;\n          \n          // 更新本地状态\n          selectedAnnotation.value.status = 'REJECTED';\n          \n          ElMessage.success('标注已拒绝，状态已更新为已拒绝');\n          \n          // 如果在详情视图，返回列表\n          if (isViewingDetail.value) {\n            backToList();\n          }\n          \n          // 重新加载列表\n          loadAnnotations();\n        } catch (apiError) {\n          console.error('API调用失败:', apiError);\n          console.log('错误详情:', apiError.response?.data);\n          ElMessage.error(`拒绝失败: ${apiError.response?.data?.message || apiError.message}`);\n          \n          // 尝试使用备用方法\n          try {\n            console.log('尝试使用备用方法...');\n            \n            // 使用API服务的备用方法\n            const backupResponse = await api.reviews.rejectReviewBackup(\n              selectedAnnotation.value.id, \n              reviewNotes.value\n            );\n            \n            console.log('备用方法成功:', backupResponse);\n            \n            // 关闭对话框\n            rejectDialogVisible.value = false;\n            \n            // 更新本地状态\n            selectedAnnotation.value.status = 'REJECTED';\n            \n            ElMessage.success('标注已拒绝（备用方法），状态已更新为已拒绝');\n            \n            // 如果在详情视图，返回列表\n            if (isViewingDetail.value) {\n              backToList();\n            }\n            \n            // 重新加载列表\n            loadAnnotations();\n          } catch (backupError) {\n            console.error('备用方法也失败:', backupError);\n            ElMessage.error('所有拒绝方法均失败，请稍后重试');\n          }\n        }\n      } catch (error) {\n        console.error('拒绝标注失败', error);\n        ElMessage.error(error.response?.data || '拒绝标注失败');\n      } finally {\n        submitting.value = false;\n      }\n    };\n    \n    // 格式化日期时间\n    const formatDateTime = (dateTimeString) => {\n      if (!dateTimeString) return '-';\n      \n      try {\n        const date = new Date(dateTimeString);\n        \n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          return '-';\n        }\n        \n        return new Intl.DateTimeFormat('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        }).format(date);\n      } catch (error) {\n        console.warn('日期格式化错误:', dateTimeString, error);\n        return '-';\n      }\n    };\n    \n    // 获取图像路径，尝试多个可能的字段\n    const getImagePath = (annotation) => {\n      if (!annotation) return null;\n      \n      console.log('获取图像路径, annotation对象:', annotation);\n      \n      // 如果是图像对数据，直接优先使用image_two_path\n      if (annotation.image_two_path) {\n        console.log('直接使用annotation.image_two_path:', annotation.image_two_path);\n        return annotation.image_two_path;\n      }\n      \n      // 如果有图像对属性，优先使用其image_two_path\n      if (annotation.imagePair && annotation.imagePair.image_two_path) {\n        console.log('使用annotation.imagePair.image_two_path:', annotation.imagePair.image_two_path);\n        return annotation.imagePair.image_two_path;\n      }\n      \n      // 优先级顺序尝试 - 优先使用带标注框的图像\n      const possiblePaths = [\n        // 1. 直接在对象上的属性 - 优先使用image_two_path\n        annotation.image_two_path,\n        annotation.imageTwoPath,\n        \n        // 2. 嵌套在imagePair对象中的属性\n        annotation.imagePair?.image_two_path,\n        annotation.imagePair?.imageTwoPath,\n        \n        // 3. 尝试其他可能的字段\n        annotation.annotated_image_path,\n        annotation.annotatedImagePath,\n        \n        // 4. 最后才考虑原始图像路径\n        annotation.image_path,\n        annotation.imagePath,\n        annotation.path\n      ];\n      \n      // 返回第一个非空的路径\n      for (const path of possiblePaths) {\n        if (path) {\n          console.log('找到图像路径:', path);\n          return path;\n        }\n      }\n      \n      console.log('未找到任何图像路径');\n      return null;\n    };\n    \n    // 获取完整的图片URL\n    const getFullImageUrl = (path) => {\n      if (!path) return '';\n      \n      // 检查路径是否已经是完整URL\n      if (path.startsWith('http://') || path.startsWith('https://')) {\n        return path;\n      }\n      \n      // 从配置文件或当前窗口位置动态获取基础URL，避免硬编码\n      let baseUrl;\n      \n      // 尝试从API配置获取\n      try {\n        // 导入API配置可能无法在这里工作，所以使用备用方案\n        const apiConfig = window.apiConfig || {};\n        baseUrl = apiConfig.API_BASE_URL;\n      } catch (e) {\n        console.log('无法从配置获取API基础URL，使用当前窗口位置');\n      }\n      \n      // 如果无法从配置获取，使用当前窗口的origin\n      if (!baseUrl) {\n        // 使用当前窗口的origin（协议+主机名+端口）\n        baseUrl = window.location.origin;\n        console.log('使用当前窗口origin作为基础URL:', baseUrl);\n      }\n      \n      // 确保路径以/开头\n      let normalizedPath = path.startsWith('/') ? path : `/${path}`;\n      \n      // 获取用户信息用于认证\n      const userInfo = JSON.parse(localStorage.getItem('user') || '{}');\n      const userId = userInfo.customId || userInfo.id || '';\n      \n      // 添加认证参数到URL\n      const separator = normalizedPath.includes('?') ? '&' : '?';\n      normalizedPath = `${normalizedPath}${separator}userId=${userId}&t=${new Date().getTime()}`;\n      \n      return `${baseUrl}${normalizedPath}`;\n    };\n    \n    // 生命周期钩子\n    onMounted(() => {\n      console.log('========== 页面组件挂载完成 ==========');\n      console.log('当前环境:', process.env.NODE_ENV);\n      console.log('baseURL:', axios.defaults.baseURL);\n      \n      // 添加到window对象，方便控制台调试\n      window.debugAnnotationReview = {\n        loadAnnotations,\n        getUserInfo: () => {\n          const userStr = localStorage.getItem('user');\n          return userStr ? JSON.parse(userStr) : null;\n        },\n        directFetchApi: async () => {\n          try {\n            // 从localStorage获取用户信息\n            const userInfo = JSON.parse(localStorage.getItem('user') || '{}');\n            const userId = userInfo.customId || userInfo.id;\n            \n            // 直接通过fetch调用API\n            const response = await fetch(`/api/reviews/pending?userId=${userId}&page=0&size=20`);\n            const data = await response.json();\n            console.log('直接API调用结果:', data);\n            \n            // 显示总数和内容\n            console.log(`找到${data.totalElements}条记录，当前页${data.content?.length || 0}条数据`);\n            return data;\n          } catch (error) {\n            console.error('直接API调用失败:', error);\n          }\n        }\n      };\n      \n      loadCurrentUser();\n      loadTeams();\n      loadAnnotations();\n    });\n    \n    // 根据ID获取团队名称\n    const getTeamNameById = (teamId) => {\n      if (!teamId) return '无团队';\n      \n      // 从已加载的团队列表中查找\n      const team = teams.value.find(t => t.id === teamId);\n      if (team) {\n        return team.name;\n      }\n      \n      // 使用泛化的格式而非硬编码\n      return `团队${teamId}`;\n    };\n    \n    // 根据ID获取医生姓名\n    const getDoctorNameById = (doctorId) => {\n      if (!doctorId) return '未知';\n      \n      // 查找当前用户，如果ID匹配则使用当前用户名\n      if (currentUser.value && (currentUser.value.id === doctorId || currentUser.value.customId === doctorId)) {\n        return currentUser.value.name;\n      }\n      \n      // 使用泛化的格式而非硬编码\n      return `医生${doctorId}`;\n    };\n    \n    // 获取状态显示文本\n    const getStatusDisplayText = (status) => {\n      if (!status) return '未知';\n      \n      const statusMap = {\n        'DRAFT': '草稿',\n        'SUBMITTED': '待审核',\n        'REVIEWED': '已审阅',\n        'REJECTED': '已拒绝',\n        'APPROVED': '已通过'\n      };\n      \n      return statusMap[status] || status;\n    };\n    \n    // 获取状态标签类型\n    const getStatusTagType = (status) => {\n      if (!status) return 'info';\n      \n      const typeMap = {\n        'DRAFT': 'info',\n        'SUBMITTED': 'warning',\n        'REVIEWED': 'success',\n        'REJECTED': 'danger',\n        'APPROVED': 'success'\n      };\n      \n      return typeMap[status] || 'info';\n    };\n    \n    return {\n      annotations,\n      loading,\n      submitting,\n      currentPage,\n      pageSize,\n      total,\n      searchQuery,\n      filters,\n      teams,\n      rejectDialogVisible,\n      selectedAnnotation,\n      isViewingDetail,\n      loadAnnotations,\n      handleSizeChange,\n      handleCurrentChange,\n      canReview,\n      showAnnotationDetail,\n      backToList,\n      approveAnnotation,\n      showRejectDialog,\n      rejectAnnotation,\n      formatDateTime,\n      getRowClassName,\n      currentUser,\n      getTeamNameById,\n      getDoctorNameById,\n      getFullImageUrl,\n      getImagePath,\n      isStandalone,\n      reviewNotes,\n      // 添加状态显示相关方法\n      getStatusDisplayText,\n      getStatusTagType\n    };\n  }\n};\n</script>\n\n<style scoped>\n.annotation-review-list {\n  padding: 20px;\n}\n\n/* 独立模式样式 */\n.standalone-mode {\n  padding: 30px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.standalone-mode h2 {\n  text-align: center;\n  margin-bottom: 30px;\n  font-size: 24px;\n  color: #303133;\n}\n\n.filters {\n  display: flex;\n  margin-top: 20px;\n  margin-bottom: 20px;\n  align-items: center;\n  gap: 10px;\n}\n\n.search-input {\n  width: 300px;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n}\n\n/* 团队标签样式 */\n.el-tag--warning {\n  font-weight: bold;\n}\n\n/* 审核规则样式 */\n.review-rules {\n  margin-top: 8px;\n  font-size: 14px;\n}\n\n.review-rules ul {\n  margin-top: 5px;\n  padding-left: 20px;\n}\n\n.review-rules li {\n  margin-bottom: 4px;\n}\n\n/* 高亮无团队标签 */\n.el-tag--warning.el-tag--dark {\n  background-color: #e6a23c;\n  border-color: #e6a23c;\n  color: white;\n  font-weight: bold;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n/* 表格行样式 */\n:deep(.el-table__row) {\n  cursor: pointer;\n}\n\n/* 为无团队的标注行添加背景色标识 */\n:deep(.el-table__row.no-team-row) {\n  background-color: rgba(230, 162, 60, 0.08);\n}\n\n/* 详情视图样式 */\n.annotation-detail-view {\n  width: 100%;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.top-actions {\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.back-button {\n  font-weight: 500;\n}\n\n.detail-title {\n  padding: 10px 20px;\n  background-color: #f5f7fa;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.detail-title h2 {\n  margin: 0;\n  font-size: 18px;\n  color: #303133;\n}\n\n.annotation-detail {\n  padding: 20px;\n}\n\n.section {\n  margin-bottom: 30px;\n  background-color: #fff;\n  border-radius: 4px;\n}\n\n.section h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.image-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-color: #f9f9f9;\n  padding: 20px;\n  border-radius: 4px;\n  border: 1px solid #ebeef5;\n  min-height: 350px;\n  max-height: 350px;\n  width: 100%;\n  overflow: visible;\n  text-align: center;\n  position: relative;\n}\n\n:deep(.el-image) {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  pointer-events: none; /* 禁用所有鼠标事件 */\n}\n\n:deep(.el-image__inner) {\n  max-width: 350px !important;\n  max-height: 350px !important;\n  object-fit: contain;\n  cursor: default !important; /* 强制使用默认光标 */\n  pointer-events: none; /* 禁用所有鼠标事件 */\n}\n\n.image-error,\n.no-image {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #909399;\n  font-size: 14px;\n}\n\n.image-error i,\n.no-image i {\n  font-size: 40px;\n  margin-bottom: 10px;\n}\n\n/* 操作按钮样式 */\n.action-buttons {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n  margin-top: 30px;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  border-top: 1px solid #ebeef5;\n}\n\n.action-buttons .el-button {\n  min-width: 80px;\n}\n\n/* 对话框底部按钮样式 */\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n/* 已通过标注的提示样式 */\n.approved-notice {\n  margin-top: 10px;\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 审核意见样式 */\n.review-notes {\n  margin-top: 15px;\n  padding: 10px;\n  background-color: #f8f8f8;\n  border-left: 3px solid #409EFF;\n  border-radius: 4px;\n}\n</style> ", "import { render } from \"./AnnotationReviewList.vue?vue&type=template&id=7f3ba300&scoped=true\"\nimport script from \"./AnnotationReviewList.vue?vue&type=script&lang=js\"\nexport * from \"./AnnotationReviewList.vue?vue&type=script&lang=js\"\n\nimport \"./AnnotationReviewList.vue?vue&type=style&index=0&id=7f3ba300&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-7f3ba300\"]])\n\nexport default __exports__", "import axios from 'axios';\n\n// 创建axios实例\nconst apiClient = axios.create({\n  baseURL: process.env.VUE_APP_API_BASE_URL || '/medical/api',  // 使用环境变量或默认为相对路径\n  timeout: 30000,  // 超时时间30秒\n  withCredentials: true // 允许跨域请求携带凭证\n});\n\n// 配置请求拦截器\napiClient.interceptors.request.use(\n  config => {\n    // 使用通用函数规范化URL路径\n    config.url = normalizePath(config.url);\n    console.log(`[API] ${config.method.toUpperCase()} ${config.baseURL}${config.url}`);\n    \n    // 处理会话检查请求的特殊情况\n    if (config.url.includes('/users/me') || config.url.includes('/session-check')) {\n      console.log('[API] 检测到会话检查请求，使用简化认证');\n      \n      // 仅使用最基本的头部，减少跨域问题\n      config.headers = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      };\n      \n      // 添加时间戳，避免缓存\n      const separator = config.url.includes('?') ? '&' : '?';\n      config.url += `${separator}_t=${Date.now()}`;\n      \n      // 从localStorage获取用户基本信息\n      try {\n        const userStr = localStorage.getItem('user');\n        if (userStr) {\n          const user = JSON.parse(userStr);\n          if (user && (user.id || user.customId)) {\n            const userId = user.customId || user.id;\n            config.headers['Authorization'] = `Bearer user_${userId}`;\n            \n            // 添加userId作为URL参数，增强兼容性\n            const paramSeparator = config.url.includes('?') ? '&' : '?';\n            config.url += `${paramSeparator}userId=${userId}`;\n          }\n        }\n      } catch (e) {\n        console.warn('[API] 读取用户信息失败:', e);\n      }\n      \n      // 关键修复: 将用户ID也添加到 X-User-ID 头中，这是许多后端端点需要的\n      config.headers['X-User-ID'] = userId;\n      \n      return config;\n    }\n    \n    // 从localStorage获取用户信息，添加到请求头\n    let user;\n    try {\n      // 首先尝试从localStorage获取\n      const userStr = localStorage.getItem('user');\n      if (userStr) {\n        user = JSON.parse(userStr);\n      }\n      \n      // 如果localStorage中没有用户信息，尝试从sessionStorage获取\n      if (!user || !user.id) {\n        const sessionUserStr = sessionStorage.getItem('preservedUser');\n        if (sessionUserStr) {\n          console.log('[API] 从会话存储恢复用户信息');\n          user = JSON.parse(sessionUserStr);\n          // 同步回localStorage，保持一致性\n          localStorage.setItem('user', sessionUserStr);\n        }\n      }\n      \n      // 如果用户对象存在但不完整，尝试恢复更多信息\n      if (user && (!user.email || !user.role)) {\n        const userProfileStr = localStorage.getItem('userProfile');\n        if (userProfileStr) {\n          const userProfile = JSON.parse(userProfileStr);\n          // 合并缺失的信息\n          user = { ...user, ...userProfile };\n          console.log('[API] 从用户配置文件合并额外信息');\n        }\n      }\n    } catch (e) {\n      console.error('解析用户信息失败:', e);\n    }\n    \n    if (user) {\n      // 关键修复：始终优先使用数据库ID (user.id)，并简化认证头\n      const userId = user.id || user.customId || '';\n      \n      // 简化Authorization头，只包含用户ID，避免后端解析混乱\n      config.headers['Authorization'] = `Bearer user_${userId}`;\n      \n      // 关键修复: 将用户ID也添加到 X-User-ID 头中，这是许多后端端点需要的\n      config.headers['X-User-ID'] = userId;\n      \n      // 将角色等其他信息作为URL参数传递，这更符合RESTful规范\n      if (user.role) {\n        // 使用URLSearchParams来安全地添加参数，避免重复\n        const params = new URLSearchParams(config.params || {});\n        if (!params.has('_role')) {\n            params.set('_role', user.role);\n        }\n        config.params = params;\n      }\n      \n      // 添加userId作为URL参数，增强兼容性\n      const params = new URLSearchParams(config.params || {});\n      if (!params.has('userId')) {\n          params.set('userId', userId);\n      }\n      config.params = params;\n      \n      // 团队相关请求的特殊处理也应使用URL参数\n      if (user.team && user.team.id && \n         (config.url.includes('/teams/') || \n          config.url.includes('/team-applications') || \n          config.url.includes('/images/team-'))) {\n        const params = new URLSearchParams(config.params);\n        if (!params.has('_teamId')) {\n            params.set('_teamId', user.team.id);\n        }\n        config.params = params;\n      }\n      \n    } else {\n      console.warn('[API] 未找到用户信息，请求可能未认证');\n    }\n    \n    // 增加跨域请求处理\n    if (config.method && config.method.toLowerCase() === 'options') {\n      // 对于预检请求，确保包含所有必要的头部\n      config.headers['Access-Control-Request-Headers'] = 'authorization, content-type';\n      config.headers['Access-Control-Request-Method'] = 'GET, POST, PUT, DELETE';\n    }\n    \n    // 避免缓存造成的问题\n    if (!config.headers['Cache-Control']) {\n      config.headers['Cache-Control'] = 'no-cache';\n    }\n    \n    // 添加时间戳参数，避免缓存问题\n    const timestampParam = `_t=${Date.now()}`;\n    if (config.url.includes('?')) {\n      config.url += `&${timestampParam}`;\n    } else {\n      config.url += `?${timestampParam}`;\n    }\n    \n    // 添加防止异常JSON数据造成的解析错误\n    if (config.transformResponse && Array.isArray(config.transformResponse)) {\n      config.transformResponse = [...config.transformResponse];\n    } else {\n      config.transformResponse = [data => {\n        if (typeof data === 'string') {\n          try {\n            return JSON.parse(data);\n          } catch (e) {\n            console.warn('JSON解析失败:', e);\n            return data;\n          }\n        }\n        return data;\n      }];\n    }\n    \n    // 记录完整请求信息，便于调试\n    console.log(`[API完整请求] ${config.method.toUpperCase()} ${config.baseURL}${config.url}`, {\n      参数: config.params,\n      头部: config.headers\n    });\n    \n    return config;\n  },\n  error => {\n    console.error('[API Request Error]', error);\n    return Promise.reject(error);\n  }\n);\n\n// 配置响应拦截器\napiClient.interceptors.response.use(response => {\n  console.log(`[API响应] ${response.status} ${response.config.url}`, {\n    数据: response.data\n  });\n  return response;\n}, error => {\n  console.error('[API响应错误]', {\n    请求URL: error.config ? error.config.url : '未知',\n    请求方法: error.config ? error.config.method : '未知',\n    状态码: error.response ? error.response.status : '网络错误',\n    响应数据: error.response ? error.response.data : null\n  });\n  return Promise.reject(error);\n});\n\n// 创建一个使用代理的客户端，避免CORS问题\n// 这会使用当前域名和端口，通过服务器代理访问\nconst fileApiClient = axios.create({\n  baseURL: process.env.VUE_APP_API_BASE_URL || '/medical/api',  // 使用环境变量或相对路径\n  headers: {\n    'Content-Type': 'multipart/form-data', // 文件上传\n    'Accept': 'application/json'\n  },\n  timeout: 15000,\n  withCredentials: true\n});\n\n// 创建一个专门用于调试的客户端，便于跟踪请求与响应\nconst debugApiClient = axios.create({\n  baseURL: process.env.VUE_APP_API_BASE_URL || '/medical/api',  // 使用环境变量或相对路径\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'X-Debug': 'true'  // 添加调试标记\n  },\n  timeout: 10000,\n  withCredentials: true\n});\n\n// 调试客户端拦截器\ndebugApiClient.interceptors.request.use(\n  config => {\n    // 使用通用函数规范化URL路径\n    config.url = normalizePath(config.url);\n    console.log(`[Debug API] 请求: ${config.method.toUpperCase()} ${config.baseURL}${config.url}`);\n    \n    // 添加用户认证信息，与apiClient保持一致\n    try {\n      const userStr = localStorage.getItem('user');\n      if (userStr) {\n        const user = JSON.parse(userStr);\n        if (user) {\n          // 使用标准Authorization头\n          config.headers['Authorization'] = `Bearer user_${user.customId || user.id || ''}`;\n          \n          // 将额外信息编码到Authorization头中\n          if (user.email) {\n            const emailBase64 = btoa(user.email);\n            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;\n          }\n          \n          if (user.role) {\n            // 将角色信息添加到请求URL参数中\n            const separator = config.url.includes('?') ? '&' : '?';\n            config.url += `${separator}_role=${user.role}`;\n          }\n        }\n      }\n    } catch (e) {\n      console.error('[Debug API] 读取用户信息失败:', e);\n    }\n    \n    console.log('请求头:', config.headers);\n    return config;\n  },\n  error => {\n    console.error('[Debug API] 请求错误:', error);\n    return Promise.reject(error);\n  }\n);\n\ndebugApiClient.interceptors.response.use(\n  response => {\n    console.log(`[Debug API] 响应状态: ${response.status}`);\n    console.log('响应数据:', response.data);\n    return response;\n  },\n  error => {\n    console.error('[Debug API] 响应错误:', error.response || error);\n    return Promise.reject(error);\n  }\n);\n\n// 文件API拦截器\nfileApiClient.interceptors.request.use(\n  config => {\n    // 使用通用函数规范化URL路径\n    config.url = normalizePath(config.url);\n    console.log(`[FileAPI] ${config.method.toUpperCase()} ${config.url}`);\n    \n    // 添加用户信息到请求头 - 修复认证问题\n    try {\n      const userStr = localStorage.getItem('user');\n      if (userStr) {\n        const user = JSON.parse(userStr);\n    if (user) {\n          // 不使用可能导致CORS预检失败的自定义头\n          config.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;\n          if (user.email) {\n            // 使用标准Authorization头部格式而非自定义头\n            const emailBase64 = btoa(user.email);\n            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;\n          }\n        }\n      }\n    } catch (e) {\n      console.error('[FileAPI] 读取用户信息失败:', e);\n    }\n    \n    return config;\n  },\n  error => {\n    console.error('[FileAPI Request Error]', error);\n    return Promise.reject(error);\n  }\n);\n\nfileApiClient.interceptors.response.use(\n  response => {\n    console.log(`[FileAPI] Response: ${response.status}`);\n    return response;\n  },\n  error => {\n    if (error.response) {\n      console.error(`[FileAPI] Error: ${error.response.status}`, error.response.data);\n    } else {\n      console.error('[FileAPI] Network Error:', error.message);\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 添加响应拦截器\napiClient.interceptors.response.use(\n  response => {\n    console.log(`[API] Response: ${response.status}`);\n    // 添加更详细的日志，帮助调试数据结构问题\n    try {\n      const url = response.config.url;\n      \n      // 特殊处理团队成员API\n      if (url.includes('/teams/') && url.includes('/members')) {\n        console.log(`[API Debug] 团队成员API返回数据:`, \n          typeof response.data, \n          Array.isArray(response.data) ? '是数组' : '不是数组',\n          response.data ? '非空' : '为空');\n        \n        // 修复非数组的团队成员响应\n        if (response.data && !Array.isArray(response.data)) {\n          console.warn('[API Debug] 团队成员API返回非数组数据，尝试修复');\n          \n          // 如果响应是对象但不是数组，尝试将其转换为数组\n          if (typeof response.data === 'object') {\n            // 检查已知的团队成员响应格式\n            const extractedArray = response.data.members || response.data.data || response.data.content || response.data.users;\n            if (Array.isArray(extractedArray)) {\n              console.log('[API Debug] 从对象中提取到数组数据');\n              response.data = extractedArray;\n            } else if (response.data.content && typeof response.data.content === 'object') {\n              // 处理嵌套内容对象\n              const nestedArray = response.data.content.members || response.data.content.users;\n              if (Array.isArray(nestedArray)) {\n                console.log('[API Debug] 从嵌套对象中提取到数组数据');\n                response.data = nestedArray;\n              } else {\n                // 如果无法提取数组，将对象放入数组中\n                console.log('[API Debug] 将对象放入数组中');\n                response.data = [response.data];\n              }\n            } else {\n              // 检查是否有普通对象结构的团队成员\n              const hasUserFields = response.data.id && (response.data.name || response.data.email);\n              if (hasUserFields) {\n                console.log('[API Debug] 检测到单个团队成员对象');\n                response.data = [response.data];\n              } else {\n                // 尝试从对象键值中查找成员\n                const membersFromKeys = Object.values(response.data).filter(v => \n                  typeof v === 'object' && v !== null && (v.id || v.userId || v.user_id)\n                );\n                if (membersFromKeys.length > 0) {\n                  console.log('[API Debug] 从对象键值中提取成员');\n                  response.data = membersFromKeys;\n                } else {\n                  // 最后尝试：将对象放入数组\n                  console.log('[API Debug] 将对象放入数组中');\n                  response.data = [response.data];\n                }\n              }\n            }\n          } else {\n            // 如果不是对象也不是数组，返回空数组\n            console.warn('[API Debug] 无法解析团队成员数据，返回空数组');\n            response.data = [];\n          }\n        }\n        \n        // 确保数据始终是数组\n        if (!response.data) {\n          console.warn('[API Debug] 团队成员API返回空数据，初始化为空数组');\n          response.data = [];\n        }\n        \n        // 处理团队成员的用户ID和名称格式化\n        if (Array.isArray(response.data)) {\n          response.data = response.data.map(member => {\n            // 如果没有id属性但有user_id，规范化数据结构\n            if (!member.id && member.user_id) {\n              member.id = member.user_id;\n            }\n            // 如果没有name属性但有username或user对象，规范化数据结构\n            if (!member.name) {\n              if (member.username) {\n                member.name = member.username;\n              } else if (member.user && member.user.name) {\n                member.name = member.user.name;\n              }\n            }\n            return member;\n          });\n        }\n        \n        console.log('[API Debug] 处理后的团队成员数据，成员数量:', \n          Array.isArray(response.data) ? response.data.length : 0);\n      }\n      \n      // 用户API日志\n      if (url.includes('/users')) {\n        console.log(`[API Debug] 用户API返回数据结构:`, \n          typeof response.data, \n          Array.isArray(response.data) ? '是数组' : '不是数组',\n          response.data ? '非空' : '为空');\n          \n        // 如果是对象且不是数组，打印其结构\n        if (response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {\n          console.log('[API Debug] 对象键:', Object.keys(response.data));\n        }\n      }\n      \n      // 图像API日志 - 添加专门处理病例列表\n      if (url.includes('/images/') || url.includes('/api/images')) {\n        console.log(`[API Debug] 图像API返回数据:`, \n          typeof response.data, \n          Array.isArray(response.data) ? `是数组[${response.data.length}]` : '不是数组',\n          response.data ? '非空' : '为空');\n          \n        // 如果响应是空的，初始化为空数组\n        if (!response.data && (url.includes('my-images') || url.includes('team-images'))) {\n          console.warn('[API Debug] 病例列表返回空数据，初始化为空数组');\n          response.data = [];\n        }\n        \n        // 确保病例列表是数组格式\n        if (response.data && !Array.isArray(response.data) && \n           (url.includes('my-images') || url.includes('team-images'))) {\n          console.warn('[API Debug] 病例列表返回非数组数据，尝试修复');\n          if (typeof response.data === 'object') {\n            // 检查多种可能的嵌套数组字段\n            const extractedArray = response.data.content || response.data.images || \n                                  response.data.data || response.data.records || \n                                  response.data.items || response.data.list;\n            \n            if (Array.isArray(extractedArray)) {\n              console.log('[API Debug] 从对象中提取到病例数组数据');\n              response.data = extractedArray;\n            } else if (response.data.page && typeof response.data.page === 'object') {\n              // 处理分页结构\n              const pagedArray = response.data.page.content || response.data.page.data || response.data.page.list;\n              if (Array.isArray(pagedArray)) {\n                console.log('[API Debug] 从分页对象中提取病例数组');\n                response.data = pagedArray;\n              } else {\n                console.log('[API Debug] 将单个病例对象放入数组');\n                response.data = [response.data];\n              }\n            } else if (Object.keys(response.data).length > 0 && \n                     (response.data.id || response.data.imageId || response.data.metadata_id)) {\n              // 如果是单个病例对象\n              console.log('[API Debug] 检测到单个病例对象，转换为数组');\n              response.data = [response.data];\n            } else {\n              // 尝试从对象值中提取病例列表\n              const imagesFromValues = Object.values(response.data).filter(v => \n                Array.isArray(v) && v.length > 0 && \n                typeof v[0] === 'object' && (v[0].id || v[0].imageId || v[0].metadata_id)\n              );\n              \n              if (imagesFromValues.length > 0) {\n                console.log('[API Debug] 从对象值中提取病例数组');\n                response.data = imagesFromValues[0]; // 使用第一个找到的数组\n              } else {\n                console.log('[API Debug] 将对象放入数组');\n                response.data = [response.data];\n              }\n            }\n          }\n        }\n        \n        // 为病例添加缺失的必要字段\n        if (Array.isArray(response.data) && \n           (url.includes('my-images') || url.includes('team-images'))) {\n          response.data = response.data.map(image => {\n            // 确保每个病例都有ID\n            if (!image.id && image.imageId) {\n              image.id = image.imageId;\n            } else if (!image.id && image.metadata_id) {\n              image.id = image.metadata_id;\n            }\n            \n            // 确保每个病例都有状态\n            if (!image.status && image.imageStatus) {\n              image.status = image.imageStatus;\n            }\n            \n            // 确保每个病例都有标题\n            if (!image.title && image.name) {\n              image.title = image.name;\n            }\n            \n            return image;\n          });\n          \n          console.log(`[API Debug] 处理后的病例列表数量: ${response.data.length}`);\n        }\n      }\n    } catch (e) {\n      console.error('[API Debug] 日志错误:', e);\n    }\n    return response;\n  },\n  error => {\n    if (error.response) {\n      console.error(`[API] Error: ${error.response.status}`, error.response.data);\n      \n      // 如果是未授权错误，尝试自动重定向到登录页面\n      if (error.response.status === 401) {\n        console.warn('未授权，可能需要重新登录');\n        \n        // 检查是否是团队申请相关的API请求\n        const isTeamApplicationRequest = error.config.url && (\n          error.config.url.includes('/team-applications') ||\n          error.config.url.includes('/teams/') && error.config.url.includes('/applications')\n        );\n        \n        // 检查是否是标注相关的API请求\n        const isAnnotationRelatedRequest = error.config.url && (\n          error.config.url.includes('/annotations') || \n          error.config.url.includes('/tags') || \n          error.config.url.includes('/image-after-annotation') ||\n          error.config.url.includes('/images/') ||\n          error.config.url.includes('/structured-form')\n        );\n        \n        // 检查是否在标注流程中\n        const isInAnnotationFlow = isAnnotationRelatedRequest || \n                                  window.location.pathname.includes('/annotations') ||\n                                  window.location.pathname.includes('/cases/structured-form') ||\n                                  window.location.pathname.includes('/cases/form') ||\n                                  sessionStorage.getItem('isNavigatingAfterSave');\n        \n        // 检查是否有特殊标记表示正在执行保存后跳转\n        const isNavigatingAfterSave = sessionStorage.getItem('isNavigatingAfterSave');\n        const skipAuthCheck = localStorage.getItem('skipAuthCheck');\n        \n        // 检查是否在团队相关页面 - 移除对team-management的引用\n        const isTeamPage = window.location.pathname.includes('/teams');\n          \n        // 检查是否是病例列表相关请求\n        const isCasesRelatedRequest = error.config.url && (\n          error.config.url.includes('/images/my-images') || \n          error.config.url.includes('/images/team-images')\n        );\n        \n        // 检查是否在病例相关页面\n        const isCasePage = window.location.pathname.includes('/cases');\n        \n        // 如果是团队申请相关操作或病例列表操作 - 更新条件，使用isTeamPage替代isTeamManagementPage\n        if (isTeamApplicationRequest || isTeamPage || isCasesRelatedRequest || isCasePage) {\n          console.log('重要操作检测到认证问题，显示用户提示而非重定向');\n          \n          // 尝试自动修复\n          const autoFixAttempted = attemptAutoFix();\n          \n          // 如果是病例列表 API，返回一个空数组以避免UI崩溃\n          if (isCasesRelatedRequest) {\n            console.log('病例列表API认证失败，返回空数组');\n            const emptyResponse = {\n              status: 200,\n              statusText: 'OK (Mock)',\n              data: []\n            };\n            return Promise.resolve(emptyResponse);\n          }\n          \n          if (autoFixAttempted) {\n            // 重试请求，用标准格式重发\n            return retryRequestWithStandardAuth(error.config);\n          }\n          \n          // 返回特殊错误，供UI层友好提示\n          return Promise.reject({\n            teamAuthError: true, \n            needsRelogin: true,\n            message: '会话已过期，请重新登录后再操作'\n          });\n        }\n        \n        // 如果是标注流程或其他特殊情况，不执行强制重定向\n        if (isNavigatingAfterSave || skipAuthCheck || isInAnnotationFlow) {\n          console.log('检测到标注流程相关请求或特殊设置，不执行自动重定向');\n          \n          // 显示友好提示\n          if (window.$message) {\n            window.$message.warning('您的登录已过期，但可以继续当前操作');\n          }\n          \n          // 尝试自动恢复会话\n          const preservedUser = sessionStorage.getItem('preservedUser');\n          if (preservedUser) {\n            console.log('尝试使用保存的用户信息恢复会话');\n            localStorage.setItem('user', preservedUser);\n            \n            // 重新发送请求\n            const config = error.config;\n            // 避免循环重试\n            config._retry = true;\n            \n            // 添加新的认证信息\n            const user = JSON.parse(preservedUser);\n            if (user) {\n              config.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;\n              if (user.email) {\n                const emailBase64 = btoa(user.email);\n                config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;\n              }\n            }\n            \n            return apiClient(config);\n          }\n          \n          // 返回特殊错误，但不中断流程\n          return Promise.reject(new Error('会话已过期，请重新登录后再操作'));\n        }\n        \n        // 对于其他请求，执行标准的重定向逻辑\n        // 清除本地存储的用户信息\n        localStorage.removeItem('user');\n        \n        // 如果不在登录页面，重定向到登录页面\n        if (window.location.pathname !== '/login') {\n          // 保存当前URL以便登录后返回\n          const currentPath = window.location.pathname + window.location.search;\n          sessionStorage.setItem('redirectAfterLogin', currentPath);\n          \n          window.location.href = '/login';\n        }\n      }\n    } else if (error.request) {\n      // 请求已发送但没有收到响应 - 网络错误或后端服务未启动\n      console.error('[API] 网络错误:', error.message);\n      \n      // 特殊处理病例列表和团队列表，避免UI崩溃\n      const url = error.config?.url || '';\n      if (url.includes('/images/my-images') || url.includes('/images/team-images') ||\n          url.includes('/teams') && !url.includes('/applications')) {\n        console.log('关键列表API网络错误，返回空数组以避免UI崩溃');\n        const emptyResponse = {\n          status: 200,\n          statusText: 'OK (Mock due to network error)',\n          data: []\n        };\n        return Promise.resolve(emptyResponse);\n      }\n    } else {\n      console.error('[API] 请求配置错误:', error.message);\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 添加自动修复会话的函数\nfunction attemptAutoFix() {\n  try {\n    // 检查是否有保存的用户信息\n    const userStr = localStorage.getItem('user');\n    if (!userStr) return false;\n    \n    const user = JSON.parse(userStr);\n    \n    // 尝试从Session Storage获取更多认证信息\n    const sessionAuth = sessionStorage.getItem('authData');\n    if (sessionAuth) {\n      const authData = JSON.parse(sessionAuth);\n      // 将认证信息合并到用户对象\n      if (authData.token) {\n        user.token = authData.token;\n        // 保存更新后的用户信息\n        localStorage.setItem('user', JSON.stringify(user));\n        console.log('已从会话存储恢复认证令牌');\n        return true;\n      }\n    }\n    \n    // 尝试使用Email和ID重新确认身份\n    if (user.email && (user.id || user.customId)) {\n      // 至少确认有这些基本信息\n      return true;\n    }\n    \n    return false;\n  } catch (e) {\n    console.error('自动修复尝试失败:', e);\n    return false;\n  }\n}\n\n// 添加会话恢复功能 - 在页面刷新时调用\nfunction recoverSessionAfterRefresh() {\n  try {\n    console.log('检查页面是否需要恢复会话...');\n    \n    // 检查是否有保存的用户信息\n    const userStr = localStorage.getItem('user');\n    if (!userStr) {\n      console.log('本地存储中没有用户信息，无需恢复会话');\n      return false;\n    }\n    \n    const user = JSON.parse(userStr);\n    if (!user || (!user.id && !user.customId)) {\n      console.log('用户信息不完整，无法恢复会话');\n      return false;\n    }\n    \n    // 在页面加载完成后发送认证恢复请求，使用简化格式\n    const userId = user.customId || user.id;\n    \n    // 简化请求参数\n    const url = '/medical/api/users/me';\n    const params = {\n      _t: new Date().getTime(),\n      _refresh: true\n    };\n    \n    // 构建简化的请求头，减少跨域问题\n    const headers = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n      'Cache-Control': 'no-cache, no-store'\n    };\n    \n    // 只使用最基本的认证方式\n    if (userId) {\n      headers['Authorization'] = `Bearer user_${userId}`;\n    }\n    \n    // 创建URL参数字符串\n    const queryString = Object.keys(params)\n      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)\n      .join('&');\n    \n    // 构建完整URL\n    const fullUrl = `${url}?${queryString}`;\n    \n    // 使用fetch API代替axios，减少中间层处理\n    console.log('发送简化的会话恢复请求...');\n    return fetch(fullUrl, {\n      method: 'GET',\n      headers: headers,\n      credentials: 'include'\n    })\n      .then(response => {\n        if (!response.ok) {\n          throw new Error(`会话恢复请求失败: ${response.status}`);\n        }\n        return response.json();\n      })\n      .then(data => {\n        console.log('会话恢复成功:', data);\n        // 更新用户信息\n        if (data && data.user) {\n          localStorage.setItem('user', JSON.stringify(data.user));\n          console.log('用户信息已更新');\n        }\n        return true;\n      })\n      .catch(error => {\n        console.warn('会话恢复失败:', error);\n        // 如果失败，不要阻止页面继续加载\n        return false;\n      });\n  } catch (e) {\n    console.error('会话恢复过程出错:', e);\n    return false;\n  }\n}\n\n// 页面加载时尝试恢复会话\nif (typeof window !== 'undefined') {\n  window.addEventListener('DOMContentLoaded', () => {\n    recoverSessionAfterRefresh();\n  });\n}\n\n// 添加一个工具函数，用于规范化API路径，避免重复前缀\nfunction normalizePath(url) {\n  if (!url) return url;\n\n  // 保存原始URL，用于比较和记录\n  const originalUrl = url;\n\n  // 先去除开头的重复斜杠\n  let normalizedUrl = url;\n  while (normalizedUrl.startsWith('//')) {\n    normalizedUrl = normalizedUrl.substring(1);\n  }\n  \n  // 检查各种重复模式\n  const patterns = [\n    { find: 'medical/api/medical/api/', replace: 'medical/api/' },\n    { find: 'api/medical/api/', replace: 'medical/api/' },\n    { find: 'medical/api/api/', replace: 'medical/api/' },\n    { find: 'medical/medical/', replace: 'medical/' },\n    { find: 'api/api/', replace: 'api/' }\n  ];\n  \n  // 应用所有模式\n  let foundPatternMatch = false;\n  for (const pattern of patterns) {\n    if (normalizedUrl.includes(pattern.find)) {\n      normalizedUrl = normalizedUrl.replace(new RegExp(pattern.find, 'g'), pattern.replace);\n      console.log(`[normalizePath] 路径修正: 将 ${pattern.find} 替换为 ${pattern.replace}`);\n      foundPatternMatch = true;\n    }\n  }\n  \n  // 确保以斜杠开头\n  if (!normalizedUrl.startsWith('/')) {\n    normalizedUrl = '/' + normalizedUrl;\n  }\n  \n  // 特殊处理：确保/api/teams等关键路径保持正确格式\n  // 如果URL路径已经包含/api前缀，不要使用apiClient.baseURL再添加一次/api\n  if (normalizedUrl.startsWith('/api/') && apiClient && apiClient.defaults.baseURL === '/api') {\n    // 这种情况下，我们需要修改URL，移除apiClient的baseURL以避免重复\n    normalizedUrl = normalizedUrl.replace(/^\\/api\\//, '/');\n    console.log(`[normalizePath] 检测到路径已有/api前缀，移除重复: ${normalizedUrl}`);\n  }\n  \n  // 记录修正结果\n  if (originalUrl !== normalizedUrl) {\n    console.log(`[normalizePath] URL路径已规范化: ${originalUrl} -> ${normalizedUrl}`);\n  }\n  \n  return normalizedUrl;\n}\n\n// 使用标准认证格式重新发送请求\nfunction retryRequestWithStandardAuth(config) {\n  try {\n    const userStr = localStorage.getItem('user');\n    if (!userStr) return Promise.reject(new Error('无可用认证信息'));\n    \n    const user = JSON.parse(userStr);\n    const newConfig = {...config};\n    \n    // 重置头部，使用标准Authorization头\n    newConfig.headers = {...config.headers};\n    newConfig.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;\n    if (user.email) {\n      const emailBase64 = btoa(user.email);\n      newConfig.headers['Authorization'] = `${newConfig.headers['Authorization']}:${emailBase64}`;\n    }\n    \n    // 移除所有可能导致CORS问题的自定义头\n    delete newConfig.headers['X-User-Id'];\n    delete newConfig.headers['X-User-Email']; \n    delete newConfig.headers['X-Authentication-Email'];\n    delete newConfig.headers['X-User-Role'];\n    delete newConfig.headers['X-User-Team-Id'];\n    \n    // 修复URL路径，避免重复前缀\n    newConfig.url = normalizePath(newConfig.url);\n    \n    // 避免循环重试\n    newConfig._retry = true;\n    \n    console.log('使用标准认证头重试请求，URL:', newConfig.url);\n    return apiClient(newConfig);\n  } catch (e) {\n    console.error('重试请求失败:', e);\n    return Promise.reject(e);\n  }\n}\n\n// 添加一个工具函数来处理userId可能是customId的情况\nconst getUserIdParam = (userId) => {\n  // 如果是对象，尝试提取customId或id属性\n  if (userId && typeof userId === 'object') {\n    return userId.customId || userId.id || null;\n  }\n\n  // 如果是字符串或数字，直接返回\n  return userId;\n};\n\n// 添加一个工具函数，用于确保对象中的指定字段为数字类型\nfunction ensureNumericFields(obj, fields) {\n  if (!obj || typeof obj !== 'object') return obj;\n  \n  const result = {...obj};\n  \n  fields.forEach(field => {\n    if (result[field] !== undefined && result[field] !== null) {\n      const numValue = parseInt(result[field], 10);\n      if (!isNaN(numValue)) {\n        result[field] = numValue;\n      } else {\n        console.warn(`字段 ${field} 的值 \"${result[field]}\" 无法转换为数字`);\n      }\n    }\n  });\n  \n  return result;\n}\n\n// API功能\nconst api = {\n  // 认证相关\n  auth: {\n    // 登录\n    login(credentials) {\n      return apiClient.post('/users/authenticate', credentials);\n    },\n    // 注册\n    register(userData) {\n      return apiClient.post('/users', userData);\n    }\n  },\n  \n  // 统计数据相关\n  stats: {\n    // 获取仪表盘统计数据\n    getDashboard(userId) {\n      // 添加临时调试代码，追踪此函数的调用来源\n      console.log(`[调试] getDashboard 被调用，传入的 userId:`, userId, `类型:`, typeof userId);\n      \n      // 验证用户ID是否有效\n      if (!userId) {\n        const userStr = localStorage.getItem('user');\n        if (userStr) {\n            const user = JSON.parse(userStr);\n            // 统一使用数字ID\n            userId = user.id;\n            console.log('从localStorage中获取用户ID:', userId);\n        }\n      }\n      \n      // 如果仍然无法获取，返回拒绝的Promise\n      if (!userId) {\n        console.error('获取仪表盘统计数据时用户ID为空');\n        return Promise.reject(new Error('用户ID不能为空'));\n      }\n\n      // 添加时间戳，避免缓存问题\n      const timestamp = Date.now();\n      const random = Math.random();\n      \n      // 使用标准API端点而非无限制版本，并添加强制个人统计参数\n      let url = `/stats-v2/dashboard/${userId}?t=${timestamp}&r=${random}&forcePersonalStats=true&view=personal`;\n      \n      console.log(`获取用户[${userId}]的仪表盘统计数据, 完整URL: ${apiClient.defaults.baseURL}${url}`);\n      \n      // 设置请求头，只保留必要的标准头部，避免CORS预检问题\n      const config = {\n        headers: {\n          'X-User-Id': userId\n        },\n        timeout: 15000,  // 增加超时时间到15秒\n        withCredentials: true  // 确保发送认证信息\n      };\n      \n      // 使用apiClient发送请求，错误处理不再回退到旧API\n      return apiClient.get(url, config)\n        .catch(error => {\n          console.error(`获取用户[${userId}]的仪表盘统计数据失败:`, error.message || error);\n          \n          if (error.response) {\n            console.error('服务器响应状态码:', error.response.status);\n            console.error('响应数据:', error.response.data);\n          } else if (error.request) {\n            console.error('请求已发送但未收到响应，可能是网络问题');\n          }\n          \n          // 直接返回空数据，避免UI崩溃\n          return {\n            data: {\n              totalCount: 0,\n              draftCount: 0,\n              reviewedCount: 0,\n              submittedCount: 0,\n              approvedCount: 0,\n              rejectedCount: 0,\n              dataSource: 'error_fallback',\n              userId: userId,  // 记录实际使用的用户ID\n              error: error.message || '获取统计数据失败'\n            }\n          };\n        });\n    }\n  },\n  \n  // 用户相关\n  users: {\n    // 获取当前用户信息\n    getCurrentUser() {\n      return apiClient.get('/users/me');\n    },\n    \n    // 更新用户个人信息\n    updateProfile(profileData) {\n      return apiClient.put('/users/profile', profileData);\n    },\n    \n    // 修改密码\n    changePassword(passwordData) {\n      return apiClient.put('/users/password', passwordData);\n    },\n    \n    // 上传头像\n    uploadAvatar(userId, formData) {\n      return apiClient.post(`/users/${userId}/avatar`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n    },\n    \n    // 获取当前用户的审核医生申请状态\n    getReviewerApplicationStatus() {\n      return apiClient.get('/reviewer-applications/me');\n    },\n    // 申请成为审核医生\n    applyForReviewer(reason) {\n      return apiClient.post('/reviewer-applications', { reason });\n    },\n    // 获取所有用户\n    getAll() {\n      return apiClient.get('/users');\n    },\n    // 获取单个用户\n    getUser(id) {\n      return apiClient.get(`/users/${id}`);\n    },\n    // 创建用户\n    createUser(userData) {\n      return apiClient.post('/users', userData);\n    },\n    // 更新用户\n    updateUser(id, userData) {\n      return apiClient.put(`/users/${id}`, userData);\n    },\n    // 删除用户\n    deleteUser(id) {\n      return apiClient.delete(`/users/${id}`);\n    },\n    // 重置密码\n    resetPassword(userId, newPassword) {\n      return apiClient.post(`/users/${userId}/reset-password`, { newPassword });\n    },\n    // 获取所有审核医生\n    getAllReviewers() {\n      return apiClient.get('/users/reviewers');\n    },\n    // 获取所有待处理的审核医生申请\n    getPendingReviewerApplications() {\n      return apiClient.get('/reviewer-applications/pending');\n    },\n    // 处理审核医生申请\n    processReviewerApplication(applicationId, data) {\n      return apiClient.put(`/reviewer-applications/${applicationId}`, data);\n    },\n    // 获取无团队的用户\n    getUsersWithoutTeam() {\n      return apiClient.get('/users/without-team');\n    },\n    // 发送密码重置邮件\n    sendResetPasswordEmail(email) {\n      return apiClient.post('/users/forgot-password', { email });\n    },\n    // 验证重置密码验证码\n    verifyResetCode(email, code) {\n      return apiClient.post('/users/verify-reset-code', { email, code });\n    },\n    // 重置密码（忘记密码流程）\n    resetPassword(email, code, newPassword) {\n      return apiClient.post('/users/reset-password', { email, code, newPassword });\n    },\n    \n    // 获取用户头像\n    getUserAvatar(userId) {\n      return apiClient.get(`/users/${userId}/avatar`);\n    },\n  },\n  \n  // 团队相关\n  teams: {\n    // 创建团队\n    create(teamData) {\n      return apiClient.post('/teams', teamData);\n    },\n    // 获取所有团队\n    getAll() {\n      return apiClient.get('/teams');\n    },\n    // 获取单个团队信息\n    getOne(id) {\n      return apiClient.get(`/teams/${id}`);\n    },\n    // 加入团队/部门\n    joinTeam(teamId, userId) {\n      return apiClient.post(`/teams/${teamId}/members`, { userId });\n    },\n    // 申请加入团队\n    applyToJoinTeam(teamId, reason) {\n      // 从localStorage获取用户信息\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      const userId = user.customId || user.id;\n      \n      console.log('申请加入团队，使用用户ID:', userId, '类型:', typeof userId);\n      \n      // 检查是否获取到了有效的userId\n      if (!userId) {\n        console.error('无法获取有效的用户ID，可能用户未登录或会话已过期');\n        // 返回一个被拒绝的Promise，这样调用者可以捕获错误\n        return Promise.reject(new Error('无法获取用户ID，请重新登录'));\n      }\n      \n      // 构建请求数据，只包含必要的参数，不包含userId\n      const requestData = {\n        teamId: parseInt(teamId, 10) || teamId, // 尝试将字符串转为数字\n        reason: reason,\n        status: 'PENDING' // 明确设置状态为\"申请中\"\n      };\n      \n      console.log('团队申请请求数据:', JSON.stringify(requestData));\n      \n      // 使用apiClient发送请求，让请求拦截器自动添加认证信息\n      return apiClient.post('/team-applications', requestData);\n    },\n    // 获取团队的所有申请记录\n    getTeamApplications(teamId, status) {\n      let url = `/team-applications/team/${teamId}`;\n      if (status) {\n        url += `?status=${status}`;\n      }\n      return apiClient.get(url);\n    },\n    // 处理团队申请（批准或拒绝）\n    processTeamApplication(applicationId, action, reason) {\n      return apiClient.put(`/team-applications/${applicationId}`, {\n        action: action, // 'APPROVE' 或 'REJECT'\n        reason: reason || ''\n      });\n    },\n    // 获取用户的团队申请（增强版，支持customId）\n    getUserApplications(userId) {\n      const idParam = getUserIdParam(userId);\n      return apiClient.get(`/team-applications/user/${idParam}`);\n    },\n    // 获取团队成员\n    getTeamMembers(teamId) {\n      return apiClient.get(`/teams/${teamId}/members`);\n    },\n    // 获取团队已通过标注\n    getTeamAnnotations(teamId) {\n      return apiClient.get(`/teams/${teamId}/annotations/approved`);\n    },\n    // 获取单个标注详情\n    getAnnotationDetail(annotationId) {\n      return apiClient.get(`/annotations/${annotationId}`);\n    },\n    // 移除团队成员\n    removeTeamMember(teamId, userId) {\n      return apiClient.delete(`/teams/${teamId}/members/${userId}`);\n    },\n    // 添加检查用户是否已经是团队成员的方法\n    checkUserInTeam(userId, teamId) {\n      return apiClient.get(`/teams/${teamId}/members/check/${userId}`)\n        .catch(error => {\n          // 处理404等错误，返回一个默认响应\n          if (error.response) {\n            return { data: { isMember: false } };\n          }\n          throw error;\n        });\n    },\n    deleteTeam(teamId) {\n      const userStr = localStorage.getItem('user');\n      const user = userStr ? JSON.parse(userStr) : null;\n      const headers = {};\n      if (user && user.id) {\n        headers['X-User-ID'] = user.id;\n      }\n      return apiClient.delete(`/teams/${teamId}`, { headers });\n    },\n    transferOwnership(teamId, newOwnerId) {\n      const userStr = localStorage.getItem('user');\n      const user = userStr ? JSON.parse(userStr) : null;\n      const headers = {};\n      if (user && user.id) {\n        headers['X-User-ID'] = user.id;\n      }\n      return apiClient.post(`/teams/${teamId}/transfer-ownership`, { newOwnerId }, { headers });\n    },\n  },\n  \n  // 审核医生申请相关\n  reviewer: {\n    // 获取已处理的申请\n    getProcessedApplications() {\n      return apiClient.get('/reviewer-applications/processed');\n    }\n  },\n  \n  // 图像相关\n  images: {\n    // 获取图像列表\n    getAll() {\n      return apiClient.get('/images');\n    },\n    // 获取单张图像\n    getOne(id) {\n      return apiClient.get(`/images/${id}`);\n    },\n    // 上传图像\n    upload(file) {\n      // 创建FormData对象\n      const formData = new FormData();\n      formData.append('file', file);\n      \n      // 获取用户信息\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      if (user.id) {\n        formData.append('user_id', user.id);\n          }\n      \n      // 添加文件名和描述\n      if (file.name) {\n        formData.append('name', file.name);\n      }\n      \n      // 确保timeoutDuration和retryCount有有效值\n      const timeoutDuration = 15000;\n      const retryCount = 3;\n      \n      console.log('上传文件中...', {\n        文件名: file.name,\n        文件大小: file.size,\n        文件类型: file.type,\n        用户ID: user.id\n      });\n      \n      // 使用fileApiClient发送请求，增加超时时间\n      const config = {\n        timeout: timeoutDuration,\n      };\n      \n      return fileApiClient.post('/images/upload', formData, config);\n    },\n    // 保存图像到指定路径\n    saveToPath(formData) {\n      return fileApiClient.post('/images/save-to-path', formData);\n    },\n    \n    // 保存标注后的图像 - 修改为适配所有调用场景的统一实现\n    saveAnnotatedImage(imageId, processedImageFile = null) {\n      console.log(`[API] 保存标注图像，图像ID: ${imageId}`);\n      if (!imageId) {\n        console.error('[API] 保存标注图像失败: 缺少图像ID');\n        return Promise.reject(new Error('缺少图像ID'));\n      }\n      \n      // 如果提供了图像文件，使用上传处理\n      if (processedImageFile) {\n        console.log('[API] 检测到图像文件，使用文件上传方式');\n        const formData = new FormData();\n        formData.append('file', processedImageFile);\n        \n        return fileApiClient.post(`/images/${imageId}/annotate`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        }).catch(error => {\n          console.error('[API] 文件上传方式保存标注失败:', error);\n          return Promise.reject(error);\n        });\n      }\n      \n      // 否则使用服务器端生成\n      // 添加时间戳避免缓存问题\n      const timestamp = Date.now();\n      const params = { \n        metadata_id: imageId,\n        t: timestamp\n      };\n      \n      console.log(`[API] 调用标注保存API，参数:`, params);\n      \n      // 直接尝试多种端点，提高成功率\n      return Promise.any([\n        // 方法1: 使用generate-annotated-image端点\n        apiClient.get(`/tags/generate-annotated-image/${imageId}`, { \n          params: { \n            ...params,\n            mode: 'edit'\n          },\n          timeout: 10000 // 10秒超时\n        }).then(response => {\n          console.log('[API] generate-annotated-image成功:', response.data);\n          return response;\n        }).catch(error => {\n          console.error('[API] generate-annotated-image失败:', error);\n          return Promise.reject(error);\n        }),\n        \n        // 方法2: 使用save-image-after-annotation端点\n        apiClient.get(`/tags/save-image-after-annotation/${imageId}`, { \n          params,\n          timeout: 10000 // 10秒超时\n        }).then(response => {\n          console.log('[API] save-image-after-annotation成功:', response.data);\n          return response;\n        }).catch(error => {\n          console.error('[API] save-image-after-annotation失败:', error);\n          return Promise.reject(error);\n        }),\n        \n        // 方法3: 使用直接的标注保存端点\n        apiClient.post(`/tags/save-annotated-image`, { \n          metadata_id: imageId,\n          timestamp: timestamp\n        }, {\n          timeout: 10000 // 10秒超时\n        }).then(response => {\n          console.log('[API] 直接保存标注成功:', response.data);\n          return response;\n        }).catch(error => {\n          console.error('[API] 直接保存标注失败:', error);\n          return Promise.reject(error);\n        })\n      ]).catch(errors => {\n        console.error('[API] 所有保存标注方法均失败:', errors);\n        // 返回一个模拟成功的响应，以便前端能继续流程\n        return {\n          data: {\n            success: false,\n            message: '标注保存失败，但允许继续',\n            error: errors.message || '所有API调用均失败'\n          }\n        };\n      });\n    },\n    \n    // 删除图像\n    delete(id) {\n      console.log(`删除图像ID: ${id}`);\n      return apiClient.delete(`/hemangioma-diagnoses/${id}`);\n    },\n    \n    // 更新图像元数据\n    update(id, metadata) {\n      return apiClient.put(`/images/${id}`, metadata);\n    },\n    \n    // 提交图像到审核流程\n    submitForReview(id) {\n      return apiClient.post(`/images/${id}/submit`);\n    },\n    \n    // 审核图像\n    reviewImage(id, approved, reviewNotes) {\n      return apiClient.post(`/images/${id}/review`, { approved, reviewNotes });\n    },\n    \n    // 保存标注图像 - 统一实现，避免方法重复\n    saveAnnotatedImage(imageId) {\n      console.log(`[API] 保存标注图像，图像ID: ${imageId}`);\n      if (!imageId) {\n        console.error('[API] 保存标注图像失败: 缺少图像ID');\n        return Promise.reject(new Error('缺少图像ID'));\n      }\n      \n      // 添加时间戳避免缓存问题\n      const timestamp = Date.now();\n      const params = { \n        metadata_id: imageId,\n        t: timestamp\n      };\n      \n      console.log(`[API] 调用标注保存API，参数:`, params);\n      \n      // 首先尝试调用generate-annotated-image端点（更可靠）\n      return apiClient.get(`/tags/generate-annotated-image/${imageId}`, { \n        params: { \n          ...params,\n          mode: 'edit'\n        } \n      }).catch(error => {\n        console.error('[API] 调用generate-annotated-image失败:', error);\n        \n        // 如果失败，尝试调用save-image-after-annotation端点\n        console.log('[API] 尝试备用端点 save-image-after-annotation');\n        return apiClient.get(`/tags/save-image-after-annotation/${imageId}`, { params });\n      });\n    },\n    \n    // 删除标注图像\n    deleteAnnotatedImage(path) {\n      return apiClient.delete('/images/annotated', { params: { path } });\n    },\n    \n    // 获取当前用户上传的所有图像\n    getUserImages(status = null, customUserId = null) {\n      // 优先使用传入的用户ID，否则从localStorage获取\n      let userId;\n      if (customUserId) {\n        userId = customUserId;\n        console.log('getUserImages: 使用传入的用户ID:', userId);\n      } else {\n        // 从localStorage获取用户信息\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        userId = user.customId || user.id;\n        console.log('getUserImages: 从localStorage获取用户ID:', userId);\n      }\n      \n      // 构建查询参数，明确指定用户ID\n      let params = { userId };\n      if (status) {\n        params.status = status;\n      }\n      \n      // 明确传递用户ID作为查询参数\n      console.log('获取用户图像，用户ID:', userId, '状态:', status || '全部');\n      \n      // 使用完整的直接路径，完全避免前缀问题\n      // 不依赖interceptor添加前缀，而是直接指定完整路径\n      const directUrl = '/medical/api/images/my-images';\n      \n      // 添加调试信息，确认请求的完整URL\n      console.log('getUserImages使用完整路径:', directUrl, '参数:', params);\n      \n      try {\n        // 使用axios直接请求，避免拦截器可能添加的前缀\n        return axios.get(directUrl, { params });\n      } catch (error) {\n        console.error('获取用户图像失败:', error);\n        return Promise.reject(error);\n      }\n    },\n    \n    // 获取团队内的所有图像\n    getTeamImages(customUserId = null) {\n      // 优先使用传入的用户ID，否则从localStorage获取\n      let userId;\n      if (customUserId) {\n        userId = customUserId;\n        console.log('getTeamImages: 使用传入的用户ID:', userId);\n      } else {\n        // 从localStorage获取用户信息\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        userId = user.customId || user.id;\n        console.log('getTeamImages: 从localStorage获取用户ID:', userId);\n      }\n      \n      // 构建查询参数，明确指定用户ID\n      let params = { userId };\n      \n      console.log('获取团队图像，用户ID:', userId);\n      \n      // 使用完整的直接路径，完全避免前缀问题\n      const directUrl = '/medical/api/images/team-images';\n      \n      // 添加调试信息\n      console.log('getTeamImages使用完整路径:', directUrl, '参数:', params);\n      \n      try {\n        // 使用axios直接请求，避免拦截器可能添加的前缀\n        return axios.get(directUrl, { params });\n      } catch (error) {\n        console.error('获取团队图像失败:', error);\n        return Promise.reject(error);\n      }\n    },\n    \n    // 获取待审核的图像\n    getPendingReviewImages() {\n      return apiClient.get('/images/pending-review');\n    },\n    \n    // 获取已审核的图像\n    getReviewedImages() {\n      return apiClient.get('/images/reviewed');\n    },\n    \n    // 保存结构化表单数据（改为简单请求：POST + application/x-www-form-urlencoded，不带自定义头）\n    saveStructuredFormData(imageId, formData) {\n      console.log('保存结构化表单数据，图像ID:', imageId);\n      \n      // 检查是否包含标注数据\n      const annotations = formData.annotations;\n      if (annotations) {\n        console.log('表单包含标注数据:', annotations.length, '个标注');\n        delete formData.annotations; // 从表单数据中移除标注数据\n      }\n      \n      // 将对象转为 x-www-form-urlencoded 字符串\n      const params = new URLSearchParams();\n      Object.keys(formData).forEach(key => {\n        let value = formData[key];\n        // 数组转为逗号分隔字符串\n        if (Array.isArray(value)) {\n          value = value.join(',');\n        }\n        params.append(key, value == null ? '' : value);\n      });\n      \n      // 首先保存表单数据\n      return apiClient.post(`/images/${imageId}/structured-form`, params, {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        }\n      })\n      .then(response => {\n        console.log('表单数据保存成功:', response.data);\n        \n        // 如果有标注数据，确保它们也被保存\n        if (annotations && annotations.length > 0) {\n          console.log('开始保存标注数据...');\n          \n          // 获取用户信息\n          const user = JSON.parse(localStorage.getItem('user') || '{}');\n          \n          // 创建保存标注的Promise数组\n          const annotationPromises = annotations.map(annotation => {\n            // 确保标注数据格式正确\n            const tagData = {\n              metadata_id: parseInt(imageId, 10),\n              tag: annotation.tag || annotation.name,\n              x: annotation.x,\n              y: annotation.y,\n              width: annotation.width,\n              height: annotation.height,\n              created_by: user.id || 3 // 使用实际的用户ID或默认值3\n            };\n            \n            console.log('保存标注数据:', tagData);\n            \n            // 使用tags API保存标注\n            return this.saveTag(tagData);\n          });\n          \n          // 等待所有标注保存完成\n          return Promise.all(annotationPromises)\n            .then(results => {\n              console.log('所有标注保存成功:', results);\n              return response; // 返回原始表单保存响应\n            })\n            .catch(error => {\n              console.error('保存标注数据失败:', error);\n              // 即使标注保存失败，也返回表单保存结果\n              return response;\n            });\n        }\n        \n        return response;\n      });\n    },\n    \n    // 保存单个标注\n    saveTag(tagData) {\n      console.log('保存单个标注:', tagData);\n      \n      return apiClient.post('/tags', tagData, {\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n    },\n    \n    // 更新结构化表单数据 - 实际上与保存是同一个API\n    updateStructuredFormData(id, formData) {\n      console.log('API调用: 更新结构化表单数据，ID:', id, '数据:', formData);\n      return apiClient.put(`/images/${id}/structured-form`, formData);\n    },\n    \n    // 将图像标记为已标注状态\n    markAsAnnotated(id, timestamp) {\n      console.log('API调用: 将图像标记为已标注，ID:', id, timestamp ? '时间戳:' + timestamp : '');\n      \n      // 构建查询参数\n      const params = {};\n      if (timestamp) {\n        params.reviewTimestamp = timestamp;\n      }\n      \n      // 添加防重复提交标志\n      const requestKey = `mark_annotated_${id}_${new Date().getTime()}`;\n      if (window.pendingRequests && window.pendingRequests[requestKey]) {\n        console.warn('已有相同请求正在处理中，避免重复提交');\n        return window.pendingRequests[requestKey];\n      }\n      \n      // 创建请求并保存引用\n      const request = apiClient.put(`/images/${id}/mark-annotated`, null, { params })\n        .then(response => {\n          // 请求成功，移除引用\n          if (window.pendingRequests) {\n            delete window.pendingRequests[requestKey];\n          }\n          return response;\n        })\n        .catch(error => {\n          // 请求失败，移除引用\n          if (window.pendingRequests) {\n            delete window.pendingRequests[requestKey];\n          }\n          throw error;\n        });\n      \n      // 初始化pendingRequests对象（如果不存在）\n      if (!window.pendingRequests) {\n        window.pendingRequests = {};\n      }\n      \n      // 存储请求引用\n      window.pendingRequests[requestKey] = request;\n      \n      return request;\n    },\n    \n    // 更新图像状态\n    updateStatus(id, status) {\n      console.log('API调用: 更新图像状态，ID:', id, '状态:', status);\n      return apiClient.put(`/images/${id}/status`, null, { params: { status } });\n    },\n    \n    // 新增：获取经过筛选的图像列表（专用于病例列表页面）\n    getFilteredImages(status = null) {\n      // 从localStorage获取用户信息\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      const userId = user.customId || user.id;\n      \n      if (!userId) {\n        console.error('获取筛选图像列表失败：未找到用户ID');\n        return Promise.reject(new Error('用户未登录或ID不可用'));\n      }\n      \n      // 检查是否有特殊标记表示这是从Dashboard直接导航过来的\n      const isDirectNavigation = localStorage.getItem('directNavigation') === 'true';\n      if (isDirectNavigation) {\n        // 清除标记\n        localStorage.removeItem('directNavigation');\n        \n        // 检查是否有保存的状态\n        const savedStatus = localStorage.getItem('lastSelectedStatus');\n        if (savedStatus && !status) {\n          status = savedStatus;\n          console.log('使用保存的状态值:', status);\n        }\n      }\n      \n      // 构建查询参数\n      let params = { userId };\n      if (status) {\n        params.status = status;\n      }\n      \n      console.log('获取筛选图像列表，用户ID:', userId, '状态:', status || '全部');\n      \n      // 使用固定的直接路径\n      const directUrl = '/medical/api/images/my-images';\n      \n      // 添加日志\n      console.log('getFilteredImages使用路径:', directUrl, '参数:', params);\n      \n      try {\n        // 使用axios直接请求，避免拦截器问题\n        return axios.get(directUrl, { params })\n          .then(response => {\n            console.log('获取筛选图像列表成功，数量:', response.data ? response.data.length : 0);\n            return response;\n          });\n      } catch (error) {\n        console.error('获取筛选图像列表失败:', error);\n        return Promise.reject(error);\n      }\n    },\n    // 保存标注图像\n    saveAnnotatedImage(imageId) {\n      console.log(`保存标注图像，图像ID: ${imageId}`);\n      if (!imageId) {\n        return Promise.reject(new Error('缺少图像ID'));\n      }\n      \n      // 修改为GET请求，添加参数到URL\n      return apiClient.get(`/tags/save-image-after-annotation/${imageId}`, {\n        params: {\n          metadata_id: imageId,\n          t: Date.now() // 添加时间戳避免缓存问题\n        }\n      });\n    },\n    // 关键修复：保存标注图像，使用 apiClient\n    saveAnnotatedImage(imageId) {\n      console.log(`保存标注图像，图像ID: ${imageId}`);\n      if (!imageId) {\n        return Promise.reject(new Error('缺少图像ID'));\n      }\n      \n      // 使用新增的端点直接生成标注图像\n      return apiClient.get(`/tags/generate-annotated-image/${imageId}`, {\n        params: {\n          t: Date.now(), // 添加时间戳避免缓存问题\n          mode: 'edit'   // 指定模式\n        }\n      }).catch(error => {\n        console.error('生成标注图像失败:', error);\n        // 尝试备用端点\n        return apiClient.get(`/tags/save-annotated-image/${imageId}`, {\n          params: {\n            metadata_id: imageId,\n            t: Date.now()\n          }\n        });\n      });\n    },\n  },\n  \n  // 标签管理\n  tags: {\n    // 根据图像ID获取标签\n    getByImageId(imageId, mode = 'view') {\n      console.log(`获取图像标签，ID: ${imageId}, 模式: ${mode}`);\n      return apiClient.get(`/tags/image/${imageId}`, { params: { mode } });\n    },\n\n    // 根据ID获取单个标签\n    getById(id) {\n      return apiClient.get(`/tags/${id}`);\n    },\n\n    // 创建新标签\n    create(tagData) {\n      console.log('创建新标签:', tagData);\n      // 确保关键字段是数字\n      const cleanData = {\n        ...tagData,\n        metadata_id: parseInt(tagData.metadata_id, 10),\n        created_by: parseInt(tagData.created_by, 10)\n      };\n      return apiClient.post('/tags', cleanData);\n    },\n\n    // 更新标签\n    update(id, tagData) {\n      return apiClient.put(`/tags/${id}`, tagData);\n    },\n\n    // 删除标签\n    delete(id) {\n      // 显式传递userId，以便后端进行权限校验\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      const params = {};\n      \n      // 同时传递数据库ID和业务ID，确保后端能够正确识别用户身份\n      if (user && user.id) {\n        params.userId = user.id;\n      }\n      \n      // 添加自定义ID作为备用\n      if (user && user.customId) {\n        params.customUserId = user.customId;\n      }\n      \n      console.log('删除标注，传递用户信息:', { \n        标注ID: id, \n        用户ID: params.userId, \n        自定义ID: params.customUserId \n      });\n      \n      return apiClient.delete(`/tags/${id}`, { params });\n    },\n    \n    // 删除与图像关联的所有标签\n    deleteByImageId(imageId) {\n        return apiClient.delete(`/tags/image/${imageId}`);\n    },\n\n    // 根据用户ID获取标签\n    getByUserId(userId) {\n      return apiClient.get(`/tags/user/${userId}`);\n    },\n\n    // 关键修复：保存标注图像，使用 apiClient\n    saveAnnotatedImage(imageId) {\n      console.log(`保存标注图像，图像ID: ${imageId}`);\n      if (!imageId) {\n        return Promise.reject(new Error('缺少图像ID'));\n      }\n      \n      // 修改为GET请求，添加参数到URL\n      return apiClient.get(`/tags/save-image-after-annotation/${imageId}`, {\n        params: {\n          metadata_id: imageId,\n          t: Date.now() // 添加时间戳避免缓存问题\n        }\n      });\n    },\n\n    // 获取标注详情\n    getAnnotationDetail(id) {\n      return apiClient.get(`/annotations/${id}`);\n    },\n  },\n  \n  // 标注图像操作\n  annotations: {\n    // 获取标注详情\n    getAnnotationDetail(id) {\n      return apiClient.get(`/annotations/${id}`);\n    },\n    \n    // 重定向到统一的标注图像生成方法，避免重复实现\n    saveAnnotatedImage(imageId) {\n      console.log('使用统一的标注图像生成方法');\n      // 修正：使用api.tags而不是this.tags\n      return api.tags.saveAnnotatedImage(imageId);\n    },\n    \n    // 更新已有标注图像\n    updateAnnotatedImage(imageId, imagePairId) {\n      // 获取用户信息用于认证\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      const params = { \n        _t: new Date().getTime(),\n        userId: user.customId || user.id || ''\n      };\n      \n      if (user.role) {\n        params._role = user.role;\n      }\n      \n      return apiClient.put(`/annotations/${imageId}/${imagePairId}`, null, { params });\n    },\n    \n    // 生成带标注的图像 - 重定向到统一方法\n    generateAnnotatedImage(imageId) {\n      console.log('重定向到统一的标注图像生成方法');\n      // 修正：使用api.tags而不是this.tags\n      return api.tags.saveAnnotatedImage(imageId);\n    }\n  },\n  \n  // 图像对（图像与标注）\n  imagePairs: {\n    // 获取所有图像对\n    getAll() {\n      return apiClient.get('/image-pairs');\n    },\n    // 按元数据ID获取图像对\n    getByMetadataId: (metadataId) => {\n      // 在请求前添加认证调试信息\n      console.log('获取图像对 - 认证调试:');\n      let user;\n      try {\n        const userStr = localStorage.getItem('user');\n        if (userStr) {\n          user = JSON.parse(userStr);\n          console.log('用户信息:', {\n            id: user.id,\n            customId: user.customId,\n            email: user.email,\n            role: user.role\n          });\n        } else {\n          console.warn('本地存储中没有用户信息');\n        }\n      } catch (e) {\n        console.error('解析用户信息失败:', e);\n      }\n\n      // 处理长整型ID（如时间戳ID）\n      if (metadataId && metadataId.toString().length > 9) {\n        const fullId = metadataId.toString();\n        console.log(`使用完整ID参数请求: ${fullId}`);\n        // 使用查询参数API而非路径参数\n        return apiClient.get(`/api/image-pairs/by-metadata?id=${fullId}`);\n      }\n      \n      // 尝试使用ImageFileController的API获取图像对信息\n      const directPath = `/api/images/${metadataId}/image-pairs`;\n      console.log('尝试使用图像控制器API:', directPath);\n      \n      // 添加自定义请求头和查询参数，确保认证信息被传递\n      const config = {};\n      if (user) {\n        // 添加查询参数\n        config.params = {\n          _role: user.role || 'USER',\n          userId: user.customId || user.id,\n          _t: new Date().getTime()  // 时间戳避免缓存\n        };\n        \n        console.log('添加查询参数:', config.params);\n      }\n      \n      // 使用axios直接请求，而不是apiClient，完全避免拦截器\n      return axios.get(directPath, config)\n        .catch(error => {\n          console.error('获取图像对失败:', error);\n          // 返回空数据作为备选方案\n          return { data: [] };\n        });\n    },\n    // 获取单个图像对\n    getOne(id) {\n      return apiClient.get(`/image-pairs/${id}`);\n    },\n    // 创建图像对\n    create(imagePairData) {\n      return apiClient.post('/image-pairs', imagePairData);\n    },\n    // 更新图像对\n    update(id, imagePairData) {\n      return apiClient.put(`/image-pairs/${id}`, imagePairData);\n    },\n    // 删除图像对\n    delete(id) {\n      return apiClient.delete(`/image-pairs/${id}`);\n    },\n    // 删除一个元数据相关的所有图像对\n    deleteByMetadataId(metadataId) {\n      return apiClient.delete(`/image-pairs/metadata/${metadataId}`);\n    },\n    // 删除标注图像 - 使用正确的注解控制器路径\n    deleteAnnotatedImage(id) {\n      return apiClient.delete(`/annotations/${id}`);\n    },\n    upload(formData) {\n      // 关键修复：使用正确的URL和文件上传客户端\n      return fileApiClient.post('/images/upload', formData);\n    },\n    saveToPath(formData) {\n      // 使用POST请求保存标注数据\n      return fileApiClient.post('/images/save-to-path', formData);\n    },\n  },\n  \n  // 团队申请相关API（新增）\n  teamApplications: {\n    // 获取所有待处理的申请（管理员视图）\n    getPendingApplications() {\n      return apiClient.get('/team-applications/pending');\n    },\n    // 获取已处理的申请（可选状态过滤）\n    getProcessedApplications(status) {\n      if (status) {\n        return apiClient.get(`/team-applications/processed?status=${status}`);\n      } else {\n        return apiClient.get('/team-applications/processed');\n      }\n    },\n    // 处理申请\n    processApplication(applicationId, data) {\n      console.log(`调用处理申请API: ID=${applicationId}, 数据=`, data);\n      return apiClient.put(`/team-applications/${applicationId}`, data);\n    },\n    // 获取团队的申请\n    getTeamApplications(teamId, status) {\n      let url = `/team-applications/team/${teamId}`;\n      if (status) {\n        url += `?status=${status}`;\n          }\n      return apiClient.get(url);\n    },\n    // 获取用户的申请\n    getUserApplications(userId) {\n      return apiClient.get(`/team-applications/user/${userId}`);\n    },\n    // 申请加入团队\n    applyToJoinTeam(teamId, reason) {\n      return apiClient.post(`/team-applications`, { teamId, reason });\n    }\n  },\n  \n  // 图像日志相关\n  imageLogs: {\n    // 记录图像操作日志\n    logOperation(logData) {\n      console.log('记录图像操作日志:', logData);\n      return apiClient.post('/image-logs/log', logData);\n    },\n    \n    // 获取时区信息\n    getTimezoneInfo() {\n      return apiClient.get('/image-logs/timezone-info');\n    }\n  }\n};\n\n// 处理长整型ID的函数，将其转换为合适的整数\nfunction processLongId(id) {\n  if (!id) return null;\n  \n  const idStr = id.toString();\n  // 如果ID长度超过9（INT范围限制），使用完整ID但添加特殊参数\n  if (idStr.length > 9) {\n    console.log(`处理长ID: ${idStr} - 使用完整ID和查询参数`);\n    // 使用原始ID，但添加长ID标记，传递给新的API端点\n    return idStr;\n  }\n  return idStr;\n}\n\nexport default api;", "// 权限配置文件\n// 定义系统中各角色的权限\n\n// 功能权限列表\nexport const PERMISSIONS = {\n  // 系统管理权限\n  MANAGE_USERS: 'manage_users',         // 用户管理\n  MANAGE_TEAMS: 'manage_teams',         // 团队管理\n  VIEW_STATS: 'view_stats',             // 查看统计\n  \n  // 团队权限\n  VIEW_TEAM: 'view_team',               // 查看团队\n  JOIN_TEAM: 'join_team',               // 加入团队\n  LEAVE_TEAM: 'leave_team',             // 离开团队\n  \n  // 病例权限\n  CREATE_CASE: 'create_case',           // 创建病例\n  EDIT_CASE: 'edit_case',               // 编辑病例\n  DELETE_CASE: 'delete_case',           // 删除病例\n  VIEW_ALL_CASES: 'view_all_cases',     // 查看所有病例\n  VIEW_OWN_CASES: 'view_own_cases',     // 查看自己的病例\n  \n  // 审核权限\n  REVIEW_CASES: 'review_cases',         // 审核病例\n  APPROVE_CASES: 'approve_cases',       // 批准病例\n  \n  // 标注权限\n  ANNOTATE_IMAGES: 'annotate_images',   // 标注图像\n  VIEW_OWN_ANNOTATIONS: 'view_own_annotations', // 查看自己的标注\n  EDIT_OWN_ANNOTATIONS: 'edit_own_annotations', // 编辑自己的标注\n  \n  // 图像管理\n  UPLOAD_IMAGES: 'upload_images',       // 上传图像\n  DELETE_IMAGES: 'delete_images'        // 删除图像\n}\n\n// 角色权限映射\nexport const ROLE_PERMISSIONS = {\n  // 管理员拥有所有权限\n  ADMIN: [\n    PERMISSIONS.MANAGE_USERS,\n    PERMISSIONS.MANAGE_TEAMS,\n    PERMISSIONS.VIEW_STATS,\n    PERMISSIONS.VIEW_TEAM,\n    PERMISSIONS.JOIN_TEAM,\n    PERMISSIONS.LEAVE_TEAM,\n    PERMISSIONS.CREATE_CASE,\n    PERMISSIONS.EDIT_CASE,\n    PERMISSIONS.DELETE_CASE,\n    PERMISSIONS.VIEW_ALL_CASES,\n    PERMISSIONS.VIEW_OWN_CASES,\n    PERMISSIONS.REVIEW_CASES,\n    PERMISSIONS.APPROVE_CASES,\n    PERMISSIONS.ANNOTATE_IMAGES,\n    PERMISSIONS.VIEW_OWN_ANNOTATIONS,\n    PERMISSIONS.EDIT_OWN_ANNOTATIONS,\n    PERMISSIONS.UPLOAD_IMAGES,\n    PERMISSIONS.DELETE_IMAGES\n  ],\n  \n  // 标注医生权限 - 只能处理自己的标注\n  DOCTOR: [\n    PERMISSIONS.CREATE_CASE,\n    PERMISSIONS.EDIT_CASE,\n    PERMISSIONS.VIEW_OWN_CASES,\n    PERMISSIONS.ANNOTATE_IMAGES,\n    PERMISSIONS.VIEW_OWN_ANNOTATIONS,\n    PERMISSIONS.EDIT_OWN_ANNOTATIONS,\n    PERMISSIONS.UPLOAD_IMAGES,\n    PERMISSIONS.VIEW_TEAM,\n    PERMISSIONS.JOIN_TEAM,\n    PERMISSIONS.LEAVE_TEAM\n  ],\n  \n  // 审核医生权限 - 可以查看所有人的标注，但只能编辑自己的\n  REVIEWER: [\n    PERMISSIONS.VIEW_ALL_CASES,\n    PERMISSIONS.VIEW_OWN_CASES,\n    PERMISSIONS.REVIEW_CASES,\n    PERMISSIONS.APPROVE_CASES,\n    PERMISSIONS.ANNOTATE_IMAGES,\n    PERMISSIONS.VIEW_OWN_ANNOTATIONS,\n    PERMISSIONS.EDIT_OWN_ANNOTATIONS,\n    PERMISSIONS.CREATE_CASE,\n    PERMISSIONS.EDIT_CASE, // 确保REVIEWER角色有编辑病例的权限\n    PERMISSIONS.VIEW_TEAM,\n    PERMISSIONS.JOIN_TEAM,\n    PERMISSIONS.LEAVE_TEAM\n  ]\n}\n\n// 路由权限映射（哪些角色可以访问哪些路由）\nexport const ROUTE_PERMISSIONS = {\n  // 仪表盘 - 所有角色\n  '/app/dashboard': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  \n  // 用户管理 - 仅管理员\n  '/app/users': ['ADMIN'],\n  \n  // 团队管理 - 所有角色\n  '/app/teams': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  '/app/teams/join': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  '/app/teams/view': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  \n  // 病例管理 - 所有角色但视图不同\n  '/app/cases': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  '/app/cases/new': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  '/app/cases/edit': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  '/app/cases/view': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  '/app/cases/form': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  \n  // 标注页面 - 所有角色都可以访问\n  '/app/case': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  \n  // 审核页面 - 管理员和审核医生\n  '/app/review': ['ADMIN', 'REVIEWER'],\n  \n  // 标注审核页面 - 管理员和审核医生\n  '/app/annotation-reviews': ['ADMIN', 'REVIEWER'],\n  \n  // 图片上传 - 现在所有角色都可以\n  '/app/images/upload': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  \n  // 血管瘤诊断 - 所有角色都可以访问\n  '/app/hemangioma-diagnosis': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  \n  // 个人中心 - 所有角色都可以访问\n  '/app/profile': ['ADMIN', 'DOCTOR', 'REVIEWER'],\n  \n  // 管理页面 - 仅管理员\n  '/admin': ['ADMIN']\n}\n\n// 检查用户是否有特定权限\nexport function hasPermission(userRole, permission) {\n  if (!userRole) return false\n  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false\n}\n\n// 检查用户是否可以访问指定路由\nexport function canAccessRoute(userRole, routePath) {\n  if (!userRole || !routePath) {\n    console.log(`[权限检查] 缺少用户角色或路由路径: 角色=${userRole}, 路径=${routePath}`);\n    return false;\n  }\n  \n  console.log(`[权限检查] 检查用户角色 ${userRole} 是否可以访问路径 ${routePath}`);\n  \n  // 精确匹配\n  if (ROUTE_PERMISSIONS[routePath]) {\n    const hasAccess = ROUTE_PERMISSIONS[routePath].includes(userRole);\n    console.log(`[权限检查] 精确匹配结果: ${hasAccess ? '有权访问' : '无权访问'}`);\n    return hasAccess;\n  }\n  \n  // 模糊匹配（针对带参数的路由，如 /app/cases/edit/123）\n  for (const route in ROUTE_PERMISSIONS) {\n    if (routePath.startsWith(route)) {\n      const hasAccess = ROUTE_PERMISSIONS[route].includes(userRole);\n      console.log(`[权限检查] 模糊匹配 ${route}: ${hasAccess ? '有权访问' : '无权访问'}`);\n      if (hasAccess) {\n        return true;\n      }\n    }\n  }\n  \n  console.log(`[权限检查] 未找到匹配的路由权限配置，默认无权访问`);\n  return false;\n}\n\n// 检查用户是否可以访问特定资源（根据资源所有者ID）\nexport function canAccessResource(currentUserId, resourceOwnerId, userRole) {\n  // 如果是当前用户自己的资源，允许访问\n  if (currentUserId === resourceOwnerId) {\n    return true\n  }\n  \n  // 如果用户是管理员，允许访问任何资源\n  if (userRole === 'ADMIN') {\n    return true\n  }\n  \n  // 如果用户是审核人员，且具有查看所有病例的权限，允许访问\n  if (userRole === 'REVIEWER' && hasPermission(userRole, PERMISSIONS.VIEW_ALL_CASES)) {\n    return true\n  }\n  \n  // 其他情况不允许访问\n  return false\n} ", "/**\r\n * Element Plus 统一修复模块\r\n * \r\n * 此文件合并了所有Element Plus相关的修复功能：\r\n * - 解决ES模块加载问题，特别是.mjs文件找不到的问题\r\n * - 修复缺少的模块路径\r\n * - 提供通用的tryNextPath函数\r\n * - 模拟缺失的类型和辅助函数\r\n */\r\n\r\n// 创建通用的tryNextPath函数处理模块路径解析\r\nexport function tryNextPath(paths, index) {\r\n  if (!paths || index >= paths.length) {\r\n    console.warn('模块路径解析失败', paths, index);\r\n    return '';\r\n  }\r\n  return paths[index];\r\n}\r\n\r\n// 全局注入tryNextPath函数\r\nif (typeof window !== 'undefined') {\r\n  window.tryNextPath = tryNextPath;\r\n}\r\n\r\nif (typeof global !== 'undefined') {\r\n  global.tryNextPath = tryNextPath;\r\n}\r\n\r\n// 修复Element Plus类型相关的模块\r\nexport const isString = (val) => typeof val === 'string';\r\nexport const isNumber = (val) => typeof val === 'number';\r\nexport const isBoolean = (val) => typeof val === 'boolean';\r\nexport const isObject = (val) => val !== null && typeof val === 'object';\r\nexport const isFunction = (val) => typeof val === 'function';\r\nexport const isArray = Array.isArray;\r\nexport const isDate = (val) => val instanceof Date;\r\nexport const isPromise = (val) => isObject(val) && isFunction(val.then) && isFunction(val.catch);\r\n\r\n// 模拟常用的缺失模块\r\nexport const Size = {\r\n  LARGE: 'large',\r\n  DEFAULT: 'default',\r\n  SMALL: 'small',\r\n};\r\n\r\n// 模拟运行时props帮助函数\r\nexport const buildProp = (prop, key) => prop;\r\nexport const buildProps = (props) => props;\r\nexport const definePropType = (type) => type;\r\nexport const mutable = (val) => val;\r\n\r\n// 导出空的token对象，用于替换缺失的模块\r\nexport const TOKEN = Symbol('token');\r\nexport const TOOLTIP_INJECTION_KEY = Symbol('tooltip');\r\n\r\n// 修复缺少的模块路径\r\nconst fixElementPlusImports = () => {\r\n  try {\r\n    if (typeof window !== 'undefined') {\r\n      // 创建虚拟的utils模块以避免缺失\r\n      window.__EP_UTILS_TYPES__ = {\r\n        isString,\r\n        isNumber,\r\n        isBoolean,\r\n        isObject,\r\n        isFunction,\r\n      };\r\n      \r\n      // 修复可能缺失的token和hooks\r\n      window.__EP_VIRTUAL_MODULES__ = {};\r\n      \r\n      // 定义一个模块导入拦截器\r\n      const originalImport = window.__webpack_require__ || (() => {});\r\n      if (window.__webpack_require__) {\r\n        window.__webpack_require__ = function(moduleId) {\r\n          try {\r\n            return originalImport(moduleId);\r\n          } catch (e) {\r\n            // 如果是Element Plus内部模块导入错误\r\n            if (e.message && e.message.includes('Cannot find module')) {\r\n              // 返回空对象避免崩溃\r\n              console.warn('Element Plus模块加载失败:', e.message);\r\n              return {};\r\n            }\r\n            throw e;\r\n          }\r\n        };\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.warn('Element Plus兼容性修复应用失败', e);\r\n  }\r\n};\r\n\r\n// 应用修复\r\nfixElementPlusImports();\r\n\r\n// 导出统一的修复工具\r\nexport default {\r\n  tryNextPath,\r\n  isString,\r\n  isNumber,\r\n  isBoolean,\r\n  isObject,\r\n  isFunction,\r\n  isArray,\r\n  isDate,\r\n  isPromise,\r\n  Size,\r\n  buildProp,\r\n  buildProps,\r\n  definePropType,\r\n  mutable,\r\n  TOKEN,\r\n  TOOLTIP_INJECTION_KEY\r\n}; ", "// 统一的本地存储处理工具\r\nexport const storageUtils = {\r\n  // 保存数据到本地存储\r\n  saveToStorage(key, data) {\r\n    try {\r\n      localStorage.setItem(key, JSON.stringify(data));\r\n      return true;\r\n    } catch (e) {\r\n      console.error(`保存数据到本地存储失败: ${key}`, e);\r\n      return false;\r\n    }\r\n  },\r\n  \r\n  // 从本地存储获取数据\r\n  getFromStorage(key, defaultValue = null) {\r\n    try {\r\n      const data = localStorage.getItem(key);\r\n      return data ? JSON.parse(data) : defaultValue;\r\n    } catch (e) {\r\n      console.error(`从本地存储获取数据失败: ${key}`, e);\r\n      return defaultValue;\r\n    }\r\n  },\r\n  \r\n  // 从本地存储中移除数据\r\n  removeFromStorage(key) {\r\n    try {\r\n      localStorage.removeItem(key);\r\n      return true;\r\n    } catch (e) {\r\n      console.error(`从本地存储中移除数据失败: ${key}`, e);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\n// 异步操作处理帮助函数\r\nexport const asyncActionHandler = {\r\n  // 使用开始和结束loading的mutation名称\r\n  start(commit, loadingMutation = 'setLoading', errorMutation = 'setError') {\r\n    commit(loadingMutation, true);\r\n    if (errorMutation) commit(errorMutation, null);\r\n  },\r\n  \r\n  // 结束异步操作\r\n  end(commit, loadingMutation = 'setLoading') {\r\n    commit(loadingMutation, false);\r\n  },\r\n  \r\n  // 处理错误\r\n  error(commit, error, errorMutation = 'setError', loadingMutation = 'setLoading') {\r\n    let errorMessage;\r\n    \r\n    // 处理不同格式的错误响应\r\n    if (error.response) {\r\n      // 服务器返回了错误响应\r\n      const responseData = error.response.data;\r\n      \r\n      // 处理不同格式的错误数据\r\n      if (typeof responseData === 'string') {\r\n        // 直接是字符串错误信息\r\n        errorMessage = responseData;\r\n      } else if (responseData && responseData.message) {\r\n        // 包含message字段的对象\r\n        errorMessage = responseData.message;\r\n      } else if (responseData && responseData.error) {\r\n        // 包含error字段的对象\r\n        errorMessage = responseData.error;\r\n      } else {\r\n        // 其他情况，使用HTTP状态文本\r\n        errorMessage = error.response.statusText;\r\n      }\r\n    } else if (error.message) {\r\n      // 请求没有到达服务器，或者其他客户端错误\r\n      errorMessage = error.message;\r\n    } else {\r\n      // 未知错误\r\n      errorMessage = '未知错误';\r\n    }\r\n    \r\n    // 设置错误状态\r\n    commit(errorMutation, errorMessage);\r\n    commit(loadingMutation, false);\r\n    \r\n    console.error('操作失败:', error);\r\n    if (error.response) {\r\n      console.error('错误响应:', error.response.status, error.response.data);\r\n    }\r\n    \r\n    return errorMessage;\r\n  }\r\n}; ", "/**\r\n * 前端调试工具 - 用于监控和显示标注相关的网络请求\r\n */\r\n\r\n// 保存原始的fetch和XMLHttpRequest\r\nconst originalFetch = window.fetch;\r\nconst originalXHR = window.XMLHttpRequest;\r\n\r\n// 监控标注相关的API请求\r\nexport const enableAnnotationDebug = () => {\r\n  // 开启调试模式\r\n  window.ANNOTATION_DEBUG_ENABLED = true;\r\n  console.log('%c标注调试模式已启用', 'background: #4CAF50; color: white; font-size: 16px; padding: 3px 8px; border-radius: 4px;');\r\n  \r\n  // 监控fetch请求\r\n  window.fetch = function(url, options) {\r\n    if (typeof url === 'string' && (url.includes('/api/tags') || url.includes('/api/api'))) {\r\n      console.group('%c[标注调试] Fetch 请求', 'background: #2196F3; color: white; font-size: 14px; padding: 2px 5px;');\r\n      console.log('URL:', url);\r\n      console.log('选项:', options);\r\n      \r\n      // 获取请求体\r\n      if (options && options.body) {\r\n        try {\r\n          const body = JSON.parse(options.body);\r\n          console.log('请求体:', body);\r\n        } catch (e) {\r\n          console.log('请求体 (无法解析):', options.body);\r\n        }\r\n      }\r\n      \r\n      console.groupEnd();\r\n    }\r\n    \r\n    // 调用原始fetch并监控响应\r\n    return originalFetch.apply(this, arguments)\r\n      .then(response => {\r\n        // 仅处理标注相关请求\r\n        if (typeof url === 'string' && (url.includes('/api/tags') || url.includes('/api/api'))) {\r\n          console.group('%c[标注调试] Fetch 响应', 'background: #4CAF50; color: white; font-size: 14px; padding: 2px 5px;');\r\n          console.log('状态:', response.status, response.statusText);\r\n          console.log('响应头:', response.headers);\r\n          \r\n          // 克隆响应以避免消耗流\r\n          const clonedResponse = response.clone();\r\n          clonedResponse.json().then(data => {\r\n            console.log('响应数据:', data);\r\n            console.groupEnd();\r\n          }).catch(() => {\r\n            console.log('响应数据: 无法解析为JSON');\r\n            console.groupEnd();\r\n          });\r\n        }\r\n        return response;\r\n      })\r\n      .catch(error => {\r\n        if (typeof url === 'string' && (url.includes('/api/tags') || url.includes('/api/api'))) {\r\n          console.group('%c[标注调试] Fetch 错误', 'background: #F44336; color: white; font-size: 14px; padding: 2px 5px;');\r\n          console.error('错误信息:', error);\r\n          console.groupEnd();\r\n        }\r\n        throw error;\r\n      });\r\n  };\r\n  \r\n  // 监控XMLHttpRequest\r\n  window.XMLHttpRequest = function() {\r\n    const xhr = new originalXHR();\r\n    const originalOpen = xhr.open;\r\n    const originalSend = xhr.send;\r\n    \r\n    let requestUrl = '';\r\n    let requestMethod = '';\r\n    let requestBody = null;\r\n    \r\n    xhr.open = function(method, url) {\r\n      requestUrl = url;\r\n      requestMethod = method;\r\n      if (typeof url === 'string' && (url.includes('/api/tags') || url.includes('/api/api'))) {\r\n        console.group('%c[标注调试] XHR 请求', 'background: #673AB7; color: white; font-size: 14px; padding: 2px 5px;');\r\n        console.log('URL:', url);\r\n        console.log('方法:', method);\r\n      }\r\n      return originalOpen.apply(this, arguments);\r\n    };\r\n    \r\n    xhr.send = function(body) {\r\n      requestBody = body;\r\n      if (typeof requestUrl === 'string' && (requestUrl.includes('/api/tags') || requestUrl.includes('/api/api'))) {\r\n        if (body) {\r\n          try {\r\n            const parsedBody = JSON.parse(body);\r\n            console.log('请求体:', parsedBody);\r\n          } catch (e) {\r\n            console.log('请求体 (无法解析):', body);\r\n          }\r\n        }\r\n      }\r\n      return originalSend.apply(this, arguments);\r\n    };\r\n    \r\n    // 监听加载完成事件\r\n    xhr.addEventListener('load', function() {\r\n      if (typeof requestUrl === 'string' && (requestUrl.includes('/api/tags') || requestUrl.includes('/api/api'))) {\r\n        console.log('状态:', xhr.status, xhr.statusText);\r\n        if (xhr.responseText) {\r\n          try {\r\n            const responseData = JSON.parse(xhr.responseText);\r\n            console.log('响应数据:', responseData);\r\n          } catch (e) {\r\n            console.log('响应数据 (无法解析):', xhr.responseText);\r\n          }\r\n        }\r\n        console.groupEnd();\r\n      }\r\n    });\r\n    \r\n    // 监听错误事件\r\n    xhr.addEventListener('error', function() {\r\n      if (typeof requestUrl === 'string' && (requestUrl.includes('/api/tags') || requestUrl.includes('/api/api'))) {\r\n        console.group('%c[标注调试] XHR 错误', 'background: #F44336; color: white; font-size: 14px; padding: 2px 5px;');\r\n        console.error('请求失败:', {\r\n          url: requestUrl,\r\n          method: requestMethod,\r\n          status: xhr.status,\r\n          statusText: xhr.statusText,\r\n          responseText: xhr.responseText\r\n        });\r\n        console.groupEnd();\r\n      }\r\n    });\r\n    \r\n    return xhr;\r\n  };\r\n  \r\n  return true;\r\n};\r\n\r\n// 显示标注数据\r\nexport const displayTagData = (tagData) => {\r\n  console.group('%c标注数据详情', 'background: #FF9800; color: white; font-size: 14px; padding: 3px 8px;');\r\n  console.log('标签类型:', tagData.tag);\r\n  console.log('坐标:', `(${tagData.x}, ${tagData.y})`);\r\n  console.log('尺寸:', `${tagData.width} x ${tagData.height}`);\r\n  console.log('图像ID:', tagData.metadata_id);\r\n  console.log('创建者:', tagData.created_by);\r\n  console.groupEnd();\r\n  \r\n  return {\r\n    标签类型: tagData.tag,\r\n    中心点X坐标: tagData.x,\r\n    中心点Y坐标: tagData.y, \r\n    宽度: tagData.width,\r\n    高度: tagData.height,\r\n    图像ID: tagData.metadata_id,\r\n    创建者ID: tagData.created_by\r\n  };\r\n};\r\n\r\n// 导出工具函数\r\nexport default {\r\n  enableAnnotationDebug,\r\n  displayTagData\r\n}; ", "// webpack自定义入口点\nconsole.log('正在启动应用，应用模块修复...');\n\n// 导入统一修复模块\nrequire('./src/element-plus-unified-fix.js');\n\n// 不再需要注入tryNextPath函数，它已在统一修复文件中定义\n// if (typeof global !== 'undefined' && !global.tryNextPath) {\n//   global.tryNextPath = function(paths, index) {\n//     if (!paths || index >= paths.length) return '';\n//     return paths[index];\n//   };\n// }\n\n// 不再需要为浏览器注入tryNextPath\n// if (typeof window !== 'undefined' && !window.tryNextPath) {\n//   window.tryNextPath = function(paths, index) {\n//     if (!paths || index >= paths.length) return '';\n//     return paths[index];\n//   };\n// }\n\n// 导入真正的入口文件\nrequire('./src/main.js'); ", "/**\n * API 配置文件\n * 统一管理API基础URL和上下文路径\n */\n\n// 获取环境变量或使用默认值 - 修改为使用相对路径\n// 在开发环境下，如果是通过内网穿透访问，我们应该使用相对路径\n// 这样请求会发送到当前域名，而不是硬编码的IP地址\nconst API_BASE_URL = ''; // 使用空字符串表示相对于当前域名\nconst API_CONTEXT_PATH = process.env.VUE_APP_CONTEXT_PATH || '/medical';\nconst API_PREFIX = process.env.VUE_APP_API_PREFIX || '/api';\n\n// 构建完整的API URL\nconst API_URL = `${API_BASE_URL}${API_CONTEXT_PATH}${API_PREFIX}`;\n\n// 常用URL - 修改为标准仪表盘API，不再使用无限制版本\nconst DASHBOARD_STATS_URL = `${API_CONTEXT_PATH}/api/stats-v2/dashboard`;\n// 确保仪表盘使用标准API且添加缓存控制参数\nconst DASHBOARD_STATS_WITH_PARAMS = (userId) => {\n  const timestamp = Date.now();\n  const random = Math.random();\n  // 注意: userId必须为数字类型ID (如3)，不能使用自定义ID (如300000001)\n  return `${DASHBOARD_STATS_URL}/${userId}?t=${timestamp}&r=${random}&forcePersonalStats=true&view=personal`;\n};\n\n// 添加缺失的isLocalhost函数，并修改为始终返回true，以便在任何环境下都使用相对路径\nconst isLocalhost = () => {\n  return true; // 始终返回true，确保imageHelper使用相对路径\n};\n\n// 导出配置\nexport {\n  API_BASE_URL,\n  API_CONTEXT_PATH,\n  API_PREFIX,\n  API_URL,\n  DASHBOARD_STATS_URL,\n  DASHBOARD_STATS_WITH_PARAMS,\n  isLocalhost\n}; ", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"js/\" + chunkId + \".\" + {\"9\":\"a8cdb7fa\",\"35\":\"0d08ecdb\",\"52\":\"8a412771\",\"80\":\"7320b977\",\"86\":\"01342c10\",\"110\":\"9a6803eb\",\"191\":\"7d6c1f07\",\"282\":\"8b1cf9e0\",\"301\":\"13bfe164\",\"344\":\"e01b8cd7\",\"359\":\"7209d510\",\"381\":\"cd9492de\",\"402\":\"78ca37b4\",\"453\":\"aad7fb15\",\"556\":\"eb2b18ef\",\"669\":\"8db92e4a\",\"751\":\"e1cc22ae\",\"763\":\"c78ee3c3\",\"976\":\"e6e99f46\",\"989\":\"bf4e9507\"}[chunkId] + \".js\";\n};", "// This function allow to reference async chunks\n__webpack_require__.miniCssF = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"css/\" + chunkId + \".\" + {\"9\":\"31434080\",\"35\":\"7abf8db7\",\"52\":\"bb541bc8\",\"80\":\"50e3f2d8\",\"110\":\"308ad403\",\"191\":\"63574b3d\",\"282\":\"99991a56\",\"301\":\"92c0830f\",\"344\":\"99c5710a\",\"359\":\"e86ff1d2\",\"381\":\"02384c7a\",\"402\":\"4008058f\",\"453\":\"44d27529\",\"556\":\"2974cbd2\",\"669\":\"6fb84a2d\",\"751\":\"9a68c00b\",\"763\":\"0d375eec\",\"838\":\"53b09eb3\",\"976\":\"f143d9f0\",\"989\":\"12cb3ed1\"}[chunkId] + \".css\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "var inProgress = {};\nvar dataWebpackPrefix = \"medical-annotation-frontend:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"/\";", "if (typeof document === \"undefined\") return;\nvar createStylesheet = (chunkId, fullhref, oldTag, resolve, reject) => {\n\tvar linkTag = document.createElement(\"link\");\n\n\tlinkTag.rel = \"stylesheet\";\n\tlinkTag.type = \"text/css\";\n\tif (__webpack_require__.nc) {\n\t\tlinkTag.nonce = __webpack_require__.nc;\n\t}\n\tvar onLinkComplete = (event) => {\n\t\t// avoid mem leaks.\n\t\tlinkTag.onerror = linkTag.onload = null;\n\t\tif (event.type === 'load') {\n\t\t\tresolve();\n\t\t} else {\n\t\t\tvar errorType = event && event.type;\n\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + errorType + \": \" + realHref + \")\");\n\t\t\terr.name = \"ChunkLoadError\";\n\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n\t\t\terr.type = errorType;\n\t\t\terr.request = realHref;\n\t\t\tif (linkTag.parentNode) linkTag.parentNode.removeChild(linkTag)\n\t\t\treject(err);\n\t\t}\n\t}\n\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n\tlinkTag.href = fullhref;\n\n\n\tif (oldTag) {\n\t\toldTag.parentNode.insertBefore(linkTag, oldTag.nextSibling);\n\t} else {\n\t\tdocument.head.appendChild(linkTag);\n\t}\n\treturn linkTag;\n};\nvar findStylesheet = (href, fullhref) => {\n\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n\tfor(var i = 0; i < existingLinkTags.length; i++) {\n\t\tvar tag = existingLinkTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return tag;\n\t}\n\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n\tfor(var i = 0; i < existingStyleTags.length; i++) {\n\t\tvar tag = existingStyleTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\");\n\t\tif(dataHref === href || dataHref === fullhref) return tag;\n\t}\n};\nvar loadStylesheet = (chunkId) => {\n\treturn new Promise((resolve, reject) => {\n\t\tvar href = __webpack_require__.miniCssF(chunkId);\n\t\tvar fullhref = __webpack_require__.p + href;\n\t\tif(findStylesheet(href, fullhref)) return resolve();\n\t\tcreateStylesheet(chunkId, fullhref, null, resolve, reject);\n\t});\n}\n// object to store loaded CSS chunks\nvar installedCssChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.miniCss = (chunkId, promises) => {\n\tvar cssChunks = {\"9\":1,\"35\":1,\"52\":1,\"80\":1,\"110\":1,\"191\":1,\"282\":1,\"301\":1,\"344\":1,\"359\":1,\"381\":1,\"402\":1,\"453\":1,\"556\":1,\"669\":1,\"751\":1,\"763\":1,\"838\":1,\"976\":1,\"989\":1};\n\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n\t\tpromises.push(installedCssChunks[chunkId] = loadStylesheet(chunkId).then(() => {\n\t\t\tinstalledCssChunks[chunkId] = 0;\n\t\t}, (e) => {\n\t\t\tdelete installedCssChunks[chunkId];\n\t\t\tthrow e;\n\t\t}));\n\t}\n};\n\n// no hmr\n\n// no prefetching\n\n// no preloaded", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(838 != chunkId) {\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t} else installedChunks[chunkId] = 0;\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkmedical_annotation_frontend\"] = self[\"webpackChunkmedical_annotation_frontend\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], () => (__webpack_require__(72068)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["window", "tryNextPath", "process", "browser", "env", "<PERSON><PERSON><PERSON>", "require", "global", "util", "_extend", "Object", "assign", "_createBlock", "_component_router_view", "name", "__exports__", "render", "class", "_component_el_container", "_withCtx", "_createVNode", "_component_el_aside", "width", "_createElementVNode", "_component_el_menu", "$options", "activeMenu", "_component_el_menu_item", "index", "onClick", "_cache", "$event", "_ctx", "$router", "push", "_component_el_icon", "_component_HomeFilled", "_", "__", "_component_Document", "navigateToHemangiomaDiagnosis", "_component_Picture", "isAdmin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_component_Check", "_createCommentVNode", "_component_UserFilled", "_component_User", "_component_el_header", "height", "_hoisted_1", "$data", "showDashboardButton", "_component_el_button", "type", "icon", "_createTextVNode", "showBackButton", "goBack", "_hoisted_2", "_component_el_dropdown", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "_component_router_link", "to", "style", "isDoctor", "hasAppliedForReviewer", "handleApplyForReviewer", "disabled", "handleLogout", "_hoisted_3", "_component_el_avatar", "size", "_toDisplayString", "username", "_normalizeClass", "roleTagClass", "userRoleText", "_component_el_main", "components", "HomeFilled", "Document", "Check", "User", "UserFilled", "Picture", "data", "pollTimer", "computed", "_objectSpread", "mapGetters", "currentUser", "hasPendingApplications", "pendingApplicationsCount", "this", "$route", "path", "user", "JSON", "parse", "localStorage", "getItem", "role", "err", "console", "error", "watch", "currentUserId", "customId", "id", "lastUserId", "log", "refreshDashboardStats", "setTimeout", "setItem", "newVal", "oldVal", "created", "loadUserInfo", "fetchTeamApplicationsCount", "mounted", "startPendingApplicationsPolling", "beforeUnmount", "stopPendingApplicationsPolling", "methods", "_this", "api", "users", "getReviewerApplicationStatus", "then", "response", "status", "$store", "dispatch", "_this2", "setInterval", "clearInterval", "location", "href", "sessionStorage", "removeItem", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "go", "checkReviewerApplication", "_this4", "length", "some", "app", "_this5", "$prompt", "inputType", "inputPlaceholder", "_ref", "value", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$message", "success", "message", "warning", "e", "navigateToProfile", "_createElementBlock", "_component_el_row", "gutter", "_component_el_col", "xs", "sm", "md", "lg", "xl", "manualRefresh", "_component_Refresh", "_component_el_card", "shadow", "navigateTo", "_hoisted_4", "dashboardStats", "totalCount", "_hoisted_5", "_hoisted_6", "reviewedCount", "_hoisted_7", "_hoisted_8", "submittedCount", "_hoisted_9", "_hoisted_10", "approvedCount", "_hoisted_11", "_hoisted_12", "_hoisted_13", "globalStatsCache", "rejectedCount", "Loading", "Refresh", "Plus", "from", "beforeRouteEnter", "next", "vm", "addEventListener", "handlePageFocus", "activated", "removeEventListener", "mapActions", "query", "formatDate", "date", "Date", "toLocaleString", "_asyncToGenerator", "_regenerator", "m", "_callee", "userId", "userStr", "loading", "url", "_t", "w", "_context", "n", "a", "$loading", "lock", "text", "p", "DASHBOARD_STATS_WITH_PARAMS", "fetch", "method", "headers", "credentials", "v", "ok", "Error", "concat", "json", "parseInt", "$forceUpdate", "close", "f", "state", "storageUtils", "getFromStorage", "isAuthenticated", "currentUserDetails", "getters", "getUser", "getUserRole", "getUserId", "hasPermission", "permission", "_state$user", "canAccessRoute", "route", "_state$user2", "canAccessResource", "resourceOwnerId", "getAllUsers", "getCurrentUserDetails", "isUsersLoading", "getUsersError", "mutations", "setUser", "setUsers", "setCurrentUserDetails", "setLoading", "setError", "actions", "login", "commit", "rawData", "asyncActionHandler", "start", "auth", "email", "department", "hospital", "team", "description", "warn", "split", "startsWith", "saveToStorage", "end", "register", "_ref2", "userData", "_callee2", "_t2", "_context2", "logout", "_ref3", "removeFromStorage", "fetchUserProfile", "_ref4", "_callee3", "_t3", "_context3", "updateUserProfile", "_ref5", "_callee4", "updatedUser", "_t4", "_context4", "updateUser", "fetchUsers", "_ref6", "_callee5", "_t5", "_context5", "getAll", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authorization", "btoa", "password", "images", "currentImage", "getAllImages", "getCurrentImage", "isImagesLoading", "getImagesError", "setImages", "setCurrentImage", "image", "addImage", "unshift", "updateImage", "updatedImage", "findIndex", "img", "splice", "removeImage", "imageId", "filter", "fetchImages", "axios", "API_URL", "Array", "isArray", "slice", "_typeof", "content", "items", "values", "item", "fetchImagesByStatus", "fetchImageById", "createImage", "imageData", "updateImageStatus", "_ref7", "_ref8", "_callee6", "reviewerId", "reviewNotes", "_t6", "_context6", "params", "deleteImage", "_ref9", "_callee7", "errorMessage", "_t7", "_context7", "stringify", "fetchStats", "_ref0", "_callee8", "_context8", "rootState", "root", "getCurrentUser", "fetchUser", "stats", "draftCount", "getStats", "isStatsLoading", "getStatsError", "getImagesStats", "setStats", "pendingCount", "setStatField", "field", "hasOwnProperty", "defaultStats", "getDashboard", "setManualStats", "statsData", "step", "formData", "testMode", "imagePath", "lastUpdated", "getAnnotationProgress", "hasUnfinishedAnnotation", "getCurrentStep", "getImageId", "getFormData", "isTestMode", "getImagePath", "getStructuredFormProgress", "saveProgress", "progress", "completeAnnotation", "updateFormData", "saveAnnotationProgress", "clearAnnotationProgress", "SET_PROGRESS", "simplifiedFormData", "keys", "for<PERSON>ach", "map", "toISOString", "UPDATE_FORM_DATA", "COMPLETE_ANNOTATION", "getPendingApplicationsCount", "getLastUpdated", "setPendingCount", "count", "incrementPendingCount", "decrementPendingCount", "resetPendingCount", "fetchPendingApplicationsCount", "_user$team", "teamId", "teams", "getTeamApplications", "processApplication", "action", "namespaced", "createStore", "payload", "modules", "annotation", "teamApplications", "plugins", "createPersistedState", "paths", "storage", "i", "DebugView", "Heman<PERSON>ma<PERSON>sis", "routes", "component", "MainLayout", "meta", "requiresAuth", "children", "redirect", "Dashboard", "keepAlive", "title", "props", "accessRoles", "requiresAdmin", "SimpleLayout", "router", "createRouter", "history", "createWebHistory", "beforeEach", "从", "fullPath", "到", "时间", "toLocaleTimeString", "query参数", "来源页面", "document", "referrer", "isNavigatingAfterSave", "isNavigatingFromForm", "publicPages", "authRequired", "includes", "matched", "record", "store", "userRole", "目标路由", "用户角色", "路由权限要求", "是否需要管理员", "是否需要审核医生", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "是否需要标注医生", "requiresDoctor", "hasAccess", "after<PERSON>ach", "当前URL", "导航状态", "isAppOperation", "returningToWorkbench", "isLogoutOperation", "currentRoute", "xhr", "XMLHttpRequest", "open", "DASHBOARD_STATS_URL", "now", "send", "responseText", "dataSource", "timestamp", "statElements", "querySelectorAll", "textContent", "el", "binding", "arg", "权限值", "参数", "hasAllPermissions", "every", "checkPermission", "disableElement", "hasAnyPermission", "roles", "display", "setAttribute", "pointerEvents", "classList", "add", "registerPermissionDirective", "directive", "VueComponents", "AnnotationReviewList", "ElMessage", "info", "fixUserPermission", "expectedRole", "originalSetItem", "originalRemoveItem", "call", "withCredentials", "common", "timeout", "request", "use", "config", "Promise", "reject", "createApp", "App", "<PERSON><PERSON><PERSON><PERSON>", "event", "_i", "_Object$entries", "entries", "ElementPlusIconsVue", "_Object$entries$_i", "_slicedToArray", "globalProperties", "$axios", "$notify", "ElNotification", "$msgbox", "ElMessageBox", "$alert", "alert", "confirm", "prompt", "ElLoading", "service", "ElementPlus", "locale", "zhCn", "disableConsoleOutput", "debug", "mount", "retryElements", "get", "API_BASE_URL", "API_CONTEXT_PATH", "Math", "random", "originalPushState", "pushState", "originalReplaceState", "replaceState", "状态", "arguments", "标题", "URL", "当前时间", "调用方法", "stack", "apply", "eventBus", "mitt", "$enableAnnotationDebug", "enableAnnotationDebug", "$isAnnotationDebugEnabled", "$setup", "isStandalone", "isViewingDetail", "backToList", "selectedAnnotation", "_hoisted_14", "_hoisted_15", "image_two_path", "processedImagePath", "_component_el_image", "src", "getFullImageUrl", "fit", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "label", "formattedId", "_component_el_tag", "getStatusTagType", "getStatusDisplayText", "_$setup$selectedAnnot", "submittedBy", "getDoctorNameById", "team_id", "getTeamNameById", "formatDateTime", "createdAt", "updatedAt", "caseNumber", "detectedType", "_hoisted_19", "patientName", "patientAge", "gender", "span", "bodyPart", "lesionLocation", "color", "vesselTexture", "symptomDescription", "_hoisted_20", "diagnosticSummary", "treatmentSuggestion", "precautions", "_hoisted_21", "_component_el_input", "modelValue", "rows", "placeholder", "_hoisted_22", "_hoisted_23", "_hoisted_24", "plain", "_Fragment", "approveAnnotation", "showRejectDialog", "_component_el_alert", "closable", "searchQuery", "clearable", "onChange", "loadAnnotations", "_component_el_select", "filters", "_renderList", "_component_el_option", "_component_el_table", "annotations", "stripe", "getRowClassName", "onRowClick", "showAnnotationDetail", "_component_el_table_column", "prop", "default", "scope", "row", "submittedAt", "uploadedBy", "effect", "_component_el_tooltip", "placement", "fixed", "_withModifiers", "_component_el_pagination", "background", "layout", "pageSize", "total", "currentPage", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "rejectDialogVisible", "footer", "_hoisted_25", "submitting", "rejectAnnotation", "_component_el_form", "_component_el_form_item", "standaloneMode", "Boolean", "setup", "ref", "reactive", "useRouter", "useRoute", "loadCurrentUser", "loadTeams", "getAllTeams", "userInfo", "page", "sort", "search", "find", "t", "totalElements", "canReview", "annotationTeamId", "userTeamId", "diagnosisId", "_x", "_apiError$response", "_apiError$response2", "backupResponse", "_error$response", "_t8", "reviews", "approve<PERSON><PERSON>iew", "_x2", "_apiError$response3", "_apiError$response4", "_error$response2", "_t9", "_t0", "_t1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rejectReviewBackup", "dateTimeString", "isNaN", "getTime", "Intl", "DateTimeFormat", "year", "month", "day", "hour", "minute", "format", "_annotation$imagePair", "_annotation$imagePair2", "imagePair", "possiblePaths", "imageTwoPath", "annotated_image_path", "annotatedImagePath", "image_path", "_possiblePaths", "baseUrl", "apiConfig", "origin", "normalizedPath", "separator", "onMounted", "baseURL", "debugAnnotationReview", "getUserInfo", "directFetchApi", "_directFetchApi", "_data$content", "_t10", "doctorId", "statusMap", "typeMap", "apiClient", "VUE_APP_API_BASE_URL", "interceptors", "normalizePath", "toUpperCase", "paramSeparator", "sessionUserStr", "userProfileStr", "userProfile", "URLSearchParams", "has", "set", "toLowerCase", "timestampParam", "transformResponse", "_toConsumableArray", "头部", "数据", "请求URL", "请求方法", "状态码", "响应数据", "fileApiClient", "debugApiClient", "attemptAutoFix", "sessionAuth", "authData", "token", "recoverSessionAfterRefresh", "_refresh", "queryString", "encodeURIComponent", "join", "fullUrl", "originalUrl", "normalizedUrl", "substring", "patterns", "replace", "_patterns", "pattern", "RegExp", "defaults", "retryRequestWithStandardAuth", "newConfig", "emailBase64", "_retry", "extractedArray", "members", "nested<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>s", "membersFromKeys", "user_id", "member", "records", "list", "paged<PERSON><PERSON>y", "metadata_id", "imagesFromValues", "imageStatus", "isTeamApplicationRequest", "isAnnotationRelatedRequest", "isInAnnotationFlow", "pathname", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTeamPage", "isCasesRelatedRequest", "isCasePage", "autoFixAttempted", "emptyResponse", "statusText", "resolve", "teamAuthError", "<PERSON><PERSON><PERSON><PERSON>", "preservedUser", "currentPath", "_error$config", "getUserIdParam", "post", "_defineProperty", "updateProfile", "profileData", "put", "changePassword", "passwordData", "uploadAvatar", "reason", "createUser", "deleteUser", "resetPassword", "newPassword", "getAllReviewers", "getPendingReviewerApplications", "processReviewerApplication", "applicationId", "getUsersWithoutTeam", "sendResetPasswordEmail", "verifyResetCode", "code", "create", "teamData", "getOne", "joinTeam", "applyToJoinTeam", "requestData", "processTeamApplication", "getUserApplications", "idParam", "getTeamMembers", "getTeamAnnotations", "getAnnotationDetail", "annotationId", "removeTeamMember", "checkUserInTeam", "isMember", "deleteTeam", "transferOwnership", "newOwnerId", "reviewer", "getProcessedApplications", "_images", "upload", "file", "FormData", "append", "timeoutDuration", "文件名", "文件大小", "文件类型", "用户ID", "saveToPath", "saveAnnotatedImage", "processedImageFile", "undefined", "any", "mode", "errors", "update", "metadata", "submitForReview", "reviewImage", "approved", "customUserId", "directUrl", "annotationPromises", "tagData", "tag", "x", "y", "created_by", "saveTag", "all", "results", "reviewTimestamp", "request<PERSON>ey", "pendingRequests", "isDirectNavigation", "savedStatus", "tags", "getByImageId", "getById", "cleanData", "标注ID", "自定义ID", "deleteByImageId", "getByUserId", "updateAnnotatedImage", "imagePairId", "_role", "generateAnnotatedImage", "imagePairs", "getByMetadataId", "metadataId", "toString", "fullId", "directPath", "imagePairData", "deleteByMetadataId", "deleteAnnotatedImage", "getPendingApplications", "imageLogs", "logOperation", "logData", "getTimezoneInfo", "PERMISSIONS", "MANAGE_USERS", "MANAGE_TEAMS", "VIEW_STATS", "VIEW_TEAM", "JOIN_TEAM", "LEAVE_TEAM", "CREATE_CASE", "EDIT_CASE", "DELETE_CASE", "VIEW_ALL_CASES", "VIEW_OWN_CASES", "REVIEW_CASES", "APPROVE_CASES", "ANNOTATE_IMAGES", "VIEW_OWN_ANNOTATIONS", "EDIT_OWN_ANNOTATIONS", "UPLOAD_IMAGES", "DELETE_IMAGES", "ROLE_PERMISSIONS", "ADMIN", "DOCTOR", "REVIEWER", "ROUTE_PERMISSIONS", "_ROLE_PERMISSIONS$use", "routePath", "isString", "val", "isNumber", "isBoolean", "isObject", "isFunction", "isDate", "isPromise", "Size", "LARGE", "DEFAULT", "SMALL", "buildProp", "buildProps", "definePropType", "mutable", "TOKEN", "Symbol", "TOOLTIP_INJECTION_KEY", "fixElementPlusImports", "__EP_UTILS_TYPES__", "__EP_VIRTUAL_MODULES__", "originalImport", "__webpack_require__", "moduleId", "defaultValue", "loadingMutation", "errorMutation", "responseData", "originalFetch", "originalXHR", "ANNOTATION_DEBUG_ENABLED", "options", "group", "body", "groupEnd", "clonedResponse", "clone", "originalOpen", "originalSend", "requestUrl", "requestMethod", "parsedBody", "displayTagData", "标签类型", "中心点X坐标", "中心点Y坐标", "宽度", "高度", "图像ID", "创建者ID", "API_PREFIX", "isLocalhost", "__webpack_module_cache__", "cachedModule", "exports", "module", "__webpack_modules__", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "r", "getter", "__esModule", "d", "definition", "o", "defineProperty", "enumerable", "chunkId", "reduce", "promises", "u", "miniCssF", "g", "globalThis", "Function", "obj", "prototype", "inProgress", "dataWebpackPrefix", "l", "done", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "getElementsByTagName", "s", "getAttribute", "createElement", "charset", "nc", "onScriptComplete", "prev", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "bind", "target", "head", "append<PERSON><PERSON><PERSON>", "toStringTag", "createStylesheet", "fullhref", "oldTag", "linkTag", "rel", "nonce", "onLinkComplete", "errorType", "realHref", "insertBefore", "nextS<PERSON>ling", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "existingLinkTags", "dataHref", "existingStyleTags", "loadStylesheet", "installedCssChunks", "miniCss", "cssChunks", "installedChunks", "installedChunkData", "promise", "loadingEnded", "realSrc", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}