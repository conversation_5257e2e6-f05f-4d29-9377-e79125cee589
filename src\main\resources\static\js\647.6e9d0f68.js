"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[647],{22647:(e,t,a)=>{a.r(t),a.d(t,{default:()=>x});a(44114);var n=a(20641),s=a(90033),r={class:"cases-container"},i={class:"page-header"},u={key:0},o={key:1},l={key:2},c={class:"filter-row"},d={key:0,class:"text-center my-5"},g={key:2,class:"text-center my-5"},f={key:0},h={key:1},p={key:2};function m(e,t,a,m,b,E){var v=(0,n.g2)("el-button"),C=(0,n.g2)("el-option"),y=(0,n.g2)("el-select"),A=(0,n.g2)("el-form-item"),I=(0,n.g2)("el-form"),k=(0,n.g2)("el-table-column"),D=(0,n.g2)("status-badge"),S=(0,n.g2)("el-table"),_=(0,n.g2)("el-pagination"),T=(0,n.gN)("permission"),F=(0,n.gN)("loading");return(0,n.uX)(),(0,n.CE)("div",r,[(0,n.Lk)("div",i,[(0,n.Lk)("h2",null,[e.isAdmin?((0,n.uX)(),(0,n.CE)("span",u,"病例标注管理")):e.isReviewer?((0,n.uX)(),(0,n.CE)("span",o,"待审核病例")):((0,n.uX)(),(0,n.CE)("span",l,"我的病例标注"))]),(0,n.bo)(((0,n.uX)(),(0,n.Wv)(v,{type:"primary",onClick:t[0]||(t[0]=function(t){return e.$router.push("/app/cases/new")})},{default:(0,n.k6)((function(){return t[2]||(t[2]=[(0,n.eW)("新建标注")])})),_:1,__:[2]})),[[T,"create_case"]])]),(0,n.Lk)("div",c,[(0,n.bF)(I,{inline:!0,class:"filter-form"},{default:(0,n.k6)((function(){return[(0,n.bF)(A,{label:"状态"},{default:(0,n.k6)((function(){return[(0,n.bF)(y,{modelValue:b.filterStatus,"onUpdate:modelValue":t[1]||(t[1]=function(e){return b.filterStatus=e}),placeholder:"选择状态",clearable:"",onChange:E.handleFilterChange},{default:(0,n.k6)((function(){return[(0,n.bF)(C,{label:"未标注",value:"DRAFT"}),(0,n.bF)(C,{label:"已标注",value:"REVIEWED"}),(0,n.bF)(C,{label:"待审核",value:"SUBMITTED"}),(0,n.bF)(C,{label:"已通过",value:"APPROVED"}),(0,n.bF)(C,{label:"已驳回",value:"REJECTED"})]})),_:1},8,["modelValue","onChange"])]})),_:1}),(0,n.bF)(A,null,{default:(0,n.k6)((function(){return[(0,n.bF)(v,{type:"primary",onClick:E.handleFilterChange},{default:(0,n.k6)((function(){return t[3]||(t[3]=[(0,n.eW)("查询")])})),_:1,__:[3]},8,["onClick"]),(0,n.bF)(v,{onClick:E.resetFilter},{default:(0,n.k6)((function(){return t[4]||(t[4]=[(0,n.eW)("重置")])})),_:1,__:[4]},8,["onClick"])]})),_:1})]})),_:1})]),e.loading?((0,n.uX)(),(0,n.CE)("div",d,t[5]||(t[5]=[(0,n.Lk)("div",{class:"spinner-border",role:"status"},[(0,n.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):(0,n.bo)(((0,n.uX)(),(0,n.Wv)(S,{key:1,data:b.casesList,style:{width:"100%"}},{default:(0,n.k6)((function(){return[(0,n.bF)(k,{prop:"caseId",label:"病例编号",width:"180"}),(0,n.bF)(k,{prop:"department",label:"部位"}),(0,n.bF)(k,{prop:"type",label:"类型"}),(0,n.bF)(k,{prop:"status",label:"状态"},{default:(0,n.k6)((function(e){return[(0,n.bF)(D,{status:E.getStatusValue(e.row.status)},null,8,["status"])]})),_:1}),(0,n.bF)(k,{prop:"createTime",label:"创建时间"}),(0,n.bF)(k,{label:"操作",width:"220"},{default:(0,n.k6)((function(e){return[(0,n.bo)(((0,n.uX)(),(0,n.Wv)(v,{link:"",size:"small",onClick:function(t){return E.handleEdit(e.row)}},{default:(0,n.k6)((function(){return t[6]||(t[6]=[(0,n.eW)(" 编辑 ")])})),_:2,__:[6]},1032,["onClick"])),[[T,"edit_case"]]),(0,n.bF)(v,{link:"",size:"small",onClick:function(t){return E.handleView(e.row)}},{default:(0,n.k6)((function(){return t[7]||(t[7]=[(0,n.eW)(" 查看 ")])})),_:2,__:[7]},1032,["onClick"]),(0,n.bo)(((0,n.uX)(),(0,n.Wv)(v,{link:"",type:"danger",size:"small",onClick:function(t){return E.handleDelete(e.row)}},{default:(0,n.k6)((function(){return t[8]||(t[8]=[(0,n.eW)(" 删除 ")])})),_:2,__:[8]},1032,["onClick"])),[[T,"ADMIN","role"]])]})),_:1})]})),_:1},8,["data"])),[[F,b.tableLoading]]),e.loading||0!==b.casesList.length?(0,n.Q3)("",!0):((0,n.uX)(),(0,n.CE)("div",g,[b.allImages&&b.allImages.length>0&&b.filterStatus?((0,n.uX)(),(0,n.CE)("p",f," 没有符合条件的病例，当前筛选状态: "+(0,s.v_)(E.getStatusText(b.filterStatus)),1)):b.allImages&&b.allImages.length>0?((0,n.uX)(),(0,n.CE)("p",h," 暂无病例匹配当前筛选条件 ")):((0,n.uX)(),(0,n.CE)("p",p," 暂无病例标注数据 "))])),b.totalItems>0?((0,n.uX)(),(0,n.Wv)(_,{key:3,background:"",layout:"total, sizes, prev, pager, next, jumper",total:b.totalItems,"page-size":b.pageSize,"page-sizes":[10,20,50,100],"current-page":b.currentPage,onCurrentChange:E.handlePageChange,onSizeChange:E.handleSizeChange},null,8,["total","page-size","current-page","onCurrentChange","onSizeChange"])):(0,n.Q3)("",!0)])}var b=a(14048),E=a(59258),v=a(30388),C=a(41034),y=(a(28706),a(2008),a(64346),a(62062),a(34782),a(26910),a(60739),a(23288),a(18111),a(22489),a(61701),a(33110),a(26099),a(58940),a(47764),a(42762),a(62953),a(40834)),A=a(69553),I=a(38221),k=a.n(I),D=a(653),S=a(72505),_=a.n(S),T={class:"status-badge"};function F(e,t,a,r,i,u){var o=(0,n.g2)("el-tag");return(0,n.uX)(),(0,n.CE)("div",T,[(0,n.bF)(o,{type:u.tagType,effect:u.effect,size:"medium"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,s.v_)(u.displayText),1)]})),_:1},8,["type","effect"])])}a(74423);const w={name:"StatusBadge",props:{status:{type:String,required:!0,validator:function(e){return["DRAFT","REVIEWED","SUBMITTED","APPROVED","REJECTED"].includes(e)}},plain:{type:Boolean,default:!1}},computed:{tagType:function(){var e={DRAFT:"info",REVIEWED:"warning",SUBMITTED:"primary",APPROVED:"success",REJECTED:"danger"};return e[this.status]||"info"},displayText:function(){var e={DRAFT:"未标注",REVIEWED:"已标注",SUBMITTED:"待审核",APPROVED:"已通过",REJECTED:"已驳回"};return e[this.status]||this.status},effect:function(){return this.plain?"plain":"light"}}};var R=a(66262);const P=(0,R.A)(w,[["render",F],["__scopeId","data-v-3bce608d"]]),L=P,V={name:"CasesList",components:{StatusBadge:L},data:function(){return{casesList:[],pageSize:10,currentPage:1,totalItems:0,filterStatus:"",tableLoading:!1,myImages:[],needReviewImages:[],allImages:[]}},computed:(0,C.A)((0,C.A)({},(0,y.L8)({images:"getAllImages",loading:"isLoading",error:"getError",isAdmin:"isAdmin",isDoctor:"isDoctor",isReviewer:"isReviewer",hasPermission:"hasPermission",currentUserId:"getUserId",canAccessResource:"canAccessResource"})),{},{filteredImages:function(){var e=this.filterStatus||this.$route.query.status,t=this.allImages;return e?t.filter((function(t){var a=t.status||"DRAFT";return a===e})):t}}),methods:(0,C.A)((0,C.A)({},(0,y.i0)(["fetchImages"])),{},{getStatusType:function(e){var t={未标注:"info",已标注:"success",待审核:"warning",已通过:"success",已驳回:"danger"};return t[e]||"info"},getStatusValue:function(e){var t={未标注:"DRAFT",已标注:"REVIEWED",待审核:"SUBMITTED",已通过:"APPROVED",已驳回:"REJECTED"};return t[e]||"DRAFT"},getStatusText:function(e){var t={DRAFT:"未标注",REVIEWED:"已标注",SUBMITTED:"待审核",APPROVED:"已通过",REJECTED:"已驳回"};return t[e]||e},handleEdit:function(e){this.canEditCase(e)?(localStorage.setItem("isEditingCase","true"),this.$router.push({path:"/app/cases/form",query:{imageId:e.id,edit:"true"}})):this.$message.error("您没有权限编辑此病例")},handleView:function(e){this.canViewCase(e)?this.$router.push("/app/cases/view/".concat(e.id)):this.$message.error("您没有权限查看此病例")},handleDelete:function(e){var t=this;this.canDeleteCase(e)?this.$confirm("此操作将永久删除该病例及其所有相关数据，是否继续?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n=t.$loading({lock:!0,text:"删除中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Promise.resolve().then(a.bind(a,653)).then((function(a){a["default"].images["delete"](e.id).then((function(){n.close(),t.$message.success("删除成功"),t.fetchCases()}))["catch"]((function(e){n.close();var a="删除失败";e.response&&e.response.data?a="string"===typeof e.response.data?e.response.data:JSON.stringify(e.response.data):a+=": "+(e.message||"未知错误"),t.$message({type:"error",message:a,duration:5e3})}))}))}))["catch"]((function(){t.$message.info("已取消删除")})):this.$message.error("您没有权限删除此病例")},handlePageChange:function(e){this.currentPage=e,this.processCases()},handleSizeChange:function(e){this.pageSize=e,this.currentPage=1,this.processCases()},handleFilterChange:k()((function(){this.currentPage=1,this.processCases(),this.$router.push({query:(0,C.A)((0,C.A)({},this.$route.query),{},{status:this.filterStatus||void 0,page:1!==this.currentPage?this.currentPage:void 0})})["catch"]((function(){}))}),300),resetFilter:function(){this.filterStatus="",this.handleFilterChange()},canEditCase:function(e){return!0},canViewCase:function(e){return!0},canDeleteCase:function(e){return!!this.isAdmin&&this.hasPermission(A.Jj.DELETE_CASE)},fetchCases:function(){var e=this;return(0,v.A)((0,b.A)().mark((function t(){var a,n,s,r,i;return(0,b.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.tableLoading=!0,a=parseInt(e.$route.query.page)||1,n=e.$route.query.status,e.currentPage=a,n&&(e.filterStatus=n),s="200000001",_().defaults.headers.common["X-User-Id"]=s,t.next=12,D["default"].images.getUserImages(e.filterStatus);case 12:if(r=t.sent,r&&r.data&&Array.isArray(r.data)){t.next=20;break}return e.allImages=[],e.myImages=[],e.needReviewImages=[],e.tableLoading=!1,t.abrupt("return");case 20:if(e.allImages=(0,E.A)(r.data),0!==e.allImages.length||!e.filterStatus){t.next=28;break}return t.next=26,D["default"].images.getUserImages();case 26:i=t.sent,i&&i.data&&Array.isArray(i.data)&&(e.allImages=(0,E.A)(i.data));case 28:e.processCases(),t.next=35;break;case 31:t.prev=31,t.t0=t["catch"](0),e.$message.error("获取病例列表失败: "+t.t0.message);case 35:return t.prev=35,e.tableLoading=!1,t.finish(35);case 38:case"end":return t.stop()}}),t,null,[[0,31,35,38]])})))()},processCases:function(){var e=this,t=this.filteredImages;if(0===t.length)return this.totalItems=0,void(this.casesList=[]);var a=(0,E.A)(t).sort((function(e,t){return e.createdAt?t.createdAt?new Date(t.createdAt)-new Date(e.createdAt):-1:1}));this.totalItems=a.length;var n=(this.currentPage-1)*this.pageSize,s=n+this.pageSize;this.casesList=a.slice(n,s).map((function(t){return{id:t.id,caseId:t.caseNumber||"CASE-".concat(t.id),department:t.lesionLocation||"未知",type:t.diagnosisCategory||"未知",status:e.getStatusText(t.status||"DRAFT"),createTime:e.formatDate(t.createdAt),rawDate:t.createdAt,creatorId:t.uploadedBy,hasAnnotation:!(!t.image_two_path||""===t.image_two_path.trim())}}))},formatDate:function(e){if(!e)return"未知";var t=new Date(e);return t.toLocaleString("zh-CN")},getFilteredCases:function(){var e=this,t=this.filteredImages;if(!t||0===t.length)return this.casesList=[],void(this.totalItems=0);var a=(this.currentPage-1)*this.pageSize,n=a+this.pageSize;this.casesList=t.slice(a,n).map((function(t){return{id:t.id,caseId:t.case_number||t.id,department:t.lesion_location||"未知",type:t.diagnosis_category||"未指定",status:e.getStatusText(t.status||"DRAFT"),uploadedBy:t.uploaded_by,createTime:e.formatDate(t.created_at),originalRecord:t}})),this.totalItems=t.length}}),created:function(){this.fetchCases()},watch:{"$route.query":{handler:function(e,t){if(!t)return e.status&&(this.filterStatus=e.status),void(e.page&&(this.currentPage=parseInt(e.page)||1));e.status===t.status&&e.page===t.page||(e.status!==t.status&&(this.filterStatus=e.status||""),e.page!==t.page&&(this.currentPage=parseInt(e.page)||1),this.fetchCases())},deep:!0,immediate:!0}}},z=(0,R.A)(V,[["render",m],["__scopeId","data-v-a6ea34b6"]]),x=z},59258:(e,t,a)=>{a.d(t,{A:()=>o});a(64346);var n=a(6562);function s(e){if(Array.isArray(e))return(0,n.A)(e)}a(52675),a(89463),a(2259),a(23418),a(26099),a(47764),a(62953);function r(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}var i=a(12635);a(16280),a(76918);function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function o(e){return s(e)||r(e)||(0,i.A)(e)||u()}}}]);