<template>
  <div class="image-annotator">
    <div class="annotation-toolbar">
      <div class="tool-section">
        <h3>标签分类</h3>
        <el-select v-model="selectedTag" placeholder="请选择标签类型" style="width: 100%">
          <el-option label="血管瘤" value="血管瘤"></el-option>
          <el-option label="淋巴管瘤" value="淋巴管瘤"></el-option>
          <el-option label="混合型" value="混合型"></el-option>
          <el-option label="其他" value="其他"></el-option>
        </el-select>
      </div>
      
      <div class="tool-section">
        <h3>标注工具</h3>
        <el-radio-group v-model="currentTool">
          <el-radio label="rectangle">矩形框</el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="annotation-workspace">
      <div class="image-container" ref="imageContainer">
        <img 
          v-if="imageUrl" 
          :src="imageUrl" 
          ref="annotationImage"
          class="annotation-image"
          @load="onImageLoad" 
          alt="医学影像"
        />
        
        <!-- 透明覆盖层，用于标注，与图片大小一致 -->
        <div 
          v-if="imageUrl"
          class="annotation-overlay"
          :style="{
            width: imageWidth + 'px',
            height: imageHeight + 'px'
          }"
          @mousedown="startDrawing"
          @mousemove="drawing"
          @mouseup="endDrawing"
          @mouseleave="cancelDrawing"
        ></div>
        
        <!-- 显示标注框 -->
        <div 
          v-for="(box, index) in annotations" 
          :key="index" 
          class="annotation-box"
          :style="{
            left: (box.x - box.width/2) * imageWidth + 'px',
            top: (box.y - box.height/2) * imageHeight + 'px',
            width: box.width * imageWidth + 'px',
            height: box.height * imageHeight + 'px',
            borderColor: getTagColor(box.tag)
          }"
        >
          <div class="tag-label">{{ box.tag }}</div>
        </div>
        
        <!-- 正在绘制的框 -->
        <div 
          v-if="isDrawing" 
          class="annotation-box drawing"
          :style="{
            left: Math.min(startX, currentX) + 'px',
            top: Math.min(startY, currentY) + 'px',
            width: Math.abs(currentX - startX) + 'px',
            height: Math.abs(currentY - startY) + 'px'
          }"
        ></div>
      </div>
    </div>

    <div class="annotation-list">
      <h3>已添加标注</h3>
      <el-table :data="annotations" style="width: 100%">
        <el-table-column label="编号" width="60">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="tag" label="标签" width="90" />
        <el-table-column label="位置" width="160">
          <template #default="scope">
            中心点: ({{ (scope.row.x * 100).toFixed(1) }}%, {{ (scope.row.y * 100).toFixed(1) }}%)
          </template>
        </el-table-column>
        <el-table-column label="尺寸" width="160">
          <template #default="scope">
            {{ (scope.row.width * 100).toFixed(1) }}% × {{ (scope.row.height * 100).toFixed(1) }}%
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button type="danger" size="small" @click="deleteAnnotation(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="actions">
        <el-button type="primary" @click="saveAnnotations" :disabled="annotations.length === 0">
          保存标注
        </el-button>
        <el-button type="danger" @click="clearAnnotations" :disabled="annotations.length === 0">
          清除所有
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import api from '../utils/api';

export default {
  name: 'ImageAnnotator',
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    imageId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      selectedTag: '血管瘤',
      currentTool: 'rectangle',
      annotations: [],
      isDrawing: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
      imageWidth: 0,
      imageHeight: 0,
      savedAnnotations: []
    };
  },
  mounted() {
    this.fetchSavedAnnotations();
  },
  methods: {
    onImageLoad() {
      const img = this.$refs.annotationImage;
      if (img) {
        // 获取图像的自然尺寸
        this.imageWidth = img.naturalWidth || img.width;
        this.imageHeight = img.naturalHeight || img.height;
        
        console.log('图像加载完成:', {
          url: this.imageUrl,
          width: this.imageWidth,
          height: this.imageHeight
        });
        
        if (!this.imageWidth || !this.imageHeight) {
          console.error('无法获取图像尺寸:', {
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight,
            width: img.width,
            height: img.height
          });
          this.$message.warning('无法获取图像尺寸，标注功能可能无法正常工作');
        }
      } else {
        console.error('找不到图像元素');
        this.$message.error('图像加载失败');
      }
    },
    
    startDrawing(event) {
      if (!this.selectedTag) {
        this.$message.warning('请先选择标签类型');
        return;
      }
      
      // 先检查图像状态
      api.images.getOne(this.imageId)
        .then(response => {
          const imageData = response.data;
          // 检查图像状态是否为草稿状态
          if (imageData.status && imageData.status !== 'DRAFT') {
            this.$message.error(`当前图像状态为"${imageData.status}"，只有草稿状态才能添加标注`);
            return;
          }
          
          // 如果是草稿状态，则允许开始绘制
          this.isDrawing = true;
          const rect = this.$refs.imageContainer.getBoundingClientRect();
          this.startX = event.clientX - rect.left;
          this.startY = event.clientY - rect.top;
          this.currentX = this.startX;
          this.currentY = this.startY;
        })
        .catch(error => {
          console.error('获取图像状态失败', error);
          this.$message.error('无法验证图像状态，操作失败');
        });
    },
    
    drawing(event) {
      if (!this.isDrawing) return;
      
      const rect = this.$refs.imageContainer.getBoundingClientRect();
      this.currentX = event.clientX - rect.left;
      this.currentY = event.clientY - rect.top;
    },
    
    endDrawing() {
      if (!this.isDrawing) return;
      
      // 计算归一化坐标（中心点和尺寸）
      const left = Math.min(this.startX, this.currentX);
      const top = Math.min(this.startY, this.currentY);
      const width = Math.abs(this.currentX - this.startX);
      const height = Math.abs(this.currentY - this.startY);
      
      // 确保框的大小至少为5x5像素
      if (width < 5 || height < 5) {
        this.isDrawing = false;
        return;
      }
      
      // 确保imageWidth和imageHeight有效
      if (!this.imageWidth || !this.imageHeight) {
        console.error('图像尺寸无效，无法计算归一化坐标');
        this.$message.error('无法获取图像尺寸，请刷新页面重试');
        this.isDrawing = false;
        return;
      }
      
      // 归一化坐标计算
      let normalizedX = (left + width / 2) / this.imageWidth;
      let normalizedY = (top + height / 2) / this.imageHeight;
      let normalizedWidth = width / this.imageWidth;
      let normalizedHeight = height / this.imageHeight;
      
      // 验证并修正坐标范围
      // x, y必须在[0,1]之间
      normalizedX = Math.max(0, Math.min(1, normalizedX));
      normalizedY = Math.max(0, Math.min(1, normalizedY));
      
      // width, height必须在(0,1]之间
      normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth));
      normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight));
      
      // 添加新标注
      this.annotations.push({
        tag: this.selectedTag,
        x: normalizedX,
        y: normalizedY,
        width: normalizedWidth,
        height: normalizedHeight
      });
      
      this.isDrawing = false;
    },
    
    cancelDrawing() {
      this.isDrawing = false;
    },
    
    deleteAnnotation(index) {
      // 先检查图像状态
      api.images.getOne(this.imageId)
        .then(response => {
          const imageData = response.data;
          // 检查图像状态是否为草稿状态
          if (imageData.status && imageData.status !== 'DRAFT') {
            this.$message.error(`当前图像状态为"${imageData.status}"，只有草稿状态才能修改标注`);
            return;
          }
          
          // 如果是草稿状态，则允许删除标注
          this.annotations.splice(index, 1);
        })
        .catch(error => {
          console.error('获取图像状态失败', error);
          this.$message.error('无法验证图像状态，删除失败');
        });
    },
    
    clearAnnotations() {
      this.$confirm('确定要清除所有标注吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 先检查图像状态
        api.images.getOne(this.imageId)
          .then(response => {
            const imageData = response.data;
            // 检查图像状态是否为草稿状态
            if (imageData.status && imageData.status !== 'DRAFT') {
              this.$message.error(`当前图像状态为"${imageData.status}"，只有草稿状态才能修改标注`);
              return;
            }
            
            // 如果是草稿状态，则允许清除标注
            this.annotations = [];
            this.$message.success('已清除所有标注');
          })
          .catch(error => {
            console.error('获取图像状态失败', error);
            this.$message.error('无法验证图像状态，清除失败');
          });
      }).catch(() => {});
    },
    
    saveAnnotations() {
      if (this.annotations.length === 0) {
        this.$message.warning('没有标注可保存');
        return;
      }
      
      // 获取当前用户信息
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user) {
        this.$message.error('未检测到用户信息，无法保存标注');
        return;
      }

      // 先检查图像状态
      api.images.getOne(this.imageId)
        .then(response => {
          const imageData = response.data;
          // 检查图像状态是否为草稿状态
          if (imageData.status && imageData.status !== 'DRAFT') {
            this.$message.error(`当前图像状态为"${imageData.status}"，只有草稿状态才能修改标注`);
            return;
          }
          
          // 验证并处理每个标注
          const validAnnotations = this.annotations.filter(annotation => {
            // 验证坐标是否在有效范围内
            const isXValid = annotation.x >= 0 && annotation.x <= 1;
            const isYValid = annotation.y >= 0 && annotation.y <= 1;
            const isWidthValid = annotation.width > 0 && annotation.width <= 1;
            const isHeightValid = annotation.height > 0 && annotation.height <= 1;
            
            return isXValid && isYValid && isWidthValid && isHeightValid;
          });
          
          if (validAnnotations.length !== this.annotations.length) {
            const invalidCount = this.annotations.length - validAnnotations.length;
            this.$message.warning(`检测到 ${invalidCount} 个标注坐标无效，已自动过滤`);
            
            // 更新标注列表，移除无效标注
            this.annotations = validAnnotations;
          }
          
          if (validAnnotations.length === 0) {
            this.$message.error('没有有效的标注可保存');
            return;
          }
          
          // 分离新增和已有标注
          const newAnnotations = [];
          const existingAnnotations = [];
          
          validAnnotations.forEach(annotation => {
            if (annotation.id) {
              // 已有标注，需要更新
              existingAnnotations.push(annotation);
            } else {
              // 新增标注
              newAnnotations.push(annotation);
            }
          });
          
          console.log(`标注总数: ${validAnnotations.length}, 新增: ${newAnnotations.length}, 更新: ${existingAnnotations.length}`);
          
          // 处理所有请求的Promise数组
          const promises = [];
          
          // 处理新增标注
          if (newAnnotations.length > 0) {
            const createPromises = newAnnotations.map(annotation => {
              // 确保坐标在有效范围内（再次验证）
              const x = Math.max(0, Math.min(1, annotation.x));
              const y = Math.max(0, Math.min(1, annotation.y));
              const width = Math.max(0.001, Math.min(1, annotation.width));
              const height = Math.max(0.001, Math.min(1, annotation.height));
              
              const tagData = {
                metadata_id: this.imageId,
                tag: annotation.tag,
                x: x,
                y: y,
                width: width,
                height: height,
                created_by: user.id
              };
              
              console.log('保存新标注数据:', tagData);
              return api.tags.create(tagData);
            });
            
            promises.push(...createPromises);
          }
          
          // 处理已有标注的更新
          if (existingAnnotations.length > 0) {
            const updatePromises = existingAnnotations.map(annotation => {
              // 确保坐标在有效范围内（再次验证）
              const x = Math.max(0, Math.min(1, annotation.x));
              const y = Math.max(0, Math.min(1, annotation.y));
              const width = Math.max(0.001, Math.min(1, annotation.width));
              const height = Math.max(0.001, Math.min(1, annotation.height));
              
              const tagData = {
                tag: annotation.tag,
                x: x,
                y: y,
                width: width,
                height: height
              };
              
              console.log('更新已有标注数据:', annotation.id, tagData);
              return api.tags.update(annotation.id, tagData);
            });
            
            promises.push(...updatePromises);
          }
          
          // 执行所有请求
          Promise.all(promises)
            .then(responses => {
              this.$message.success(`成功保存 ${responses.length} 个标注`);
              this.fetchSavedAnnotations(); // 刷新标注列表
              this.$emit('annotations-saved', this.annotations);
              
              // 在标注保存成功后，立即调用保存标注图像的API
              this.saveAnnotatedImage();
            })
            .catch(error => {
              console.error('保存标注失败', error);
              if (error.response && error.response.data) {
                this.$message.error('保存标注失败: ' + error.response.data);
                console.error('服务器响应详情:', error.response);
              } else {
                this.$message.error('保存标注失败: ' + error.message);
              }
            });
        })
        .catch(error => {
          console.error('获取图像状态失败', error);
          this.$message.error('无法验证图像状态，保存失败');
        });
    },
    
    fetchSavedAnnotations() {
      // 添加加载状态
      const loading = this.$loading({
        lock: true,
        text: '加载标注数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      });
      
      // 记录旧的标注，用于维护用户可能的修改
      const oldAnnotations = [...this.annotations];
      
      api.tags.getByImageId(this.imageId)
        .then(response => {
          this.savedAnnotations = response.data;
          console.log(`获取到${this.savedAnnotations.length}个保存的标注`);
          
          // 合并已保存的标注
          if (this.savedAnnotations && this.savedAnnotations.length > 0) {
            // 存储已有标注的ID和新获取的标注
            const existingAnnotationMap = {};
            this.savedAnnotations.forEach(tag => {
              existingAnnotationMap[tag.id] = tag;
            });
            
            // 如果用户已经修改了标注位置，但尚未保存，保留其位置
            // 只在首次加载时替换整个标注数组
            if (oldAnnotations.length === 0) {
              // 首次加载，直接使用服务器的数据
              this.annotations = [];
              this.savedAnnotations.forEach(tag => {
                this.annotations.push({
                  id: tag.id,
                  tag: tag.tag,
                  x: tag.x,
                  y: tag.y,
                  width: tag.width,
                  height: tag.height
                });
              });
              
              console.log(`首次加载，添加${this.annotations.length}个标注`);
            } else {
              // 非首次加载，保留用户修改的位置
              // 更新现有注释的属性（除位置外）或添加新的注释
              const updatedAnnotations = [...oldAnnotations];
              let hasChanges = false;
              
              // 检查是否有新的标注需要添加
              this.savedAnnotations.forEach(serverTag => {
                const existingIndex = updatedAnnotations.findIndex(a => a.id === serverTag.id);
                
                if (existingIndex === -1) {
                  // 这是一个新标注，添加到数组
                  updatedAnnotations.push({
                    id: serverTag.id,
                    tag: serverTag.tag,
                    x: serverTag.x,
                    y: serverTag.y,
                    width: serverTag.width,
                    height: serverTag.height
                  });
                  hasChanges = true;
                  console.log(`添加新标注: ID=${serverTag.id}`);
                } else {
                  // 检查tag标签值是否变化，如果变化则更新
                  const existingTag = updatedAnnotations[existingIndex];
                  if (existingTag.tag !== serverTag.tag) {
                    console.log(`更新标注标签: ID=${serverTag.id}, 从"${existingTag.tag}"到"${serverTag.tag}"`);
                    existingTag.tag = serverTag.tag;
                    hasChanges = true;
                  }
                }
              });
              
              // 检查是否有被删除的标注需要从前端移除
              for (let i = updatedAnnotations.length - 1; i >= 0; i--) {
                const annotation = updatedAnnotations[i];
                if (annotation.id && !existingAnnotationMap[annotation.id]) {
                  console.log(`删除不存在的标注: ID=${annotation.id}`);
                  updatedAnnotations.splice(i, 1);
                  hasChanges = true;
                }
              }
              
              // 如果有变化，更新数组
              if (hasChanges) {
                this.annotations = updatedAnnotations;
                console.log(`更新标注数组，现在有${this.annotations.length}个标注`);
              }
            }
          } else {
            // 如果没有标注，清空数组
            console.log('没有找到标注数据，清空标注数组');
            this.annotations = [];
          }
        })
        .catch(error => {
          console.error('获取已保存的标注失败', error);
          this.$message.error('获取标注数据失败: ' + (error.message || '未知错误'));
        })
        .finally(() => {
          // 关闭加载状态
          loading.close();
        });
    },
    
    getTagColor(tag) {
      const colors = {
        '血管瘤': '#ff0000',
        '淋巴管瘤': '#00ff00',
        '混合型': '#0000ff',
        '其他': '#ff00ff'
      };
      
      return colors[tag] || '#ff0000';
    },
    
    saveAnnotatedImage() {
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '生成标注图像中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      });
      
      // 调用API保存标注后的图像
      api.tags.saveAnnotatedImage(this.imageId)
        .then(response => {
          console.log('标注图像保存成功:', response.data);
          this.$message.success('标注图像已成功生成');
        })
        .catch(error => {
          console.error('生成标注图像失败:', error);
          let errorMsg = '生成标注图像失败';
          if (error.response && error.response.data) {
            // 如果是标注为空错误，不显示错误提示（避免干扰用户）
            if (typeof error.response.data === 'string' && error.response.data.includes('无法生成标注后图像')) {
              console.log('暂无标注数据，稍后保存');
              return;
            }
            errorMsg += ': ' + error.response.data;
          } else if (error.message) {
            errorMsg += ': ' + error.message;
          }
          this.$message.error(errorMsg);
        })
        .finally(() => {
          loading.close();
        });
    }
  }
};
</script>

<style scoped>
.image-annotator {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.annotation-toolbar {
  display: flex;
  gap: 20px;
}

.tool-section {
  padding: 10px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  flex: 1;
}

.tool-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
}

.annotation-workspace {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 10px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.image-container {
  position: relative;
  max-width: 100%;
  max-height: 600px;
  overflow: hidden;
}

.annotation-image {
  display: block;
  max-width: 100%;
  max-height: 600px;
}

.annotation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  cursor: crosshair;
}

.annotation-box {
  position: absolute;
  border: 2px solid #ff0000;
  pointer-events: none;
}

.annotation-box.drawing {
  border: 2px dashed #ff0000;
  background-color: rgba(255, 0, 0, 0.1);
}

.tag-label {
  position: absolute;
  top: -22px;
  left: 0;
  background-color: #ff0000;
  color: white;
  padding: 2px 5px;
  font-size: 12px;
  border-radius: 3px;
}

.annotation-list {
  padding: 15px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.annotation-list h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #333;
}

.actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 