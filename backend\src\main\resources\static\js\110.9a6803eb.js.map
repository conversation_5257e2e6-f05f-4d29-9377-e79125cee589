{"version": 3, "file": "js/110.9a6803eb.js", "mappings": "kNACOA,MAAM,mB,GAGJA,MAAM,Q,GAGJA,MAAM,c,GAKNA,MAAM,c,GAKNA,MAAM,c,GAKNA,MAAM,Y,GACJA,MAAM,c,GAKNA,MAAM,c,GAMRA,MAAM,Y,GACJA,MAAM,c,GAKNA,MAAM,c,GAxCnBC,IAAA,EAoD8BD,MAAM,iB,GApDpCC,IAAA,EAyD+BD,MAAM,iB,GAM5BA,MAAM,Q,0CA9DbE,EAAAA,EAAAA,IAmEM,MAnENC,EAmEM,gBAlEJC,EAAAA,EAAAA,IAAe,UAAX,UAAM,KAEVA,EAAAA,EAAAA,IAyDM,MAzDNC,EAyDM,gBAxDJD,EAAAA,EAAAA,IAAe,UAAX,UAAM,KAEVA,EAAAA,EAAAA,IAGM,MAHNE,EAGM,cAFJF,EAAAA,EAAAA,IAAkC,aAA3B,uBAAmB,cAC1BA,EAAAA,EAAAA,IAA6C,SATrD,sBAAAG,EAAA,KAAAA,EAAA,YAAAC,GAAA,OASwBC,EAAAC,YAAWF,CAAA,GAAEG,KAAK,U,iBAAlBF,EAAAC,kBAGlBN,EAAAA,EAAAA,IAGM,MAHNQ,EAGM,gBAFJR,EAAAA,EAAAA,IAA0B,aAAnB,eAAW,cAClBA,EAAAA,EAAAA,IAAmC,SAd3C,sBAAAG,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAcwBC,EAAAI,IAAGL,CAAA,GAAEG,KAAK,Q,iBAAVF,EAAAI,UAGlBT,EAAAA,EAAAA,IAGM,MAHNU,EAGM,gBAFJV,EAAAA,EAAAA,IAAiC,aAA1B,sBAAkB,cACzBA,EAAAA,EAAAA,IAA4C,SAnBpD,sBAAAG,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAmBwBC,EAAAM,WAAUP,CAAA,GAAEG,KAAK,U,iBAAjBF,EAAAM,iBAGlBX,EAAAA,EAAAA,IAUM,MAVNY,EAUM,EATJZ,EAAAA,EAAAA,IAGM,MAHNa,EAGM,gBAFJb,EAAAA,EAAAA,IAA4B,aAArB,iBAAa,cACpBA,EAAAA,EAAAA,IAA+D,SAzBzE,sBAAAG,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAyB0BC,EAAAS,EAACV,CAAA,GAAEG,KAAK,SAASQ,KAAK,OAAOC,IAAI,IAAIC,IAAI,K,iBAAzCZ,EAAAS,QAGlBd,EAAAA,EAAAA,IAGM,MAHNkB,EAGM,gBAFJlB,EAAAA,EAAAA,IAA4B,aAArB,iBAAa,cACpBA,EAAAA,EAAAA,IAA+D,SA9BzE,sBAAAG,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA8B0BC,EAAAc,EAACf,CAAA,GAAEG,KAAK,SAASQ,KAAK,OAAOC,IAAI,IAAIC,IAAI,K,iBAAzCZ,EAAAc,UAIpBnB,EAAAA,EAAAA,IAUM,MAVNoB,EAUM,EATJpB,EAAAA,EAAAA,IAGM,MAHNqB,EAGM,gBAFJrB,EAAAA,EAAAA,IAA2B,aAApB,gBAAY,cACnBA,EAAAA,EAAAA,IAAsE,SArChF,sBAAAG,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAqC0BC,EAAAiB,MAAKlB,CAAA,GAAEG,KAAK,SAASQ,KAAK,OAAOC,IAAI,OAAOC,IAAI,K,iBAAhDZ,EAAAiB,YAGlBtB,EAAAA,EAAAA,IAGM,MAHNuB,EAGM,gBAFJvB,EAAAA,EAAAA,IAA2B,aAApB,gBAAY,cACnBA,EAAAA,EAAAA,IAAuE,SA1CjF,sBAAAG,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA0C0BC,EAAAmB,OAAMpB,CAAA,GAAEG,KAAK,SAASQ,KAAK,OAAOC,IAAI,OAAOC,IAAI,K,iBAAjDZ,EAAAmB,eAIpBxB,EAAAA,EAAAA,IAAuE,UAA9DyB,QAAKtB,EAAA,KAAAA,EAAA,qBAAEuB,EAAAC,oBAAAD,EAAAC,mBAAAC,MAAAF,EAAAG,UAAkB,GAAEjC,MAAM,eAAc,UAE7CS,EAAAyB,SAAM,WAAjBhC,EAAAA,EAAAA,IAEM,OAlDZD,IAAA,EAgD0BD,OAhD1BmC,EAAAA,EAAAA,IAAA,UAgD4C1B,EAAAyB,OAAOE,QAAU,UAAY,Y,QAC9D3B,EAAAyB,OAAOG,SAAO,KAjDzBC,EAAAA,EAAAA,IAAA,OAoDiB7B,EAAA8B,cAAW,WAAtBrC,EAAAA,EAAAA,IAGM,MAHNsC,EAGM,gBAFJpC,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAAqD,YAAAqC,EAAAA,EAAAA,IAA7CC,KAAKC,UAAUlC,EAAA8B,YAAa,KAAM,IAAR,OAtD1CD,EAAAA,EAAAA,IAAA,OAyDiB7B,EAAAmC,eAAY,WAAvB1C,EAAAA,EAAAA,IAGM,MAHN2C,EAGM,gBAFJzC,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAAsD,YAAAqC,EAAAA,EAAAA,IAA9CC,KAAKC,UAAUlC,EAAAmC,aAAc,KAAM,IAAR,OA3D3CN,EAAAA,EAAAA,IAAA,UA+DIlC,EAAAA,EAAAA,IAIM,MAJN0C,EAIM,gBAHJ1C,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAAgJ,UAAvIyB,QAAKtB,EAAA,KAAAA,EAAA,qBAAEuB,EAAAiB,kBAAAjB,EAAAiB,iBAAAf,MAAAF,EAAAG,UAAgB,GAAEjC,OAjExCmC,EAAAA,EAAAA,IAAA,CAiE8C,MAAK,CAAAa,SAAqBvC,EAAAwC,yB,QAA0BxC,EAAAwC,oBAAsB,QAAU,UAAb,kBAC/G7C,EAAAA,EAAAA,IAA6C,KAA1CJ,MAAM,QAAO,6BAAyB,O,qKAS/C,SACEkD,KAAM,YACNC,KAAI,WAEF,IAAMC,EAAOV,KAAKW,MAAMC,aAAaC,QAAQ,SAAW,MAClDC,EAASJ,EAAKK,IAAML,EAAKM,UAAY,EAE3C,MAAO,CAELhD,YAAa,EACbG,IAAK,MACLE,WAAYyC,EACZtC,EAAG,GACHK,EAAG,GACHG,MAAO,GACPE,OAAQ,GAGRqB,qBAAqB,EACrBV,YAAa,KACbK,aAAc,KACdV,OAAQ,KAEZ,EACAyB,QAAO,WAELC,KAAKX,qBAA0D,IAApCY,OAAOC,yBAGlC,IAAMC,EAAY,IAAIC,gBAAgBH,OAAOI,SAASC,QAChDC,EAAUJ,EAAUK,IAAI,WAC1BD,IACFP,KAAKlD,YAAc2D,SAASF,EAAS,IAEzC,EACAG,QAAS,CAIDvC,mBAAkB,WAAG,IAAAwC,EAAA,YAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAN,EAAAA,EAAAA,KAAAO,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAuB+B,OAtBxDX,EAAKhC,YAAc,KACnBgC,EAAK3B,aAAe,KACpB2B,EAAKrC,OAAS,KAAI+C,EAAAE,EAAA,EAIVP,EAAU,CACdlE,YAAa2D,SAASE,EAAK7D,YAAa,IACxCG,IAAK0D,EAAK1D,IACVK,EAAGkE,WAAWb,EAAKrD,GACnBK,EAAG6D,WAAWb,EAAKhD,GACnBG,MAAO0D,WAAWb,EAAK7C,OACvBE,OAAQwD,WAAWb,EAAK3C,QACxBb,WAAYsD,SAASE,EAAKxD,WAAY,KAIxCwD,EAAKhC,YAAcqC,EACnBS,QAAQC,IAAI,UAAWV,IACvBW,EAAAA,EAAAA,IAAeX,GAGfL,EAAKrC,OAAS,CAAEE,SAAS,EAAOC,QAAS,aAAa4C,EAAAC,EAAA,EAC/BM,EAAAA,WAAIC,KAAKC,OAAOd,GAAQ,OAAzCC,EAAOI,EAAAU,EAGbpB,EAAK3B,aAAeiC,EAAS1B,KAC7BoB,EAAKrC,OAAS,CAAEE,SAAS,EAAMC,QAAS,eAAFuD,OAAiBf,EAAS1B,KAAKM,KACrE4B,QAAQC,IAAI,UAAWT,EAAS1B,MAAK8B,EAAAC,EAAA,eAAAD,EAAAE,EAAA,EAAAJ,EAAAE,EAAAU,EAGrCN,QAAQQ,MAAM,UAASd,GAGnBD,EAAe,SACfC,EAAMF,UACRC,GAAW,KAAAc,OAAUb,EAAMF,SAAS3C,OAAM,KAAA0D,OAAIb,EAAMF,SAASiB,YAC7DvB,EAAK3B,aAAemC,EAAMF,SAAS1B,MAC1B4B,EAAM1C,UACfyC,GAAW,KAAAc,OAAUb,EAAM1C,UAG7BkC,EAAKrC,OAAS,CAAEE,SAAS,EAAOC,QAASyC,GAAc,cAAAG,EAAAc,EAAA,MAAApB,EAAA,iBA3ChCH,EA6C3B,EAKAzB,iBAAgB,WACd,IAAIa,KAAKX,oBAIT,KACE+C,EAAAA,EAAAA,MACApC,KAAKX,qBAAsB,EAC3BW,KAAK1B,OAAS,CAAEE,SAAS,EAAMC,QAAS,qBAGxCiB,aAAa2C,QAAQ,cAAe,OAEtC,CAAE,MAAOJ,GACPR,QAAQQ,MAAM,UAAWA,GACzBjC,KAAK1B,OAAS,CAAEE,SAAS,EAAOC,QAAS,WAAFuD,OAAaC,EAAMxD,SAC5D,CACF,I,eC9KJ,MAAM6D,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/DebugView.vue", "webpack://medical-annotation-frontend/./src/views/DebugView.vue?a71b"], "sourcesContent": ["<template>\r\n  <div class=\"debug-container\">\r\n    <h1>标注调试工具</h1>\r\n    \r\n    <div class=\"card\">\r\n      <h2>标注数据测试</h2>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>图像ID (metadata_id):</label>\r\n        <input v-model=\"metadata_id\" type=\"number\" />\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>标签类型 (tag):</label>\r\n        <input v-model=\"tag\" type=\"text\" />\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>用户ID (created_by):</label>\r\n        <input v-model=\"created_by\" type=\"number\" />\r\n      </div>\r\n      \r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label>X坐标 (归一化0-1):</label>\r\n          <input v-model=\"x\" type=\"number\" step=\"0.01\" min=\"0\" max=\"1\" />\r\n        </div>\r\n        \r\n        <div class=\"form-group\">\r\n          <label>Y坐标 (归一化0-1):</label>\r\n          <input v-model=\"y\" type=\"number\" step=\"0.01\" min=\"0\" max=\"1\" />\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label>宽度 (归一化0-1):</label>\r\n          <input v-model=\"width\" type=\"number\" step=\"0.01\" min=\"0.01\" max=\"1\" />\r\n        </div>\r\n        \r\n        <div class=\"form-group\">\r\n          <label>高度 (归一化0-1):</label>\r\n          <input v-model=\"height\" type=\"number\" step=\"0.01\" min=\"0.01\" max=\"1\" />\r\n        </div>\r\n      </div>\r\n      \r\n      <button @click=\"testSaveAnnotation\" class=\"btn primary\">测试保存标注</button>\r\n      \r\n      <div v-if=\"status\" :class=\"['status', status.success ? 'success' : 'error']\">\r\n        {{ status.message }}\r\n      </div>\r\n      \r\n      <div v-if=\"requestData\" class=\"debug-section\">\r\n        <h3>请求数据:</h3>\r\n        <pre>{{ JSON.stringify(requestData, null, 2) }}</pre>\r\n      </div>\r\n      \r\n      <div v-if=\"responseData\" class=\"debug-section\">\r\n        <h3>响应数据:</h3>\r\n        <pre>{{ JSON.stringify(responseData, null, 2) }}</pre>\r\n      </div>\r\n    </div>\r\n    \r\n    <div class=\"card\">\r\n      <h2>网络请求监控</h2>\r\n      <button @click=\"enableMonitoring\" class=\"btn\" :class=\"{ primary: !isMonitoringEnabled }\">{{ isMonitoringEnabled ? '已启用监控' : '启用请求监控' }}</button>\r\n      <p class=\"hint\">启用后，所有标注相关的网络请求将在控制台中详细显示</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/utils/api';\r\nimport { enableAnnotationDebug, displayTagData } from '@/utils/debug';\r\n\r\nexport default {\r\n  name: 'DebugView',\r\n  data() {\r\n    // 从本地存储或用户信息中获取默认值\r\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n    const userId = user.id || user.customId || 1;\r\n    \r\n    return {\r\n      // 表单字段\r\n      metadata_id: 1, // 默认图像ID\r\n      tag: '血管瘤', // 默认标签类型\r\n      created_by: userId, // 用户ID\r\n      x: 0.5, // 默认X位置(中心点)\r\n      y: 0.5, // 默认Y位置(中心点)\r\n      width: 0.3, // 默认宽度\r\n      height: 0.3, // 默认高度\r\n      \r\n      // 状态\r\n      isMonitoringEnabled: false,\r\n      requestData: null,\r\n      responseData: null,\r\n      status: null\r\n    };\r\n  },\r\n  mounted() {\r\n    // 检查是否已启用监控\r\n    this.isMonitoringEnabled = window.ANNOTATION_DEBUG_ENABLED === true;\r\n    \r\n    // 自动填充图像ID\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const imageId = urlParams.get('imageId');\r\n    if (imageId) {\r\n      this.metadata_id = parseInt(imageId, 10);\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 测试保存标注数据\r\n     */\r\n    async testSaveAnnotation() {\r\n      this.requestData = null;\r\n      this.responseData = null;\r\n      this.status = null;\r\n      \r\n      try {\r\n        // 构建标注数据\r\n        const tagData = {\r\n          metadata_id: parseInt(this.metadata_id, 10),\r\n          tag: this.tag,\r\n          x: parseFloat(this.x),\r\n          y: parseFloat(this.y),\r\n          width: parseFloat(this.width),\r\n          height: parseFloat(this.height),\r\n          created_by: parseInt(this.created_by, 10)\r\n        };\r\n        \r\n        // 显示请求数据\r\n        this.requestData = tagData;\r\n        console.log('请求标注数据:', tagData);\r\n        displayTagData(tagData);\r\n        \r\n        // 发送API请求\r\n        this.status = { success: false, message: '正在发送请求...' };\r\n        const response = await api.tags.create(tagData);\r\n        \r\n        // 显示响应数据\r\n        this.responseData = response.data;\r\n        this.status = { success: true, message: `保存成功! 标注ID: ${response.data.id}` };\r\n        console.log('标注保存成功:', response.data);\r\n      } catch (error) {\r\n        // 处理错误\r\n        console.error('保存标注失败:', error);\r\n        \r\n        // 提取错误信息\r\n        let errorMessage = '保存标注失败';\r\n        if (error.response) {\r\n          errorMessage += `: ${error.response.status} ${error.response.statusText}`;\r\n          this.responseData = error.response.data;\r\n        } else if (error.message) {\r\n          errorMessage += `: ${error.message}`;\r\n        }\r\n        \r\n        this.status = { success: false, message: errorMessage };\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 启用网络请求监控\r\n     */\r\n    enableMonitoring() {\r\n      if (this.isMonitoringEnabled) {\r\n        return; // 已启用\r\n      }\r\n      \r\n      try {\r\n        enableAnnotationDebug();\r\n        this.isMonitoringEnabled = true;\r\n        this.status = { success: true, message: '请求监控已启用，请查看浏览器控制台' };\r\n        \r\n        // 持久化调试设置\r\n        localStorage.setItem('enableDebug', 'true');\r\n        \r\n      } catch (error) {\r\n        console.error('启用监控失败:', error);\r\n        this.status = { success: false, message: `启用监控失败: ${error.message}` };\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.debug-container {\r\n  padding: 20px;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\nh1 {\r\n  color: #333;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\nh2 {\r\n  margin-top: 0;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 10px;\r\n  margin-bottom: 20px;\r\n  color: #444;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-row {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.form-row .form-group {\r\n  flex: 1;\r\n}\r\n\r\nlabel {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: bold;\r\n  color: #666;\r\n}\r\n\r\ninput {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n.btn {\r\n  padding: 10px 15px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  background-color: #f0f0f0;\r\n  margin-top: 10px;\r\n}\r\n\r\n.btn.primary {\r\n  background-color: #1976D2;\r\n  color: white;\r\n}\r\n\r\n.status {\r\n  margin-top: 15px;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.status.success {\r\n  background-color: #E8F5E9;\r\n  color: #2E7D32;\r\n  border-left: 4px solid #2E7D32;\r\n}\r\n\r\n.status.error {\r\n  background-color: #FFEBEE;\r\n  color: #C62828;\r\n  border-left: 4px solid #C62828;\r\n}\r\n\r\n.debug-section {\r\n  margin-top: 20px;\r\n  border-top: 1px dashed #ddd;\r\n  padding-top: 15px;\r\n}\r\n\r\n.debug-section h3 {\r\n  margin-top: 0;\r\n  color: #666;\r\n}\r\n\r\npre {\r\n  background-color: #f5f5f5;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  overflow-x: auto;\r\n  font-size: 13px;\r\n}\r\n\r\n.hint {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-top: 5px;\r\n}\r\n</style> ", "import { render } from \"./DebugView.vue?vue&type=template&id=a1e97730&scoped=true\"\nimport script from \"./DebugView.vue?vue&type=script&lang=js\"\nexport * from \"./DebugView.vue?vue&type=script&lang=js\"\n\nimport \"./DebugView.vue?vue&type=style&index=0&id=a1e97730&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-a1e97730\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_cache", "$event", "$data", "metadata_id", "type", "_hoisted_4", "tag", "_hoisted_5", "created_by", "_hoisted_6", "_hoisted_7", "x", "step", "min", "max", "_hoisted_8", "y", "_hoisted_9", "_hoisted_10", "width", "_hoisted_11", "height", "onClick", "$options", "testSaveAnnotation", "apply", "arguments", "status", "_normalizeClass", "success", "message", "_createCommentVNode", "requestData", "_hoisted_12", "_toDisplayString", "JSON", "stringify", "responseData", "_hoisted_13", "_hoisted_14", "enableMonitoring", "primary", "isMonitoringEnabled", "name", "data", "user", "parse", "localStorage", "getItem", "userId", "id", "customId", "mounted", "this", "window", "ANNOTATION_DEBUG_ENABLED", "urlParams", "URLSearchParams", "location", "search", "imageId", "get", "parseInt", "methods", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "tagData", "response", "errorMessage", "_t", "w", "_context", "n", "p", "parseFloat", "console", "log", "displayTagData", "api", "tags", "create", "v", "concat", "error", "statusText", "a", "enableAnnotationDebug", "setItem", "__exports__", "render"], "sourceRoot": ""}