# 血管瘤辅助系统API问题解决方案

## 问题描述
前端无法访问后端API，出现404错误：
```
XHRGET http://localhost:8080/medical/api/users [HTTP/1.1 404 Not Found]
```

## 问题原因
1. 前端API配置与后端不一致：
   - 前端使用 `http://localhost:8080/medical/api`
   - 后端实际运行在 `http://localhost:8085/medical/api`
   - 后端安全配置中只允许了 `/api/**` 路径，没有明确允许 `/medical/api/**`

2. 数据库问题：
   - 管理员用户没有被添加到管理员团队中
   - 用户的department字段编码问题

## 解决方案

### 1. API路径问题
1. 修改前端配置，使用正确的端口：
   ```javascript
   // 创建一个直接指向后端API的客户端
   const apiClient = axios.create({
     baseURL: 'http://localhost:8085/medical/api',  // 修改为正确的后端API路径和端口
     // ...
   });
   ```

2. 修改后端安全配置，允许访问/medical/api/**路径：
   ```java
   .authorizeRequests(auth -> auth
       // ...
       .antMatchers("/api/**").permitAll()
       .antMatchers("/medical/api/**").permitAll() // 添加这一行
       // ...
   )
   ```

### 2. 数据库问题
使用SQL脚本将管理员添加到管理员团队：
```sql
-- 切换到医疗注释数据库  
USE medical_annotations;

-- 添加管理员用户(ID=1)到管理员团队(ID=1)
INSERT INTO team_member_logs (user_id, team_id, action, performed_by, performed_at) 
VALUES (1, 1, 'ADD', 1, NOW());

-- 更新用户的部门字段为 "管理部门"
UPDATE users SET department = '管理部门' WHERE id = 1;
```

## 验证
使用PowerShell命令验证API可以正常访问：
```powershell
Invoke-WebRequest -Uri "http://localhost:8085/medical/api/users" -Method "GET"
```
返回状态码200，表示请求成功。

## 注意事项
1. 确保后端服务器正常运行在8085端口
2. 确保数据库中的中文字段使用UTF-8编码
3. 前端请求需要使用正确的端口和路径 