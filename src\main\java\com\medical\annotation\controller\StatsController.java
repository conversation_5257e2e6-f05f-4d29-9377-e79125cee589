package com.medical.annotation.controller;

import com.medical.annotation.model.User;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Logger;

@RestController
@RequestMapping({"/api/stats", "/api/stats-v2"})
@CrossOrigin(origins = {"http://localhost:8080", "http://127.0.0.1:8080"})
public class StatsController {

    private static final Logger logger = Logger.getLogger(StatsController.class.getName());

    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    /**
     * 获取仪表盘统计数据
     */
    @GetMapping("/dashboard/{userId}")
    public ResponseEntity<Map<String, Object>> getDashboardStats(@PathVariable String userId) {
        try {
            logger.info("获取用户 " + userId + " 的仪表盘统计数据");
            
            Optional<User> userOpt;
            
            // 尝试将userId解析为数字，以处理数字ID的情况
            try {
                int numericId = Integer.parseInt(userId);
                // 如果能成功解析为数字，直接使用数字ID查询用户
                userOpt = userRepository.findById(numericId);
                logger.info("使用数字ID " + numericId + " 查找用户");
            } catch (NumberFormatException e) {
                // 如果不是数字，则按自定义ID查询
                userOpt = userService.getUserByCustomId(userId);
                logger.info("使用自定义ID " + userId + " 查找用户");
            }
            
            if (!userOpt.isPresent()) {
                logger.warning("用户ID " + userId + " 不存在，返回空结果");
                Map<String, Object> emptyStats = new HashMap<>();
                emptyStats.put("totalCount", 0);
                emptyStats.put("draftCount", 0);
                emptyStats.put("reviewedCount", 0);
                emptyStats.put("submittedCount", 0);
                emptyStats.put("approvedCount", 0);
                emptyStats.put("rejectedCount", 0);
                emptyStats.put("message", "用户不存在");
                return ResponseEntity.ok(emptyStats);
            }

            Integer userNumericId = userOpt.get().getId();
            Map<String, Object> stats = jdbcTemplate.queryForMap(
                "SELECT '" + userId + "' AS userId, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE user_id = ?) AS totalCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'DRAFT' AND user_id = ?) AS draftCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'REVIEWED' AND user_id = ?) AS reviewedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'SUBMITTED' AND user_id = ?) AS submittedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'APPROVED' AND user_id = ?) AS approvedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'REJECTED' AND user_id = ?) AS rejectedCount",
                userNumericId, userNumericId, userNumericId, userNumericId, userNumericId, userNumericId);
            
            logger.info("统计数据查询结果: " + stats);
            return ResponseEntity.ok(stats);

        } catch (Exception e) {
            logger.severe("获取仪表板统计数据失败: " + e.getMessage());
            e.printStackTrace(); // 添加详细堆栈跟踪以便调试
            
            // 返回带有错误信息的空结果
            Map<String, Object> errorStats = new HashMap<>();
            errorStats.put("totalCount", 0);
            errorStats.put("draftCount", 0);
            errorStats.put("reviewedCount", 0);
            errorStats.put("submittedCount", 0);
            errorStats.put("approvedCount", 0);
            errorStats.put("rejectedCount", 0);
            errorStats.put("error", "获取统计数据失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorStats);
        }
    }
    
    /**
     * 执行自定义SQL查询（仅用于统计数据）
     */
    @PostMapping("/sql-query")
    public ResponseEntity<?> executeSqlQuery(@RequestBody Map<String, Object> request) {
        try {
            String userId = request.get("userId").toString();
            String query = request.get("query").toString();
            
            logger.info("执行自定义SQL查询: " + query);
            
            // 安全检查：确保只是执行SELECT COUNT查询
            String normalizedQuery = query.trim().toLowerCase();
            if (!normalizedQuery.startsWith("select") || normalizedQuery.contains("delete") || 
                normalizedQuery.contains("update") || normalizedQuery.contains("insert") || 
                normalizedQuery.contains("drop") || normalizedQuery.contains("alter")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                         .body(Collections.singletonMap("error", "只允许执行SELECT查询"));
            }
            
            // 执行查询
            Map<String, Object> result = jdbcTemplate.queryForMap(query);
            logger.info("查询结果: " + result);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.severe("执行SQL查询失败: " + e.getMessage());
            Map<String, String> error = new HashMap<>();
            error.put("error", "执行SQL查询失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserStats(@PathVariable String userId) {
        System.out.println("获取用户统计数据: " + userId);
        
        // 查找用户ID
        Optional<User> userOptional = userService.getUserByCustomId(userId);
        if (!userOptional.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        // 获取用户的数字ID
        Integer userNumericId = userOptional.get().getId();
        
        // 使用JOIN查询获取统计数据
        String sql = "SELECT " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE user_id = ?) AS totalCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'DRAFT' AND user_id = ?) AS draftCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'REVIEWED' AND user_id = ?) AS reviewedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'SUBMITTED' AND user_id = ?) AS submittedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'APPROVED' AND user_id = ?) AS approvedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'REJECTED' AND user_id = ?) AS rejectedCount";
        
        Map<String, Object> stats = jdbcTemplate.queryForMap(sql, 
                userNumericId, userNumericId, userNumericId, 
                userNumericId, userNumericId, userNumericId);
        
        System.out.println("用户 " + userId + " 统计数据: " + stats);
        
        // 添加用户信息
        stats.put("userId", userId);
        stats.put("userName", userOptional.get().getName());
        
        return ResponseEntity.ok(stats);
    }
    
    @GetMapping("/all")
    public ResponseEntity<Map<String, Object>> getAllStats() {
        // 查询全局统计数据
        String sql = "SELECT " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses) AS totalCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'DRAFT') AS draftCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'REVIEWED') AS reviewedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'SUBMITTED') AS submittedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'APPROVED') AS approvedCount, " +
                "(SELECT COUNT(*) FROM hemangioma_diagnoses WHERE status = 'REJECTED') AS rejectedCount, " +
                "(SELECT COUNT(*) FROM users) AS userCount";
        
        Map<String, Object> stats = jdbcTemplate.queryForMap(sql);
        
        return ResponseEntity.ok(stats);
    }
} 