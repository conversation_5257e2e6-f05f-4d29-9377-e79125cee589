package com.medical.annotation.controller;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import com.medical.annotation.service.FileService;

@RestController
public class DirectImageController {

    @Autowired
    private FileService fileService;

    /**
     * 直接从文件系统提供图片，绕过复杂的路径处理和数据库查询
     */
    @GetMapping("/direct-image/{filename:.+}")
    public ResponseEntity<Resource> getDirectImage(@PathVariable String filename) {
        try {
            System.out.println("直接获取图片: " + filename);
            
            // 固定路径，直接从已知的位置读取文件
            String imagePath = fileService.getTempDirectoryPath() + File.separator + filename;
            File file = new File(imagePath);
            
            System.out.println("检查文件路径: " + file.getAbsolutePath());
            System.out.println("文件是否存在: " + file.exists() + ", 大小: " + (file.exists() ? file.length() : 0));
            
            if (file.exists()) {
                byte[] data = Files.readAllBytes(file.toPath());
                System.out.println("成功读取图片数据，大小: " + data.length + " 字节");
                
                ByteArrayResource resource = new ByteArrayResource(data);
                String contentType;
                try {
                    contentType = Files.probeContentType(file.toPath());
                    if (contentType == null) {
                        contentType = "image/jpeg";  // 默认为JPEG
                    }
                } catch (Exception e) {
                    contentType = "image/jpeg";  // 出错时默认为JPEG
                }
                
                System.out.println("返回图片，内容类型: " + contentType);
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .contentLength(data.length)
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                        .body(resource);
            } else {
                System.out.println("文件不存在: " + imagePath);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.err.println("获取图片时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }
} 