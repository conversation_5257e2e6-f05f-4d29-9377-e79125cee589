package com.medical.annotation.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Value("${spring.datasource.url}")
    private String dataSourceUrl;
    
    @Value("${spring.datasource.username}")
    private String dataSourceUsername;
    
    @Value("${spring.datasource.password}")
    private String dataSourcePassword;
    
    @Value("${spring.datasource.driver-class-name:com.mysql.cj.jdbc.Driver}")
    private String driverClassName;

    @Override
    public void run(String... args) throws Exception {
        System.out.println("数据库初始化开始...");

        try {
            // 首先确保驱动程序加载
            Class.forName(driverClassName);
            System.out.println("数据库驱动已加载: " + driverClassName);
            
            // 然后确保数据库存在
            createDatabaseIfNotExists();
            
            // 1. 确保用户表存在
            createUsersTable();
            
            // 2. 确保团队表存在
            createTeamsTable();
            
            // 3. 确保图像元数据表存在
            createImageMetadataTable();
            
            // 4. 确保标签表存在
            createTagsTable();
            
            // 5. 确保图像对表存在
            createImagePairsTable();
            
            System.out.println("数据库初始化完成！");
        } catch (Exception e) {
            System.err.println("数据库初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 创建数据库（如果不存在）
     */
    private void createDatabaseIfNotExists() {
        // 从JDBC URL中提取数据库名称
        String url = dataSourceUrl;
        String databaseName = extractDatabaseNameFromJdbcUrl(url);
        
        if (databaseName == null || databaseName.isEmpty()) {
            System.out.println("无法从JDBC URL提取数据库名: " + url);
            return;
        }
        
        // 构建不带数据库名称的基本连接URL
        String baseUrl = extractBaseUrlFromJdbcUrl(url);
        
        System.out.println("尝试创建数据库（如果不存在）: " + databaseName);
        System.out.println("使用基础URL: " + baseUrl);
        
        try (Connection conn = DriverManager.getConnection(baseUrl, dataSourceUsername, dataSourcePassword);
             Statement stmt = conn.createStatement()) {
            
            // 创建数据库
            String sql = "CREATE DATABASE IF NOT EXISTS " + databaseName + " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            stmt.executeUpdate(sql);
            System.out.println("数据库已创建或已存在: " + databaseName);
            
        } catch (SQLException e) {
            System.err.println("创建数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 从JDBC URL中提取数据库名称
     */
    private String extractDatabaseNameFromJdbcUrl(String url) {
        // 典型的JDBC URL格式: *****************************************************
        try {
            int slashIndex = url.lastIndexOf('/');
            if (slashIndex < 0) return null;
            
            String dbNameWithParams = url.substring(slashIndex + 1);
            int questionMarkIndex = dbNameWithParams.indexOf('?');
            
            if (questionMarkIndex < 0) {
                return dbNameWithParams; // 没有参数
            } else {
                return dbNameWithParams.substring(0, questionMarkIndex); // 提取数据库名
            }
        } catch (Exception e) {
            System.err.println("从URL提取数据库名称失败: " + e.getMessage());
            e.printStackTrace();
            return "medical_annotations"; // 默认数据库名
        }
    }
    
    /**
     * 从JDBC URL提取基本URL（不包含数据库名和参数）
     */
    private String extractBaseUrlFromJdbcUrl(String url) {
        try {
            // *****************************************************
            // 转换为 ***************************/
            int slashIndex = url.lastIndexOf('/');
            if (slashIndex < 0) return url;
            
            String baseUrl = url.substring(0, slashIndex);
            return baseUrl + '/';
        } catch (Exception e) {
            System.err.println("从URL提取基本URL失败: " + e.getMessage());
            e.printStackTrace();
            return "***************************/";
        }
    }
    
    private void createUsersTable() {
        try {
            System.out.println("检查并创建users表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS users (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(255) NOT NULL UNIQUE, " +
                "password VARCHAR(255) NOT NULL, " +
                "role VARCHAR(50) NOT NULL, " +
                "custom_id VARCHAR(9), " +
                "hospital VARCHAR(255), " +
                "department VARCHAR(255), " +
                "specialty VARCHAR(255), " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "reset_password_token VARCHAR(255), " +
                "reset_password_expire DATETIME, " +
                "reviewer_application_date DATETIME, " +
                "reviewer_application_processed_date DATETIME, " +
                "reviewer_application_reason TEXT, " +
                "reviewer_application_status VARCHAR(50), " +
                "reviewer_application_processed_by INT, " +
                "team_id INT, " +
                "FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE SET NULL, " +
                "FOREIGN KEY (reviewer_application_processed_by) REFERENCES users(id) ON DELETE SET NULL, " +
                "INDEX(email), " +
                "INDEX(role), " +
                "INDEX(custom_id)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            
            // 检查是否有默认用户，如果没有则创建
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM users", Integer.class);
            if (count != null && count == 0) {
                System.out.println("创建默认用户...");
                jdbcTemplate.update(
                    "INSERT INTO users (name, email, password, role, custom_id, created_at, updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                    "管理员", "<EMAIL>", "$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG", 
                    "ADMIN", "200000001"
                );
                System.out.println("默认用户已创建");
            }
            
            System.out.println("users表已创建或已存在");
        } catch (Exception e) {
            System.err.println("创建users表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建users表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS users (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "email VARCHAR(255) NOT NULL UNIQUE, " +
                    "password VARCHAR(255) NOT NULL, " +
                    "role VARCHAR(50) NOT NULL, " +
                    "custom_id VARCHAR(9), " +
                    "hospital VARCHAR(255), " +
                    "department VARCHAR(255), " +
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("users表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版users表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
    
    private void createTeamsTable() {
        try {
            System.out.println("检查并创建teams表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS teams (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "description TEXT, " +
                "created_by INT, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "INDEX(name), " +
                "FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            System.out.println("teams表已创建或已存在");
        } catch (Exception e) {
            System.err.println("创建teams表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建teams表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS teams (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "description TEXT, " +
                    "created_by INT, " +
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("teams表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版teams表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
    
    private void createImageMetadataTable() {
        try {
            System.out.println("检查并创建image_metadata表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS image_metadata (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "formatted_id VARCHAR(9) UNIQUE COMMENT '9位数字格式的ID，从000000001开始递增', " +
                "filename VARCHAR(255) NOT NULL, " +
                "original_name VARCHAR(255) NOT NULL, " +
                "path MEDIUMTEXT NOT NULL, " +
                "mimetype VARCHAR(50) NOT NULL, " +
                "size INT NOT NULL, " +
                "width INT, " +
                "height INT, " +
                "uploaded_by INT NOT NULL, " +
                "uploaded_by_custom_id VARCHAR(9) COMMENT '上传用户的9位数自定义ID', " +
                "team_id INT, " +
                "patient_name VARCHAR(100), " +
                "patient_age INT, " +
                "patient_gender VARCHAR(10), " +
                "case_number VARCHAR(50) UNIQUE, " +
                "lesion_location VARCHAR(100), " +
                "lesion_size VARCHAR(50), " +
                "lesion_color VARCHAR(50), " +
                "border_clarity VARCHAR(50), " +
                "blood_flow VARCHAR(50), " +
                "diagnosis_category VARCHAR(100), " +
                "disease_stage VARCHAR(50), " +
                "morphological_features TEXT, " +
                "symptoms TEXT, " +
                "symptom_details TEXT, " +
                "complications TEXT, " +
                "complication_details TEXT, " +
                "diagnosis_icd_code VARCHAR(20), " +
                "treatment_priority VARCHAR(20), " +
                "recommended_treatment TEXT, " +
                "treatment_plan TEXT, " +
                "contraindications TEXT, " +
                "follow_up_schedule VARCHAR(100), " +
                "prognosis_rating INT, " +
                "patient_education TEXT, " +
                "acquisition_date DATE, " +
                "modality VARCHAR(50), " +
                "study_description TEXT, " +
                "status VARCHAR(20) DEFAULT 'DRAFT', " +
                "reviewed_by INT, " +
                "reviewed_by_custom_id VARCHAR(9) COMMENT '审核用户的9位数自定义ID', " +
                "review_notes TEXT, " +
                "review_date DATETIME, " +
                "submitted_by INT, " +
                "submitted_at DATETIME, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "image_two_path MEDIUMTEXT COMMENT '标注后的图像路径', " +
                "INDEX(formatted_id), " +
                "INDEX(uploaded_by), " +
                "INDEX(status), " +
                "FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE SET NULL, " +
                "FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL, " +
                "FOREIGN KEY (submitted_by) REFERENCES users(id) ON DELETE SET NULL" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            System.out.println("image_metadata表已创建或已存在");
            
            // 检查formatted_id列是否存在，如果不存在则添加
            try {
                jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'image_metadata' AND column_name = 'formatted_id'", 
                    Integer.class
                );
            } catch (Exception e) {
                System.out.println("添加formatted_id列...");
                jdbcTemplate.execute("ALTER TABLE image_metadata ADD COLUMN formatted_id VARCHAR(9) UNIQUE COMMENT '9位数字格式的ID，从000000001开始递增'");
            }
            
        } catch (Exception e) {
            System.err.println("创建image_metadata表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建image_metadata表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS image_metadata (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "formatted_id VARCHAR(9) UNIQUE COMMENT '9位数字格式的ID，从000000001开始递增', " +
                    "filename VARCHAR(255) NOT NULL, " +
                    "original_name VARCHAR(255) NOT NULL, " +
                    "path MEDIUMTEXT NOT NULL, " +
                    "mimetype VARCHAR(50) NOT NULL, " +
                    "size INT NOT NULL, " +
                    "width INT, " +
                    "height INT, " +
                    "uploaded_by INT NOT NULL, " +
                    "status VARCHAR(20) DEFAULT 'DRAFT', " +
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                    "image_two_path MEDIUMTEXT COMMENT '标注后的图像路径', " +
                    "INDEX(formatted_id)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("image_metadata表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版image_metadata表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
    
    private void createTagsTable() {
        try {
            System.out.println("检查并创建tags表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS tags (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "metadata_id BIGINT NOT NULL, " +
                "tag VARCHAR(255) NOT NULL, " +
                "x DOUBLE NOT NULL, " +
                "y DOUBLE NOT NULL, " +
                "width DOUBLE NOT NULL, " +
                "height DOUBLE NOT NULL, " +
                "created_by INT, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "INDEX(metadata_id), " +
                "FOREIGN KEY (metadata_id) REFERENCES image_metadata(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            System.out.println("tags表已创建或已存在");
        } catch (Exception e) {
            System.err.println("创建tags表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建tags表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS tags (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "metadata_id BIGINT NOT NULL, " +
                    "tag VARCHAR(255) NOT NULL, " +
                    "x DOUBLE NOT NULL, " +
                    "y DOUBLE NOT NULL, " +
                    "width DOUBLE NOT NULL, " +
                    "height DOUBLE NOT NULL, " +
                    "created_by INT, " +
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("tags表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版tags表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
    
    private void createImagePairsTable() {
        try {
            System.out.println("检查并创建image_pairs表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS image_pairs (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "metadata_id BIGINT NOT NULL, " +
                "image_one_path MEDIUMTEXT NOT NULL, " +
                "image_two_path MEDIUMTEXT, " +
                "description TEXT, " +
                "created_by INT, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "deleted BOOLEAN DEFAULT FALSE, " +
                "INDEX(metadata_id), " +
                "FOREIGN KEY (metadata_id) REFERENCES image_metadata(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            System.out.println("image_pairs表已创建或已存在");
        } catch (Exception e) {
            System.err.println("创建image_pairs表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建image_pairs表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS image_pairs (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "metadata_id BIGINT NOT NULL, " +
                    "image_one_path MEDIUMTEXT NOT NULL, " +
                    "image_two_path MEDIUMTEXT, " +
                    "description TEXT, " +
                    "created_by INT, " +
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                    "deleted BOOLEAN DEFAULT FALSE" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("image_pairs表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版image_pairs表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
} 