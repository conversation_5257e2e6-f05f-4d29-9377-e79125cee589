<template>
  <div class="image-annotator">
    <div class="toolbar">
      <!-- 主分类选择 -->
      <el-select
        v-model="selectedMainCategory"
        placeholder="选择主分类"
        size="small"
        style="margin-right: 10px; width: 150px;"
        @change="onMainCategoryChange"
      >
        <el-option label="真性血管肿瘤" value="真性血管肿瘤"></el-option>
        <el-option label="血管畸形" value="血管畸形"></el-option>
        <el-option label="血管假瘤/易混淆病变" value="血管假瘤/易混淆病变"></el-option>
      </el-select>

      <!-- 具体类型选择 -->
      <el-select
        v-model="currentTag"
        placeholder="选择具体类型"
        size="small"
        style="width: 200px;"
        :disabled="!selectedMainCategory"
      >
        <el-option
          v-for="tag in availableSubCategories"
          :key="tag.value"
          :label="tag.label"
          :value="tag.value"
        ></el-option>
      </el-select>
      <el-button-group>
        <el-button 
          size="small" 
          :type="currentTool === 'rectangle' ? 'primary' : 'default'"
          @click="currentTool = 'rectangle'"
        >矩形</el-button>
        <el-button 
          size="small" 
          :type="currentTool === 'circle' ? 'primary' : 'default'"
          @click="currentTool = 'circle'"
        >圆形</el-button>
      </el-button-group>
      <el-button size="small" type="danger" @click="clearAnnotations">清除标注</el-button>
      <el-button size="small" type="success" @click="saveAnnotations">保存标注</el-button>
      </div>
      
    <div class="image-container" ref="imageContainer">
      <!-- 错误信息显示 -->
      <div v-if="hasErrors" class="error-message">
        {{ errorMessage }}
        <el-button size="mini" type="primary" @click="reloadImage">重试</el-button>
      </div>
      
      <!-- 加载中显示 -->
      <div v-else-if="!_imageLoaded" class="loading-container">
          <div class="loading-spinner"></div>
        <div class="loading-text">加载图像中...</div>
        </div>
        
      <!-- 图像和标注区域 -->
      <div v-else class="image-wrapper" ref="imageWrapper" @mousedown="startDrawing">
        <img 
          v-if="_cachedImageSrc"
          :src="_cachedImageSrc"
          class="annotatable-image"
          ref="image"
          @load="onImageLoad"
          draggable="false"
        />
        
        <!-- 已保存的标注 -->
        <div 
          v-for="(annotation, index) in annotations"
          :key="index"
          class="annotation"
          :style="getAnnotationStyle(annotation)"
        >
          <div class="tag-label" :style="{ backgroundColor: getTagColor(annotation.tag) }">
            {{ annotation.tag }}
          </div>
        </div>

        <!-- 实时绘制预览框将由JS手动创建和销毁 -->

      </div>
    </div>
  </div>
</template>

<script>
import { API_BASE_URL, API_CONTEXT_PATH } from '../config/api.config.js';

// 全局图像缓存，避免重复加载
const globalImageCache = {};

// 全局请求锁，防止重复请求
const requestLocks = {};

// 血管瘤分类数据
const HEMANGIOMA_CATEGORIES = {
  '真性血管肿瘤': [
    { label: '婴幼儿血管瘤', value: '婴幼儿血管瘤' },
    { label: '先天性快速消退型血管瘤', value: '先天性快速消退型血管瘤' },
    { label: '先天性部分消退型血管瘤', value: '先天性部分消退型血管瘤' },
    { label: '先天性不消退型血管瘤', value: '先天性不消退型血管瘤' },
    { label: '卡波西型血管内皮细胞瘤', value: '卡波西型血管内皮细胞瘤' },
    { label: '丛状血管瘤', value: '丛状血管瘤' },
    { label: '化脓性肉芽肿', value: '化脓性肉芽肿' },
    { label: '梭形细胞血管瘤', value: '梭形细胞血管瘤' },
    { label: '上皮样血管内皮瘤', value: '上皮样血管内皮瘤' },
    { label: '网状血管内皮瘤', value: '网状血管内皮瘤' },
    { label: '假肌源性血管内皮瘤', value: '假肌源性血管内皮瘤' },
    { label: '多形性血管内皮瘤', value: '多形性血管内皮瘤' },
    { label: '血管肉瘤', value: '血管肉瘤' },
    { label: '上皮样血管肉瘤', value: '上皮样血管肉瘤' },
    { label: '卡波西肉瘤', value: '卡波西肉瘤' }
  ],
  '血管畸形': [
    { label: '微静脉畸形', value: '微静脉畸形' },
    { label: '静脉畸形', value: '静脉畸形' },
    { label: '动静脉畸形', value: '动静脉畸形' },
    { label: '淋巴管畸形', value: '淋巴管畸形' },
    { label: '球细胞静脉畸形', value: '球细胞静脉畸形' },
    { label: '毛细血管-淋巴管-静脉畸形', value: '毛细血管-淋巴管-静脉畸形' },
    { label: '毛细血管-动静脉畸形', value: '毛细血管-动静脉畸形' },
    { label: '淋巴管-静脉畸形', value: '淋巴管-静脉畸形' }
  ],
  '血管假瘤/易混淆病变': [
    { label: '血管外皮细胞瘤', value: '血管外皮细胞瘤' },
    { label: '血管球瘤', value: '血管球瘤' },
    { label: '血管平滑肌瘤', value: '血管平滑肌瘤' },
    { label: '血管纤维瘤', value: '血管纤维瘤' },
    { label: '靶样含铁血黄素沉积性血管瘤', value: '靶样含铁血黄素沉积性血管瘤' },
    { label: '鞋钉样血管瘤', value: '鞋钉样血管瘤' }
  ]
};

export default {
  name: 'ImageAnnotator',
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    imageId: {
      type: Number,
      required: true
    },
    // 添加可选的标签列表prop
    tags: {
      type: Array,
      default: () => []
    },
    // 添加选中的标签prop
    selectedTag: {
      type: String,
      default: 'IH-婴幼儿血管瘤'
    }
  },
  data() {
    return {
      selectedMainCategory: '',
      currentTag: this.selectedTag,
      currentTool: 'rectangle',
      annotations: [],
      isDrawing: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
      imageWidth: 0,
      imageHeight: 0,
      imageLoaded: false,
      hasErrors: false,
      errorMessage: '',
      imageLoadRetries: 0,
      maxLoadRetries: 3,
      _cachedImageSrc: null, // 用于存储缓存的图像数据URL
      preventImageRequests: false,
      _drawingDebounce: null,
      _imageLoaded: false, // 标记图像是否已经完全加载
      cacheKey: '', // 缓存键
      isLoadingImage: false, // 标记是否正在加载图像
      abortController: null, // 用于取消请求的控制器
      fetchPromise: null, // 存储fetch请求的Promise
      _lastUpdateTime: 0, // 最后一次更新时间戳
      _updateThreshold: 5, // 降低更新阈值到5毫秒以提高响应速度
      _rafId: null, // requestAnimationFrame ID
      _rect: null, // 缓存元素边界矩形，减少重复计算
      tempAnnotation: null, // 临时标注框，用于拖动预览
      animationFrameId: null,
      previewEl: null, // 用于存储手动创建的预览元素
    };
  },
  computed: {
    // 根据选择的主要分类返回可用的子分类
    availableSubCategories() {
      if (!this.selectedMainCategory) {
        return [];
      }
      return HEMANGIOMA_CATEGORIES[this.selectedMainCategory] || [];
    },
  },
  watch: {
    // 监听props中的selectedTag变化，更新本地的currentTag
    selectedTag(newVal) {
      this.currentTag = newVal;
    },
    // 监听本地的currentTag变化，通知父组件
    currentTag(newVal) {
      this.$emit('update:selectedTag', newVal);
    }
  },
  created() {
    // 生成唯一的缓存键
    this.cacheKey = `image_${this.imageId}`;
      
    // 检查全局缓存中是否已有此图像
    if (globalImageCache[this.cacheKey]) {
      console.log('从全局缓存中获取图像');
      this._cachedImageSrc = globalImageCache[this.cacheKey];
      this._imageLoaded = true;
      this.imageLoaded = true;
      
      // 使用nextTick确保DOM更新后再获取图像尺寸
      this.$nextTick(() => {
        this.updateImageDimensions();
        // 图像已缓存且尺寸已更新后，再加载标注
        this.loadExistingAnnotations();
      });
    }
    
    // 不再在created钩子中加载标注，而是等待图像加载完成后再加载
  },
  mounted() {
    // 如果全局缓存中没有图像，则加载
    if (!globalImageCache[this.cacheKey]) {
      // 预加载图像并缓存
      this.preloadAndCacheImage();
      // 图像加载完成后会自动触发onImageLoad方法，在那里加载标注
    } else {
      // 如果图片已缓存，确保图像尺寸已更新
      this.$nextTick(() => {
        this.updateImageDimensions();
      });
    }

    // 加载AI检测结果并自动设置分类
    this.loadAIDetectionResults();
    
    // 添加主动检查和调试按钮（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        this.debugAnnotations();
        
        // 如果标注数据为空，重新尝试加载
        if (this.annotations.length === 0 && this._imageLoaded) {
          console.log('未检测到标注数据，主动重新加载');
          this.loadExistingAnnotations();
        }
      }, 2000);
    }
    
    // 监听窗口大小变化，更新图像尺寸
    window.addEventListener('resize', this.handleResize);

    // 添加页面卸载前的清理函数
    window.addEventListener('beforeunload', this.cleanupBeforeUnload);
  },
  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('beforeunload', this.cleanupBeforeUnload);
    document.removeEventListener('mousemove', this.handleGlobalMouseMove);
    document.removeEventListener('mouseup', this.handleGlobalMouseUp);
    
    // 取消可能正在进行的请求
    this.cancelPendingRequests();
    
    // 清除可能正在进行的动画帧
    if (this._rafId) {
      cancelAnimationFrame(this._rafId);
      this._rafId = null;
    }
  },
  methods: {
    // 处理主分类变化
    onMainCategoryChange() {
      // 清空子分类选择
      this.currentTag = '';
    },

    // 加载AI检测结果并自动设置分类
    async loadAIDetectionResults() {
      try {
        const diagnosisId = this.imageId;
        if (!diagnosisId) {
          console.log('没有诊断ID，跳过AI检测结果加载');
          return;
        }

        console.log('ImageAnnotator: 开始加载AI检测结果，诊断ID:', diagnosisId);

        // 获取血管瘤诊断数据
        const response = await fetch(`${API_BASE_URL}${API_CONTEXT_PATH}/api/hemangioma-diagnoses/${diagnosisId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const diagnosisData = await response.json();
        console.log('ImageAnnotator: 获取到的诊断数据:', diagnosisData);

        if (diagnosisData && diagnosisData.detectedType) {
          // 将AI检测到的类型转换为完整的中文名称
          const detectedType = diagnosisData.detectedType;
          console.log('ImageAnnotator: AI检测到的类型:', detectedType);

          // 自动设置分类
          this.autoSetCategoryFromAI(detectedType);
        }
      } catch (error) {
        console.error('ImageAnnotator: 加载AI检测结果失败:', error);
        // 不显示错误消息，因为这是自动加载，失败了用户可以手动选择
      }
    },

    // 根据AI检测结果自动设置分类
    autoSetCategoryFromAI(detectedType) {
      console.log('ImageAnnotator: 开始自动设置分类，检测类型:', detectedType);

      // AI检测结果可能是缩写形式，需要转换为完整名称
      const typeMapping = {
        'IH': '婴幼儿血管瘤',
        'RICH': '先天性快速消退型血管瘤',
        'PICH': '先天性部分消退型血管瘤',
        'NICH': '先天性不消退型血管瘤',
        'KHE': '卡波西型血管内皮细胞瘤',
        'TA': '丛状血管瘤',
        'PG': '化脓性肉芽肿',
        'MVM': '微静脉畸形',
        'VM': '静脉畸形',
        'AVM': '动静脉畸形',
        'LM': '淋巴管畸形',
        'GVM': '球细胞静脉畸形',
        'CLVM': '毛细血管-淋巴管-静脉畸形',
        'CAVM': '毛细血管-动静脉畸形',
        'LVM': '淋巴管-静脉畸形'
      };

      // 处理多个检测类型（用+分隔）
      const types = detectedType.split('+');
      const firstType = types[0].trim();

      // 获取完整的中文名称
      const fullTypeName = typeMapping[firstType] || firstType;
      console.log('ImageAnnotator: 转换后的完整类型名称:', fullTypeName);

      // 查找该类型属于哪个主分类
      let targetMainCategory = '';
      let targetSubCategory = fullTypeName;

      for (const [mainCategory, subCategories] of Object.entries(HEMANGIOMA_CATEGORIES)) {
        const found = subCategories.find(item => item.value === fullTypeName);
        if (found) {
          targetMainCategory = mainCategory;
          targetSubCategory = found.value;
          break;
        }
      }

      if (targetMainCategory) {
        console.log('ImageAnnotator: 自动设置分类:', targetMainCategory, '->', targetSubCategory);

        // 设置主分类
        this.selectedMainCategory = targetMainCategory;

        // 等待Vue更新DOM后设置子分类
        this.$nextTick(() => {
          this.currentTag = targetSubCategory;

          // 通知父组件标签已更新
          this.$emit('update:selectedTag', targetSubCategory);

          console.log('ImageAnnotator: 已根据AI检测结果自动设置分类：', targetSubCategory);
        });
      } else {
        console.log('ImageAnnotator: 未找到匹配的分类，使用默认设置');
        // 如果没找到匹配的，设置为第一个分类作为默认
        this.selectedMainCategory = '真性血管肿瘤';
        this.$nextTick(() => {
          this.currentTag = '婴幼儿血管瘤';
          this.$emit('update:selectedTag', '婴幼儿血管瘤');
        });
      }
    },

    // 在页面卸载前清理资源
    cleanupBeforeUnload() {
      this.cancelPendingRequests();
    },
    
    // 取消正在进行的请求
    cancelPendingRequests() {
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }
      
      // 释放请求锁
      if (requestLocks[this.cacheKey]) {
        delete requestLocks[this.cacheKey];
      }
    },
    
    // 预加载并缓存图像
    preloadAndCacheImage() {
      if (!this.imageUrl) return;
      
      // 如果全局缓存中已有此图像，直接使用
      if (globalImageCache[this.cacheKey]) {
        console.log('从全局缓存中获取图像');
        this._cachedImageSrc = globalImageCache[this.cacheKey];
        this._imageLoaded = true;
        this.imageLoaded = true;
        return;
      }
      
      // 如果已经在加载中，不重复加载
      if (this.isLoadingImage || requestLocks[this.cacheKey]) {
        console.log('图像正在加载中，跳过重复请求');
        return;
      }
      
      // 设置加载状态和请求锁
      this.isLoadingImage = true;
      requestLocks[this.cacheKey] = true;
      
      // 创建加载提示
      const loading = this.$message({
        message: '正在加载图像...',
        type: 'info',
        duration: 0
      });
      
      // 尝试不同的URL格式，提高加载成功率
      const baseUrl = this.imageUrl;
      
      // 构建可能的URL列表
      const urls = [
        baseUrl,
        baseUrl.replace('/medical/', '/'),
        baseUrl.replace('/', '/medical/'),
        baseUrl.includes('http') ? baseUrl : `http://localhost:8085${baseUrl}`
      ];
      
      console.log('尝试加载图像，URL列表:', urls);
      
      // 创建图像加载函数
      const tryLoadImage = (url) => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          
          img.onload = () => {
            console.log('成功加载图像:', url);
            resolve(img);
          };
          
          img.onerror = () => {
            console.error('加载图像失败:', url);
            reject(new Error(`Failed to load image from ${url}`));
          };
          
          // 设置跨域属性，避免CORS问题
          img.crossOrigin = 'anonymous';
          img.src = url;
        });
      };
      
      // 使用Promise.any尝试所有URL，只要有一个成功即可
      let successfulImg = null;
      Promise.any(urls.map(url => tryLoadImage(url)))
        .then(img => {
          successfulImg = img;
          console.log('成功加载图像:', img.src);
          
          // 创建canvas将图像转换为dataURL
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0);
          
          // 将图像缓存为dataURL
          const dataURL = canvas.toDataURL('image/jpeg');
          
          // 保存到全局缓存和组件缓存
          globalImageCache[this.cacheKey] = dataURL;
          this._cachedImageSrc = dataURL;
          
          // 更新图像尺寸
          this.imageWidth = img.width;
          this.imageHeight = img.height;
          this.imageLoaded = true;
          this._imageLoaded = true;
          this.hasErrors = false;
          
          // 清理资源
          canvas.remove();
          
          // 图像加载完成后，再加载标注数据
          console.log('图像加载完成，开始加载标注数据');
          return this.loadExistingAnnotations();
        })
        .then(() => {
          console.log('图像和标注加载完成');
          this.$forceUpdate();
        })
        .catch(error => {
          console.error('所有图像加载尝试都失败:', error);
          this.hasErrors = true;
          this.errorMessage = '无法加载图像，请检查网络连接或图像路径是否正确';
          this.$message.error(this.errorMessage);
        })
        .finally(() => {
          loading.close();
          
          // 重置加载状态和请求锁
          this.isLoadingImage = false;
          delete requestLocks[this.cacheKey];
        });
    },
    
    // 重新加载图像
    reloadImage() {
      // 取消可能正在进行的请求
      this.cancelPendingRequests();
      
      // 从全局缓存中移除
      delete globalImageCache[this.cacheKey];
      
      // 如果有请求锁，先释放
      if (requestLocks[this.cacheKey]) {
        delete requestLocks[this.cacheKey];
        }
      
      this._cachedImageSrc = null;
      this.imageLoaded = false;
      this._imageLoaded = false;
      this.hasErrors = false;
      this.imageLoadRetries = 0;
      this.isLoadingImage = false;
        
      // 延迟一点再加载，确保之前的请求已完全中止
        setTimeout(() => {
        this.preloadAndCacheImage();
      }, 100);
    },
    
    // 处理窗口大小变化
    handleResize() {
      if (this._imageLoaded && this.$refs.image) {
        this.updateImageDimensions();
          }
    },
    
    // 更新图像尺寸
    updateImageDimensions() {
      const img = this.$refs.image;
      if (img) {
            this.imageWidth = img.naturalWidth || img.width;
            this.imageHeight = img.naturalHeight || img.height;
        console.log('图像尺寸:', {width: this.imageWidth, height: this.imageHeight});
      }
    },
    
    // 图像加载完成
    onImageLoad() {
      console.log('图像加载完成');
      this._imageLoaded = true;
      this.imageLoaded = true;
      this.hasErrors = false;
      
      // 更新图像尺寸
      this.updateImageDimensions();
      
      // 图像加载完成后，加载标注数据
      this.$nextTick(() => {
        console.log('图像尺寸已更新，开始加载标注数据');
        this.loadExistingAnnotations();
      });
      
      // 发出图像加载完成事件
      this.$emit('image-loaded');
    },
    
    // 开始绘制 - 重写版本
    startDrawing(e) {
      // 只在图片上点击时开始
      if (e.target !== this.$refs.image) return;

      e.preventDefault();
      
      const rect = this.$refs.image.getBoundingClientRect();
      this.startX = e.clientX - rect.left;
      this.startY = e.clientY - rect.top;
      this.currentX = this.startX;
      this.currentY = this.startY;
      this.isDrawing = true;

      // 手动创建预览元素
      this.previewEl = document.createElement('div');
      this.previewEl.className = 'drawing-preview';
      this.$refs.imageWrapper.appendChild(this.previewEl);

      document.addEventListener('mousemove', this.drawing);
      document.addEventListener('mouseup', this.endDrawing);
    },
    
    drawing(e) {
      if (!this.isDrawing) return;
      
      e.preventDefault();

      const rect = this.$refs.image.getBoundingClientRect();
      this.currentX = e.clientX - rect.left;
      this.currentY = e.clientY - rect.top;

      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
      }

      this.animationFrameId = requestAnimationFrame(this.updatePreviewBox);
    },
    
    updatePreviewBox() {
        if (!this.isDrawing || !this.previewEl) return;

        const x = Math.min(this.startX, this.currentX);
        const y = Math.min(this.startY, this.currentY);
        const width = Math.abs(this.currentX - this.startX);
        const height = Math.abs(this.currentY - this.startY);
        
        this.previewEl.style.transform = `translate3d(${x}px, ${y}px, 0)`;
        this.previewEl.style.width = `${width}px`;
        this.previewEl.style.height = `${height}px`;

        if (this.currentTool === 'circle') {
             const radius = Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2)) / 2;
             this.previewEl.style.transform = `translate3d(${x}px, ${y}px, 0)`;
             this.previewEl.style.borderRadius = '50%';
        } else {
             this.previewEl.style.borderRadius = '0';
        }
    },
    
    endDrawing(e) {
      if (!this.isDrawing) return;
      
      this.isDrawing = false;
      document.removeEventListener('mousemove', this.drawing);
      document.removeEventListener('mouseup', this.endDrawing);

      // 清理预览框
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }
      if (this.previewEl) {
        this.previewEl.remove();
        this.previewEl = null;
      }

      const width = Math.abs(this.currentX - this.startX);
      const height = Math.abs(this.currentY - this.startY);
      const minSize = 5;

      if (width < minSize && height < minSize) return;

      let newAnnotation;
      if (this.currentTool === 'rectangle') {
        newAnnotation = {
          type: 'rectangle',
          x: Math.min(this.startX, this.currentX),
          y: Math.min(this.startY, this.currentY),
          width,
          height,
          tag: this.currentTag,
        };
      } else if (this.currentTool === 'circle') {
        newAnnotation = {
          type: 'circle',
          cx: this.startX + width / 2,
          cy: this.startY + height / 2,
          r: Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2)) / 2,
          tag: this.currentTag,
        };
      }

      if (newAnnotation) {
        this.annotations.push(newAnnotation);
        this.saveToLocalStorage();
      }
    },
    
    // 清除所有标注
    clearAnnotations() {
              this.annotations = [];
      this.saveToLocalStorage();
              this.$message.success('已清除所有标注');
    },
    
    // 保存标注到本地存储
    saveToLocalStorage() {
      try {
        const storageKey = `annotations_${this.imageId}`;
        localStorage.setItem(storageKey, JSON.stringify(this.annotations));
        console.log('标注已保存到本地存储');
      } catch (error) {
        console.error('保存到本地存储失败:', error);
        this.$message.error('本地保存失败: ' + error.message);
      }
    },
    
    // 从本地存储获取已保存的标注
    fetchSavedAnnotations() {
      if (!this.imageId) return;
      
      try {
        const storageKey = `annotations_${this.imageId}`;
        const savedData = localStorage.getItem(storageKey);
          
        if (savedData) {
          this.annotations = JSON.parse(savedData);
          console.log('从本地存储加载了标注数据');
        } else {
          // 如果本地没有数据，尝试从服务器加载
          console.log('本地没有标注数据，将从服务器加载');
          this.loadExistingAnnotations();
        }
      } catch (error) {
        console.error('从本地存储加载标注失败:', error);
        // 如果本地加载失败，尝试从服务器获取
        this.loadExistingAnnotations();
      }
    },
    
    // 将原来的保存逻辑重命名，并改造为返回Promise，以便父组件可以等待它完成
    async saveAnnotationsToBackend() {
      if (this.annotations.length === 0) {
        this.$message.warning('没有标注可保存');
        return Promise.reject(new Error('没有标注可保存'));
      }
      
      const loading = this.$loading({
        lock: true,
        text: '正在保存标注到服务器...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const token = localStorage.getItem('token') || '';
        
        // 优先使用customId，确保用户ID一致性
        const userId = user.customId || user.id || '';
        const userRole = user.role || 'DOCTOR';

        console.log('准备保存标注，用户信息:', {
          userId: userId,
          userRole: userRole,
          原始id: user.id,
          原始customId: user.customId,
          使用ID类型: user.customId ? 'customId' : 'id'
        });

        const savePromises = this.annotations.map(annotation => {
          const tagData = {
            metadata_id: parseInt(this.imageId, 10),
            tag: annotation.tag,
            x: annotation.x,
            y: annotation.y,
            width: annotation.width,
            height: annotation.height,
            created_by: parseInt(userId, 10)
          };
          console.log('发送标注数据:', tagData);
          return fetch(`${API_BASE_URL}${API_CONTEXT_PATH}/api/tags`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json; charset=UTF-8',
              'Authorization': token,
              'X-User-Id': userId,
              'X-User-Role': userRole,
            },
            body: JSON.stringify(tagData),
          });
        });

        const responses = await Promise.all(savePromises);
        const responseData = [];
        
        for (const response of responses) {
          if (!response.ok) {
            const errorText = await response.text();
            console.error('标注保存失败:', errorText);
            throw new Error(`创建标注失败: ${errorText}`);
          }
          
          try {
            const data = await response.json();
            responseData.push(data);
            console.log('标注保存成功:', data);
          } catch (err) {
            console.warn('无法解析响应JSON:', err);
          }
        }

        try {
        await this.generateAnnotatedImage(userId, token, userRole);
          console.log('生成标注图像成功');
        } catch (err) {
          console.warn('生成标注图像失败，但继续处理:', err.message);
        }
        
        try {
        const result = await this.markImageAsAnnotated(userId, token, userRole);
          console.log('标记图像为已标注成功');
        } catch (err) {
          console.warn('标记图像为已标注失败，但继续处理:', err.message);
        }

        this.$message.success('标注已成功保存到服务器！');
        this.$emit('annotations-saved', {
          imageId: this.imageId,
          annotations: this.annotations
        });
        
        // 保存成功后，重新获取标注列表，以验证标注是否真的保存成功
        try {
          await this.fetchExistingAnnotations();
          if (this.annotations.length > 0) {
            console.log('验证成功：标注已保存到数据库');
          } else {
            console.warn('警告：标注可能未正确保存，请检查数据库');
            this.$message.warning('标注已保存，但未在数据库中找到，请刷新页面确认。');
          }
        } catch (err) {
          console.warn('获取保存后的标注失败:', err.message);
        }
        
        return responseData;
      } catch (error) {
        console.error('保存标注到后端时出错:', error);
        this.$message.error(`保存标注失败: ${error.message}`);
        throw error; // 抛出错误，让父组件知道操作失败
      } finally {
        loading.close();
      }
    },

    // 修改组件内部"保存标注"按钮的行为，只进行本地缓存
    saveAnnotations() {
      this.saveToLocalStorage();
      this.$message.success('标注已临时保存在本地');
    },
    
    // 生成标注图像
    generateAnnotatedImage(userId, token, userRole) {
      console.log('生成标注图像，图像ID:', this.imageId);
      
      // 使用fetch API调用后端API生成标注图像
      return fetch(`${API_BASE_URL}${API_CONTEXT_PATH}/api/tags/generate-annotated-image/${this.imageId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token,
          'X-User-Id': userId,
          'X-User-Role': userRole,
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'POST'
        },
        credentials: 'include',
        mode: 'cors'
      })
      .then(response => {
        if (!response.ok) {
          return response.text().then(text => {
            console.error('生成标注图像失败，错误详情:', text);
            throw new Error(`生成标注图像失败(${response.status}): ${text}`);
          });
        }
        return response.json();
      })
      .then(data => {
        console.log('标注图像生成成功:', data);
        return data;
      })
      .catch(error => {
        console.error('生成标注图像失败:', error);
        
        // 尝试使用另一个API端点
        console.log('尝试使用备用API端点...');
        return fetch(`${API_BASE_URL}${API_CONTEXT_PATH}/api/tags/save-image-after-annotation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token,
            'X-User-Id': userId,
            'X-User-Role': userRole,
            'Origin': window.location.origin,
            'Access-Control-Request-Method': 'POST'
          },
          body: JSON.stringify({ metadata_id: parseInt(this.imageId, 10) }),
          credentials: 'include',
          mode: 'cors'
        })
          .then(response => {
          if (!response.ok) {
            return response.text().then(text => {
              console.error('备用API调用失败，错误详情:', text);
              throw new Error(`备用API调用失败(${response.status}): ${text}`);
            });
          }
          return response.json();
        })
        .then(data => {
          console.log('备用API调用成功:', data);
          return data;
        });
      });
    },
    
    // 标记图像为已标注
    markImageAsAnnotated(userId, token, userRole) {
      console.log('标记图像为已标注，图像ID:', this.imageId);
      
      // 获取当前时间戳
      const timestamp = new Date().toISOString();
              
      // 使用fetch API调用后端API标记图像为已标注
      return fetch(`${API_BASE_URL}${API_CONTEXT_PATH}/api/images/${this.imageId}/mark-annotated?reviewTimestamp=${timestamp}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token,
          'X-User-Id': userId,
          'X-User-Role': userRole,
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'PUT'
        },
        credentials: 'include',
        mode: 'cors'
      })
      .then(response => {
        if (!response.ok) {
          return response.text().then(text => {
            console.error('标记图像为已标注失败，错误详情:', text);
            throw new Error(`标记图像为已标注失败(${response.status}): ${text}`);
          });
        }
        return response.json();
      })
      .then(data => {
        console.log('标记图像为已标注成功:', data);
        return data;
      })
      .catch(error => {
        console.error('标记图像为已标注失败:', error);
        throw error;
      });
    },
    
    // 计算标签X位置
    getTagPositionX(annotation) {
      if (annotation.type === 'rectangle') {
        return annotation.x;
      } else if (annotation.type === 'circle') {
        return annotation.cx - annotation.r;
      }
      return 0;
    },
    
    // 计算标签Y位置
    getTagPositionY(annotation) {
      if (annotation.type === 'rectangle') {
        return annotation.y - 25; // 标签高度
      } else if (annotation.type === 'circle') {
        return annotation.cy - annotation.r - 25; // 标签高度
      }
      return 0;
    },
    
    // 获取标签颜色
    getTagColor(tag) {
      // 根据标签类型返回不同的颜色
      const colorMap = {
        // 真性血管肿瘤 - 红色系
        '婴幼儿血管瘤': '#ff5252',
        '先天性快速消退型血管瘤': '#ff7252',
        '先天性部分消退型血管瘤': '#ff9252',
        '先天性不消退型血管瘤': '#ffb252',
        '卡波西型血管内皮细胞瘤': '#e91e63',
        '丛状血管瘤': '#f44336',
        '化脓性肉芽肿': '#ff6b6b',
        '梭形细胞血管瘤': '#ff8a80',
        '上皮样血管内皮瘤': '#ffab91',
        '网状血管内皮瘤': '#ffcc02',
        '假肌源性血管内皮瘤': '#ff9800',
        '多形性血管内皮瘤': '#ff5722',
        '血管肉瘤': '#d32f2f',
        '上皮样血管肉瘤': '#c62828',
        '卡波西肉瘤': '#b71c1c',

        // 血管畸形 - 蓝色系
        '微静脉畸形': '#2196f3',
        '静脉畸形': '#03a9f4',
        '动静脉畸形': '#00bcd4',
        '淋巴管畸形': '#4fc3f7',
        '球细胞静脉畸形': '#29b6f6',
        '毛细血管-淋巴管-静脉畸形': '#42a5f5',
        '毛细血管-动静脉畸形': '#1e88e5',
        '淋巴管-静脉畸形': '#1976d2',

        // 血管假瘤/易混淆病变 - 绿色系
        '血管外皮细胞瘤': '#4caf50',
        '血管球瘤': '#66bb6a',
        '血管平滑肌瘤': '#81c784',
        '血管纤维瘤': '#a5d6a7',
        '靶样含铁血黄素沉积性血管瘤': '#c8e6c9',
        '鞋钉样血管瘤': '#388e3c',

        // 兼容旧版本
        'IH-婴幼儿血管瘤': '#ff5252',
        'RICH-先天性快速消退型血管瘤': '#ff7252',
        'PICH-先天性部分消退型血管瘤': '#ff9252',
        'NICH-先天性不消退型血管瘤': '#ffb252',
        'KHE-卡泊西型血管内皮细胞瘤': '#e91e63',
        '血管瘤': '#ff5252',
        '肝囊肿': '#f56c6c',
        '肝血管瘤': '#f56c6c',
        '肝癌': '#f56c6c',
        '转移瘤': '#f56c6c',
        '胆管细胞癌': '#f56c6c'
      };

      return colorMap[tag] || '#ff5252'; // 默认红色
    },
    
    getAnnotationStyle(annotation) {
      console.log('渲染标注:', annotation);
      
      // 确保坐标是数字类型
      const x = typeof annotation.x === 'string' ? parseFloat(annotation.x) : annotation.x;
      const y = typeof annotation.y === 'string' ? parseFloat(annotation.y) : annotation.y;
      const width = typeof annotation.width === 'string' ? parseFloat(annotation.width) : annotation.width;
      const height = typeof annotation.height === 'string' ? parseFloat(annotation.height) : annotation.height;
      
      // 判断是否是相对坐标（0-1之间的小数）
      const isRelative = x <= 1 && y <= 1 && width <= 1 && height <= 1;
      
      // 根据是否为相对坐标计算实际像素值
      let pixelX = isRelative ? x * this.imageWidth : x;
      let pixelY = isRelative ? y * this.imageHeight : y;
      let pixelWidth = isRelative ? width * this.imageWidth : width;
      let pixelHeight = isRelative ? height * this.imageHeight : height;
      
      if (annotation.type === 'rectangle') {
        return {
          transform: `translate3d(${pixelX}px, ${pixelY}px, 0)`,
          width: `${pixelWidth}px`,
          height: `${pixelHeight}px`,
          borderColor: this.getTagColor(annotation.tag),
        };
      }
      if (annotation.type === 'circle') {
        return {
          // Circles are positioned by their center, so we offset by the radius
          transform: `translate3d(${annotation.cx - annotation.r}px, ${annotation.cy - annotation.r}px, 0)`,
          width: `${annotation.r * 2}px`,
          height: `${annotation.r * 2}px`,
          borderRadius: '50%',
          borderColor: this.getTagColor(annotation.tag),
        };
      }
      return {};
    },

    // 用于验证标注是否成功保存到数据库
    async fetchExistingAnnotations() {
      try {
        console.log('正在验证标注是否已保存到数据库...');
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const userId = user.customId || user.id || '';
        
        // 添加时间戳和随机数避免缓存
        const timestamp = new Date().getTime();
        const random = Math.random();
        
        const response = await fetch(`${API_BASE_URL}${API_CONTEXT_PATH}/api/tags/image/${this.imageId}?t=${timestamp}&r=${random}&userId=${userId}`);
        
        if (!response.ok) {
          throw new Error(`获取标注失败: ${response.status}`);
        }
        
        const data = await response.json();
        console.log(`获取到 ${data.length} 个标注，验证成功`);
        
        // 如果有标注数据，说明保存成功
        if (data && data.length > 0) {
          return true;
        } else {
          return false;
        }
      } catch (error) {
        console.error('获取标注数据失败:', error);
        throw error;
      }
    },

    // 增强loadExistingAnnotations方法，添加重试机制
    async loadExistingAnnotations() {
      console.log('加载图像ID为', this.imageId, '的现有标注');
      
      // 确保图像已加载且尺寸已知
      if (!this._imageLoaded || this.imageWidth <= 0 || this.imageHeight <= 0) {
        console.warn('图像尚未完全加载，延迟加载标注数据');
        // 延迟200ms后重试
        setTimeout(() => this.loadExistingAnnotations(), 200);
        return;
      }
      
      // 最多重试3次
      let retryCount = 0;
      const maxRetries = 3;
      
      const tryLoadAnnotations = async () => {
        try {
          const user = JSON.parse(localStorage.getItem('user') || '{}');
          const userId = user.customId || user.id || '';
          
          // 添加时间戳和随机数避免缓存
          const timestamp = new Date().getTime();
          const random = Math.random();
          
          // 添加更多日志，帮助调试
          console.log(`请求标注数据URL: ${API_BASE_URL}${API_CONTEXT_PATH}/api/tags/image/${this.imageId}?t=${timestamp}&r=${random}&userId=${userId}`);
          console.log('当前图像尺寸:', {width: this.imageWidth, height: this.imageHeight});
          
          const response = await fetch(`${API_BASE_URL}${API_CONTEXT_PATH}/api/tags/image/${this.imageId}?t=${timestamp}&r=${random}&userId=${userId}`);
          
          if (!response.ok) {
            throw new Error(`获取标注失败: ${response.status}`);
          }
          
          const data = await response.json();
          console.log(`获取到 ${data.length} 个已有标注`, data);
          
          // 只有当本地没有标注时，才使用从服务器获取的标注
          if (this.annotations.length === 0 && data && data.length > 0) {
            // 将后端标注数据格式转换为前端格式
            const convertedAnnotations = data.map(tag => ({
              type: 'rectangle', // 默认使用矩形标注
              x: tag.x * this.imageWidth,
              y: tag.y * this.imageHeight,
              width: tag.width * this.imageWidth,
              height: tag.height * this.imageHeight,
              tag: tag.tag || tag.tagName || 'IH-婴幼儿血管瘤',
              serverId: tag.id // 保存服务器ID，便于后续更新操作
            }));
            
            console.log('转换后的标注数据:', convertedAnnotations);
            this.annotations = convertedAnnotations;
            
            // 立即更新图片尺寸以正确显示标注
            this.$nextTick(() => {
              // 强制重新渲染
              this.$forceUpdate();
            });
          }
          
          // 发出标注加载完成事件
          this.$emit('annotations-loaded', this.annotations);
          
        } catch (error) {
          console.error(`获取现有标注失败(尝试 ${retryCount + 1}/${maxRetries}):`, error);
          
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`${retryCount}秒后重试...`);
            // 指数退避重试
            await new Promise(resolve => setTimeout(resolve, retryCount * 1000));
            return tryLoadAnnotations();
          } else {
            console.error('达到最大重试次数，使用空标注数组');
            this.annotations = [];
            // 即使失败也发出事件
            this.$emit('annotations-loaded', []);
          }
        }
      };
      
      return tryLoadAnnotations();
    },

    // 根据图像尺寸调整标注框
    resizeAnnotations() {
      if (this.annotations.length === 0 || !this.imageWidth || !this.imageHeight) return;
      
      console.log('调整标注框大小，当前图像尺寸:', {width: this.imageWidth, height: this.imageHeight});
      console.log('调整前的标注数据:', JSON.stringify(this.annotations));
      
      // 检查标注数据是否包含相对坐标
      const hasRelativeCoordinates = this.annotations.some(
        annotation => annotation.x <= 1 && annotation.y <= 1 && annotation.width <= 1 && annotation.height <= 1
      );
      
      // 如果是相对坐标，转换为绝对像素坐标
      if (hasRelativeCoordinates) {
        console.log('检测到相对坐标，进行转换...');
        this.annotations = this.annotations.map(annotation => ({
          ...annotation,
          x: annotation.x * this.imageWidth,
          y: annotation.y * this.imageHeight,
          width: annotation.width * this.imageWidth,
          height: annotation.height * this.imageHeight
        }));
      }
      
      console.log('调整后的标注数据:', JSON.stringify(this.annotations));
      this.$forceUpdate(); // 强制重新渲染
    },

    // 调试输出所有标注信息
    debugAnnotations() {
      console.log('===== 调试标注信息 =====');
      console.log('图像尺寸:', {width: this.imageWidth, height: this.imageHeight});
      console.log('标注总数:', this.annotations.length);
      this.annotations.forEach((annotation, index) => {
        console.log(`标注[${index}]:`, {
          类型: annotation.type,
          x: annotation.x,
          y: annotation.y,
          宽度: annotation.width,
          高度: annotation.height,
          标签: annotation.tag,
          是否相对坐标: annotation.x <= 1 && annotation.y <= 1 && annotation.width <= 1 && annotation.height <= 1
        });
      });
      console.log('========================');
    }
  }
};
</script>

<style scoped>
.image-annotator {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.toolbar {
  display: flex;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  gap: 10px;
  align-items: center;
  z-index: 10;
  position: relative;
}

.image-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border: 1px solid #dcdfe6;
  /* 添加硬件加速 */
  transform: translateZ(0);
  will-change: transform;
}

.image-wrapper {
  position: relative;
  cursor: crosshair;
  user-select: none;
  overflow: hidden;
}

.annotatable-image {
  max-width: 100%;
  max-height: calc(100vh - 300px);
  display: block;
  cursor: crosshair;
  user-select: none;
  -webkit-user-drag: none;
  /* 防止图像闪烁 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  /* 添加硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  /* 防止图像在拖动时重新加载 */
  pointer-events: auto;
  /* 防止FOUC (Flash of Unstyled Content) */
  backface-visibility: hidden;
  /* 提高渲染性能 */
  contain: paint;
  z-index: 1; /* 确保在标注层下方 */
}

.annotation-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.annotation {
  position: absolute;
  border: 2px solid;
  box-sizing: border-box;
  pointer-events: none;
  background-color: rgba(255, 0, 0, 0.1);
}

.drawing-preview {
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(255, 0, 0, 0.3);
  border: 2px dashed #ff0000;
  box-sizing: border-box;
  pointer-events: none; 
  z-index: 1000;
  will-change: transform, width, height, border-radius;
}

.circle-preview {
  border-radius: 50%;
}

@keyframes pulse-preview {
  from { background-color: rgba(255, 0, 0, 0.2); }
  to { background-color: rgba(255, 0, 0, 0.4); }
}

.error-message {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  color: #f56c6c;
  font-size: 16px;
  z-index: 10;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 11;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 10px;
  font-size: 16px;
  color: #606266;
}

.tag-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  /* 添加硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  /* 提高渲染性能 */
  contain: layout style;
}

.tag-label {
  position: absolute;
  padding: 2px 6px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  font-size: 12px;
  color: #606266;
  /* 添加硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  /* 防止闪烁 */
  backface-visibility: hidden;
}

.temp-annotation {
  border-width: 3px;
  border-color: #ff0000 !important;
  background-color: rgba(255, 0, 0, 0.3);
  box-shadow: 0 0 8px rgba(255, 0, 0, 0.6);
  z-index: 1000;
  animation: pulse-preview 1s infinite alternate;
}
</style> 