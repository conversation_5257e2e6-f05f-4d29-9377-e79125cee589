-- 创建团队表
CREATE TABLE IF NOT EXISTS teams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by INT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 添加用户的团队关联字段
ALTER TABLE users 
    ADD COLUMN team_id INT NULL,
    ADD COLUMN reviewer_application_status VARCHAR(20) NULL,
    ADD COLUMN reviewer_application_reason TEXT NULL,
    ADD COLUMN reviewer_application_date DATETIME NULL,
    ADD COLUMN reviewer_application_processed_by INT NULL,
    ADD COLUMN reviewer_application_processed_date DATETIME NULL,
    ADD FOREIGN KEY (team_id) REFERENCES teams(id),
    ADD FOREIGN KEY (reviewer_application_processed_by) REFERENCES users(id);
    
-- 添加图像元数据的团队关联字段
ALTER TABLE image_metadata
    ADD COLUMN team_id INT NULL,
    ADD COLUMN submitted_at DATETIME NULL,
    ADD COLUMN submitted_by INT NULL,
    ADD FOREIGN KEY (team_id) REFERENCES teams(id),
    ADD FOREIGN KEY (submitted_by) REFERENCES users(id);

-- 创建角色变更审计日志表
CREATE TABLE IF NOT EXISTS role_change_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    old_role VARCHAR(20) NULL,
    new_role VARCHAR(20) NOT NULL,
    changed_by INT NOT NULL,
    reason TEXT,
    changed_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (changed_by) REFERENCES users(id)
);

-- 创建团队成员变更审计日志表
CREATE TABLE IF NOT EXISTS team_member_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    team_id INT NOT NULL,
    action ENUM('ADD', 'REMOVE') NOT NULL,
    performed_by INT NOT NULL,
    performed_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (team_id) REFERENCES teams(id),
    FOREIGN KEY (performed_by) REFERENCES users(id)
);

-- 创建初始管理员团队（可选）
INSERT INTO teams(name, description, created_by, created_at, updated_at)
SELECT '管理员团队', '系统管理员团队', id, NOW(), NOW() 
FROM users 
WHERE role = 'ADMIN' 
LIMIT 1;

-- 将现有管理员添加到管理员团队（可选）
UPDATE users u
JOIN teams t ON t.name = '管理员团队'
SET u.team_id = t.id
WHERE u.role = 'ADMIN'; 