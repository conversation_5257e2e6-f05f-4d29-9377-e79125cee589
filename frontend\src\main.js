// 导入polyfills解决模块问题
import './polyfills.js';

// 导入Element Plus统一修复模块
import './element-plus-unified-fix.js';

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import axios from 'axios'
// 导入API配置
import { API_BASE_URL, API_CONTEXT_PATH, DASHBOARD_STATS_URL, DASHBOARD_STATS_WITH_PARAMS } from './config/api.config'

// 全局引入 Element Plus
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 单独引入 Element Plus 组件的方法，确保正确注册
import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'

// 覆盖ElMessage方法，使其不显示任何提示
// 替换为空函数
ElMessage.success = () => {};
ElMessage.error = () => {};
ElMessage.info = () => {};
ElMessage.warning = () => {};

// 引入自定义权限指令
import { registerPermissionDirective } from './directives/permission'

// 引入组件暴露模块
import './expose-components'

// 引入全局样式
import './assets/css/style.css'

// 引入背景样式
import '@/assets/css/background.css'

// 引入调试工具
import { enableAnnotationDebug } from './utils/debug.js';

// 添加用户权限修复工具
window.fixUserPermission = function() {
  try {
    // 获取当前用户
    const userStr = localStorage.getItem('user');
    if (!userStr) {
      return {success: false, message: '未找到已登录用户'};
    }
    
    const user = JSON.parse(userStr);
    
    // 检查用户角色
    if (!user.role) {
      // 根据用户ID模式判断角色
      if (user.customId && user.customId.startsWith('1')) {
        user.role = 'ADMIN';
      } else if (user.customId && user.customId.startsWith('3')) {
        user.role = 'REVIEWER';
      } else {
        user.role = 'DOCTOR';
      }
      
      // 保存修复后的用户数据
      localStorage.setItem('user', JSON.stringify(user));
      
      // 更新store
      store.commit('setUser', user);
      
      return {success: true, message: `已修复用户角色为: ${user.role}`};
    } else {
      // 检查角色是否与ID模式一致
      let expectedRole = null;
      
      if (user.customId && user.customId.startsWith('1')) {
        expectedRole = 'ADMIN';
      } else if (user.customId && user.customId.startsWith('3')) {
        expectedRole = 'REVIEWER';
      } else if (user.customId && user.customId.startsWith('2')) {
        expectedRole = 'DOCTOR';
      }
      
      // 如果有期望角色且与当前角色不符，进行修复
      if (expectedRole && user.role !== expectedRole) {
        user.role = expectedRole;
        localStorage.setItem('user', JSON.stringify(user));
        // 更新store
        store.commit('setUser', user);
        return {success: true, message: `已将用户角色修复为${expectedRole}`};
      }
      
      return {success: true, message: `用户角色已存在: ${user.role}`};
    }
  } catch (error) {
    return {success: false, message: `修复失败: ${error.message}`};
  }
};

// 替换localStorage监听为空函数
const originalSetItem = localStorage.setItem;
const originalRemoveItem = localStorage.removeItem;

localStorage.setItem = function(key, value) {
  originalSetItem.call(this, key, value);
};

localStorage.removeItem = function(key) {
  originalRemoveItem.call(this, key);
};

// 设置axios全局默认值
// axios.defaults.baseURL = '/api'; // 不使用baseURL，避免路径重复
axios.defaults.withCredentials = true; // 允许跨域请求发送cookies
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.timeout = 10000; // 10秒超时

// 移除所有日志输出的请求拦截器
axios.interceptors.request.use(
  config => {
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 移除所有日志输出的响应拦截器
axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    return Promise.reject(error);
  }
);

const app = createApp(App)

// 禁用全局错误处理器中的日志输出
app.config.errorHandler = function(err, vm, info) {
  // 错误处理逻辑保留，但不输出日志
};

// 禁用全局未捕获的Promise异常处理中的日志输出
window.addEventListener('unhandledrejection', event => {
  // 异常处理逻辑保留，但不输出日志
});

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局配置
app.config.globalProperties.$axios = axios

// 正确注册Element Plus的消息组件
app.config.globalProperties.$message = ElMessage
app.config.globalProperties.$notify = ElNotification
app.config.globalProperties.$msgbox = ElMessageBox
app.config.globalProperties.$alert = ElMessageBox.alert
app.config.globalProperties.$confirm = ElMessageBox.confirm
app.config.globalProperties.$prompt = ElMessageBox.prompt
app.config.globalProperties.$loading = ElLoading.service

// 注册权限指令
registerPermissionDirective(app)

// 注册Element Plus
app.use(store)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})

// 将消息组件暴露给全局，以便API响应拦截器可以使用
window.$message = ElMessage

// 定义全局的仪表盘数据刷新函数
window.refreshDashboardStats = async () => {
  console.log('全局刷新函数 window.refreshDashboardStats 被调用');
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return;
    const user = JSON.parse(userStr);
    // 只使用数字ID
    const userId = user.id;
    if (!userId) return;

    // 确保使用带个人统计参数的正确URL
    const url = DASHBOARD_STATS_WITH_PARAMS(userId);
    console.log('全局刷新函数调用的URL:', url);
    
    const response = await axios.get(url, {
      headers: { 'Cache-Control': 'no-cache' }
    });

    if (response.data) {
      store.commit('setDashboardStats', response.data);
      console.log('全局统计数据已刷新并存入store');
    }
  } catch (error) {
    console.error('全局刷新仪表盘数据失败:', error);
  }
};

// 添加禁用日志输出的函数
const disableConsoleOutput = () => {
  if (typeof window !== 'undefined') {
    window.console = {
      ...console,
      log: function() {},
      info: function() {},
      warn: function() {},
      error: function() {},
      debug: function() {}
    };
  }
};

// 初始化应用
app.mount('#app');

// 禁用控制台输出
disableConsoleOutput();

// 在应用挂载后执行数据强制更新
setTimeout(() => {
  console.log('🔄 应用挂载后尝试强制更新统计数据');
  
  try {
    // 1. 尝试从localStorage获取统计数据
    const statsData = JSON.parse(localStorage.getItem('dashboardStats') || '{}');
    if (statsData && statsData.totalCount) {
      console.log('找到统计数据:', statsData);
      
      // 2. 直接更新DOM
      const statElements = document.querySelectorAll('.stat-value');
      if (statElements && statElements.length >= 5) {
        statElements[0].textContent = statsData.totalCount || 0;
        statElements[1].textContent = statsData.draftCount || 0;
        statElements[2].textContent = statsData.reviewedCount || 0;
        statElements[3].textContent = statsData.submittedCount || 0;
        statElements[4].textContent = statsData.approvedCount || 0;
        console.log('📊 统计数据DOM元素已强制更新!');
      } else {
        console.log('未找到统计卡片DOM元素:', statElements ? statElements.length : 0);
        
        // 如果找不到元素，可能是还没渲染，再等待一小段时间
        setTimeout(() => {
          const retryElements = document.querySelectorAll('.stat-value');
          if (retryElements && retryElements.length >= 5) {
            retryElements[0].textContent = statsData.totalCount || 0;
            retryElements[1].textContent = statsData.draftCount || 0;
            retryElements[2].textContent = statsData.reviewedCount || 0;
            retryElements[3].textContent = statsData.submittedCount || 0;
            retryElements[4].textContent = statsData.approvedCount || 0;
            console.log('📊 第二次尝试：统计数据DOM元素已强制更新!');
          }
        }, 300);
      }
    } else {
      console.log('localStorage中没有找到统计数据，尝试直接获取');
      
      // 3. 如果没有localStorage数据，获取当前用户ID后请求
      const axios = require('axios').default;
      
      // 从localStorage获取当前用户ID
      try {
        const userStr = localStorage.getItem('user') || '{}';
        const user = JSON.parse(userStr);
        // 只使用数字ID
        const userId = user.id;
        
        if (!userId) {
          console.log('找不到有效的用户ID，跳过数据获取');
          return;
        }
        
        console.log('尝试获取用户', userId, '的统计数据');
        
        // 使用无认证限制的API端点
        axios.get(`${API_BASE_URL}${API_CONTEXT_PATH}/api/stats-v2/dashboard/${userId}?t=${Date.now()}&r=${Math.random()}&forcePersonalStats=true&view=personal`, {
          headers: {
            'Cache-Control': 'no-cache, no-store',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
          .then(response => {
            console.log('直接获取统计数据成功:', response.data);
            
            // 更新DOM
            const statElements = document.querySelectorAll('.stat-value');
            if (statElements && statElements.length >= 5) {
              statElements[0].textContent = response.data.totalCount || 0;
              statElements[1].textContent = response.data.draftCount || 0;
              statElements[2].textContent = response.data.reviewedCount || 0;
              statElements[3].textContent = response.data.submittedCount || 0;
              statElements[4].textContent = response.data.approvedCount || 0;
              console.log('📊 通过API获取的统计数据已更新到DOM!');
            }
          })
          .catch(error => {
            console.error('直接获取统计数据失败:', error);
          });
      } catch (error) {
        console.error('获取用户ID失败:', error);
      }
    }
  } catch (error) {
    console.error('强制更新统计数据失败:', error);
  }
}, 500);

// 在应用挂载后执行数据库清理
// console.log('应用启动，执行数据库清理...');
// cleanInvalidImagePairs(); 

// 添加window.location变更监听
const originalPushState = history.pushState;
const originalReplaceState = history.replaceState;

// 重写pushState
history.pushState = function() {
  console.log('[URL变更] history.pushState 调用:', {
    状态: arguments[0],
    标题: arguments[1],
    URL: arguments[2],
    当前时间: new Date().toLocaleTimeString(),
    调用方法: '直接调用'
  });
  
  // 获取调用堆栈
  const stack = new Error().stack;
  console.log('[URL变更] pushState调用堆栈:', stack);
  
  return originalPushState.apply(this, arguments);
};

// 重写replaceState
history.replaceState = function() {
  console.log('[URL变更] history.replaceState 调用:', {
    状态: arguments[0],
    标题: arguments[1],
    URL: arguments[2],
    当前时间: new Date().toLocaleTimeString(),
    调用方法: '直接调用'
  });
  
  // 获取调用堆栈
  const stack = new Error().stack;
  console.log('[URL变更] replaceState调用堆栈:', stack);
  
  return originalReplaceState.apply(this, arguments);
};

// 监听popstate事件
window.addEventListener('popstate', function(event) {
  console.log('[URL变更] popstate事件触发:', {
    状态: event.state,
    当前URL: window.location.href,
    当前时间: new Date().toLocaleTimeString(),
    调用方法: 'popstate事件'
  });
});

// 创建全局事件总线用于组件间通信
import mitt from 'mitt';
const eventBus = mitt();

// 全局事件总线，用于组件间通信
window.eventBus = eventBus;

// 启用标注调试模式 - 修复Vue未定义错误
app.config.globalProperties.$enableAnnotationDebug = function() {
  enableAnnotationDebug();
  app.config.globalProperties.$isAnnotationDebugEnabled = true;
};

// 在进入应用后自动启用标注调试模式（可通过控制台禁用）
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    if (window.location.href.includes('annotation') || 
        window.location.href.includes('case') ||
        localStorage.getItem('enableDebug') === 'true') {
      try {
        enableAnnotationDebug();
        console.log('标注调试模式已自动启用');
      } catch (e) {
        console.error('启用调试模式失败', e);
      }
    }
  }, 1000);
}); 