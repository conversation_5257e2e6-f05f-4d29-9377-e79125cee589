package com.medical.annotation.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

@Configuration
@Primary
public class WebConfig implements WebMvcConfigurer {

    // 注入AppConfig中配置的图片存储路径
    @Autowired
    @Qualifier("getUploadDir")
    private String uploadDir;
    
    @Autowired
    @Qualifier("getAnnotatedDir")
    private String annotatedDir;
    
    @Autowired
    @Qualifier("getProcessedDir")
    private String processedDir;
    
    @Autowired
    @Qualifier("getTempImagesDir")
    private String tempImagesDir;
    
    @Autowired
    private List<String> allowedOrigins;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 确保目录存在
        createDirectoryIfNotExists(uploadDir);
        createDirectoryIfNotExists(annotatedDir);
        createDirectoryIfNotExists(processedDir);
        createDirectoryIfNotExists(tempImagesDir);
        
        // 使用绝对路径而不是相对路径
        File uploadDirFile = new File(uploadDir);
        File processedDirFile = new File(processedDir);
        File annotatedDirFile = new File(annotatedDir);
        File tempImagesDirFile = new File(tempImagesDir);
        
        // 确保使用绝对路径
        String uploadDirFullPath = uploadDirFile.getAbsolutePath();
        String processedDirFullPath = processedDirFile.getAbsolutePath();
        String annotatedDirFullPath = annotatedDirFile.getAbsolutePath();
        String tempImagesDirFullPath = tempImagesDirFile.getAbsolutePath();
        
        // 格式化为文件URL格式
        String uploadDirUrl = "file:" + ensureCorrectPathFormat(uploadDirFullPath) + "/";
        String processedDirUrl = "file:" + ensureCorrectPathFormat(processedDirFullPath) + "/";
        String annotatedDirUrl = "file:" + ensureCorrectPathFormat(annotatedDirFullPath) + "/";
        String tempImagesDirUrl = "file:" + ensureCorrectPathFormat(tempImagesDirFullPath) + "/";
        
        // 添加备用目录 - 使前端更容易找到图片文件
        // 使用注入的uploadDir作为备用路径
        String backupImageUrl = "file:" + ensureCorrectPathFormat(uploadDir) + "/";
        
        System.out.println("\n===== 增强的静态资源映射配置 =====");
        
        // 简化路径映射 - 不带/medical前缀
        System.out.println("配置简化路径映射 - 不带/medical前缀");
        
        // 上传原始图片和公共路径 - 简化路径
        registry.addResourceHandler("/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        // 处理后的图片路径 - 简化路径
        registry.addResourceHandler("/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/images/processed/** -> " + processedDirUrl);
        
        // 标注后的图片路径 - 简化路径
        registry.addResourceHandler("/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/images/annotated/** -> " + annotatedDirUrl);
        
        // 临时图像目录映射 - 简化路径
        registry.addResourceHandler("/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 标准路径映射 - 带/medical前缀
        System.out.println("\n配置标准路径映射 - 带/medical前缀");
        
        // 上传原始图片和公共路径 - 标准路径
        registry.addResourceHandler("/medical/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/medical/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        // 处理后的图片路径 - 标准路径
        registry.addResourceHandler("/medical/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/medical/images/processed/** -> " + processedDirUrl);
        
        // 标注后的图片路径 - 标准路径
        registry.addResourceHandler("/medical/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/medical/images/annotated/** -> " + annotatedDirUrl);
        
        // 临时图像目录映射 - 标准路径
        registry.addResourceHandler("/medical/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/medical/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 双重路径问题处理 - 添加对/medical/medical/images/**的支持
        System.out.println("\n配置双重路径映射处理");
        registry.addResourceHandler("/medical/medical/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/medical/medical/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        registry.addResourceHandler("/medical/medical/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/medical/medical/images/processed/** -> " + processedDirUrl);
        
        registry.addResourceHandler("/medical/medical/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/medical/medical/images/annotated/** -> " + annotatedDirUrl);
        
        registry.addResourceHandler("/medical/medical/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/medical/medical/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 临时目录映射
        String tempDir = System.getProperty("java.io.tmpdir");
        String tempDirUrl = "file:" + ensureCorrectPathFormat(tempDir) + "/";
        registry.addResourceHandler("/temp/**")
                .addResourceLocations(tempDirUrl);
        System.out.println("/temp/** -> " + tempDirUrl);
        
        // 验证目录是否存在并有正确权限
        System.out.println("\n===== 目录权限检查 =====");
        System.out.println("上传目录: " + uploadDirFullPath);
        System.out.println("  - 存在: " + uploadDirFile.exists());
        System.out.println("  - 可读: " + uploadDirFile.canRead());
        System.out.println("  - 可写: " + uploadDirFile.canWrite());
        
        System.out.println("处理目录: " + processedDirFullPath);
        System.out.println("  - 存在: " + processedDirFile.exists());
        System.out.println("  - 可读: " + processedDirFile.canRead());
        System.out.println("  - 可写: " + processedDirFile.canWrite());
        
        System.out.println("标注目录: " + annotatedDirFullPath);
        System.out.println("  - 存在: " + annotatedDirFile.exists());
        System.out.println("  - 可读: " + annotatedDirFile.canRead());
        System.out.println("  - 可写: " + annotatedDirFile.canWrite());
        
        System.out.println("临时目录: " + tempImagesDirFullPath);
        System.out.println("  - 存在: " + tempImagesDirFile.exists());
        System.out.println("  - 可读: " + tempImagesDirFile.canRead());
        System.out.println("  - 可写: " + tempImagesDirFile.canWrite());
        System.out.println("===============================");
    }
    
    /**
     * 确保路径格式正确（适用于文件URL）
     */
    private String ensureCorrectPathFormat(String path) {
        // 确保路径使用正斜杠
        String formattedPath = path.replace('\\', '/');
        
        // 确保路径不以斜杠结尾
        if (formattedPath.endsWith("/")) {
            formattedPath = formattedPath.substring(0, formattedPath.length() - 1);
        }
        
        return formattedPath;
    }
    
    /**
     * 确保目录存在
     */
    private void createDirectoryIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            System.out.println("创建目录 " + dirPath + ": " + (created ? "成功" : "失败"));
            if (!created) {
                System.err.println("无法创建目录: " + dir.getAbsolutePath());
                // 检查父目录权限
                File parentDir = dir.getParentFile();
                if (parentDir != null) {
                    System.err.println("父目录存在: " + parentDir.exists());
                    if (parentDir.exists()) {
                        System.err.println("父目录可写: " + parentDir.canWrite());
                    }
                }
            }
        }
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(stringHttpMessageConverter());
    }

    @Bean
    public StringHttpMessageConverter stringHttpMessageConverter() {
        return new StringHttpMessageConverter(StandardCharsets.UTF_8);
    }
    
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("http://localhost:8080", "http://************:8080") // 允许特定来源访问
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH")
                .allowedHeaders("Authorization", "Content-Type", "X-Requested-With", "Accept", 
                               "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers",
                               "X-User-Id", "X-User-Email", "X-Authentication-Email", "X-Request-Time", "x-user-custom-id")
                .exposedHeaders("Authorization", "Content-Type", "Content-Length", 
                               "Access-Control-Allow-Origin", "Access-Control-Allow-Headers")
                .allowCredentials(true) // 允许凭证
                .maxAge(3600); // 1小时的预检请求缓存
                
        System.out.println("配置全局CORS - 允许特定来源并启用凭证");
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        
        // 允许特定来源
        config.addAllowedOrigin("http://localhost:8080");
        config.addAllowedOrigin("http://************:8080");
        
        // 允许所有HTTP方法
        config.addAllowedMethod("*");
        
        // 允许所有头
        config.addAllowedHeader("*");
        
        // 设置预检请求的缓存时间
        config.setMaxAge(3600L);
        
        // 允许凭证
        config.setAllowCredentials(true);
        
        source.registerCorsConfiguration("/**", config);
        System.out.println("创建全局CORS过滤器 - 允许特定来源并启用凭证");
        return new CorsFilter(source);
    }
} 