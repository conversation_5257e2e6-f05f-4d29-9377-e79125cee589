package com.medical.annotation.controller;

import com.medical.annotation.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestEmailController {

    @Autowired
    private EmailService emailService;

    @GetMapping("/send-email")
    public ResponseEntity<?> testSendEmail(@RequestParam String email) {
        try {
            // 生成6位随机验证码
            String code = String.format("%06d", (int)(Math.random() * 1000000));
            
            // 发送邮件
            emailService.sendPasswordResetEmail(email, code);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "测试邮件已发送，验证码: " + code);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/send-simple-email")
    public ResponseEntity<?> testSendSimpleEmail(@RequestParam String email) {
        try {
            // 生成6位随机验证码
            String code = String.format("%06d", (int)(Math.random() * 1000000));
            
            // 发送邮件
            emailService.sendSimplePasswordResetEmail(email, code);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "测试简单邮件已发送，验证码: " + code);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
} 