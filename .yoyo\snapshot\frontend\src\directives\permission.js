// 权限控制指令

import store from '../store'
import { hasPermission as checkPermission } from '../utils/permissions'

/**
 * 使用方法：
 * 1. v-permission="'permission_name'"  - 检查单个权限
 * 2. v-permission="['permission1', 'permission2']" - 检查多个权限（OR关系）
 * 3. v-permission:all="['permission1', 'permission2']" - 检查多个权限（AND关系）
 * 4. v-permission:role="'ADMIN'" - 直接检查角色
 */
export const permission = {
  mounted(el, binding) {
    const { value, arg } = binding
    const userRole = store.getters.getUserRole
    
    if (!userRole) {
      el.style.display = 'none'
      return
    }
    
    // 直接检查角色
    if (arg === 'role') {
      const roles = Array.isArray(value) ? value : [value]
      if (!roles.includes(userRole)) {
        el.style.display = 'none'
      }
      return
    }
    
    // 检查权限
    if (Array.isArray(value)) {
      // 多个权限
      if (arg === 'all') {
        // AND逻辑 - 必须拥有所有权限
        const hasAllPermissions = value.every(permission => 
          checkPermission(userRole, permission)
        )
        if (!hasAllPermissions) {
          el.style.display = 'none'
        }
      } else {
        // OR逻辑 - 拥有其中一个权限即可
        const hasAnyPermission = value.some(permission => 
          checkPermission(userRole, permission)
        )
        if (!hasAnyPermission) {
          el.style.display = 'none'
        }
      }
    } else {
      // 单个权限
      if (!checkPermission(userRole, value)) {
        el.style.display = 'none'
      }
    }
  }
}

// 注册全局权限检查函数
export function registerPermissionDirective(app) {
  app.directive('permission', permission)
} 