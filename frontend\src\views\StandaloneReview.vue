<template>
  <div class="standalone-container">
    <AnnotationReviewList standalone-mode="true" />
  </div>
</template>

<script>
import AnnotationReviewList from '@/components/AnnotationReviewList.vue';

export default {
  name: 'StandaloneReview',
  components: {
    AnnotationReviewList
  },
  created() {
    // 设置文档标题
    document.title = '标注审核页面';
  }
};
</script>

<style>
/* 重置全局样式，确保没有主布局元素 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}

#app {
  height: 100%;
}

.standalone-container {
  height: 100vh;
  width: 100vw;
  overflow: auto;
  background-color: #f5f7fa;
  padding: 0;
  margin: 0;
}
</style> 