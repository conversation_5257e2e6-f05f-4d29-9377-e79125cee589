package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.model.User;
import com.medical.annotation.model.Team;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.TeamRepository;
import com.medical.annotation.service.FileService;
import com.medical.annotation.service.ImageMetadataService;
import com.medical.annotation.util.FileNameGenerator;
import com.medical.annotation.util.DatabaseUtil;
import com.medical.annotation.util.TypeConverter;
import com.medical.annotation.util.SequentialIdGenerator;
import com.medical.annotation.util.ImagePathUtil;
import com.medical.annotation.util.BasePathConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.security.Principal;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.nio.file.Files;
import java.io.FileOutputStream;
import java.nio.file.Paths;
import java.util.Collections;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import javax.annotation.PostConstruct;

@RestController
@RequestMapping("/api")
public class FileUploadController {

    @Autowired
    private FileService fileService;
    
    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    @Autowired
    private ImagePairRepository imagePairRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TeamRepository teamRepository;
    
    @Autowired
    private SequentialIdGenerator sequentialIdGenerator;
    
    @Autowired
    private BasePathConfig basePathConfig;

    // 使用BasePathConfig中的路径，不再直接注入
    private String uploadDir;
    private String tempDir;

    @PostConstruct
    public void init() {
        uploadDir = basePathConfig.getUploadDir();
        tempDir = basePathConfig.getTempDir();
    }
    
    /**
     * 初始化文件目录
     */
    @GetMapping("/files/init")
    public ResponseEntity<?> initDirectories() {
        try {
            fileService.init();
            Map<String, String> response = new HashMap<>();
            response.put("originalDir", fileService.getOriginalDirectoryPath());
            response.put("tempDir", fileService.getTempDirectoryPath());
            response.put("processedDir", fileService.getProcessedDirectoryPath());
            response.put("uploadDir", fileService.getUploadDirectoryPath());
            System.out.println("初始化目录成功: " + fileService.getProcessedDirectoryPath());
            
            // 验证目录是否存在且可写
            File uploadDir = new File(fileService.getUploadDirectoryPath());
            File originalDir = new File(fileService.getOriginalDirectoryPath());
            File tempDir = new File(fileService.getTempDirectoryPath());
            File processedDir = new File(fileService.getProcessedDirectoryPath());
            
            response.put("uploadDirExists", String.valueOf(uploadDir.exists()));
            response.put("uploadDirCanWrite", String.valueOf(uploadDir.canWrite()));
            response.put("originalDirExists", String.valueOf(originalDir.exists()));
            response.put("originalDirCanWrite", String.valueOf(originalDir.canWrite()));
            response.put("tempDirExists", String.valueOf(tempDir.exists()));
            response.put("tempDirCanWrite", String.valueOf(tempDir.canWrite()));
            response.put("processedDirExists", String.valueOf(processedDir.exists()));
            response.put("processedDirCanWrite", String.valueOf(processedDir.canWrite()));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("初始化目录失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("无法初始化目录: " + e.getMessage());
        }
    }
    
    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file, 
                                     @RequestParam(value = "user_id", required = false) Integer userId,
                                     @RequestParam(value = "name", required = false) String imageName,
                                     @RequestParam(value = "description", required = false) String description,
                                     HttpServletRequest request) {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body("请选择文件");
            }
            
            // 记录文件详细信息到日志
            System.out.println("===== 文件上传详细信息: =====");
            System.out.println("原始文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize() + " 字节");
            System.out.println("内容类型: " + file.getContentType());
            
            // 获取当前用户ID - 优先使用请求参数，其次从请求头获取，最后使用默认值
            Integer currentUserId = userId;
            
            // 如果请求参数中没有用户ID，尝试从请求头获取
            if (currentUserId == null) {
                String userIdHeader = request.getHeader("X-User-Id");
                if (userIdHeader != null && !userIdHeader.isEmpty()) {
                    try {
                        currentUserId = Integer.parseInt(userIdHeader);
                        System.out.println("从请求头获取用户ID: " + currentUserId);
                    } catch (NumberFormatException e) {
                        System.out.println("无法解析请求头中的用户ID: " + userIdHeader);
                    }
                }
            }
            
            // 如果仍未获取到用户ID，尝试从认证信息获取
            if (currentUserId == null) {
                String authHeader = request.getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    // JWT令牌处理 - 如果您使用的是JWT认证
                    // 这里需要根据您的认证方式调整
                    System.out.println("检测到Bearer认证头");
                }
            }
            
            // 如果最终没有获取到有效的用户ID，使用默认值2（应该对应于系统中存在的用户）
            if (currentUserId == null) {
                currentUserId = 2; // 默认用户ID
                System.out.println("未找到用户ID，使用默认值: " + currentUserId);
            } else {
                System.out.println("使用用户ID: " + currentUserId);
            }
            
            // 确保目录初始化
            fileService.init();
            
            // 使用FileNameGenerator生成唯一文件名和原始文件名
            String originalFilename = file.getOriginalFilename();
            String uniqueFilename = FileNameGenerator.generateUniqueFileName(originalFilename);
            String uniqueOriginalName = FileNameGenerator.generateUniqueOriginalName(originalFilename);
            
            // 使用完整的时间戳作为唯一ID - 与前端保持一致
            long uniqueId = DatabaseUtil.generateUniqueLongId();
            System.out.println("生成的唯一ID (时间戳): " + uniqueId);
            
            // 记录生成的唯一文件名
            System.out.println("生成的唯一文件名: " + uniqueFilename);
            System.out.println("生成的唯一原始名: " + uniqueOriginalName);
            
            // 使用服务保存并处理图像 - 先保存原图，然后处理尺寸保存到temp目录
            Map<String, String> processedImageInfo = null;
            try {
                processedImageInfo = fileService.processAndSaveImage(file, uniqueFilename);
                System.out.println("图像处理信息: " + processedImageInfo);
            } catch (Exception e) {
                System.err.println("处理图像失败: " + e.getMessage());
                e.printStackTrace();
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("图像处理失败: " + e.getMessage());
            }
            
            if (processedImageInfo == null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("图像处理结果为空");
            }
            
            // 获取处理后的图片路径
            String processedWebPath = processedImageInfo.get("webPath");
            String processedPhysicalPath = processedImageInfo.get("physicalPath");
            String originalPath = processedImageInfo.get("originalPath");
            String originalWebPath = processedImageInfo.get("originalWebPath");
            
            // 检查物理文件是否存在
            File processedFile = new File(processedPhysicalPath);
            System.out.println("检查物理文件是否存在: " + processedPhysicalPath + " - " + processedFile.exists());
            
            if (!processedFile.exists() || processedFile.length() == 0) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("处理后的文件不存在或为空");
            }
            
            // 创建图像元数据记录
            ImageMetadata metadata = new ImageMetadata();
            metadata.setId(uniqueId); // 设置生成的唯一ID
            metadata.setFilename(uniqueFilename);
            metadata.setOriginalName(uniqueOriginalName);
            metadata.setMimetype(file.getContentType());
            metadata.setSize((int)Math.min(file.getSize(), Integer.MAX_VALUE));
            
            // 设置图像名称和描述（如果提供）
            if (imageName != null && !imageName.trim().isEmpty()) {
                metadata.setStudyDescription(imageName);
            }
            if (description != null && !description.trim().isEmpty()) {
                metadata.setStudyDescription(description);
            }
            
            // 获取图像宽高
            try {
                BufferedImage bufferedImage = ImageIO.read(processedFile);
                if (bufferedImage != null) {
                    metadata.setWidth(bufferedImage.getWidth());
                    metadata.setHeight(bufferedImage.getHeight());
                    System.out.println("图像尺寸: " + bufferedImage.getWidth() + "x" + bufferedImage.getHeight());
                }
            } catch (Exception e) {
                System.err.println("无法获取图像尺寸: " + e.getMessage());
            }
            
            // 设置上传用户 - 使用确定的当前用户ID
            Optional<User> userOpt = userRepository.findById(currentUserId);
            if (userOpt.isPresent()) {
                metadata.setUploadedBy(userOpt.get());
                System.out.println("设置上传用户: " + userOpt.get().getName() + " (ID: " + currentUserId + ")");
            } else {
                // 如果用户不存在，尝试使用默认用户(ID为1)
                System.err.println("找不到ID为 " + currentUserId + " 的用户，尝试使用默认用户");
                Optional<User> defaultUser = userRepository.findById(1);
                if (defaultUser.isPresent()) {
                    metadata.setUploadedBy(defaultUser.get());
                    System.out.println("使用默认用户: " + defaultUser.get().getName());
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("找不到有效用户，无法完成上传");
                }
            }
            
            // 设置初始状态为草稿
            metadata.setStatus(ImageMetadata.Status.DRAFT);
            
            // 保存元数据
            ImageMetadata savedMetadata = imageMetadataRepository.save(metadata);
            
            // 记录保存的元数据ID
            System.out.println("保存的图像元数据ID: " + savedMetadata.getId());
            
            // 创建ImagePair记录 - 关联原图和处理后的图片
            ImagePair imagePair = new ImagePair();
            imagePair.setMetadataId(savedMetadata.getId());  // 设置关联的元数据ID
            imagePair.setImageOnePath(processedWebPath);   // 保存Web访问路径，而非物理路径
            imagePair.setImageTwoPath(null);  // 标注后的图片路径初始为空
            imagePair.setDescription("处理后图像ID:" + savedMetadata.getId());
            
            // 使用当前用户ID作为创建者ID
            imagePair.setCreatedBy(currentUserId);
            imagePair.setCreatedAt(LocalDateTime.now());
            
            // 保存ImagePair
            ImagePair savedImagePair = imagePairRepository.save(imagePair);
            System.out.println("创建ImagePair记录成功，ID: " + savedImagePair.getId());
            System.out.println("保存的图像路径: " + processedWebPath);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("id", savedMetadata.getId());
            response.put("filename", savedMetadata.getFilename());
            response.put("original_name", savedMetadata.getOriginalName());
            response.put("path", processedWebPath);
            response.put("original_path", originalWebPath);
            response.put("physical_path", processedPhysicalPath);
            response.put("original_physical_path", originalPath);
            response.put("message", "文件上传成功");
            response.put("file_exists", processedFile.exists());
            response.put("file_size", processedFile.exists() ? processedFile.length() : 0);
            response.put("image_pair_id", savedImagePair.getId());
            response.put("user_id", currentUserId);
            
            // 添加详细的返回值日志
            System.out.println("====== 文件保存成功，返回详情 =======");
            System.out.println("完整ID(metadataId): " + savedMetadata.getId());
            System.out.println("简短ID(末尾9位): " + String.format("%09d", savedMetadata.getId() % 1000000000));
            System.out.println("文件路径: " + processedFile.getAbsolutePath());
            System.out.println("Web路径: " + processedWebPath);
            System.out.println("ImagePair ID: " + savedImagePair.getId());
            System.out.println("====================================");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取目录信息
     */
    @GetMapping("/files/directories")
    public ResponseEntity<?> getDirectoriesInfo() {
        try {
            Map<String, String> response = new HashMap<>();
            response.put("originalDir", fileService.getOriginalDirectoryPath());
            response.put("tempDir", fileService.getTempDirectoryPath());
            response.put("processedDir", fileService.getProcessedDirectoryPath());
            response.put("uploadDir", fileService.getUploadDirectoryPath());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("获取目录信息失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("无法获取目录信息: " + e.getMessage());
        }
    }
    
    /**
     * 删除标注图片
     * 通过图像对ID删除对应的标注图片文件
     */
    @PostMapping("/images/delete-annotated/{imagePairId}")
    public ResponseEntity<?> deleteAnnotatedImage(@PathVariable Long imagePairId) {
        try {
            // 查找图像对
            Optional<ImagePair> imagePairOpt = imagePairRepository.findById(imagePairId);
            if (!imagePairOpt.isPresent()) {
                return ResponseEntity.badRequest().body("找不到ID为" + imagePairId + "的图像对");
            }
            
            ImagePair imagePair = imagePairOpt.get();
            
            // 获取标注图片路径
            String annotatedImagePath = imagePair.getImageTwoPath();
            
            // 如果没有标注图片，直接返回成功
            if (annotatedImagePath == null || annotatedImagePath.isEmpty()) {
                return ResponseEntity.ok("没有标注图片需要删除");
            }
            
            // 删除文件
            File annotatedImageFile = new File(annotatedImagePath);
            boolean deleted = false;
            
            if (annotatedImageFile.exists()) {
                deleted = annotatedImageFile.delete();
            }
            
            // 清空路径
            imagePair.setImageTwoPath(null);
            imagePairRepository.save(imagePair);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("deleted", deleted);
            response.put("message", "标注图片路径已清空" + (deleted ? "并且文件已删除" : "，但文件删除失败或不存在"));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("删除标注图片失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("删除标注图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存文件到指定路径
     */
    @PostMapping("/api/files/save-to-path")
    public ResponseEntity<?> saveToPath(@RequestParam("file") MultipartFile file,
                                  @RequestParam(value = "targetPath", defaultValue = "F:/血管瘤辅助系统/medical_images/temp") String targetPath,
                                  @RequestParam(value = "userId", required = false) Integer userId,
                                  @RequestParam(value = "userCustomId", required = false) String userCustomId,
                                  @RequestParam(value = "teamId", required = false) Integer teamId) {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body("请选择文件");
            }
            
            // 记录文件详细信息到日志
            System.out.println("===== 保存文件到指定路径 =====");
            System.out.println("原始文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize() + " 字节");
            System.out.println("内容类型: " + file.getContentType());
            System.out.println("目标路径: " + targetPath);
            System.out.println("用户ID: " + userId);
            System.out.println("用户自定义ID: " + userCustomId);
            System.out.println("团队ID: " + teamId);
            
            // 添加: 检查最近几秒内是否有相同用户上传相同名称和大小的文件
            // 如果有，可能是重复请求，直接返回之前的记录
            LocalDateTime fiveSecondsAgo = LocalDateTime.now().minusSeconds(5);
            
            List<ImageMetadata> recentUploads = null;
            
            // 基于用户ID查找
            if (userId != null) {
                System.out.println("检查用户ID " + userId + " 在最近5秒内的上传记录");
                // 使用现有的findByUploadedBy方法并手动过滤时间
                User uploader = null;
                Optional<User> userOpt = userRepository.findById(userId);
                if (userOpt.isPresent()) {
                    uploader = userOpt.get();
                    recentUploads = imageMetadataRepository.findByUploadedBy(uploader);
                    // 手动过滤最近几秒的记录
                    if (recentUploads != null) {
                        recentUploads = recentUploads.stream()
                            .filter(img -> img.getCreatedAt() != null && img.getCreatedAt().isAfter(fiveSecondsAgo))
                            .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                            .collect(java.util.stream.Collectors.toList());
                    }
                }
            } 
            // 或者基于自定义ID查找
            else if (userCustomId != null && !userCustomId.isEmpty()) {
                System.out.println("检查用户自定义ID " + userCustomId + " 在最近5秒内的上传记录");
                // 查找用户
                Optional<User> userOpt = userRepository.findByCustomId(userCustomId);
                if (userOpt.isPresent()) {
                    User uploader = userOpt.get();
                    recentUploads = imageMetadataRepository.findByUploadedBy(uploader);
                    // 手动过滤最近几秒的记录
                    if (recentUploads != null) {
                        recentUploads = recentUploads.stream()
                            .filter(img -> img.getCreatedAt() != null && img.getCreatedAt().isAfter(fiveSecondsAgo))
                            .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                            .collect(java.util.stream.Collectors.toList());
                    }
                }
            }
            
            // 如果找到最近上传的记录，检查是否可能是重复上传
            if (recentUploads != null && !recentUploads.isEmpty()) {
                for (ImageMetadata recentImage : recentUploads) {
                    // 检查原始文件名和文件大小是否一致
                    if (file.getOriginalFilename() != null && 
                        file.getOriginalFilename().equals(recentImage.getOriginalName()) && 
                        recentImage.getSize() == (int) file.getSize()) {
                        
                        System.out.println("发现可能的重复上传: ID=" + recentImage.getId() + 
                                           ", 格式化ID=" + recentImage.getFormattedId() + 
                                           ", 创建时间=" + recentImage.getCreatedAt());
                        
                        // 检查是否有关联的ImagePair记录
                        List<ImagePair> existingPairs = imagePairRepository.findByMetadataId(recentImage.getId());
                        ImagePair imagePair = null;
                        
                        if (!existingPairs.isEmpty()) {
                            // 如果有多条记录，只保留最新的一条
                            if (existingPairs.size() > 1) {
                                System.out.println("警告：发现重复的ImagePair记录，共 " + existingPairs.size() + " 条，进行清理...");
                                
                                // 按创建时间降序排序，保留最新的一条
                                existingPairs.sort((a, b) -> {
                                    if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
                                    if (a.getCreatedAt() == null) return 1;
                                    if (b.getCreatedAt() == null) return -1;
                                    return b.getCreatedAt().compareTo(a.getCreatedAt());
                                });
                                
                                // 获取最新的记录
                                imagePair = existingPairs.get(0);
                                System.out.println("保留最新记录，ID: " + imagePair.getId() + ", 创建时间: " + imagePair.getCreatedAt());
                                
                                // 删除其他记录
                                for (int i = 1; i < existingPairs.size(); i++) {
                                    ImagePair pair = existingPairs.get(i);
                                    System.out.println("删除重复记录，ID: " + pair.getId() + ", 创建时间: " + pair.getCreatedAt());
                                    imagePairRepository.deleteById(pair.getId());
                                }
                            } else {
                                imagePair = existingPairs.get(0);
                            }
                            
                            // 返回现有记录信息
                            Map<String, Object> response = new HashMap<>();
                            response.put("success", true);
                            response.put("message", "发现重复上传，返回现有记录");
                            response.put("id", recentImage.getId());
                            response.put("metadataId", recentImage.getFormattedId());
                            response.put("formattedId", recentImage.getFormattedId());
                            response.put("fileName", recentImage.getFilename());
                            response.put("originalName", recentImage.getOriginalName());
                            response.put("webPath", recentImage.getPath());
                            response.put("filePath", getPhysicalPathFromWebPath(recentImage.getPath()));
                            response.put("fileSize", recentImage.getSize());
                            
                            if (imagePair != null) {
                                response.put("imagePairId", imagePair.getId());
                                response.put("imageOnePath", imagePair.getImageOnePath());
                                if (imagePair.getImageTwoPath() != null) {
                                    response.put("imageTwoPath", imagePair.getImageTwoPath());
                                }
                            }
                            
                            System.out.println("返回现有记录，避免重复创建: ID=" + recentImage.getId() + 
                                               ", 格式化ID=" + recentImage.getFormattedId());
                            return ResponseEntity.ok(response);
                        }
                    }
                }
            }
            
            // 修改：使用targetPath作为完整路径，无需再组合uploadDir
            String fullPath = targetPath;
            if (targetPath == null || targetPath.isEmpty()) {
                fullPath = tempDir;
            } else if (!targetPath.contains(":") && !targetPath.startsWith("/") && !targetPath.startsWith("\\")) {
                fullPath = uploadDir;
                if (!uploadDir.endsWith("/") && !uploadDir.endsWith("\\")) {
                    fullPath += "/";
                }
                fullPath += targetPath.replace("medical_images/", ""); // 避免路径重复
            }
            
            System.out.println("完整保存路径: " + fullPath);
            
            // 确保目标目录存在
            File targetDir = new File(fullPath);
            if (!targetDir.exists()) {
                boolean created = targetDir.mkdirs();
                System.out.println("创建目标目录: " + created + ", 路径: " + targetDir.getAbsolutePath());
            }
            
            // 生成基于UUID的唯一文件名
            String originalFileName = file.getOriginalFilename();
            String extension = "";
            if (originalFileName != null && originalFileName.contains(".")) {
                extension = originalFileName.substring(originalFileName.lastIndexOf("."));
            } else {
                extension = ".jpg"; // 默认扩展名
            }
            
            // 生成UUID格式的文件名
            String uuid = UUID.randomUUID().toString();
            String fileName = "temp_" + uuid + (originalFileName != null ? "_" + originalFileName : extension);
            
            // 保存文件
            File savedFile = new File(targetDir, fileName);
            file.transferTo(savedFile);
            
            // 检查文件是否保存成功
            if (!savedFile.exists() || savedFile.length() == 0) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("文件保存失败，无法在目标位置创建文件");
            }
            
            // 构建响应基础信息
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("filePath", savedFile.getAbsolutePath());
            response.put("fileName", fileName);
            response.put("originalName", file.getOriginalFilename());
            response.put("fileSize", savedFile.length());
            response.put("message", "文件成功保存到指定路径");
            
            // 创建数据库记录 - 确保异常处理
            try {
                // 1. 获取当前用户ID和用户对象
                User user = null;
                if (userId != null) {
                    Optional<User> userOptional = userRepository.findById(userId);
                    user = userOptional.orElse(null);
                    System.out.println("根据ID查找用户: " + userId + ", 结果: " + (user != null ? "找到" : "未找到"));
                } else if (userCustomId != null && !userCustomId.isEmpty()) {
                    Optional<User> userOptional = userRepository.findByCustomId(userCustomId);
                    user = userOptional.orElse(null);
                    System.out.println("根据自定义ID查找用户: " + userCustomId + ", 结果: " + (user != null ? "找到" : "未找到"));
                }
                
                // 查找团队
                Team team = null;
                if (teamId != null) {
                    Optional<Team> teamOptional = teamRepository.findById(teamId);
                    team = teamOptional.orElse(null);
                    System.out.println("根据ID查找团队: " + teamId + ", 结果: " + (team != null ? "找到" : "未找到"));
                }
                
                // 2. 准备图像元数据
                // 2.1 转换为Web访问路径
                String processedPhysicalPath = savedFile.getAbsolutePath();
                String webPath = "/medical/images/" + fullPath.replace(uploadDir + "/", "") + "/" + fileName;
                
                // 2.2 使用SequentialIdGenerator生成顺序ID
                String formattedId = sequentialIdGenerator.nextId();
                System.out.println("生成的顺序ID (格式化): " + formattedId);
                
                // 2.3 读取图像尺寸
                Integer imageWidth = null;
                Integer imageHeight = null;
                try {
                    BufferedImage image = ImageIO.read(savedFile);
                    if (image != null) {
                        imageWidth = image.getWidth();
                        imageHeight = image.getHeight();
                        System.out.println("图像尺寸: " + imageWidth + "x" + imageHeight);
                    }
                } catch (Exception e) {
                    System.err.println("无法读取图像尺寸: " + e.getMessage());
                }
                
                // 2.4 创建并保存元数据对象
                ImageMetadata metadata = new ImageMetadata();
                metadata.setFormattedId(formattedId);  // 设置9位格式化ID
                metadata.setFilename(fileName);
                metadata.setOriginalName(originalFileName);
                metadata.setPath(webPath);
                metadata.setMimetype(file.getContentType());
                metadata.setSize((int) file.getSize());
                metadata.setWidth(imageWidth);
                metadata.setHeight(imageHeight);
                
                // 如果找到用户，设置上传者
                if (user != null) {
                    metadata.setUploadedBy(user);
                    metadata.setUploadedByCustomId(user.getCustomId());
                    System.out.println("设置上传者: ID=" + user.getId() + ", 自定义ID=" + user.getCustomId() + ", 名称=" + user.getName());
                } else {
                    System.out.println("未找到用户，无法设置上传者");
                }
                
                // 如果找到团队，设置团队
                if (team != null) {
                    metadata.setTeam(team);
                    System.out.println("设置团队: ID=" + team.getId() + ", 名称=" + team.getName());
                }
                
                metadata.setStatus(ImageMetadata.Status.DRAFT);
                metadata.setCreatedAt(LocalDateTime.now());
                
                // 保存元数据到数据库
                try {
                    System.out.println("准备保存图像元数据: 文件名=" + fileName + ", 格式化ID=" + formattedId);
                    ImageMetadata savedMetadata = imageMetadataRepository.save(metadata);
                    if (savedMetadata != null && savedMetadata.getId() != null) {
                        System.out.println("图像元数据保存成功: ID=" + savedMetadata.getId() + ", 格式化ID=" + savedMetadata.getFormattedId());
                        
                        // 3. 创建ImagePair记录
                        ImagePair imagePair = new ImagePair();
                        imagePair.setMetadataId(savedMetadata.getId());
                        imagePair.setMetadata(savedMetadata);  // 设置关联的metadata对象
                        imagePair.setImageOnePath(webPath);  // 使用Web访问路径
                        imagePair.setImageTwoPath(null);
                        imagePair.setDescription("上传图像ID:" + savedMetadata.getId() + ", 格式化ID:" + formattedId);
                        
                        // 设置创建者ID和创建时间
                        if (user != null) {
                            imagePair.setCreatedBy(user.getId());
                        }
                        imagePair.setCreatedAt(LocalDateTime.now());
                        
                        // 保存ImagePair到数据库
                        try {
                            System.out.println("准备保存ImagePair: metadataId=" + savedMetadata.getId());
                            ImagePair savedPair = imagePairRepository.save(imagePair);
                            System.out.println("ImagePair保存成功: ID=" + savedPair.getId() + ", metadataId=" + savedPair.getMetadataId());
                            
                            // 4. 添加数据库记录ID到返回结果
                            response.put("id", savedMetadata.getId());  // 数据库自增ID
                            response.put("metadataId", formattedId);  // 返回格式化的9位ID作为主要ID
                            response.put("formattedId", formattedId);  // 格式化ID（冗余，保持向后兼容）
                            response.put("imagePairId", savedPair.getId());
                            response.put("webPath", webPath);
                            
                            // 添加详细的返回值日志
                            System.out.println("====== 文件保存成功，返回详情 =======");
                            System.out.println("ID: " + savedMetadata.getId() + " (格式化: " + formattedId + ")");
                            System.out.println("文件路径: " + savedFile.getAbsolutePath());
                            System.out.println("Web路径: " + webPath);
                            System.out.println("ImagePair ID: " + savedPair.getId());
                            System.out.println("====================================");
                        } catch (Exception e) {
                            System.err.println("保存ImagePair记录失败: " + e.getMessage());
                            e.printStackTrace();
                            // 虽然ImagePair保存失败，但仍返回成功状态和元数据ID
                            response.put("id", savedMetadata.getId());
                            response.put("metadataId", formattedId);
                            response.put("formattedId", formattedId);
                            response.put("warning", "图像对记录创建失败: " + e.getMessage());
                        }
                    } else {
                        System.err.println("保存元数据失败，返回值为null或ID为null");
                        response.put("warning", "元数据记录创建失败，返回值异常");
                    }
                } catch (Exception e) {
                    System.err.println("创建数据库记录失败: " + e.getMessage());
                    e.printStackTrace();
                    // 即使数据库记录创建失败，仍然返回文件保存成功的信息
                    response.put("warning", "文件已保存，但数据库记录创建失败: " + e.getMessage());
                }
            } catch (Exception e) {
                System.err.println("创建数据库记录失败: " + e.getMessage());
                e.printStackTrace();
                // 即使数据库记录创建失败，仍然返回文件保存成功的信息
                response.put("warning", "文件已保存，但数据库记录创建失败: " + e.getMessage());
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("保存文件到指定路径失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("保存文件失败: " + e.getMessage());
        }
    }

    /**
     * API映射：新增GET方法支持图像保存，解决前端调用问题
     */
    @GetMapping("/api/images/save-to-path")
    public ResponseEntity<?> saveToPathGet(@RequestParam(value = "targetPath", defaultValue = "F:/血管瘤辅助系统/medical_images/temp") String targetPath) {
        if (targetPath == null || targetPath.trim().isEmpty()) {
            targetPath = tempDir;
        }
        
        // 由于GET请求不能传输文件，返回方法不支持错误
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED)
            .body("GET请求不支持文件上传，请使用POST请求");
    }

    /**
     * 保存图像到指定路径 - 适配前端路径
     */
    @PostMapping("/images/save-to-path")
    public ResponseEntity<?> saveToPathAlternate(@RequestParam("file") MultipartFile file,
                                      @RequestParam(value = "targetPath", defaultValue = "") String targetPath,
                                      @RequestParam(value = "userId", required = false) Integer userId,
                                      @RequestParam(value = "userCustomId", required = false) String userCustomId,
                                      @RequestParam(value = "teamId", required = false) Integer teamId) {
        System.out.println("接收到图像保存请求 (新端点): " + (file != null ? file.getOriginalFilename() : "null"));
        System.out.println("目标路径: " + targetPath);
        System.out.println("用户ID: " + userId + ", 自定义ID: " + userCustomId + ", 团队ID: " + teamId);
        
        // 添加: 检查最近几秒内是否有相同用户上传相同名称和大小的文件
        // 如果有，可能是重复请求，直接返回之前的记录
        LocalDateTime fiveSecondsAgo = LocalDateTime.now().minusSeconds(5);
        
        List<ImageMetadata> recentUploads = null;
        
        // 基于用户ID查找
        if (userId != null) {
            System.out.println("检查用户ID " + userId + " 在最近5秒内的上传记录");
            // 使用现有的findByUploadedBy方法并手动过滤时间
            User uploader = null;
            Optional<User> userOpt = userRepository.findById(userId);
            if (userOpt.isPresent()) {
                uploader = userOpt.get();
                recentUploads = imageMetadataRepository.findByUploadedBy(uploader);
                // 手动过滤最近几秒的记录
                if (recentUploads != null) {
                    recentUploads = recentUploads.stream()
                        .filter(img -> img.getCreatedAt() != null && img.getCreatedAt().isAfter(fiveSecondsAgo))
                        .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                        .collect(java.util.stream.Collectors.toList());
                }
            }
        } 
        // 或者基于自定义ID查找
        else if (userCustomId != null && !userCustomId.isEmpty()) {
            System.out.println("检查用户自定义ID " + userCustomId + " 在最近5秒内的上传记录");
            // 查找用户
            Optional<User> userOpt = userRepository.findByCustomId(userCustomId);
            if (userOpt.isPresent()) {
                User uploader = userOpt.get();
                recentUploads = imageMetadataRepository.findByUploadedBy(uploader);
                // 手动过滤最近几秒的记录
                if (recentUploads != null) {
                    recentUploads = recentUploads.stream()
                        .filter(img -> img.getCreatedAt() != null && img.getCreatedAt().isAfter(fiveSecondsAgo))
                        .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                        .collect(java.util.stream.Collectors.toList());
                }
            }
        }
        
        // 如果找到最近上传的记录，检查是否可能是重复上传
        if (recentUploads != null && !recentUploads.isEmpty()) {
            for (ImageMetadata recentImage : recentUploads) {
                // 检查原始文件名和文件大小是否一致
                if (file.getOriginalFilename() != null && 
                    file.getOriginalFilename().equals(recentImage.getOriginalName()) && 
                    recentImage.getSize() == (int) file.getSize()) {
                    
                    System.out.println("发现可能的重复上传: ID=" + recentImage.getId() + 
                                       ", 格式化ID=" + recentImage.getFormattedId() + 
                                       ", 创建时间=" + recentImage.getCreatedAt());
                    
                    // 检查是否有关联的ImagePair记录
                    List<ImagePair> existingPairs = imagePairRepository.findByMetadataId(recentImage.getId());
                    ImagePair imagePair = null;
                    
                    if (!existingPairs.isEmpty()) {
                        // 如果有多条记录，只保留最新的一条
                        if (existingPairs.size() > 1) {
                            System.out.println("警告：发现重复的ImagePair记录，共 " + existingPairs.size() + " 条，进行清理...");
                            
                            // 按创建时间降序排序，保留最新的一条
                            existingPairs.sort((a, b) -> {
                                if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
                                if (a.getCreatedAt() == null) return 1;
                                if (b.getCreatedAt() == null) return -1;
                                return b.getCreatedAt().compareTo(a.getCreatedAt());
                            });
                            
                            // 获取最新的记录
                            imagePair = existingPairs.get(0);
                            System.out.println("保留最新记录，ID: " + imagePair.getId() + ", 创建时间: " + imagePair.getCreatedAt());
                            
                            // 删除其他记录
                            for (int i = 1; i < existingPairs.size(); i++) {
                                ImagePair pair = existingPairs.get(i);
                                System.out.println("删除重复记录，ID: " + pair.getId() + ", 创建时间: " + pair.getCreatedAt());
                                imagePairRepository.deleteById(pair.getId());
                            }
                        } else {
                            imagePair = existingPairs.get(0);
                        }
                        
                        // 返回现有记录信息
                        Map<String, Object> response = new HashMap<>();
                        response.put("success", true);
                        response.put("message", "发现重复上传，返回现有记录");
                        response.put("id", recentImage.getId());
                        response.put("metadataId", recentImage.getFormattedId());
                        response.put("formattedId", recentImage.getFormattedId());
                        response.put("fileName", recentImage.getFilename());
                        response.put("originalName", recentImage.getOriginalName());
                        response.put("webPath", recentImage.getPath());
                        response.put("filePath", getPhysicalPathFromWebPath(recentImage.getPath()));
                        response.put("fileSize", recentImage.getSize());
                        
                        if (imagePair != null) {
                            response.put("imagePairId", imagePair.getId());
                            response.put("imageOnePath", imagePair.getImageOnePath());
                            if (imagePair.getImageTwoPath() != null) {
                                response.put("imageTwoPath", imagePair.getImageTwoPath());
                            }
                        }
                        
                        System.out.println("返回现有记录，避免重复创建: ID=" + recentImage.getId() + 
                                           ", 格式化ID=" + recentImage.getFormattedId());
                        return ResponseEntity.ok(response);
                    }
                }
            }
        }
        
        return saveToPath(file, targetPath, userId, userCustomId, teamId);
    }
    
    /**
     * 将物理文件路径转换为Web访问路径
     */
    private String convertPhysicalPathToWebPath(String physicalPath) {
        if (physicalPath == null) return null;
        
        // 处理不同的路径格式
        if (physicalPath.contains("medical_images/temp") || 
            physicalPath.contains("medical_images\\temp")) {
            // 提取文件名
            String filename = physicalPath.substring(physicalPath.lastIndexOf(File.separator) + 1);
            return "/medical/images/temp/" + filename;
        } else if (physicalPath.contains("medical_images/processed") || 
                  physicalPath.contains("medical_images\\processed")) {
            String filename = physicalPath.substring(physicalPath.lastIndexOf(File.separator) + 1);
            return "/medical/images/processed/" + filename;
        }
        
        // 默认情况 - 保持原路径返回一个替代路径
        System.out.println("无法转换物理路径: " + physicalPath);
        return "/medical/images/temp/" + new File(physicalPath).getName();
    }
    
    /**
     * 为已处理的图片创建数据库记录
     * 解决已上传但未创建数据库记录的图片问题
     */
    @PostMapping("/images/register")
    public ResponseEntity<?> registerExistingImage(@RequestBody Map<String, Object> requestData) {
        try {
            // 1. 提取必要信息
            String processedWebPath = (String) requestData.get("image_path");
            String filename = (String) requestData.get("filename");
            Integer userId = convertToInteger(requestData.get("user_id"));
            
            if (processedWebPath == null || filename == null) {
                return ResponseEntity.badRequest().body("缺少必要参数: image_path, filename");
            }
            
            System.out.println("注册图像: path=" + processedWebPath + ", filename=" + filename);
            
            // 2. 创建物理路径
            String processedPhysicalPath = fileService.convertWebPathToFilePath(processedWebPath);
            File imageFile = new File(processedPhysicalPath);
            if (!imageFile.exists()) {
                System.err.println("图像文件不存在: " + processedPhysicalPath);
                return ResponseEntity.badRequest().body("图像文件不存在: " + processedPhysicalPath);
            }
            
            // 3. 创建图像元数据记录
            ImageMetadata metadata = new ImageMetadata();
            String uniqueFilename = filename;
            if (!uniqueFilename.contains("_")) {
                uniqueFilename = "registered_" + System.currentTimeMillis() + "_" + filename;
            }
            metadata.setFilename(uniqueFilename);
            metadata.setOriginalName(filename);
            metadata.setPath(processedWebPath);
            metadata.setMimetype("image/jpeg"); // 默认MIME类型
            
            // 设置尺寸信息
            try {
                BufferedImage image = ImageIO.read(imageFile);
                metadata.setWidth(image.getWidth());
                metadata.setHeight(image.getHeight());
                metadata.setSize((int)imageFile.length());
                System.out.println("图像尺寸: " + image.getWidth() + "x" + image.getHeight());
            } catch (Exception e) {
                System.err.println("无法读取图像尺寸: " + e.getMessage());
            }
            
            // 4. 设置用户信息
            if (userId != null) {
                Optional<User> user = userRepository.findById(userId);
                if (user.isPresent()) {
                    metadata.setUploadedBy(user.get());
                    System.out.println("设置上传用户: " + user.get().getName());
                } else {
                    Optional<User> defaultUser = userRepository.findById(1);
                    if (defaultUser.isPresent()) {
                        metadata.setUploadedBy(defaultUser.get());
                        System.out.println("使用默认用户: " + defaultUser.get().getName());
                    }
                }
            } else {
                Optional<User> defaultUser = userRepository.findById(1);
                if (defaultUser.isPresent()) {
                    metadata.setUploadedBy(defaultUser.get());
                    System.out.println("使用默认用户: " + defaultUser.get().getName());
                }
            }
            
            // 设置状态为草稿
            metadata.setStatus(ImageMetadata.Status.DRAFT);
            
            // 生成9位随机ID
            Long uniqueId = DatabaseUtil.generateRandom9DigitId();
            String formattedId = DatabaseUtil.formatIdToNineDigits(uniqueId);
            System.out.println("注册已存在图像 - 生成的唯一随机ID: " + uniqueId + " (格式化: " + formattedId + ")");
            
            // 设置ID
            metadata.setId(uniqueId);
            
            // 5. 保存元数据
            ImageMetadata savedMetadata = imageMetadataRepository.save(metadata);
            System.out.println("已创建图像元数据记录: ID=" + savedMetadata.getId());
            
            // 6. 创建ImagePair记录
            ImagePair imagePair = new ImagePair();
            imagePair.setMetadataId(savedMetadata.getId());
            imagePair.setImageOnePath(processedWebPath);
            imagePair.setImageTwoPath(null);
            imagePair.setDescription("手动注册图像ID:" + savedMetadata.getId());
            imagePair.setCreatedBy(userId != null ? userId : 1);
            imagePair.setCreatedAt(LocalDateTime.now());
            
            // 7. 保存ImagePair
            ImagePair savedImagePair = imagePairRepository.save(imagePair);
            System.out.println("已创建ImagePair记录: ID=" + savedImagePair.getId());
            
            // 8. 返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("id", savedMetadata.getId());
            response.put("path", savedMetadata.getPath());
            response.put("message", "图像已成功注册到数据库");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("图像注册失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("图像注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 为指定图片创建正确的数据库记录(针对血管瘤标注系统)
     * 这将创建image_metadata和image_pairs表中的记录
     */
    @PostMapping("/images/register-medical")
    public ResponseEntity<?> registerMedicalImage(@RequestBody Map<String, Object> requestData) {
        try {
            // 1. 提取必要信息
            String imageFileName = (String) requestData.get("filename");
            Integer userId = convertToInteger(requestData.get("user_id"));
            
            if (imageFileName == null || imageFileName.isEmpty()) {
                return ResponseEntity.badRequest().body("缺少必要参数: filename");
            }
            
            System.out.println("注册医学图像: " + imageFileName);
            
            // 2. 确定Web访问路径和物理路径
            String processedWebPath = "/medical/images/temp/temp_" + imageFileName;
            String processedPhysicalPath = tempDir + File.separator + "temp_" + imageFileName;
            
            // 检查物理文件是否存在
            File imageFile = new File(processedPhysicalPath);
            if (!imageFile.exists()) {
                // 尝试在不同目录查找
                String[] directories = {
                    tempDir,
                    fileService.getOriginalDirectoryPath(),
                    fileService.getProcessedDirectoryPath(),
                    fileService.getAnnotateDirectoryPath(),
                    fileService.getUploadDirectoryPath()
                };
                
                boolean found = false;
                for (String dir : directories) {
                    // 尝试带temp_前缀和不带前缀两种情况
                    File fileWithPrefix = new File(dir, "temp_" + imageFileName);
                    File fileWithoutPrefix = new File(dir, imageFileName);
                    
                    if (fileWithPrefix.exists()) {
                        imageFile = fileWithPrefix;
                        processedPhysicalPath = fileWithPrefix.getAbsolutePath();
                        processedWebPath = "/medical/images/" + (dir.contains("temp") ? "temp" : 
                                            dir.contains("processed") ? "processed" : 
                                            dir.contains("annotate") ? "annotate" : 
                                            dir.contains("original") ? "original" : "") + 
                                            "/temp_" + imageFileName;
                        found = true;
                        break;
                    }
                    
                    if (fileWithoutPrefix.exists()) {
                        imageFile = fileWithoutPrefix;
                        processedPhysicalPath = fileWithoutPrefix.getAbsolutePath();
                        processedWebPath = "/medical/images/" + (dir.contains("temp") ? "temp" : 
                                            dir.contains("processed") ? "processed" : 
                                            dir.contains("annotate") ? "annotate" : 
                                            dir.contains("original") ? "original" : "") + 
                                            "/" + imageFileName;
                        found = true;
                        break;
                    }
                }
                
                if (!found) {
                    return ResponseEntity.badRequest().body("图像文件不存在，请检查文件名是否正确: " + imageFileName);
                }
            }
            
            System.out.println("找到图像文件: " + processedPhysicalPath);
            System.out.println("Web访问路径: " + processedWebPath);
            
            // 3. 创建图像元数据记录
            ImageMetadata metadata = new ImageMetadata();
            String uniqueFilename = "medical_" + System.currentTimeMillis() + "_" + imageFileName;
            metadata.setFilename(uniqueFilename);
            metadata.setOriginalName(imageFileName);
            metadata.setPath(processedWebPath); // 使用Web访问路径
            metadata.setMimetype("image/jpeg"); // 默认MIME类型，可根据文件扩展名确定
            
            // 设置尺寸信息
            try {
                BufferedImage image = ImageIO.read(imageFile);
                metadata.setWidth(image.getWidth());
                metadata.setHeight(image.getHeight());
                metadata.setSize((int)imageFile.length());
                System.out.println("图像尺寸: " + image.getWidth() + "x" + image.getHeight());
            } catch (Exception e) {
                System.err.println("无法读取图像尺寸: " + e.getMessage());
            }
            
            // 4. 设置用户信息
            if (userId != null) {
                Optional<User> user = userRepository.findById(userId);
                if (user.isPresent()) {
                    metadata.setUploadedBy(user.get());
                    System.out.println("设置上传用户: " + user.get().getName());
                } else {
                    Optional<User> defaultUser = userRepository.findById(1);
                    if (defaultUser.isPresent()) {
                        metadata.setUploadedBy(defaultUser.get());
                        System.out.println("使用默认用户: " + defaultUser.get().getName());
                    }
                }
            } else {
                Optional<User> defaultUser = userRepository.findById(1);
                if (defaultUser.isPresent()) {
                    metadata.setUploadedBy(defaultUser.get());
                    System.out.println("使用默认用户: " + defaultUser.get().getName());
                }
            }
            
            // 设置状态为草稿
            metadata.setStatus(ImageMetadata.Status.DRAFT);
            
            // 生成9位随机ID
            Long uniqueId = DatabaseUtil.generateRandom9DigitId();
            String formattedId = DatabaseUtil.formatIdToNineDigits(uniqueId);
            System.out.println("注册医学图像 - 生成的唯一随机ID: " + uniqueId + " (格式化: " + formattedId + ")");
            
            // 设置ID
            metadata.setId(uniqueId);
            
            // 5. 保存元数据
            ImageMetadata savedMetadata = imageMetadataRepository.save(metadata);
            System.out.println("已创建图像元数据记录: ID=" + savedMetadata.getId());
            
            // 6. 创建ImagePair记录
            ImagePair imagePair = new ImagePair();
            imagePair.setMetadataId(savedMetadata.getId());
            imagePair.setImageOnePath(processedWebPath);
            imagePair.setImageTwoPath(null);
            imagePair.setDescription("手动注册医学图像ID:" + savedMetadata.getId());
            imagePair.setCreatedBy(userId != null ? userId : 1);
            imagePair.setCreatedAt(LocalDateTime.now());
            
            // 7. 保存ImagePair
            ImagePair savedImagePair = imagePairRepository.save(imagePair);
            System.out.println("已创建ImagePair记录: ID=" + savedImagePair.getId());
            
            // 8. 返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("id", savedMetadata.getId());
            response.put("path", savedMetadata.getPath());
            response.put("image_pair_id", savedImagePair.getId());
            response.put("message", "血管瘤图像已成功注册到数据库");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("图像注册失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("图像注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 工具方法：转换为Integer
     */
    private Integer convertToInteger(Object value) {
        if (value == null) {
            return null;
        }
        
        System.out.println("正在转换值为Integer: " + value + ", 类型: " + value.getClass().getName());
        
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof String) {
            try {
                // 尝试直接转换
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                // 如果无法直接转换，使用默认ID
                System.out.println("无法解析String为Integer: " + value + ", 使用默认ID 1");
                return 1;
            }
        }
        if (value instanceof Long) {
            Long longValue = (Long) value;
            // 使用固定ID，避免溢出问题
            return longValue.intValue();
        }
        if (value instanceof Double) {
            return ((Double) value).intValue();
        }
        
        // 如果无法转换，使用默认ID
        System.out.println("无法转换为Integer: " + value + ", 使用默认ID 1");
        return 1;
    }

    // 添加: 从Web路径转换回物理路径
    private String getPhysicalPathFromWebPath(String webPath) {
        if (webPath == null || webPath.isEmpty()) {
            return null;
        }
        
        // 移除可能的URL前缀
        String path = webPath;
        if (path.startsWith("http://") || path.startsWith("https://")) {
            // 尝试提取路径部分
            try {
                java.net.URL url = new java.net.URL(path);
                path = url.getPath();
            } catch (Exception e) {
                System.err.println("解析URL失败: " + e.getMessage());
                // 继续使用原始路径
            }
        }
        
        // 替换Web路径中的/medical为物理路径
        if (path.startsWith("/medical/")) {
            path = path.replace("/medical/", "");
            return uploadDir + "/" + path;
        }
        
        // 如果路径不包含/medical/，尝试直接使用uploadDir作为根目录
        if (!path.startsWith("/")) {
            return uploadDir + "/" + path;
        }
        
        // 如果以/开头但不是/medical/，去掉前导/再拼接
        return uploadDir + path;
    }

    /**
     * 添加的新方法，处理 /api/images/upload 路径
     * 前端实际使用的API路径
     */
    @PostMapping("/images/upload")
    public ResponseEntity<?> uploadImage(@RequestParam("file") MultipartFile file, 
                                      @RequestParam(value = "name", required = false) String imageName,
                                      @RequestParam(value = "description", required = false) String description,
                                      @RequestParam(value = "user_id", required = false) Integer userId,
                                      HttpServletRequest request) {
        // 调用现有的上传方法处理请求
        return uploadFile(file, userId, imageName, description, request);
    }

    /**
     * 直接处理/api/files/upload路径而非依赖转发
     * 这样可以避免双重路径问题
     */
    @PostMapping("/files/upload")
    public ResponseEntity<?> uploadFileDirectly(@RequestParam("file") MultipartFile file,
                                             @RequestParam(value = "originalFilename", required = false) String originalFilename,
                                             @RequestParam(value = "targetPath", required = false) String targetPath,
                                             @RequestParam(value = "userId", required = false) Integer userId,
                                             HttpServletRequest request) {
        System.out.println("===== 文件上传请求 =====");
        System.out.println("请求URI: " + request.getRequestURI());
        System.out.println("请求URL: " + request.getRequestURL());
        System.out.println("原始文件名: " + file.getOriginalFilename());
        System.out.println("提供的文件名: " + originalFilename);
        System.out.println("目标路径: " + targetPath);
        System.out.println("提供的用户ID: " + userId);
        
        try {
            if (file.isEmpty()) {
                System.err.println("上传失败: 文件为空");
                return ResponseEntity.badRequest().body("请选择文件");
            }
            
            // 获取当前用户ID - 优先使用请求参数中的userId
            Integer currentUserId = userId != null ? userId : getCurrentUserId(request);
            System.out.println("使用的用户ID: " + currentUserId);
            
            // 处理用户提供的目标路径 - 使用相对路径
            String actualTargetPath = targetPath;
            if (actualTargetPath == null || actualTargetPath.isEmpty()) {
                actualTargetPath = "temp"; // 默认使用temp子目录
            }
            
            // 构建完整的物理路径
            String fullPath = uploadDir + File.separator + actualTargetPath;
            System.out.println("构建的完整物理路径: " + fullPath);
            
            // 确保目录存在
            File targetDir = new File(fullPath);
            if (!targetDir.exists()) {
                boolean created = targetDir.mkdirs();
                System.out.println("创建目录 " + fullPath + ": " + (created ? "成功" : "失败"));
            }
            
            // 确定文件名
            String finalFilename = originalFilename != null ? originalFilename : file.getOriginalFilename();
            String uniqueFilename = FileNameGenerator.generateUniqueFileName(finalFilename);
            System.out.println("生成的唯一文件名: " + uniqueFilename);
            
            // 文件保存路径
            File destFile = new File(targetDir, uniqueFilename);
            
            // 保存文件
            file.transferTo(destFile);
            System.out.println("文件已保存到: " + destFile.getAbsolutePath());
            
            // 构建Web访问路径 - 使用相对路径
            String webPath = "/medical/images/" + actualTargetPath + "/" + uniqueFilename;
            System.out.println("构建的Web访问路径: " + webPath);
            
            // 创建数据库记录
            System.out.println("开始创建数据库记录...");
            
            // 1. 创建ImageMetadata记录
            ImageMetadata metadata = new ImageMetadata();
            metadata.setFilename(uniqueFilename);
            metadata.setOriginalName(finalFilename);
            metadata.setPath(webPath);
            metadata.setMimetype(file.getContentType());
            metadata.setSize((int)file.getSize());
            
            // 生成9位随机ID
            Long uniqueId = DatabaseUtil.generateRandom9DigitId();
            String formattedId = DatabaseUtil.formatIdToNineDigits(uniqueId);
            System.out.println("生成的ID: " + uniqueId + ", 格式化ID: " + formattedId);
            
            metadata.setId(uniqueId);
            metadata.setFormattedId(formattedId);
            
            // 设置上传用户
            Optional<User> userOpt = userRepository.findById(currentUserId);
            if (userOpt.isPresent()) {
                metadata.setUploadedBy(userOpt.get());
                System.out.println("设置上传用户: " + userOpt.get().getName());
            }
            
            // 设置状态为草稿
            metadata.setStatus(ImageMetadata.Status.DRAFT);
            metadata.setCreatedAt(LocalDateTime.now());
            
            // 保存元数据
            ImageMetadata savedMetadata = imageMetadataRepository.save(metadata);
            System.out.println("ImageMetadata记录已保存, ID: " + savedMetadata.getId());
            
            // 2. 创建ImagePair记录
            ImagePair imagePair = new ImagePair();
            imagePair.setMetadataId(savedMetadata.getId());
            imagePair.setMetadata(savedMetadata);
            imagePair.setImageOnePath(webPath);
            imagePair.setDescription("上传的图像");
            imagePair.setCreatedBy(currentUserId);
            imagePair.setCreatedAt(LocalDateTime.now());
            
            ImagePair savedPair = imagePairRepository.save(imagePair);
            System.out.println("ImagePair记录已保存, ID: " + savedPair.getId());
            
            // 构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "文件上传成功");
            result.put("filename", uniqueFilename);
            result.put("original_name", finalFilename);
            result.put("path", webPath);
            result.put("physical_path", destFile.getAbsolutePath());
            result.put("size", file.getSize());
            result.put("id", savedMetadata.getId());
            result.put("metadataId", savedMetadata.getFormattedId());
            result.put("imagePairId", savedPair.getId());
            
            System.out.println("文件上传处理完成，返回结果");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            System.err.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "文件上传失败: " + e.getMessage());
            result.put("error", e.getClass().getName());
            result.put("stackTrace", e.getStackTrace()[0].toString());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 添加API前缀的文件上传接口 - 处理/medical/api/files/upload路径
     */
    @PostMapping("/api/files/upload")
    public ResponseEntity<?> uploadFileDirectApi(@RequestParam("file") MultipartFile file,
                                             @RequestParam(value = "originalFilename", required = false) String originalFilename,
                                             @RequestParam(value = "targetPath", required = false) String targetPath,
                                             @RequestParam(value = "userId", required = false) Integer userId,
                                             HttpServletRequest request) {
        System.out.println("收到API前缀的文件上传请求: " + request.getRequestURI());
        return uploadFileDirectly(file, originalFilename, targetPath, userId, request);
    }

    /**
     * 添加辅助方法：从请求中获取当前用户ID
     */
    private Integer getCurrentUserId(HttpServletRequest request) {
        // 尝试从请求头获取用户ID
        Integer userId = null;
        String userIdHeader = request.getHeader("X-User-Id");
        if (userIdHeader != null && !userIdHeader.isEmpty()) {
            try {
                userId = Integer.parseInt(userIdHeader);
            } catch (NumberFormatException e) {
                System.out.println("无法解析请求头中的用户ID: " + userIdHeader);
            }
        }
        
        // 如果仍未获取到用户ID，使用默认值
        if (userId == null) {
            userId = 2; // 默认用户ID
        }
        
        return userId;
    }
} 