"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[280],{1240:(e,t,a)=>{var r=a(9504);e.exports=r(1..valueOf)},1278:(e,t,a)=>{var r=a(6518),o=a(3724),n=a(5031),l=a(5397),u=a(7347),i=a(4659);r({target:"Object",stat:!0,sham:!o},{getOwnPropertyDescriptors:function(e){var t,a,r=l(e),o=u.f,c=n(r),f={},s=0;while(c.length>s)a=o(r,t=c[s++]),void 0!==a&&i(f,t,a);return f}})},3280:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h});var r=a(641),o={class:"structured-form-container"},n={key:0,class:"text-center my-5"},l={key:1,class:"form-content"},u={class:"form-section"},i={class:"form-grid"},c={class:"form-section"},f={class:"form-grid"},s={class:"form-section"},d={class:"form-grid"},m={class:"form-section"},b={class:"form-grid"},p={class:"form-actions"};function g(e,t,a,g,_,v){var F=(0,r.g2)("el-cascader"),k=(0,r.g2)("el-form-item"),h=(0,r.g2)("el-input-number"),D=(0,r.g2)("el-radio"),V=(0,r.g2)("el-radio-group"),y=(0,r.g2)("el-checkbox"),w=(0,r.g2)("el-checkbox-group"),P=(0,r.g2)("el-input"),U=(0,r.g2)("el-option"),W=(0,r.g2)("el-select"),O=(0,r.g2)("el-rate"),E=(0,r.g2)("el-button"),x=(0,r.g2)("el-form");return(0,r.uX)(),(0,r.CE)("div",o,[t[58]||(t[58]=(0,r.Lk)("div",{class:"page-header"},[(0,r.Lk)("h2",null,"病例信息填写")],-1)),_.loading?((0,r.uX)(),(0,r.CE)("div",n,t[21]||(t[21]=[(0,r.Lk)("div",{class:"spinner-border",role:"status"},[(0,r.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):((0,r.uX)(),(0,r.CE)("div",l,[(0,r.bF)(x,{ref:"caseForm",model:_.formData,"label-position":"top",rules:_.rules},{default:(0,r.k6)((function(){return[(0,r.Lk)("div",u,[t[25]||(t[25]=(0,r.Lk)("h3",{class:"section-title"},"基础信息",-1)),(0,r.Lk)("div",i,[(0,r.bF)(k,{label:"病变部位",prop:"location"},{default:(0,r.k6)((function(){return[(0,r.bF)(F,{modelValue:_.formData.location,"onUpdate:modelValue":t[0]||(t[0]=function(e){return _.formData.location=e}),options:_.locationOptions,props:{expandTrigger:"hover"},placeholder:"请选择病变部位"},null,8,["modelValue","options"])]})),_:1}),(0,r.bF)(k,{label:"患者年龄",prop:"patientAge"},{default:(0,r.k6)((function(){return[(0,r.bF)(h,{modelValue:_.formData.patientAge,"onUpdate:modelValue":t[1]||(t[1]=function(e){return _.formData.patientAge=e}),min:0,max:100},null,8,["modelValue"])]})),_:1}),(0,r.bF)(k,{label:"病程阶段",prop:"stage"},{default:(0,r.k6)((function(){return[(0,r.bF)(V,{modelValue:_.formData.stage,"onUpdate:modelValue":t[2]||(t[2]=function(e){return _.formData.stage=e})},{default:(0,r.k6)((function(){return[(0,r.bF)(D,{label:"初期"},{default:(0,r.k6)((function(){return t[22]||(t[22]=[(0,r.eW)("初期（<6个月）")])})),_:1,__:[22]}),(0,r.bF)(D,{label:"进展期"},{default:(0,r.k6)((function(){return t[23]||(t[23]=[(0,r.eW)("进展期")])})),_:1,__:[23]}),(0,r.bF)(D,{label:"稳定期"},{default:(0,r.k6)((function(){return t[24]||(t[24]=[(0,r.eW)("稳定期")])})),_:1,__:[24]})]})),_:1},8,["modelValue"])]})),_:1})])]),(0,r.Lk)("div",c,[t[38]||(t[38]=(0,r.Lk)("h3",{class:"section-title"},"形态与临床特征",-1)),(0,r.Lk)("div",f,[(0,r.bF)(k,{label:"形态特征",prop:"morphology"},{default:(0,r.k6)((function(){return[(0,r.bF)(w,{modelValue:_.formData.morphology,"onUpdate:modelValue":t[3]||(t[3]=function(e){return _.formData.morphology=e})},{default:(0,r.k6)((function(){return[(0,r.bF)(y,{label:"结节状"},{default:(0,r.k6)((function(){return t[26]||(t[26]=[(0,r.eW)("结节状")])})),_:1,__:[26]}),(0,r.bF)(y,{label:"团块状"},{default:(0,r.k6)((function(){return t[27]||(t[27]=[(0,r.eW)("团块状")])})),_:1,__:[27]}),(0,r.bF)(y,{label:"弥漫性"},{default:(0,r.k6)((function(){return t[28]||(t[28]=[(0,r.eW)("弥漫性")])})),_:1,__:[28]}),(0,r.bF)(y,{label:"溃疡形成"},{default:(0,r.k6)((function(){return t[29]||(t[29]=[(0,r.eW)("溃疡形成")])})),_:1,__:[29]})]})),_:1},8,["modelValue"]),(0,r.bF)(P,{modelValue:_.formData.morphologyDesc,"onUpdate:modelValue":t[4]||(t[4]=function(e){return _.formData.morphologyDesc=e}),type:"textarea",placeholder:"补充形态描述",rows:2,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1}),(0,r.bF)(k,{label:"血流信号",prop:"bloodFlow"},{default:(0,r.k6)((function(){return[(0,r.bF)(W,{modelValue:_.formData.bloodFlow,"onUpdate:modelValue":t[5]||(t[5]=function(e){return _.formData.bloodFlow=e}),placeholder:"请选择血流信号强度"},{default:(0,r.k6)((function(){return[(0,r.bF)(U,{label:"丰富",value:"丰富"}),(0,r.bF)(U,{label:"中等",value:"中等"}),(0,r.bF)(U,{label:"稀疏",value:"稀疏"}),(0,r.bF)(U,{label:"无",value:"无"})]})),_:1},8,["modelValue"])]})),_:1}),(0,r.bF)(k,{label:"症状表现",prop:"symptoms"},{default:(0,r.k6)((function(){return[(0,r.bF)(w,{modelValue:_.formData.symptoms,"onUpdate:modelValue":t[6]||(t[6]=function(e){return _.formData.symptoms=e})},{default:(0,r.k6)((function(){return[(0,r.bF)(y,{label:"疼痛"},{default:(0,r.k6)((function(){return t[30]||(t[30]=[(0,r.eW)("疼痛")])})),_:1,__:[30]}),(0,r.bF)(y,{label:"瘙痒"},{default:(0,r.k6)((function(){return t[31]||(t[31]=[(0,r.eW)("瘙痒")])})),_:1,__:[31]}),(0,r.bF)(y,{label:"出血"},{default:(0,r.k6)((function(){return t[32]||(t[32]=[(0,r.eW)("出血")])})),_:1,__:[32]}),(0,r.bF)(y,{label:"功能障碍"},{default:(0,r.k6)((function(){return t[33]||(t[33]=[(0,r.eW)("功能障碍")])})),_:1,__:[33]})]})),_:1},8,["modelValue"]),(0,r.bF)(P,{modelValue:_.formData.symptomsDesc,"onUpdate:modelValue":t[7]||(t[7]=function(e){return _.formData.symptomsDesc=e}),type:"textarea",placeholder:"症状详细描述",rows:2,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1}),(0,r.bF)(k,{label:"并发症",prop:"complications"},{default:(0,r.k6)((function(){return[(0,r.bF)(w,{modelValue:_.formData.complications,"onUpdate:modelValue":t[8]||(t[8]=function(e){return _.formData.complications=e})},{default:(0,r.k6)((function(){return[(0,r.bF)(y,{label:"感染"},{default:(0,r.k6)((function(){return t[34]||(t[34]=[(0,r.eW)("感染")])})),_:1,__:[34]}),(0,r.bF)(y,{label:"血栓"},{default:(0,r.k6)((function(){return t[35]||(t[35]=[(0,r.eW)("血栓")])})),_:1,__:[35]}),(0,r.bF)(y,{label:"压迫邻近器官"},{default:(0,r.k6)((function(){return t[36]||(t[36]=[(0,r.eW)("压迫邻近器官")])})),_:1,__:[36]}),(0,r.bF)(y,{label:"其他"},{default:(0,r.k6)((function(){return t[37]||(t[37]=[(0,r.eW)("其他")])})),_:1,__:[37]})]})),_:1},8,["modelValue"]),(0,r.bF)(P,{modelValue:_.formData.complicationsDesc,"onUpdate:modelValue":t[9]||(t[9]=function(e){return _.formData.complicationsDesc=e}),type:"textarea",placeholder:"并发症详细描述",rows:2,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1})])]),(0,r.Lk)("div",s,[t[46]||(t[46]=(0,r.Lk)("h3",{class:"section-title"},"诊断与治疗建议",-1)),(0,r.Lk)("div",d,[(0,r.bF)(k,{label:"诊断结论",prop:"diagnosis"},{default:(0,r.k6)((function(){return[(0,r.bF)(P,{modelValue:_.formData.diagnosis,"onUpdate:modelValue":t[10]||(t[10]=function(e){return _.formData.diagnosis=e}),placeholder:"请输入诊断结论"},null,8,["modelValue"]),(0,r.bF)(P,{modelValue:_.formData.diagnosisCode,"onUpdate:modelValue":t[11]||(t[11]=function(e){return _.formData.diagnosisCode=e}),placeholder:"ICD-11编码",style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1}),(0,r.bF)(k,{label:"治疗优先级",prop:"treatmentPriority"},{default:(0,r.k6)((function(){return[(0,r.bF)(V,{modelValue:_.formData.treatmentPriority,"onUpdate:modelValue":t[12]||(t[12]=function(e){return _.formData.treatmentPriority=e})},{default:(0,r.k6)((function(){return[(0,r.bF)(D,{label:"紧急"},{default:(0,r.k6)((function(){return t[39]||(t[39]=[(0,r.eW)("紧急（24h内处理）")])})),_:1,__:[39]}),(0,r.bF)(D,{label:"常规"},{default:(0,r.k6)((function(){return t[40]||(t[40]=[(0,r.eW)("常规（1-2周）")])})),_:1,__:[40]}),(0,r.bF)(D,{label:"观察"},{default:(0,r.k6)((function(){return t[41]||(t[41]=[(0,r.eW)("观察")])})),_:1,__:[41]})]})),_:1},8,["modelValue"])]})),_:1}),(0,r.bF)(k,{label:"推荐方案",prop:"treatmentPlan"},{default:(0,r.k6)((function(){return[(0,r.bF)(w,{modelValue:_.formData.treatmentPlan,"onUpdate:modelValue":t[13]||(t[13]=function(e){return _.formData.treatmentPlan=e})},{default:(0,r.k6)((function(){return[(0,r.bF)(y,{label:"手术切除"},{default:(0,r.k6)((function(){return t[42]||(t[42]=[(0,r.eW)("手术切除")])})),_:1,__:[42]}),(0,r.bF)(y,{label:"激光治疗"},{default:(0,r.k6)((function(){return t[43]||(t[43]=[(0,r.eW)("激光治疗")])})),_:1,__:[43]}),(0,r.bF)(y,{label:"硬化剂注射"},{default:(0,r.k6)((function(){return t[44]||(t[44]=[(0,r.eW)("硬化剂注射")])})),_:1,__:[44]}),(0,r.bF)(y,{label:"药物治疗"},{default:(0,r.k6)((function(){return t[45]||(t[45]=[(0,r.eW)("药物治疗")])})),_:1,__:[45]})]})),_:1},8,["modelValue"]),(0,r.bF)(P,{modelValue:_.formData.treatmentDetail,"onUpdate:modelValue":t[14]||(t[14]=function(e){return _.formData.treatmentDetail=e}),type:"textarea",placeholder:"请输入详细治疗方案，包括剂量、注意事项等",rows:3,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1}),(0,r.bF)(k,{label:"禁忌症提示",prop:"contraindications"},{default:(0,r.k6)((function(){return[(0,r.bF)(P,{modelValue:_.formData.contraindications,"onUpdate:modelValue":t[15]||(t[15]=function(e){return _.formData.contraindications=e}),type:"textarea",placeholder:"请输入禁忌症提示",rows:3},null,8,["modelValue"])]})),_:1})])]),(0,r.Lk)("div",m,[t[56]||(t[56]=(0,r.Lk)("h3",{class:"section-title"},"随访与预后",-1)),(0,r.Lk)("div",b,[(0,r.bF)(k,{label:"复查周期",prop:"followUpPeriod"},{default:(0,r.k6)((function(){return[(0,r.bF)(V,{modelValue:_.formData.followUpPeriod,"onUpdate:modelValue":t[16]||(t[16]=function(e){return _.formData.followUpPeriod=e})},{default:(0,r.k6)((function(){return[(0,r.bF)(D,{label:"1个月"},{default:(0,r.k6)((function(){return t[47]||(t[47]=[(0,r.eW)("1个月")])})),_:1,__:[47]}),(0,r.bF)(D,{label:"3个月"},{default:(0,r.k6)((function(){return t[48]||(t[48]=[(0,r.eW)("3个月")])})),_:1,__:[48]}),(0,r.bF)(D,{label:"6个月"},{default:(0,r.k6)((function(){return t[49]||(t[49]=[(0,r.eW)("6个月")])})),_:1,__:[49]}),(0,r.bF)(D,{label:"custom"},{default:(0,r.k6)((function(){return t[50]||(t[50]=[(0,r.eW)("自定义")])})),_:1,__:[50]})]})),_:1},8,["modelValue"]),"custom"===_.formData.followUpPeriod?((0,r.uX)(),(0,r.Wv)(P,{key:0,modelValue:_.formData.customFollowUp,"onUpdate:modelValue":t[17]||(t[17]=function(e){return _.formData.customFollowUp=e}),placeholder:"请输入自定义时间",style:{"margin-top":"10px",width:"200px"}},null,8,["modelValue"])):(0,r.Q3)("",!0)]})),_:1}),(0,r.bF)(k,{label:"预后评估",prop:"prognosisRating"},{default:(0,r.k6)((function(){return[(0,r.bF)(O,{modelValue:_.formData.prognosisRating,"onUpdate:modelValue":t[18]||(t[18]=function(e){return _.formData.prognosisRating=e}),texts:["易复发","有复发风险","一般","较好","完全根治"],"show-text":""},null,8,["modelValue"])]})),_:1}),(0,r.bF)(k,{label:"患者教育重点",prop:"patientEducation"},{default:(0,r.k6)((function(){return[(0,r.bF)(w,{modelValue:_.formData.patientEducation,"onUpdate:modelValue":t[19]||(t[19]=function(e){return _.formData.patientEducation=e})},{default:(0,r.k6)((function(){return[(0,r.bF)(y,{label:"避免日晒"},{default:(0,r.k6)((function(){return t[51]||(t[51]=[(0,r.eW)("避免日晒")])})),_:1,__:[51]}),(0,r.bF)(y,{label:"禁止抓挠"},{default:(0,r.k6)((function(){return t[52]||(t[52]=[(0,r.eW)("禁止抓挠")])})),_:1,__:[52]}),(0,r.bF)(y,{label:"定期服药提醒"},{default:(0,r.k6)((function(){return t[53]||(t[53]=[(0,r.eW)("定期服药提醒")])})),_:1,__:[53]}),(0,r.bF)(y,{label:"防护建议"},{default:(0,r.k6)((function(){return t[54]||(t[54]=[(0,r.eW)("防护建议")])})),_:1,__:[54]}),(0,r.bF)(y,{label:"生活方式调整"},{default:(0,r.k6)((function(){return t[55]||(t[55]=[(0,r.eW)("生活方式调整")])})),_:1,__:[55]})]})),_:1},8,["modelValue"]),(0,r.bF)(P,{modelValue:_.formData.patientEducationDetail,"onUpdate:modelValue":t[20]||(t[20]=function(e){return _.formData.patientEducationDetail=e}),type:"textarea",placeholder:"详细教育指导内容",rows:3,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1})])]),(0,r.Lk)("div",p,[(0,r.bF)(E,{type:"success",onClick:v.submitForm},{default:(0,r.k6)((function(){return t[57]||(t[57]=[(0,r.eW)("提交")])})),_:1,__:[57]},8,["onClick"])])]})),_:1},8,["model","rules"])]))])}var _=a(9201);a(4114),a(9432),a(6031);const v={name:"CaseStructuredForm",data:function(){return{loading:!1,annotationData:null,formData:{location:[],patientAge:null,stage:"",morphology:[],morphologyDesc:"",bloodFlow:"",symptoms:[],symptomsDesc:"",complications:[],complicationsDesc:"",diagnosis:"",diagnosisCode:"",treatmentPriority:"",treatmentPlan:[],treatmentDetail:"",contraindications:"",followUpPeriod:"",customFollowUp:"",prognosisRating:3,patientEducation:[],patientEducationDetail:""},rules:{location:[{required:!0,message:"请选择病变部位",trigger:"change"}],patientAge:[{required:!0,message:"请输入患者年龄",trigger:"blur"}],stage:[{required:!0,message:"请选择病程阶段",trigger:"change"}],bloodFlow:[{required:!0,message:"请选择血流信号",trigger:"change"}],diagnosis:[{required:!0,message:"请输入诊断结论",trigger:"blur"}],treatmentPriority:[{required:!0,message:"请选择治疗优先级",trigger:"change"}],followUpPeriod:[{required:!0,message:"请选择复查周期",trigger:"change"}]},locationOptions:[{value:"皮肤",label:"皮肤",children:[{value:"面部",label:"面部",children:[{value:"额头",label:"额头"},{value:"眼睑",label:"眼睑"},{value:"鼻翼",label:"鼻翼"},{value:"唇部",label:"唇部"},{value:"耳朵",label:"耳朵"},{value:"其他",label:"其他"}]},{value:"颈部",label:"颈部"},{value:"躯干",label:"躯干"},{value:"四肢",label:"四肢",children:[{value:"手臂",label:"手臂"},{value:"腿部",label:"腿部"},{value:"足部",label:"足部"}]}]},{value:"内脏",label:"内脏",children:[{value:"肝脏",label:"肝脏",children:[{value:"左叶",label:"左叶"},{value:"右叶",label:"右叶"}]},{value:"脾脏",label:"脾脏"},{value:"肺部",label:"肺部"},{value:"肠道",label:"肠道"}]}]}},created:function(){var e=localStorage.getItem("annotations");e&&(this.annotationData=JSON.parse(e),this.prefilFormFromAnnotations())},methods:{prefilFormFromAnnotations:function(){},submitForm:function(){var e=this;this.$refs.caseForm.validate((function(t){if(!t)return e.$message.error("请填写完整必填信息"),!1;e.loading=!0;var a=(0,_.A)((0,_.A)({},e.formData),{},{annotations:e.annotationData});console.log("提交的表单数据:",a),setTimeout((function(){e.loading=!1,e.$message.success("病例信息提交成功！"),localStorage.removeItem("annotations"),e.$router.push("/cases")}),1e3)}))}}};var F=a(6262);const k=(0,F.A)(v,[["render",g],["__scopeId","data-v-d67bcf88"]]),h=k},3640:(e,t,a)=>{var r=a(8551),o=a(4270),n=TypeError;e.exports=function(e){if(r(this),"string"===e||"default"===e)e="string";else if("number"!==e)throw new n("Incorrect hint");return o(this,e)}},3802:(e,t,a)=>{var r=a(9504),o=a(7750),n=a(655),l=a(7452),u=r("".replace),i=RegExp("^["+l+"]+"),c=RegExp("(^|[^"+l+"])["+l+"]+$"),f=function(e){return function(t){var a=n(o(t));return 1&e&&(a=u(a,i,"")),2&e&&(a=u(a,c,"$1")),a}};e.exports={start:f(1),end:f(2),trim:f(3)}},3851:(e,t,a)=>{var r=a(6518),o=a(9039),n=a(5397),l=a(7347).f,u=a(3724),i=!u||o((function(){l(1)}));r({target:"Object",stat:!0,forced:i,sham:!u},{getOwnPropertyDescriptor:function(e,t){return l(n(e),t)}})},4599:(e,t,a)=>{var r=a(6518),o=a(4576),n=a(9472),l=n(o.setTimeout,!0);r({global:!0,bind:!0,forced:o.setTimeout!==l},{setTimeout:l})},5575:(e,t,a)=>{var r=a(6518),o=a(4576),n=a(9472),l=n(o.setInterval,!0);r({global:!0,bind:!0,forced:o.setInterval!==l},{setInterval:l})},5700:(e,t,a)=>{var r=a(511),o=a(8242);r("toPrimitive"),o()},6031:(e,t,a)=>{a(5575),a(4599)},7452:e=>{e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},7945:(e,t,a)=>{var r=a(6518),o=a(3724),n=a(6801).f;r({target:"Object",stat:!0,forced:Object.defineProperties!==n,sham:!o},{defineProperties:n})},8130:(e,t,a)=>{var r=a(6518),o=a(6395),n=a(3724),l=a(4576),u=a(9167),i=a(9504),c=a(2796),f=a(9297),s=a(3167),d=a(1625),m=a(757),b=a(2777),p=a(9039),g=a(8480).f,_=a(7347).f,v=a(4913).f,F=a(1240),k=a(3802).trim,h="Number",D=l[h],V=u[h],y=D.prototype,w=l.TypeError,P=i("".slice),U=i("".charCodeAt),W=function(e){var t=b(e,"number");return"bigint"==typeof t?t:O(t)},O=function(e){var t,a,r,o,n,l,u,i,c=b(e,"number");if(m(c))throw new w("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=k(c),t=U(c,0),43===t||45===t){if(a=U(c,2),88===a||120===a)return NaN}else if(48===t){switch(U(c,1)){case 66:case 98:r=2,o=49;break;case 79:case 111:r=8,o=55;break;default:return+c}for(n=P(c,2),l=n.length,u=0;u<l;u++)if(i=U(n,u),i<48||i>o)return NaN;return parseInt(n,r)}return+c},E=c(h,!D(" 0o1")||!D("0b1")||D("+0x1")),x=function(e){return d(y,e)&&p((function(){F(e)}))},I=function(e){var t=arguments.length<1?0:D(W(e));return x(this)?s(Object(t),this,I):t};I.prototype=y,E&&!o&&(y.constructor=I),r({global:!0,constructor:!0,wrap:!0,forced:E},{Number:I});var N=function(e,t){for(var a,r=n?g(t):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;r.length>o;o++)f(t,a=r[o])&&!f(e,a)&&v(e,a,_(t,a))};o&&V&&N(u[h],V),(E||o)&&N(u[h],D)},9201:(e,t,a)=>{a.d(t,{A:()=>i});a(2675),a(2008),a(1629),a(4114),a(8111),a(2489),a(7588),a(7945),a(4185),a(3851),a(1278),a(9432),a(6099),a(3500);var r=a(4119);a(5700),a(6280),a(6918),a(9572),a(8130);function o(e,t){if("object"!=(0,r.A)(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var o=a.call(e,t||"default");if("object"!=(0,r.A)(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function n(e){var t=o(e,"string");return"symbol"==(0,r.A)(t)?t:t+""}function l(e,t,a){return(t=n(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function i(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){l(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}},9472:(e,t,a)=>{var r=a(4576),o=a(8745),n=a(4901),l=a(4215),u=a(2839),i=a(7680),c=a(2812),f=r.Function,s=/MSIE .\./.test(u)||"BUN"===l&&function(){var e=r.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}();e.exports=function(e,t){var a=t?2:1;return s?function(r,l){var u=c(arguments.length,1)>a,s=n(r)?r:f(r),d=u?i(arguments,a):[],m=u?function(){o(s,this,d)}:s;return t?e(m,l):e(m)}:e}},9572:(e,t,a)=>{var r=a(9297),o=a(6840),n=a(3640),l=a(8227),u=l("toPrimitive"),i=Date.prototype;r(i,u)||o(i,u,n)}}]);