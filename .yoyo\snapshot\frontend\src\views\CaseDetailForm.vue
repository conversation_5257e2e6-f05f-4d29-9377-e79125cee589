<template>
  <div class="annotation-container">
    <div class="page-header">
      <h2>图像标注</h2>
    </div>

    <div class="main-content">
      <!-- 标注工具栏 -->
      <div class="toolbar">
        <div class="tool-section">
          <h3>标签分类</h3>
          <el-select v-model="selectedTag" placeholder="请选择标签分类" style="width: 100%">
            <el-option label="血管瘤" value="血管瘤"></el-option>
            <el-option label="淋巴管瘤" value="淋巴管瘤"></el-option>
            <el-option label="混合型" value="混合型"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </div>
        
        <div class="tool-section">
          <h3>标注工具</h3>
          <el-radio-group v-model="currentTool" style="display: block; margin-top: 10px;">
            <el-radio label="rectangle">矩形框</el-radio>
          </el-radio-group>
        </div>

        <div class="annotations-list" v-if="annotations.length > 0">
          <h3>已添加标注</h3>
          <el-table :data="filteredAnnotations" style="width: 100%">
            <el-table-column label="编号" width="60">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="tag" label="标签" width="90" />
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button type="danger" size="small" @click="deleteAnnotation(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 图像标注区域 -->
      <div class="annotation-area">
        <div class="image-wrapper">
          <div class="image-container" ref="imageContainerInner">
            <!-- 固定显示的图片 -->
            <img 
              v-if="currentImage" 
              :src="getImageUrl(currentImage.url)" 
              ref="annotationImage"
              class="annotation-image"
              @load="handleImageLoad" 
              alt="医学影像"
            />
            
            <!-- 透明覆盖层，用于标注，与图片大小一致 -->
            <div 
              v-if="currentImage"
              class="annotation-overlay"
              :style="{
                width: imageWidth + 'px',
                height: imageHeight + 'px'
              }"
              @mousedown="startDrawing"
              @mousemove="drawing"
              @mouseup="endDrawing"
            ></div>
            
            <!-- 标注框 -->
            <div 
              v-for="(box, index) in filteredAnnotations" 
              :key="index" 
              class="annotation-box"
              :style="{
                left: box.x + 'px',
                top: box.y + 'px',
                width: box.width + 'px',
                height: box.height + 'px',
                borderColor: getTagColor(box.tag),
                cursor: isEditingBox && editingBoxId === box.id ? 'move' : 'default'
              }"
              @mousedown.stop="startEditBox($event, box.id)"
            >
              <span class="annotation-label" :style="{ backgroundColor: getTagColor(box.tag) }">
                {{ box.tag }}
              </span>
              
              <!-- 四个角的调整大小的手柄 -->
              <div class="resize-handle top-left" @mousedown.stop="startResizeBox($event, box.id, 'top-left')"></div>
              <div class="resize-handle top-right" @mousedown.stop="startResizeBox($event, box.id, 'top-right')"></div>
              <div class="resize-handle bottom-left" @mousedown.stop="startResizeBox($event, box.id, 'bottom-left')"></div>
              <div class="resize-handle bottom-right" @mousedown.stop="startResizeBox($event, box.id, 'bottom-right')"></div>
            </div>
            
            <!-- 正在绘制的框 -->
            <div 
              v-if="isDrawing" 
              class="drawing-box"
              :style="{
                left: Math.min(drawStart.x, drawCurrent.x) + 'px',
                top: Math.min(drawStart.y, drawCurrent.y) + 'px',
                width: Math.abs(drawCurrent.x - drawStart.x) + 'px',
                height: Math.abs(drawCurrent.y - drawStart.y) + 'px'
              }"
            ></div>
          </div>
        </div>

        <div class="navigation-controls">
          <div>
            <el-button type="info" @click="goBack">返回上传图片</el-button>
          </div>
          <div>
            <el-button type="primary" @click="saveAndNext" style="margin-right: 10px;">保存并填写表单</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import api from '../utils/api'
import { getImageUrl } from '../utils/imageHelper'

export default {
  name: 'CaseDetailForm',
  data() {
    return {
      uploadedImages: [],
      currentImageIndex: 0,
      currentImage: null,
      selectedTag: '血管瘤',
      currentTool: 'rectangle',
      annotations: [],
      isDrawing: false,
      drawStart: { x: 0, y: 0 },
      drawCurrent: { x: 0, y: 0 },
      imageWidth: 0,
      imageHeight: 0,
      // 编辑框相关状态
      isEditingBox: false,
      isResizingBox: false,
      editingBoxId: null,
      resizeHandle: null,
      editStartPos: { x: 0, y: 0 },
      originalBox: null,
      imagePairId: null,
      // 新增：存储数据库标注
      dbAnnotations: [],
      annotationsLoaded: false,
      processedFilePath: null,
      shouldSaveOriginalImage: false,
      imageLoaded: false,
      isSavingAnnotations: false
    }
  },
  computed: {
    filteredAnnotations() {
      return this.annotations.filter(a => a.imageIndex === this.currentImageIndex)
    }
  },
  created() {
    // 从URL参数获取图片ID
    const imageId = this.$route.query.id || this.$route.query.imageId || localStorage.getItem('lastUploadedImageId');
    if (imageId) {
      console.log('检测到图像ID:', imageId);
      this.currentImageId = imageId;
      
      // 标记已登录操作状态
      sessionStorage.setItem('isAppOperation', 'true');
      
      // 直接从image_pairs表加载图像
      this.loadImageFromPairs(imageId);
    } else {
      // 如果没有ID，尝试从路径加载
      const imagePath = this.$route.query.path || this.$route.query.filePath || localStorage.getItem('processedFilePath');
      if (imagePath) {
        console.log('未提供图像ID，尝试使用路径:', imagePath);
        this.processedFilePath = imagePath;
        this.loadImageByPath(imagePath);
      } else {
        console.error('未提供图像ID和路径，无法加载图像');
        this.$message.error('未提供图像ID，请先上传图像');
      }
    }
    
    // 注册事件监听器
    window.addEventListener('resize', this.handleResize);
  },
  mounted() {
    console.log('CaseDetailForm mounted - 查询参数:', this.$route.query);
    
    // 获取图像ID，确保转换为字符串类型
    const imageId = String(this.$route.query.imageId || '');
    console.log('图像ID:', imageId, '类型:', typeof imageId);
    
    // 检查是否有有效的图像ID
    if (!imageId) {
      this.$message.error('未提供有效的图像ID');
      return;
    }
    
    // 保存图像ID到组件实例
    this.imageId = imageId;
    
    // 获取图像详情
    api.images.getOne(imageId)
      .then(response => {
        console.log('成功获取图像详情:', response.data);
        this.imageData = response.data;
        
        // 获取图像对
        return api.imagePairs.getByMetadataId(imageId);
      })
      .then(response => {
        console.log('成功获取图像对:', response.data);
        if (Array.isArray(response.data) && response.data.length > 0) {
          // 如果返回数组，使用第一个元素
          this.imagePair = response.data[0];
        } else {
          // 如果返回单个对象
          this.imagePair = response.data;
        }
        
        // 保存图像对ID
        if (this.imagePair && this.imagePair.id) {
          this.imagePairId = this.imagePair.id;
          console.log('图像对ID:', this.imagePairId);
        } else {
          console.warn('未找到有效的图像对');
        }
        
        // 加载图像
        this.loadImage();
        
        // 加载标签
        return api.tags.getByImageId(imageId);
      })
      .then(response => {
        console.log('成功获取标签:', response.data);
        if (response.data && Array.isArray(response.data)) {
          this.tags = response.data;
          // 初始化标注
          this.initializeAnnotations();
        } else {
          console.warn('标签数据格式不正确:', response.data);
          this.tags = [];
        }
      })
      .catch(error => {
        console.error('加载图像数据失败:', error);
        
        // 尝试从localStorage加载离线数据
        this.tryLoadOfflineData(imageId);
        
        // 显示友好的错误消息
        this.$message.error('加载图像数据失败，请检查网络连接或刷新页面重试');
      });
    
    // 标记为应用内操作
    sessionStorage.setItem('isAppOperation', 'true');
    
    // 加载当前图像的标注数据
    if (this.currentImage) {
      this.loadAnnotationsFromDatabase()
      // 保存原始图像到image_pairs的image_one_path字段
      this.saveOriginalImage()
    }
    
    // 添加全局鼠标事件监听
    window.addEventListener('mousemove', this.handleGlobalMouseMove)
    window.addEventListener('mouseup', this.handleGlobalMouseUp)

    // 加载完组件后，如果设置了应该保存图像，则执行保存
    if (this.shouldSaveOriginalImage && this.imageLoaded && this.processedFilePath) {
      this.saveOriginalImage();
    }
  },
  unmounted() {
    // 移除全局鼠标事件监听
    window.removeEventListener('mousemove', this.handleGlobalMouseMove)
    window.removeEventListener('mouseup', this.handleGlobalMouseUp)
    // 移除resize事件监听器
    window.removeEventListener('resize', this.handleResize)
    
    // 清除导航标记
    sessionStorage.removeItem('isNavigatingAfterSave');
    sessionStorage.removeItem('returningToWorkbench');
    // 保留isAppOperation，以维持应用内操作状态
  },
  methods: {
    // 处理长ID，将时间戳ID转换为合适的整数
    processLongId(id) {
      if (!id) return null;
      
      const idStr = id.toString();
      // 如果ID长度超过9（INT范围限制），截取后9位
      if (idStr.length > 9) {
        console.log(`处理长ID: ${idStr} -> ${idStr.substring(idStr.length - 9)}`);
        return idStr.substring(idStr.length - 9);
      }
      return idStr;
    },

    // 添加loadImage方法，解决"this.loadImage is not a function"错误
    loadImage() {
      console.log('CaseDetailForm.loadImage 被调用');
      
      if (!this.currentImage || !this.currentImage.url) {
        console.error('loadImage: 当前图像或URL为空');
        return;
      }
      
      console.log('加载图像:', this.currentImage.url);
      
      // 在下一个渲染周期执行，确保DOM已更新
      this.$nextTick(() => {
        // 图像加载会触发handleImageLoad方法
        if (this.$refs.annotationImage) {
          // 强制刷新图像src
          const img = this.$refs.annotationImage;
          const currentSrc = img.src;
          img.src = '';
          setTimeout(() => {
            img.src = this.getImageUrl(this.currentImage.url);
          }, 10);
        } else {
          console.error('annotationImage引用不存在');
        }
      });
    },

    // 从image_pairs表加载图像
    loadImageFromPairs(imageId) {
      console.log('🔍 开始从image_pairs表加载图像，ID:', imageId);
      
      // 获取当前用户信息
      const user = JSON.parse(localStorage.getItem('user')) || {};
      const userId = user.customId || user.id;
      
      if (!userId) {
        console.warn('未找到用户ID，可能导致权限问题');
          }
      
      // 显式添加用户ID作为查询参数
      const params = { userId };
      
      // 添加时间戳避免缓存
      params.t = new Date().getTime();
      
      // 先从image_pairs表查询相关记录
      api.imagePairs.getByMetadataId(imageId)
        .then(response => {
          console.log('image_pairs查询响应:', response);
          const data = response.data;
          
          if (data && data.length > 0) {
            const imagePair = data[0];
            console.log('image_pairs第一条记录:', imagePair);
            this.imagePairId = imagePair.id;
            
            // 使用处理后的图像路径
            const processedImagePath = imagePair.imageOnePath || imagePair.image_one_path;
            console.log('获取到图像路径:', processedImagePath);
            
            // 如果路径仍为空，尝试其他字段
            if (!processedImagePath && imagePair.image_one_url) {
              console.log('尝试使用image_one_url:', imagePair.image_one_url);
              this.processedFilePath = imagePair.image_one_url;
            } else if (!processedImagePath && imagePair.url) {
              console.log('尝试使用url字段:', imagePair.url);
              this.processedFilePath = imagePair.url;
            } else {
              this.processedFilePath = processedImagePath;
            }
            
            // 如果仍然没有找到路径，直接从image_metadata表获取
            if (!this.processedFilePath) {
              console.log('image_pairs中未找到图像路径，尝试从image_metadata表获取');
              this.tryLoadImagePathFromMetadata(imageId, (path) => {
                if (path) {
                  console.log('从image_metadata表获取到图像路径:', path);
                  this.processedFilePath = path;
                  this.updateImagePairPath(imageId, path);
                  
                  // 更新上传图像数组
                  this.uploadedImages = [{
                    id: imageId,
                    url: path,
                    filename: `图像 #${imageId}`
                  }];
                  
                  this.currentImage = this.uploadedImages[0];
                  this.imageLoaded = true;
                }
              });
              return;  // 中断当前流程，等待回调处理
            }
            
            // 更新上传图像数组
            this.uploadedImages = [{
              id: imageId,
              url: this.processedFilePath,
              filename: `图像 #${imageId}`
            }];
            
            this.currentImage = this.uploadedImages[0];
            this.currentImageIndex = 0;
            this.currentImageId = imageId;
            
            // 标记图像已加载
            this.imageLoaded = true;
            
            // 加载标注框
            this.loadAnnotations(imageId);
          } else {
            console.log('image_pairs未找到数据，尝试从image_metadata表直接获取路径');
            this.tryLoadImagePathFromMetadata(imageId, (path) => {
              if (path) {
                console.log('从image_metadata表获取到图像路径:', path);
                this.processedFilePath = path;
                this.createImageRecord(path, imageId);
              } else {
                this.$message.error('未能找到图像路径，无法加载图像');
              }
            });
          }
        })
        .catch(error => {
          console.error('加载图像对失败:', error);
          // 尝试直接从image_metadata获取
          this.tryLoadImagePathFromMetadata(imageId);
        });
    },
    
    // 作为备用，尝试从image_metadata加载
    tryLoadFromMetadata(imageId) {
      console.log('从image_metadata表尝试加载图像ID:', imageId);
      
      api.images.getOne(imageId)
        .then(response => {
          console.log('从image_metadata表获取的完整响应:', response);
          const imageData = response.data;
          console.log('image_metadata表获取的图像数据:', imageData);
          
          if (!imageData || !imageData.path) {
            console.error('image_metadata表中图像路径不存在，无法加载图像');
            this.$message.error('图像路径不存在，请检查数据库');
            return;
          }
          
          // 准备图像数据
          this.uploadedImages = [{
            id: imageData.id,
            url: imageData.path,
            filename: imageData.original_name || `图像 #${imageData.id}`
          }];
          
          this.currentImage = this.uploadedImages[0];
          this.currentImageIndex = 0;
          
          // 保存处理后的图像路径
          this.processedFilePath = imageData.path;
          
          // 创建ImagePair记录 - 直接使用fetch创建，避免API调用
          this.createImagePairDirectly(imageData.id, imageData.path);
          
          // 加载标注数据
          this.loadAnnotationsFromDatabase();
          
          // 标记图像已成功加载
          this.imageLoaded = true;
          
          console.log('从image_metadata加载成功并创建image_pairs记录');
        })
        .catch(error => {
          console.error('从image_metadata加载图像失败:', error);
          this.loadingError = true;
          this.loadingErrorMessage = '无法加载图像，请检查数据库记录';
          
          // 如果有物理路径，尝试创建新的图像记录
          if (this.processedFilePath && this.currentImageId) {
            console.log('尝试使用现有路径创建图像记录');
            this.createImageRecord(this.processedFilePath, this.currentImageId);
          } else {
            this.$message.error('无法找到图像数据，请返回上一步重新上传');
          }
        });
    },
    
    // 直接创建ImagePair记录，不使用API
    createImagePairDirectly(metadataId, imagePath) {
      console.log('直接创建ImagePair记录:', { metadataId, path: imagePath });
      
      // 准备数据
      const data = {
        metadataId: metadataId.toString(),
        imageOnePath: imagePath,
        description: `图像${metadataId}`
      };
      
      // 直接使用fetch API，完全绕过axios和api.js
      fetch('/api/image-pairs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data),
        credentials: 'include'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(result => {
          console.log('成功创建ImagePair记录:', result);
          if (result && result.id) {
            this.imagePairId = result.id;
          }
          this.$message.success('图像关联信息创建成功');
        })
        .catch(error => {
          console.error('创建ImagePair记录失败:', error);
          this.$message.warning('图像关联创建失败，但您仍可继续使用');
        });
    },
    
    // 保留原有方法但不再主动调用
    loadImageById(imageId) {
      // 重定向到新的加载方法
      this.loadImageFromPairs(imageId);
    },
    
    // 创建新的图像记录
    createImageRecord(imagePath, metadataId) {
      console.log('正在创建新的图像记录:', { path: imagePath, id: metadataId });
      
      // 获取当前用户
      const user = JSON.parse(localStorage.getItem('user')) || { id: 1 };
      
      // 提取文件名
      const filename = imagePath.substring(imagePath.lastIndexOf('/') + 1);
      
      // 创建元数据
      const metadata = {
        filename: filename,
        original_name: filename,
        path: imagePath,
        mimetype: 'image/jpeg',
        size: 0,
        width: 800,
        height: 600,
        status: 'DRAFT',
        uploaded_by: user.id
      };
      
      // 保存元数据
      api.images.update(metadataId, metadata)
        .then(response => {
          console.log('成功创建/更新图像元数据:', response.data);
          
          // 创建ImagePair记录
          this.saveOriginalImage();
          
          // 重新加载页面
          this.loadImageById(metadataId);
        })
        .catch(error => {
          console.error('创建图像元数据失败:', error);
          this.$message.error('无法创建图像记录，请检查数据库连接');
        });
    },
    
    // 根据路径加载图像
    loadImageByPath(imagePath) {
      // 模拟图像加载过程
      this.uploadedImages = [{
        id: this.currentImageId || Date.now().toString(),
        url: imagePath,
        filename: imagePath.substring(imagePath.lastIndexOf('/') + 1)
      }];
      
      this.currentImage = this.uploadedImages[0];
      this.currentImageIndex = 0;
      
      // 如果没有图像ID，使用当前时间戳作为ID
      if (!this.currentImageId) {
        this.currentImageId = this.uploadedImages[0].id;
        console.log('生成临时图像ID:', this.currentImageId);
      }
      
      // 确保ImagePair记录存在
      this.checkExistingImagePair();
      
      // 标记图像已成功加载
      this.imageLoaded = true;
    },
    
    // 检查并确保图像对记录存在
    checkExistingImagePair() {
      if (!this.currentImageId || !this.processedFilePath) {
        console.log('缺少图像ID或物理路径，不检查ImagePair记录');
        return;
      }
      
      console.log('检查图像ID对应的ImagePair记录:', this.currentImageId);
      
      api.imagePairs.getByMetadataId(this.currentImageId)
        .then(response => {
          if (response.data && response.data.length > 0) {
            // 找到现有记录
            const imagePair = response.data[0];
            this.imagePairId = imagePair.id;
            console.log('找到现有ImagePair记录:', imagePair);
            
            // 如果记录中的路径与当前路径不一致，更新记录
            if (imagePair.imageOnePath !== this.processedFilePath) {
              console.log('更新ImagePair中的路径信息');
              this.saveOriginalImage();
            }
          } else {
            // 没有找到记录，创建新记录
            console.log('未找到ImagePair记录，创建新记录');
            this.saveOriginalImage();
          }
        })
        .catch(error => {
          console.error('检查ImagePair记录失败:', error);
          // 尝试创建新记录
          console.log('尝试创建新的ImagePair记录');
          this.saveOriginalImage();
        });
    },
    
    saveOriginalImage() {
      // 如果没有图像ID，则不保存
      if (!this.currentImageId) {
        console.log('缺少图像ID，不保存到ImagePair表');
        return;
      }
      
      let imagePath = '';
      
      // 首选：从currentImage中获取URL（已经从数据库加载）
      if (this.currentImage && this.currentImage.url) {
        imagePath = this.currentImage.url;
        console.log('使用从数据库加载的图像路径:', imagePath);
      } 
      // 次选：使用本地保存的processedFilePath
      else if (this.processedFilePath) {
        imagePath = this.processedFilePath;
        console.log('使用本地保存的处理后图像路径:', imagePath);
      }
      // 如果都没有，无法保存
      else {
        console.error('无法找到有效的图像路径，不保存到ImagePair表');
        return;
      }
      
      // 确保图像元数据存在
      this.ensureImageMetadataExists(this.currentImageId, imagePath, () => {
        // 获取当前用户
        const user = JSON.parse(localStorage.getItem('user')) || { id: 1 };
        
        try {
          // 准备请求数据 - 极简格式，避免任何不必要的字段
          const data = {
            metadataId: this.currentImageId.toString(), // 使用字符串格式
            imageOnePath: imagePath,
            description: `图像${this.currentImageId}` // 简化描述
          };
          
          console.log('保存到image_pairs的数据:', data);
          
          // 使用fetch API直接发送请求 - 绕过所有中间件和缓存
          const directRequest = async () => {
            try {
              // 查询image_pairs是否存在
              console.log('直接查询ImagePair是否存在:', this.currentImageId);
              const checkResponse = await fetch(`/api/image-pairs/metadata/${this.currentImageId}`, {
                method: 'GET',
                headers: {
                  'Accept': 'application/json',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache'
                },
                credentials: 'include'
              });
              
              // 解析响应
              const pairsData = await checkResponse.json();
              let existingPairId = null;
              let exists = false;
              
              if (pairsData && pairsData.length > 0 && pairsData[0].id) {
                exists = true;
                existingPairId = pairsData[0].id;
                console.log('找到现有ImagePair:', existingPairId);
              }
              
              // 如果存在，添加ID字段
              if (exists && existingPairId) {
                data.id = existingPairId;
              }
              
              // 发送创建/更新请求
              const saveResponse = await fetch('/api/image-pairs', {
                method: 'POST', // 始终使用POST
                headers: {
                  'Content-Type': 'application/json',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache'
                },
                body: JSON.stringify(data),
                credentials: 'include'
              });
              
              if (!saveResponse.ok) {
                throw new Error(`保存失败: ${saveResponse.status}`);
              }
              
              const result = await saveResponse.json();
              console.log('保存ImagePair成功:', result);
              
              // 更新本地ID
              if (result && result.id) {
                this.imagePairId = result.id;
              }
              
              this.$message.success('图像关联信息保存成功');
              
            } catch (error) {
              console.error('保存ImagePair失败:', error);
              this.$message.error('保存失败: ' + error.message);
            }
          };
          
          // 执行直接请求
          directRequest();
          
        } catch (err) {
          console.error('准备ImagePair数据时出错:', err);
          this.$message.error('保存图像对失败: ' + err.message);
        }
      });
    },
    
    // 确保图像元数据存在
    ensureImageMetadataExists(imageId, imagePath, callback) {
      // 检查图像元数据是否存在
      api.images.getOne(imageId)
        .then(() => {
          // 元数据存在，执行回调
          callback();
        })
        .catch(() => {
          // 元数据不存在，创建新的
          console.log('图像元数据不存在，创建新记录');
          this.createImageRecord(imagePath, imageId);
        });
    },
    
    // 窗口大小变化时更新图像尺寸
    handleResize() {
      // 如果图像已加载，重新获取尺寸
      if (this.$refs.annotationImage) {
        const img = this.$refs.annotationImage;
        const rect = img.getBoundingClientRect();
        this.imageWidth = rect.width;
        this.imageHeight = rect.height;
        console.log('窗口尺寸变化，更新图像尺寸:', this.imageWidth, 'x', this.imageHeight);
      }
    },
    
    // 添加空的initTools方法以修复错误
    initTools() {
      console.log('初始化标注工具');
      // 这个方法被调用但原本不存在，添加一个空实现
    },
    
    getImageUrl(path) {
      // 使用imageHelper处理图片URL
      console.log('CaseDetailForm.getImageUrl 调用 - 输入路径:', path);
      const url = getImageUrl(path);
      console.log('CaseDetailForm.getImageUrl 结果:', url);
      return url;
    },
    
    // 图像加载完成后的处理
    handleImageLoad(event) {
      // 添加详细的图像URL日志
      const imageUrl = this.currentImage ? this.getImageUrl(this.currentImage.url) : 'undefined';
      console.log('图片加载事件触发，图像路径:', {
        原始URL: this.currentImage?.url,
        处理后URL: imageUrl,
        图像元素: this.$refs.annotationImage,
        完整src: this.$refs.annotationImage?.src,
        尺寸: this.$refs.annotationImage ? `${this.$refs.annotationImage.naturalWidth} x ${this.$refs.annotationImage.naturalHeight}` : '未知'
      });
      
      // 获取图片的实际显示尺寸
      if (this.$refs.annotationImage) {
        const img = this.$refs.annotationImage;
        
        // 使用getBoundingClientRect获取当前图片的实际显示尺寸
        const rect = img.getBoundingClientRect();
        this.imageWidth = rect.width;
        this.imageHeight = rect.height;
        
        // 需要等待下一个渲染循环确保尺寸已更新
        this.$nextTick(() => {
          console.log('图片尺寸更新为:', this.imageWidth, 'x', this.imageHeight);
          
          // 设置图像已加载标志
          this.imageLoaded = true;
          
          // 处理已加载的标注
          if (this.dbAnnotations.length > 0 && !this.annotationsLoaded) {
            this.processLoadedAnnotations();
          }
        });
      }
    },
    selectTool(tool) {
      this.currentTool = tool
    },
    startDrawing(event) {
      // 如果正在编辑框，则不开始绘制新框
      if (this.isEditingBox || this.isResizingBox) return
      if (this.currentTool !== 'rectangle') return

      const rect = this.$refs.imageContainerInner.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      // 确保绘制在图片范围内
      if (x < 0 || x > this.imageWidth || y < 0 || y > this.imageHeight) return

      this.isDrawing = true
      this.drawStart = { x, y }
      this.drawCurrent = { x, y }
    },
    drawing(event) {
      if (!this.isDrawing) return

      const rect = this.$refs.imageContainerInner.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      // 约束坐标在图片范围内，但不停止绘制
      const boundedX = Math.max(0, Math.min(this.imageWidth, x))
      const boundedY = Math.max(0, Math.min(this.imageHeight, y))

      this.drawCurrent = { x: boundedX, y: boundedY }
    },
    // 开始编辑已有的标注框（移动整个框）
    startEditBox(event, boxId) {
      event.preventDefault()
      
      // 查找要编辑的标注框
      const boxToEdit = this.annotations.find(box => box.id === boxId)
      if (!boxToEdit) return
      
      // 保存原始框的信息，用于计算偏移量
      this.originalBox = {...boxToEdit}
      
      const rect = this.$refs.imageContainerInner.getBoundingClientRect()
      this.editStartPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
      
      this.isEditingBox = true
      this.editingBoxId = boxId
      this.isDrawing = false // 确保不在绘制新框
    },
    // 开始调整标注框大小
    startResizeBox(event, boxId, handle) {
      event.preventDefault()
      
      // 查找要调整大小的标注框
      const boxToResize = this.annotations.find(box => box.id === boxId)
      if (!boxToResize) return
      
      // 保存原始框的信息
      this.originalBox = {...boxToResize}
      
      const rect = this.$refs.imageContainerInner.getBoundingClientRect()
      this.editStartPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
      
      this.isResizingBox = true
      this.editingBoxId = boxId
      this.resizeHandle = handle
      this.isDrawing = false // 确保不在绘制新框
    },
    // 处理拖动框或调整大小
    handleGlobalMouseMove(event) {
      // 处理绘制新框
      if (this.isDrawing) {
        const rect = this.$refs.imageContainerInner.getBoundingClientRect()
        const x = event.clientX - rect.left
        const y = event.clientY - rect.top
        
        // 约束坐标在图片范围内
        const boundedX = Math.max(0, Math.min(this.imageWidth, x))
        const boundedY = Math.max(0, Math.min(this.imageHeight, y))
        
        this.drawCurrent = { x: boundedX, y: boundedY }
        return
      }
      
      // 处理移动整个框
      if (this.isEditingBox) {
        const rect = this.$refs.imageContainerInner.getBoundingClientRect()
        const currentX = event.clientX - rect.left
        const currentY = event.clientY - rect.top
        
        // 计算移动的偏移量
        const deltaX = currentX - this.editStartPos.x
        const deltaY = currentY - this.editStartPos.y
        
        // 找到正在编辑的框
        const boxIndex = this.annotations.findIndex(box => box.id === this.editingBoxId)
        if (boxIndex === -1) return
        
        // 计算新位置，确保在图片范围内
        let newX = this.originalBox.x + deltaX
        let newY = this.originalBox.y + deltaY
        
        // 防止框移出图片边界
        newX = Math.max(0, Math.min(newX, this.imageWidth - this.originalBox.width))
        newY = Math.max(0, Math.min(newY, this.imageHeight - this.originalBox.height))
        
        // 更新框位置
        this.annotations[boxIndex].x = newX
        this.annotations[boxIndex].y = newY
        return
      }
      
      // 处理调整框大小
      if (this.isResizingBox) {
        const rect = this.$refs.imageContainerInner.getBoundingClientRect()
        const currentX = event.clientX - rect.left
        const currentY = event.clientY - rect.top
        
        // 约束坐标在图片范围内
        const boundedX = Math.max(0, Math.min(this.imageWidth, currentX))
        const boundedY = Math.max(0, Math.min(this.imageHeight, currentY))
        
        // 找到正在调整的框
        const boxIndex = this.annotations.findIndex(box => box.id === this.editingBoxId)
        if (boxIndex === -1) return
        
        const box = this.annotations[boxIndex]
        const original = this.originalBox
        
        // 根据拖动的角落调整大小
        switch (this.resizeHandle) {
          case 'top-left':
            // 调整左上角
            box.width = original.x + original.width - boundedX
            box.height = original.y + original.height - boundedY
            box.x = boundedX
            box.y = boundedY
            // 确保宽度和高度为正值
            if (box.width < 10) {
              box.width = 10
              box.x = original.x + original.width - 10
            }
            if (box.height < 10) {
              box.height = 10
              box.y = original.y + original.height - 10
            }
            break;
            
          case 'top-right':
            // 调整右上角
            box.width = boundedX - original.x
            box.height = original.y + original.height - boundedY
            box.y = boundedY
            // 确保宽度和高度为正值
            if (box.width < 10) box.width = 10
            if (box.height < 10) {
              box.height = 10
              box.y = original.y + original.height - 10
            }
            break;
            
          case 'bottom-left':
            // 调整左下角
            box.width = original.x + original.width - boundedX
            box.height = boundedY - original.y
            box.x = boundedX
            // 确保宽度和高度为正值
            if (box.width < 10) {
              box.width = 10
              box.x = original.x + original.width - 10
            }
            if (box.height < 10) box.height = 10
            break;
            
          case 'bottom-right':
            // 调整右下角
            box.width = boundedX - original.x
            box.height = boundedY - original.y
            // 确保宽度和高度为正值
            if (box.width < 10) box.width = 10
            if (box.height < 10) box.height = 10
            break;
        }
      }
    },
    handleGlobalMouseUp() {
      if (this.isDrawing) {
        this.endDrawing()
      } else if (this.isEditingBox || this.isResizingBox) {
        // 结束框的编辑或调整大小
        
        // 找到正在编辑的标注框
        const editedAnnotation = this.annotations.find(box => box.id === this.editingBoxId)
        if (editedAnnotation) {
          // 如果原始框存在，比较是否有变更
          if (this.originalBox) {
            const hasChanged = 
              editedAnnotation.x !== this.originalBox.x || 
              editedAnnotation.y !== this.originalBox.y || 
              editedAnnotation.width !== this.originalBox.width || 
              editedAnnotation.height !== this.originalBox.height
            
            if (hasChanged) {
              console.log('标注框已修改，更新数据库:', {
                原位置: `(${this.originalBox.x}, ${this.originalBox.y})`,
                原尺寸: `${this.originalBox.width} x ${this.originalBox.height}`,
                新位置: `(${editedAnnotation.x}, ${editedAnnotation.y})`,
                新尺寸: `${editedAnnotation.width} x ${editedAnnotation.height}`
              })
              
              // 将修改后的标注框更新到数据库
              this.updateAnnotationInDatabase(editedAnnotation)
            }
          }
        }
        
        // 重置状态
        this.isEditingBox = false
        this.isResizingBox = false
        this.editingBoxId = null
        this.resizeHandle = null
        this.originalBox = null
      }
    },
    endDrawing() {
      if (!this.isDrawing) return

      this.isDrawing = false

      // 添加标注
      if (Math.abs(this.drawCurrent.x - this.drawStart.x) > 10 && 
          Math.abs(this.drawCurrent.y - this.drawStart.y) > 10) {
        
        const x = Math.min(this.drawStart.x, this.drawCurrent.x)
        const y = Math.min(this.drawStart.y, this.drawCurrent.y)
        const width = Math.abs(this.drawCurrent.x - this.drawStart.x)
        const height = Math.abs(this.drawCurrent.y - this.drawStart.y)
        
        // 创建标注对象
        const annotation = {
          id: Date.now(),
          imageIndex: this.currentImageIndex,
          tag: this.selectedTag,
          type: 'rectangle',
          x: x,
          y: y,
          width: width,
          height: height
        }
        
        // 添加到本地数组
        this.annotations.push(annotation)
        
        // 保存到数据库
        this.saveAnnotationToDatabase(annotation)
      }
    },
    saveAnnotationToDatabase(annotation) {
      // 获取当前用户信息
      const user = JSON.parse(localStorage.getItem('user'))
      if (!user) {
        this.$message.error('未检测到用户信息，无法保存标注')
        return
      }
      
      // 获取当前图像的metadata_id
      const currentImage = this.uploadedImages[this.currentImageIndex]
      if (!currentImage || !currentImage.id) {
        this.$message.error('图像信息不完整，无法保存标注')
        return
      }
      
      console.log('准备保存标注', {
        图像ID: currentImage.id,
        标签类型: annotation.tag,
        坐标: `(${annotation.x}, ${annotation.y})`,
        尺寸: `${annotation.width} x ${annotation.height}`,
        用户ID: user.id
      })
      
      // 计算归一化坐标（将像素坐标转换为0-1范围）
      // 确保imageWidth和imageHeight不为0
      if (!this.imageWidth || !this.imageHeight) {
        this.$message.error('图片尺寸无效，无法计算标注坐标')
        return
      }
      
      // 归一化计算
      let normalizedX = annotation.x / this.imageWidth
      let normalizedY = annotation.y / this.imageHeight
      let normalizedWidth = annotation.width / this.imageWidth
      let normalizedHeight = annotation.height / this.imageHeight
      
      // 验证并修正坐标范围
      // x, y必须在[0,1]之间
      normalizedX = Math.max(0, Math.min(1, normalizedX))
      normalizedY = Math.max(0, Math.min(1, normalizedY))
      
      // width, height必须在(0,1]之间
      normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth))
      normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight))
      
      // 保留4位小数
      normalizedX = Number(normalizedX.toFixed(4))
      normalizedY = Number(normalizedY.toFixed(4))
      normalizedWidth = Number(normalizedWidth.toFixed(4))
      normalizedHeight = Number(normalizedHeight.toFixed(4))
      
      // 构建要发送到后端的数据
      const tagData = {
        metadata_id: currentImage.id,
        tag: annotation.tag,
        x: normalizedX,
        y: normalizedY,
        width: normalizedWidth,
        height: normalizedHeight,
        created_by: user.id
      }
      
      console.log('发送归一化坐标到服务器:', tagData)
      
      // 使用API工具类保存标注
      api.tags.create(tagData)
        .then(response => {
          console.log('标注已保存到数据库', response.data)
          // 更新本地标注对象，添加服务器返回的ID
          const index = this.annotations.findIndex(a => a.id === annotation.id)
          if (index !== -1) {
            this.annotations[index].dbId = response.data.id
          }
          this.$message.success('标注已保存')
        })
        .catch(error => {
          console.error('保存标注失败', error)
          if (error.response) {
            console.error('服务器响应:', error.response.status, error.response.data)
            
            // 根据错误类型显示不同的提示
            if (error.response.status === 401) {
              this.$message.error('未授权，请先登录')
            } else if (error.response.status === 400) {
              this.$message.error(error.response.data || '提交的标注数据无效')
            } else {
              this.$message.error('保存标注到数据库失败')
            }
          } else {
            this.$message.error('保存标注到数据库失败，网络错误')
          }
        })
    },
    getTagColor(tag) {
      const colors = {
        '血管瘤': '#f56c6c',
        '淋巴管瘤': '#409eff',
        '混合型': '#67c23a',
        '其他': '#909399'
      }
      return colors[tag] || '#409eff'
    },
    deleteAnnotation(id) {
      const index = this.annotations.findIndex(item => item.id === id)
      if (index !== -1) {
        const annotation = this.annotations[index]
        
        // 从本地数组中删除
        this.annotations.splice(index, 1)
        
        // 如果有数据库ID，则从数据库中也删除
        if (annotation.dbId) {
          console.log('正在从数据库删除标注', annotation.dbId)
          api.tags.delete(annotation.dbId)
            .then(() => {
              console.log('标注已从数据库中删除')
              this.$message.success('标注已删除')
            })
            .catch(error => {
              console.error('删除标注失败', error)
              this.$message.error('从数据库删除标注失败')
              // 恢复本地删除的标注
              this.annotations.splice(index, 0, annotation)
            })
        }
      }
    },
    async saveAndNext() {
      try {
        console.log('[导航跟踪] 保存并跳转到表单页，当前URL:', window.location.href);
        
        // 设置保存后导航标记
        sessionStorage.setItem('isNavigatingAfterSave', 'true');
        
        // 尝试保存标注
        let success = true;
        try {
          // 设置超时，确保不会永久等待
          const savePromise = this.saveAnnotations('保存中，准备跳转到表单页面...');
          const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('请求超时')), 8000)
          );
          
          // 使用Promise.race确保请求不会超过8秒
          success = await Promise.race([savePromise, timeoutPromise]);
        } catch (saveError) {
          console.error('保存标注时出错', saveError);
          this.$message.warning('保存标注过程中出现问题，将继续跳转到表单页面');
          success = true; // 即使保存失败也继续跳转
        }
        
        if (success || await this.confirmContinue()) {
          // 确保图像ID存在
          const imageId = this.currentImage?.id;
          if (!imageId) {
            this.$message.error('找不到图像ID，无法继续');
            return;
          }
          
          console.log('准备跳转到结构化表单页面，图像ID:', imageId);
          
          // 跳转到结构化表单页面
          this.$router.push({
            path: '/cases/structured-form',
            query: { 
              imageId: imageId,
              direct: '1'
            }
          }).then(() => {
            console.log('[导航跟踪] 跳转成功，当前URL:', window.location.href);
          }).catch(err => {
            console.error('路由跳转失败', err);
            // 使用window.location作为备选跳转方式
            window.location.href = `/cases/structured-form?imageId=${imageId}&direct=1`;
          });
        }
      } catch (error) {
        console.error('处理保存并跳转流程时出错', error);
        this.$message.error('出现错误：' + (error.message || '未知错误'));
      }
    },
    // 新增：确认是否继续的辅助方法
    async confirmContinue() {
      try {
        await this.$confirm('保存标注失败，是否仍要继续到表单页面？', '提示', {
          confirmButtonText: '继续',
          cancelButtonText: '留在当前页面',
          type: 'warning'
        });
        return true;
      } catch (e) {
        return false;
      }
    },
    // 新增：通用的保存标注方法
    async saveAnnotations(loadingText = '保存中...') {
      // 获取当前图像
      const currentImage = this.uploadedImages[this.currentImageIndex]
      if (!currentImage || !currentImage.id) {
        this.$message.error('无效的图像，无法保存标注')
        return false
      }
      
      // 防止重复提交
      if (this.isSavingAnnotations) {
        console.warn('正在保存标注，请勿重复操作')
        return false
      }
      
      this.isSavingAnnotations = true
      
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: loadingText,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      try {
        // 调用API保存标注图像
        console.log('保存标注，图像ID:', currentImage.id)
        
        // 调用saveAnnotatedImage API（这个API会自动生成标注图像）
        try {
          const response = await api.tags.saveAnnotatedImage(currentImage.id)
          console.log('标注图像保存成功:', response.data)
        } catch (annotationError) {
          console.error('保存标注图像失败:', annotationError)
          
          // 如果是服务器错误，可以尝试更新原有的图像
          if (annotationError.response && annotationError.response.status >= 500) {
            try {
              console.log('尝试使用另一种方法更新标注图像')
              await api.tags.updateImageAfterAnnotation({ metadata_id: currentImage.id })
              console.log('更新标注图像成功')
            } catch (updateError) {
              console.error('更新标注图像也失败:', updateError)
              throw updateError
            }
          } else {
            throw annotationError
          }
        }
        
        // 更新图像状态为已标注，添加重试机制
        try {
          // 获取当前中国时间
          const now = new Date()
          const chinaTime = new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
            timeZone: 'Asia/Shanghai'
          }).format(now)
          
          // 转换为ISO-8601格式
          const isoTime = now.toISOString()
          console.log('当前中国时间:', chinaTime, '，ISO时间:', isoTime)
          
          // 传递时间戳参数，避免重复请求
          try {
            await api.images.markAsAnnotated(currentImage.id, isoTime)
            console.log('图像状态已成功更新为"已标注"(REVIEWED)')
          } catch (markError) {
            console.error('标记为已标注失败:', markError)
            // 如果失败是因为特定错误，不再重试
            if (markError.response && markError.response.data && 
                markError.response.data.includes('重复')) {
              console.log('检测到重复标记消息，不再重试')
            } else {
              // 尝试使用另一种方法更新状态
              await api.images.updateStatus(currentImage.id, 'REVIEWED')
              console.log('使用updateStatus成功更新状态为"已标注"')
            }
          }
        } catch (statusError) {
          console.error('所有更新图像状态的尝试都失败:', statusError)
          // 即使状态更新失败，我们仍然继续流程
        }
        
        // 显示成功消息
        this.$message.success('标注已保存')
        
        // 关闭加载状态并重置防重复标志
        loading.close()
        this.isSavingAnnotations = false
        return true
      } catch (error) {
        console.error('保存标注图片失败', error)
        
        // 获取详细错误信息
        let errorMessage = '保存标注失败'
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data)
          
          if (typeof error.response.data === 'string') {
            errorMessage += ': ' + error.response.data
          } else if (error.response.data && error.response.data.message) {
            errorMessage += ': ' + error.response.data.message
          } else {
            errorMessage += ' (状态码: ' + error.response.status + ')'
          }
        } else if (error.message) {
          errorMessage += ': ' + error.message
        }
        
        // 仅显示错误消息，不阻止流程继续
        this.$message.error(errorMessage)
        loading.close()
        this.isSavingAnnotations = false
        
        // 询问用户是否要继续，即使保存标注失败
        try {
          const result = await this.$confirm('保存标注失败，但已在本地保存标注数据。是否继续?', '警告', {
            confirmButtonText: '继续',
            cancelButtonText: '留在当前页面',
            type: 'warning'
          })
          
          // 如果用户选择继续，保存标注数据到本地存储
          if (result === 'confirm') {
            localStorage.setItem('annotations', JSON.stringify(this.annotations))
            localStorage.setItem('pendingImageId', currentImage.id.toString())
            return true
          }
          return false
        } catch (e) {
          // 用户选择留在当前页面
          return false
        }
      }
    },
    loadAnnotationsFromDatabase() {
      // 获取当前用户
      const user = JSON.parse(localStorage.getItem('user'))
      if (!user) {
        console.error('未检测到用户信息，无法加载标注')
        return
      }
      
      // 对每个图像加载标注
      this.uploadedImages.forEach((image, index) => {
        if (image && image.id) {
          // 打印当前图像ID信息，用于调试
          console.log(`准备加载图像(${index})的标注数据，ID信息:`, {
            原始ID: image.id,
            ID类型: typeof image.id,
            ID长度: String(image.id).length,
            图像URL: image.url,
            图像对象: JSON.stringify(image)
          })
          
          // 确保ID是整数格式
          let metadataId = image.id
          
          // 针对长格式ID进行特殊处理
          if (String(metadataId).length > 10) {
            console.log('检测到长格式ID，使用特殊API查询方法');
          }
          
          // 使用API工具类获取标注，添加用户ID参数只获取当前用户的标注
          api.tags.getByImageId(metadataId, user.id)
            .then(response => {
              console.log(`成功获取图像(${index})的标注数据`, response)
              
              // 确保返回的数据是数组
              this.dbAnnotations = response.data || []
              
              // 如果返回的不是数组，记录错误并转换为空数组
              if (!Array.isArray(this.dbAnnotations)) {
                console.error('从服务器获取的标注数据不是数组格式', this.dbAnnotations)
                this.dbAnnotations = []
              }
              
              // 如果图像已加载并且有尺寸，立即处理标注
              if (this.imageWidth > 0 && this.imageHeight > 0) {
                this.processLoadedAnnotations()
              } else {
                console.log('图像尺寸尚未加载，将在图像加载后处理标注')
                // 标注数据已存储在this.dbAnnotations中，将在图像加载后处理
              }
            })
            .catch(error => {
              console.error(`加载图像${index + 1}的标注失败`, error)
              // 更详细地记录错误信息
              console.error('标注加载错误详情:', {
                图像ID: metadataId,
                错误状态: error.response ? error.response.status : '网络错误',
                错误消息: error.response ? error.response.data : error.message,
                请求URL: error.config ? error.config.url : '未知',
                请求方法: error.config ? error.config.method : '未知'
              })
              
              if (error.response) {
                console.error('服务器响应:', error.response.status, error.response.data)
                
                // 如果是401未授权错误，提示用户登录
                if (error.response.status === 401) {
                  this.$message.error('请先登录后再进行标注')
                }
                // 如果是404或400错误，可能是图像ID格式问题
                else if (error.response.status === 404 || error.response.status === 400) {
                  console.warn('图像ID可能无效，尝试检查图像上传和保存逻辑')
                  this.$message.warning('无法加载图像标注，但您仍可以添加新标注')
                }
              }
            })
        }
      })
    },
    updateAnnotationInDatabase(annotation) {
      // 获取当前用户信息
      const user = JSON.parse(localStorage.getItem('user'))
      if (!user) {
        this.$message.error('未检测到用户信息，无法更新标注')
        return
      }
      
      // 如果没有数据库ID，则无法更新
      if (!annotation.dbId) {
        console.warn('标注没有数据库ID，无法更新:', annotation)
        return
      }
      
      // 计算归一化坐标（将像素坐标转换为0-1范围）
      // 确保imageWidth和imageHeight不为0
      if (!this.imageWidth || !this.imageHeight) {
        this.$message.error('图片尺寸无效，无法计算标注坐标')
        return
      }
      
      // 归一化计算
      let normalizedX = annotation.x / this.imageWidth
      let normalizedY = annotation.y / this.imageHeight
      let normalizedWidth = annotation.width / this.imageWidth
      let normalizedHeight = annotation.height / this.imageHeight
      
      // 验证并修正坐标范围
      // x, y必须在[0,1]之间
      normalizedX = Math.max(0, Math.min(1, normalizedX))
      normalizedY = Math.max(0, Math.min(1, normalizedY))
      
      // width, height必须在(0,1]之间
      normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth))
      normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight))
      
      // 保留4位小数
      normalizedX = Number(normalizedX.toFixed(4))
      normalizedY = Number(normalizedY.toFixed(4))
      normalizedWidth = Number(normalizedWidth.toFixed(4))
      normalizedHeight = Number(normalizedHeight.toFixed(4))
      
      // 构建要发送到后端的数据
      const tagData = {
        tag: annotation.tag,
        x: normalizedX,
        y: normalizedY,
        width: normalizedWidth,
        height: normalizedHeight,
        updated_by: user.id
      }
      
      console.log('更新标注数据:', {
        id: annotation.dbId,
        归一化坐标: `(${normalizedX}, ${normalizedY})`,
        归一化尺寸: `${normalizedWidth} x ${normalizedHeight}`
      })
      
      // 使用API工具类更新标注
      api.tags.update(annotation.dbId, tagData)
        .then(response => {
          console.log('标注已更新', response.data)
          this.$message.success('标注已更新')
        })
        .catch(error => {
          console.error('更新标注失败', error)
          if (error.response) {
            console.error('服务器响应:', error.response.status, error.response.data)
            
            // 根据错误类型显示不同的提示
            if (error.response.status === 401) {
              this.$message.error('未授权，请先登录')
            } else if (error.response.status === 400) {
              this.$message.error(error.response.data || '提交的标注数据无效')
            } else {
              this.$message.error('更新标注失败')
            }
          } else {
            this.$message.error('更新标注失败，网络错误')
          }
        })
    },
    // 新增：处理标注数据的方法
    processLoadedAnnotations() {
      // 确保有尺寸和标注数据
      if (this.imageWidth <= 0 || this.imageHeight <= 0) {
        console.warn('图像尺寸无效，无法处理标注')
        return
      }
      
      if (!this.dbAnnotations || this.dbAnnotations.length === 0) {
        console.log('没有标注数据需要处理')
        return
      }
      
      console.log('处理已加载的标注数据，图像尺寸:', 
                 this.imageWidth + 'x' + this.imageHeight,
                 '标注数量:', this.dbAnnotations.length)
      
      // 清空现有标注
      this.annotations = []
      
      // 转换数据库标注为本地格式
      this.dbAnnotations.forEach(dbTag => {
        const annotation = {
          id: Date.now() + Math.random(), // 生成一个唯一ID
          dbId: dbTag.id, // 保存数据库ID
          imageIndex: this.currentImageIndex,
          tag: dbTag.tag,
          type: 'rectangle',
          // 将归一化坐标转换为像素坐标
          x: dbTag.x * this.imageWidth,
          y: dbTag.y * this.imageHeight,
          width: dbTag.width * this.imageWidth,
          height: dbTag.height * this.imageHeight
        }
        
        // 添加调试日志
        console.log('处理标注:', {
          标签: dbTag.tag,
          归一化坐标: `(${dbTag.x}, ${dbTag.y})`,
          归一化尺寸: `${dbTag.width} x ${dbTag.height}`,
          像素坐标: `(${annotation.x}, ${annotation.y})`,
          像素尺寸: `${annotation.width} x ${annotation.height}`
        })
        
        this.annotations.push(annotation)
      })
      
      this.annotationsLoaded = true
      console.log(`已处理${this.dbAnnotations.length}个标注，添加到图像${this.currentImageIndex + 1}`)
    },
    goBack() {
      // 提示用户可能丢失未保存的标注
      console.log('[导航跟踪] 点击返回按钮，当前URL:', window.location.href);
      
      this.$confirm('返回上传页面可能会丢失当前未保存的标注，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 设置返回工作台标记，防止401错误时跳转到登录页
        sessionStorage.setItem('returningToWorkbench', 'true');
        console.log('[导航跟踪] 用户确认返回，准备导航到: /app/cases/new');
        
        // 检查是否有图像对ID和已标注的图像
        if (this.imagePairId) {
          // 显示加载状态
          const loading = this.$loading({
            lock: true,
            text: '正在处理...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 使用新API直接删除标注图片并清空路径
          api.imagePairs.deleteAnnotatedImage(this.imagePairId)
            .then(response => {
              console.log('标注图片删除结果:', response.data);
              loading.close();
              // 用户确认后，返回到上传图片页面
              console.log('[导航跟踪] 导航到: /app/cases/new (API调用后)');
              
              // 记录导航前的信息
              const beforeUrl = window.location.href;
              
              this.$router.push('/app/cases/new').then(() => {
                console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);
              }).catch(err => {
                console.error('[导航跟踪] 导航失败:', err);
                // 备用方案，使用window.location
                window.location.href = '/app/cases/new';
              });
            })
            .catch(error => {
              console.error('删除标注图片失败', error);
              loading.close();
              // 即使失败也返回上一页
              this.$message.error('删除标注图片失败，但仍将返回上一页');
              
              console.log('[导航跟踪] 导航到: /app/cases/new (API调用失败后)');
              const beforeUrl = window.location.href;
              
              this.$router.push('/app/cases/new').then(() => {
                console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);
              }).catch(err => {
                console.error('[导航跟踪] 导航失败:', err);
                // 备用方案，使用window.location
                window.location.href = '/app/cases/new';
              });
            });
        } else {
          // 如果没有图像对ID，直接返回上一页
          console.log('[导航跟踪] 导航到: /app/cases/new (无图像对ID)');
          const beforeUrl = window.location.href;
          
          this.$router.push('/app/cases/new').then(() => {
            console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);
          }).catch(err => {
            console.error('[导航跟踪] 导航失败:', err);
            // 备用方案，使用window.location
            window.location.href = '/app/cases/new';
          });
        }
      }).catch(() => {
        // 用户取消，不做任何操作
        console.log('[导航跟踪] 用户取消返回操作');
      });
    },
    tryLoadImagePathFromMetadata(imageId, callback) {
      console.log('从image_metadata表获取图像路径:', imageId);
      
      api.images.getOne(imageId)
        .then(response => {
          if (response.data && response.data.path) {
            console.log('成功从image_metadata获取图像路径:', response.data.path);
            callback(response.data.path);
          } else {
            console.error('image_metadata中图像路径不存在');
            callback(null);
          }
        })
        .catch(error => {
          console.error('从image_metadata获取图像路径失败:', error);
          callback(null);
        });
    },
    updateImagePairPath(metadataId, path) {
      if (!this.imagePairId || !path) {
        console.error('缺少imagePairId或path，无法更新');
        return;
      }
      
      console.log('更新image_pairs表中的图像路径:', {
        imagePairId: this.imagePairId,
        metadataId: metadataId,
        path: path
      });
      
      // 构建要更新的数据
      const updateData = {
        id: this.imagePairId,
        metadataId: metadataId.toString(),
        imageOnePath: path
      };
      
      // 使用API更新image_pairs表
      fetch(`/api/image-pairs`, {
        method: 'POST',  // 使用POST方法
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(updateData),
        credentials: 'include'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(result => {
          console.log('成功更新image_pairs中的图像路径:', result);
        })
        .catch(error => {
          console.error('更新image_pairs中的图像路径失败:', error);
        });
    },
    // 加载图像标注
    loadAnnotations(imageId) {
      if (!imageId) {
        console.error('loadAnnotations: 图像ID无效，无法加载标注');
        return;
      }
      
      console.log('开始加载图像标注，ID:', imageId);
      
      // 获取当前用户
      const user = JSON.parse(localStorage.getItem('user')) || {};
      const userId = user.customId || user.id;
      
      if (!userId) {
        console.warn('未找到用户ID，标注加载可能存在权限问题');
      }
      
      // 构建请求参数
      const params = { userId };
      
      // 添加时间戳避免缓存问题
      params.t = new Date().getTime();
      
      // 使用API工具类获取标注，添加参数
      api.tags.getByImageId(imageId)
        .then(response => {
          console.log('成功获取图像标注数据:', response.data);
          
          // 确保返回的数据是数组
          this.dbAnnotations = response.data || [];
          
          // 如果返回的不是数组，记录错误并转换为空数组
          if (!Array.isArray(this.dbAnnotations)) {
            console.error('从服务器获取的标注数据不是数组格式:', this.dbAnnotations);
            this.dbAnnotations = [];
          }
          
          // 如果图像已加载并且有尺寸，立即处理标注
          if (this.imageWidth > 0 && this.imageHeight > 0) {
            this.processLoadedAnnotations();
          } else {
            console.log('图像尺寸尚未加载，将在图像加载后处理标注');
            // 标注数据已存储在this.dbAnnotations中，将在图像加载后处理
          }
        })
        .catch(error => {
          console.error('加载图像标注失败:', error);
          // 更详细地记录错误信息
          console.error('标注加载错误详情:', {
            图像ID: imageId,
            错误状态: error.response ? error.response.status : '网络错误',
            错误消息: error.response ? error.response.data : error.message,
            请求URL: error.config ? error.config.url : '未知',
            请求方法: error.config ? error.config.method : '未知'
          });
          
          // 如果是403错误，显示认证提示并尝试重新刷新认证
          if (error.response && error.response.status === 403) {
            this.$message.warning('获取标注失败：认证问题。请尝试重新登录后再试。');
            this.refreshAuthentication();
          }
        });
    },
    
    // 刷新认证信息
    refreshAuthentication() {
      // 尝试刷新用户认证信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user.id || user.customId) {
        console.log('尝试刷新用户认证信息');
        // 这里可以添加刷新token的逻辑，如果系统支持
      }
    },
    // 添加一个新方法用于尝试从localStorage加载离线数据
    tryLoadOfflineData(imageId) {
      console.log('尝试从localStorage加载离线数据...');
      
      // 尝试从localStorage获取图像数据
      const offlineImageData = localStorage.getItem(`offline_image_${imageId}`);
      if (offlineImageData) {
        console.log('找到离线图像数据');
        
        // 创建临时图像对象
        this.imageData = {
          id: imageId,
          path: offlineImageData
        };
        
        // 创建临时图像对
        this.imagePair = {
          id: Date.now(),
          metadataId: imageId,
          imageOnePath: offlineImageData
        };
        
        // 加载图像
        this.loadImage();
      } else {
        console.warn('未找到离线图像数据');
      }
    },
    // 初始化标注方法，修复未定义报错
    initializeAnnotations() {
      // 假设 this.tags 是后端返回的标注数组
      if (!this.tags || !Array.isArray(this.tags)) {
        console.warn('标签数据为空或格式不正确，无法初始化标注');
        return;
      }
      // 清空现有标注
      this.annotations = [];
      // 转换标签为本地标注格式
      this.tags.forEach(tag => {
        this.annotations.push({
          id: tag.id || (Date.now() + Math.random()),
          tag: tag.tag,
          x: tag.x * this.imageWidth,
          y: tag.y * this.imageHeight,
          width: tag.width * this.imageWidth,
          height: tag.height * this.imageHeight,
          imageIndex: this.currentImageIndex,
          type: 'rectangle'
        });
      });
      this.annotationsLoaded = true;
      console.log('已初始化标注', this.annotations);
    }
  },
  watch: {
    // 监听图像加载状态和物理路径，当两者都准备好时保存原始图像
    imageLoaded(newVal) {
      if (newVal && this.shouldSaveOriginalImage && this.processedFilePath) {
        this.saveOriginalImage();
      }
    }
  }
}
</script>

<style scoped>
.annotation-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 4px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.main-content {
  display: flex;
  gap: 20px;
}

.toolbar {
  width: 240px;
  flex-shrink: 0;
}

.tool-section {
  margin-bottom: 20px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.tool-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
}

.annotation-area {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.image-wrapper {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 100%;
  height: 600px;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.image-container {
  position: relative;
  display: inline-block;
  line-height: 0;
  font-size: 0;
}

.annotation-image {
  display: block;
  max-width: 100%;
  max-height: 600px;
  object-fit: contain;
  position: relative;
  z-index: 1;
}

.annotation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  cursor: crosshair;
  background-color: transparent;
  transform: none !important;
  transition: none !important;
  pointer-events: all;
}

.drawing-box {
  position: absolute;
  border: 2px dashed #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  pointer-events: none;
  z-index: 3;
}

.annotation-box {
  position: absolute;
  border: 2px solid;
  background-color: rgba(255, 255, 255, 0.1);
  z-index: 3;
  /* 允许交互 */
  pointer-events: all;
}

.annotation-label {
  position: absolute;
  top: -20px;
  left: -2px;
  padding: 2px 6px;
  color: white;
  font-size: 12px;
  border-radius: 2px 2px 0 0;
  pointer-events: none;
}

/* 调整大小的手柄样式 */
.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #fff;
  border: 1px solid #409eff;
  z-index: 4;
}

.resize-handle.top-left {
  top: -4px;
  left: -4px;
  cursor: nwse-resize;
}

.resize-handle.top-right {
  top: -4px;
  right: -4px;
  cursor: nesw-resize;
}

.resize-handle.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: nesw-resize;
}

.resize-handle.bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: nwse-resize;
}

.navigation-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.active {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
  color: #409eff;
}

.annotations-list {
  margin-top: 20px;
}
</style> 