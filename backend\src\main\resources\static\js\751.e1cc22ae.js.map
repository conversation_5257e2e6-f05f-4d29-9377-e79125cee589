{"version": 3, "file": "js/751.e1cc22ae.js", "mappings": "0VAsGO,SAASA,EAAaC,GAC3B,IAAMC,EAAY,MAAHC,OAASC,KAAKC,OAC7B,OAAIJ,EAAIK,SAAS,KACR,GAAPH,OAAUF,EAAG,KAAAE,OAAID,GAEV,GAAPC,OAAUF,EAAG,KAAAE,OAAID,EAErB,CC3FO,SAASK,EAAYN,GAC1B,IAAKA,EAAK,MAAO,GAKjB,GAHAO,QAAQC,IAAI,wBAAyBR,GAGjC,eAAeS,KAAKT,GAAM,CAC5BO,QAAQC,IAAI,2BACZ,IAAME,EAAcC,mBAAmBX,GAGjCY,EAAUC,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAiB,GAClD,OAAOf,EAAa,GAADG,OAAIU,EAAO,oCAAAV,OAAmCQ,GACnE,CAGA,GAAIV,EAAIe,WAAW,YAAcf,EAAIe,WAAW,YAC9C,OAAOhB,EAAaC,GAItB,GAAIA,EAAIe,WAAW,SACjB,OAAOf,EAIT,IAAIgB,EAAWhB,EAaf,OAXIA,EAAIe,WAAW,aACjBR,QAAQC,IAAI,mCAEZQ,EAAWH,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAYZ,OAAGF,GAAQA,GAC1CA,EAAIe,WAAW,OACxBR,QAAQC,IAAI,2CAEZQ,EAAWH,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAYZ,OAAGe,EAAAA,IAAgBf,OAAGF,GAAG,GAAAE,OAAQe,EAAAA,IAAgBf,OAAGF,IAG9FO,QAAQC,IAAI,yBAA0BQ,GAC/BjB,EAAaiB,EACtB,C,kECzDOE,MAAM,wB,GACJA,MAAM,e,GAONA,MAAM,gB,GAEJA,MAAM,W,GACJA,MAAM,gB,GAgBNA,MAAM,gB,GAONA,MAAM,oB,GAnCnBC,IAAA,EAyCuDD,MAAM,qB,GAYxCA,MAAM,e,GAQNA,MAAM,c,GAgBhBA,MAAM,mB,GACJA,MAAM,iB,GACJE,IAAI,sBAAsBF,MAAM,mB,EA/E/C,Q,EAAA,gB,EAAA,gB,EAAA,gB,EAAA,gB,EAAA,gB,GAiJSA,MAAM,gB,qSAhJbG,EAAAA,EAAAA,IAmJM,MAnJNC,EAmJM,EAlJJC,EAAAA,EAAAA,IAKM,MALNC,EAKM,cAJJD,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRE,EAAAA,EAAAA,IAEYC,EAAA,CAFDC,KAAK,UAAUC,KAAK,QAASC,QAAOC,EAAAC,a,CAJrD,SAAAC,EAAAA,EAAAA,KAKQ,iBAA8B,EAA9BP,EAAAA,EAAAA,IAA8BQ,EAAA,MALtC,SAAAD,EAAAA,EAAAA,KAKiB,iBAAW,EAAXP,EAAAA,EAAAA,IAAWS,G,IAL5BC,EAAA,I,aAAAC,EAAAA,EAAAA,IAKsC,W,IALtCD,EAAA,EAAAE,GAAA,K,kBASId,EAAAA,EAAAA,IAuIM,MAvINe,EAuIM,EArIJf,EAAAA,EAAAA,IA+DM,MA/DNgB,EA+DM,EA9DJhB,EAAAA,EAAAA,IAcM,MAdNiB,EAcM,cAbJjB,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRE,EAAAA,EAAAA,IAWYgB,EAAA,CAzBtBC,WAc8BC,EAAAC,YAd9B,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAc8BH,EAAAC,YAAWE,CAAA,GAAEC,YAAY,UAAUC,MAAA,gB,CAdjE,SAAAhB,EAAAA,EAAAA,KAeY,iBAA2D,EAA3DP,EAAAA,EAAAA,IAA2DwB,EAAA,CAAhDC,MAAM,YAAYC,MAAM,eACnC1B,EAAAA,EAAAA,IAAyEwB,EAAA,CAA9DC,MAAM,mBAAmBC,MAAM,sBAC1C1B,EAAAA,EAAAA,IAAyEwB,EAAA,CAA9DC,MAAM,mBAAmBC,MAAM,sBAC1C1B,EAAAA,EAAAA,IAAuEwB,EAAA,CAA5DC,MAAM,kBAAkBC,MAAM,qBACzC1B,EAAAA,EAAAA,IAAuEwB,EAAA,CAA5DC,MAAM,kBAAkBC,MAAM,qBACzC1B,EAAAA,EAAAA,IAA2DwB,EAAA,CAAhDC,MAAM,YAAYC,MAAM,eACnC1B,EAAAA,EAAAA,IAA6DwB,EAAA,CAAlDC,MAAM,aAAaC,MAAM,gBACpC1B,EAAAA,EAAAA,IAA2DwB,EAAA,CAAhDC,MAAM,YAAYC,MAAM,eACnC1B,EAAAA,EAAAA,IAAuDwB,EAAA,CAA5CC,MAAM,UAAUC,MAAM,aACjC1B,EAAAA,EAAAA,IAA2DwB,EAAA,CAAhDC,MAAM,YAAYC,MAAM,c,IAxB/ChB,EAAA,G,qBA4BQZ,EAAAA,EAAAA,IAKM,MALN6B,EAKM,gBAJJ7B,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRE,EAAAA,EAAAA,IAEiB4B,EAAA,CAhC3BX,WA8BmCC,EAAAW,YA9BnC,sBAAAT,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA8BmCH,EAAAW,YAAWR,CAAA,GAAEE,MAAA,uC,CA9BhD,SAAAhB,EAAAA,EAAAA,KA+BY,iBAA0C,EAA1CP,EAAAA,EAAAA,IAA0C8B,EAAA,CAAhCL,MAAM,aAAW,CA/BvC,SAAAlB,EAAAA,EAAAA,KA+BwC,kBAAGa,EAAA,MAAAA,EAAA,MA/B3CT,EAAAA,EAAAA,IA+BwC,Q,IA/BxCD,EAAA,EAAAE,GAAA,O,IAAAF,EAAA,G,qBAmCQZ,EAAAA,EAAAA,IAsCM,MAtCNiC,EAsCM,EArCJjC,EAAAA,EAAAA,IAIK,0BAxCfa,EAAAA,EAAAA,IAoCc,YACFX,EAAAA,EAAAA,IAEYC,EAAA,CAFDC,KAAK,OAAOC,KAAK,QAASC,QAAOC,EAAA2B,kBAAmBC,MAAM,Q,CArCjF,SAAA1B,EAAAA,EAAAA,KAsCc,iBAA8B,EAA9BP,EAAAA,EAAAA,IAA8BQ,EAAA,MAtC5C,SAAAD,EAAAA,EAAAA,KAsCuB,iBAAW,EAAXP,EAAAA,EAAAA,IAAWS,G,IAtClCC,EAAA,I,IAAAA,EAAA,G,iBAyCoD,IAA/BL,EAAA6B,oBAAoBC,SAAM,WAArCvC,EAAAA,EAAAA,IAEM,MAFNwC,EAEMhB,EAAA,MAAAA,EAAA,MADJtB,EAAAA,EAAAA,IAA0B,SAAvB,uBAAmB,oBAExBuC,EAAAA,EAAAA,IA4BWC,EAAA,CAxErB5C,IAAA,EA4C4B6C,KAAMlC,EAAA6B,oBAAqBX,MAAA,gB,CA5CvD,SAAAhB,EAAAA,EAAAA,KA6CY,iBAIkB,EAJlBP,EAAAA,EAAAA,IAIkBwC,EAAA,CAJDf,MAAM,KAAKgB,MAAM,M,CACrBC,SAAOnC,EAAAA,EAAAA,KAChB,SAAsBoC,GADC,QA9CvChC,EAAAA,EAAAA,KAAAiC,EAAAA,EAAAA,IA+CmBD,EAAME,OAAS,GAAH,G,IA/C/BnC,EAAA,KAkDYV,EAAAA,EAAAA,IAAoDwC,EAAA,CAAnCM,KAAK,MAAMrB,MAAM,KAAKgB,MAAM,QAC7CzC,EAAAA,EAAAA,IAOkBwC,EAAA,CAPDf,MAAM,KAAKgB,MAAM,O,CACrBC,SAAOnC,EAAAA,EAAAA,KAChB,SAGMoC,GAJiB,QACvB7C,EAAAA,EAAAA,IAGM,MAHNiD,EAGM,EAFJjD,EAAAA,EAAAA,IAA+C,aAAxC,OAAG8C,EAAAA,EAAAA,IAAGI,KAAKC,MAAMN,EAAMO,IAAIC,IAAC,kBAAYrD,EAAAA,EAAAA,IAAI,qBACnDA,EAAAA,EAAAA,IAA+C,aAAxC,OAAG8C,EAAAA,EAAAA,IAAGI,KAAKC,MAAMN,EAAMO,IAAIE,IAAC,K,IAvDrD1C,EAAA,KA2DYV,EAAAA,EAAAA,IAOkBwC,EAAA,CAPDf,MAAM,KAAKgB,MAAM,O,CACrBC,SAAOnC,EAAAA,EAAAA,KAChB,SAGMoC,GAJiB,QACvB7C,EAAAA,EAAAA,IAGM,MAHNuD,EAGM,EAFJvD,EAAAA,EAAAA,IAAmD,aAA5C,OAAG8C,EAAAA,EAAAA,IAAGI,KAAKC,MAAMN,EAAMO,IAAIT,QAAK,kBAAY3C,EAAAA,EAAAA,IAAI,qBACvDA,EAAAA,EAAAA,IAAoD,aAA7C,OAAG8C,EAAAA,EAAAA,IAAGI,KAAKC,MAAMN,EAAMO,IAAII,SAAM,K,IA/D1D5C,EAAA,KAmEYV,EAAAA,EAAAA,IAIkBwC,EAAA,CAJDf,MAAM,KAAKgB,MAAM,M,CACrBC,SAAOnC,EAAAA,EAAAA,KAChB,SAA4FoC,GADrE,QACvB3C,EAAAA,EAAAA,IAA4FC,EAAA,CAAjFC,KAAK,SAASC,KAAK,QAASC,QAAK,SAAAiB,GAAA,OAAEhB,EAAAkD,iBAAiBZ,EAAMO,IAAIM,GAAE,G,CArE3F,SAAAjD,EAAAA,EAAAA,KAqE8F,kBAAEa,EAAA,MAAAA,EAAA,MArEhGT,EAAAA,EAAAA,IAqE8F,O,IArE9FD,EAAA,EAAAE,GAAA,M,sBAAAF,EAAA,I,IAAAA,EAAA,G,kBA6EMZ,EAAAA,EAAAA,IAkEM,MAlEN2D,EAkEM,EAjEJ3D,EAAAA,EAAAA,IAgEM,MAhEN4D,EAgEM,EA/DJ5D,EAAAA,EAAAA,IA8DM,MA9DN6D,EA8DM,CA3DIzC,EAAA0C,eAAY,WADpBhE,EAAAA,EAAAA,IAOE,OAxFdF,IAAA,EAmFcC,IAAI,kBACHkE,IAAKxD,EAAAxB,YAAYqC,EAAA0C,aAAarF,KAC/BkB,MAAM,mBACNqE,IAAI,OACHC,OAAI3C,EAAA,KAAAA,EAAA,qBAAEf,EAAA2D,iBAAA3D,EAAA2D,gBAAAC,MAAA5D,EAAA6D,UAAe,I,QAvFpCC,KAAAC,EAAAA,EAAAA,IAAA,OA4FoBlD,EAAA0C,eAAY,WADpBhE,EAAAA,EAAAA,IAWO,OAtGnBF,IAAA,EA6FcD,MAAM,qBACL8B,OA9Ff8C,EAAAA,EAAAA,IAAA,C,MA8F+CnD,EAAAoD,WAAU,K,OAAiCpD,EAAAqD,YAAW,OAItFC,YAASpD,EAAA,KAAAA,EAAA,qBAAEf,EAAAoE,cAAApE,EAAAoE,aAAAR,MAAA5D,EAAA6D,UAAY,GACvBQ,YAAStD,EAAA,KAAAA,EAAA,qBAAEf,EAAAsE,WAAAtE,EAAAsE,UAAAV,MAAA5D,EAAA6D,UAAS,GACpBU,UAAOxD,EAAA,KAAAA,EAAA,qBAAEf,EAAAwE,YAAAxE,EAAAwE,WAAAZ,MAAA5D,EAAA6D,UAAU,GACnBY,aAAU1D,EAAA,KAAAA,EAAA,qBAAEf,EAAA0E,eAAA1E,EAAA0E,cAAAd,MAAA5D,EAAA6D,UAAa,I,WArGxCE,EAAAA,EAAAA,IAAA,sBAyGYxE,EAAAA,EAAAA,IAuBMoF,EAAAA,GAAA,MAhIlBC,EAAAA,EAAAA,IA0GqC5E,EAAA6B,qBA1GrC,SA0GsBgD,EAAKC,G,kBADfvF,EAAAA,EAAAA,IAuBM,OArBHF,IAAKyF,EACN1F,MAAM,iBACL8B,OA7Gf8C,EAAAA,EAAAA,IAAA,C,KA6G8Ca,EAAI/B,EAAC,K,IAA8B+B,EAAI9B,EAAC,K,MAAgC8B,EAAIzC,MAAK,K,OAAiCyC,EAAI5B,OAAM,K,YAAsCjD,EAAA+E,YAAYF,EAAIG,K,OAA8BC,EAAAC,cAAgBD,EAAAE,eAAiBN,EAAI1B,GAAE,mBAQtRgB,aArHfiB,EAAAA,EAAAA,KAAA,SAAApE,GAAA,OAqH+BhB,EAAAqF,aAAarE,EAAQ6D,EAAI1B,GAAE,c,EAE5C1D,EAAAA,EAAAA,IAEO,QAFDL,MAAM,mBAAoB8B,OAvH9C8C,EAAAA,EAAAA,IAAA,CAAAsB,gBAuHwEtF,EAAA+E,YAAYF,EAAIG,S,QACrEH,EAAIG,KAAG,IAIZvF,EAAAA,EAAAA,IAAuG,OAAlGL,MAAM,yBAA0B+E,aA5HnDiB,EAAAA,EAAAA,KAAA,SAAApE,GAAA,OA4HmEhB,EAAAuF,eAAevE,EAAQ6D,EAAI1B,GAAI,WAAF,c,QA5HhGqC,IA6Hc/F,EAAAA,EAAAA,IAAyG,OAApGL,MAAM,0BAA2B+E,aA7HpDiB,EAAAA,EAAAA,KAAA,SAAApE,GAAA,OA6HoEhB,EAAAuF,eAAevE,EAAQ6D,EAAI1B,GAAI,YAAF,c,QA7HjGsC,IA8HchG,EAAAA,EAAAA,IAA6G,OAAxGL,MAAM,4BAA6B+E,aA9HtDiB,EAAAA,EAAAA,KAAA,SAAApE,GAAA,OA8HsEhB,EAAAuF,eAAevE,EAAQ6D,EAAI1B,GAAI,cAAF,c,QA9HnGuC,IA+HcjG,EAAAA,EAAAA,IAA+G,OAA1GL,MAAM,6BAA8B+E,aA/HvDiB,EAAAA,EAAAA,KAAA,SAAApE,GAAA,OA+HuEhB,EAAAuF,eAAevE,EAAQ6D,EAAI1B,GAAI,eAAF,c,QA/HpGwC,IAAA,GAAAC,E,UAoIoB/E,EAAAgF,YAAS,WADjBtG,EAAAA,EAAAA,IASO,OA5InBF,IAAA,EAqIcD,MAAM,cACL8B,OAtIf8C,EAAAA,EAAAA,IAAA,C,KAsI8CrB,KAAKmD,IAAIjF,EAAAkF,UAAUjD,EAAGjC,EAAAmF,YAAYlD,GAAC,K,IAA+BH,KAAKmD,IAAIjF,EAAAkF,UAAUhD,EAAGlC,EAAAmF,YAAYjD,GAAC,K,MAAiCJ,KAAKsD,IAAIpF,EAAAmF,YAAYlD,EAAIjC,EAAAkF,UAAUjD,GAAC,K,OAAkCH,KAAKsD,IAAIpF,EAAAmF,YAAYjD,EAAIlC,EAAAkF,UAAUhD,GAAC,Q,UAtI9RgB,EAAAA,EAAAA,IAAA,oBAiJItE,EAAAA,EAAAA,IAEM,MAFNyG,EAEM,EADJvG,EAAAA,EAAAA,IAA2BwG,M,2cAc3BC,EAAS,CACbC,aAAc,CACZC,SAAU,OACVC,MAAO,QACPC,UAAW,YACXC,sBAAuB,wBACvBC,iBAAkB,qBAClBC,eAAgB,oBAElBC,OAAQ,CACNC,eAAgB,8BAElBC,GAAI,CACFC,gBAAiB,MAIrB,SACEC,KAAM,iBACNC,WAAY,CACVC,QAAAA,EAAAA,SAEFhF,KAAI,WACF,MAAO,CACLiF,QAASC,KAAKC,OAAOC,OAAOnE,IAAMiE,KAAKC,OAAOE,MAAMJ,SAAW,KAC/D5D,aAAc,KACdzC,YAAa,YACbU,YAAa,YACbgG,SAAU,CAERC,YAAa,GACbC,YAAa,GACbC,WAAY,GACZC,cAAe,GACfC,UAAW,GACXC,cAAe,GACfC,MAAO,IAETC,cAAe,GACfC,eAAgB,GAChBC,kBAAmB,EACnBC,YAAa,GACblE,WAAY,EACZC,YAAa,EACbkE,WAAY,KACZvC,WAAW,EACXwC,mBAAoB,KACpBC,UAAU,EACVC,YAAY,EACZC,aAAc,KACdC,WAAY,EACZC,WAAY,EACZ3C,UAAW,CAAEjD,EAAG,EAAGC,EAAG,GACtBiD,YAAa,CAAElD,EAAG,EAAGC,EAAG,GAExB4F,oBAAoB,EACpBC,kBAAmB,KACnBC,UAAW,EACXC,UAAW,CAAEhG,EAAG,EAAGC,EAAG,GACtBgG,WAAW,EACXC,aAAc,CAAElG,EAAG,EAAGC,EAAG,GACzBkG,mBAAmB,EACnBC,yBAAyB,EACzBC,aAAa,EACbC,qBAAqB,EACrBC,cAAe,EACfC,eAAgB,EAChBC,OAAQ,EACRC,OAAQ,EACRC,UAAU,EACVC,sBAAsB,EAEtBC,eAAgB,KAEpB,EACAC,SAAU,CACR/H,oBAAmB,WAAG,IAAAgI,EAAA,KACpB,OAAOzC,KAAKe,YAAY2B,QAAO,SAAAC,GAAA,OAAKA,EAAEC,aAAeH,EAAK3B,iBAAiB,GAC7E,GAEF+B,MAAO,CAELjC,cAAe,CACbkC,QAAO,SAACC,GACF/C,KAAK+B,aAAegB,GAAkBA,EAAerI,OAAS,IAAMsF,KAAKsC,uBAC3EjL,QAAQC,IAAI,0BACZ0I,KAAKgD,2BAET,EACAC,MAAM,EACNC,WAAW,GAGbnB,YAAa,CACXe,QAAO,SAACK,GACFA,GAAUnD,KAAKY,eAAiBZ,KAAKY,cAAclG,OAAS,IAAMsF,KAAKsC,uBACzEjL,QAAQC,IAAI,4BACZ0I,KAAKgD,2BAET,EACAE,WAAW,IAGfE,QAAO,WAELC,OAAOC,iBAAiB,SAAUtD,KAAKuD,aACzC,EACAC,QAAO,WAAG,IAAAC,EAAA,KACRpM,QAAQC,IAAI,iCAAkC0I,KAAKC,OAAOE,MAAO,QAASH,KAAKC,OAAOC,QAGtF,IAAIH,EAAUC,KAAKC,OAAOC,OAAOnE,IAAMiE,KAAKC,OAAOE,MAAMJ,SAAWC,KAAKC,OAAOE,MAAMuD,aAAe,GAOrG,GANArM,QAAQC,IAAI,UAAWyI,EAAS,OAAK4D,EAAAA,EAAAA,GAAS5D,IAG9CA,EAAU6D,OAAO7D,GAGZA,GAAuB,cAAZA,GAAuC,SAAZA,EAA3C,CAMAC,KAAKD,QAAUA,EACf1I,QAAQC,IAAI,YAAa0I,KAAKD,QAAS,OAAK4D,EAAAA,EAAAA,GAAS3D,KAAKD,UAG1D,IAAM8D,EAAU7D,KAAK8D,SAAS,CAC5BC,MAAM,EACNC,KAAM,iBACNC,QAAS,kBACTC,WAAY,uBAKZC,eAAeC,QAAQ,iBAAkB,QAG3C,IAAIC,EAAa,EACXC,EAAa,EAEbC,EAAoB,WACxBlN,QAAQC,IAAI,cAADN,OAAeqN,EAAa,EAAC,SAGxCZ,EAAKe,aAAazE,GACf0E,MAAK,WAGJ,OAFApN,QAAQC,IAAI,YAELmM,EAAKiB,gBAAgB3E,EAC9B,IACC0E,MAAK,WACJpN,QAAQC,IAAI,aACZuM,EAAQc,OACV,IAAC,UACM,SAAAC,GACLvN,QAAQuN,MAAM,KAAD5N,OAAMqN,EAAa,EAAC,aAAaO,GAE1CP,EAAaC,GACfD,IACAhN,QAAQC,IAAI,GAADN,OAAiB,EAAbqN,EAAc,YAG7BQ,WAAWN,EAAgC,IAAbF,KAE9BhN,QAAQuN,MAAM,iBACdnB,EAAKqB,SAASF,MAAM,kBACpBf,EAAQc,QAEZ,GACJ,EAGAJ,IAGAQ,SAASzB,iBAAiB,YAAatD,KAAKgF,uBAC5CD,SAASzB,iBAAiB,UAAWtD,KAAKiF,qBAG1C5B,OAAOC,iBAAiB,SAAUtD,KAAKuD,aA7DvC,MAFEvD,KAAK8E,SAASI,QAAQ,yBAgE1B,EACAC,cAAa,WAEX9B,OAAO+B,oBAAoB,SAAUpF,KAAKuD,cAG1CY,eAAekB,WAAW,yBAC1BlB,eAAekB,WAAW,uBAE5B,EACAC,SAAOC,EAAA,CAELC,cAAa,SAACzJ,GACZ,IAAKA,EAAI,OAAO,KAEhB,IAAM0J,EAAQ1J,EAAG2J,WAEjB,OAAID,EAAM/K,OAAS,GACjBrD,QAAQC,IAAI,UAADN,OAAWyO,EAAK,QAAAzO,OAAOyO,EAAME,UAAUF,EAAM/K,OAAS,KAC1D+K,EAAME,UAAUF,EAAM/K,OAAS,IAEjC+K,CACT,EAGArO,YAAW,SAACN,GACV,IAAKA,EAAK,MAAO,GAGjB,GAAIA,EAAIe,WAAW,QACjB,OAAOf,EAIT,IAAM8O,EAAc9O,EAChBgB,EAAWhB,EAGf,GAAIA,EAAIe,WAAW,KAAM,CAEvB,IAAMH,EAAU2L,OAAOwC,SAASC,OAChChO,EAAWJ,EAAUZ,CACvB,CAGA,IAAMiP,EAAU,KAAA/O,OAASC,KAAKC,MAAK,OAAAF,OAAMuE,KAAKyK,UAQ9C,OANElO,EADEA,EAASX,SAAS,KACb,GAAAH,OAAOc,EAAQ,KAAAd,OAAI+O,GAEnB,GAAA/O,OAAOc,EAAQ,KAAAd,OAAI+O,GAG5B1O,QAAQC,IAAI,YAADN,OAAa4O,EAAW,QAAA5O,OAAOc,IACnCA,CACT,EAGMmO,cAAa,WAAG,IAAAC,EAAA,YAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAvP,EAAAiP,EAAAO,EAAAC,EAAA,OAAAJ,EAAAA,EAAAA,KAAAK,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,UACfT,EAAKnG,QAAS,CAAF2G,EAAAC,EAAA,QACsB,OAArCtP,QAAQuN,MAAM,wBAAuB8B,EAAA/D,EAAA,UAST,OATS+D,EAAAE,EAAA,EAKrCvP,QAAQC,IAAI,8BAADN,OAA+BkP,EAAKnG,UAGzChJ,EAAYE,KAAKC,MACjB8O,EAASzK,KAAKyK,SAAQU,EAAAC,EAAA,EAELE,EAAAA,WAAIC,IAAI,WAAD9P,OAAYkP,EAAKnG,QAAO,OAAA/I,OAAMD,EAAS,OAAAC,OAAMgP,GAAU,CACnFe,QAAS,CACP,gBAAiB,qBACjB,OAAU,WACV,QAAW,OAEb,OANIR,EAAOG,EAAAM,EAQTT,EAASzL,MACXoL,EAAK/J,aAAeoK,EAASzL,KAC7BzD,QAAQC,IAAI,qBAAsB4O,EAAK/J,cAGvC+J,EAAKxB,mBAELrN,QAAQuN,MAAM,qDAChB8B,EAAAC,EAAA,eAAAD,EAAAE,EAAA,EAAAJ,EAAAE,EAAAM,EAEA3P,QAAQuN,MAAM,6BAA4B4B,GAAQ,cAAAE,EAAA/D,EAAA,MAAA2D,EAAA,iBA/BhCH,EAiCtB,EAGMzB,gBAAe,SAAC3E,GAAS,IAAAkH,EAAA,YAAAd,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAa,IAAA,IAAAnL,EAAAuI,EAAA6C,EAAAC,EAAA,OAAAhB,EAAAA,EAAAA,KAAAK,GAAA,SAAAY,GAAA,eAAAA,EAAAV,GAAA,UACxB5G,EAAS,CAAFsH,EAAAV,EAAA,QACoB,OAA9BtP,QAAQuN,MAAM,iBAAgByC,EAAA1E,EAAA,EACvB2E,QAAQC,OAAO,WAAS,OA6HjC,OA1HMxL,EAAKgE,GAAWkH,EAAKlH,QAC3B1I,QAAQC,IAAI,UAAWyE,EAAI,SAGrBuI,EAAa,EACf6C,EAAU,EAERK,EAAiB,eAAAC,GAAAtB,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAqB,IAAA,IAAA3Q,EAAAiP,EAAA2B,EAAAC,EAAAC,EAAAC,EAAAvB,EAAAzL,EAAAiN,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAArC,EAAAA,EAAAA,KAAAK,GAAA,SAAAiC,GAAA,eAAAA,EAAA/B,GAAA,OAuBvB,OAvBuB+B,EAAA9B,EAAA,EAGjB7P,GAAY,IAAIE,MAAO0R,UACvB3C,EAASzK,KAAKyK,SAGd2B,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDnB,EAASD,EAAKqB,UAAYrB,EAAK5L,IAAM,GAGrC8L,EAAgBZ,EAAKhH,OAAOE,MAAMuD,cAAgB3H,EAGpD+L,EAAK,2BAAA9Q,OAA+B+E,EAAE,OAAA/E,OAAMD,EAAS,OAAAC,OAAMgP,EAAM,qCAAAhP,OAAoC4Q,GAGzGC,IACEC,EAAK,qCAAA9Q,OAAyC+E,EAAE,YAAA/E,OAAWD,EAAS,OAAAC,OAAMgP,IAG5E3O,QAAQC,IAAI,cAADN,OAAemQ,EAAU,EAAC,cAAAnQ,OAAa8Q,IAElDY,EAAA/B,EAAA,EACuBsC,MAAMnB,GAAO,OAAvB,GAAPvB,EAAOmC,EAAA1B,EAERT,EAAS2C,GAAI,CAAFR,EAAA/B,EAAA,cACR,IAAIwC,MAAM,WAADnS,OAAYuP,EAAS6C,SAAS,cAAAV,EAAA/B,EAAA,EAG5BJ,EAAS8C,OAAM,OAY9B,OAZEvO,EAAG4N,EAAA1B,EACT3P,QAAQC,IAAI,OAADN,OAAQ8D,EAAKJ,OAAM,WAAWI,GAGzCmM,EAAKrG,cAAgB9F,GAAQ,GAGrBmM,EAAKpK,WAAa,GAAKoK,EAAKnK,YAAc,EAChDmK,EAAKjE,2BAGDiE,EAAKpF,mBAAoB,EAC3B6G,EAAA/F,EAAA,EAEG7H,GAAI,OAIX,GAJW4N,EAAA9B,EAAA,EAAA4B,EAAAE,EAAA1B,EAEX3P,QAAQuN,MAAM,cAAD5N,OAAemQ,EAAU,EAAC,SAAAqB,KAGnCrB,EAAU7C,GAAU,CAAAoE,EAAA/B,EAAA,QAGgB,OAFtCQ,IACMY,EAAqB,IAAVZ,EACjB9P,QAAQC,IAAI,MAADN,OAAO+Q,EAAQ,cAAYW,EAAA/F,EAAA,EAE/B,IAAI2E,SAAQ,SAACgC,EAAS/B,GAC3B1C,YAAUsB,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAC,SAAAkD,IAAA,IAAAC,EAAAC,EAAA,OAAArD,EAAAA,EAAAA,KAAAK,GAAA,SAAAiD,GAAA,eAAAA,EAAA/C,GAAA,cAAA+C,EAAA9C,EAAA,EAAA8C,EAAA/C,EAAA,EAEca,IAAoB,OAAnCgC,EAAKE,EAAA1C,EACXsC,EAAQE,GAAOE,EAAA/C,EAAA,eAAA+C,EAAA9C,EAAA,EAAA6C,EAAAC,EAAA1C,EAEfO,EAAMkC,GAAY,cAAAC,EAAA/G,EAAA,MAAA4G,EAAA,kBAEnBxB,EACL,KAAE,OAsBoC,OAtBpCW,EAAA9B,EAAA,EAKFvP,QAAQC,IAAI,kBACNP,GAAY,IAAIE,MAAO0R,UAC3B3C,EAASzK,KAAKyK,SACV2B,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDnB,EAASD,EAAKqB,UAAYrB,EAAK5L,IAAM,GAGrC8L,EAAgBZ,EAAKhH,OAAOE,MAAMuD,cAAgB3H,EAGpDsM,EAAQ,mBAAArR,OAAuB+E,EAAE,OAAA/E,OAAMD,EAAS,OAAAC,OAAMgP,EAAM,YAAAhP,OAAW4Q,GAGvEC,IACFQ,EAAQ,6BAAArR,OAAiC+E,EAAE,YAAA/E,OAAWD,EAAS,OAAAC,OAAMgP,IAGvE3O,QAAQC,IAAI,cAADN,OAAeqR,IAAYK,EAAA/B,EAAA,EACfsC,MAAMZ,GAAU,OAA1B,GAAP9B,EAAOmC,EAAA1B,EAERT,EAAS2C,GAAI,CAAFR,EAAA/B,EAAA,cACR,IAAIwC,MAAM,aAADnS,OAAcuP,EAAS6C,SAAS,cAAAV,EAAA/B,EAAA,EAG9BJ,EAAS8C,OAAM,OAYlC,OAZMvO,EAAG4N,EAAA1B,EACT3P,QAAQC,IAAI,cAADN,OAAe8D,EAAKJ,OAAM,WAAWI,GAGhDmM,EAAKrG,cAAgB9F,GAAQ,GAGzBmM,EAAKpK,WAAa,GAAKoK,EAAKnK,YAAc,EAC1CmK,EAAKjE,2BAGPiE,EAAKpF,mBAAoB,EAC3B6G,EAAA/F,EAAA,EAEO7H,GAAI,OAG8B,OAH9B4N,EAAA9B,EAAA,EAAA6B,EAAAC,EAAA1B,EAEX3P,QAAQuN,MAAM,cAAa6D,GAC3BxB,EAAKnC,SAASF,MAAM,sBAAqB8D,EAAA/F,EAAA,EAClC2E,QAAQC,OAAMkB,IAAa,GAAAf,EAAA,wBAGvC,kBAjHsB,OAAAD,EAAAjL,MAAA,KAAAC,UAAA,KAmHvB4K,EAAA1E,EAAA,EACO6E,KAAoB,GAAAN,EAAA,IAjIEf,EAkI/B,EAGAwD,sBAAqB,SAACC,GAAM,IAAAC,EAAA,KAG1B,GAFAxS,QAAQC,IAAI,aAAcsS,GAErBA,GAASA,EAAKlP,QAAWsF,KAAKnD,YAAemD,KAAKlD,YAAvD,CASA,IAAMgN,EAAuBF,EAAKG,KAAI,SAACnM,EAAKF,GAE1C,IAAMsM,EACJpM,EAAIlC,GAAK,GAAKkC,EAAIlC,GAAK,GACvBkC,EAAIjC,GAAK,GAAKiC,EAAIjC,GAAK,GACvBiC,EAAI5C,OAAS,GAAK4C,EAAI5C,OAAS,GAC/B4C,EAAI/B,QAAU,GAAK+B,EAAI/B,QAAU,EAG7BH,EAAIsO,EAAwBpM,EAAIlC,EAAImO,EAAKhN,WAAae,EAAIlC,EAC1DC,EAAIqO,EAAwBpM,EAAIjC,EAAIkO,EAAK/M,YAAcc,EAAIjC,EAC3DX,EAAQgP,EAAwBpM,EAAI5C,MAAQ6O,EAAKhN,WAAae,EAAI5C,MAClEa,EAASmO,EAAwBpM,EAAI/B,OAASgO,EAAK/M,YAAcc,EAAI/B,OAE3E,MAAO,CACLE,GAAI,GAAF/E,OAAK0G,EAAQ,GACfuM,SAAUrM,EAAI7B,IAAM,KACpB6B,IAAKA,EAAIA,KAAOA,EAAIsM,SAAW,YAC/BxO,EAAGA,EACHC,EAAGA,EACHX,MAAOA,EACPa,OAAQA,EACRsO,WAAYvM,EAAIuM,YAAc,KAC9B1R,KAAM,YAEV,IAEApB,QAAQC,IAAI,YAAawS,GACzB9J,KAAKe,YAAc+I,CA9BnB,MANEzS,QAAQ+S,KAAK,mBAAoB,CAC/BC,WAAYT,EAAOA,EAAKlP,OAAS,EACjCmC,WAAYmD,KAAKnD,WACjBC,YAAakD,KAAKlD,aAkCxB,EAGAwN,YAAW,SAACV,GAAM,IAAAW,EAAA,KAGhB,GAFAlT,QAAQC,IAAI,YAAasS,GAEpBA,GAASA,EAAKlP,QAAWsF,KAAKnD,YAAemD,KAAKlD,YAAvD,CAKA,IAAMgN,EAAuBF,EAAKG,KAAI,SAACnM,EAAKF,GAE1C,IAAMsM,EACJpM,EAAIlC,GAAK,GAAKkC,EAAIlC,GAAK,GACvBkC,EAAIjC,GAAK,GAAKiC,EAAIjC,GAAK,GACvBiC,EAAI5C,OAAS,GAAK4C,EAAI5C,OAAS,GAC/B4C,EAAI/B,QAAU,GAAK+B,EAAI/B,QAAU,EAG7BH,EAAIsO,EAAwBpM,EAAIlC,EAAI6O,EAAK1N,WAAae,EAAIlC,EAC1DC,EAAIqO,EAAwBpM,EAAIjC,EAAI4O,EAAKzN,YAAcc,EAAIjC,EAC3DX,EAAQgP,EAAwBpM,EAAI5C,MAAQuP,EAAK1N,WAAae,EAAI5C,MAClEa,EAASmO,EAAwBpM,EAAI/B,OAAS0O,EAAKzN,YAAcc,EAAI/B,OAE3E,MAAO,CACLE,GAAI6B,EAAI7B,IAAC,GAAA/E,OAAQ0G,EAAQ,GACzBE,IAAKA,EAAIA,KAAOA,EAAIsM,SAAW,YAC/BxO,EAAGA,EACHC,EAAGA,EACHX,MAAOA,EACPa,OAAQA,EACRsO,WAAYvM,EAAIuM,YAAc,KAC9B1R,KAAM,YAEV,IAEApB,QAAQC,IAAI,YAAawS,GACzB9J,KAAKe,YAAc+I,CA7Bf,MAFFzS,QAAQ+S,KAAK,mBAgCjB,EAGMvR,YAAW,WAAG,IAAA2R,EAAA,YAAArE,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAoE,IAAA,OAAArE,EAAAA,EAAAA,KAAAK,GAAA,SAAAiE,GAAA,eAAAA,EAAA/D,GAAA,OAMlB,OAJA6D,EAAK5J,cAAgB,GACrB4J,EAAKzJ,YAAc,GACnByJ,EAAK3I,mBAAoB,EAEzB6I,EAAA/D,EAAA,EACM6D,EAAKvE,gBAAe,OAG1BuE,EAAK1F,SAAS,CACZ6F,QAAS,QACTlS,KAAM,UACNmS,SAAU,MACV,cAAAF,EAAA/H,EAAA,MAAA8H,EAAA,IAdgBtE,EAepB,EAGA0E,mBAAkB,SAAC9K,GAAS,IAAA+K,EAAA,KAC1BzT,QAAQC,IAAI,6BAA8ByI,GAG1C,IAAM4H,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,UAAY,CAAC,EACpDnB,EAASD,EAAKqB,UAAYrB,EAAK5L,GAEhC6L,GACHvQ,QAAQ+S,KAAK,oBAIf,IAAMlK,EAAS,CAAE0H,OAAAA,GAGjB1H,EAAO6K,GAAI,IAAI9T,MAAO0R,UAGtB9B,EAAAA,WAAImE,WAAWC,gBAAgBlL,GAC5B0E,MAAK,SAAA8B,GACJlP,QAAQC,IAAI,mBAAoBiP,GAChC,IAAMzL,EAAOyL,EAASzL,KAEtB,GAAIA,GAAQA,EAAKJ,OAAS,EAAG,CAC3B,IAAMwQ,EAAYpQ,EAAK,GACvBzD,QAAQC,IAAI,oBAAqB4T,GACjCJ,EAAKK,YAAcD,EAAUnP,GAG7B,IAAMqP,EAAqBF,EAAUG,cAAgBH,EAAUI,eAe/D,GAdAjU,QAAQC,IAAI,WAAY8T,IAGnBA,GAAsBF,EAAUK,eACnClU,QAAQC,IAAI,qBAAsB4T,EAAUK,eAC5CT,EAAKU,kBAAoBN,EAAUK,gBACzBH,GAAsBF,EAAUpU,KAC1CO,QAAQC,IAAI,aAAc4T,EAAUpU,KACpCgU,EAAKU,kBAAoBN,EAAUpU,KAEnCgU,EAAKU,kBAAoBJ,GAItBN,EAAKU,kBAmBR,OAlBAnU,QAAQC,IAAI,iDACZwT,EAAKW,6BAA6B1L,GAAS,SAAC2L,GACtCA,IACFrU,QAAQC,IAAI,2BAA4BoU,GACxCZ,EAAKU,kBAAoBE,EACzBZ,EAAKa,oBAAoB5L,EAAS2L,GAGlCZ,EAAKjK,eAAiB,CAAC,CACrB9E,GAAIgE,EACJjJ,IAAK4U,EACLE,SAAU,OAAF5U,OAAS+I,KAGnB+K,EAAK3O,aAAe2O,EAAKjK,eAAe,GACxCiK,EAAK/I,aAAc,EAEvB,IAKF+I,EAAKjK,eAAiB,CAAC,CACrB9E,GAAIgE,EACJjJ,IAAKgU,EAAKU,kBACVI,SAAU,OAAF5U,OAAS+I,KAGnB+K,EAAK3O,aAAe2O,EAAKjK,eAAe,GACxCiK,EAAKhK,kBAAoB,EACzBgK,EAAKe,eAAiB9L,EAGtB+K,EAAK/I,aAAc,EAGnB+I,EAAKpG,gBAAgB3E,EACvB,MACE1I,QAAQC,IAAI,6CACZwT,EAAKW,6BAA6B1L,GAAS,SAAC2L,GACtCA,GACFrU,QAAQC,IAAI,2BAA4BoU,GACxCZ,EAAKU,kBAAoBE,EACzBZ,EAAKgB,kBAAkBJ,EAAM3L,IAE7B+K,EAAKhG,SAASF,MAAM,kBAExB,GAEJ,IAAC,UACM,SAAAA,GACLvN,QAAQuN,MAAM,WAAYA,GAE1BkG,EAAKW,6BAA6B1L,EACpC,GACJ,EAGAgM,oBAAmB,SAAChM,GAAS,IAAAiM,EAAA,KAC3B3U,QAAQC,IAAI,4BAA6ByI,GAEzC8G,EAAAA,WAAIoF,OAAOC,OAAOnM,GACf0E,MAAK,SAAA8B,GACJlP,QAAQC,IAAI,2BAA4BiP,GACxC,IAAM4F,EAAY5F,EAASzL,KAG3B,GAFAzD,QAAQC,IAAI,0BAA2B6U,IAElCA,IAAcA,EAAUT,KAG3B,OAFArU,QAAQuN,MAAM,uCACdoH,EAAKlH,SAASF,MAAM,kBAKtBoH,EAAKnL,eAAiB,CAAC,CACrB9E,GAAIoQ,EAAUpQ,GACdjF,IAAKqV,EAAUT,KACfE,SAAUO,EAAUC,eAAY,OAAApV,OAAYmV,EAAUpQ,MAGxDiQ,EAAK7P,aAAe6P,EAAKnL,eAAe,GACxCmL,EAAKlL,kBAAoB,EAGzBkL,EAAKR,kBAAoBW,EAAUT,KAGnCM,EAAKK,wBAAwBF,EAAUpQ,GAAIoQ,EAAUT,MAGrDM,EAAKM,8BAGLN,EAAKjK,aAAc,EAEnB1K,QAAQC,IAAI,sCACd,IAAC,UACM,SAAAsN,GACLvN,QAAQuN,MAAM,yBAA0BA,GACxCoH,EAAKO,cAAe,EACpBP,EAAKQ,oBAAsB,kBAGvBR,EAAKR,mBAAqBQ,EAAKH,gBACjCxU,QAAQC,IAAI,kBACZ0U,EAAKF,kBAAkBE,EAAKR,kBAAmBQ,EAAKH,iBAEpDG,EAAKlH,SAASF,MAAM,sBAExB,GACJ,EAGAyH,wBAAuB,SAACI,EAAYC,GAAW,IAAAC,EAAA,KAC7CtV,QAAQC,IAAI,mBAAoB,CAAEmV,WAAAA,EAAYf,KAAMgB,IAGpD,IAAM5R,EAAO,CACX2R,WAAYA,EAAW/G,WACvB2F,aAAcqB,EACdE,YAAa,KAAF5V,OAAOyV,IAIpBxD,MAAM,mBAAoB,CACxB4D,OAAQ,OACR9F,QAAS,CACP,eAAgB,oBAElB+F,KAAMlE,KAAKmE,UAAUjS,GACrBkS,YAAa,YAEZvI,MAAK,SAAA8B,GACJ,IAAKA,EAAS2C,GACZ,MAAM,IAAIC,MAAM,uBAADnS,OAAwBuP,EAAS6C,SAElD,OAAO7C,EAAS8C,MAClB,IACC5E,MAAK,SAAA+E,GACJnS,QAAQC,IAAI,mBAAoBkS,GAC5BA,GAAUA,EAAOzN,KACnB4Q,EAAKxB,YAAc3B,EAAOzN,IAE5B4Q,EAAK7H,SAASmI,QAAQ,aACxB,IAAC,UACM,SAAArI,GACLvN,QAAQuN,MAAM,mBAAoBA,GAClC+H,EAAK7H,SAASI,QAAQ,oBACxB,GACJ,EAGAgI,cAAa,SAACnN,GAEZC,KAAK6K,mBAAmB9K,EAC1B,EAGA+L,kBAAiB,SAACY,EAAWD,GAAY,IAAAU,EAAA,KACvC9V,QAAQC,IAAI,cAAe,CAAEoU,KAAMgB,EAAW3Q,GAAI0Q,IAGlD,IAAM9E,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,UAAY,CAAEhN,GAAI,GAGzD6P,EAAWc,EAAU/G,UAAU+G,EAAUU,YAAY,KAAO,GAG5DC,EAAW,CACfzB,SAAUA,EACVQ,cAAeR,EACfF,KAAMgB,EACNY,SAAU,aACV5U,KAAM,EACNsC,MAAO,IACPa,OAAQ,IACRuN,OAAQ,QACRmE,YAAa5F,EAAK5L,IAIpB8K,EAAAA,WAAIoF,OAAOuB,OAAOf,EAAYY,GAC3B5I,MAAK,SAAA8B,GACJlP,QAAQC,IAAI,gBAAiBiP,EAASzL,MAGtCqS,EAAKM,oBAGLN,EAAKD,cAAcT,EACrB,IAAC,UACM,SAAA7H,GACLvN,QAAQuN,MAAM,aAAcA,GAC5BuI,EAAKrI,SAASF,MAAM,oBACtB,GACJ,EAGA8I,gBAAe,SAAChB,GAEd1M,KAAKa,eAAiB,CAAC,CACrB9E,GAAIiE,KAAK6L,gBAAkB5U,KAAKC,MAAMwO,WACtC5O,IAAK4V,EACLd,SAAUc,EAAU/G,UAAU+G,EAAUU,YAAY,KAAO,KAG7DpN,KAAK7D,aAAe6D,KAAKa,eAAe,GACxCb,KAAKc,kBAAoB,EAGpBd,KAAK6L,iBACR7L,KAAK6L,eAAiB7L,KAAKa,eAAe,GAAG9E,GAC7C1E,QAAQC,IAAI,YAAa0I,KAAK6L,iBAIhC7L,KAAK2N,yBAGL3N,KAAK+B,aAAc,CACrB,EAGA4L,uBAAsB,WAAG,IAAAC,EAAA,KAClB5N,KAAK6L,gBAAmB7L,KAAKwL,mBAKlCnU,QAAQC,IAAI,wBAAyB0I,KAAK6L,gBAE1ChF,EAAAA,WAAImE,WAAWC,gBAAgBjL,KAAK6L,gBACjCpH,MAAK,SAAA8B,GACJ,GAAIA,EAASzL,MAAQyL,EAASzL,KAAKJ,OAAS,EAAG,CAE7C,IAAMwQ,EAAY3E,EAASzL,KAAK,GAChC8S,EAAKzC,YAAcD,EAAUnP,GAC7B1E,QAAQC,IAAI,mBAAoB4T,GAG5BA,EAAUG,eAAiBuC,EAAKpC,oBAClCnU,QAAQC,IAAI,qBACZsW,EAAKH,oBAET,MAEEpW,QAAQC,IAAI,wBACZsW,EAAKH,mBAET,IAAC,UACM,SAAA7I,GACLvN,QAAQuN,MAAM,mBAAoBA,GAElCvN,QAAQC,IAAI,qBACZsW,EAAKH,mBACP,KA9BApW,QAAQC,IAAI,6BA+BhB,EAEAmW,kBAAiB,WAAG,IAAAI,EAAA,KAElB,GAAK7N,KAAK6L,eAAV,CAKA,IAAIa,EAAY,GAGhB,GAAI1M,KAAK7D,cAAgB6D,KAAK7D,aAAarF,IACzC4V,EAAY1M,KAAK7D,aAAarF,IAC9BO,QAAQC,IAAI,iBAAkBoV,OAG3B,KAAI1M,KAAKwL,kBAOZ,YADAnU,QAAQuN,MAAM,8BALd8H,EAAY1M,KAAKwL,kBACjBnU,QAAQC,IAAI,kBAAmBoV,EAMjC,CAGA1M,KAAK8N,0BAA0B9N,KAAK6L,eAAgBa,GAAW,WAEhD9D,KAAKC,MAAMC,aAAaC,QAAQ,SAE7C,IAEE,IAAMjO,EAAO,CACX2R,WAAYoB,EAAKhC,eAAenG,WAChC2F,aAAcqB,EACdE,YAAa,KAAF5V,OAAO6W,EAAKhC,iBAGzBxU,QAAQC,IAAI,qBAAsBwD,GAGlC,IAAMiT,EAAY,eAAAC,GAAA7H,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAA4H,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA9E,EAAA+E,EAAA,OAAAnI,EAAAA,EAAAA,KAAAK,GAAA,SAAA+H,GAAA,eAAAA,EAAA7H,GAAA,OAGoC,OAHpC6H,EAAA5H,EAAA,EAGlBvP,QAAQC,IAAI,qBAAsBuW,EAAKhC,gBAAe2C,EAAA7H,EAAA,EAC1BsC,MAAM,6BAADjS,OAA8B6W,EAAKhC,gBAAkB,CACpFgB,OAAQ,MACR9F,QAAS,CACP,OAAU,mBACV,gBAAiB,WACjB,OAAU,YAEZiG,YAAa,YACb,OARgB,OAAZkB,EAAYM,EAAAxH,EAAAwH,EAAA7H,EAAA,EAWMuH,EAAc7E,OAAM,OAe5C,OAfM8E,EAAQK,EAAAxH,EACVoH,EAAiB,KACjBC,GAAS,EAETF,GAAaA,EAAUzT,OAAS,GAAKyT,EAAU,GAAGpS,KACpDsS,GAAS,EACTD,EAAiBD,EAAU,GAAGpS,GAC9B1E,QAAQC,IAAI,iBAAkB8W,IAI5BC,GAAUD,IACZtT,EAAKiB,GAAKqS,GAGZI,EAAA7H,EAAA,EAC2BsC,MAAM,mBAAoB,CACnD4D,OAAQ,OACR9F,QAAS,CACP,eAAgB,mBAChB,gBAAiB,WACjB,OAAU,YAEZ+F,KAAMlE,KAAKmE,UAAUjS,GACrBkS,YAAa,YACb,OATe,GAAXsB,EAAWE,EAAAxH,EAWZsH,EAAapF,GAAI,CAAFsF,EAAA7H,EAAA,cACZ,IAAIwC,MAAM,SAADnS,OAAUsX,EAAalF,SAAS,cAAAoF,EAAA7H,EAAA,EAG5B2H,EAAajF,OAAM,OAAlCG,EAAKgF,EAAAxH,EACX3P,QAAQC,IAAI,iBAAkBkS,GAG1BA,GAAUA,EAAOzN,KACnB8R,EAAK1C,YAAc3B,EAAOzN,IAG5B8R,EAAK/I,SAASmI,QAAQ,cAAauB,EAAA7H,EAAA,eAAA6H,EAAA5H,EAAA,EAAA2H,EAAAC,EAAAxH,EAGnC3P,QAAQuN,MAAM,iBAAgB2J,GAC9BV,EAAK/I,SAASF,MAAM,SAAW2J,EAAM5D,SAAQ,cAAA6D,EAAA7L,EAAA,MAAAsL,EAAA,kBAEhD,kBA5DiB,OAAAD,EAAAxR,MAAA,KAAAC,UAAA,KA+DlBsR,GAEF,CAAE,MAAOU,GACPpX,QAAQuN,MAAM,oBAAqB6J,GACnCZ,EAAK/I,SAASF,MAAM,YAAc6J,EAAI9D,QACxC,CACF,GAzGA,MAFEtT,QAAQC,IAAI,wBA4GhB,EAGAwW,0BAAyB,SAAC/N,EAAS2M,EAAWgC,GAAU,IAAAC,EAAA,KAEtD9H,EAAAA,WAAIoF,OAAOC,OAAOnM,GACf0E,MAAK,WAEJiK,GACF,IAAC,UACM,WAELrX,QAAQC,IAAI,kBACZqX,EAAK7C,kBAAkBY,EAAW3M,EACpC,GACJ,EAGAwD,aAAY,WAAG,IAAAqL,EAAA,KAEb5O,KAAK6O,WAAU,WACb,IAAMC,EAAQF,EAAKG,MAAMC,gBACzB,GAAIF,EAAO,CAET,IAAMG,EAAOH,EAAMI,wBACbC,EAAWF,EAAKjU,MAChBoU,EAAYH,EAAKpT,OAGnBsT,IAAaP,EAAK/R,YAAcuS,IAAcR,EAAK9R,cACrDzF,QAAQC,IAAI,gBAADN,OAAiB4X,EAAK/R,WAAU,KAAA7F,OAAI4X,EAAK9R,YAAW,QAAA9F,OAAOmY,EAAQ,KAAAnY,OAAIoY,IAClFR,EAAK/R,WAAasS,EAClBP,EAAK9R,YAAcsS,EAGnBR,EAAKzM,OAASyM,EAAK/R,WAAa+R,EAAK3M,cACrC2M,EAAKxM,OAASwM,EAAK9R,YAAc8R,EAAK1M,eAGtC0M,EAAKS,iCAET,CACF,GACF,EAGAC,UAAS,WACPjY,QAAQC,IAAI,UAEd,EAGAiF,gBAAe,SAACgT,GACdlY,QAAQC,IAAI,UAGZ,IAAMkY,EAAMD,EAAME,OACZR,EAAOO,EAAIN,wBAEflP,KAAKnD,WAAaoS,EAAKjU,MACvBgF,KAAKlD,YAAcmS,EAAKpT,OAC1BmE,KAAKiC,cAAgBuN,EAAIE,aACzB1P,KAAKkC,eAAiBsN,EAAIG,cAE1BtY,QAAQC,IAAI,QAAS,CACnB0D,MAAOgF,KAAKnD,WACZhB,OAAQmE,KAAKlD,YACbmF,cAAejC,KAAKiC,cACpBC,eAAgBlC,KAAKkC,iBAIrBlC,KAAK+B,aAAc,EAGjB/B,KAAK6B,mBAAqB7B,KAAKY,cAAclG,OAAS,IACxDrD,QAAQC,IAAI,qBACR0I,KAAKgD,4BAIXhD,KAAK4P,MAAM,eAAgB,CACzB5U,MAAOgF,KAAKnD,WACZhB,OAAQmE,KAAKlD,aAEjB,EAGAuS,+BAA8B,WAAG,IAAAQ,EAAA,KAC1B7P,KAAKe,aAAgBf,KAAKe,YAAYrG,SAE3CrD,QAAQC,IAAI,iBAAkB0I,KAAKnD,WAAY,IAAKmD,KAAKlD,aAGrDkD,KAAKnD,YAAc,GAAKmD,KAAKlD,aAAe,EAC9CzF,QAAQuN,MAAM,sBAIhB5E,KAAKe,YAAcf,KAAKe,YAAYgJ,KAAI,SAAAnM,GAEtC,GAAIA,EAAIkS,aAAc,CAEpB,IAAMC,EAAUnS,EAAIoS,YACdC,EAAUrS,EAAIsS,YACdlV,EAAQ4C,EAAIuS,gBACZtU,EAAS+B,EAAIwS,iBAGbC,EAAS9U,KAAKC,OAAOuU,EAAU/U,EAAM,GAAK6U,EAAKhT,YAC/CyT,EAAS/U,KAAKC,OAAOyU,EAAUpU,EAAO,GAAKgU,EAAK/S,aAChDyT,EAAahV,KAAKC,MAAMR,EAAQ6U,EAAKhT,YACrC2T,EAAcjV,KAAKC,MAAMK,EAASgU,EAAK/S,aAK7C,OAHAzF,QAAQC,IAAI,cAADN,OAAe4G,EAAI7B,GAAE,UAAA/E,OAAS+Y,EAAQU,QAAQ,GAAE,MAAAzZ,OAAKiZ,EAAQQ,QAAQ,GAAE,aAAAzZ,OAAYqZ,EAAM,MAAArZ,OAAKsZ,EAAM,WAAAtZ,OAAUuZ,EAAU,OAAAvZ,OAAMwZ,KAGzIE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACK9S,GAAG,IACNlC,EAAG2U,EACH1U,EAAG2U,EACHtV,MAAOuV,EACP1U,OAAQ2U,GAEZ,CAEK,QAAwBG,IAApB/S,EAAIoS,YAA2B,CAEtC,IAAMY,EAAOrV,KAAKC,MAAMoC,EAAIoS,YAAcH,EAAKhT,YACzCgU,EAAOtV,KAAKC,MAAMoC,EAAIsS,YAAcL,EAAK/S,aACzCqS,EAAW5T,KAAKC,MAAMoC,EAAIuS,gBAAkBN,EAAKhT,YACjDuS,EAAY7T,KAAKC,MAAMoC,EAAIwS,iBAAmBP,EAAK/S,aAIzD,OAFAzF,QAAQC,IAAI,YAADN,OAAa4G,EAAI7B,GAAE,UAAA/E,OAAS4G,EAAIoS,YAAYS,QAAQ,GAAE,MAAAzZ,OAAK4G,EAAIsS,YAAYO,QAAQ,GAAE,YAAAzZ,OAAW4Z,EAAI,MAAA5Z,OAAK6Z,EAAI,WAAA7Z,OAAUmY,EAAQ,OAAAnY,OAAMoY,KAEhJsB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACK9S,GAAG,IACNlC,EAAGkV,EACHjV,EAAGkV,EACH7V,MAAOmU,EACPtT,OAAQuT,GAEZ,CAEK,GAAIxR,EAAIlC,GAAK,GAAKkC,EAAIjC,GAAK,GAAKiC,EAAI5C,OAAS,GAAK4C,EAAI/B,QAAU,EAAG,CACtE,IAAM+U,EAAOrV,KAAKC,MAAMoC,EAAIlC,EAAImU,EAAKhT,YAC/BgU,EAAOtV,KAAKC,MAAMoC,EAAIjC,EAAIkU,EAAK/S,aAC/BqS,EAAW5T,KAAKC,MAAMoC,EAAI5C,MAAQ6U,EAAKhT,YACvCuS,EAAY7T,KAAKC,MAAMoC,EAAI/B,OAASgU,EAAK/S,aAK/C,OAHAzF,QAAQC,IAAI,eAADN,OAAgB4G,EAAI7B,GAAE,OAAA/E,OAAM4G,EAAIlC,EAAE+U,QAAQ,GAAE,MAAAzZ,OAAK4G,EAAIjC,EAAE8U,QAAQ,GAAE,UAAAzZ,OAAS4Z,EAAI,MAAA5Z,OAAK6Z,EAAI,WAAA7Z,OAAUmY,EAAQ,OAAAnY,OAAMoY,KAG1HsB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACK9S,GAAG,IACNlC,EAAGkV,EACHjV,EAAGkV,EACH7V,MAAOmU,EACPtT,OAAQuT,EACRY,YAAapS,EAAIlC,EACjBwU,YAAatS,EAAIjC,EACjBwU,gBAAiBvS,EAAI5C,MACrBoV,iBAAkBxS,EAAI/B,QAE1B,CAEE,IAAMsG,EAAS0N,EAAKhT,WAAagT,EAAK5N,eAAiB,EACjDG,EAASyN,EAAK/S,YAAc+S,EAAK3N,gBAAkB,EAEzD,GAAI3G,KAAKsD,IAAIsD,EAAS,GAAK,KAAQ5G,KAAKsD,IAAIuD,EAAS,GAAK,IAExD,OAAOxE,EAGT,IAAMgT,EAAOrV,KAAKC,MAAMoC,EAAIlC,EAAIyG,GAC1B0O,EAAOtV,KAAKC,MAAMoC,EAAIjC,EAAIyG,GAC1B+M,EAAW5T,KAAKC,MAAMoC,EAAI5C,MAAQmH,GAClCiN,EAAY7T,KAAKC,MAAMoC,EAAI/B,OAASuG,GAI1C,OAFA/K,QAAQC,IAAI,WAADN,OAAY4G,EAAI7B,GAAE,SAAA/E,OAAQmL,EAAOsO,QAAQ,GAAE,MAAAzZ,OAAKoL,EAAOqO,QAAQ,GAAE,QAAAzZ,OAAO4G,EAAIlC,EAAC,MAAA1E,OAAK4G,EAAIjC,EAAC,UAAA3E,OAAS4Z,EAAI,MAAA5Z,OAAK6Z,EAAI,WAAA7Z,OAAUmY,EAAQ,OAAAnY,OAAMoY,KAEhJsB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACK9S,GAAG,IACNlC,EACAC,EAAGkV,EACH7V,MAAOmU,EACPtT,OAAQuT,GAGd,IAEA/X,QAAQC,IAAI,eAAgB0I,KAAKe,YAAYrG,OAAQ,QACvD,EACAoW,WAAU,SAACC,GACT/Q,KAAK5F,YAAc2W,CACrB,EACA/T,aAAY,SAACuS,GACX,GAAyB,cAArBvP,KAAK5F,YAAT,CAEA4F,KAAKvB,WAAY,EACjB,IAAMwQ,EAAOjP,KAAK+O,MAAMiC,oBAAoB9B,wBAC5ClP,KAAKrB,UAAUjD,EAAI6T,EAAM0B,QAAUhC,EAAKiC,KACxClR,KAAKrB,UAAUhD,EAAI4T,EAAM4B,QAAUlC,EAAKmC,IACxCpR,KAAKpB,YAAYlD,EAAIsE,KAAKrB,UAAUjD,EACpCsE,KAAKpB,YAAYjD,EAAIqE,KAAKrB,UAAUhD,CAPQ,CAQ9C,EAEAuB,UAAS,SAACqS,GACR,GAAKvP,KAAKvB,UAAV,CAEA,IAAMwQ,EAAOjP,KAAK+O,MAAMiC,oBAAoB9B,wBAC5ClP,KAAKpB,YAAYlD,EAAI6T,EAAM0B,QAAUhC,EAAKiC,KAC1ClR,KAAKpB,YAAYjD,EAAI4T,EAAM4B,QAAUlC,EAAKmC,GAJf,CAK7B,EAEAhU,WAAU,WACR,GAAK4C,KAAKvB,YAEVuB,KAAKvB,WAAY,EAGblD,KAAKsD,IAAImB,KAAKpB,YAAYlD,EAAIsE,KAAKrB,UAAUjD,GAAK,IAClDH,KAAKsD,IAAImB,KAAKpB,YAAYjD,EAAIqE,KAAKrB,UAAUhD,GAAK,IAAI,CAExD,IAAMD,EAAIH,KAAKmD,IAAIsB,KAAKrB,UAAUjD,EAAGsE,KAAKpB,YAAYlD,GAChDC,EAAIJ,KAAKmD,IAAIsB,KAAKrB,UAAUhD,EAAGqE,KAAKpB,YAAYjD,GAChDX,EAAQO,KAAKsD,IAAImB,KAAKpB,YAAYlD,EAAIsE,KAAKrB,UAAUjD,GACrDG,EAASN,KAAKsD,IAAImB,KAAKpB,YAAYjD,EAAIqE,KAAKrB,UAAUhD,GAGtDqU,EAActU,EAAIsE,KAAKnD,WACvBqT,EAAcvU,EAAIqE,KAAKlD,YACvBqT,EAAkBnV,EAAQgF,KAAKnD,WAC/BuT,EAAmBvU,EAASmE,KAAKlD,YAGjCuU,EAAa,CACjBtV,GAAI9E,KAAKC,MACT0L,WAAY5C,KAAKc,kBACjBlD,IAAKoC,KAAKtG,YACVjB,KAAM,YACNiD,EAAGA,EACHC,EAAGA,EACHX,MAAOA,EACPa,OAAQA,EAERmU,YAAaA,EACbE,YAAaA,EACbC,gBAAiBA,EACjBC,iBAAkBA,GAIpBpQ,KAAKe,YAAYuQ,KAAKD,GAGtBrR,KAAKuR,yBAAyBF,EAChC,CACF,EAEA/T,cAAa,WACP0C,KAAKvB,YACPuB,KAAKvB,WAAY,EAErB,EAEAR,aAAY,SAACsR,EAAOiC,GAClBjC,EAAMkC,iBAGN,IAAMC,EAAY1R,KAAKe,YAAY4Q,MAAK,SAAAlU,GAAE,OAAKA,EAAI1B,KAAOyV,CAAK,IAC/D,GAAKE,EAAL,CAGA1R,KAAK4R,aAAUlB,EAAAA,EAAAA,GAAA,GAAQgB,GAEvB,IAAMzC,EAAOjP,KAAK+O,MAAMiC,oBAAoB9B,wBAC5ClP,KAAK6R,aAAe,CAClBnW,EAAG6T,EAAM0B,QAAUhC,EAAKiC,KACxBvV,EAAG4T,EAAM4B,QAAUlC,EAAKmC,KAG1BpR,KAAKlC,cAAe,EACpBkC,KAAKjC,aAAeyT,EACpBxR,KAAKvB,WAAY,CAbK,CAcxB,EAEAN,eAAc,SAACoR,EAAOiC,EAAOM,GAC3BvC,EAAMkC,iBAGN,IAAMM,EAAc/R,KAAKe,YAAY4Q,MAAK,SAAAlU,GAAE,OAAKA,EAAI1B,KAAOyV,CAAK,IACjE,GAAKO,EAAL,CAGA/R,KAAK4R,aAAUlB,EAAAA,EAAAA,GAAA,GAAQqB,GAEvB,IAAM9C,EAAOjP,KAAK+O,MAAMiC,oBAAoB9B,wBAC5ClP,KAAK6R,aAAe,CAClBnW,EAAG6T,EAAM0B,QAAUhC,EAAKiC,KACxBvV,EAAG4T,EAAM4B,QAAUlC,EAAKmC,KAG1BpR,KAAKgS,eAAgB,EACrBhS,KAAKjC,aAAeyT,EACpBxR,KAAKoB,aAAe0Q,EACpB9R,KAAKvB,WAAY,CAdO,CAe1B,EAEAuG,sBAAqB,SAACuK,GAAO,IAAA0C,EAAA,KAE3B,GAAIjS,KAAKvB,UAAT,CACE,IAAMwQ,EAAOjP,KAAK+O,MAAMiC,oBAAoB9B,wBACtCxT,EAAI6T,EAAM0B,QAAUhC,EAAKiC,KACzBvV,EAAI4T,EAAM4B,QAAUlC,EAAKmC,IAGzBc,EAAW3W,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAIsB,KAAKnD,WAAYnB,IACjD0W,EAAW7W,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAIsB,KAAKlD,YAAanB,IAExDqE,KAAKpB,YAAc,CAAElD,EAAGwW,EAAUvW,EAAGyW,EAEvC,KAXA,CAcA,GAAIpS,KAAKlC,aAAc,CACrB,IAAMmR,EAAOjP,KAAK+O,MAAMiC,oBAAoB9B,wBACtCmD,EAAW9C,EAAM0B,QAAUhC,EAAKiC,KAChCoB,EAAW/C,EAAM4B,QAAUlC,EAAKmC,IAGhCmB,EAASF,EAAWrS,KAAK6R,aAAanW,EACtC8W,EAASF,EAAWtS,KAAK6R,aAAalW,EAGtC8W,EAAWzS,KAAKe,YAAY2R,WAAU,SAAAjV,GAAE,OAAKA,EAAI1B,KAAOkW,EAAKlU,YAAY,IAC/E,IAAkB,IAAd0U,EAAiB,OAGrB,IAAI7B,EAAO5Q,KAAK4R,YAAYlW,EAAI6W,EAC5B1B,EAAO7Q,KAAK4R,YAAYjW,EAAI6W,EAShC,OANA5B,EAAOrV,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAIkS,EAAM5Q,KAAKnD,WAAamD,KAAK4R,YAAY5W,QACrE6V,EAAOtV,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAImS,EAAM7Q,KAAKlD,YAAckD,KAAK4R,YAAY/V,SAGtEmE,KAAKe,YAAY0R,GAAU/W,EAAIkV,OAC/B5Q,KAAKe,YAAY0R,GAAU9W,EAAIkV,EAEjC,CAGA,GAAI7Q,KAAKgS,cAAe,CACtB,IAAM/C,EAAOjP,KAAK+O,MAAMiC,oBAAoB9B,wBACtCmD,EAAW9C,EAAM0B,QAAUhC,EAAKiC,KAChCoB,EAAW/C,EAAM4B,QAAUlC,EAAKmC,IAGhCc,EAAW3W,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAIsB,KAAKnD,WAAYwV,IACjDD,EAAW7W,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAIsB,KAAKlD,YAAawV,IAGlDG,EAAWzS,KAAKe,YAAY2R,WAAU,SAAAjV,GAAE,OAAKA,EAAI1B,KAAOkW,EAAKlU,YAAY,IAC/E,IAAkB,IAAd0U,EAAiB,OAErB,IAAMhV,EAAMuC,KAAKe,YAAY0R,GACvBE,EAAW3S,KAAK4R,YAGtB,OAAQ5R,KAAKoB,cACX,IAAK,WAEH3D,EAAIzC,MAAQ2X,EAASjX,EAAIiX,EAAS3X,MAAQkX,EAC1CzU,EAAI5B,OAAS8W,EAAShX,EAAIgX,EAAS9W,OAASuW,EAC5C3U,EAAI/B,EAAIwW,EACRzU,EAAI9B,EAAIyW,EAEJ3U,EAAIzC,MAAQ,KACdyC,EAAIzC,MAAQ,GACZyC,EAAI/B,EAAIiX,EAASjX,EAAIiX,EAAS3X,MAAQ,IAEpCyC,EAAI5B,OAAS,KACf4B,EAAI5B,OAAS,GACb4B,EAAI9B,EAAIgX,EAAShX,EAAIgX,EAAS9W,OAAS,IAEzC,MAEF,IAAK,YAEH4B,EAAIzC,MAAQkX,EAAWS,EAASjX,EAChC+B,EAAI5B,OAAS8W,EAAShX,EAAIgX,EAAS9W,OAASuW,EAC5C3U,EAAI9B,EAAIyW,EAEJ3U,EAAIzC,MAAQ,KAAIyC,EAAIzC,MAAQ,IAC5ByC,EAAI5B,OAAS,KACf4B,EAAI5B,OAAS,GACb4B,EAAI9B,EAAIgX,EAAShX,EAAIgX,EAAS9W,OAAS,IAEzC,MAEF,IAAK,cAEH4B,EAAIzC,MAAQ2X,EAASjX,EAAIiX,EAAS3X,MAAQkX,EAC1CzU,EAAI5B,OAASuW,EAAWO,EAAShX,EACjC8B,EAAI/B,EAAIwW,EAEJzU,EAAIzC,MAAQ,KACdyC,EAAIzC,MAAQ,GACZyC,EAAI/B,EAAIiX,EAASjX,EAAIiX,EAAS3X,MAAQ,IAEpCyC,EAAI5B,OAAS,KAAI4B,EAAI5B,OAAS,IAClC,MAEF,IAAK,eAEH4B,EAAIzC,MAAQkX,EAAWS,EAASjX,EAChC+B,EAAI5B,OAASuW,EAAWO,EAAShX,EAE7B8B,EAAIzC,MAAQ,KAAIyC,EAAIzC,MAAQ,IAC5ByC,EAAI5B,OAAS,KAAI4B,EAAI5B,OAAS,IAClC,MAEN,CArGA,CAsGF,EACAoJ,oBAAmB,WAAG,IAAA2N,EAAA,KACpB,GAAI5S,KAAKvB,UACPuB,KAAK5C,kBACA,GAAI4C,KAAKlC,cAAgBkC,KAAKgS,cAAe,CAIlD,IAAMa,EAAmB7S,KAAKe,YAAY4Q,MAAK,SAAAlU,GAAE,OAAKA,EAAI1B,KAAO6W,EAAK7U,YAAY,IAClF,GAAI8U,GAEE7S,KAAK4R,YAAa,CACpB,IAAMkB,EACJD,EAAiBnX,IAAMsE,KAAK4R,YAAYlW,GACxCmX,EAAiBlX,IAAMqE,KAAK4R,YAAYjW,GACxCkX,EAAiB7X,QAAUgF,KAAK4R,YAAY5W,OAC5C6X,EAAiBhX,SAAWmE,KAAK4R,YAAY/V,OAE3CiX,IACFzb,QAAQC,IAAI,gBAAiB,CAC3Byb,IAAK,IAAF/b,OAAMgJ,KAAK4R,YAAYlW,EAAC,MAAA1E,OAAKgJ,KAAK4R,YAAYjW,EAAC,KAClDqX,IAAK,GAAFhc,OAAKgJ,KAAK4R,YAAY5W,MAAK,OAAAhE,OAAMgJ,KAAK4R,YAAY/V,QACrDoX,IAAK,IAAFjc,OAAM6b,EAAiBnX,EAAC,MAAA1E,OAAK6b,EAAiBlX,EAAC,KAClDuX,IAAK,GAAFlc,OAAK6b,EAAiB7X,MAAK,OAAAhE,OAAM6b,EAAiBhX,UAIvDgX,EAAiB7C,YAAc6C,EAAiBnX,EAAIsE,KAAKnD,WACzDgW,EAAiB3C,YAAc2C,EAAiBlX,EAAIqE,KAAKlD,YACzD+V,EAAiB1C,gBAAkB0C,EAAiB7X,MAAQgF,KAAKnD,WACjEgW,EAAiBzC,iBAAmByC,EAAiBhX,OAASmE,KAAKlD,YAGnEkD,KAAKmT,2BAA2BN,GAEpC,CAIF7S,KAAKlC,cAAe,EACpBkC,KAAKgS,eAAgB,EACrBhS,KAAKjC,aAAe,KACpBiC,KAAKoB,aAAe,KACpBpB,KAAK4R,YAAc,IACrB,CACF,IAACwB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA7N,EAAA,yBAEC,GAAKvF,KAAKvB,YAEVuB,KAAKvB,WAAY,EAGblD,KAAKsD,IAAImB,KAAKpB,YAAYlD,EAAIsE,KAAKrB,UAAUjD,GAAK,IAClDH,KAAKsD,IAAImB,KAAKpB,YAAYjD,EAAIqE,KAAKrB,UAAUhD,GAAK,IAAI,CAExD,IAAMD,EAAIH,KAAKmD,IAAIsB,KAAKrB,UAAUjD,EAAGsE,KAAKpB,YAAYlD,GAChDC,EAAIJ,KAAKmD,IAAIsB,KAAKrB,UAAUhD,EAAGqE,KAAKpB,YAAYjD,GAChDX,EAAQO,KAAKsD,IAAImB,KAAKpB,YAAYlD,EAAIsE,KAAKrB,UAAUjD,GACrDG,EAASN,KAAKsD,IAAImB,KAAKpB,YAAYjD,EAAIqE,KAAKrB,UAAUhD,GAGtDqU,EAActU,EAAIsE,KAAKnD,WACvBqT,EAAcvU,EAAIqE,KAAKlD,YACvBqT,EAAkBnV,EAAQgF,KAAKnD,WAC/BuT,EAAmBvU,EAASmE,KAAKlD,YAGjCuU,EAAa,CACjBtV,GAAI9E,KAAKC,MACT0L,WAAY5C,KAAKc,kBACjBlD,IAAKoC,KAAKtG,YACVjB,KAAM,YACNiD,EAAGA,EACHC,EAAGA,EACHX,MAAOA,EACPa,OAAQA,EAERmU,YAAaA,EACbE,YAAaA,EACbC,gBAAiBA,EACjBC,iBAAkBA,GAIpBpQ,KAAKe,YAAYuQ,KAAKD,GAGtBrR,KAAKuR,yBAAyBF,EAChC,CACF,IAAC,qCAC8BA,GAAY,IAAAgC,EAAA,YAAAlN,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAiN,IAAA,IAAAtD,EAAAE,EAAAC,EAAAC,EAAAmD,EAAAhN,EAAAiN,EAAA,OAAApN,EAAAA,EAAAA,KAAAK,GAAA,SAAAgN,GAAA,eAAAA,EAAA9M,GAAA,OAGzC,GAFAtP,QAAQC,IAAI,WAAY+Z,GAGnBgC,EAAKtT,QAAS,CAAF0T,EAAA9M,EAAA,QACe,OAA9BtP,QAAQuN,MAAM,iBAAgB6O,EAAA9Q,EAAA,aAK3B0Q,EAAKxW,YAAewW,EAAKvW,YAAW,CAAA2W,EAAA9M,EAAA,QACN,OAAjCtP,QAAQuN,MAAM,oBAAmB6O,EAAA9Q,EAAA,UA0DjC,OA1DiC8Q,EAAA7M,EAAA,EAS7ByK,EAAWvB,cAGbK,EAAkBkB,EAAWrW,MAAQqY,EAAKxW,WAC1CuT,EAAmBiB,EAAWxV,OAASwX,EAAKvW,YAC5CkT,EAAeqB,EAAW3V,EAAI2X,EAAKxW,WAAesT,EAAkB,EACpED,EAAemB,EAAW1V,EAAI0X,EAAKvW,YAAgBsT,EAAmB,EAEtE/Y,QAAQC,IAAI,uBAAwB,CAClCoc,MAAO,IAAF1c,OAAMqa,EAAW3V,EAAC,MAAA1E,OAAKqa,EAAW1V,EAAC,KACxCgY,KAAM,GAAF3c,OAAKqa,EAAWrW,MAAK,OAAAhE,OAAMqa,EAAWxV,QAC1C+X,OAAQ,IAAF5c,OAAMgZ,EAAYS,QAAQ,GAAE,MAAAzZ,OAAKkZ,EAAYO,QAAQ,GAAE,KAC7DoD,MAAO,GAAF7c,OAAKmZ,EAAgBM,QAAQ,GAAE,OAAAzZ,OAAMoZ,EAAiBK,QAAQ,QAIrET,EAAcqB,EAAW3V,EAAI2X,EAAKxW,WAClCqT,EAAcmB,EAAW1V,EAAI0X,EAAKvW,YAClCqT,EAAkBkB,EAAWrW,MAAQqY,EAAKxW,WAC1CuT,EAAmBiB,EAAWxV,OAASwX,EAAKvW,YAE5CzF,QAAQC,IAAI,oBAAqB,CAC/Bwc,KAAM,IAAF9c,OAAMqa,EAAW3V,EAAC,MAAA1E,OAAKqa,EAAW1V,EAAC,KACvCgY,KAAM,GAAF3c,OAAKqa,EAAWrW,MAAK,OAAAhE,OAAMqa,EAAWxV,QAC1CkY,MAAO,IAAF/c,OAAMgZ,EAAYS,QAAQ,GAAE,MAAAzZ,OAAKkZ,EAAYO,QAAQ,GAAE,KAC5DoD,MAAO,GAAF7c,OAAKmZ,EAAgBM,QAAQ,GAAE,OAAAzZ,OAAMoZ,EAAiBK,QAAQ,OAKvET,EAAczU,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAI,EAAGsR,IACtCE,EAAc3U,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAI,EAAGwR,IACtCC,EAAkB5U,KAAK4W,IAAI,KAAO5W,KAAKmD,IAAI,EAAGyR,IAC9CC,EAAmB7U,KAAK4W,IAAI,KAAO5W,KAAKmD,IAAI,EAAG0R,IAGzCmD,EAAU,CACdS,YAAaC,SAASZ,EAAKtT,QAAS,IACpCnC,IAAKyT,EAAWzT,IAChBlC,EAAGwY,OAAOlE,EAAYS,QAAQ,IAC9B9U,EAAGuY,OAAOhE,EAAYO,QAAQ,IAC9BzV,MAAOkZ,OAAO/D,EAAgBM,QAAQ,IACtC5U,OAAQqY,OAAO9D,EAAiBK,QAAQ,IAExC0D,aAAc9C,EAAWvB,aAAe,cAAgB,YAG1DzY,QAAQC,IAAI,UAAWic,GAEvBE,EAAA9M,EAAA,EACuBE,EAAAA,WAAI+C,KAAKwK,OAAOb,GAAQ,OAUhB,OAVzBhN,EAAOkN,EAAAzM,EACb3P,QAAQC,IAAI,UAAWiP,EAASzL,MAGhCuW,EAAWgD,KAAO9N,EAASzL,KAAKiB,GAChCsV,EAAWrB,YAAcA,EACzBqB,EAAWnB,YAAcA,EACzBmB,EAAWlB,gBAAkBA,EAC7BkB,EAAWjB,iBAAmBA,EAE9BiD,EAAKvO,SAASmI,QAAQ,UAASwG,EAAA9Q,EAAA,EACxB4D,EAASzL,MAAI,OAGuC,MAHvC2Y,EAAA7M,EAAA,EAAA4M,EAAAC,EAAAzM,EAEpB3P,QAAQuN,MAAM,UAAS4O,GACvBH,EAAKvO,SAASF,MAAM,YAAc4O,EAAM7I,SAAW,SAAQ6I,EAAA,cAAAC,EAAA9Q,EAAA,MAAA2Q,EAAA,iBApFpBnN,EAuF3C,IAAC,qCACwBkL,GAAY,IAAAiD,EAAA,KAEnCjd,QAAQkd,QACRld,QAAQC,IAAI,yDAA0D,sDAGtE,IAAMqQ,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,IAAKpB,IAASA,EAAK5L,GAGjB,OAFAiE,KAAK8E,SAASF,MAAM,gCACpBvN,QAAQuN,MAAM,0BAA2B+C,GAK3C,IAAMxL,EAAe6D,KAAKa,eAAeb,KAAKc,mBAC9C,IAAK3E,IAAiBA,EAAaJ,GAGjC,OAFAiE,KAAK8E,SAASF,MAAM,uBACpBvN,QAAQuN,MAAM,gCAAiCzI,GAKjD,IAaI6T,EAAaE,EAAaC,EAAiBC,EAbzCoE,EAAY,CAChBC,KAAMtY,EAAaJ,GACnB2Y,QAAM/Q,EAAAA,EAAAA,GAASxH,EAAaJ,IAC5B4Y,KAAMtD,EAAWzT,IACjBgX,GAAI,IAAF5d,OAAMqa,EAAW3V,EAAC,MAAA1E,OAAKqa,EAAW1V,EAAC,KACrCkZ,GAAI,GAAF7d,OAAKqa,EAAWrW,MAAK,OAAAhE,OAAMqa,EAAWxV,QACxCiZ,KAAMnN,EAAK5L,IAAM4L,EAAKqB,SACtB+L,QAAMpR,EAAAA,EAAAA,GAAUgE,EAAK5L,IAAM4L,EAAKqB,UAChCgM,KAAMrN,EAAKsN,MAOb,GALA5d,QAAQC,IAAI,UAAW,qCAAsCkd,QAK9B7D,IAA3BU,EAAWrB,YAEbA,EAAcqB,EAAWrB,YACzBE,EAAcmB,EAAWnB,YACzBC,EAAkBkB,EAAWlB,gBAC7BC,EAAmBiB,EAAWjB,iBAC9B/Y,QAAQC,IAAI,eAAgB,CAC1BoE,EAAGsU,EAAarU,EAAGuU,EACnBlV,MAAOmV,EAAiBtU,OAAQuU,QAE7B,CAEL,IAAKpQ,KAAKnD,aAAemD,KAAKlD,YAE5B,YADAkD,KAAK8E,SAASF,MAAM,mBAKtBoL,EAAcqB,EAAW3V,EAAIsE,KAAKnD,WAClCqT,EAAcmB,EAAW1V,EAAIqE,KAAKlD,YAClCqT,EAAkBkB,EAAWrW,MAAQgF,KAAKnD,WAC1CuT,EAAmBiB,EAAWxV,OAASmE,KAAKlD,YAC5CzF,QAAQC,IAAI,aAAc,CACxBoE,EAAGsU,EAAarU,EAAGuU,EACnBlV,MAAOmV,EAAiBtU,OAAQuU,GAEpC,CAIAJ,EAAczU,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAI,EAAGsR,IACtCE,EAAc3U,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAI,EAAGwR,IAGtCC,EAAkB5U,KAAK4W,IAAI,KAAO5W,KAAKmD,IAAI,EAAGyR,IAC9CC,EAAmB7U,KAAK4W,IAAI,KAAO5W,KAAKmD,IAAI,EAAG0R,IAG/CJ,EAAckE,OAAOlE,EAAYS,QAAQ,IACzCP,EAAcgE,OAAOhE,EAAYO,QAAQ,IACzCN,EAAkB+D,OAAO/D,EAAgBM,QAAQ,IACjDL,EAAmB8D,OAAO9D,EAAiBK,QAAQ,IAGnD,IAAM8C,EAAU,CACd3V,IAAKyT,EAAWzT,IAChBsM,QAASmH,EAAWzT,IACpBlC,EAAGsU,EACHrU,EAAGuU,EACHlV,MAAOmV,EACPtU,OAAQuU,EACR4D,YAAaC,SAAS9X,EAAaJ,GAAI,IACvCmZ,WAAYjB,SAAStM,EAAK5L,GAAI,KAIhC1E,QAAQC,IAAI,UAAWic,EAAS,CAC9B4B,eAAaxR,EAAAA,EAAAA,GAAS4P,EAAQS,aAC9BoB,cAAYzR,EAAAA,EAAAA,GAAS4P,EAAQ2B,cAI/B,IAAMG,EAAkBrV,KAAK8D,SAAS,CACpCC,MAAM,EACNC,KAAM,YACNC,QAAS,kBACTC,WAAY,uBAIR/E,EAAQ2J,aAAaC,QAAQ,UAAY,GACzCnB,EAASD,EAAK5L,IAAM4L,EAAKqB,UAAY,GACrCsM,EAAW3N,EAAKsN,MAAQ,OAExBlO,EAAU,CACd,eAAgB,mBAChB,OAAU,oBAIR5H,IACF4H,EAAQ,iBAAmB,UAAJ/P,OAAcmI,IAIvC4H,EAAQ,aAAea,EACvBb,EAAQ,eAAiBuO,EAEzBje,QAAQC,IAAI,WAAY,CACtBie,cAAepW,EAAQ,eAAiB,QACxC,YAAayI,EACb,cAAe0N,IAIjBrM,MAAM,oBAAqB,CACzB4D,OAAQ,OACR9F,QAASA,EACT+F,KAAMlE,KAAKmE,UAAUwG,GACrBvG,YAAa,YAEdvI,MAAK,SAAA8B,GAGJ,GAFAlP,QAAQC,IAAI,YAAaiP,EAAS6C,SAE7B7C,EAAS2C,GACZ,MAAM,IAAIC,MAAM,uBAADnS,OAAwBuP,EAAS6C,SAElD,OAAO7C,EAAS8C,MAClB,IACC5E,MAAK,SAAA3J,GACJua,EAAgB1Q,QAChBtN,QAAQC,IAAI,UAAWwD,GACvBwZ,EAAKxP,SAASmI,QAAQ,UAGtBoE,EAAWgD,KAAOvZ,EAAKiB,GAGvBsV,EAAWrB,YAAcA,EACzBqB,EAAWnB,YAAcA,EACzBmB,EAAWlB,gBAAkBA,EAC7BkB,EAAWjB,iBAAmBA,EAG9BkE,EAAKhI,8BAA8B7H,MAAK,WACtC6P,EAAKtR,0BACP,GACF,IAAC,UACM,SAAA4B,GACLvN,QAAQuN,MAAM,UAAWA,GAGzBvN,QAAQC,IAAI,gBAEZ2R,MAAM,YAAa,CACjB4D,OAAQ,OACR9F,QAASA,EACT+F,KAAMlE,KAAKmE,UAAUwG,GACrBvG,YAAa,YAEdvI,MAAK,SAAA8B,GACJ,IAAKA,EAAS2C,GACZ,MAAM,IAAIC,MAAM,oBAADnS,OAAqBuP,EAAS6C,SAE/C,OAAO7C,EAAS8C,MAClB,IACC5E,MAAK,SAAA3J,GACJua,EAAgB1Q,QAChBtN,QAAQC,IAAI,gBAAiBwD,GAC7BwZ,EAAKxP,SAASmI,QAAQ,UAGtBoE,EAAWgD,KAAOvZ,EAAKiB,GAGvBsV,EAAWrB,YAAcA,EACzBqB,EAAWnB,YAAcA,EACzBmB,EAAWlB,gBAAkBA,EAC7BkB,EAAWjB,iBAAmBA,EAG9BkE,EAAKhI,8BAA8B7H,MAAK,WACtC6P,EAAKtR,0BACP,GACF,IAAC,UACM,SAAAwS,GACLH,EAAgB1Q,QAChBtN,QAAQuN,MAAM,WAAY4Q,GAG1BlB,EAAKxP,SAASI,QAAQ,wBAGtBmM,EAAWrB,YAAcA,EACzBqB,EAAWnB,YAAcA,EACzBmB,EAAWlB,gBAAkBA,EAC7BkB,EAAWjB,iBAAmBA,CAChC,GACF,GACF,IAAC,wBACWxS,GAEV,IAAM6X,EAAW,CACf,YAAa,UACb,mBAAoB,UACpB,mBAAoB,UACpB,kBAAmB,UACnB,kBAAmB,UACnB,YAAa,UACb,aAAc,UACd,YAAa,UACb,UAAW,UACX,YAAa,UACb,IAAO,UACP,KAAQ,UACR,IAAO,UACP,GAAM,WAGR,OAAOA,EAAS7X,IAAQ,SAC1B,IAAC,6BACgB7B,GAAI,IAAA2Z,EAAA,KAEbC,EAAc3V,KAAKe,YAAYrG,OACrCrD,QAAQC,IAAI,kBAADN,OAAmB2e,EAAW,cAAA3e,OAAa+E,IAEtD,IAAM2B,EAAQsC,KAAKe,YAAY2R,WAAU,SAAAkD,GAAG,OAAKA,EAAK7Z,KAAOA,CAAE,IAC/D,IAAe,IAAX2B,EAAc,CAChB,IAAM2T,EAAarR,KAAKe,YAAYrD,GAG9BmG,EAAU7D,KAAK8D,SAAS,CAC5BC,MAAM,EACNC,KAAM,YACNC,QAAS,kBACTC,WAAY,uBAQd,GAJAlE,KAAKe,YAAY8U,OAAOnY,EAAO,GAC/BrG,QAAQC,IAAI,uBAADN,OAAwBgJ,KAAKe,YAAYrG,SAGhD2W,EAAWgD,KAAM,CACnBhd,QAAQC,IAAI,iBAAkB+Z,EAAWgD,MAGzC,IAAMyB,GAAiBpF,EAAAA,EAAAA,GAAA,GAAQW,GAE/BxK,EAAAA,WAAI+C,KAAI,UAAQyH,EAAWgD,MACxB5P,MAAK,WACJpN,QAAQC,IAAI,cACZoe,EAAK5Q,SAASmI,QAAQ,SACtBpJ,EAAQc,QAGRtN,QAAQC,IAAI,cAADN,OAAe0e,EAAK3U,YAAYrG,QAC7C,IAAC,UACM,SAAAkK,GACLvN,QAAQuN,MAAM,SAAUA,GAGpBA,EAAM2B,UACRlP,QAAQuN,MAAM,SAAUA,EAAM2B,SAAS6C,OAAQxE,EAAM2B,SAASzL,MAGhC,MAA1B8J,EAAM2B,SAAS6C,OACjBsM,EAAK5Q,SAASF,MAAM,cACe,MAA1BA,EAAM2B,SAAS6C,OACxBsM,EAAK5Q,SAASF,MAAM,cAEpB8Q,EAAK5Q,SAASF,MAAM,SAAD5N,OAAU4N,EAAM2B,SAASzL,MAAQ,WAE7C8J,EAAMmR,QACfL,EAAK5Q,SAASF,MAAM,iBAEpB8Q,EAAK5Q,SAASF,MAAM,WAAaA,EAAM+F,SAGzC9G,EAAQc,QAGRtN,QAAQC,IAAI,WAADN,OAAY0G,EAAK,cAAA1G,OAAa0e,EAAK3U,YAAYrG,SAC1Dgb,EAAK3U,YAAY8U,OAAOnY,EAAO,EAAGoY,GAClCze,QAAQC,IAAI,YAADN,OAAa0e,EAAK3U,YAAYrG,SAGzCgb,EAAKM,8BACP,GACJ,MAEEhW,KAAK8E,SAASmI,QAAQ,SACtBpJ,EAAQc,QACRtN,QAAQC,IAAI,oBAADN,OAAqBgJ,KAAKe,YAAYrG,QAErD,MACErD,QAAQuN,MAAM,UAAD5N,OAAW+E,EAAE,SAC1BiE,KAAK8E,SAASI,QAAQ,YAE1B,IAAC,2CAIC7N,QAAQC,IAAI,gBAGZ,IAAM2e,EAAQ,IAAIC,IACZC,EAAa,GAYnB,GATAnW,KAAKe,YAAYqV,SAAQ,SAAC/E,EAAY3T,GAChCuY,EAAMI,IAAIhF,EAAWtV,IACvBoa,EAAW7E,KAAK5T,GAEhBuY,EAAMK,IAAIjF,EAAWtV,GAAI2B,EAE7B,IAGIyY,EAAWzb,OAAS,EAAG,CACzBrD,QAAQ+S,KAAK,MAADpT,OAAOmf,EAAWzb,OAAM,gBACpC,IAAK,IAAI6b,EAAIJ,EAAWzb,OAAS,EAAG6b,GAAK,EAAGA,IAC1CvW,KAAKe,YAAY8U,OAAOM,EAAWI,GAAI,GAEzCvW,KAAK8E,SAASI,QAAQ,SAADlO,OAAUmf,EAAWzb,OAAM,UAClD,CACF,IAAC,0BACmB,IAAA8b,EAAA,YAAArQ,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAoQ,IAAA,OAAArQ,EAAAA,EAAAA,KAAAK,GAAA,SAAAiQ,GAAA,eAAAA,EAAA/P,GAAA,cAAA+P,EAAA/T,EAAA,EACX,IAAI2E,QAAO,eAAAqP,GAAAxQ,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAC,SAAAuQ,EAAOtN,EAAS/B,GAAM,IAAAI,EAAAC,EAAA0N,EAAA5R,EAAAvH,EAAA4D,EAAA8D,EAAAS,EAAAuS,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAApR,EAAAA,EAAAA,KAAAK,GAAA,SAAAgR,GAAA,eAAAA,EAAA9Q,GAAA,OASrC,OATqC8Q,EAAA7Q,EAAA,EAGrC4P,EAAKnU,UAAW,EAGhBhL,QAAQC,IAAI,2BACZD,QAAQC,IAAI,wBAAyBkf,EAAKzV,YAAYrG,QAEtD+c,EAAA7Q,EAAA,EAAA6Q,EAAA9Q,EAAA,EAEQ6P,EAAKkB,SAAS,YAAa,OAAQ,CACvCC,kBAAmB,KACnBC,iBAAkB,KAClBnf,KAAM,SACN,OACFpB,QAAQC,IAAI,wBAAuBmgB,EAAA9Q,EAAA,eAKrB,OALqB8Q,EAAA7Q,EAAA,EAAA6Q,EAAAzQ,EAGnC3P,QAAQC,IAAI,yBACZkf,EAAKnU,UAAW,EAChBiH,GAAQ,GAAMmO,EAAA9U,EAAA,UAkD+C,GA9C/DtL,QAAQC,IAAI,yBAA0B+L,OAAOwC,SAASgS,MAGhDlQ,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ/J,EAAOC,aAAaC,WAAa,MACxE0I,EAASD,EAAK5L,IAAM4L,EAAKqB,UAAY,GACrCsM,EAAW3N,EAAKsN,MAAQ,OAChBnM,aAAaC,QAAQ/J,EAAOC,aAAaE,OAGvDgF,eAAeC,QAAQpF,EAAOC,aAAaG,UAAW,QACtD+E,eAAeC,QAAQpF,EAAOC,aAAaI,sBAAuB,QAElEhI,QAAQC,IAAI,eAAgBsQ,EAAQ,MAAO0N,GAGvC5R,EAAc8S,EAAK9S,YAGlBA,IACHA,EAAc8S,EAAKvW,OAAOE,MAAMuD,aAAe8S,EAAKvW,OAAOC,OAAOnE,KAI/D2H,GAAe8S,EAAKzW,UACvB2D,EAAc8S,EAAKzW,QACnB1I,QAAQC,IAAI,0BAA2BoM,IAInCvH,EAAeqa,EAAK3V,gBAAkB2V,EAAK3V,eAAenG,OAAS,EACrD8b,EAAK3V,eAAe2V,EAAK1V,mBAAqB,KAE7D3E,GAAiBA,EAAaJ,GAOjC1E,QAAQC,IAAI,wBAAyB,CACnCyE,GAAII,EAAaJ,GACjBjF,IAAKqF,EAAarF,OARpBO,QAAQuN,MAAM,4BAA6B,CACzCkT,qBAAsBtB,EAAK3V,gBAAkB2V,EAAK3V,eAAenG,OAAS,GAC1EoG,kBAAmB0V,EAAK1V,oBAE1B0V,EAAK1R,SAASI,QAAQ,2BASlBnF,GAAsB,OAAZ5D,QAAY,IAAZA,OAAY,EAAZA,EAAcJ,KAAM2H,GAAe8S,EAAKzW,QAEnDA,EAAS,CAAF0X,EAAA9Q,EAAA,QAGI,OAFd6P,EAAK1R,SAASF,MAAM,sBACpB4R,EAAKnU,UAAW,EAChBiH,GAAQ,GAAMmO,EAAA9U,EAAA,UAMhB,GAFAtL,QAAQC,IAAI,yBAA0ByI,KAGlCyW,EAAKzV,aAAeyV,EAAKzV,YAAYrG,OAAS,GAAC,CAAA+c,EAAA9Q,EAAA,SAAA8Q,EAAA7Q,EAAA,EAGzC/C,EAAU2S,EAAK1S,SAAS,CAC5BC,MAAM,EACNC,KAAM,eACNC,QAAS,kBACTC,WAAY,uBAIRI,EAAa,EACfuS,EAAe,EAEfC,GAAc,EACdC,EAAe,GAAE,YAEdF,EAAevS,IAAewS,EAAW,CAAAW,EAAA9Q,EAAA,SAI5C,OAJ4C8Q,EAAA7Q,EAAA,EAE5CvP,QAAQC,IAAI,0BAADN,OAA2B6f,EAAe,EAAC,SAEtDY,EAAA9Q,EAAA,EACM6P,EAAKuB,4BAA4BhY,GAAS,GAAK,OACrD1I,QAAQC,IAAI,0BAGZwf,GAAc,EAGdhO,aAAa1E,QAAQ,cAAewE,KAAKmE,UAAUyJ,EAAKzV,cACxDyV,EAAK1R,SAASmI,QAAQ,SAAQwK,EAAA9Q,EAAA,iBAmB9B,OAnB8B8Q,EAAA7Q,EAAA,GAAAwQ,EAAAK,EAAAzQ,EAI9B3P,QAAQuN,MAAM,mBAAD5N,OAAoB6f,EAAe,EAAC,aAAAO,GAI/CL,EADEK,EAAM7Q,SACG,UAAAvP,OAAcogB,EAAM7Q,SAAS6C,OAAM,OAAApS,OACb,kBAAxBogB,EAAM7Q,SAASzL,KAClBsc,EAAM7Q,SAASzL,MACK,QAAnBkc,EAAAI,EAAM7Q,SAASzL,YAAI,IAAAkc,OAAA,EAAnBA,EAAqBpS,SAA4B,QAAxBqS,EAAKG,EAAM7Q,SAASzL,YAAI,IAAAmc,OAAA,EAAnBA,EAAqBtM,UAAW/B,KAAKmE,UAAUqK,EAAM7Q,SAASzL,OAE1Fsc,EAAMrB,QACA,SAEAqB,EAAMzM,SAAW,OAGlC8M,EAAA9Q,EAAA,GACM,IAAIW,SAAQ,SAAAgC,GAAM,OAAKzE,WAAWyE,EAAS,KAAQuN,EAAe,GAAG,IAAC,QAC5EA,IAAc,QAAAY,EAAA9Q,EAAA,gBAKH,GAAf9C,EAAQc,QAEHmS,EAAa,CAAFW,EAAA9Q,EAAA,SAId,OAHAtP,QAAQuN,MAAM,2BACd4R,EAAK1R,SAASF,MAAM,WAAD5N,OAAY+f,IAE/BU,EAAA7Q,EAAA,GAAA6Q,EAAA9Q,EAAA,GAEQ6P,EAAKkB,SAAS,sBAAuB,KAAM,CAC/CC,kBAAmB,KACnBC,iBAAkB,SAClBnf,KAAM,YACN,QACFpB,QAAQC,IAAI,6BACZmgB,EAAA9Q,EAAA,iBAIc,OAJd8Q,EAAA7Q,EAAA,GAAA6Q,EAAAzQ,EAEA3P,QAAQC,IAAI,4BACZkf,EAAKnU,UAAW,EAChBiH,GAAQ,GAAMmO,EAAA9U,EAAA,WAAA8U,EAAA9Q,EAAA,iBAQlB,OARkB8Q,EAAA7Q,EAAA,GAAAyQ,EAAAI,EAAAzQ,EAKlB3P,QAAQuN,MAAM,6BAA4ByS,GAC1Cb,EAAK1R,SAASF,MAAM,eAAiByS,EAAM1M,SAAW,SAEtD8M,EAAA7Q,EAAA,GAAA6Q,EAAA9Q,EAAA,GAEQ6P,EAAKkB,SAAS,sBAAuB,KAAM,CAC/CC,kBAAmB,KACnBC,iBAAkB,SAClBnf,KAAM,YACN,QAAAgf,EAAA9Q,EAAA,iBAGY,OAHZ8Q,EAAA7Q,EAAA,GAAA6Q,EAAAzQ,EAEFwP,EAAKnU,UAAW,EAChBiH,GAAQ,GAAMmO,EAAA9U,EAAA,WAAA8U,EAAA9Q,EAAA,iBAKlBtP,QAAQC,IAAI,+BAA8B,eAAAmgB,EAAA9Q,EAAA,GAItC,IAAIW,SAAQ,SAAAgC,GAAM,OAAKzE,WAAWyE,EAAStK,EAAOU,GAAGC,gBAAgB,IAAC,QAW5E,IARK+D,GAAe8S,EAAK1V,mBAAqB,GAAK0V,EAAK3V,gBAAkB2V,EAAK3V,eAAenG,OAAS,IAC/FyB,EAAeqa,EAAK3V,eAAe2V,EAAK1V,mBAC1C3E,GAAgBA,EAAaJ,KAC/B2H,EAAcvH,EAAaJ,GAC3B1E,QAAQC,IAAI,yBAA0BoM,KAKrCA,EAAa,CAAF+T,EAAA9Q,EAAA,SAYA,OAXd6P,EAAK1R,SAASF,MAAM,gBACpBvN,QAAQuN,MAAM,qBACdvN,QAAQC,IAAI,SAAU,CACpB0gB,iBAAkBxB,EAAK9S,YACvBuU,YAAazB,EAAKvW,OAAOE,MAAMuD,YAC/BwU,aAAc1B,EAAKvW,OAAOC,OAAOnE,GACjCgE,QAASyW,EAAKzW,QACde,kBAAmB0V,EAAK1V,kBACxBgX,qBAAsBtB,EAAK3V,gBAAkB2V,EAAK3V,eAAenG,OAAS,KAE5E8b,EAAKnU,UAAW,EAChBiH,GAAQ,GAAMmO,EAAA9U,EAAA,WAQiB,OAJjCtL,QAAQC,IAAI,oBAAqBoM,GAG3ByT,EAAI,GAAAngB,OAAOgI,EAAOQ,OAAOC,eAAc,iBAAAzI,OAAgB0M,GAC7DrM,QAAQC,IAAI,cAAe6f,GAAMM,EAAA7Q,EAAA,GAAA6Q,EAAA9Q,EAAA,GAGzB6P,EAAK2B,QAAQ7G,KAAK6F,GAAM,QAC9B9f,QAAQC,IAAI,aACZgS,GAAQ,GAAKmO,EAAA9Q,EAAA,iBAIb,OAJa8Q,EAAA7Q,EAAA,GAAA0Q,EAAAG,EAAAzQ,EAEb3P,QAAQuN,MAAM,eAAc0S,GAE5BG,EAAA7Q,EAAA,GAAA6Q,EAAA9Q,EAAA,GAEQ6P,EAAK2B,QAAQC,QAAQjB,GAAM,QACjC9f,QAAQC,IAAI,eACZgS,GAAQ,GAAKmO,EAAA9Q,EAAA,iBAAA8Q,EAAA7Q,EAAA,GAAA2Q,EAAAE,EAAAzQ,EAEb3P,QAAQuN,MAAM,gBAAe2S,GAC7Bf,EAAK1R,SAASF,MAAM,qBAGpB4R,EAAKnU,UAAW,EAChB8B,eAAeC,QAAQpF,EAAOC,aAAaK,iBAAkBoE,GAC7DmB,YAAW,WACTxB,OAAOwC,SAASgS,KAAOV,CACzB,GAAG,KACH7N,GAAQ,GAAM,QAAAmO,EAAA9Q,EAAA,iBAAA8Q,EAAA7Q,EAAA,GAAA4Q,EAAAC,EAAAzQ,EAIlB3P,QAAQuN,MAAM,sBAAqB4S,GACnChB,EAAK1R,SAASF,MAAM,YACpB2C,EAAMiQ,GAAO,QAKL,OALKC,EAAA7Q,EAAA,GAGb/B,YAAW,WACT2R,EAAKnU,UAAW,CAClB,GAAG,KAAKoV,EAAAY,EAAA,mBAAAZ,EAAA9U,EAAA,MAAAiU,EAAA,6EAEX,gBAAA0B,EAAAC,GAAA,OAAA5B,EAAAna,MAAA,KAAAC,UAAA,EA5PiB,KA4PhB,GAAAga,EAAA,IA7PgBtQ,EA8PpB,IAAC,8BAEuB,IAAAqS,EAAA,YAAArS,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAoS,IAAA,OAAArS,EAAAA,EAAAA,KAAAK,GAAA,SAAAiS,GAAA,eAAAA,EAAA/R,GAAA,cAAA+R,EAAA9R,EAAA,EAAA8R,EAAA/R,EAAA,EAEd6R,EAAKd,SAAS,sBAAuB,KAAM,CAC/CC,kBAAmB,KACnBC,iBAAkB,SAClBnf,KAAM,YACN,cAAAigB,EAAA/V,EAAA,GACK,GAAI,cAAA+V,EAAA9R,EAAA,EAAA8R,EAAA1R,EAAA0R,EAAA/V,EAAA,GAEJ,GAAK,GAAA8V,EAAA,iBATQtS,EAWxB,IAAC,8BAE6C,IAAAwS,EAAAlc,UAAAmc,EAAA,YAAAzS,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAwS,IAAA,IAAAC,EAAA3c,EAAA0H,EAAA0C,EAAArP,EAAA6hB,EAAAC,EAAAjC,EAAAvN,EAAAyP,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAjT,EAAAA,EAAAA,KAAAK,GAAA,SAAA6S,GAAA,eAAAA,EAAA3S,GAAA,OAEmB,GAF3CmS,EAAUH,EAAAje,OAAA,QAAAiW,IAAAgI,EAAA,GAAAA,EAAA,GAAI,SAE5Bxc,EAAeyc,EAAK/X,eAAe+X,EAAK9X,mBACzC3E,GAAiBA,EAAaJ,GAAE,CAAAud,EAAA3S,EAAA,QACD,OAAlCiS,EAAK9T,SAASF,MAAM,gBAAc0U,EAAA3W,EAAA,GAC3B,GAAI,WAITiW,EAAK5W,oBAAqB,CAAFsX,EAAA3S,EAAA,QACE,OAA5BtP,QAAQ+S,KAAK,iBAAekP,EAAA3W,EAAA,GACrB,GAAI,OAiBX,OAdFiW,EAAK5W,qBAAsB,EAGrB6B,EAAU+U,EAAK9U,SAAS,CAC5BC,MAAM,EACNC,KAAM8U,EACN7U,QAAS,kBACTC,WAAY,uBACboV,EAAA1S,EAAA,EAICvP,QAAQC,IAAI,aAAc6E,EAAaJ,IAEvCud,EAAA1S,EAAA,EAAA0S,EAAA3S,EAAA,EAEyBE,EAAAA,WAAI+C,KAAK2P,mBAAmBpd,EAAaJ,IAAE,OAA5DwK,EAAO+S,EAAAtS,EACb3P,QAAQC,IAAI,YAAaiP,EAASzL,MAAIwe,EAAA3S,EAAA,gBAItC,GAJsC2S,EAAA1S,EAAA,EAAAqS,EAAAK,EAAAtS,EAEtC3P,QAAQuN,MAAM,YAAWqU,KAGrBA,EAAgB1S,UAAY0S,EAAgB1S,SAAS6C,QAAU,KAAG,CAAAkQ,EAAA3S,EAAA,SAErC,OAFqC2S,EAAA1S,EAAA,EAElEvP,QAAQC,IAAI,mBAAiBgiB,EAAA3S,EAAA,EACvBE,EAAAA,WAAI+C,KAAK4P,2BAA2B,CAAExF,YAAa7X,EAAaJ,KAAI,OAC1E1E,QAAQC,IAAI,YAAUgiB,EAAA3S,EAAA,gBAEiB,MAFjB2S,EAAA1S,EAAA,EAAAsS,EAAAI,EAAAtS,EAEtB3P,QAAQuN,MAAM,aAAYsU,GAAaA,EAAA,QAAAI,EAAA3S,EAAA,uBAAAsS,EAAA,QA2B3C,OA3B2CK,EAAA1S,EAAA,GAWrC1P,EAAM,IAAID,KACV8hB,EAAY,IAAIU,KAAKC,eAAe,QAAS,CACjDC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,UACRC,OAAQ,UACRC,QAAQ,EACRC,SAAU,kBACTC,OAAOjjB,GAGJ8hB,EAAU9hB,EAAIkjB,cACpB/iB,QAAQC,IAAI,UAAWyhB,EAAW,UAAWC,GAE7CM,EAAA1S,EAAA,GAAA0S,EAAA3S,EAAA,GAEQE,EAAAA,WAAIoF,OAAOoO,gBAAgBle,EAAaJ,GAAIid,GAAO,QACzD3hB,QAAQC,IAAI,6BAA2BgiB,EAAA3S,EAAA,iBAGvC,GAHuC2S,EAAA1S,EAAA,GAAAuS,EAAAG,EAAAtS,EAEvC3P,QAAQuN,MAAM,YAAWuU,KAErBA,EAAU5S,UAAY4S,EAAU5S,SAASzL,MACzCqe,EAAU5S,SAASzL,KAAK3D,SAAS,OAAK,CAAAmiB,EAAA3S,EAAA,SACxCtP,QAAQC,IAAI,kBAAgBgiB,EAAA3S,EAAA,wBAAA2S,EAAA3S,EAAA,GAGtBE,EAAAA,WAAIoF,OAAOqO,aAAane,EAAaJ,GAAI,YAAU,QACzD1E,QAAQC,IAAI,8BAA4B,QAAAgiB,EAAA3S,EAAA,iBAAA2S,EAAA1S,EAAA,GAAAwS,EAAAE,EAAAtS,EAI5C3P,QAAQuN,MAAM,kBAAiBwU,GAC/B,QAQ6B,OAJ/BR,EAAK9T,SAASmI,QAAQ,SAGtBpJ,EAAQc,QACRiU,EAAK5W,qBAAsB,EAAIsX,EAAA3W,EAAA,GACxB,GAAG,QAyBV,OAzBU2W,EAAA1S,EAAA,GAAAyS,EAAAC,EAAAtS,EAEV3P,QAAQuN,MAAM,WAAUyU,GAGpBtC,EAAe,SACfsC,EAAM9S,UACRlP,QAAQuN,MAAM,SAAUyU,EAAM9S,SAAS6C,OAAQiQ,EAAM9S,SAASzL,MAE3B,kBAAxBue,EAAM9S,SAASzL,KACxBic,GAAgB,KAAOsC,EAAM9S,SAASzL,KAC7Bue,EAAM9S,SAASzL,MAAQue,EAAM9S,SAASzL,KAAK6P,QACpDoM,GAAgB,KAAOsC,EAAM9S,SAASzL,KAAK6P,QAE3CoM,GAAgB,UAAYsC,EAAM9S,SAAS6C,OAAS,KAE7CiQ,EAAM1O,UACfoM,GAAgB,KAAOsC,EAAM1O,SAI/BiO,EAAK9T,SAASF,MAAMmS,GACpBlT,EAAQc,QACRiU,EAAK5W,qBAAsB,EAE3BsX,EAAA1S,EAAA,GAAA0S,EAAA3S,EAAA,GAEuBiS,EAAKlB,SAAS,2BAA4B,KAAM,CACnEC,kBAAmB,KACnBC,iBAAkB,SAClBnf,KAAM,YACP,QAJU,GAAL+Q,EAAK8P,EAAAtS,EAOI,YAAXwC,EAAoB,CAAA8P,EAAA3S,EAAA,SAE2C,OADjEmC,aAAa1E,QAAQ,cAAewE,KAAKmE,UAAU6L,EAAK7X,cACxD+H,aAAa1E,QAAQ,iBAAkBjI,EAAaJ,GAAG2J,YAAU4T,EAAA3W,EAAA,GAC1D,GAAG,eAAA2W,EAAA3W,EAAA,GAEL,GAAI,eAAA2W,EAAA1S,EAAA,GAAA0S,EAAAtS,EAAAsS,EAAA3W,EAAA,GAGJ,GAAI,GAAAkW,EAAA,sDA1I6B1S,EA6I9C,IAAC,0CAC6B,IAAAoU,EAAA,KAG5B,OAFAljB,QAAQC,IAAI,mBAAoB0I,KAAKD,SAE9B,IAAIuH,SAAQ,SAACgC,EAAS/B,GAE3B,IAAMI,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClD5J,EAAQ2J,aAAaC,QAAQ,UAAY,GACzCnB,EAASD,EAAK5L,IAAM4L,EAAKqB,UAAY,GACrCsM,EAAW3N,EAAKsN,MAAQ,OAGxBlO,EAAU,CACd,OAAU,mBACV,gBAAiB,WACjB,eAAgB,oBAId5H,IACF4H,EAAQ,iBAAmB,UAAJ/P,OAAcmI,IAIvC4H,EAAQ,aAAea,EACvBb,EAAQ,eAAiBuO,EAEzBje,QAAQC,IAAI,kBAAmB,CAC7Bie,cAAepW,EAAQ,eAAiB,QACxC,YAAayI,EACb,cAAe0N,IAIjB,IAAMve,EAAYE,KAAKC,MACjB8O,EAASzK,KAAKyK,SACdD,EAAU,KAAA/O,OAASD,EAAS,OAAAC,OAAMgP,GAGlClP,EAAE,2BAAAE,OAA+BujB,EAAKxa,QAAO,KAAA/I,OAAI+O,EAAW,YAAA/O,OAAW4Q,GAE7EqB,MAAMnS,EAAK,CACT+V,OAAQ,MACR9F,QAASA,EACTiG,YAAa,YAEZvI,MAAK,SAAA8B,GACJ,IAAKA,EAAS2C,GACZ,MAAM,IAAIC,MAAM,uBAADnS,OAAwBuP,EAAS6C,SAElD,OAAO7C,EAAS8C,MAClB,IACC5E,MAAK,SAAAmF,GACJvS,QAAQC,IAAI,eAAgBsS,GAG5B2Q,EAAK3Z,cAAgBgJ,EAAKG,KAAI,SAAAnM,GAAE,MAAM,CACpC7B,GAAI6B,EAAI7B,GACR6B,IAAKA,EAAIsM,SAAWtM,EAAIA,IACxBgC,KAAMhC,EAAIsM,SAAWtM,EAAIA,IACzBlC,EAAGkC,EAAIlC,EACPC,EAAGiC,EAAIjC,EACPX,MAAO4C,EAAI5C,MACXa,OAAQ+B,EAAI/B,OACZqZ,WAAYtX,EAAIsX,WACjB,IAED7d,QAAQC,IAAI,YAAaijB,EAAK3Z,eAC9B0I,EAAQiR,EAAK3Z,cACf,IAAC,UACM,SAAAgE,GACLvN,QAAQuN,MAAM,YAAaA,GAG3BvN,QAAQC,IAAI,gBACZ,IAAM+Q,EAAQ,mBAAArR,OAAuBujB,EAAKxa,QAAO,KAAA/I,OAAI+O,EAAW,YAAA/O,OAAW4Q,GAE3EqB,MAAMZ,EAAW,CACfwE,OAAQ,MACR9F,QAASA,EACTiG,YAAa,YAEZvI,MAAK,SAAA8B,GACJ,IAAKA,EAAS2C,GACZ,MAAM,IAAIC,MAAM,oBAADnS,OAAqBuP,EAAS6C,SAE/C,OAAO7C,EAAS8C,MAClB,IACC5E,MAAK,SAAAmF,GACJvS,QAAQC,IAAI,iBAAkBsS,GAG9B2Q,EAAK3Z,cAAgBgJ,EAAKG,KAAI,SAAAnM,GAAE,MAAM,CACpC7B,GAAI6B,EAAI7B,GACR6B,IAAKA,EAAIsM,SAAWtM,EAAIA,IACxBgC,KAAMhC,EAAIsM,SAAWtM,EAAIA,IACzBlC,EAAGkC,EAAIlC,EACPC,EAAGiC,EAAIjC,EACPX,MAAO4C,EAAI5C,MACXa,OAAQ+B,EAAI/B,OACZqZ,WAAYtX,EAAIsX,WACjB,IAED7d,QAAQC,IAAI,YAAaijB,EAAK3Z,eAC9B0I,EAAQiR,EAAK3Z,cACf,IAAC,UACM,SAAA4U,GACLne,QAAQuN,MAAM,WAAY4Q,GAG1B,IAAMgF,EAAmB1R,aAAaC,QAAQ,eAAD/R,OAAgBujB,EAAKxa,UAClE,GAAIya,EACF,IACE,IAAMC,EAAoB7R,KAAKC,MAAM2R,GACrCnjB,QAAQC,IAAI,eAAgBmjB,GAC5BF,EAAK3Z,cAAgB6Z,EACrBnR,EAAQiR,EAAK3Z,cACf,CAAE,MAAO8Z,GACPrjB,QAAQuN,MAAM,cAAe8V,GAC7BnT,EAAOiO,EACT,MAEAjO,EAAOiO,EAEX,GACJ,GACJ,GACF,KAACpC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA7N,EAAA,uCAC0B8L,GAAY,IAAAsJ,EAAA,KAE/BhT,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACxD,GAAKpB,IAAUA,EAAK5L,IAAO4L,EAAKqB,UAAhC,CAKA,IAAMpB,EAASD,EAAK5L,IAAM4L,EAAKqB,SACzBsM,EAAW3N,EAAKsN,MAAQ,OAG9B,GAAK5D,EAAWgD,KAOhB,GAAKrU,KAAKnD,YAAemD,KAAKlD,YAA9B,CAMA,IAAIkT,EAAaE,EAAaC,EAAiBC,EAG3CiB,EAAWvB,cAGbK,EAAkBkB,EAAWrW,MAAQgF,KAAKnD,WAC1CuT,EAAmBiB,EAAWxV,OAASmE,KAAKlD,YAC5CkT,EAAeqB,EAAW3V,EAAIsE,KAAKnD,WAAesT,EAAkB,EACpED,EAAemB,EAAW1V,EAAIqE,KAAKlD,YAAgBsT,EAAmB,IAGtEJ,EAAcqB,EAAW3V,EAAIsE,KAAKnD,WAClCqT,EAAcmB,EAAW1V,EAAIqE,KAAKlD,YAClCqT,EAAkBkB,EAAWrW,MAAQgF,KAAKnD,WAC1CuT,EAAmBiB,EAAWxV,OAASmE,KAAKlD,aAK9CkT,EAAczU,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAI,EAAGsR,IACtCE,EAAc3U,KAAK4W,IAAI,EAAG5W,KAAKmD,IAAI,EAAGwR,IAGtCC,EAAkB5U,KAAK4W,IAAI,KAAO5W,KAAKmD,IAAI,EAAGyR,IAC9CC,EAAmB7U,KAAK4W,IAAI,KAAO5W,KAAKmD,IAAI,EAAG0R,IAG/CJ,EAAckE,OAAOlE,EAAYS,QAAQ,IACzCP,EAAcgE,OAAOhE,EAAYO,QAAQ,IACzCN,EAAkB+D,OAAO/D,EAAgBM,QAAQ,IACjDL,EAAmB8D,OAAO9D,EAAiBK,QAAQ,IAGnD,IAAM8C,EAAU,CACd3V,IAAKyT,EAAWzT,IAChBlC,EAAGsU,EACHrU,EAAGuU,EACHlV,MAAOmV,EACPtU,OAAQuU,EACRwK,WAAYhT,EACZuM,aAAc9C,EAAWvB,aAAe,cAAgB,YAG1DzY,QAAQC,IAAI,UAAW,CACrByE,GAAIsV,EAAWgD,KACfN,MAAO,IAAF/c,OAAMgZ,EAAW,MAAAhZ,OAAKkZ,EAAW,KACtC2K,MAAO,GAAF7jB,OAAKmZ,EAAe,OAAAnZ,OAAMoZ,GAC/B0K,KAAMzJ,EAAWvB,aAAe,UAAY,QAI9C,IAAMjM,EAAU7D,KAAK8D,SAAS,CAC5BC,MAAM,EACNC,KAAM,YACNC,QAAS,kBACTC,WAAY,uBAIR6C,EAAU,CACd,eAAgB,mBAChB,OAAU,mBACV,YAAaa,EACb,cAAe0N,EACf,gBAAiB,YAIbve,EAAYE,KAAKC,MAGvB+R,MAAM,qBAADjS,OAAsBqa,EAAWgD,KAAI,OAAArd,OAAMD,EAAS,YAAAC,OAAW4Q,GAAU,CAC5EiF,OAAQ,MACR9F,QAASA,EACT+F,KAAMlE,KAAKmE,UAAUwG,GACrBvG,YAAa,YAEZvI,MAAK,SAAA8B,GACJ,IAAKA,EAAS2C,GACZ,MAAM,IAAIC,MAAM,uBAADnS,OAAwBuP,EAAS6C,SAElD,OAAO7C,EAAS8C,MAClB,IACC5E,MAAK,SAAA3J,GACJ+I,EAAQc,QACRtN,QAAQC,IAAI,QAASwD,GACrB6f,EAAK7V,SAASmI,QAAQ,SAGtBoE,EAAWrB,YAAcA,EACzBqB,EAAWnB,YAAcA,EACzBmB,EAAWlB,gBAAkBA,EAC7BkB,EAAWjB,iBAAmBA,EAG9BuK,EAAKI,2BAA2BJ,EAAK5a,QACvC,IAAC,UACM,SAAA6E,GACLvN,QAAQuN,MAAM,SAAUA,GACxBf,EAAQc,QAGRtN,QAAQC,IAAI,gBACZ2R,MAAM,aAADjS,OAAcqa,EAAWgD,KAAI,OAAArd,OAAMD,EAAS,YAAAC,OAAW4Q,GAAU,CACpEiF,OAAQ,MACR9F,QAASA,EACT+F,KAAMlE,KAAKmE,UAAUwG,GACrBvG,YAAa,YAEZvI,MAAK,SAAA8B,GACJ,IAAKA,EAAS2C,GACZ,MAAM,IAAIC,MAAM,oBAADnS,OAAqBuP,EAAS6C,SAE/C,OAAO7C,EAAS8C,MAClB,IACC5E,MAAK,SAAA3J,GACJzD,QAAQC,IAAI,gBAAiBwD,GAC7B6f,EAAK7V,SAASmI,QAAQ,SAGtBoE,EAAWrB,YAAcA,EACzBqB,EAAWnB,YAAcA,EACzBmB,EAAWlB,gBAAkBA,EAC7BkB,EAAWjB,iBAAmBA,EAG9BuK,EAAKI,2BAA2BJ,EAAK5a,QACvC,IAAC,UACM,SAAAyV,GACLne,QAAQuN,MAAM,WAAY4Q,GAC1BmF,EAAK7V,SAASF,MAAM,eACtB,GACJ,GAxIF,MAFE5E,KAAK8E,SAASF,MAAM,wBAPpBvN,QAAQ+S,KAAK,kBAAmBiH,EAPlC,MAFErR,KAAK8E,SAASF,MAAM,oBA2JxB,IAAC,wCAGiC7E,GAA4B,IAAAib,EAAAve,UAAAwe,EAAA,YAAA9U,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAA6U,IAAA,IAAAhY,EAAAW,EAAA0C,EAAAxP,EAAAokB,EAAAC,EAAA,OAAAhV,EAAAA,EAAAA,KAAAK,GAAA,SAAA4U,GAAA,eAAAA,EAAA1U,GAAA,OAAF,GAAjBzD,EAAQ8X,EAAAtgB,OAAA,QAAAiW,IAAAqK,EAAA,IAAAA,EAAA,GAC5Cjb,EAAS,CAAFsb,EAAA1U,EAAA,QAE4B,OADtCtP,QAAQuN,MAAM,mBACdqW,EAAKnW,SAASF,MAAM,mBAAkByW,EAAA1Y,EAAA,aAKnCO,IAAa+X,EAAK1Y,eAAc,CAAA8Y,EAAA1U,EAAA,QACL,OAA9BtP,QAAQC,IAAI,mBAAkB+jB,EAAA1Y,EAAA,UAkB9B,OAbEsY,EAAK1Y,iBACP+Y,aAAaL,EAAK1Y,gBAClB0Y,EAAK1Y,eAAiB,MAGxBlL,QAAQC,IAAI,wBAAyByI,GAG/B8D,EAAUoX,EAAKnX,SAAS,CAC5BC,MAAM,EACNC,KAAM,cACNC,QAAS,kBACTC,WAAY,uBACZmX,EAAAzU,EAAA,EAAAyU,EAAA1U,EAAA,EAIuBE,EAAAA,WAAI+C,KAAK2P,mBAAmBxZ,GAAQ,OAArDwG,EAAO8U,EAAArU,EACb3P,QAAQC,IAAI,YAAaiP,EAASzL,MAG9ByL,EAASzL,MAAQyL,EAASzL,KAAKygB,eACjClkB,QAAQC,IAAI,cAAeiP,EAASzL,KAAKygB,eAEnCxkB,EAAYE,KAAKC,MACjBikB,EAAU,GAAAnkB,OAAOuP,EAASzL,KAAKygB,cAAa,OAAAvkB,OAAMD,GAGpDkkB,EAAK9e,eACP8e,EAAKzP,kBAAoB2P,GAG3BF,EAAKnW,SAASmI,QAAQ,YAEtBgO,EAAKnW,SAASI,QAAQ,qBAGxBrB,EAAQc,QAAO0W,EAAA1U,EAAA,eAAA0U,EAAAzU,EAAA,EAAAwU,EAAAC,EAAArU,EAEf3P,QAAQuN,MAAM,YAAWwW,GACzBH,EAAKnW,SAASF,MAAM,kBACpBf,EAAQc,QAAO,cAAA0W,EAAA1Y,EAAA,MAAAuY,EAAA,iBAvD2C/U,EAyD9D,IAAC,uCAE0B,IAAAqV,EAAA,KAEzB,GAAIxb,KAAKsC,qBACPjL,QAAQ+S,KAAK,kCAKf,GAAIpK,KAAKnD,YAAc,GAAKmD,KAAKlD,aAAe,EAC9CzF,QAAQ+S,KAAK,gBAAiB,CAC5B3D,EAAGzG,KAAKnD,WAAY4e,EAAGzb,KAAKlD,kBAFhC,CAOA,IAAKkD,KAAKY,eAA+C,IAA9BZ,KAAKY,cAAclG,OAG5C,OAFArD,QAAQC,IAAI,mBACZ0I,KAAKe,YAAc,IAIrB1J,QAAQC,IAAI,qBAAsB,OAAFN,OACdgJ,KAAKnD,WAAU,KAAA7F,OAAIgJ,KAAKlD,aAAW,OAAA9F,OACnCgJ,KAAKiC,cAAa,KAAAjL,OAAIgJ,KAAKkC,gBAClC,QAASlC,KAAKY,cAAclG,QAEvC,IAAMqI,EAAiB,GAGvB/C,KAAKY,cAAcwV,SAAQ,SAAAsF,GACzB,IAAMxR,EAAUwR,EAAMxR,SAAWwR,EAAM9d,KAAO,YAGxC+d,EAAcC,WAAWF,EAAMhgB,IAAM,EACrCmgB,EAAcD,WAAWF,EAAM/f,IAAM,EACrCmgB,EAAYF,WAAWF,EAAM1gB,QAAU,EACvC+gB,EAAaH,WAAWF,EAAM7f,SAAW,EAG/C,GAAIigB,GAAa,GAAKC,GAAc,EAClC1kB,QAAQ+S,KAAK,MAADpT,OAAO0kB,EAAM3f,GAAE,qBAD7B,CAMA,IAAMwU,EAAauL,EAAYN,EAAK3e,WAC9B2T,EAAcuL,EAAaP,EAAK1e,YAGhCkf,EAAeL,EAAcH,EAAK3e,WAClCof,EAAeJ,EAAcL,EAAK1e,YAGlCof,EAASF,EAAgBzL,EAAa,EACtC4L,EAASF,EAAgBzL,EAAc,EAE7CnZ,QAAQC,IAAI,QAADN,OAAS0kB,EAAM3f,GAAE,UAAA/E,OAASglB,EAAavL,QAAQ,GAAE,MAAAzZ,OAAKilB,EAAaxL,QAAQ,GAAE,aAAAzZ,OAAYklB,EAAOzL,QAAQ,GAAE,MAAAzZ,OAAKmlB,EAAO1L,QAAQ,GAAE,MAE3I,IAAMY,EAAa,CACjBtV,GAAI2f,EAAM3f,GACVsY,KAAMqH,EAAM3f,GACZ6B,IAAKsM,EACLxO,EAAGwgB,EACHvgB,EAAGwgB,EACHnhB,MAAOuV,EACP1U,OAAQ2U,EAERR,YAAa2L,EACbzL,YAAa2L,EACb1L,gBAAiB2L,EACjB1L,iBAAkB2L,EAClBnZ,WAAY4Y,EAAK1a,kBACjBrI,KAAM,YACNqX,cAAc,GAGhB/M,EAAeuO,KAAKD,EAlCpB,CAmCF,IAEArR,KAAKe,YAAcgC,EACnB/C,KAAKsC,sBAAuB,EAC5BtC,KAAK6B,mBAAoB,CApEzB,CAqEF,IAAC,qBACQ,IAAAua,EAAA,KAEP/kB,QAAQC,IAAI,uBAAwB+L,OAAOwC,SAASgS,MAEpD7X,KAAK0X,SAAS,4BAA6B,KAAM,CAC/CC,kBAAmB,KACnBC,iBAAkB,KAClBnf,KAAM,YACLgM,MAAK,WAMN,GAJAN,eAAeC,QAAQ,uBAAwB,QAC/C/M,QAAQC,IAAI,uCAGR8kB,EAAKjR,YAAa,CAEpB,IAAMtH,EAAUuY,EAAKtY,SAAS,CAC5BC,MAAM,EACNC,KAAM,UACNC,QAAS,kBACTC,WAAY,uBAId2C,EAAAA,WAAImE,WAAWqR,qBAAqBD,EAAKjR,aACtC1G,MAAK,SAAA8B,GACJlP,QAAQC,IAAI,YAAaiP,EAASzL,MAClC+I,EAAQc,QAERtN,QAAQC,IAAI,uCAGZ,IAAMglB,EAAYjZ,OAAOwC,SAASgS,KAElCuE,EAAKjE,QAAQ7G,KAAK,kBAAkB7M,MAAK,WACvCpN,QAAQC,IAAI,gBAAiBglB,EAAW,IAAKjZ,OAAOwC,SAASgS,KAC/D,IAAE,UAAO,SAAApJ,GACPpX,QAAQuN,MAAM,eAAgB6J,GAE9BpL,OAAOwC,SAASgS,KAAO,gBACzB,GACF,IAAC,UACM,SAAAjT,GACLvN,QAAQuN,MAAM,WAAYA,GAC1Bf,EAAQc,QAERyX,EAAKtX,SAASF,MAAM,qBAEpBvN,QAAQC,IAAI,yCACZ,IAAMglB,EAAYjZ,OAAOwC,SAASgS,KAElCuE,EAAKjE,QAAQ7G,KAAK,kBAAkB7M,MAAK,WACvCpN,QAAQC,IAAI,gBAAiBglB,EAAW,IAAKjZ,OAAOwC,SAASgS,KAC/D,IAAE,UAAO,SAAApJ,GACPpX,QAAQuN,MAAM,eAAgB6J,GAE9BpL,OAAOwC,SAASgS,KAAO,gBACzB,GACF,GACJ,KAAO,CAELxgB,QAAQC,IAAI,uCACZ,IAAMglB,EAAYjZ,OAAOwC,SAASgS,KAElCuE,EAAKjE,QAAQ7G,KAAK,kBAAkB7M,MAAK,WACvCpN,QAAQC,IAAI,gBAAiBglB,EAAW,IAAKjZ,OAAOwC,SAASgS,KAC/D,IAAE,UAAO,SAAApJ,GACPpX,QAAQuN,MAAM,eAAgB6J,GAE9BpL,OAAOwC,SAASgS,KAAO,gBACzB,GACF,CACF,IAAE,UAAO,WAEPxgB,QAAQC,IAAI,kBACd,GACF,IAAC,yCAC4ByI,EAAS2O,GACpCrX,QAAQC,IAAI,0BAA2ByI,GAEvC8G,EAAAA,WAAIoF,OAAOC,OAAOnM,GACf0E,MAAK,SAAA8B,GACAA,EAASzL,MAAQyL,EAASzL,KAAK4Q,MACjCrU,QAAQC,IAAI,2BAA4BiP,EAASzL,KAAK4Q,MACtDgD,EAASnI,EAASzL,KAAK4Q,QAEvBrU,QAAQuN,MAAM,0BACd8J,EAAS,MAEb,IAAC,UACM,SAAA9J,GACLvN,QAAQuN,MAAM,2BAA4BA,GAC1C8J,EAAS,KACX,GACJ,IAAC,gCACmBjC,EAAYf,GAC9B,GAAK1L,KAAKmL,aAAgBO,EAA1B,CAKArU,QAAQC,IAAI,wBAAyB,CACnC6T,YAAanL,KAAKmL,YAClBsB,WAAYA,EACZf,KAAMA,IAIR,IAAM6Q,EAAa,CACjBxgB,GAAIiE,KAAKmL,YACTsB,WAAYA,EAAW/G,WACvB2F,aAAcK,GAIhBzC,MAAM,mBAAoB,CACxB4D,OAAQ,OACR9F,QAAS,CACP,eAAgB,mBAChB,OAAU,oBAEZ+F,KAAMlE,KAAKmE,UAAUwP,GACrBvP,YAAa,YAEZvI,MAAK,SAAA8B,GACJ,IAAKA,EAAS2C,GACZ,MAAM,IAAIC,MAAM,uBAADnS,OAAwBuP,EAAS6C,SAElD,OAAO7C,EAAS8C,MAClB,IACC5E,MAAK,SAAA+E,GACJnS,QAAQC,IAAI,yBAA0BkS,EACxC,IAAC,UACM,SAAA5E,GACLvN,QAAQuN,MAAM,yBAA0BA,EAC1C,GApCF,MAFEvN,QAAQuN,MAAM,0BAuClB,IAAC,4BAEe7E,GAAS,IAAAyc,EAAA,KACvB,GAAKzc,EAAL,CAMAC,KAAK6B,mBAAoB,EAEzBxK,QAAQC,IAAI,eAAgByI,GAG5B,IAAM4H,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,UAAY,CAAC,EACpDnB,EAASD,EAAKqB,UAAYrB,EAAK5L,GAEhC6L,GACHvQ,QAAQ+S,KAAK,wBAIfpK,KAAK8E,SAAS2X,KAAK,eAGnB,IAAM1lB,GAAY,IAAIE,MAAO0R,UAC7BtR,QAAQC,IAAI,UAADN,OAAWD,EAAS,mBAG/B8P,EAAAA,WAAI+C,KAAK8S,aAAa3c,EAAS,QAC5B0E,MAAK,SAAA8B,GACJlP,QAAQC,IAAI,cAAeiP,EAASzL,MAGpC0hB,EAAK5b,cAAgB2F,EAASzL,MAAQ,GAGjC6hB,MAAMC,QAAQJ,EAAK5b,iBACtBvJ,QAAQuN,MAAM,qBAAsB4X,EAAK5b,eACzC4b,EAAK5b,cAAgB,IAIvBvJ,QAAQC,IAAI,SAADN,OAAUwlB,EAAK5b,cAAclG,OAAM,SAC1C8hB,EAAK5b,cAAclG,OAAS,EAC9B8hB,EAAK1X,SAASmI,QAAQ,OAADjW,OAAQwlB,EAAK5b,cAAclG,OAAM,SAEtD8hB,EAAK1X,SAAS2X,KAAK,qBAIjBD,EAAK3f,WAAa,GAAK2f,EAAK1f,YAAc,EAC5C0f,EAAKxZ,2BAEL3L,QAAQC,IAAI,uBAGhB,IAAC,UACM,SAAAsN,GACLvN,QAAQuN,MAAM,YAAaA,GAC3B4X,EAAK5b,cAAgB,GACrB4b,EAAK3a,mBAAoB,EACzB2a,EAAK1X,SAASF,MAAM,oBACtB,GAzDF,MAFEvN,QAAQuN,MAAM,iCA4DlB,IAAC,oCAKC,IAAM+C,EAAOiB,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,OACpDpB,EAAK5L,IAAM4L,EAAKqB,WAClB3R,QAAQC,IAAI,aAGhB,IAAC,+BAEkByI,GACjB1I,QAAQC,IAAI,4BAGZ,IAAMulB,EAAmB/T,aAAaC,QAAQ,iBAAD/R,OAAkB+I,IAC3D8c,GACFxlB,QAAQC,IAAI,YAGZ0I,KAAKmM,UAAY,CACfpQ,GAAIgE,EACJ2L,KAAMmR,GAIR7c,KAAKkL,UAAY,CACfnP,GAAI9E,KAAKC,MACTuV,WAAY1M,EACZsL,aAAcwR,GAIhB7c,KAAK8c,aAELzlB,QAAQ+S,KAAK,YAEjB,IAAC,yBAEY,IAAA2S,EAAA,KAEX/c,KAAK+O,MAAMtP,eAAeud,UAAS,SAAAC,GACjC,IAAIA,EA8CF,OAFA5lB,QAAQuN,MAAM,UACdmY,EAAKjY,SAASF,MAAM,iBACb,EA7CPvN,QAAQC,IAAI,iBAGZylB,EAAKzQ,8BAA8B7H,MAAK,WAEtC,IAAMZ,EAAUkZ,EAAKjZ,SAAS,CAC5BC,MAAM,EACNC,KAAM,cACNC,QAAS,kBACTC,WAAY,6BAIR9D,GAAOsQ,EAAAA,EAAAA,GAAA,GAASqM,EAAK3c,UAG3BA,EAASW,YAAcgc,EAAKnc,cAE5BvJ,QAAQC,IAAI,UAAW8I,GAGvByG,EAAAA,WAAIoF,OAAOiR,uBAAuBH,EAAKhd,QAASK,GAC7CqE,MAAK,SAAA8B,GACJlP,QAAQC,IAAI,YAAaiP,EAASzL,MAClC+I,EAAQc,QACRoY,EAAKjY,SAASmI,QAAQ,YAGtB8P,EAAK5E,QAAQ7G,KAAK,aACpB,IAAC,UACM,SAAA1M,GACLvN,QAAQuN,MAAM,YAAaA,GAC3Bf,EAAQc,QACRoY,EAAKjY,SAASF,MAAM,cAAgBA,EAAM+F,SAAW,QACvD,GACJ,IAAE,UAAO,SAAA/F,GACPvN,QAAQuN,MAAM,YAAaA,GAC3BmY,EAAKjY,SAASI,QAAQ,uBAGtB6X,EAAKI,gBACP,GAMJ,GACF,KAAC/J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA7N,EAAA,6BAGgB,IAAA6X,EAAA,KAETvZ,EAAU7D,KAAK8D,SAAS,CAC5BC,MAAM,EACNC,KAAM,cACNC,QAAS,kBACTC,WAAY,6BAIR9D,GAAOsQ,EAAAA,EAAAA,GAAA,GAAS1Q,KAAKI,UAE3B/I,QAAQC,IAAI,UAAW8I,GAGvByG,EAAAA,WAAIoF,OAAOiR,uBAAuBld,KAAKD,QAASK,GAC7CqE,MAAK,SAAA8B,GACJlP,QAAQC,IAAI,YAAaiP,EAASzL,MAClC+I,EAAQc,QACRyY,EAAKtY,SAASmI,QAAQ,YAGtBmQ,EAAKjF,QAAQ7G,KAAK,aACpB,IAAC,UACM,SAAA1M,GACLvN,QAAQuN,MAAM,YAAaA,GAC3Bf,EAAQc,QACRyY,EAAKtY,SAASF,MAAM,cAAgBA,EAAM+F,SAAW,QACvD,GACJ,IAAC,2BAEc,IAAA0S,EAAA,KACbhmB,QAAQC,IAAI,eAAgB0I,KAAKD,SAGjC8G,EAAAA,WAAIoF,OAAOC,OAAOlM,KAAKD,SACpB0E,MAAK,SAAA8B,GACJlP,QAAQC,IAAI,aAAciP,EAASzL,MACnC,IAAMqR,EAAY5F,EAASzL,KAGvBqR,EAAU1M,gBACZ4d,EAAKjd,UAAOsQ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACP2M,EAAKjd,UACL+L,EAAU1M,gBAEfpI,QAAQC,IAAI,cAAe+lB,EAAKjd,WAEhC/I,QAAQC,IAAI,cAEhB,IAAC,UACM,SAAAsN,GACLvN,QAAQuN,MAAM,YAAaA,GAC3ByY,EAAKvY,SAASI,QAAQ,kBACxB,GACJ,IAAC,yBAEkBxB,GAAa,IAAA4Z,EAAA,YAAAnX,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAkX,IAAA,IAAAhX,EAAAzL,EAAA0iB,EAAAC,EAAAtR,EAAAuR,EAAAC,EAAAC,EAAA,OAAAxX,EAAAA,EAAAA,KAAAK,GAAA,SAAAoX,GAAA,eAAAA,EAAAlX,GAAA,UACzBjD,EAAa,CAAFma,EAAAlX,EAAA,eAAAkX,EAAAlb,EAAA,EACP2E,QAAQC,OAAO,YAAU,OAGW,OAA7ClQ,QAAQC,IAAI,UAADN,OAAW0M,EAAW,cAAYma,EAAAjX,EAAA,EAAAiX,EAAAjX,EAAA,EAAAiX,EAAAlX,EAAA,EAIpBmX,IAAAA,IAAU,qCAAD9mB,OAAsC0M,IAAc,OAE3C,GAFnC6C,EAAOsX,EAAA7W,EACPlM,EAAOyL,EAASzL,KACpBzD,QAAQC,IAAI,qBAAsBwD,IAEhCA,IAAQA,EAAK4R,UAAS,CAAAmR,EAAAlX,EAAA,QAWgB,OAVlC6W,EAAW1iB,EAAK4R,UAEtB4Q,EAAKzc,eAAiB,CAAC,CACrB9E,GAAI2H,EACJ5M,IAAK0mB,EACL5R,SAAU,SAAF5U,OAAW0M,KAEnB4Z,EAAKnhB,aAAemhB,EAAKzc,eAAe,GACxCyc,EAAK9R,kBAAoBgS,EAEzBnmB,QAAQC,IAAI,gBAAiBkmB,GAASK,EAAAlb,EAAA,EAC/B2E,QAAQgC,WAAS,OAAAuU,EAAAlX,EAAA,eAAAkX,EAAAjX,EAAA,EAAA+W,EAAAE,EAAA7W,EAG1B3P,QAAQC,IAAI,6BAA8BqmB,EAAehT,SAAQ,OAIrB,OAA9CtT,QAAQC,IAAI,qBAAsBoM,GAAYma,EAAAlX,EAAA,EAClBmX,IAAAA,IAAU,eAAD9mB,OAAgB0M,GAAe,CAClEqD,QAAS,CACP,gBAAiB,qBACjB,OAAU,WACV,QAAW,OAEb,OAGuC,GATnC0W,EAAYI,EAAA7W,EAQZmF,EAAYsR,EAAc3iB,KAChCzD,QAAQC,IAAI,kBAAmB6U,IAE3BA,IAAcA,EAAUT,OAAQS,EAAUrV,IAAI,CAAA+mB,EAAAlX,EAAA,QAWV,OAVhC6W,EAAWrR,EAAUT,MAAQS,EAAUrV,IAE7CwmB,EAAKzc,eAAiB,CAAC,CACrB9E,GAAI2H,EACJ5M,IAAK0mB,EACL5R,SAAUO,EAAUC,eAAY,OAAApV,OAAY0M,KAE9C4Z,EAAKnhB,aAAemhB,EAAKzc,eAAe,GACxCyc,EAAK9R,kBAAoBgS,EAEzBnmB,QAAQC,IAAI,gBAAiBkmB,GAASK,EAAAlb,EAAA,EAC/B2E,QAAQgC,WAAS,aAElB,IAAIH,MAAM,eAAc,QAAA0U,EAAAlX,EAAA,iBAIiB,OAJjBkX,EAAAjX,EAAA,GAAAgX,EAAAC,EAAA7W,EAGhC3P,QAAQuN,MAAM,iBAAD5N,OAAkB0M,EAAW,MAAAka,GAC1CN,EAAKxY,SAASF,MAAM,aAAD5N,OAAc4mB,EAAMjT,UAAUkT,EAAAlb,EAAA,EAC1C2E,QAAQC,OAAMqW,IAAO,eAAAC,EAAAlb,EAAA,MAAA4a,EAAA,wBA/DApX,EAiEhC,IAAC,gCAEmB,IAAA4X,EAAA,KAClB1mB,QAAQC,IAAI,YAGZ0I,KAAKe,YAAc,GACnBf,KAAKY,cAAgB,GACrBZ,KAAK6B,mBAAoB,EAGzB7B,KAAK8E,SAAS2X,KAAK,iBAGnBzc,KAAKsM,8BACF7H,MAAK,WAEAsZ,EAAKlhB,WAAa,GAAKkhB,EAAKjhB,YAAc,GAC5CihB,EAAK/a,2BAIP+a,EAAKjZ,SAASmI,QAAQ,OAADjW,OAAQ+mB,EAAKnd,cAAclG,OAAM,QACxD,IAAC,UACM,SAAAkK,GACLvN,QAAQuN,MAAM,YAAaA,GAC3BmZ,EAAKjZ,SAASF,MAAM,mBACtB,GACJ,IAAC,kCAEqB2K,GAAO,IAAAyO,EAAA,KAC3B3mB,QAAQC,IAAI,WAAYiY,GAEpBA,IACFA,EAAM0O,kBACN1O,EAAMkC,kBAIRpa,QAAQC,IAAI,UAAW,CACrByI,QAASC,KAAKD,QACdme,eAAgBle,KAAKe,YAAYrG,OAAS,EAC1C2H,SAAUrC,KAAKqC,SACfvB,kBAAmBd,KAAKc,kBACxBgX,qBAAsB9X,KAAKa,gBAAkBb,KAAKa,eAAenG,OAAS,KAI5E,IAAMyjB,EAAoBtZ,YAAW,WACnCxN,QAAQC,IAAI,uCACZ0mB,EAAKI,sBACP,GAAG,KAGHpe,KAAKqe,cAAa,YAAS,WAEzB/C,aAAa6C,EACf,GACF,IAAC,mCAGC,IAEE,IAAMza,EAAc1D,KAAK0D,aAAe1D,KAAKC,OAAOE,MAAMuD,aAAe1D,KAAKC,OAAOC,OAAOnE,IAAMiE,KAAKD,QAEvG,IAAK2D,EAGH,OAFArM,QAAQuN,MAAM,mCACd5E,KAAK8E,SAASF,MAAM,kBACb,EAIT,IAAMuS,EAAI,GAAAngB,OAAOgI,EAAOQ,OAAOC,eAAc,iBAAAzI,OAAgB0M,GAK7D,OAJArM,QAAQC,IAAI,gCAAiC6f,GAG7C9T,OAAOwC,SAASgS,KAAOV,GAChB,CACT,CAAE,MAAOvS,GAEP,OADAvN,QAAQuN,MAAM,+BAAgCA,IACvC,CACT,CACF,IAAC,uCAE0B7E,GAAS,IAAAue,EAAA,KAClCjnB,QAAQC,IAAI,wBAGR0I,KAAKuC,iBACPlL,QAAQC,IAAI,cACZgkB,aAAatb,KAAKuC,iBAIpBvC,KAAKuC,eAAiBsC,YAAW,WAC/BxN,QAAQC,IAAI,kBACZgnB,EAAKvG,4BAA4BhY,GACjCue,EAAK/b,eAAiB,IACxB,GAAG,IACL,IAAC,0BAEmB,IAAAgc,EAAA,YAAApY,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAmY,IAAA,IAAA3a,EAAA1H,EAAAsiB,EAAA,OAAArY,EAAAA,EAAAA,KAAAK,GAAA,SAAAiY,GAAA,eAAAA,EAAA/X,GAAA,UACc,IAA5B4X,EAAKxd,YAAYrG,OAAY,CAAAgkB,EAAA/X,EAAA,QAEA,OAA/B4X,EAAKpG,QAAQ7G,KAAK,cAAaoN,EAAA/b,EAAA,UAY+B,GAP1DkB,EAAU0a,EAAKza,SAAS,CAC5BC,MAAM,EACNC,KAAM,SACNC,QAAS,kBACTC,WAAY,uBAGR/H,EAAeoiB,EAAK1d,eAAe0d,EAAKzd,mBACzC3E,GAAiBA,EAAaJ,GAAE,CAAA2iB,EAAA/X,EAAA,QAEpB,OADf4X,EAAKzZ,SAASF,MAAM,kBACpBf,EAAQc,QAAO+Z,EAAA/b,EAAA,UAOf,OAPe+b,EAAA9X,EAAA,EAKfvP,QAAQC,IAAI,6BAA8B6E,EAAaJ,IAEvD2iB,EAAA/X,EAAA,EACM4X,EAAKxG,4BAA4B5b,EAAaJ,IAAI,GAAK,OAE7D+M,aAAa1E,QAAQ,cAAewE,KAAKmE,UAAUwR,EAAKxd,cACxDwd,EAAKzZ,SAASmI,QAAQ,SAEtBpJ,EAAQc,QAGRmE,aAAa1E,QAAQ,gBAAiB,QACtCma,EAAKpG,QAAQ7G,KAAK,cAAaoN,EAAA/X,EAAA,eAAA+X,EAAA9X,EAAA,EAAA6X,EAAAC,EAAA1X,EAE/B3P,QAAQuN,MAAM,UAAS6Z,GACvBF,EAAKzZ,SAASF,MAAM,YAAc6Z,EAAM9T,SAAW,SACnD9G,EAAQc,QAAO,cAAA+Z,EAAA/b,EAAA,MAAA6b,EAAA,iBAvCCrY,EAyCpB,M,eCh2GJ,MAAMwY,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/utils/apiHelpers.js", "webpack://medical-annotation-frontend/./src/utils/imageHelper.js", "webpack://medical-annotation-frontend/./src/views/AnnotationAndForm.vue", "webpack://medical-annotation-frontend/./src/views/AnnotationAndForm.vue?fa1e"], "sourcesContent": ["/**\r\n * API 工具函数\r\n * 提供API请求相关的通用功能\r\n */\r\nimport { storageUtils } from './storeHelpers';\r\n\r\n/**\r\n * 标准化API路径，确保路径格式一致\r\n * @param {string} url 原始URL\r\n * @returns {string} 标准化后的URL\r\n */\r\nexport function normalizePath(url) {\r\n  if (!url) return '';\r\n  \r\n  // 确保URL以/开头\r\n  if (!url.startsWith('/')) {\r\n    url = '/' + url;\r\n  }\r\n  \r\n  // 处理双斜杠的情况\r\n  url = url.replace(/\\/+/g, '/');\r\n  \r\n  return url;\r\n}\r\n\r\n/**\r\n * 获取用户ID参数，优先使用数字ID\r\n * @param {string|number} userId 用户ID\r\n * @returns {string|number} 处理后的用户ID\r\n */\r\nexport function getUserIdParam(userId) {\r\n  return userId || '';\r\n}\r\n\r\n/**\r\n * 确保对象中的指定字段为数字类型\r\n * @param {object} obj 要处理的对象\r\n * @param {array} fields 要确保为数字的字段列表\r\n * @returns {object} 处理后的对象\r\n */\r\nexport function ensureNumericFields(obj, fields) {\r\n  if (!obj || typeof obj !== 'object') return obj;\r\n  \r\n  const result = { ...obj };\r\n  \r\n  fields.forEach(field => {\r\n    if (result[field] !== undefined) {\r\n      const parsedValue = parseInt(result[field], 10);\r\n      if (!isNaN(parsedValue)) {\r\n        result[field] = parsedValue;\r\n      }\r\n    }\r\n  });\r\n  \r\n  return result;\r\n}\r\n\r\n/**\r\n * 获取认证头信息\r\n * @returns {object} 包含认证信息的头部对象\r\n */\r\nexport function getAuthHeaders() {\r\n  const headers = {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json'\r\n  };\r\n  \r\n  // 从本地存储获取用户信息\r\n  const user = storageUtils.getFromStorage('user');\r\n  if (user) {\r\n    // 使用用户ID生成简化的认证头\r\n    const userId = user.id || user.customId || '';\r\n    headers['Authorization'] = `Bearer user_${userId}`;\r\n    headers['X-User-ID'] = userId;\r\n  }\r\n  \r\n  return headers;\r\n}\r\n\r\n/**\r\n * 处理API错误\r\n * @param {Error} error 错误对象\r\n * @returns {object} 格式化的错误信息\r\n */\r\nexport function handleApiError(error) {\r\n  const errorInfo = {\r\n    message: error.message || '未知错误',\r\n    status: error.response ? error.response.status : null,\r\n    data: error.response ? error.response.data : null\r\n  };\r\n  \r\n  // 记录错误信息\r\n  console.error('[API错误]', errorInfo);\r\n  \r\n  return errorInfo;\r\n}\r\n\r\n/**\r\n * 向URL添加时间戳，防止缓存\r\n * @param {string} url 原始URL\r\n * @returns {string} 添加时间戳后的URL\r\n */\r\nexport function addTimestamp(url) {\r\n  const timestamp = `_t=${Date.now()}`;\r\n  if (url.includes('?')) {\r\n    return `${url}&${timestamp}`;\r\n  } else {\r\n    return `${url}?${timestamp}`;\r\n  }\r\n}\r\n\r\nexport default {\r\n  normalizePath,\r\n  getUserIdParam,\r\n  ensureNumericFields,\r\n  getAuthHeaders,\r\n  handleApiError,\r\n  addTimestamp\r\n}; ", "/**\n * 图像路径处理工具\n * 用于统一处理图像URL，解决路径不一致、中文编码等问题\n */\n\nimport { API_BASE_URL, API_CONTEXT_PATH, isLocalhost } from '../config/api.config';\nimport { storageUtils } from './storeHelpers';\nimport { addTimestamp } from './apiHelpers';\n\n// 图像大小限制\nconst MAX_IMAGE_WIDTH = 700;\nconst MAX_IMAGE_HEIGHT = 700;\n\n/**\n * 获取图像URL，添加防缓存参数并处理不同的路径格式\n * @param {string} url 原始图像URL\n * @returns {string} 处理后的URL\n */\nexport function getImageUrl(url) {\n  if (!url) return '';\n  \n  console.log('[ImageHelper] 处理图像路径:', url);\n  \n  // 处理文件系统路径\n  if (/^[a-zA-Z]:\\\\/.test(url)) {\n    console.log('[ImageHelper] 检测到文件系统路径');\n    const encodedPath = encodeURIComponent(url);\n    \n    // 使用当前域名而非硬编码地址\n    const baseUrl = isLocalhost ? `${API_BASE_URL}` : '';\n    return addTimestamp(`${baseUrl}/medical/image/system-path?path=${encodedPath}`);\n  }\n  \n  // 如果URL已经是完整的HTTP(S)地址，直接使用\n  if (url.startsWith('http://') || url.startsWith('https://')) {\n    return addTimestamp(url);\n  }\n  \n  // 如果URL是data:开头的数据URI，直接返回\n  if (url.startsWith('data:')) {\n    return url;\n  }\n  \n  // 处理相对路径\n  let finalUrl = url;\n  \n  if (url.startsWith('/medical')) {\n    console.log('[ImageHelper] 检测到相对路径，添加后端服务器地址');\n    // 根据环境使用适当的基础URL\n    finalUrl = isLocalhost ? `${API_BASE_URL}${url}` : url;\n  } else if (url.startsWith('/')) {\n    console.log('[ImageHelper] 检测到其他相对路径，添加后端服务器地址和上下文路径');\n    // 根据环境使用适当的基础URL\n    finalUrl = isLocalhost ? `${API_BASE_URL}${API_CONTEXT_PATH}${url}` : `${API_CONTEXT_PATH}${url}`;\n  }\n  \n  console.log('[ImageHelper] 处理后的URL:', finalUrl);\n  return addTimestamp(finalUrl);\n}\n\n/**\n * 从图像路径中提取文件名\n * @param {string} path - 图像路径\n * @returns {string} 文件名\n */\nexport function getImageFilename(path) {\n  if (!path) return '';\n  \n  const parts = path.split('/');\n  return parts[parts.length - 1];\n}\n\n/**\n * 判断是否是图像文件\n * @param {string} filename - 文件名\n * @returns {boolean} 是否是图像文件\n */\nexport function isImageFile(filename) {\n  if (!filename) return false;\n  \n  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\n  const lowerFilename = filename.toLowerCase();\n  \n  return imageExtensions.some(ext => lowerFilename.endsWith(ext));\n}\n\n/**\n * 调整图像大小，确保不超过最大宽高限制\n * @param {HTMLImageElement|File} imageSource - 图像元素或文件对象\n * @param {Function} callback - 回调函数，接收处理后的图像数据(DataURL或Blob)和尺寸信息\n * @param {Object} options - 选项\n * @param {number} options.maxWidth - 最大宽度，默认700px\n * @param {number} options.maxHeight - 最大高度，默认700px\n * @param {string} options.outputFormat - 输出格式，'dataUrl'或'blob'，默认'dataUrl'\n * @param {string} options.imageType - 图像类型，默认'image/jpeg'\n * @param {number} options.quality - 压缩质量，0-1之间，默认0.9\n */\nexport function resizeImage(imageSource, callback, options = {}) {\n  const settings = {\n    maxWidth: options.maxWidth || MAX_IMAGE_WIDTH,\n    maxHeight: options.maxHeight || MAX_IMAGE_HEIGHT,\n    outputFormat: options.outputFormat || 'dataUrl',\n    imageType: options.imageType || 'image/jpeg',\n    quality: options.quality || 0.9\n  };\n  \n  // 处理File对象\n  if (imageSource instanceof File) {\n    const reader = new FileReader();\n    reader.onload = function(e) {\n      const img = new Image();\n      img.onload = function() {\n        processImageResize(img, settings, callback);\n      };\n      img.src = e.target.result;\n    };\n    reader.readAsDataURL(imageSource);\n    return;\n  }\n  \n  // 处理Image对象\n  if (imageSource instanceof HTMLImageElement) {\n    if (imageSource.complete) {\n      processImageResize(imageSource, settings, callback);\n    } else {\n      imageSource.onload = function() {\n        processImageResize(imageSource, settings, callback);\n      };\n    }\n    return;\n  }\n  \n  // 处理其他情况（字符串URL或DataURL）\n  if (typeof imageSource === 'string') {\n    const img = new Image();\n    img.onload = function() {\n      processImageResize(img, settings, callback);\n    };\n    img.src = imageSource;\n    return;\n  }\n  \n  // 不支持的类型\n  console.error('不支持的图像源类型:', imageSource);\n  callback(null, { error: '不支持的图像源类型' });\n}\n\n/**\n * 处理图像大小调整的核心逻辑\n * @private\n */\nfunction processImageResize(img, settings, callback) {\n  // 获取原始尺寸\n  const originalWidth = img.naturalWidth || img.width;\n  const originalHeight = img.naturalHeight || img.height;\n  \n  // 计算调整后的尺寸，保持宽高比\n  let newWidth = originalWidth;\n  let newHeight = originalHeight;\n  \n  // 如果图像超出最大限制，按比例缩小\n  if (originalWidth > settings.maxWidth || originalHeight > settings.maxHeight) {\n    const widthRatio = settings.maxWidth / originalWidth;\n    const heightRatio = settings.maxHeight / originalHeight;\n    const ratio = Math.min(widthRatio, heightRatio);\n    \n    newWidth = Math.floor(originalWidth * ratio);\n    newHeight = Math.floor(originalHeight * ratio);\n  }\n  \n  // 创建Canvas绘制调整后的图像\n  const canvas = document.createElement('canvas');\n  const ctx = canvas.getContext('2d');\n  canvas.width = newWidth;\n  canvas.height = newHeight;\n  \n  // 绘制图像\n  ctx.drawImage(img, 0, 0, newWidth, newHeight);\n  \n  // 输出数据\n  const sizeInfo = {\n    originalWidth,\n    originalHeight,\n    newWidth,\n    newHeight,\n    resized: (originalWidth !== newWidth || originalHeight !== newHeight)\n  };\n  \n  // 根据需要的输出格式返回结果\n  if (settings.outputFormat === 'blob') {\n    canvas.toBlob(\n      (blob) => callback(blob, sizeInfo),\n      settings.imageType,\n      settings.quality\n    );\n  } else {\n    // 默认返回DataURL\n    const dataUrl = canvas.toDataURL(settings.imageType, settings.quality);\n    callback(dataUrl, sizeInfo);\n  }\n}\n\n/**\n * 转换相对坐标到绝对坐标\n * @param {Object} annotation 标注数据\n * @param {number} imageWidth 图像宽度\n * @param {number} imageHeight 图像高度\n * @returns {Object} 转换后的标注数据\n */\nexport function convertToAbsoluteCoordinates(annotation, imageWidth, imageHeight) {\n  // 判断是否是相对坐标（0-1范围内）\n  const isRelativeCoordinates = \n    annotation.x <= 1 && annotation.x >= 0 && \n    annotation.y <= 1 && annotation.y >= 0 && \n    annotation.width <= 1 && annotation.width >= 0 && \n    annotation.height <= 1 && annotation.height >= 0;\n  \n  if (!isRelativeCoordinates) {\n    return annotation; // 已经是绝对坐标，直接返回\n  }\n  \n  // 计算绝对坐标\n  const x = Math.round(annotation.x * imageWidth);\n  const y = Math.round(annotation.y * imageHeight);\n  const width = Math.round(annotation.width * imageWidth);\n  const height = Math.round(annotation.height * imageHeight);\n  \n  return {\n    ...annotation,\n    x,\n    y,\n    width,\n    height,\n    // 保存原始的相对坐标\n    normalizedX: annotation.x,\n    normalizedY: annotation.y,\n    normalizedWidth: annotation.width,\n    normalizedHeight: annotation.height\n  };\n}\n\n/**\n * 创建用户ID一致性处理工具\n */\nexport const UserIdConsistencyFixer = {\n  /**\n   * 获取一致的用户ID，优先使用customId\n   * @param {Object} user 用户对象\n   * @returns {string|number} 用户ID\n   */\n  getConsistentUserId(user) {\n    if (!user) return null;\n    return user.customId || user.id || null;\n  },\n  \n  /**\n   * 获取用户对象，确保包含一致的ID\n   * @returns {Object} 用户对象\n   */\n  getCurrentUser() {\n    const user = storageUtils.getFromStorage('user', {});\n      return {\n        ...user,\n        consistentId: this.getConsistentUserId(user)\n      };\n  }\n};\n\nexport default {\n  getImageUrl,\n  getImageFilename,\n  isImageFile,\n  resizeImage,\n  MAX_IMAGE_WIDTH,\n  MAX_IMAGE_HEIGHT\n}; ", "<template>\n  <div class=\"annotation-container\">\n    <div class=\"page-header\">\n      <h2>图像标注</h2>\n      <el-button type=\"primary\" size=\"small\" @click=\"refreshData\">\n        <el-icon><Refresh /></el-icon> 刷新数据\n      </el-button>\n    </div>\n\n    <div class=\"main-content\">\n      <!-- 标注工具栏 -->\n      <div class=\"toolbar\">\n        <div class=\"tool-section\">\n          <h3>标签分类</h3>\n          <el-select v-model=\"selectedTag\" placeholder=\"请选择标签分类\" style=\"width: 100%\">\n            <el-option label=\"IH-婴幼儿血管瘤\" value=\"IH-婴幼儿血管瘤\"></el-option>\n            <el-option label=\"RICH-先天性快速消退型血管瘤\" value=\"RICH-先天性快速消退型血管瘤\"></el-option>\n            <el-option label=\"PICH-先天性部分消退型血管瘤\" value=\"PICH-先天性部分消退型血管瘤\"></el-option>\n            <el-option label=\"NICH-先天性不消退型血管瘤\" value=\"NICH-先天性不消退型血管瘤\"></el-option>\n            <el-option label=\"KHE-卡泊西型血管内皮细胞瘤\" value=\"KHE-卡泊西型血管内皮细胞瘤\"></el-option>\n            <el-option label=\"KH-角化型血管瘤\" value=\"KH-角化型血管瘤\"></el-option>\n            <el-option label=\"PG-肉芽肿性血管瘤\" value=\"PG-肉芽肿性血管瘤\"></el-option>\n            <el-option label=\"MVM-微静脉畸形\" value=\"MVM-微静脉畸形\"></el-option>\n            <el-option label=\"VM-静脉畸形\" value=\"VM-静脉畸形\"></el-option>\n            <el-option label=\"AVM-动静脉畸形\" value=\"AVM-动静脉畸形\"></el-option>\n          </el-select>\n        </div>\n        \n        <div class=\"tool-section\">\n          <h3>标注工具</h3>\n          <el-radio-group v-model=\"currentTool\" style=\"display: block; margin-top: 10px;\">\n            <el-radio label=\"rectangle\">矩形框</el-radio>\n          </el-radio-group>\n        </div>\n\n        <div class=\"annotations-list\">\n          <h3>已添加标注 \n            <el-button type=\"text\" size=\"small\" @click=\"reloadAnnotations\" title=\"刷新标注\">\n              <el-icon><Refresh /></el-icon>\n            </el-button>\n          </h3>\n          <div v-if=\"filteredAnnotations.length === 0\" class=\"empty-annotations\">\n            <p>暂无标注，请在图片上绘制矩形框添加标注</p>\n          </div>\n          <el-table v-else :data=\"filteredAnnotations\" style=\"width: 100%\">\n            <el-table-column label=\"编号\" width=\"60\">\n              <template #default=\"scope\">\n                {{ scope.$index + 1 }}\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"tag\" label=\"标签\" width=\"90\" />\n            <el-table-column label=\"坐标\" width=\"120\">\n              <template #default=\"scope\">\n                <div class=\"coordinates\">\n                  <small>X: {{ Math.round(scope.row.x) }}</small><br>\n                  <small>Y: {{ Math.round(scope.row.y) }}</small>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"尺寸\" width=\"120\">\n              <template #default=\"scope\">\n                <div class=\"dimensions\">\n                  <small>W: {{ Math.round(scope.row.width) }}</small><br>\n                  <small>H: {{ Math.round(scope.row.height) }}</small>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"80\">\n              <template #default=\"scope\">\n                <el-button type=\"danger\" size=\"small\" @click=\"deleteAnnotation(scope.row.id)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </div>\n\n      <!-- 图像标注区域 -->\n      <div class=\"annotation-area\">\n        <div class=\"image-wrapper\">\n          <div ref=\"imageContainerInner\" class=\"image-container\">\n            <!-- 固定显示的图片 -->\n            <img \n              v-if=\"currentImage\" \n              ref=\"annotationImage\" \n              :src=\"getImageUrl(currentImage.url)\"\n              class=\"annotation-image\"\n              alt=\"医学影像\" \n              @load=\"handleImageLoad\"\n            />\n            \n            <!-- 透明覆盖层，用于标注，与图片大小一致 -->\n            <div \n              v-if=\"currentImage\"\n              class=\"annotation-overlay\"\n              :style=\"{\n                width: imageWidth + 'px',\n                height: imageHeight + 'px'\n              }\"\n              @mousedown=\"startDrawing\"\n              @mousemove=\"onDrawing\"\n              @mouseup=\"endDrawing\"\n              @mouseleave=\"cancelDrawing\"\n            ></div>\n            \n            <!-- 标注框 -->\n            <div \n              v-for=\"(box, index) in filteredAnnotations\" \n              :key=\"index\" \n              class=\"annotation-box\"\n              :style=\"{\n                left: box.x + 'px',\n                top: box.y + 'px',\n                width: box.width + 'px',\n                height: box.height + 'px',\n                borderColor: getTagColor(box.tag),\n                cursor: isEditingBox && editingBoxId === box.id ? 'move' : 'default'\n              }\"\n              @mousedown.stop=\"startEditBox($event, box.id)\"\n            >\n              <span class=\"annotation-label\" :style=\"{ backgroundColor: getTagColor(box.tag) }\">\n                {{ box.tag }}\n              </span>\n              \n              <!-- 四个角的调整大小的手柄 -->\n              <div class=\"resize-handle top-left\" @mousedown.stop=\"startResizeBox($event, box.id, 'top-left')\"></div>\n              <div class=\"resize-handle top-right\" @mousedown.stop=\"startResizeBox($event, box.id, 'top-right')\"></div>\n              <div class=\"resize-handle bottom-left\" @mousedown.stop=\"startResizeBox($event, box.id, 'bottom-left')\"></div>\n              <div class=\"resize-handle bottom-right\" @mousedown.stop=\"startResizeBox($event, box.id, 'bottom-right')\"></div>\n            </div>\n            \n            <!-- 正在绘制的框 -->\n            <div \n              v-if=\"isDrawing\" \n              class=\"drawing-box\"\n              :style=\"{\n                left: Math.min(drawStart.x, drawCurrent.x) + 'px',\n                top: Math.min(drawStart.y, drawCurrent.y) + 'px',\n                width: Math.abs(drawCurrent.x - drawStart.x) + 'px',\n                height: Math.abs(drawCurrent.y - drawStart.y) + 'px'\n              }\"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-section\">\n      <router-view></router-view>\n    </div>\n  </div>\n</template>\n\n<script>\nimport api from '@/utils/api'\nimport { getImageUrl, convertToAbsoluteCoordinates, UserIdConsistencyFixer } from '../utils/imageHelper'\nimport { Refresh } from '@element-plus/icons-vue'\nimport axios from 'axios'\nimport { mapGetters } from 'vuex'\nimport { ElMessage } from 'element-plus'\n\n// 配置对象，用于集中管理API路径和存储键名等\nconst CONFIG = {\n  STORAGE_KEYS: {\n    userInfo: 'user',\n    token: 'token',\n    authValid: 'authValid',\n    isNavigatingAfterSave: 'isNavigatingAfterSave',\n    pendingDiagnosis: 'pendingDiagnosisId',\n    formDataPrefix: 'formData_backup_'\n  },\n  ROUTES: {\n    structuredForm: '/app/cases/structured-form'\n  },\n  UI: {\n    navigationDelay: 100\n  }\n};\n\nexport default {\n  name: 'CaseDetailForm',\n  components: {\n    Refresh\n  },\n  data() {\n    return {\n      imageId: this.$route.params.id || this.$route.query.imageId || null,\n      currentImage: null, // 初始化 currentImage\n      selectedTag: 'IH-婴幼儿血管瘤', // 默认选择IH类型\n      currentTool: 'rectangle', // 添加并初始化工具\n      formData: {\n        // 表单数据结构\n        diseasePart: '',\n        diseaseType: '',\n        patientAge: '',\n        patientGender: '',\n        diagnosis: '',\n        treatmentPlan: '',\n        notes: ''\n      },\n      dbAnnotations: [], // 存储从数据库获取的标注数据\n      uploadedImages: [],\n      currentImageIndex: 0,\n      annotations: [],\n      imageWidth: 0,\n      imageHeight: 0,\n      drawingBox: null,\n      isDrawing: false,\n      selectedAnnotation: null,\n      isMoving: false,\n      isResizing: false,\n      resizeHandle: null,\n      moveStartX: 0,\n      moveStartY: 0,\n      drawStart: { x: 0, y: 0 },\n      drawCurrent: { x: 0, y: 0 },\n      // annotationTool: 'rectangle', // 移除旧的、冲突的属性\n      showAnnotationForm: false,\n      currentAnnotation: null,\n      zoomLevel: 1,\n      panOffset: { x: 0, y: 0 },\n      isPanning: false,\n      lastPanPoint: { x: 0, y: 0 },\n      annotationsLoaded: false,\n      shouldSaveOriginalImage: false,\n      imageLoaded: false,\n      isSavingAnnotations: false,\n      originalWidth: 0,\n      originalHeight: 0,\n      scaleX: 1,\n      scaleY: 1,\n      isSaving: false, // 添加保存状态变量\n      processedAnnotations: false, // 添加标注处理状态\n      // 添加延迟保存的计时器ID\n      saveImageTimer: null,\n    };\n  },\n  computed: {\n    filteredAnnotations() {\n      return this.annotations.filter(a => a.imageIndex === this.currentImageIndex)\n    }\n  },\n  watch: {\n    // 关键修复：当标注数据和图像都加载完成后，处理标注\n    dbAnnotations: {\n      handler(newAnnotations) {\n        if (this.imageLoaded && newAnnotations && newAnnotations.length > 0 && !this.processedAnnotations) {\n          console.log('监听到标注数据变化且图像已加载，开始处理标注');\n          this.processLoadedAnnotations();\n        }\n      },\n      deep: true,\n      immediate: true\n    },\n    // 关键修复：当图像加载完成后，如果标注数据已存在，处理标注\n    imageLoaded: {\n      handler(newVal) {\n        if (newVal && this.dbAnnotations && this.dbAnnotations.length > 0 && !this.processedAnnotations) {\n          console.log('监听到图像加载完成且标注数据已存在，开始处理标注');\n          this.processLoadedAnnotations();\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    // 注册窗口大小改变事件监听\n    window.addEventListener('resize', this.handleResize);\n  },\n  mounted() {\n    console.log('CaseDetailForm mounted - 查询参数:', this.$route.query, '路由参数:', this.$route.params);\n    \n    // 获取图像ID，优先从route params中获取，然后是query参数\n    let imageId = this.$route.params.id || this.$route.query.imageId || this.$route.query.diagnosisId || '';\n    console.log('原始图像ID:', imageId, '类型:', typeof imageId);\n    \n    // 确保imageId是字符串类型\n    imageId = String(imageId);\n    \n    // 检查是否有有效的图像ID\n    if (!imageId || imageId === 'undefined' || imageId === 'null') {\n      this.$message.warning('未提供有效的图像ID，请选择一个图像进行标注');\n      return;\n    }\n    \n    // 保存图像ID到组件实例\n    this.imageId = imageId;\n    console.log('处理后的图像ID:', this.imageId, '类型:', typeof this.imageId);\n    \n    // 显示加载提示\n    const loading = this.$loading({\n      lock: true,\n      text: '正在加载图像和标注数据...',\n      spinner: 'el-icon-loading',\n      background: 'rgba(0, 0, 0, 0.7)'\n    });\n    \n    // 统一加载流程\n      // 标记为应用内操作\n      sessionStorage.setItem('isAppOperation', 'true');\n    \n    // 添加重试机制，确保能正确加载图像和标注数据\n    let retryCount = 0;\n    const maxRetries = 3;\n    \n    const loadDataWithRetry = () => {\n      console.log(`尝试加载页面数据，第 ${retryCount + 1} 次尝试`);\n      \n      // 异步加载数据\n      this.loadPageData(imageId)\n        .then(() => {\n          console.log('页面数据加载成功');\n          // 成功后加载关联的标注\n          return this.loadAnnotations(imageId);\n        })\n        .then(() => {\n          console.log('标注数据也加载成功');\n          loading.close();\n        })\n        .catch(error => {\n          console.error(`第 ${retryCount + 1} 次加载数据失败:`, error);\n          \n          if (retryCount < maxRetries) {\n            retryCount++;\n            console.log(`${retryCount * 2}秒后重试...`);\n            \n            // 延迟重试，每次增加等待时间\n            setTimeout(loadDataWithRetry, retryCount * 2000);\n          } else {\n            console.error('达到最大重试次数，放弃加载');\n            this.$message.error('无法加载页面数据，请返回重试');\n            loading.close();\n          }\n        });\n    };\n    \n    // 开始加载数据\n    loadDataWithRetry();\n    \n    // 添加事件监听器\n    document.addEventListener('mousemove', this.handleGlobalMouseMove);\n    document.addEventListener('mouseup', this.handleGlobalMouseUp);\n    \n    // 响应窗口大小变化，更新图像尺寸\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeUnmount() {\n    // 移除窗口大小改变事件监听，避免内存泄漏\n    window.removeEventListener('resize', this.handleResize);\n    \n    // 清除导航标记\n    sessionStorage.removeItem('isNavigatingAfterSave');\n    sessionStorage.removeItem('returningToWorkbench');\n    // 保留isAppOperation，以维持应用内操作状态\n  },\n  methods: {\n    // 处理长ID，将时间戳ID转换为合适的整数\n    processLongId(id) {\n      if (!id) return null;\n      \n      const idStr = id.toString();\n      // 如果ID长度超过9（INT范围限制），截取后9位\n      if (idStr.length > 9) {\n        console.log(`处理长ID: ${idStr} -> ${idStr.substring(idStr.length - 9)}`);\n        return idStr.substring(idStr.length - 9);\n      }\n      return idStr;\n    },\n\n    // 添加防止缓存的参数\n    getImageUrl(url) {\n      if (!url) return '';\n      \n      // 如果URL已经包含完整路径(http)，直接使用\n      if (url.startsWith('http')) {\n        return url;\n      }\n      \n      // 尝试不同的URL格式\n      const originalUrl = url;\n      let finalUrl = url;\n      \n      // 如果是相对路径，加上基础URL\n      if (url.startsWith('/')) {\n        // 获取当前域名作为基础URL\n        const baseUrl = window.location.origin;\n        finalUrl = baseUrl + url;\n      }\n      \n      // 添加时间戳和随机数参数，防止缓存\n      const cacheBuster = `t=${Date.now()}&r=${Math.random()}`;\n      if (finalUrl.includes('?')) {\n        finalUrl = `${finalUrl}&${cacheBuster}`;\n      } else {\n        finalUrl = `${finalUrl}?${cacheBuster}`;\n      }\n      \n      console.log(`处理图像URL: ${originalUrl} -> ${finalUrl}`);\n      return finalUrl;\n    },\n    \n    // 修改加载图像数据的方法，添加防止缓存的头信息\n    async loadImageData() {\n      if (!this.imageId) {\n        console.error('No image ID provided');\n        return;\n      }\n      \n      try {\n        console.log(`Loading image data for ID: ${this.imageId}`);\n        \n        // 添加时间戳和随机数，防止缓存\n        const timestamp = Date.now();\n        const random = Math.random();\n        \n        const response = await api.get(`/images/${this.imageId}?t=${timestamp}&r=${random}`, {\n          headers: {\n            'Cache-Control': 'no-cache, no-store',\n            'Pragma': 'no-cache',\n            'Expires': '0'\n          }\n        });\n        \n        if (response.data) {\n          this.currentImage = response.data;\n          console.log('Image data loaded:', this.currentImage);\n          \n          // 加载关联的标注\n          this.loadAnnotations();\n        } else {\n          console.error('Failed to load image data: Response data is empty');\n        }\n      } catch (error) {\n        console.error('Failed to load image data:', error);\n      }\n    },\n    \n    // 修改加载标注的方法，解决刷新后标注框不显示的问题\n    async loadAnnotations(imageId) {\n      if (!imageId) {\n        console.error('无法加载标注：缺少图像ID');\n        return Promise.reject('缺少图像ID');\n      }\n      \n      const id = imageId || this.imageId;\n      console.log('加载图像ID为', id, '的标注数据');\n      \n      // 最大重试次数\n      const maxRetries = 3;\n      let retries = 0;\n      \n      const tryLoadAnnotations = async () => {\n        try {\n          // 添加时间戳和随机数避免缓存\n          const timestamp = new Date().getTime();\n          const random = Math.random();\n          \n          // 获取当前用户信息\n          const user = JSON.parse(localStorage.getItem('user') || '{}');\n          const userId = user.customId || user.id || '';\n      \n          // 检查是否是血管瘤诊断ID\n          const isDiagnosisId = this.$route.query.diagnosisId === id;\n          \n          // 构建API URL\n          let apiUrl = `/medical/api/tags/image/${id}?t=${timestamp}&r=${random}&mode=view&_role=REVIEWER&userId=${userId}`;\n      \n          // 如果是血管瘤诊断ID，使用特殊的API端点\n      if (isDiagnosisId) {\n            apiUrl = `/medical/api/hemangioma-diagnoses/${id}/tags?t=${timestamp}&r=${random}`;\n          }\n          \n          console.log(`尝试加载标注数据 (第${retries + 1}次尝试)，URL: ${apiUrl}`);\n          \n          // 尝试从API加载标注数据\n          const response = await fetch(apiUrl);\n          \n          if (!response.ok) {\n            throw new Error(`获取标注失败: ${response.status}`);\n          }\n          \n          const data = await response.json();\n          console.log(`获取到 ${data.length} 个标注数据:`, data);\n            \n            // 保存原始标注数据\n          this.dbAnnotations = data || [];\n            \n          // 如果图像已加载，立即转换标注\n              if (this.imageWidth > 0 && this.imageHeight > 0) {\n            this.processLoadedAnnotations();\n              } else {\n            // 否则标记为待处理\n                this.annotationsLoaded = true;\n              }\n          \n          return data;\n        } catch (error) {\n          console.error(`加载标注数据失败 (第${retries + 1}次尝试):`, error);\n          \n          // 如果还有重试次数，则等待后重试\n          if (retries < maxRetries) {\n            retries++;\n            const waitTime = retries * 1000; // 每次重试增加等待时间\n            console.log(`等待 ${waitTime}ms 后重试...`);\n            \n            return new Promise((resolve, reject) => {\n              setTimeout(async () => {\n                try {\n                  const result = await tryLoadAnnotations();\n                  resolve(result);\n                } catch (retryError) {\n                  reject(retryError);\n                }\n              }, waitTime);\n            });\n          }\n          \n          // 尝试备用API路径\n          try {\n            console.log('尝试使用备用API路径...');\n            const timestamp = new Date().getTime();\n        const random = Math.random();\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const userId = user.customId || user.id || '';\n            \n            // 检查是否是血管瘤诊断ID\n            const isDiagnosisId = this.$route.query.diagnosisId === id;\n            \n            // 构建备用API URL\n            let backupUrl = `/api/tags/image/${id}?t=${timestamp}&r=${random}&userId=${userId}`;\n            \n            // 如果是血管瘤诊断ID，使用特殊的API端点\n            if (isDiagnosisId) {\n              backupUrl = `/api/hemangioma-diagnoses/${id}/tags?t=${timestamp}&r=${random}`;\n            }\n            \n            console.log(`尝试备用API路径: ${backupUrl}`);\n            const response = await fetch(backupUrl);\n            \n            if (!response.ok) {\n              throw new Error(`备用API也失败: ${response.status}`);\n            }\n            \n            const data = await response.json();\n            console.log(`通过备用API获取到 ${data.length} 个标注数据:`, data);\n            \n            // 保存原始标注数据\n            this.dbAnnotations = data || [];\n            \n            // 如果图像已加载，立即转换标注\n            if (this.imageWidth > 0 && this.imageHeight > 0) {\n                this.processLoadedAnnotations();\n            } else {\n              // 否则标记为待处理\n              this.annotationsLoaded = true;\n            }\n            \n            return data;\n          } catch (backupError) {\n            console.error('所有API尝试都失败:', backupError);\n            this.$message.error('加载标注数据失败，您可以创建新的标注');\n            return Promise.reject(backupError);\n          }\n        }\n      };\n      \n      // 开始尝试加载\n      return tryLoadAnnotations();\n    },\n    \n    // 转换血管瘤标签数据为前端标注格式\n    convertHemangiomaTags(tags) {\n      console.log('转换血管瘤标签数据:', tags);\n      \n      if (!tags || !tags.length || !this.imageWidth || !this.imageHeight) {\n        console.warn('无法转换标签数据: 缺少必要信息', {\n          tagsLength: tags ? tags.length : 0,\n          imageWidth: this.imageWidth,\n          imageHeight: this.imageHeight\n        });\n        return;\n      }\n      \n      const convertedAnnotations = tags.map((tag, index) => {\n        // 判断是否是相对坐标（0-1范围内）\n        const isRelativeCoordinates = \n          tag.x <= 1 && tag.x >= 0 && \n          tag.y <= 1 && tag.y >= 0 && \n          tag.width <= 1 && tag.width >= 0 && \n          tag.height <= 1 && tag.height >= 0;\n        \n        // 根据坐标类型计算实际像素值\n        const x = isRelativeCoordinates ? tag.x * this.imageWidth : tag.x;\n        const y = isRelativeCoordinates ? tag.y * this.imageHeight : tag.y;\n        const width = isRelativeCoordinates ? tag.width * this.imageWidth : tag.width;\n        const height = isRelativeCoordinates ? tag.height * this.imageHeight : tag.height;\n        \n        return {\n          id: `${index + 1}`,\n          serverId: tag.id || null,\n          tag: tag.tag || tag.tagName || 'IH-婴幼儿血管瘤',\n          x: x,\n          y: y,\n          width: width,\n          height: height,\n          confidence: tag.confidence || null,\n          type: 'rectangle'\n        };\n      });\n      \n      console.log('转换后的标注数据:', convertedAnnotations);\n      this.annotations = convertedAnnotations;\n    },\n    \n    // 转换普通标签数据为前端标注格式\n    convertTags(tags) {\n      console.log('转换普通标签数据:', tags);\n          \n      if (!tags || !tags.length || !this.imageWidth || !this.imageHeight) {\n        console.warn('无法转换标签数据: 缺少必要信息');\n        return;\n          }\n      \n      const convertedAnnotations = tags.map((tag, index) => {\n        // 判断是否是相对坐标（0-1范围内）\n        const isRelativeCoordinates = \n          tag.x <= 1 && tag.x >= 0 && \n          tag.y <= 1 && tag.y >= 0 && \n          tag.width <= 1 && tag.width >= 0 && \n          tag.height <= 1 && tag.height >= 0;\n        \n        // 根据坐标类型计算实际像素值\n        const x = isRelativeCoordinates ? tag.x * this.imageWidth : tag.x;\n        const y = isRelativeCoordinates ? tag.y * this.imageHeight : tag.y;\n        const width = isRelativeCoordinates ? tag.width * this.imageWidth : tag.width;\n        const height = isRelativeCoordinates ? tag.height * this.imageHeight : tag.height;\n        \n        return {\n          id: tag.id || `${index + 1}`,\n          tag: tag.tag || tag.tagName || 'IH-婴幼儿血管瘤',\n          x: x,\n          y: y,\n          width: width,\n          height: height,\n          confidence: tag.confidence || null,\n          type: 'rectangle'\n        };\n      });\n      \n      console.log('转换后的标注数据:', convertedAnnotations);\n      this.annotations = convertedAnnotations;\n    },\n    \n    // 添加手动刷新方法\n    async refreshData() {\n      // 清除本地缓存\n      this.dbAnnotations = [];\n      this.annotations = [];\n      this.annotationsLoaded = false;\n      \n      // 重新加载数据\n      await this.loadImageData();\n      \n      // 显示提示\n      this.$message({\n        message: '数据已刷新',\n        type: 'success',\n        duration: 2000\n      });\n    },\n\n    // 从image_pairs表加载图像\n    loadImageFromPairs(imageId) {\n      console.log('🔍 开始从image_pairs表加载图像，ID:', imageId);\n      \n      // 获取当前用户信息\n      const user = JSON.parse(localStorage.getItem('user')) || {};\n      const userId = user.customId || user.id;\n      \n      if (!userId) {\n        console.warn('未找到用户ID，可能导致权限问题');\n          }\n      \n      // 显式添加用户ID作为查询参数\n      const params = { userId };\n      \n      // 添加时间戳避免缓存\n      params.t = new Date().getTime();\n      \n      // 先从image_pairs表查询相关记录\n      api.imagePairs.getByMetadataId(imageId)\n        .then(response => {\n          console.log('image_pairs查询响应:', response);\n          const data = response.data;\n          \n          if (data && data.length > 0) {\n            const imagePair = data[0];\n            console.log('image_pairs第一条记录:', imagePair);\n            this.imagePairId = imagePair.id;\n            \n            // 使用处理后的图像路径\n            const processedImagePath = imagePair.imageOnePath || imagePair.image_one_path;\n            console.log('获取到图像路径:', processedImagePath);\n            \n            // 如果路径仍为空，尝试其他字段\n            if (!processedImagePath && imagePair.image_one_url) {\n              console.log('尝试使用image_one_url:', imagePair.image_one_url);\n              this.processedFilePath = imagePair.image_one_url;\n            } else if (!processedImagePath && imagePair.url) {\n              console.log('尝试使用url字段:', imagePair.url);\n              this.processedFilePath = imagePair.url;\n            } else {\n              this.processedFilePath = processedImagePath;\n            }\n            \n            // 如果仍然没有找到路径，直接从image_metadata表获取\n            if (!this.processedFilePath) {\n              console.log('image_pairs中未找到图像路径，尝试从image_metadata表获取');\n              this.tryLoadImagePathFromMetadata(imageId, (path) => {\n                if (path) {\n                  console.log('从image_metadata表获取到图像路径:', path);\n                  this.processedFilePath = path;\n                  this.updateImagePairPath(imageId, path);\n                  \n                  // 更新上传图像数组\n                  this.uploadedImages = [{\n                    id: imageId,\n                    url: path,\n                    filename: `图像 #${imageId}`\n                  }];\n                  \n                  this.currentImage = this.uploadedImages[0];\n                  this.imageLoaded = true;\n                }\n              });\n              return;  // 中断当前流程，等待回调处理\n            }\n            \n            // 更新上传图像数组\n            this.uploadedImages = [{\n              id: imageId,\n              url: this.processedFilePath,\n              filename: `图像 #${imageId}`\n            }];\n            \n            this.currentImage = this.uploadedImages[0];\n            this.currentImageIndex = 0;\n            this.currentImageId = imageId;\n            \n            // 标记图像已加载\n            this.imageLoaded = true;\n            \n            // 加载标注框\n            this.loadAnnotations(imageId);\n          } else {\n            console.log('image_pairs未找到数据，尝试从image_metadata表直接获取路径');\n            this.tryLoadImagePathFromMetadata(imageId, (path) => {\n              if (path) {\n                console.log('从image_metadata表获取到图像路径:', path);\n                this.processedFilePath = path;\n                this.createImageRecord(path, imageId);\n              } else {\n                this.$message.error('未能找到图像路径，无法加载图像');\n              }\n            });\n          }\n        })\n        .catch(error => {\n          console.error('加载图像对失败:', error);\n          // 尝试直接从image_metadata获取\n          this.tryLoadImagePathFromMetadata(imageId);\n        });\n    },\n    \n    // 作为备用，尝试从image_metadata加载\n    tryLoadFromMetadata(imageId) {\n      console.log('从image_metadata表尝试加载图像ID:', imageId);\n      \n      api.images.getOne(imageId)\n        .then(response => {\n          console.log('从image_metadata表获取的完整响应:', response);\n          const imageData = response.data;\n          console.log('image_metadata表获取的图像数据:', imageData);\n          \n          if (!imageData || !imageData.path) {\n            console.error('image_metadata表中图像路径不存在，无法加载图像');\n            this.$message.error('图像路径不存在，请检查数据库');\n            return;\n          }\n          \n          // 准备图像数据\n          this.uploadedImages = [{\n            id: imageData.id,\n            url: imageData.path,\n            filename: imageData.original_name || `图像 #${imageData.id}`\n          }];\n          \n          this.currentImage = this.uploadedImages[0];\n          this.currentImageIndex = 0;\n          \n          // 保存处理后的图像路径\n          this.processedFilePath = imageData.path;\n          \n          // 创建ImagePair记录 - 直接使用fetch创建，避免API调用\n          this.createImagePairDirectly(imageData.id, imageData.path);\n          \n          // 加载标注数据\n          this.loadAnnotationsFromDatabase();\n          \n          // 标记图像已成功加载\n          this.imageLoaded = true;\n          \n          console.log('从image_metadata加载成功并创建image_pairs记录');\n        })\n        .catch(error => {\n          console.error('从image_metadata加载图像失败:', error);\n          this.loadingError = true;\n          this.loadingErrorMessage = '无法加载图像，请检查数据库记录';\n          \n          // 如果有物理路径，尝试创建新的图像记录\n          if (this.processedFilePath && this.currentImageId) {\n            console.log('尝试使用现有路径创建图像记录');\n            this.createImageRecord(this.processedFilePath, this.currentImageId);\n          } else {\n            this.$message.error('无法找到图像数据，请返回上一步重新上传');\n          }\n        });\n    },\n    \n    // 直接创建ImagePair记录，不使用API\n    createImagePairDirectly(metadataId, imagePath) {\n      console.log('直接创建ImagePair记录:', { metadataId, path: imagePath });\n      \n      // 准备数据\n      const data = {\n        metadataId: metadataId.toString(),\n        imageOnePath: imagePath,\n        description: `图像${metadataId}`\n      };\n      \n      // 直接使用fetch API，完全绕过axios和api.js\n      fetch('/api/image-pairs', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(data),\n        credentials: 'include'\n      })\n        .then(response => {\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n          }\n          return response.json();\n        })\n        .then(result => {\n          console.log('成功创建ImagePair记录:', result);\n          if (result && result.id) {\n            this.imagePairId = result.id;\n          }\n          this.$message.success('图像关联信息创建成功');\n        })\n        .catch(error => {\n          console.error('创建ImagePair记录失败:', error);\n          this.$message.warning('图像关联创建失败，但您仍可继续使用');\n        });\n    },\n    \n    // 保留原有方法但不再主动调用\n    loadImageById(imageId) {\n      // 重定向到新的加载方法\n      this.loadImageFromPairs(imageId);\n    },\n    \n    // 创建新的图像记录\n    createImageRecord(imagePath, metadataId) {\n      console.log('正在创建新的图像记录:', { path: imagePath, id: metadataId });\n      \n      // 获取当前用户\n      const user = JSON.parse(localStorage.getItem('user')) || { id: 1 };\n      \n      // 提取文件名\n      const filename = imagePath.substring(imagePath.lastIndexOf('/') + 1);\n      \n      // 创建元数据\n      const metadata = {\n        filename: filename,\n        original_name: filename,\n        path: imagePath,\n        mimetype: 'image/jpeg',\n        size: 0,\n        width: 800,\n        height: 600,\n        status: 'DRAFT',\n        uploaded_by: user.id\n      };\n      \n      // 保存元数据\n      api.images.update(metadataId, metadata)\n        .then(response => {\n          console.log('成功创建/更新图像元数据:', response.data);\n          \n          // 创建ImagePair记录\n          this.saveOriginalImage();\n          \n          // 重新加载页面\n          this.loadImageById(metadataId);\n        })\n        .catch(error => {\n          console.error('创建图像元数据失败:', error);\n          this.$message.error('无法创建图像记录，请检查数据库连接');\n        });\n    },\n    \n    // 根据路径加载图像\n    loadImageByPath(imagePath) {\n      // 模拟图像加载过程\n      this.uploadedImages = [{\n        id: this.currentImageId || Date.now().toString(),\n        url: imagePath,\n        filename: imagePath.substring(imagePath.lastIndexOf('/') + 1)\n      }];\n      \n      this.currentImage = this.uploadedImages[0];\n      this.currentImageIndex = 0;\n      \n      // 如果没有图像ID，使用当前时间戳作为ID\n      if (!this.currentImageId) {\n        this.currentImageId = this.uploadedImages[0].id;\n        console.log('生成临时图像ID:', this.currentImageId);\n      }\n      \n      // 确保ImagePair记录存在\n      this.checkExistingImagePair();\n      \n      // 标记图像已成功加载\n      this.imageLoaded = true;\n    },\n    \n    // 检查并确保图像对记录存在\n    checkExistingImagePair() {\n      if (!this.currentImageId || !this.processedFilePath) {\n        console.log('缺少图像ID或物理路径，不检查ImagePair记录');\n        return;\n      }\n      \n      console.log('检查图像ID对应的ImagePair记录:', this.currentImageId);\n      \n      api.imagePairs.getByMetadataId(this.currentImageId)\n        .then(response => {\n          if (response.data && response.data.length > 0) {\n            // 找到现有记录\n            const imagePair = response.data[0];\n            this.imagePairId = imagePair.id;\n            console.log('找到现有ImagePair记录:', imagePair);\n            \n            // 如果记录中的路径与当前路径不一致，更新记录\n            if (imagePair.imageOnePath !== this.processedFilePath) {\n              console.log('更新ImagePair中的路径信息');\n              this.saveOriginalImage();\n            }\n          } else {\n            // 没有找到记录，创建新记录\n            console.log('未找到ImagePair记录，创建新记录');\n            this.saveOriginalImage();\n          }\n        })\n        .catch(error => {\n          console.error('检查ImagePair记录失败:', error);\n          // 尝试创建新记录\n          console.log('尝试创建新的ImagePair记录');\n          this.saveOriginalImage();\n        });\n    },\n    \n    saveOriginalImage() {\n      // 如果没有图像ID，则不保存\n      if (!this.currentImageId) {\n        console.log('缺少图像ID，不保存到ImagePair表');\n        return;\n      }\n      \n      let imagePath = '';\n      \n      // 首选：从currentImage中获取URL（已经从数据库加载）\n      if (this.currentImage && this.currentImage.url) {\n        imagePath = this.currentImage.url;\n        console.log('使用从数据库加载的图像路径:', imagePath);\n      } \n      // 次选：使用本地保存的processedFilePath\n      else if (this.processedFilePath) {\n        imagePath = this.processedFilePath;\n        console.log('使用本地保存的处理后图像路径:', imagePath);\n      }\n      // 如果都没有，无法保存\n      else {\n        console.error('无法找到有效的图像路径，不保存到ImagePair表');\n        return;\n      }\n      \n      // 确保图像元数据存在\n      this.ensureImageMetadataExists(this.currentImageId, imagePath, () => {\n        // 获取当前用户\n        const user = JSON.parse(localStorage.getItem('user')) || { id: 1 };\n        \n        try {\n          // 准备请求数据 - 极简格式，避免任何不必要的字段\n          const data = {\n            metadataId: this.currentImageId.toString(), // 使用字符串格式\n            imageOnePath: imagePath,\n            description: `图像${this.currentImageId}` // 简化描述\n          };\n          \n          console.log('保存到image_pairs的数据:', data);\n          \n          // 使用fetch API直接发送请求 - 绕过所有中间件和缓存\n          const directRequest = async () => {\n            try {\n              // 查询image_pairs是否存在\n              console.log('直接查询ImagePair是否存在:', this.currentImageId);\n              const checkResponse = await fetch(`/api/image-pairs/metadata/${this.currentImageId}`, {\n                method: 'GET',\n                headers: {\n                  'Accept': 'application/json',\n                  'Cache-Control': 'no-cache',\n                  'Pragma': 'no-cache'\n                },\n                credentials: 'include'\n              });\n              \n              // 解析响应\n              const pairsData = await checkResponse.json();\n              let existingPairId = null;\n              let exists = false;\n              \n              if (pairsData && pairsData.length > 0 && pairsData[0].id) {\n                exists = true;\n                existingPairId = pairsData[0].id;\n                console.log('找到现有ImagePair:', existingPairId);\n              }\n              \n              // 如果存在，添加ID字段\n              if (exists && existingPairId) {\n                data.id = existingPairId;\n              }\n              \n              // 发送创建/更新请求\n              const saveResponse = await fetch('/api/image-pairs', {\n                method: 'POST', // 始终使用POST\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Cache-Control': 'no-cache',\n                  'Pragma': 'no-cache'\n                },\n                body: JSON.stringify(data),\n                credentials: 'include'\n              });\n              \n              if (!saveResponse.ok) {\n                throw new Error(`保存失败: ${saveResponse.status}`);\n              }\n              \n              const result = await saveResponse.json();\n              console.log('保存ImagePair成功:', result);\n              \n              // 更新本地ID\n              if (result && result.id) {\n                this.imagePairId = result.id;\n              }\n              \n              this.$message.success('图像关联信息保存成功');\n              \n            } catch (error) {\n              console.error('保存ImagePair失败:', error);\n              this.$message.error('保存失败: ' + error.message);\n            }\n          };\n          \n          // 执行直接请求\n          directRequest();\n          \n        } catch (err) {\n          console.error('准备ImagePair数据时出错:', err);\n          this.$message.error('保存图像对失败: ' + err.message);\n        }\n      });\n    },\n    \n    // 确保图像元数据存在\n    ensureImageMetadataExists(imageId, imagePath, callback) {\n      // 检查图像元数据是否存在\n      api.images.getOne(imageId)\n        .then(() => {\n          // 元数据存在，执行回调\n          callback();\n        })\n        .catch(() => {\n          // 元数据不存在，创建新的\n          console.log('图像元数据不存在，创建新记录');\n          this.createImageRecord(imagePath, imageId);\n        });\n    },\n    \n    // 窗口大小变化时更新图像尺寸\n    handleResize() {\n      // 窗口大小变化时，需要延迟一点以确保DOM已更新\n      this.$nextTick(() => {\n        const image = this.$refs.annotationImage;\n        if (image) {\n          // 获取新的图像显示尺寸\n          const rect = image.getBoundingClientRect();\n          const newWidth = rect.width;\n          const newHeight = rect.height;\n          \n          // 如果尺寸有变化，更新并重新计算标注框位置\n          if (newWidth !== this.imageWidth || newHeight !== this.imageHeight) {\n            console.log(`窗口大小改变，图像尺寸从 ${this.imageWidth}x${this.imageHeight} 变为 ${newWidth}x${newHeight}`);\n            this.imageWidth = newWidth;\n            this.imageHeight = newHeight;\n            \n            // 更新缩放比例\n            this.scaleX = this.imageWidth / this.originalWidth;\n            this.scaleY = this.imageHeight / this.originalHeight;\n            \n            // 重新计算标注框位置\n            this.recalculateAnnotationPositions();\n          }\n        }\n      });\n    },\n    \n    // 添加空的initTools方法以修复错误\n    initTools() {\n      console.log('初始化标注工具');\n      // 这个方法被调用但原本不存在，添加一个空实现\n    },\n    \n    // 图像加载完成后的处理\n    handleImageLoad(event) {\n      console.log('图像加载完成');\n      \n      // 获取图像实际尺寸\n      const img = event.target;\n      const rect = img.getBoundingClientRect();\n\n        this.imageWidth = rect.width;\n        this.imageHeight = rect.height;\n      this.originalWidth = img.naturalWidth;\n      this.originalHeight = img.naturalHeight;\n      \n      console.log('图像尺寸:', {\n        width: this.imageWidth,\n        height: this.imageHeight,\n        originalWidth: this.originalWidth,\n        originalHeight: this.originalHeight\n      });\n        \n        // 标记图像已加载\n        this.imageLoaded = true;\n        \n      // 如果有待处理的标注数据，现在可以处理了\n      if (this.annotationsLoaded && this.dbAnnotations.length > 0) {\n        console.log('图像加载完成，处理待转换的标注数据');\n            this.processLoadedAnnotations();\n      }\n      \n      // 发出图像加载完成事件\n      this.$emit('image-loaded', {\n        width: this.imageWidth,\n        height: this.imageHeight\n      });\n    },\n    \n    // 重新计算标注位置\n    recalculateAnnotationPositions() {\n      if (!this.annotations || !this.annotations.length) return;\n      \n      console.log('重新计算标注位置，图像尺寸:', this.imageWidth, 'x', this.imageHeight);\n      \n      // 确保图像尺寸有效\n      if (this.imageWidth <= 0 || this.imageHeight <= 0) {\n        console.error('图像尺寸无效，无法重新计算标注位置');\n        return;\n      }\n      \n      this.annotations = this.annotations.map(tag => {\n        // 根据标注格式进行不同处理\n        if (tag.isYoloFormat) {\n          // YOLO格式 - 使用归一化的中心点坐标\n          const centerX = tag.normalizedX; // 中心点X\n          const centerY = tag.normalizedY; // 中心点Y\n          const width = tag.normalizedWidth;\n          const height = tag.normalizedHeight;\n          \n          // 计算左上角坐标\n          const pixelX = Math.round((centerX - width/2) * this.imageWidth);\n          const pixelY = Math.round((centerY - height/2) * this.imageHeight);\n          const pixelWidth = Math.round(width * this.imageWidth);\n          const pixelHeight = Math.round(height * this.imageHeight);\n          \n          console.log(`重新计算YOLO标注 ${tag.id}: 中心点(${centerX.toFixed(4)}, ${centerY.toFixed(4)}) -> 左上角(${pixelX}, ${pixelY}), 尺寸: ${pixelWidth} x ${pixelHeight}`);\n          \n          // 返回更新后的标注对象\n          return {\n            ...tag,\n            x: pixelX,\n            y: pixelY,\n            width: pixelWidth,\n            height: pixelHeight\n          };\n        } \n        // 普通左上角坐标系统\n        else if (tag.normalizedX !== undefined) {\n          // 使用归一化坐标重新计算\n          const newX = Math.round(tag.normalizedX * this.imageWidth);\n          const newY = Math.round(tag.normalizedY * this.imageHeight);\n          const newWidth = Math.round(tag.normalizedWidth * this.imageWidth);\n          const newHeight = Math.round(tag.normalizedHeight * this.imageHeight);\n          \n          console.log(`重新计算普通标注 ${tag.id}: 归一化(${tag.normalizedX.toFixed(4)}, ${tag.normalizedY.toFixed(4)}) -> 像素(${newX}, ${newY}), 尺寸: ${newWidth} x ${newHeight}`);\n          \n          return {\n            ...tag,\n            x: newX,\n            y: newY,\n            width: newWidth,\n            height: newHeight\n          };\n        } \n        // 处理历史数据：如果坐标值在0-1范围内，视为归一化坐标\n        else if (tag.x <= 1 && tag.y <= 1 && tag.width <= 1 && tag.height <= 1) {\n          const newX = Math.round(tag.x * this.imageWidth);\n          const newY = Math.round(tag.y * this.imageHeight);\n          const newWidth = Math.round(tag.width * this.imageWidth);\n          const newHeight = Math.round(tag.height * this.imageHeight);\n          \n          console.log(`重新计算旧式归一化标注 ${tag.id}: (${tag.x.toFixed(4)}, ${tag.y.toFixed(4)}) -> (${newX}, ${newY}), 尺寸: ${newWidth} x ${newHeight}`);\n          \n          // 保存归一化坐标，便于后续更新\n          return {\n            ...tag,\n            x: newX,\n            y: newY,\n            width: newWidth,\n            height: newHeight,\n            normalizedX: tag.x,\n            normalizedY: tag.y,\n            normalizedWidth: tag.width,\n            normalizedHeight: tag.height\n          };\n        } else {\n          // 如果是像素坐标，按比例缩放\n          const scaleX = this.imageWidth / this.originalWidth || 1;\n          const scaleY = this.imageHeight / this.originalHeight || 1;\n          \n          if (Math.abs(scaleX - 1) < 0.01 && Math.abs(scaleY - 1) < 0.01) {\n            // 如果缩放比例接近1，则不做调整\n            return tag;\n          }\n          \n          const newX = Math.round(tag.x * scaleX);\n          const newY = Math.round(tag.y * scaleY);\n          const newWidth = Math.round(tag.width * scaleX);\n          const newHeight = Math.round(tag.height * scaleY);\n          \n          console.log(`按比例缩放标注 ${tag.id}: 比例(${scaleX.toFixed(2)}, ${scaleY.toFixed(2)}), (${tag.x}, ${tag.y}) -> (${newX}, ${newY}), 尺寸: ${newWidth} x ${newHeight}`);\n          \n          return {\n            ...tag,\n            x: newX,\n            y: newY,\n            width: newWidth,\n            height: newHeight\n          };\n        }\n      });\n      \n      console.log('标注位置重新计算完成，共', this.annotations.length, '个标注');\n    },\n    selectTool(tool) {\n      this.currentTool = tool\n    },\n    startDrawing(event) {\n      if (this.currentTool !== 'rectangle') return;\n\n      this.isDrawing = true;\n      const rect = this.$refs.imageContainerInner.getBoundingClientRect();\n      this.drawStart.x = event.clientX - rect.left;\n      this.drawStart.y = event.clientY - rect.top;\n      this.drawCurrent.x = this.drawStart.x;\n      this.drawCurrent.y = this.drawStart.y;\n    },\n\n    onDrawing(event) {\n      if (!this.isDrawing) return;\n\n      const rect = this.$refs.imageContainerInner.getBoundingClientRect();\n      this.drawCurrent.x = event.clientX - rect.left;\n      this.drawCurrent.y = event.clientY - rect.top;\n    },\n\n    endDrawing() {\n      if (!this.isDrawing) return\n\n      this.isDrawing = false\n\n      // 添加标注\n      if (Math.abs(this.drawCurrent.x - this.drawStart.x) > 10 && \n          Math.abs(this.drawCurrent.y - this.drawStart.y) > 10) {\n        \n        const x = Math.min(this.drawStart.x, this.drawCurrent.x)\n        const y = Math.min(this.drawStart.y, this.drawCurrent.y)\n        const width = Math.abs(this.drawCurrent.x - this.drawStart.x)\n        const height = Math.abs(this.drawCurrent.y - this.drawStart.y)\n        \n        // 计算归一化坐标 - 存储归一化坐标，以便适应不同大小的显示\n        const normalizedX = x / this.imageWidth\n        const normalizedY = y / this.imageHeight\n        const normalizedWidth = width / this.imageWidth\n        const normalizedHeight = height / this.imageHeight\n        \n        // 创建标注对象\n        const annotation = {\n          id: Date.now(),\n          imageIndex: this.currentImageIndex,\n          tag: this.selectedTag,\n          type: 'rectangle',\n          x: x,\n          y: y,\n          width: width,\n          height: height,\n          // 保存归一化坐标，用于窗口大小变化时重新计算\n          normalizedX: normalizedX,\n          normalizedY: normalizedY,\n          normalizedWidth: normalizedWidth,\n          normalizedHeight: normalizedHeight\n        }\n        \n        // 添加到本地数组\n        this.annotations.push(annotation)\n        \n        // 保存到数据库\n        this.saveAnnotationToDatabase(annotation)\n      }\n    },\n    \n    cancelDrawing() {\n      if (this.isDrawing) {\n        this.isDrawing = false;\n      }\n    },\n    // 开始编辑已有的标注框（移动整个框）\n    startEditBox(event, boxId) {\n      event.preventDefault()\n      \n      // 查找要编辑的标注框\n      const boxToEdit = this.annotations.find(box => box.id === boxId)\n      if (!boxToEdit) return\n      \n      // 保存原始框的信息，用于计算偏移量\n      this.originalBox = {...boxToEdit}\n      \n      const rect = this.$refs.imageContainerInner.getBoundingClientRect()\n      this.editStartPos = {\n        x: event.clientX - rect.left,\n        y: event.clientY - rect.top\n      }\n      \n      this.isEditingBox = true\n      this.editingBoxId = boxId\n      this.isDrawing = false // 确保不在绘制新框\n    },\n    // 开始调整标注框大小\n    startResizeBox(event, boxId, handle) {\n      event.preventDefault()\n      \n      // 查找要调整大小的标注框\n      const boxToResize = this.annotations.find(box => box.id === boxId)\n      if (!boxToResize) return\n      \n      // 保存原始框的信息\n      this.originalBox = {...boxToResize}\n      \n      const rect = this.$refs.imageContainerInner.getBoundingClientRect()\n      this.editStartPos = {\n        x: event.clientX - rect.left,\n        y: event.clientY - rect.top\n      }\n      \n      this.isResizingBox = true\n      this.editingBoxId = boxId\n      this.resizeHandle = handle\n      this.isDrawing = false // 确保不在绘制新框\n    },\n    // 处理拖动框或调整大小\n    handleGlobalMouseMove(event) {\n      // 处理绘制新框\n      if (this.isDrawing) {\n        const rect = this.$refs.imageContainerInner.getBoundingClientRect()\n        const x = event.clientX - rect.left\n        const y = event.clientY - rect.top\n        \n        // 约束坐标在图片范围内\n        const boundedX = Math.max(0, Math.min(this.imageWidth, x))\n        const boundedY = Math.max(0, Math.min(this.imageHeight, y))\n        \n        this.drawCurrent = { x: boundedX, y: boundedY }\n        return\n      }\n      \n      // 处理移动整个框\n      if (this.isEditingBox) {\n        const rect = this.$refs.imageContainerInner.getBoundingClientRect()\n        const currentX = event.clientX - rect.left\n        const currentY = event.clientY - rect.top\n        \n        // 计算移动的偏移量\n        const deltaX = currentX - this.editStartPos.x\n        const deltaY = currentY - this.editStartPos.y\n        \n        // 找到正在编辑的框\n        const boxIndex = this.annotations.findIndex(box => box.id === this.editingBoxId)\n        if (boxIndex === -1) return\n        \n        // 计算新位置，确保在图片范围内\n        let newX = this.originalBox.x + deltaX\n        let newY = this.originalBox.y + deltaY\n        \n        // 防止框移出图片边界\n        newX = Math.max(0, Math.min(newX, this.imageWidth - this.originalBox.width))\n        newY = Math.max(0, Math.min(newY, this.imageHeight - this.originalBox.height))\n        \n        // 更新框位置\n        this.annotations[boxIndex].x = newX\n        this.annotations[boxIndex].y = newY\n        return\n      }\n      \n      // 处理调整框大小\n      if (this.isResizingBox) {\n        const rect = this.$refs.imageContainerInner.getBoundingClientRect()\n        const currentX = event.clientX - rect.left\n        const currentY = event.clientY - rect.top\n        \n        // 约束坐标在图片范围内\n        const boundedX = Math.max(0, Math.min(this.imageWidth, currentX))\n        const boundedY = Math.max(0, Math.min(this.imageHeight, currentY))\n        \n        // 找到正在调整的框\n        const boxIndex = this.annotations.findIndex(box => box.id === this.editingBoxId)\n        if (boxIndex === -1) return\n        \n        const box = this.annotations[boxIndex]\n        const original = this.originalBox\n        \n        // 根据拖动的角落调整大小\n        switch (this.resizeHandle) {\n          case 'top-left':\n            // 调整左上角\n            box.width = original.x + original.width - boundedX\n            box.height = original.y + original.height - boundedY\n            box.x = boundedX\n            box.y = boundedY\n            // 确保宽度和高度为正值\n            if (box.width < 10) {\n              box.width = 10\n              box.x = original.x + original.width - 10\n            }\n            if (box.height < 10) {\n              box.height = 10\n              box.y = original.y + original.height - 10\n            }\n            break;\n            \n          case 'top-right':\n            // 调整右上角\n            box.width = boundedX - original.x\n            box.height = original.y + original.height - boundedY\n            box.y = boundedY\n            // 确保宽度和高度为正值\n            if (box.width < 10) box.width = 10\n            if (box.height < 10) {\n              box.height = 10\n              box.y = original.y + original.height - 10\n            }\n            break;\n            \n          case 'bottom-left':\n            // 调整左下角\n            box.width = original.x + original.width - boundedX\n            box.height = boundedY - original.y\n            box.x = boundedX\n            // 确保宽度和高度为正值\n            if (box.width < 10) {\n              box.width = 10\n              box.x = original.x + original.width - 10\n            }\n            if (box.height < 10) box.height = 10\n            break;\n            \n          case 'bottom-right':\n            // 调整右下角\n            box.width = boundedX - original.x\n            box.height = boundedY - original.y\n            // 确保宽度和高度为正值\n            if (box.width < 10) box.width = 10\n            if (box.height < 10) box.height = 10\n            break;\n        }\n      }\n    },\n    handleGlobalMouseUp() {\n      if (this.isDrawing) {\n        this.endDrawing()\n      } else if (this.isEditingBox || this.isResizingBox) {\n        // 结束框的编辑或调整大小\n        \n        // 找到正在编辑的标注框\n        const editedAnnotation = this.annotations.find(box => box.id === this.editingBoxId)\n        if (editedAnnotation) {\n          // 如果原始框存在，比较是否有变更\n          if (this.originalBox) {\n            const hasChanged = \n              editedAnnotation.x !== this.originalBox.x || \n              editedAnnotation.y !== this.originalBox.y || \n              editedAnnotation.width !== this.originalBox.width || \n              editedAnnotation.height !== this.originalBox.height\n            \n            if (hasChanged) {\n              console.log('标注框已修改，更新数据库:', {\n                原位置: `(${this.originalBox.x}, ${this.originalBox.y})`,\n                原尺寸: `${this.originalBox.width} x ${this.originalBox.height}`,\n                新位置: `(${editedAnnotation.x}, ${editedAnnotation.y})`,\n                新尺寸: `${editedAnnotation.width} x ${editedAnnotation.height}`\n              })\n              \n              // 更新归一化坐标 - 确保在窗口大小变化时标注框保持一致\n              editedAnnotation.normalizedX = editedAnnotation.x / this.imageWidth\n              editedAnnotation.normalizedY = editedAnnotation.y / this.imageHeight\n              editedAnnotation.normalizedWidth = editedAnnotation.width / this.imageWidth\n              editedAnnotation.normalizedHeight = editedAnnotation.height / this.imageHeight\n              \n              // 将修改后的标注框更新到数据库\n              this.updateAnnotationInDatabase(editedAnnotation)\n            }\n          }\n        }\n        \n        // 重置状态\n        this.isEditingBox = false\n        this.isResizingBox = false\n        this.editingBoxId = null\n        this.resizeHandle = null\n        this.originalBox = null\n      }\n    },\n    endDrawing() {\n      if (!this.isDrawing) return\n\n      this.isDrawing = false\n\n      // 添加标注\n      if (Math.abs(this.drawCurrent.x - this.drawStart.x) > 10 && \n          Math.abs(this.drawCurrent.y - this.drawStart.y) > 10) {\n        \n        const x = Math.min(this.drawStart.x, this.drawCurrent.x)\n        const y = Math.min(this.drawStart.y, this.drawCurrent.y)\n        const width = Math.abs(this.drawCurrent.x - this.drawStart.x)\n        const height = Math.abs(this.drawCurrent.y - this.drawStart.y)\n        \n        // 计算归一化坐标 - 存储归一化坐标，以便适应不同大小的显示\n        const normalizedX = x / this.imageWidth\n        const normalizedY = y / this.imageHeight\n        const normalizedWidth = width / this.imageWidth\n        const normalizedHeight = height / this.imageHeight\n        \n        // 创建标注对象\n        const annotation = {\n          id: Date.now(),\n          imageIndex: this.currentImageIndex,\n          tag: this.selectedTag,\n          type: 'rectangle',\n          x: x,\n          y: y,\n          width: width,\n          height: height,\n          // 保存归一化坐标，用于窗口大小变化时重新计算\n          normalizedX: normalizedX,\n          normalizedY: normalizedY,\n          normalizedWidth: normalizedWidth,\n          normalizedHeight: normalizedHeight\n        }\n        \n        // 添加到本地数组\n        this.annotations.push(annotation)\n        \n        // 保存到数据库\n        this.saveAnnotationToDatabase(annotation)\n      }\n    },\n    async saveAnnotationToDatabase(annotation) {\n      console.log('保存标注到数据库', annotation);\n      \n      // 检查是否有imageId\n      if (!this.imageId) {\n        console.error('缺少图像ID，无法保存标注');\n        return;\n      }\n      \n      // 确保图像尺寸有效\n      if (!this.imageWidth || !this.imageHeight) {\n        console.error('图像尺寸无效，无法计算归一化坐标');\n        return;\n      }\n      \n      try {\n        // 声明归一化坐标变量\n        let normalizedX, normalizedY, normalizedWidth, normalizedHeight;\n        \n        // 根据标注类型计算归一化坐标\n        if (annotation.isYoloFormat) {\n          // 如果是YOLO格式的标注，使用中心点坐标系统\n          // 先将左上角坐标转换为中心点坐标\n          normalizedWidth = annotation.width / this.imageWidth;\n          normalizedHeight = annotation.height / this.imageHeight;\n          normalizedX = (annotation.x / this.imageWidth) + (normalizedWidth / 2);\n          normalizedY = (annotation.y / this.imageHeight) + (normalizedHeight / 2);\n          \n          console.log('保存YOLO格式标注，转换为中心点坐标：', {\n            左上角像素: `(${annotation.x}, ${annotation.y})`,\n            像素宽高: `${annotation.width} x ${annotation.height}`,\n            归一化中心点: `(${normalizedX.toFixed(4)}, ${normalizedY.toFixed(4)})`,\n            归一化宽高: `${normalizedWidth.toFixed(4)} x ${normalizedHeight.toFixed(4)}`\n          });\n        } else {\n          // 普通左上角坐标系统\n          normalizedX = annotation.x / this.imageWidth;\n          normalizedY = annotation.y / this.imageHeight;\n          normalizedWidth = annotation.width / this.imageWidth;\n          normalizedHeight = annotation.height / this.imageHeight;\n          \n          console.log('保存普通格式标注，使用左上角坐标：', {\n            像素坐标: `(${annotation.x}, ${annotation.y})`,\n            像素宽高: `${annotation.width} x ${annotation.height}`,\n            归一化坐标: `(${normalizedX.toFixed(4)}, ${normalizedY.toFixed(4)})`,\n            归一化宽高: `${normalizedWidth.toFixed(4)} x ${normalizedHeight.toFixed(4)}`\n          });\n        }\n        \n        // 限制归一化坐标在0-1范围内\n        normalizedX = Math.max(0, Math.min(1, normalizedX));\n        normalizedY = Math.max(0, Math.min(1, normalizedY));\n        normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth));\n        normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight));\n        \n        // 创建要保存的标注数据\n        const tagData = {\n          metadata_id: parseInt(this.imageId, 10),\n          tag: annotation.tag,\n          x: Number(normalizedX.toFixed(6)),\n          y: Number(normalizedY.toFixed(6)),\n          width: Number(normalizedWidth.toFixed(6)),\n          height: Number(normalizedHeight.toFixed(6)),\n          // 记录坐标系统类型，方便后续处理\n          coord_system: annotation.isYoloFormat ? 'yolo_center' : 'top_left'\n        };\n        \n        console.log('保存标注数据:', tagData);\n        \n        // 使用API保存标注\n        const response = await api.tags.create(tagData);\n        console.log('标注保存成功:', response.data);\n        \n        // 更新annotation对象，添加数据库ID和正确的归一化坐标\n        annotation.dbId = response.data.id;\n        annotation.normalizedX = normalizedX;\n        annotation.normalizedY = normalizedY;\n        annotation.normalizedWidth = normalizedWidth;\n        annotation.normalizedHeight = normalizedHeight;\n        \n        this.$message.success('标注保存成功');\n        return response.data;\n      } catch (error) {\n        console.error('保存标注失败:', error);\n        this.$message.error('保存标注失败: ' + (error.message || '未知错误'));\n        throw error;\n      }\n    },\n    saveAnnotationToDatabase(annotation) {\n      // 强制输出调试信息\n      console.clear(); // 清除之前的控制台输出\n      console.log('%c==================== 开始保存标注到数据库 ====================', 'background: #222; color: #bada55; font-size: 16px;');\n      \n      // 获取当前用户信息\n      const user = JSON.parse(localStorage.getItem('user'))\n      if (!user || !user.id) {\n        this.$message.error('未检测到有效用户信息(缺少ID)，无法保存标注')\n        console.error('未检测到有效用户信息(缺少ID)，无法保存标注', user)\n        return\n      }\n      \n      // 获取当前图像的metadata_id\n      const currentImage = this.uploadedImages[this.currentImageIndex]\n      if (!currentImage || !currentImage.id) {\n        this.$message.error('图像信息不完整，无法保存标注')\n        console.error('图像信息不完整，无法保存标注, currentImage:', currentImage)\n        return\n      }\n      \n      // 打印调试信息（强制显示在控制台）\n      const debugInfo = {\n        图像ID: currentImage.id,\n        图像ID类型: typeof currentImage.id,\n        标签类型: annotation.tag,\n        坐标: `(${annotation.x}, ${annotation.y})`,\n        尺寸: `${annotation.width} x ${annotation.height}`,\n        用户ID: user.id || user.customId,\n        用户ID类型: typeof (user.id || user.customId),\n        用户角色: user.role\n      };\n      console.log('%c标注数据:', 'color: #47B881; font-weight: bold;', debugInfo);\n      \n      // 使用已保存的归一化坐标(如果存在)，否则计算归一化坐标\n      let normalizedX, normalizedY, normalizedWidth, normalizedHeight;\n      \n      if (annotation.normalizedX !== undefined) {\n        // 使用已保存的归一化坐标\n        normalizedX = annotation.normalizedX;\n        normalizedY = annotation.normalizedY;\n        normalizedWidth = annotation.normalizedWidth;\n        normalizedHeight = annotation.normalizedHeight;\n        console.log('使用已保存的归一化坐标:', {\n          x: normalizedX, y: normalizedY, \n          width: normalizedWidth, height: normalizedHeight\n        });\n      } else {\n        // 确保imageWidth和imageHeight不为0\n        if (!this.imageWidth || !this.imageHeight) {\n          this.$message.error('图片尺寸无效，无法计算标注坐标')\n          return\n        }\n        \n        // 归一化计算 - 将像素坐标转换为0-1范围\n        normalizedX = annotation.x / this.imageWidth;\n        normalizedY = annotation.y / this.imageHeight;\n        normalizedWidth = annotation.width / this.imageWidth;\n        normalizedHeight = annotation.height / this.imageHeight;\n        console.log('计算新的归一化坐标:', {\n          x: normalizedX, y: normalizedY, \n          width: normalizedWidth, height: normalizedHeight\n        });\n      }\n      \n      // 验证并修正坐标范围\n      // x, y必须在[0,1]之间\n      normalizedX = Math.max(0, Math.min(1, normalizedX))\n      normalizedY = Math.max(0, Math.min(1, normalizedY))\n      \n      // width, height必须在(0,1]之间\n      normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth))\n      normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight))\n      \n      // 保留4位小数\n      normalizedX = Number(normalizedX.toFixed(4))\n      normalizedY = Number(normalizedY.toFixed(4))\n      normalizedWidth = Number(normalizedWidth.toFixed(4))\n      normalizedHeight = Number(normalizedHeight.toFixed(4))\n      \n      // 构建要发送到后端的数据\n      const tagData = {\n        tag: annotation.tag,\n        tagName: annotation.tag,\n        x: normalizedX,\n        y: normalizedY,\n        width: normalizedWidth,\n        height: normalizedHeight,\n        metadata_id: parseInt(currentImage.id, 10),\n        created_by: parseInt(user.id, 10)\n      }\n      \n      // 确认数据类型正确\n      console.log('发送标注数据:', tagData, {\n        metadata_id类型: typeof tagData.metadata_id,\n        created_by类型: typeof tagData.created_by\n      })\n      \n      // 显示保存中提示\n      const loadingInstance = this.$loading({\n        lock: true,\n        text: '正在保存标注...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      });\n      \n      // 获取用户认证信息 - 如果有token则添加到请求头\n      const token = localStorage.getItem('token') || '';\n      const userId = user.id || user.customId || '';\n      const userRole = user.role || 'USER';\n      \n      const headers = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      };\n      \n      // 如果有token，添加认证头\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      \n      // 添加用户ID和角色头\n      headers['X-User-Id'] = userId;\n      headers['X-User-Role'] = userRole;\n      \n      console.log('使用认证头信息:', {\n        Authorization: token ? 'Bearer [已设置]' : '[未设置]',\n        'X-User-Id': userId,\n        'X-User-Role': userRole\n      });\n      \n      // 使用fetch API直接发送请求\n      fetch('/medical/api/tags', {\n        method: 'POST',\n        headers: headers,\n        body: JSON.stringify(tagData),\n        credentials: 'include'\n      })\n      .then(response => {\n        console.log('保存标注响应状态:', response.status);\n        \n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return response.json();\n      })\n      .then(data => {\n        loadingInstance.close();\n        console.log('标注保存成功:', data);\n        this.$message.success('标注保存成功');\n        \n        // 更新标注数据，添加数据库ID\n        annotation.dbId = data.id;\n        \n        // 添加/更新归一化坐标，方便窗口大小变化时重新计算\n        annotation.normalizedX = normalizedX;\n        annotation.normalizedY = normalizedY;\n        annotation.normalizedWidth = normalizedWidth;\n        annotation.normalizedHeight = normalizedHeight;\n        \n        // 刷新标注列表\n        this.loadAnnotationsFromDatabase().then(() => {\n          this.processLoadedAnnotations();\n        });\n      })\n      .catch(error => {\n        console.error('保存标注失败:', error);\n        \n        // 尝试备用路径\n        console.log('尝试备用API路径...');\n        \n        fetch('/api/tags', {\n          method: 'POST',\n          headers: headers,\n          body: JSON.stringify(tagData),\n          credentials: 'include'\n        })\n        .then(response => {\n          if (!response.ok) {\n            throw new Error(`备用路径也失败! status: ${response.status}`);\n          }\n          return response.json();\n        })\n        .then(data => {\n          loadingInstance.close();\n          console.log('通过备用路径保存标注成功:', data);\n          this.$message.success('标注保存成功');\n          \n          // 更新标注数据，添加数据库ID\n          annotation.dbId = data.id;\n          \n          // 添加归一化坐标\n          annotation.normalizedX = normalizedX;\n          annotation.normalizedY = normalizedY;\n          annotation.normalizedWidth = normalizedWidth;\n          annotation.normalizedHeight = normalizedHeight;\n          \n          // 刷新标注列表\n          this.loadAnnotationsFromDatabase().then(() => {\n            this.processLoadedAnnotations();\n          });\n        })\n        .catch(backupError => {\n          loadingInstance.close();\n          console.error('所有尝试都失败:', backupError);\n          \n          // 即使保存失败，也添加到本地标注列表\n          this.$message.warning('无法保存到服务器，但已添加到本地标注列表');\n          \n          // 添加归一化坐标\n          annotation.normalizedX = normalizedX;\n          annotation.normalizedY = normalizedY;\n          annotation.normalizedWidth = normalizedWidth;\n          annotation.normalizedHeight = normalizedHeight;\n        });\n      });\n    },\n    getTagColor(tag) {\n      // 根据标签类型返回不同的颜色\n      const colorMap = {\n        'IH-婴幼儿血管瘤': '#ff5252',\n        'RICH-先天性快速消退型血管瘤': '#ff7252',\n        'PICH-先天性部分消退型血管瘤': '#ff9252',\n        'NICH-先天性不消退型血管瘤': '#ffb252',\n        'KHE-卡泊西型血管内皮细胞瘤': '#4caf50',\n        'KH-角化型血管瘤': '#8bc34a',\n        'PG-肉芽肿性血管瘤': '#cddc39',\n        'MVM-微静脉畸形': '#2196f3',\n        'VM-静脉畸形': '#03a9f4',\n        'AVM-动静脉畸形': '#00bcd4',\n        '血管瘤': '#ff5252',\n        '淋巴管瘤': '#2196f3',\n        '混合型': '#9c27b0',\n        '其他': '#607d8b'\n      };\n      \n      return colorMap[tag] || '#ff5252'; // 默认红色\n    },\n    deleteAnnotation(id) {\n      // 记录当前标注数量，用于后续检查\n      const beforeCount = this.annotations.length;\n      console.log(`删除标注开始，当前标注数量: ${beforeCount}, 要删除的ID: ${id}`);\n      \n      const index = this.annotations.findIndex(item => item.id === id);\n      if (index !== -1) {\n        const annotation = this.annotations[index];\n        \n        // 显示删除中的加载状态\n        const loading = this.$loading({\n          lock: true,\n          text: '正在删除标注...',\n          spinner: 'el-icon-loading',\n          background: 'rgba(0, 0, 0, 0.7)'\n        });\n        \n        // 从本地数组中删除\n        this.annotations.splice(index, 1);\n        console.log(`已从本地数组中删除标注，剩余标注数量: ${this.annotations.length}`);\n        \n        // 如果有数据库ID，则从数据库中也删除\n        if (annotation.dbId) {\n          console.log('正在从数据库删除标注，ID:', annotation.dbId);\n          \n          // 保存原始标注，用于恢复\n          const originalAnnotation = {...annotation};\n          \n          api.tags.delete(annotation.dbId)\n            .then(() => {\n              console.log('标注已从数据库中删除');\n              this.$message.success('标注已删除');\n              loading.close();\n              \n              // 删除成功后，检查标注数量是否正确\n              console.log(`删除成功后标注数量: ${this.annotations.length}`);\n            })\n            .catch(error => {\n              console.error('删除标注失败', error);\n              \n              // 详细记录错误信息\n              if (error.response) {\n                console.error('服务器响应:', error.response.status, error.response.data);\n                \n                // 根据不同的错误状态码提供不同的错误信息\n                if (error.response.status === 403) {\n                  this.$message.error('您没有权限删除此标注');\n                } else if (error.response.status === 404) {\n                  this.$message.error('标注不存在或已被删除');\n                } else {\n                  this.$message.error(`删除失败: ${error.response.data || '服务器错误'}`);\n                }\n              } else if (error.request) {\n                this.$message.error('网络错误，无法连接到服务器');\n              } else {\n                this.$message.error('删除标注失败: ' + error.message);\n              }\n              \n              loading.close();\n              \n              // 恢复本地删除的标注 - 使用索引确保不会重复添加\n              console.log(`恢复标注到索引 ${index}，恢复前标注数量: ${this.annotations.length}`);\n              this.annotations.splice(index, 0, originalAnnotation);\n              console.log(`恢复后标注数量: ${this.annotations.length}`);\n              \n              // 检查是否有重复标注\n              this.checkForDuplicateAnnotations();\n            });\n        } else {\n          // 如果没有数据库ID，只需要本地删除\n          this.$message.success('标注已删除');\n          loading.close();\n          console.log(`本地标注删除完成，当前标注数量: ${this.annotations.length}`);\n        }\n      } else {\n        console.error(`未找到ID为 ${id} 的标注`);\n        this.$message.warning('未找到要删除的标注');\n      }\n    },\n    \n    // 检查并移除重复的标注\n    checkForDuplicateAnnotations() {\n      console.log('检查是否有重复标注...');\n      \n      // 使用Map记录已存在的标注ID\n      const idMap = new Map();\n      const duplicates = [];\n      \n      // 查找重复项\n      this.annotations.forEach((annotation, index) => {\n        if (idMap.has(annotation.id)) {\n          duplicates.push(index);\n        } else {\n          idMap.set(annotation.id, index);\n        }\n      });\n      \n      // 如果找到重复项，从后向前删除（避免索引变化）\n      if (duplicates.length > 0) {\n        console.warn(`发现 ${duplicates.length} 个重复标注，将被移除`);\n        for (let i = duplicates.length - 1; i >= 0; i--) {\n          this.annotations.splice(duplicates[i], 1);\n        }\n        this.$message.warning(`已自动移除 ${duplicates.length} 个重复标注`);\n      }\n    },\n    async saveAndNext() {\n      return new Promise(async (resolve, reject) => {\n        try {\n          // 设置保存状态\n          this.isSaving = true;\n          \n          // 输出调试信息\n          console.log('[saveAndNext] 开始保存标注并跳转');\n          console.log('[saveAndNext] 当前标注数量:', this.annotations.length);\n          \n          // 显示确认对话框\n          try {\n            await this.$confirm('是否继续填写表单？', '确认操作', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'info'\n            });\n            console.log('[saveAndNext] 用户确认继续');\n          } catch (e) {\n            // 用户取消了操作\n            console.log('[saveAndNext] 用户取消了操作');\n            this.isSaving = false; // 重置保存状态\n            resolve(false);\n            return;\n          }\n          \n          console.log('[导航跟踪] 准备跳转到表单页，当前URL:', window.location.href);\n          \n          // 获取当前用户信息，确保认证有效\n          const user = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.userInfo) || '{}');\n          const userId = user.id || user.customId || '';\n          const userRole = user.role || 'USER';\n          const token = localStorage.getItem(CONFIG.STORAGE_KEYS.token);\n          \n          // 主动更新认证状态标记\n          sessionStorage.setItem(CONFIG.STORAGE_KEYS.authValid, 'true');\n          sessionStorage.setItem(CONFIG.STORAGE_KEYS.isNavigatingAfterSave, 'true');\n          \n          console.log('[认证状态] 用户ID:', userId, '角色:', userRole);\n          \n          // 确保诊断ID存在，尝试多种方式获取\n          let diagnosisId = this.diagnosisId;\n          \n          // 如果diagnosisId不存在，尝试从路由参数获取\n          if (!diagnosisId) {\n            diagnosisId = this.$route.query.diagnosisId || this.$route.params.id;\n          }\n          \n          // 如果仍然不存在，尝试从当前图像获取\n          if (!diagnosisId && this.imageId) {\n            diagnosisId = this.imageId;\n            console.log('[诊断ID] 使用imageId作为诊断ID:', diagnosisId);\n          }\n          \n          // 获取当前图像信息，用于保存标注\n          const currentImage = this.uploadedImages && this.uploadedImages.length > 0 ? \n                              this.uploadedImages[this.currentImageIndex] : null;\n          \n          if (!currentImage || !currentImage.id) {\n            console.error('[saveAndNext] 无法获取当前图像信息:', {\n              hasUploadedImages: !!(this.uploadedImages && this.uploadedImages.length > 0),\n              currentImageIndex: this.currentImageIndex\n            });\n            this.$message.warning('无法获取图像信息，将尝试使用备用方法保存标注');\n          } else {\n            console.log('[saveAndNext] 使用图像信息:', {\n              id: currentImage.id,\n              url: currentImage.url\n            });\n          }\n          \n          // 使用图像ID\n          const imageId = currentImage?.id || diagnosisId || this.imageId;\n          \n          if (!imageId) {\n            this.$message.error('无法获取有效的图像ID，无法保存标注');\n            this.isSaving = false;\n            resolve(false);\n            return;\n          }\n          \n          console.log('[saveAndNext] 使用的图像ID:', imageId);\n          \n          // 只有在有标注数据时才尝试保存\n          if (this.annotations && this.annotations.length > 0) {\n            try {\n              // 显示保存中的加载状态\n              const loading = this.$loading({\n                lock: true,\n                text: '保存标注并生成图像...',\n                spinner: 'el-icon-loading',\n                background: 'rgba(0, 0, 0, 0.7)'\n              });\n              \n              // 添加重试机制\n              const maxRetries = 3;\n              let currentRetry = 0;\n              let lastError = null;\n              let saveSuccess = false;\n              let errorMessage = '';\n              \n              while (currentRetry < maxRetries && !saveSuccess) {\n                try {\n                  console.log(`[saveAndNext] 尝试保存标注，第 ${currentRetry + 1} 次尝试`);\n                  \n                  // 修改为调用立即保存方法\n                  await this.saveAnnotatedImageAfterEdit(imageId, true);\n                  console.log('[saveAndNext] 标注图像保存成功');\n                  \n                  // 标记为成功\n                  saveSuccess = true;\n                  \n                  // 在本地存储中保存标注数据作为备份\n                  localStorage.setItem('annotations', JSON.stringify(this.annotations));\n                  this.$message.success('标注已保存');\n                  \n                } catch (error) {\n                  lastError = error;\n                  console.error(`[saveAndNext] 第 ${currentRetry + 1} 次保存标注失败:`, error);\n                  \n                  // 获取详细错误信息用于显示\n                  if (error.response) {\n                    errorMessage = `服务器错误 (${error.response.status}): ${\n                      typeof error.response.data === 'string' \n                        ? error.response.data \n                        : (error.response.data?.error || error.response.data?.message || JSON.stringify(error.response.data))\n                    }`;\n                  } else if (error.request) {\n                    errorMessage = '服务器未响应';\n                  } else {\n                    errorMessage = error.message || '未知错误';\n                  }\n                  \n                  // 增加重试间隔\n                  await new Promise(resolve => setTimeout(resolve, 1000 * (currentRetry + 1)));\n                  currentRetry++;\n                }\n              }\n              \n              // 关闭加载状态\n              loading.close();\n              \n              if (!saveSuccess) {\n                console.error('[saveAndNext] 所有保存尝试均失败');\n                this.$message.error(`保存标注失败: ${errorMessage}`);\n                \n                // 询问用户是否要继续\n                try {\n                  await this.$confirm('保存标注失败，是否仍要继续到表单页面？', '提示', {\n                    confirmButtonText: '继续',\n                    cancelButtonText: '留在当前页面',\n                    type: 'warning'\n                  });\n                  console.log('[saveAndNext] 用户选择继续到表单页面');\n                  // 如果用户选择继续，我们会继续导航\n                } catch (e) {\n                  console.log('[saveAndNext] 用户选择留在当前页面');\n                  this.isSaving = false;\n                  resolve(false);\n                  return;\n                }\n              }\n            } catch (error) {\n              console.error('[saveAndNext] 保存标注图像过程中出错:', error);\n              this.$message.error('保存标注过程中出错: ' + (error.message || '未知错误'));\n              \n              // 询问用户是否要继续\n              try {\n                await this.$confirm('保存标注失败，是否仍要继续到表单页面？', '提示', {\n                  confirmButtonText: '继续',\n                  cancelButtonText: '留在当前页面',\n                  type: 'warning'\n                });\n              } catch (e) {\n                this.isSaving = false;\n                resolve(false);\n                return;\n              }\n            }\n          } else {\n            console.log('[saveAndNext] 没有标注数据，跳过保存步骤');\n          }\n          \n          // 添加一个小延迟，确保状态保存后再导航\n          await new Promise(resolve => setTimeout(resolve, CONFIG.UI.navigationDelay));\n          \n          // 如果仍然不存在，尝试从当前图像对象获取\n          if (!diagnosisId && this.currentImageIndex >= 0 && this.uploadedImages && this.uploadedImages.length > 0) {\n            const currentImage = this.uploadedImages[this.currentImageIndex];\n            if (currentImage && currentImage.id) {\n              diagnosisId = currentImage.id;\n              console.log('[诊断ID] 使用当前图像ID作为诊断ID:', diagnosisId);\n            }\n          }\n          \n          // 最终确认是否有有效的诊断ID\n          if (!diagnosisId) {\n            this.$message.error('未找到诊断ID，无法继续');\n            console.error('[导航错误] 未找到有效的诊断ID');\n            console.log('[诊断状态]', {\n              this_diagnosisId: this.diagnosisId,\n              route_query: this.$route.query.diagnosisId,\n              route_params: this.$route.params.id,\n              imageId: this.imageId,\n              currentImageIndex: this.currentImageIndex,\n              hasUploadedImages: !!(this.uploadedImages && this.uploadedImages.length > 0)\n            });\n            this.isSaving = false;\n            resolve(false);\n            return;\n          }\n          \n          console.log('[诊断ID] 最终使用的诊断ID:', diagnosisId);\n          \n          // 使用router进行导航，不使用window.location\n          const route = `${CONFIG.ROUTES.structuredForm}?diagnosisId=${diagnosisId}`;\n          console.log('[导航] 即将导航到:', route);\n          \n          try {\n            await this.$router.push(route);\n            console.log('[导航] 导航完成');\n            resolve(true);\n          } catch (routerError) {\n            console.error('[导航] 路由导航失败:', routerError);\n            \n            // 如果路由导航失败，尝试备用方案\n            try {\n              await this.$router.replace(route);\n              console.log('[导航] 备用导航完成');\n              resolve(true);\n            } catch (backupError) {\n              console.error('[导航] 备用导航也失败:', backupError);\n              this.$message.error('导航失败，请手动访问结构化表单页面');\n              \n              // 最后的备用方案，刷新页面\n              this.isSaving = false;\n              sessionStorage.setItem(CONFIG.STORAGE_KEYS.pendingDiagnosis, diagnosisId);\n              setTimeout(() => {\n                window.location.href = route;\n              }, 200);\n              resolve(false);\n            }\n          }\n        } catch (error) {\n          console.error('[saveAndNext] 操作失败:', error);\n          this.$message.error('操作失败，请重试');\n          reject(error);\n        } finally {\n          // 确保总是重置保存状态\n          setTimeout(() => {\n            this.isSaving = false;\n          }, 1000);\n        }\n      });\n    },\n    // 新增：确认是否继续的辅助方法\n    async confirmContinue() {\n      try {\n        await this.$confirm('保存标注失败，是否仍要继续到表单页面？', '提示', {\n          confirmButtonText: '继续',\n          cancelButtonText: '留在当前页面',\n          type: 'warning'\n        });\n        return true;\n      } catch (e) {\n        return false;\n      }\n    },\n    // 新增：通用的保存标注方法\n    async saveAnnotations(loadingText = '保存中...') {\n      // 获取当前图像\n      const currentImage = this.uploadedImages[this.currentImageIndex]\n      if (!currentImage || !currentImage.id) {\n        this.$message.error('无效的图像，无法保存标注')\n        return false\n      }\n      \n      // 防止重复提交\n      if (this.isSavingAnnotations) {\n        console.warn('正在保存标注，请勿重复操作')\n        return false\n      }\n      \n      this.isSavingAnnotations = true\n      \n      // 显示加载状态\n      const loading = this.$loading({\n        lock: true,\n        text: loadingText,\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n      \n      try {\n        // 调用API保存标注图像\n        console.log('保存标注，图像ID:', currentImage.id)\n        \n        // 调用saveAnnotatedImage API（这个API会自动生成标注图像）\n        try {\n          const response = await api.tags.saveAnnotatedImage(currentImage.id)\n          console.log('标注图像保存成功:', response.data)\n        } catch (annotationError) {\n          console.error('保存标注图像失败:', annotationError)\n          \n          // 如果是服务器错误，可以尝试更新原有的图像\n          if (annotationError.response && annotationError.response.status >= 500) {\n            try {\n              console.log('尝试使用另一种方法更新标注图像')\n              await api.tags.updateImageAfterAnnotation({ metadata_id: currentImage.id })\n              console.log('更新标注图像成功')\n            } catch (updateError) {\n              console.error('更新标注图像也失败:', updateError)\n              throw updateError\n            }\n          } else {\n            throw annotationError\n          }\n        }\n        \n        // 更新图像状态为已标注，添加重试机制\n        try {\n          // 获取当前中国时间\n          const now = new Date()\n          const chinaTime = new Intl.DateTimeFormat('zh-CN', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit',\n            hour: '2-digit',\n            minute: '2-digit',\n            second: '2-digit',\n            hour12: false,\n            timeZone: 'Asia/Shanghai'\n          }).format(now)\n          \n          // 转换为ISO-8601格式\n          const isoTime = now.toISOString()\n          console.log('当前中国时间:', chinaTime, '，ISO时间:', isoTime)\n          \n          // 传递时间戳参数，避免重复请求\n          try {\n            await api.images.markAsAnnotated(currentImage.id, isoTime)\n            console.log('图像状态已成功更新为\"已标注\"(REVIEWED)')\n          } catch (markError) {\n            console.error('标记为已标注失败:', markError)\n            // 如果失败是因为特定错误，不再重试\n            if (markError.response && markError.response.data && \n                markError.response.data.includes('重复')) {\n              console.log('检测到重复标记消息，不再重试')\n            } else {\n              // 尝试使用另一种方法更新状态\n              await api.images.updateStatus(currentImage.id, 'REVIEWED')\n              console.log('使用updateStatus成功更新状态为\"已标注\"')\n            }\n          }\n        } catch (statusError) {\n          console.error('所有更新图像状态的尝试都失败:', statusError)\n          // 即使状态更新失败，我们仍然继续流程\n        }\n        \n        // 显示成功消息\n        this.$message.success('标注已保存')\n        \n        // 关闭加载状态并重置防重复标志\n        loading.close()\n        this.isSavingAnnotations = false\n        return true\n      } catch (error) {\n        console.error('保存标注图片失败', error)\n        \n        // 获取详细错误信息\n        let errorMessage = '保存标注失败'\n        if (error.response) {\n          console.error('服务器响应:', error.response.status, error.response.data)\n          \n          if (typeof error.response.data === 'string') {\n            errorMessage += ': ' + error.response.data\n          } else if (error.response.data && error.response.data.message) {\n            errorMessage += ': ' + error.response.data.message\n          } else {\n            errorMessage += ' (状态码: ' + error.response.status + ')'\n          }\n        } else if (error.message) {\n          errorMessage += ': ' + error.message\n        }\n        \n        // 仅显示错误消息，不阻止流程继续\n        this.$message.error(errorMessage)\n        loading.close()\n        this.isSavingAnnotations = false\n        \n        // 询问用户是否要继续，即使保存标注失败\n        try {\n          const result = await this.$confirm('保存标注失败，但已在本地保存标注数据。是否继续?', '警告', {\n            confirmButtonText: '继续',\n            cancelButtonText: '留在当前页面',\n            type: 'warning'\n          })\n          \n          // 如果用户选择继续，保存标注数据到本地存储\n          if (result === 'confirm') {\n            localStorage.setItem('annotations', JSON.stringify(this.annotations))\n            localStorage.setItem('pendingImageId', currentImage.id.toString())\n            return true\n          }\n          return false\n        } catch (e) {\n          // 用户选择留在当前页面\n          return false\n        }\n      }\n    },\n    loadAnnotationsFromDatabase() {\n      console.log('从数据库加载标注数据，图像ID:', this.imageId);\n      \n      return new Promise((resolve, reject) => {\n        // 获取用户认证信息\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        const token = localStorage.getItem('token') || '';\n        const userId = user.id || user.customId || '';\n        const userRole = user.role || 'USER';\n        \n        // 设置请求头\n        const headers = {\n          'Accept': 'application/json',\n          'Cache-Control': 'no-cache',\n          'Content-Type': 'application/json'\n        };\n        \n        // 如果有token，添加认证头\n        if (token) {\n          headers['Authorization'] = `Bearer ${token}`;\n        }\n        \n        // 添加用户ID和角色头\n        headers['X-User-Id'] = userId;\n        headers['X-User-Role'] = userRole;\n        \n        console.log('加载标注数据 - 使用认证头:', {\n          Authorization: token ? 'Bearer [已设置]' : '[未设置]',\n          'X-User-Id': userId,\n          'X-User-Role': userRole\n        });\n        \n        // 添加时间戳和随机数，避免缓存问题\n        const timestamp = Date.now();\n        const random = Math.random();\n        const cacheBuster = `t=${timestamp}&r=${random}`;\n        \n        // 使用fetch API加载标注数据\n        const url = `/medical/api/tags/image/${this.imageId}?${cacheBuster}&userId=${userId}`;\n        \n        fetch(url, {\n          method: 'GET',\n          headers: headers,\n          credentials: 'include'\n        })\n          .then(response => {\n            if (!response.ok) {\n              throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return response.json();\n          })\n          .then(tags => {\n            console.log('从数据库获取到标注数据:', tags);\n            \n            // 更新数据库标注数据\n            this.dbAnnotations = tags.map(tag => ({\n              id: tag.id,\n              tag: tag.tagName || tag.tag, // 优先使用tagName字段，如果不存在则使用tag字段\n              name: tag.tagName || tag.tag, // 同样优先使用tagName\n              x: tag.x,\n              y: tag.y,\n              width: tag.width,\n              height: tag.height,\n              created_by: tag.created_by\n            }));\n            \n            console.log('处理后的标注数据:', this.dbAnnotations);\n            resolve(this.dbAnnotations);\n          })\n          .catch(error => {\n            console.error('获取标注数据失败:', error);\n            \n            // 尝试备用API路径\n            console.log('尝试备用API路径...');\n            const backupUrl = `/api/tags/image/${this.imageId}?${cacheBuster}&userId=${userId}`;\n            \n            fetch(backupUrl, {\n              method: 'GET',\n              headers: headers,\n              credentials: 'include'\n            })\n              .then(response => {\n                if (!response.ok) {\n                  throw new Error(`备用路径也失败! status: ${response.status}`);\n                }\n                return response.json();\n              })\n              .then(tags => {\n                console.log('通过备用路径获取到标注数据:', tags);\n                \n                // 更新数据库标注数据\n                this.dbAnnotations = tags.map(tag => ({\n                  id: tag.id,\n                  tag: tag.tagName || tag.tag,\n                  name: tag.tagName || tag.tag,\n                  x: tag.x,\n                  y: tag.y,\n                  width: tag.width,\n                  height: tag.height,\n                  created_by: tag.created_by\n                }));\n                \n                console.log('处理后的标注数据:', this.dbAnnotations);\n                resolve(this.dbAnnotations);\n              })\n              .catch(backupError => {\n                console.error('所有尝试都失败:', backupError);\n                \n                // 如果都失败了，检查是否有本地保存的标注数据\n                const localAnnotations = localStorage.getItem(`annotations_${this.imageId}`);\n                if (localAnnotations) {\n                  try {\n                    const parsedAnnotations = JSON.parse(localAnnotations);\n                    console.log('找到本地保存的标注数据:', parsedAnnotations);\n                    this.dbAnnotations = parsedAnnotations;\n                    resolve(this.dbAnnotations);\n                  } catch (e) {\n                    console.error('解析本地标注数据失败:', e);\n                    reject(backupError);\n                  }\n                } else {\n                  reject(backupError);\n                }\n              });\n          });\n      });\n    },\n    updateAnnotationInDatabase(annotation) {\n      // 获取当前用户信息\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      if (!user || (!user.id && !user.customId)) {\n        this.$message.error('未检测到有效用户信息，无法更新标注');\n        return;\n      }\n      \n      const userId = user.id || user.customId;\n      const userRole = user.role || 'USER';\n      \n      // 如果没有数据库ID，则无法更新\n      if (!annotation.dbId) {\n        console.warn('标注没有数据库ID，无法更新:', annotation);\n        return;\n      }\n      \n      // 计算归一化坐标（将像素坐标转换为0-1范围）\n      // 确保imageWidth和imageHeight不为0\n      if (!this.imageWidth || !this.imageHeight) {\n        this.$message.error('图片尺寸无效，无法计算标注坐标');\n        return;\n      }\n      \n      // 归一化计算\n      let normalizedX, normalizedY, normalizedWidth, normalizedHeight;\n      \n      // 根据标注类型计算归一化坐标\n      if (annotation.isYoloFormat) {\n        // 如果是YOLO格式的标注，使用中心点坐标系统\n        // 先将左上角坐标转换为中心点坐标\n        normalizedWidth = annotation.width / this.imageWidth;\n        normalizedHeight = annotation.height / this.imageHeight;\n        normalizedX = (annotation.x / this.imageWidth) + (normalizedWidth / 2);\n        normalizedY = (annotation.y / this.imageHeight) + (normalizedHeight / 2);\n      } else {\n        // 普通左上角坐标系统\n        normalizedX = annotation.x / this.imageWidth;\n        normalizedY = annotation.y / this.imageHeight;\n        normalizedWidth = annotation.width / this.imageWidth;\n        normalizedHeight = annotation.height / this.imageHeight;\n      }\n      \n      // 验证并修正坐标范围\n      // x, y必须在[0,1]之间\n      normalizedX = Math.max(0, Math.min(1, normalizedX));\n      normalizedY = Math.max(0, Math.min(1, normalizedY));\n      \n      // width, height必须在(0,1]之间\n      normalizedWidth = Math.max(0.001, Math.min(1, normalizedWidth));\n      normalizedHeight = Math.max(0.001, Math.min(1, normalizedHeight));\n      \n      // 保留4位小数\n      normalizedX = Number(normalizedX.toFixed(4));\n      normalizedY = Number(normalizedY.toFixed(4));\n      normalizedWidth = Number(normalizedWidth.toFixed(4));\n      normalizedHeight = Number(normalizedHeight.toFixed(4));\n      \n      // 构建要发送到后端的数据\n      const tagData = {\n        tag: annotation.tag,\n        x: normalizedX,\n        y: normalizedY,\n        width: normalizedWidth,\n        height: normalizedHeight,\n        updated_by: userId,\n        coord_system: annotation.isYoloFormat ? 'yolo_center' : 'top_left'\n      };\n      \n      console.log('更新标注数据:', {\n        id: annotation.dbId,\n        归一化坐标: `(${normalizedX}, ${normalizedY})`,\n        归一化尺寸: `${normalizedWidth} x ${normalizedHeight}`,\n        坐标系统: annotation.isYoloFormat ? 'YOLO中心点' : '左上角'\n      });\n      \n      // 显示加载状态\n      const loading = this.$loading({\n        lock: true,\n        text: '正在更新标注...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      });\n      \n      // 设置请求头\n      const headers = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'X-User-Id': userId,\n        'X-User-Role': userRole,\n        'Cache-Control': 'no-cache'\n      };\n      \n      // 添加时间戳，避免缓存问题\n      const timestamp = Date.now();\n      \n      // 使用fetch API直接发送请求，避免可能的缓存问题\n      fetch(`/medical/api/tags/${annotation.dbId}?t=${timestamp}&userId=${userId}`, {\n        method: 'PUT',\n        headers: headers,\n        body: JSON.stringify(tagData),\n        credentials: 'include'\n      })\n        .then(response => {\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n          }\n          return response.json();\n        })\n        .then(data => {\n          loading.close();\n          console.log('标注已更新', data);\n          this.$message.success('标注已更新');\n          \n          // 更新本地标注的归一化坐标\n          annotation.normalizedX = normalizedX;\n          annotation.normalizedY = normalizedY;\n          annotation.normalizedWidth = normalizedWidth;\n          annotation.normalizedHeight = normalizedHeight;\n          \n          // 安排延迟保存标注图像，而不是立即保存\n          this.scheduleSaveAnnotatedImage(this.imageId);\n        })\n        .catch(error => {\n          console.error('更新标注失败', error);\n          loading.close();\n          \n          // 尝试备用API路径\n          console.log('尝试备用API路径...');\n          fetch(`/api/tags/${annotation.dbId}?t=${timestamp}&userId=${userId}`, {\n            method: 'PUT',\n            headers: headers,\n            body: JSON.stringify(tagData),\n            credentials: 'include'\n          })\n            .then(response => {\n              if (!response.ok) {\n                throw new Error(`备用路径也失败! status: ${response.status}`);\n              }\n              return response.json();\n            })\n            .then(data => {\n              console.log('通过备用路径更新标注成功:', data);\n              this.$message.success('标注已更新');\n              \n              // 更新本地标注的归一化坐标\n              annotation.normalizedX = normalizedX;\n              annotation.normalizedY = normalizedY;\n              annotation.normalizedWidth = normalizedWidth;\n              annotation.normalizedHeight = normalizedHeight;\n              \n              // 安排延迟保存标注图像，而不是立即保存\n              this.scheduleSaveAnnotatedImage(this.imageId);\n            })\n            .catch(backupError => {\n              console.error('所有尝试都失败:', backupError);\n              this.$message.error('更新标注失败，请稍后重试');\n            });\n        });\n    },\n    \n    // 新增：编辑后保存标注图像\n    async saveAnnotatedImageAfterEdit(imageId, immediate = false) {\n      if (!imageId) {\n        console.error('保存标注图像失败：缺少图像ID');\n        this.$message.error('保存标注图像失败：缺少图像ID');\n        return;\n      }\n      \n      // 如果不是立即保存且有计时器正在运行，则不执行保存\n      if (!immediate && this.saveImageTimer) {\n        console.log('已经有延迟保存计划，不重复保存');\n        return;\n      }\n      \n      // 清除计时器（如果存在）\n      if (this.saveImageTimer) {\n        clearTimeout(this.saveImageTimer);\n        this.saveImageTimer = null;\n      }\n      \n      console.log('编辑标注完成，正在保存标注图像，图像ID:', imageId);\n      \n      // 显示加载状态\n      const loading = this.$loading({\n        lock: true,\n        text: '正在保存标注图像...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      });\n      \n      try {\n        // 调用API保存标注图像\n        const response = await api.tags.saveAnnotatedImage(imageId);\n        console.log('标注图像保存成功:', response.data);\n        \n        // 如果返回了新的处理后图像路径，更新图像\n        if (response.data && response.data.processedPath) {\n          console.log('更新处理后的图像路径:', response.data.processedPath);\n          // 添加时间戳避免缓存问题\n          const timestamp = Date.now();\n          const newImageUrl = `${response.data.processedPath}?t=${timestamp}`;\n          \n          // 更新当前图像的URL\n          if (this.currentImage) {\n            this.processedFilePath = newImageUrl;\n          }\n          \n          this.$message.success('标注图像已保存');\n        } else {\n          this.$message.warning('标注图像已保存，但未返回新图像路径');\n        }\n        \n        loading.close();\n      } catch (error) {\n        console.error('保存标注图像失败:', error);\n        this.$message.error('保存标注图像失败，请稍后重试');\n        loading.close();\n      }\n    },\n    // 新增：处理标注数据的方法\n    processLoadedAnnotations() {\n      // 关键修复：添加\"哨兵\"判断，防止重复执行\n      if (this.processedAnnotations) {\n        console.warn('标注已处理过，跳过重复渲染，防止生成重复的标注框。');\n        return;\n      }\n      \n      // 确保有尺寸和标注数据\n      if (this.imageWidth <= 0 || this.imageHeight <= 0) {\n        console.warn('图像尺寸无效，无法处理标注', {\n          w: this.imageWidth, h: this.imageHeight\n        });\n        return;\n      }\n      \n      if (!this.dbAnnotations || this.dbAnnotations.length === 0) {\n        console.log('没有标注数据需要处理');\n        this.annotations = []; // 确保清空\n        return;\n      }\n      \n      console.log('开始处理已加载的标注数据，图像尺寸:', \n                 `显示: ${this.imageWidth}x${this.imageHeight}`,\n                 `原始: ${this.originalWidth}x${this.originalHeight}`,\n                 '标注数量:', this.dbAnnotations.length);\n      \n      const newAnnotations = [];\n      \n      // 转换数据库标注为本地格式\n      this.dbAnnotations.forEach(dbTag => {\n        const tagName = dbTag.tagName || dbTag.tag || 'IH-婴幼儿血管瘤';\n        \n        // 确保坐标值是数值类型\n        const normCenterX = parseFloat(dbTag.x) || 0;\n        const normCenterY = parseFloat(dbTag.y) || 0;\n        const normWidth = parseFloat(dbTag.width) || 0;\n        const normHeight = parseFloat(dbTag.height) || 0;\n        \n        // 检查坐标是否有效\n        if (normWidth <= 0 || normHeight <= 0) {\n          console.warn(`标注 ${dbTag.id} 的尺寸无效，跳过该标注`);\n          return;\n        }\n\n        // 1. 将归一化尺寸转换为像素尺寸\n        const pixelWidth = normWidth * this.imageWidth;\n        const pixelHeight = normHeight * this.imageHeight;\n\n        // 2. 将归一化中心点转换为像素中心点\n        const pixelCenterX = normCenterX * this.imageWidth;\n        const pixelCenterY = normCenterY * this.imageHeight;\n\n        // 3. 从像素中心点计算左上角坐标\n        const finalX = pixelCenterX - (pixelWidth / 2);\n        const finalY = pixelCenterY - (pixelHeight / 2);\n        \n        console.log(`处理标注 ${dbTag.id}: 中心点(${pixelCenterX.toFixed(2)}, ${pixelCenterY.toFixed(2)}) -> 左上角(${finalX.toFixed(2)}, ${finalY.toFixed(2)})`);\n\n        const annotation = {\n          id: dbTag.id,\n          dbId: dbTag.id,\n          tag: tagName,\n          x: finalX,\n          y: finalY,\n          width: pixelWidth,\n          height: pixelHeight,\n          // 保存原始的归一化YOLO坐标\n          normalizedX: normCenterX,\n          normalizedY: normCenterY,\n          normalizedWidth: normWidth,\n          normalizedHeight: normHeight,\n          imageIndex: this.currentImageIndex,\n          type: 'rectangle',\n          isYoloFormat: true // 添加标志位\n        };\n        \n        newAnnotations.push(annotation);\n      });\n      \n      this.annotations = newAnnotations;\n      this.processedAnnotations = true;\n      this.annotationsLoaded = true;\n    },\n    goBack() {\n      // 提示用户可能丢失未保存的标注\n      console.log('[导航跟踪] 点击返回按钮，当前URL:', window.location.href);\n      \n      this.$confirm('返回上传页面可能会丢失当前未保存的标注，是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 设置返回工作台标记，防止401错误时跳转到登录页\n        sessionStorage.setItem('returningToWorkbench', 'true');\n        console.log('[导航跟踪] 用户确认返回，准备导航到: /app/cases/new');\n        \n        // 检查是否有图像对ID和已标注的图像\n        if (this.imagePairId) {\n          // 显示加载状态\n          const loading = this.$loading({\n            lock: true,\n            text: '正在处理...',\n            spinner: 'el-icon-loading',\n            background: 'rgba(0, 0, 0, 0.7)'\n          });\n\n          // 使用新API直接删除标注图片并清空路径\n          api.imagePairs.deleteAnnotatedImage(this.imagePairId)\n            .then(response => {\n              console.log('标注图片删除结果:', response.data);\n              loading.close();\n              // 用户确认后，返回到上传图片页面\n              console.log('[导航跟踪] 导航到: /app/cases/new (API调用后)');\n              \n              // 记录导航前的信息\n              const beforeUrl = window.location.href;\n              \n              this.$router.push('/app/cases/new').then(() => {\n                console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);\n              }).catch(err => {\n                console.error('[导航跟踪] 导航失败:', err);\n                // 备用方案，使用window.location\n                window.location.href = '/app/cases/new';\n              });\n            })\n            .catch(error => {\n              console.error('删除标注图片失败', error);\n              loading.close();\n              // 即使失败也返回上一页\n              this.$message.error('删除标注图片失败，但仍将返回上一页');\n              \n              console.log('[导航跟踪] 导航到: /app/cases/new (API调用失败后)');\n              const beforeUrl = window.location.href;\n              \n              this.$router.push('/app/cases/new').then(() => {\n                console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);\n              }).catch(err => {\n                console.error('[导航跟踪] 导航失败:', err);\n                // 备用方案，使用window.location\n                window.location.href = '/app/cases/new';\n              });\n            });\n        } else {\n          // 如果没有图像对ID，直接返回上一页\n          console.log('[导航跟踪] 导航到: /app/cases/new (无图像对ID)');\n          const beforeUrl = window.location.href;\n          \n          this.$router.push('/app/cases/new').then(() => {\n            console.log('[导航跟踪] 导航成功，从', beforeUrl, '到', window.location.href);\n          }).catch(err => {\n            console.error('[导航跟踪] 导航失败:', err);\n            // 备用方案，使用window.location\n            window.location.href = '/app/cases/new';\n          });\n        }\n      }).catch(() => {\n        // 用户取消，不做任何操作\n        console.log('[导航跟踪] 用户取消返回操作');\n      });\n    },\n    tryLoadImagePathFromMetadata(imageId, callback) {\n      console.log('从image_metadata表获取图像路径:', imageId);\n      \n      api.images.getOne(imageId)\n        .then(response => {\n          if (response.data && response.data.path) {\n            console.log('成功从image_metadata获取图像路径:', response.data.path);\n            callback(response.data.path);\n          } else {\n            console.error('image_metadata中图像路径不存在');\n            callback(null);\n          }\n        })\n        .catch(error => {\n          console.error('从image_metadata获取图像路径失败:', error);\n          callback(null);\n        });\n    },\n    updateImagePairPath(metadataId, path) {\n      if (!this.imagePairId || !path) {\n        console.error('缺少imagePairId或path，无法更新');\n        return;\n      }\n      \n      console.log('更新image_pairs表中的图像路径:', {\n        imagePairId: this.imagePairId,\n        metadataId: metadataId,\n        path: path\n      });\n      \n      // 构建要更新的数据\n      const updateData = {\n        id: this.imagePairId,\n        metadataId: metadataId.toString(),\n        imageOnePath: path\n      };\n      \n      // 使用API更新image_pairs表\n      fetch(`/api/image-pairs`, {\n        method: 'POST',  // 使用POST方法\n        headers: {\n          'Content-Type': 'application/json',\n          'Accept': 'application/json'\n        },\n        body: JSON.stringify(updateData),\n        credentials: 'include'\n      })\n        .then(response => {\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n          }\n          return response.json();\n        })\n        .then(result => {\n          console.log('成功更新image_pairs中的图像路径:', result);\n        })\n        .catch(error => {\n          console.error('更新image_pairs中的图像路径失败:', error);\n        });\n    },\n    // 加载图像标注\n    loadAnnotations(imageId) {\n      if (!imageId) {\n        console.error('loadAnnotations: 图像ID无效，无法加载标注');\n        return;\n      }\n      \n      // 重置标注加载状态\n      this.annotationsLoaded = false;\n      \n      console.log('开始加载图像标注，ID:', imageId);\n      \n      // 获取当前用户\n      const user = JSON.parse(localStorage.getItem('user')) || {};\n      const userId = user.customId || user.id;\n      \n      if (!userId) {\n        console.warn('未找到用户ID，标注加载可能存在权限问题');\n      }\n      \n      // 显示加载中提示\n      this.$message.info('正在加载标注数据...');\n      \n      // 添加时间戳避免缓存问题\n      const timestamp = new Date().getTime();\n      console.log(`请求时间戳: ${timestamp}, 使用edit模式加载标注`);\n      \n      // 使用API工具类获取标注，添加edit模式参数\n      api.tags.getByImageId(imageId, 'edit')\n        .then(response => {\n          console.log('成功获取图像标注数据:', response.data);\n          \n          // 确保返回的数据是数组\n          this.dbAnnotations = response.data || [];\n          \n          // 如果返回的不是数组，记录错误并转换为空数组\n          if (!Array.isArray(this.dbAnnotations)) {\n            console.error('从服务器获取的标注数据不是数组格式:', this.dbAnnotations);\n            this.dbAnnotations = [];\n          }\n          \n          // 显示获取到的标注数量\n          console.log(`成功获取到 ${this.dbAnnotations.length} 个标注`);\n          if (this.dbAnnotations.length > 0) {\n            this.$message.success(`已加载 ${this.dbAnnotations.length} 个标注`);\n          } else {\n            this.$message.info('没有找到标注数据，您可以添加新标注');\n          }\n          \n          // 如果图像已加载并且有尺寸，立即处理标注\n          if (this.imageWidth > 0 && this.imageHeight > 0) {\n            this.processLoadedAnnotations();\n          } else {\n            console.log('图像尺寸尚未加载，将在图像加载后处理标注');\n            // 标注数据已存储在this.dbAnnotations中，将在图像加载后处理\n          }\n        })\n        .catch(error => {\n          console.error('加载标注数据失败:', error);\n          this.dbAnnotations = [];\n          this.annotationsLoaded = true; // 即使加载失败也标记为已处理，防止无限循环\n          this.$message.error('加载标注失败，但您仍可以添加新标注');\n        });\n    },\n    \n    // 刷新认证信息\n    refreshAuthentication() {\n      // 尝试刷新用户认证信息\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      if (user.id || user.customId) {\n        console.log('尝试刷新用户认证信息');\n        // 这里可以添加刷新token的逻辑，如果系统支持\n      }\n    },\n    // 添加一个新方法用于尝试从localStorage加载离线数据\n    tryLoadOfflineData(imageId) {\n      console.log('尝试从localStorage加载离线数据...');\n      \n      // 尝试从localStorage获取图像数据\n      const offlineImageData = localStorage.getItem(`offline_image_${imageId}`);\n      if (offlineImageData) {\n        console.log('找到离线图像数据');\n        \n        // 创建临时图像对象\n        this.imageData = {\n          id: imageId,\n          path: offlineImageData\n        };\n        \n        // 创建临时图像对\n        this.imagePair = {\n          id: Date.now(),\n          metadataId: imageId,\n          imageOnePath: offlineImageData\n        };\n        \n        // 加载图像\n        this.loadImage();\n      } else {\n        console.warn('未找到离线图像数据');\n      }\n    },\n    // 提交表单\n    submitForm() {\n      // 先验证表单\n      this.$refs.structuredForm.validate(valid => {\n        if (valid) {\n          console.log('表单验证通过，准备提交数据');\n          \n          // 先获取最新的标注数据\n          this.loadAnnotationsFromDatabase().then(() => {\n            // 显示加载提示\n            const loading = this.$loading({\n              lock: true,\n              text: '正在保存表单数据...',\n              spinner: 'el-icon-loading',\n              background: 'rgba(255, 255, 255, 0.7)'\n            });\n            \n            // 准备表单数据\n            const formData = { ...this.formData };\n            \n            // 添加标注数据到表单\n            formData.annotations = this.dbAnnotations;\n            \n            console.log('提交表单数据:', formData);\n            \n            // 使用API保存表单数据\n            api.images.saveStructuredFormData(this.imageId, formData)\n              .then(response => {\n                console.log('表单数据保存成功:', response.data);\n                loading.close();\n                this.$message.success('表单数据保存成功');\n                \n                // 跳转到病例列表页面\n                this.$router.push('/app/cases');\n              })\n              .catch(error => {\n                console.error('表单数据保存失败:', error);\n                loading.close();\n                this.$message.error('表单数据保存失败: ' + (error.message || '未知错误'));\n              });\n          }).catch(error => {\n            console.error('获取标注数据失败:', error);\n            this.$message.warning('无法获取最新标注数据，将只保存表单信息');\n            \n            // 即使获取标注失败，也继续保存表单\n            this.submitFormOnly();\n          });\n        } else {\n          console.error('表单验证失败');\n          this.$message.error('表单验证失败，请检查输入');\n          return false;\n        }\n      });\n    },\n    \n    // 仅提交表单数据\n    submitFormOnly() {\n      // 显示加载提示\n      const loading = this.$loading({\n        lock: true,\n        text: '正在保存表单数据...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(255, 255, 255, 0.7)'\n      });\n      \n      // 准备表单数据\n      const formData = { ...this.formData };\n      \n      console.log('提交表单数据:', formData);\n      \n      // 使用API保存表单数据\n      api.images.saveStructuredFormData(this.imageId, formData)\n        .then(response => {\n          console.log('表单数据保存成功:', response.data);\n          loading.close();\n          this.$message.success('表单数据保存成功');\n          \n          // 跳转到病例列表页面\n          this.$router.push('/app/cases');\n        })\n        .catch(error => {\n          console.error('表单数据保存失败:', error);\n          loading.close();\n          this.$message.error('表单数据保存失败: ' + (error.message || '未知错误'));\n        });\n    },\n    // 加载表单数据\n    loadFormData() {\n      console.log('加载表单数据，图像ID:', this.imageId);\n      \n      // 使用API获取图像元数据\n      api.images.getOne(this.imageId)\n        .then(response => {\n          console.log('获取图像元数据成功:', response.data);\n          const imageData = response.data;\n          \n          // 如果有结构化表单数据，填充到表单中\n          if (imageData.structuredForm) {\n            this.formData = {\n              ...this.formData,\n              ...imageData.structuredForm\n            };\n            console.log('已加载结构化表单数据:', this.formData);\n          } else {\n            console.log('图像没有结构化表单数据');\n          }\n        })\n        .catch(error => {\n          console.error('加载表单数据失败:', error);\n          this.$message.warning('加载表单数据失败，将使用空表单');\n        });\n    },\n    // 异步加载页面数据，解决时序问题\n    async loadPageData(diagnosisId) {\n      if (!diagnosisId) {\n        return Promise.reject('未提供诊断ID');\n      }\n\n      console.log(`开始为ID: ${diagnosisId} 加载页面核心数据`);\n      try {\n        // 首先尝试从血管瘤诊断API获取数据\n      try {\n        const response = await axios.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);\n        const data = response.data;\n          console.log('从血管瘤诊断API获取到的核心数据:', data);\n\n        if (data && data.imagePath) {\n          const imageUrl = data.imagePath;\n          \n          this.uploadedImages = [{\n            id: diagnosisId,\n            url: imageUrl,\n            filename: `诊断图像 #${diagnosisId}`\n            }];\n            this.currentImage = this.uploadedImages[0];\n            this.processedFilePath = imageUrl;\n            \n            console.log('图片信息设置成功，URL:', imageUrl);\n            return Promise.resolve();\n          }\n        } catch (diagnosisError) {\n          console.log('从血管瘤诊断API获取数据失败，尝试普通图像API:', diagnosisError.message);\n        }\n        \n        // 如果血管瘤诊断API失败，尝试从普通图像API获取数据\n        console.log('尝试从普通图像API获取数据，ID:', diagnosisId);\n        const imageResponse = await axios.get(`/api/images/${diagnosisId}`, {\n          headers: {\n            'Cache-Control': 'no-cache, no-store',\n            'Pragma': 'no-cache',\n            'Expires': '0'\n          }\n        });\n        \n        const imageData = imageResponse.data;\n        console.log('从普通图像API获取到的数据:', imageData);\n        \n        if (imageData && (imageData.path || imageData.url)) {\n          const imageUrl = imageData.path || imageData.url;\n          \n          this.uploadedImages = [{\n            id: diagnosisId,\n            url: imageUrl,\n            filename: imageData.original_name || `图像 #${diagnosisId}`\n          }];\n          this.currentImage = this.uploadedImages[0];\n          this.processedFilePath = imageUrl;\n          \n          console.log('图片信息设置成功，URL:', imageUrl);\n          return Promise.resolve();\n        } else {\n          throw new Error('响应数据中缺少图像路径');\n        }\n      } catch (error) {\n        console.error(`加载页面数据失败 (ID: ${diagnosisId}):`, error);\n        this.$message.error(`加载图像信息失败: ${error.message}`);\n        return Promise.reject(error);\n      }\n    },\n    // 重新加载标注数据\n    reloadAnnotations() {\n      console.log('手动刷新标注数据');\n      \n      // 清除当前标注\n      this.annotations = [];\n      this.dbAnnotations = [];\n      this.annotationsLoaded = false;\n      \n      // 显示加载中提示\n      this.$message.info('正在重新加载标注数据...');\n      \n      // 重新加载标注\n      this.loadAnnotationsFromDatabase()\n        .then(() => {\n          // 如果图像已加载，处理标注\n          if (this.imageWidth > 0 && this.imageHeight > 0) {\n            this.processLoadedAnnotations();\n          }\n          \n          // 显示成功消息\n          this.$message.success(`已加载 ${this.dbAnnotations.length} 个标注`);\n        })\n        .catch(error => {\n          console.error('重新加载标注失败:', error);\n          this.$message.error('重新加载标注失败，请刷新页面重试');\n        });\n    },\n    // 添加一个直接的按钮点击处理方法，便于调试\n    handleNextButtonClick(event) {\n      console.log('下一步按钮被点击', event);\n      // 防止事件冒泡\n      if (event) {\n        event.stopPropagation();\n        event.preventDefault();\n      }\n      \n      // 添加额外的调试信息\n      console.log('当前组件状态:', {\n        imageId: this.imageId,\n        hasAnnotations: this.annotations.length > 0,\n        isSaving: this.isSaving,\n        currentImageIndex: this.currentImageIndex,\n        hasUploadedImages: !!(this.uploadedImages && this.uploadedImages.length > 0)\n      });\n      \n      // 设置超时，如果正常流程在5秒内没有完成，则使用备用导航\n      const navigationTimeout = setTimeout(() => {\n        console.log('[handleNextButtonClick] 导航超时，使用备用导航');\n        this.directNavigateToForm();\n      }, 5000);\n      \n      // 调用原有的保存方法\n      this.saveAndNext().finally(() => {\n        // 清除超时\n        clearTimeout(navigationTimeout);\n      });\n    },\n    // 添加直接导航方法，确保即使其他方法失败也能导航到下一页\n    directNavigateToForm() {\n      try {\n        // 获取诊断ID\n        const diagnosisId = this.diagnosisId || this.$route.query.diagnosisId || this.$route.params.id || this.imageId;\n        \n        if (!diagnosisId) {\n          console.error('[directNavigateToForm] 无法获取诊断ID');\n          this.$message.error('无法获取诊断ID，无法导航');\n          return false;\n        }\n        \n        // 构建导航URL\n        const route = `${CONFIG.ROUTES.structuredForm}?diagnosisId=${diagnosisId}`;\n        console.log('[directNavigateToForm] 直接导航到:', route);\n        \n        // 使用window.location直接导航\n        window.location.href = route;\n        return true;\n      } catch (error) {\n        console.error('[directNavigateToForm] 导航失败:', error);\n        return false;\n      }\n    },\n    // 新增：安排延迟保存标注图像\n    scheduleSaveAnnotatedImage(imageId) {\n      console.log('安排延迟保存标注图像，10秒后执行...');\n      \n      // 清除之前的计时器（如果存在）\n      if (this.saveImageTimer) {\n        console.log('清除之前的保存计时器');\n        clearTimeout(this.saveImageTimer);\n      }\n      \n      // 设置新的计时器，10秒后执行\n      this.saveImageTimer = setTimeout(() => {\n        console.log('10秒延迟后执行保存标注图像');\n        this.saveAnnotatedImageAfterEdit(imageId);\n        this.saveImageTimer = null;\n      }, 10000); // 10秒延迟\n    },\n    // 添加saveAndExit方法，立即保存标注图像并退出\n    async saveAndExit() {\n      if (this.annotations.length === 0) {\n        // 如果没有标注，直接退出\n        this.$router.push('/app/cases');\n        return;\n      }\n      \n      // 显示加载状态\n      const loading = this.$loading({\n        lock: true,\n        text: '保存中...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      });\n      \n      const currentImage = this.uploadedImages[this.currentImageIndex];\n      if (!currentImage || !currentImage.id) {\n        this.$message.error('图像信息不完整，无法保存标注');\n        loading.close();\n        return;\n      }\n      \n      try {\n        console.log('saveAndExit: 使用真实图像ID保存标注:', currentImage.id);\n        \n        // 立即保存标注图像，不使用延迟\n        await this.saveAnnotatedImageAfterEdit(currentImage.id, true);\n        \n        localStorage.setItem('annotations', JSON.stringify(this.annotations));\n        this.$message.success('标注已保存');\n        \n        loading.close();\n        \n        // 退出到病例列表\n        localStorage.setItem('isSaveAndExit', 'true');\n        this.$router.push('/app/cases');\n      } catch (error) {\n        console.error('保存标注失败:', error);\n        this.$message.error('保存标注失败: ' + (error.message || '未知错误'));\n        loading.close();\n      }\n    },\n  }\n}\n</script>\n\n<style scoped>\n/* Add all styles from CaseDetailForm.vue here */\n.annotation-container {\n  display: flex;\n  flex-direction: column;\n  height: calc(100vh - 120px);\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  border-bottom: 1px solid #ddd;\n}\n\n.main-content {\n  display: flex;\n  flex: 1;\n}\n\n.toolbar {\n  width: 300px;\n  padding: 20px;\n  border-right: 1px solid #ddd;\n  overflow-y: auto;\n}\n\n.tool-section {\n  margin-bottom: 20px;\n}\n\n.annotations-list {\n  margin-top: 20px;\n}\n\n.annotation-area {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n}\n\n.image-wrapper {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n  position: relative;\n}\n\n.image-container {\n  position: relative;\n}\n\n.annotation-image {\n  max-width: 500px;\n  max-height: 500px;\n  display: block;\n}\n\n.annotation-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  cursor: crosshair;\n}\n\n.annotation-box {\n  position: absolute;\n  border: 2px solid;\n  box-sizing: border-box;\n}\n\n.annotation-label {\n  position: absolute;\n  top: -20px;\n  left: 0;\n  color: white;\n  padding: 2px 5px;\n  font-size: 12px;\n  white-space: nowrap;\n}\n\n.resize-handle {\n  position: absolute;\n  width: 10px;\n  height: 10px;\n  background: white;\n  border: 1px solid black;\n}\n\n.top-left { top: -5px; left: -5px; cursor: nwse-resize; }\n.top-right { top: -5px; right: -5px; cursor: nesw-resize; }\n.bottom-left { bottom: -5px; left: -5px; cursor: nesw-resize; }\n.bottom-right { bottom: -5px; right: -5px; cursor: nwse-resize; }\n\n.drawing-box {\n  position: absolute;\n  border: 2px dashed #007bff;\n  box-sizing: border-box;\n}\n\n.navigation-controls {\n  display: flex;\n  justify-content: space-between;\n  padding: 10px 20px;\n  border-top: 1px solid #ddd;\n}\n\n.form-section {\n  padding: 20px;\n}\n</style> ", "import { render } from \"./AnnotationAndForm.vue?vue&type=template&id=46ae9090&scoped=true\"\nimport script from \"./AnnotationAndForm.vue?vue&type=script&lang=js\"\nexport * from \"./AnnotationAndForm.vue?vue&type=script&lang=js\"\n\nimport \"./AnnotationAndForm.vue?vue&type=style&index=0&id=46ae9090&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-46ae9090\"]])\n\nexport default __exports__"], "names": ["addTimestamp", "url", "timestamp", "concat", "Date", "now", "includes", "getImageUrl", "console", "log", "test", "encodedPath", "encodeURIComponent", "baseUrl", "isLocalhost", "API_BASE_URL", "startsWith", "finalUrl", "API_CONTEXT_PATH", "class", "key", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_button", "type", "size", "onClick", "$options", "refreshData", "_withCtx", "_component_el_icon", "_component_Refresh", "_", "_createTextVNode", "__", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_component_el_select", "modelValue", "$data", "selectedTag", "_cache", "$event", "placeholder", "style", "_component_el_option", "label", "value", "_hoisted_6", "_component_el_radio_group", "currentTool", "_component_el_radio", "_hoisted_7", "reloadAnnotations", "title", "filteredAnnotations", "length", "_hoisted_8", "_createBlock", "_component_el_table", "data", "_component_el_table_column", "width", "default", "scope", "_toDisplayString", "$index", "prop", "_hoisted_9", "Math", "round", "row", "x", "y", "_hoisted_10", "height", "deleteAnnotation", "id", "_hoisted_11", "_hoisted_12", "_hoisted_13", "currentImage", "src", "alt", "onLoad", "handleImageLoad", "apply", "arguments", "_hoisted_14", "_createCommentVNode", "_normalizeStyle", "imageWidth", "imageHeight", "onMousedown", "startDrawing", "onMousemove", "onDrawing", "onMouseup", "endDrawing", "onMouseleave", "cancelDrawing", "_Fragment", "_renderList", "box", "index", "getTagColor", "tag", "_ctx", "isEditingBox", "editingBoxId", "_withModifiers", "startEditBox", "backgroundColor", "startResizeBox", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_15", "isDrawing", "min", "drawStart", "drawCurrent", "abs", "_hoisted_20", "_component_router_view", "CONFIG", "STORAGE_KEYS", "userInfo", "token", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isNavigatingAfterSave", "pendingDiagnosis", "formDataPrefix", "ROUTES", "structuredForm", "UI", "navigationDelay", "name", "components", "Refresh", "imageId", "this", "$route", "params", "query", "formData", "disease<PERSON>art", "diseaseType", "patientAge", "patientGender", "diagnosis", "treatmentPlan", "notes", "dbAnnotations", "uploadedImages", "currentImageIndex", "annotations", "drawingBox", "selectedAnnotation", "isMoving", "isResizing", "resizeHandle", "moveStartX", "moveStartY", "showAnnotationForm", "currentAnnotation", "zoomLevel", "panOffset", "isPanning", "lastPanPoint", "annotationsLoaded", "shouldSaveOriginalImage", "imageLoaded", "isSavingAnnotations", "originalWidth", "originalHeight", "scaleX", "scaleY", "isSaving", "processedAnnotations", "saveImageTimer", "computed", "_this", "filter", "a", "imageIndex", "watch", "handler", "newAnnotations", "processLoadedAnnotations", "deep", "immediate", "newVal", "created", "window", "addEventListener", "handleResize", "mounted", "_this2", "diagnosisId", "_typeof", "String", "loading", "$loading", "lock", "text", "spinner", "background", "sessionStorage", "setItem", "retryCount", "maxRetries", "loadDataWithRetry", "loadPageData", "then", "loadAnnotations", "close", "error", "setTimeout", "$message", "document", "handleGlobalMouseMove", "handleGlobalMouseUp", "warning", "beforeUnmount", "removeEventListener", "removeItem", "methods", "_methods", "processLongId", "idStr", "toString", "substring", "originalUrl", "location", "origin", "cacheBuster", "random", "loadImageData", "_this3", "_asyncToGenerator", "_regenerator", "m", "_callee", "response", "_t", "w", "_context", "n", "p", "api", "get", "headers", "v", "_this4", "_callee4", "retries", "_tryLoadAnnotations", "_context4", "Promise", "reject", "tryLoadAnnotations", "_ref", "_callee3", "user", "userId", "isDiagnosisId", "apiUrl", "waitTime", "_timestamp", "_random", "_user", "_userId", "_isDiagnosisId", "backupUrl", "_response", "_data", "_t3", "_t4", "_context3", "getTime", "JSON", "parse", "localStorage", "getItem", "customId", "fetch", "ok", "Error", "status", "json", "resolve", "_callee2", "result", "_t2", "_context2", "convertHemangiomaTags", "tags", "_this5", "convertedAnnotations", "map", "isRelativeCoordinates", "serverId", "tagName", "confidence", "warn", "<PERSON><PERSON><PERSON><PERSON>", "convertTags", "_this6", "_this7", "_callee5", "_context5", "message", "duration", "loadImageFromPairs", "_this8", "t", "imagePairs", "getByMetadataId", "imagePair", "imagePairId", "processedImagePath", "imageOnePath", "image_one_path", "image_one_url", "processedFilePath", "tryLoadImagePathFromMetadata", "path", "updateImagePairPath", "filename", "currentImageId", "createImageRecord", "tryLoadFromMetadata", "_this9", "images", "getOne", "imageData", "original_name", "createImagePairDirectly", "loadAnnotationsFromDatabase", "loadingError", "loadingErrorMessage", "metadataId", "imagePath", "_this0", "description", "method", "body", "stringify", "credentials", "success", "loadImageById", "_this1", "lastIndexOf", "metadata", "mimetype", "uploaded_by", "update", "saveOriginalImage", "loadImageByPath", "checkExistingImagePair", "_this10", "_this11", "ensureImageMetadataExists", "directRequest", "_ref3", "_callee6", "checkResponse", "pairsData", "existingPairId", "exists", "saveResponse", "_t5", "_context6", "err", "callback", "_this12", "_this13", "$nextTick", "image", "$refs", "annotationImage", "rect", "getBoundingClientRect", "newWidth", "newHeight", "recalculateAnnotationPositions", "initTools", "event", "img", "target", "naturalWidth", "naturalHeight", "$emit", "_this14", "isYoloFormat", "centerX", "normalizedX", "centerY", "normalizedY", "normalizedWidth", "normalizedHeight", "pixelX", "pixelY", "pixelWidth", "pixelHeight", "toFixed", "_objectSpread", "undefined", "newX", "newY", "selectTool", "tool", "imageContainerInner", "clientX", "left", "clientY", "top", "annotation", "push", "saveAnnotationToDatabase", "boxId", "preventDefault", "boxToEdit", "find", "originalBox", "editStartPos", "handle", "boxToResize", "isResizingBox", "_this15", "boundedX", "max", "boundedY", "currentX", "currentY", "deltaX", "deltaY", "boxIndex", "findIndex", "original", "_this16", "editedAnnotation", "has<PERSON><PERSON>ed", "原位置", "原尺寸", "新位置", "新尺寸", "updateAnnotationInDatabase", "_defineProperty", "_this17", "_callee7", "tagData", "_t6", "_context7", "左上角像素", "像素宽高", "归一化中心点", "归一化宽高", "像素坐标", "归一化坐标", "metadata_id", "parseInt", "Number", "coord_system", "create", "dbId", "_this18", "clear", "debugInfo", "图像ID", "图像ID类型", "标签类型", "坐标", "尺寸", "用户ID", "用户ID类型", "用户角色", "role", "created_by", "metadata_id类型", "created_by类型", "loadingInstance", "userRole", "Authorization", "backup<PERSON><PERSON>r", "colorMap", "_this19", "beforeCount", "item", "splice", "originalAnnotation", "request", "checkForDuplicateAnnotations", "idMap", "Map", "duplicates", "for<PERSON>ach", "has", "set", "i", "_this20", "_callee9", "_context9", "_ref4", "_callee8", "currentRetry", "saveSuccess", "errorMessage", "_error$response$data", "_error$response$data2", "_currentImage", "route", "_t8", "_t0", "_t10", "_t11", "_t12", "_context8", "$confirm", "confirmButtonText", "cancelButtonText", "href", "hasUploadedImages", "saveAnnotatedImageAfterEdit", "this_diagnosisId", "route_query", "route_params", "$router", "replace", "f", "_x", "_x2", "_this21", "_callee0", "_context0", "_arguments", "_this22", "_callee1", "loadingText", "chinaTime", "isoTime", "_t14", "_t15", "_t16", "_t17", "_t18", "_context1", "saveAnnotatedImage", "updateImageAfterAnnotation", "Intl", "DateTimeFormat", "year", "month", "day", "hour", "minute", "second", "hour12", "timeZone", "format", "toISOString", "markAsAnnotated", "updateStatus", "_this23", "localAnnotations", "parsedAnnotations", "e", "_this24", "updated_by", "归一化尺寸", "坐标系统", "scheduleSaveAnnotatedImage", "_arguments2", "_this25", "_callee10", "newImageUrl", "_t20", "_context10", "clearTimeout", "processedPath", "_this26", "h", "dbTag", "normCenterX", "parseFloat", "normCenterY", "<PERSON><PERSON><PERSON><PERSON>", "norm<PERSON><PERSON>ght", "pixelCenterX", "pixelCenterY", "finalX", "finalY", "_this27", "deleteAnnotatedImage", "beforeUrl", "updateData", "_this28", "info", "getByImageId", "Array", "isArray", "offlineImageData", "loadImage", "_this29", "validate", "valid", "saveStructuredFormData", "submitFormOnly", "_this30", "_this31", "_this32", "_callee11", "imageUrl", "imageResponse", "_imageUrl", "_t21", "_t22", "_context11", "axios", "_this33", "_this34", "stopPropagation", "hasAnnotations", "navigationTimeout", "directNavigateToForm", "saveAndNext", "_this35", "_this36", "_callee12", "_t23", "_context12", "__exports__", "render"], "sourceRoot": ""}