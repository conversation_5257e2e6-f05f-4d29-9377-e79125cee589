"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[80],{7080:(e,a,s)=>{s.r(a),s.d(a,{default:()=>B});var i=s(61431),r={class:"profile-container"},l={class:"profile-header"},t=["src"],n={class:"user-id"},c={class:"profile-content"},o={class:"info-section"},u={class:"info-item"},d={class:"item-content"},v={class:"value"},k={class:"info-item"},p={class:"item-content"},f={class:"value"},h={class:"info-item"},L={class:"item-content"},m={class:"value"},g={class:"info-item"},b={class:"item-content"},I={class:"value"},w={class:"info-item"},A={class:"item-content"},_={class:"value"},U={class:"action-section"};function C(e,a,C,R,y,F){var D=(0,i.g2)("el-button");return(0,i.uX)(),(0,i.CE)("div",r,[(0,i.Lk)("div",l,[(0,i.Lk)("div",{class:"avatar-container",onClick:a[0]||(a[0]=function(){return F.triggerFileInput&&F.triggerFileInput.apply(F,arguments)})},[(0,i.Lk)("img",{src:y.avatarUrl||s(34648),alt:"用户头像",class:"avatar"},null,8,t),a[2]||(a[2]=(0,i.Lk)("div",{class:"avatar-overlay"},[(0,i.Lk)("i",{class:"el-icon-camera"}),(0,i.Lk)("span",null,"更换头像")],-1))]),(0,i.Lk)("div",n,"ID "+(0,i.v_)(y.userId),1),(0,i.Lk)("input",{type:"file",ref:"fileInput",style:{display:"none"},accept:"image/*",onChange:a[1]||(a[1]=function(){return F.handleFileChange&&F.handleFileChange.apply(F,arguments)})},null,544)]),(0,i.Lk)("div",c,[(0,i.Lk)("div",o,[(0,i.Lk)("div",u,[a[5]||(a[5]=(0,i.Lk)("div",{class:"icon"},[(0,i.Lk)("i",{class:"el-icon-user"})],-1)),(0,i.Lk)("div",d,[a[3]||(a[3]=(0,i.Lk)("div",{class:"label"},"个人资料",-1)),(0,i.Lk)("div",v,(0,i.v_)(y.userName),1),a[4]||(a[4]=(0,i.Lk)("i",{class:"el-icon-arrow-right"},null,-1))])]),(0,i.Lk)("div",k,[a[8]||(a[8]=(0,i.Lk)("div",{class:"icon"},[(0,i.Lk)("i",{class:"el-icon-message"})],-1)),(0,i.Lk)("div",p,[a[6]||(a[6]=(0,i.Lk)("div",{class:"label"},"邮箱",-1)),(0,i.Lk)("div",f,(0,i.v_)(y.userEmail),1),a[7]||(a[7]=(0,i.Lk)("i",{class:"el-icon-arrow-right"},null,-1))])]),(0,i.Lk)("div",h,[a[11]||(a[11]=(0,i.Lk)("div",{class:"icon"},[(0,i.Lk)("i",{class:"el-icon-office-building"})],-1)),(0,i.Lk)("div",L,[a[9]||(a[9]=(0,i.Lk)("div",{class:"label"},"医院",-1)),(0,i.Lk)("div",m,(0,i.v_)(y.userHospital),1),a[10]||(a[10]=(0,i.Lk)("i",{class:"el-icon-arrow-right"},null,-1))])]),(0,i.Lk)("div",g,[a[14]||(a[14]=(0,i.Lk)("div",{class:"icon"},[(0,i.Lk)("i",{class:"el-icon-first-aid-kit"})],-1)),(0,i.Lk)("div",b,[a[12]||(a[12]=(0,i.Lk)("div",{class:"label"},"科室",-1)),(0,i.Lk)("div",I,(0,i.v_)(y.userDepartment),1),a[13]||(a[13]=(0,i.Lk)("i",{class:"el-icon-arrow-right"},null,-1))])]),(0,i.Lk)("div",w,[a[17]||(a[17]=(0,i.Lk)("div",{class:"icon"},[(0,i.Lk)("i",{class:"el-icon-s-custom"})],-1)),(0,i.Lk)("div",A,[a[15]||(a[15]=(0,i.Lk)("div",{class:"label"},"角色",-1)),(0,i.Lk)("div",_,(0,i.v_)(y.userRole),1),a[16]||(a[16]=(0,i.Lk)("i",{class:"el-icon-arrow-right"},null,-1))])])]),(0,i.Lk)("div",U,[(0,i.bF)(D,{type:"primary",onClick:F.goBack},{default:(0,i.k6)((function(){return a[18]||(a[18]=[(0,i.eW)("返回工作台")])})),_:1,__:[18]},8,["onClick"])])])])}var R=s(24059),y=s(698),F=(s(44114),s(62010),s(79432),s(11392),s(81052)),D=s(36149);const E={name:"UserProfile",data:function(){return{userId:"",userName:"",userEmail:"",userHospital:"",userDepartment:"",userRole:"",avatarUrl:null,isUploading:!1}},created:function(){try{var e=JSON.parse(localStorage.getItem("user")||"{}");switch(this.userId=e.id||"未设置",this.userName=e.name||"未设置",this.userEmail=e.email||"未设置",this.userHospital=e.hospital||"未设置",this.userDepartment=e.department||"未设置",e.role){case"ADMIN":this.userRole="管理员";break;case"DOCTOR":this.userRole="标注医生";break;case"REVIEWER":this.userRole="审核医生";break;default:this.userRole="未知角色"}this.fetchUserAvatar()}catch(a){console.error("获取用户信息失败:",a)}},methods:{goBack:function(){this.$router.push("/app/dashboard")},triggerFileInput:function(){this.$refs.fileInput.click()},handleFileChange:function(e){var a=e.target.files[0];a&&(a.type.startsWith("image/")?a.size>2097152?this.$message.error("图片大小不能超过2MB"):this.uploadAvatar(a):this.$message.error("请选择图片文件"))},uploadAvatar:function(e){var a=this;return(0,y.A)((0,R.A)().m((function s(){var i,r,l,t;return(0,R.A)().w((function(s){while(1)switch(s.n){case 0:return s.p=0,a.isUploading=!0,i=new FormData,i.append("file",e),s.n=1,D["default"].users.uploadAvatar(a.userId,i);case 1:r=s.v,a.avatarUrl=F.JR+r.data.avatarUrl,a.$message.success("头像上传成功"),s.n=3;break;case 2:s.p=2,t=s.v,console.error("上传头像失败:",t),a.$message.error("上传头像失败: "+((null===(l=t.response)||void 0===l||null===(l=l.data)||void 0===l?void 0:l.error)||t.message||"未知错误"));case 3:return s.p=3,a.isUploading=!1,s.f(3);case 4:return s.a(2)}}),s,null,[[0,2,3,4]])})))()},fetchUserAvatar:function(){var e=this;return(0,y.A)((0,R.A)().m((function a(){var s,i;return(0,R.A)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,D["default"].users.getUserAvatar(e.userId);case 1:s=a.v,s.data.avatarUrl&&(e.avatarUrl=F.JR+s.data.avatarUrl),a.n=3;break;case 2:a.p=2,i=a.v,console.error("获取头像失败:",i);case 3:return a.a(2)}}),a,null,[[0,2]])})))()}}};var $=s(66262);const N=(0,$.A)(E,[["render",C],["__scopeId","data-v-20073e8e"]]),B=N},34648:e=>{e.exports="data:image/png;base64,IA=="}}]);
//# sourceMappingURL=80.7320b977.js.map