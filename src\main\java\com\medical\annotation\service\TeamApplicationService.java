package com.medical.annotation.service;

import com.medical.annotation.model.Team;
import com.medical.annotation.model.TeamApplication;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.TeamApplicationRepository;
import com.medical.annotation.repository.TeamRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.exception.UserNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class TeamApplicationService {

    @Autowired
    private TeamApplicationRepository teamApplicationRepository;
    
    @Autowired
    private TeamRepository teamRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TeamService teamService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 创建团队申请
     */
    @Transactional
    public TeamApplication createApplication(TeamApplication application) throws Exception {
        // 验证用户是否存在
        userRepository.findById(application.getUserId())
                .orElseThrow(() -> UserNotFoundException.forId(application.getUserId()));
        
        Optional<Team> teamOpt = teamRepository.findById(application.getTeamId());
        if (!teamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        
        // 检查是否已经有未处理的申请
        Optional<TeamApplication> existingApplication = teamApplicationRepository.findByUserIdAndTeamIdAndStatus(
            application.getUserId(), 
            application.getTeamId(), 
            TeamApplication.Status.PENDING
        );
        
        if (existingApplication.isPresent()) {
            throw new Exception("已有待处理的加入申请");
        }
        
        // 设置申请状态和日期
        application.setStatus(TeamApplication.Status.PENDING);
        application.setApplicationDate(LocalDateTime.now());
        
        return teamApplicationRepository.save(application);
    }
    
    /**
     * 获取用户的团队申请
     */
    public List<TeamApplication> getUserApplications(Integer userId) {
        return teamApplicationRepository.findByUserId(userId);
    }
    
    /**
     * 获取团队的申请
     */
    public List<TeamApplication> getTeamApplications(Integer teamId) {
        return teamApplicationRepository.findByTeamId(teamId);
    }
    
    /**
     * 获取待处理的申请
     */
    public List<TeamApplication> getPendingApplications() {
        return teamApplicationRepository.findByStatus(TeamApplication.Status.PENDING);
    }
    
    /**
     * 根据状态获取申请
     */
    public List<TeamApplication> getApplicationsByStatus(TeamApplication.Status status) {
        return teamApplicationRepository.findByStatus(status);
    }
    
    /**
     * 获取所有已处理的申请（已批准和已拒绝）
     */
    public List<TeamApplication> getAllProcessedApplications() {
        List<TeamApplication> approved = teamApplicationRepository.findByStatus(TeamApplication.Status.APPROVED);
        List<TeamApplication> rejected = teamApplicationRepository.findByStatus(TeamApplication.Status.REJECTED);
        
        // 合并两个列表
        approved.addAll(rejected);
        return approved;
    }
    
    /**
     * 处理团队申请
     */
    @Transactional
    public TeamApplication processApplication(Integer applicationId, String statusStr, Integer processedById) throws Exception {
        Optional<TeamApplication> applicationOpt = teamApplicationRepository.findById(applicationId);
        if (!applicationOpt.isPresent()) {
            throw new Exception("申请不存在");
        }
        
        TeamApplication application = applicationOpt.get();
        
        // 检查是否已经处理过
        if (application.getStatus() != TeamApplication.Status.PENDING) {
            throw new Exception("此申请已被处理");
        }
        
        // 验证处理人是否存在
        Optional<User> processorOpt = userRepository.findById(processedById);
        if (!processorOpt.isPresent()) {
            throw new Exception("处理人不存在");
        }
        
        // 设置处理状态
        TeamApplication.Status status;
        try {
            status = TeamApplication.Status.valueOf(statusStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new Exception("无效的状态值: " + statusStr);
        }
        
        // 更新申请记录
        application.setStatus(status);
        application.setProcessedBy(processedById);
        application.setProcessedDate(LocalDateTime.now());
        
        // 如果批准，将用户添加到团队
        if (status == TeamApplication.Status.APPROVED) {
            try {
                teamService.addUserToTeam(
                    application.getTeamId(), 
                    application.getUserId(), 
                    processedById
                );
            } catch (Exception e) {
                throw new Exception("将用户添加到团队失败: " + e.getMessage());
            }
        }
        
        return teamApplicationRepository.save(application);
    }
} 