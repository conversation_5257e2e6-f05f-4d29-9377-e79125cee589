"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[669,838],{73904:(e,n,a)=>{a.r(n),a.d(n,{default:()=>B});var r=a(55593),t=(a(52675),a(89463),a(44114),a(62010),a(61431)),o={class:"teams-container"},l={class:"page-header"},i={class:"action-buttons"},u={class:"card-header"},s={class:"team-header-actions"},c={class:"team-info"},d={class:"team-description"},m={class:"team-stats"},f={class:"stat-item"},p={class:"stat-value"},v={class:"stat-item"},g={class:"stat-value"},k={class:"stat-item"},b={class:"stat-value"},h={class:"team-actions"},w={key:1,class:"no-team-message"},T={key:2,class:"team-annotations-section"},y={class:"section-header"},_={class:"section-actions"},A={key:2,class:"pagination-container"},C={style:{display:"flex","flex-direction":"column",gap:"4px",width:"100%"}},F={style:{display:"flex","justify-content":"space-between","align-items":"center"}},L={style:{"font-weight":"bold"}},R={style:{color:"#8492a6","font-size":"13px"}},D={style:{"font-size":"12px",color:"#606266","white-space":"normal","line-height":"1.3"}},W={class:"dialog-footer"},V={class:"dialog-footer"},x={class:"debug-info",style:{"margin-bottom":"15px",padding:"10px","background-color":"#f0f9eb","border-radius":"4px"}},I={class:"dialog-footer"};function O(e,n,a,O,E,K){var X=(0,t.g2)("el-button"),N=(0,t.g2)("el-badge"),j=(0,t.g2)("el-card"),M=(0,t.g2)("el-empty"),S=(0,t.g2)("el-input"),J=(0,t.g2)("el-option"),U=(0,t.g2)("el-select"),P=(0,t.g2)("el-table-column"),Q=(0,t.g2)("el-table"),z=(0,t.g2)("el-pagination"),B=(0,t.g2)("el-form-item"),q=(0,t.g2)("el-form"),$=(0,t.g2)("el-dialog"),G=(0,t.g2)("el-tag"),H=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",o,[(0,t.Lk)("div",l,[n[20]||(n[20]=(0,t.Lk)("h2",null,"我的团队",-1)),(0,t.Lk)("div",i,[!O.currentTeam&&O.isAdmin||!O.currentTeam&&O.isReviewer?((0,t.uX)(),(0,t.Wv)(X,{key:0,type:"success",onClick:n[0]||(n[0]=function(e){return O.showCreateTeamDialog=!0})},{default:(0,t.k6)((function(){return n[18]||(n[18]=[(0,t.eW)(" 创建团队 ")])})),_:1,__:[18]})):(0,t.Q3)("",!0),O.currentTeam?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.Wv)(X,{key:1,type:"primary",onClick:n[1]||(n[1]=function(e){return O.showJoinTeamDialog=!0})},{default:(0,t.k6)((function(){return n[19]||(n[19]=[(0,t.eW)(" 加入团队 ")])})),_:1,__:[19]}))])]),O.currentTeam?((0,t.uX)(),(0,t.Wv)(j,{key:0,class:"current-team-card"},{header:(0,t.k6)((function(){return[(0,t.Lk)("div",u,[n[24]||(n[24]=(0,t.Lk)("span",null,"当前团队",-1)),(0,t.Lk)("div",s,[O.isTeamOwner||O.isAdmin?((0,t.uX)(),(0,t.Wv)(X,{key:0,type:"danger",size:"small",onClick:O.handleDisbandTeam},{default:(0,t.k6)((function(){return n[21]||(n[21]=[(0,t.eW)(" 解散团队 ")])})),_:1,__:[21]},8,["onClick"])):(0,t.Q3)("",!0),O.isAdmin||O.isReviewer?((0,t.uX)(),(0,t.Wv)(X,{key:1,type:"primary",size:"small",onClick:n[2]||(n[2]=function(){return e.$router.push({name:"TeamApplications"})}),class:"team-app-button"},{default:(0,t.k6)((function(){return[n[22]||(n[22]=(0,t.eW)(" 团队申请管理 ")),O.pendingApplicationsCount>0?((0,t.uX)(),(0,t.Wv)(N,{key:0,value:O.pendingApplicationsCount,class:"team-app-button-badge",type:"danger",max:99},null,8,["value"])):(0,t.Q3)("",!0)]})),_:1,__:[22]})):(0,t.Q3)("",!0),(0,t.bF)(X,{type:"danger",text:"",onClick:O.handleLeaveTeam},{default:(0,t.k6)((function(){return n[23]||(n[23]=[(0,t.eW)(" 退出团队 ")])})),_:1,__:[23]},8,["onClick"])])])]})),default:(0,t.k6)((function(){return[(0,t.Lk)("div",c,[(0,t.Lk)("h3",null,(0,t.v_)(O.currentTeam.name),1),(0,t.Lk)("p",d,(0,t.v_)(O.currentTeam.description||"暂无团队介绍"),1),(0,t.Lk)("div",m,[(0,t.Lk)("div",f,[n[25]||(n[25]=(0,t.Lk)("div",{class:"stat-label"},"成员数",-1)),(0,t.Lk)("div",p,(0,t.v_)(O.currentTeam.memberCount||0),1)]),(0,t.Lk)("div",v,[n[26]||(n[26]=(0,t.Lk)("div",{class:"stat-label"},"病例数",-1)),(0,t.Lk)("div",g,(0,t.v_)(O.currentTeam.caseCount||0),1)]),(0,t.Lk)("div",k,[n[27]||(n[27]=(0,t.Lk)("div",{class:"stat-label"},"创建时间",-1)),(0,t.Lk)("div",b,(0,t.v_)(O.formatDate(O.currentTeam.createdAt)),1)])]),(0,t.Lk)("div",h,[(0,t.bF)(X,{type:"primary",onClick:n[3]||(n[3]=function(e){return O.showTeamMembersDialog=!0})},{default:(0,t.k6)((function(){return n[28]||(n[28]=[(0,t.eW)("查看团队成员")])})),_:1,__:[28]})])])]})),_:1})):((0,t.uX)(),(0,t.CE)("div",w,[(0,t.bF)(M,{description:"您当前不属于任何团队"}),(0,t.bF)(X,{type:"primary",onClick:n[4]||(n[4]=function(e){return O.showJoinTeamDialog=!0})},{default:(0,t.k6)((function(){return n[29]||(n[29]=[(0,t.eW)("加入团队")])})),_:1,__:[29]})])),O.currentTeam?((0,t.uX)(),(0,t.CE)("div",T,[(0,t.Lk)("div",y,[n[30]||(n[30]=(0,t.Lk)("h3",null,"团队已通过标注",-1)),(0,t.Lk)("div",_,[(0,t.bF)(S,{modelValue:O.searchQuery,"onUpdate:modelValue":n[5]||(n[5]=function(e){return O.searchQuery=e}),placeholder:"搜索病例","prefix-icon":"el-icon-search",clearable:"",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue"]),(0,t.bF)(U,{modelValue:O.filterType,"onUpdate:modelValue":n[6]||(n[6]=function(e){return O.filterType=e}),placeholder:"标注类型",clearable:"",style:{width:"150px"}},{default:(0,t.k6)((function(){return[(0,t.bF)(J,{label:"全部",value:""}),(0,t.bF)(J,{label:"血管瘤",value:"血管瘤"}),(0,t.bF)(J,{label:"其他",value:"其他"})]})),_:1},8,["modelValue"])])]),(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",null,[O.teamAnnotations.length>0?((0,t.uX)(),(0,t.Wv)(Q,{key:0,data:O.filteredAnnotations,stripe:"",style:{width:"100%"},onRowClick:O.handleViewAnnotation},{default:(0,t.k6)((function(){return[(0,t.bF)(P,{prop:"caseNumber",label:"病例号",width:"120"}),(0,t.bF)(P,{prop:"uploadedByName",label:"标注医生",width:"120"}),(0,t.bF)(P,{prop:"lesionLocation",label:"部位",width:"120"}),(0,t.bF)(P,{prop:"tags",label:"类型",width:"120"}),(0,t.bF)(P,{prop:"formattedReviewDate",label:"审核时间",width:"180"}),(0,t.bF)(P,{fixed:"right",label:"操作",width:"120"},{default:(0,t.k6)((function(e){return[(0,t.bF)(X,{type:"primary",size:"small",onClick:(0,t.D$)((function(n){return O.handleViewAnnotation(e.row)}),["stop"])},{default:(0,t.k6)((function(){return n[31]||(n[31]=[(0,t.eW)("查看")])})),_:2,__:[31]},1032,["onClick"])]})),_:1})]})),_:1},8,["data","onRowClick"])):((0,t.uX)(),(0,t.Wv)(M,{key:1,description:"暂无已通过标注"})),O.teamAnnotations.length>0?((0,t.uX)(),(0,t.CE)("div",A,[(0,t.bF)(z,{"current-page":O.currentPage,"onUpdate:currentPage":n[7]||(n[7]=function(e){return O.currentPage=e}),background:"",layout:"prev, pager, next",total:O.filteredAnnotations.length,"page-size":10},null,8,["current-page","total"])])):(0,t.Q3)("",!0)])),[[H,O.annotationsLoading]])])):(0,t.Q3)("",!0),(0,t.bF)($,{modelValue:O.showJoinTeamDialog,"onUpdate:modelValue":n[11]||(n[11]=function(e){return O.showJoinTeamDialog=e}),title:"加入团队",width:"500px"},{footer:(0,t.k6)((function(){return[(0,t.Lk)("span",W,[(0,t.bF)(X,{onClick:n[10]||(n[10]=function(e){return O.showJoinTeamDialog=!1})},{default:(0,t.k6)((function(){return n[32]||(n[32]=[(0,t.eW)("取消")])})),_:1,__:[32]}),(0,t.bF)(X,{type:"primary",loading:O.joinTeamLoading,onClick:O.handleJoinTeam},{default:(0,t.k6)((function(){return n[33]||(n[33]=[(0,t.eW)(" 提交申请 ")])})),_:1,__:[33]},8,["loading","onClick"])])]})),default:(0,t.k6)((function(){return[(0,t.bF)(q,{ref:"joinTeamFormRef",model:O.joinTeamForm,"label-width":"80px",rules:O.joinTeamRules},{default:(0,t.k6)((function(){return[(0,t.bF)(B,{label:"团队代码",prop:"teamCode"},{default:(0,t.k6)((function(){return[(0,t.bF)(U,{modelValue:O.joinTeamForm.teamCode,"onUpdate:modelValue":n[8]||(n[8]=function(e){return O.joinTeamForm.teamCode=e}),filterable:"",placeholder:"请选择要加入的团队",style:{width:"100%"}},{default:(0,t.k6)((function(){return[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(O.teamOptions,(function(e){return(0,t.uX)(),(0,t.Wv)(J,{key:e.code,label:e.name,value:e.code},{default:(0,t.k6)((function(){return[(0,t.Lk)("div",C,[(0,t.Lk)("div",F,[(0,t.Lk)("span",L,(0,t.v_)(e.name),1),(0,t.Lk)("span",R,"ID: "+(0,t.v_)(e.code),1)]),(0,t.Lk)("div",D,(0,t.v_)(e.description||"暂无描述"),1)])]})),_:2},1032,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),(0,t.bF)(B,{label:"加入理由",prop:"reason"},{default:(0,t.k6)((function(){return[(0,t.bF)(S,{modelValue:O.joinTeamForm.reason,"onUpdate:modelValue":n[9]||(n[9]=function(e){return O.joinTeamForm.reason=e}),type:"textarea",placeholder:"请简要说明加入原因",rows:3},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"]),(0,t.bF)($,{modelValue:O.showCreateTeamDialog,"onUpdate:modelValue":n[15]||(n[15]=function(e){return O.showCreateTeamDialog=e}),title:"创建团队",width:"500px"},{footer:(0,t.k6)((function(){return[(0,t.Lk)("span",V,[(0,t.bF)(X,{onClick:n[14]||(n[14]=function(e){return O.showCreateTeamDialog=!1})},{default:(0,t.k6)((function(){return n[34]||(n[34]=[(0,t.eW)("取消")])})),_:1,__:[34]}),(0,t.bF)(X,{type:"primary",loading:O.createTeamLoading,onClick:O.handleCreateTeam},{default:(0,t.k6)((function(){return n[35]||(n[35]=[(0,t.eW)(" 创建团队 ")])})),_:1,__:[35]},8,["loading","onClick"])])]})),default:(0,t.k6)((function(){return[(0,t.bF)(q,{ref:"createTeamFormRef",model:O.createTeamForm,"label-width":"80px",rules:O.createTeamRules},{default:(0,t.k6)((function(){return[(0,t.bF)(B,{label:"团队名称",prop:"name"},{default:(0,t.k6)((function(){return[(0,t.bF)(S,{modelValue:O.createTeamForm.name,"onUpdate:modelValue":n[12]||(n[12]=function(e){return O.createTeamForm.name=e}),placeholder:"请输入团队名称"},null,8,["modelValue"])]})),_:1}),(0,t.bF)(B,{label:"团队描述",prop:"description"},{default:(0,t.k6)((function(){return[(0,t.bF)(S,{modelValue:O.createTeamForm.description,"onUpdate:modelValue":n[13]||(n[13]=function(e){return O.createTeamForm.description=e}),type:"textarea",placeholder:"请输入团队描述",rows:4},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"]),(0,t.bF)($,{modelValue:O.showTeamMembersDialog,"onUpdate:modelValue":n[17]||(n[17]=function(e){return O.showTeamMembersDialog=e}),title:"团队成员",width:"800px","destroy-on-close":"","append-to-body":!0,onOpen:O.fetchTeamMembers},{footer:(0,t.k6)((function(){return[(0,t.Lk)("span",I,[(0,t.bF)(X,{onClick:n[16]||(n[16]=function(e){return O.showTeamMembersDialog=!1})},{default:(0,t.k6)((function(){return n[39]||(n[39]=[(0,t.eW)("关闭")])})),_:1,__:[39]})])]})),default:(0,t.k6)((function(){var a,o,l,i;return[(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",null,[(0,t.Lk)("div",x,[n[36]||(n[36]=(0,t.Lk)("p",null,[(0,t.Lk)("strong",null,"调试信息:")],-1)),(0,t.Lk)("p",null,"当前用户ID: "+(0,t.v_)(null===(a=e.currentUser)||void 0===a?void 0:a.id),1),(0,t.Lk)("p",null,"团队所有者: "+(0,t.v_)("object"===(0,r.A)(null===(o=O.currentTeam)||void 0===o?void 0:o.owner)?null===(l=O.currentTeam)||void 0===l||null===(l=l.owner)||void 0===l?void 0:l.id:null===(i=O.currentTeam)||void 0===i?void 0:i.owner),1),(0,t.Lk)("p",null,"是否为团队所有者: "+(0,t.v_)(O.isTeamOwner?"是":"否"),1)]),O.teamMembers.length>0?((0,t.uX)(),(0,t.Wv)(Q,{key:0,data:O.teamMembers,stripe:"",style:{width:"100%"}},{default:(0,t.k6)((function(){return[(0,t.bF)(P,{prop:"name",label:"姓名"}),(0,t.bF)(P,{prop:"role",label:"角色"},{default:(0,t.k6)((function(e){return[(0,t.bF)(G,{type:O.getRoleType(e.row.role)},{default:(0,t.k6)((function(){return[(0,t.eW)((0,t.v_)(O.getRoleName(e.row.role)),1)]})),_:2},1032,["type"])]})),_:1}),(0,t.bF)(P,{prop:"department",label:"部门"}),(0,t.bF)(P,{prop:"hospital",label:"医院"}),(0,t.bF)(P,{label:"操作",fixed:"right",width:"150"},{default:(0,t.k6)((function(a){return[O.isTeamOwner&&a.row.id!==e.currentUser.id?((0,t.uX)(),(0,t.Wv)(X,{key:0,type:"primary",size:"small",onClick:function(e){return O.handleTransferOwnership(a.row)}},{default:(0,t.k6)((function(){return n[37]||(n[37]=[(0,t.eW)(" 设为群主 ")])})),_:2,__:[37]},1032,["onClick"])):(0,t.Q3)("",!0),O.isTeamOwner&&a.row.id!==e.currentUser.id?((0,t.uX)(),(0,t.Wv)(X,{key:1,type:"danger",size:"small",onClick:function(n){return e.handleRemoveMember(a.row.id)}},{default:(0,t.k6)((function(){return n[38]||(n[38]=[(0,t.eW)(" 移除 ")])})),_:2,__:[38]},1032,["onClick"])):(0,t.Q3)("",!0)]})),_:1})]})),_:1},8,["data"])):((0,t.uX)(),(0,t.Wv)(M,{key:1,description:"暂无团队成员"}))])),[[H,O.membersLoading]])]})),_:1},8,["modelValue","onOpen"])])}var E=a(24059),K=a(88844),X=a(698),N=(a(76918),a(2008),a(74423),a(64346),a(62062),a(26910),a(60739),a(23288),a(18111),a(22489),a(61701),a(33110),a(79432),a(26099),a(38781),a(21699),a(80142)),j=a(98351),M=(a(72505),a(36149)),S=a(66278),J=a(29086),U=a(60455);const P={name:"TeamsView",components:{TeamApplicationManagement:J["default"]},setup:function(){var e=(0,S.Pj)(),n=(0,U.rd)(),a=(0,t.EW)((function(){return e.getters.isAdmin})),o=(0,t.EW)((function(){return e.getters.isReviewer})),l=(0,t.EW)((function(){return e.getters.getUser})),i=(0,t.EW)((function(){return e.getters["teamApplications/getPendingApplicationsCount"]})),u=(0,t.KR)(null),s=(0,t.KR)([]),c=(0,t.KR)(!1),d=(0,t.Kh)({teamCode:"",reason:""}),m={teamCode:[{required:!0,message:"请选择要加入的团队",trigger:"change"}],reason:[{required:!0,message:"请输入加入理由",trigger:"blur"},{min:5,max:200,message:"请输入5-200个字符",trigger:"blur"}]},f=(0,t.KR)(null),p=(0,t.KR)(!1),v=(0,t.KR)([]),g=(0,t.KR)(!1),k=(0,t.KR)(!1),b=(0,t.Kh)({name:"",description:""}),h={name:[{required:!0,message:"请输入团队名称",trigger:"blur"},{min:2,max:50,message:"团队名称长度在2-50个字符之间",trigger:"blur"}],description:[{required:!0,message:"请输入团队描述",trigger:"blur"},{min:5,max:200,message:"请输入5-200个字符",trigger:"blur"}]},w=(0,t.KR)(null),T=(0,t.KR)(!1),y=(0,t.KR)(!1),_=(0,t.KR)(!1),A=(0,t.KR)(!1),C=(0,t.KR)(""),F=(0,t.KR)(""),L=(0,t.KR)([]),R=(0,t.KR)(!1),D=(0,t.KR)(1),W=(0,t.EW)((function(){return!(!l.value||!u.value)&&(!!u.value.owner&&("object"===(0,r.A)(u.value.owner)?l.value.id===u.value.owner.id:l.value.id===u.value.owner))})),V=function(){var e=(0,X.A)((0,E.A)().m((function e(){var n,a,t,o,l,i,s;return(0,E.A)().w((function(e){while(1)switch(e.n){case 0:if(e.p=0,n=localStorage.getItem("user"),n){e.n=1;break}return console.log("未找到用户信息"),u.value=null,e.a(2);case 1:if(a=JSON.parse(n),console.log("当前用户:",a),!a||!a.id){e.n=14;break}return e.p=2,console.log("尝试从API获取用户的最新团队信息"),e.n=3,M["default"].users.getUser(a.id);case 3:if(t=e.v,!(t&&t.data&&t.data.team)){e.n=10;break}if(console.log("成功从API获取用户团队信息:",t.data.team),console.log("团队owner数据类型:",(0,r.A)(t.data.team.owner)),console.log("团队owner值:",t.data.team.owner),!t.data.team._isCircularRef&&!t.data.team._simplified){e.n=8;break}return console.log("团队数据被简化处理，尝试获取完整团队信息"),e.p=4,e.n=5,M["default"].teams.getOne(t.data.team.id);case 5:o=e.v,o&&o.data?u.value=(0,K.A)({},o.data):u.value=(0,K.A)({},t.data.team),e.n=7;break;case 6:e.p=6,l=e.v,console.error("获取完整团队信息失败:",l),u.value=(0,K.A)({},t.data.team);case 7:e.n=9;break;case 8:u.value=(0,K.A)({},t.data.team);case 9:x(),e.n=11;break;case 10:console.log("用户当前不属于任何团队"),u.value=null,a.team&&(console.log("清除localStorage中的团队信息"),delete a.team,localStorage.setItem("user",JSON.stringify(a)));case 11:e.n=13;break;case 12:e.p=12,i=e.v,console.error("API获取用户团队信息失败:",i),u.value=null;case 13:e.n=15;break;case 14:console.log("用户数据不完整，无法获取团队信息"),u.value=null;case 15:e.n=17;break;case 16:e.p=16,s=e.v,console.error("获取用户信息失败:",s),u.value=null;case 17:return e.a(2)}}),e,null,[[4,6],[2,12],[0,16]])})));return function(){return e.apply(this,arguments)}}(),x=function(){var e=(0,X.A)((0,E.A)().m((function e(){var n,a;return(0,E.A)().w((function(e){while(1)switch(e.n){case 0:if(u.value&&u.value.id){e.n=1;break}return e.a(2);case 1:return e.p=1,A.value=!0,console.log("获取团队成员，团队ID:",u.value.id),e.n=2,M["default"].teams.getTeamMembers(u.value.id);case 2:n=e.v,console.log("团队成员API返回数据:",n),n.data&&Array.isArray(n.data)?(console.log("使用数据库返回的真实团队成员数据，成员数量:",n.data.length),s.value=n.data.map((function(e){return(0,K.A)((0,K.A)({},e),{},{joinDate:Q(e.joinDate)})})).sort((function(e,n){var a={ADMIN:1,REVIEWER:2,DOCTOR:3};return(a[e.role]||99)-(a[n.role]||99)})),u.value&&(u.value.memberCount=s.value.length,console.log("更新团队成员数量:",u.value.memberCount))):(console.error("团队成员数据不是数组:",n.data),s.value=[],u.value&&(u.value.memberCount=0)),e.n=4;break;case 3:e.p=3,a=e.v,console.error("获取团队成员失败:",a),s.value=[],N.nk.error("获取团队成员失败，请稍后重试"),u.value&&(u.value.memberCount=0);case 4:return e.p=4,A.value=!1,e.f(4);case 5:return e.a(2)}}),e,null,[[1,3,4,5]])})));return function(){return e.apply(this,arguments)}}(),I=function(){var e=(0,X.A)((0,E.A)().m((function e(){var n,a,r,t,o,l;return(0,E.A)().w((function(e){while(1)switch(e.n){case 0:if(f.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,f.value.validate();case 2:if(p.value=!0,n=d.teamCode,console.log("尝试加入团队ID: ".concat(n)),e.p=3,a=localStorage.getItem("user"),a){e.n=4;break}return console.warn("未找到用户信息"),N.nk.error("未找到用户信息，请重新登录"),p.value=!1,e.a(2);case 4:if(r=JSON.parse(a),!r){e.n=6;break}return e.n=5,M["default"].teams.applyToJoinTeam(n,d.reason);case 5:N.nk.success("已成功提交加入申请，请等待审核"),d.teamCode="",d.reason="",c.value=!1,V(),e.n=7;break;case 6:N.nk.error("用户信息不完整，请重新登录");case 7:e.n=9;break;case 8:e.p=8,o=e.v,console.error("申请加入团队失败:",o),N.nk.error((null===(t=o.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||"申请加入团队失败");case 9:return e.p=9,p.value=!1,e.f(9);case 10:e.n=12;break;case 11:e.p=11,l=e.v,console.error("表单验证失败:",l),p.value=!1;case 12:return e.a(2)}}),e,null,[[3,8,9,10],[1,11]])})));return function(){return e.apply(this,arguments)}}(),O=function(){j.s.confirm("确定要退出当前团队吗？退出后需要重新申请加入。","退出团队",{confirmButtonText:"确定退出",cancelButtonText:"取消",type:"warning"}).then((0,X.A)((0,E.A)().m((function e(){var n,a,r,t;return(0,E.A)().w((function(e){while(1)switch(e.n){case 0:if(e.p=0,n=localStorage.getItem("user"),n){e.n=1;break}return console.warn("未找到用户信息"),N.nk.error("未找到用户信息，请重新登录"),e.a(2);case 1:if(a=JSON.parse(n),!(a&&a.team&&u.value)){e.n=3;break}return e.n=2,M["default"].teams.removeTeamMember(u.value.id,a.id);case 2:N.nk.success("已成功退出团队"),u.value=null,s.value=[],e.n=4;break;case 3:N.nk.error("您当前不在任何团队中");case 4:e.n=6;break;case 5:e.p=5,t=e.v,console.error("退出团队失败:",t),N.nk.error("退出团队失败: "+((null===(r=t.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||t.message));case 6:return e.a(2)}}),e,null,[[0,5]])}))))["catch"]((function(){}))},J=function(e){var n={ADMIN:"danger",DOCTOR:"primary",REVIEWER:"success"};return n[e]||"info"},P=function(e){var n={ADMIN:"管理员",DOCTOR:"标注医生",REVIEWER:"审核医生"};return n[e]||"未知"},Q=function(e){if(!e)return"未知";try{var n=new Date(e);return isNaN(n.getTime())?"未知":n.toLocaleString("zh-CN")}catch(a){return"未知"}},z=function(){var e=(0,X.A)((0,E.A)().m((function e(){var n,a,r;return(0,E.A)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,g.value=!0,e.p=1,e.n=2,M["default"].teams.getAll();case 2:if(n=e.v,!(n&&n.data&&n.data.length>0)){e.n=3;break}return v.value=n.data.map((function(e){return{code:e.id.toString(),name:e.name,description:e.description||"暂无描述"}})),console.log("成功获取到团队列表:",v.value),e.a(2);case 3:e.n=5;break;case 4:e.p=4,a=e.v,console.error("API获取团队列表失败，将使用模拟数据:",a);case 5:console.log("使用模拟团队数据"),v.value=[{code:"1",name:"管理员团队",description:"系统管理团队"},{code:"5",name:"第一测试",description:"仅限于测试"},{code:"6",name:"第二测试团队",description:"仅限于测试"}],e.n=7;break;case 6:e.p=6,r=e.v,console.error("获取团队列表完全失败:",r),N.nk.error("获取团队列表失败，已使用默认数据"),v.value=[{code:"1",name:"默认团队",description:"系统默认团队"}];case 7:return e.p=7,g.value=!1,e.f(7);case 8:return e.a(2)}}),e,null,[[1,4],[0,6,7,8]])})));return function(){return e.apply(this,arguments)}}();(0,t.wB)(c,(function(e){e&&z()["catch"]((function(e){console.error("加载团队列表失败:",e),v.value=[]}))})),(0,t.sV)((function(){V().then((function(){u.value&&u.value.id&&$()}))["catch"]((function(e){console.error("获取用户团队信息失败，但不影响界面显示:",e)}))}));var B=function(){var e=(0,X.A)((0,E.A)().m((function e(){var n,a,r;return(0,E.A)().w((function(e){while(1)switch(e.n){case 0:if(w.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,w.value.validate();case 2:if(T.value=!0,n=localStorage.getItem("user"),n){e.n=3;break}return console.warn("未找到用户信息"),N.nk.error("未找到用户信息，请重新登录"),T.value=!1,e.a(2);case 3:return JSON.parse(n),e.n=4,M["default"].teams.create({name:b.name,description:b.description});case 4:b.name="",b.description="",k.value=!1,N.nk.success("团队创建成功"),V(),e.n=7;break;case 5:if(e.p=5,r=e.v,"ValidationError"!==r.name){e.n=6;break}return e.a(2);case 6:console.error("创建团队失败:",r),N.nk.error("创建团队失败: "+((null===(a=r.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||r.message));case 7:return e.p=7,T.value=!1,e.f(7);case 8:return e.a(2)}}),e,null,[[1,5,7,8]])})));return function(){return e.apply(this,arguments)}}(),q=function(){if(u.value&&u.value.id){var e=localStorage.getItem("user");if(!e)return N.nk.warning("请先登录后再操作"),sessionStorage.setItem("redirectAfterLogin",window.location.pathname),void(window.location.href="/login");try{var n=JSON.parse(e);if(console.log("当前用户:",n),!a.value&&!o.value)return void N.nk.warning("只有管理员和审核医生可以管理团队申请");y.value=!0}catch(r){console.error("解析用户信息失败:",r),N.nk.error("获取用户信息失败，请重新登录")}}else N.nk.warning("请先选择一个团队")},$=function(){var e=(0,X.A)((0,E.A)().m((function e(){var n,a;return(0,E.A)().w((function(e){while(1)switch(e.n){case 0:if(u.value&&u.value.id){e.n=1;break}return e.a(2);case 1:return e.p=1,R.value=!0,console.log("获取团队已通过标注，团队ID:",u.value.id),e.n=2,M["default"].teams.getTeamAnnotations(u.value.id);case 2:n=e.v,console.log("团队已通过标注API返回数据:",n),n&&n.data&&Array.isArray(n.data)?(L.value=n.data.map((function(e){return console.log("标注ID ".concat(e.id," 的审核时间:"),e.reviewDate),(0,K.A)((0,K.A)({},e),{},{formattedReviewDate:Q(e.reviewDate)})})),console.log("成功获取到团队已通过标注:",L.value)):(console.error("团队已通过标注数据不是数组:",n.data),L.value=[]),e.n=4;break;case 3:e.p=3,a=e.v,console.error("获取团队已通过标注失败:",a),L.value=[],N.nk.error("获取团队已通过标注失败，请稍后重试");case 4:return e.p=4,R.value=!1,e.f(4);case 5:return e.a(2)}}),e,null,[[1,3,4,5]])})));return function(){return e.apply(this,arguments)}}(),G=(0,t.EW)((function(){if(!L.value)return[];var e=L.value;return C.value&&(e=e.filter((function(e){var n,a,r,t,o=C.value.toLowerCase();return(null===(n=e.caseNumber)||void 0===n?void 0:n.toLowerCase().includes(o))||(null===(a=e.uploadedByName)||void 0===a?void 0:a.toLowerCase().includes(o))||(null===(r=e.lesionLocation)||void 0===r?void 0:r.toLowerCase().includes(o))||(null===(t=e.tags)||void 0===t?void 0:t.toLowerCase().includes(o))}))),F.value&&(e=e.filter((function(e){var n;return null===(n=e.tags)||void 0===n?void 0:n.includes(F.value)}))),e})),H=function(e){e&&e.id?(console.log("导航到病例详情页, ID:",e.id),n.push({name:"ViewCase",params:{id:e.id}})):(console.error("无法查看标注，缺少有效ID",e),N.nk.error("无法查看病例，缺少有效ID"))},Y=function(e){var n={待标注:"info",已标注:"success",审核中:"warning",已通过:"success",已驳回:"danger"};return n[e]||"info"},Z=function(){j.s.confirm("此操作将永久解散该团队，所有成员将被移除。请确认所有重要数据已备份。是否继续?","警告：解散团队",{confirmButtonText:"确认解散",cancelButtonText:"取消",type:"error"}).then((0,X.A)((0,E.A)().m((function e(){var n,a;return(0,E.A)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,M["default"].teams.deleteTeam(u.value.id);case 1:N.nk.success("团队已成功解散"),V(),e.n=3;break;case 2:e.p=2,a=e.v,N.nk.error((null===(n=a.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||"解散团队失败");case 3:return e.a(2)}}),e,null,[[0,2]])}))))["catch"]((function(){}))},ee=function(e){j.s.confirm("您确定要将团队所有权转让给【".concat(e.name,"】吗？此操作不可撤销。"),"警告：转让所有权",{confirmButtonText:"确认转让",cancelButtonText:"取消",type:"warning"}).then((0,X.A)((0,E.A)().m((function n(){var a,r;return(0,E.A)().w((function(n){while(1)switch(n.n){case 0:return n.p=0,n.n=1,M["default"].teams.transferOwnership(u.value.id,e.id);case 1:N.nk.success("团队所有权已成功转让"),V(),x(),n.n=3;break;case 2:n.p=2,r=n.v,N.nk.error((null===(a=r.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"转让所有权失败");case 3:return n.a(2)}}),n,null,[[0,2]])}))))["catch"]((function(){}))};return{currentTeam:u,teamMembers:s,showJoinTeamDialog:c,joinTeamForm:d,joinTeamRules:m,joinTeamFormRef:f,joinTeamLoading:p,handleJoinTeam:I,handleLeaveTeam:O,getRoleType:J,getRoleName:P,formatDate:Q,teamOptions:v,loading:g,searchTeams:z,showCreateTeamDialog:k,createTeamForm:b,createTeamRules:h,createTeamFormRef:w,createTeamLoading:T,handleCreateTeam:B,isAdmin:a,isReviewer:o,showTeamApplicationDialog:y,showTeamApplications:q,showTeamMembersDialog:_,membersLoading:A,fetchTeamMembers:x,searchQuery:C,filterType:F,teamAnnotations:L,annotationsLoading:R,currentPage:D,filteredAnnotations:G,handleViewAnnotation:H,getStatusType:Y,isTeamOwner:W,handleDisbandTeam:Z,handleTransferOwnership:ee,pendingApplicationsCount:i}}};var Q=a(66262);const z=(0,Q.A)(P,[["render",O],["__scopeId","data-v-d00dae2e"]]),B=z}}]);
//# sourceMappingURL=669.8db92e4a.js.map