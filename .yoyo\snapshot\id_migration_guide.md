# ID类型迁移指南：从Integer到Long

本指南帮助您将项目中涉及ID字段的类型从`Integer`更改为`Long`。

## 为什么需要迁移

我们将数据库中的ID字段从`INT`类型更改为`BIGINT`类型，以支持更大范围的ID值。相应地，Java代码中也需要将`Integer`类型更改为`Long`类型。

## 迁移步骤

### 1. 实体类修改

- [x] 将所有实体类中的ID字段和相关外键字段从`Integer`改为`Long`
- [x] 修改getter和setter方法的参数和返回值类型
- [x] 添加兼容方法，接受`Integer`类型并转换为`Long`类型

### 2. 仓库接口修改

- [x] 将JpaRepository的泛型参数从`Integer`改为`Long` 
- [x] 修改查询方法的参数类型
- [x] 添加兼容方法，接受`Integer`类型参数

### 3. 服务类修改

- [x] 将涉及ID的方法参数从`Integer`改为`Long`
- [x] 添加兼容方法，接受`Integer`类型参数并调用`Long`类型的方法

### 4. 控制器修改

以下是需要手动修改的控制器类和方法：

#### FileUploadController.java
- 第289行: `imageMetadataService.getImageMetadata(imageId)` 调用时将`Integer`转换为`Long`

#### ApiController.java
- 第71行: `imageMetadataRepository.findById(imageId)` 将`Integer`转换为`Long`
- 第190行: `imageMetadataService.submitForReview(imageId, userId)` 将第一个参数转换为`Long`
- 第232行: `imageMetadataRepository.findById(imageId)` 将`Integer`转换为`Long`
- 第236行: `imagePairRepository.findByMetadataId(metadata.getId())` 确保ID为`Long`类型
- 第247行: `imageMetadataService.reviewImage(imageId, approved, reviewNotes, userId)` 将第一个参数转换为`Long`
- 第475行: `imageMetadataRepository.findById(imageId)` 将`Integer`转换为`Long`
- 第482行: `imageMetadataRepository.findById(imageId)` 将`Integer`转换为`Long`
- 第631行: `saveAnnotatedImage` 方法中的`int`类型参数转换为`Long`

#### AnnotationController.java
- 第59行: `imageMetadataService.getImageMetadata(imageId)` 将`Integer`转换为`Long`
- 第83行: `imageMetadataService.submitForReview(imageId, userId)` 将第一个参数转换为`Long`
- 第192行: `imageMetadataService.getImageMetadata(imageId)` 将`Integer`转换为`Long`

#### TagApiController.java
- 第74行: 将`Integer`参数转换为`Long`
- 第136行: 将`Integer`参数转换为`Long`
- 第217行: 将`Integer`参数转换为`Long`
- 第294行: 将`Integer`参数转换为`Long`
- 第394行: 将`Integer`参数转换为`Long`
- 第629行: 将`Integer`参数转换为`Long`
- 第689行: 将`Integer`参数转换为`Long`
- 第812行: 将`Integer`参数转换为`Long`
- 第902行: 将`Integer`参数转换为`Long`
- 第909行: 将`Integer`参数转换为`Long`
- 第1114行: 将`Long`转换为`Integer`
- 第1123行: 将`Integer`转换为`Long`

### 5. 使用TypeConverter工具类

为了简化转换，我们创建了一个`TypeConverter`工具类，该类提供了以下方法：

```java
// 将Integer转换为Long
TypeConverter.toLong(Integer value)

// 将int转换为Long
TypeConverter.toLong(int value)

// 将Long转换为Integer
TypeConverter.toInteger(Long value)
```

在所有需要转换的地方使用这些方法，例如：

```java
// 修改前
imageMetadataRepository.findById(imageId);

// 修改后
imageMetadataRepository.findById(TypeConverter.toLong(imageId));
```

## 测试

完成上述修改后，运行单元测试和集成测试，确保所有功能正常工作。特别注意以下几点：

1. ID的传递和转换是否正确
2. 数据库查询是否正常执行
3. 分页功能是否受到影响

## 回滚计划

如果迁移后出现严重问题，可以执行以下步骤回滚：

1. 还原实体类、仓库接口和服务类中的类型更改
2. 更新数据库表，将ID字段从BIGINT改回INT
3. 删除辅助方法和转换代码 