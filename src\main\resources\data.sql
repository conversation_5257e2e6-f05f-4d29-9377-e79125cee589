-- 确保users表中有测试用户数据
INSERT IGNORE INTO users (id, name, email, password, role, department, hospital, created_at)
VALUES (1, '测试用户', '<EMAIL>', '$2a$10$qr7EQr0lPsGgPz8VXBftHeQU3jZMbGMItlK1KY7y9mO.f/NtQG5AC', 'DOCTOR', '影像科', '测试医院', NOW());

-- 确保image_metadata表中有测试图像数据
INSERT IGNORE INTO image_metadata (id, filename, original_name, path, mimetype, size, width, height, uploaded_by, status, created_at)
VALUES (1, 'sample.jpg', 'sample.jpg', 'F:/血管瘤辅助系统/medical_images/sample.jpg', 'image/jpeg', 1024, 800, 600, 1, 'DRAFT', NOW());

-- 创建image_pairs表 (如果不存在)
CREATE TABLE IF NOT EXISTS image_pairs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metadata_id INT NOT NULL,
    image_one_path MEDIUMTEXT NOT NULL,
    image_two_path MEDIUMTEXT,
    description TEXT,
    created_by INT,
    created_at DATETIME DEFAULT NOW(),
    FOREIGN KEY (metadata_id) REFERENCES image_metadata(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 添加测试图像对数据
INSERT IGNORE INTO image_pairs (metadata_id, image_one_path, image_two_path, description, created_by)
VALUES (1, 'F:/血管瘤辅助系统/medical_images/sample.jpg', NULL, '图像ID:1', 1); 