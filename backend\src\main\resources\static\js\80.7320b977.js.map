{"version": 3, "file": "js/80.7320b977.js", "mappings": "8LACOA,MAAM,qB,GAEJA,MAAM,kB,EAHf,Q,GAWWA,MAAM,W,GAWRA,MAAM,mB,GACJA,MAAM,gB,GACJA,MAAM,a,GAEJA,MAAM,gB,GAEJA,MAAM,S,GAKVA,MAAM,a,GAEJA,MAAM,gB,GAEJA,MAAM,S,GAKVA,MAAM,a,GAEJA,MAAM,gB,GAEJA,MAAM,S,GAKVA,MAAM,a,GAEJA,MAAM,gB,GAEJA,MAAM,S,GAKVA,MAAM,a,GAEJA,MAAM,gB,GAEJA,MAAM,S,GAOZA,MAAM,kB,sEAtEfC,EAAAA,EAAAA,IA0EM,MA1ENC,EA0EM,EAxEJC,EAAAA,EAAAA,IAgBM,MAhBNC,EAgBM,EAfJD,EAAAA,EAAAA,IAMM,OANDH,MAAM,mBAAoBK,QAAKC,EAAA,KAAAA,EAAA,qBAAEC,EAAAC,kBAAAD,EAAAC,iBAAAC,MAAAF,EAAAG,UAAgB,I,EACpDP,EAAAA,EAAAA,IAAmG,OAA7FQ,IAAKC,EAAAC,WAAaC,EAAQ,OAAuCC,IAAI,OAAOf,MAAM,U,OALhGgB,GAAA,aAMQb,EAAAA,EAAAA,IAGM,OAHDH,MAAM,kBAAgB,EACzBG,EAAAA,EAAAA,IAA8B,KAA3BH,MAAM,oBACTG,EAAAA,EAAAA,IAAiB,YAAX,UAAI,OAGdA,EAAAA,EAAAA,IAA0C,MAA1Cc,EAAqB,OAAGC,EAAAA,EAAAA,IAAGN,EAAAO,QAAM,IACjChB,EAAAA,EAAAA,IAME,SALAiB,KAAK,OACLC,IAAI,YACJC,MAAA,iBACAC,OAAO,UACNC,SAAMlB,EAAA,KAAAA,EAAA,qBAAEC,EAAAkB,kBAAAlB,EAAAkB,iBAAAhB,MAAAF,EAAAG,UAAgB,I,aAK7BP,EAAAA,EAAAA,IAoDM,MApDNuB,EAoDM,EAnDJvB,EAAAA,EAAAA,IA6CM,MA7CNwB,EA6CM,EA5CJxB,EAAAA,EAAAA,IAOM,MAPNyB,EAOM,cANJzB,EAAAA,EAAAA,IAAoD,OAA/CH,MAAM,QAAM,EAACG,EAAAA,EAAAA,IAA4B,KAAzBH,MAAM,mBAAc,KACzCG,EAAAA,EAAAA,IAIM,MAJN0B,EAIM,cAHJ1B,EAAAA,EAAAA,IAA6B,OAAxBH,MAAM,SAAQ,QAAI,KACvBG,EAAAA,EAAAA,IAAuC,MAAvC2B,GAAuCZ,EAAAA,EAAAA,IAAjBN,EAAAmB,UAAQ,gBAC9B5B,EAAAA,EAAAA,IAAmC,KAAhCH,MAAM,uBAAqB,eAIlCG,EAAAA,EAAAA,IAOM,MAPN6B,EAOM,cANJ7B,EAAAA,EAAAA,IAAuD,OAAlDH,MAAM,QAAM,EAACG,EAAAA,EAAAA,IAA+B,KAA5BH,MAAM,sBAAiB,KAC5CG,EAAAA,EAAAA,IAIM,MAJN8B,EAIM,cAHJ9B,EAAAA,EAAAA,IAA2B,OAAtBH,MAAM,SAAQ,MAAE,KACrBG,EAAAA,EAAAA,IAAwC,MAAxC+B,GAAwChB,EAAAA,EAAAA,IAAlBN,EAAAuB,WAAS,gBAC/BhC,EAAAA,EAAAA,IAAmC,KAAhCH,MAAM,uBAAqB,eAIlCG,EAAAA,EAAAA,IAOM,MAPNiC,EAOM,gBANJjC,EAAAA,EAAAA,IAA+D,OAA1DH,MAAM,QAAM,EAACG,EAAAA,EAAAA,IAAuC,KAApCH,MAAM,8BAAyB,KACpDG,EAAAA,EAAAA,IAIM,MAJNkC,EAIM,cAHJlC,EAAAA,EAAAA,IAA2B,OAAtBH,MAAM,SAAQ,MAAE,KACrBG,EAAAA,EAAAA,IAA2C,MAA3CmC,GAA2CpB,EAAAA,EAAAA,IAArBN,EAAA2B,cAAY,kBAClCpC,EAAAA,EAAAA,IAAmC,KAAhCH,MAAM,uBAAqB,eAIlCG,EAAAA,EAAAA,IAOM,MAPNqC,EAOM,gBANJrC,EAAAA,EAAAA,IAA6D,OAAxDH,MAAM,QAAM,EAACG,EAAAA,EAAAA,IAAqC,KAAlCH,MAAM,4BAAuB,KAClDG,EAAAA,EAAAA,IAIM,MAJNsC,EAIM,gBAHJtC,EAAAA,EAAAA,IAA2B,OAAtBH,MAAM,SAAQ,MAAE,KACrBG,EAAAA,EAAAA,IAA6C,MAA7CuC,GAA6CxB,EAAAA,EAAAA,IAAvBN,EAAA+B,gBAAc,kBACpCxC,EAAAA,EAAAA,IAAmC,KAAhCH,MAAM,uBAAqB,eAIlCG,EAAAA,EAAAA,IAOM,MAPNyC,EAOM,gBANJzC,EAAAA,EAAAA,IAAwD,OAAnDH,MAAM,QAAM,EAACG,EAAAA,EAAAA,IAAgC,KAA7BH,MAAM,uBAAkB,KAC7CG,EAAAA,EAAAA,IAIM,MAJN0C,EAIM,gBAHJ1C,EAAAA,EAAAA,IAA2B,OAAtBH,MAAM,SAAQ,MAAE,KACrBG,EAAAA,EAAAA,IAAuC,MAAvC2C,GAAuC5B,EAAAA,EAAAA,IAAjBN,EAAAmC,UAAQ,kBAC9B5C,EAAAA,EAAAA,IAAmC,KAAhCH,MAAM,uBAAqB,iBAMpCG,EAAAA,EAAAA,IAEM,MAFN6C,EAEM,EADJC,EAAAA,EAAAA,IAA2DC,EAAA,CAAhD9B,KAAK,UAAWf,QAAOE,EAAA4C,Q,CAxE1C,SAAAC,EAAAA,EAAAA,KAwEkD,kBAAK9C,EAAA,MAAAA,EAAA,MAxEvD+C,EAAAA,EAAAA,IAwEkD,U,IAxElDC,EAAA,EAAAC,GAAA,M,yGAkFA,SACEC,KAAM,cACNC,KAAI,WACF,MAAO,CACLtC,OAAQ,GACRY,SAAU,GACVI,UAAW,GACXI,aAAc,GACdI,eAAgB,GAChBI,SAAU,GACVlC,UAAW,KACX6C,aAAa,EAEjB,EACAC,QAAO,WAEL,IACE,IAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAQxD,OAPAC,KAAK9C,OAASyC,EAAKM,IAAM,MACzBD,KAAKlC,SAAW6B,EAAKJ,MAAQ,MAC7BS,KAAK9B,UAAYyB,EAAKO,OAAS,MAC/BF,KAAK1B,aAAeqB,EAAKQ,UAAY,MACrCH,KAAKtB,eAAiBiB,EAAKS,YAAc,MAGlCT,EAAKU,MACV,IAAK,QACHL,KAAKlB,SAAW,MAChB,MACF,IAAK,SACHkB,KAAKlB,SAAW,OAChB,MACF,IAAK,WACHkB,KAAKlB,SAAW,OAChB,MACF,QACEkB,KAAKlB,SAAW,OAIpBkB,KAAKM,iBACP,CAAE,MAAOC,GACPC,QAAQD,MAAM,YAAaA,EAC7B,CACF,EACAE,QAAS,CACPvB,OAAM,WACJc,KAAKU,QAAQC,KAAK,iBACpB,EACApE,iBAAgB,WAEdyD,KAAKY,MAAMC,UAAUC,OACvB,EACAtD,iBAAgB,SAACuD,GACf,IAAMC,EAAOD,EAAME,OAAOC,MAAM,GAC3BF,IAGAA,EAAK7D,KAAKgE,WAAW,UAMtBH,EAAKI,KAAO,QACdpB,KAAKqB,SAASd,MAAM,eAKtBP,KAAKsB,aAAaN,GAXhBhB,KAAKqB,SAASd,MAAM,WAYxB,EACMe,aAAY,SAACN,GAAM,IAAAO,EAAA,YAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAN,EAAAA,EAAAA,KAAAO,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAQrB,OARqBD,EAAAE,EAAA,EAErBZ,EAAK9B,aAAc,EAGbmC,EAAW,IAAIQ,SACrBR,EAASS,OAAO,OAAQrB,GAExBiB,EAAAC,EAAA,EACuBI,EAAAA,WAAIC,MAAMjB,aAAaC,EAAKrE,OAAQ0E,GAAS,OAA9DC,EAAOI,EAAAO,EAGbjB,EAAK3E,UAAY6F,EAAAA,GAAeZ,EAASrC,KAAK5C,UAE9C2E,EAAKF,SAASqB,QAAQ,UAAST,EAAAC,EAAA,eAAAD,EAAAE,EAAA,EAAAJ,EAAAE,EAAAO,EAE/BhC,QAAQD,MAAM,UAASwB,GACvBR,EAAKF,SAASd,MAAM,aAA4B,QAAduB,EAAAC,EAAMF,gBAAQ,IAAAC,GAAM,QAANA,EAAdA,EAAgBtC,YAAI,IAAAsC,OAAA,EAApBA,EAAsBvB,QAASwB,EAAMY,SAAW,SAAQ,OAElE,OAFkEV,EAAAE,EAAA,EAE1FZ,EAAK9B,aAAc,EAAKwC,EAAAW,EAAA,iBAAAX,EAAAY,EAAA,MAAAlB,EAAA,qBAnBHH,EAqBzB,EACMlB,gBAAe,WAAG,IAAAwC,EAAA,YAAAtB,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAqB,IAAA,IAAAlB,EAAAmB,EAAA,OAAAvB,EAAAA,EAAAA,KAAAO,GAAA,SAAAiB,GAAA,eAAAA,EAAAf,GAAA,cAAAe,EAAAd,EAAA,EAAAc,EAAAf,EAAA,EAGGI,EAAAA,WAAIC,MAAMW,cAAcJ,EAAK5F,QAAO,OAArD2E,EAAOoB,EAAAT,EAETX,EAASrC,KAAK5C,YAChBkG,EAAKlG,UAAY6F,EAAAA,GAAeZ,EAASrC,KAAK5C,WAChDqG,EAAAf,EAAA,eAAAe,EAAAd,EAAA,EAAAa,EAAAC,EAAAT,EAEAhC,QAAQD,MAAM,UAASyC,GAAQ,cAAAC,EAAAJ,EAAA,MAAAE,EAAA,iBATXvB,EAWxB,I,eCpLJ,MAAM2B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/UserProfile.vue", "webpack://medical-annotation-frontend/./src/views/UserProfile.vue?5f8e"], "sourcesContent": ["<template>\r\n  <div class=\"profile-container\">\r\n    <!-- 顶部紫色渐变背景区域 -->\r\n    <div class=\"profile-header\">\r\n      <div class=\"avatar-container\" @click=\"triggerFileInput\">\r\n        <img :src=\"avatarUrl || require('@/assets/images/default-avatar.png')\" alt=\"用户头像\" class=\"avatar\" />\r\n        <div class=\"avatar-overlay\">\r\n          <i class=\"el-icon-camera\"></i>\r\n          <span>更换头像</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"user-id\">ID {{ userId }}</div>\r\n      <input \r\n        type=\"file\" \r\n        ref=\"fileInput\" \r\n        style=\"display: none\" \r\n        accept=\"image/*\" \r\n        @change=\"handleFileChange\"\r\n      />\r\n    </div>\r\n    \r\n    <!-- 底部白色背景区域 -->\r\n    <div class=\"profile-content\">\r\n      <div class=\"info-section\">\r\n        <div class=\"info-item\">\r\n          <div class=\"icon\"><i class=\"el-icon-user\"></i></div>\r\n          <div class=\"item-content\">\r\n            <div class=\"label\">个人资料</div>\r\n            <div class=\"value\">{{ userName }}</div>\r\n            <i class=\"el-icon-arrow-right\"></i>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"info-item\">\r\n          <div class=\"icon\"><i class=\"el-icon-message\"></i></div>\r\n          <div class=\"item-content\">\r\n            <div class=\"label\">邮箱</div>\r\n            <div class=\"value\">{{ userEmail }}</div>\r\n            <i class=\"el-icon-arrow-right\"></i>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"info-item\">\r\n          <div class=\"icon\"><i class=\"el-icon-office-building\"></i></div>\r\n          <div class=\"item-content\">\r\n            <div class=\"label\">医院</div>\r\n            <div class=\"value\">{{ userHospital }}</div>\r\n            <i class=\"el-icon-arrow-right\"></i>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"info-item\">\r\n          <div class=\"icon\"><i class=\"el-icon-first-aid-kit\"></i></div>\r\n          <div class=\"item-content\">\r\n            <div class=\"label\">科室</div>\r\n            <div class=\"value\">{{ userDepartment }}</div>\r\n            <i class=\"el-icon-arrow-right\"></i>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"info-item\">\r\n          <div class=\"icon\"><i class=\"el-icon-s-custom\"></i></div>\r\n          <div class=\"item-content\">\r\n            <div class=\"label\">角色</div>\r\n            <div class=\"value\">{{ userRole }}</div>\r\n            <i class=\"el-icon-arrow-right\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 返回按钮 -->\r\n      <div class=\"action-section\">\r\n        <el-button type=\"primary\" @click=\"goBack\">返回工作台</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { API_BASE_URL } from '@/config/api.config';\r\nimport api from '@/utils/api';\r\n\r\nexport default {\r\n  name: 'UserProfile',\r\n  data() {\r\n    return {\r\n      userId: '',\r\n      userName: '',\r\n      userEmail: '',\r\n      userHospital: '',\r\n      userDepartment: '',\r\n      userRole: '',\r\n      avatarUrl: null,\r\n      isUploading: false\r\n    }\r\n  },\r\n  created() {\r\n    // 从localStorage获取用户信息\r\n    try {\r\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n      this.userId = user.id || '未设置';\r\n      this.userName = user.name || '未设置';\r\n      this.userEmail = user.email || '未设置';\r\n      this.userHospital = user.hospital || '未设置';\r\n      this.userDepartment = user.department || '未设置';\r\n      \r\n      // 转换角色显示\r\n      switch(user.role) {\r\n        case 'ADMIN':\r\n          this.userRole = '管理员';\r\n          break;\r\n        case 'DOCTOR':\r\n          this.userRole = '标注医生';\r\n          break;\r\n        case 'REVIEWER':\r\n          this.userRole = '审核医生';\r\n          break;\r\n        default:\r\n          this.userRole = '未知角色';\r\n      }\r\n      \r\n      // 获取用户头像\r\n      this.fetchUserAvatar();\r\n    } catch (error) {\r\n      console.error('获取用户信息失败:', error);\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      this.$router.push('/app/dashboard');\r\n    },\r\n    triggerFileInput() {\r\n      // 触发文件选择\r\n      this.$refs.fileInput.click();\r\n    },\r\n    handleFileChange(event) {\r\n      const file = event.target.files[0];\r\n      if (!file) return;\r\n      \r\n      // 检查文件类型\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('请选择图片文件');\r\n        return;\r\n      }\r\n      \r\n      // 检查文件大小（限制为2MB）\r\n      if (file.size > 2 * 1024 * 1024) {\r\n        this.$message.error('图片大小不能超过2MB');\r\n        return;\r\n      }\r\n      \r\n      // 上传头像\r\n      this.uploadAvatar(file);\r\n    },\r\n    async uploadAvatar(file) {\r\n      try {\r\n        this.isUploading = true;\r\n        \r\n        // 创建FormData对象\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        \r\n        // 调用API上传头像\r\n        const response = await api.users.uploadAvatar(this.userId, formData);\r\n        \r\n        // 更新头像URL\r\n        this.avatarUrl = API_BASE_URL + response.data.avatarUrl;\r\n        \r\n        this.$message.success('头像上传成功');\r\n      } catch (error) {\r\n        console.error('上传头像失败:', error);\r\n        this.$message.error('上传头像失败: ' + (error.response?.data?.error || error.message || '未知错误'));\r\n      } finally {\r\n        this.isUploading = false;\r\n      }\r\n    },\r\n    async fetchUserAvatar() {\r\n      try {\r\n        // 调用API获取头像URL\r\n        const response = await api.users.getUserAvatar(this.userId);\r\n        \r\n        if (response.data.avatarUrl) {\r\n          this.avatarUrl = API_BASE_URL + response.data.avatarUrl;\r\n        }\r\n      } catch (error) {\r\n        console.error('获取头像失败:', error);\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.profile-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.profile-header {\r\n  background: linear-gradient(135deg, #8a6bff, #6b4fff);\r\n  padding: 40px 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  color: white;\r\n}\r\n\r\n.avatar-container {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  border: 3px solid rgba(255, 255, 255, 0.7);\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  cursor: pointer;\r\n}\r\n\r\n.avatar {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.avatar-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  color: white;\r\n  font-size: 12px;\r\n}\r\n\r\n.avatar-container:hover .avatar-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.avatar-overlay i {\r\n  font-size: 20px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.user-id {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin-top: 5px;\r\n}\r\n\r\n.profile-content {\r\n  flex: 1;\r\n  background-color: white;\r\n  border-top-left-radius: 20px;\r\n  border-top-right-radius: 20px;\r\n  margin-top: -20px;\r\n  padding: 20px;\r\n}\r\n\r\n.info-section {\r\n  background-color: white;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  padding: 15px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.info-item .icon {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 15px;\r\n  color: #6b4fff;\r\n}\r\n\r\n.info-item .item-content {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.info-item .label {\r\n  width: 80px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.info-item .value {\r\n  flex: 1;\r\n  color: #666;\r\n}\r\n\r\n.info-item .el-icon-arrow-right {\r\n  color: #ccc;\r\n}\r\n\r\n.action-section {\r\n  margin-top: 30px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style> ", "import { render } from \"./UserProfile.vue?vue&type=template&id=20073e8e&scoped=true\"\nimport script from \"./UserProfile.vue?vue&type=script&lang=js\"\nexport * from \"./UserProfile.vue?vue&type=script&lang=js\"\n\nimport \"./UserProfile.vue?vue&type=style&index=0&id=20073e8e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-20073e8e\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "onClick", "_cache", "$options", "triggerFileInput", "apply", "arguments", "src", "$data", "avatarUrl", "require", "alt", "_hoisted_3", "_hoisted_4", "_toDisplayString", "userId", "type", "ref", "style", "accept", "onChange", "handleFileChange", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "userName", "_hoisted_10", "_hoisted_11", "_hoisted_12", "userEmail", "_hoisted_13", "_hoisted_14", "_hoisted_15", "userHospital", "_hoisted_16", "_hoisted_17", "_hoisted_18", "userDepartment", "_hoisted_19", "_hoisted_20", "_hoisted_21", "userRole", "_hoisted_22", "_createVNode", "_component_el_button", "goBack", "_withCtx", "_createTextVNode", "_", "__", "name", "data", "isUploading", "created", "user", "JSON", "parse", "localStorage", "getItem", "this", "id", "email", "hospital", "department", "role", "fetchUserAvatar", "error", "console", "methods", "$router", "push", "$refs", "fileInput", "click", "event", "file", "target", "files", "startsWith", "size", "$message", "uploadAvatar", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "formData", "response", "_error$response", "_t", "w", "_context", "n", "p", "FormData", "append", "api", "users", "v", "API_BASE_URL", "success", "message", "f", "a", "_this2", "_callee2", "_t2", "_context2", "getUserAvatar", "__exports__", "render"], "sourceRoot": ""}