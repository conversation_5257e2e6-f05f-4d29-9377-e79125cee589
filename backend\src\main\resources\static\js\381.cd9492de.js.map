{"version": 3, "file": "js/381.cd9492de.js", "mappings": "2NACOA,MAAM,a,GAGJA,MAAM,O,GACJA,MAAM,Y,GACJA,MAAM,mB,EANnB,Y,GAkBWA,MAAM,Y,GAlBjBC,IAAA,EAoB0CD,MAAM,Q,GACjCA,MAAM,iE,GAEJA,MAAM,cAAcE,MAAA,uB,GAYtBF,MAAM,a,GAnCrBC,IAAA,EAoCqCD,MAAM,oB,GApC3CC,IAAA,EA0CuCD,MAAM,sB,GA1C7CC,IAAA,G,GA+CmBD,MAAM,Q,GAKJA,MAAM,mC,GAyCAA,MAAM,0B,EA7FjC,oB,EAAA,Y,GAAAC,IAAA,EAkH6C,aAAW,Q,GACpCD,MAAM,qC,EAnH1B,Y,GAAAC,IAAA,EAwI2CD,MAAM,Q,GAClCA,MAAM,iE,GAEJA,MAAM,cAAcE,MAAA,uB,GAYtBF,MAAM,a,GACJA,MAAM,Q,GAxJvBC,IAAA,EAmKsCD,MAAM,oB,GAnK5CC,IAAA,EAyKwCD,MAAM,sB,GAzK9CC,IAAA,G,GA8KmBD,MAAM,oB,GACFA,MAAM,mC,EA/K7B,Q,GAuN6BA,MAAM,0B,EAvNnC,Y,EAAA,Y,GAAAC,IAAA,EA6O8C,aAAW,Q,GACrCD,MAAM,qC,EA9O1B,Y,GAAAC,IAAA,EAmQ6CD,MAAM,Q,GAIpCA,MAAM,a,GAEFA,MAAM,Q,GAKNA,MAAM,Q,GAKNA,MAAM,+B,GAKNA,MAAM,+B,EAxRzB,a,GAAAC,IAAA,G,GAAAA,IAAA,G,GAAAA,IAAA,EAySyDD,MAAM,Q,GAIhDA,MAAM,a,4CA5SnBG,EAAAA,EAAAA,IAqTM,MArTNC,EAqTM,gBApTJC,EAAAA,EAAAA,IAAiC,MAA7BL,MAAM,aAAY,UAAM,KAE5BK,EAAAA,EAAAA,IAiTM,MAjTNC,EAiTM,EAhTJD,EAAAA,EAAAA,IAWM,MAXNE,EAWM,EAVJF,EAAAA,EAAAA,IASM,MATNG,EASM,gBARJL,EAAAA,EAAAA,IAOSM,EAAAA,GAAA,MAdnBC,EAAAA,EAAAA,IAQmCC,GAAAC,MARnC,SAQoBC,EAAKC,G,kBADfX,EAAAA,EAAAA,IAOS,UALNF,IAAKa,EACLd,OAVbe,EAAAA,EAAAA,IAAA,0CAU+DJ,GAAAK,YAAcH,EAAII,GAAK,SAAW,KACpFC,QAAK,SAAAC,GAAA,OAAER,GAAAK,UAAYH,EAAII,EAAE,G,EAE1BZ,EAAAA,EAAAA,IAAyB,KAArBL,OAbhBe,EAAAA,EAAAA,IAauBF,EAAIO,O,SAb3BC,EAAAA,EAAAA,IAaqC,KAACC,EAAAA,EAAAA,IAAGT,EAAIU,MAAI,OAbjDC,E,eAkBMnB,EAAAA,EAAAA,IAkSM,MAlSNoB,EAkSM,CAhSqB,UAAdd,GAAAK,YAAS,WAApBb,EAAAA,EAAAA,IAiHM,MAjHNuB,EAiHM,EAhHJrB,EAAAA,EAAAA,IAaM,MAbNsB,EAaM,gBAZJtB,EAAAA,EAAAA,IAA0B,MAAtBL,MAAM,QAAO,QAAI,KACrBK,EAAAA,EAAAA,IAUM,MAVNuB,EAUM,WATJvB,EAAAA,EAAAA,IAKC,SA7Bf,sBAAAwB,EAAA,KAAAA,EAAA,YAAAV,GAAA,OAyByBR,GAAAmB,gBAAeX,CAAA,GACxBY,KAAK,OACL/B,MAAM,eACNgC,YAAY,W,iBAHHrB,GAAAmB,oBAKXzB,EAAAA,EAAAA,IAES,UAFDL,MAAM,4BAA4B+B,KAAK,SAAUb,QAAKW,EAAA,KAAAA,EAAA,qBAAEI,GAAAC,aAAAD,GAAAC,YAAAC,MAAAF,GAAAG,UAAW,I,gBACzE/B,EAAAA,EAAAA,IAA4B,KAAzBL,MAAM,gBAAc,iBAI7BK,EAAAA,EAAAA,IAiGM,MAjGNgC,EAiGM,CAhGO1B,GAAA2B,eAAY,WAAvBnC,EAAAA,EAAAA,IAIM,MAJNoC,EAIMV,EAAA,MAAAA,EAAA,MAHJxB,EAAAA,EAAAA,IAEM,OAFDL,MAAM,iBAAiBwC,KAAK,U,EAC/BnC,EAAAA,EAAAA,IAA2C,QAArCL,MAAM,mBAAkB,YAAM,OAIxBW,GAAA8B,YAAS,WAAzBtC,EAAAA,EAAAA,IAEM,MAFNuC,GAEMpB,EAAAA,EAAAA,IADDX,GAAA8B,WAAS,iBAGdtC,EAAAA,EAAAA,IAqFM,MAnIlBwC,EAAA,EA+CctC,EAAAA,EAAAA,IAIM,MAJNuC,EAIM,EAHJvC,EAAAA,EAAAA,IAES,UAFDL,MAAM,kBAAmBkB,QAAKW,EAAA,KAAAA,EAAA,qBAAEI,GAAAY,0BAAAZ,GAAAY,yBAAAV,MAAAF,GAAAG,UAAwB,I,gBAC9D/B,EAAAA,EAAAA,IAAkC,KAA/BL,MAAM,sBAAoB,UAjD/CqB,EAAAA,EAAAA,IAiDoD,oBAGtChB,EAAAA,EAAAA,IA4DQ,QA5DRyC,EA4DQ,gBA3DNzC,EAAAA,EAAAA,IAUQ,eATNA,EAAAA,EAAAA,IAQK,YAPHA,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,OAChB1C,EAAAA,EAAAA,IAAwB,MAApB0C,MAAM,OAAM,QAChB1C,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,OAChB1C,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,OAChB1C,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,OAChB1C,EAAAA,EAAAA,IAAyB,MAArB0C,MAAM,OAAM,SAChB1C,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,UAAE,KAGtB1C,EAAAA,EAAAA,IA+CQ,6BA9CNF,EAAAA,EAAAA,IA6CKM,EAAAA,GAAA,MA9GvBC,EAAAA,EAAAA,IAiEqCC,GAAAqC,OAjErC,SAiE6BC,G,kBAAX9C,EAAAA,EAAAA,IA6CK,MA7CsBF,IAAKgD,EAAKhC,I,EACnCZ,EAAAA,EAAAA,IAAsB,WAAAiB,EAAAA,EAAAA,IAAf2B,EAAKhC,IAAE,IACdZ,EAAAA,EAAAA,IAA4B,WAAAiB,EAAAA,EAAAA,IAArB2B,EAAKC,UAAQ,IACpB7C,EAAAA,EAAAA,IAAyB,WAAAiB,EAAAA,EAAAA,IAAlB2B,EAAKE,OAAK,IACjB9C,EAAAA,EAAAA,IAWK,YAVHA,EAAAA,EAAAA,IASO,QARJL,OAvEzBe,EAAAA,EAAAA,IAAA,SAuEyG,UAATkC,EAAKT,KAAI,YAAiE,aAATS,EAAKT,KAAI,gC,QAO/IS,EAAKT,MAAI,MAGhBnC,EAAAA,EAAAA,IASK,YARHA,EAAAA,EAAAA,IAOO,QANJL,OAnFzBe,EAAAA,EAAAA,IAAA,SAmFgGkC,EAAKG,OAAM,6B,QAKhFH,EAAKG,OAAS,MAAQ,OAAX,MAGlB/C,EAAAA,EAAAA,IAAyC,WAAAiB,EAAAA,EAAAA,IAAlCW,GAAAoB,WAAWJ,EAAKK,YAAS,IAChCjD,EAAAA,EAAAA,IAiBK,YAhBHA,EAAAA,EAAAA,IAeM,MAfNkD,EAeM,EAdJlD,EAAAA,EAAAA,IAMS,UALPL,MAAM,0BACLwD,MAAOP,EAAKG,OAAS,OAAS,OAC9BlC,QAAK,SAAAC,GAAA,OAAEc,GAAAwB,iBAAiBR,EAAI,G,EAE7B5C,EAAAA,EAAAA,IAAsE,KAAlEL,OAnG9Be,EAAAA,EAAAA,IAmGqCkC,EAAKG,OAAS,iBAAmB,uB,WAnGtEM,IAqGwBrD,EAAAA,EAAAA,IAMS,UALPL,MAAM,4BACNwD,MAAM,OACLtC,QAAK,SAAAC,GAAA,OAAEc,GAAA0B,aAAaV,EAAI,G,gBAEzB5C,EAAAA,EAAAA,IAA4B,KAAzBL,MAAM,gBAAc,aA1GjD4D,Q,cAkHyBjD,GAAAkD,eAAiB,IAAH,WAAzB1D,EAAAA,EAAAA,IAgBM,MAhBN2D,EAgBM,EAfJzD,EAAAA,EAAAA,IAcK,KAdL0D,EAcK,EAbH1D,EAAAA,EAAAA,IAEK,MAFAL,OApHvBe,EAAAA,EAAAA,IAAA,aAoHgE,IAApBJ,GAAAqD,gBAAwB,WAAa,M,EAC7D3D,EAAAA,EAAAA,IAA+F,KAA5FL,MAAM,YAAYiE,KAAK,IAAK/C,QAAKW,EAAA,KAAAA,EAAA,IArHxDqC,EAAAA,EAAAA,KAAA,SAAA/C,GAAA,OAqHkEc,GAAAkC,WAAWxD,GAAAqD,gBAAkB,EAAG,QAAN,kBAAgB,QAAG,kBAE7F7D,EAAAA,EAAAA,IAMKM,EAAAA,GAAA,MA7HvBC,EAAAA,EAAAA,IAwHsCC,GAAAkD,gBAxHtC,SAwH2BO,G,kBADTjE,EAAAA,EAAAA,IAMK,MAJFF,IAAKmE,EACLpE,OA1HrBe,EAAAA,EAAAA,IAAA,aA0H0CJ,GAAAqD,kBAAoBI,EAAU,EAAI,SAAW,M,EAEnE/D,EAAAA,EAAAA,IAAiG,KAA9FL,MAAM,YAAYiE,KAAK,IAAK/C,SA5HnDgD,EAAAA,EAAAA,KAAA,SAAA/C,GAAA,OA4HkEc,GAAAkC,WAAWC,EAAU,EAAG,QAAN,kB,QAAmBA,GAAO,EA5H9GC,IAAA,E,WA8HkBhE,EAAAA,EAAAA,IAEK,MAFAL,OA9HvBe,EAAAA,EAAAA,IAAA,aA8H4CJ,GAAAqD,kBAAoBrD,GAAAkD,eAAiB,EAAI,WAAa,M,EAC9ExD,EAAAA,EAAAA,IAA+F,KAA5FL,MAAM,YAAYiE,KAAK,IAAK/C,QAAKW,EAAA,KAAAA,EAAA,IA/HxDqC,EAAAA,EAAAA,KAAA,SAAA/C,GAAA,OA+HkEc,GAAAkC,WAAWxD,GAAAqD,gBAAkB,EAAG,QAAN,kBAAgB,QAAG,SA/H/GM,EAAAA,EAAAA,IAAA,gBAAAA,EAAAA,EAAAA,IAAA,OAwIiC,WAAd3D,GAAAK,YAAS,WAApBb,EAAAA,EAAAA,IAwHM,MAxHNoE,EAwHM,EAvHJlE,EAAAA,EAAAA,IAaM,MAbNmE,EAaM,gBAZJnE,EAAAA,EAAAA,IAA0B,MAAtBL,MAAM,QAAO,QAAI,KACrBK,EAAAA,EAAAA,IAUM,MAVNoE,EAUM,WATJpE,EAAAA,EAAAA,IAKC,SAjJf,sBAAAwB,EAAA,KAAAA,EAAA,YAAAV,GAAA,OA6IyBR,GAAA+D,iBAAgBvD,CAAA,GACzBY,KAAK,OACL/B,MAAM,eACNgC,YAAY,W,iBAHHrB,GAAA+D,qBAKXrE,EAAAA,EAAAA,IAES,UAFDL,MAAM,4BAA4B+B,KAAK,SAAUb,QAAKW,EAAA,KAAAA,EAAA,qBAAEI,GAAA0C,cAAA1C,GAAA0C,aAAAxC,MAAAF,GAAAG,UAAY,I,gBAC1E/B,EAAAA,EAAAA,IAA4B,KAAzBL,MAAM,gBAAc,iBAI7BK,EAAAA,EAAAA,IAwGM,MAxGNuE,EAwGM,EAvGJvE,EAAAA,EAAAA,IASM,MATNwE,EASM,WARJxE,EAAAA,EAAAA,IAOS,UAhKvB,sBAAAwB,EAAA,KAAAA,EAAA,YAAAV,GAAA,OAyJ+BR,GAAAmE,kBAAiB3D,CAAA,GAAEnB,MAAM,qBAAsB+E,SAAMlD,EAAA,KAAAA,EAAA,YAAAV,GAAA,OAAEc,GAAA+C,aAAW,I,gBAzJjGC,EAAAA,EAAAA,IAAA,4UAyJ+BtE,GAAAmE,uBAURnE,GAAAuE,gBAAa,WAAxB/E,EAAAA,EAAAA,IAIM,MAJNgF,EAIMtD,EAAA,MAAAA,EAAA,MAHJxB,EAAAA,EAAAA,IAEM,OAFDL,MAAM,iBAAiBwC,KAAK,U,EAC/BnC,EAAAA,EAAAA,IAA2C,QAArCL,MAAM,mBAAkB,YAAM,OAIxBW,GAAAyE,aAAU,WAA1BjF,EAAAA,EAAAA,IAEM,MAFNkF,GAEM/D,EAAAA,EAAAA,IADDX,GAAAyE,YAAU,iBAGfjF,EAAAA,EAAAA,IAiFM,MA9PlBmF,EAAA,EA8KcjF,EAAAA,EAAAA,IA6DM,MA7DNkF,EA6DM,EA5DJlF,EAAAA,EAAAA,IA2DQ,QA3DRmF,EA2DQ,gBA1DNnF,EAAAA,EAAAA,IAUQ,eATNA,EAAAA,EAAAA,IAQK,YAPHA,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,OAChB1C,EAAAA,EAAAA,IAAwB,MAApB0C,MAAM,OAAM,QAChB1C,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,OAChB1C,EAAAA,EAAAA,IAAwB,MAApB0C,MAAM,OAAM,QAChB1C,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,OAChB1C,EAAAA,EAAAA,IAAyB,MAArB0C,MAAM,OAAM,SAChB1C,EAAAA,EAAAA,IAAuB,MAAnB0C,MAAM,OAAM,UAAE,KAGtB1C,EAAAA,EAAAA,IA8CQ,6BA7CNF,EAAAA,EAAAA,IA4CKM,EAAAA,GAAA,MAxOzBC,EAAAA,EAAAA,IA4LwCC,GAAA8E,QA5LxC,SA4L+BC,G,kBAAXvF,EAAAA,EAAAA,IA4CK,MA5CwBF,IAAKyF,EAAMzE,I,EACtCZ,EAAAA,EAAAA,IAAuB,WAAAiB,EAAAA,EAAAA,IAAhBoE,EAAMzE,IAAE,IACfZ,EAAAA,EAAAA,IAMK,YALHA,EAAAA,EAAAA,IAIE,OAHCsF,IAAG,GAAAC,OAAKjF,GAAAkF,OAAM,gBAAAD,OAAeF,EAAMzE,GAAE,cACtCjB,MAAM,kBACN8F,IAAI,O,OAlM9BC,MAqMsB1F,EAAAA,EAAAA,IAAyB,WAAAiB,EAAAA,EAAAA,IAAlBoE,EAAMnE,MAAI,IACjBlB,EAAAA,EAAAA,IAAiC,WAAAiB,EAAAA,EAAAA,IAA1BoE,EAAMM,cAAY,IACzB3F,EAAAA,EAAAA,IAaK,YAZHA,EAAAA,EAAAA,IAWO,QAVJL,OAzM3Be,EAAAA,EAAAA,IAAA,SAyMkH,aAAZ2E,EAAMO,OAAM,aAA0E,aAAZP,EAAMO,OAAM,YAAyE,cAAZP,EAAMO,OAAM,aAA2E,aAAZP,EAAMO,OAAM,6B,QASnThE,GAAAiE,cAAcR,EAAMO,SAAM,MAGjC5F,EAAAA,EAAAA,IAA2C,WAAAiB,EAAAA,EAAAA,IAApCW,GAAAoB,WAAWqC,EAAMS,aAAU,IAClC9F,EAAAA,EAAAA,IAiBK,YAhBHA,EAAAA,EAAAA,IAeM,MAfN+F,EAeM,EAdJ/F,EAAAA,EAAAA,IAMS,UALPL,MAAM,uBACNwD,MAAM,OACLtC,QAAK,SAAAC,GAAA,OAAEkF,EAAAC,QAAQC,KAAK,WAADX,OAAYF,EAAMzE,IAAE,G,gBAExCZ,EAAAA,EAAAA,IAAyB,KAAtBL,MAAM,aAAW,aA7NhDwG,IA+N0BnG,EAAAA,EAAAA,IAMS,UALPL,MAAM,yBACNwD,MAAM,OACLtC,QAAK,SAAAC,GAAA,OAAEc,GAAAwE,YAAYf,EAAMzE,GAAE,G,gBAE5BZ,EAAAA,EAAAA,IAA2B,KAAxBL,MAAM,eAAa,aApOlD0G,Q,gBA6OyB/F,GAAAgG,gBAAkB,IAAH,WAA1BxG,EAAAA,EAAAA,IAgBM,MAhBNyG,EAgBM,EAfJvG,EAAAA,EAAAA,IAcK,KAdLwG,EAcK,EAbHxG,EAAAA,EAAAA,IAEK,MAFAL,OA/OvBe,EAAAA,EAAAA,IAAA,aA+OiE,IAArBJ,GAAAmG,iBAAyB,WAAa,M,EAC9DzG,EAAAA,EAAAA,IAAiG,KAA9FL,MAAM,YAAYiE,KAAK,IAAK/C,QAAKW,EAAA,KAAAA,EAAA,IAhPxDqC,EAAAA,EAAAA,KAAA,SAAA/C,GAAA,OAgPkEc,GAAAkC,WAAWxD,GAAAmG,iBAAmB,EAAG,SAAN,kBAAiB,QAAG,kBAE/F3G,EAAAA,EAAAA,IAMKM,EAAAA,GAAA,MAxPvBC,EAAAA,EAAAA,IAmPsCC,GAAAgG,iBAnPtC,SAmP2BvC,G,kBADTjE,EAAAA,EAAAA,IAMK,MAJFF,IAAKmE,EACLpE,OArPrBe,EAAAA,EAAAA,IAAA,aAqP0CJ,GAAAmG,mBAAqB1C,EAAU,EAAI,SAAW,M,EAEpE/D,EAAAA,EAAAA,IAAkG,KAA/FL,MAAM,YAAYiE,KAAK,IAAK/C,SAvPnDgD,EAAAA,EAAAA,KAAA,SAAA/C,GAAA,OAuPkEc,GAAAkC,WAAWC,EAAU,EAAG,SAAN,kB,QAAoBA,GAAO,EAvP/G2C,IAAA,E,WAyPkB1G,EAAAA,EAAAA,IAEK,MAFAL,OAzPvBe,EAAAA,EAAAA,IAAA,aAyP4CJ,GAAAmG,mBAAqBnG,GAAAgG,gBAAkB,EAAI,WAAa,M,EAChFtG,EAAAA,EAAAA,IAAiG,KAA9FL,MAAM,YAAYiE,KAAK,IAAK/C,QAAKW,EAAA,MAAAA,EAAA,KA1PxDqC,EAAAA,EAAAA,KAAA,SAAA/C,GAAA,OA0PkEc,GAAAkC,WAAWxD,GAAAmG,iBAAmB,EAAG,SAAN,kBAAiB,QAAG,SA1PjHxC,EAAAA,EAAAA,IAAA,gBAAAA,EAAAA,EAAAA,IAAA,OAmQiC,aAAd3D,GAAAK,YAAS,WAApBb,EAAAA,EAAAA,IAmCM,MAnCN6G,EAmCM,gBAlCJ3G,EAAAA,EAAAA,IAEM,OAFDL,MAAM,eAAa,EACtBK,EAAAA,EAAAA,IAA0B,MAAtBL,MAAM,QAAO,UAAI,KAEvBK,EAAAA,EAAAA,IA8BM,MA9BN4G,EA8BM,EA7BJ5G,EAAAA,EAAAA,IA4BO,QA5BA6G,SAAMrF,EAAA,MAAAA,EAAA,KAxQzBqC,EAAAA,EAAAA,KAAA,kBAwQmCjC,GAAAkF,cAAAlF,GAAAkF,aAAAhF,MAAAF,GAAAG,UAAY,kB,EACjC/B,EAAAA,EAAAA,IAGM,MAHN+G,EAGM,gBAFJ/G,EAAAA,EAAAA,IAAqD,SAA9CgH,IAAI,WAAWrH,MAAM,cAAa,QAAI,cAC7CK,EAAAA,EAAAA,IAAkF,SAA3EY,GAAG,WA3Q1B,sBAAAY,EAAA,MAAAA,EAAA,aAAAV,GAAA,OA2Q8CR,GAAA2G,SAASC,SAAQpG,CAAA,GAAEY,KAAK,OAAO/B,MAAM,gB,iBAArCW,GAAA2G,SAASC,eAGzClH,EAAAA,EAAAA,IAGM,MAHNmH,EAGM,gBAFJnH,EAAAA,EAAAA,IAA+D,SAAxDgH,IAAI,eAAerH,MAAM,cAAa,cAAU,cACvDK,EAAAA,EAAAA,IAA8F,SAAvFY,GAAG,eAhR1B,sBAAAY,EAAA,MAAAA,EAAA,aAAAV,GAAA,OAgRkDR,GAAA2G,SAASG,eAActG,CAAA,GAAEY,KAAK,SAAS/B,MAAM,gB,iBAA7CW,GAAA2G,SAASG,qBAG7CpH,EAAAA,EAAAA,IAGM,MAHNqH,EAGM,WAFJrH,EAAAA,EAAAA,IAAkH,SAA3GY,GAAG,oBApR1B,sBAAAY,EAAA,MAAAA,EAAA,aAAAV,GAAA,OAoRuDR,GAAA2G,SAASK,wBAAuBxG,CAAA,GAAEnB,MAAM,mBAAmB+B,KAAK,Y,iBAAhEpB,GAAA2G,SAASK,2BAAuB,eACvEtH,EAAAA,EAAAA,IAAsE,SAA/DL,MAAM,mBAAmBqH,IAAI,qBAAoB,UAAM,OAGhEhH,EAAAA,EAAAA,IAGM,MAHNuH,EAGM,WAFJvH,EAAAA,EAAAA,IAAqG,SAA9FY,GAAG,iBAzR1B,sBAAAY,EAAA,MAAAA,EAAA,aAAAV,GAAA,OAyRoDR,GAAA2G,SAASO,cAAa1G,CAAA,GAAEnB,MAAM,mBAAmB+B,KAAK,Y,iBAAtDpB,GAAA2G,SAASO,iBAAa,eAC1DxH,EAAAA,EAAAA,IAAmE,SAA5DL,MAAM,mBAAmBqH,IAAI,kBAAiB,UAAM,OAG7DhH,EAAAA,EAAAA,IAMS,UAND0B,KAAK,SAAS/B,MAAM,kBAAmB8H,SAAUnH,GAAAoH,gB,CAC3CpH,GAAAoH,iBAAc,WAA1B5H,EAAAA,EAAAA,IAGO,OAjSvB6H,EAAAnG,EAAA,MAAAA,EAAA,MA+RkBxB,EAAAA,EAAAA,IAAuF,QAAjFL,MAAM,mCAAmCwC,KAAK,SAAS,cAAY,Q,UA/R3FnB,EAAAA,EAAAA,IA+RyG,kBAEzF,WACAlB,EAAAA,EAAAA,IAAwB,OAlSxC8H,EAkS6B,UAAI,EAlSjCC,IAAA,UAAA5D,EAAAA,EAAAA,IAAA,OAySiC,yBAAd3D,GAAAK,YAAS,WAApBb,EAAAA,EAAAA,IAUM,MAVNgI,EAUM,gBATJ9H,EAAAA,EAAAA,IAEM,OAFDL,MAAM,eAAa,EACtBK,EAAAA,EAAAA,IAA4B,MAAxBL,MAAM,QAAO,YAAM,KAEzBK,EAAAA,EAAAA,IAKM,MALN+H,EAKM,gBAJJ/H,EAAAA,EAAAA,IAA6B,SAA1B,0BAAsB,KACzBA,EAAAA,EAAAA,IAES,UAFDL,MAAM,kBAAmBkB,QAAKW,EAAA,MAAAA,EAAA,sBAAEI,GAAAY,0BAAAZ,GAAAY,yBAAAV,MAAAF,GAAAG,UAAwB,I,gBAC9D/B,EAAAA,EAAAA,IAAgC,KAA7BL,MAAM,oBAAkB,UAhTzCqB,EAAAA,EAAAA,IAgT8C,qBAhT9CiD,EAAAA,EAAAA,IAAA,Y,sEA4TA,UACE/C,KAAM,QACN8G,KAAI,WACF,MAAO,CACLxC,OAAQyC,GACRtH,UAAW,QACXJ,KAAM,CACJ,CAAEK,GAAI,QAASM,KAAM,OAAQH,KAAM,gBACnC,CAAEH,GAAI,SAAUM,KAAM,OAAQH,KAAM,gBACpC,CAAEH,GAAI,WAAYM,KAAM,OAAQH,KAAM,cACtC,CAAEH,GAAI,uBAAwBM,KAAM,SAAUH,KAAM,uBAItD4B,MAAO,GACPV,cAAc,EACdG,UAAW,KACXuB,gBAAiB,EACjBH,eAAgB,EAChB/B,gBAAiB,GAGjB2D,OAAQ,GACRP,eAAe,EACfE,WAAY,KACZ0B,iBAAkB,EAClBH,gBAAiB,EACjBjC,iBAAkB,GAClBI,kBAAmB,GAGnBwC,SAAU,CACRC,SAAU,YACVE,eAAgB,GAChBE,yBAAyB,EACzBE,eAAe,GAEjBE,gBAAgB,EAEpB,EACAQ,MAAO,CACLvH,UAAS,SAACwH,GACO,UAAXA,EACFC,KAAKC,aACe,WAAXF,EACTC,KAAKzD,cACe,aAAXwD,EACTC,KAAKE,gBACe,yBAAXH,GAETI,QAAQC,IAAI,aAEhB,GAEFC,QAAO,WACLL,KAAKC,aACLD,KAAKzD,cACLyD,KAAKE,eACP,EACAI,QAAS,CACDL,WAAU,WAAW,IAAAM,EAAA5G,UAAA6G,EAAA,YAAAC,EAAAA,GAAAA,IAAAC,EAAAA,GAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAP,EAAAA,GAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAStB,OATYP,EAAGN,EAAAc,OAAA,QAAAC,IAAAf,EAAA,GAAAA,EAAA,GAAI,EACtBC,EAAK3G,cAAe,EACpB2G,EAAKxG,UAAY,KAAImH,EAAAI,EAAA,EAGbT,EAAS,CACbD,KAAAA,EACAW,KAAM,GACNC,MAAOjB,EAAKnH,iBACb8H,EAAAC,EAAA,EAEsBM,KAAAA,IAAU,GAADvE,OAAIqD,EAAKpD,OAAM,oBAAoB,CAAE0D,OAAAA,IAAS,OAAxEC,EAAOI,EAAAQ,EACbnB,EAAKjG,MAAQwG,EAASnB,KAAKgC,QAC3BpB,EAAKjF,gBAAkBwF,EAASnB,KAAKiC,OACrCrB,EAAKpF,eAAiB2F,EAASnB,KAAKkC,WAAUX,EAAAC,EAAA,eAAAD,EAAAI,EAAA,EAAAN,EAAAE,EAAAQ,EAE9CnB,EAAKxG,UAAY,eAA4B,QAAZgH,EAAAC,EAAIF,gBAAQ,IAAAC,GAAM,QAANA,EAAZA,EAAcpB,YAAI,IAAAoB,OAAA,EAAlBA,EAAoBe,UAAWd,EAAIc,SAAQ,OAEnD,OAFmDZ,EAAAI,EAAA,EAE5Ef,EAAK3G,cAAe,EAAKsH,EAAAa,EAAA,iBAAAb,EAAAc,EAAA,MAAArB,EAAA,qBAlBFH,EAoB3B,EAEMlE,YAAW,WAAW,IAAA2F,EAAAvI,UAAAwI,EAAA,YAAA1B,EAAAA,GAAAA,IAAAC,EAAAA,GAAAA,KAAAC,GAAA,SAAAyB,IAAA,IAAAvB,EAAAC,EAAAC,EAAAsB,EAAAC,EAAA,OAAA5B,EAAAA,GAAAA,KAAAQ,GAAA,SAAAqB,GAAA,eAAAA,EAAAnB,GAAA,OAUvB,OAVaP,EAAGqB,EAAAb,OAAA,QAAAC,IAAAY,EAAA,GAAAA,EAAA,GAAI,EACvBC,EAAK1F,eAAgB,EACrB0F,EAAKxF,WAAa,KAAI4F,EAAAhB,EAAA,EAGdT,EAAS,CACbD,KAAAA,EACAW,KAAM,GACNC,MAAOU,EAAKlG,iBACZuB,OAAQ2E,EAAK9F,mBACdkG,EAAAnB,EAAA,EAEsBM,KAAAA,IAAU,GAADvE,OAAIgF,EAAK/E,OAAM,qBAAqB,CAAE0D,OAAAA,IAAS,OAAzEC,EAAOwB,EAAAZ,EACbQ,EAAKnF,OAAS+D,EAASnB,KAAKgC,QAC5BO,EAAK9D,iBAAmB0C,EAASnB,KAAKiC,OACtCM,EAAKjE,gBAAkB6C,EAASnB,KAAKkC,WAAUS,EAAAnB,EAAA,eAAAmB,EAAAhB,EAAA,EAAAe,EAAAC,EAAAZ,EAE/CQ,EAAKxF,WAAa,eAA4B,QAAZ0F,EAAAC,EAAIvB,gBAAQ,IAAAsB,GAAM,QAANA,EAAZA,EAAczC,YAAI,IAAAyC,OAAA,EAAlBA,EAAoBN,UAAWO,EAAIP,SAAQ,OAEnD,OAFmDQ,EAAAhB,EAAA,EAE7EY,EAAK1F,eAAgB,EAAK8F,EAAAP,EAAA,iBAAAO,EAAAN,EAAA,MAAAG,EAAA,qBAnBF3B,EAqB5B,EAEMP,cAAa,WAAG,IAAAsC,EAAA,YAAA/B,EAAAA,GAAAA,IAAAC,EAAAA,GAAAA,KAAAC,GAAA,SAAA8B,IAAA,IAAA1B,EAAA2B,EAAAC,EAAA,OAAAjC,EAAAA,GAAAA,KAAAQ,GAAA,SAAA0B,GAAA,eAAAA,EAAAxB,GAAA,cAAAwB,EAAArB,EAAA,EAAAqB,EAAAxB,EAAA,EAEKM,KAAAA,IAAU,GAADvE,OAAIqF,EAAKpF,OAAM,wBAAsB,OAA/D2D,EAAO6B,EAAAjB,EACba,EAAK3D,SAAWkC,EAASnB,KAAIgD,EAAAxB,EAAA,eAAAwB,EAAArB,EAAA,EAAAoB,EAAAC,EAAAjB,EAE7Ba,EAAKK,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,QACNyI,QAAS,eAA4B,QAAZW,EAAAC,EAAI5B,gBAAQ,IAAA2B,GAAM,QAANA,EAAZA,EAAc9C,YAAI,IAAA8C,OAAA,EAAlBA,EAAoBX,UAAWY,EAAIZ,WAC5D,cAAAa,EAAAX,EAAA,MAAAQ,EAAA,iBARgBhC,EAUtB,EAEM/B,aAAY,WAAG,IAAAqE,EAAA,YAAAtC,EAAAA,GAAAA,IAAAC,EAAAA,GAAAA,KAAAC,GAAA,SAAAqC,IAAA,IAAAC,EAAAC,EAAA,OAAAxC,EAAAA,GAAAA,KAAAQ,GAAA,SAAAiC,GAAA,eAAAA,EAAA/B,GAAA,OACO,OAA1B2B,EAAKzD,gBAAiB,EAAI6D,EAAA5B,EAAA,EAAA4B,EAAA/B,EAAA,EAGlBM,KAAAA,KAAW,GAADvE,OAAI4F,EAAK3F,OAAM,uBAAuB2F,EAAKlE,UAAS,OACpEkE,EAAKF,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,UACNyI,QAAS,aACToB,EAAA/B,EAAA,eAAA+B,EAAA5B,EAAA,EAAA2B,EAAAC,EAAAxB,EAEFoB,EAAKF,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,QACNyI,QAAS,eAA4B,QAAZkB,EAAAC,EAAInC,gBAAQ,IAAAkC,GAAM,QAANA,EAAZA,EAAcrD,YAAI,IAAAqD,OAAA,EAAlBA,EAAoBlB,UAAWmB,EAAInB,WAC5D,OAEyB,OAFzBoB,EAAA5B,EAAA,EAEFwB,EAAKzD,gBAAiB,EAAK6D,EAAAnB,EAAA,iBAAAmB,EAAAlB,EAAA,MAAAe,EAAA,qBAfVvC,EAiBrB,EAEA/E,WAAU,SAACmF,EAAMvH,GACF,UAATA,EACEuH,GAAQ,GAAKA,EAAOb,KAAK5E,gBAC3B4E,KAAKC,WAAWY,GAEA,WAATvH,GACLuH,GAAQ,GAAKA,EAAOb,KAAK9B,iBAC3B8B,KAAKzD,YAAYsE,EAGvB,EAEApH,YAAW,WACTuG,KAAKC,WAAW,EAClB,EAEA/D,aAAY,WACV8D,KAAKzD,YAAY,EACnB,EAEMvB,iBAAgB,SAACR,GAAM,IAAA4I,EAAA,YAAA3C,EAAAA,GAAAA,IAAAC,EAAAA,GAAAA,KAAAC,GAAA,SAAA0C,IAAA,IAAAC,EAAAC,EAAA,OAAA7C,EAAAA,GAAAA,KAAAQ,GAAA,SAAAsC,GAAA,eAAAA,EAAApC,GAAA,cAAAoC,EAAAjC,EAAA,EAAAiC,EAAApC,EAAA,EAEnBM,KAAAA,KAAW,GAADvE,OAAIiG,EAAKhG,OAAM,qBAAAD,OAAoB3C,EAAKhC,GAAE,mBAAiB,OAC3E4K,EAAKP,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,UACNyI,QAAS,MAAF5E,OAAQ3C,EAAKC,SAAQ,MAAA0C,OAAK3C,EAAKG,OAAS,KAAO,QAIxDyI,EAAKnD,WAAWmD,EAAK7H,iBAAgBiI,EAAApC,EAAA,eAAAoC,EAAAjC,EAAA,EAAAgC,EAAAC,EAAA7B,EAErCyB,EAAKP,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,QACNyI,QAAS,eAA4B,QAAZuB,EAAAC,EAAIxC,gBAAQ,IAAAuC,GAAM,QAANA,EAAZA,EAAc1D,YAAI,IAAA0D,OAAA,EAAlBA,EAAoBvB,UAAWwB,EAAIxB,WAC5D,cAAAyB,EAAAvB,EAAA,MAAAoB,EAAA,iBAduB5C,EAgB7B,EAEMvF,aAAY,SAACV,GAAM,IAAAiJ,EAAA,YAAAhD,EAAAA,GAAAA,IAAAC,EAAAA,GAAAA,KAAAC,GAAA,SAAA+C,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAnD,EAAAA,GAAAA,KAAAQ,GAAA,SAAA4C,GAAA,eAAAA,EAAA1C,GAAA,OAEiE,GAAlFuC,EAAUI,OAAO,QAAD5G,OAAS3C,EAAKC,SAAQ,kCAAkCD,EAAKT,OAE/E4J,IAAW,CAAC,OAAQ,WAAY,SAASK,SAASL,EAAQM,eAAc,CAAAH,EAAA1C,EAAA,eAAA0C,EAAAvC,EAAA,EAAAuC,EAAA1C,EAAA,EAElEM,KAAAA,KAAW,GAADvE,OAAIsG,EAAKrG,OAAM,qBAAAD,OAAoB3C,EAAKhC,GAAE,gBAAgB,CACxEuB,KAAM4J,EAAQM,gBACd,OAEFR,EAAKZ,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,UACNyI,QAAS,MAAF5E,OAAQ3C,EAAKC,SAAQ,aAAA0C,OAAYwG,EAAQM,iBAIlDR,EAAKxD,WAAWwD,EAAKlI,iBAAgBuI,EAAA1C,EAAA,eAAA0C,EAAAvC,EAAA,EAAAsC,EAAAC,EAAAnC,EAErC8B,EAAKZ,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,QACNyI,QAAS,eAA4B,QAAZ6B,EAAAC,EAAI9C,gBAAQ,IAAA6C,GAAM,QAANA,EAAZA,EAAchE,YAAI,IAAAgE,OAAA,EAAlBA,EAAoB7B,UAAW8B,EAAI9B,WAC5D,OAAA+B,EAAA1C,EAAA,eAEKuC,GACTF,EAAKZ,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,QACNyI,QAAS,WAEb,cAAA+B,EAAA7B,EAAA,MAAAyB,EAAA,iBA5BuBjD,EA6BzB,EAEMzC,YAAW,SAACkG,GAAS,IAAAC,EAAA,YAAA1D,EAAAA,GAAAA,IAAAC,EAAAA,GAAAA,KAAAC,GAAA,SAAAyD,IAAA,IAAAC,EAAAC,EAAA,OAAA5D,EAAAA,GAAAA,KAAAQ,GAAA,SAAAqD,GAAA,eAAAA,EAAAnD,GAAA,WACrBoD,QAAQ,uBAAwB,CAAFD,EAAAnD,EAAA,eAAAmD,EAAAhD,EAAA,EAAAgD,EAAAnD,EAAA,EAExBM,KAAAA,UAAa,GAADvE,OAAIgH,EAAK/G,OAAM,sBAAAD,OAAqB+G,IAAU,OAChEC,EAAKtB,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,UACNyI,QAAS,YAIXoC,EAAK5H,YAAY4H,EAAK9F,kBAAiBkG,EAAAnD,EAAA,eAAAmD,EAAAhD,EAAA,EAAA+C,EAAAC,EAAA5C,EAEvCwC,EAAKtB,OAAOC,SAAS,YAAa,CAChCxJ,KAAM,QACNyI,QAAS,aAA0B,QAAZsC,EAAAC,EAAIvD,gBAAQ,IAAAsD,GAAM,QAANA,EAAZA,EAAczE,YAAI,IAAAyE,OAAA,EAAlBA,EAAoBtC,UAAWuC,EAAIvC,WAC1D,cAAAwC,EAAAtC,EAAA,MAAAmC,EAAA,iBAfmB3D,EAkB3B,EAEA7F,WAAU,SAAC6J,GACT,IAAKA,EAAY,MAAO,GACxB,IAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,eAAe,QAC7B,EAEAnH,cAAa,SAACD,GACZ,IAAMqH,EAAY,CAChB,SAAY,MACZ,UAAa,MACb,SAAY,MACZ,SAAY,MACZ,SAAY,OAEd,OAAOA,EAAUrH,IAAWA,CAC9B,EAEApD,yBAAwB,WACtB4F,KAAKnC,QAAQC,KAAK,+BACpB,I,gBCziBJ,MAAMgH,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,K", "sources": ["webpack://medical-annotation-frontend/./src/views/Admin.vue", "webpack://medical-annotation-frontend/./src/views/Admin.vue?98f8"], "sourcesContent": ["<template>\n  <div class=\"container\">\n    <h2 class=\"mt-4 mb-4\">管理员控制台</h2>\n    \n    <div class=\"row\">\n      <div class=\"col-md-3\">\n        <div class=\"list-group mb-4\">\n          <button \n            v-for=\"(tab, index) in tabs\" \n            :key=\"index\"\n            :class=\"['list-group-item list-group-item-action', activeTab === tab.id ? 'active' : '']\"\n            @click=\"activeTab = tab.id\"\n          >\n            <i :class=\"tab.icon\"></i> {{ tab.name }}\n          </button>\n        </div>\n      </div>\n      \n      <div class=\"col-md-9\">\n        <!-- 用户管理 -->\n        <div v-if=\"activeTab === 'users'\" class=\"card\">\n          <div class=\"card-header d-flex justify-content-between align-items-center\">\n            <h5 class=\"mb-0\">用户管理</h5>\n            <div class=\"input-group\" style=\"max-width: 300px;\">\n              <input \n                v-model=\"userSearchQuery\" \n                type=\"text\" \n                class=\"form-control\" \n                placeholder=\"搜索用户...\"\n              >\n              <button class=\"btn btn-outline-secondary\" type=\"button\" @click=\"searchUsers\">\n                <i class=\"bi bi-search\"></i>\n              </button>\n            </div>\n          </div>\n          <div class=\"card-body\">\n            <div v-if=\"loadingUsers\" class=\"text-center my-5\">\n              <div class=\"spinner-border\" role=\"status\">\n                <span class=\"visually-hidden\">加载中...</span>\n              </div>\n            </div>\n            \n            <div v-else-if=\"userError\" class=\"alert alert-danger\">\n              {{ userError }}\n            </div>\n            \n            <div v-else>\n              <div class=\"mb-3\">\n                <button class=\"btn btn-warning\" @click=\"goToReviewerApplications\">\n                  <i class=\"bi bi-person-check\"></i> 查看权限升级申请\n                </button>\n              </div>\n              <table class=\"table table-striped table-hover\">\n                <thead>\n                  <tr>\n                    <th scope=\"col\">ID</th>\n                    <th scope=\"col\">用户名</th>\n                    <th scope=\"col\">邮箱</th>\n                    <th scope=\"col\">角色</th>\n                    <th scope=\"col\">状态</th>\n                    <th scope=\"col\">注册时间</th>\n                    <th scope=\"col\">操作</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr v-for=\"user in users\" :key=\"user.id\">\n                    <td>{{ user.id }}</td>\n                    <td>{{ user.username }}</td>\n                    <td>{{ user.email }}</td>\n                    <td>\n                      <span \n                        :class=\"[\n                          'badge', \n                          user.role === 'ADMIN' ? 'bg-danger' : \n                          user.role === 'REVIEWER' ? 'bg-primary' : \n                          'bg-secondary'\n                        ]\"\n                      >\n                        {{ user.role }}\n                      </span>\n                    </td>\n                    <td>\n                      <span \n                        :class=\"[\n                          'badge', \n                          user.active ? 'bg-success' : 'bg-danger'\n                        ]\"\n                      >\n                        {{ user.active ? '已激活' : '已禁用' }}\n                      </span>\n                    </td>\n                    <td>{{ formatDate(user.createdAt) }}</td>\n                    <td>\n                      <div class=\"btn-group btn-group-sm\">\n                        <button \n                          class=\"btn btn-outline-primary\" \n                          :title=\"user.active ? '禁用用户' : '激活用户'\"\n                          @click=\"toggleUserStatus(user)\"\n                        >\n                          <i :class=\"user.active ? 'bi bi-x-circle' : 'bi bi-check-circle'\"></i>\n                        </button>\n                        <button \n                          class=\"btn btn-outline-secondary\" \n                          title=\"修改角色\"\n                          @click=\"editUserRole(user)\"\n                        >\n                          <i class=\"bi bi-pencil\"></i>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n              \n              <nav v-if=\"userTotalPages > 1\" aria-label=\"用户分页\">\n                <ul class=\"pagination justify-content-center\">\n                  <li :class=\"['page-item', userCurrentPage === 0 ? 'disabled' : '']\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(userCurrentPage - 1, 'users')\">上一页</a>\n                  </li>\n                  <li \n                    v-for=\"pageNum in userTotalPages\" \n                    :key=\"pageNum\"\n                    :class=\"['page-item', userCurrentPage === pageNum - 1 ? 'active' : '']\"\n                  >\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pageNum - 1, 'users')\">{{ pageNum }}</a>\n                  </li>\n                  <li :class=\"['page-item', userCurrentPage === userTotalPages - 1 ? 'disabled' : '']\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(userCurrentPage + 1, 'users')\">下一页</a>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 图像管理 -->\n        <div v-if=\"activeTab === 'images'\" class=\"card\">\n          <div class=\"card-header d-flex justify-content-between align-items-center\">\n            <h5 class=\"mb-0\">图像管理</h5>\n            <div class=\"input-group\" style=\"max-width: 300px;\">\n              <input \n                v-model=\"imageSearchQuery\" \n                type=\"text\" \n                class=\"form-control\" \n                placeholder=\"搜索图像...\"\n              >\n              <button class=\"btn btn-outline-secondary\" type=\"button\" @click=\"searchImages\">\n                <i class=\"bi bi-search\"></i>\n              </button>\n            </div>\n          </div>\n          <div class=\"card-body\">\n            <div class=\"mb-3\">\n              <select v-model=\"imageStatusFilter\" class=\"form-select w-auto\" @change=\"fetchImages()\">\n                <option value=\"\">所有状态</option>\n                <option value=\"UPLOADED\">已上传</option>\n                <option value=\"ANNOTATED\">已标注</option>\n                <option value=\"REVIEWED\">已审核</option>\n                <option value=\"APPROVED\">已批准</option>\n                <option value=\"REJECTED\">已拒绝</option>\n              </select>\n            </div>\n            \n            <div v-if=\"loadingImages\" class=\"text-center my-5\">\n              <div class=\"spinner-border\" role=\"status\">\n                <span class=\"visually-hidden\">加载中...</span>\n              </div>\n            </div>\n            \n            <div v-else-if=\"imageError\" class=\"alert alert-danger\">\n              {{ imageError }}\n            </div>\n            \n            <div v-else>\n              <div class=\"table-responsive\">\n                <table class=\"table table-striped table-hover\">\n                  <thead>\n                    <tr>\n                      <th scope=\"col\">ID</th>\n                      <th scope=\"col\">缩略图</th>\n                      <th scope=\"col\">名称</th>\n                      <th scope=\"col\">上传者</th>\n                      <th scope=\"col\">状态</th>\n                      <th scope=\"col\">上传时间</th>\n                      <th scope=\"col\">操作</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr v-for=\"image in images\" :key=\"image.id\">\n                      <td>{{ image.id }}</td>\n                      <td>\n                        <img \n                          :src=\"`${apiUrl}/api/images/${image.id}/thumbnail`\" \n                          class=\"admin-thumbnail\" \n                          alt=\"缩略图\"\n                        />\n                      </td>\n                      <td>{{ image.name }}</td>\n                      <td>{{ image.uploaderName }}</td>\n                      <td>\n                        <span \n                          :class=\"[\n                            'badge', \n                            image.status === 'APPROVED' ? 'bg-success' : \n                            image.status === 'REJECTED' ? 'bg-danger' : \n                            image.status === 'ANNOTATED' ? 'bg-primary' : \n                            image.status === 'REVIEWED' ? 'bg-info' : \n                            'bg-secondary'\n                          ]\"\n                        >\n                          {{ getStatusText(image.status) }}\n                        </span>\n                      </td>\n                      <td>{{ formatDate(image.uploadTime) }}</td>\n                      <td>\n                        <div class=\"btn-group btn-group-sm\">\n                          <button \n                            class=\"btn btn-outline-info\" \n                            title=\"查看详情\"\n                            @click=\"$router.push(`/images/${image.id}`)\"\n                          >\n                            <i class=\"bi bi-eye\"></i>\n                          </button>\n                          <button \n                            class=\"btn btn-outline-danger\" \n                            title=\"删除图像\"\n                            @click=\"deleteImage(image.id)\"\n                          >\n                            <i class=\"bi bi-trash\"></i>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n              \n              <nav v-if=\"imageTotalPages > 1\" aria-label=\"图像分页\">\n                <ul class=\"pagination justify-content-center\">\n                  <li :class=\"['page-item', imageCurrentPage === 0 ? 'disabled' : '']\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(imageCurrentPage - 1, 'images')\">上一页</a>\n                  </li>\n                  <li \n                    v-for=\"pageNum in imageTotalPages\" \n                    :key=\"pageNum\"\n                    :class=\"['page-item', imageCurrentPage === pageNum - 1 ? 'active' : '']\"\n                  >\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pageNum - 1, 'images')\">{{ pageNum }}</a>\n                  </li>\n                  <li :class=\"['page-item', imageCurrentPage === imageTotalPages - 1 ? 'disabled' : '']\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(imageCurrentPage + 1, 'images')\">下一页</a>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 系统设置 -->\n        <div v-if=\"activeTab === 'settings'\" class=\"card\">\n          <div class=\"card-header\">\n            <h5 class=\"mb-0\">系统设置</h5>\n          </div>\n          <div class=\"card-body\">\n            <form @submit.prevent=\"saveSettings\">\n              <div class=\"mb-3\">\n                <label for=\"siteName\" class=\"form-label\">系统名称</label>\n                <input id=\"siteName\" v-model=\"settings.siteName\" type=\"text\" class=\"form-control\">\n              </div>\n              \n              <div class=\"mb-3\">\n                <label for=\"maxImageSize\" class=\"form-label\">最大图像大小(MB)</label>\n                <input id=\"maxImageSize\" v-model=\"settings.maxImageSizeMB\" type=\"number\" class=\"form-control\">\n              </div>\n              \n              <div class=\"form-check form-switch mb-3\">\n                <input id=\"allowRegistration\" v-model=\"settings.allowPublicRegistration\" class=\"form-check-input\" type=\"checkbox\">\n                <label class=\"form-check-label\" for=\"allowRegistration\">允许公开注册</label>\n              </div>\n              \n              <div class=\"form-check form-switch mb-3\">\n                <input id=\"reviewRequired\" v-model=\"settings.requireReview\" class=\"form-check-input\" type=\"checkbox\">\n                <label class=\"form-check-label\" for=\"reviewRequired\">标注需要审核</label>\n              </div>\n              \n              <button type=\"submit\" class=\"btn btn-primary\" :disabled=\"savingSettings\">\n                <span v-if=\"savingSettings\">\n                  <span class=\"spinner-border spinner-border-sm\" role=\"status\" aria-hidden=\"true\"></span>\n                  保存中...\n                </span>\n                <span v-else>保存设置</span>\n              </button>\n            </form>\n          </div>\n        </div>\n        \n        <!-- 审核医生申请 -->\n        <div v-if=\"activeTab === 'reviewerApplications'\" class=\"card\">\n          <div class=\"card-header\">\n            <h5 class=\"mb-0\">审核医生申请</h5>\n          </div>\n          <div class=\"card-body\">\n            <p>您可以在这里管理标注医生提交的审核医生申请。</p>\n            <button class=\"btn btn-primary\" @click=\"goToReviewerApplications\">\n              <i class=\"bi bi-list-check\"></i> 查看申请列表\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport axios from 'axios';\n\nexport default {\n  name: 'Admin',\n  data() {\n    return {\n      apiUrl: process.env.VUE_APP_API_URL,\n      activeTab: 'users',\n      tabs: [\n        { id: 'users', name: '用户管理', icon: 'bi bi-people' },\n        { id: 'images', name: '图像管理', icon: 'bi bi-images' },\n        { id: 'settings', name: '系统设置', icon: 'bi bi-gear' },\n        { id: 'reviewerApplications', name: '审核医生申请', icon: 'bi bi-person-check' }\n      ],\n      \n      // 用户管理\n      users: [],\n      loadingUsers: false,\n      userError: null,\n      userCurrentPage: 0,\n      userTotalPages: 0,\n      userSearchQuery: '',\n      \n      // 图像管理\n      images: [],\n      loadingImages: false,\n      imageError: null,\n      imageCurrentPage: 0,\n      imageTotalPages: 0,\n      imageSearchQuery: '',\n      imageStatusFilter: '',\n      \n      // 系统设置\n      settings: {\n        siteName: '血管瘤辅助标注系统',\n        maxImageSizeMB: 10,\n        allowPublicRegistration: true,\n        requireReview: true\n      },\n      savingSettings: false\n    };\n  },\n  watch: {\n    activeTab(newTab) {\n      if (newTab === 'users') {\n        this.fetchUsers();\n      } else if (newTab === 'images') {\n        this.fetchImages();\n      } else if (newTab === 'settings') {\n        this.fetchSettings();\n      } else if (newTab === 'reviewerApplications') {\n        // 当切换到审核医生申请选项卡时，加载数据\n        console.log('加载审核医生申请数据');\n      }\n    }\n  },\n  mounted() {\n    this.fetchUsers();\n    this.fetchImages();\n    this.fetchSettings();\n  },\n  methods: {\n    async fetchUsers(page = 0) {\n      this.loadingUsers = true;\n      this.userError = null;\n      \n      try {\n        const params = {\n          page,\n          size: 10,\n          query: this.userSearchQuery\n        };\n        \n        const response = await axios.get(`${this.apiUrl}/api/admin/users`, { params });\n        this.users = response.data.content;\n        this.userCurrentPage = response.data.number;\n        this.userTotalPages = response.data.totalPages;\n      } catch (err) {\n        this.userError = '加载用户列表失败: ' + (err.response?.data?.message || err.message);\n      } finally {\n        this.loadingUsers = false;\n      }\n    },\n    \n    async fetchImages(page = 0) {\n      this.loadingImages = true;\n      this.imageError = null;\n      \n      try {\n        const params = {\n          page,\n          size: 10,\n          query: this.imageSearchQuery,\n          status: this.imageStatusFilter\n        };\n        \n        const response = await axios.get(`${this.apiUrl}/api/admin/images`, { params });\n        this.images = response.data.content;\n        this.imageCurrentPage = response.data.number;\n        this.imageTotalPages = response.data.totalPages;\n      } catch (err) {\n        this.imageError = '加载图像列表失败: ' + (err.response?.data?.message || err.message);\n      } finally {\n        this.loadingImages = false;\n      }\n    },\n    \n    async fetchSettings() {\n      try {\n        const response = await axios.get(`${this.apiUrl}/api/admin/settings`);\n        this.settings = response.data;\n      } catch (err) {\n        this.$store.dispatch('showAlert', {\n          type: 'error',\n          message: '加载系统设置失败: ' + (err.response?.data?.message || err.message)\n        });\n      }\n    },\n    \n    async saveSettings() {\n      this.savingSettings = true;\n      \n      try {\n        await axios.post(`${this.apiUrl}/api/admin/settings`, this.settings);\n        this.$store.dispatch('showAlert', {\n          type: 'success',\n          message: '系统设置保存成功'\n        });\n      } catch (err) {\n        this.$store.dispatch('showAlert', {\n          type: 'error',\n          message: '保存系统设置失败: ' + (err.response?.data?.message || err.message)\n        });\n      } finally {\n        this.savingSettings = false;\n      }\n    },\n    \n    changePage(page, type) {\n      if (type === 'users') {\n        if (page >= 0 && page < this.userTotalPages) {\n          this.fetchUsers(page);\n        }\n      } else if (type === 'images') {\n        if (page >= 0 && page < this.imageTotalPages) {\n          this.fetchImages(page);\n        }\n      }\n    },\n    \n    searchUsers() {\n      this.fetchUsers(0);\n    },\n    \n    searchImages() {\n      this.fetchImages(0);\n    },\n    \n    async toggleUserStatus(user) {\n      try {\n        await axios.post(`${this.apiUrl}/api/admin/users/${user.id}/toggle-status`);\n        this.$store.dispatch('showAlert', {\n          type: 'success',\n          message: `用户 ${user.username} 已${user.active ? '禁用' : '激活'}`\n        });\n        \n        // 刷新用户列表\n        this.fetchUsers(this.userCurrentPage);\n      } catch (err) {\n        this.$store.dispatch('showAlert', {\n          type: 'error',\n          message: '更改用户状态失败: ' + (err.response?.data?.message || err.message)\n        });\n      }\n    },\n    \n    async editUserRole(user) {\n      // 简单实现，实际项目中可以使用模态窗口实现更复杂的界面\n      const newRole = prompt(`请为用户 ${user.username} 选择新角色 (USER, REVIEWER, ADMIN)`, user.role);\n      \n      if (newRole && ['USER', 'REVIEWER', 'ADMIN'].includes(newRole.toUpperCase())) {\n        try {\n          await axios.post(`${this.apiUrl}/api/admin/users/${user.id}/change-role`, {\n            role: newRole.toUpperCase()\n          });\n          \n          this.$store.dispatch('showAlert', {\n            type: 'success',\n            message: `用户 ${user.username} 的角色已更改为 ${newRole.toUpperCase()}`\n          });\n          \n          // 刷新用户列表\n          this.fetchUsers(this.userCurrentPage);\n        } catch (err) {\n          this.$store.dispatch('showAlert', {\n            type: 'error',\n            message: '更改用户角色失败: ' + (err.response?.data?.message || err.message)\n          });\n        }\n      } else if (newRole) {\n        this.$store.dispatch('showAlert', {\n          type: 'error',\n          message: '无效的角色值'\n        });\n      }\n    },\n    \n    async deleteImage(imageId) {\n      if (confirm('确定要删除这张图像吗？此操作不可撤销。')) {\n        try {\n          await axios.delete(`${this.apiUrl}/api/admin/images/${imageId}`);\n          this.$store.dispatch('showAlert', {\n            type: 'success',\n            message: '图像已成功删除'\n          });\n          \n          // 刷新图像列表\n          this.fetchImages(this.imageCurrentPage);\n        } catch (err) {\n          this.$store.dispatch('showAlert', {\n            type: 'error',\n            message: '删除图像失败: ' + (err.response?.data?.message || err.message)\n          });\n        }\n      }\n    },\n    \n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleString('zh-CN');\n    },\n    \n    getStatusText(status) {\n      const statusMap = {\n        'UPLOADED': '已上传',\n        'ANNOTATED': '已标注',\n        'REVIEWED': '已审核',\n        'APPROVED': '已批准',\n        'REJECTED': '已拒绝'\n      };\n      return statusMap[status] || status;\n    },\n    \n    goToReviewerApplications() {\n      this.$router.push('/admin/reviewer-applications');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.admin-thumbnail {\n  width: 60px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n</style> ", "import { render } from \"./Admin.vue?vue&type=template&id=537c82f4&scoped=true\"\nimport script from \"./Admin.vue?vue&type=script&lang=js\"\nexport * from \"./Admin.vue?vue&type=script&lang=js\"\n\nimport \"./Admin.vue?vue&type=style&index=0&id=537c82f4&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-537c82f4\"]])\n\nexport default __exports__"], "names": ["class", "key", "style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_Fragment", "_renderList", "$data", "tabs", "tab", "index", "_normalizeClass", "activeTab", "id", "onClick", "$event", "icon", "_createTextVNode", "_toDisplayString", "name", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_cache", "userSearchQuery", "type", "placeholder", "$options", "searchUsers", "apply", "arguments", "_hoisted_10", "loadingUsers", "_hoisted_11", "role", "userError", "_hoisted_12", "_hoisted_13", "_hoisted_14", "goToReviewerApplications", "_hoisted_15", "scope", "users", "user", "username", "email", "active", "formatDate", "createdAt", "_hoisted_16", "title", "toggleUserStatus", "_hoisted_17", "editUserRole", "_hoisted_18", "userTotalPages", "_hoisted_19", "_hoisted_20", "userCurrentPage", "href", "_withModifiers", "changePage", "pageNum", "_hoisted_21", "_createCommentVNode", "_hoisted_22", "_hoisted_23", "_hoisted_24", "imageSearchQuery", "searchImages", "_hoisted_25", "_hoisted_26", "imageStatusFilter", "onChange", "fetchImages", "_createStaticVNode", "loadingImages", "_hoisted_27", "imageError", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "images", "image", "src", "concat", "apiUrl", "alt", "_hoisted_32", "uploaderName", "status", "getStatusText", "uploadTime", "_hoisted_33", "_ctx", "$router", "push", "_hoisted_34", "deleteImage", "_hoisted_35", "imageTotalPages", "_hoisted_36", "_hoisted_37", "imageCurrentPage", "_hoisted_38", "_hoisted_39", "_hoisted_40", "onSubmit", "saveSettings", "_hoisted_41", "for", "settings", "siteName", "_hoisted_42", "maxImageSizeMB", "_hoisted_43", "allowPublicRegistration", "_hoisted_44", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "savingSettings", "_hoisted_46", "_hoisted_47", "_hoisted_45", "_hoisted_48", "_hoisted_49", "data", "process", "watch", "newTab", "this", "fetchUsers", "fetchSettings", "console", "log", "mounted", "methods", "_arguments", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "page", "params", "response", "_err$response", "_t", "w", "_context", "n", "length", "undefined", "p", "size", "query", "axios", "v", "content", "number", "totalPages", "message", "f", "a", "_arguments2", "_this2", "_callee2", "_err$response2", "_t2", "_context2", "_this3", "_callee3", "_err$response3", "_t3", "_context3", "$store", "dispatch", "_this4", "_callee4", "_err$response4", "_t4", "_context4", "_this5", "_callee5", "_err$response5", "_t5", "_context5", "_this6", "_callee6", "newRole", "_err$response6", "_t6", "_context6", "prompt", "includes", "toUpperCase", "imageId", "_this7", "_callee7", "_err$response7", "_t7", "_context7", "confirm", "dateString", "date", "Date", "toLocaleString", "statusMap", "__exports__", "render"], "sourceRoot": ""}