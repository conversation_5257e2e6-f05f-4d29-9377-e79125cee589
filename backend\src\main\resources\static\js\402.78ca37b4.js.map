{"version": 3, "file": "js/402.78ca37b4.js", "mappings": "gMACOA,MAAM,+B,iFAAXC,EAAAA,EAAAA,IAEM,MAFNC,EAEM,EADJC,EAAAA,EAAAA,IAAwBC,I,gBAO5B,SACEC,KAAM,mBACNC,WAAY,CACVC,qBAAAA,EAAAA,I,eCLJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/AnnotationReview.vue", "webpack://medical-annotation-frontend/./src/views/AnnotationReview.vue?6feb"], "sourcesContent": ["<template>\n  <div class=\"annotation-review-container\">\n    <AnnotationReviewList />\n  </div>\n</template>\n\n<script>\nimport AnnotationReviewList from '@/components/AnnotationReviewList.vue';\n\nexport default {\n  name: 'AnnotationReview',\n  components: {\n    AnnotationReviewList\n  }\n};\n</script>\n\n<style scoped>\n.annotation-review-container {\n  padding: 0;\n  height: 100%;\n  background-color: #f5f7fa;\n}\n\n/* 在SimpleLayout中时填满整个页面 */\n:deep(.simple-layout .annotation-review-container) {\n  padding: 0;\n}\n</style> ", "import { render } from \"./AnnotationReview.vue?vue&type=template&id=11a02caa&scoped=true\"\nimport script from \"./AnnotationReview.vue?vue&type=script&lang=js\"\nexport * from \"./AnnotationReview.vue?vue&type=script&lang=js\"\n\nimport \"./AnnotationReview.vue?vue&type=style&index=0&id=11a02caa&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-11a02caa\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_AnnotationReviewList", "name", "components", "AnnotationReviewList", "__exports__", "render"], "sourceRoot": ""}