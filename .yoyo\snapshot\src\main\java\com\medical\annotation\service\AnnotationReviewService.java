package com.medical.annotation.service;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImageMetadata.Status;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.ImageMetadataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 标注审核服务
 * 处理标注审核相关的业务逻辑
 */
@Service
public class AnnotationReviewService {

    @Autowired
    private ImageMetadataRepository imageMetadataRepository;

    /**
     * 获取待审核的标注列表
     * 根据用户角色和团队关系进行权限过滤：
     * 1. 管理员和审核医生都只能查看无团队的标注和自己团队内的标注
     * 2. 标注医生无权查看待审核标注
     *
     * @param currentUser 当前用户
     * @param pageable 分页参数
     * @return 符合条件的待审核标注列表
     */
    public Page<ImageMetadata> getPendingReviews(User currentUser, Pageable pageable) {
        // 标注医生无权查看待审核标注
        if (currentUser.getRole() == User.Role.DOCTOR) {
            return Page.empty(pageable);
        }

        // 构建查询条件
        Specification<ImageMetadata> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 只查询状态为SUBMITTED的标注
            predicates.add(cb.equal(root.get("status"), Status.SUBMITTED));
            
            // 对于管理员和审核医生，都只能查看无团队的标注和自己团队的标注
            if (currentUser.getTeam() != null) {
                // 用户有团队，可以查看无团队的标注和自己团队的标注
                Predicate noTeam = cb.isNull(root.get("team"));
                Predicate sameTeam = cb.equal(root.get("team"), currentUser.getTeam());
                predicates.add(cb.or(noTeam, sameTeam));
            } else {
                // 用户无团队，只能查看无团队的标注
                predicates.add(cb.isNull(root.get("team")));
            }
            
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        
        return imageMetadataRepository.findAll(spec, pageable);
    }

    /**
     * 判断用户是否有权限审核特定标注
     * @param currentUser 当前用户
     * @param metadata 标注元数据
     * @return 是否有权限审核
     */
    public boolean canReview(User currentUser, ImageMetadata metadata) {
        // 管理员有所有权限
        if (currentUser.getRole() == User.Role.ADMIN) {
            return true;
        }
        
        // 只有审核医生和管理员可以审核
        if (currentUser.getRole() != User.Role.REVIEWER) {
            return false;
        }
        
        // 标注不是SUBMITTED状态，不能审核
        if (metadata.getStatus() != Status.SUBMITTED) {
            return false;
        }
        
        // 对于有团队的标注，必须是同团队审核医生才能审核
        if (metadata.getTeam() != null) {
            return currentUser.getTeam() != null && currentUser.getTeam().getId().equals(metadata.getTeam().getId());
        }
        
        // 无团队的标注，所有审核医生都可以审核
        return true;
    }

    /**
     * 审核标注
     * @param metadataId 标注ID
     * @param approved 是否批准
     * @param reviewNotes 审核备注
     * @param reviewer 审核人
     * @return 审核后的标注元数据
     */
    @Transactional
    public ImageMetadata reviewAnnotation(Long metadataId, boolean approved, String reviewNotes, User reviewer) {
        // 获取标注元数据
        Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findById(metadataId);
        if (!metadataOpt.isPresent()) {
            throw new IllegalArgumentException("标注不存在: " + metadataId);
        }
        
        ImageMetadata metadata = metadataOpt.get();
        
        // 检查审核权限
        if (!canReview(reviewer, metadata)) {
            throw new IllegalStateException("无权审核此标注");
        }
        
        // 更新标注状态和审核信息
        metadata.setStatus(approved ? Status.APPROVED : Status.REJECTED);
        metadata.setReviewedBy(reviewer);
        metadata.setReviewDate(LocalDateTime.now());
        metadata.setReviewNotes(reviewNotes);
        
        // 保存并返回更新后的标注
        return imageMetadataRepository.save(metadata);
    }

    /**
     * 自动完成审核（用于ADMIN和REVIEWER角色自己提交的标注）
     * @param metadata 标注元数据
     * @param user 提交人
     * @return 更新后的标注元数据
     */
    @Transactional
    public ImageMetadata autoApprove(ImageMetadata metadata, User user) {
        if (user.getRole() == User.Role.ADMIN || user.getRole() == User.Role.REVIEWER) {
            metadata.setStatus(Status.APPROVED);
            metadata.setReviewedBy(user);
            metadata.setReviewDate(LocalDateTime.now());
            metadata.setReviewNotes("自动批准（管理员/审核医生）");
            return imageMetadataRepository.save(metadata);
        }
        return metadata;
    }
} 