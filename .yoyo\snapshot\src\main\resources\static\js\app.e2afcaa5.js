(()=>{"use strict";var e={653:(e,t,r)=>{r.r(t),r.d(t,{default:()=>d});var a=r(29357),n=r(54119),s=(r(16280),r(76918),r(28706),r(74423),r(64346),r(44114),r(54743),r(11745),r(38309),r(16573),r(78100),r(77936),r(23288),r(79432),r(26099),r(27495),r(38781),r(21699),r(47764),r(5746),r(11392),r(21489),r(48140),r(81630),r(72170),r(75044),r(69539),r(31694),r(89955),r(21903),r(91134),r(33206),r(44496),r(66651),r(12887),r(19369),r(66812),r(8995),r(31575),r(36072),r(88747),r(28845),r(29423),r(57301),r(373),r(86614),r(41405),r(37467),r(44732),r(33684),r(79577),r(2945),r(62953),r(55815),r(64979),r(79739),r(3296),r(27208),r(48408),r(14603),r(47566),r(98721),r(72505)),o=r.n(s),i=o().create({baseURL:"http://localhost:8085",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:1e4,withCredentials:!0}),u=o().create({baseURL:"http://localhost:8085",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:15e3,withCredentials:!0}),c=o().create({baseURL:"http://localhost:8085",headers:{"Content-Type":"application/json",Accept:"application/json","X-Debug":"true"},timeout:1e4,withCredentials:!0});c.interceptors.request.use((function(e){return e.url.startsWith("/medical")||(e.url="/medical"+e.url),e}),(function(e){return Promise.reject(e)})),c.interceptors.response.use((function(e){return e}),(function(e){return Promise.reject(e)})),u.interceptors.request.use((function(e){e.url.startsWith("/medical")||(e.url="/medical"+e.url);var t=JSON.parse(localStorage.getItem("user"));return t&&(e.headers["X-User-Id"]=t.customId||t.id,e.headers["X-User-Email"]=t.email),e}),(function(e){return Promise.reject(e)})),u.interceptors.response.use((function(e){return e}),(function(e){return e.response,Promise.reject(e)})),i.interceptors.request.use((function(e){e.url.startsWith("/medical")||(e.url="/medical"+e.url);var t=JSON.parse(localStorage.getItem("user"));return t&&(e.headers["X-User-Id"]=t.customId||t.id,e.headers["X-User-Email"]=t.email),e}),(function(e){return Promise.reject(e)})),i.interceptors.response.use((function(e){try{var t=e.config.url;t.includes("/users")&&e.data&&"object"===(0,n.A)(e.data)&&Array.isArray(e.data)}catch(r){}return e}),(function(e){if(e.response&&401===e.response.status){var t=e.config.url&&(e.config.url.includes("/annotations")||e.config.url.includes("/tags")||e.config.url.includes("/image-after-annotation")||e.config.url.includes("/images/")||e.config.url.includes("/structured-form")),r=t||window.location.pathname.includes("/annotations")||window.location.pathname.includes("/cases/structured-form")||window.location.pathname.includes("/cases/form")||sessionStorage.getItem("isNavigatingAfterSave"),a=sessionStorage.getItem("isNavigatingAfterSave"),n=localStorage.getItem("skipAuthCheck");if(a||n||r){a&&sessionStorage.removeItem("isNavigatingAfterSave");var s=sessionStorage.getItem("preservedUser");if(s){localStorage.setItem("user",s);var o=e.config;o._retry=!0;var u=JSON.parse(s);return u&&(o.headers["X-User-Id"]=u.customId||u.id,o.headers["X-User-Email"]=u.email),i(o)}return Promise.reject(new Error("会话已过期，但允许继续标注操作"))}if(localStorage.removeItem("user"),"/login"!==window.location.pathname){var c=window.location.pathname+window.location.search;sessionStorage.setItem("redirectAfterLogin",c),window.location.href="/login"}}return Promise.reject(e)}));var l={auth:{login:function(e){return i.post("/api/users/authenticate",e)},register:function(e){return i.post("/api/users",e)}},stats:{getDashboard:function(e){if(!e)return Promise.reject(new Error("用户ID不能为空"));var t="/api/stats-v2/dashboard/".concat(e);return c.get(t)}},users:{getCurrentUser:function(){return i.get("/api/users/me")},updateCurrentUser:function(e){return i.put("/api/users/me",e)},changePassword:function(e,t){return i.post("/api/users/me/change-password",{oldPassword:e,newPassword:t})},applyForReviewer:function(e){return i.post("/api/users/me/reviewer-application",{reason:e})},getAll:function(){return i.get("/api/users")},getUser:function(e){return i.get("/api/users/".concat(e))},createUser:function(e){return i.post("/api/users",e)},updateUser:function(e,t){return i.put("/api/users/".concat(e),t)},deleteUser:function(e){return i["delete"]("/api/users/".concat(e))},resetPassword:function(e,t){return i.post("/api/users/".concat(e,"/reset-password"),{newPassword:t})},getAllReviewers:function(){return i.get("/api/users/reviewers")},getPendingReviewerApplications:function(){return i.get("/api/users/reviewer-applications")},processReviewerApplication:function(e,t){return i.post("/api/users/reviewer-applications/".concat(e),{approved:t})},getUsersWithoutTeam:function(){return i.get("/api/users/without-team")}},teams:{create:function(e){return i.post("/api/teams",e)},getAll:function(){return i.get("/api/teams")},getOne:function(e){return i.get("/api/teams/".concat(e))},joinTeam:function(e,t){return i.post("/api/teams/".concat(e,"/members"),{userId:t})},applyToJoinTeam:function(e,t){return i.post("/api/team-applications",{teamId:e,reason:t,status:"PENDING"})},getTeamApplications:function(e){return i.get("/api/teams/".concat(e,"/applications"))},processTeamApplication:function(e,t,r){return i.put("/api/team-applications/".concat(e),{action:t,reason:r||""})},getUserApplications:function(e){return i.get("/api/teams/".concat(e,"/applications"))},getTeamMembers:function(e){return i.get("/api/teams/".concat(e,"/members"))},removeTeamMember:function(e,t){return i["delete"]("/api/teams/".concat(e,"/members/").concat(t))}},images:(0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)({getAll:function(){return i.get("/api/images")},getOne:function(e){return i.get("/api/images/".concat(e))},upload:function(e){var t=new FormData;return t.append("file",e),u.post("/api/images/upload",t,{headers:{"Content-Type":"multipart/form-data"}})},saveToPath:function(e){return u.post("/api/images/save-to-path",e,{headers:{"Content-Type":"multipart/form-data"}})},saveAnnotatedImage:function(e,t){var r=new FormData;return r.append("file",t),r.append("originalImageId",e),r.append("type","processed"),u.post("/api/images/save-processed",r,{headers:{"Content-Type":"multipart/form-data"}})},delete:function(e){return i["delete"]("/api/images/".concat(e))},update:function(e,t){return i.put("/api/images/".concat(e),t)},submitForReview:function(e){return i.post("/api/images/".concat(e,"/submit"))},reviewImage:function(e,t,r){return i.post("/api/images/".concat(e,"/review"),{approved:t,reviewNotes:r})}},"saveAnnotatedImage",(function(e,t){var r=new FormData;return r.append("file",t),u.post("/api/images/".concat(e,"/annotate"),r,{headers:{"Content-Type":"multipart/form-data"}})})),"deleteAnnotatedImage",(function(e){return i["delete"]("/api/images/annotated",{params:{path:e}})})),"getUserImages",(function(e){var t=e?{status:e}:{};return i.get("/api/images/my-images",{params:t})})),"getTeamImages",(function(){return i.get("/api/images/team-images")})),"getPendingReviewImages",(function(){return i.get("/api/images/pending-review")})),"getReviewedImages",(function(){return i.get("/api/images/reviewed")})),"saveStructuredFormData",(function(e,t){return i.put("/api/images/".concat(e,"/structured-form"),t)})),"updateStructuredFormData",(function(e,t){return i.put("/api/images/".concat(e,"/structured-form"),t)})),"markAsAnnotated",(function(e){return i.put("/api/images/".concat(e,"/mark-annotated"))})),"updateStatus",(function(e,t){return i.put("/api/images/".concat(e,"/status"),null,{params:{status:t}})})),tags:{getByImageId:function(e){if(e&&e.toString().length>9){var t=e.toString();return i.get("/api/tags/image/".concat(t))}return i.get("/api/tags/image/".concat(e))},create:function(e){return i.post("/api/tags",e)},update:function(e,t){return i.put("/api/tags/".concat(e),t)},delete:function(e){return i["delete"]("/api/tags/".concat(e))},deleteByImageId:function(e){return i["delete"]("/api/tags/image/".concat(e))},getByUserId:function(e){return i.get("/api/tags/user/".concat(e))},saveAnnotatedImage:function(e){return i.post("/api/tags/save-image-after-annotation",{metadata_id:e})}},annotations:{saveAnnotatedImage:function(e){return i.post("/api/annotations/".concat(e))},updateAnnotatedImage:function(e,t){return i.put("/api/annotations/".concat(e,"/").concat(t))},saveAnnotatedImageToLocal:function(e,t){for(var r=atob(t.split(",")[1]),a=[],n=0;n<r.length;n++)a.push(r.charCodeAt(n));var s=new Blob([new Uint8Array(a)],{type:"image/png"}),o=document.createElement("a");o.href=URL.createObjectURL(s),o.download="annotated_".concat(e,".png"),document.body.appendChild(o),o.click(),document.body.removeChild(o)},generateAnnotatedImage:function(e){return i.post("/api/annotations/generate/".concat(e))}},imagePairs:{getAll:function(){return i.get("/api/image-pairs")},getByMetadataId:function(e){if(e&&e.toString().length>9){var t=e.toString();return i.get("/api/image-pairs/by-metadata?id=".concat(t))}return i.get("/api/image-pairs/metadata/".concat(e))},getOne:function(e){return i.get("/api/image-pairs/".concat(e))},create:function(e){return i.post("/api/image-pairs",e)},update:function(e,t){return i.put("/api/image-pairs/".concat(e),t)},delete:function(e){return i["delete"]("/api/image-pairs/".concat(e))},deleteByMetadataId:function(e){return i["delete"]("/api/image-pairs/metadata/".concat(e))},deleteAnnotatedImage:function(e){return i["delete"]("/api/annotations/".concat(e))}}};const d=l},61561:(e,t,r)=>{var a=r(78676),n=(r(23792),r(3362),r(69085),r(9391),r(16280),r(76918),r(23288),r(62010),r(5506),r(79432),r(26099),r(76031),r(53751)),s=r(20641);function o(e,t,r,a,n,o){var i=(0,s.g2)("router-view"),u=(0,s.g2)("stagewise-toolbar");return(0,s.uX)(),(0,s.CE)(s.FK,null,[(0,s.bF)(i),n.isDevelopment?((0,s.uX)(),(0,s.Wv)(u,{key:0,config:n.stagewiseConfig},null,8,["config"])):(0,s.Q3)("",!0)],64)}var i=r(13421);const u={name:"App",components:{StagewiseToolbar:i.G},data:function(){return{isDevelopment:!1,stagewiseConfig:{plugins:[]}}}};var c=r(66262);const l=(0,c.A)(u,[["render",o]]),d=l;r(28706),r(74423),r(15086),r(60739),r(18111),r(13579),r(33110),r(21699),r(47764),r(62953);var p=r(75220),m=(r(44114),r(90033)),f={class:"header-left"},g={class:"header-right"},h={class:"user-info"},v={class:"role-tag"};function b(e,t,r,a,n,o){var i=(0,s.g2)("HomeFilled"),u=(0,s.g2)("el-icon"),c=(0,s.g2)("el-menu-item"),l=(0,s.g2)("Document"),d=(0,s.g2)("Check"),p=(0,s.g2)("UserFilled"),b=(0,s.g2)("User"),A=(0,s.g2)("Setting"),I=(0,s.g2)("el-menu"),E=(0,s.g2)("el-aside"),C=(0,s.g2)("el-button"),w=(0,s.g2)("el-avatar"),k=(0,s.g2)("el-dropdown-item"),S=(0,s.g2)("el-dropdown-menu"),_=(0,s.g2)("el-dropdown"),y=(0,s.g2)("el-header"),T=(0,s.g2)("router-view"),D=(0,s.g2)("el-main"),L=(0,s.g2)("el-container");return(0,s.uX)(),(0,s.Wv)(L,{class:"layout-container"},{default:(0,s.k6)((function(){return[(0,s.bF)(E,{width:"200px",class:"sidebar"},{default:(0,s.k6)((function(){return[t[13]||(t[13]=(0,s.Lk)("div",{class:"logo"},"医生标注平台",-1)),(0,s.bF)(I,{"default-active":o.activeMenu,class:"sidebar-menu","background-color":"#001529","text-color":"#fff","active-text-color":"#409EFF"},{default:(0,s.k6)((function(){return[(0,s.bF)(c,{index:"/app/dashboard",onClick:t[0]||(t[0]=function(t){return e.$router.push("/app/dashboard")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(i)]})),_:1}),t[7]||(t[7]=(0,s.Lk)("span",null,"工作台",-1))]})),_:1,__:[7]}),(0,s.bF)(c,{index:"/app/cases",onClick:t[1]||(t[1]=function(t){return e.$router.push("/app/cases")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(l)]})),_:1}),t[8]||(t[8]=(0,s.Lk)("span",null,"病例标注",-1))]})),_:1,__:[8]}),e.isAdmin||e.isReviewer?((0,s.uX)(),(0,s.Wv)(c,{key:0,index:"/app/review",onClick:t[2]||(t[2]=function(t){return e.$router.push("/app/review")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(d)]})),_:1}),t[9]||(t[9]=(0,s.Lk)("span",null,"标注审核",-1))]})),_:1,__:[9]})):(0,s.Q3)("",!0),(0,s.bF)(c,{index:"/app/teams",onClick:t[3]||(t[3]=function(t){return e.$router.push("/app/teams")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(p)]})),_:1}),t[10]||(t[10]=(0,s.Lk)("span",null,"我的团队",-1))]})),_:1,__:[10]}),e.isAdmin?((0,s.uX)(),(0,s.Wv)(c,{key:1,index:"/app/users",onClick:t[4]||(t[4]=function(t){return e.$router.push("/app/users")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(b)]})),_:1}),t[11]||(t[11]=(0,s.Lk)("span",null,"部门成员",-1))]})),_:1,__:[11]})):(0,s.Q3)("",!0),e.isAdmin?((0,s.uX)(),(0,s.Wv)(c,{key:2,index:"/admin",onClick:t[5]||(t[5]=function(t){return e.$router.push("/admin")})},{default:(0,s.k6)((function(){return[(0,s.bF)(u,null,{default:(0,s.k6)((function(){return[(0,s.bF)(A)]})),_:1}),t[12]||(t[12]=(0,s.Lk)("span",null,"系统管理",-1))]})),_:1,__:[12]})):(0,s.Q3)("",!0)]})),_:1},8,["default-active"])]})),_:1,__:[13]}),(0,s.bF)(L,null,{default:(0,s.k6)((function(){return[(0,s.bF)(y,{height:"60px",class:"header"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",f,[n.showDashboardButton?((0,s.uX)(),(0,s.Wv)(C,{key:0,type:"primary",icon:"el-icon-s-home",onClick:t[6]||(t[6]=function(t){return e.$router.push("/app/dashboard")})},{default:(0,s.k6)((function(){return t[14]||(t[14]=[(0,s.eW)("返回工作台")])})),_:1,__:[14]})):(0,s.Q3)("",!0),n.showBackButton?((0,s.uX)(),(0,s.Wv)(C,{key:1,type:"info",icon:"el-icon-back",onClick:o.goBack},{default:(0,s.k6)((function(){return t[15]||(t[15]=[(0,s.eW)("返回上一步")])})),_:1,__:[15]},8,["onClick"])):(0,s.Q3)("",!0)]),(0,s.Lk)("div",g,[(0,s.bF)(_,null,{dropdown:(0,s.k6)((function(){return[(0,s.bF)(S,null,{default:(0,s.k6)((function(){return[(0,s.bF)(k,{onClick:o.handleLogout},{default:(0,s.k6)((function(){return t[16]||(t[16]=[(0,s.eW)("退出登录")])})),_:1,__:[16]},8,["onClick"])]})),_:1})]})),default:(0,s.k6)((function(){return[(0,s.Lk)("span",h,[(0,s.bF)(w,{size:"small",icon:"el-icon-user"}),(0,s.eW)(" "+(0,m.v_)(n.username)+" ",1),(0,s.Lk)("span",v,(0,m.v_)(o.userRoleText),1)])]})),_:1})])]})),_:1}),(0,s.bF)(D,null,{default:(0,s.k6)((function(){return[(0,s.bF)(T)]})),_:1})]})),_:1})]})),_:1})}var A=r(41034),I=r(40834),E=r(48548);const C={name:"MainLayout",components:{HomeFilled:E.HomeFilled,Document:E.Document,Check:E.Check,User:E.User,UserFilled:E.UserFilled,Setting:E.Setting},data:function(){return{username:"标注医生",showBackButton:!1,showDashboardButton:!1}},computed:(0,A.A)((0,A.A)({},(0,I.L8)({currentUser:"getUser",isAdmin:"isAdmin",isDoctor:"isDoctor",isReviewer:"isReviewer"})),{},{activeMenu:function(){return this.$route.path},userRoleText:function(){return this.isAdmin?"管理员":this.isDoctor?"标注医生":this.isReviewer?"审核医生":"未知角色"}}),watch:{$route:function(e){this.showBackButton="/app/cases/structured-form"===e.path,this.showDashboardButton="/app/dashboard"!==e.path}},created:function(){var e=this,t=JSON.parse(localStorage.getItem("user")||"{}");this.username=t.name||"标注医生",this.currentUser&&this.currentUser.name&&(this.username=this.currentUser.name),this.$store.watch((function(e){return e.auth.user}),(function(t){t&&t.name&&(e.username=t.name)})),this.showBackButton="/app/cases/structured-form"===this.$route.path,this.showDashboardButton="/app/dashboard"!==this.$route.path},methods:{handleLogout:function(){sessionStorage.removeItem("isAppOperation"),sessionStorage.removeItem("isNavigatingAfterSave"),sessionStorage.removeItem("returningToWorkbench"),sessionStorage.setItem("isLogoutOperation","true"),localStorage.removeItem("user"),this.$router.push("/login").then((function(){}))["catch"]((function(e){window.location.href="/login"})),setTimeout((function(){sessionStorage.removeItem("isLogoutOperation")}),3e3)},goBack:function(){var e=this;this.$confirm("返回上一页将可能丢失当前未保存的数据，是否确认返回？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$router.go(-1)}))["catch"]((function(){}))}}},w=(0,c.A)(C,[["render",b],["__scopeId","data-v-61e49fb0"]]),k=w;r(34782);var S={class:"dashboard"},_={class:"stat-item"},y={class:"stat-value"},T={class:"stat-item"},D={class:"stat-value"},L={class:"stat-item"},R={class:"stat-value"},O={class:"stat-item"},N={class:"stat-value"},x={class:"stat-item"},F={class:"stat-value"},P={class:"recent-tasks"},U={class:"table-header"},W={key:0},V={key:1},M={key:2},j={key:0,class:"filter-row"},B={key:1,class:"loading-container"},$={class:"loading-spinner"},q={key:2},X={key:1,class:"empty-container"};function J(e,t,r,a,n,o){var i=(0,s.g2)("el-card"),u=(0,s.g2)("el-col"),c=(0,s.g2)("el-row"),l=(0,s.g2)("el-button"),d=(0,s.g2)("el-option"),p=(0,s.g2)("el-select"),f=(0,s.g2)("el-form-item"),g=(0,s.g2)("el-form"),h=(0,s.g2)("loading"),v=(0,s.g2)("el-icon"),b=(0,s.g2)("el-table-column"),A=(0,s.g2)("el-tag"),I=(0,s.g2)("el-table"),E=(0,s.g2)("el-empty"),C=(0,s.gN)("permission"),w=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)("div",S,[(0,s.bF)(c,{gutter:15,class:"stat-cards"},{default:(0,s.k6)((function(){return[(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[0]||(t[0]=function(e){return o.navigateTo("/app/cases")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",_,[t[6]||(t[6]=(0,s.Lk)("div",{class:"stat-title"},"病例总数",-1)),(0,s.Lk)("div",y,(0,m.v_)(n.dashboardStats.totalCount),1)])]})),_:1})]})),_:1}),(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[1]||(t[1]=function(e){return o.navigateTo("/app/cases","DRAFT")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",T,[t[7]||(t[7]=(0,s.Lk)("div",{class:"stat-title"},"未标注",-1)),(0,s.Lk)("div",D,(0,m.v_)(n.dashboardStats.draftCount),1)])]})),_:1})]})),_:1}),(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[2]||(t[2]=function(e){return o.navigateTo("/app/cases","REVIEWED")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",L,[t[8]||(t[8]=(0,s.Lk)("div",{class:"stat-title"},"已标注",-1)),(0,s.Lk)("div",R,(0,m.v_)(n.dashboardStats.reviewedCount),1)])]})),_:1})]})),_:1}),(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[3]||(t[3]=function(e){return o.navigateTo("/app/cases","SUBMITTED")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",O,[t[9]||(t[9]=(0,s.Lk)("div",{class:"stat-title"},"待审核",-1)),(0,s.Lk)("div",N,(0,m.v_)(n.dashboardStats.submittedCount),1)])]})),_:1})]})),_:1}),(0,s.bF)(u,{xs:12,sm:8,md:6,lg:4,xl:4},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{shadow:"hover",onClick:t[4]||(t[4]=function(e){return o.navigateTo("/app/cases","APPROVED")}),class:"clickable-card"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",x,[t[10]||(t[10]=(0,s.Lk)("div",{class:"stat-title"},"已通过",-1)),(0,s.Lk)("div",F,(0,m.v_)(n.dashboardStats.approvedCount),1)])]})),_:1})]})),_:1})]})),_:1}),(0,s.Lk)("div",P,[(0,s.Lk)("div",U,[(0,s.Lk)("h2",null,[e.isAdmin?((0,s.uX)(),(0,s.CE)("span",W,"最近标注任务")):e.isReviewer?((0,s.uX)(),(0,s.CE)("span",V,"待审核的任务")):((0,s.uX)(),(0,s.CE)("span",M,"最近标注任务"))]),(0,s.bo)(((0,s.uX)(),(0,s.Wv)(l,{type:"primary",onClick:o.handleNewAnnotation},{default:(0,s.k6)((function(){return t[11]||(t[11]=[(0,s.eW)(" 新建标注 ")])})),_:1,__:[11]},8,["onClick"])),[[C,"create_case"]])]),e.isAdmin||e.isReviewer?((0,s.uX)(),(0,s.CE)("div",j,[(0,s.bF)(g,{inline:!0,class:"filter-form"},{default:(0,s.k6)((function(){return[(0,s.bF)(f,{label:"状态"},{default:(0,s.k6)((function(){return[(0,s.bF)(p,{modelValue:n.filterStatus,"onUpdate:modelValue":t[5]||(t[5]=function(e){return n.filterStatus=e}),placeholder:"选择状态",clearable:"",onChange:o.handleFilterChange},{default:(0,s.k6)((function(){return[(0,s.bF)(d,{label:"未标注",value:"DRAFT"}),(0,s.bF)(d,{label:"已标注",value:"REVIEWED"}),(0,s.bF)(d,{label:"待审核",value:"SUBMITTED"}),(0,s.bF)(d,{label:"已通过",value:"APPROVED"}),(0,s.bF)(d,{label:"已驳回",value:"REJECTED"})]})),_:1},8,["modelValue","onChange"])]})),_:1}),(0,s.bF)(f,null,{default:(0,s.k6)((function(){return[(0,s.bF)(l,{type:"primary",onClick:o.handleFilterChange},{default:(0,s.k6)((function(){return t[12]||(t[12]=[(0,s.eW)("查询")])})),_:1,__:[12]},8,["onClick"]),(0,s.bF)(l,{onClick:o.resetFilter},{default:(0,s.k6)((function(){return t[13]||(t[13]=[(0,s.eW)("重置")])})),_:1,__:[13]},8,["onClick"])]})),_:1})]})),_:1})])):(0,s.Q3)("",!0),e.loading?((0,s.uX)(),(0,s.CE)("div",B,[(0,s.Lk)("div",$,[(0,s.bF)(v,{class:"is-loading"},{default:(0,s.k6)((function(){return[(0,s.bF)(h)]})),_:1}),t[14]||(t[14]=(0,s.Lk)("span",null,"加载中...",-1))])])):((0,s.uX)(),(0,s.CE)("div",q,[n.recentTasks.length>0?(0,s.bo)(((0,s.uX)(),(0,s.Wv)(I,{key:0,data:n.recentTasks.slice(0,3),style:{width:"100%"}},{default:(0,s.k6)((function(){return[(0,s.bF)(b,{prop:"caseId",label:"病例编号",width:"150"}),(0,s.bF)(b,{prop:"department",label:"部位"}),(0,s.bF)(b,{prop:"type",label:"类型"}),(0,s.bF)(b,{prop:"status",label:"状态"},{default:(0,s.k6)((function(e){return[(0,s.bF)(A,{type:o.getStatusType(e.row.status)},{default:(0,s.k6)((function(){return[(0,s.eW)((0,m.v_)(e.row.status),1)]})),_:2},1032,["type"])]})),_:1}),(0,s.bF)(b,{prop:"createTime",label:"创建时间"}),(0,s.bF)(b,{label:"操作",width:"220"},{default:(0,s.k6)((function(e){return[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(l,{link:"",size:"small",onClick:function(t){return o.handleEdit(e.row)}},{default:(0,s.k6)((function(){return t[15]||(t[15]=[(0,s.eW)(" 编辑 ")])})),_:2,__:[15]},1032,["onClick"])),[[C,"edit_case"]]),(0,s.bF)(l,{link:"",size:"small",onClick:function(t){return o.handleView(e.row)}},{default:(0,s.k6)((function(){return t[16]||(t[16]=[(0,s.eW)(" 查看 ")])})),_:2,__:[16]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[w,n.tableLoading]]):((0,s.uX)(),(0,s.CE)("div",X,[(0,s.bF)(E,{description:e.isReviewer?"暂无需要审核的标注任务":"暂无最近标注任务"},null,8,["description"])]))]))])])}var G=r(14048),H=r(30388),Q=(r(2008),r(64346),r(62062),r(26910),r(1688),r(22489),r(61701),r(58940),r(27495),r(25440),r(11392),r(69553)),K=r(38221),z=r.n(K),Y=r(653),Z=r(72505),ee=r.n(Z);const te={name:"Dashboard",components:{Loading:E.Loading},data:function(){return{recentTasks:[],myTasks:[],needReviewTasks:[],filterStatus:"",currentPage:1,pageSize:10,tableLoading:!1,dashboardStats:{totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},refreshInterval:null}},computed:(0,A.A)((0,A.A)({},(0,I.L8)({images:"getAllImages",loading:"isLoading",error:"getError",isAdmin:"isAdmin",isDoctor:"isDoctor",isReviewer:"isReviewer",hasPermission:"hasPermission",currentUserId:"getUserId",canAccessResource:"canAccessResource"})),{},{stats:function(){return this.dashboardStats},filteredTasks:function(){var e=[];if(e=this.isAdmin?this.recentTasks:this.isReviewer&&this.hasPermission(Q.Jj.REVIEW_CASES)?this.needReviewTasks:this.myTasks,this.filterStatus){var t={DRAFT:"待标注",SUBMITTED:"已标注",PENDING:"审核中",REVIEWED:"审核中",APPROVED:"已通过",REJECTED:"已驳回"},r=t[this.filterStatus];if(r)return e.filter((function(e){return e.status===r}))}return e},displayTasks:function(){var e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredTasks.slice(e,t)},totalTasks:function(){return this.filteredTasks.length}}),methods:(0,A.A)((0,A.A)({},(0,I.i0)(["fetchImages","resetState"])),{},{navigateTo:function(e,t){var r=e.startsWith("/app")?e:e.replace("/cases","/app/cases"),a=t;if(t)switch(t){case"REVIEWED":a="REVIEWED";break;case"SUBMITTED":a="SUBMITTED";break;case"APPROVED":a="APPROVED";break;case"REJECTED":a="REJECTED";break;case"DRAFT":a="DRAFT";break;default:a=t}this.$router.push({path:r,query:t?{status:a}:{}})},handleNewAnnotation:function(){this.$router.push("/app/cases/new")},handleEdit:function(e){this.canEditCase(e)?(localStorage.setItem("isEditingCase","true"),this.$router.push({path:"/app/cases/form",query:{imageId:e.id,edit:"true"}})):this.$message.error("您没有权限编辑此病例")},handleView:function(e){this.canViewCase(e)?this.$router.push("/app/cases/view/".concat(e.id)):this.$message.error("您没有权限查看此病例")},handleDelete:function(e){var t=this;this.canDeleteCase(e)?this.$confirm("此操作将永久删除该病例及其所有相关数据，是否继续?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a=t.$loading({lock:!0,text:"删除中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Promise.resolve().then(r.bind(r,653)).then((function(r){r["default"].images["delete"](e.id).then((function(){a.close(),t.$message.success("删除成功"),t.fetchDashboardData()}))["catch"]((function(e){a.close();var r="删除失败";e.response&&e.response.data?r="string"===typeof e.response.data?e.response.data:JSON.stringify(e.response.data):r+=": "+(e.message||"未知错误"),t.$message({type:"error",message:r,duration:5e3})}))}))}))["catch"]((function(){t.$message.info("已取消删除")})):this.$message.error("您没有权限删除此病例")},handlePageChange:function(e){this.currentPage=e},handleFilterChange:z()((function(){this.currentPage=1}),300),resetFilter:function(){this.filterStatus="",this.handleFilterChange()},canEditCase:function(e){return!0},canViewCase:function(e){return!0},canDeleteCase:function(e){return!!this.isAdmin&&this.hasPermission(Q.Jj.DELETE_CASE)},getStatusType:function(e){var t={待标注:"info",已标注:"success",审核中:"warning",已通过:"success",已驳回:"danger",未知:"info"};return t[e]||"info"},getStatusText:function(e){if(!e)return"未知";var t={DRAFT:"未标注",REVIEWED:"已标注",SUBMITTED:"待审核",PENDING:"待审核",APPROVED:"已通过",REJECTED:"已驳回","":"未知"};return t[e]||"未知"},getStatusValue:function(e){var t={未标注:"DRAFT",待标注:"DRAFT",已标注:"REVIEWED",待审核:"SUBMITTED",审核中:"SUBMITTED",已通过:"APPROVED",已驳回:"REJECTED",未知:""},r=t[e]||"";return r},fetchDashboardData:function(){var e=this;return(0,H.A)((0,G.A)().mark((function t(){var r,a,n,s;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=2,e.tableLoading=!0,r=e.currentUserId||localStorage.getItem("userId")||sessionStorage.getItem("userId")||"1",r){t.next=9;break}return e.$message.error("找不到用户ID，请重新登录"),t.abrupt("return",Promise.reject("找不到用户ID"));case 9:return t.prev=11,t.next=15,Y["default"].stats.getDashboard(r);case 15:if(a=t.sent,n=a.data,Array.isArray(n)&&n.length>0&&(n=n[0]),!n){t.next=34;break}return e.dashboardStats={totalCount:parseInt(n.totalCount||0),draftCount:parseInt(n.draftCount||0),reviewedCount:parseInt(n.reviewedCount||0),submittedCount:parseInt(n.submittedCount||0),approvedCount:parseInt(n.approvedCount||0),rejectedCount:parseInt(n.rejectedCount||0)},e.$forceUpdate(),t.next=33,e.loadTasksList(r);case 33:return t.abrupt("return",Promise.resolve(e.dashboardStats));case 34:t.next=44;break;case 36:return t.prev=36,t.t0=t["catch"](11),s={totalCount:14,draftCount:6,reviewedCount:4,submittedCount:4,approvedCount:0,rejectedCount:0},e.dashboardStats=s,e.$forceUpdate(),t.abrupt("return",Promise.resolve(e.dashboardStats));case 44:t.next=50;break;case 46:return t.prev=46,t.t1=t["catch"](2),t.abrupt("return",Promise.reject(t.t1));case 50:return t.prev=50,e.tableLoading=!1,t.finish(50);case 53:case"end":return t.stop()}}),t,null,[[2,46,50,53],[11,36]])})))()},loadTasksList:function(e){var t=this;return(0,H.A)((0,G.A)().mark((function r(){var a,n,s;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,Y["default"].images.getUserImages();case 3:a=r.sent,a&&a.data&&(n=a.data,s=n.map((function(r){return{id:r.id,caseId:r.caseNumber||"CASE-".concat(r.id),department:r.lesionLocation||"未知",type:r.diagnosisCategory||"未知",status:t.getStatusText(r.status||"DRAFT"),createTime:t.formatDate(r.createdAt||(new Date).toISOString()),rawDate:r.createdAt||(new Date).toISOString(),creatorId:r.uploadedBy||e}})).sort((function(e,t){return new Date(t.rawDate)-new Date(e.rawDate)})),t.recentTasks=s,t.myTasks=s.filter((function(t){return t.creatorId===e})),t.needReviewTasks=s.filter((function(e){return"待审核"===e.status}))),r.next=11;break;case 7:r.prev=7,r.t0=r["catch"](0),t.$message.error("获取任务列表失败: "+r.t0.message);case 11:case"end":return r.stop()}}),r,null,[[0,7]])})))()},formatDate:function(e){if(!e)return"未知";try{var t=new Date(e);return isNaN(t.getTime())?"未知":t.toLocaleString("zh-CN")}catch(r){return"未知"}},created:function(){this.initPage()},mounted:function(){var e=this;if(this.loadStatsFromStorage(),this.refreshInterval=setInterval((function(){e.loadStatsFromStorage()}),5e3),0===this.dashboardStats.totalCount){var t="200000001";ee().get("http://localhost:8085/medical/api/stats-v2/dashboard/".concat(t)).then((function(t){t.data&&(e.dashboardStats={totalCount:parseInt(t.data.totalCount||0),draftCount:parseInt(t.data.draftCount||0),reviewedCount:parseInt(t.data.reviewedCount||0),submittedCount:parseInt(t.data.submittedCount||0),approvedCount:parseInt(t.data.approvedCount||0),rejectedCount:parseInt(t.data.rejectedCount||0)},e.$forceUpdate())}))["catch"]((function(t){e.useHardcodedData()}))}setTimeout((function(){0===e.dashboardStats.totalCount&&e.useHardcodedData()}),2e3)},loadStatsFromStorage:function(){try{var e=localStorage.getItem("dashboardStats");if(e){var t=JSON.parse(e);this.dashboardStats={totalCount:parseInt(t.totalCount||0),draftCount:parseInt(t.draftCount||0),reviewedCount:parseInt(t.reviewedCount||0),submittedCount:parseInt(t.submittedCount||0),approvedCount:parseInt(t.approvedCount||0),rejectedCount:parseInt(t.rejectedCount||0)},this.$forceUpdate()}else this.fetchDashboardData()}catch(r){this.fetchDashboardData()}},beforeDestroy:function(){clearInterval(this.refreshInterval)},initPage:function(){var e=this.currentUserId||localStorage.getItem("userId"),t={totalCount:10,draftCount:5,reviewedCount:3,submittedCount:1,approvedCount:1,rejectedCount:0};this.dashboardStats=(0,A.A)({},t);var r=[{id:1,caseId:"CASE-001",department:"左腿",type:"血管瘤",status:"已标注",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:e||1},{id:2,caseId:"CASE-002",department:"右臂",type:"血管瘤",status:"未标注",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:e||1}];this.recentTasks=r,this.tableLoading=!1},loadTasksWithTestData:function(){var e=[{id:1,caseId:"CASE-001",department:"左腿",type:"血管瘤",status:"已标注",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:this.currentUserId||1},{id:2,caseId:"CASE-002",department:"右臂",type:"血管瘤",status:"未标注",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:this.currentUserId||1},{id:3,caseId:"CASE-003",department:"头部",type:"血管瘤",status:"待审核",createTime:this.formatDate(new Date),rawDate:(new Date).toISOString(),creatorId:this.currentUserId||1}];this.recentTasks=e,this.myTasks=e,this.needReviewTasks=e.filter((function(e){return"待审核"===e.status})),this.loadTasksList(this.currentUserId||localStorage.getItem("userId"))["catch"]((function(e){}))},useHardcodedData:function(){this.dashboardStats={totalCount:14,draftCount:6,reviewedCount:4,submittedCount:4,approvedCount:0,rejectedCount:0},this.$forceUpdate()}})},re=(0,c.A)(te,[["render",J],["__scopeId","data-v-929f0116"]]),ae=re;var ne={class:"home-container"},se={class:"container mt-5"},oe={class:"row justify-content-center"},ie={class:"col-md-10"},ue={class:"card shadow border-0"},ce={class:"card-body p-4"},le={class:"row"},de={class:"col-md-7 border-end pe-4"},pe={key:0,class:"alert alert-info mt-4"},me={class:"col-md-5 ps-4"},fe={key:0},ge={class:"text-center mb-4"},he={class:"avatar-container mb-3"},ve={class:"avatar bg-primary text-white"},be={class:"text-muted"},Ae={class:"d-grid gap-3"},Ie={key:1},Ee={key:0,class:"alert alert-danger"},Ce={class:"mb-3"},we=["disabled"],ke={class:"mb-3"},Se=["disabled"],_e={class:"mb-3 form-check"},ye=["disabled"],Te={class:"d-grid gap-2"},De=["disabled"],Le={key:0,class:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"},Re={class:"mt-3 text-center"},Oe={class:"card-footer text-center bg-light py-3"},Ne={class:"mb-0 small text-muted"};function xe(e,t,r,a,o,i){var u=(0,s.g2)("router-link");return(0,s.uX)(),(0,s.CE)("div",ne,[(0,s.Lk)("div",se,[(0,s.Lk)("div",oe,[(0,s.Lk)("div",ie,[(0,s.Lk)("div",ue,[t[16]||(t[16]=(0,s.Lk)("div",{class:"card-header bg-primary text-white py-3"},[(0,s.Lk)("h2",{class:"mb-0 text-center"},"血管瘤辅助系统")],-1)),(0,s.Lk)("div",ce,[(0,s.Lk)("div",le,[(0,s.Lk)("div",de,[t[7]||(t[7]=(0,s.Fv)('<h3 class="mb-4" data-v-5c6f6ac4>系统介绍</h3><p class="lead" data-v-5c6f6ac4>欢迎使用血管瘤辅助系统，该系统旨在帮助医疗专业人员进行血管瘤的标注、分类和分析。</p><div class="mb-4" data-v-5c6f6ac4><h5 data-v-5c6f6ac4>主要功能：</h5><ul class="list-group list-group-flush" data-v-5c6f6ac4><li class="list-group-item" data-v-5c6f6ac4>血管瘤图像标注与分类</li><li class="list-group-item" data-v-5c6f6ac4>结构化病历数据记录</li><li class="list-group-item" data-v-5c6f6ac4>多级审核与质控流程</li><li class="list-group-item" data-v-5c6f6ac4>数据统计与分析</li></ul></div>',3)),a.redirectInfo?((0,s.uX)(),(0,s.CE)("div",pe,[t[6]||(t[6]=(0,s.Lk)("i",{class:"bi bi-info-circle-fill me-2"},null,-1)),(0,s.eW)((0,m.v_)(a.redirectInfo),1)])):(0,s.Q3)("",!0)]),(0,s.Lk)("div",me,[a.isAuthenticated?((0,s.uX)(),(0,s.CE)("div",fe,[(0,s.Lk)("div",ge,[(0,s.Lk)("div",he,[(0,s.Lk)("div",ve,(0,m.v_)(a.userInitials),1)]),(0,s.Lk)("h4",null,(0,m.v_)(a.userName),1),(0,s.Lk)("p",be,(0,m.v_)(a.userRole),1)]),(0,s.Lk)("div",Ae,[(0,s.Lk)("button",{onClick:t[0]||(t[0]=function(){return a.goToDashboard&&a.goToDashboard.apply(a,arguments)}),class:"btn btn-primary btn-lg"},t[8]||(t[8]=[(0,s.Lk)("i",{class:"bi bi-speedometer2 me-2"},null,-1),(0,s.eW)("进入工作台 ")])),(0,s.Lk)("button",{onClick:t[1]||(t[1]=function(){return a.handleLogout&&a.handleLogout.apply(a,arguments)}),class:"btn btn-outline-secondary"},t[9]||(t[9]=[(0,s.Lk)("i",{class:"bi bi-box-arrow-right me-2"},null,-1),(0,s.eW)("退出登录 ")]))])])):((0,s.uX)(),(0,s.CE)("div",Ie,[t[15]||(t[15]=(0,s.Lk)("h4",{class:"mb-3 text-center"},"用户登录",-1)),a.error?((0,s.uX)(),(0,s.CE)("div",Ee,(0,m.v_)(a.error),1)):(0,s.Q3)("",!0),(0,s.Lk)("form",{onSubmit:t[5]||(t[5]=(0,n.D$)((function(){return a.handleLogin&&a.handleLogin.apply(a,arguments)}),["prevent"]))},[(0,s.Lk)("div",Ce,[t[10]||(t[10]=(0,s.Lk)("label",{for:"email",class:"form-label"},"邮箱",-1)),(0,s.bo)((0,s.Lk)("input",{type:"email",class:"form-control",id:"email","onUpdate:modelValue":t[2]||(t[2]=function(e){return a.email=e}),required:"",disabled:a.loading,placeholder:"请输入邮箱"},null,8,we),[[n.Jo,a.email]])]),(0,s.Lk)("div",ke,[t[11]||(t[11]=(0,s.Lk)("label",{for:"password",class:"form-label"},"密码",-1)),(0,s.bo)((0,s.Lk)("input",{type:"password",class:"form-control",id:"password","onUpdate:modelValue":t[3]||(t[3]=function(e){return a.password=e}),required:"",disabled:a.loading,placeholder:"请输入密码"},null,8,Se),[[n.Jo,a.password]])]),(0,s.Lk)("div",_e,[(0,s.bo)((0,s.Lk)("input",{type:"checkbox",class:"form-check-input",id:"remember","onUpdate:modelValue":t[4]||(t[4]=function(e){return a.remember=e}),disabled:a.loading},null,8,ye),[[n.lH,a.remember]]),t[12]||(t[12]=(0,s.Lk)("label",{class:"form-check-label",for:"remember"},"记住我",-1))]),(0,s.Lk)("div",Te,[(0,s.Lk)("button",{type:"submit",class:"btn btn-primary",disabled:a.loading},[a.loading?((0,s.uX)(),(0,s.CE)("span",Le)):(0,s.Q3)("",!0),t[13]||(t[13]=(0,s.eW)(" 登录 "))],8,De)])],32),(0,s.Lk)("div",Re,[(0,s.bF)(u,{to:"/register",class:"text-decoration-none"},{default:(0,s.k6)((function(){return t[14]||(t[14]=[(0,s.eW)("没有账号？点击注册")])})),_:1,__:[14]})])]))])])]),(0,s.Lk)("div",Oe,[(0,s.Lk)("p",Ne,"© "+(0,m.v_)((new Date).getFullYear())+" 血管瘤辅助系统 - 版本 1.0.0",1)])])])])])])}var Fe=r(50953);const Pe={name:"HomePage",setup:function(){var e=(0,I.Pj)(),t=(0,p.rd)(),r=(0,p.lq)(),a=(0,Fe.KR)(""),n=(0,Fe.KR)(""),o=(0,Fe.KR)(!1),i=(0,Fe.KR)(!1),u=(0,Fe.KR)(""),c=(0,Fe.KR)(""),l=(0,s.EW)((function(){return e.state.auth.isAuthenticated})),d=(0,s.EW)((function(){return e.state.auth.user})),m=(0,s.EW)((function(){return d.value?d.value.name:""})),f=(0,s.EW)((function(){return d.value&&d.value.name?d.value.name.charAt(0).toUpperCase():""})),g=(0,s.EW)((function(){if(!d.value)return"";switch(d.value.role){case"ADMIN":return"管理员";case"DOCTOR":return"医生";case"REVIEWER":return"审核员";default:return"用户"}}));(0,s.sV)((function(){r.query.redirect?c.value="登录后将跳转到您请求的页面":r.query.from&&(c.value="您需要登录才能继续操作"),l.value&&r.query.redirect&&(c.value='检测到您已登录，点击"进入工作台"继续')}));var h=function(){var s=(0,H.A)((0,G.A)().mark((function s(){return(0,G.A)().wrap((function(s){while(1)switch(s.prev=s.next){case 0:return i.value=!0,u.value="",s.prev=2,s.next=5,e.dispatch("login",{email:a.value,password:n.value});case 5:s.sent,r.query.redirect?t.push(decodeURIComponent(r.query.redirect)):r.query.from?t.push(decodeURIComponent(r.query.from)):t.push("/app/dashboard"),s.next=14;break;case 10:s.prev=10,s.t0=s["catch"](2),u.value="string"===typeof s.t0?s.t0:"登录失败，请检查邮箱和密码";case 14:return s.prev=14,i.value=!1,s.finish(14);case 17:case"end":return s.stop()}}),s,null,[[2,10,14,17]])})));return function(){return s.apply(this,arguments)}}(),v=function(){r.query.redirect?t.push(decodeURIComponent(r.query.redirect)):t.push("/app/dashboard")},b=function(){e.dispatch("logout"),t.go(0)};return{email:a,password:n,remember:o,loading:i,error:u,redirectInfo:c,isAuthenticated:l,userName:m,userInitials:f,userRole:g,handleLogin:h,goToDashboard:v,handleLogout:b}}},Ue=(0,c.A)(Pe,[["render",xe],["__scopeId","data-v-5c6f6ac4"]]),We=Ue;var Ve=r(11786),Me={user:JSON.parse(localStorage.getItem("user"))||null,isAuthenticated:!!localStorage.getItem("user")},je={getUser:function(e){return e.user},getUserRole:function(e){return e.user?e.user.role:null},getUserId:function(e){return e.user?e.user.id:null},isAdmin:function(e){return e.user&&"ADMIN"===e.user.role},isDoctor:function(e){return e.user&&"DOCTOR"===e.user.role},isReviewer:function(e){return e.user&&"REVIEWER"===e.user.role},hasPermission:function(e){return function(t){var r;return(0,Q._m)(null===(r=e.user)||void 0===r?void 0:r.role,t)}},canAccessRoute:function(e){return function(t){var r;return(0,Q.kL)(null===(r=e.user)||void 0===r?void 0:r.role,t)}},canAccessResource:function(e){return function(t){return!!e.user&&(0,Q.X2)(e.user.id,t,e.user.role)}}},Be={setUser:function(e,t){e.user=t,e.isAuthenticated=!!t}},$e={login:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n,s;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,r.prev=1,r.next=5,Y["default"].auth.login(t);case 5:return n=r.sent,s=n.data,s.name,localStorage.setItem("user",JSON.stringify(s)),a("setUser",s),r.abrupt("return",s);case 15:if(r.prev=15,r.t0=r["catch"](1),!r.t0.response){r.next=23;break}throw r.t0.response.data||"登录失败，服务器错误";case 23:if(!r.t0.request){r.next=28;break}throw"登录失败，无法连接到服务器";case 28:throw r.t0.message||"登录失败，请求配置错误";case 30:case"end":return r.stop()}}),r,null,[[1,15]])})))()},register:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.commit,r.prev=1,r.next=4,Y["default"].auth.register(t);case 4:return a=r.sent,r.abrupt("return",a.data);case 8:throw r.prev=8,r.t0=r["catch"](1),r.t0.response?r.t0.response.data:r.t0.message;case 11:case"end":return r.stop()}}),r,null,[[1,8]])})))()},logout:function(e){var t=e.commit;localStorage.removeItem("user"),t("setUser",null)},fetchUserProfile:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.commit,r.prev=1,r.next=4,Y["default"].users.getUser(t);case 4:return a=r.sent,r.abrupt("return",a.data);case 8:throw r.prev=8,r.t0=r["catch"](1),r.t0.response?r.t0.response.data:r.t0.message;case 11:case"end":return r.stop()}}),r,null,[[1,8]])})))()},updateUserProfile:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n,s,o;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,n=e.state,r.prev=1,r.next=4,Y["default"].users.updateUser(n.user.id,t);case 4:return s=r.sent,o=s.data,localStorage.setItem("user",JSON.stringify(o)),a("setUser",o),r.abrupt("return",o);case 11:throw r.prev=11,r.t0=r["catch"](1),r.t0.response?r.t0.response.data:r.t0.message;case 14:case"end":return r.stop()}}),r,null,[[1,11]])})))()}};const qe={state:Me,getters:je,mutations:Be,actions:$e};var Xe=r(54119),Je=(r(48980),r(54554),r(13609),r(16034),r(42207),r(55815),r(64979),r(79739),"http://localhost:8085/medical/api"),Ge=function(){var e=JSON.parse(localStorage.getItem("user"));return e?{Authorization:"Basic ".concat(btoa("".concat(e.email,":").concat(e.password)))}:{}},He={images:[],currentImage:null,stats:{totalCount:0,draftCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},loading:!1,error:null},Qe={getAllImages:function(e){return e.images},getCurrentImage:function(e){return e.currentImage},getImagesStats:function(e){return e.stats},isImagesLoading:function(e){return e.loading},getImagesError:function(e){return e.error}},Ke={setImages:function(e,t){e.images=t},setCurrentImage:function(e,t){e.currentImage=t},setStats:function(e,t){e.stats=t},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t},addImage:function(e,t){e.images.unshift(t)},updateImage:function(e,t){var r=e.images.findIndex((function(e){return e.id===t.id}));-1!==r&&e.images.splice(r,1,t),e.currentImage&&e.currentImage.id===t.id&&(e.currentImage=t)},removeImage:function(e,t){e.images=e.images.filter((function(e){return e.id!==t})),e.currentImage&&e.currentImage.id===t&&(e.currentImage=null)}},ze={fetchImages:function(e){return(0,H.A)((0,G.A)().mark((function t(){var r,a,n;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),t.prev=3,t.next=7,ee().get("".concat(Je,"/images"),{headers:Ge()});case 7:return a=t.sent,n=[],Array.isArray(a.data)?(n=a.data,n.length):"object"===(0,Xe.A)(a.data)&&null!==a.data&&(n=Array.isArray(a.data.content)?a.data.content:Array.isArray(a.data.data)?a.data.data:Array.isArray(a.data.items)?a.data.items:Object.values(a.data).filter((function(e){return e&&"object"===(0,Xe.A)(e)}))),r("setImages",n),t.abrupt("return",n);case 16:throw t.prev=16,t.t0=t["catch"](3),r("setError",t.t0.response?t.t0.response.data:t.t0.message),t.t0;case 21:return t.prev=21,r("setLoading",!1),t.finish(21);case 24:case"end":return t.stop()}}),t,null,[[3,16,21,24]])})))()},fetchImagesByStatus:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n,s;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,a("setLoading",!0),a("setError",null),r.prev=3,r.next=6,ee().get("".concat(Je,"/images/status/").concat(t),{headers:Ge()});case 6:return n=r.sent,s=Array.isArray(n.data)?n.data:[],a("setImages",s),r.abrupt("return",s);case 12:throw r.prev=12,r.t0=r["catch"](3),a("setError",r.t0.response?r.t0.response.data:r.t0.message),r.t0;case 16:return r.prev=16,a("setLoading",!1),r.finish(16);case 19:case"end":return r.stop()}}),r,null,[[3,12,16,19]])})))()},fetchImageById:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,a("setLoading",!0),a("setError",null),r.prev=3,r.next=6,ee().get("".concat(Je,"/images/").concat(t),{headers:Ge()});case 6:return n=r.sent,a("setCurrentImage",n.data),r.abrupt("return",n.data);case 11:throw r.prev=11,r.t0=r["catch"](3),a("setError",r.t0.response?r.t0.response.data:r.t0.message),r.t0;case 15:return r.prev=15,a("setLoading",!1),r.finish(15);case 18:case"end":return r.stop()}}),r,null,[[3,11,15,18]])})))()},createImage:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,a("setLoading",!0),a("setError",null),r.prev=3,r.next=6,ee().post("".concat(Je,"/images"),t,{headers:Ge()});case 6:return n=r.sent,a("addImage",n.data),r.abrupt("return",n.data);case 11:throw r.prev=11,r.t0=r["catch"](3),a("setError",r.t0.response?r.t0.response.data:r.t0.message),r.t0;case 15:return r.prev=15,a("setLoading",!1),r.finish(15);case 18:case"end":return r.stop()}}),r,null,[[3,11,15,18]])})))()},updateImage:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n,s,o;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,n=t.id,s=t.imageData,a("setLoading",!0),a("setError",null),r.prev=4,r.next=7,ee().put("".concat(Je,"/images/").concat(n),s,{headers:Ge()});case 7:return o=r.sent,a("updateImage",o.data),r.abrupt("return",o.data);case 12:throw r.prev=12,r.t0=r["catch"](4),a("setError",r.t0.response?r.t0.response.data:r.t0.message),r.t0;case 16:return r.prev=16,a("setLoading",!1),r.finish(16);case 19:case"end":return r.stop()}}),r,null,[[4,12,16,19]])})))()},updateImageStatus:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n,s,o,i,u;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,n=t.id,s=t.status,o=t.reviewerId,i=t.reviewNotes,a("setLoading",!0),a("setError",null),r.prev=4,r.next=7,ee().put("".concat(Je,"/images/").concat(n,"/status"),null,{params:{status:s,reviewerId:o,reviewNotes:i},headers:Ge()});case 7:return r.next=9,ee().get("".concat(Je,"/images/").concat(n),{headers:Ge()});case 9:return u=r.sent,a("updateImage",u.data),r.abrupt("return",u.data);case 14:throw r.prev=14,r.t0=r["catch"](4),a("setError",r.t0.response?r.t0.response.data:r.t0.message),r.t0;case 18:return r.prev=18,a("setLoading",!1),r.finish(18);case 21:case"end":return r.stop()}}),r,null,[[4,14,18,21]])})))()},deleteImage:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,a("setLoading",!0),a("setError",null),r.prev=3,r.next=6,Y["default"].images["delete"](t);case 6:return a("removeImage",t),r.abrupt("return",{success:!0});case 10:return r.prev=10,r.t0=r["catch"](3),n="删除失败",r.t0.response&&r.t0.response.data?n="string"===typeof r.t0.response.data?r.t0.response.data:JSON.stringify(r.t0.response.data):n+=": "+(r.t0.message||"未知错误"),a("setError",n),r.abrupt("return",{success:!1,error:n});case 16:return r.prev=16,a("setLoading",!1),r.finish(16);case 19:case"end":return r.stop()}}),r,null,[[3,10,16,19]])})))()},fetchStats:function(e){return(0,H.A)((0,G.A)().mark((function t(){var r,a;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),t.prev=3,t.next=6,ee().get("".concat(Je,"/stats"),{headers:Ge()});case 6:return a=t.sent,r("setStats",a.data),t.abrupt("return",a.data);case 11:throw t.prev=11,t.t0=t["catch"](3),r("setError",t.t0.response?t.t0.response.data:t.t0.message),t.t0;case 15:return t.prev=15,r("setLoading",!1),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[3,11,15,18]])})))()}};const Ye={state:He,getters:Qe,mutations:Ke,actions:ze};var Ze={users:[],currentUser:null,loading:!1,error:null},et={getAllUsers:function(e){return e.users},getCurrentUser:function(e){return e.currentUser},isLoading:function(e){return e.loading},getError:function(e){return e.error}},tt={setUsers:function(e,t){e.users=t},setCurrentUser:function(e,t){e.currentUser=t},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t}},rt={fetchUsers:function(e){return(0,H.A)((0,G.A)().mark((function t(){var r,a;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),t.prev=3,t.next=6,ee().get("/medical/api/users");case 6:return a=t.sent,r("setUsers",a.data),t.abrupt("return",a.data);case 11:t.prev=11,t.t0=t["catch"](3),r("setError",t.t0.message);case 15:return t.prev=15,r("setLoading",!1),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[3,11,15,18]])})))()},fetchUser:function(e,t){return(0,H.A)((0,G.A)().mark((function r(){var a,n;return(0,G.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.commit,a("setLoading",!0),a("setError",null),r.prev=3,r.next=6,ee().get("/medical/api/users/".concat(t));case 6:return n=r.sent,a("setCurrentUser",n.data),r.abrupt("return",n.data);case 11:r.prev=11,r.t0=r["catch"](3),a("setError",r.t0.message);case 15:return r.prev=15,a("setLoading",!1),r.finish(15);case 18:case"end":return r.stop()}}),r,null,[[3,11,15,18]])})))()}};const at={state:Ze,getters:et,mutations:tt,actions:rt};var nt={stats:{totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},loading:!1,error:null},st={getStats:function(e){return e.stats},isLoading:function(e){return e.loading},getError:function(e){return e.error}},ot={setStats:function(e,t){e.stats={totalCount:parseInt(t.totalCount)||0,draftCount:parseInt(t.draftCount)||0,reviewedCount:parseInt(t.reviewedCount||t.pendingCount)||0,submittedCount:parseInt(t.submittedCount)||0,approvedCount:parseInt(t.approvedCount)||0,rejectedCount:parseInt(t.rejectedCount)||0};try{localStorage.setItem("dashboardStats",JSON.stringify(e.stats))}catch(r){}},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t},setStatField:function(e,t){var r=t.field,a=t.value;e.stats.hasOwnProperty(r)&&(e.stats[r]=parseInt(a)||0)}},it={fetchStats:function(e){return(0,H.A)((0,G.A)().mark((function t(){var r,a,n,s;return(0,G.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=e.commit,r("setLoading",!0),r("setError",null),t.prev=3,a=localStorage.getItem("userId"),a){t.next=8;break}throw new Error("未找到用户ID");case 8:return t.next=12,Y["default"].stats.getDashboard(a);case 12:if(n=t.sent,!n||!n.data){t.next=32;break}return n.data,r("setStats",n.data),t.abrupt("return",n.data);case 32:throw new Error("响应中没有数据");case 33:t.next=43;break;case 35:t.prev=35,t.t0=t["catch"](3),t.t0.response||t.t0.request,r("setError",t.t0.message||"获取统计数据失败"),s={totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},r("setStats",s);case 43:return t.prev=43,r("setLoading",!1),t.finish(43);case 47:case"end":return t.stop()}}),t,null,[[3,35,43,47]])})))()},setManualStats:function(e,t){var r=e.commit;r("setStats",t)}};const ut={state:nt,getters:st,mutations:ot,actions:it},ct=(0,I.y$)({state:{annotationProgress:{currentStep:0,imageId:null,formData:null,lastUpdated:null}},getters:{isAuthenticated:function(e){return e.auth.isAuthenticated},isAdmin:function(e){return e.auth.user&&"ADMIN"===e.auth.user.role},getAnnotationProgress:function(e){return e.annotationProgress},hasUnfinishedAnnotation:function(e){return null!==e.annotationProgress.imageId&&e.annotationProgress.currentStep>0}},mutations:{saveAnnotationProgress:function(e,t){var r=t.step,a=t.imageId,n=t.formData;e.annotationProgress={currentStep:r,imageId:a,formData:n,lastUpdated:(new Date).toISOString()}},clearAnnotationProgress:function(e){e.annotationProgress={currentStep:0,imageId:null,formData:null,lastUpdated:null}}},actions:{saveProgress:function(e,t){var r=e.commit,a=t.step,n=t.imageId,s=t.formData;r("saveAnnotationProgress",{step:a,imageId:n,formData:s})},completeAnnotation:function(e){var t=e.commit;t("clearAnnotationProgress")}},modules:{auth:qe,images:Ye,users:at,stats:ut},plugins:[(0,Ve.A)({paths:["auth","annotationProgress","stats"]})]});var lt=[{path:"/app",component:k,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"Dashboard",component:ae},{path:"cases",name:"Cases",component:function(){return Promise.all([r.e(603),r.e(647)]).then(r.bind(r,22647))}},{path:"cases/new",name:"NewCase",component:function(){return Promise.all([r.e(603),r.e(610)]).then(r.bind(r,16610))}},{path:"cases/edit/:id",name:"EditCase",component:function(){return Promise.all([r.e(603),r.e(610)]).then(r.bind(r,16610))}},{path:"cases/view/:id",name:"ViewCase",component:function(){return Promise.all([r.e(603),r.e(362)]).then(r.bind(r,90362))}},{path:"cases/form",name:"CaseDetailForm",component:function(){return Promise.all([r.e(603),r.e(726)]).then(r.bind(r,97726))}},{path:"cases/structured-form",name:"CaseStructuredForm",component:function(){return Promise.all([r.e(603),r.e(878)]).then(r.bind(r,11878))}},{path:"review",name:"Review",component:function(){return r.e(958).then(r.bind(r,42958))}},{path:"teams",name:"Teams",component:function(){return r.e(498).then(r.bind(r,59498))}},{path:"users",name:"Users",component:function(){return Promise.all([r.e(603),r.e(697)]).then(r.bind(r,28697))},meta:{requiresAdmin:!0,title:"部门成员"}}]},{path:"/",name:"Root",component:We},{path:"/login",name:"Login",component:function(){return r.e(277).then(r.bind(r,38658))}},{path:"/register",name:"Register",component:function(){return r.e(272).then(r.bind(r,44272))}},{path:"/images",name:"ImageList",component:function(){return r.e(995).then(r.bind(r,46995))},meta:{requiresAuth:!0}},{path:"/images/:id",name:"ImageDetail",component:function(){return r.e(35).then(r.bind(r,57035))},meta:{requiresAuth:!0}},{path:"/images/upload",name:"ImageUpload",component:function(){return r.e(587).then(r.bind(r,91587))},meta:{requiresAuth:!0}},{path:"/annotations",name:"ImageAnnotation",component:function(){return Promise.all([r.e(603),r.e(63)]).then(r.bind(r,47063))}},{path:"/cases/form",name:"PublicCaseDetailForm",component:function(){return Promise.all([r.e(603),r.e(726)]).then(r.bind(r,97726))}},{path:"/cases/structured-form",name:"PublicCaseStructuredForm",component:function(){return Promise.all([r.e(603),r.e(878)]).then(r.bind(r,11878))}},{path:"/admin",name:"Admin",component:function(){return r.e(236).then(r.bind(r,35236))},meta:{requiresAuth:!0,requiresAdmin:!0}},{path:"/:pathMatch(.*)*",redirect:"/"}],dt=(0,p.aE)({history:(0,p.LA)("/"),routes:lt});dt.beforeEach((function(e,t,r){t.name&&sessionStorage.setItem("isAppOperation","true");var a="true"===sessionStorage.getItem("isNavigatingAfterSave"),n="true"===sessionStorage.getItem("navigatingFromForm");if((a||n)&&"/login"===e.path)return sessionStorage.removeItem("isNavigatingAfterSave"),sessionStorage.removeItem("navigatingFromForm"),r("/app/dashboard");var s=["/","/login","/register","/annotations","/cases/form","/cases/structured-form"],o=!s.includes(e.path),i=JSON.parse(localStorage.getItem("user")),u=ct.getters.isAuthenticated;if("/login"===e.path||"/"===e.path||u||sessionStorage.setItem("redirectPath",e.fullPath),o&&!i)return"/"!==e.path?r("/?redirect="+encodeURIComponent(e.fullPath)):r();var c=e.matched.some((function(e){return e.meta.requiresAuth}));if(c&&!u)return t.path.includes("/cases/structured-form")&&"true"===sessionStorage.getItem("allowFormOperation")?(sessionStorage.removeItem("allowFormOperation"),r("/app/dashboard")):r("/?redirect="+encodeURIComponent(e.fullPath));var l=i?i.role:null;if(c&&l){var d=e.matched.some((function(e){return e.meta.requiresAdmin})),p=e.matched.some((function(e){return e.meta.requiresReviewer})),m=e.matched.some((function(e){return e.meta.requiresDoctor}));if(d&&"ADMIN"!==l||p&&"REVIEWER"!==l&&"ADMIN"!==l||m&&"DOCTOR"!==l&&"ADMIN"!==l)return r("/app/dashboard");if(!(0,Q.kL)(l,e.path))return r("/app/dashboard")}return"/dashboard"===e.path?r("/app/dashboard"):"/cases"===e.path?r("/app/cases"):void r()})),dt.beforeResolve((function(e,t,a){if("/app/dashboard"===e.path){var n=JSON.parse(localStorage.getItem("user")),s=(null===n||void 0===n?void 0:n.customId)||(null===n||void 0===n?void 0:n.id)||"200000001",o=r(72505)["default"];o.get("http://localhost:8085/medical/api/stats-v2/dashboard/".concat(s)).then((function(e){localStorage.setItem("dashboardStats",JSON.stringify(e.data));try{setTimeout((function(){var t=document.getElementById("totalCount"),r=document.getElementById("draftCount"),a=document.getElementById("readyCount"),n=document.getElementById("reviewedCount");t&&(t.textContent=e.data.totalCount||"0"),r&&(r.textContent=e.data.draftCount||"0"),a&&(a.textContent=e.data.readyCount||"0"),n&&(n.textContent=e.data.reviewedCount||"0")}),1e3)}catch(t){}}))["catch"]((function(e){}))}a()})),dt.afterEach((function(e,t){sessionStorage.setItem("isAppOperation","true"),"/login"!==dt.currentRoute.value.path&&sessionStorage.setItem("isAppOperation","true")}));const pt=dt;var mt=r(57928),ft=(r(4188),r(47487)),gt=r(20163),ht=r(61644),vt=r(77918),bt=r(1132),At=(r(88431),r(81148),{mounted:function(e,t){var r=t.value,a=t.arg,n=ct.getters.getUserRole;if(n)if("role"!==a)if(Array.isArray(r))if("all"===a){var s=r.every((function(e){return(0,Q._m)(n,e)}));s||(e.style.display="none")}else{var o=r.some((function(e){return(0,Q._m)(n,e)}));o||(e.style.display="none")}else(0,Q._m)(n,r)||(e.style.display="none");else{var i=Array.isArray(r)?r:[r];i.includes(n)||(e.style.display="none")}else e.style.display="none"}});function It(e){e.directive("permission",At)}var Et=localStorage.setItem,Ct=localStorage.removeItem;localStorage.setItem=function(e,t){Et.call(this,e,t)},localStorage.removeItem=function(e){Ct.call(this,e)},ee().defaults.withCredentials=!0,ee().defaults.headers.common["Content-Type"]="application/json",ee().defaults.headers.common["Accept"]="application/json",ee().defaults.timeout=1e4,ee().interceptors.request.use((function(e){return e}),(function(e){return Promise.reject(e)})),ee().interceptors.response.use((function(e){return e}),(function(e){return e.response||e.request,Promise.reject(e)}));var wt=(0,n.Ef)(d);wt.config.errorHandler=function(e,t,r){},window.addEventListener("unhandledrejection",(function(e){}));for(var kt=0,St=Object.entries(E);kt<St.length;kt++){var _t=(0,a.A)(St[kt],2),yt=_t[0],Tt=_t[1];wt.component(yt,Tt)}wt.config.globalProperties.$axios=ee(),wt.config.globalProperties.$message=gt.nk,wt.config.globalProperties.$notify=ht.df,wt.config.globalProperties.$msgbox=vt.s,wt.config.globalProperties.$alert=vt.s.alert,wt.config.globalProperties.$confirm=vt.s.confirm,wt.config.globalProperties.$prompt=vt.s.prompt,wt.config.globalProperties.$loading=bt.Ks.service,It(wt),wt.use(ct),wt.use(pt),wt.use(mt.A,{locale:ft.A}),wt.mount("#app"),setTimeout((function(){try{var e=JSON.parse(localStorage.getItem("dashboardStats")||"{}");if(e&&e.totalCount){var t=document.querySelectorAll(".stat-value");t&&t.length>=5?(t[0].textContent=e.totalCount||0,t[1].textContent=e.draftCount||0,t[2].textContent=e.reviewedCount||0,t[3].textContent=e.submittedCount||0,t[4].textContent=e.approvedCount||0):setTimeout((function(){var t=document.querySelectorAll(".stat-value");t&&t.length>=5&&(t[0].textContent=e.totalCount||0,t[1].textContent=e.draftCount||0,t[2].textContent=e.reviewedCount||0,t[3].textContent=e.submittedCount||0,t[4].textContent=e.approvedCount||0)}),300)}else{var a=r(72505)["default"];a.get("http://localhost:8085/medical/api/stats-v2/dashboard/200000001").then((function(e){var t=document.querySelectorAll(".stat-value");t&&t.length>=5&&(t[0].textContent=e.data.totalCount||0,t[1].textContent=e.data.draftCount||0,t[2].textContent=e.data.reviewedCount||0,t[3].textContent=e.data.submittedCount||0,t[4].textContent=e.data.approvedCount||0)}))["catch"]((function(e){}))}}catch(n){}}),500);var Dt=history.pushState,Lt=history.replaceState;history.pushState=function(){(new Error).stack;return Dt.apply(this,arguments)},history.replaceState=function(){(new Error).stack;return Lt.apply(this,arguments)},window.addEventListener("popstate",(function(e){}))},69553:(e,t,r)=>{r.d(t,{Jj:()=>a,X2:()=>u,_m:()=>o,kL:()=>i});r(74423),r(21699),r(11392);var a={MANAGE_USERS:"manage_users",MANAGE_TEAMS:"manage_teams",VIEW_STATS:"view_stats",VIEW_TEAM:"view_team",JOIN_TEAM:"join_team",LEAVE_TEAM:"leave_team",CREATE_CASE:"create_case",EDIT_CASE:"edit_case",DELETE_CASE:"delete_case",VIEW_ALL_CASES:"view_all_cases",VIEW_OWN_CASES:"view_own_cases",REVIEW_CASES:"review_cases",APPROVE_CASES:"approve_cases",ANNOTATE_IMAGES:"annotate_images",VIEW_OWN_ANNOTATIONS:"view_own_annotations",EDIT_OWN_ANNOTATIONS:"edit_own_annotations",UPLOAD_IMAGES:"upload_images",DELETE_IMAGES:"delete_images"},n={ADMIN:[a.MANAGE_USERS,a.MANAGE_TEAMS,a.VIEW_STATS,a.VIEW_TEAM,a.JOIN_TEAM,a.LEAVE_TEAM,a.CREATE_CASE,a.EDIT_CASE,a.DELETE_CASE,a.VIEW_ALL_CASES,a.VIEW_OWN_CASES,a.REVIEW_CASES,a.APPROVE_CASES,a.ANNOTATE_IMAGES,a.VIEW_OWN_ANNOTATIONS,a.EDIT_OWN_ANNOTATIONS,a.UPLOAD_IMAGES,a.DELETE_IMAGES],DOCTOR:[a.CREATE_CASE,a.EDIT_CASE,a.VIEW_OWN_CASES,a.ANNOTATE_IMAGES,a.VIEW_OWN_ANNOTATIONS,a.EDIT_OWN_ANNOTATIONS,a.UPLOAD_IMAGES,a.VIEW_TEAM,a.JOIN_TEAM,a.LEAVE_TEAM],REVIEWER:[a.VIEW_ALL_CASES,a.VIEW_OWN_CASES,a.REVIEW_CASES,a.APPROVE_CASES,a.ANNOTATE_IMAGES,a.VIEW_OWN_ANNOTATIONS,a.EDIT_OWN_ANNOTATIONS,a.CREATE_CASE,a.EDIT_CASE,a.VIEW_TEAM,a.JOIN_TEAM,a.LEAVE_TEAM]},s={"/app/dashboard":["ADMIN","DOCTOR","REVIEWER"],"/app/users":["ADMIN"],"/app/teams":["ADMIN","DOCTOR","REVIEWER"],"/app/teams/join":["ADMIN","DOCTOR","REVIEWER"],"/app/teams/view":["ADMIN","DOCTOR","REVIEWER"],"/app/cases":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/new":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/edit":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/view":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/form":["ADMIN","DOCTOR","REVIEWER"],"/app/review":["ADMIN","REVIEWER"],"/app/images/upload":["ADMIN","DOCTOR","REVIEWER"],"/admin":["ADMIN"]};function o(e,t){var r;return e&&(null===(r=n[e])||void 0===r?void 0:r.includes(t))||!1}function i(e,t){if(!e||!t)return!1;if(s[t])return s[t].includes(e);for(var r in s)if(t.startsWith(r)&&s[r].includes(e))return!0;return!1}function u(e,t,r){return e===t||("ADMIN"===r||!("REVIEWER"!==r||!o(r,a.VIEW_ALL_CASES)))}}},t={};function r(a){var n=t[a];if(void 0!==n)return n.exports;var s=t[a]={exports:{}};return e[a].call(s.exports,s,s.exports,r),s.exports}r.m=e,(()=>{var e=[];r.O=(t,a,n,s)=>{if(!a){var o=1/0;for(l=0;l<e.length;l++){for(var[a,n,s]=e[l],i=!0,u=0;u<a.length;u++)(!1&s||o>=s)&&Object.keys(r.O).every((e=>r.O[e](a[u])))?a.splice(u--,1):(i=!1,s<o&&(o=s));if(i){e.splice(l--,1);var c=n();void 0!==c&&(t=c)}}return t}s=s||0;for(var l=e.length;l>0&&e[l-1][2]>s;l--)e[l]=e[l-1];e[l]=[a,n,s]}})(),(()=>{r.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return r.d(t,{a:t}),t}})(),(()=>{r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}})(),(()=>{r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce(((t,a)=>(r.f[a](e,t),t)),[]))})(),(()=>{r.u=e=>"js/"+e+"."+{35:"5d0fd8e5",63:"1b16b6e8",236:"0f7e58a0",272:"344a3702",277:"3b6bf3bf",362:"3741105c",498:"467acecb",587:"3b6fe773",610:"a968cce7",647:"6e9d0f68",697:"7cfe3dad",726:"2e521f52",878:"6e46db58",958:"5698e066",995:"a959481b"}[e]+".js"})(),(()=>{r.miniCssF=e=>"css/"+e+"."+{35:"da550125",63:"b93eaa9b",236:"f6967544",272:"0d8c01b5",277:"860b4413",362:"0edbe0e2",498:"ae355c9b",587:"98e3d387",610:"a4285273",647:"6cb23462",697:"99c5710a",726:"06f720cd",878:"f103afeb",958:"2db98da0",995:"e512093f"}[e]+".css"})(),(()=>{r.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="medical-annotation-frontend:";r.l=(a,n,s,o)=>{if(e[a])e[a].push(n);else{var i,u;if(void 0!==s)for(var c=document.getElementsByTagName("script"),l=0;l<c.length;l++){var d=c[l];if(d.getAttribute("src")==a||d.getAttribute("data-webpack")==t+s){i=d;break}}i||(u=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,r.nc&&i.setAttribute("nonce",r.nc),i.setAttribute("data-webpack",t+s),i.src=a),e[a]=[n];var p=(t,r)=>{i.onerror=i.onload=null,clearTimeout(m);var n=e[a];if(delete e[a],i.parentNode&&i.parentNode.removeChild(i),n&&n.forEach((e=>e(r))),t)return t(r)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=p.bind(null,i.onerror),i.onload=p.bind(null,i.onload),u&&document.head.appendChild(i)}}})(),(()=>{r.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{r.p="/"})(),(()=>{if("undefined"!==typeof document){var e=(e,t,a,n,s)=>{var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",r.nc&&(o.nonce=r.nc);var i=r=>{if(o.onerror=o.onload=null,"load"===r.type)n();else{var a=r&&r.type,i=r&&r.target&&r.target.href||t,u=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+i+")");u.name="ChunkLoadError",u.code="CSS_CHUNK_LOAD_FAILED",u.type=a,u.request=i,o.parentNode&&o.parentNode.removeChild(o),s(u)}};return o.onerror=o.onload=i,o.href=t,a?a.parentNode.insertBefore(o,a.nextSibling):document.head.appendChild(o),o},t=(e,t)=>{for(var r=document.getElementsByTagName("link"),a=0;a<r.length;a++){var n=r[a],s=n.getAttribute("data-href")||n.getAttribute("href");if("stylesheet"===n.rel&&(s===e||s===t))return n}var o=document.getElementsByTagName("style");for(a=0;a<o.length;a++){n=o[a],s=n.getAttribute("data-href");if(s===e||s===t)return n}},a=a=>new Promise(((n,s)=>{var o=r.miniCssF(a),i=r.p+o;if(t(o,i))return n();e(a,i,null,n,s)})),n={524:0};r.f.miniCss=(e,t)=>{var r={35:1,63:1,236:1,272:1,277:1,362:1,498:1,587:1,610:1,647:1,697:1,726:1,878:1,958:1,995:1};n[e]?t.push(n[e]):0!==n[e]&&r[e]&&t.push(n[e]=a(e).then((()=>{n[e]=0}),(t=>{throw delete n[e],t})))}}})(),(()=>{var e={524:0};r.f.j=(t,a)=>{var n=r.o(e,t)?e[t]:void 0;if(0!==n)if(n)a.push(n[2]);else{var s=new Promise(((r,a)=>n=e[t]=[r,a]));a.push(n[2]=s);var o=r.p+r.u(t),i=new Error,u=a=>{if(r.o(e,t)&&(n=e[t],0!==n&&(e[t]=void 0),n)){var s=a&&("load"===a.type?"missing":a.type),o=a&&a.target&&a.target.src;i.message="Loading chunk "+t+" failed.\n("+s+": "+o+")",i.name="ChunkLoadError",i.type=s,i.request=o,n[1](i)}};r.l(o,u,"chunk-"+t,t)}},r.O.j=t=>0===e[t];var t=(t,a)=>{var n,s,[o,i,u]=a,c=0;if(o.some((t=>0!==e[t]))){for(n in i)r.o(i,n)&&(r.m[n]=i[n]);if(u)var l=u(r)}for(t&&t(a);c<o.length;c++)s=o[c],r.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return r.O(l)},a=self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var a=r.O(void 0,[105,507,603,244,843,386,820,5,97,846,658,504],(()=>r(61561)));a=r.O(a)})();