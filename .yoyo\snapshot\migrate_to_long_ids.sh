#!/bin/bash
# 脚本：migrate_to_long_ids.sh
# 用途：自动修复Integer到Long类型的转换问题
# 使用方法：在项目根目录下运行 ./migrate_to_long_ids.sh

echo "开始修复Integer到Long的类型转换问题..."

# 1. 创建备份目录
BACKUP_DIR="./backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 2. 备份所有Java文件
echo "备份所有Java文件到 $BACKUP_DIR..."
find ./src -name "*.java" -exec cp --parents {} $BACKUP_DIR \;

# 3. 处理所有控制器中的imageId类型
echo "替换控制器中的参数类型..."
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/@PathVariable Integer imageId/@PathVariable Long imageId/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/@PathVariable("imageId") Integer imageId/@PathVariable("imageId") Long imageId/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/@RequestParam Integer imageId/@RequestParam Long imageId/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/@RequestParam("imageId") Integer imageId/@RequestParam("imageId") Long imageId/g' {} \;

# 4. 处理直接传入Integer变量到Long参数的方法调用
echo "替换方法调用中的参数类型..."

# 导入工具类
echo "添加TypeConverter工具类导入..."
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i '/^import /a import com.medical.annotation.util.TypeConverter;' {} \;

# 替换Integer类型直接传给需要Long类型方法的调用
echo "替换方法调用..."
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/findById(imageId)/findById(TypeConverter.toLong(imageId))/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/deleteById(imageId)/deleteById(TypeConverter.toLong(imageId))/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/existsById(imageId)/existsById(TypeConverter.toLong(imageId))/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/getImageMetadata(imageId)/getImageMetadata(TypeConverter.toLong(imageId))/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/submitForReview(imageId/submitForReview(TypeConverter.toLong(imageId)/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/reviewImage(imageId/reviewImage(TypeConverter.toLong(imageId)/g' {} \;
find ./src/main/java/com/medical/annotation/controller -name "*.java" -type f -exec sed -i 's/saveAnnotatedImage(imageId/saveAnnotatedImage(TypeConverter.toLong(imageId)/g' {} \;

echo "处理完成. 请编译项目检查是否还有其他错误."
echo "如需恢复备份，请运行: cp -r $BACKUP_DIR/* ./" 