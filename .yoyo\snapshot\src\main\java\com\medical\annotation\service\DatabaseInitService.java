package com.medical.annotation.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class DatabaseInitService implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitService.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    public void run(ApplicationArguments args) {
        logger.info("正在初始化数据库...");
        initImagePairsTable();
    }
    
    /**
     * 确保image_pairs表存在
     */
    private void initImagePairsTable() {
        try {
            // 检查表是否存在
            Boolean tableExists = false;
            try {
                jdbcTemplate.queryForObject("SELECT 1 FROM image_pairs LIMIT 1", Boolean.class);
                tableExists = true;
            } catch (Exception e) {
                logger.info("image_pairs表不存在，将创建表");
            }
            
            if (!tableExists) {
                logger.info("开始创建image_pairs表");
                
                // 创建image_pairs表
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS image_pairs (" +
                    "    id INT AUTO_INCREMENT PRIMARY KEY," +
                    "    metadata_id INT NOT NULL," +
                    "    image_one_path MEDIUMTEXT NOT NULL COMMENT '处理后的图像路径(temp目录)'," +
                    "    image_two_path MEDIUMTEXT COMMENT '标注后的图像路径(annotate目录)'," +
                    "    description VARCHAR(255) COMMENT '描述'," +
                    "    created_by INT COMMENT '创建用户ID'," +
                    "    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                    "    FOREIGN KEY (metadata_id) REFERENCES image_metadata(id) ON DELETE CASCADE," +
                    "    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                
                // 创建索引
                jdbcTemplate.execute(
                    "CREATE INDEX IF NOT EXISTS idx_image_pairs_metadata_id ON image_pairs (metadata_id)"
                );
                jdbcTemplate.execute(
                    "CREATE INDEX IF NOT EXISTS idx_image_pairs_created_by ON image_pairs (created_by)"
                );
                
                logger.info("image_pairs表创建成功");
            } else {
                logger.info("image_pairs表已存在，跳过创建");
            }
            
        } catch (Exception e) {
            logger.error("创建image_pairs表失败: " + e.getMessage(), e);
        }
    }
} 