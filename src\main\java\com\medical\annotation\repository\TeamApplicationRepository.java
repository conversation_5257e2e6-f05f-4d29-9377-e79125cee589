package com.medical.annotation.repository;

import com.medical.annotation.model.TeamApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TeamApplicationRepository extends JpaRepository<TeamApplication, Integer> {
    
    // 根据用户ID查找申请
    List<TeamApplication> findByUserId(Integer userId);
    
    // 根据团队ID查找申请
    List<TeamApplication> findByTeamId(Integer teamId);
    
    // 根据状态查找申请
    List<TeamApplication> findByStatus(TeamApplication.Status status);
    
    // 根据用户ID、团队ID和状态查找申请
    Optional<TeamApplication> findByUserIdAndTeamIdAndStatus(
        Integer userId, 
        Integer teamId, 
        TeamApplication.Status status
    );
} 