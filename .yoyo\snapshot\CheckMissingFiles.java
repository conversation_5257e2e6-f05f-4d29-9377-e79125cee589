package com.medical.annotation.util;

import com.medical.annotation.model.ImagePair;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * 用于检测数据库中记录的图片是否实际存在
 * 
 * 执行方式：
 * 1. 将此类复制到src/main/java/com/medical/annotation/util/目录下
 * 2. 运行应用时添加--spring.profiles.active=check-files选项
 *    例如：./mvnw spring-boot:run -Dspring-boot.run.profiles=check-files
 */
@Component
@Profile("check-files")
public class CheckMissingFiles implements CommandLineRunner {

    @Autowired
    private ImagePairRepository imagePairRepository;
    
    @Autowired
    private FileService fileService;
    
    @Override
    public void run(String... args) throws Exception {
        System.out.println("===== 开始检查图片文件 =====");
        
        // 获取所有图像对
        List<ImagePair> imagePairs = imagePairRepository.findAll();
        System.out.printf("数据库中存在 %d 个图像对记录\n", imagePairs.size());
        
        int missingFiles = 0;
        int emptyPaths = 0;
        int goodFiles = 0;
        
        // 检查每个图像对的标注图片是否存在
        for (ImagePair pair : imagePairs) {
            String path = pair.getImageTwoPath();
            
            if (path == null || path.isEmpty()) {
                emptyPaths++;
                continue;
            }
            
            // 解析路径获取实际物理文件路径
            String physicalPath = null;
            
            if (path.startsWith("/medical/images/processed/")) {
                String filename = path.substring("/medical/images/processed/".length());
                physicalPath = fileService.getProcessedDirectoryPath() + File.separator + filename;
            } else if (path.startsWith("/medical/images/annotate/")) {
                String filename = path.substring("/medical/images/annotate/".length());
                physicalPath = fileService.getAnnotateDirectoryPath() + File.separator + filename;
            } else if (path.startsWith("/images/processed/")) {
                String filename = path.substring("/images/processed/".length());
                physicalPath = fileService.getProcessedDirectoryPath() + File.separator + filename;
            } else if (path.startsWith("/images/annotate/")) {
                String filename = path.substring("/images/annotate/".length());
                physicalPath = fileService.getAnnotateDirectoryPath() + File.separator + filename;
            } else {
                // 如果是直接文件名
                String filename = path;
                if (path.contains("/")) {
                    filename = path.substring(path.lastIndexOf('/') + 1);
                }
                physicalPath = fileService.getProcessedDirectoryPath() + File.separator + filename;
            }
            
            // 检查文件是否存在
            File file = new File(physicalPath);
            if (file.exists() && file.isFile()) {
                goodFiles++;
            } else {
                missingFiles++;
                System.out.printf("ID: %d - 文件不存在: 数据库路径[%s], 物理路径[%s]\n", 
                                 pair.getId(), path, physicalPath);
                
                // 如果需要自动清理数据库中的无效路径，可以取消以下注释
                // pair.setImageTwoPath(null);
                // imagePairRepository.save(pair);
                // System.out.println("  已清除数据库中的无效路径");
            }
        }
        
        // 打印统计信息
        System.out.println("\n===== 检查完成 =====");
        System.out.printf("总图像对数: %d\n", imagePairs.size());
        System.out.printf("有效文件: %d\n", goodFiles);
        System.out.printf("空路径: %d\n", emptyPaths);
        System.out.printf("文件缺失: %d\n", missingFiles);
    }
} 