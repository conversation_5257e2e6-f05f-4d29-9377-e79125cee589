// Vuex用户模块
import axios from 'axios'

const state = {
  users: [],
  currentUser: null,
  loading: false,
  error: null
}

const getters = {
  getAllUsers: state => state.users,
  getCurrentUser: state => state.currentUser,
  isUsersLoading: state => state.loading,
  getUsersError: state => state.error
}

const mutations = {
  setUsers(state, users) {
    state.users = users
  },
  setCurrentUser(state, user) {
    state.currentUser = user
  },
  setLoading(state, status) {
    state.loading = status
  },
  setError(state, error) {
    state.error = error
  }
}

const actions = {
  // 获取用户列表
  async fetchUsers({ commit }) {
    commit('setLoading', true)
    commit('setError', null)
    
    try {
      const response = await axios.get('/api/users')
      commit('setUsers', response.data)
      return response.data
    } catch (error) {
      commit('setError', error.message)
      console.error('获取用户列表失败:', error)
    } finally {
      commit('setLoading', false)
    }
  },
  
  // 获取单个用户
  async fetchUser({ commit }, userId) {
    commit('setLoading', true)
    commit('setError', null)
    
    try {
      const response = await axios.get(`/medical/api/users/${userId}`)
      commit('setCurrentUser', response.data)
      return response.data
    } catch (error) {
      commit('setError', error.message)
      console.error('获取用户信息失败:', error)
    } finally {
      commit('setLoading', false)
    }
  }
}

export default {
  state,
  getters,
  mutations,
  actions
} 