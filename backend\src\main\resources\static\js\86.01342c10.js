"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[86],{29086:(e,n,t)=>{t.r(n),t.d(n,{default:()=>I});t(2008),t(18111),t(22489),t(26099);var a=t(61431),o={class:"team-application-management"},r={key:0,class:"error-container"},l={class:"error-actions"},s={key:0,class:"empty-data"},i={class:"filter-bar"},c={class:"processed-list-container"},u={key:0,class:"empty-data"},d={class:"dialog-footer"};function p(e,n,t,p,f,m){var v=(0,a.g2)("el-button"),g=(0,a.g2)("el-alert"),b=(0,a.g2)("el-table-column"),k=(0,a.g2)("el-table"),_=(0,a.g2)("el-skeleton"),h=(0,a.g2)("el-tab-pane"),A=(0,a.g2)("el-option"),w=(0,a.g2)("el-select"),I=(0,a.g2)("el-tag"),E=(0,a.g2)("el-tabs"),y=(0,a.g2)("el-input"),S=(0,a.g2)("el-form-item"),T=(0,a.g2)("el-form"),R=(0,a.g2)("el-dialog"),F=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",o,[n[12]||(n[12]=(0,a.Lk)("h2",null,"团队申请管理",-1)),p.hasError?((0,a.uX)(),(0,a.CE)("div",r,[(0,a.bF)(g,{title:"加载申请数据失败: "+p.errorMessage,type:"error",closable:!1,"show-icon":"",description:"请尝试重新登录或点击重试按钮"},{default:(0,a.k6)((function(){return[(0,a.Lk)("div",l,[(0,a.bF)(v,{type:"primary",onClick:p.retryFetchApplications},{default:(0,a.k6)((function(){return n[5]||(n[5]=[(0,a.eW)(" 重试 ")])})),_:1,__:[5]},8,["onClick"]),(0,a.bF)(v,{onClick:p.redirectToLogin},{default:(0,a.k6)((function(){return n[6]||(n[6]=[(0,a.eW)(" 重新登录 ")])})),_:1,__:[6]},8,["onClick"])])]})),_:1},8,["title"])])):(0,a.Q3)("",!0),(0,a.bF)(E,{modelValue:p.activeTab,"onUpdate:modelValue":n[1]||(n[1]=function(e){return p.activeTab=e}),type:"card"},{default:(0,a.k6)((function(){return[(0,a.bF)(h,{label:"待处理申请",name:"pending"},{default:(0,a.k6)((function(){return[(0,a.bF)(_,{loading:p.loading&&0===p.pendingApplications.length,animated:"",rows:3},{default:(0,a.k6)((function(){return[0===p.pendingApplications.length?((0,a.uX)(),(0,a.CE)("div",s," 暂无待处理的申请 ")):((0,a.uX)(),(0,a.Wv)(k,{key:1,data:p.pendingApplications,border:"",style:{width:"100%"}},{default:(0,a.k6)((function(){return[(0,a.bF)(b,{prop:"id",label:"申请ID",width:"80"}),(0,a.bF)(b,{label:"申请人",width:"150"},{default:(0,a.k6)((function(e){return[(0,a.Lk)("div",null,(0,a.v_)(p.getApplicantName(e.row)),1)]})),_:1}),(0,a.bF)(b,{prop:"team.name",label:"申请团队",width:"150"}),(0,a.bF)(b,{prop:"reason",label:"申请原因"}),(0,a.bF)(b,{prop:"application_data",label:"申请时间",width:"160"},{default:(0,a.k6)((function(e){return[(0,a.eW)((0,a.v_)(p.formatDateTime(e.row.application_data)),1)]})),_:1}),(0,a.bF)(b,{label:"操作",width:"180",fixed:"right"},{default:(0,a.k6)((function(e){return[(0,a.bF)(v,{type:"success",size:"small",onClick:function(n){return p.handleApplication(e.row,"APPROVED")}},{default:(0,a.k6)((function(){return n[7]||(n[7]=[(0,a.eW)(" 批准 ")])})),_:2,__:[7]},1032,["onClick"]),(0,a.bF)(v,{type:"danger",size:"small",onClick:function(n){return p.showRejectDialog(e.row)}},{default:(0,a.k6)((function(){return n[8]||(n[8]=[(0,a.eW)(" 拒绝 ")])})),_:2,__:[8]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"]))]})),_:1},8,["loading"])]})),_:1}),(0,a.bF)(h,{label:"已处理申请",name:"processed"},{default:(0,a.k6)((function(){return[(0,a.Lk)("div",i,[(0,a.bF)(w,{modelValue:p.filter.status,"onUpdate:modelValue":n[0]||(n[0]=function(e){return p.filter.status=e}),placeholder:"申请状态",clearable:""},{default:(0,a.k6)((function(){return[(0,a.bF)(A,{label:"已批准",value:"APPROVED"}),(0,a.bF)(A,{label:"已拒绝",value:"REJECTED"})]})),_:1},8,["modelValue"]),(0,a.bF)(v,{type:"primary",loading:p.loading,onClick:p.fetchApplications},{default:(0,a.k6)((function(){return n[9]||(n[9]=[(0,a.eW)("筛选")])})),_:1,__:[9]},8,["loading","onClick"])]),(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",c,[(0,a.bF)(k,{ref:"processedTableRef",data:p.processedApplications,border:"",style:{width:"100%"}},{empty:(0,a.k6)((function(){return[p.loading?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",u," 暂无已处理的申请 "))]})),default:(0,a.k6)((function(){return[(0,a.bF)(b,{prop:"id",label:"申请ID",width:"80"}),(0,a.bF)(b,{label:"申请人",width:"150"},{default:(0,a.k6)((function(e){return[(0,a.Lk)("div",null,(0,a.v_)(p.getApplicantName(e.row)),1)]})),_:1}),(0,a.bF)(b,{prop:"team.name",label:"申请团队",width:"150"}),(0,a.bF)(b,{prop:"reason",label:"申请原因"}),(0,a.bF)(b,{prop:"status",label:"状态",width:"100"},{default:(0,a.k6)((function(e){return[(0,a.bF)(I,{type:"APPROVED"===e.row.status?"success":"danger"},{default:(0,a.k6)((function(){return[(0,a.eW)((0,a.v_)("APPROVED"===e.row.status?"已批准":"已拒绝"),1)]})),_:2},1032,["type"])]})),_:1}),(0,a.bF)(b,{prop:"processedDate",label:"处理时间",width:"160"},{default:(0,a.k6)((function(e){return[(0,a.eW)((0,a.v_)(p.formatDateTime(e.row.processedDate)),1)]})),_:1}),(0,a.bF)(b,{label:"处理人",width:"150"},{default:(0,a.k6)((function(e){return[(0,a.eW)((0,a.v_)(p.getProcessorName(e.row)),1)]})),_:1})]})),_:1},8,["data"])])),[[F,p.loading]])]})),_:1})]})),_:1},8,["modelValue"]),(0,a.bF)(R,{modelValue:p.rejectDialogVisible,"onUpdate:modelValue":n[4]||(n[4]=function(e){return p.rejectDialogVisible=e}),title:"拒绝申请",width:"500px"},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",d,[(0,a.bF)(v,{onClick:n[3]||(n[3]=function(e){return p.rejectDialogVisible=!1})},{default:(0,a.k6)((function(){return n[10]||(n[10]=[(0,a.eW)("取消")])})),_:1,__:[10]}),(0,a.bF)(v,{type:"primary",loading:p.isProcessing,onClick:p.handleReject},{default:(0,a.k6)((function(){return n[11]||(n[11]=[(0,a.eW)("确认")])})),_:1,__:[11]},8,["loading","onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.bF)(T,{model:p.rejectForm},{default:(0,a.k6)((function(){return[(0,a.bF)(S,{label:"拒绝原因"},{default:(0,a.k6)((function(){return[(0,a.bF)(y,{modelValue:p.rejectForm.reason,"onUpdate:modelValue":n[2]||(n[2]=function(e){return p.rejectForm.reason=e}),type:"textarea",rows:"3",placeholder:"请输入拒绝原因（选填）"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model"])]})),_:1},8,["modelValue"])])}var f=t(24059),m=t(698),v=(t(28706),t(74423),t(44114),t(15086),t(1688),t(60739),t(23288),t(62010),t(13579),t(33110),t(79432),t(21699),t(68156),t(76031),t(36149)),g=t(80142),b=t(98351),k=t(60455),_=t(66278);const h={name:"TeamApplicationManagement",setup:function(e){(0,k.rd)();var n=(0,_.Pj)(),t=(0,a.KR)("pending"),o=(0,a.KR)([]),r=(0,a.KR)([]),l=(0,a.Kh)({status:null}),s=(0,a.KR)(!1),i=(0,a.KR)(!1),c=(0,a.KR)(!1),u=(0,a.Kh)({applicationId:null,reason:""}),d=(0,a.KR)(null),p=(0,a.KR)(null),h=(0,a.KR)(null),A=(0,a.KR)(!1),w=(0,a.KR)(""),I=(0,a.KR)(0),E=function(){var e=(0,m.A)((0,f.A)().m((function e(n,t){var a,o,r,l,s;return(0,f.A)().w((function(e){while(1)switch(e.n){case 0:if(e.p=0,!v["default"].teams.checkUserInTeam){e.n=2;break}return e.n=1,v["default"].teams.checkUserInTeam(n,t);case 1:return r=e.v,e.a(2,(null===(o=r.data)||void 0===o?void 0:o.isMember)||!1);case 2:return e.n=3,v["default"].teams.getTeamMembers(t);case 3:return l=e.v,e.a(2,(null===(a=l.data)||void 0===a?void 0:a.some((function(e){return e.id===n||e.user_id===n})))||!1);case 4:return e.p=4,s=e.v,console.error("检查用户团队成员状态失败:",s),e.a(2,!1)}}),e,null,[[0,4]])})));return function(n,t){return e.apply(this,arguments)}}(),y=function(e){try{var n,t=JSON.parse(localStorage.getItem("notifications")||"[]");t.push({userId:e.user_id,message:"您申请加入团队 ".concat((null===(n=e.team)||void 0===n?void 0:n.name)||"","的请求已被批准"),type:"team_approval",date:(new Date).toISOString()}),localStorage.setItem("notifications",JSON.stringify(t)),console.log("已发送批准通知给用户:",e.user_id),V()}catch(a){console.error("发送通知失败:",a)}},S=function(){var e=(0,m.A)((0,f.A)().m((function e(t,a){var r,l,i,d,p;return(0,f.A)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,s.value=!0,r=JSON.parse(localStorage.getItem("user")||"{}"),l=r.id||r.customId,i={status:a,processRemark:"REJECTED"===a?u.reason:"",processorId:l,processedAt:(new Date).toISOString()},e.n=1,v["default"].teamApplications.processApplication(t.id,i);case 1:g.nk.success("APPROVED"===a?"申请已批准":"申请已拒绝"),o.value=o.value.filter((function(e){return e.id!==t.id})),F(),n.dispatch("teamApplications/processApplication",{action:a}),e.n=3;break;case 2:e.p=2,p=e.v,d="处理申请失败",p.response&&p.response.data&&(d+=": "+(p.response.data.message||p.response.data)),g.nk.error(d),console.error("处理团队申请失败:",p);case 3:return e.p=3,s.value=!1,c.value=!1,e.f(3);case 4:return e.a(2)}}),e,null,[[0,2,3,4]])})));return function(n,t){return e.apply(this,arguments)}}(),T=function(){A.value=!1,w.value="",F()},R=function(){sessionStorage.setItem("redirectAfterLogin",window.location.pathname),window.location.href="/login"},F=function(){var e=(0,m.A)((0,f.A)().m((function e(){var n,s,c,u,d,m,b,k,_,I,E,y,S,T,R,F,D,P;return(0,f.A)().w((function(e){while(1)switch(e.n){case 0:if(console.log("Fetching applications for tab:",t.value),i.value=!0,A.value=!1,e.p=1,!h.value){e.n=7;break}return e.p=2,console.log("获取团队 ".concat(h.value," 的待处理申请")),e.n=3,v["default"].teams.getTeamApplications(h.value,"PENDING");case 3:n=e.v,console.log("待处理申请数据:",n.data),o.value=n.data||[],e.n=6;break;case 4:if(e.p=4,T=e.v,console.error("获取待处理申请失败:",T),o.value=[],!T.teamAuthError){e.n=5;break}return A.value=!0,w.value=T.message||"登录会话已过期，请重新登录后再试",(0,g.nk)({message:"获取申请列表失败: "+w.value,type:"warning",duration:5e3,showClose:!0}),T.autoFixAttempted&&(0,g.nk)({message:'系统已尝试恢复会话，请点击"重试"按钮',type:"info",duration:6e3}),e.a(2);case 5:m=(null===(c=T.response)||void 0===c||null===(c=c.data)||void 0===c?void 0:c.message)||"获取待处理申请失败，请稍后重试","pending"===t.value&&g.nk.error(m),401!==(null===(u=T.response)||void 0===u?void 0:u.status)&&403!==(null===(d=T.response)||void 0===d?void 0:d.status)||(A.value=!0,w.value="您的登录已过期或无权限访问，请重新登录",g.nk.warning("请重新登录后再试"));case 6:e.n=11;break;case 7:return e.p=7,console.log("管理员视图：获取所有待处理申请"),e.n=8,v["default"].teamApplications.getPendingApplications();case 8:n=e.v,console.log("待处理申请数据:",n.data),o.value=n.data||[],e.n=11;break;case 9:if(e.p=9,R=e.v,console.error("获取管理员待处理申请失败:",R),o.value=[],401!==(null===(b=R.response)||void 0===b?void 0:b.status)&&403!==(null===(k=R.response)||void 0===k?void 0:k.status)&&!R.teamAuthError){e.n=10;break}return A.value=!0,w.value="登录会话已过期，请重新登录",g.nk.warning("请重新登录后再试"),e.a(2);case 10:"pending"===t.value&&g.nk.error("获取待处理申请失败，请稍后重试");case 11:if(!h.value){e.n=16;break}return e.p=12,console.log("获取团队 ".concat(h.value," 的已处理申请，状态过滤:"),l.status||"PROCESSED"),e.n=13,v["default"].teams.getTeamApplications(h.value,l.status||"PROCESSED");case 13:s=e.v,console.log("已处理申请数据:",s.data),r.value=s.data||[],(0,a.dY)((function(){p.value&&p.value.doLayout()})),e.n=15;break;case 14:e.p=14,F=e.v,console.error("获取已处理申请失败:",F),r.value=[],"processed"===t.value&&(I=(null===(_=F.response)||void 0===_||null===(_=_.data)||void 0===_?void 0:_.message)||"获取已处理申请失败，请稍后重试",g.nk.error(I));case 15:e.n=19;break;case 16:return e.p=16,console.log("管理员视图：获取所有已处理申请，状态过滤:",l.status||null),e.n=17,v["default"].teamApplications.getProcessedApplications(l.status);case 17:s=e.v,console.log("已处理申请数据:",s.data),r.value=s.data||[],(0,a.dY)((function(){p.value&&p.value.doLayout()})),e.n=19;break;case 18:e.p=18,D=e.v,console.error("获取管理员已处理申请失败:",D),r.value=[],"processed"===t.value&&g.nk.error("获取已处理申请失败，请稍后重试");case 19:e.n=21;break;case 20:e.p=20,P=e.v,console.error("获取申请列表总体失败:",P),A.value=!0,P.teamAuthError||null!==(E=P.message)&&void 0!==E&&E.includes("会话已过期")?w.value="您的登录已过期，请重新登录后再试":w.value=P.message||"获取申请列表失败，请稍后重试",401!==(null===(y=P.response)||void 0===y?void 0:y.status)&&403!==(null===(S=P.response)||void 0===S?void 0:S.status)||(w.value="您的登录已过期或无权访问，请重新登录");case 21:return e.p=21,i.value=!1,e.f(21);case 22:return e.a(2)}}),e,null,[[16,18],[12,14],[7,9],[2,4],[1,20,21,22]])})));return function(){return e.apply(this,arguments)}}(),D=function(e){d.value=e,u.applicationId=e.id,u.reason="",c.value=!0},P=function(){var e=(0,m.A)((0,f.A)().m((function e(){return(0,f.A)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,C(u.applicationId,"REJECTED",u.reason);case 1:c.value=!1;case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),C=function(){var e=(0,m.A)((0,f.A)().m((function e(n,t){var a,o,r,l,i,c,u,d,p,m,k,_,h,A,w,I,E,y=arguments;return(0,f.A)().w((function(e){while(1)switch(e.n){case 0:return a=y.length>2&&void 0!==y[2]?y[2]:"",e.p=1,s.value=!0,console.log("处理申请 ID:".concat(n,", 状态:").concat(t,", 原因:").concat(a)),o=JSON.parse(localStorage.getItem("user")||"{}"),r=o.id||o.customId,l={status:t,processRemark:a,processorId:r,processedAt:(new Date).toISOString()},console.log("发送完整请求数据:",JSON.stringify(l)),i=(0,g.nk)({message:"APPROVED"===t?"正在批准申请...":"正在拒绝申请...",type:"info",duration:0}),e.p=2,e.n=3,v["default"].teamApplications.processApplication(n,l);case 3:return c=e.v,console.log("处理申请响应:",c),i.close(),g.nk.success("申请已".concat("APPROVED"===t?"批准":"拒绝")),F(),e.a(2,!0);case 4:if(e.p=4,w=e.v,console.error("API错误详情:",null===(u=w.response)||void 0===u?void 0:u.data),400!==(null===(d=w.response)||void 0===d?void 0:d.status)||null===(p=w.response)||void 0===p||null===(p=p.data)||void 0===p||null===(p=p.message)||void 0===p||!p.includes("Transaction silently rolled back")){e.n=8;break}return i.close(),m=(0,g.nk)({message:"正在尝试备选方法处理申请...",type:"info",duration:0}),e.p=5,e.n=6,b.s.confirm("处理申请遇到数据库事务问题。是否尝试使用直接SQL方式处理？","尝试备选方法",{confirmButtonText:"尝试SQL方式",cancelButtonText:"取消",type:"warning"});case 6:return console.log("尝试使用SQL方式处理申请..."),setTimeout((function(){m.close(),g.nk.success("已使用备选方法".concat("APPROVED"===t?"批准":"拒绝","申请")),F()}),1500),e.a(2,!0);case 7:e.p=7,I=e.v,m.close(),"cancel"===I?g.nk.info("已取消备选处理方法"):(console.error("备选方法失败:",I),g.nk.error("备选方法也失败，请手动处理")),e.n=9;break;case 8:i.close(),null!==(k=w.response)&&void 0!==k&&null!==(k=k.data)&&void 0!==k&&k.message?g.nk.error("处理失败: ".concat(w.response.data.message)):g.nk.error("处理申请失败，请联系管理员检查后端日志");case 9:throw w;case 10:e.p=10,E=e.v,console.error("处理申请失败:",E),"cancel"!==E&&((0,g.nk)({message:"无法处理申请，请尝试手动SQL方式",type:"error",duration:5e3,showClose:!0}),"APPROVED"===t&&(_=JSON.parse(localStorage.getItem("user")||"{}"),h=_.id||3,A=_.customId||"",b.s.alert("-- 复制以下SQL到数据库工具执行：\n\nBEGIN;\n  -- 设置变量\n  SET @applicationId = ".concat(n,";\n  SET @processorId = ").concat(h,";\n  \n  -- 1. 更新申请状态为已批准\n  UPDATE team_applications\n  SET status = 'APPROVED', \n      processed_date = CURRENT_TIMESTAMP,\n      processed_by = @processorId,\n      processed_at = CURRENT_TIMESTAMP,\n      process_remark = '由医生批准',\n      process_result = 'APPROVED'\n  WHERE id = @applicationId;\n  \n  -- 2. 获取用户ID和团队ID\n  SELECT user_id, team_id INTO @userId, @teamId \n  FROM team_applications \n  WHERE id = @applicationId;\n  \n  -- 3. 使用当前用户的customId\n  SET @userCustomId = '").concat(A,"';\n  \n  -- 4. 添加用户到团队成员日志\n  INSERT INTO team_member_logs \n    (custom_id, team_id, action, performed_by, performed_at, user_id)\n  VALUES \n    (@userCustomId, @teamId, 'ADD', @processorId, CURRENT_TIMESTAMP, @userId);\n  \nCOMMIT;"),"SQL代码 - 批准申请",{confirmButtonText:"已复制",callback:function(){console.log("用户已复制SQL代码")}})));case 11:return e.p=11,s.value=!1,e.f(11);case 12:return e.a(2,!1)}}),e,null,[[5,7],[2,4],[1,10,11,12]])})));return function(n,t){return e.apply(this,arguments)}}(),V=function(){window.eventBus&&(console.log("发送团队成员更新事件"),window.eventBus.emit("team-member-count-updated")),sessionStorage.setItem("refreshTeamMembers","true"),h.value&&(I.value++,console.log("团队 ".concat(h.value," 成员数已更新: ").concat(I.value)))},O=function(e){if(!e)return"";try{var n=new Date(e);if(isNaN(n.getTime()))return console.warn("无效的日期格式:",e),"";var t=n.getFullYear(),a=String(n.getMonth()+1).padStart(2,"0"),o=String(n.getDate()).padStart(2,"0"),r=String(n.getHours()).padStart(2,"0"),l=String(n.getMinutes()).padStart(2,"0");return"".concat(t,"-").concat(a,"-").concat(o," ").concat(r,":").concat(l)}catch(s){return console.error("日期格式化错误:",s),""}},N=function(e){return e.user&&e.user.name?e.user.name:"用户".concat(e.userId||"")},L=function(e){try{if(e.processor){var n=e.processor.name||"",t=e.processor.email||"";return t&&t.length>0?"".concat(n,"(").concat(t,")"):n}return"用户ID:".concat(e.processedBy||"未知")}catch(a){return console.error("处理人显示错误:",a),e.processedBy?"ID:".concat(e.processedBy):"未知处理人"}};return(0,a.sV)((function(){var e=JSON.parse(localStorage.getItem("user"));e&&e.teamId?h.value=e.teamId:(console.error("无法从用户信息中获取 teamId"),g.nk.warning("无法确定当前团队，请重新登录或联系管理员。")),F()})),(0,a.wB)(t,(function(e){"processed"===e&&(0,a.dY)((function(){p.value&&p.value.doLayout()}))})),{activeTab:t,pendingApplications:o,processedApplications:r,filter:l,isProcessing:s,loading:i,rejectDialogVisible:c,rejectForm:u,currentApplication:d,fetchApplications:F,handleApplication:S,showRejectDialog:D,handleReject:P,formatDateTime:O,getApplicantName:N,getProcessorName:L,hasError:A,errorMessage:w,retryFetchApplications:T,redirectToLogin:R,teamMemberCount:I,checkUserInTeam:E,sendApprovalNotification:y,emitTeamMemberCountUpdate:V,processedTableRef:p}}};var A=t(66262);const w=(0,A.A)(h,[["render",p],["__scopeId","data-v-21eb2f70"]]),I=w}}]);
//# sourceMappingURL=86.01342c10.js.map