<template>
  <div class="annotation-page">
    <div class="page-header">
      <div class="header-left">
      <h2>图像标注系统</h2>
      <p>在图像上绘制矩形区域，添加标签信息以标注病变区域</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="nextStep">下一步</el-button>
        <el-button @click="showSaveDialog">保存并退出</el-button>
      </div>
    </div>
    
    <div class="main-content">
      <div class="image-selector">
        <h3>选择图像</h3>
        <el-select 
          v-model="selectedImageId" 
          placeholder="请选择图像" 
          @change="loadSelectedImage"
          style="width: 100%;"
        >
          <el-option 
            v-for="image in availableImages" 
            :key="image.id" 
            :label="image.original_name || `图像 #${image.id}`" 
            :value="image.id" 
          />
        </el-select>
        
        <div class="image-metadata" v-if="selectedImage">
          <h4>图像信息</h4>
          <p><strong>ID:</strong> {{ selectedImage.id }}</p>
          <p><strong>原始文件名:</strong> {{ selectedImage.original_name }}</p>
          <p><strong>上传时间:</strong> {{ formatDate(selectedImage.created_at) }}</p>
          <p v-if="selectedImage.patient_name"><strong>患者:</strong> {{ selectedImage.patient_name }}</p>
          <p v-if="selectedImage.case_number"><strong>病例号:</strong> {{ selectedImage.case_number }}</p>
        </div>
      </div>
      
      <div class="annotation-container">
        <template v-if="selectedImage">
          <ImageAnnotator 
            :imageUrl="getImageUrl(selectedImage.path)" 
            :imageId="selectedImage.id"
            @annotations-saved="onAnnotationsSaved"
            ref="annotator"
          />
        </template>
        <div v-else class="no-image-placeholder">
          <el-empty description="请选择一张图像进行标注"></el-empty>
        </div>
      </div>
    </div>
    
    <!-- 保存并退出确认对话框 -->
    <ConfirmDialog
      v-model="saveDialogVisible"
      title="保存并退出"
      message="是否保留当前标注进度？下次可以继续完成"
      confirm-text="保留进度"
      cancel-text="不保留"
      icon="warning"
      @confirm="handleSaveProgress"
      @cancel="handleDiscardProgress"
    />
    
    <!-- 加载提示 -->
    <el-dialog
      title="提示"
      :visible.sync="resumeDialogVisible"
      width="30%"
    >
      <p>检测到您有未完成的标注任务，是否继续？</p>
      <p v-if="savedProgress" class="progress-info">
        上次保存于：{{ formatDate(savedProgress.lastUpdated) }}
      </p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="discardSavedProgress">不需要，重新开始</el-button>
          <el-button type="primary" @click="resumeSavedProgress">继续上次任务</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import ImageAnnotator from '@/components/ImageAnnotator.vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import api from '@/utils/api';
import { formatDate } from '@/utils/formatters';
import { getImageUrl } from '@/utils/imageHelper';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'ImageAnnotationPage',
  components: {
    ImageAnnotator,
    ConfirmDialog
  },
  data() {
    return {
      availableImages: [],
      selectedImageId: null,
      selectedImage: null,
      loading: false,
      error: null,
      annotations: [], // 存储标注数据
      saveDialogVisible: false, // 保存进度对话框
      resumeDialogVisible: false // 恢复进度对话框
    };
  },
  computed: {
    ...mapGetters(['getAnnotationProgress', 'hasUnfinishedAnnotation']),
    // 获取已保存的进度
    savedProgress() {
      return this.getAnnotationProgress;
    }
  },
  created() {
    console.log('ImageAnnotationPage 已创建');
    
    // 检查是否有保存的标注进度
    if (this.hasUnfinishedAnnotation) {
      console.log('检测到未完成的标注任务：', this.savedProgress);
      this.resumeDialogVisible = true;
    } else {
      // 检查URL中的imageId参数
      const urlImageId = this.$route.query.imageId;
      if (urlImageId) {
        console.log('从URL加载图像ID:', urlImageId);
        this.selectedImageId = parseInt(urlImageId);
        this.fetchImageDetails(this.selectedImageId);
      } else {
    this.fetchAvailableImages();
      }
    }
  },
  methods: {
    ...mapActions(['saveProgress', 'completeAnnotation']),
    formatDate,
    getImageUrl,
    
    // 获取图像详情
    fetchImageDetails(imageId) {
      if (!imageId) return;
      
      this.loading = true;
      api.images.getOne(imageId)
        .then(response => {
          this.selectedImage = response.data;
          // 保存进度
          this.saveProgress({
            step: 1, // 图像标注步骤
            imageId: imageId,
            formData: null
          });
          this.loading = false;
        })
        .catch(error => {
          this.error = "加载图像详情失败: " + (error.response?.data || error.message);
          this.loading = false;
          this.$message.error("加载图像详情失败");
          console.error('Failed to load image details', error);
        });
    },
    
    fetchAvailableImages() {
      this.loading = true;
      api.images.getAll()
        .then(response => {
          this.availableImages = response.data;
          this.loading = false;
          
          // 如果有URL参数指定图像ID，则加载该图像
          if (this.selectedImageId) {
            this.loadSelectedImage();
          }
        })
        .catch(error => {
          this.error = "加载图像列表失败: " + (error.response?.data || error.message);
          this.loading = false;
          console.error('Failed to load images', error);
        });
    },
    
    loadSelectedImage() {
      if (!this.selectedImageId) {
        this.selectedImage = null;
        return;
      }
      
      this.fetchImageDetails(this.selectedImageId);
    },
    
    onAnnotationsSaved(annotations) {
      console.log('标注已保存', annotations);
      this.annotations = annotations;
      this.$message.success(`已成功保存 ${annotations.length} 个标注`);
    },
    
    // 保存标注并进入下一步
    nextStep() {
      // 确保已选择图像
      if (!this.selectedImageId || !this.selectedImage) {
        this.$message.warning('请先选择一个图像');
        return;
      }
      
      // 先保存当前标注
      if (this.$refs.annotator && typeof this.$refs.annotator.saveAnnotations === 'function') {
        this.$refs.annotator.saveAnnotations();
      }
      
      // 保存进度，更新为第2步（病例信息填写）
      this.saveProgress({
        step: 2, // 病例信息填写步骤
        imageId: this.selectedImageId,
        formData: null
      });
      
      // 跳转到病例信息填写页面
      this.$router.push({
        path: '/cases/structured-form',
        query: { imageId: this.selectedImageId }
      });
    },
    
    // 显示保存进度对话框
    showSaveDialog() {
      // 确保已选择图像
      if (!this.selectedImageId || !this.selectedImage) {
        this.$router.push('/cases/new');
        return;
      }
      
      // 先保存当前标注
      if (this.$refs.annotator && typeof this.$refs.annotator.saveAnnotations === 'function') {
        this.$refs.annotator.saveAnnotations();
      }
      
      // 显示确认对话框
      this.saveDialogVisible = true;
    },
    
    // 处理保留标注进度
    handleSaveProgress() {
      // 确保已保存进度
      this.saveProgress({
        step: 1, // 图像标注步骤
        imageId: this.selectedImageId,
        formData: null
      });
      
      // 确保用户信息存储到会话中
      try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (user) {
          sessionStorage.setItem('preservedUser', user);
        }
      } catch (e) {
        console.error('保存用户信息时出错:', e);
      }
      
      this.$message.success('已保存标注进度');
      
      // 使用更可靠的方法导航到工作台
      window.location.replace('/app/dashboard');
    },
    
    // 处理丢弃标注进度
    handleDiscardProgress() {
      // 清除标注进度
      this.completeAnnotation();
      
      // 确保用户信息存储到会话中
      try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (user) {
          sessionStorage.setItem('preservedUser', user);
        }
      } catch (e) {
        console.error('保存用户信息时出错:', e);
      }
      
      this.$message.info('已清除标注进度');
      
      // 使用更可靠的方法导航到工作台
      window.location.replace('/app/dashboard');
    },
    
    // 恢复已保存的进度
    resumeSavedProgress() {
      if (!this.savedProgress || !this.savedProgress.imageId) {
        this.resumeDialogVisible = false;
        return;
      }
      
      // 加载已保存的图像
      this.selectedImageId = this.savedProgress.imageId;
      this.fetchImageDetails(this.selectedImageId);
      this.resumeDialogVisible = false;
    },
    
    // 丢弃已保存的进度
    discardSavedProgress() {
      this.completeAnnotation();
      
      // 检查URL中的imageId参数
      const urlImageId = this.$route.query.imageId;
      if (urlImageId) {
        this.selectedImageId = parseInt(urlImageId);
        this.fetchImageDetails(this.selectedImageId);
      } else {
        this.fetchAvailableImages();
      }
      
      this.resumeDialogVisible = false;
    }
  }
};
</script>

<style scoped>
.annotation-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.main-content {
  display: flex;
  gap: 20px;
}

.image-selector {
  width: 300px;
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
}

.image-selector h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #303133;
}

.image-metadata {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.image-metadata h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
}

.image-metadata p {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}

.annotation-container {
  flex-grow: 1;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0;
  overflow: hidden;
}

.no-image-placeholder {
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-info {
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

@media (max-width: 992px) {
  .main-content {
    flex-direction: column;
  }
  
  .image-selector {
    width: 100%;
  }
}
</style> 