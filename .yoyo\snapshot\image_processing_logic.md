# 血管瘤辅助系统 - 图像处理与保存逻辑

## 问题分析

系统目前存在的问题是：用户上传图片后，标注完成并保存，但图片没有被正确地保存到指定的文件夹中。

## 系统流程

### 1. 图像处理流程

1. **图像上传**：
   - 用户通过前端界面上传图片
   - 图片通过 `/api/images/upload` 端点上传到服务器
   - 服务器使用 `FileService.processAndSaveImage()` 方法处理图片并保存到 `F:/medical_images/processed` 目录
   - 创建图像路径记录并保存到数据库的表image_pairs里面的image_one_path(也就是保存图片的路径)

2. **图像标注**：
   - 用户在前端界面上对图片进行标注（添加矩形框和标签）
   - 每个标注通过 `/api/tags` 端点保存到数据库的image_tags表
   - 标注数据包括图像ID、标签类型、中心点坐标、宽高（中心点坐标和宽高都需要归一化）

3. **保存标注后的图像**：
   - 用户点击"保存退出"或"保存并填写表单"按钮
   - 系统应该将带有标注的图片保存到 `F:/medical_images/annotated` 目录
   - 更新图像状态为已标注（submitted）

### 2. 问题所在

经过分析，问题出在第三步：系统没有将标注后的图片实际保存到文件系统中。前端调用了 `saveAnnotatedImage()` 方法，但这个方法只是更新了数据库中的图像对（image_pairs）记录，没有真正将图片保存到文件系统。

## 解决方案

### 1. 后端修改

1. **新增API端点**：
   - 在 `TagApiController` 中添加 `/api/tags/save-image-after-annotation` 端点
   - 该端点接收图像ID，查找该图像的所有标注
   - 使用 `ImageUtil.drawMultipleAnnotations()` 在原图上绘制标注
   - 使用 `FileService.saveAnnotatedImage()` 将标注后的图片保存到 `F:/medical_images/annotated` 目录
   - 更新图像状态为 `submitted`

```java
@PostMapping("/save-image-after-annotation")
public ResponseEntity<?> saveImageAfterAnnotation(@RequestBody Map<String, Object> requestData) {
    try {
        // 获取图像元数据ID
        Integer metadataId = convertToInteger(requestData.get("metadata_id"));
        
        // 获取图像元数据
        Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(metadataId);
        if (!imageMetadataOpt.isPresent()) {
            System.err.println("图像元数据不存在: metadataId=" + metadataId);
            return ResponseEntity.badRequest().body("Image metadata not found for ID: " + metadataId);
        }
        
        ImageMetadata imageMetadata = imageMetadataOpt.get();
        String processedImagePath = imageMetadata.getPath();
        
        // 获取该图像的所有标注
        List<Tag> tags = tagRepository.findByMetadataId(metadataId);
        if (tags.isEmpty()) {
            System.err.println("图像没有标注数据: metadataId=" + metadataId);
            return ResponseEntity.badRequest().body("No tags found for image ID: " + metadataId);
        }
        
        System.out.println("找到标注数量: " + tags.size() + ", 准备绘制标注");
        
        // 准备标注数据
        Object[][] annotations = new Object[tags.size()][5];
        for (int i = 0; i < tags.size(); i++) {
            Tag tag = tags.get(i);
            
            // 从数据库获取标注坐标（已归一化的中心点坐标和宽高）
            double normalizedX = tag.getX();
            double normalizedY = tag.getY();
            double normalizedWidth = tag.getWidth();
            double normalizedHeight = tag.getHeight();
            String label = tag.getTag();
            
            int actualWidth = imageMetadata.getWidth();
            int actualHeight = imageMetadata.getHeight();
            
            // 计算实际像素坐标（将归一化的中心点坐标转换为左上角坐标）
            int pixelX = (int) ((normalizedX - normalizedWidth / 2) * actualWidth);
            int pixelY = (int) ((normalizedY - normalizedHeight / 2) * actualHeight);
            int pixelWidth = (int) (normalizedWidth * actualWidth);
            int pixelHeight = (int) (normalizedHeight * actualHeight);
            
            annotations[i] = new Object[]{pixelX, pixelY, pixelWidth, pixelHeight, label};
            System.out.println("标注 " + i + ": " + label + " 在 (" + pixelX + "," + pixelY + ") 大小 " + pixelWidth + "x" + pixelHeight);
        }
        
        // 绘制标注
        System.out.println("从路径加载图像: " + processedImagePath);
        BufferedImage annotatedImage = ImageUtil.drawMultipleAnnotations(processedImagePath, annotations);
        
        // 保存标注后的图像
        String annotatedImagePath = fileService.saveAnnotatedImage(processedImagePath, annotatedImage);
        System.out.println("标注图像已保存到: " + annotatedImagePath);
        
        // 更新图像元数据状态
        imageMetadata.setStatus(ImageMetadata.Status.submitted);
        imageMetadataRepository.save(imageMetadata);
        System.out.println("图像状态已更新为已提交");
        
        // 更新image_pairs表中的image_two_path字段
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(metadataId);
        if (!imagePairs.isEmpty()) {
            ImagePair imagePair = imagePairs.get(0);
            imagePair.setImageTwoPath(annotatedImagePath);
            imagePairRepository.save(imagePair);
            System.out.println("已更新image_pairs表中的image_two_path字段");
        }
        
        // 返回结果
        Map<String, Object> response = new HashMap<>();
        response.put("annotated_image_path", annotatedImagePath);
        response.put("message", "图像标注保存成功");
        
        return ResponseEntity.ok(response);
        
    } catch (Exception e) {
        System.err.println("保存标注图像失败: " + e.getMessage());
        e.printStackTrace();
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Failed to save annotated image: " + e.getMessage());
    }
}
```

### 2. 前端修改

1. **API工具类添加方法**：
   - 在 `api.js` 中的 `tags` 对象中添加 `saveAnnotatedImage` 方法
   - 该方法调用新增的API端点

```javascript
// 保存标注后的图像
saveAnnotatedImage(imageId) {
  return apiClient.post('/tags/save-image-after-annotation', { metadata_id: imageId });
}
```

2. **修改保存按钮处理逻辑**：
   - 修改 `CaseDetailForm.vue` 中的 `saveAndExit` 和 `saveAndNext` 方法
   - 使用新的API方法替换原有的保存逻辑

```javascript
saveAndExit() {
  // 检查是否有至少一个标注框
  if (this.annotations.length === 0) {
    this.$message.warning('请至少添加一个标注框后再保存');
    return;
  }
  
  // 显示加载状态
  const loading = this.$loading({
    lock: true,
    text: '保存中...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  // 获取当前图像
  const currentImage = this.uploadedImages[this.currentImageIndex]
  if (!currentImage || !currentImage.id) {
    this.$message.error('图像信息不完整，无法保存标注')
    loading.close()
    return
  }
  
  // 保存标注后的图像
  api.tags.saveAnnotatedImage(currentImage.id)
    .then(response => {
      console.log('标注图像保存成功', response.data)
      
      // 保存标注数据到本地存储
      localStorage.setItem('annotations', JSON.stringify(this.annotations))
      
      // 显示成功消息
      this.$message.success('标注已保存')
      
      // 关闭加载状态
      loading.close()
      
      // 返回列表页面
      this.$router.push('/cases')
    })
    .catch(error => {
      console.error('保存失败', error)
      this.$message.error('保存标注失败: ' + (error.response?.data || error.message || '未知错误'))
      loading.close()
    })
}
```

## 可能需要注意的问题

1. **路径问题**：
   - 确保 `F:/medical_images/processed` 和 `F:/medical_images/annotated` 目录存在并有写入权限
   - 检查图像路径是否正确，特别是在从数据库读取路径时

2. **图像格式问题**：
   - 确保 `ImageIO.write()` 方法使用正确的图像格式（扩展名）
   - 处理可能的图像格式转换错误

3. **坐标转换问题**：
   - 确保正确处理归一化的中心点坐标和宽高，将其转换为实际像素坐标
   - 注意坐标系统的转换（从中心点坐标转为左上角坐标）

4. **错误处理**：
   - 添加详细的日志记录，便于调试
   - 提供清晰的错误消息给用户

## 测试方案

1. 上传一张新图片
2. 添加几个标注
3. 点击"保存退出"按钮
4. 检查 `F:/medical_images/annotated` 目录中是否有新的图片文件
5. 检查生成的图片是否包含正确的标注
6. 验证数据库中的image_pairs表是否正确更新了image_two_path字段

## 后续优化建议

1. **批量处理**：
   - 添加批量保存标注图像的功能，提高效率

2. **异步处理**：
   - 考虑使用异步方式处理图像，避免长时间阻塞用户界面

3. **图像压缩**：
   - 添加图像压缩选项，减少存储空间占用

4. **备份机制**：
   - 添加图像备份机制，防止数据丢失

5. **预览功能**：
   - 在保存前提供标注预览功能 