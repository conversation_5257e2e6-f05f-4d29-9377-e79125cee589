{"name": "medical-annotation-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "deploy": "node build-and-deploy.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^0.27.2", "core-js": "^3.8.3", "element-plus": "^2.9.10", "lodash": "^4.17.21", "mitt": "^3.0.1", "regenerator-runtime": "^0.14.1", "vue": "^3.2.13", "vue-router": "^4.0.15", "vuex": "^4.0.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@stagewise/toolbar-vue": "^0.1.2", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "fs-extra": "^11.2.0", "https-browserify": "^1.0.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5"}}