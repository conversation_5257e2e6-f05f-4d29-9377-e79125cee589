// 权限控制指令

import store from '../store'
import { hasPermission as checkPermission } from '../utils/permissions'

/**
 * 使用方法：
 * 1. v-permission="'permission_name'"  - 检查单个权限
 * 2. v-permission="['permission1', 'permission2']" - 检查多个权限（OR关系）
 * 3. v-permission:all="['permission1', 'permission2']" - 检查多个权限（AND关系）
 * 4. v-permission:role="'ADMIN'" - 直接检查角色
 */
export const permission = {
  mounted(el, binding) {
    const { value, arg } = binding
    const userRole = store.getters.getUserRole
    
    // 添加调试日志
    console.log('权限指令检查:', {
      权限值: value,
      参数: arg,
      用户角色: userRole
    });
    
    if (!userRole) {
      disableElement(el);
      return
    }
    
    // 直接检查角色
    if (arg === 'role') {
      const roles = Array.isArray(value) ? value : [value]
      if (!roles.includes(userRole)) {
        disableElement(el);
      }
      return
    }
    
    // 检查权限
    if (Array.isArray(value)) {
      // 多个权限
      if (arg === 'all') {
        // AND逻辑 - 必须拥有所有权限
        const hasAllPermissions = value.every(permission => 
          checkPermission(userRole, permission)
        )
        if (!hasAllPermissions) {
          disableElement(el);
        }
      } else {
        // OR逻辑 - 拥有其中一个权限即可
        const hasAnyPermission = value.some(permission => 
          checkPermission(userRole, permission)
        )
        if (!hasAnyPermission) {
          disableElement(el);
        }
      }
    } else {
      // 单个权限
      const hasPermission = checkPermission(userRole, value);
      console.log(`权限检查结果: ${value} -> ${hasPermission ? '有权限' : '无权限'}`);
      if (!hasPermission) {
        disableElement(el);
      }
    }
  }
}

// 禁用元素的辅助函数
function disableElement(el) {
  // 隐藏元素
  el.style.display = 'none';
  
  // 添加disabled属性
  el.setAttribute('disabled', 'disabled');
  
  // 阻止点击事件
  el.style.pointerEvents = 'none';
  
  // 添加自定义属性标记为禁用
  el.setAttribute('data-permission-disabled', 'true');
  
  // 添加禁用样式
  el.classList.add('permission-disabled');
}

// 注册全局权限检查函数
export function registerPermissionDirective(app) {
  app.directive('permission', permission)
} 