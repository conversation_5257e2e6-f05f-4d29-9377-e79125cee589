"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[5],{52307:(e,t,r)=>{r.d(t,{AI:()=>d,bW:()=>R,uA:()=>d,D_:()=>S,oc:()=>j,C8:()=>w,Tn:()=>f,un:()=>v,Kg:()=>P,lQ:()=>m,V7:()=>x,x_:()=>g,rd:()=>B,Uo:()=>D,k3:()=>T,TO:()=>_});var o=r(50953),n=r(20641);var p,c=Object.defineProperty,i=Object.defineProperties,s=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,y=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable,u=(e,t,r)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,a=(e,t)=>{for(var r in t||(t={}))y.call(t,r)&&u(e,r,t[r]);if(l)for(var r of l(t))O.call(t,r)&&u(e,r,t[r]);return e},b=(e,t)=>i(e,s(t));function d(e,t){var r;const p=(0,o.IJ)();return(0,n.nT)((()=>{p.value=e()}),b(a({},t),{flush:null!=(r=null==t?void 0:t.flush)?r:"sync"})),(0,o.tB)(p)}const j="undefined"!==typeof window,w=e=>"undefined"!==typeof e,f=(Object.prototype.toString,e=>"function"===typeof e),P=e=>"string"===typeof e,m=()=>{},v=j&&(null==(p=null==window?void 0:window.navigator)?void 0:p.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function g(e){return"function"===typeof e?e():(0,o.R1)(e)}function h(e,t){function r(...r){return new Promise(((o,n)=>{Promise.resolve(e((()=>t.apply(this,r)),{fn:t,thisArg:this,args:r})).then(o).catch(n)}))}return r}function I(e,t={}){let r,o,n=m;const p=e=>{clearTimeout(e),n(),n=m},c=c=>{const i=g(e),s=g(t.maxWait);return r&&p(r),i<=0||void 0!==s&&s<=0?(o&&(p(o),o=null),Promise.resolve(c())):new Promise(((e,l)=>{n=t.rejectOnCancel?l:e,s&&!o&&(o=setTimeout((()=>{r&&p(r),o=null,e(c())}),s)),r=setTimeout((()=>{o&&p(o),o=null,e(c())}),i)}))};return c}function E(e,t=!0,r=!0,o=!1){let n,p,c=0,i=!0,s=m;const l=()=>{n&&(clearTimeout(n),n=void 0,s(),s=m)},y=y=>{const O=g(e),u=Date.now()-c,a=()=>p=y();return l(),O<=0?(c=Date.now(),a()):(u>O&&(r||!i)?(c=Date.now(),a()):t&&(p=new Promise(((e,t)=>{s=o?t:e,n=setTimeout((()=>{c=Date.now(),i=!0,e(a()),l()}),Math.max(0,O-u))}))),r||n||(n=setTimeout((()=>i=!0),O)),i=!1,p)};return y}function S(e){return e}function R(e,t){let r,p,c;const i=(0,o.KR)(!0),s=()=>{i.value=!0,c()};(0,n.wB)(e,s,{flush:"sync"});const l=f(t)?t:t.get,y=f(t)?void 0:t.set,O=(0,o.rY)(((e,t)=>(p=e,c=t,{get(){return i.value&&(r=l(),i.value=!1),p(),r},set(e){null==y||y(e)}})));return Object.isExtensible(O)&&(O.trigger=s),O}function D(e){return!!(0,o.o5)()&&((0,o.jr)(e),!0)}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function K(e,t=200,r={}){return h(I(t,r),e)}function x(e,t=200,r={}){const p=(0,o.KR)(e.value),c=K((()=>{p.value=e.value}),t,r);return(0,n.wB)(e,(()=>c())),p}function T(e,t=200,r=!1,o=!0,n=!1){return h(E(t,r,o,n),e)}Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function B(e,t=!0){(0,n.nI)()?(0,n.sV)(e):t?e():(0,n.dY)(e)}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function _(e,t,r={}){const{immediate:n=!0}=r,p=(0,o.KR)(!1);let c=null;function i(){c&&(clearTimeout(c),c=null)}function s(){p.value=!1,i()}function l(...r){i(),p.value=!0,c=setTimeout((()=>{p.value=!1,c=null,e(...r)}),g(t))}return n&&(p.value=!0,j&&l()),D(s),{isPending:(0,o.tB)(p),start:l,stop:s}}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable},96433:(e,t,r)=>{r.d(t,{X2F:()=>u,F4c:()=>i,YC1:()=>a,eU5:()=>f,fho:()=>P,SSU:()=>E,MLh:()=>y,P1n:()=>x,wYm:()=>I,hRP:()=>N,esz:()=>U,lWr:()=>M});var o=r(52307),n=r(20641),p=r(50953),c=!1;function i(e){var t;const r=(0,o.x_)(e);return null!=(t=null==r?void 0:r.$el)?t:r}const s=o.oc?window:void 0,l=o.oc?window.document:void 0;o.oc&&window.navigator,o.oc&&window.location;function y(...e){let t,r,p,c;if((0,o.Kg)(e[0])||Array.isArray(e[0])?([r,p,c]=e,t=s):[t,r,p,c]=e,!t)return o.lQ;Array.isArray(r)||(r=[r]),Array.isArray(p)||(p=[p]);const l=[],y=()=>{l.forEach((e=>e())),l.length=0},O=(e,t,r,o)=>(e.addEventListener(t,r,o),()=>e.removeEventListener(t,r,o)),u=(0,n.wB)((()=>[i(t),(0,o.x_)(c)]),(([e,t])=>{y(),e&&l.push(...r.flatMap((r=>p.map((o=>O(e,r,o,t))))))}),{immediate:!0,flush:"post"}),a=()=>{u(),y()};return(0,o.Uo)(a),a}let O=!1;function u(e,t,r={}){const{window:n=s,ignore:p=[],capture:c=!0,detectIframe:l=!1}=r;if(!n)return;o.un&&!O&&(O=!0,Array.from(n.document.body.children).forEach((e=>e.addEventListener("click",o.lQ))));let u=!0;const a=e=>p.some((t=>{if("string"===typeof t)return Array.from(n.document.querySelectorAll(t)).some((t=>t===e.target||e.composedPath().includes(t)));{const r=i(t);return r&&(e.target===r||e.composedPath().includes(r))}})),b=r=>{const o=i(e);o&&o!==r.target&&!r.composedPath().includes(o)&&(0===r.detail&&(u=!a(r)),u?t(r):u=!0)},d=[y(n,"click",b,{passive:!0,capture:c}),y(n,"pointerdown",(t=>{const r=i(e);r&&(u=!t.composedPath().includes(r)&&!a(t))}),{passive:!0}),l&&y(n,"blur",(r=>{var o;const p=i(e);"IFRAME"!==(null==(o=n.document.activeElement)?void 0:o.tagName)||(null==p?void 0:p.contains(n.document.activeElement))||t(r)}))].filter(Boolean),j=()=>d.forEach((e=>e()));return j}Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function a(e={}){var t;const{window:r=s}=e,n=null!=(t=e.document)?t:null==r?void 0:r.document,p=(0,o.bW)((()=>null),(()=>null==n?void 0:n.activeElement));return r&&(y(r,"blur",(e=>{null===e.relatedTarget&&p.trigger()}),!0),y(r,"focus",p.trigger,!0)),p}function b(e,t=!1){const r=(0,p.KR)(),n=()=>r.value=Boolean(e());return n(),(0,o.rd)(n,t),r}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function d(e){return JSON.parse(JSON.stringify(e))}const j="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof global?global:"undefined"!==typeof self?self:{},w="__vueuse_ssr_handlers__";j[w]=j[w]||{};j[w];Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function f(e,t,{window:r=s,initialValue:c=""}={}){const l=(0,p.KR)(c),y=(0,n.EW)((()=>{var e;return i(t)||(null==(e=null==r?void 0:r.document)?void 0:e.documentElement)}));return(0,n.wB)([y,()=>(0,o.x_)(e)],(([e,t])=>{var o;if(e&&r){const n=null==(o=r.getComputedStyle(e).getPropertyValue(t))?void 0:o.trim();l.value=n||c}}),{immediate:!0}),(0,n.wB)(l,(t=>{var r;(null==(r=y.value)?void 0:r.style)&&y.value.style.setProperty((0,o.x_)(e),t)})),l}Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function P({document:e=l}={}){if(!e)return(0,p.KR)("visible");const t=(0,p.KR)(e.visibilityState);return y(e,"visibilitychange",(()=>{t.value=e.visibilityState})),t}Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var m=Object.getOwnPropertySymbols,v=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable,h=(e,t)=>{var r={};for(var o in e)v.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&m)for(var o of m(e))t.indexOf(o)<0&&g.call(e,o)&&(r[o]=e[o]);return r};function I(e,t,r={}){const p=r,{window:c=s}=p,l=h(p,["window"]);let y;const O=b((()=>c&&"ResizeObserver"in c)),u=()=>{y&&(y.disconnect(),y=void 0)},a=(0,n.wB)((()=>i(e)),(e=>{u(),O.value&&c&&e&&(y=new ResizeObserver(t),y.observe(e,l))}),{immediate:!0,flush:"post"}),d=()=>{u(),a()};return(0,o.Uo)(d),{isSupported:O,stop:d}}function E(e,t={}){const{reset:r=!0,windowResize:c=!0,windowScroll:s=!0,immediate:l=!0}=t,O=(0,p.KR)(0),u=(0,p.KR)(0),a=(0,p.KR)(0),b=(0,p.KR)(0),d=(0,p.KR)(0),j=(0,p.KR)(0),w=(0,p.KR)(0),f=(0,p.KR)(0);function P(){const t=i(e);if(!t)return void(r&&(O.value=0,u.value=0,a.value=0,b.value=0,d.value=0,j.value=0,w.value=0,f.value=0));const o=t.getBoundingClientRect();O.value=o.height,u.value=o.bottom,a.value=o.left,b.value=o.right,d.value=o.top,j.value=o.width,w.value=o.x,f.value=o.y}return I(e,P),(0,n.wB)((()=>i(e)),(e=>!e&&P())),s&&y("scroll",P,{capture:!0,passive:!0}),c&&y("resize",P,{passive:!0}),(0,o.rd)((()=>{l&&P()})),{height:O,bottom:u,left:a,right:b,top:d,width:j,x:w,y:f,update:P}}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;new Map;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var S=Object.getOwnPropertySymbols,R=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable,K=(e,t)=>{var r={};for(var o in e)R.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&S)for(var o of S(e))t.indexOf(o)<0&&D.call(e,o)&&(r[o]=e[o]);return r};function x(e,t,r={}){const p=r,{window:c=s}=p,l=K(p,["window"]);let y;const O=b((()=>c&&"MutationObserver"in c)),u=()=>{y&&(y.disconnect(),y=void 0)},a=(0,n.wB)((()=>i(e)),(e=>{u(),O.value&&c&&e&&(y=new MutationObserver(t),y.observe(e,l))}),{immediate:!0}),d=()=>{u(),a()};return(0,o.Uo)(d),{isSupported:O,stop:d}}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var T;(function(e){e["UP"]="UP",e["RIGHT"]="RIGHT",e["DOWN"]="DOWN",e["LEFT"]="LEFT",e["NONE"]="NONE"})(T||(T={}));Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var B=Object.defineProperty,_=Object.getOwnPropertySymbols,A=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable,Q=(e,t,r)=>t in e?B(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,W=(e,t)=>{for(var r in t||(t={}))A.call(t,r)&&Q(e,r,t[r]);if(_)for(var r of _(t))C.call(t,r)&&Q(e,r,t[r]);return e};const k={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]};W({linear:o.D_},k);function N(e,t,r,i={}){var s,l,y,O,u;const{clone:a=!1,passive:b=!1,eventName:j,deep:w=!1,defaultValue:f}=i,P=(0,n.nI)(),m=r||(null==P?void 0:P.emit)||(null==(s=null==P?void 0:P.$emit)?void 0:s.bind(P))||(null==(y=null==(l=null==P?void 0:P.proxy)?void 0:l.$emit)?void 0:y.bind(null==P?void 0:P.proxy));let v=j;if(!t)if(c){const e=null==(u=null==(O=null==P?void 0:P.proxy)?void 0:O.$options)?void 0:u.model;t=(null==e?void 0:e.value)||"value",j||(v=(null==e?void 0:e.event)||"input")}else t="modelValue";v=j||v||`update:${t.toString()}`;const g=e=>a?(0,o.Tn)(a)?a(e):d(e):e,h=()=>(0,o.C8)(e[t])?g(e[t]):f;if(b){const r=h(),o=(0,p.KR)(r);return(0,n.wB)((()=>e[t]),(e=>o.value=g(e))),(0,n.wB)(o,(r=>{(r!==e[t]||w)&&m(v,r)}),{deep:w}),o}return(0,n.EW)({get(){return h()},set(e){m(v,e)}})}function U({window:e=s}={}){if(!e)return(0,p.KR)(!1);const t=(0,p.KR)(e.document.hasFocus());return y(e,"blur",(()=>{t.value=!1})),y(e,"focus",(()=>{t.value=!0})),t}function M(e={}){const{window:t=s,initialWidth:r=1/0,initialHeight:n=1/0,listenOrientation:c=!0,includeScrollbar:i=!0}=e,l=(0,p.KR)(r),O=(0,p.KR)(n),u=()=>{t&&(i?(l.value=t.innerWidth,O.value=t.innerHeight):(l.value=t.document.documentElement.clientWidth,O.value=t.document.documentElement.clientHeight))};return u(),(0,o.rd)(u),y("resize",u,{passive:!0}),c&&y("orientationchange",u,{passive:!0}),{width:l,height:O}}}}]);