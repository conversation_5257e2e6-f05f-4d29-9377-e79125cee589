package com.medical.annotation.controller;

import com.medical.annotation.model.ImagePair;
import com.medical.annotation.repository.ImagePairRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统维护控制器，用于清理数据库中的无效数据
 */
@RestController
@RequestMapping("/api/maintenance")
public class MaintenanceController {

    @Autowired
    private ImagePairRepository imagePairRepository;

    /**
     * 获取所有diagnosis_id为1的记录
     */
    @GetMapping("/invalid-records")
    public ResponseEntity<List<ImagePair>> getInvalidRecords() {
        List<ImagePair> invalidRecords = imagePairRepository.findByDiagnosisId(1);
        return ResponseEntity.ok(invalidRecords);
    }

    /**
     * 删除所有diagnosis_id为1的记录
     */
    @DeleteMapping("/clean-invalid-records")
    public ResponseEntity<Map<String, Object>> cleanInvalidRecords() {
        List<ImagePair> invalidRecords = imagePairRepository.findByDiagnosisId(1);
        int count = invalidRecords.size();
        
        // 记录要删除的数据
        List<String> deletedPaths = invalidRecords.stream()
                .map(pair -> pair.getId() + ": " + pair.getImageOnePath())
                .collect(Collectors.toList());
        
        // 删除这些记录
        imagePairRepository.deleteByDiagnosisId(1);
        
        // 返回删除结果
        return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "成功删除 " + count + " 条无效记录",
                "count", count,
                "deletedRecords", deletedPaths
        ));
    }
} 