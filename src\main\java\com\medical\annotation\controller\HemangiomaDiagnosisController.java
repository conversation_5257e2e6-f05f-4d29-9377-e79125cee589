package com.medical.annotation.controller;

import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.model.User;
import com.medical.annotation.model.Tag;
import com.medical.annotation.repository.HemangiomaDiagnosisRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.service.FileService;
import com.medical.annotation.util.FileNameGenerator;
import com.medical.annotation.util.BasePathConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.util.concurrent.CompletableFuture;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.medical.annotation.service.UserService;

@RestController
@RequestMapping({"/api/hemangioma-diagnoses", "/medical/api/hemangioma-diagnoses"})
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class HemangiomaDiagnosisController {

    @Autowired
    private HemangiomaDiagnosisRepository hemangiomaDiagnosisRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TagRepository tagRepository;
    
    @Autowired
    private FileService fileService;
    
    @Autowired
    private BasePathConfig basePathConfig;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private UserService userService;
    
    @Value("${ai.service.url:http://localhost:8086}")
    private String aiServiceUrl;
    
    private String tempDir;
    
    @PostConstruct
    public void init() {
        tempDir = basePathConfig.getTempDir();
        
        // 如果RestTemplate没有被Spring自动注入，手动创建一个
        if (restTemplate == null) {
            restTemplate = new RestTemplate();
        }
    }
    
    /**
     * 上传图像并创建诊断记录
     */
    @PostMapping("/upload-and-diagnose")
    public ResponseEntity<?> uploadAndDiagnose(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "patient_age", required = false) Integer patientAge,
            @RequestParam(value = "gender", required = false) String gender,
            @RequestParam(value = "origin_type", required = false) String originType,
            @RequestParam(value = "vessel_texture", required = false) String vesselTexture,
            @RequestParam(value = "user_id", required = false) Integer userId,
    @RequestParam(value = "annotationData", required = false) String annotationData,
    @RequestParam(value = "boundingBoxes", required = false) List<String> boundingBoxes,
    HttpServletRequest request) {
        try {
            // 1. 获取用户ID
            if (userId == null) {
                String userIdHeader = request.getHeader("X-User-ID");
                if (userIdHeader != null && !userIdHeader.isEmpty()) {
                    try {
                        // 处理可能包含逗号的复合ID格式（如"300000001, 3"）
                        if (userIdHeader.contains(",")) {
                            // 分割并返回去掉空格的第一个有效ID
                            String[] idParts = userIdHeader.split(",");
                            for (String idPart : idParts) {
                                String trimmedId = idPart.trim();
                                if (!trimmedId.isEmpty()) {
                                    System.out.println("从复合ID中提取第一个有效ID: " + trimmedId);
                                    userIdHeader = trimmedId;
                                    break;
                                }
                            }
                        }
                        
                        userId = Integer.parseInt(userIdHeader);
                        System.out.println("成功解析用户ID: " + userId);
                    } catch (NumberFormatException e) {
                        System.err.println("无效的用户ID格式: " + userIdHeader);
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                .body(Map.of("error", "无效的用户ID格式"));
                    }
                } else {
                    System.err.println("未提供用户ID");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(Map.of("error", "需要提供用户ID，请先登录"));
                }
            } else {
                System.out.println("使用表单参数中的用户ID: " + userId);
            }
            
            System.out.println("处理血管瘤诊断请求 - 用户ID: " + userId);
            
            // 2. 保存图像到temp目录
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String uniqueFilename = FileNameGenerator.generateUniqueFileName("hemangioma_" + fileExtension);
            
            // 确保temp目录存在
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                tempDirFile.mkdirs();
            }
            
            // 保存文件 - 使用临时文件机制
            Path targetPath = Paths.get(tempDir, uniqueFilename);
            File targetFile = targetPath.toFile();
            file.transferTo(targetFile);
            
            // 构建Web访问路径
            String webPath = "/medical/images/temp/" + uniqueFilename;
            System.out.println("图像已保存到: " + targetPath + ", Web路径: " + webPath);
            
            // 3. 创建HemangiomaDiagnosis记录 (暂时保留，但不更新AI检测结果)
            HemangiomaDiagnosis diagnosis = new HemangiomaDiagnosis();
            diagnosis.setPatientAge(patientAge);
            diagnosis.setGender(gender);
            diagnosis.setOriginType(originType);
            diagnosis.setVesselTexture(vesselTexture);
            diagnosis.setCreatedAt(LocalDateTime.now());
            diagnosis.setImagePath(webPath);
            if (annotationData != null) diagnosis.setAnnotationData(annotationData);
            if (boundingBoxes != null) diagnosis.setBoundingBoxes(new ArrayList<>(boundingBoxes));

            // 设置用户关联
            Optional<User> user = userRepository.findById(userId);
            if (user.isPresent()) {
                diagnosis.setUser(user.get());
            } else {
                // 找不到用户，返回错误
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "无法找到ID为 " + userId + " 的用户"));
            }
            
            // 保存诊断记录
            HemangiomaDiagnosis savedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
            System.out.println("诊断记录已保存，ID: " + savedDiagnosis.getId());
            
            // 4. 调用AI服务进行快速YOLO检测
            System.out.println("\n开始调用AI服务进行血管瘤YOLO检测...");
            Map<String, Object> yoloResult = callAiServiceOnlyYolo(targetFile, patientAge, gender, originType, vesselTexture, savedDiagnosis.getId());
            System.out.println("YOLO检测完成，返回结果: " + yoloResult);
            
            // 5. 使用YOLO返回的数据更新诊断记录
            if (yoloResult != null && !yoloResult.isEmpty()) {
                // 更新基本信息
                if (yoloResult.containsKey("detected_type")) {
                    savedDiagnosis.setDetectedType((String) yoloResult.get("detected_type"));
                }
                
                // 更新置信度字段
                if (yoloResult.containsKey("confidence")) {
                    savedDiagnosis.setConfidence(((Number) yoloResult.get("confidence")).doubleValue());
                }
                
                // 设置处理后的图像路径
                if (yoloResult.containsKey("processed_image_path")) {
                    savedDiagnosis.setProcessedImagePath((String) yoloResult.get("processed_image_path"));
                    System.out.println("设置处理后图像路径: " + yoloResult.get("processed_image_path"));
                }
                
                // 添加对颜色和部位的处理
                if (yoloResult.containsKey("predicted_color")) {
                    Map<String, Object> colorInfo = (Map<String, Object>) yoloResult.get("predicted_color");
                    if (colorInfo != null && colorInfo.containsKey("name")) {
                        String colorName = (String) colorInfo.get("name");
                        savedDiagnosis.setColor(colorName);
                        System.out.println("设置颜色: " + colorName);
                    }
                }
                
                if (yoloResult.containsKey("predicted_part")) {
                    Map<String, Object> partInfo = (Map<String, Object>) yoloResult.get("predicted_part");
                    if (partInfo != null && partInfo.containsKey("name")) {
                        String partName = (String) partInfo.get("name");
                        savedDiagnosis.setBodyPart(partName);
                        System.out.println("设置部位: " + partName);
                    }
                }
                
                // 保存更新后的诊断记录
                savedDiagnosis = hemangiomaDiagnosisRepository.save(savedDiagnosis);
                System.out.println("诊断记录更新成功，ID: " + savedDiagnosis.getId());
            }
            
            // 6. 异步处理大模型生成建议
            // 启动异步任务生成详细诊断建议
            generateRecommendationAsync(savedDiagnosis.getId(), targetFile, patientAge, gender, originType, vesselTexture);
            
            // 7. 添加延迟，确保数据库操作完成
            try {
                System.out.println("添加延迟，确保数据库操作完成...");
                Thread.sleep(1000); // 延迟1秒
                
                // 重新查询诊断记录，确保获取最新数据
                Optional<HemangiomaDiagnosis> refreshedDiagnosis = hemangiomaDiagnosisRepository.findById(savedDiagnosis.getId());
                if (refreshedDiagnosis.isPresent()) {
                    savedDiagnosis = refreshedDiagnosis.get();
                    System.out.println("已重新加载诊断记录，ID: " + savedDiagnosis.getId());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("延迟等待被中断: " + e.getMessage());
            }
            
            // 8. 立即返回YOLO检测结果
            return ResponseEntity.status(HttpStatus.CREATED).body(savedDiagnosis);
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "处理诊断请求失败: " + e.getMessage()));
        }
    }
    
    /**
     * 调用AI服务进行诊断
     */
    private Map<String, Object> callAiService(File imageFile, Integer diagnosisId, Integer patientAge, String gender, String originType, String vesselTexture) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new FileSystemResource(imageFile));
            
        // 添加诊断ID参数
        body.add("diagnosis_id", diagnosisId != null ? diagnosisId.toString() : "");
        
        // 添加所有元数据
        if (patientAge != null) body.add("patient_age", patientAge.toString());
        if (gender != null) body.add("gender", gender);
        if (originType != null) body.add("origin_type", originType);
        if (vesselTexture != null) body.add("vessel_texture", vesselTexture);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
        String url = aiServiceUrl + "/diagnose"; // 新的Python端点
        try {
            System.out.println("开始调用AI服务，URL: " + url + ", 诊断ID: " + diagnosisId);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
            System.out.println("AI服务返回状态码: " + response.getStatusCodeValue());
            return response.getBody();
        } catch (Exception e) {
            e.printStackTrace();
            // 返回一个包含错误信息的Map，以便上层可以处理
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "调用AI服务失败: " + e.getMessage());
            return errorMap;
        }
    }
    
    /**
     * 调用AI服务仅进行YOLO检测，不等待大模型处理
     */
    private Map<String, Object> callAiServiceOnlyYolo(File imageFile, Integer patientAge, String gender, String originType, String vesselTexture, Integer diagnosisId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new FileSystemResource(imageFile));
        body.add("only_yolo", "true"); // 添加参数告知AI服务只进行YOLO检测
        body.add("diagnosis_id", diagnosisId);
        
        // 添加所有元数据
        if (patientAge != null) body.add("patient_age", patientAge.toString());
        if (gender != null) body.add("gender", gender);
        if (originType != null) body.add("origin_type", originType);
        if (vesselTexture != null) body.add("vessel_texture", vesselTexture);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        String url = aiServiceUrl + "/diagnose-yolo"; // 新的仅YOLO检测端点
        try {
            System.out.println("开始调用YOLO检测服务，URL: " + url);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
            Map<String, Object> result = response.getBody();
            
            System.out.println("YOLO检测服务返回状态码: " + response.getStatusCodeValue());
            System.out.println("YOLO检测结果: " + (result != null ? result.keySet() : "null"));
            
            // 处理标注框数据
            if (result != null) {
                if (result.containsKey("detection_boxes")) {
                    List<Map<String, Object>> boxes = (List<Map<String, Object>>) result.get("detection_boxes");
                    System.out.println("YOLO检测返回了标注框数据，数量: " + boxes.size());
                    System.out.println("标注框数据示例: " + (!boxes.isEmpty() ? boxes.get(0) : "无"));
                    
                    // 保存标注框数据
                    saveDetectionBoxes(diagnosisId, result);
                } else if (result.containsKey("tags")) {
                    // 新的数据结构，使用tags字段
                    System.out.println("发现新的数据结构: 使用tags字段作为标注数据");
                    List<Map<String, Object>> tags = (List<Map<String, Object>>) result.get("tags");
                    System.out.println("YOLO检测返回了标签数据，数量: " + (tags != null ? tags.size() : 0));
                    System.out.println("标签数据示例: " + (tags != null && !tags.isEmpty() ? tags.get(0) : "无"));
                    
                    // 创建包含标签数据的新Map
                    Map<String, Object> tagsResult = new HashMap<>(result);
                    // 将tags字段映射为detection_boxes字段，保持向后兼容
                    tagsResult.put("detection_boxes", result.get("tags"));
                    
                    // 保存标注框数据
                    saveDetectionBoxes(diagnosisId, tagsResult);
                } else {
                    System.out.println("YOLO检测结果中不包含detection_boxes或tags字段");
                }
                
                if (result.containsKey("error")) {
                    System.err.println("YOLO检测服务返回错误: " + result.get("error"));
                }
            } else {
                System.out.println("YOLO检测服务返回空结果");
            }
            
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            // 返回一个包含错误信息的Map，以便上层可以处理
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "调用YOLO检测服务失败: " + e.getMessage());
            return errorMap;
        }
    }
    
    /**
     * 保存YOLO检测返回的标注框数据
     */
    private void saveDetectionBoxes(Integer diagnosisId, Map<String, Object> yoloResult) {
        try {
            System.out.println("开始保存标注框数据，诊断ID: " + diagnosisId);
            
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(diagnosisId);
            if (!diagnosisOpt.isPresent()) {
                System.err.println("找不到诊断记录ID: " + diagnosisId + "，无法保存标注框");
                return;
            }
            
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            System.out.println("找到诊断记录: " + diagnosis.getId());
            
            // 提取检测框数据
            Object detectionBoxesObj = yoloResult.get("detection_boxes");
            System.out.println("检测框数据类型: " + (detectionBoxesObj != null ? detectionBoxesObj.getClass().getName() : "null"));
            System.out.println("YOLO结果键集: " + yoloResult.keySet());
            
            // 如果detection_boxes为null，尝试从tags字段获取
            if (detectionBoxesObj == null && yoloResult.containsKey("tags")) {
                detectionBoxesObj = yoloResult.get("tags");
                System.out.println("从tags字段获取标注数据，类型: " + (detectionBoxesObj != null ? detectionBoxesObj.getClass().getName() : "null"));
            }
            
            // 处理不同格式的数据
            List<Map<String, Object>> detectionBoxes;
            if (detectionBoxesObj instanceof List) {
                try {
                    detectionBoxes = (List<Map<String, Object>>) detectionBoxesObj;
                } catch (ClassCastException e) {
                    System.err.println("无法转换检测框数据为List<Map>: " + e.getMessage());
                    
                    // 尝试转换每个元素
                    List<?> rawList = (List<?>) detectionBoxesObj;
                    detectionBoxes = new ArrayList<>();
                    for (Object item : rawList) {
                        if (item instanceof Map) {
                            detectionBoxes.add((Map<String, Object>) item);
                        } else {
                            System.err.println("检测框数据项类型不是Map: " + (item != null ? item.getClass().getName() : "null"));
                        }
                    }
                }
            } else {
                System.err.println("检测框数据不是List类型");
                detectionBoxes = new ArrayList<>();
            }
            
            if (detectionBoxes == null || detectionBoxes.isEmpty()) {
                System.out.println("没有检测到标注框数据");
                return;
            }
            
            System.out.println("检测到 " + detectionBoxes.size() + " 个标注框，准备保存");
            
            // 先删除现有的标签，避免重复
            List<Tag> existingTags = tagRepository.findByAnyDiagnosisId(diagnosisId);
            if (!existingTags.isEmpty()) {
                System.out.println("删除 " + existingTags.size() + " 个现有标签");
                tagRepository.deleteAll(existingTags);
            }
            
            // 创建标签对象并保存
            List<Tag> tags = new ArrayList<>();
            for (Map<String, Object> box : detectionBoxes) {
                System.out.println("处理标注框: " + box);
                
                Tag tag = new Tag();
                tag.setHemangiomaDiagnosis(diagnosis);
                tag.setHemangioma_id(diagnosis.getId().longValue()); // 同时设置hemangioma_id字段
                
                // 设置标签类型
                if (box.containsKey("class")) {
                    String className = String.valueOf(box.get("class"));
                    tag.setTagName(className);
                    System.out.println("标签类型: " + className);
                } else if (box.containsKey("class_name")) {
                    // 新数据结构使用class_name字段
                    String className = String.valueOf(box.get("class_name"));
                    tag.setTagName(className);
                    System.out.println("标签类型(新格式): " + className);
                } else {
                    tag.setTagName("未知类型");
                    System.out.println("标签类型: 未知类型 (未找到class或class_name字段)");
                }
                
                // 设置坐标和尺寸 (确保是相对坐标，范围0-1)
                try {
                    // 检查是否有直接的相对坐标
                    if (box.containsKey("x") && box.containsKey("y") && box.containsKey("width") && box.containsKey("height")) {
                        // 旧格式，直接使用相对坐标
                        double x = parseNumberValue(box.get("x"));
                        double y = parseNumberValue(box.get("y"));
                        double width = parseNumberValue(box.get("width"));
                        double height = parseNumberValue(box.get("height"));
                        
                        tag.setX(roundToSixDecimals(x));
                        tag.setY(roundToSixDecimals(y));
                        tag.setWidth(roundToSixDecimals(width));
                        tag.setHeight(roundToSixDecimals(height));
                        
                        System.out.println("使用相对坐标: x=" + x + ", y=" + y + ", width=" + width + ", height=" + height);
                    } else if (box.containsKey("x_center") && box.containsKey("y_center") && 
                               box.containsKey("width") && box.containsKey("height")) {
                        // 新格式，使用中心点相对坐标
                        double centerX = parseNumberValue(box.get("x_center"));
                        double centerY = parseNumberValue(box.get("y_center"));
                        double width = parseNumberValue(box.get("width"));
                        double height = parseNumberValue(box.get("height"));
                        
                        // 设置相对坐标
                        tag.setX(roundToSixDecimals(centerX));
                        tag.setY(roundToSixDecimals(centerY));
                        tag.setWidth(roundToSixDecimals(width));
                        tag.setHeight(roundToSixDecimals(height));
                        
                        System.out.println("使用中心点相对坐标: centerX=" + centerX + ", centerY=" + centerY + 
                                          ", width=" + width + ", height=" + height);
                    } else if (box.containsKey("bbox")) {
                        // 使用绝对坐标bbox [x1, y1, x2, y2]
                        Object bboxObj = box.get("bbox");
                        
                        if (bboxObj instanceof List) {
                            List<?> bboxList = (List<?>) bboxObj;
                            if (bboxList.size() >= 4) {
                                int x1 = ((Number) bboxList.get(0)).intValue();
                                int y1 = ((Number) bboxList.get(1)).intValue();
                                int x2 = ((Number) bboxList.get(2)).intValue();
                                int y2 = ((Number) bboxList.get(3)).intValue();
                                
                                // 获取原始图像尺寸
                                int origWidth = 0;
                                int origHeight = 0;
                                
                                if (box.containsKey("orig_width") && box.containsKey("orig_height")) {
                                    origWidth = ((Number) box.get("orig_width")).intValue();
                                    origHeight = ((Number) box.get("orig_height")).intValue();
                                } else {
                                    // 如果没有原始尺寸，尝试从结果中获取
                                    Map<String, Integer> dimensions = (Map<String, Integer>) yoloResult.get("image_dimensions");
                                    if (dimensions != null) {
                                        origWidth = dimensions.get("width");
                                        origHeight = dimensions.get("height");
                                    }
                                }
                                
                                if (origWidth <= 0 || origHeight <= 0) {
                                    System.err.println("无法获取原始图像尺寸，无法计算相对坐标");
                                    continue;
                                }
                                
                                // 计算中心点坐标
                                double centerX = (x1 + x2) / 2.0 / origWidth;
                                double centerY = (y1 + y2) / 2.0 / origHeight;
                                
                                // 计算宽高
                                double width = (x2 - x1) * 1.0 / origWidth;
                                double height = (y2 - y1) * 1.0 / origHeight;
                                
                                // 设置相对坐标
                                tag.setX(roundToSixDecimals(centerX));
                                tag.setY(roundToSixDecimals(centerY));
                                tag.setWidth(roundToSixDecimals(width));
                                tag.setHeight(roundToSixDecimals(height));
                                
                                System.out.println("从bbox转换为相对坐标: centerX=" + centerX + ", centerY=" + centerY + 
                                                  ", width=" + width + ", height=" + height);
                            }
                        }
                    }
                    
                    // 设置置信度
                    if (box.containsKey("confidence")) {
                        Object confValue = box.get("confidence");
                        double confidence = parseNumberValue(confValue);
                        tag.setConfidence(roundToSixDecimals(confidence));
                        System.out.println("置信度: " + confidence + " (" + confValue.getClass().getName() + ")");
                    }
                } catch (Exception e) {
                    System.err.println("处理标注框坐标时出错: " + e.getMessage());
                    e.printStackTrace();
                }
                
                // 设置创建者ID为系统用户(1)
                tag.setCreatedBy(1L);
                
                // 验证标签数据完整性
                if (tag.getX() != null && tag.getY() != null && tag.getWidth() != null && tag.getHeight() != null) {
                    // 确保hemangioma_id字段已设置
                    if (tag.getHemangioma_id() == null && diagnosis != null) {
                        tag.setHemangioma_id(diagnosis.getId().longValue());
                    }
                    
                    // 确保tag字段已设置
                    if (tag.getTag() == null && tag.getTagName() != null) {
                        tag.setTag(tag.getTagName());
                    } else if (tag.getTagName() == null && tag.getTag() != null) {
                        tag.setTagName(tag.getTag());
                    }
                    
                    tags.add(tag);
                    System.out.println("添加有效标签: " + tag.getTagName() + " 位置: (" + tag.getX() + "," + tag.getY() + "," + tag.getWidth() + "," + tag.getHeight() + ")");
                } else {
                    System.err.println("标签数据不完整，跳过保存: X=" + tag.getX() + ", Y=" + tag.getY() + 
                                     ", Width=" + tag.getWidth() + ", Height=" + tag.getHeight());
                }
            }
            
            // 保存所有标签
            if (!tags.isEmpty()) {
                List<Tag> savedTags = tagRepository.saveAll(tags);
                System.out.println("成功保存 " + savedTags.size() + " 个标签，ID列表: " + 
                                  savedTags.stream().map(t -> t.getId().toString()).reduce("", (a, b) -> a + "," + b));
                
                // 检查是否能从数据库中查询到保存的标签
                List<Tag> verifyTags = tagRepository.findByAnyDiagnosisId(diagnosisId);
                System.out.println("验证查询: 数据库中找到 " + verifyTags.size() + " 个标签");
            } else {
                System.err.println("没有有效的标签数据可保存");
            }
            
        } catch (Exception e) {
            System.err.println("保存标注框数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析各种类型的数值
     */
    private double parseNumberValue(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            return Double.parseDouble((String) value);
        } else if (value instanceof Map) {
            // 处理JSON结构可能的数值表示
            Map<?, ?> map = (Map<?, ?>) value;
            if (map.containsKey("value")) {
                return parseNumberValue(map.get("value"));
            }
        }
        throw new IllegalArgumentException("无法解析数值: " + value);
    }
    
    /**
     * 异步执行大模型生成建议
     */
    private void generateRecommendationAsync(final Integer diagnosisId, final File imageFile, 
                                           final Integer patientAge, final String gender, 
                                           final String originType, final String vesselTexture) {
        // 使用线程池异步执行
        CompletableFuture.runAsync(() -> {
            try {
                System.out.println("开始异步生成诊断建议，诊断ID: " + diagnosisId);
                
                // 调用完整的AI服务（包括大模型），传递诊断ID
                Map<String, Object> aiResult = callAiService(imageFile, diagnosisId, patientAge, gender, originType, 
                                                         vesselTexture);
                
                if (aiResult != null && !aiResult.containsKey("error")) {
                    // 查找诊断记录
                    Optional<HemangiomaDiagnosis> optDiagnosis = hemangiomaDiagnosisRepository.findById(diagnosisId);
                    if (optDiagnosis.isPresent()) {
                        HemangiomaDiagnosis diagnosis = optDiagnosis.get();
                        
                        // 更新LLM生成的建议
                        if (aiResult.containsKey("diagnostic_summary")) {
                            diagnosis.setDiagnosticSummary((String) aiResult.get("diagnostic_summary"));
                        }
                        if (aiResult.containsKey("treatment_suggestion")) {
                            diagnosis.setTreatmentSuggestion((String) aiResult.get("treatment_suggestion"));
                        }
                        // 处理注意事项 (原紧急情况处理)
                        if (aiResult.containsKey("precautions")) {
                            diagnosis.setPrecautions((String) aiResult.get("precautions"));
                        }
                        // 兼容旧版API，将emergency_instructions映射到precautions
                        else if (aiResult.containsKey("emergency_instructions")) {
                            diagnosis.setPrecautions((String) aiResult.get("emergency_instructions"));
                        }
                        if (aiResult.containsKey("disclaimer")) {
                            diagnosis.setDisclaimer((String) aiResult.get("disclaimer"));
                        }
                        
                        // 保存更新后的诊断记录
                        hemangiomaDiagnosisRepository.save(diagnosis);
                        System.out.println("异步生成诊断建议完成，已更新诊断记录 ID: " + diagnosisId);
                    } else {
                        System.err.println("无法找到诊断记录 ID: " + diagnosisId);
                    }
                } else {
                    System.err.println("生成诊断建议失败: " + (aiResult != null ? aiResult.get("error") : "未知错误"));
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("异步生成诊断建议时发生错误: " + e.getMessage());
            }
        });
    }
    
    private double roundToSixDecimals(double value) {
        return Math.round(value * 1_000_000.0) / 1_000_000.0;
    }
    
    /**
     * 获取所有诊断记录
     */
    @GetMapping
    public ResponseEntity<?> getAllDiagnoses() {
        List<HemangiomaDiagnosis> diagnoses = hemangiomaDiagnosisRepository.findAll();
        return ResponseEntity.ok(diagnoses);
    }
    
    @GetMapping("/pending")
    public ResponseEntity<Page<HemangiomaDiagnosis>> getPendingDiagnoses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "created_at,desc") String sort) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentPrincipalName = authentication.getName();
        User currentUser = userRepository.findByEmail(currentPrincipalName)
                .orElseThrow(() -> new RuntimeException("当前登录用户不存在: " + currentPrincipalName));

        // 创建一个不包含排序的Pageable对象
        Pageable pageable = PageRequest.of(page, size);

        Page<HemangiomaDiagnosis> diagnosesPage;
        
        // 先获取枚举类型，再获取其名称进行比较，这样更健壮
        String userRole = (currentUser.getRole() != null) ? currentUser.getRole().name() : "";

        if ("ADMIN".equals(userRole)) {
            diagnosesPage = hemangiomaDiagnosisRepository.findAllPendingForAdmin(pageable);
        } else if ("REVIEWER".equals(userRole)) {
            Integer teamId = Optional.ofNullable(currentUser.getTeam()).map(team -> team.getId()).orElse(null);
            if (teamId == null) {
                 // 如果审核员没有团队，理论上他不应该能审核任何与团队相关的病例
                 // 但根据需求，他可以审核无团队医生的病例。为了简化，我们这里假设他能看到所有无团队的。
                 // 注意：这个逻辑可能需要根据实际需求调整。此处我们假设 teamId 为一个数据库中不存在的负数来仅匹配 IS NULL。
                 // 一个更好的方法是修改Query，但这需要更复杂的动态查询。
                 // 暂定为可以看到所有无团队提交的内容
                 diagnosesPage = hemangiomaDiagnosisRepository.findAllPendingForReviewer(null, pageable);
            } else {
                diagnosesPage = hemangiomaDiagnosisRepository.findAllPendingForReviewer(teamId, pageable);
            }
        } else {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }

        return ResponseEntity.ok(diagnosesPage);
    }

    /**
     * 获取单个诊断记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getDiagnosisById(@PathVariable Integer id) {
        Optional<HemangiomaDiagnosis> diagnosis = hemangiomaDiagnosisRepository.findById(id);
        if (diagnosis.isPresent()) {
            return ResponseEntity.ok(diagnosis.get());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "未找到指定ID的诊断记录"));
        }
    }

    /**
     * 接收AI服务回调的诊断建议数据
     */
    @PostMapping("/update-recommendation")
    public ResponseEntity<?> updateRecommendation(@RequestBody Map<String, Object> payload) {
        try {
            // 从请求体中提取诊断ID和建议JSON
            Integer diagnosisId = (Integer) payload.get("diagnosisId");
            String recommendation = (String) payload.get("recommendation");
            
            System.out.println("接收到AI服务回调，更新诊断ID: " + diagnosisId);
            
            if (diagnosisId == null || recommendation == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "缺少必要参数：diagnosisId或recommendation"));
            }
            
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> optDiagnosis = hemangiomaDiagnosisRepository.findById(diagnosisId);
            if (!optDiagnosis.isPresent()) {
                System.out.println("未找到指定ID的诊断记录: " + diagnosisId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到指定ID的诊断记录"));
            }
            
            HemangiomaDiagnosis diagnosis = optDiagnosis.get();
            
            // 处理颜色信息
            if (payload.containsKey("predictedColor")) {
                Map<String, Object> colorInfo = (Map<String, Object>) payload.get("predictedColor");
                if (colorInfo != null && colorInfo.containsKey("name")) {
                    String colorName = (String) colorInfo.get("name");
                    diagnosis.setColor(colorName);
                    System.out.println("设置颜色: " + colorName);
                }
            }
            
            // 处理部位信息
            if (payload.containsKey("predictedPart")) {
                Map<String, Object> partInfo = (Map<String, Object>) payload.get("predictedPart");
                if (partInfo != null && partInfo.containsKey("name")) {
                    String partName = (String) partInfo.get("name");
                    diagnosis.setBodyPart(partName);
                    System.out.println("设置部位: " + partName);
                }
            }
            
            // 解析JSON字符串中的建议
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, String> recMap = mapper.readValue(recommendation, Map.class);
                System.out.println("成功解析建议JSON: " + recMap);
                
                // 更新诊断记录中的建议字段
                if (recMap.containsKey("treatment_suggestion")) {
                    diagnosis.setTreatmentSuggestion(recMap.get("treatment_suggestion"));
                }
                if (recMap.containsKey("precautions")) {
                    diagnosis.setPrecautions(recMap.get("precautions"));
                }
                if (recMap.containsKey("disclaimer")) {
                    diagnosis.setDisclaimer(recMap.get("disclaimer"));
                }
                
                // 保存更新后的诊断记录
                HemangiomaDiagnosis updatedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
                System.out.println("成功更新诊断记录的建议，ID: " + diagnosisId);
                return ResponseEntity.ok(Map.of(
                    "success", true, 
                    "message", "成功更新诊断建议", 
                    "id", updatedDiagnosis.getId()
                ));
            } catch (Exception e) {
                System.out.println("解析建议JSON失败: " + e.getMessage());
                e.printStackTrace();
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "解析建议JSON失败: " + e.getMessage()));
            }
        } catch (Exception e) {
            System.out.println("更新诊断建议时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "更新诊断建议失败: " + e.getMessage()));
        }
    }

    /**
     * 为现有诊断记录生成LLM建议
     * @param id 诊断记录ID
     * @return 更新后的诊断记录
     */
    @PostMapping("/{id}/generate-recommendation")
    public ResponseEntity<?> generateRecommendationForExistingRecord(@PathVariable Integer id) {
        try {
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(id);
            if (!diagnosisOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到指定ID的诊断记录"));
            }
            
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            String imagePath = diagnosis.getImagePath();
            
            // 检查图像路径是否有效
            if (imagePath == null || imagePath.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "诊断记录缺少图像路径"));
            }
            
            // 将Web路径转换为文件系统路径
            String filePath = fileService.convertWebPathToFilePath(imagePath);
            File imageFile = new File(filePath);
            
            if (!imageFile.exists()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "找不到图像文件: " + filePath));
            }
            
            // 获取诊断元数据
            Integer patientAge = diagnosis.getPatientAge();
            String gender = diagnosis.getGender();
            String originType = diagnosis.getOriginType();
            String vesselTexture = diagnosis.getVesselTexture();
            
            // 启动异步任务生成详细诊断建议
            generateRecommendationAsync(id, imageFile, patientAge, gender, originType, vesselTexture);
            
            // 立即执行YOLO检测并更新记录
            Map<String, Object> yoloResult = callAiServiceOnlyYolo(imageFile, patientAge, gender, originType, vesselTexture, id);
            
            // 使用YOLO返回的数据更新诊断记录
            if (yoloResult != null && !yoloResult.isEmpty()) {
                // 更新基本信息
                if (yoloResult.containsKey("detected_type")) {
                    diagnosis.setDetectedType((String) yoloResult.get("detected_type"));
                }
                
                // 更新置信度字段
                if (yoloResult.containsKey("confidence")) {
                    diagnosis.setConfidence(((Number) yoloResult.get("confidence")).doubleValue());
                }
                
                // 设置处理后的图像路径
                if (yoloResult.containsKey("processed_image_path")) {
                    diagnosis.setProcessedImagePath((String) yoloResult.get("processed_image_path"));
                }
                
                // 添加对颜色和部位的处理
                if (yoloResult.containsKey("predicted_color")) {
                    Map<String, Object> colorInfo = (Map<String, Object>) yoloResult.get("predicted_color");
                    if (colorInfo != null && colorInfo.containsKey("name")) {
                        String colorName = (String) colorInfo.get("name");
                        diagnosis.setColor(colorName);
                        System.out.println("设置颜色: " + colorName);
                    }
                }
                
                if (yoloResult.containsKey("predicted_part")) {
                    Map<String, Object> partInfo = (Map<String, Object>) yoloResult.get("predicted_part");
                    if (partInfo != null && partInfo.containsKey("name")) {
                        String partName = (String) partInfo.get("name");
                        diagnosis.setBodyPart(partName);
                        System.out.println("设置部位: " + partName);
                    }
                }
                
                // 保存更新后的诊断记录
                diagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
            }
            
            return ResponseEntity.ok()
                    .body(Map.of(
                        "message", "已启动生成诊断建议的任务，请稍后查看结果",
                        "diagnosis_id", id,
                        "yolo_result", yoloResult != null ? yoloResult : "无YOLO检测结果"
                    ));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "生成诊断建议失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据用户ID获取病例列表 - 支持前端"/api/images/my-images"请求
     * @param userId 用户ID或customId
     * @param status 可选的状态筛选
     * @return 用户的病例列表
     */
    @GetMapping("/by-user")
    public ResponseEntity<?> getDiagnosesByUser(
            @RequestParam(value = "userId", required = true) String userId,
            @RequestParam(value = "status", required = false) String status) {
        
        System.out.println("收到按用户查询诊断记录的请求 - 用户ID: " + userId + ", 状态: " + status);
        
        try {
            // 根据 custom_id 找到用户
            Optional<User> userOpt = userService.getUserByCustomId(userId);
            
            if (!userOpt.isPresent()) {
                System.err.println("找不到用户，ID: " + userId);
                return ResponseEntity.ok(new ArrayList<>());
            }
            
            // 根据用户和可选的状态参数查询病例
            List<HemangiomaDiagnosis> diagnoses;
            if (status != null && !status.isEmpty()) {
                try {
                    HemangiomaDiagnosis.Status statusEnum = HemangiomaDiagnosis.Status.valueOf(status);
                    diagnoses = hemangiomaDiagnosisRepository.findByUserAndStatus(userOpt.get(), statusEnum);
                } catch (IllegalArgumentException e) {
                    System.out.println("无效的状态参数: " + status + "，返回所有病例");
                    diagnoses = hemangiomaDiagnosisRepository.findByUser(userOpt.get());
                }
            } else {
                diagnoses = hemangiomaDiagnosisRepository.findByUser(userOpt.get());
            }
            
            System.out.println("找到 " + diagnoses.size() + " 条病例记录");
            return ResponseEntity.ok(diagnoses);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "获取病例列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取标签数据
     */
    @GetMapping("/{id}/tags")
    public ResponseEntity<?> getTagsForDiagnosis(@PathVariable Integer id) {
            Optional<HemangiomaDiagnosis> diagnosis = hemangiomaDiagnosisRepository.findById(id);
            if (diagnosis.isPresent()) {
            HemangiomaDiagnosis hemangiomaDiagnosis = diagnosis.get();
            List<Tag> tags = hemangiomaDiagnosis.getTags();
            return ResponseEntity.ok(tags);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "未找到指定ID的诊断记录"));
        }
    }
    
    /**
     * 临时修复方法 - 完整修复诊断记录，包括图像路径和标签
     */
    @GetMapping("/fix-diagnosis/{id}")
    public ResponseEntity<?> fixDiagnosis(@PathVariable Integer id) {
        Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(id);
        if (!diagnosisOpt.isPresent()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "未找到指定ID的诊断记录"));
        }
        
        HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
        Map<String, Object> result = new HashMap<>();
        
        // 1. 修复imagePath
        String tempDir = basePathConfig.getTempDir();
        String fileName = "hemangioma_example_" + id + ".jpg";
        String imagePath = "/medical/images/temp/" + fileName;
        
        // 更新诊断记录的imagePath
        diagnosis.setImagePath(imagePath);
        result.put("imagePath", imagePath);
        
        // 2. 创建示例图像文件
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists()) {
            tempDirFile.mkdirs();
        }
        
        File imageFile = new File(tempDir, fileName);
        try {
            if (!imageFile.exists()) {
                // 创建一个空白图像
                BufferedImage img = new BufferedImage(800, 600, BufferedImage.TYPE_INT_RGB);
                java.awt.Graphics2D g2d = img.createGraphics();
                
                // 填充白色背景
                g2d.setColor(java.awt.Color.WHITE);
                g2d.fillRect(0, 0, 800, 600);
                
                // 添加一些文本和形状
                g2d.setColor(java.awt.Color.RED);
                g2d.fillOval(300, 200, 200, 200); // 画一个红色圆形模拟血管瘤
                
                g2d.setColor(java.awt.Color.BLACK);
                g2d.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 20));
                g2d.drawString("示例血管瘤图像 ID: " + id, 50, 50);
                
                g2d.dispose();
                
                // 保存图像
                javax.imageio.ImageIO.write(img, "jpg", imageFile);
                result.put("imageCreated", true);
                result.put("imagePath", imageFile.getAbsolutePath());
            } else {
                result.put("imageCreated", false);
                result.put("imageExists", true);
            }
        } catch (Exception e) {
            result.put("imageError", e.getMessage());
            e.printStackTrace();
        }
        
        // 3. 创建示例标签
        List<Tag> existingTags = tagRepository.findByHemangiomaDiagnosisId(id);
        if (existingTags.isEmpty()) {
            // 创建示例标签
            List<Tag> newTags = new ArrayList<>();
            
            // 创建第一个标签 - 居中位置
            Tag tag1 = new Tag();
            tag1.setHemangiomaDiagnosis(diagnosis);
            tag1.setTagName("RICH-先天性快速消退型血管瘤");
            tag1.setX(0.4); // 相对X坐标，范围0-1
            tag1.setY(0.4); // 相对Y坐标，范围0-1
            tag1.setWidth(0.2); // 相对宽度，范围0-1
            tag1.setHeight(0.2); // 相对高度，范围0-1
            tag1.setConfidence(0.95); // 置信度
            newTags.add(tag1);
            
            // 创建第二个标签 - 右下角
            Tag tag2 = new Tag();
            tag2.setHemangiomaDiagnosis(diagnosis);
            tag2.setTagName("NICH-先天性不消退型血管瘤");
            tag2.setX(0.7);
            tag2.setY(0.7);
            tag2.setWidth(0.15);
            tag2.setHeight(0.15);
            tag2.setConfidence(0.85);
            newTags.add(tag2);
            
            // 保存标签
            List<Tag> savedTags = tagRepository.saveAll(newTags);
            result.put("tagsCreated", true);
            result.put("tagCount", savedTags.size());
        } else {
            result.put("tagsCreated", false);
            result.put("existingTagCount", existingTags.size());
        }
        
        // 4. 保存更新后的诊断记录
        HemangiomaDiagnosis savedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
        result.put("diagnosisUpdated", true);
        result.put("diagnosis", savedDiagnosis);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 根据用户ID获取最近的标注记录
     */
    @GetMapping("/recent-by-user/{userId}")
    public ResponseEntity<?> getRecentByUser(@PathVariable String userId) {
        System.out.println("获取用户最近诊断记录 - 用户ID: " + userId);
        
        try {
            // 根据 custom_id 找到用户
            Optional<User> userOpt = userService.getUserByCustomId(userId);
            
            if (!userOpt.isPresent()) {
                System.err.println("找不到用户，ID: " + userId);
                return ResponseEntity.ok(new ArrayList<>());
            }
            
            // 查询用户最近标注的记录，最多10条
            List<HemangiomaDiagnosis> recentDiagnoses = hemangiomaDiagnosisRepository.findTop10ByUserOrderByCreatedAtDesc(userOpt.get());
            System.out.println("找到 " + recentDiagnoses.size() + " 条最近标注记录");
            
            return ResponseEntity.ok(recentDiagnoses);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "获取最近标注记录失败: " + e.getMessage()));
        }
    }

    /**
     * 更新诊断记录的状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<?> updateStatus(
            @PathVariable Integer id,
            @RequestParam(value = "status", required = true) String status) {
        try {
            Optional<HemangiomaDiagnosis> optionalDiagnosis = hemangiomaDiagnosisRepository.findById(id);
            if (!optionalDiagnosis.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到指定ID的诊断记录"));
            }
            
            HemangiomaDiagnosis diagnosis = optionalDiagnosis.get();
            
            // 尝试将字符串转换为Status枚举
            HemangiomaDiagnosis.Status newStatus;
            try {
                newStatus = HemangiomaDiagnosis.Status.valueOf(status);
            } catch (IllegalArgumentException e) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "无效的状态值: " + status));
            }
            
            // 更新状态
            diagnosis.setStatus(newStatus);
            HemangiomaDiagnosis updatedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "状态已更新为: " + newStatus,
                "diagnosis", updatedDiagnosis
            ));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "更新状态失败: " + e.getMessage()));
        }
    }

    /**
     * 更新诊断记录 - 用于保存结构化表单数据
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateDiagnosis(
            @PathVariable Integer id,
            @RequestParam(value = "status", required = false) String statusParam,
            @RequestBody Map<String, Object> updateData) {
        try {
            // 查找现有的诊断记录
            Optional<HemangiomaDiagnosis> optionalDiagnosis = hemangiomaDiagnosisRepository.findById(id);
            if (!optionalDiagnosis.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到指定ID的诊断记录: " + id));
            }

            HemangiomaDiagnosis existingDiagnosis = optionalDiagnosis.get();
            
            // 检查是否有状态参数，优先使用请求参数中的状态
            if (statusParam != null && !statusParam.isEmpty()) {
                try {
                    HemangiomaDiagnosis.Status newStatus = HemangiomaDiagnosis.Status.valueOf(statusParam);
                    existingDiagnosis.setStatus(newStatus);
                    System.out.println("使用请求参数更新状态为: " + newStatus);
                } catch (IllegalArgumentException e) {
                    System.out.println("无效的状态参数值: " + statusParam);
                }
            }
            
            // 检查是否只更新审核意见
            Boolean updateOnlyReviewNotes = Boolean.TRUE.equals(updateData.get("_updateOnlyReviewNotes"));
            if (updateOnlyReviewNotes) {
                System.out.println("仅更新审核意见，不修改其他字段");
                
                // 只更新审核意见字段
                if (updateData.containsKey("reviewNotes")) {
                    existingDiagnosis.setReviewNotes((String) updateData.get("reviewNotes"));
                }
                
                // 如果请求体中也提供了状态，且请求参数中没有状态，则使用请求体中的状态
                if (statusParam == null && updateData.containsKey("status")) {
                    String statusStr = (String) updateData.get("status");
                    try {
                        HemangiomaDiagnosis.Status newStatus = HemangiomaDiagnosis.Status.valueOf(statusStr);
                        existingDiagnosis.setStatus(newStatus);
                        System.out.println("使用请求体更新状态为: " + newStatus);
                    } catch (IllegalArgumentException e) {
                        System.out.println("无效的状态值: " + statusStr);
                    }
                }
                
                // 保存更新后的实体
                HemangiomaDiagnosis updatedDiagnosis = hemangiomaDiagnosisRepository.save(existingDiagnosis);
                
                System.out.println("成功更新诊断记录的审核意见, ID: " + updatedDiagnosis.getId());
                return ResponseEntity.ok(updatedDiagnosis);
            }

            // 正常更新所有字段 - 使用请求体中的数据创建HemangiomaDiagnosis对象
            HemangiomaDiagnosis diagnosisDetails = new HemangiomaDiagnosis();
            
            // 使用Jackson ObjectMapper将Map转换为HemangiomaDiagnosis对象
            ObjectMapper mapper = new ObjectMapper();
            try {
                diagnosisDetails = mapper.convertValue(updateData, HemangiomaDiagnosis.class);
            } catch (Exception e) {
                System.out.println("转换请求数据失败: " + e.getMessage());
                // 继续使用空对象，后续会根据Map中的值单独设置
            }

            // 更新基础信息 - 仅在提供了相应字段时更新
            if (updateData.containsKey("patientAge")) {
                existingDiagnosis.setPatientAge(diagnosisDetails.getPatientAge());
            }
            if (updateData.containsKey("gender")) {
                existingDiagnosis.setGender(diagnosisDetails.getGender());
            }
            if (updateData.containsKey("originType")) {
                existingDiagnosis.setOriginType(diagnosisDetails.getOriginType());
            }
            if (updateData.containsKey("bodyPart")) {
                existingDiagnosis.setBodyPart(diagnosisDetails.getBodyPart());
            }
            if (updateData.containsKey("color")) {
                existingDiagnosis.setColor(diagnosisDetails.getColor());
            }
            if (updateData.containsKey("vesselTexture")) {
                existingDiagnosis.setVesselTexture(diagnosisDetails.getVesselTexture());
            }


            // 更新治疗与管理方案 - 仅在提供了相应字段时更新
            if (updateData.containsKey("treatmentSuggestion")) {
                existingDiagnosis.setTreatmentSuggestion(diagnosisDetails.getTreatmentSuggestion());
            }
            if (updateData.containsKey("precautions")) {
                existingDiagnosis.setPrecautions(diagnosisDetails.getPrecautions());
            }
            // 兼容旧版API，将emergencyInstructions字段的值映射到precautions
            if (updateData.containsKey("emergencyInstructions")) {
                existingDiagnosis.setPrecautions((String)updateData.get("emergencyInstructions"));
            }
            
            // 更新审核意见
            if (updateData.containsKey("reviewNotes")) {
                existingDiagnosis.setReviewNotes((String) updateData.get("reviewNotes"));
            }
            
            // 更新状态 - 如果请求参数中没有状态，则使用请求体中的状态
            if (statusParam == null && updateData.containsKey("status")) {
                if (diagnosisDetails.getStatus() != null) {
                    existingDiagnosis.setStatus(diagnosisDetails.getStatus());
                } else if (updateData.get("status") instanceof String) {
                    String statusStr = (String) updateData.get("status");
                    try {
                        HemangiomaDiagnosis.Status newStatus = HemangiomaDiagnosis.Status.valueOf(statusStr);
                        existingDiagnosis.setStatus(newStatus);
                    } catch (IllegalArgumentException e) {
                        System.out.println("无效的状态值: " + statusStr);
                    }
                }
            }

            // 保存更新后的实体
            HemangiomaDiagnosis updatedDiagnosis = hemangiomaDiagnosisRepository.save(existingDiagnosis);
            
            System.out.println("成功更新诊断记录, ID: " + updatedDiagnosis.getId());
            return ResponseEntity.ok(updatedDiagnosis);

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "更新诊断记录失败: " + e.getMessage()));
        }
    }

    /**
     * 删除诊断记录
     * 只能删除非"已完成"(APPROVED)状态的病例
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteDiagnosis(@PathVariable Integer id) {
        try {
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(id);
            if (!diagnosisOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到指定ID的诊断记录"));
            }
            
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            
            // 检查状态 - 如果是"已完成"(APPROVED)状态，则不允许删除
            if (diagnosis.getStatus() == HemangiomaDiagnosis.Status.APPROVED) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of(
                            "error", "无法删除已完成状态的诊断记录",
                            "status", diagnosis.getStatus()
                        ));
            }
            
            // 删除关联的标签 - 由于设置了cascade=CascadeType.ALL和orphanRemoval=true，
            // JPA会自动删除关联的标签，无需手动操作
            
            // 删除关联的边界框数据
            if (diagnosis.getBoundingBoxes() != null && !diagnosis.getBoundingBoxes().isEmpty()) {
                diagnosis.setBoundingBoxes(new ArrayList<>());
                diagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
            }
            
            // 删除诊断记录
            hemangiomaDiagnosisRepository.delete(diagnosis);
            
            // 返回成功消息
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "诊断记录已成功删除",
                "id", id
            ));
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "删除诊断记录失败: " + e.getMessage()));
        }
    }

    /**
     * 更新诊断记录的处理后图像路径
     */
    @PostMapping("/update-image-path")
    public ResponseEntity<?> updateImagePath(@RequestBody Map<String, Object> request) {
        try {
            // 获取请求参数
            Integer diagnosisId = (Integer) request.get("diagnosisId");
            String processedImagePath = (String) request.get("processedImagePath");
            
            if (diagnosisId == null || processedImagePath == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "必须提供诊断ID和处理后图像路径"));
            }
            
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(diagnosisId);
            if (!diagnosisOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "未找到指定ID的诊断记录: " + diagnosisId));
            }
            
            // 更新处理后的图像路径
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            diagnosis.setProcessedImagePath(processedImagePath);
            hemangiomaDiagnosisRepository.save(diagnosis);
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "图像路径已更新",
                "diagnosisId", diagnosisId,
                "processedImagePath", processedImagePath
            ));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "更新图像路径时发生错误: " + e.getMessage()));
        }
    }
}