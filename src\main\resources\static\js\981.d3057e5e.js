"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[981],{259:(e,a,t)=>{var i=t(4376),n=t(6198),r=t(6837),o=t(6080),s=function(e,a,t,l,c,u,g,m){var p,d,h=c,f=0,v=!!g&&o(g,m);while(f<l)f in t&&(p=v?v(t[f],f,a):t[f],u>0&&i(p)?(d=n(p),h=s(e,a,p,d,h,u-1)-1):(r(h+1),e[h]=p),h++),f++;return h};e.exports=s},788:(e,a,t)=>{var i=t(34),n=t(2195),r=t(8227),o=r("match");e.exports=function(e){var a;return i(e)&&(void 0!==(a=e[o])?!!a:"RegExp"===n(e))}},1392:(e,a,t)=>{var i=t(6518),n=t(7476),r=t(7347).f,o=t(8014),s=t(655),l=t(2892),c=t(7750),u=t(1436),g=t(6395),m=n("".slice),p=Math.min,d=u("startsWith"),h=!g&&!d&&!!function(){var e=r(String.prototype,"startsWith");return e&&!e.writable}();i({target:"String",proto:!0,forced:!h&&!d},{startsWith:function(e){var a=s(c(this));l(e);var t=o(p(arguments.length>1?arguments[1]:void 0,a.length)),i=s(e);return m(a,t,t+i.length)===i}})},1436:(e,a,t)=>{var i=t(8227),n=i("match");e.exports=function(e){var a=/./;try{"/./"[e](a)}catch(t){try{return a[n]=!1,"/./"[e](a)}catch(i){}}return!1}},1701:(e,a,t)=>{var i=t(6518),n=t(9565),r=t(9306),o=t(8551),s=t(1767),l=t(9462),c=t(6319),u=t(9539),g=t(4549),m=t(6395),p=!m&&g("map",TypeError),d=l((function(){var e=this.iterator,a=o(n(this.next,e)),t=this.done=!!a.done;if(!t)return c(e,this.mapper,[a.value,this.counter++],!0)}));i({target:"Iterator",proto:!0,real:!0,forced:m||p},{map:function(e){o(this);try{r(e)}catch(a){u(this,"throw",a)}return p?n(p,this,e):new d(s(this),{mapper:e})}})},2062:(e,a,t)=>{var i=t(6518),n=t(9213).map,r=t(597),o=r("map");i({target:"Array",proto:!0,forced:!o},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},2892:(e,a,t)=>{var i=t(788),n=TypeError;e.exports=function(e){if(i(e))throw new n("The method doesn't accept regular expressions");return e}},3514:(e,a,t)=>{var i=t(6469);i("flat")},4599:(e,a,t)=>{var i=t(6518),n=t(4576),r=t(9472),o=r(n.setTimeout,!0);i({global:!0,bind:!0,forced:n.setTimeout!==o},{setTimeout:o})},5575:(e,a,t)=>{var i=t(6518),n=t(4576),r=t(9472),o=r(n.setInterval,!0);i({global:!0,bind:!0,forced:n.setInterval!==o},{setInterval:o})},6031:(e,a,t)=>{t(5575),t(4599)},6449:(e,a,t)=>{var i=t(6518),n=t(259),r=t(8981),o=t(6198),s=t(1291),l=t(1469);i({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,a=r(this),t=o(a),i=l(a,0);return i.length=n(i,a,a,t,0,void 0===e?1:s(e)),i}})},9472:(e,a,t)=>{var i=t(4576),n=t(8745),r=t(4901),o=t(4215),s=t(2839),l=t(7680),c=t(2812),u=i.Function,g=/MSIE .\./.test(s)||"BUN"===o&&function(){var e=i.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}();e.exports=function(e,a){var t=a?2:1;return g?function(i,o){var s=c(arguments.length,1)>t,g=r(i)?i:u(i),m=s?l(arguments,t):[],p=s?function(){n(g,this,m)}:g;return a?e(p,o):e(p)}:e}},9981:(e,a,t)=>{t.r(a),t.d(a,{default:()=>B});t(4114);var i=t(641),n=t(33),r={class:"case-view-container"},o={class:"page-header"},s={class:"header-actions"},l={class:"card-header"},c={class:"case-info"},u={key:0,class:"case-images"},g={class:"image-list"},m={key:1,class:"case-image-pairs"},p={class:"image-pair-list"},d={key:2,class:"case-annotations"},h={class:"annotation-list"},f={class:"annotation-user"};function v(e,a,t,v,_,k){var w=(0,i.g2)("el-button"),P=(0,i.g2)("el-tag"),I=(0,i.g2)("el-descriptions-item"),b=(0,i.g2)("el-descriptions"),L=(0,i.g2)("el-image"),y=(0,i.g2)("image-pair-view"),D=(0,i.g2)("el-timeline-item"),C=(0,i.g2)("el-timeline"),E=(0,i.g2)("el-card"),S=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",r,[(0,i.Lk)("div",o,[a[4]||(a[4]=(0,i.Lk)("h2",null,"病例详情",-1)),(0,i.Lk)("div",s,[(0,i.bF)(w,{type:"primary",size:"small",onClick:a[0]||(a[0]=function(a){return e.$router.push("/cases/edit/".concat(_.caseId))})},{default:(0,i.k6)((function(){return a[2]||(a[2]=[(0,i.eW)("编辑")])})),_:1,__:[2]}),(0,i.bF)(w,{onClick:a[1]||(a[1]=function(a){return e.$router.back()})},{default:(0,i.k6)((function(){return a[3]||(a[3]=[(0,i.eW)("返回")])})),_:1,__:[3]})])]),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(E,null,{header:(0,i.k6)((function(){return[(0,i.Lk)("div",l,[a[5]||(a[5]=(0,i.Lk)("span",null,"基本信息",-1)),(0,i.bF)(P,{type:k.getStatusType(_.caseDetail.status)},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(_.caseDetail.status),1)]})),_:1},8,["type"])])]})),default:(0,i.k6)((function(){return[(0,i.Lk)("div",c,[(0,i.bF)(b,{column:2,border:""},{default:(0,i.k6)((function(){return[(0,i.bF)(I,{label:"病例编号"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(_.caseDetail.caseId||"暂无"),1)]})),_:1}),(0,i.bF)(I,{label:"患者信息"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(_.caseDetail.patientInfo||"暂无"),1)]})),_:1}),(0,i.bF)(I,{label:"部位"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(_.caseDetail.department||"暂无"),1)]})),_:1}),(0,i.bF)(I,{label:"类型"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(_.caseDetail.type||"暂无"),1)]})),_:1}),(0,i.bF)(I,{label:"创建时间"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(_.caseDetail.createTime||"暂无"),1)]})),_:1}),(0,i.bF)(I,{label:"更新时间"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(_.caseDetail.updateTime||"暂无"),1)]})),_:1}),(0,i.bF)(I,{label:"备注",span:2},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(_.caseDetail.note||"暂无备注"),1)]})),_:1})]})),_:1})]),_.caseDetail.images&&_.caseDetail.images.length?((0,i.uX)(),(0,i.CE)("div",u,[a[7]||(a[7]=(0,i.Lk)("h3",null,"医学影像",-1)),(0,i.Lk)("div",g,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(_.caseDetail.images,(function(e,t){return(0,i.uX)(),(0,i.CE)("div",{key:t,class:"image-item"},[(0,i.bF)(L,{src:e.url,"preview-src-list":_.imageUrls,"initial-index":t},{error:(0,i.k6)((function(){return a[6]||(a[6]=[(0,i.Lk)("div",{class:"image-error"},[(0,i.Lk)("i",{class:"el-icon-picture-outline"})],-1)])})),_:2},1032,["src","preview-src-list","initial-index"])])})),128))])])):(0,i.Q3)("",!0),_.imagePairs&&_.imagePairs.length?((0,i.uX)(),(0,i.CE)("div",m,[a[8]||(a[8]=(0,i.Lk)("h3",null,"标注对比",-1)),(0,i.Lk)("div",p,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(_.imagePairs,(function(e,a){return(0,i.uX)(),(0,i.Wv)(y,{key:a,imagePair:e,title:"标注图对比 #".concat(a+1)},null,8,["imagePair","title"])})),128))])])):(0,i.Q3)("",!0),_.caseDetail.annotations&&_.caseDetail.annotations.length?((0,i.uX)(),(0,i.CE)("div",d,[a[9]||(a[9]=(0,i.Lk)("h3",null,"标注信息",-1)),(0,i.Lk)("div",h,[(0,i.bF)(C,null,{default:(0,i.k6)((function(){return[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(_.caseDetail.annotations,(function(e,a){return(0,i.uX)(),(0,i.Wv)(D,{key:a,timestamp:e.createTime,type:"approve"===e.type?"success":"reject"===e.type?"danger":"primary"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,n.v_)(e.content)+" ",1),(0,i.Lk)("p",f,(0,n.v_)(e.user),1)]})),_:2},1032,["timestamp","type"])})),128))]})),_:1})])])):(0,i.Q3)("",!0)]})),_:1})),[[S,_.loading]])])}t(6449),t(2062),t(3514),t(8111),t(1701),t(6099),t(7764),t(2953),t(6031),t(2675),t(9463);var _={class:"image-pair-container"},k={key:0,class:"image-pair-title"},w={class:"image-pair-content"},P={class:"image-wrapper"},I={class:"image-container"},b=["src"],L={key:1,class:"no-image"},y={key:0,class:"debug-info"},D={class:"image-wrapper"},C={class:"image-container"},E=["src"],S={key:1,class:"no-image"},T={key:0,class:"debug-info"},X={key:1,class:"image-pair-description"},W={class:"debug-controls"};function F(e,a,t,r,o,s){return(0,i.uX)(),(0,i.CE)("div",_,[t.title?((0,i.uX)(),(0,i.CE)("h4",k,(0,n.v_)(t.title),1)):(0,i.Q3)("",!0),(0,i.Lk)("div",w,[(0,i.Lk)("div",P,[a[6]||(a[6]=(0,i.Lk)("h5",null,"原始图像",-1)),(0,i.Lk)("div",I,[t.imagePair.image_one_path?((0,i.uX)(),(0,i.CE)("img",{key:0,src:s.getImageSrc(t.imagePair.image_one_path),class:"pair-image",alt:"原始图像",onLoad:a[0]||(a[0]=function(e){return s.onImageLoad("原始图像加载成功",t.imagePair.image_one_path)}),onError:a[1]||(a[1]=function(e){return s.onImageError("原始图像加载失败",t.imagePair.image_one_path)})},null,40,b)):((0,i.uX)(),(0,i.CE)("div",L,a[5]||(a[5]=[(0,i.Lk)("i",{class:"el-icon-picture-outline"},null,-1),(0,i.Lk)("p",null,"无原始图像",-1)])))]),o.showDebug?((0,i.uX)(),(0,i.CE)("div",y,[(0,i.Lk)("p",null,"原始路径: "+(0,n.v_)(t.imagePair.image_one_path),1),(0,i.Lk)("p",null,"处理后路径: "+(0,n.v_)(s.getImageSrc(t.imagePair.image_one_path)),1),(0,i.Lk)("p",null,"状态: "+(0,n.v_)(o.imageOneStatus),1)])):(0,i.Q3)("",!0)]),(0,i.Lk)("div",D,[a[8]||(a[8]=(0,i.Lk)("h5",null,"标注后图像",-1)),(0,i.Lk)("div",C,[t.imagePair.image_two_path?((0,i.uX)(),(0,i.CE)("img",{key:0,src:s.getImageSrc(t.imagePair.image_two_path),class:"pair-image",alt:"标注后图像",onLoad:a[2]||(a[2]=function(e){return s.onImageLoad("标注图像加载成功",t.imagePair.image_two_path)}),onError:a[3]||(a[3]=function(e){return s.onImageError("标注图像加载失败",t.imagePair.image_two_path)})},null,40,E)):((0,i.uX)(),(0,i.CE)("div",S,a[7]||(a[7]=[(0,i.Lk)("i",{class:"el-icon-picture-outline"},null,-1),(0,i.Lk)("p",null,"无标注图像",-1)])))]),o.showDebug?((0,i.uX)(),(0,i.CE)("div",T,[(0,i.Lk)("p",null,"原始路径: "+(0,n.v_)(t.imagePair.image_two_path),1),(0,i.Lk)("p",null,"处理后路径: "+(0,n.v_)(s.getImageSrc(t.imagePair.image_two_path)),1),(0,i.Lk)("p",null,"状态: "+(0,n.v_)(o.imageTwoStatus),1)])):(0,i.Q3)("",!0)])]),t.imagePair.description?((0,i.uX)(),(0,i.CE)("div",X,[(0,i.Lk)("p",null,(0,n.v_)(t.imagePair.description),1)])):(0,i.Q3)("",!0),(0,i.Lk)("div",W,[(0,i.Lk)("button",{onClick:a[4]||(a[4]=function(){return s.toggleDebug&&s.toggleDebug.apply(s,arguments)}),class:"debug-toggle"},(0,n.v_)(o.showDebug?"隐藏调试信息":"显示调试信息"),1)])])}t(7495),t(906),t(1392);const V={name:"ImagePairView",props:{imagePair:{type:Object,required:!0,default:function(){return{image_one_path:"",image_two_path:"",description:""}}},title:{type:String,default:""}},data:function(){return{showDebug:!0,imageOneStatus:"等待加载",imageTwoStatus:"等待加载"}},mounted:function(){console.log("[ImagePairView] 组件挂载, 图像对数据:",this.imagePair),this.imagePair.image_one_path&&(console.log("[ImagePairView] 原始图像路径:",this.imagePair.image_one_path),console.log("[ImagePairView] 处理后的URL:",this.getImageSrc(this.imagePair.image_one_path))),this.imagePair.image_two_path&&(console.log("[ImagePairView] 标注图像路径:",this.imagePair.image_two_path),console.log("[ImagePairView] 处理后的URL:",this.getImageSrc(this.imagePair.image_two_path)))},methods:{getImageSrc:function(e){return e?(console.log("[ImagePairView] 处理图像路径: ".concat(e)),/^[a-zA-Z]:\\/.test(e)?(console.log("[ImagePairView] 检测到文件系统路径"),"http://localhost:8085/medical/image/system-path?path=".concat(encodeURIComponent(e))):e.startsWith("/")?(console.log("[ImagePairView] 相对路径转换为完整URL"),"http://localhost:8085".concat(e)):(console.log("[ImagePairView] 已是完整URL格式，不需转换: ".concat(e)),e)):""},onImageLoad:function(e,a){console.log("[ImagePairView] ".concat(e,":"),a),a===this.imagePair.image_one_path?this.imageOneStatus="加载成功":a===this.imagePair.image_two_path&&(this.imageTwoStatus="加载成功")},onImageError:function(e,a){console.error("[ImagePairView] ".concat(e,":"),a),a===this.imagePair.image_one_path?this.imageOneStatus="加载失败":a===this.imagePair.image_two_path&&(this.imageTwoStatus="加载失败"),console.log("[ImagePairView] 尝试调整路径格式以排查问题")},toggleDebug:function(){this.showDebug=!this.showDebug}}};var x=t(6262);const U=(0,x.A)(V,[["render",F],["__scopeId","data-v-50d1f86e"]]),Q=U;var A=t(653);const R={name:"CaseView",components:{ImagePairView:Q},data:function(){return{caseId:this.$route.params.id,loading:!0,caseDetail:{caseId:"",patientInfo:"",department:"",type:"",status:"",note:"",createTime:"",updateTime:"",images:[],annotations:[]},imageUrls:[],imagePairs:[]}},created:function(){this.fetchCaseDetail()},methods:{getStatusType:function(e){var a={待标注:"info",已标注:"success",审核中:"warning",已通过:"success",已驳回:"danger"};return a[e]||"info"},fetchCaseDetail:function(){var e=this;setTimeout((function(){e.caseDetail={caseId:e.caseId,patientInfo:"模拟患者",department:"头部",type:"血管瘤",status:"已标注",note:"这是备注信息",createTime:"2023-06-10 12:30:45",updateTime:"2023-06-12 15:20:36",images:[],annotations:[]},e.imageUrls=e.caseDetail.images.map((function(e){return e.url})),e.fetchImagePairs()}),800)},fetchImagePairs:function(){var e=this;if(this.caseDetail.images&&this.caseDetail.images.length>0){var a=this.caseDetail.images.map((function(e){return A.A.imagePairs.getByMetadataId(e.id).then((function(e){return e.data}))["catch"]((function(e){return console.error("获取图像对失败",e),[]}))}));Promise.all(a).then((function(a){e.imagePairs=a.flat(),console.log("加载了",e.imagePairs.length,"组图像对")}))["finally"]((function(){e.loading=!1}))}else this.loading=!1}}},O=(0,x.A)(R,[["render",v],["__scopeId","data-v-315a0491"]]),B=O}}]);