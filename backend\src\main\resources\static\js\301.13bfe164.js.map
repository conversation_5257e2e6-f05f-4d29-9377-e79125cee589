{"version": 3, "file": "js/301.13bfe164.js", "mappings": "0VAsGO,SAASA,EAAaC,GAC3B,IAAMC,EAAY,MAAHC,OAASC,KAAKC,OAC7B,OAAIJ,EAAIK,SAAS,KACR,GAAPH,OAAUF,EAAG,KAAAE,OAAID,GAEV,GAAPC,OAAUF,EAAG,KAAAE,OAAID,EAErB,CC3FO,SAASK,EAAYN,GAC1B,IAAKA,EAAK,MAAO,GAKjB,GAHAO,QAAQC,IAAI,wBAAyBR,GAGjC,eAAeS,KAAKT,GAAM,CAC5BO,QAAQC,IAAI,2BACZ,IAAME,EAAcC,mBAAmBX,GAGjCY,EAAUC,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAiB,GAClD,OAAOf,EAAa,GAADG,OAAIU,EAAO,oCAAAV,OAAmCQ,GACnE,CAGA,GAAIV,EAAIe,WAAW,YAAcf,EAAIe,WAAW,YAC9C,OAAOhB,EAAaC,GAItB,GAAIA,EAAIe,WAAW,SACjB,OAAOf,EAIT,IAAIgB,EAAWhB,EAaf,OAXIA,EAAIe,WAAW,aACjBR,QAAQC,IAAI,mCAEZQ,EAAWH,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAYZ,OAAGF,GAAQA,GAC1CA,EAAIe,WAAW,OACxBR,QAAQC,IAAI,2CAEZQ,EAAWH,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAYZ,OAAGe,EAAAA,IAAgBf,OAAGF,GAAG,GAAAE,OAAQe,EAAAA,IAAgBf,OAAGF,IAG9FO,QAAQC,IAAI,yBAA0BQ,GAC/BjB,EAAaiB,EACtB,C,kECzDOE,MAAM,uB,GACJA,MAAM,e,GAEJA,MAAM,kB,GAJjBC,IAAA,EAeoCD,MAAM,2B,GAC7BA,MAAM,kB,GAhBnBC,IAAA,EAkBwCD,MAAM,gB,GAEjCA,MAAM,6B,GAGAA,MAAM,e,GAvBzBC,IAAA,EA0B2CD,MAAM,c,GA1BjDC,IAAA,EAqCkBD,MAAM,oB,GAMXA,MAAM,e,GAORA,MAAM,a,GAkBNA,MAAM,sB,GACJA,MAAM,kB,GArEnBC,IAAA,EAuEwCD,MAAM,gB,GAvE9CC,IAAA,EA2E6ED,MAAM,+B,wPA1EjFE,EAAAA,EAAAA,IA0GM,MA1GNC,EA0GM,EAzGJC,EAAAA,EAAAA,IASM,MATNC,EASM,cARJD,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAMM,MANNE,EAMM,CALaC,EAAAC,gBAAa,WAA9BC,EAAAA,EAAAA,IAAsGC,EAAA,CAL9GT,IAAA,EAKwCU,KAAK,UAAUC,KAAK,QAASC,QAAOC,EAAAC,oB,CAL5E,SAAAC,EAAAA,EAAAA,KAKgG,kBAAEC,EAAA,KAAAA,EAAA,KALlGC,EAAAA,EAAAA,IAKgG,O,IALhGC,EAAA,EAAAC,GAAA,K,iBAAAC,EAAAA,EAAAA,IAAA,QAMQC,EAAAA,EAAAA,IAEaC,EAAA,CAFDC,QAAQ,SAASC,UAAU,O,CAN/C,SAAAT,EAAAA,EAAAA,KAOU,iBAA0G,EAA1GM,EAAAA,EAAAA,IAA0GZ,EAAA,CAA/FgB,KAAK,kBAAkBd,KAAK,QAAQe,OAAA,GAAQC,QAASrB,EAAAqB,QAAUf,QAAOC,EAAAe,a,kCAP3FV,EAAA,KASQG,EAAAA,EAAAA,IAAsDZ,EAAA,CAA3CoB,KAAA,GAAMjB,QAAKI,EAAA,KAAAA,EAAA,YAAAc,GAAA,OAAEC,EAAAC,QAAQC,MAAI,I,CAT5C,SAAAlB,EAAAA,EAAAA,KASgD,kBAAEC,EAAA,KAAAA,EAAA,KATlDC,EAAAA,EAAAA,IASgD,O,IAThDC,EAAA,EAAAC,GAAA,W,qBAaIX,EAAAA,EAAAA,IA6FU0B,EAAA,MAhEGC,QAAMpB,EAAAA,EAAAA,KACf,iBAGM,EAHNZ,EAAAA,EAAAA,IAGM,MAHNiC,EAGM,gBAFJjC,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVkB,EAAAA,EAAAA,IAAiFgB,EAAA,CAAxE3B,KAAMG,EAAAyB,cAAchC,EAAAiC,WAAWC,S,CA7ClD,SAAAzB,EAAAA,EAAAA,KA6C2D,iBAAuB,EA7ClFE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA6C8DnC,EAAAiC,WAAWC,QAAM,G,IA7C/EtB,EAAA,G,kBAAA,SAAAH,EAAAA,EAAAA,KAkBsB,iBAmChB,CAtCWT,EAAAoC,oBAAiB,WAA5BzC,EAAAA,EAAAA,IAqBM,MArBN0C,EAqBM,EApBJxC,EAAAA,EAAAA,IAGM,MAHNyC,EAGM,cAFJzC,EAAAA,EAAAA,IAAe,UAAX,UAAM,IACGG,EAAAuC,kBAAe,WAA5B5C,EAAAA,EAAAA,IAAkG,QAAlG6C,EAAmD,UAAML,EAAAA,EAAAA,IAAG5B,EAAAkC,WAAWzC,EAAAuC,kBAAe,KAlBhGzB,EAAAA,EAAAA,IAAA,UAoBQjB,EAAAA,EAAAA,IAeM,MAfN6C,EAeM,EAdJ3B,EAAAA,EAAAA,IAaW4B,EAAA,CAbAC,IAAK5C,EAAAoC,kBAAmBS,IAAI,UAAUpD,MAAM,kBAAmB,mBAAgB,CAAGO,EAAAoC,mBAAqBU,QAAOvC,EAAAwC,kB,CAC5GC,OAAKvC,EAAAA,EAAAA,KACd,iBASM,EATNZ,EAAAA,EAAAA,IASM,MATNoD,EASM,cARJpD,EAAAA,EAAAA,IAAuC,KAApCJ,MAAM,2BAAyB,yBAClCI,EAAAA,EAAAA,IAAe,SAAZ,YAAQ,IACAG,EAAAkD,iBAAc,WAAzBvD,EAAAA,EAAAA,IAKM,MALNwD,EAKM,cAJJtD,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAA8C,uBAA3CA,EAAAA,EAAAA,IAAe,SAAZ,YAAQ,KA5BhCc,EAAAA,EAAAA,IA4BoC,KAACwB,EAAAA,EAAAA,IAAGnC,EAAAoD,UAAUC,SAAO,MACvCxD,EAAAA,EAAAA,IAA8C,uBAA3CA,EAAAA,EAAAA,IAAe,SAAZ,YAAQ,KA7BhCc,EAAAA,EAAAA,IA6BoC,KAACwB,EAAAA,EAAAA,IAAGnC,EAAAoD,UAAUE,SAAO,kBACvCzD,EAAAA,EAAAA,IAAyC,aAAlC,8BAA0B,QA9BnDiB,EAAAA,EAAAA,IAAA,S,IAAAF,EAAA,G,0DAqCMjB,EAAAA,EAAAA,IAGM,MAHN4D,EAGM7C,EAAA,MAAAA,EAAA,MAFJb,EAAAA,EAAAA,IAAuC,KAApCJ,MAAM,2BAAyB,UAClCI,EAAAA,EAAAA,IAAgB,SAAb,aAAS,QAWdA,EAAAA,EAAAA,IAeM,MAfN2D,EAeM,EAdJzC,EAAAA,EAAAA,IAakB0C,EAAA,CAbAC,OAAQ,EAAGC,OAAA,I,CAnDrC,SAAAlD,EAAAA,EAAAA,KAoDU,iBAAwF,EAAxFM,EAAAA,EAAAA,IAAwF6C,EAAA,CAAlEC,MAAM,QAAM,CApD5C,SAAApD,EAAAA,EAAAA,KAoD6C,iBAA8B,EApD3EE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAoDgDnC,EAAAiC,WAAW6B,QAAU,KAAJ,G,IApDjElD,EAAA,IAqDsCZ,EAAAiC,WAAW8B,cAAW,WAAlD7D,EAAAA,EAAAA,IAAoH0D,EAAA,CArD9HlE,IAAA,EAqD8DmE,MAAM,Q,CArDpE,SAAApD,EAAAA,EAAAA,KAqD2E,iBAA4B,EArDvGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAqD8EnC,EAAAiC,WAAW8B,aAAW,G,IArDpGnD,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAsDsCd,EAAAiC,WAAW+B,aAAU,WAAjD9D,EAAAA,EAAAA,IAAgH0D,EAAA,CAtD1HlE,IAAA,EAsD6DmE,MAAM,M,CAtDnE,SAAApD,EAAAA,EAAAA,KAsDwE,iBAA2B,EAtDnGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAsD2EnC,EAAAiC,WAAW+B,YAAU,G,IAtDhGpD,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAuDsCd,EAAAiC,WAAW7B,OAAI,WAA3CF,EAAAA,EAAAA,IAAuG0D,EAAA,CAvDjHlE,IAAA,EAuDuDmE,MAAM,S,CAvD7D,SAAApD,EAAAA,EAAAA,KAuDqE,iBAAqB,EAvD1FE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAuDwEnC,EAAAiC,WAAW7B,MAAI,G,IAvDvFQ,EAAA,MAAAE,EAAAA,EAAAA,IAAA,QAwDUC,EAAAA,EAAAA,IAA4F6C,EAAA,CAAtEC,MAAM,QAAM,CAxD5C,SAAApD,EAAAA,EAAAA,KAwD6C,iBAAkC,EAxD/EE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAwDgDnC,EAAAiC,WAAWgC,YAAc,KAAJ,G,IAxDrErD,EAAA,KAyDUG,EAAAA,EAAAA,IAA4F6C,EAAA,CAAtEC,MAAM,QAAM,CAzD5C,SAAApD,EAAAA,EAAAA,KAyD6C,iBAAkC,EAzD/EE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAyDgDnC,EAAAiC,WAAWiC,YAAc,KAAJ,G,IAzDrEtD,EAAA,IA0DsCZ,EAAAiC,WAAWkC,cAAW,WAAlDjE,EAAAA,EAAAA,IAAoH0D,EAAA,CA1D9HlE,IAAA,EA0D8DmE,MAAM,Q,CA1DpE,SAAApD,EAAAA,EAAAA,KA0D2E,iBAA4B,EA1DvGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA0D8EnC,EAAAiC,WAAWkC,aAAW,G,IA1DpGvD,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA2DsCd,EAAAiC,WAAWmC,eAAY,WAAnDlE,EAAAA,EAAAA,IAA2H0D,EAAA,CA3DrIlE,IAAA,EA2D+DmE,MAAM,a,CA3DrE,SAAApD,EAAAA,EAAAA,KA2DiF,iBAA6B,EA3D9GE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA2DoFnC,EAAAiC,WAAWmC,cAAY,G,IA3D3GxD,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA4DsCd,EAAAiC,WAAWoC,gBAAa,WAApDnE,EAAAA,EAAAA,IAA+I0D,EAAA,CA5DzJlE,IAAA,EA4DgEmE,MAAM,Q,CA5DtE,SAAApD,EAAAA,EAAAA,KA4D6E,iBAAqD,EA5DlIE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA4DgF5B,EAAA+D,sBAAsBtE,EAAAiC,WAAWoC,gBAAa,G,IA5D9HzD,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA6DsCd,EAAAiC,WAAWsC,OAAI,WAA3CrE,EAAAA,EAAAA,IAEuB0D,EAAA,CA/DjClE,IAAA,EA6DuDmE,MAAM,KAAMW,KAAM,G,CA7DzE,SAAA/D,EAAAA,EAAAA,KA8DY,iBAAqB,EA9DjCE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA8DenC,EAAAiC,WAAWsC,MAAI,G,IA9D9B3D,EAAA,MAAAE,EAAAA,EAAAA,IAAA,O,IAAAF,EAAA,OAoEMf,EAAAA,EAAAA,IAqCM,MArCN4E,EAqCM,EApCJ5E,EAAAA,EAAAA,IAGM,MAHN6E,EAGM,gBAFJ7E,EAAAA,EAAAA,IAAa,UAAT,QAAI,IACKG,EAAA2E,YAAc,IAAH,WAAxBhF,EAAAA,EAAAA,IAAgF,QAAhFiF,EAAmD,QAAIzC,EAAAA,EAAAA,IAAGnC,EAAA2E,aAAW,KAvE/E7D,EAAAA,EAAAA,IAAA,SA2EmBd,EAAAiC,WAAW4C,qBAAuB7E,EAAAiC,WAAW6C,cAAW,WAAnEnF,EAAAA,EAAAA,IAQM,MARNoF,EAQM,gBAPJlF,EAAAA,EAAAA,IAAgB,UAAZ,WAAO,KACXkB,EAAAA,EAAAA,IAKkB0C,EAAA,CALAC,OAAQ,EAAGC,OAAA,I,CA7EvC,SAAAlD,EAAAA,EAAAA,KA8HC,iBAOe,CAvDwBT,EAAAiC,WAAW+C,oBAAiB,WAAxD9E,EAAAA,EAAAA,IAAgI0D,EAAA,CA9E5IlE,IAAA,EA8EsEmE,MAAM,Q,CA9E5E,SAAApD,EAAAA,EAAAA,KA8EmF,iBAAkC,EA9ErHE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA8EsFnC,EAAAiC,WAAW+C,mBAAiB,G,IA9ElHpE,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA+EwCd,EAAAiC,WAAW4C,sBAAmB,WAA1D3E,EAAAA,EAAAA,IAAoI0D,EAAA,CA/EhJlE,IAAA,EA+EwEmE,MAAM,Q,CA/E9E,SAAApD,EAAAA,EAAAA,KA+EqF,iBAAoC,EA/EzHE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA+EwFnC,EAAAiC,WAAW4C,qBAAmB,G,IA/EtHjE,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAgFwCd,EAAAiC,WAAW6C,cAAW,WAAlD5E,EAAAA,EAAAA,IAAoH0D,EAAA,CAhFhIlE,IAAA,EAgFgEmE,MAAM,Q,CAhFtE,SAAApD,EAAAA,EAAAA,KAgF6E,iBAA4B,EAhFzGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAgFgFnC,EAAAiC,WAAW6C,aAAW,G,IAhFtGlE,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAiFwCd,EAAAiC,WAAWgD,cAAW,WAAlD/E,EAAAA,EAAAA,IAAoH0D,EAAA,CAjFhIlE,IAAA,EAiFgEmE,MAAM,Q,CAjFtE,SAAApD,EAAAA,EAAAA,KAiF6E,iBAA4B,EAjFzGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAiFgFnC,EAAAiC,WAAWgD,aAAW,G,IAjFtGrE,EAAA,MAAAE,EAAAA,EAAAA,IAAA,O,IAAAF,EAAA,QAAAE,EAAAA,EAAAA,IAAA,QAsFQC,EAAAA,EAAAA,IAkBkB0C,EAAA,CAlBAC,OAAQ,EAAGC,OAAA,GAAOlE,MAAM,Q,CAtFlD,SAAAgB,EAAAA,EAAAA,KA+JmD,iBAM7C,CA7EgCT,EAAAiC,WAAWiD,aAAU,WAAjDhF,EAAAA,EAAAA,IAAkH0D,EAAA,CAxF5HlE,IAAA,EAwF6DmE,MAAM,Q,CAxFnE,SAAApD,EAAAA,EAAAA,KAwF0E,iBAA2B,EAxFrGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAwF6EnC,EAAAiC,WAAWiD,YAAU,G,IAxFlGtE,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAyFsCd,EAAAiC,WAAWkD,YAAS,WAAhDjF,EAAAA,EAAAA,IAAgH0D,EAAA,CAzF1HlE,IAAA,EAyF4DmE,MAAM,Q,CAzFlE,SAAApD,EAAAA,EAAAA,KAyFyE,iBAA0B,EAzFnGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAyF4EnC,EAAAiC,WAAWkD,WAAS,G,IAzFhGvE,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA0FsCd,EAAAiC,WAAWmD,gBAAa,WAApDlF,EAAAA,EAAAA,IAAyH0D,EAAA,CA1FnIlE,IAAA,EA0FgEmE,MAAM,S,CA1FtE,SAAApD,EAAAA,EAAAA,KA0F8E,iBAA8B,EA1F5GE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA0FiFnC,EAAAiC,WAAWmD,eAAa,G,IA1FzGxE,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA2FsCd,EAAAiC,WAAWoD,eAAY,WAAnDnF,EAAAA,EAAAA,IAAsH0D,EAAA,CA3FhIlE,IAAA,EA2F+DmE,MAAM,Q,CA3FrE,SAAApD,EAAAA,EAAAA,KA2F4E,iBAA6B,EA3FzGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA2F+EnC,EAAAiC,WAAWoD,cAAY,G,IA3FtGzE,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA4FsCd,EAAAiC,WAAWqD,wBAAqB,WAA5DpF,EAAAA,EAAAA,IAAwI0D,EAAA,CA5FlJlE,IAAA,EA4FwEmE,MAAM,Q,CA5F9E,SAAApD,EAAAA,EAAAA,KA4FqF,iBAAsC,EA5F3HE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA4FwFnC,EAAAiC,WAAWqD,uBAAqB,G,IA5FxH1E,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA6FsCd,EAAAiC,WAAWsD,WAAQ,WAA/CrF,EAAAA,EAAAA,IAA8G0D,EAAA,CA7FxHlE,IAAA,EA6F2DmE,MAAM,Q,CA7FjE,SAAApD,EAAAA,EAAAA,KA6FwE,iBAAyB,EA7FjGE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA6F2EnC,EAAAiC,WAAWsD,UAAQ,G,IA7F9F3E,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA8FsCd,EAAAiC,WAAWuD,iBAAc,WAArDtF,EAAAA,EAAAA,IAA0H0D,EAAA,CA9FpIlE,IAAA,EA8FiEmE,MAAM,Q,CA9FvE,SAAApD,EAAAA,EAAAA,KA8F8E,iBAA+B,EA9F7GE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA8FiFnC,EAAAiC,WAAWuD,gBAAc,G,IA9F1G5E,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OA+FsCd,EAAAiC,WAAWwD,gBAAa,WAApDvF,EAAAA,EAAAA,IAAuH0D,EAAA,CA/FjIlE,IAAA,EA+FgEmE,MAAM,O,CA/FtE,SAAApD,EAAAA,EAAAA,KA+F4E,iBAA8B,EA/F1GE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IA+F+EnC,EAAAiC,WAAWwD,eAAa,G,IA/FvG7E,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAgGsCd,EAAAiC,WAAWyD,sBAAmB,WAA1DxF,EAAAA,EAAAA,IAAqI0D,EAAA,CAhG/IlE,IAAA,EAgGsEmE,MAAM,S,CAhG5E,SAAApD,EAAAA,EAAAA,KAgGoF,iBAAoC,EAhGxHE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAgGuFnC,EAAAiC,WAAWyD,qBAAmB,G,IAhGrH9E,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAiGsCd,EAAAiC,WAAW0D,gBAAa,WAApDzF,EAAAA,EAAAA,IAAwH0D,EAAA,CAjGlIlE,IAAA,EAiGgEmE,MAAM,Q,CAjGtE,SAAApD,EAAAA,EAAAA,KAiG6E,iBAA8B,EAjG3GE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAiGgFnC,EAAAiC,WAAW0D,eAAa,G,IAjGxG/E,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAkGsCd,EAAAiC,WAAW2D,oBAAiB,WAAxD1F,EAAAA,EAAAA,IAAiI0D,EAAA,CAlG3IlE,IAAA,GAkGoEmE,MAAM,S,CAlG1E,SAAApD,EAAAA,EAAAA,KAkGkF,iBAAkC,EAlGpHE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAkGqFnC,EAAAiC,WAAW2D,mBAAiB,G,IAlGjHhF,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAmGsCd,EAAAiC,WAAW4D,uBAAoB,WAA3D3F,EAAAA,EAAAA,IAAsI0D,EAAA,CAnGhJlE,IAAA,GAmGuEmE,MAAM,Q,CAnG7E,SAAApD,EAAAA,EAAAA,KAmGoF,iBAAqC,EAnGzHE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAmGuFnC,EAAAiC,WAAW4D,sBAAoB,G,IAnGtHjF,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAoGsCd,EAAAiC,WAAW6D,oBAAiB,WAAxD5F,EAAAA,EAAAA,IAA+H0D,EAAA,CApGzIlE,IAAA,GAoGoEmE,MAAM,O,CApG1E,SAAApD,EAAAA,EAAAA,KAoGgF,iBAAkC,EApGlHE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAoGmFnC,EAAAiC,WAAW6D,mBAAiB,G,IApG/GlF,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAqGsCd,EAAAiC,WAAW8D,mBAAgB,WAAvD7F,EAAAA,EAAAA,IAA8H0D,EAAA,CArGxIlE,IAAA,GAqGmEmE,MAAM,Q,CArGzE,SAAApD,EAAAA,EAAAA,KAqGgF,iBAAiC,EArGjHE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAqGmFnC,EAAAiC,WAAW8D,kBAAgB,G,IArG9GnF,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAsGsCd,EAAAiC,WAAW+D,kBAAe,WAAtD9F,EAAAA,EAAAA,IAA4H0D,EAAA,CAtGtIlE,IAAA,GAsGkEmE,MAAM,Q,CAtGxE,SAAApD,EAAAA,EAAAA,KAsG+E,iBAAgC,EAtG/GE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAsGkFnC,EAAAiC,WAAW+D,iBAAe,G,IAtG5GpF,EAAA,MAAAE,EAAAA,EAAAA,IAAA,OAuGsCd,EAAAiC,WAAWgE,mBAAgB,WAAvD/F,EAAAA,EAAAA,IAAgI0D,EAAA,CAvG1IlE,IAAA,GAuGmEmE,MAAM,U,CAvGzE,SAAApD,EAAAA,EAAAA,KAuGkF,iBAAiC,EAvGnHE,EAAAA,EAAAA,KAAAwB,EAAAA,EAAAA,IAuGqFnC,EAAAiC,WAAWgE,kBAAgB,G,IAvGhHrF,EAAA,MAAAE,EAAAA,EAAAA,IAAA,O,IAAAF,EAAA,M,IAAAA,EAAA,K,IAawBZ,EAAAqB,Y,4HAuGxB,SACE6E,KAAM,WACNC,KAAI,WACF,MAAO,CACLrC,OAAQsC,KAAKC,OAAOC,OAAOC,GAC3BlF,SAAS,EACTY,WAAY,CACV6B,OAAQ,GACRC,YAAa,GACbC,WAAY,GACZ5D,KAAM,GACN8B,OAAQ,GACRqC,KAAM,GACNN,WAAY,GACZC,WAAY,GAEZgB,WAAY,GACZf,YAAa,GACbgB,UAAW,GACXC,cAAe,GACfC,aAAc,GACdC,sBAAuB,GACvBC,SAAU,GACVC,eAAgB,GAChBC,cAAe,GACfC,oBAAqB,GACrBC,cAAe,GACfC,kBAAmB,GACnBY,cAAe,GACfX,qBAAsB,GACtBC,kBAAmB,GACnBC,iBAAkB,GAClBC,gBAAiB,GACjBC,iBAAkB,GAElBjB,kBAAmB,GACnByB,cAAe,GACfC,sBAAuB,GACvBzB,YAAa,GAEb0B,UAAW,GACXC,OAAQ,GACRxC,aAAc,GACdC,cAAe,IAEjBwC,KAAM,GACNzE,kBAAmB,KACnBc,gBAAgB,EAChBE,UAAW,CACTC,QAAS,GACTC,QAAS,IAGXwD,YAAa,KACbvE,gBAAiB,KACjBwE,gBAAiB,IACjB9G,eAAe,EACf0E,YAAa,EAEjB,EACAqC,QAAO,WAELZ,KAAKa,OAASC,IAAAA,OAAa,CACzBC,QAAS9H,EAAAA,GACT+H,QAAS,IACTC,QAAS,CACP,eAAgB,mBAChB,cAAiB,UAAF5I,OAAY6I,aAAaC,QAAQ,aAIpDnB,KAAKoB,iBACLpB,KAAKqB,kBAELC,OAAOC,iBAAiB,oBAAqBvB,KAAKwB,iBACpD,EACAC,cAAa,WAEXH,OAAOI,oBAAoB,oBAAqB1B,KAAKwB,iBACvD,EACAG,QAAS,CAEPP,eAAc,WACZ,IACE,IAAMQ,EAAUV,aAAaC,QAAQ,QACjCS,GACF5B,KAAKU,YAAcmB,KAAKC,MAAMF,GAC9BlJ,QAAQC,IAAI,QAASqH,KAAKU,cAE1BhI,QAAQqJ,KAAK,YAEjB,CAAE,MAAOnF,GACPlE,QAAQkE,MAAM,YAAaA,EAC7B,CACF,EAGAoF,gBAAe,WACb,IAAKhC,KAAKU,YAER,OADAhI,QAAQqJ,KAAK,eACN,EAIT,IAAME,EAAoC,UAA1BjC,KAAKU,YAAYwB,KAC3BC,EAAwC,cAA1BnC,KAAKU,YAAYwB,MAAkD,WAA1BlC,KAAKU,YAAYwB,KAGxEE,EAAYpC,KAAKnE,WAAW0E,YAAcP,KAAKU,YAAYP,IAC/CH,KAAKnE,WAAW0E,YAAcP,KAAKU,YAAY2B,SAC3DC,EAAatC,KAAKnE,WAAW2E,QACjBR,KAAKU,YAAYF,QACjBR,KAAKnE,WAAW2E,SAAWR,KAAKU,YAAYF,OAS9D,OANAR,KAAKnG,cAAgBoI,GAAYE,IAAgBC,GAAaE,GAE9D5J,QAAQC,IAAI,UAAWqH,KAAKnG,cAAe,CACzCoI,QAAAA,EAASE,YAAAA,EAAaC,UAAAA,EAAWE,WAAAA,IAG5BtC,KAAKnG,aACd,EAGA2H,iBAAgB,SAACe,GAEmD,IAAAC,EAAAC,EAAlE,GAAIF,GAASA,EAAMG,QAAUH,EAAMG,OAAOhF,SAAWsC,KAAKtC,SACxDhF,QAAQC,IAAI,eAAgB4J,EAAMG,SAI/BH,EAAMG,OAAOC,QACdJ,EAAMG,OAAOC,UAA2B,QAAtBH,EAAMxC,KAAKU,mBAAW,IAAA8B,OAAA,EAAhBA,EAAkBrC,KAC1CoC,EAAMG,OAAOC,UAA2B,QAAtBF,EAAMzC,KAAKU,mBAAW,IAAA+B,OAAA,EAAhBA,EAAkBJ,WAC1C,CAEArC,KAAKzB,cAGL,IAAMqE,GAA8C,IAA3BL,EAAMG,OAAOG,WACdvK,KAAKC,MAAQyH,KAAK7D,gBAAmB6D,KAAKW,gBAE9DiC,GACFlK,QAAQC,IAAI,aACZqH,KAAKqB,mBAEL3I,QAAQC,IAAI,gBAEhB,CAEJ,EAGAmK,kBAAiB,WAEf,IAAK9C,KAAK7D,gBAAiB,OAAO,EAGlC,IAAM5D,EAAMD,KAAKC,MACXwK,EAAuBxK,EAAMyH,KAAK7D,gBAExC,OAAO4G,EAAuB/C,KAAKW,eACrC,EAGAqC,kBAAiB,WACfhD,KAAK7D,gBAAkB7D,KAAKC,KAC9B,EAEA6B,mBAAkB,WAGhB8G,aAAa+B,QAAQ,gBAAiB,QAEtCjD,KAAK1E,QAAQ4H,KAAK,CAChBC,KAAM,SAAWnD,KAAKoD,SAASjD,GAAK,qBACpCkD,MAAO,CACLC,QAAStD,KAAKtC,OACd6F,KAAM,SAGZ,EAEA3H,cAAa,SAACE,GACZ,IAAM0H,EAAQ,CACZ,IAAO,OACP,IAAO,UACP,IAAO,UACP,IAAO,UACP,IAAO,UAET,OAAOA,EAAM1H,IAAW,MAC1B,EAGAoC,sBAAqB,SAACuF,GACpB,IAAMC,EAAa,CACjB,KAAQ,KACR,QAAW,KACX,KAAQ,KACR,OAAU,KACV,aAAgB,OAElB,OAAOA,EAAWD,IAAUA,CAC9B,EAEMpC,gBAAe,WAAG,IAAAsC,EAAA,YAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAN,EAAAA,EAAAA,KAAAO,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAKpB,OAJFX,EAAK1I,SAAU,EAAIoJ,EAAAE,EAAA,EAEjB7L,QAAQC,IAAI,kBAAmBgL,EAAKjG,QAEpC2G,EAAAC,EAAA,EACuBX,EAAK9C,OAAO2D,IAAI,qCAADnM,OAAsCsL,EAAKjG,SAAS,OAApFsG,EAAOK,EAAAI,EACPR,EAAgBD,EAASjE,KAE/BrH,QAAQC,IAAI,YAAasL,GAErBA,GAEFN,EAAKpF,cACLoF,EAAKxH,gBAAkB7D,KAAKC,MAG5BoL,EAAK9H,WAAa,CAChB6B,OAAQ,QAAFrF,OAAU4L,EAAc9D,IAC9BxC,YAAa,GAAFtF,OAAK4L,EAAcS,YAAc,KAAI,MAAArM,OAAK4L,EAAcU,QAAU,MAC7E/G,WAAYqG,EAAcW,UAAY,KACtC5K,KAAMiK,EAAcY,YAAc,KAClC/I,OAAQ6H,EAAKmB,cAAcb,EAAcnI,QAAU,SACnDqC,KAAM,IACNN,WAAY8F,EAAKoB,WAAWd,EAAce,WAC1ClH,WAAY6F,EAAKoB,WAAWd,EAAcgB,WAAahB,EAAce,WAGrEjH,YAAakG,EAAciB,OAAS,IACpCjH,cAAegG,EAAchG,eAAiB,IAC9CD,aAAciG,EAAcjG,cAAgB,KAG5CS,oBAAqBwF,EAAcxF,qBAAuB,IAC1DC,YAAauF,EAAcvF,aAAe,IAC1CE,kBAAmBqF,EAAcrF,mBAAqB,IACtDC,YAAaoF,EAAcpF,aAAe,IAG1C0B,UAAW0D,EAAckB,KAAOlB,EAAckB,KAAKhF,GAAK,KACxDK,OAAQyD,EAAckB,MAAQlB,EAAckB,KAAKC,KAAOnB,EAAckB,KAAKC,KAAKjF,GAAK,MAInF8D,EAAcoB,oBAChB1B,EAAK3H,kBAAoB2H,EAAKlL,YAAYwL,EAAcoB,oBACxD1B,EAAK3G,UAAUC,QAAUgH,EAAcoB,mBACvC1B,EAAK3G,UAAUE,QAAUyG,EAAK3H,mBAE9B2H,EAAK3H,kBAAoB,KAI3B2H,EAAK3B,kBAGL2B,EAAK2B,aAEL3B,EAAK4B,SAAS3I,MAAM,cACtByH,EAAAC,EAAA,eAAAD,EAAAE,EAAA,EAAAJ,EAAAE,EAAAI,EAEA/L,QAAQkE,MAAM,YAAWuH,GACzBR,EAAK4B,SAAS3I,MAAM,eAA8B,QAAdsH,EAAAC,EAAMH,gBAAQ,IAAAE,GAAM,QAANA,EAAdA,EAAgBnE,YAAI,IAAAmE,OAAA,EAApBA,EAAsBtH,QAASuH,EAAMqB,UAAS,OAE9D,OAF8DnB,EAAAE,EAAA,EAElFZ,EAAK1I,SAAU,EAAKoJ,EAAAoB,EAAA,iBAAApB,EAAAqB,EAAA,MAAA3B,EAAA,qBAhEAH,EAkExB,EAEAkB,cAAa,SAAChJ,GACZ,IAAM6J,EAAY,CAChB,MAAS,MACT,UAAa,MACb,QAAW,MACX,SAAY,MACZ,SAAY,OAEd,OAAOA,EAAU7J,IAAW,IAC9B,EAEMwJ,UAAS,WAAG,IAAAM,EAAA,YAAAhC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAA+B,IAAA,IAAA7B,EAAA8B,EAAA,OAAAjC,EAAAA,EAAAA,KAAAO,GAAA,SAAA2B,GAAA,eAAAA,EAAAzB,GAAA,cAAAyB,EAAAxB,EAAA,EAAAwB,EAAAzB,EAAA,EAES0B,EAAAA,WAAIxB,IAAI,yBAADnM,OAA0BuN,EAAKlI,OAAM,UAAQ,OAArEsG,EAAO+B,EAAAtB,EACbmB,EAAKnF,KAAOuD,EAASjE,KAAIgG,EAAAzB,EAAA,eAAAyB,EAAAxB,EAAA,EAAAuB,EAAAC,EAAAtB,EAEzB/L,QAAQkE,MAAM,YAAWkJ,GAAQ,cAAAC,EAAAL,EAAA,MAAAG,EAAA,iBALnBjC,EAOlB,EAEAmB,WAAU,SAACkB,GACT,IAAKA,EAAY,MAAO,IACxB,IAAMC,EAAO,IAAI5N,KAAK2N,GACtB,OAAOC,EAAKC,eAAe,QAC7B,EACAxJ,iBAAgB,WACdjE,QAAQkE,MAAM,YACdoD,KAAKlD,gBAAiB,EACtBpE,QAAQC,IAAI,eAAgBqH,KAAKhD,UACnC,EAEAvE,YAAAA,EAAAA,GAGAyC,YAAW,WACTxC,QAAQC,IAAI,UACZqH,KAAKqB,iBACP,EAGAhF,WAAU,SAACjE,GACT,IAAKA,EAAW,MAAO,GAEvB,IAAM8N,EAAO,IAAI5N,KAAKF,GACtB,OAAO8N,EAAKE,mBAAmB,QAAS,CACtCC,KAAM,UACNC,OAAQ,UACRC,OAAQ,WAEZ,I,eCjbJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/utils/apiHelpers.js", "webpack://medical-annotation-frontend/./src/utils/imageHelper.js", "webpack://medical-annotation-frontend/./src/views/CaseView.vue", "webpack://medical-annotation-frontend/./src/views/CaseView.vue?c190"], "sourcesContent": ["/**\r\n * API 工具函数\r\n * 提供API请求相关的通用功能\r\n */\r\nimport { storageUtils } from './storeHelpers';\r\n\r\n/**\r\n * 标准化API路径，确保路径格式一致\r\n * @param {string} url 原始URL\r\n * @returns {string} 标准化后的URL\r\n */\r\nexport function normalizePath(url) {\r\n  if (!url) return '';\r\n  \r\n  // 确保URL以/开头\r\n  if (!url.startsWith('/')) {\r\n    url = '/' + url;\r\n  }\r\n  \r\n  // 处理双斜杠的情况\r\n  url = url.replace(/\\/+/g, '/');\r\n  \r\n  return url;\r\n}\r\n\r\n/**\r\n * 获取用户ID参数，优先使用数字ID\r\n * @param {string|number} userId 用户ID\r\n * @returns {string|number} 处理后的用户ID\r\n */\r\nexport function getUserIdParam(userId) {\r\n  return userId || '';\r\n}\r\n\r\n/**\r\n * 确保对象中的指定字段为数字类型\r\n * @param {object} obj 要处理的对象\r\n * @param {array} fields 要确保为数字的字段列表\r\n * @returns {object} 处理后的对象\r\n */\r\nexport function ensureNumericFields(obj, fields) {\r\n  if (!obj || typeof obj !== 'object') return obj;\r\n  \r\n  const result = { ...obj };\r\n  \r\n  fields.forEach(field => {\r\n    if (result[field] !== undefined) {\r\n      const parsedValue = parseInt(result[field], 10);\r\n      if (!isNaN(parsedValue)) {\r\n        result[field] = parsedValue;\r\n      }\r\n    }\r\n  });\r\n  \r\n  return result;\r\n}\r\n\r\n/**\r\n * 获取认证头信息\r\n * @returns {object} 包含认证信息的头部对象\r\n */\r\nexport function getAuthHeaders() {\r\n  const headers = {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json'\r\n  };\r\n  \r\n  // 从本地存储获取用户信息\r\n  const user = storageUtils.getFromStorage('user');\r\n  if (user) {\r\n    // 使用用户ID生成简化的认证头\r\n    const userId = user.id || user.customId || '';\r\n    headers['Authorization'] = `Bearer user_${userId}`;\r\n    headers['X-User-ID'] = userId;\r\n  }\r\n  \r\n  return headers;\r\n}\r\n\r\n/**\r\n * 处理API错误\r\n * @param {Error} error 错误对象\r\n * @returns {object} 格式化的错误信息\r\n */\r\nexport function handleApiError(error) {\r\n  const errorInfo = {\r\n    message: error.message || '未知错误',\r\n    status: error.response ? error.response.status : null,\r\n    data: error.response ? error.response.data : null\r\n  };\r\n  \r\n  // 记录错误信息\r\n  console.error('[API错误]', errorInfo);\r\n  \r\n  return errorInfo;\r\n}\r\n\r\n/**\r\n * 向URL添加时间戳，防止缓存\r\n * @param {string} url 原始URL\r\n * @returns {string} 添加时间戳后的URL\r\n */\r\nexport function addTimestamp(url) {\r\n  const timestamp = `_t=${Date.now()}`;\r\n  if (url.includes('?')) {\r\n    return `${url}&${timestamp}`;\r\n  } else {\r\n    return `${url}?${timestamp}`;\r\n  }\r\n}\r\n\r\nexport default {\r\n  normalizePath,\r\n  getUserIdParam,\r\n  ensureNumericFields,\r\n  getAuthHeaders,\r\n  handleApiError,\r\n  addTimestamp\r\n}; ", "/**\n * 图像路径处理工具\n * 用于统一处理图像URL，解决路径不一致、中文编码等问题\n */\n\nimport { API_BASE_URL, API_CONTEXT_PATH, isLocalhost } from '../config/api.config';\nimport { storageUtils } from './storeHelpers';\nimport { addTimestamp } from './apiHelpers';\n\n// 图像大小限制\nconst MAX_IMAGE_WIDTH = 700;\nconst MAX_IMAGE_HEIGHT = 700;\n\n/**\n * 获取图像URL，添加防缓存参数并处理不同的路径格式\n * @param {string} url 原始图像URL\n * @returns {string} 处理后的URL\n */\nexport function getImageUrl(url) {\n  if (!url) return '';\n  \n  console.log('[ImageHelper] 处理图像路径:', url);\n  \n  // 处理文件系统路径\n  if (/^[a-zA-Z]:\\\\/.test(url)) {\n    console.log('[ImageHelper] 检测到文件系统路径');\n    const encodedPath = encodeURIComponent(url);\n    \n    // 使用当前域名而非硬编码地址\n    const baseUrl = isLocalhost ? `${API_BASE_URL}` : '';\n    return addTimestamp(`${baseUrl}/medical/image/system-path?path=${encodedPath}`);\n  }\n  \n  // 如果URL已经是完整的HTTP(S)地址，直接使用\n  if (url.startsWith('http://') || url.startsWith('https://')) {\n    return addTimestamp(url);\n  }\n  \n  // 如果URL是data:开头的数据URI，直接返回\n  if (url.startsWith('data:')) {\n    return url;\n  }\n  \n  // 处理相对路径\n  let finalUrl = url;\n  \n  if (url.startsWith('/medical')) {\n    console.log('[ImageHelper] 检测到相对路径，添加后端服务器地址');\n    // 根据环境使用适当的基础URL\n    finalUrl = isLocalhost ? `${API_BASE_URL}${url}` : url;\n  } else if (url.startsWith('/')) {\n    console.log('[ImageHelper] 检测到其他相对路径，添加后端服务器地址和上下文路径');\n    // 根据环境使用适当的基础URL\n    finalUrl = isLocalhost ? `${API_BASE_URL}${API_CONTEXT_PATH}${url}` : `${API_CONTEXT_PATH}${url}`;\n  }\n  \n  console.log('[ImageHelper] 处理后的URL:', finalUrl);\n  return addTimestamp(finalUrl);\n}\n\n/**\n * 从图像路径中提取文件名\n * @param {string} path - 图像路径\n * @returns {string} 文件名\n */\nexport function getImageFilename(path) {\n  if (!path) return '';\n  \n  const parts = path.split('/');\n  return parts[parts.length - 1];\n}\n\n/**\n * 判断是否是图像文件\n * @param {string} filename - 文件名\n * @returns {boolean} 是否是图像文件\n */\nexport function isImageFile(filename) {\n  if (!filename) return false;\n  \n  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\n  const lowerFilename = filename.toLowerCase();\n  \n  return imageExtensions.some(ext => lowerFilename.endsWith(ext));\n}\n\n/**\n * 调整图像大小，确保不超过最大宽高限制\n * @param {HTMLImageElement|File} imageSource - 图像元素或文件对象\n * @param {Function} callback - 回调函数，接收处理后的图像数据(DataURL或Blob)和尺寸信息\n * @param {Object} options - 选项\n * @param {number} options.maxWidth - 最大宽度，默认700px\n * @param {number} options.maxHeight - 最大高度，默认700px\n * @param {string} options.outputFormat - 输出格式，'dataUrl'或'blob'，默认'dataUrl'\n * @param {string} options.imageType - 图像类型，默认'image/jpeg'\n * @param {number} options.quality - 压缩质量，0-1之间，默认0.9\n */\nexport function resizeImage(imageSource, callback, options = {}) {\n  const settings = {\n    maxWidth: options.maxWidth || MAX_IMAGE_WIDTH,\n    maxHeight: options.maxHeight || MAX_IMAGE_HEIGHT,\n    outputFormat: options.outputFormat || 'dataUrl',\n    imageType: options.imageType || 'image/jpeg',\n    quality: options.quality || 0.9\n  };\n  \n  // 处理File对象\n  if (imageSource instanceof File) {\n    const reader = new FileReader();\n    reader.onload = function(e) {\n      const img = new Image();\n      img.onload = function() {\n        processImageResize(img, settings, callback);\n      };\n      img.src = e.target.result;\n    };\n    reader.readAsDataURL(imageSource);\n    return;\n  }\n  \n  // 处理Image对象\n  if (imageSource instanceof HTMLImageElement) {\n    if (imageSource.complete) {\n      processImageResize(imageSource, settings, callback);\n    } else {\n      imageSource.onload = function() {\n        processImageResize(imageSource, settings, callback);\n      };\n    }\n    return;\n  }\n  \n  // 处理其他情况（字符串URL或DataURL）\n  if (typeof imageSource === 'string') {\n    const img = new Image();\n    img.onload = function() {\n      processImageResize(img, settings, callback);\n    };\n    img.src = imageSource;\n    return;\n  }\n  \n  // 不支持的类型\n  console.error('不支持的图像源类型:', imageSource);\n  callback(null, { error: '不支持的图像源类型' });\n}\n\n/**\n * 处理图像大小调整的核心逻辑\n * @private\n */\nfunction processImageResize(img, settings, callback) {\n  // 获取原始尺寸\n  const originalWidth = img.naturalWidth || img.width;\n  const originalHeight = img.naturalHeight || img.height;\n  \n  // 计算调整后的尺寸，保持宽高比\n  let newWidth = originalWidth;\n  let newHeight = originalHeight;\n  \n  // 如果图像超出最大限制，按比例缩小\n  if (originalWidth > settings.maxWidth || originalHeight > settings.maxHeight) {\n    const widthRatio = settings.maxWidth / originalWidth;\n    const heightRatio = settings.maxHeight / originalHeight;\n    const ratio = Math.min(widthRatio, heightRatio);\n    \n    newWidth = Math.floor(originalWidth * ratio);\n    newHeight = Math.floor(originalHeight * ratio);\n  }\n  \n  // 创建Canvas绘制调整后的图像\n  const canvas = document.createElement('canvas');\n  const ctx = canvas.getContext('2d');\n  canvas.width = newWidth;\n  canvas.height = newHeight;\n  \n  // 绘制图像\n  ctx.drawImage(img, 0, 0, newWidth, newHeight);\n  \n  // 输出数据\n  const sizeInfo = {\n    originalWidth,\n    originalHeight,\n    newWidth,\n    newHeight,\n    resized: (originalWidth !== newWidth || originalHeight !== newHeight)\n  };\n  \n  // 根据需要的输出格式返回结果\n  if (settings.outputFormat === 'blob') {\n    canvas.toBlob(\n      (blob) => callback(blob, sizeInfo),\n      settings.imageType,\n      settings.quality\n    );\n  } else {\n    // 默认返回DataURL\n    const dataUrl = canvas.toDataURL(settings.imageType, settings.quality);\n    callback(dataUrl, sizeInfo);\n  }\n}\n\n/**\n * 转换相对坐标到绝对坐标\n * @param {Object} annotation 标注数据\n * @param {number} imageWidth 图像宽度\n * @param {number} imageHeight 图像高度\n * @returns {Object} 转换后的标注数据\n */\nexport function convertToAbsoluteCoordinates(annotation, imageWidth, imageHeight) {\n  // 判断是否是相对坐标（0-1范围内）\n  const isRelativeCoordinates = \n    annotation.x <= 1 && annotation.x >= 0 && \n    annotation.y <= 1 && annotation.y >= 0 && \n    annotation.width <= 1 && annotation.width >= 0 && \n    annotation.height <= 1 && annotation.height >= 0;\n  \n  if (!isRelativeCoordinates) {\n    return annotation; // 已经是绝对坐标，直接返回\n  }\n  \n  // 计算绝对坐标\n  const x = Math.round(annotation.x * imageWidth);\n  const y = Math.round(annotation.y * imageHeight);\n  const width = Math.round(annotation.width * imageWidth);\n  const height = Math.round(annotation.height * imageHeight);\n  \n  return {\n    ...annotation,\n    x,\n    y,\n    width,\n    height,\n    // 保存原始的相对坐标\n    normalizedX: annotation.x,\n    normalizedY: annotation.y,\n    normalizedWidth: annotation.width,\n    normalizedHeight: annotation.height\n  };\n}\n\n/**\n * 创建用户ID一致性处理工具\n */\nexport const UserIdConsistencyFixer = {\n  /**\n   * 获取一致的用户ID，优先使用customId\n   * @param {Object} user 用户对象\n   * @returns {string|number} 用户ID\n   */\n  getConsistentUserId(user) {\n    if (!user) return null;\n    return user.customId || user.id || null;\n  },\n  \n  /**\n   * 获取用户对象，确保包含一致的ID\n   * @returns {Object} 用户对象\n   */\n  getCurrentUser() {\n    const user = storageUtils.getFromStorage('user', {});\n      return {\n        ...user,\n        consistentId: this.getConsistentUserId(user)\n      };\n  }\n};\n\nexport default {\n  getImageUrl,\n  getImageFilename,\n  isImageFile,\n  resizeImage,\n  MAX_IMAGE_WIDTH,\n  MAX_IMAGE_HEIGHT\n}; ", "<template>\n  <div class=\"case-view-container\">\n    <div class=\"page-header\">\n      <h2>病例详情</h2>\n      <div class=\"header-actions\">\n        <el-button v-if=\"hasPermission\" type=\"primary\" size=\"small\" @click=\"redirectToAnnotate\">编辑</el-button>\n        <el-tooltip content=\"刷新病例数据\" placement=\"top\">\n          <el-button icon=\"el-icon-refresh\" size=\"small\" circle :loading=\"loading\" @click=\"refreshData\"></el-button>\n        </el-tooltip>\n        <el-button link @click=\"$router.back()\">返回</el-button>\n      </div>\n    </div>\n\n    <el-card v-loading=\"loading\">\n      <!-- 标注后的图像 - 基本信息上方 -->\n      <div v-if=\"annotatedImageUrl\" class=\"annotated-image-section\">\n        <div class=\"section-header\">\n          <h3>病变标注图像</h3>\n          <small v-if=\"lastRefreshTime\" class=\"refresh-info\">最后更新: {{ formatTime(lastRefreshTime) }}</small>\n        </div>\n        <div class=\"annotated-image-container\">\n          <el-image :src=\"annotatedImageUrl\" fit=\"contain\" class=\"annotated-image\" :preview-src-list=\"[annotatedImageUrl]\" @error=\"handleImageError\">\n            <template #error>\n              <div class=\"image-error\">\n                <i class=\"el-icon-picture-outline\"></i>\n                <p>无法加载标注图像</p>\n                <div v-if=\"imageLoadError\" class=\"debug-info\">\n                  <h4>图片调试信息</h4>\n                  <p><b>数据库图片路径:</b> {{ debugInfo.rawPath }}</p>\n                  <p><b>完整访问URL:</b> {{ debugInfo.fullUrl }}</p>\n                  <small>请检查此路径是否正确，以及后端是否提供了静态文件访问</small>\n                </div>\n              </div>\n            </template>\n          </el-image>\n        </div>\n      </div>\n      <div v-else class=\"no-image-message\">\n        <i class=\"el-icon-picture-outline\"></i>\n        <p>此病例暂无标注图像</p>\n      </div>\n      \n      <template #header>\n        <div class=\"card-header\">\n          <span>基本信息</span>\n          <el-tag :type=\"getStatusType(caseDetail.status)\">{{ caseDetail.status }}</el-tag>\n        </div>\n      </template>\n      \n      <!-- 先显示病例基本信息 -->\n      <div class=\"case-info\">\n        <el-descriptions :column=\"4\" border>\n          <el-descriptions-item label=\"病例编号\">{{ caseDetail.caseId || '无' }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.patientInfo\" label=\"患者信息\">{{ caseDetail.patientInfo }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.department\" label=\"部位\">{{ caseDetail.department }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.type\" label=\"血管瘤类型\">{{ caseDetail.type }}</el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\">{{ caseDetail.createTime || '无' }}</el-descriptions-item>\n          <el-descriptions-item label=\"更新时间\">{{ caseDetail.updateTime || '无' }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.lesionColor\" label=\"病变颜色\">{{ caseDetail.lesionColor }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.detectedType\" label=\"检测到的血管瘤类型\">{{ caseDetail.detectedType }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.vesselTexture\" label=\"血管质地\">{{ getVesselTextureLabel(caseDetail.vesselTexture) }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.note\" label=\"备注\" :span=\"4\">\n            {{ caseDetail.note }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n      \n      <!-- 详细信息 -->\n      <div class=\"detailed-case-info\">\n        <div class=\"section-header\">\n          <h3>详细信息</h3>\n          <small v-if=\"dataVersion > 0\" class=\"version-info\">版本: {{ dataVersion }}</small>\n        </div>\n        \n        <!-- 血管瘤特有字段 -->\n        <div v-if=\"caseDetail.treatmentSuggestion || caseDetail.precautions\" class=\"hemangioma-specific-section\">\n          <h4>血管瘤诊断建议</h4>\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item v-if=\"caseDetail.diagnosticSummary\" label=\"诊断摘要\">{{ caseDetail.diagnosticSummary }}</el-descriptions-item>\n            <el-descriptions-item v-if=\"caseDetail.treatmentSuggestion\" label=\"治疗建议\">{{ caseDetail.treatmentSuggestion }}</el-descriptions-item>\n            <el-descriptions-item v-if=\"caseDetail.precautions\" label=\"注意事项\">{{ caseDetail.precautions }}</el-descriptions-item>\n            <el-descriptions-item v-if=\"caseDetail.reviewNotes\" label=\"审核备注\">{{ caseDetail.reviewNotes }}</el-descriptions-item>\n          </el-descriptions>\n        </div>\n        \n        <!-- 其他详细信息 -->\n        <el-descriptions :column=\"3\" border class=\"mt-4\">\n          <!-- 以下是从image_metadata中获取的其他已填写的字段 -->\n          <el-descriptions-item v-if=\"caseDetail.lesionSize\" label=\"病变大小\">{{ caseDetail.lesionSize }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.bloodFlow\" label=\"血流信号\">{{ caseDetail.bloodFlow }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.borderClarity\" label=\"边界清晰度\">{{ caseDetail.borderClarity }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.diseaseStage\" label=\"病程阶段\">{{ caseDetail.diseaseStage }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.morphologicalFeatures\" label=\"形态特征\">{{ caseDetail.morphologicalFeatures }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.symptoms\" label=\"症状表现\">{{ caseDetail.symptoms }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.symptomDetails\" label=\"症状详情\">{{ caseDetail.symptomDetails }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.complications\" label=\"并发症\">{{ caseDetail.complications }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.complicationDetails\" label=\"并发症详情\">{{ caseDetail.complicationDetails }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.diagnosisCode\" label=\"诊断编码\">{{ caseDetail.diagnosisCode }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.treatmentPriority\" label=\"治疗优先级\">{{ caseDetail.treatmentPriority }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.recommendedTreatment\" label=\"推荐治疗\">{{ caseDetail.recommendedTreatment }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.contraindications\" label=\"禁忌症\">{{ caseDetail.contraindications }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.followUpSchedule\" label=\"随访周期\">{{ caseDetail.followUpSchedule }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.prognosisRating\" label=\"预后评级\">{{ caseDetail.prognosisRating }}</el-descriptions-item>\n          <el-descriptions-item v-if=\"caseDetail.patientEducation\" label=\"患者教育重点\">{{ caseDetail.patientEducation }}</el-descriptions-item>\n        </el-descriptions>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport api from '@/utils/api'\nimport { getImageUrl } from '@/utils/imageHelper'\nimport { API_BASE_URL } from '../config/api.config'\nimport axios from 'axios'\n\nexport default {\n  name: 'CaseView',\n  data() {\n    return {\n      caseId: this.$route.params.id,\n      loading: true,\n      caseDetail: {\n        caseId: '',\n        patientInfo: '',\n        department: '',\n        type: '',\n        status: '',\n        note: '',\n        createTime: '',\n        updateTime: '',\n        // 新增字段\n        lesionSize: '',\n        lesionColor: '',\n        bloodFlow: '',\n        borderClarity: '',\n        diseaseStage: '',\n        morphologicalFeatures: '',\n        symptoms: '',\n        symptomDetails: '',\n        complications: '',\n        complicationDetails: '',\n        diagnosisCode: '',\n        treatmentPriority: '',\n        treatmentPlan: '',\n        recommendedTreatment: '',\n        contraindications: '',\n        followUpSchedule: '',\n        prognosisRating: '',\n        patientEducation: '',\n        // 血管瘤特有字段\n        diagnosticSummary: '',\n        dietaryAdvice: '',\n        emergencyInstructions: '',\n        reviewNotes: '',\n        // 添加权限检查相关字段\n        createdBy: '',\n        teamId: '',\n        detectedType: '', // 新增：检测到的血管瘤类型\n        vesselTexture: '' // 新增：血管质地\n      },\n      tags: [],\n      annotatedImageUrl: null,\n      imageLoadError: false,\n      debugInfo: {\n        rawPath: '',\n        fullUrl: ''\n      },\n      // 新增数据刷新控制\n      currentUser: null,\n      lastRefreshTime: null,\n      refreshInterval: 60000, // 刷新间隔，默认1分钟\n      hasPermission: false,   // 用户权限标志\n      dataVersion: 0          // 数据版本号，用于判断是否需要刷新\n    }\n  },\n  created() {\n    // 将axios实例挂载到Vue实例上，方便调用\n    this.$axios = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${localStorage.getItem('token')}`\n      }\n    });\n\n    this.getCurrentUser();\n    this.fetchCaseDetail();\n    // 监听数据更新事件\n    window.addEventListener('case-data-updated', this.handleDataUpdate);\n  },\n  beforeUnmount() {\n    // 清理事件监听器\n    window.removeEventListener('case-data-updated', this.handleDataUpdate);\n  },\n  methods: {\n    // 获取当前登录用户信息\n    getCurrentUser() {\n      try {\n        const userStr = localStorage.getItem('user');\n        if (userStr) {\n          this.currentUser = JSON.parse(userStr);\n          console.log('当前用户:', this.currentUser);\n        } else {\n          console.warn('未找到登录用户信息');\n        }\n      } catch (error) {\n        console.error('获取用户信息失败:', error);\n      }\n    },\n\n    // 检查用户权限\n    checkPermission() {\n      if (!this.currentUser) {\n        console.warn('未登录，无法验证权限');\n        return false;\n      }\n      \n      // 检查是否为系统管理员或标注医生\n      const isAdmin = this.currentUser.role === 'ADMIN';\n      const isAnnotator = this.currentUser.role === 'ANNOTATOR' || this.currentUser.role === 'DOCTOR';\n      \n      // 检查是否是病例创建者或所属团队成员\n      const isCreator = this.caseDetail.createdBy === this.currentUser.id || \n                        this.caseDetail.createdBy === this.currentUser.customId;\n      const isSameTeam = this.caseDetail.teamId && \n                        this.currentUser.teamId && \n                        this.caseDetail.teamId === this.currentUser.teamId;\n      \n      // 设置权限标志\n      this.hasPermission = isAdmin || (isAnnotator && (isCreator || isSameTeam));\n      \n      console.log('权限检查结果:', this.hasPermission, {\n        isAdmin, isAnnotator, isCreator, isSameTeam\n      });\n      \n      return this.hasPermission;\n    },\n    \n    // 处理数据更新事件\n    handleDataUpdate(event) {\n      // 检查是否是当前病例的更新\n      if (event && event.detail && event.detail.caseId === this.caseId) {\n        console.log('接收到病例数据更新事件:', event.detail);\n        \n        // 检查是否是当前用户的更新\n        if (\n          !event.detail.userId || // 如果没有指定用户ID，认为是系统级更新\n          event.detail.userId === this.currentUser?.id || \n          event.detail.userId === this.currentUser?.customId\n        ) {\n          // 增加数据版本号，触发刷新\n          this.dataVersion++;\n          \n          // 判断是否需要立即刷新\n          const shouldRefreshNow = event.detail.immediate === true || \n                                 (Date.now() - this.lastRefreshTime) > this.refreshInterval;\n          \n          if (shouldRefreshNow) {\n            console.log('刷新病例数据和图片');\n            this.fetchCaseDetail();\n          } else {\n            console.log('延迟刷新，等待下次刷新周期');\n          }\n        }\n      }\n    },\n\n    // 数据刷新控制\n    shouldRefreshData() {\n      // 如果没有上次刷新时间，说明是首次加载\n      if (!this.lastRefreshTime) return true;\n      \n      // 检查是否超过刷新间隔\n      const now = Date.now();\n      const timeSinceLastRefresh = now - this.lastRefreshTime;\n      \n      return timeSinceLastRefresh > this.refreshInterval;\n    },\n    \n    // 更新刷新时间\n    updateRefreshTime() {\n      this.lastRefreshTime = Date.now();\n    },\n    \n    redirectToAnnotate() {\n      // 跳转到标注页面，而不是编辑页面\n      // 设置标志表明这是编辑操作，以便表单组件知道是更新现有数据而不是创建新数据\n      localStorage.setItem('isEditingCase', 'true');\n      \n      this.$router.push({\n        path: '/case/' + this.caseData.id + '/annotate-and-form',\n        query: { \n          imageId: this.caseId,\n          edit: 'true' \n        }\n      });\n    },\n    \n    getStatusType(status) {\n      const types = {\n        '待标注': 'info',\n        '已标注': 'success',\n        '审核中': 'warning',\n        '已通过': 'success',\n        '已驳回': 'danger'\n      }\n      return types[status] || 'info'\n    },\n    \n    // 获取血管质地标签\n    getVesselTextureLabel(value) {\n      const textureMap = {\n        'soft': '质软',\n        'elastic': '质韧',\n        'hard': '质硬',\n        'cystic': '囊性',\n        'compressible': '可压缩'\n      };\n      return textureMap[value] || value;\n    },\n    \n    async fetchCaseDetail() {\n      this.loading = true;\n      try {\n        console.log('开始获取血管瘤诊断数据，ID:', this.caseId);\n        \n        // 直接使用axios，并构造完整的URL，避免api.js中的潜在问题\n        const response = await this.$axios.get(`/medical/api/hemangioma-diagnoses/${this.caseId}`);\n        const diagnosisData = response.data;\n        \n        console.log('获取到的诊断数据:', diagnosisData);\n        \n        if (diagnosisData) {\n          // 更新数据版本号和刷新时间\n          this.dataVersion++;\n          this.lastRefreshTime = Date.now();\n          \n          // 准备基本信息\n          this.caseDetail = {\n            caseId: `CASE-${diagnosisData.id}`,\n            patientInfo: `${diagnosisData.patientAge || '未知'}岁 ${diagnosisData.gender || '未知'}`,\n            department: diagnosisData.bodyPart || '未知',\n            type: diagnosisData.originType || '未知',\n            status: this.getStatusText(diagnosisData.status || 'DRAFT'),\n            note: '无',\n            createTime: this.formatDate(diagnosisData.createdAt),\n            updateTime: this.formatDate(diagnosisData.updatedAt || diagnosisData.createdAt),\n            \n            // 详细信息字段\n            lesionColor: diagnosisData.color || '无',\n            vesselTexture: diagnosisData.vesselTexture || '无',\n            detectedType: diagnosisData.detectedType || '未知', // 设置检测到的类型\n            \n            // LLM生成的建议\n            treatmentSuggestion: diagnosisData.treatmentSuggestion || '无',\n            precautions: diagnosisData.precautions || '无',\n            diagnosticSummary: diagnosisData.diagnosticSummary || '无',\n            reviewNotes: diagnosisData.reviewNotes || '无', // 新增：审核备注\n\n            // 权限相关字段\n            createdBy: diagnosisData.user ? diagnosisData.user.id : null,\n            teamId: diagnosisData.user && diagnosisData.user.team ? diagnosisData.user.team.id : null\n          };\n\n          // 设置标注后的图像URL\n          if (diagnosisData.processedImagePath) {\n            this.annotatedImageUrl = this.getImageUrl(diagnosisData.processedImagePath);\n            this.debugInfo.rawPath = diagnosisData.processedImagePath;\n            this.debugInfo.fullUrl = this.annotatedImageUrl;\n          } else {\n            this.annotatedImageUrl = null;\n          }\n          \n          // 检查用户权限\n          this.checkPermission();\n          \n          // 获取相关的标签信息\n          this.fetchTags();\n        } else {\n          this.$message.error('未找到有效的病例数据');\n        }\n      } catch (error) {\n        console.error('获取病例详情失败:', error);\n        this.$message.error('获取病例详情失败: ' + (error.response?.data?.error || error.message));\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    getStatusText(status) {\n      const statusMap = {\n        'DRAFT': '待标注',\n        'SUBMITTED': '已标注',\n        'PENDING': '审核中', \n        'APPROVED': '已通过',\n        'REJECTED': '已驳回'\n      }\n      return statusMap[status] || '未知';\n    },\n    \n    async fetchTags() {\n      try {\n        const response = await api.get(`/hemangioma-diagnoses/${this.caseId}/tags`);\n        this.tags = response.data;\n      } catch (error) {\n        console.error('获取标签数据失败:', error);\n      }\n    },\n    \n    formatDate(dateString) {\n      if (!dateString) return '无';\n      const date = new Date(dateString);\n      return date.toLocaleString('zh-CN');\n    },\n    handleImageError() {\n      console.error('标注图片加载失败');\n      this.imageLoadError = true;\n      console.log('图片加载失败的调试信息:', this.debugInfo);\n    },\n    \n    getImageUrl,\n    \n    // 新增: 手动刷新数据的方法\n    refreshData() {\n      console.log('手动刷新数据');\n      this.fetchCaseDetail();\n    },\n\n    // 格式化时间为友好格式\n    formatTime(timestamp) {\n      if (!timestamp) return '';\n      \n      const date = new Date(timestamp);\n      return date.toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.case-view-container {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  font-size: 20px;\n  color: #333;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.annotated-image-section {\n  margin-bottom: 20px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.annotated-image-section h3 {\n  margin-bottom: 15px;\n  margin-top: 0;\n  font-size: 18px;\n  color: #333;\n}\n\n.annotated-image-container {\n  width: 100%;\n  max-width: 300px;\n  height: auto;\n  overflow: visible;\n  border-radius: 4px;\n  border: 1px solid #eee;\n  margin: 0 auto;\n  box-shadow: 0 2px 4px rgba(0,0,0,.05);\n  padding-bottom: 10px;\n}\n\n.annotated-image {\n  width: 100%;\n  max-width: 300px;\n  height: auto;\n  object-fit: contain;\n  display: block;\n  margin: 0 auto;\n}\n\n.image-error, .no-image-message {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 150px;\n  background-color: #f5f7fa;\n  color: #909399;\n  margin-bottom: 20px;\n  border-radius: 4px;\n  border: 1px dashed #d9d9d9;\n}\n\n.detailed-case-info {\n  margin-top: 20px;\n  border-top: 1px solid #ebeef5;\n  padding-top: 20px;\n}\n\n.detailed-case-info h3 {\n  margin-bottom: 15px;\n  font-size: 18px;\n  color: #333;\n}\n\n.case-info {\n  margin-bottom: 20px;\n}\n\n.debug-info {\n  margin-top: 15px;\n  padding: 10px;\n  background-color: #f8f8f8;\n  border: 1px dashed #ccc;\n  border-radius: 4px;\n  text-align: left;\n  font-size: 12px;\n  color: #666;\n  max-width: 100%;\n}\n\n.debug-info h4 {\n  margin: 0 0 10px 0;\n  font-size: 14px;\n  color: #333;\n}\n\n.debug-info h5 {\n  margin: 15px 0 5px 0;\n  font-size: 13px;\n  color: #444;\n}\n\n.debug-info p {\n  margin: 5px 0;\n  word-break: break-all;\n}\n\n.path-options {\n  margin-top: 10px;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.path-option {\n  display: flex;\n  flex-direction: column;\n}\n\n.path-option small {\n  margin-top: 2px;\n  color: #888;\n}\n\n.actions {\n  margin-top: 15px;\n  display: flex;\n  gap: 10px;\n}\n\n.full-paths {\n  margin-top: 15px;\n  padding: 10px;\n  background-color: #f0f0f0;\n  border-radius: 4px;\n}\n\n.full-path {\n  margin: 5px 0;\n  word-break: break-all;\n}\n\n.hint {\n  margin-top: 10px;\n  font-style: italic;\n  color: #909399;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-header h3 {\n  margin: 0;\n  font-size: 18px;\n  color: #333;\n}\n\n.refresh-info, .version-info {\n  color: #909399;\n  font-size: 12px;\n}\n\n.version-info {\n  background-color: #f0f9eb;\n  padding: 2px 6px;\n  border-radius: 10px;\n  border: 1px solid #e1f3d8;\n}\n\n.hemangioma-specific-section {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #f8f8f8;\n  border-radius: 4px;\n  border: 1px solid #ebeef5;\n}\n\n.hemangioma-specific-section h4 {\n  margin-bottom: 15px;\n  font-size: 18px;\n  color: #333;\n}\n\n.mt-4 {\n  margin-top: 20px;\n}\n</style> ", "import { render } from \"./CaseView.vue?vue&type=template&id=b7093354&scoped=true\"\nimport script from \"./CaseView.vue?vue&type=script&lang=js\"\nexport * from \"./CaseView.vue?vue&type=script&lang=js\"\n\nimport \"./CaseView.vue?vue&type=style&index=0&id=b7093354&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-b7093354\"]])\n\nexport default __exports__"], "names": ["addTimestamp", "url", "timestamp", "concat", "Date", "now", "includes", "getImageUrl", "console", "log", "test", "encodedPath", "encodeURIComponent", "baseUrl", "isLocalhost", "API_BASE_URL", "startsWith", "finalUrl", "API_CONTEXT_PATH", "class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "$data", "hasPermission", "_createBlock", "_component_el_button", "type", "size", "onClick", "$options", "redirectToAnnotate", "_withCtx", "_cache", "_createTextVNode", "_", "__", "_createCommentVNode", "_createVNode", "_component_el_tooltip", "content", "placement", "icon", "circle", "loading", "refreshData", "link", "$event", "_ctx", "$router", "back", "_component_el_card", "header", "_hoisted_11", "_component_el_tag", "getStatusType", "caseDetail", "status", "_toDisplayString", "annotatedImageUrl", "_hoisted_4", "_hoisted_5", "lastRefreshTime", "_hoisted_6", "formatTime", "_hoisted_7", "_component_el_image", "src", "fit", "onError", "handleImageError", "error", "_hoisted_8", "imageLoadError", "_hoisted_9", "debugInfo", "rawPath", "fullUrl", "_hoisted_10", "_hoisted_12", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "label", "caseId", "patientInfo", "department", "createTime", "updateTime", "lesionColor", "detectedType", "vesselTexture", "getVesselTextureLabel", "note", "span", "_hoisted_13", "_hoisted_14", "dataVersion", "_hoisted_15", "treatmentSuggestion", "precautions", "_hoisted_16", "diagnosticSummary", "reviewNotes", "lesionSize", "bloodFlow", "borderClarity", "diseaseStage", "morphologicalFeatures", "symptoms", "symptomDetails", "complications", "complicationDetails", "diagnosisCode", "treatmentPriority", "recommendedTreatment", "contraindications", "followUpSchedule", "prognosisRating", "patientEducation", "name", "data", "this", "$route", "params", "id", "treatmentPlan", "dietaryAdvice", "emergencyInstructions", "created<PERSON>y", "teamId", "tags", "currentUser", "refreshInterval", "created", "$axios", "axios", "baseURL", "timeout", "headers", "localStorage", "getItem", "getCurrentUser", "fetchCaseDetail", "window", "addEventListener", "handleDataUpdate", "beforeUnmount", "removeEventListener", "methods", "userStr", "JSON", "parse", "warn", "checkPermission", "isAdmin", "role", "isAnnotator", "isCreator", "customId", "isSameTeam", "event", "_this$currentUser", "_this$currentUser2", "detail", "userId", "shouldRefreshNow", "immediate", "shouldRefreshData", "timeSinceLastRefresh", "updateRefreshTime", "setItem", "push", "path", "caseData", "query", "imageId", "edit", "types", "value", "textureMap", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "response", "diagnosisData", "_error$response", "_t", "w", "_context", "n", "p", "get", "v", "patientAge", "gender", "bodyPart", "originType", "getStatusText", "formatDate", "createdAt", "updatedAt", "color", "user", "team", "processedImagePath", "fetchTags", "$message", "message", "f", "a", "statusMap", "_this2", "_callee2", "_t2", "_context2", "api", "dateString", "date", "toLocaleString", "toLocaleTimeString", "hour", "minute", "second", "__exports__", "render"], "sourceRoot": ""}