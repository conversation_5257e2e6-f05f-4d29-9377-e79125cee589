package com.medical.annotation.filter;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.CharArrayWriter;
import java.nio.charset.StandardCharsets;

/**
 * 用户ID一致性修复过滤器
 * 在HTML响应中注入脚本，确保前端localStorage中的用户ID一致性
 */
@Component
@Order(1)
public class UserIdConsistencyFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hain filterChain)
            throws ServletException, IOException {
        
        // 只处理HTML响应
        String contentType = response.getContentType();
        if (contentType != null && contentType.contains("text/html")) {
            CharResponseWrapper responseWrapper = new CharResponseWrapper(response);
            
            // 继续过滤链，使用包装的响应对象
            filterChain.doFilter(request, responseWrapper);
            
            // 获取原始HTML内容
            String content = responseWrapper.toString();
            
            // 注入用户ID一致性修复脚本
            if (content.contains("</body>")) {
                content = content.replace("</body>", generateFixerScript() + "</body>");
                System.out.println("已注入用户ID一致性修复脚本");
            }
            
            // 设置响应内容类型和编码
            response.setContentType("text/html;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            
            // 写入修改后的内容
            response.getWriter().write(content);
        } else {
            // 对于非HTML响应，直接继续过滤链
            filterChain.doFilter(request, response);
        }
    }
    
    /**
     * 生成用户ID一致性修复脚本
     * @return 修复脚本的HTML字符串
     */
    private String generateFixerScript() {
        return "<script>\n" +
               "  // 确保用户ID一致性的脚本\n" +
               "  (function() {\n" +
               "    try {\n" +
               "      var userId = localStorage.getItem('userId');\n" +
               "      if (userId) {\n" +
               "        // 确保所有相关的用户标识保持一致\n" +
               "        localStorage.setItem('currentUserId', userId);\n" +
               "        console.log('用户ID一致性已修复');\n" +
               "      }\n" +
               "    } catch (e) {\n" +
               "      console.error('用户ID一致性修复失败:', e);\n" +
               "    }\n" +
               "  })();\n" +
               "</script>";
    }
    
    /**
     * 用于包装HttpServletResponse以捕获响应内容
     */
    private static class CharResponseWrapper extends HttpServletResponseWrapper {
        private final CharArrayWriter output;
        
        public CharResponseWrapper(HttpServletResponse response) {
            super(response);
            output = new CharArrayWriter();
        }
        
        @Override
        public PrintWriter getWriter() {
            return new PrintWriter(output);
        }
        
        @Override
        public String toString() {
            return output.toString();
        }
    }
} 