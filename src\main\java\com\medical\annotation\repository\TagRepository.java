package com.medical.annotation.repository;

import com.medical.annotation.model.Tag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface TagRepository extends JpaRepository<Tag, Long> {
    
    // 根据诊断ID查找标签
    List<Tag> findByHemangiomaDiagnosisId(Integer hemangiomaDiagnosisId);
    
    // 兼容老版本 - 直接查询hemangioma_id字段
    @Query(value = "SELECT * FROM tags WHERE hemangioma_id = :hemangioma_id", nativeQuery = true)
    List<Tag> findByHemangioma_id(@Param("hemangioma_id") Long hemangioma_id);
    
    // 兼容老版本 - 直接查询hemangioma_id字段(Integer类型)
    @Query(value = "SELECT * FROM tags WHERE hemangioma_id = :hemangioma_id", nativeQuery = true)
    List<Tag> findByHemangioma_id(@Param("hemangioma_id") Integer hemangioma_id);
    
    // 添加根据metadata_id查询的方法
    @Query(value = "SELECT * FROM tags WHERE metadata_id = :metadata_id", nativeQuery = true)
    List<Tag> findByMetadataId(@Param("metadata_id") Integer metadata_id);
    
    // 综合查询 - 同时查询hemangioma_diagnosis_id和hemangioma_id字段
    @Query(value = "SELECT * FROM tags WHERE hemangioma_diagnosis_id = :id OR hemangioma_id = :id", nativeQuery = true)
    List<Tag> findByAnyDiagnosisId(@Param("id") Integer id);
    
    // 根据诊断ID删除所有标签
    @Transactional
    void deleteByHemangiomaDiagnosisId(Integer hemangiomaDiagnosisId);
    
    // 根据hemangioma_id删除所有标签
    @Transactional
    @Query(value = "DELETE FROM tags WHERE hemangioma_id = :hemangioma_id", nativeQuery = true)
    void deleteByHemangioma_id(@Param("hemangioma_id") Integer hemangioma_id);
} 