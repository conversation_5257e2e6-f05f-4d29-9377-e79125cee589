"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[191],{37191:(t,e,a)=>{a.r(e),a.d(e,{default:()=>S});var r=a(61431),n={class:"tag-test"},l={class:"input-area"},o={class:"form-group"},i={class:"form-group"},s={class:"form-group"},u={class:"form-group"},c={class:"form-group"},p={class:"form-group"},d={class:"form-group"},h={class:"action-area"},m={class:"result-area"},f={key:0},g={key:1,class:"error"};function k(t,e,a,k,y,b){return(0,r.uX)(),(0,r.CE)("div",n,[e[19]||(e[19]=(0,r.Lk)("h2",null,"标注测试工具",-1)),e[20]||(e[20]=(0,r.Lk)("p",null,"这个组件用于测试直接向后端发送标注请求",-1)),(0,r.Lk)("div",l,[(0,r.Lk)("div",o,[e[10]||(e[10]=(0,r.Lk)("label",null,"图像ID (metadata_id)",-1)),(0,r.bo)((0,r.Lk)("input",{type:"number","onUpdate:modelValue":e[0]||(e[0]=function(t){return y.metadata_id=t}),placeholder:"输入图像ID"},null,512),[[r.Jo,y.metadata_id]])]),(0,r.Lk)("div",i,[e[11]||(e[11]=(0,r.Lk)("label",null,"标签名称 (tag)",-1)),(0,r.bo)((0,r.Lk)("input",{type:"text","onUpdate:modelValue":e[1]||(e[1]=function(t){return y.tag=t}),placeholder:"输入标签名称"},null,512),[[r.Jo,y.tag]])]),(0,r.Lk)("div",s,[e[12]||(e[12]=(0,r.Lk)("label",null,"创建者ID (created_by)",-1)),(0,r.bo)((0,r.Lk)("input",{type:"number","onUpdate:modelValue":e[2]||(e[2]=function(t){return y.created_by=t}),placeholder:"输入创建者ID"},null,512),[[r.Jo,y.created_by]])]),(0,r.Lk)("div",u,[e[13]||(e[13]=(0,r.Lk)("label",null,"X坐标 (x)",-1)),(0,r.bo)((0,r.Lk)("input",{type:"number","onUpdate:modelValue":e[3]||(e[3]=function(t){return y.x=t}),step:"0.01",placeholder:"输入归一化X坐标"},null,512),[[r.Jo,y.x]])]),(0,r.Lk)("div",c,[e[14]||(e[14]=(0,r.Lk)("label",null,"Y坐标 (y)",-1)),(0,r.bo)((0,r.Lk)("input",{type:"number","onUpdate:modelValue":e[4]||(e[4]=function(t){return y.y=t}),step:"0.01",placeholder:"输入归一化Y坐标"},null,512),[[r.Jo,y.y]])]),(0,r.Lk)("div",p,[e[15]||(e[15]=(0,r.Lk)("label",null,"宽度 (width)",-1)),(0,r.bo)((0,r.Lk)("input",{type:"number","onUpdate:modelValue":e[5]||(e[5]=function(t){return y.width=t}),step:"0.01",placeholder:"输入归一化宽度"},null,512),[[r.Jo,y.width]])]),(0,r.Lk)("div",d,[e[16]||(e[16]=(0,r.Lk)("label",null,"高度 (height)",-1)),(0,r.bo)((0,r.Lk)("input",{type:"number","onUpdate:modelValue":e[6]||(e[6]=function(t){return y.height=t}),step:"0.01",placeholder:"输入归一化高度"},null,512),[[r.Jo,y.height]])])]),(0,r.Lk)("div",h,[(0,r.Lk)("button",{onClick:e[7]||(e[7]=function(){return b.testFetch&&b.testFetch.apply(b,arguments)})},"使用Fetch API测试"),(0,r.Lk)("button",{onClick:e[8]||(e[8]=function(){return b.testAxios&&b.testAxios.apply(b,arguments)})},"使用Axios测试"),(0,r.Lk)("button",{onClick:e[9]||(e[9]=function(){return b.testXHR&&b.testXHR.apply(b,arguments)})},"使用XHR测试")]),(0,r.Lk)("div",m,[e[18]||(e[18]=(0,r.Lk)("h3",null,"测试结果",-1)),y.result?((0,r.uX)(),(0,r.CE)("div",f,[(0,r.Lk)("pre",null,(0,r.v_)(y.result),1)])):(0,r.Q3)("",!0),y.error?((0,r.uX)(),(0,r.CE)("div",g,[e[17]||(e[17]=(0,r.Lk)("h4",null,"错误信息",-1)),(0,r.Lk)("pre",null,(0,r.v_)(y.error),1)])):(0,r.Q3)("",!0)])])}var y=a(24059),b=a(698),L=(a(16280),a(76918),a(28706),a(60739),a(23288),a(33110),a(79432),a(26099),a(78459),a(58940),a(38781),a(72505)),v=a.n(L);const _={name:"TagTest",data:function(){return{metadata_id:1,tag:"血管瘤",created_by:1,x:.5,y:.5,width:.2,height:.2,result:null,error:null}},methods:{testFetch:function(){var t=this;return(0,b.A)((0,y.A)().m((function e(){var a,r,n,l,o,i,s;return(0,y.A)().w((function(e){while(1)switch(e.n){case 0:if(t.result=null,t.error=null,e.p=1,a=JSON.parse(localStorage.getItem("user")||"{}"),r={metadata_id:parseInt(t.metadata_id),tag:t.tag,created_by:parseInt(t.created_by),x:parseFloat(t.x),y:parseFloat(t.y),width:parseFloat(t.width),height:parseFloat(t.height)},n={"Content-Type":"application/json",Accept:"application/json"},(a.id||a.customId)&&(l=a.customId||a.id,n["Authorization"]="Bearer user_".concat(l)),console.log("发送请求到: /medical/api/api",r),confirm("即将发送请求到 /medical/api/api\n数据: ".concat(JSON.stringify(r,null,2),"\n确认发送?"))){e.n=2;break}return t.result="用户取消了请求",e.a(2);case 2:return e.n=3,fetch("/medical/api/api",{method:"POST",headers:n,body:JSON.stringify(r),credentials:"include"});case 3:if(o=e.v,o.ok){e.n=4;break}throw new Error("HTTP error! Status: ".concat(o.status));case 4:return e.n=5,o.json();case 5:i=e.v,t.result=JSON.stringify(i,null,2),alert("请求成功，查看结果!"),e.n=7;break;case 6:e.p=6,s=e.v,console.error("测试失败:",s),t.error=s.toString(),alert("请求失败: "+s.toString());case 7:return e.a(2)}}),e,null,[[1,6]])})))()},testAxios:function(){var t=this;return(0,b.A)((0,y.A)().m((function e(){var a,r,n;return(0,y.A)().w((function(e){while(1)switch(e.n){case 0:if(t.result=null,t.error=null,e.p=1,a={metadata_id:parseInt(t.metadata_id),tag:t.tag,created_by:parseInt(t.created_by),x:parseFloat(t.x),y:parseFloat(t.y),width:parseFloat(t.width),height:parseFloat(t.height)},console.log("使用Axios发送请求到: /medical/api/api",a),confirm("即将使用Axios发送请求到 /medical/api/api\n数据: ".concat(JSON.stringify(a,null,2),"\n确认发送?"))){e.n=2;break}return t.result="用户取消了请求",e.a(2);case 2:return e.n=3,v().post("/medical/api/api",a,{headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});case 3:r=e.v,t.result=JSON.stringify(r.data,null,2),alert("请求成功，查看结果!"),e.n=5;break;case 4:e.p=4,n=e.v,console.error("Axios测试失败:",n),t.error=n.toString(),n.response&&(t.error+="\n\nResponse data: "+JSON.stringify(n.response.data,null,2)),alert("请求失败: "+n.toString());case 5:return e.a(2)}}),e,null,[[1,4]])})))()},testXHR:function(){var t=this;this.result=null,this.error=null;try{var e={metadata_id:parseInt(this.metadata_id),tag:this.tag,created_by:parseInt(this.created_by),x:parseFloat(this.x),y:parseFloat(this.y),width:parseFloat(this.width),height:parseFloat(this.height)};if(console.log("使用XHR发送请求到: /medical/api/api",e),!confirm("即将使用XHR发送请求到 /medical/api/api\n数据: ".concat(JSON.stringify(e,null,2),"\n确认发送?")))return void(this.result="用户取消了请求");var a=new XMLHttpRequest;a.open("POST","/medical/api/api",!0),a.setRequestHeader("Content-Type","application/json"),a.setRequestHeader("Accept","application/json"),a.withCredentials=!0,a.onload=function(){a.status>=200&&a.status<300?(t.result=JSON.stringify(JSON.parse(a.responseText),null,2),alert("请求成功，查看结果!")):(t.error="XHR Error: ".concat(a.status," ").concat(a.statusText,"\n").concat(a.responseText),alert("请求失败: "+a.status+" "+a.statusText))},a.onerror=function(){t.error="XHR请求失败",alert("XHR请求失败")},a.send(JSON.stringify(e))}catch(r){console.error("XHR测试失败:",r),this.error=r.toString(),alert("请求失败: "+r.toString())}}}};var w=a(66262);const x=(0,w.A)(_,[["render",k],["__scopeId","data-v-08dc852c"]]),S=x}}]);
//# sourceMappingURL=191.7d6c1f07.js.map