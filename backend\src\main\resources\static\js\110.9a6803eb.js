"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[110],{86110:(e,t,a)=>{a.r(t),a.d(t,{default:()=>I});a(60739),a(33110);var n=a(61431),s={class:"debug-container"},o={class:"card"},l={class:"form-group"},r={class:"form-group"},u={class:"form-group"},i={class:"form-row"},c={class:"form-group"},d={class:"form-group"},p={class:"form-row"},m={class:"form-group"},g={class:"form-group"},b={key:1,class:"debug-section"},k={key:2,class:"debug-section"},h={class:"card"};function L(e,t,a,L,f,v){return(0,n.uX)(),(0,n.CE)("div",s,[t[21]||(t[21]=(0,n.Lk)("h1",null,"标注调试工具",-1)),(0,n.Lk)("div",o,[t[18]||(t[18]=(0,n.Lk)("h2",null,"标注数据测试",-1)),(0,n.Lk)("div",l,[t[9]||(t[9]=(0,n.Lk)("label",null,"图像ID (metadata_id):",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[0]||(t[0]=function(e){return f.metadata_id=e}),type:"number"},null,512),[[n.Jo,f.metadata_id]])]),(0,n.Lk)("div",r,[t[10]||(t[10]=(0,n.Lk)("label",null,"标签类型 (tag):",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[1]||(t[1]=function(e){return f.tag=e}),type:"text"},null,512),[[n.Jo,f.tag]])]),(0,n.Lk)("div",u,[t[11]||(t[11]=(0,n.Lk)("label",null,"用户ID (created_by):",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[2]||(t[2]=function(e){return f.created_by=e}),type:"number"},null,512),[[n.Jo,f.created_by]])]),(0,n.Lk)("div",i,[(0,n.Lk)("div",c,[t[12]||(t[12]=(0,n.Lk)("label",null,"X坐标 (归一化0-1):",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[3]||(t[3]=function(e){return f.x=e}),type:"number",step:"0.01",min:"0",max:"1"},null,512),[[n.Jo,f.x]])]),(0,n.Lk)("div",d,[t[13]||(t[13]=(0,n.Lk)("label",null,"Y坐标 (归一化0-1):",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[4]||(t[4]=function(e){return f.y=e}),type:"number",step:"0.01",min:"0",max:"1"},null,512),[[n.Jo,f.y]])])]),(0,n.Lk)("div",p,[(0,n.Lk)("div",m,[t[14]||(t[14]=(0,n.Lk)("label",null,"宽度 (归一化0-1):",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[5]||(t[5]=function(e){return f.width=e}),type:"number",step:"0.01",min:"0.01",max:"1"},null,512),[[n.Jo,f.width]])]),(0,n.Lk)("div",g,[t[15]||(t[15]=(0,n.Lk)("label",null,"高度 (归一化0-1):",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[6]||(t[6]=function(e){return f.height=e}),type:"number",step:"0.01",min:"0.01",max:"1"},null,512),[[n.Jo,f.height]])])]),(0,n.Lk)("button",{onClick:t[7]||(t[7]=function(){return v.testSaveAnnotation&&v.testSaveAnnotation.apply(v,arguments)}),class:"btn primary"},"测试保存标注"),f.status?((0,n.uX)(),(0,n.CE)("div",{key:0,class:(0,n.C4)(["status",f.status.success?"success":"error"])},(0,n.v_)(f.status.message),3)):(0,n.Q3)("",!0),f.requestData?((0,n.uX)(),(0,n.CE)("div",b,[t[16]||(t[16]=(0,n.Lk)("h3",null,"请求数据:",-1)),(0,n.Lk)("pre",null,(0,n.v_)(JSON.stringify(f.requestData,null,2)),1)])):(0,n.Q3)("",!0),f.responseData?((0,n.uX)(),(0,n.CE)("div",k,[t[17]||(t[17]=(0,n.Lk)("h3",null,"响应数据:",-1)),(0,n.Lk)("pre",null,(0,n.v_)(JSON.stringify(f.responseData,null,2)),1)])):(0,n.Q3)("",!0)]),(0,n.Lk)("div",h,[t[19]||(t[19]=(0,n.Lk)("h2",null,"网络请求监控",-1)),(0,n.Lk)("button",{onClick:t[8]||(t[8]=function(){return v.enableMonitoring&&v.enableMonitoring.apply(v,arguments)}),class:(0,n.C4)(["btn",{primary:!f.isMonitoringEnabled}])},(0,n.v_)(f.isMonitoringEnabled?"已启用监控":"启用请求监控"),3),t[20]||(t[20]=(0,n.Lk)("p",{class:"hint"},"启用后，所有标注相关的网络请求将在控制台中详细显示",-1))])])}var f=a(24059),v=a(698),y=(a(28706),a(79432),a(26099),a(78459),a(58940),a(27495),a(47764),a(5746),a(62953),a(48408),a(14603),a(47566),a(98721),a(36149)),_=a(69018);const w={name:"DebugView",data:function(){var e=JSON.parse(localStorage.getItem("user")||"{}"),t=e.id||e.customId||1;return{metadata_id:1,tag:"血管瘤",created_by:t,x:.5,y:.5,width:.3,height:.3,isMonitoringEnabled:!1,requestData:null,responseData:null,status:null}},mounted:function(){this.isMonitoringEnabled=!0===window.ANNOTATION_DEBUG_ENABLED;var e=new URLSearchParams(window.location.search),t=e.get("imageId");t&&(this.metadata_id=parseInt(t,10))},methods:{testSaveAnnotation:function(){var e=this;return(0,v.A)((0,f.A)().m((function t(){var a,n,s,o;return(0,f.A)().w((function(t){while(1)switch(t.n){case 0:return e.requestData=null,e.responseData=null,e.status=null,t.p=1,a={metadata_id:parseInt(e.metadata_id,10),tag:e.tag,x:parseFloat(e.x),y:parseFloat(e.y),width:parseFloat(e.width),height:parseFloat(e.height),created_by:parseInt(e.created_by,10)},e.requestData=a,console.log("请求标注数据:",a),(0,_.h3)(a),e.status={success:!1,message:"正在发送请求..."},t.n=2,y["default"].tags.create(a);case 2:n=t.v,e.responseData=n.data,e.status={success:!0,message:"保存成功! 标注ID: ".concat(n.data.id)},console.log("标注保存成功:",n.data),t.n=4;break;case 3:t.p=3,o=t.v,console.error("保存标注失败:",o),s="保存标注失败",o.response?(s+=": ".concat(o.response.status," ").concat(o.response.statusText),e.responseData=o.response.data):o.message&&(s+=": ".concat(o.message)),e.status={success:!1,message:s};case 4:return t.a(2)}}),t,null,[[1,3]])})))()},enableMonitoring:function(){if(!this.isMonitoringEnabled)try{(0,_.AW)(),this.isMonitoringEnabled=!0,this.status={success:!0,message:"请求监控已启用，请查看浏览器控制台"},localStorage.setItem("enableDebug","true")}catch(e){console.error("启用监控失败:",e),this.status={success:!1,message:"启用监控失败: ".concat(e.message)}}}}};var D=a(66262);const E=(0,D.A)(w,[["render",L],["__scopeId","data-v-a1e97730"]]),I=E}}]);
//# sourceMappingURL=110.9a6803eb.js.map