# 背景样式指南

## 血管瘤AI诊断背景

当前实现采用了蓝紫色渐变背景，设计如下：

```css
background: linear-gradient(135deg, #0d47a1, #4a148c, #6a1b9a);
```

这种设计简洁大方，且不依赖外部图片资源，确保系统在任何环境下都能正常运行。

## 添加自定义背景图片

如果您希望使用背景图片替代渐变色，可以按照以下步骤操作：

1. 将图片文件保存为：
   ```
   hemangioma-bg.jpg
   ```

2. 将此图片放置在以下位置之一：
   ```
   frontend/public/backgrounds/hemangioma-bg.jpg
   ```

3. 然后修改CSS文件 `frontend/src/assets/css/background.css`：
   ```css
   .login-background, .register-background {
       background: url('/backgrounds/hemangioma-bg.jpg') no-repeat center center;
       background-size: cover;
       min-height: 100vh;
       width: 100%;
   }
   ```

## 图片要求

- 分辨率：建议至少1920×1080像素
- 格式：JPG或PNG
- 风格：与系统设计风格一致的蓝紫色调
- 内容：建议使用与血管瘤相关的医学可视化图像 