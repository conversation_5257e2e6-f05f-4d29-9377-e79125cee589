"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[498],{59498:(e,a,t)=>{t.r(a),t.d(a,{default:()=>O});t(52675),t(89463),t(62010);var r=t(20641),n=t(90033),l={class:"teams-container"},o={class:"page-header"},u={class:"action-buttons"},i={class:"card-header"},s={class:"team-info"},c={class:"team-description"},m={class:"team-stats"},d={class:"stat-item"},p={class:"stat-value"},f={class:"stat-item"},v={class:"stat-value"},k={class:"stat-item"},b={class:"stat-value"},g={key:1,class:"no-team-message"},h={key:2,class:"team-members-section"},T={style:{display:"flex","flex-direction":"column",gap:"4px",width:"100%"}},_={style:{display:"flex","justify-content":"space-between","align-items":"center"}},w={style:{"font-weight":"bold"}},x={style:{color:"#8492a6","font-size":"13px"}},F={style:{"font-size":"12px",color:"#606266","white-space":"normal","line-height":"1.3"}},y={class:"dialog-footer"},C={class:"dialog-footer"};function L(e,a,t,L,R,A){var V=(0,r.g2)("el-button"),D=(0,r.g2)("el-card"),j=(0,r.g2)("el-empty"),W=(0,r.g2)("el-table-column"),E=(0,r.g2)("el-tag"),J=(0,r.g2)("el-table"),K=(0,r.g2)("el-option"),I=(0,r.g2)("el-select"),N=(0,r.g2)("el-form-item"),O=(0,r.g2)("el-input"),S=(0,r.g2)("el-form"),M=(0,r.g2)("el-dialog");return(0,r.uX)(),(0,r.CE)("div",l,[(0,r.Lk)("div",o,[a[13]||(a[13]=(0,r.Lk)("h2",null,"我的团队",-1)),(0,r.Lk)("div",u,[L.isAdmin||L.isReviewer?((0,r.uX)(),(0,r.Wv)(V,{key:0,type:"success",onClick:a[0]||(a[0]=function(e){return L.showCreateTeamDialog=!0})},{default:(0,r.k6)((function(){return a[11]||(a[11]=[(0,r.eW)("创建团队")])})),_:1,__:[11]})):(0,r.Q3)("",!0),(0,r.bF)(V,{type:"primary",onClick:a[1]||(a[1]=function(e){return L.showJoinTeamDialog=!0})},{default:(0,r.k6)((function(){return a[12]||(a[12]=[(0,r.eW)("加入团队")])})),_:1,__:[12]})])]),L.currentTeam?((0,r.uX)(),(0,r.Wv)(D,{key:0,class:"current-team-card"},{header:(0,r.k6)((function(){return[(0,r.Lk)("div",i,[a[15]||(a[15]=(0,r.Lk)("span",null,"当前团队",-1)),(0,r.bF)(V,{type:"danger",text:"",onClick:L.handleLeaveTeam},{default:(0,r.k6)((function(){return a[14]||(a[14]=[(0,r.eW)("退出团队")])})),_:1,__:[14]},8,["onClick"])])]})),default:(0,r.k6)((function(){return[(0,r.Lk)("div",s,[(0,r.Lk)("h3",null,(0,n.v_)(L.currentTeam.name),1),(0,r.Lk)("p",c,(0,n.v_)(L.currentTeam.description||"暂无团队介绍"),1),(0,r.Lk)("div",m,[(0,r.Lk)("div",d,[a[16]||(a[16]=(0,r.Lk)("div",{class:"stat-label"},"成员数",-1)),(0,r.Lk)("div",p,(0,n.v_)(L.currentTeam.memberCount||0),1)]),(0,r.Lk)("div",f,[a[17]||(a[17]=(0,r.Lk)("div",{class:"stat-label"},"病例数",-1)),(0,r.Lk)("div",v,(0,n.v_)(L.currentTeam.caseCount||0),1)]),(0,r.Lk)("div",k,[a[18]||(a[18]=(0,r.Lk)("div",{class:"stat-label"},"创建时间",-1)),(0,r.Lk)("div",b,(0,n.v_)(L.formatDate(L.currentTeam.createdAt)),1)])])])]})),_:1})):((0,r.uX)(),(0,r.CE)("div",g,[(0,r.bF)(j,{description:"您当前不属于任何团队"}),(0,r.bF)(V,{type:"primary",onClick:a[2]||(a[2]=function(e){return L.showJoinTeamDialog=!0})},{default:(0,r.k6)((function(){return a[19]||(a[19]=[(0,r.eW)("加入团队")])})),_:1,__:[19]})])),L.currentTeam&&L.teamMembers.length>0?((0,r.uX)(),(0,r.CE)("div",h,[a[20]||(a[20]=(0,r.Lk)("h3",null,"团队成员",-1)),(0,r.bF)(J,{data:L.teamMembers,stripe:"",style:{width:"100%"}},{default:(0,r.k6)((function(){return[(0,r.bF)(W,{prop:"name",label:"姓名"}),(0,r.bF)(W,{prop:"role",label:"角色"},{default:(0,r.k6)((function(e){return[(0,r.bF)(E,{type:L.getRoleType(e.row.role)},{default:(0,r.k6)((function(){return[(0,r.eW)((0,n.v_)(L.getRoleName(e.row.role)),1)]})),_:2},1032,["type"])]})),_:1}),(0,r.bF)(W,{prop:"department",label:"部门"}),(0,r.bF)(W,{prop:"hospital",label:"医院"}),(0,r.bF)(W,{prop:"joinDate",label:"加入时间"})]})),_:1},8,["data"])])):(0,r.Q3)("",!0),(0,r.bF)(M,{title:"加入团队",modelValue:L.showJoinTeamDialog,"onUpdate:modelValue":a[6]||(a[6]=function(e){return L.showJoinTeamDialog=e}),width:"500px"},{footer:(0,r.k6)((function(){return[(0,r.Lk)("span",y,[(0,r.bF)(V,{onClick:a[5]||(a[5]=function(e){return L.showJoinTeamDialog=!1})},{default:(0,r.k6)((function(){return a[21]||(a[21]=[(0,r.eW)("取消")])})),_:1,__:[21]}),(0,r.bF)(V,{type:"primary",onClick:L.handleJoinTeam,loading:L.joinTeamLoading},{default:(0,r.k6)((function(){return a[22]||(a[22]=[(0,r.eW)(" 提交申请 ")])})),_:1,__:[22]},8,["onClick","loading"])])]})),default:(0,r.k6)((function(){return[(0,r.bF)(S,{model:L.joinTeamForm,"label-width":"80px",rules:L.joinTeamRules,ref:"joinTeamFormRef"},{default:(0,r.k6)((function(){return[(0,r.bF)(N,{label:"团队代码",prop:"teamCode"},{default:(0,r.k6)((function(){return[(0,r.bF)(I,{modelValue:L.joinTeamForm.teamCode,"onUpdate:modelValue":a[3]||(a[3]=function(e){return L.joinTeamForm.teamCode=e}),filterable:"",placeholder:"请选择要加入的团队",style:{width:"100%"}},{default:(0,r.k6)((function(){return[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(L.teamOptions,(function(e){return(0,r.uX)(),(0,r.Wv)(K,{key:e.code,label:e.name,value:e.code},{default:(0,r.k6)((function(){return[(0,r.Lk)("div",T,[(0,r.Lk)("div",_,[(0,r.Lk)("span",w,(0,n.v_)(e.name),1),(0,r.Lk)("span",x,"ID: "+(0,n.v_)(e.code),1)]),(0,r.Lk)("div",F,(0,n.v_)(e.description||"暂无描述"),1)])]})),_:2},1032,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),(0,r.bF)(N,{label:"加入理由",prop:"reason"},{default:(0,r.k6)((function(){return[(0,r.bF)(O,{modelValue:L.joinTeamForm.reason,"onUpdate:modelValue":a[4]||(a[4]=function(e){return L.joinTeamForm.reason=e}),type:"textarea",placeholder:"请简要说明加入原因",rows:3},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"]),(0,r.bF)(M,{title:"创建团队",modelValue:L.showCreateTeamDialog,"onUpdate:modelValue":a[10]||(a[10]=function(e){return L.showCreateTeamDialog=e}),width:"500px"},{footer:(0,r.k6)((function(){return[(0,r.Lk)("span",C,[(0,r.bF)(V,{onClick:a[9]||(a[9]=function(e){return L.showCreateTeamDialog=!1})},{default:(0,r.k6)((function(){return a[23]||(a[23]=[(0,r.eW)("取消")])})),_:1,__:[23]}),(0,r.bF)(V,{type:"primary",onClick:L.handleCreateTeam,loading:L.createTeamLoading},{default:(0,r.k6)((function(){return a[24]||(a[24]=[(0,r.eW)(" 创建团队 ")])})),_:1,__:[24]},8,["onClick","loading"])])]})),default:(0,r.k6)((function(){return[(0,r.bF)(S,{model:L.createTeamForm,"label-width":"80px",rules:L.createTeamRules,ref:"createTeamFormRef"},{default:(0,r.k6)((function(){return[(0,r.bF)(N,{label:"团队名称",prop:"name"},{default:(0,r.k6)((function(){return[(0,r.bF)(O,{modelValue:L.createTeamForm.name,"onUpdate:modelValue":a[7]||(a[7]=function(e){return L.createTeamForm.name=e}),placeholder:"请输入团队名称"},null,8,["modelValue"])]})),_:1}),(0,r.bF)(N,{label:"团队描述",prop:"description"},{default:(0,r.k6)((function(){return[(0,r.bF)(O,{modelValue:L.createTeamForm.description,"onUpdate:modelValue":a[8]||(a[8]=function(e){return L.createTeamForm.description=e}),type:"textarea",placeholder:"请输入团队描述",rows:4},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"])])}var R=t(41034),A=t(14048),V=t(30388),D=(t(76918),t(62062),t(23288),t(18111),t(61701),t(79432),t(26099),t(38781),t(50953)),j=t(20163),W=t(77918),E=(t(72505),t(653)),J=t(40834);const K={name:"TeamsView",setup:function(){var e=(0,J.Pj)(),a=(0,r.EW)((function(){return e.getters.isAdmin})),t=(0,r.EW)((function(){return e.getters.isReviewer})),n=(0,D.KR)(null),l=(0,D.KR)([]),o=(0,D.KR)(!1),u=(0,D.Kh)({teamCode:"",reason:""}),i={teamCode:[{required:!0,message:"请选择要加入的团队",trigger:"change"}],reason:[{required:!0,message:"请输入加入理由",trigger:"blur"},{min:5,max:200,message:"请输入5-200个字符",trigger:"blur"}]},s=(0,D.KR)(null),c=(0,D.KR)(!1),m=(0,D.KR)([]),d=(0,D.KR)(!1),p=(0,D.KR)(!1),f=(0,D.Kh)({name:"",description:""}),v={name:[{required:!0,message:"请输入团队名称",trigger:"blur"},{min:2,max:50,message:"团队名称长度在2-50个字符之间",trigger:"blur"}],description:[{required:!0,message:"请输入团队描述",trigger:"blur"},{min:5,max:200,message:"请输入5-200个字符",trigger:"blur"}]},k=(0,D.KR)(null),b=(0,D.KR)(!1),g=function(){var e=(0,V.A)((0,A.A)().mark((function e(){var a,t;return(0,A.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,a=localStorage.getItem("user"),a){e.next=6;break}return n.value=null,e.abrupt("return");case 6:t=JSON.parse(a),t&&t.team?(n.value=t.team,h()):n.value=null,e.next=14;break;case 11:e.prev=11,e.t0=e["catch"](0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(){return e.apply(this,arguments)}}(),h=function(){var e=(0,V.A)((0,A.A)().mark((function e(){var a;return(0,A.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n.value&&n.value.id){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,E["default"].teams.getTeamMembers(n.value.id);case 5:a=e.sent,l.value=a.data.map((function(e){return(0,R.A)((0,R.A)({},e),{},{joinDate:F(e.joinDate)})})),e.next=13;break;case 9:e.prev=9,e.t0=e["catch"](2),j.nk.error("获取团队成员失败");case 13:case"end":return e.stop()}}),e,null,[[2,9]])})));return function(){return e.apply(this,arguments)}}(),T=function(){var e=(0,V.A)((0,A.A)().mark((function e(){var a,t,r,n;return(0,A.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(s.value){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,s.value.validate();case 5:if(c.value=!0,a=u.teamCode,e.prev=8,t=localStorage.getItem("user"),t){e.next=15;break}return j.nk.error("未找到用户信息，请重新登录"),c.value=!1,e.abrupt("return");case 15:if(r=JSON.parse(t),!r){e.next=26;break}return e.next=19,E["default"].teams.applyToJoinTeam(a,u.reason);case 19:j.nk.success("已成功提交加入申请，请等待审核"),u.teamCode="",u.reason="",o.value=!1,g(),e.next=27;break;case 26:j.nk.error("用户信息不完整，请重新登录");case 27:e.next=33;break;case 29:e.prev=29,e.t0=e["catch"](8),j.nk.error((null===(n=e.t0.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||"申请加入团队失败");case 33:return e.prev=33,c.value=!1,e.finish(33);case 36:e.next=42;break;case 38:e.prev=38,e.t1=e["catch"](2),c.value=!1;case 42:case"end":return e.stop()}}),e,null,[[2,38],[8,29,33,36]])})));return function(){return e.apply(this,arguments)}}(),_=function(){W.s.confirm("确定要退出当前团队吗？退出后需要重新申请加入。","退出团队",{confirmButtonText:"确定退出",cancelButtonText:"取消",type:"warning"}).then((0,V.A)((0,A.A)().mark((function e(){var a,t,r;return(0,A.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,a=localStorage.getItem("user"),a){e.next=6;break}return j.nk.error("未找到用户信息，请重新登录"),e.abrupt("return");case 6:if(t=JSON.parse(a),!(t&&t.team&&n.value)){e.next=15;break}return e.next=10,E["default"].teams.removeTeamMember(n.value.id,t.id);case 10:j.nk.success("已成功退出团队"),n.value=null,l.value=[],e.next=16;break;case 15:j.nk.error("您当前不在任何团队中");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e["catch"](0),j.nk.error("退出团队失败: "+((null===(r=e.t0.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||e.t0.message));case 22:case"end":return e.stop()}}),e,null,[[0,18]])}))))["catch"]((function(){}))},w=function(e){var a={ADMIN:"danger",DOCTOR:"primary",REVIEWER:"success"};return a[e]||"info"},x=function(e){var a={ADMIN:"管理员",DOCTOR:"标注医生",REVIEWER:"审核医生"};return a[e]||"未知"},F=function(e){if(!e)return"未知";try{var a=new Date(e);return isNaN(a.getTime())?"未知":a.toLocaleString("zh-CN")}catch(t){return"未知"}},y=function(){var e=(0,V.A)((0,A.A)().mark((function e(){var a;return(0,A.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,d.value=!0,e.prev=2,e.next=5,E["default"].teams.getAll();case 5:if(a=e.sent,!(a&&a.data&&a.data.length>0)){e.next=10;break}return m.value=a.data.map((function(e){return{code:e.id.toString(),name:e.name,description:e.description||"暂无描述"}})),e.abrupt("return");case 10:e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](2);case 15:m.value=[{code:"1",name:"管理员团队",description:"系统管理团队"},{code:"5",name:"第一测试",description:"仅限于测试"},{code:"6",name:"第二测试团队",description:"仅限于测试"}],e.next=24;break;case 19:e.prev=19,e.t1=e["catch"](0),j.nk.error("获取团队列表失败，已使用默认数据"),m.value=[{code:"1",name:"默认团队",description:"系统默认团队"}];case 24:return e.prev=24,d.value=!1,e.finish(24);case 27:case"end":return e.stop()}}),e,null,[[0,19,24,27],[2,12]])})));return function(){return e.apply(this,arguments)}}();(0,r.wB)(o,(function(e){e&&y()["catch"]((function(e){m.value=[]}))})),(0,r.sV)((function(){g()["catch"]((function(e){}))}));var C=function(){var e=(0,V.A)((0,A.A)().mark((function e(){var a,t;return(0,A.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(k.value){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,k.value.validate();case 5:if(b.value=!0,a=localStorage.getItem("user"),a){e.next=12;break}return j.nk.error("未找到用户信息，请重新登录"),b.value=!1,e.abrupt("return");case 12:return JSON.parse(a),e.next=15,E["default"].teams.createTeam(f.name,f.description);case 15:f.name="",f.description="",p.value=!1,j.nk.success("团队创建成功"),g(),e.next=28;break;case 22:if(e.prev=22,e.t0=e["catch"](2),"ValidationError"!==e.t0.name){e.next=26;break}return e.abrupt("return");case 26:j.nk.error("创建团队失败: "+((null===(t=e.t0.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||e.t0.message));case 28:return e.prev=28,b.value=!1,e.finish(28);case 31:case"end":return e.stop()}}),e,null,[[2,22,28,31]])})));return function(){return e.apply(this,arguments)}}();return{currentTeam:n,teamMembers:l,showJoinTeamDialog:o,joinTeamForm:u,joinTeamRules:i,joinTeamFormRef:s,joinTeamLoading:c,handleJoinTeam:T,handleLeaveTeam:_,getRoleType:w,getRoleName:x,formatDate:F,teamOptions:m,loading:d,searchTeams:y,showCreateTeamDialog:p,createTeamForm:f,createTeamRules:v,createTeamFormRef:k,createTeamLoading:b,handleCreateTeam:C,isAdmin:a,isReviewer:t}}};var I=t(66262);const N=(0,I.A)(K,[["render",L],["__scopeId","data-v-55c082ea"]]),O=N}}]);