package com.medical.annotation.config;

import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.util.UrlPathHelper;

/**
 * 上下文路径配置
 * 专门处理双重路径前缀问题
 */
@Configuration
public class ContextPathConfig implements WebMvcConfigurer {

    /**
     * 配置路径匹配，确保能正确处理双重路径前缀
     */
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        UrlPathHelper urlPathHelper = new UrlPathHelper();
        // 不移除分号内容，允许矩阵变量
        urlPathHelper.setRemoveSemicolonContent(false);
        // 不移除上下文路径，确保能正确处理/medical/medical前缀
        urlPathHelper.setAlwaysUseFullPath(true);
        configurer.setUrlPathHelper(urlPathHelper);
    }
    
    /**
     * 自定义Web服务器工厂，确保能正确处理上下文路径
     */
    @Bean
    public WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> webServerFactoryCustomizer() {
        return factory -> {
            // 设置上下文路径
            factory.setContextPath("/medical");
            // 设置显示名称
            factory.setDisplayName("血管瘤辅助系统");
            // 设置端口
            factory.setPort(8085);
        };
    }
} 