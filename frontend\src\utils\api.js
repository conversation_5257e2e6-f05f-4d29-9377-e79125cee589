import axios from 'axios';

// 创建axios实例
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/medical/api',  // 使用环境变量或默认为相对路径
  timeout: 30000,  // 超时时间30秒
  withCredentials: true // 允许跨域请求携带凭证
});

// 配置请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 使用通用函数规范化URL路径
    config.url = normalizePath(config.url);
    console.log(`[API] ${config.method.toUpperCase()} ${config.baseURL}${config.url}`);
    
    // 处理会话检查请求的特殊情况
    if (config.url.includes('/users/me') || config.url.includes('/session-check')) {
      console.log('[API] 检测到会话检查请求，使用简化认证');
      
      // 仅使用最基本的头部，减少跨域问题
      config.headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };
      
      // 添加时间戳，避免缓存
      const separator = config.url.includes('?') ? '&' : '?';
      config.url += `${separator}_t=${Date.now()}`;
      
      // 从localStorage获取用户基本信息
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const user = JSON.parse(userStr);
          if (user && (user.id || user.customId)) {
            const userId = user.customId || user.id;
            config.headers['Authorization'] = `Bearer user_${userId}`;
            
            // 添加userId作为URL参数，增强兼容性
            const paramSeparator = config.url.includes('?') ? '&' : '?';
            config.url += `${paramSeparator}userId=${userId}`;
          }
        }
      } catch (e) {
        console.warn('[API] 读取用户信息失败:', e);
      }
      
      // 关键修复: 将用户ID也添加到 X-User-ID 头中，这是许多后端端点需要的
      config.headers['X-User-ID'] = userId;
      
      return config;
    }
    
    // 从localStorage获取用户信息，添加到请求头
    let user;
    try {
      // 首先尝试从localStorage获取
      const userStr = localStorage.getItem('user');
      if (userStr) {
        user = JSON.parse(userStr);
      }
      
      // 如果localStorage中没有用户信息，尝试从sessionStorage获取
      if (!user || !user.id) {
        const sessionUserStr = sessionStorage.getItem('preservedUser');
        if (sessionUserStr) {
          console.log('[API] 从会话存储恢复用户信息');
          user = JSON.parse(sessionUserStr);
          // 同步回localStorage，保持一致性
          localStorage.setItem('user', sessionUserStr);
        }
      }
      
      // 如果用户对象存在但不完整，尝试恢复更多信息
      if (user && (!user.email || !user.role)) {
        const userProfileStr = localStorage.getItem('userProfile');
        if (userProfileStr) {
          const userProfile = JSON.parse(userProfileStr);
          // 合并缺失的信息
          user = { ...user, ...userProfile };
          console.log('[API] 从用户配置文件合并额外信息');
        }
      }
    } catch (e) {
      console.error('解析用户信息失败:', e);
    }
    
    if (user) {
      // 关键修复：始终优先使用数据库ID (user.id)，并简化认证头
      const userId = user.id || user.customId || '';
      
      // 简化Authorization头，只包含用户ID，避免后端解析混乱
      config.headers['Authorization'] = `Bearer user_${userId}`;
      
      // 关键修复: 将用户ID也添加到 X-User-ID 头中，这是许多后端端点需要的
      config.headers['X-User-ID'] = userId;
      
      // 将角色等其他信息作为URL参数传递，这更符合RESTful规范
      if (user.role) {
        // 使用URLSearchParams来安全地添加参数，避免重复
        const params = new URLSearchParams(config.params || {});
        if (!params.has('_role')) {
            params.set('_role', user.role);
        }
        config.params = params;
      }
      
      // 添加userId作为URL参数，增强兼容性
      const params = new URLSearchParams(config.params || {});
      if (!params.has('userId')) {
          params.set('userId', userId);
      }
      config.params = params;
      
      // 团队相关请求的特殊处理也应使用URL参数
      if (user.team && user.team.id && 
         (config.url.includes('/teams/') || 
          config.url.includes('/team-applications') || 
          config.url.includes('/images/team-'))) {
        const params = new URLSearchParams(config.params);
        if (!params.has('_teamId')) {
            params.set('_teamId', user.team.id);
        }
        config.params = params;
      }
      
    } else {
      console.warn('[API] 未找到用户信息，请求可能未认证');
    }
    
    // 增加跨域请求处理
    if (config.method && config.method.toLowerCase() === 'options') {
      // 对于预检请求，确保包含所有必要的头部
      config.headers['Access-Control-Request-Headers'] = 'authorization, content-type';
      config.headers['Access-Control-Request-Method'] = 'GET, POST, PUT, DELETE';
    }
    
    // 避免缓存造成的问题
    if (!config.headers['Cache-Control']) {
      config.headers['Cache-Control'] = 'no-cache';
    }
    
    // 添加时间戳参数，避免缓存问题
    const timestampParam = `_t=${Date.now()}`;
    if (config.url.includes('?')) {
      config.url += `&${timestampParam}`;
    } else {
      config.url += `?${timestampParam}`;
    }
    
    // 添加防止异常JSON数据造成的解析错误
    if (config.transformResponse && Array.isArray(config.transformResponse)) {
      config.transformResponse = [...config.transformResponse];
    } else {
      config.transformResponse = [data => {
        if (typeof data === 'string') {
          try {
            return JSON.parse(data);
          } catch (e) {
            console.warn('JSON解析失败:', e);
            return data;
          }
        }
        return data;
      }];
    }
    
    // 记录完整请求信息，便于调试
    console.log(`[API完整请求] ${config.method.toUpperCase()} ${config.baseURL}${config.url}`, {
      参数: config.params,
      头部: config.headers
    });
    
    return config;
  },
  error => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 配置响应拦截器
apiClient.interceptors.response.use(response => {
  console.log(`[API响应] ${response.status} ${response.config.url}`, {
    数据: response.data
  });
  return response;
}, error => {
  console.error('[API响应错误]', {
    请求URL: error.config ? error.config.url : '未知',
    请求方法: error.config ? error.config.method : '未知',
    状态码: error.response ? error.response.status : '网络错误',
    响应数据: error.response ? error.response.data : null
  });
  return Promise.reject(error);
});

// 创建一个使用代理的客户端，避免CORS问题
// 这会使用当前域名和端口，通过服务器代理访问
const fileApiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/medical/api',  // 使用环境变量或相对路径
  headers: {
    'Content-Type': 'multipart/form-data', // 文件上传
    'Accept': 'application/json'
  },
  timeout: 15000,
  withCredentials: true
});

// 创建一个专门用于调试的客户端，便于跟踪请求与响应
const debugApiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/medical/api',  // 使用环境变量或相对路径
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Debug': 'true'  // 添加调试标记
  },
  timeout: 10000,
  withCredentials: true
});

// 调试客户端拦截器
debugApiClient.interceptors.request.use(
  config => {
    // 使用通用函数规范化URL路径
    config.url = normalizePath(config.url);
    console.log(`[Debug API] 请求: ${config.method.toUpperCase()} ${config.baseURL}${config.url}`);
    
    // 添加用户认证信息，与apiClient保持一致
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        if (user) {
          // 使用标准Authorization头
          config.headers['Authorization'] = `Bearer user_${user.customId || user.id || ''}`;
          
          // 将额外信息编码到Authorization头中
          if (user.email) {
            const emailBase64 = btoa(user.email);
            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
          }
          
          if (user.role) {
            // 将角色信息添加到请求URL参数中
            const separator = config.url.includes('?') ? '&' : '?';
            config.url += `${separator}_role=${user.role}`;
          }
        }
      }
    } catch (e) {
      console.error('[Debug API] 读取用户信息失败:', e);
    }
    
    console.log('请求头:', config.headers);
    return config;
  },
  error => {
    console.error('[Debug API] 请求错误:', error);
    return Promise.reject(error);
  }
);

debugApiClient.interceptors.response.use(
  response => {
    console.log(`[Debug API] 响应状态: ${response.status}`);
    console.log('响应数据:', response.data);
    return response;
  },
  error => {
    console.error('[Debug API] 响应错误:', error.response || error);
    return Promise.reject(error);
  }
);

// 文件API拦截器
fileApiClient.interceptors.request.use(
  config => {
    // 使用通用函数规范化URL路径
    config.url = normalizePath(config.url);
    console.log(`[FileAPI] ${config.method.toUpperCase()} ${config.url}`);
    
    // 添加用户信息到请求头 - 修复认证问题
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
    if (user) {
          // 不使用可能导致CORS预检失败的自定义头
          config.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;
          if (user.email) {
            // 使用标准Authorization头部格式而非自定义头
            const emailBase64 = btoa(user.email);
            config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
          }
        }
      }
    } catch (e) {
      console.error('[FileAPI] 读取用户信息失败:', e);
    }
    
    return config;
  },
  error => {
    console.error('[FileAPI Request Error]', error);
    return Promise.reject(error);
  }
);

fileApiClient.interceptors.response.use(
  response => {
    console.log(`[FileAPI] Response: ${response.status}`);
    return response;
  },
  error => {
    if (error.response) {
      console.error(`[FileAPI] Error: ${error.response.status}`, error.response.data);
    } else {
      console.error('[FileAPI] Network Error:', error.message);
    }
    return Promise.reject(error);
  }
);

// 添加响应拦截器
apiClient.interceptors.response.use(
  response => {
    console.log(`[API] Response: ${response.status}`);
    // 添加更详细的日志，帮助调试数据结构问题
    try {
      const url = response.config.url;
      
      // 特殊处理团队成员API
      if (url.includes('/teams/') && url.includes('/members')) {
        console.log(`[API Debug] 团队成员API返回数据:`, 
          typeof response.data, 
          Array.isArray(response.data) ? '是数组' : '不是数组',
          response.data ? '非空' : '为空');
        
        // 修复非数组的团队成员响应
        if (response.data && !Array.isArray(response.data)) {
          console.warn('[API Debug] 团队成员API返回非数组数据，尝试修复');
          
          // 如果响应是对象但不是数组，尝试将其转换为数组
          if (typeof response.data === 'object') {
            // 检查已知的团队成员响应格式
            const extractedArray = response.data.members || response.data.data || response.data.content || response.data.users;
            if (Array.isArray(extractedArray)) {
              console.log('[API Debug] 从对象中提取到数组数据');
              response.data = extractedArray;
            } else if (response.data.content && typeof response.data.content === 'object') {
              // 处理嵌套内容对象
              const nestedArray = response.data.content.members || response.data.content.users;
              if (Array.isArray(nestedArray)) {
                console.log('[API Debug] 从嵌套对象中提取到数组数据');
                response.data = nestedArray;
              } else {
                // 如果无法提取数组，将对象放入数组中
                console.log('[API Debug] 将对象放入数组中');
                response.data = [response.data];
              }
            } else {
              // 检查是否有普通对象结构的团队成员
              const hasUserFields = response.data.id && (response.data.name || response.data.email);
              if (hasUserFields) {
                console.log('[API Debug] 检测到单个团队成员对象');
                response.data = [response.data];
              } else {
                // 尝试从对象键值中查找成员
                const membersFromKeys = Object.values(response.data).filter(v => 
                  typeof v === 'object' && v !== null && (v.id || v.userId || v.user_id)
                );
                if (membersFromKeys.length > 0) {
                  console.log('[API Debug] 从对象键值中提取成员');
                  response.data = membersFromKeys;
                } else {
                  // 最后尝试：将对象放入数组
                  console.log('[API Debug] 将对象放入数组中');
                  response.data = [response.data];
                }
              }
            }
          } else {
            // 如果不是对象也不是数组，返回空数组
            console.warn('[API Debug] 无法解析团队成员数据，返回空数组');
            response.data = [];
          }
        }
        
        // 确保数据始终是数组
        if (!response.data) {
          console.warn('[API Debug] 团队成员API返回空数据，初始化为空数组');
          response.data = [];
        }
        
        // 处理团队成员的用户ID和名称格式化
        if (Array.isArray(response.data)) {
          response.data = response.data.map(member => {
            // 如果没有id属性但有user_id，规范化数据结构
            if (!member.id && member.user_id) {
              member.id = member.user_id;
            }
            // 如果没有name属性但有username或user对象，规范化数据结构
            if (!member.name) {
              if (member.username) {
                member.name = member.username;
              } else if (member.user && member.user.name) {
                member.name = member.user.name;
              }
            }
            return member;
          });
        }
        
        console.log('[API Debug] 处理后的团队成员数据，成员数量:', 
          Array.isArray(response.data) ? response.data.length : 0);
      }
      
      // 用户API日志
      if (url.includes('/users')) {
        console.log(`[API Debug] 用户API返回数据结构:`, 
          typeof response.data, 
          Array.isArray(response.data) ? '是数组' : '不是数组',
          response.data ? '非空' : '为空');
          
        // 如果是对象且不是数组，打印其结构
        if (response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
          console.log('[API Debug] 对象键:', Object.keys(response.data));
        }
      }
      
      // 图像API日志 - 添加专门处理病例列表
      if (url.includes('/images/') || url.includes('/api/images')) {
        console.log(`[API Debug] 图像API返回数据:`, 
          typeof response.data, 
          Array.isArray(response.data) ? `是数组[${response.data.length}]` : '不是数组',
          response.data ? '非空' : '为空');
          
        // 如果响应是空的，初始化为空数组
        if (!response.data && (url.includes('my-images') || url.includes('team-images'))) {
          console.warn('[API Debug] 病例列表返回空数据，初始化为空数组');
          response.data = [];
        }
        
        // 确保病例列表是数组格式
        if (response.data && !Array.isArray(response.data) && 
           (url.includes('my-images') || url.includes('team-images'))) {
          console.warn('[API Debug] 病例列表返回非数组数据，尝试修复');
          if (typeof response.data === 'object') {
            // 检查多种可能的嵌套数组字段
            const extractedArray = response.data.content || response.data.images || 
                                  response.data.data || response.data.records || 
                                  response.data.items || response.data.list;
            
            if (Array.isArray(extractedArray)) {
              console.log('[API Debug] 从对象中提取到病例数组数据');
              response.data = extractedArray;
            } else if (response.data.page && typeof response.data.page === 'object') {
              // 处理分页结构
              const pagedArray = response.data.page.content || response.data.page.data || response.data.page.list;
              if (Array.isArray(pagedArray)) {
                console.log('[API Debug] 从分页对象中提取病例数组');
                response.data = pagedArray;
              } else {
                console.log('[API Debug] 将单个病例对象放入数组');
                response.data = [response.data];
              }
            } else if (Object.keys(response.data).length > 0 && 
                     (response.data.id || response.data.imageId || response.data.metadata_id)) {
              // 如果是单个病例对象
              console.log('[API Debug] 检测到单个病例对象，转换为数组');
              response.data = [response.data];
            } else {
              // 尝试从对象值中提取病例列表
              const imagesFromValues = Object.values(response.data).filter(v => 
                Array.isArray(v) && v.length > 0 && 
                typeof v[0] === 'object' && (v[0].id || v[0].imageId || v[0].metadata_id)
              );
              
              if (imagesFromValues.length > 0) {
                console.log('[API Debug] 从对象值中提取病例数组');
                response.data = imagesFromValues[0]; // 使用第一个找到的数组
              } else {
                console.log('[API Debug] 将对象放入数组');
                response.data = [response.data];
              }
            }
          }
        }
        
        // 为病例添加缺失的必要字段
        if (Array.isArray(response.data) && 
           (url.includes('my-images') || url.includes('team-images'))) {
          response.data = response.data.map(image => {
            // 确保每个病例都有ID
            if (!image.id && image.imageId) {
              image.id = image.imageId;
            } else if (!image.id && image.metadata_id) {
              image.id = image.metadata_id;
            }
            
            // 确保每个病例都有状态
            if (!image.status && image.imageStatus) {
              image.status = image.imageStatus;
            }
            
            // 确保每个病例都有标题
            if (!image.title && image.name) {
              image.title = image.name;
            }
            
            return image;
          });
          
          console.log(`[API Debug] 处理后的病例列表数量: ${response.data.length}`);
        }
      }
    } catch (e) {
      console.error('[API Debug] 日志错误:', e);
    }
    return response;
  },
  error => {
    if (error.response) {
      console.error(`[API] Error: ${error.response.status}`, error.response.data);
      
      // 如果是未授权错误，尝试自动重定向到登录页面
      if (error.response.status === 401) {
        console.warn('未授权，可能需要重新登录');
        
        // 检查是否是团队申请相关的API请求
        const isTeamApplicationRequest = error.config.url && (
          error.config.url.includes('/team-applications') ||
          error.config.url.includes('/teams/') && error.config.url.includes('/applications')
        );
        
        // 检查是否是标注相关的API请求
        const isAnnotationRelatedRequest = error.config.url && (
          error.config.url.includes('/annotations') || 
          error.config.url.includes('/tags') || 
          error.config.url.includes('/image-after-annotation') ||
          error.config.url.includes('/images/') ||
          error.config.url.includes('/structured-form')
        );
        
        // 检查是否在标注流程中
        const isInAnnotationFlow = isAnnotationRelatedRequest || 
                                  window.location.pathname.includes('/annotations') ||
                                  window.location.pathname.includes('/cases/structured-form') ||
                                  window.location.pathname.includes('/cases/form') ||
                                  sessionStorage.getItem('isNavigatingAfterSave');
        
        // 检查是否有特殊标记表示正在执行保存后跳转
        const isNavigatingAfterSave = sessionStorage.getItem('isNavigatingAfterSave');
        const skipAuthCheck = localStorage.getItem('skipAuthCheck');
        
        // 检查是否在团队相关页面 - 移除对team-management的引用
        const isTeamPage = window.location.pathname.includes('/teams');
          
        // 检查是否是病例列表相关请求
        const isCasesRelatedRequest = error.config.url && (
          error.config.url.includes('/images/my-images') || 
          error.config.url.includes('/images/team-images')
        );
        
        // 检查是否在病例相关页面
        const isCasePage = window.location.pathname.includes('/cases');
        
        // 如果是团队申请相关操作或病例列表操作 - 更新条件，使用isTeamPage替代isTeamManagementPage
        if (isTeamApplicationRequest || isTeamPage || isCasesRelatedRequest || isCasePage) {
          console.log('重要操作检测到认证问题，显示用户提示而非重定向');
          
          // 尝试自动修复
          const autoFixAttempted = attemptAutoFix();
          
          // 如果是病例列表 API，返回一个空数组以避免UI崩溃
          if (isCasesRelatedRequest) {
            console.log('病例列表API认证失败，返回空数组');
            const emptyResponse = {
              status: 200,
              statusText: 'OK (Mock)',
              data: []
            };
            return Promise.resolve(emptyResponse);
          }
          
          if (autoFixAttempted) {
            // 重试请求，用标准格式重发
            return retryRequestWithStandardAuth(error.config);
          }
          
          // 返回特殊错误，供UI层友好提示
          return Promise.reject({
            teamAuthError: true, 
            needsRelogin: true,
            message: '会话已过期，请重新登录后再操作'
          });
        }
        
        // 如果是标注流程或其他特殊情况，不执行强制重定向
        if (isNavigatingAfterSave || skipAuthCheck || isInAnnotationFlow) {
          console.log('检测到标注流程相关请求或特殊设置，不执行自动重定向');
          
          // 显示友好提示
          if (window.$message) {
            window.$message.warning('您的登录已过期，但可以继续当前操作');
          }
          
          // 尝试自动恢复会话
          const preservedUser = sessionStorage.getItem('preservedUser');
          if (preservedUser) {
            console.log('尝试使用保存的用户信息恢复会话');
            localStorage.setItem('user', preservedUser);
            
            // 重新发送请求
            const config = error.config;
            // 避免循环重试
            config._retry = true;
            
            // 添加新的认证信息
            const user = JSON.parse(preservedUser);
            if (user) {
              config.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;
              if (user.email) {
                const emailBase64 = btoa(user.email);
                config.headers['Authorization'] = `${config.headers['Authorization']}:${emailBase64}`;
              }
            }
            
            return apiClient(config);
          }
          
          // 返回特殊错误，但不中断流程
          return Promise.reject(new Error('会话已过期，请重新登录后再操作'));
        }
        
        // 对于其他请求，执行标准的重定向逻辑
        // 清除本地存储的用户信息
        localStorage.removeItem('user');
        
        // 如果不在登录页面，重定向到登录页面
        if (window.location.pathname !== '/login') {
          // 保存当前URL以便登录后返回
          const currentPath = window.location.pathname + window.location.search;
          sessionStorage.setItem('redirectAfterLogin', currentPath);
          
          window.location.href = '/login';
        }
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应 - 网络错误或后端服务未启动
      console.error('[API] 网络错误:', error.message);
      
      // 特殊处理病例列表和团队列表，避免UI崩溃
      const url = error.config?.url || '';
      if (url.includes('/images/my-images') || url.includes('/images/team-images') ||
          url.includes('/teams') && !url.includes('/applications')) {
        console.log('关键列表API网络错误，返回空数组以避免UI崩溃');
        const emptyResponse = {
          status: 200,
          statusText: 'OK (Mock due to network error)',
          data: []
        };
        return Promise.resolve(emptyResponse);
      }
    } else {
      console.error('[API] 请求配置错误:', error.message);
    }
    return Promise.reject(error);
  }
);

// 添加自动修复会话的函数
function attemptAutoFix() {
  try {
    // 检查是否有保存的用户信息
    const userStr = localStorage.getItem('user');
    if (!userStr) return false;
    
    const user = JSON.parse(userStr);
    
    // 尝试从Session Storage获取更多认证信息
    const sessionAuth = sessionStorage.getItem('authData');
    if (sessionAuth) {
      const authData = JSON.parse(sessionAuth);
      // 将认证信息合并到用户对象
      if (authData.token) {
        user.token = authData.token;
        // 保存更新后的用户信息
        localStorage.setItem('user', JSON.stringify(user));
        console.log('已从会话存储恢复认证令牌');
        return true;
      }
    }
    
    // 尝试使用Email和ID重新确认身份
    if (user.email && (user.id || user.customId)) {
      // 至少确认有这些基本信息
      return true;
    }
    
    return false;
  } catch (e) {
    console.error('自动修复尝试失败:', e);
    return false;
  }
}

// 添加会话恢复功能 - 在页面刷新时调用
function recoverSessionAfterRefresh() {
  try {
    console.log('检查页面是否需要恢复会话...');
    
    // 检查是否有保存的用户信息
    const userStr = localStorage.getItem('user');
    if (!userStr) {
      console.log('本地存储中没有用户信息，无需恢复会话');
      return false;
    }
    
    const user = JSON.parse(userStr);
    if (!user || (!user.id && !user.customId)) {
      console.log('用户信息不完整，无法恢复会话');
      return false;
    }
    
    // 在页面加载完成后发送认证恢复请求，使用简化格式
    const userId = user.customId || user.id;
    
    // 简化请求参数
    const url = '/medical/api/users/me';
    const params = {
      _t: new Date().getTime(),
      _refresh: true
    };
    
    // 构建简化的请求头，减少跨域问题
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Cache-Control': 'no-cache, no-store'
    };
    
    // 只使用最基本的认证方式
    if (userId) {
      headers['Authorization'] = `Bearer user_${userId}`;
    }
    
    // 创建URL参数字符串
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    // 构建完整URL
    const fullUrl = `${url}?${queryString}`;
    
    // 使用fetch API代替axios，减少中间层处理
    console.log('发送简化的会话恢复请求...');
    return fetch(fullUrl, {
      method: 'GET',
      headers: headers,
      credentials: 'include'
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`会话恢复请求失败: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log('会话恢复成功:', data);
        // 更新用户信息
        if (data && data.user) {
          localStorage.setItem('user', JSON.stringify(data.user));
          console.log('用户信息已更新');
        }
        return true;
      })
      .catch(error => {
        console.warn('会话恢复失败:', error);
        // 如果失败，不要阻止页面继续加载
        return false;
      });
  } catch (e) {
    console.error('会话恢复过程出错:', e);
    return false;
  }
}

// 页面加载时尝试恢复会话
if (typeof window !== 'undefined') {
  window.addEventListener('DOMContentLoaded', () => {
    recoverSessionAfterRefresh();
  });
}

// 添加一个工具函数，用于规范化API路径，避免重复前缀
function normalizePath(url) {
  if (!url) return url;

  // 保存原始URL，用于比较和记录
  const originalUrl = url;

  // 先去除开头的重复斜杠
  let normalizedUrl = url;
  while (normalizedUrl.startsWith('//')) {
    normalizedUrl = normalizedUrl.substring(1);
  }
  
  // 检查各种重复模式
  const patterns = [
    { find: 'medical/api/medical/api/', replace: 'medical/api/' },
    { find: 'api/medical/api/', replace: 'medical/api/' },
    { find: 'medical/api/api/', replace: 'medical/api/' },
    { find: 'medical/medical/', replace: 'medical/' },
    { find: 'api/api/', replace: 'api/' }
  ];
  
  // 应用所有模式
  let foundPatternMatch = false;
  for (const pattern of patterns) {
    if (normalizedUrl.includes(pattern.find)) {
      normalizedUrl = normalizedUrl.replace(new RegExp(pattern.find, 'g'), pattern.replace);
      console.log(`[normalizePath] 路径修正: 将 ${pattern.find} 替换为 ${pattern.replace}`);
      foundPatternMatch = true;
    }
  }
  
  // 确保以斜杠开头
  if (!normalizedUrl.startsWith('/')) {
    normalizedUrl = '/' + normalizedUrl;
  }
  
  // 特殊处理：确保/api/teams等关键路径保持正确格式
  // 如果URL路径已经包含/api前缀，不要使用apiClient.baseURL再添加一次/api
  if (normalizedUrl.startsWith('/api/') && apiClient && apiClient.defaults.baseURL === '/api') {
    // 这种情况下，我们需要修改URL，移除apiClient的baseURL以避免重复
    normalizedUrl = normalizedUrl.replace(/^\/api\//, '/');
    console.log(`[normalizePath] 检测到路径已有/api前缀，移除重复: ${normalizedUrl}`);
  }
  
  // 记录修正结果
  if (originalUrl !== normalizedUrl) {
    console.log(`[normalizePath] URL路径已规范化: ${originalUrl} -> ${normalizedUrl}`);
  }
  
  return normalizedUrl;
}

// 使用标准认证格式重新发送请求
function retryRequestWithStandardAuth(config) {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return Promise.reject(new Error('无可用认证信息'));
    
    const user = JSON.parse(userStr);
    const newConfig = {...config};
    
    // 重置头部，使用标准Authorization头
    newConfig.headers = {...config.headers};
    newConfig.headers['Authorization'] = `Bearer user_${user.customId || user.id}`;
    if (user.email) {
      const emailBase64 = btoa(user.email);
      newConfig.headers['Authorization'] = `${newConfig.headers['Authorization']}:${emailBase64}`;
    }
    
    // 移除所有可能导致CORS问题的自定义头
    delete newConfig.headers['X-User-Id'];
    delete newConfig.headers['X-User-Email']; 
    delete newConfig.headers['X-Authentication-Email'];
    delete newConfig.headers['X-User-Role'];
    delete newConfig.headers['X-User-Team-Id'];
    
    // 修复URL路径，避免重复前缀
    newConfig.url = normalizePath(newConfig.url);
    
    // 避免循环重试
    newConfig._retry = true;
    
    console.log('使用标准认证头重试请求，URL:', newConfig.url);
    return apiClient(newConfig);
  } catch (e) {
    console.error('重试请求失败:', e);
    return Promise.reject(e);
  }
}

// 添加一个工具函数来处理userId可能是customId的情况
const getUserIdParam = (userId) => {
  // 如果是对象，尝试提取customId或id属性
  if (userId && typeof userId === 'object') {
    return userId.customId || userId.id || null;
  }

  // 如果是字符串或数字，直接返回
  return userId;
};

// 添加一个工具函数，用于确保对象中的指定字段为数字类型
function ensureNumericFields(obj, fields) {
  if (!obj || typeof obj !== 'object') return obj;
  
  const result = {...obj};
  
  fields.forEach(field => {
    if (result[field] !== undefined && result[field] !== null) {
      const numValue = parseInt(result[field], 10);
      if (!isNaN(numValue)) {
        result[field] = numValue;
      } else {
        console.warn(`字段 ${field} 的值 "${result[field]}" 无法转换为数字`);
      }
    }
  });
  
  return result;
}

// API功能
const api = {
  // 认证相关
  auth: {
    // 登录
    login(credentials) {
      return apiClient.post('/users/authenticate', credentials);
    },
    // 注册
    register(userData) {
      return apiClient.post('/users', userData);
    }
  },
  
  // 统计数据相关
  stats: {
    // 获取仪表盘统计数据
    getDashboard(userId) {
      // 添加临时调试代码，追踪此函数的调用来源
      console.log(`[调试] getDashboard 被调用，传入的 userId:`, userId, `类型:`, typeof userId);
      
      // 验证用户ID是否有效
      if (!userId) {
        const userStr = localStorage.getItem('user');
        if (userStr) {
            const user = JSON.parse(userStr);
            // 统一使用数字ID
            userId = user.id;
            console.log('从localStorage中获取用户ID:', userId);
        }
      }
      
      // 如果仍然无法获取，返回拒绝的Promise
      if (!userId) {
        console.error('获取仪表盘统计数据时用户ID为空');
        return Promise.reject(new Error('用户ID不能为空'));
      }

      // 添加时间戳，避免缓存问题
      const timestamp = Date.now();
      const random = Math.random();
      
      // 使用标准API端点而非无限制版本，并添加强制个人统计参数
      let url = `/stats-v2/dashboard/${userId}?t=${timestamp}&r=${random}&forcePersonalStats=true&view=personal`;
      
      console.log(`获取用户[${userId}]的仪表盘统计数据, 完整URL: ${apiClient.defaults.baseURL}${url}`);
      
      // 设置请求头，只保留必要的标准头部，避免CORS预检问题
      const config = {
        headers: {
          'X-User-Id': userId
        },
        timeout: 15000,  // 增加超时时间到15秒
        withCredentials: true  // 确保发送认证信息
      };
      
      // 使用apiClient发送请求，错误处理不再回退到旧API
      return apiClient.get(url, config)
        .catch(error => {
          console.error(`获取用户[${userId}]的仪表盘统计数据失败:`, error.message || error);
          
          if (error.response) {
            console.error('服务器响应状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
          } else if (error.request) {
            console.error('请求已发送但未收到响应，可能是网络问题');
          }
          
          // 直接返回空数据，避免UI崩溃
          return {
            data: {
              totalCount: 0,
              draftCount: 0,
              reviewedCount: 0,
              submittedCount: 0,
              approvedCount: 0,
              rejectedCount: 0,
              dataSource: 'error_fallback',
              userId: userId,  // 记录实际使用的用户ID
              error: error.message || '获取统计数据失败'
            }
          };
        });
    }
  },
  
  // 用户相关
  users: {
    // 获取当前用户信息
    getCurrentUser() {
      return apiClient.get('/users/me');
    },
    
    // 更新用户个人信息
    updateProfile(profileData) {
      return apiClient.put('/users/profile', profileData);
    },
    
    // 修改密码
    changePassword(passwordData) {
      // 获取当前用户ID
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = currentUser.id;
      
      if (!userId) {
        console.error('修改密码失败: 未找到用户ID');
        return Promise.reject(new Error('未找到用户ID'));
      }
      
      return apiClient.post(`/users/${userId}/change-password`, passwordData);
    },
    
    // 上传头像
    uploadAvatar(userId, formData) {
      return apiClient.post(`/users/${userId}/avatar`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    
    // 获取当前用户的审核医生申请状态
    getReviewerApplicationStatus() {
      return apiClient.get('/reviewer-applications/me');
    },
    // 申请成为审核医生
    applyForReviewer(reason) {
      return apiClient.post('/reviewer-applications', { reason });
    },
    // 获取所有用户
    getAll() {
      return apiClient.get('/users');
    },
    // 获取单个用户
    getUser(id) {
      return apiClient.get(`/users/${id}`);
    },
    // 创建用户
    createUser(userData) {
      return apiClient.post('/users', userData);
    },
    // 更新用户
    updateUser(id, userData) {
      return apiClient.put(`/users/${id}`, userData);
    },
    // 删除用户
    deleteUser(id) {
      return apiClient.delete(`/users/${id}`);
    },
    // 重置密码
    resetPassword(userId, newPassword) {
      return apiClient.post(`/users/${userId}/reset-password`, { newPassword });
    },
    // 获取所有审核医生
    getAllReviewers() {
      return apiClient.get('/users/reviewers');
    },
    // 获取所有待处理的审核医生申请
    getPendingReviewerApplications() {
      return apiClient.get('/reviewer-applications/pending');
    },
    // 处理审核医生申请
    processReviewerApplication(applicationId, data) {
      return apiClient.put(`/reviewer-applications/${applicationId}`, data);
    },
    // 获取无团队的用户
    getUsersWithoutTeam() {
      return apiClient.get('/users/without-team');
    },
    // 发送密码重置邮件
    sendResetPasswordEmail(email) {
      return apiClient.post('/users/forgot-password', { email });
    },
    // 验证重置密码验证码
    verifyResetCode(email, code) {
      return apiClient.post('/users/verify-reset-code', { email, code });
    },
    // 重置密码（忘记密码流程）
    resetPassword(email, code, newPassword) {
      return apiClient.post('/users/reset-password', { email, code, newPassword });
    },
    
    // 获取用户头像
    getUserAvatar(userId) {
      return apiClient.get(`/users/${userId}/avatar`);
    },
  },
  
  // 团队相关
  teams: {
    // 创建团队
    create(teamData) {
      return apiClient.post('/teams', teamData);
    },
    // 获取所有团队
    getAll() {
      return apiClient.get('/teams');
    },
    // 获取单个团队信息
    getOne(id) {
      return apiClient.get(`/teams/${id}`);
    },
    // 加入团队/部门
    joinTeam(teamId, userId) {
      return apiClient.post(`/teams/${teamId}/members`, { userId });
    },
    // 申请加入团队
    applyToJoinTeam(teamId, reason) {
      // 从localStorage获取用户信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = user.customId || user.id;
      
      console.log('申请加入团队，使用用户ID:', userId, '类型:', typeof userId);
      
      // 检查是否获取到了有效的userId
      if (!userId) {
        console.error('无法获取有效的用户ID，可能用户未登录或会话已过期');
        // 返回一个被拒绝的Promise，这样调用者可以捕获错误
        return Promise.reject(new Error('无法获取用户ID，请重新登录'));
      }
      
      // 构建请求数据，只包含必要的参数，不包含userId
      const requestData = {
        teamId: parseInt(teamId, 10) || teamId, // 尝试将字符串转为数字
        reason: reason,
        status: 'PENDING' // 明确设置状态为"申请中"
      };
      
      console.log('团队申请请求数据:', JSON.stringify(requestData));
      
      // 使用apiClient发送请求，让请求拦截器自动添加认证信息
      return apiClient.post('/team-applications', requestData);
    },
    // 获取团队的所有申请记录
    getTeamApplications(teamId, status) {
      let url = `/team-applications/team/${teamId}`;
      if (status) {
        url += `?status=${status}`;
      }
      return apiClient.get(url);
    },
    // 处理团队申请（批准或拒绝）
    processTeamApplication(applicationId, action, reason) {
      return apiClient.put(`/team-applications/${applicationId}`, {
        action: action, // 'APPROVE' 或 'REJECT'
        reason: reason || ''
      });
    },
    // 获取用户的团队申请（增强版，支持customId）
    getUserApplications(userId) {
      const idParam = getUserIdParam(userId);
      return apiClient.get(`/team-applications/user/${idParam}`);
    },
    // 获取团队成员
    getTeamMembers(teamId) {
      return apiClient.get(`/teams/${teamId}/members`);
    },
    // 获取团队已通过标注
    getTeamAnnotations(teamId) {
      return apiClient.get(`/teams/${teamId}/annotations/approved`);
    },
    // 获取单个标注详情
    getAnnotationDetail(annotationId) {
      return apiClient.get(`/annotations/${annotationId}`);
    },
    // 移除团队成员
    removeTeamMember(teamId, userId) {
      return apiClient.delete(`/teams/${teamId}/members/${userId}`);
    },
    // 添加检查用户是否已经是团队成员的方法
    checkUserInTeam(userId, teamId) {
      return apiClient.get(`/teams/${teamId}/members/check/${userId}`)
        .catch(error => {
          // 处理404等错误，返回一个默认响应
          if (error.response) {
            return { data: { isMember: false } };
          }
          throw error;
        });
    },
    deleteTeam(teamId) {
      const userStr = localStorage.getItem('user');
      const user = userStr ? JSON.parse(userStr) : null;
      const headers = {};
      if (user && user.id) {
        headers['X-User-ID'] = user.id;
      }
      return apiClient.delete(`/teams/${teamId}`, { headers });
    },
    transferOwnership(teamId, newOwnerId) {
      const userStr = localStorage.getItem('user');
      const user = userStr ? JSON.parse(userStr) : null;
      const headers = {};
      if (user && user.id) {
        headers['X-User-ID'] = user.id;
      }
      return apiClient.post(`/teams/${teamId}/transfer-ownership`, { newOwnerId }, { headers });
    },
  },
  
  // 审核医生申请相关
  reviewer: {
    // 获取已处理的申请
    getProcessedApplications() {
      return apiClient.get('/reviewer-applications/processed');
    }
  },
  
  // 图像相关
  images: {
    // 获取图像列表
    getAll() {
      return apiClient.get('/images');
    },
    // 获取单张图像
    getOne(id) {
      return apiClient.get(`/images/${id}`);
    },
    // 上传图像
    upload(file) {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      
      // 获取用户信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user.id) {
        formData.append('user_id', user.id);
          }
      
      // 添加文件名和描述
      if (file.name) {
        formData.append('name', file.name);
      }
      
      // 确保timeoutDuration和retryCount有有效值
      const timeoutDuration = 15000;
      const retryCount = 3;
      
      console.log('上传文件中...', {
        文件名: file.name,
        文件大小: file.size,
        文件类型: file.type,
        用户ID: user.id
      });
      
      // 使用fileApiClient发送请求，增加超时时间
      const config = {
        timeout: timeoutDuration,
      };
      
      return fileApiClient.post('/images/upload', formData, config);
    },
    // 保存图像到指定路径
    saveToPath(formData) {
      return fileApiClient.post('/images/save-to-path', formData);
    },
    
    // 保存标注后的图像 - 修改为适配所有调用场景的统一实现
    saveAnnotatedImage(imageId, processedImageFile = null) {
      console.log(`[API] 保存标注图像，图像ID: ${imageId}`);
      if (!imageId) {
        console.error('[API] 保存标注图像失败: 缺少图像ID');
        return Promise.reject(new Error('缺少图像ID'));
      }
      
      // 如果提供了图像文件，使用上传处理
      if (processedImageFile) {
        console.log('[API] 检测到图像文件，使用文件上传方式');
        const formData = new FormData();
        formData.append('file', processedImageFile);
        
        return fileApiClient.post(`/images/${imageId}/annotate`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }).catch(error => {
          console.error('[API] 文件上传方式保存标注失败:', error);
          return Promise.reject(error);
        });
      }
      
      // 否则使用服务器端生成
      // 添加时间戳避免缓存问题
      const timestamp = Date.now();
      const params = { 
        metadata_id: imageId,
        t: timestamp
      };
      
      console.log(`[API] 调用标注保存API，参数:`, params);
      
      // 直接尝试多种端点，提高成功率
      return Promise.any([
        // 方法1: 使用generate-annotated-image端点
        apiClient.get(`/tags/generate-annotated-image/${imageId}`, { 
          params: { 
            ...params,
            mode: 'edit'
          },
          timeout: 10000 // 10秒超时
        }).then(response => {
          console.log('[API] generate-annotated-image成功:', response.data);
          return response;
        }).catch(error => {
          console.error('[API] generate-annotated-image失败:', error);
          return Promise.reject(error);
        }),
        
        // 方法2: 使用save-image-after-annotation端点
        apiClient.get(`/tags/save-image-after-annotation/${imageId}`, { 
          params,
          timeout: 10000 // 10秒超时
        }).then(response => {
          console.log('[API] save-image-after-annotation成功:', response.data);
          return response;
        }).catch(error => {
          console.error('[API] save-image-after-annotation失败:', error);
          return Promise.reject(error);
        }),
        
        // 方法3: 使用直接的标注保存端点
        apiClient.post(`/tags/save-annotated-image`, { 
          metadata_id: imageId,
          timestamp: timestamp
        }, {
          timeout: 10000 // 10秒超时
        }).then(response => {
          console.log('[API] 直接保存标注成功:', response.data);
          return response;
        }).catch(error => {
          console.error('[API] 直接保存标注失败:', error);
          return Promise.reject(error);
        })
      ]).catch(errors => {
        console.error('[API] 所有保存标注方法均失败:', errors);
        // 返回一个模拟成功的响应，以便前端能继续流程
        return {
          data: {
            success: false,
            message: '标注保存失败，但允许继续',
            error: errors.message || '所有API调用均失败'
          }
        };
      });
    },
    
    // 删除图像
    delete(id) {
      console.log(`删除图像ID: ${id}`);
      return apiClient.delete(`/hemangioma-diagnoses/${id}`);
    },
    
    // 更新图像元数据
    update(id, metadata) {
      return apiClient.put(`/images/${id}`, metadata);
    },
    
    // 提交图像到审核流程
    submitForReview(id) {
      return apiClient.post(`/images/${id}/submit`);
    },
    
    // 审核图像
    reviewImage(id, approved, reviewNotes) {
      return apiClient.post(`/images/${id}/review`, { approved, reviewNotes });
    },
    
    // 保存标注图像 - 统一实现，避免方法重复
    saveAnnotatedImage(imageId) {
      console.log(`[API] 保存标注图像，图像ID: ${imageId}`);
      if (!imageId) {
        console.error('[API] 保存标注图像失败: 缺少图像ID');
        return Promise.reject(new Error('缺少图像ID'));
      }
      
      // 添加时间戳避免缓存问题
      const timestamp = Date.now();
      const params = { 
        metadata_id: imageId,
        t: timestamp
      };
      
      console.log(`[API] 调用标注保存API，参数:`, params);
      
      // 首先尝试调用generate-annotated-image端点（更可靠）
      return apiClient.get(`/tags/generate-annotated-image/${imageId}`, { 
        params: { 
          ...params,
          mode: 'edit'
        } 
      }).catch(error => {
        console.error('[API] 调用generate-annotated-image失败:', error);
        
        // 如果失败，尝试调用save-image-after-annotation端点
        console.log('[API] 尝试备用端点 save-image-after-annotation');
        return apiClient.get(`/tags/save-image-after-annotation/${imageId}`, { params });
      });
    },
    
    // 删除标注图像
    deleteAnnotatedImage(path) {
      return apiClient.delete('/images/annotated', { params: { path } });
    },
    
    // 获取当前用户上传的所有图像
    getUserImages(status = null, customUserId = null) {
      // 优先使用传入的用户ID，否则从localStorage获取
      let userId;
      if (customUserId) {
        userId = customUserId;
        console.log('getUserImages: 使用传入的用户ID:', userId);
      } else {
        // 从localStorage获取用户信息
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        userId = user.customId || user.id;
        console.log('getUserImages: 从localStorage获取用户ID:', userId);
      }
      
      // 构建查询参数，明确指定用户ID
      let params = { userId };
      if (status) {
        params.status = status;
      }
      
      // 明确传递用户ID作为查询参数
      console.log('获取用户图像，用户ID:', userId, '状态:', status || '全部');
      
      // 使用完整的直接路径，完全避免前缀问题
      // 不依赖interceptor添加前缀，而是直接指定完整路径
      const directUrl = '/medical/api/images/my-images';
      
      // 添加调试信息，确认请求的完整URL
      console.log('getUserImages使用完整路径:', directUrl, '参数:', params);
      
      try {
        // 使用axios直接请求，避免拦截器可能添加的前缀
        return axios.get(directUrl, { params });
      } catch (error) {
        console.error('获取用户图像失败:', error);
        return Promise.reject(error);
      }
    },
    
    // 获取团队内的所有图像
    getTeamImages(customUserId = null) {
      // 优先使用传入的用户ID，否则从localStorage获取
      let userId;
      if (customUserId) {
        userId = customUserId;
        console.log('getTeamImages: 使用传入的用户ID:', userId);
      } else {
        // 从localStorage获取用户信息
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        userId = user.customId || user.id;
        console.log('getTeamImages: 从localStorage获取用户ID:', userId);
      }
      
      // 构建查询参数，明确指定用户ID
      let params = { userId };
      
      console.log('获取团队图像，用户ID:', userId);
      
      // 使用完整的直接路径，完全避免前缀问题
      const directUrl = '/medical/api/images/team-images';
      
      // 添加调试信息
      console.log('getTeamImages使用完整路径:', directUrl, '参数:', params);
      
      try {
        // 使用axios直接请求，避免拦截器可能添加的前缀
        return axios.get(directUrl, { params });
      } catch (error) {
        console.error('获取团队图像失败:', error);
        return Promise.reject(error);
      }
    },
    
    // 获取待审核的图像
    getPendingReviewImages() {
      return apiClient.get('/images/pending-review');
    },
    
    // 获取已审核的图像
    getReviewedImages() {
      return apiClient.get('/images/reviewed');
    },
    
    // 保存结构化表单数据（改为简单请求：POST + application/x-www-form-urlencoded，不带自定义头）
    saveStructuredFormData(imageId, formData) {
      console.log('保存结构化表单数据，图像ID:', imageId);
      
      // 检查是否包含标注数据
      const annotations = formData.annotations;
      if (annotations) {
        console.log('表单包含标注数据:', annotations.length, '个标注');
        delete formData.annotations; // 从表单数据中移除标注数据
      }
      
      // 将对象转为 x-www-form-urlencoded 字符串
      const params = new URLSearchParams();
      Object.keys(formData).forEach(key => {
        let value = formData[key];
        // 数组转为逗号分隔字符串
        if (Array.isArray(value)) {
          value = value.join(',');
        }
        params.append(key, value == null ? '' : value);
      });
      
      // 首先保存表单数据
      return apiClient.post(`/images/${imageId}/structured-form`, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
      .then(response => {
        console.log('表单数据保存成功:', response.data);
        
        // 如果有标注数据，确保它们也被保存
        if (annotations && annotations.length > 0) {
          console.log('开始保存标注数据...');
          
          // 获取用户信息
          const user = JSON.parse(localStorage.getItem('user') || '{}');
          
          // 创建保存标注的Promise数组
          const annotationPromises = annotations.map(annotation => {
            // 确保标注数据格式正确
            const tagData = {
              metadata_id: parseInt(imageId, 10),
              tag: annotation.tag || annotation.name,
              x: annotation.x,
              y: annotation.y,
              width: annotation.width,
              height: annotation.height,
              created_by: user.id || 3 // 使用实际的用户ID或默认值3
            };
            
            console.log('保存标注数据:', tagData);
            
            // 使用tags API保存标注
            return this.saveTag(tagData);
          });
          
          // 等待所有标注保存完成
          return Promise.all(annotationPromises)
            .then(results => {
              console.log('所有标注保存成功:', results);
              return response; // 返回原始表单保存响应
            })
            .catch(error => {
              console.error('保存标注数据失败:', error);
              // 即使标注保存失败，也返回表单保存结果
              return response;
            });
        }
        
        return response;
      });
    },
    
    // 保存单个标注
    saveTag(tagData) {
      console.log('保存单个标注:', tagData);
      
      return apiClient.post('/tags', tagData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    },
    
    // 更新结构化表单数据 - 实际上与保存是同一个API
    updateStructuredFormData(id, formData) {
      console.log('API调用: 更新结构化表单数据，ID:', id, '数据:', formData);
      return apiClient.put(`/images/${id}/structured-form`, formData);
    },
    
    // 将图像标记为已标注状态
    markAsAnnotated(id, timestamp) {
      console.log('API调用: 将图像标记为已标注，ID:', id, timestamp ? '时间戳:' + timestamp : '');
      
      // 构建查询参数
      const params = {};
      if (timestamp) {
        params.reviewTimestamp = timestamp;
      }
      
      // 添加防重复提交标志
      const requestKey = `mark_annotated_${id}_${new Date().getTime()}`;
      if (window.pendingRequests && window.pendingRequests[requestKey]) {
        console.warn('已有相同请求正在处理中，避免重复提交');
        return window.pendingRequests[requestKey];
      }
      
      // 创建请求并保存引用
      const request = apiClient.put(`/images/${id}/mark-annotated`, null, { params })
        .then(response => {
          // 请求成功，移除引用
          if (window.pendingRequests) {
            delete window.pendingRequests[requestKey];
          }
          return response;
        })
        .catch(error => {
          // 请求失败，移除引用
          if (window.pendingRequests) {
            delete window.pendingRequests[requestKey];
          }
          throw error;
        });
      
      // 初始化pendingRequests对象（如果不存在）
      if (!window.pendingRequests) {
        window.pendingRequests = {};
      }
      
      // 存储请求引用
      window.pendingRequests[requestKey] = request;
      
      return request;
    },
    
    // 更新图像状态
    updateStatus(id, status) {
      console.log('API调用: 更新图像状态，ID:', id, '状态:', status);
      return apiClient.put(`/images/${id}/status`, null, { params: { status } });
    },
    
    // 新增：获取经过筛选的图像列表（专用于病例列表页面）
    getFilteredImages(status = null) {
      // 从localStorage获取用户信息
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = user.customId || user.id;
      
      if (!userId) {
        console.error('获取筛选图像列表失败：未找到用户ID');
        return Promise.reject(new Error('用户未登录或ID不可用'));
      }
      
      // 检查是否有特殊标记表示这是从Dashboard直接导航过来的
      const isDirectNavigation = localStorage.getItem('directNavigation') === 'true';
      if (isDirectNavigation) {
        // 清除标记
        localStorage.removeItem('directNavigation');
        
        // 检查是否有保存的状态
        const savedStatus = localStorage.getItem('lastSelectedStatus');
        if (savedStatus && !status) {
          status = savedStatus;
          console.log('使用保存的状态值:', status);
        }
      }
      
      // 构建查询参数
      let params = { userId };
      if (status) {
        params.status = status;
      }
      
      console.log('获取筛选图像列表，用户ID:', userId, '状态:', status || '全部');
      
      // 使用固定的直接路径
      const directUrl = '/medical/api/images/my-images';
      
      // 添加日志
      console.log('getFilteredImages使用路径:', directUrl, '参数:', params);
      
      try {
        // 使用axios直接请求，避免拦截器问题
        return axios.get(directUrl, { params })
          .then(response => {
            console.log('获取筛选图像列表成功，数量:', response.data ? response.data.length : 0);
            return response;
          });
      } catch (error) {
        console.error('获取筛选图像列表失败:', error);
        return Promise.reject(error);
      }
    },
    // 保存标注图像
    saveAnnotatedImage(imageId) {
      console.log(`保存标注图像，图像ID: ${imageId}`);
      if (!imageId) {
        return Promise.reject(new Error('缺少图像ID'));
      }
      
      // 修改为GET请求，添加参数到URL
      return apiClient.get(`/tags/save-image-after-annotation/${imageId}`, {
        params: {
          metadata_id: imageId,
          t: Date.now() // 添加时间戳避免缓存问题
        }
      });
    },
    // 关键修复：保存标注图像，使用 apiClient
    saveAnnotatedImage(imageId) {
      console.log(`保存标注图像，图像ID: ${imageId}`);
      if (!imageId) {
        return Promise.reject(new Error('缺少图像ID'));
      }
      
      // 使用新增的端点直接生成标注图像
      return apiClient.get(`/tags/generate-annotated-image/${imageId}`, {
        params: {
          t: Date.now(), // 添加时间戳避免缓存问题
          mode: 'edit'   // 指定模式
        }
      }).catch(error => {
        console.error('生成标注图像失败:', error);
        // 尝试备用端点
        return apiClient.get(`/tags/save-annotated-image/${imageId}`, {
          params: {
            metadata_id: imageId,
            t: Date.now()
          }
        });
      });
    },
  },
  
  // 标签管理
  tags: {
    // 根据图像ID获取标签
    getByImageId(imageId, mode = 'view') {
      console.log(`获取图像标签，ID: ${imageId}, 模式: ${mode}`);
      return apiClient.get(`/tags/image/${imageId}`, { params: { mode } });
    },

    // 根据ID获取单个标签
    getById(id) {
      return apiClient.get(`/tags/${id}`);
    },

    // 创建新标签
    create(tagData) {
      console.log('创建新标签:', tagData);
      // 确保关键字段是数字
      const cleanData = {
        ...tagData,
        metadata_id: parseInt(tagData.metadata_id, 10),
        created_by: parseInt(tagData.created_by, 10)
      };
      return apiClient.post('/tags', cleanData);
    },

    // 更新标签
    update(id, tagData) {
      return apiClient.put(`/tags/${id}`, tagData);
    },

    // 删除标签
    delete(id) {
      // 显式传递userId，以便后端进行权限校验
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const params = {};
      
      // 同时传递数据库ID和业务ID，确保后端能够正确识别用户身份
      if (user && user.id) {
        params.userId = user.id;
      }
      
      // 添加自定义ID作为备用
      if (user && user.customId) {
        params.customUserId = user.customId;
      }
      
      console.log('删除标注，传递用户信息:', { 
        标注ID: id, 
        用户ID: params.userId, 
        自定义ID: params.customUserId 
      });
      
      return apiClient.delete(`/tags/${id}`, { params });
    },
    
    // 删除与图像关联的所有标签
    deleteByImageId(imageId) {
        return apiClient.delete(`/tags/image/${imageId}`);
    },

    // 根据用户ID获取标签
    getByUserId(userId) {
      return apiClient.get(`/tags/user/${userId}`);
    },

    // 关键修复：保存标注图像，使用 apiClient
    saveAnnotatedImage(imageId) {
      console.log(`保存标注图像，图像ID: ${imageId}`);
      if (!imageId) {
        return Promise.reject(new Error('缺少图像ID'));
      }
      
      // 修改为GET请求，添加参数到URL
      return apiClient.get(`/tags/save-image-after-annotation/${imageId}`, {
        params: {
          metadata_id: imageId,
          t: Date.now() // 添加时间戳避免缓存问题
        }
      });
    },

    // 获取标注详情
    getAnnotationDetail(id) {
      return apiClient.get(`/annotations/${id}`);
    },
  },
  
  // 标注图像操作
  annotations: {
    // 获取标注详情
    getAnnotationDetail(id) {
      return apiClient.get(`/annotations/${id}`);
    },
    
    // 重定向到统一的标注图像生成方法，避免重复实现
    saveAnnotatedImage(imageId) {
      console.log('使用统一的标注图像生成方法');
      // 修正：使用api.tags而不是this.tags
      return api.tags.saveAnnotatedImage(imageId);
    },
    
    // 更新已有标注图像
    updateAnnotatedImage(imageId, imagePairId) {
      // 获取用户信息用于认证
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const params = { 
        _t: new Date().getTime(),
        userId: user.customId || user.id || ''
      };
      
      if (user.role) {
        params._role = user.role;
      }
      
      return apiClient.put(`/annotations/${imageId}/${imagePairId}`, null, { params });
    },
    
    // 生成带标注的图像 - 重定向到统一方法
    generateAnnotatedImage(imageId) {
      console.log('重定向到统一的标注图像生成方法');
      // 修正：使用api.tags而不是this.tags
      return api.tags.saveAnnotatedImage(imageId);
    }
  },
  
  // 图像对（图像与标注）
  imagePairs: {
    // 获取所有图像对
    getAll() {
      return apiClient.get('/image-pairs');
    },
    // 按元数据ID获取图像对
    getByMetadataId: (metadataId) => {
      // 在请求前添加认证调试信息
      console.log('获取图像对 - 认证调试:');
      let user;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
          console.log('用户信息:', {
            id: user.id,
            customId: user.customId,
            email: user.email,
            role: user.role
          });
        } else {
          console.warn('本地存储中没有用户信息');
        }
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }

      // 处理长整型ID（如时间戳ID）
      if (metadataId && metadataId.toString().length > 9) {
        const fullId = metadataId.toString();
        console.log(`使用完整ID参数请求: ${fullId}`);
        // 使用查询参数API而非路径参数
        return apiClient.get(`/api/image-pairs/by-metadata?id=${fullId}`);
      }
      
      // 尝试使用ImageFileController的API获取图像对信息
      const directPath = `/api/images/${metadataId}/image-pairs`;
      console.log('尝试使用图像控制器API:', directPath);
      
      // 添加自定义请求头和查询参数，确保认证信息被传递
      const config = {};
      if (user) {
        // 添加查询参数
        config.params = {
          _role: user.role || 'USER',
          userId: user.customId || user.id,
          _t: new Date().getTime()  // 时间戳避免缓存
        };
        
        console.log('添加查询参数:', config.params);
      }
      
      // 使用axios直接请求，而不是apiClient，完全避免拦截器
      return axios.get(directPath, config)
        .catch(error => {
          console.error('获取图像对失败:', error);
          // 返回空数据作为备选方案
          return { data: [] };
        });
    },
    // 获取单个图像对
    getOne(id) {
      return apiClient.get(`/image-pairs/${id}`);
    },
    // 创建图像对
    create(imagePairData) {
      return apiClient.post('/image-pairs', imagePairData);
    },
    // 更新图像对
    update(id, imagePairData) {
      return apiClient.put(`/image-pairs/${id}`, imagePairData);
    },
    // 删除图像对
    delete(id) {
      return apiClient.delete(`/image-pairs/${id}`);
    },
    // 删除一个元数据相关的所有图像对
    deleteByMetadataId(metadataId) {
      return apiClient.delete(`/image-pairs/metadata/${metadataId}`);
    },
    // 删除标注图像 - 使用正确的注解控制器路径
    deleteAnnotatedImage(id) {
      return apiClient.delete(`/annotations/${id}`);
    },
    upload(formData) {
      // 关键修复：使用正确的URL和文件上传客户端
      return fileApiClient.post('/images/upload', formData);
    },
    saveToPath(formData) {
      // 使用POST请求保存标注数据
      return fileApiClient.post('/images/save-to-path', formData);
    },
  },
  
  // 团队申请相关API（新增）
  teamApplications: {
    // 获取所有待处理的申请（管理员视图）
    getPendingApplications() {
      return apiClient.get('/team-applications/pending');
    },
    // 获取已处理的申请（可选状态过滤）
    getProcessedApplications(status) {
      if (status) {
        return apiClient.get(`/team-applications/processed?status=${status}`);
      } else {
        return apiClient.get('/team-applications/processed');
      }
    },
    // 处理申请
    processApplication(applicationId, data) {
      console.log(`调用处理申请API: ID=${applicationId}, 数据=`, data);
      return apiClient.put(`/team-applications/${applicationId}`, data);
    },
    // 获取团队的申请
    getTeamApplications(teamId, status) {
      let url = `/team-applications/team/${teamId}`;
      if (status) {
        url += `?status=${status}`;
          }
      return apiClient.get(url);
    },
    // 获取用户的申请
    getUserApplications(userId) {
      return apiClient.get(`/team-applications/user/${userId}`);
    },
    // 申请加入团队
    applyToJoinTeam(teamId, reason) {
      return apiClient.post(`/team-applications`, { teamId, reason });
    }
  },
  
  // 图像日志相关
  imageLogs: {
    // 记录图像操作日志
    logOperation(logData) {
      console.log('记录图像操作日志:', logData);
      return apiClient.post('/image-logs/log', logData);
    },
    
    // 获取时区信息
    getTimezoneInfo() {
      return apiClient.get('/image-logs/timezone-info');
    }
  }
};

// 处理长整型ID的函数，将其转换为合适的整数
function processLongId(id) {
  if (!id) return null;
  
  const idStr = id.toString();
  // 如果ID长度超过9（INT范围限制），使用完整ID但添加特殊参数
  if (idStr.length > 9) {
    console.log(`处理长ID: ${idStr} - 使用完整ID和查询参数`);
    // 使用原始ID，但添加长ID标记，传递给新的API端点
    return idStr;
  }
  return idStr;
}

export default api;