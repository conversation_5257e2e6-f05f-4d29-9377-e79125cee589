"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[453],{42453:(n,t,e)=>{e.r(t),e.d(t,{default:()=>D});e(62010);var a=e(61431),i={class:"container"},o={key:0,class:"text-center my-5"},r={key:1,class:"alert alert-danger"},s={key:2,class:"row"},l={class:"col-md-8"},c={class:"image-container position-relative"},u=["src"],d={class:"mt-3 d-flex justify-content-between"},h={class:"col-md-4"},g={class:"card"},f={class:"card-body"},p={class:"btn-group mb-3"},v=["onClick"];function m(n,t,e,m,k,y){return(0,a.uX)(),(0,a.CE)("div",i,[t[19]||(t[19]=(0,a.Lk)("h2",{class:"mt-4 mb-3"},"图像详情",-1)),k.loading?((0,a.uX)(),(0,a.CE)("div",o,t[9]||(t[9]=[(0,a.Lk)("div",{class:"spinner-border",role:"status"},[(0,a.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):k.error?((0,a.uX)(),(0,a.CE)("div",r,(0,a.v_)(k.error),1)):((0,a.uX)(),(0,a.CE)("div",s,[(0,a.Lk)("div",l,[(0,a.Lk)("div",c,[(0,a.Lk)("img",{ref:"imageElement",src:y.imageUrl,class:"img-fluid",alt:"血管瘤图像",onLoad:t[0]||(t[0]=function(){return y.initializeCanvas&&y.initializeCanvas.apply(y,arguments)})},null,40,u),(0,a.Lk)("canvas",{ref:"annotationCanvas",class:"annotation-canvas position-absolute top-0 start-0",onMousedown:t[1]||(t[1]=function(){return y.startDrawing&&y.startDrawing.apply(y,arguments)}),onMousemove:t[2]||(t[2]=function(){return y.draw&&y.draw.apply(y,arguments)}),onMouseup:t[3]||(t[3]=function(){return y.endDrawing&&y.endDrawing.apply(y,arguments)}),onMouseleave:t[4]||(t[4]=function(){return y.endDrawing&&y.endDrawing.apply(y,arguments)})},null,544)]),(0,a.Lk)("div",d,[(0,a.Lk)("div",null,[(0,a.Lk)("button",{class:"btn btn-outline-primary me-2",onClick:t[5]||(t[5]=function(){return y.clearAnnotations&&y.clearAnnotations.apply(y,arguments)})},"清除标注"),(0,a.Lk)("button",{class:"btn btn-primary",onClick:t[6]||(t[6]=function(){return y.saveAnnotations&&y.saveAnnotations.apply(y,arguments)})},"保存标注")]),(0,a.Lk)("div",null,[(0,a.Lk)("button",{class:"btn btn-secondary",onClick:t[7]||(t[7]=function(t){return n.$router.go(-1)})},"返回")])])]),(0,a.Lk)("div",h,[(0,a.Lk)("div",g,[t[18]||(t[18]=(0,a.Lk)("div",{class:"card-header"},[(0,a.Lk)("h5",{class:"card-title mb-0"},"图像信息")],-1)),(0,a.Lk)("div",f,[(0,a.Lk)("p",null,[t[10]||(t[10]=(0,a.Lk)("strong",null,"名称：",-1)),(0,a.eW)(" "+(0,a.v_)(k.image.name),1)]),(0,a.Lk)("p",null,[t[11]||(t[11]=(0,a.Lk)("strong",null,"上传者：",-1)),(0,a.eW)(" "+(0,a.v_)(k.image.uploaderName),1)]),(0,a.Lk)("p",null,[t[12]||(t[12]=(0,a.Lk)("strong",null,"上传时间：",-1)),(0,a.eW)(" "+(0,a.v_)(y.formatDate(k.image.uploadTime)),1)]),(0,a.Lk)("p",null,[t[13]||(t[13]=(0,a.Lk)("strong",null,"状态：",-1)),t[14]||(t[14]=(0,a.eW)()),(0,a.Lk)("span",{class:(0,a.C4)(y.statusClass)},(0,a.v_)(y.statusText),3)]),t[15]||(t[15]=(0,a.Lk)("hr",null,null,-1)),t[16]||(t[16]=(0,a.Lk)("h6",null,"标注工具",-1)),(0,a.Lk)("div",p,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(k.drawingTools,(function(n){return(0,a.uX)(),(0,a.CE)("button",{key:n.type,class:(0,a.C4)(["btn",k.currentTool===n.type?"btn-primary":"btn-outline-primary"]),onClick:function(t){return y.selectTool(n.type)}},[(0,a.Lk)("i",{class:(0,a.C4)(n.icon)},null,2),(0,a.eW)(" "+(0,a.v_)(n.label),1)],10,v)})),128))]),t[17]||(t[17]=(0,a.Lk)("h6",null,"注释",-1)),(0,a.bo)((0,a.Lk)("textarea",{"onUpdate:modelValue":t[8]||(t[8]=function(n){return k.notes=n}),class:"form-control mb-3",rows:"3",placeholder:"添加标注说明..."},null,512),[[a.Jo,k.notes]])])])])]))])}var k=e(24059),y=e(698),w=(e(28706),e(51629),e(44114),e(60739),e(23288),e(18111),e(7588),e(33110),e(79432),e(26099),e(23500),e(72505)),b=e.n(w);const A={name:"ImageDetail",data:function(){return{image:{},loading:!0,error:null,annotations:[],currentAnnotation:null,isDrawing:!1,currentTool:"freehand",notes:"",drawingTools:[{type:"freehand",label:"自由绘制",icon:"bi bi-pencil"},{type:"rectangle",label:"矩形",icon:"bi bi-square"},{type:"circle",label:"圆形",icon:"bi bi-circle"}]}},computed:{imageId:function(){return this.$route.params.id},imageUrl:function(){return"".concat("","/api/images/").concat(this.imageId,"/file")},statusText:function(){var n={UPLOADED:"已上传",ANNOTATED:"已标注",REVIEWED:"已审核",APPROVED:"已批准",REJECTED:"已拒绝"};return n[this.image.status]||this.image.status},statusClass:function(){var n={UPLOADED:"text-secondary",ANNOTATED:"text-primary",REVIEWED:"text-info",APPROVED:"text-success",REJECTED:"text-danger"};return n[this.image.status]||""}},mounted:function(){this.fetchImageData(),window.addEventListener("resize",this.initializeCanvas)},beforeUnmount:function(){window.removeEventListener("resize",this.initializeCanvas)},methods:{fetchImageData:function(){var n=this;return(0,y.A)((0,k.A)().m((function t(){var e,a,i;return(0,k.A)().w((function(t){while(1)switch(t.n){case 0:return n.loading=!0,t.p=1,t.n=2,b().get("".concat("","/api/images/").concat(n.imageId));case 2:e=t.v,n.image=e.data,n.image.annotationData&&(n.annotations=JSON.parse(n.image.annotationData),n.notes=n.image.notes||""),t.n=4;break;case 3:t.p=3,i=t.v,n.error="加载图像详情失败: "+((null===(a=i.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||i.message);case 4:return t.p=4,n.loading=!1,t.f(4);case 5:return t.a(2)}}),t,null,[[1,3,4,5]])})))()},formatDate:function(n){if(!n)return"";var t=new Date(n);return t.toLocaleString("zh-CN")},initializeCanvas:function(){var n=this.$refs.imageElement,t=this.$refs.annotationCanvas;n&&t&&(t.width=n.clientWidth,t.height=n.clientHeight,this.redrawAnnotations())},redrawAnnotations:function(){var n=this,t=this.$refs.annotationCanvas;if(t){var e=t.getContext("2d");e.clearRect(0,0,t.width,t.height),this.annotations.forEach((function(t){n.drawAnnotation(e,t)}))}},drawAnnotation:function(n,t){if(n.strokeStyle="#FF0000",n.lineWidth=2,"freehand"===t.type)n.beginPath(),t.points.forEach((function(t,e){0===e?n.moveTo(t.x,t.y):n.lineTo(t.x,t.y)})),n.stroke();else if("rectangle"===t.type){var e=t.end.x-t.start.x,a=t.end.y-t.start.y;n.strokeRect(t.start.x,t.start.y,e,a)}else if("circle"===t.type){var i=Math.sqrt(Math.pow(t.end.x-t.start.x,2)+Math.pow(t.end.y-t.start.y,2));n.beginPath(),n.arc(t.start.x,t.start.y,i,0,2*Math.PI),n.stroke()}},startDrawing:function(n){this.isDrawing=!0;var t=this.$refs.annotationCanvas,e=t.getBoundingClientRect(),a=n.clientX-e.left,i=n.clientY-e.top;"freehand"===this.currentTool?this.currentAnnotation={type:"freehand",points:[{x:a,y:i}]}:"rectangle"!==this.currentTool&&"circle"!==this.currentTool||(this.currentAnnotation={type:this.currentTool,start:{x:a,y:i},end:{x:a,y:i}})},draw:function(n){if(this.isDrawing&&this.currentAnnotation){var t=this.$refs.annotationCanvas,e=t.getContext("2d"),a=t.getBoundingClientRect(),i=n.clientX-a.left,o=n.clientY-a.top;if("freehand"===this.currentTool){this.currentAnnotation.points.push({x:i,y:o}),e.strokeStyle="#FF0000",e.lineWidth=2,e.beginPath();var r=this.currentAnnotation.points,s=r[r.length-2],l=r[r.length-1];e.moveTo(s.x,s.y),e.lineTo(l.x,l.y),e.stroke()}else this.currentAnnotation.end={x:i,y:o},this.redrawAnnotations(),this.drawAnnotation(e,this.currentAnnotation)}},endDrawing:function(){this.isDrawing&&this.currentAnnotation&&(this.annotations.push(this.currentAnnotation),this.currentAnnotation=null,this.isDrawing=!1)},clearAnnotations:function(){if(confirm("确定要清除所有标注吗？")){this.annotations=[];var n=this.$refs.annotationCanvas,t=n.getContext("2d");t.clearRect(0,0,n.width,n.height)}},saveAnnotations:function(){var n=this;return(0,y.A)((0,k.A)().m((function t(){var e,a,i;return(0,k.A)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,e=JSON.stringify(n.annotations),t.n=1,b().post("".concat("","/api/images/").concat(n.imageId,"/annotate"),{annotationData:e,notes:n.notes});case 1:n.$store.dispatch("showAlert",{type:"success",message:"标注保存成功"}),t.n=3;break;case 2:t.p=2,i=t.v,n.$store.dispatch("showAlert",{type:"error",message:"保存标注失败: "+((null===(a=i.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||i.message)});case 3:return t.a(2)}}),t,null,[[0,2]])})))()},selectTool:function(n){this.currentTool=n}}};var L=e(66262);const C=(0,L.A)(A,[["render",m],["__scopeId","data-v-04771bcf"]]),D=C}}]);
//# sourceMappingURL=453.aad7fb15.js.map