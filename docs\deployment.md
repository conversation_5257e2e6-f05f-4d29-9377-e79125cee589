# 血管瘤AI智能诊断平台 - 部署指南

## 📋 目录
- [部署方式概览](#部署方式概览)
- [Docker容器化部署](#docker容器化部署)
- [传统部署方式](#传统部署方式)
- [生产环境配置](#生产环境配置)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 🚀 部署方式概览

本项目支持两种主要的部署方式：

1. **Docker容器化部署** (推荐)
   - 环境一致性好
   - 部署简单快速
   - 易于扩展和维护

2. **传统部署方式**
   - 直接在服务器上部署
   - 适合特殊环境要求
   - 性能调优更灵活

## 🐳 Docker容器化部署

### 环境要求
- **Docker**: 20.10+ 
- **Docker Compose**: 2.0+
- **系统内存**: 8GB+ (推荐16GB)
- **磁盘空间**: 50GB+ (用于模型和数据存储)
- **CPU**: 4核+ (推荐8核)

### 快速部署

#### 1. 准备部署环境
```bash
# 克隆项目
git clone <repository-url>
cd xueguan2

# 创建必要的目录
mkdir -p medical_images/{original,processed,annotated}
mkdir -p logs
mkdir -p resources/models
```

#### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
vim .env
```

**.env 文件示例：**
```env
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_DATABASE=hemangioma_diagnosis
MYSQL_USER=hemangioma_user
MYSQL_PASSWORD=your_db_password

# 应用配置
SPRING_PROFILES_ACTIVE=docker
AI_SERVICE_URL=http://ai-service:8086
JAVA_CALLBACK_URL=http://backend:8085/medical/api/hemangioma-diagnoses/update-recommendation

# LLM配置
LLM_MODEL_NAME=deepseek-r1:8b
OLLAMA_HOST=http://ollama:11434

# 文件上传限制
MAX_FILE_SIZE=100MB
```

#### 3. 启动服务
```bash
# 使用脚本一键部署
./scripts/deploy-docker.bat

# 或手动执行
docker-compose up -d --build
```

#### 4. 初始化Ollama模型
```bash
# 等待Ollama服务启动
sleep 60

# 下载LLM模型
docker-compose exec ollama ollama pull deepseek-r1:8b
```

#### 5. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 检查服务健康状态
curl http://localhost/health
curl http://localhost:8085/actuator/health
curl http://localhost:8086/health
```

### 服务访问地址
- **前端应用**: http://localhost
- **后端API**: http://localhost/medical/
- **AI服务**: http://localhost/ai-api/
- **数据库**: localhost:3306

## 🖥 传统部署方式

### 环境要求
- **Java**: OpenJDK 8+
- **Node.js**: 16+
- **Python**: 3.8+
- **MySQL**: 8.0+
- **Nginx**: 1.18+

### 部署步骤

#### 1. 数据库部署
```bash
# 安装MySQL
sudo apt update
sudo apt install mysql-server

# 创建数据库和用户
mysql -u root -p
CREATE DATABASE hemangioma_diagnosis DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hemangioma_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON hemangioma_diagnosis.* TO 'hemangioma_user'@'localhost';
FLUSH PRIVILEGES;

# 导入初始化脚本
mysql -u hemangioma_user -p hemangioma_diagnosis < mysql/init.sql
```

#### 2. 后端部署
```bash
# 构建应用
mvn clean package -DskipTests

# 创建服务目录
sudo mkdir -p /opt/hemangioma/backend
sudo cp target/annotation-*.jar /opt/hemangioma/backend/app.jar

# 创建配置文件
sudo cp config/application-prod.properties /opt/hemangioma/backend/

# 创建systemd服务
sudo tee /etc/systemd/system/hemangioma-backend.service > /dev/null <<EOF
[Unit]
Description=Hemangioma Backend Service
After=network.target mysql.service

[Service]
Type=simple
User=hemangioma
WorkingDirectory=/opt/hemangioma/backend
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod app.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable hemangioma-backend
sudo systemctl start hemangioma-backend
```

#### 3. AI服务部署
```bash
# 创建Python虚拟环境
python3 -m venv /opt/hemangioma/ai-service/venv
source /opt/hemangioma/ai-service/venv/bin/activate

# 安装依赖
pip install -r ai-service/requirements.txt

# 复制服务文件
sudo cp -r ai-service /opt/hemangioma/

# 创建systemd服务
sudo tee /etc/systemd/system/hemangioma-ai.service > /dev/null <<EOF
[Unit]
Description=Hemangioma AI Service
After=network.target

[Service]
Type=simple
User=hemangioma
WorkingDirectory=/opt/hemangioma/ai-service
Environment=PATH=/opt/hemangioma/ai-service/venv/bin
ExecStart=/opt/hemangioma/ai-service/venv/bin/python ai_service.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl enable hemangioma-ai
sudo systemctl start hemangioma-ai
```

#### 4. 前端部署
```bash
# 构建前端
cd frontend
npm install
npm run build

# 部署到Nginx
sudo cp -r dist/* /var/www/html/hemangioma/

# 配置Nginx
sudo cp config/nginx.conf /etc/nginx/sites-available/hemangioma
sudo ln -s /etc/nginx/sites-available/hemangioma /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔧 生产环境配置

### 安全配置

#### 1. 数据库安全
```sql
-- 创建只读用户
CREATE USER 'hemangioma_readonly'@'localhost' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON hemangioma_diagnosis.* TO 'hemangioma_readonly'@'localhost';

-- 设置连接限制
SET GLOBAL max_connections = 200;
SET GLOBAL max_user_connections = 50;
```

#### 2. 应用安全
```properties
# application-prod.properties
# 禁用开发工具
spring.devtools.add-properties=false

# 安全配置
server.error.include-stacktrace=never
server.error.include-message=never

# 日志配置
logging.level.root=WARN
logging.level.com.medical=INFO
```

#### 3. Nginx安全配置
```nginx
# 隐藏版本信息
server_tokens off;

# 安全头
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

# 限制请求大小
client_max_body_size 100M;

# 限制连接数
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
limit_conn conn_limit_per_ip 20;
```

### 性能优化

#### 1. JVM调优
```bash
# 启动参数
JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError"
```

#### 2. 数据库优化
```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = **********;  -- 2GB
SET GLOBAL innodb_log_file_size = 268435456;      -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864;           -- 64MB
```

#### 3. Nginx优化
```nginx
# 工作进程数
worker_processes auto;

# 连接数
worker_connections 1024;

# 缓存配置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g 
                 inactive=60m use_temp_path=off;
```

## 📊 监控和维护

### 健康检查
```bash
# 服务状态检查脚本
#!/bin/bash
echo "=== 服务健康检查 ==="

# 检查后端服务
if curl -f http://localhost:8085/actuator/health > /dev/null 2>&1; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常"
fi

# 检查AI服务
if curl -f http://localhost:8086/health > /dev/null 2>&1; then
    echo "✅ AI服务正常"
else
    echo "❌ AI服务异常"
fi

# 检查数据库
if mysqladmin ping -h localhost -u hemangioma_user -p > /dev/null 2>&1; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接异常"
fi
```

### 日志管理
```bash
# 日志轮转配置
sudo tee /etc/logrotate.d/hemangioma > /dev/null <<EOF
/opt/hemangioma/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 hemangioma hemangioma
    postrotate
        systemctl reload hemangioma-backend
        systemctl reload hemangioma-ai
    endscript
}
EOF
```

### 备份策略
```bash
# 数据库备份脚本
#!/bin/bash
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

mysqldump -u hemangioma_user -p hemangioma_diagnosis > $BACKUP_DIR/hemangioma_$DATE.sql

# 保留最近30天的备份
find $BACKUP_DIR -name "hemangioma_*.sql" -mtime +30 -delete
```

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 查看服务状态
systemctl status hemangioma-backend
systemctl status hemangioma-ai

# 查看日志
journalctl -u hemangioma-backend -f
journalctl -u hemangioma-ai -f
```

#### 2. 数据库连接问题
```bash
# 检查数据库状态
systemctl status mysql

# 测试连接
mysql -u hemangioma_user -p -h localhost

# 检查防火墙
sudo ufw status
```

#### 3. AI模型加载失败
```bash
# 检查模型文件
ls -la /opt/hemangioma/resources/models/

# 检查Python环境
source /opt/hemangioma/ai-service/venv/bin/activate
python -c "import torch; print(torch.__version__)"
```

#### 4. 内存不足
```bash
# 检查内存使用
free -h
top -p $(pgrep java)

# 调整JVM参数
vim /etc/systemd/system/hemangioma-backend.service
# 修改 ExecStart 行，添加内存参数
```

### 性能监控
```bash
# 系统资源监控
htop
iotop
nethogs

# 应用监控
curl http://localhost:8085/actuator/metrics
curl http://localhost:8085/actuator/prometheus
```

---

**更新时间**: 2024-01-01  
**版本**: V2.0
