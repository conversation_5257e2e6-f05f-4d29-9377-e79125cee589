"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[63],{37620:(e,t,a)=>{a.d(t,{VG:()=>i});a(54119),a(28706),a(74423),a(8921),a(15086),a(26099),a(27495),a(99449),a(21699),a(71761),a(90744),a(11392);var n="http://localhost:8085";function i(e){if(!e)return"";var t=String(e);if(t.startsWith("http://")||t.startsWith("https://"))return t;if(t.startsWith("data:"))return t;if(t.includes("medical_images")){var a=/medical_images[\/\\]([^\/\\]+)(?:[\/\\](.*))?/,i=t.match(a);if(i){var s=i[1],o=i[2]||"",r="/medical/images/".concat(s,"/").concat(o),l=n+r;return l}}if(t.startsWith("/medical/"))return n+t;if(t.match(/^[a-zA-Z]:\\/)){var c=t.split(/[\/\\]/).pop(),d="";t.includes("\\original\\")||t.includes("/original/")?d="original":t.includes("\\processed\\")||t.includes("/processed/")?d="processed":t.includes("\\annotate\\")||t.includes("/annotate/")?d="annotate":(t.includes("\\temp\\")||t.includes("/temp/"))&&(d="temp");var g="/medical/images/".concat(d,"/").concat(c);return n+g}var u=t.startsWith("/")?t:"/"+t;u.startsWith("/medical")||(u="/medical"+u);try{var h=u.lastIndexOf("/");if(-1!==h){var m=u.substring(0,h+1),f=u.substring(h+1);u=m+encodeURIComponent(f)}}catch(v){}return n+u}},47063:(e,t,a)=>{a.r(t),a.d(t,{default:()=>H});var n=a(20641),i=a(90033),s={class:"annotation-page"},o={class:"page-header"},r={class:"header-actions"},l={class:"main-content"},c={class:"image-selector"},d={key:0,class:"image-metadata"},g={key:0},u={key:1},h={class:"annotation-container"},m={key:1,class:"no-image-placeholder"},f={key:0,class:"progress-info"},v={class:"dialog-footer"};function p(e,t,a,p,I,b){var k=(0,n.g2)("el-button"),w=(0,n.g2)("el-option"),_=(0,n.g2)("el-select"),y=(0,n.g2)("ImageAnnotator"),C=(0,n.g2)("el-empty"),D=(0,n.g2)("ConfirmDialog"),S=(0,n.g2)("el-dialog");return(0,n.uX)(),(0,n.CE)("div",s,[(0,n.Lk)("div",o,[t[4]||(t[4]=(0,n.Lk)("div",{class:"header-left"},[(0,n.Lk)("h2",null,"图像标注系统"),(0,n.Lk)("p",null,"在图像上绘制矩形区域，添加标签信息以标注病变区域")],-1)),(0,n.Lk)("div",r,[(0,n.bF)(k,{type:"primary",onClick:b.nextStep},{default:(0,n.k6)((function(){return t[2]||(t[2]=[(0,n.eW)("下一步")])})),_:1,__:[2]},8,["onClick"]),(0,n.bF)(k,{onClick:b.showSaveDialog},{default:(0,n.k6)((function(){return t[3]||(t[3]=[(0,n.eW)("保存并退出")])})),_:1,__:[3]},8,["onClick"])])]),(0,n.Lk)("div",l,[(0,n.Lk)("div",c,[t[11]||(t[11]=(0,n.Lk)("h3",null,"选择图像",-1)),(0,n.bF)(_,{modelValue:I.selectedImageId,"onUpdate:modelValue":t[0]||(t[0]=function(e){return I.selectedImageId=e}),placeholder:"请选择图像",onChange:b.loadSelectedImage,style:{width:"100%"}},{default:(0,n.k6)((function(){return[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(I.availableImages,(function(e){return(0,n.uX)(),(0,n.Wv)(w,{key:e.id,label:e.original_name||"图像 #".concat(e.id),value:e.id},null,8,["label","value"])})),128))]})),_:1},8,["modelValue","onChange"]),I.selectedImage?((0,n.uX)(),(0,n.CE)("div",d,[t[10]||(t[10]=(0,n.Lk)("h4",null,"图像信息",-1)),(0,n.Lk)("p",null,[t[5]||(t[5]=(0,n.Lk)("strong",null,"ID:",-1)),(0,n.eW)(" "+(0,i.v_)(I.selectedImage.id),1)]),(0,n.Lk)("p",null,[t[6]||(t[6]=(0,n.Lk)("strong",null,"原始文件名:",-1)),(0,n.eW)(" "+(0,i.v_)(I.selectedImage.original_name),1)]),(0,n.Lk)("p",null,[t[7]||(t[7]=(0,n.Lk)("strong",null,"上传时间:",-1)),(0,n.eW)(" "+(0,i.v_)(b.formatDate(I.selectedImage.created_at)),1)]),I.selectedImage.patient_name?((0,n.uX)(),(0,n.CE)("p",g,[t[8]||(t[8]=(0,n.Lk)("strong",null,"患者:",-1)),(0,n.eW)(" "+(0,i.v_)(I.selectedImage.patient_name),1)])):(0,n.Q3)("",!0),I.selectedImage.case_number?((0,n.uX)(),(0,n.CE)("p",u,[t[9]||(t[9]=(0,n.Lk)("strong",null,"病例号:",-1)),(0,n.eW)(" "+(0,i.v_)(I.selectedImage.case_number),1)])):(0,n.Q3)("",!0)])):(0,n.Q3)("",!0)]),(0,n.Lk)("div",h,[I.selectedImage?((0,n.uX)(),(0,n.Wv)(y,{key:0,imageUrl:b.getImageUrl(I.selectedImage.path),imageId:I.selectedImage.id,onAnnotationsSaved:b.onAnnotationsSaved,ref:"annotator"},null,8,["imageUrl","imageId","onAnnotationsSaved"])):((0,n.uX)(),(0,n.CE)("div",m,[(0,n.bF)(C,{description:"请选择一张图像进行标注"})]))])]),(0,n.bF)(D,{modelValue:I.saveDialogVisible,"onUpdate:modelValue":t[1]||(t[1]=function(e){return I.saveDialogVisible=e}),title:"保存并退出",message:"是否保留当前标注进度？下次可以继续完成","confirm-text":"保留进度","cancel-text":"不保留",icon:"warning",onConfirm:b.handleSaveProgress,onCancel:b.handleDiscardProgress},null,8,["modelValue","onConfirm","onCancel"]),(0,n.bF)(S,{title:"提示",visible:I.resumeDialogVisible,width:"30%"},{footer:(0,n.k6)((function(){return[(0,n.Lk)("span",v,[(0,n.bF)(k,{onClick:b.discardSavedProgress},{default:(0,n.k6)((function(){return t[12]||(t[12]=[(0,n.eW)("不需要，重新开始")])})),_:1,__:[12]},8,["onClick"]),(0,n.bF)(k,{type:"primary",onClick:b.resumeSavedProgress},{default:(0,n.k6)((function(){return t[13]||(t[13]=[(0,n.eW)("继续上次任务")])})),_:1,__:[13]},8,["onClick"])])]})),default:(0,n.k6)((function(){return[t[14]||(t[14]=(0,n.Lk)("p",null,"检测到您有未完成的标注任务，是否继续？",-1)),b.savedProgress?((0,n.uX)(),(0,n.CE)("p",f," 上次保存于："+(0,i.v_)(b.formatDate(b.savedProgress.lastUpdated)),1)):(0,n.Q3)("",!0)]})),_:1,__:[14]},8,["visible"])])}var I=a(41034),b=(a(44114),a(79432),a(58940),a(27495),a(25440),a(9868),{class:"image-annotator"}),k={class:"annotation-toolbar"},w={class:"tool-section"},_={class:"tool-section"},y={class:"annotation-workspace"},C={class:"image-container",ref:"imageContainer"},D=["src"],S={class:"tag-label"},A={class:"annotation-list"},x={class:"actions"};function L(e,t,a,s,o,r){var l=(0,n.g2)("el-option"),c=(0,n.g2)("el-select"),d=(0,n.g2)("el-radio"),g=(0,n.g2)("el-radio-group"),u=(0,n.g2)("el-table-column"),h=(0,n.g2)("el-button"),m=(0,n.g2)("el-table");return(0,n.uX)(),(0,n.CE)("div",b,[(0,n.Lk)("div",k,[(0,n.Lk)("div",w,[t[7]||(t[7]=(0,n.Lk)("h3",null,"标签分类",-1)),(0,n.bF)(c,{modelValue:o.selectedTag,"onUpdate:modelValue":t[0]||(t[0]=function(e){return o.selectedTag=e}),placeholder:"请选择标签类型",style:{width:"100%"}},{default:(0,n.k6)((function(){return[(0,n.bF)(l,{label:"血管瘤",value:"血管瘤"}),(0,n.bF)(l,{label:"淋巴管瘤",value:"淋巴管瘤"}),(0,n.bF)(l,{label:"混合型",value:"混合型"}),(0,n.bF)(l,{label:"其他",value:"其他"})]})),_:1},8,["modelValue"])]),(0,n.Lk)("div",_,[t[9]||(t[9]=(0,n.Lk)("h3",null,"标注工具",-1)),(0,n.bF)(g,{modelValue:o.currentTool,"onUpdate:modelValue":t[1]||(t[1]=function(e){return o.currentTool=e})},{default:(0,n.k6)((function(){return[(0,n.bF)(d,{label:"rectangle"},{default:(0,n.k6)((function(){return t[8]||(t[8]=[(0,n.eW)("矩形框")])})),_:1,__:[8]})]})),_:1},8,["modelValue"])])]),(0,n.Lk)("div",y,[(0,n.Lk)("div",C,[a.imageUrl?((0,n.uX)(),(0,n.CE)("img",{key:0,src:a.imageUrl,ref:"annotationImage",class:"annotation-image",onLoad:t[2]||(t[2]=function(){return r.onImageLoad&&r.onImageLoad.apply(r,arguments)}),alt:"医学影像"},null,40,D)):(0,n.Q3)("",!0),a.imageUrl?((0,n.uX)(),(0,n.CE)("div",{key:1,class:"annotation-overlay",style:(0,i.Tr)({width:o.imageWidth+"px",height:o.imageHeight+"px"}),onMousedown:t[3]||(t[3]=function(){return r.startDrawing&&r.startDrawing.apply(r,arguments)}),onMousemove:t[4]||(t[4]=function(){return r.drawing&&r.drawing.apply(r,arguments)}),onMouseup:t[5]||(t[5]=function(){return r.endDrawing&&r.endDrawing.apply(r,arguments)}),onMouseleave:t[6]||(t[6]=function(){return r.cancelDrawing&&r.cancelDrawing.apply(r,arguments)})},null,36)):(0,n.Q3)("",!0),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(o.annotations,(function(e,t){return(0,n.uX)(),(0,n.CE)("div",{key:t,class:"annotation-box",style:(0,i.Tr)({left:(e.x-e.width/2)*o.imageWidth+"px",top:(e.y-e.height/2)*o.imageHeight+"px",width:e.width*o.imageWidth+"px",height:e.height*o.imageHeight+"px",borderColor:r.getTagColor(e.tag)})},[(0,n.Lk)("div",S,(0,i.v_)(e.tag),1)],4)})),128)),o.isDrawing?((0,n.uX)(),(0,n.CE)("div",{key:2,class:"annotation-box drawing",style:(0,i.Tr)({left:Math.min(o.startX,o.currentX)+"px",top:Math.min(o.startY,o.currentY)+"px",width:Math.abs(o.currentX-o.startX)+"px",height:Math.abs(o.currentY-o.startY)+"px"})},null,4)):(0,n.Q3)("",!0)],512)]),(0,n.Lk)("div",A,[t[13]||(t[13]=(0,n.Lk)("h3",null,"已添加标注",-1)),(0,n.bF)(m,{data:o.annotations,style:{width:"100%"}},{default:(0,n.k6)((function(){return[(0,n.bF)(u,{label:"编号",width:"60"},{default:(0,n.k6)((function(e){return[(0,n.eW)((0,i.v_)(e.$index+1),1)]})),_:1}),(0,n.bF)(u,{prop:"tag",label:"标签",width:"90"}),(0,n.bF)(u,{label:"位置",width:"160"},{default:(0,n.k6)((function(e){return[(0,n.eW)(" 中心点: ("+(0,i.v_)((100*e.row.x).toFixed(1))+"%, "+(0,i.v_)((100*e.row.y).toFixed(1))+"%) ",1)]})),_:1}),(0,n.bF)(u,{label:"尺寸",width:"160"},{default:(0,n.k6)((function(e){return[(0,n.eW)((0,i.v_)((100*e.row.width).toFixed(1))+"% × "+(0,i.v_)((100*e.row.height).toFixed(1))+"% ",1)]})),_:1}),(0,n.bF)(u,{label:"操作",width:"80"},{default:(0,n.k6)((function(e){return[(0,n.bF)(h,{type:"danger",size:"small",onClick:function(t){return r.deleteAnnotation(e.$index)}},{default:(0,n.k6)((function(){return t[10]||(t[10]=[(0,n.eW)("删除")])})),_:2,__:[10]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"]),(0,n.Lk)("div",x,[(0,n.bF)(h,{type:"primary",onClick:r.saveAnnotations,disabled:0===o.annotations.length},{default:(0,n.k6)((function(){return t[11]||(t[11]=[(0,n.eW)(" 保存标注 ")])})),_:1,__:[11]},8,["onClick","disabled"]),(0,n.bF)(h,{type:"danger",onClick:r.clearAnnotations,disabled:0===o.annotations.length},{default:(0,n.k6)((function(){return t[12]||(t[12]=[(0,n.eW)(" 清除所有 ")])})),_:1,__:[12]},8,["onClick","disabled"])])])])}a(2008),a(51629),a(62062),a(54554),a(18111),a(22489),a(7588),a(61701),a(2892),a(26099),a(47764),a(23500),a(62953);var W=a(653);const $={name:"ImageAnnotator",props:{imageUrl:{type:String,required:!0},imageId:{type:Number,required:!0}},data:function(){return{selectedTag:"血管瘤",currentTool:"rectangle",annotations:[],isDrawing:!1,startX:0,startY:0,currentX:0,currentY:0,imageWidth:0,imageHeight:0,savedAnnotations:[]}},mounted:function(){this.fetchSavedAnnotations()},methods:{onImageLoad:function(){var e=this.$refs.annotationImage;e?(this.imageWidth=e.naturalWidth||e.width,this.imageHeight=e.naturalHeight||e.height,this.imageWidth&&this.imageHeight||this.$message.warning("无法获取图像尺寸，标注功能可能无法正常工作")):this.$message.error("图像加载失败")},startDrawing:function(e){if(this.selectedTag){this.isDrawing=!0;var t=this.$refs.imageContainer.getBoundingClientRect();this.startX=e.clientX-t.left,this.startY=e.clientY-t.top,this.currentX=this.startX,this.currentY=this.startY}else this.$message.warning("请先选择标签类型")},drawing:function(e){if(this.isDrawing){var t=this.$refs.imageContainer.getBoundingClientRect();this.currentX=e.clientX-t.left,this.currentY=e.clientY-t.top}},endDrawing:function(){if(this.isDrawing){var e=Math.min(this.startX,this.currentX),t=Math.min(this.startY,this.currentY),a=Math.abs(this.currentX-this.startX),n=Math.abs(this.currentY-this.startY);if(a<5||n<5)this.isDrawing=!1;else{if(!this.imageWidth||!this.imageHeight)return this.$message.error("无法获取图像尺寸，请刷新页面重试"),void(this.isDrawing=!1);var i=(e+a/2)/this.imageWidth,s=(t+n/2)/this.imageHeight,o=a/this.imageWidth,r=n/this.imageHeight;i=Math.max(0,Math.min(1,i)),s=Math.max(0,Math.min(1,s)),o=Math.max(.001,Math.min(1,o)),r=Math.max(.001,Math.min(1,r)),this.annotations.push({tag:this.selectedTag,x:i,y:s,width:o,height:r}),this.isDrawing=!1}}},cancelDrawing:function(){this.isDrawing=!1},deleteAnnotation:function(e){this.annotations.splice(e,1)},clearAnnotations:function(){var e=this;this.$confirm("确定要清除所有标注吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.annotations=[]}))["catch"]((function(){}))},saveAnnotations:function(){var e=this;if(0!==this.annotations.length){var t=JSON.parse(localStorage.getItem("user"));if(t){var a=this.annotations.filter((function(e){var t=e.x>=0&&e.x<=1,a=e.y>=0&&e.y<=1,n=e.width>0&&e.width<=1,i=e.height>0&&e.height<=1;return t&&a&&n&&i}));if(a.length!==this.annotations.length){var n=this.annotations.length-a.length;this.$message.warning("检测到 ".concat(n," 个标注坐标无效，已自动过滤")),this.annotations=a}if(0!==a.length){var i=a.map((function(a){var n=Math.max(0,Math.min(1,a.x)),i=Math.max(0,Math.min(1,a.y)),s=Math.max(.001,Math.min(1,a.width)),o=Math.max(.001,Math.min(1,a.height)),r={metadata_id:e.imageId,tag:a.tag,x:n,y:i,width:s,height:o,created_by:t.id};return W["default"].tags.create(r)}));Promise.all(i).then((function(t){e.$message.success("成功保存 ".concat(t.length," 个标注")),e.fetchSavedAnnotations(),e.$emit("annotations-saved",e.annotations)}))["catch"]((function(t){t.response&&t.response.data?e.$message.error("保存标注失败: "+t.response.data):e.$message.error("保存标注失败: "+t.message)}))}else this.$message.error("没有有效的标注可保存")}else this.$message.error("未检测到用户信息，无法保存标注")}else this.$message.warning("没有标注可保存")},fetchSavedAnnotations:function(){var e=this;W["default"].tags.getByImageId(this.imageId).then((function(t){e.savedAnnotations=t.data,e.savedAnnotations&&e.savedAnnotations.length>0&&(e.annotations=[],e.savedAnnotations.forEach((function(t){e.annotations.push({id:t.id,tag:t.tag,x:t.x,y:t.y,width:t.width,height:t.height})})))}))["catch"]((function(e){}))},getTagColor:function(e){var t={血管瘤:"#ff0000",淋巴管瘤:"#00ff00",混合型:"#0000ff",其他:"#ff00ff"};return t[e]||"#ff0000"}}};var F=a(66262);const X=(0,F.A)($,[["render",L],["__scopeId","data-v-b0ecb562"]]),M=X;var V=a(60424),P=a(49586),T=a(37620),Y=a(40834);const U={name:"ImageAnnotationPage",components:{ImageAnnotator:M,ConfirmDialog:V.A},data:function(){return{availableImages:[],selectedImageId:null,selectedImage:null,loading:!1,error:null,annotations:[],saveDialogVisible:!1,resumeDialogVisible:!1}},computed:(0,I.A)((0,I.A)({},(0,Y.L8)(["getAnnotationProgress","hasUnfinishedAnnotation"])),{},{savedProgress:function(){return this.getAnnotationProgress}}),created:function(){if(this.hasUnfinishedAnnotation)this.resumeDialogVisible=!0;else{var e=this.$route.query.imageId;e?(this.selectedImageId=parseInt(e),this.fetchImageDetails(this.selectedImageId)):this.fetchAvailableImages()}},methods:(0,I.A)((0,I.A)({},(0,Y.i0)(["saveProgress","completeAnnotation"])),{},{formatDate:P.Yq,getImageUrl:T.VG,fetchImageDetails:function(e){var t=this;e&&(this.loading=!0,W["default"].images.getOne(e).then((function(a){t.selectedImage=a.data,t.saveProgress({step:1,imageId:e,formData:null}),t.loading=!1}))["catch"]((function(e){var a;t.error="加载图像详情失败: "+((null===(a=e.response)||void 0===a?void 0:a.data)||e.message),t.loading=!1,t.$message.error("加载图像详情失败")})))},fetchAvailableImages:function(){var e=this;this.loading=!0,W["default"].images.getAll().then((function(t){e.availableImages=t.data,e.loading=!1,e.selectedImageId&&e.loadSelectedImage()}))["catch"]((function(t){var a;e.error="加载图像列表失败: "+((null===(a=t.response)||void 0===a?void 0:a.data)||t.message),e.loading=!1}))},loadSelectedImage:function(){this.selectedImageId?this.fetchImageDetails(this.selectedImageId):this.selectedImage=null},onAnnotationsSaved:function(e){this.annotations=e,this.$message.success("已成功保存 ".concat(e.length," 个标注"))},nextStep:function(){this.selectedImageId&&this.selectedImage?(this.$refs.annotator&&"function"===typeof this.$refs.annotator.saveAnnotations&&this.$refs.annotator.saveAnnotations(),this.saveProgress({step:2,imageId:this.selectedImageId,formData:null}),this.$router.push({path:"/cases/structured-form",query:{imageId:this.selectedImageId}})):this.$message.warning("请先选择一个图像")},showSaveDialog:function(){this.selectedImageId&&this.selectedImage?(this.$refs.annotator&&"function"===typeof this.$refs.annotator.saveAnnotations&&this.$refs.annotator.saveAnnotations(),this.saveDialogVisible=!0):this.$router.push("/cases/new")},handleSaveProgress:function(){this.saveProgress({step:1,imageId:this.selectedImageId,formData:null});try{var e=JSON.parse(localStorage.getItem("user"));e&&sessionStorage.setItem("preservedUser",e)}catch(t){}this.$message.success("已保存标注进度"),window.location.replace("/app/dashboard")},handleDiscardProgress:function(){this.completeAnnotation();try{var e=JSON.parse(localStorage.getItem("user"));e&&sessionStorage.setItem("preservedUser",e)}catch(t){}this.$message.info("已清除标注进度"),window.location.replace("/app/dashboard")},resumeSavedProgress:function(){this.savedProgress&&this.savedProgress.imageId?(this.selectedImageId=this.savedProgress.imageId,this.fetchImageDetails(this.selectedImageId),this.resumeDialogVisible=!1):this.resumeDialogVisible=!1},discardSavedProgress:function(){this.completeAnnotation();var e=this.$route.query.imageId;e?(this.selectedImageId=parseInt(e),this.fetchImageDetails(this.selectedImageId)):this.fetchAvailableImages(),this.resumeDialogVisible=!1}})},E=(0,F.A)(U,[["render",p],["__scopeId","data-v-2abde6ae"]]),H=E},49586:(e,t,a)=>{a.d(t,{Yq:()=>n});a(28706),a(23288),a(9868),a(68156);function n(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"";var a="string"===typeof e?new Date(e):e;if(isNaN(a.getTime()))return"";var n=a.getFullYear(),i=String(a.getMonth()+1).padStart(2,"0"),s=String(a.getDate()).padStart(2,"0"),o="".concat(n,"-").concat(i,"-").concat(s);if(t){var r=String(a.getHours()).padStart(2,"0"),l=String(a.getMinutes()).padStart(2,"0"),c=String(a.getSeconds()).padStart(2,"0");return"".concat(o," ").concat(r,":").concat(l,":").concat(c)}return o}},60424:(e,t,a)=>{a.d(t,{A:()=>h});var n=a(20641),i=a(90033),s={class:"dialog-content"},o={key:0,class:"dialog-icon"},r={class:"dialog-message"},l={class:"dialog-footer"};function c(e,t,a,c,d,g){var u=(0,n.g2)("el-button"),h=(0,n.g2)("el-dialog");return(0,n.uX)(),(0,n.Wv)(h,{title:a.title,visible:g.dialogVisible,width:a.width,"before-close":g.handleClose},{footer:(0,n.k6)((function(){return[(0,n.Lk)("span",l,[(0,n.bF)(u,{onClick:g.handleCancel},{default:(0,n.k6)((function(){return[(0,n.eW)((0,i.v_)(a.cancelText),1)]})),_:1},8,["onClick"]),(0,n.bF)(u,{type:a.confirmType,onClick:g.handleConfirm},{default:(0,n.k6)((function(){return[(0,n.eW)((0,i.v_)(a.confirmText),1)]})),_:1},8,["type","onClick"])])]})),default:(0,n.k6)((function(){return[(0,n.Lk)("div",s,[a.icon?((0,n.uX)(),(0,n.CE)("div",o,[(0,n.Lk)("i",{class:(0,i.C4)(g.iconClass)},null,2)])):(0,n.Q3)("",!0),(0,n.Lk)("div",r,[(0,n.RG)(e.$slots,"default",{},(function(){return[(0,n.eW)((0,i.v_)(a.message),1)]}),!0)])])]})),_:3},8,["title","visible","width","before-close"])}const d={name:"ConfirmDialog",props:{title:{type:String,default:"确认"},message:{type:String,default:"确定要执行此操作吗？"},confirmText:{type:String,default:"确定"},cancelText:{type:String,default:"取消"},confirmType:{type:String,default:"primary"},icon:{type:String,default:""},width:{type:String,default:"30%"},value:{type:Boolean,default:!1}},computed:{dialogVisible:{get:function(){return this.value},set:function(e){this.$emit("input",e)}},iconClass:function(){switch(this.icon){case"warning":return"el-icon-warning-outline";case"error":return"el-icon-error";case"success":return"el-icon-success";case"info":return"el-icon-info";default:return this.icon}}},methods:{handleClose:function(e){this.$emit("cancel"),e()},handleCancel:function(){this.dialogVisible=!1,this.$emit("cancel")},handleConfirm:function(){this.dialogVisible=!1,this.$emit("confirm")}}};var g=a(66262);const u=(0,g.A)(d,[["render",c],["__scopeId","data-v-1e9c7025"]]),h=u}}]);