<template>
  <div class="cases-container">
    <div class="page-header">
      <h2>
        <!-- 根据用户角色显示不同标题 -->
        <span v-if="isAdmin">病例标注管理</span>
        <span v-else-if="isReviewer">待审核病例</span>
        <span v-else>我的病例标注</span>
      </h2>
      <el-button 
        v-permission="'create_case'"
        type="primary" 
        @click="$router.push('/app/cases/new')"
      >新建标注</el-button>
    </div>

    <div class="filter-row">
      <el-form :inline="true" class="filter-form">
        <el-form-item label="状态">
          <el-select v-model="filterStatus" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option label="未标注" value="DRAFT"></el-option>
            <el-option label="已标注" value="REVIEWED"></el-option>
            <el-option label="待审核" value="SUBMITTED"></el-option>
            <el-option label="已通过" value="APPROVED"></el-option>
            <el-option label="已驳回" value="REJECTED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilterChange">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>

    <el-table v-else :data="casesList" style="width: 100%" v-loading="tableLoading">
      <el-table-column prop="caseId" label="病例编号" width="180" />
      <el-table-column prop="department" label="部位" />
      <el-table-column prop="type" label="类型" />
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <status-badge :status="getStatusValue(scope.row.status)" />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column label="操作" width="220">
        <template #default="scope">
          <el-button 
            v-permission="'edit_case'"
            link 
            size="small" 
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button 
            link 
            size="small" 
            @click="handleView(scope.row)"
          >
            查看
          </el-button>
          <el-button 
            v-permission:role="'ADMIN'"
            link 
            type="danger" 
            size="small" 
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 无数据时显示提示 -->
    <div v-if="!loading && casesList.length === 0" class="text-center my-5">
      <p v-if="allImages && allImages.length > 0 && filterStatus">
        没有符合条件的病例，当前筛选状态: {{ getStatusText(filterStatus) }}
      </p>
      <p v-else-if="allImages && allImages.length > 0">
        暂无病例匹配当前筛选条件
      </p>
      <p v-else>
        暂无病例标注数据
      </p>
    </div>

    <el-pagination
      v-if="totalItems > 0"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalItems"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :current-page="currentPage"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { PERMISSIONS } from '../utils/permissions'
import debounce from 'lodash/debounce'
import api from '@/utils/api'  // 添加缺失的API导入
import axios from 'axios'
import StatusBadge from '../components/common/StatusBadge.vue'

export default {
  name: 'CasesList',
  components: {
    StatusBadge
  },
  data() {
    return {
      casesList: [],
      pageSize: 10,
      currentPage: 1,
      totalItems: 0,
      filterStatus: '',
      tableLoading: false,
      myImages: [], // 我的图像
      needReviewImages: [], // 需要审核的图像
      allImages: []  // 所有图像
    }
  },
  computed: {
    ...mapGetters({
      images: 'getAllImages',
      loading: 'isImagesLoading',
      error: 'getImagesError',
      isAdmin: 'isAdmin',
      isDoctor: 'isDoctor',
      isReviewer: 'isReviewer',
      hasPermission: 'hasPermission',
      currentUserId: 'getUserId',
      canAccessResource: 'canAccessResource'
    }),
    
    // 根据用户角色和路由参数确定要显示的图像列表
    filteredImages() {
      // 从路由或组件状态获取过滤状态
      let status = this.filterStatus || this.$route.query.status;
      
      console.log('过滤状态:', status);
      
      // 默认显示所有图像，不做用户筛选
      const baseImages = this.allImages;
      
      // 如果没有状态过滤，返回基础图像集
      if (!status) return baseImages;
      
      console.log(`根据状态 ${status} 过滤 ${baseImages.length} 条记录`);
      
      // 根据状态进行过滤，使用正确的状态值
      return baseImages.filter(img => {
        // 转换API返回的状态为统一的枚举值
        const imageStatus = img.status || 'DRAFT';
        
        // 调试状态匹配
        console.log(`图像 ${img.id} 状态: ${imageStatus}, 目标状态: ${status}, 匹配: ${imageStatus === status}`);
        
        // 直接比较状态值
        return imageStatus === status;
      });
    }
  },
  methods: {
    ...mapActions(['fetchImages']),
    getStatusType(status) {
      const types = {
        '未标注': 'info',
        '已标注': 'success',
        '待审核': 'warning',
        '已通过': 'success',
        '已驳回': 'danger'
      }
      return types[status] || 'info'
    },
    // 将文本状态转换为API状态值
    getStatusValue(status) {
      const statusMap = {
        '未标注': 'DRAFT',
        '已标注': 'REVIEWED',
        '待审核': 'SUBMITTED',
        '已通过': 'APPROVED',
        '已驳回': 'REJECTED'
      };
      return statusMap[status] || 'DRAFT';
    },
    // 将API状态值转换为显示文本
    getStatusText(statusValue) {
      const textMap = {
        'DRAFT': '未标注',
        'REVIEWED': '已标注',
        'SUBMITTED': '待审核',
        'APPROVED': '已通过',
        'REJECTED': '已驳回'
      };
      
      // 添加调试信息
      console.log('转换状态显示:', statusValue, '→', textMap[statusValue] || statusValue);
      
      return textMap[statusValue] || statusValue;
    },
    handleEdit(row) {
      if (!this.canEditCase(row)) {
        this.$message.error('您没有权限编辑此病例')
        return
      }
      
      localStorage.setItem('isEditingCase', 'true');
      
      this.$router.push({
        path: '/app/cases/form',
        query: { 
          imageId: row.id,
          edit: 'true'
        }
      })
    },
    handleView(row) {
      if (!this.canViewCase(row)) {
        this.$message.error('您没有权限查看此病例')
        return
      }
      
      this.$router.push(`/app/cases/view/${row.id}`)
    },
    handleDelete(row) {
      if (!this.canDeleteCase(row)) {
        this.$message.error('您没有权限删除此病例')
        return
      }
      
      this.$confirm('此操作将永久删除该病例及其所有相关数据，是否继续?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: '删除中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        
        import('@/utils/api').then(api => {
          api.default.images.delete(row.id)
            .then(() => {
              console.log('图像及相关数据已删除');
              loading.close();
              this.$message.success('删除成功');
              this.fetchCases();
            })
            .catch(error => {
              loading.close();
              let errorMsg = '删除失败';
              
              if (error.response && error.response.data) {
                errorMsg = typeof error.response.data === 'string' 
                  ? error.response.data 
                  : JSON.stringify(error.response.data);
              } else {
                errorMsg += ': ' + (error.message || '未知错误');
              }
              
              console.error('删除失败:', errorMsg);
              this.$message({
                type: 'error',
                message: errorMsg,
                duration: 5000
              });
            });
        });
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handlePageChange(page) {
      this.currentPage = page;
      this.processCases();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置为第一页
      this.processCases();
    },
    handleFilterChange: debounce(function() {
      this.currentPage = 1; // 重置为第一页
      this.processCases();
      
      // 更新URL查询参数，但不触发路由变化
      this.$router.push({
        query: { 
          ...this.$route.query,
          status: this.filterStatus || undefined,
          page: this.currentPage !== 1 ? this.currentPage : undefined
        }
      }).catch(() => {});
    }, 300),
    resetFilter() {
      this.filterStatus = '';
      this.handleFilterChange();
    },
    canEditCase(caseData) {
      // 允许编辑所有病例
      return true;
    },
    canViewCase(caseData) {
      // 允许查看所有病例
      return true;
    },
    canDeleteCase(caseData) {
      if (!this.isAdmin) return false
      
      return this.hasPermission(PERMISSIONS.DELETE_CASE)
    },
    async fetchCases() {
      try {
        console.log('获取病例列表，检查查询参数:', this.$route.query);
        
        this.tableLoading = true;
        
        // 从URL查询参数中获取页码和状态
        const pageFromQuery = parseInt(this.$route.query.page) || 1;
        const statusFromQuery = this.$route.query.status;
        
        // 同步状态到组件
        this.currentPage = pageFromQuery;
        if (statusFromQuery) {
          this.filterStatus = statusFromQuery;
          console.log('从URL参数设置过滤状态:', this.filterStatus);
        }
        
        // 从localStorage获取当前用户信息
        let userId = null;
        try {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const user = JSON.parse(userStr);
            userId = user.customId || user.id;
            console.log('从localStorage获取用户ID:', userId);
          }
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
        
        // 如果无法获取用户ID，不设置请求头中的用户ID
        // API会使用请求的Authorization头或默认用户
        if (userId) {
          // 设置请求头中的用户ID
          axios.defaults.headers.common['X-User-Id'] = userId;
        }
        
        // 根据当前页面路径确定获取个人图像还是团队图像
        let response;
        
        // 判断当前路径，决定获取哪种图像
        const isTeamView = this.$route.path.includes('/team') || 
                          this.$route.query.view === 'team';
        
        if (isTeamView) {
          console.log('获取团队图像');
          response = await api.images.getTeamImages();
        } else {
          console.log('获取个人图像');
          response = await api.images.getUserImages(this.filterStatus);
        }
        
        if (!response || !response.data || !Array.isArray(response.data)) {
          console.error('图像数据格式不正确:', response);
          
          // 如果个人视图获取失败，尝试获取团队视图作为备选
          if (!isTeamView) {
            console.log('个人图像获取失败，尝试获取团队图像');
            try {
              const teamResponse = await api.images.getTeamImages();
              if (teamResponse && teamResponse.data && Array.isArray(teamResponse.data)) {
                this.allImages = [...teamResponse.data];
                console.log('成功获取团队图像数量:', this.allImages.length);
                this.processCases();
                this.tableLoading = false;
                return;
              }
            } catch (teamError) {
              console.error('获取团队图像失败:', teamError);
            }
          }
          
          this.allImages = [];
          this.myImages = [];
          this.needReviewImages = [];
          this.tableLoading = false;
          return;
        }
        
        // 所有图像
        this.allImages = [...response.data];
        console.log('所有图像数量:', this.allImages.length);
        
        // 如果没有图像数据，尝试一次不带状态参数的请求获取所有图像
        if (this.allImages.length === 0 && this.filterStatus) {
          console.log('当前状态下没有图像，尝试获取所有图像');
          const allImagesResponse = await api.images.getUserImages();
          if (allImagesResponse && allImagesResponse.data && Array.isArray(allImagesResponse.data)) {
            this.allImages = [...allImagesResponse.data];
            console.log('获取到所有图像数量:', this.allImages.length);
          }
        }
        
        // 处理筛选后的图像数据
        this.processCases();
      } catch (error) {
        console.error('获取病例列表失败:', error);
        this.$message.error('获取病例列表失败: ' + error.message);
      } finally {
        this.tableLoading = false;
      }
    },
    processCases() {
      // 使用计算属性中过滤的图像
      const imagesToProcess = this.filteredImages;
      
      console.log(`处理 ${imagesToProcess.length} 条病例记录`);
      console.log('接口返回数据', imagesToProcess);
      
      // 如果没有匹配的记录，显示一个空表格
      if (imagesToProcess.length === 0) {
        this.totalItems = 0;
        this.casesList = [];
        return;
      }
      
      const sortedImages = [...imagesToProcess]
        .sort((a, b) => {
          const aTime = a.createdAt || a.created_at;
          const bTime = b.createdAt || b.created_at;
          if (!aTime) return 1;
          if (!bTime) return -1;
          return new Date(bTime) - new Date(aTime);
        });
      
      this.totalItems = sortedImages.length;
      
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      
      // 详细记录每条记录的创建时间字段
      console.log('详细检查创建时间字段:');
      sortedImages.slice(start, end).forEach((img, index) => {
        console.log(`记录 ${index+1}:`, {
          id: img.id,
          createdAt: img.createdAt,
          created_at: img.created_at,
          formattedDate: this.formatDate(img.createdAt || img.created_at),
          rawObject: img
        });
      });
      
      // 获取图像ID列表
      const imageIds = sortedImages.slice(start, end).map(img => img.id);
      
      // 先获取所有图像的标签信息
      const tagPromises = imageIds.map(imageId => 
        api.tags.getByImageId(imageId)
          .then(response => ({
            imageId,
            tags: response.data || []
          }))
          .catch(error => {
            console.error(`获取图像 ${imageId} 的标签失败:`, error);
            return { imageId, tags: [] };
          })
      );
      
      Promise.all(tagPromises)
        .then(tagsResults => {
          // 创建图像ID到标签的映射
          const imageTagsMap = {};
          tagsResults.forEach(result => {
            imageTagsMap[result.imageId] = result.tags;
          });
          
          this.casesList = sortedImages
            .slice(start, end)
            .map(img => {
              const createTime = this.formatDate(img.createdAt || img.created_at);
              
              // 获取该图像的标签
              const imageTags = imageTagsMap[img.id] || [];
              
              // 从标签中提取类型信息
              let tagType = '未知';
              try {
                if (imageTags.length > 0) {
                  // 确保标签对象有效
                  const firstTag = imageTags[0];
                  if (firstTag && typeof firstTag === 'object') {
                    // 直接使用标签的tag字段作为类型
                    tagType = firstTag.tag || '未知';
                  }
                } else {
                  // 如果没有标签，尝试使用图像元数据中的diagnosis_category字段
                  tagType = img.diagnosisCategory || img.diagnosis_category || '未知';
                }
              } catch (error) {
                console.error(`处理图像 ${img.id} 的标签数据时出错:`, error);
                // 使用备用数据
                tagType = img.diagnosisCategory || img.diagnosis_category || '未知';
              }
              
              return {
                id: img.id,
                caseId: img.caseNumber || `CASE-${img.id}`,
                department: img.lesionLocation || '未知',
                type: tagType, // 使用从标签中提取的类型
                status: this.getStatusText(img.status || 'DRAFT'),
                createTime: createTime,
                rawDate: img.createdAt || img.created_at,
                creatorId: img.uploadedBy, // 保存创建者ID，用于权限判断
                hasAnnotation: !!(img.image_two_path && img.image_two_path.trim() !== '') // 添加标记表示是否有标注
              };
            });
        })
        .catch(error => {
          console.error('获取标签数据失败:', error);
          // 如果获取标签失败，使用原始数据
          this.casesList = sortedImages
            .slice(start, end)
            .map(img => {
              const createTime = this.formatDate(img.createdAt || img.created_at);
              
              return {
                id: img.id,
                caseId: img.caseNumber || `CASE-${img.id}`,
                department: img.lesionLocation || '未知',
                type: img.diagnosisCategory || '未知',
                status: this.getStatusText(img.status || 'DRAFT'),
                createTime: createTime,
                rawDate: img.createdAt || img.created_at,
                creatorId: img.uploadedBy,
                hasAnnotation: !!(img.image_two_path && img.image_two_path.trim() !== '')
              };
            });
        });
    },
    formatDate(dateString) {
      console.log('格式化日期:', dateString);
      if (!dateString) {
        console.log('日期为空，返回"未知"');
        return '未知';
      }
      try {
        const date = new Date(dateString);
        console.log('转换后的日期对象:', date);
        if (isNaN(date.getTime())) {
          console.log('无效日期，返回"未知"');
          return '未知';
        }
        return date.toLocaleString('zh-CN');
      } catch (error) {
        console.error('日期格式化错误:', error);
        return '未知';
      }
    },
    getFilteredCases() {
      // 获取过滤后的图像列表
      const filtered = this.filteredImages;
      
      if (!filtered || filtered.length === 0) {
        this.casesList = [];
        this.totalItems = 0;
        return;
      }
      
      // 根据分页参数处理数据
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      
      // 转换数据格式，尤其是将状态转换为前端显示的文本
      this.casesList = filtered.slice(start, end).map(img => {
        return {
          id: img.id,
          caseId: img.case_number || img.id,
          department: img.lesion_location || '未知',
          type: img.diagnosis_category || '未指定',
          // 使用getStatusText方法转换状态
          status: this.getStatusText(img.status || 'DRAFT'),
          uploadedBy: img.uploaded_by,
          createTime: this.formatDate(img.created_at),
          // 保存原始记录，方便后续操作
          originalRecord: img
        };
      });
      
      this.totalItems = filtered.length;
    }
  },
  created() {
    this.fetchCases()
  },
  watch: {
    '$route.query': {
      handler(newQuery, oldQuery) {
        // 避免在初始化时oldQuery为undefined导致错误
        if (!oldQuery) {
          console.log('初始化路由监听，设置初始参数');
          if (newQuery.status) {
            this.filterStatus = newQuery.status;
            console.log('初始过滤状态:', this.filterStatus);
          }
          if (newQuery.page) {
            this.currentPage = parseInt(newQuery.page) || 1;
            console.log('初始页码:', this.currentPage);
          }
          // 初始化时不需要重新获取数据，因为created钩子会调用fetchCases
          return;
        }
        
        // 避免重复加载：只有在状态或页码改变时才重新加载
        if (newQuery.status !== oldQuery.status || newQuery.page !== oldQuery.page) {
          console.log('路由参数变化，旧状态:', oldQuery.status, '新状态:', newQuery.status);
          
          // 同步状态参数到组件
          if (newQuery.status !== oldQuery.status) {
            this.filterStatus = newQuery.status || '';
            console.log('更新过滤状态为:', this.filterStatus);
          }
          
          // 同步页码参数到组件
          if (newQuery.page !== oldQuery.page) {
            this.currentPage = parseInt(newQuery.page) || 1;
            console.log('更新当前页码为:', this.currentPage);
          }
          
          // 重新获取数据
          this.fetchCases();
        }
      },
      deep: true,
      immediate: true // 确保组件创建时立即触发一次
    }
  }
}
</script>

<style scoped>
.cases-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  align-items: center;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}

.text-center {
  text-align: center;
}

.my-5 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border .75s linear infinite;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style> 