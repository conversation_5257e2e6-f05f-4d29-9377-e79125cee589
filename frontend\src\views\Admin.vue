<template>
  <div class="container">
    <h2 class="mt-4 mb-4">管理员控制台</h2>
    
    <div class="row">
      <div class="col-md-3">
        <div class="list-group mb-4">
          <button 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="['list-group-item list-group-item-action', activeTab === tab.id ? 'active' : '']"
            @click="activeTab = tab.id"
          >
            <i :class="tab.icon"></i> {{ tab.name }}
          </button>
        </div>
      </div>
      
      <div class="col-md-9">
        <!-- 用户管理 -->
        <div v-if="activeTab === 'users'" class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">用户管理</h5>
            <div class="input-group" style="max-width: 300px;">
              <input 
                v-model="userSearchQuery" 
                type="text" 
                class="form-control" 
                placeholder="搜索用户..."
              >
              <button class="btn btn-outline-secondary" type="button" @click="searchUsers">
                <i class="bi bi-search"></i>
              </button>
            </div>
          </div>
          <div class="card-body">
            <div v-if="loadingUsers" class="text-center my-5">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
            </div>
            
            <div v-else-if="userError" class="alert alert-danger">
              {{ userError }}
            </div>
            
            <div v-else>
              <div class="mb-3">
                <button class="btn btn-warning" @click="goToReviewerApplications">
                  <i class="bi bi-person-check"></i> 查看权限升级申请
                </button>
              </div>
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th scope="col">ID</th>
                    <th scope="col">用户名</th>
                    <th scope="col">邮箱</th>
                    <th scope="col">角色</th>
                    <th scope="col">状态</th>
                    <th scope="col">注册时间</th>
                    <th scope="col">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="user in users" :key="user.id">
                    <td>{{ user.id }}</td>
                    <td>{{ user.username }}</td>
                    <td>{{ user.email }}</td>
                    <td>
                      <span 
                        :class="[
                          'badge', 
                          user.role === 'ADMIN' ? 'bg-danger' : 
                          user.role === 'REVIEWER' ? 'bg-primary' : 
                          'bg-secondary'
                        ]"
                      >
                        {{ user.role }}
                      </span>
                    </td>
                    <td>
                      <span 
                        :class="[
                          'badge', 
                          user.active ? 'bg-success' : 'bg-danger'
                        ]"
                      >
                        {{ user.active ? '已激活' : '已禁用' }}
                      </span>
                    </td>
                    <td>{{ formatDate(user.createdAt) }}</td>
                    <td>
                      <div class="btn-group btn-group-sm">
                        <button 
                          class="btn btn-outline-primary" 
                          :title="user.active ? '禁用用户' : '激活用户'"
                          @click="toggleUserStatus(user)"
                        >
                          <i :class="user.active ? 'bi bi-x-circle' : 'bi bi-check-circle'"></i>
                        </button>
                        <button 
                          class="btn btn-outline-secondary" 
                          title="修改角色"
                          @click="editUserRole(user)"
                        >
                          <i class="bi bi-pencil"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              
              <nav v-if="userTotalPages > 1" aria-label="用户分页">
                <ul class="pagination justify-content-center">
                  <li :class="['page-item', userCurrentPage === 0 ? 'disabled' : '']">
                    <a class="page-link" href="#" @click.prevent="changePage(userCurrentPage - 1, 'users')">上一页</a>
                  </li>
                  <li 
                    v-for="pageNum in userTotalPages" 
                    :key="pageNum"
                    :class="['page-item', userCurrentPage === pageNum - 1 ? 'active' : '']"
                  >
                    <a class="page-link" href="#" @click.prevent="changePage(pageNum - 1, 'users')">{{ pageNum }}</a>
                  </li>
                  <li :class="['page-item', userCurrentPage === userTotalPages - 1 ? 'disabled' : '']">
                    <a class="page-link" href="#" @click.prevent="changePage(userCurrentPage + 1, 'users')">下一页</a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
        
        <!-- 图像管理 -->
        <div v-if="activeTab === 'images'" class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">图像管理</h5>
            <div class="input-group" style="max-width: 300px;">
              <input 
                v-model="imageSearchQuery" 
                type="text" 
                class="form-control" 
                placeholder="搜索图像..."
              >
              <button class="btn btn-outline-secondary" type="button" @click="searchImages">
                <i class="bi bi-search"></i>
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <select v-model="imageStatusFilter" class="form-select w-auto" @change="fetchImages()">
                <option value="">所有状态</option>
                <option value="UPLOADED">已上传</option>
                <option value="ANNOTATED">已标注</option>
                <option value="REVIEWED">已审核</option>
                <option value="APPROVED">已批准</option>
                <option value="REJECTED">已拒绝</option>
              </select>
            </div>
            
            <div v-if="loadingImages" class="text-center my-5">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
            </div>
            
            <div v-else-if="imageError" class="alert alert-danger">
              {{ imageError }}
            </div>
            
            <div v-else>
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <thead>
                    <tr>
                      <th scope="col">ID</th>
                      <th scope="col">缩略图</th>
                      <th scope="col">名称</th>
                      <th scope="col">上传者</th>
                      <th scope="col">状态</th>
                      <th scope="col">上传时间</th>
                      <th scope="col">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="image in images" :key="image.id">
                      <td>{{ image.id }}</td>
                      <td>
                        <img 
                          :src="`${apiUrl}/api/images/${image.id}/thumbnail`" 
                          class="admin-thumbnail" 
                          alt="缩略图"
                        />
                      </td>
                      <td>{{ image.name }}</td>
                      <td>{{ image.uploaderName }}</td>
                      <td>
                        <span 
                          :class="[
                            'badge', 
                            image.status === 'APPROVED' ? 'bg-success' : 
                            image.status === 'REJECTED' ? 'bg-danger' : 
                            image.status === 'ANNOTATED' ? 'bg-primary' : 
                            image.status === 'REVIEWED' ? 'bg-info' : 
                            'bg-secondary'
                          ]"
                        >
                          {{ getStatusText(image.status) }}
                        </span>
                      </td>
                      <td>{{ formatDate(image.uploadTime) }}</td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button 
                            class="btn btn-outline-info" 
                            title="查看详情"
                            @click="$router.push(`/images/${image.id}`)"
                          >
                            <i class="bi bi-eye"></i>
                          </button>
                          <button 
                            class="btn btn-outline-danger" 
                            title="删除图像"
                            @click="deleteImage(image.id)"
                          >
                            <i class="bi bi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <nav v-if="imageTotalPages > 1" aria-label="图像分页">
                <ul class="pagination justify-content-center">
                  <li :class="['page-item', imageCurrentPage === 0 ? 'disabled' : '']">
                    <a class="page-link" href="#" @click.prevent="changePage(imageCurrentPage - 1, 'images')">上一页</a>
                  </li>
                  <li 
                    v-for="pageNum in imageTotalPages" 
                    :key="pageNum"
                    :class="['page-item', imageCurrentPage === pageNum - 1 ? 'active' : '']"
                  >
                    <a class="page-link" href="#" @click.prevent="changePage(pageNum - 1, 'images')">{{ pageNum }}</a>
                  </li>
                  <li :class="['page-item', imageCurrentPage === imageTotalPages - 1 ? 'disabled' : '']">
                    <a class="page-link" href="#" @click.prevent="changePage(imageCurrentPage + 1, 'images')">下一页</a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
        
        <!-- 系统设置 -->
        <div v-if="activeTab === 'settings'" class="card">
          <div class="card-header">
            <h5 class="mb-0">系统设置</h5>
          </div>
          <div class="card-body">
            <form @submit.prevent="saveSettings">
              <div class="mb-3">
                <label for="siteName" class="form-label">系统名称</label>
                <input id="siteName" v-model="settings.siteName" type="text" class="form-control">
              </div>
              
              <div class="mb-3">
                <label for="maxImageSize" class="form-label">最大图像大小(MB)</label>
                <input id="maxImageSize" v-model="settings.maxImageSizeMB" type="number" class="form-control">
              </div>
              
              <div class="form-check form-switch mb-3">
                <input id="allowRegistration" v-model="settings.allowPublicRegistration" class="form-check-input" type="checkbox">
                <label class="form-check-label" for="allowRegistration">允许公开注册</label>
              </div>
              
              <div class="form-check form-switch mb-3">
                <input id="reviewRequired" v-model="settings.requireReview" class="form-check-input" type="checkbox">
                <label class="form-check-label" for="reviewRequired">标注需要审核</label>
              </div>
              
              <button type="submit" class="btn btn-primary" :disabled="savingSettings">
                <span v-if="savingSettings">
                  <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                  保存中...
                </span>
                <span v-else>保存设置</span>
              </button>
            </form>
          </div>
        </div>
        
        <!-- 审核医生申请 -->
        <div v-if="activeTab === 'reviewerApplications'" class="card">
          <div class="card-header">
            <h5 class="mb-0">审核医生申请</h5>
          </div>
          <div class="card-body">
            <p>您可以在这里管理标注医生提交的审核医生申请。</p>
            <button class="btn btn-primary" @click="goToReviewerApplications">
              <i class="bi bi-list-check"></i> 查看申请列表
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'Admin',
  data() {
    return {
      apiUrl: process.env.VUE_APP_API_URL,
      activeTab: 'users',
      tabs: [
        { id: 'users', name: '用户管理', icon: 'bi bi-people' },
        { id: 'images', name: '图像管理', icon: 'bi bi-images' },
        { id: 'settings', name: '系统设置', icon: 'bi bi-gear' },
        { id: 'reviewerApplications', name: '审核医生申请', icon: 'bi bi-person-check' }
      ],
      
      // 用户管理
      users: [],
      loadingUsers: false,
      userError: null,
      userCurrentPage: 0,
      userTotalPages: 0,
      userSearchQuery: '',
      
      // 图像管理
      images: [],
      loadingImages: false,
      imageError: null,
      imageCurrentPage: 0,
      imageTotalPages: 0,
      imageSearchQuery: '',
      imageStatusFilter: '',
      
      // 系统设置
      settings: {
        siteName: '血管瘤辅助标注系统',
        maxImageSizeMB: 10,
        allowPublicRegistration: true,
        requireReview: true
      },
      savingSettings: false
    };
  },
  watch: {
    activeTab(newTab) {
      if (newTab === 'users') {
        this.fetchUsers();
      } else if (newTab === 'images') {
        this.fetchImages();
      } else if (newTab === 'settings') {
        this.fetchSettings();
      } else if (newTab === 'reviewerApplications') {
        // 当切换到审核医生申请选项卡时，加载数据
        console.log('加载审核医生申请数据');
      }
    }
  },
  mounted() {
    this.fetchUsers();
    this.fetchImages();
    this.fetchSettings();
  },
  methods: {
    async fetchUsers(page = 0) {
      this.loadingUsers = true;
      this.userError = null;
      
      try {
        const params = {
          page,
          size: 10,
          query: this.userSearchQuery
        };
        
        const response = await axios.get(`${this.apiUrl}/api/admin/users`, { params });
        this.users = response.data.content;
        this.userCurrentPage = response.data.number;
        this.userTotalPages = response.data.totalPages;
      } catch (err) {
        this.userError = '加载用户列表失败: ' + (err.response?.data?.message || err.message);
      } finally {
        this.loadingUsers = false;
      }
    },
    
    async fetchImages(page = 0) {
      this.loadingImages = true;
      this.imageError = null;
      
      try {
        const params = {
          page,
          size: 10,
          query: this.imageSearchQuery,
          status: this.imageStatusFilter
        };
        
        const response = await axios.get(`${this.apiUrl}/api/admin/images`, { params });
        this.images = response.data.content;
        this.imageCurrentPage = response.data.number;
        this.imageTotalPages = response.data.totalPages;
      } catch (err) {
        this.imageError = '加载图像列表失败: ' + (err.response?.data?.message || err.message);
      } finally {
        this.loadingImages = false;
      }
    },
    
    async fetchSettings() {
      try {
        const response = await axios.get(`${this.apiUrl}/api/admin/settings`);
        this.settings = response.data;
      } catch (err) {
        this.$store.dispatch('showAlert', {
          type: 'error',
          message: '加载系统设置失败: ' + (err.response?.data?.message || err.message)
        });
      }
    },
    
    async saveSettings() {
      this.savingSettings = true;
      
      try {
        await axios.post(`${this.apiUrl}/api/admin/settings`, this.settings);
        this.$store.dispatch('showAlert', {
          type: 'success',
          message: '系统设置保存成功'
        });
      } catch (err) {
        this.$store.dispatch('showAlert', {
          type: 'error',
          message: '保存系统设置失败: ' + (err.response?.data?.message || err.message)
        });
      } finally {
        this.savingSettings = false;
      }
    },
    
    changePage(page, type) {
      if (type === 'users') {
        if (page >= 0 && page < this.userTotalPages) {
          this.fetchUsers(page);
        }
      } else if (type === 'images') {
        if (page >= 0 && page < this.imageTotalPages) {
          this.fetchImages(page);
        }
      }
    },
    
    searchUsers() {
      this.fetchUsers(0);
    },
    
    searchImages() {
      this.fetchImages(0);
    },
    
    async toggleUserStatus(user) {
      try {
        await axios.post(`${this.apiUrl}/api/admin/users/${user.id}/toggle-status`);
        this.$store.dispatch('showAlert', {
          type: 'success',
          message: `用户 ${user.username} 已${user.active ? '禁用' : '激活'}`
        });
        
        // 刷新用户列表
        this.fetchUsers(this.userCurrentPage);
      } catch (err) {
        this.$store.dispatch('showAlert', {
          type: 'error',
          message: '更改用户状态失败: ' + (err.response?.data?.message || err.message)
        });
      }
    },
    
    async editUserRole(user) {
      // 简单实现，实际项目中可以使用模态窗口实现更复杂的界面
      const newRole = prompt(`请为用户 ${user.username} 选择新角色 (USER, REVIEWER, ADMIN)`, user.role);
      
      if (newRole && ['USER', 'REVIEWER', 'ADMIN'].includes(newRole.toUpperCase())) {
        try {
          await axios.post(`${this.apiUrl}/api/admin/users/${user.id}/change-role`, {
            role: newRole.toUpperCase()
          });
          
          this.$store.dispatch('showAlert', {
            type: 'success',
            message: `用户 ${user.username} 的角色已更改为 ${newRole.toUpperCase()}`
          });
          
          // 刷新用户列表
          this.fetchUsers(this.userCurrentPage);
        } catch (err) {
          this.$store.dispatch('showAlert', {
            type: 'error',
            message: '更改用户角色失败: ' + (err.response?.data?.message || err.message)
          });
        }
      } else if (newRole) {
        this.$store.dispatch('showAlert', {
          type: 'error',
          message: '无效的角色值'
        });
      }
    },
    
    async deleteImage(imageId) {
      if (confirm('确定要删除这张图像吗？此操作不可撤销。')) {
        try {
          await axios.delete(`${this.apiUrl}/api/admin/images/${imageId}`);
          this.$store.dispatch('showAlert', {
            type: 'success',
            message: '图像已成功删除'
          });
          
          // 刷新图像列表
          this.fetchImages(this.imageCurrentPage);
        } catch (err) {
          this.$store.dispatch('showAlert', {
            type: 'error',
            message: '删除图像失败: ' + (err.response?.data?.message || err.message)
          });
        }
      }
    },
    
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    },
    
    getStatusText(status) {
      const statusMap = {
        'UPLOADED': '已上传',
        'ANNOTATED': '已标注',
        'REVIEWED': '已审核',
        'APPROVED': '已批准',
        'REJECTED': '已拒绝'
      };
      return statusMap[status] || status;
    },
    
    goToReviewerApplications() {
      this.$router.push('/admin/reviewer-applications');
    }
  }
};
</script>

<style scoped>
.admin-thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}
</style> 