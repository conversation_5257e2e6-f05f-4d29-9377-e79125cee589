<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        h2 {
            margin-top: 0;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-left: 4px solid #2196F3;
            background-color: #E3F2FD;
        }
        .error {
            border-left-color: #F44336;
            background-color: #FFEBEE;
        }
        .success {
            border-left-color: #4CAF50;
            background-color: #E8F5E9;
        }
    </style>
</head>
<body>
    <h1>文件上传测试工具</h1>
    <p>使用此页面测试不同的文件上传方法和路径。</p>

    <!-- 添加最简单的单一文件上传表单 -->
    <div class="test-section" style="background-color: #FFECB3; border: 2px solid #FFA000;">
        <h2>超简单直接上传</h2>
        <p><strong>请先尝试这种最基础的方法</strong></p>
        <form action="http://localhost:8085/medical/api/images/upload" method="post" enctype="multipart/form-data">
            <input type="file" name="file" accept="image/*" id="simpleFile">
            <br><br>
            <button type="submit">直接上传</button>
        </form>
    </div>

    <div class="test-section">
        <h2>1. 原生表单上传</h2>
        <form id="nativeForm" enctype="multipart/form-data" target="responseFrame">
            <input type="file" name="file" id="nativeFile" accept="image/*">
            <div>
                <label for="nativeUrl">上传URL:</label>
                <input type="text" id="nativeUrl" style="width: 400px" value="http://localhost:8085/medical/api/images/upload">
            </div>
            <button type="button" onclick="submitNativeForm()">上传</button>
        </form>
        <div id="nativeResult" class="result">等待上传...</div>
    </div>

    <div class="test-section">
        <h2>2. Fetch API上传</h2>
        <input type="file" id="fetchFile" accept="image/*">
        <div>
            <label for="fetchUrl">上传URL:</label>
            <input type="text" id="fetchUrl" style="width: 400px" value="http://localhost:8085/medical/api/images/upload">
        </div>
        <button onclick="uploadWithFetch()">上传</button>
        <div id="fetchResult" class="result">等待上传...</div>
    </div>

    <div class="test-section">
        <h2>3. 路径测试工具</h2>
        <input type="file" id="testFile" accept="image/*">
        <button onclick="testAllPaths()">测试多个路径</button>
        <div id="pathResults"></div>
    </div>

    <div class="test-section">
        <h2>系统信息</h2>
        <button onclick="checkSystem()">检查系统</button>
        <pre id="systemInfo"></pre>
    </div>

    <iframe name="responseFrame" style="display:none;"></iframe>

    <script>
        // 添加直接检测上传文件的函数
        document.getElementById('simpleFile').addEventListener('change', function(e) {
            if (this.files && this.files.length > 0) {
                const file = this.files[0];
                alert(`已选择文件: ${file.name}\n大小: ${file.size} 字节\n类型: ${file.type}`);
            } else {
                alert('没有选择文件或选择文件失败');
            }
        });
        
        // 原生表单上传
        function submitNativeForm() {
            const fileInput = document.getElementById('nativeFile');
            const url = document.getElementById('nativeUrl').value;
            const resultDiv = document.getElementById('nativeResult');
            
            if (!fileInput.files.length) {
                resultDiv.textContent = '错误: 请选择文件';
                resultDiv.className = 'result error';
                return;
            }

            const form = document.getElementById('nativeForm');
            form.action = url;
            form.method = 'POST';
            
            resultDiv.textContent = '上传中...';
            
            // 使用iframe处理响应
            const iframe = document.querySelector('iframe[name="responseFrame"]');
            iframe.onload = function() {
                try {
                    const content = iframe.contentDocument.body.innerText;
                    resultDiv.innerHTML = `<strong>响应:</strong><br>${content}`;
                    
                    try {
                        const json = JSON.parse(content);
                        if (json.id || json.data && json.data.id) {
                            resultDiv.className = 'result success';
                        } else {
                            resultDiv.className = 'result';
                        }
                    } catch (e) {
                        resultDiv.className = 'result';
                    }
                } catch (e) {
                    resultDiv.textContent = '错误: 无法读取响应';
                    resultDiv.className = 'result error';
                }
            };
            
            form.submit();
        }

        // Fetch API上传
        async function uploadWithFetch() {
            const fileInput = document.getElementById('fetchFile');
            const url = document.getElementById('fetchUrl').value;
            const resultDiv = document.getElementById('fetchResult');
            
            if (!fileInput.files.length) {
                resultDiv.textContent = '错误: 请选择文件';
                resultDiv.className = 'result error';
                return;
            }

            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            
            resultDiv.textContent = '上传中...';
            
            try {
                // 添加用户信息
                let user = null;
                try {
                    user = JSON.parse(localStorage.getItem('user'));
                } catch (e) {
                    console.error('无法解析用户信息:', e);
                }

                const headers = {};
                if (user) {
                    headers['X-User-Id'] = user.customId || user.id;
                    headers['X-User-Email'] = user.email;
                }
                
                const response = await fetch(url, {
                    method: 'POST',
                    body: formData,
                    headers: headers,
                    credentials: 'include'
                });
                
                const text = await response.text();
                
                resultDiv.innerHTML = `
                    <strong>状态码:</strong> ${response.status}<br>
                    <strong>响应:</strong><br>${text}
                `;
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试多个路径
        async function testAllPaths() {
            const fileInput = document.getElementById('testFile');
            const resultsDiv = document.getElementById('pathResults');
            
            if (!fileInput.files.length) {
                resultsDiv.innerHTML = '<div class="result error">错误: 请选择文件</div>';
                return;
            }

            const file = fileInput.files[0];
            
            // 测试路径列表
            const paths = [
                'http://localhost:8085/medical/api/images/upload',
                'http://localhost:8085/api/images/upload',
                'http://localhost:8085/medical/images/upload',
                'http://localhost:8085/images/upload',
                'http://localhost:8080/medical/api/images/upload',
                'http://localhost:8080/api/images/upload',
                'http://localhost:8080/images/upload'
            ];
            
            resultsDiv.innerHTML = '<div>测试中，请稍候...</div>';
            
            const results = [];
            
            for (const path of paths) {
                const formData = new FormData();
                formData.append('file', file);
                
                try {
                    // 添加用户信息
                    let user = null;
                    try {
                        user = JSON.parse(localStorage.getItem('user'));
                    } catch (e) {}

                    const headers = {};
                    if (user) {
                        headers['X-User-Id'] = user.customId || user.id;
                        headers['X-User-Email'] = user.email;
                    }
                    
                    const response = await fetch(path, {
                        method: 'POST',
                        body: formData,
                        headers: headers,
                        credentials: 'include'
                    }).catch(error => {
                        return { error: error.message, path };
                    });
                    
                    if (response.error) {
                        results.push({
                            path,
                            success: false,
                            status: 'Error',
                            message: response.error
                        });
                    } else {
                        const text = await response.text();
                        results.push({
                            path,
                            success: response.ok,
                            status: response.status,
                            text
                        });
                    }
                } catch (error) {
                    results.push({
                        path,
                        success: false,
                        status: 'Error',
                        message: error.message
                    });
                }
            }
            
            // 显示结果
            let html = '';
            results.forEach(result => {
                const className = result.success ? 'success' : 'error';
                html += `
                    <div class="result ${className}">
                        <strong>${result.path}</strong><br>
                        状态: ${result.status}<br>
                        ${result.text ? `响应: ${result.text}` : `消息: ${result.message}`}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }

        // 检查系统信息
        function checkSystem() {
            const infoDiv = document.getElementById('systemInfo');
            
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                cookies: navigator.cookieEnabled,
                language: navigator.language,
                online: navigator.onLine,
                localStorage: !!window.localStorage,
                sessionStorage: !!window.sessionStorage,
                indexedDB: !!window.indexedDB,
                screen: {
                    width: window.screen.width,
                    height: window.screen.height
                },
                url: window.location.href,
                user: null
            };
            
            try {
                const user = localStorage.getItem('user');
                if (user) {
                    info.user = JSON.parse(user);
                }
            } catch (e) {
                info.userError = e.message;
            }
            
            infoDiv.textContent = JSON.stringify(info, null, 2);
        }
    </script>
</body>
</html> 