const { exec } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

// 配置项
const config = {
  // 前端构建目录
  frontendBuildDir: path.join(__dirname, 'dist'),
  // 后端静态资源目录
  backendStaticDir: path.join(__dirname, '../backend/src/main/resources/static'),
  // 保留目录（不会被清空的目录）
  reservedDirs: ['uploads', 'WEB-INF']
};

/**
 * 执行命令行
 * @param {string} command 命令
 * @returns {Promise<string>} 命令输出
 */
function runCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`执行命令: ${command}`);
    
    const child = exec(command, { cwd: __dirname });
    
    // 收集命令输出
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data;
      process.stdout.write(data);
    });
    
    child.stderr.on('data', (data) => {
      stderr += data;
      process.stderr.write(data);
    });
    
    child.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`命令执行失败: ${command}\n${stderr}`));
        return;
      }
      resolve(stdout);
    });
  });
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('=== 开始构建前端并部署到后端 ===');
    
    // 确保后端静态目录存在
    console.log(`确保后端静态目录存在: ${config.backendStaticDir}`);
    await fs.ensureDir(config.backendStaticDir);
    
    // 检查保留目录
    const reservedPaths = [];
    for (const dir of config.reservedDirs) {
      const fullPath = path.join(config.backendStaticDir, dir);
      if (await fs.pathExists(fullPath)) {
        console.log(`保留目录: ${fullPath}`);
        reservedPaths.push(fullPath);
        // 临时移动到备份目录
        const backupPath = `${fullPath}.bak`;
        await fs.move(fullPath, backupPath);
      }
    }
    
    // 构建前端
    console.log('=== 构建前端 ===');
    await runCommand('npm run build');
    
    // 清空后端静态目录（除了保留目录）
    console.log(`清空后端静态目录: ${config.backendStaticDir}`);
    await fs.emptyDir(config.backendStaticDir);
    
    // 恢复保留目录
    for (let i = 0; i < reservedPaths.length; i++) {
      const origPath = reservedPaths[i];
      const backupPath = `${origPath}.bak`;
      await fs.move(backupPath, origPath);
    }
    
    // 复制前端构建内容到后端静态目录
    console.log(`复制前端构建内容到后端静态目录: ${config.frontendBuildDir} -> ${config.backendStaticDir}`);
    await fs.copy(config.frontendBuildDir, config.backendStaticDir);
    
    console.log('=== 构建和部署完成 ===');
    console.log('现在可以启动后端服务，前端和后端将通过同一个端口提供服务');
  } catch (error) {
    console.error('构建和部署过程出错:', error);
    process.exit(1);
  }
}

// 运行主函数
main(); 