﻿后端核心源代码 - 软件著作权申请
=========================================


// --- 文件开始: src\main\java\com\medical\annotation\Application.java ---
// =================================================================================

package com.medical.annotation;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import com.medical.annotation.service.FileService;

@SpringBootApplication
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
    
    @Bean
    CommandLineRunner init(FileService fileService) {
        return args -> {
            try {
                System.out.println("初始化文件系统目录...");
                fileService.init();
                System.out.println("文件系统目录初始化完成");
            } catch (Exception e) {
                System.err.println("文件系统目录初始化失败: " + e.getMessage());
                e.printStackTrace();
            }
        };
    }
} 


// --- 文件结束: src\main\java\com\medical\annotation\Application.java ---


// --- 文件开始: src\main\java\com\medical\annotation\config\WebConfig.java ---
// =================================================================================

package com.medical.annotation.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * Web配置类，用于解决字符编码问题
 */
@Configuration
@Primary
public class WebConfig implements WebMvcConfigurer {

    // 注入AppConfig中配置的图片存储路径
    @Autowired
    @Qualifier("getUploadDir")
    private String uploadDir;
    
    @Autowired
    @Qualifier("getAnnotatedDir")
    private String annotatedDir;
    
    @Autowired
    @Qualifier("getProcessedDir")
    private String processedDir;
    
    @Autowired
    @Qualifier("getTempImagesDir")
    private String tempImagesDir;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 确保目录存在
        createDirectoryIfNotExists(uploadDir);
        createDirectoryIfNotExists(annotatedDir);
        createDirectoryIfNotExists(processedDir);
        createDirectoryIfNotExists(tempImagesDir);
        
        // 使用绝对路径而不是相对路径
        File uploadDirFile = new File(uploadDir);
        File processedDirFile = new File(processedDir);
        File annotatedDirFile = new File(annotatedDir);
        File tempImagesDirFile = new File(tempImagesDir);
        
        // 确保使用绝对路径
        String uploadDirFullPath = uploadDirFile.getAbsolutePath();
        String processedDirFullPath = processedDirFile.getAbsolutePath();
        String annotatedDirFullPath = annotatedDirFile.getAbsolutePath();
        String tempImagesDirFullPath = tempImagesDirFile.getAbsolutePath();
        
        // 格式化为文件URL格式
        String uploadDirUrl = "file:" + ensureCorrectPathFormat(uploadDirFullPath) + "/";
        String processedDirUrl = "file:" + ensureCorrectPathFormat(processedDirFullPath) + "/";
        String annotatedDirUrl = "file:" + ensureCorrectPathFormat(annotatedDirFullPath) + "/";
        String tempImagesDirUrl = "file:" + ensureCorrectPathFormat(tempImagesDirFullPath) + "/";
        
        // 添加备用目录 - 使前端更容易找到图片文件
        // 使用注入的uploadDir作为备用路径
        String backupImageUrl = "file:" + ensureCorrectPathFormat(uploadDir) + "/";
        
        System.out.println("\n===== 增强的静态资源映射配置 =====");
        
        // 简化路径映射 - 不带/medical前缀
        System.out.println("配置简化路径映射 - 不带/medical前缀");
        
        // 上传原始图片和公共路径 - 简化路径
        registry.addResourceHandler("/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        // 处理后的图片路径 - 简化路径
        registry.addResourceHandler("/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/images/processed/** -> " + processedDirUrl);
        
        // 标注后的图片路径 - 简化路径
        registry.addResourceHandler("/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/images/annotated/** -> " + annotatedDirUrl);
        
        // 临时图像目录映射 - 简化路径
        registry.addResourceHandler("/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 标准路径映射 - 带/medical前缀
        System.out.println("\n配置标准路径映射 - 带/medical前缀");
        
        // 上传原始图片和公共路径 - 标准路径
        registry.addResourceHandler("/medical/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/medical/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        // 处理后的图片路径 - 标准路径
        registry.addResourceHandler("/medical/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/medical/images/processed/** -> " + processedDirUrl);
        
        // 标注后的图片路径 - 标准路径
        registry.addResourceHandler("/medical/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/medical/images/annotated/** -> " + annotatedDirUrl);
        
        // 临时图像目录映射 - 标准路径
        registry.addResourceHandler("/medical/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/medical/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 双重路径问题处理 - 添加对/medical/medical/images/**的支持
        System.out.println("\n配置双重路径映射处理");
        registry.addResourceHandler("/medical/medical/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/medical/medical/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        registry.addResourceHandler("/medical/medical/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/medical/medical/images/processed/** -> " + processedDirUrl);
        
        registry.addResourceHandler("/medical/medical/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/medical/medical/images/annotated/** -> " + annotatedDirUrl);
        
        registry.addResourceHandler("/medical/medical/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/medical/medical/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 临时目录映射
        String tempDir = System.getProperty("java.io.tmpdir");
        String tempDirUrl = "file:" + ensureCorrectPathFormat(tempDir) + "/";
        registry.addResourceHandler("/temp/**")
                .addResourceLocations(tempDirUrl);
        System.out.println("/temp/** -> " + tempDirUrl);
        
        // 验证目录是否存在并有正确权限
        System.out.println("\n===== 目录权限检查 =====");
        System.out.println("上传目录: " + uploadDirFullPath);
        System.out.println("  - 存在: " + uploadDirFile.exists());
        System.out.println("  - 可读: " + uploadDirFile.canRead());
        System.out.println("  - 可写: " + uploadDirFile.canWrite());
        
        System.out.println("处理目录: " + processedDirFullPath);
        System.out.println("  - 存在: " + processedDirFile.exists());
        System.out.println("  - 可读: " + processedDirFile.canRead());
        System.out.println("  - 可写: " + processedDirFile.canWrite());
        
        System.out.println("标注目录: " + annotatedDirFullPath);
        System.out.println("  - 存在: " + annotatedDirFile.exists());
        System.out.println("  - 可读: " + annotatedDirFile.canRead());
        System.out.println("  - 可写: " + annotatedDirFile.canWrite());
        
        System.out.println("临时目录: " + tempImagesDirFullPath);
        System.out.println("  - 存在: " + tempImagesDirFile.exists());
        System.out.println("  - 可读: " + tempImagesDirFile.canRead());
        System.out.println("  - 可写: " + tempImagesDirFile.canWrite());
        System.out.println("===============================");
    }
    
    /**
     * 确保路径格式正确（适用于文件URL）
     */
    private String ensureCorrectPathFormat(String path) {
        // 确保路径使用正斜杠
        String formattedPath = path.replace('\\', '/');
        
        // 确保路径不以斜杠结尾
        if (formattedPath.endsWith("/")) {
            formattedPath = formattedPath.substring(0, formattedPath.length() - 1);
        }
        
        return formattedPath;
    }
    
    /**
     * 确保目录存在
     */
    private void createDirectoryIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            System.out.println("创建目录 " + dirPath + ": " + (created ? "成功" : "失败"));
            if (!created) {
                System.err.println("无法创建目录: " + dir.getAbsolutePath());
                // 检查父目录权限
                File parentDir = dir.getParentFile();
                if (parentDir != null) {
                    System.err.println("父目录存在: " + parentDir.exists());
                    if (parentDir.exists()) {
                        System.err.println("父目录可写: " + parentDir.canWrite());
                    }
                }
            }
        }
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 设置所有Jackson消息转换器使用UTF-8编码
        converters.stream()
            .filter(converter -> converter instanceof MappingJackson2HttpMessageConverter)
            .forEach(converter -> ((MappingJackson2HttpMessageConverter) converter)
                .setDefaultCharset(StandardCharsets.UTF_8));
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        
        // --- 核心修复：设置正确的来源 ---
        // 允许通过 Nginx 访问的所有可能来源 (IP地址, localhost, 127.0.0.1)
        // 注意：这里没有端口号，因为Nginx监听的是80端口
        config.setAllowedOrigins(Arrays.asList("http://localhost", "http://127.0.0.1", "http://************"));
        
        // 允许所有HTTP方法
        config.addAllowedMethod("*");
        
        // 允许所有头
        config.addAllowedHeader("*");
        
        // 允许发送凭证 (如 cookie)
        config.setAllowCredentials(true);
        
        source.registerCorsConfiguration("/**", config);
        System.out.println("CORS 过滤器已配置，统一处理所有跨域请求。");
        return new CorsFilter(source);
    }
} 


// --- 文件结束: src\main\java\com\medical\annotation\config\WebConfig.java ---


// --- 文件开始: src\main\java\com\medical\annotation\config\SecurityConfig.java ---
// =================================================================================

package com.medical.annotation.config;

import com.medical.annotation.filter.AuthenticationFilter;
import com.medical.annotation.service.CustomUserDetailsService;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.firewall.StrictHttpFirewall;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    private UserService userService;
    
    @Autowired
    private CustomUserDetailsService customUserDetailsService;
    
    @Autowired
    private org.springframework.security.crypto.password.PasswordEncoder passwordEncoder;

    @Autowired
    private List<String> allowedOrigins;

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        // 让Spring Security完全忽略图片路径
        return (web) -> web.ignoring().antMatchers("/images/**", "/medical/images/**");
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        // 配置身份验证
        configureAuthentication(http);
        
        http
            .csrf().disable() // 禁用CSRF保护
            .cors().configurationSource(corsConfigurationSource())
            .and()
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS) // 无状态会话
            .and()
            .authorizeRequests()
                // 允许所有OPTIONS请求
                .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                // 允许所有人访问登录和注册接口
                .antMatchers("/api/users/authenticate").permitAll()
                .antMatchers("/api/users/register").permitAll()
                .antMatchers(HttpMethod.POST, "/api/users").permitAll()
                .antMatchers("/api/users").permitAll()
                // 图片资源已由web.ignoring()处理，此处无需配置
                // 允许访问仪表盘统计数据API
                .antMatchers("/api/stats-v2/dashboard/**").permitAll()
                .antMatchers("/api/stats-v2/dashboard-unrestricted/**").permitAll()
                .antMatchers("/api/stats-v3/dashboard/**").permitAll()
                .antMatchers("/api/stats/dashboard/**").permitAll()
                // 允许访问最近标注接口
                .antMatchers("/api/images/recent-annotations/**").permitAll()
                // 允许访问血管瘤诊断接口
                .antMatchers("/api/hemangioma-diagnoses/**").permitAll()
                .antMatchers("/medical/api/hemangioma-diagnoses/**").permitAll()
                // 需要认证访问的API
                .antMatchers("/api/**").authenticated()
                // 其他请求允许访问
                .anyRequest().permitAll()
            .and()
            .addFilterBefore(customAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)
            .headers()
                .frameOptions().sameOrigin(); // 允许同源iframe
        
        return http.build();
    }
    
    private void configureAuthentication(HttpSecurity http) throws Exception {
        AuthenticationManagerBuilder authBuilder = http.getSharedObject(AuthenticationManagerBuilder.class);
        
        // 配置UserDetailsService和密码编码器
        authBuilder.userDetailsService(customUserDetailsService)
                   .passwordEncoder(passwordEncoder);
        
        // 保留内存认证作为备用
        authBuilder.inMemoryAuthentication()
                .withUser("admin")
                .password(passwordEncoder.encode("admin"))
                .roles("ADMIN");
    }
    
    @Bean
    public HttpFirewall allowUrlEncodedSlashHttpFirewall() {
        StrictHttpFirewall firewall = new StrictHttpFirewall();
        firewall.setAllowUrlEncodedSlash(true);
        firewall.setAllowSemicolon(true);
        firewall.setAllowBackSlash(true);
        return firewall;
    }
    
    @Bean
    public AuthenticationFilter customAuthenticationFilter() {
        return new AuthenticationFilter(userService);
    }
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许特定来源
        configuration.addAllowedOrigin("http://localhost:8080");
        configuration.addAllowedOrigin("http://************:8080");
        configuration.addAllowedOrigin("http://************:8080");
        
        // 动态允许所有ngrok-free.app的子域
        configuration.addAllowedOriginPattern("https://*.ngrok-free.app");
        
        // 打印允许的来源，用于调试
        System.out.println("配置的允许来源: " + allowedOrigins);
        
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "X-Requested-With", "Accept", 
            "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers", 
            "X-User-Id", "X-User-Email", "X-Authentication-Email", "X-Request-Time", "x-user-custom-id"
        ));
        configuration.setExposedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "Content-Length",
            "Access-Control-Allow-Origin", "Access-Control-Allow-Headers"
        ));
        configuration.setAllowCredentials(true); // 允许凭证
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
} 


// --- 文件结束: src\main\java\com\medical\annotation\config\SecurityConfig.java ---

// --- 文件未找到: src\main\java\com\medical\annotation\controller\ImageController.java ---


// --- 文件开始: src\main\java\com\medical\annotation\controller\TagController.java ---
// =================================================================================

package com.medical.annotation.controller;

import com.medical.annotation.model.Tag;
import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.repository.HemangiomaDiagnosisRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.File;
import com.medical.annotation.service.FileService;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.awt.Graphics2D;
import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import javax.imageio.ImageIO;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping({"/api/tags", "/medical/api/tags", "/tags"})
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class TagController {

    @Autowired
    private TagRepository tagRepository;
    
    @Autowired
    private HemangiomaDiagnosisRepository hemangiomaDiagnosisRepository;

    @Autowired
    private FileService fileService;

    /**
     * 获取指定诊断ID的标签信息
     */
    @GetMapping({"/image/{id}", "image/{id}"})
    public ResponseEntity<?> getTagsForImage(@PathVariable Integer id) {
        try {
            System.out.println("查询图像ID " + id + " 的标签");
            List<Tag> tags = new ArrayList<>();
            
            // 首先按照hemangioma_diagnosis_id查询
            List<Tag> diagnosisTags = tagRepository.findByHemangiomaDiagnosisId(id);
            if (diagnosisTags != null && !diagnosisTags.isEmpty()) {
                tags.addAll(diagnosisTags);
                System.out.println("通过hemangiomaDiagnosisId找到 " + diagnosisTags.size() + " 个标签");
            }
            
            // 然后按照hemangioma_id查询
            List<Tag> hemangiomaTags = tagRepository.findByHemangioma_id(id.longValue());
            if (hemangiomaTags != null && !hemangiomaTags.isEmpty()) {
                // 关键修复：添加前进行去重
                List<Tag> uniqueHemangiomaTags = hemangiomaTags.stream()
                    .filter(hTag -> tags.stream().noneMatch(t -> t.getId().equals(hTag.getId())))
                    .collect(Collectors.toList());
                tags.addAll(uniqueHemangiomaTags);
                System.out.println("通过hemangioma_id找到 " + uniqueHemangiomaTags.size() + " 个独特标签");
            }
            
            // 最后按照metadata_id查询
            List<Tag> metadataTags = tagRepository.findByMetadataId(id);
            if (metadataTags != null && !metadataTags.isEmpty()) {
                // 过滤掉可能已经通过其他方式查询到的相同标签
                List<Tag> uniqueMetadataTags = metadataTags.stream()
                    .filter(mTag -> tags.stream().noneMatch(t -> t.getId().equals(mTag.getId())))
                    .collect(Collectors.toList());
                tags.addAll(uniqueMetadataTags);
                System.out.println("通过metadata_id找到 " + uniqueMetadataTags.size() + " 个独特标签");
            }
            
            System.out.println("共找到 " + tags.size() + " 个标签");
            return ResponseEntity.ok(tags);
        } catch (Exception e) {
            System.err.println("获取标签时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("获取标签失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建新标签
     */
    @PostMapping(value = {"", "/"}, consumes = {MediaType.APPLICATION_JSON_VALUE, "application/json;charset=UTF-8"})
    public ResponseEntity<?> createTag(@RequestBody Map<String, Object> requestBody, HttpServletRequest request) {
        try {
            System.out.println("开始创建新标签，原始请求数据: " + requestBody);
            
            // 将请求数据转换为Tag对象
            Tag tag = new Tag();
            
            // 设置基本属性
            if (requestBody.containsKey("tag")) {
                tag.setTag((String) requestBody.get("tag"));
            }
            
            // 处理坐标数据
            if (requestBody.containsKey("x")) {
                tag.setX(parseDouble(requestBody.get("x")));
            }
            
            if (requestBody.containsKey("y")) {
                tag.setY(parseDouble(requestBody.get("y")));
            }
            
            if (requestBody.containsKey("width")) {
                tag.setWidth(parseDouble(requestBody.get("width")));
            }
            
            if (requestBody.containsKey("height")) {
                tag.setHeight(parseDouble(requestBody.get("height")));
            }
            
            // 处理metadata_id字段
            if (requestBody.containsKey("metadata_id")) {
                Integer metadataId = parseInteger(requestBody.get("metadata_id"));
                tag.setMetadata_id(metadataId);
                
                // 同时设置hemangioma_id
                if (metadataId != null) {
                    tag.setHemangioma_id(metadataId.longValue());
                    System.out.println("已将hemangioma_id设置为与metadata_id相同的值: " + metadataId);
                }
            }
            
            // 处理created_by字段
            Long createdBy = null;
            
            // 1. 先从请求体获取
            if (requestBody.containsKey("created_by")) {
                createdBy = parseLong(requestBody.get("created_by"));
                System.out.println("从请求体中获取created_by: " + createdBy);
            }
            
            // 2. 如果请求体中没有，则从请求头获取
            if (createdBy == null) {
                String userIdHeader = request.getHeader("X-User-Id");
                if (userIdHeader != null && !userIdHeader.isEmpty()) {
                    try {
                        createdBy = Long.parseLong(userIdHeader);
                        System.out.println("从请求头获取用户ID: " + createdBy);
                    } catch (NumberFormatException e) {
                        System.out.println("请求头中的用户ID无法解析为Long: " + userIdHeader);
                    }
                }
            }
            
            // 设置创建者ID
            if (createdBy != null) {
                tag.setCreatedBy(createdBy);
                System.out.println("最终设置createdBy = " + createdBy);
            } else {
                System.out.println("警告: 未能获取到有效的创建者ID");
            }
            
            // 设置创建时间
            tag.setCreatedAt(LocalDateTime.now());
            
            // 打印最终标签数据，用于调试
            System.out.println("准备保存的标签数据: " + 
                             "tag=" + tag.getTag() + 
                             ", metadata_id=" + tag.getMetadata_id() + 
                             ", hemangioma_id=" + tag.getHemangioma_id() + 
                             ", createdBy=" + tag.getCreatedBy());
            
            // 保存标签
            Tag savedTag = tagRepository.save(tag);
            System.out.println("标签保存成功，ID: " + savedTag.getId() + 
                              ", createdBy: " + savedTag.getCreatedBy() + 
                              ", hemangioma_id: " + savedTag.getHemangioma_id());
            
            return ResponseEntity.status(HttpStatus.CREATED).body(savedTag);
        } catch (Exception e) {
            System.err.println("创建标签时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("创建标签失败: " + e.getMessage());
        }
    }
    
    // 辅助方法：安全解析Double
    private Double parseDouble(Object value) {
        if (value == null) return null;
        if (value instanceof Double) return (Double) value;
        if (value instanceof Number) return ((Number) value).doubleValue();
        if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    // 辅助方法：安全解析Integer
    private Integer parseInteger(Object value) {
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Number) return ((Number) value).intValue();
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    // 辅助方法：安全解析Long
    private Long parseLong(Object value) {
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Number) return ((Number) value).longValue();
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 更新标签
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateTag(@PathVariable Long id, @RequestBody Tag tagUpdate) {
        try {
            System.out.println("更新标签，ID: " + id);
            
            Optional<Tag> existingTagOpt = tagRepository.findById(id);
            if (!existingTagOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            Tag existingTag = existingTagOpt.get();
            
            // 更新字段
            if (tagUpdate.getTagName() != null) {
                existingTag.setTagName(tagUpdate.getTagName());
                existingTag.setTag(tagUpdate.getTagName()); // 同步更新旧字段
            }
            
            if (tagUpdate.getX() != null) existingTag.setX(tagUpdate.getX());
            if (tagUpdate.getY() != null) existingTag.setY(tagUpdate.getY());
            if (tagUpdate.getWidth() != null) existingTag.setWidth(tagUpdate.getWidth());
            if (tagUpdate.getHeight() != null) existingTag.setHeight(tagUpdate.getHeight());
            
            // 设置更新时间
            existingTag.setUpdatedAt(LocalDateTime.now());
            
            // 保存更新
            Tag updatedTag = tagRepository.save(existingTag);
            System.out.println("标签更新成功");
            
            return ResponseEntity.ok(updatedTag);
        } catch (Exception e) {
            System.err.println("更新标签时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("更新标签失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除标签
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteTag(@PathVariable Long id) {
        try {
            System.out.println("删除标签，ID: " + id);
            
            if (!tagRepository.existsById(id)) {
                return ResponseEntity.notFound().build();
            }
            
            tagRepository.deleteById(id);
            System.out.println("标签删除成功");
            
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            System.err.println("删除标签时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("删除标签失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存标注后的图像
     */
    @PostMapping("/annotated-image/{id}")
    public ResponseEntity<?> saveAnnotatedImage(@PathVariable Integer id) {
        try {
            System.out.println("保存标注后的图像，ID: " + id);
            
            // TODO: 实现图像处理逻辑
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "标注图像保存成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("保存标注图像时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("保存标注图像失败: " + e.getMessage());
        }
    }

    /**
     * 生成并保存带标注的图像
     * 此端点根据存储的标注数据生成图像并更新HemangiomaDiagnosis中的processedImagePath
     */
    @GetMapping("/generate-annotated-image/{id}")
    public ResponseEntity<?> generateAnnotatedImage(@PathVariable Integer id, 
                                                  @RequestParam(required = false) String mode) {
        try {
            System.out.println("生成标注图像，诊断ID: " + id);
            
            // 1. 获取诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(id);
            if (!diagnosisOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到诊断记录: " + id));
            }
            
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            
            // 2. 获取原始图像路径
            String originalImagePath = diagnosis.getImagePath();
            if (originalImagePath == null || originalImagePath.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "诊断记录中没有图像路径"));
            }
            
            // 3. 获取图像的所有标注
            List<Tag> tags = tagRepository.findByHemangiomaDiagnosisId(id);
            if (tags.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "没有找到标注数据"));
            }
            
            // 4. 读取原始图像
            String actualFilePath = fileService.getActualFilePath(originalImagePath);
            BufferedImage originalImage = fileService.readImage(actualFilePath);
            if (originalImage == null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "无法读取原始图像: " + actualFilePath));
            }
            
            // 5. 在图像上绘制标注框
            BufferedImage annotatedImage = drawAnnotationsOnImage(originalImage, tags);
            
            // 6. 生成新的文件名和保存路径
            String originalFileName = originalImagePath.substring(originalImagePath.lastIndexOf('/') + 1);
            String fileNameWithoutExt = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
            String extension = originalFileName.substring(originalFileName.lastIndexOf('.'));
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String newFileName = fileNameWithoutExt + "_annotated_" + timestamp + extension;
            
            // 创建annotated目录（如果不存在）
            String baseDir = originalImagePath.substring(0, originalImagePath.lastIndexOf('/'));
            String annotatedDir = baseDir + "/annotated";
            File dir = new File(fileService.getActualFilePath(annotatedDir));
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // 新图像的Web路径
            String newImageWebPath = annotatedDir + "/" + newFileName;
            String newImageFilePath = fileService.getActualFilePath(newImageWebPath);
            
            // 7. 保存标注后的图像
            File outputFile = new File(newImageFilePath);
            ImageIO.write(annotatedImage, extension.substring(1), outputFile);
            
            // 8. 更新诊断记录中的processedImagePath
            diagnosis.setProcessedImagePath(newImageWebPath);
            hemangiomaDiagnosisRepository.save(diagnosis);
            
            // 9. 返回成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "标注图像已生成并保存");
            response.put("originalPath", originalImagePath);
            response.put("processedPath", newImageWebPath);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "生成标注图像失败: " + e.getMessage()));
        }
    }
    
    /**
     * 在图像上绘制标注框
     */
    private BufferedImage drawAnnotationsOnImage(BufferedImage originalImage, List<Tag> tags) {
        // 创建图像副本，避免修改原始图像
        BufferedImage annotatedImage = new BufferedImage(
                originalImage.getWidth(), 
                originalImage.getHeight(), 
                BufferedImage.TYPE_INT_RGB);
        
        // 复制原始图像内容
        Graphics2D g2d = annotatedImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);
        
        // 设置绘图属性
        g2d.setStroke(new BasicStroke(3)); // 线宽
        
        // 为不同标签设置不同颜色
        Map<String, Color> tagColors = new HashMap<>();
        tagColors.put("IH", new Color(0, 255, 0)); // 红色
        tagColors.put("VM", new Color(0, 255, 0)); // 绿色 (原来是蓝色，现在改为绿色)
        tagColors.put("AVM", new Color(0, 255, 0)); // 绿色
        tagColors.put("MVM", new Color(0, 255, 0)); // 橙色
        tagColors.put("KHE", new Color(0, 255, 0)); // 紫色
        tagColors.put("NICH", new Color(0, 255, 0)); // 黄色
        tagColors.put("RICH", new Color(0, 255, 0)); // 青色
        tagColors.put("PICH", new Color(0, 255, 0)); // 品红色
        
        // 默认颜色
        Color defaultColor = new Color(0, 255, 0); // 绿色
        
        // 图像尺寸
        int imgWidth = originalImage.getWidth();
        int imgHeight = originalImage.getHeight();
        
        // 绘制每个标注框
        for (Tag tag : tags) {
            try {
                // 获取标签对应的颜色，如果没有预定义则使用默认颜色
                String tagName = tag.getTag() != null ? tag.getTag() : (tag.getTagName() != null ? tag.getTagName() : "IH");
                Color boxColor = tagColors.getOrDefault(tagName, defaultColor);
                g2d.setColor(boxColor);
                
                // 标注数据中的坐标可能是中心点坐标或左上角坐标，需要正确解析
                double normalizedX = tag.getX();
                double normalizedY = tag.getY();
                double normalizedWidth = tag.getWidth();
                double normalizedHeight = tag.getHeight();
                
                // 确定坐标系统类型 - 通过检查坐标值来判断
                // 如果x+width > 1 或 y+height > 1，则可能是像素坐标而非归一化坐标
                boolean isPixelCoordinates = (normalizedX + normalizedWidth > 1.1) || (normalizedY + normalizedHeight > 1.1);
                
                // 如果是像素坐标，转换为归一化坐标
                if (isPixelCoordinates) {
                    System.out.println("检测到像素坐标，转换为归一化坐标");
                    normalizedX = normalizedX / imgWidth;
                    normalizedY = normalizedY / imgHeight;
                    normalizedWidth = normalizedWidth / imgWidth;
                    normalizedHeight = normalizedHeight / imgHeight;
                }
                
                // 检查是否是中心点坐标系统
                // 如果x < width/2 或 y < height/2，则可能是中心点坐标
                boolean isCenterCoordinates = false;
                
                // 如果confidence大于0，通常表示是AI检测结果，使用中心点坐标
                if (tag.getConfidence() != null && tag.getConfidence() > 0) {
                    isCenterCoordinates = true;
                }
                
                // 计算像素坐标
                int x, y, width, height;
                
                if (isCenterCoordinates) {
                    // 中心点坐标系统
                    width = (int) (normalizedWidth * imgWidth);
                    height = (int) (normalizedHeight * imgHeight);
                    x = (int) (normalizedX * imgWidth - width / 2);
                    y = (int) (normalizedY * imgHeight - height / 2);
                    System.out.println("使用中心点坐标系统");
                } else {
                    // 左上角坐标系统
                    x = (int) (normalizedX * imgWidth);
                    y = (int) (normalizedY * imgHeight);
                    width = (int) (normalizedWidth * imgWidth);
                    height = (int) (normalizedHeight * imgHeight);
                    System.out.println("使用左上角坐标系统");
                }
                
                // 确保坐标不为负
                x = Math.max(0, x);
                y = Math.max(0, y);
                
                // 确保宽高不超出图像边界
                width = Math.min(width, imgWidth - x);
                height = Math.min(height, imgHeight - y);
                
                // 打印调试信息
                System.out.println("绘制标注框: " + tagName + 
                                  " 归一化坐标: (" + normalizedX + "," + normalizedY + ")" +
                                  " 像素坐标: (" + x + "," + y + ")" + 
                                  " 尺寸: " + width + "x" + height);
                
                // 绘制矩形框
                g2d.drawRect(x, y, width, height);
                
                // 绘制标签文本
                g2d.setFont(new Font("Arial", Font.BOLD, 14));
                g2d.drawString(tagName, x, y - 5);
            } catch (Exception e) {
                System.err.println("绘制标注框时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        g2d.dispose();
        return annotatedImage;
    }

    /**
     * 保存标注后的图像
     * 此端点用于标注修改后重新生成图像
     */
    @GetMapping("/save-image-after-annotation/{id}")
    public ResponseEntity<?> saveImageAfterAnnotation(@PathVariable Integer id,
                                                     @RequestParam(required = false) Integer metadata_id) {
        try {
            System.out.println("==== 保存标注后的图像请求开始 ====");
            System.out.println("路径参数ID: " + id);
            System.out.println("查询参数metadata_id: " + metadata_id);
            
            // 打印堆栈跟踪，帮助调试
            System.out.println("调用堆栈:");
            Thread.dumpStack();
            
            // 如果提供了metadata_id，优先使用它
            Integer imageId = metadata_id != null ? metadata_id : id;
            System.out.println("最终使用的图像ID: " + imageId);
            
            // 调用生成标注图像的方法
            ResponseEntity<?> result = generateAnnotatedImage(imageId, null);
            System.out.println("调用生成标注图像方法完成，状态码: " + result.getStatusCode());
            
            System.out.println("==== 保存标注后的图像请求结束 ====");
            return result;
        } catch (Exception e) {
            System.out.println("==== 保存标注后的图像请求异常 ====");
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "保存标注图像失败: " + e.getMessage()));
        }
    }
} 


// --- 文件结束: src\main\java\com\medical\annotation\controller\TagController.java ---


// --- 文件开始: src\main\java\com\medical\annotation\controller\UserController.java ---
// =================================================================================

 


// --- 文件结束: src\main\java\com\medical\annotation\controller\UserController.java ---


// --- 文件开始: src\main\java\com\medical\annotation\controller\TeamController.java ---
// =================================================================================

package com.medical.annotation.controller;

import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.model.Team;
import com.medical.annotation.model.User;
import com.medical.annotation.model.Tag;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.service.TeamService;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/teams")
public class TeamController {

    @Autowired
    private TeamService teamService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TagRepository tagRepository;
    
    /**
     * 创建团队
     */
    @PostMapping
    public ResponseEntity<?> createTeam(
            @RequestBody Team team,
            @RequestParam(value = "userId", required = false) Integer userIdParam) {
        try {
            Integer userId = userIdParam; // 首先使用请求参数中的userId
            
            // 如果请求参数中没有userId，尝试从SecurityContext获取当前用户
            if (userId == null) {
            try {
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                if (auth != null && auth.getName() != null && !auth.getName().equals("anonymousUser")) {
                    User currentUser = userService.getUserByEmail(auth.getName()).orElse(null);
                    if (currentUser != null) {
                        userId = currentUser.getId();
                    }
                }
            } catch (Exception e) {
                System.out.println("无法从认证上下文获取用户: " + e.getMessage());
                }
            }
            
            // 如果无法获取用户ID，使用默认管理员ID
            if (userId == null) {
                // 尝试查找系统中的第一个管理员用户
                List<User> admins = userRepository.findByRole(User.Role.ADMIN);
                if (!admins.isEmpty()) {
                    userId = admins.get(0).getId();
                } else {
                    // 如果没有管理员，使用ID为1的用户作为备选
                    userId = 1;
                }
            }
            
            System.out.println("创建团队使用的用户ID: " + userId);
            Team createdTeam = teamService.createTeam(team, userId);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(createdTeam);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 更新团队信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateTeam(@PathVariable("id") Integer id, @RequestBody Team team) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            team.setId(id);
            Team updatedTeam = teamService.updateTeam(team, currentUser.getId());
            
            return ResponseEntity.ok(updatedTeam);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 删除团队
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteTeam(@PathVariable Integer id, @RequestHeader("X-User-ID") Integer operatorId) {
        try {
            teamService.deleteTeam(id, operatorId);
            return ResponseEntity.ok().body(Map.of("message", "团队已成功解散"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        }
    }
    
    /**
     * 获取团队信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getTeam(@PathVariable("id") Integer id) {
        try {
            Team team = teamService.getTeamById(id);
            return ResponseEntity.ok(team);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取所有团队
     */
    @GetMapping
    public ResponseEntity<?> getAllTeams(@RequestParam(value = "keyword", required = false) String keyword) {
        try {
            List<Team> teams;
            if (keyword != null && !keyword.isEmpty()) {
                teams = teamService.searchTeams(keyword);
            } else {
                teams = teamService.getAllTeams();
            }
            
            // 转换为简单格式
            List<Map<String, Object>> result = teams.stream().map(team -> {
                Map<String, Object> teamMap = new HashMap<>();
                teamMap.put("id", team.getId());
                teamMap.put("name", team.getName());
                teamMap.put("description", team.getDescription());
                return teamMap;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取团队成员
     */
    @GetMapping("/{id}/members")
    public ResponseEntity<?> getTeamMembers(@PathVariable("id") Integer id) {
        try {
            List<User> members = teamService.getTeamMembers(id);
            return ResponseEntity.ok(members);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 添加团队成员（新接口，支持通过请求体传递用户ID）
     */
    @PostMapping("/{id}/members")
    public ResponseEntity<?> addTeamMemberByBody(
            @PathVariable("id") Integer id,
            @RequestBody Map<String, Object> requestBody) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            // 从请求体中获取userId
            Object userIdObj = requestBody.get("userId");
            if (userIdObj == null) {
                throw new Exception("用户ID不能为空");
            }
            
            Integer userId;
            try {
                userId = Integer.valueOf(userIdObj.toString());
            } catch (NumberFormatException e) {
                throw new Exception("无效的用户ID格式");
            }
            
            teamService.addUserToTeam(id, userId, currentUser.getId());
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "用户已成功添加到团队");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 移除团队成员
     */
    @DeleteMapping("/{id}/members/{userId}")
    public ResponseEntity<?> removeTeamMember(@PathVariable("id") Integer id, @PathVariable("userId") Integer userId) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            teamService.removeUserFromTeam(id, userId, currentUser.getId());
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "用户已成功从团队移除");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 搜索团队
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchTeams(@RequestParam("query") String query) {
        try {
            if (query == null || query.trim().isEmpty()) {
                return ResponseEntity.ok(teamService.getAllTeams());
            }
            
            List<Team> teams = teamService.searchTeams(query.trim());
            
            // 转换为前端需要的格式
            List<Map<String, Object>> result = teams.stream().map(team -> {
                Map<String, Object> teamMap = new HashMap<>();
                teamMap.put("id", team.getId());
                teamMap.put("name", team.getName());
                teamMap.put("code", team.getId().toString()); // 使用ID作为团队代码
                teamMap.put("description", team.getDescription());
                teamMap.put("memberCount", userRepository.countByTeam_Id(team.getId()));
                return teamMap;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取团队已通过的诊断记录
     */
    @GetMapping("/{id}/annotations/approved")
    public ResponseEntity<?> getTeamApprovedAnnotations(@PathVariable("id") Integer id) {
        try {
            List<HemangiomaDiagnosis> approvedDiagnoses = teamService.getTeamApprovedAnnotations(id);
            
            System.out.println("获取到已通过诊断数量: " + approvedDiagnoses.size());
            
            // 转换为包含必要信息的格式
            List<Map<String, Object>> result = approvedDiagnoses.stream().map(diagnosis -> {
                Map<String, Object> item = new HashMap<>();
                item.put("id", diagnosis.getId());
                
                // 生成病例号
                item.put("caseNumber", "CASE-" + diagnosis.getId());
                
                // 上传者信息
                item.put("uploadedByName", diagnosis.getUser() != null ? diagnosis.getUser().getName() : "未知用户");
                
                // 部位
                item.put("lesionLocation", diagnosis.getBodyPart());
                
                // 获取并组合标签
                List<Tag> tags = diagnosis.getTags();
                String tagNames = tags.stream().map(Tag::getTagName).collect(Collectors.joining(", "));
                item.put("tags", tagNames);
                
                // 使用创建时间作为审核时间
                if (diagnosis.getCreatedAt() != null) {
                    item.put("reviewDate", diagnosis.getCreatedAt().toString());
                    item.put("formattedReviewDate", diagnosis.getCreatedAt().toString());
                } else {
                    item.put("reviewDate", "");
                    item.put("formattedReviewDate", "");
                }
                
                // 获取图像路径
                item.put("imagePath", diagnosis.getImagePath());
                item.put("imageTwoPath", ""); // The concept of a second image is removed with ImagePair
                
                return item;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    @PostMapping("/{id}/transfer-ownership")
    public ResponseEntity<?> transferOwnership(@PathVariable Integer id, @RequestBody Map<String, Integer> payload, @RequestHeader("X-User-ID") Integer operatorId) {
        try {
            Integer newOwnerId = payload.get("newOwnerId");
            if (newOwnerId == null) {
                return ResponseEntity.badRequest().body(Map.of("message", "必须提供新所有者的ID"));
            }
            teamService.transferOwnership(id, newOwnerId, operatorId);
            return ResponseEntity.ok().body(Map.of("message", "团队所有权已成功转让"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        }
    }
} 


// --- 文件结束: src\main\java\com\medical\annotation\controller\TeamController.java ---


// --- 文件开始: src\main\java\com\medical\annotation\controller\HemangiomaDiagnosisController.java ---
// =================================================================================

package com.medical.annotation.controller;

import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.model.User;
import com.medical.annotation.model.Tag;
import com.medical.annotation.repository.HemangiomaDiagnosisRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.service.FileService;
import com.medical.annotation.util.FileNameGenerator;
import com.medical.annotation.util.BasePathConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.util.concurrent.CompletableFuture;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.medical.annotation.service.UserService;

@RestController
@RequestMapping({"/api/hemangioma-diagnoses", "/medical/api/hemangioma-diagnoses"})
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class HemangiomaDiagnosisController {

    @Autowired
    private HemangiomaDiagnosisRepository hemangiomaDiagnosisRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TagRepository tagRepository;
    
    @Autowired
    private FileService fileService;
    
    @Autowired
    private BasePathConfig basePathConfig;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private UserService userService;
    
    @Value("${ai.service.url:http://localhost:8086}")
    private String aiServiceUrl;
    
    private String tempDir;
    
    @PostConstruct
    public void init() {
        tempDir = basePathConfig.getTempDir();
        
        // 如果RestTemplate没有被Spring自动注入，手动创建一个
        if (restTemplate == null) {
            restTemplate = new RestTemplate();
        }
    }
    
    /**
     * 上传图像并创建诊断记录
     */
    @PostMapping("/upload-and-diagnose")
    public ResponseEntity<?> uploadAndDiagnose(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "patient_age", required = false) Integer patientAge,
            @RequestParam(value = "gender", required = false) String gender,
            @RequestParam(value = "origin_type", required = false) String originType,
            @RequestParam(value = "vessel_texture", required = false) String vesselTexture,
            @RequestParam(value = "user_id", required = false) Integer userId,
            @RequestParam(value = "annotationData", required = false) String annotationData,
            @RequestParam(value = "boundingBoxes", required = false) List<String> boundingBoxes,
            HttpServletRequest request) {
        try {
            // 1. 获取用户ID
            if (userId == null) {
                String userIdHeader = request.getHeader("X-User-ID");
                if (userIdHeader != null && !userIdHeader.isEmpty()) {
                    try {
                        // 处理可能包含逗号的复合ID格式（如"300000001, 3"）
                        if (userIdHeader.contains(",")) {
                            // 分割并返回去掉空格的第一个有效ID
                            String[] idParts = userIdHeader.split(",");
                            for (String idPart : idParts) {
                                String trimmedId = idPart.trim();
                                if (!trimmedId.isEmpty()) {
                                    System.out.println("从复合ID中提取第一个有效ID: " + trimmedId);
                                    userIdHeader = trimmedId;
                                    break;
                                }
                            }
                        }
                        
                        userId = Integer.parseInt(userIdHeader);
                        System.out.println("成功解析用户ID: " + userId);
                    } catch (NumberFormatException e) {
                        System.err.println("无效的用户ID格式: " + userIdHeader);
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                .body(Map.of("error", "无效的用户ID格式"));
                    }
                } else {
                    System.err.println("未提供用户ID");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(Map.of("error", "需要提供用户ID，请先登录"));
                }
            } else {
                System.out.println("使用表单参数中的用户ID: " + userId);
            }
            
            System.out.println("处理血管瘤诊断请求 - 用户ID: " + userId);
            
            // 2. 保存图像到temp目录
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String uniqueFilename = FileNameGenerator.generateUniqueFileName("hemangioma_" + fileExtension);
            
            // 确保temp目录存在
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                tempDirFile.mkdirs();
            }
            
            // 保存文件 - 使用临时文件机制
            Path targetPath = Paths.get(tempDir, uniqueFilename);
            File targetFile = targetPath.toFile();
            file.transferTo(targetFile);
            
            // 构建Web访问路径
            String webPath = "/medical/images/temp/" + uniqueFilename;
            System.out.println("图像已保存到: " + targetPath + ", Web路径: " + webPath);
            
            // 3. 创建HemangiomaDiagnosis记录 (暂时保留，但不更新AI检测结果)
            HemangiomaDiagnosis diagnosis = new HemangiomaDiagnosis();
            diagnosis.setPatientAge(patientAge);
            diagnosis.setGender(gender);
            diagnosis.setOriginType(originType);
            diagnosis.setVesselTexture(vesselTexture);
            diagnosis.setCreatedAt(LocalDateTime.now());
            diagnosis.setImagePath(webPath);
            if (annotationData != null) diagnosis.setAnnotationData(annotationData);
            if (boundingBoxes != null) diagnosis.setBoundingBoxes(new ArrayList<>(boundingBoxes));

            // 设置用户关联
            Optional<User> user = userRepository.findById(userId);
            if (user.isPresent()) {
                diagnosis.setUser(user.get());
            } else {
                // 找不到用户，返回错误
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "无法找到ID为" + userId + "的用户"));
            }
            
            // 保存诊断记录
            HemangiomaDiagnosis savedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
            System.out.println("诊断记录已保存，ID: " + savedDiagnosis.getId());
            
            // 4. 调用AI服务进行快速YOLO检测
            System.out.println("\n开始调用AI服务进行血管瘤YOLO检测..");
            Map<String, Object> yoloResult = callAiServiceOnlyYolo(targetFile, patientAge, gender, originType, vesselTexture, savedDiagnosis.getId());
            System.out.println("YOLO检测完成，返回结果: " + yoloResult);
            
            // 5. 使用YOLO返回的数据更新诊断记录
            if (yoloResult != null && !yoloResult.isEmpty()) {
                // 更新基本信息
                if (yoloResult.containsKey("detected_type")) {
                    savedDiagnosis.setDetectedType((String) yoloResult.get("detected_type"));
                }
                
                // 更新置信度字段
                if (yoloResult.containsKey("confidence")) {
                    savedDiagnosis.setConfidence(((Number) yoloResult.get("confidence")).doubleValue());
                }
                
                // 设置处理后的图像路径
                if (yoloResult.containsKey("processed_image_path")) {
                    savedDiagnosis.setProcessedImagePath((String) yoloResult.get("processed_image_path"));
                    System.out.println("设置处理后图像路径: " + yoloResult.get("processed_image_path"));
                }
                
                // 添加对颜色和部位的处理
                if (yoloResult.containsKey("predicted_color")) {
                    Map<String, Object> colorInfo = (Map<String, Object>) yoloResult.get("predicted_color");
                    if (colorInfo != null && colorInfo.containsKey("name")) {
                        String colorName = (String) colorInfo.get("name");
                        savedDiagnosis.setColor(colorName);
                        System.out.println("设置颜色: " + colorName);
                    }
                }
                
                if (yoloResult.containsKey("predicted_part")) {
                    Map<String, Object> partInfo = (Map<String, Object>) yoloResult.get("predicted_part");
                    if (partInfo != null && partInfo.containsKey("name")) {
                        String partName = (String) partInfo.get("name");
                        savedDiagnosis.setBodyPart(partName);
                        System.out.println("设置部位: " + partName);
                    }
                }
                
                // 保存更新后的诊断记录
                savedDiagnosis = hemangiomaDiagnosisRepository.save(savedDiagnosis);
                System.out.println("诊断记录更新成功，ID: " + savedDiagnosis.getId());
            }
            
            // 6. 异步处理大模型生成建议
            // 启动异步任务生成详细诊断建议
            generateRecommendationAsync(savedDiagnosis.getId(), targetFile, patientAge, gender, originType, vesselTexture);
            
            // 7. 添加延迟，确保数据库操作完成
            try {
                System.out.println("添加延迟，确保数据库操作完成...");
                Thread.sleep(1000); // 延迟1秒
                
                // 重新查询诊断记录，确保获取最新数据
                Optional<HemangiomaDiagnosis> refreshedDiagnosis = hemangiomaDiagnosisRepository.findById(savedDiagnosis.getId());
                if (refreshedDiagnosis.isPresent()) {
                    savedDiagnosis = refreshedDiagnosis.get();
                    System.out.println("已重新加载诊断记录，ID: " + savedDiagnosis.getId());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("延迟等待被中断: " + e.getMessage());
            }
            
            // 8. 立即返回YOLO检测结果
            return ResponseEntity.status(HttpStatus.CREATED).body(savedDiagnosis);
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "处理诊断请求失败: " + e.getMessage()));
        }
    }
    
    /**
     * 调用AI服务进行诊断
     */
    private Map<String, Object> callAiService(File imageFile, Integer diagnosisId, Integer patientAge, String gender, String originType, String vesselTexture) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new FileSystemResource(imageFile));
            
        // 添加诊断ID参数
        body.add("diagnosis_id", diagnosisId != null ? diagnosisId.toString() : "");
        
        // 添加所有元数据
        if (patientAge != null) body.add("patient_age", patientAge.toString());
        if (gender != null) body.add("gender", gender);
        if (originType != null) body.add("origin_type", originType);
        if (vesselTexture != null) body.add("vessel_texture", vesselTexture);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
        String url = aiServiceUrl + "/diagnose"; // 新的Python端点
        try {
            System.out.println("开始调用AI服务，URL: " + url + ", 诊断ID: " + diagnosisId);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
            System.out.println("AI服务返回状态码: " + response.getStatusCodeValue());
            return response.getBody();
        } catch (Exception e) {
            e.printStackTrace();
            // 返回一个包含错误信息的Map，以便上层可以处理
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "调用AI服务失败: " + e.getMessage());
            return errorMap;
        }
    }
    
    /**
     * 调用AI服务仅进行YOLO检测，不等待大模型处理
     */
    private Map<String, Object> callAiServiceOnlyYolo(File imageFile, Integer patientAge, String gender, String originType, String vesselTexture, Integer diagnosisId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new FileSystemResource(imageFile));
        body.add("only_yolo", "true"); // 添加参数告知AI服务只进行YOLO检测
        body.add("diagnosis_id", diagnosisId);
        
        // 添加所有元数据
        if (patientAge != null) body.add("patient_age", patientAge.toString());
        if (gender != null) body.add("gender", gender);
        if (originType != null) body.add("origin_type", originType);
        if (vesselTexture != null) body.add("vessel_texture", vesselTexture);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        String url = aiServiceUrl + "/diagnose-yolo"; // 新的仅YOLO检测端点
        try {
            System.out.println("开始调用YOLO检测服务，URL: " + url);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
            Map<String, Object> result = response.getBody();
            
            System.out.println("YOLO检测服务返回状态码: " + response.getStatusCodeValue());
            System.out.println("YOLO检测结果: " + (result != null ? result.keySet() : "null"));
            
            // 处理标注框数据
            if (result != null) {
                if (result.containsKey("detection_boxes")) {
                    List<Map<String, Object>> boxes = (List<Map<String, Object>>) result.get("detection_boxes");
                    System.out.println("YOLO检测返回了标注框数据，数量: " + boxes.size());
                    System.out.println("标注框数据示例: " + (!boxes.isEmpty() ? boxes.get(0) : "无"));
                    
                    // 保存标注框数据
                    saveDetectionBoxes(diagnosisId, result);
                } else if (result.containsKey("tags")) {
                    // 新的数据结构，使用tags字段
                    System.out.println("发现新的数据结构: 使用tags字段作为标注数据");
                    List<Map<String, Object>> tags = (List<Map<String, Object>>) result.get("tags");
                    System.out.println("YOLO检测返回了标签数据，数量: " + (tags != null ? tags.size() : 0));
                    System.out.println("标签数据示例: " + (tags != null && !tags.isEmpty() ? tags.get(0) : "无"));
                    
                    // 创建包含标签数据的新Map
                    Map<String, Object> tagsResult = new HashMap<>(result);
                    // 将tags字段映射为detection_boxes字段，保持向后兼容
                    tagsResult.put("detection_boxes", result.get("tags"));
                    
                    // 保存标注框数据
                    saveDetectionBoxes(diagnosisId, tagsResult);
                } else {
                    System.out.println("YOLO检测结果中不包含detection_boxes或tags字段");
                }
                
                if (result.containsKey("error")) {
                    System.err.println("YOLO检测服务返回错误: " + result.get("error"));
                }
            } else {
                System.out.println("YOLO检测服务返回空结果");
            }
            
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            // 返回一个包含错误信息的Map，以便上层可以处理
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "调用YOLO检测服务失败: " + e.getMessage());
            return errorMap;
        }
    }
    
    /**
     * 保存YOLO检测返回的标注框数据
     */
    private void saveDetectionBoxes(Integer diagnosisId, Map<String, Object> yoloResult) {
        try {
            System.out.println("开始保存标注框数据，诊断ID: " + diagnosisId);
            
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(diagnosisId);
            if (!diagnosisOpt.isPresent()) {
                System.err.println("找不到诊断记录ID: " + diagnosisId + "，无法保存标注框");
                return;
            }
            
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            System.out.println("找到诊断记录: " + diagnosis.getId());
            
            // 提取检测框数据
            Object detectionBoxesObj = yoloResult.get("detection_boxes");
            System.out.println("检测框数据类型: " + (detectionBoxesObj != null ? detectionBoxesObj.getClass().getName() : "null"));
            System.out.println("YOLO结果键集: " + yoloResult.keySet());
            
            // 如果detection_boxes为null，尝试从tags字段获取
            if (detectionBoxesObj == null && yoloResult.containsKey("tags")) {
                detectionBoxesObj = yoloResult.get("tags");
                System.out.println("从tags字段获取标注数据，类型: " + (detectionBoxesObj != null ? detectionBoxesObj.getClass().getName() : "null"));
            }
            
            // 处理不同格式的数据
            List<Map<String, Object>> detectionBoxes;
            if (detectionBoxesObj instanceof List) {
                try {
                    detectionBoxes = (List<Map<String, Object>>) detectionBoxesObj;
                } catch (ClassCastException e) {
                    System.err.println("无法转换检测框数据为List<Map>: " + e.getMessage());
                    
                    // 尝试转换每个元素
                    List<?> rawList = (List<?>) detectionBoxesObj;
                    detectionBoxes = new ArrayList<>();
                    for (Object item : rawList) {
                        if (item instanceof Map) {
                            detectionBoxes.add((Map<String, Object>) item);
                        } else {
                            System.err.println("检测框数据项类型不是Map: " + (item != null ? item.getClass().getName() : "null"));
                        }
                    }
                }
            } else {
                System.err.println("检测框数据不是List类型");
                detectionBoxes = new ArrayList<>();
            }
            
            if (detectionBoxes == null || detectionBoxes.isEmpty()) {
                System.out.println("没有检测到标注框数据");
                return;
            }
            
            System.out.println("检测到 " + detectionBoxes.size() + " 个标注框，准备保存");
            
            // 先删除现有的标签，避免重复
            List<Tag> existingTags = tagRepository.findByAnyDiagnosisId(diagnosisId);
            if (!existingTags.isEmpty()) {
                System.out.println("删除 " + existingTags.size() + " 个现有标签");
                tagRepository.deleteAll(existingTags);
            }
            
            // 创建标签对象并保存
            List<Tag> tags = new ArrayList<>();
            for (Map<String, Object> box : detectionBoxes) {
                System.out.println("处理标注框: " + box);
                
                Tag tag = new Tag();
                tag.setHemangiomaDiagnosis(diagnosis);
                tag.setHemangioma_id(diagnosis.getId().longValue()); // 同时设置hemangioma_id字段
                
                // 设置标签类型
                if (box.containsKey("class")) {
                    String className = String.valueOf(box.get("class"));
                    tag.setTagName(className);
                    System.out.println("标签类型: " + className);
                } else if (box.containsKey("class_name")) {
                    // 新数据结构使用class_name字段
                    String className = String.valueOf(box.get("class_name"));
                    tag.setTagName(className);
                    System.out.println("标签类型(新格式): " + className);
                } else {
                    tag.setTagName("未知类型");
                    System.out.println("标签类型: 未知类型 (未找到class或class_name字段)");
                }
                
                // 设置坐标和尺寸 (确保是相对坐标，范围0-1)
                try {
                    // 检查是否有直接的相对坐标
                    if (box.containsKey("x") && box.containsKey("y") && box.containsKey("width") && box.containsKey("height")) {
                        // 旧格式，直接使用相对坐标
                        double x = parseNumberValue(box.get("x"));
                        double y = parseNumberValue(box.get("y"));
                        double width = parseNumberValue(box.get("width"));
                        double height = parseNumberValue(box.get("height"));
                        
                        tag.setX(roundToSixDecimals(x));
                        tag.setY(roundToSixDecimals(y));
                        tag.setWidth(roundToSixDecimals(width));
                        tag.setHeight(roundToSixDecimals(height));
                        
                        System.out.println("使用相对坐标: x=" + x + ", y=" + y + ", width=" + width + ", height=" + height);
                    } else if (box.containsKey("x_center") && box.containsKey("y_center") && 
                               box.containsKey("width") && box.containsKey("height")) {
                        // 新格式，使用中心点相对坐标
                        double centerX = parseNumberValue(box.get("x_center"));
                        double centerY = parseNumberValue(box.get("y_center"));
                        double width = parseNumberValue(box.get("width"));
                        double height = parseNumberValue(box.get("height"));
                        
                        // 设置相对坐标
                        tag.setX(roundToSixDecimals(centerX));
                        tag.setY(roundToSixDecimals(centerY));
                        tag.setWidth(roundToSixDecimals(width));
                        tag.setHeight(roundToSixDecimals(height));
                        
                        System.out.println("使用中心点相对坐标: centerX=" + centerX + ", centerY=" + centerY + 
                                          ", width=" + width + ", height=" + height);
                    } else if (box.containsKey("bbox")) {
                        // 使用绝对坐标bbox [x1, y1, x2, y2]
                        Object bboxObj = box.get("bbox");
                        
                        if (bboxObj instanceof List) {
                            List<?> bboxList = (List<?>) bboxObj;
                            if (bboxList.size() >= 4) {
                                int x1 = ((Number) bboxList.get(0)).intValue();
                                int y1 = ((Number) bboxList.get(1)).intValue();
                                int x2 = ((Number) bboxList.get(2)).intValue();
                                int y2 = ((Number) bboxList.get(3)).intValue();
                                
                                // 获取原始图像尺寸
                                int origWidth = 0;
                                int origHeight = 0;
                                
                                if (box.containsKey("orig_width") && box.containsKey("orig_height")) {
                                    origWidth = ((Number) box.get("orig_width")).intValue();
                                    origHeight = ((Number) box.get("orig_height")).intValue();
                                } else {
                                    // 如果没有原始尺寸，尝试从结果中获取
                                    Map<String, Integer> dimensions = (Map<String, Integer>) yoloResult.get("image_dimensions");
                                    if (dimensions != null) {
                                        origWidth = dimensions.get("width");
                                        origHeight = dimensions.get("height");
                                    }
                                }
                                
                                if (origWidth <= 0 || origHeight <= 0) {
                                    System.err.println("无法获取原始图像尺寸，无法计算相对坐标");
                                    continue;
                                }
                                
                                // 计算中心点坐标
                                double centerX = (x1 + x2) / 2.0 / origWidth;
                                double centerY = (y1 + y2) / 2.0 / origHeight;
                                
                                // 计算宽高
                                double width = (x2 - x1) * 1.0 / origWidth;
                                double height = (y2 - y1) * 1.0 / origHeight;
                                
                                // 设置相对坐标
                                tag.setX(roundToSixDecimals(centerX));
                                tag.setY(roundToSixDecimals(centerY));
                                tag.setWidth(roundToSixDecimals(width));
                                tag.setHeight(roundToSixDecimals(height));
                                
                                System.out.println("从bbox转换为相对坐标: centerX=" + centerX + ", centerY=" + centerY + 
                                                  ", width=" + width + ", height=" + height);
                            }
                        }
                    }
                    
                    // 设置置信度
                    if (box.containsKey("confidence")) {
                        Object confValue = box.get("confidence");
                        double confidence = parseNumberValue(confValue);
                        tag.setConfidence(roundToSixDecimals(confidence));
                        System.out.println("置信度: " + confidence + " (" + confValue.getClass().getName() + ")");
                    }
                } catch (Exception e) {
                    System.err.println("处理标注框坐标时出错: " + e.getMessage());
                    e.printStackTrace();
                }
                
                // 设置创建者ID为系统用户(1)
                tag.setCreatedBy(1L);
                
                // 验证标签数据完整性
                if (tag.getX() != null && tag.getY() != null && tag.getWidth() != null && tag.getHeight() != null) {
                    // 确保hemangioma_id字段已设置
                    if (tag.getHemangioma_id() == null && diagnosis != null) {
                        tag.setHemangioma_id(diagnosis.getId().longValue());
                    }
                    
                    // 确保tag字段已设置
                    if (tag.getTag() == null && tag.getTagName() != null) {
                        tag.setTag(tag.getTagName());
                    } else if (tag.getTagName() == null && tag.getTag() != null) {
                        tag.setTagName(tag.getTag());
                    }
                    
                    tags.add(tag);
                    System.out.println("添加有效标签: " + tag.getTagName() + " 位置: (" + tag.getX() + "," + tag.getY() + "," + tag.getWidth() + "," + tag.getHeight() + ")");
                } else {
                    System.err.println("标签数据不完整，跳过保存: X=" + tag.getX() + ", Y=" + tag.getY() + 
                                     ", Width=" + tag.getWidth() + ", Height=" + tag.getHeight());
                }
            }
            
            // 保存所有标签
            if (!tags.isEmpty()) {
                List<Tag> savedTags = tagRepository.saveAll(tags);
                System.out.println("成功保存 " + savedTags.size() + " 个标签，ID列表: " + 
                                  savedTags.stream().map(t -> t.getId().toString()).reduce("", (a, b) -> a + "," + b));
                
                // 检查是否能从数据库中查询到保存的标签
                List<Tag> verifyTags = tagRepository.findByAnyDiagnosisId(diagnosisId);
                System.out.println("验证查询: 数据库中找到 " + verifyTags.size() + " 个标签");
            } else {
                System.err.println("没有有效的标签数据可保存");
            }
            
        } catch (Exception e) {
            System.err.println("保存标注框数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析各种类型的数值
     */
    private double parseNumberValue(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            return Double.parseDouble((String) value);
        } else if (value instanceof Map) {
            // 处理JSON结构可能的数值表示
            Map<?, ?> map = (Map<?, ?>) value;
            if (map.containsKey("value")) {
                return parseNumberValue(map.get("value"));
            }
        }
        throw new IllegalArgumentException("无法解析数值: " + value);
    }
    
    /**
     * 异步执行大模型生成建议
     */
    private void generateRecommendationAsync(final Integer diagnosisId, final File imageFile, 
                                           final Integer patientAge, final String gender, 
                                           final String originType, final String vesselTexture) {
        // 使用线程池异步执行
        CompletableFuture.runAsync(() -> {
            try {
                System.out.println("开始异步生成诊断建议，诊断ID: " + diagnosisId);
                
                // 调用完整的AI服务（包括大模型），传递诊断ID
                Map<String, Object> aiResult = callAiService(imageFile, diagnosisId, patientAge, gender, originType, vesselTexture);
                
                if (aiResult != null && !aiResult.containsKey("error")) {
                    // 查找诊断记录
                    Optional<HemangiomaDiagnosis> optDiagnosis = hemangiomaDiagnosisRepository.findById(diagnosisId);
                    if (optDiagnosis.isPresent()) {
                        HemangiomaDiagnosis diagnosis = optDiagnosis.get();
                        
                        // 更新LLM生成的建议
                        if (aiResult.containsKey("diagnostic_summary")) {
                            diagnosis.setDiagnosticSummary((String) aiResult.get("diagnostic_summary"));
                        }
                        if (aiResult.containsKey("treatment_suggestion")) {
                            diagnosis.setTreatmentSuggestion((String) aiResult.get("treatment_suggestion"));
                        }
                        if (aiResult.containsKey("precautions")) {
                            diagnosis.setPrecautions((String) aiResult.get("precautions"));
                        }
                        if (aiResult.containsKey("disclaimer")) {
                            diagnosis.setDisclaimer((String) aiResult.get("disclaimer"));
                        }
                        
                        // 保存更新后的诊断记录
                        hemangiomaDiagnosisRepository.save(diagnosis);
                        System.out.println("异步生成诊断建议完成，已更新诊断记录 ID: " + diagnosisId);
                    } else {
                        System.err.println("无法找到诊断记录 ID: " + diagnosisId);
                    }
                } else {
                    System.err.println("异步生成诊断建议失败: " + (aiResult != null ? aiResult.get("error") : "未知错误"));
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("异步生成诊断建议时发生错误: " + e.getMessage());
            }
        });
    }
    
    private double roundToSixDecimals(double value) {
        return Math.round(value * 1_000_000.0) / 1_000_000.0;
    }
    
    /**
     * 获取所有诊断记录
     */
    @GetMapping
    public ResponseEntity<?> getAllDiagnoses() {
        List<HemangiomaDiagnosis> diagnoses = hemangiomaDiagnosisRepository.findAll();
        return ResponseEntity.ok(diagnoses);
    }
    
    @GetMapping("/pending")
    public ResponseEntity<Page<HemangiomaDiagnosis>> getPendingDiagnoses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "created_at,desc") String sort) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentPrincipalName = authentication.getName();
        User currentUser = userRepository.findByEmail(currentPrincipalName)
                .orElseThrow(() -> new RuntimeException("当前登录用户不存在: " + currentPrincipalName));

        // 创建一个不包含排序的Pageable对象
        Pageable pageable = PageRequest.of(page, size);

        Page<HemangiomaDiagnosis> diagnosesPage;
        
        // 先获取枚举类型，再获取其名称进行比较，这样更健壮
        String userRole = (currentUser.getRole() != null) ? currentUser.getRole().name() : "";

        if ("ADMIN".equals(userRole)) {
            diagnosesPage = hemangiomaDiagnosisRepository.findAllPendingForAdmin(pageable);
        } else if ("REVIEWER".equals(userRole)) {
            Integer teamId = Optional.ofNullable(currentUser.getTeam()).map(team -> team.getId()).orElse(null);
            if (teamId == null) {
                 // 如果审核员没有团队，理论上他不应该能审核任何与团队相关的病例
                 // 但根据需求，他可以审核无团队医生的病例。为了简化，我们这里假设他能看到所有无团队的。
                 // 注意：这个逻辑可能需要根据实际需求调整。此处我们假设 teamId 为一个数据库中不存在的负数来仅匹配 IS NULL。
                 // 一个更好的方法是修改Query，但这需要更复杂的动态查询。
                 // 暂定为可以看到所有无团队提交的内容
                 diagnosesPage = hemangiomaDiagnosisRepository.findAllPendingForReviewer(null, pageable);
            } else {
                diagnosesPage = hemangiomaDiagnosisRepository.findAllPendingForReviewer(teamId, pageable);
            }
        } else {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }

        return ResponseEntity.ok(diagnosesPage);
    }

    /**
     * 获取单个诊断记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getDiagnosisById(@PathVariable Integer id) {
        Optional<HemangiomaDiagnosis> diagnosis = hemangiomaDiagnosisRepository.findById(id);
        if (diagnosis.isPresent()) {
            return ResponseEntity.ok(diagnosis.get());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "未找到指定ID的诊断记录"));
        }
    }

    /**
     * 接收AI服务回调的诊断建议数据
     */
    @PostMapping("/update-recommendation")
    public ResponseEntity<?> updateRecommendation(@RequestBody Map<String, Object> payload) {
        try {
            // 从请求体中提取诊断ID和建议JSON
            Integer diagnosisId = (Integer) payload.get("diagnosisId");
            String recommendation = (String) payload.get("recommendation");
            
            System.out.println("接收到AI服务回调，更新诊断ID: " + diagnosisId);
            
            if (diagnosisId == null || recommendation == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "缺少必要参数：diagnosisId或recommendation"));
            }
            
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> optDiagnosis = hemangiomaDiagnosisRepository.findById(diagnosisId);
            if (!optDiagnosis.isPresent()) {
                System.out.println("未找到指定ID的诊断记录: " + diagnosisId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到指定ID的诊断记录"));
            }
            
            HemangiomaDiagnosis diagnosis = optDiagnosis.get();
            
            // 处理颜色信息
            if (payload.containsKey("predictedColor")) {
                Map<String, Object> colorInfo = (Map<String, Object>) payload.get("predictedColor");
                if (colorInfo != null && colorInfo.containsKey("name")) {
                    String colorName = (String) colorInfo.get("name");
                    diagnosis.setColor(colorName);
                    System.out.println("设置颜色: " + colorName);
                }
            }
            
            // 处理部位信息
            if (payload.containsKey("predictedPart")) {
                Map<String, Object> partInfo = (Map<String, Object>) payload.get("predictedPart");
                if (partInfo != null && partInfo.containsKey("name")) {
                    String partName = (String) partInfo.get("name");
                    diagnosis.setBodyPart(partName);
                    System.out.println("设置部位: " + partName);
                }
            }
            
            // 解析JSON字符串中的建议
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, String> recMap = mapper.readValue(recommendation, Map.class);
                System.out.println("成功解析建议JSON: " + recMap);
                
                // 更新诊断记录中的建议字段
                if (recMap.containsKey("treatment_suggestion")) {
                    diagnosis.setTreatmentSuggestion(recMap.get("treatment_suggestion"));
                }
                if (recMap.containsKey("precautions")) {
                    diagnosis.setPrecautions(recMap.get("precautions"));
                }
                if (recMap.containsKey("disclaimer")) {
                    diagnosis.setDisclaimer(recMap.get("disclaimer"));
                }
                
                // 保存更新后的诊断记录
                HemangiomaDiagnosis updatedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
                System.out.println("成功更新诊断记录的建议，ID: " + diagnosisId);
                return ResponseEntity.ok(Map.of(
                    "success", true, 
                    "message", "成功更新诊断建议", 
                    "id", updatedDiagnosis.getId()
                ));
            } catch (Exception e) {
                System.out.println("解析建议JSON失败: " + e.getMessage());
                e.printStackTrace();
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "解析建议JSON失败: " + e.getMessage()));
            }
        } catch (Exception e) {
            System.out.println("更新诊断建议时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "更新诊断建议失败: " + e.getMessage()));
        }
    }

    /**
     * 为现有诊断记录生成LLM建议
     * @param id 诊断记录ID
     * @return 更新后的诊断记录
     */
    @PostMapping("/{id}/generate-recommendation")
    public ResponseEntity<?> generateRecommendationForExistingRecord(@PathVariable Integer id) {
        try {
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(id);
            if (!diagnosisOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到指定ID的诊断记录"));
            }
            
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            String imagePath = diagnosis.getImagePath();
            
            // 检查图像路径是否有效
            if (imagePath == null || imagePath.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "诊断记录缺少图像路径"));
            }
            
            // 将Web路径转换为文件系统路径
            String filePath = fileService.convertWebPathToFilePath(imagePath);
            File imageFile = new File(filePath);
            
            if (!imageFile.exists()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "找不到图像文件: " + filePath));
            }
            
            // 获取诊断元数据
            Integer patientAge = diagnosis.getPatientAge();
            String gender = diagnosis.getGender();
            String originType = diagnosis.getOriginType();
            String vesselTexture = diagnosis.getVesselTexture();
            
            // 启动异步任务生成详细诊断建议
            generateRecommendationAsync(id, imageFile, patientAge, gender, originType, vesselTexture);
            
            // 立即执行YOLO检测并更新记录
            Map<String, Object> yoloResult = callAiServiceOnlyYolo(imageFile, patientAge, gender, originType, vesselTexture, id);
            
            // 使用YOLO返回的数据更新诊断记录
            if (yoloResult != null && !yoloResult.isEmpty()) {
                // 更新基本信息
                if (yoloResult.containsKey("detected_type")) {
                    diagnosis.setDetectedType((String) yoloResult.get("detected_type"));
                }
                
                // 更新置信度字段
                if (yoloResult.containsKey("confidence")) {
                    diagnosis.setConfidence(((Number) yoloResult.get("confidence")).doubleValue());
                }
                
                // 设置处理后的图像路径
                if (yoloResult.containsKey("processed_image_path")) {
                    diagnosis.setProcessedImagePath((String) yoloResult.get("processed_image_path"));
                }
                
                // 添加对颜色和部位的处理
                if (yoloResult.containsKey("predicted_color")) {
                    Map<String, Object> colorInfo = (Map<String, Object>) yoloResult.get("predicted_color");
                    if (colorInfo != null && colorInfo.containsKey("name")) {
                        String colorName = (String) colorInfo.get("name");
                        diagnosis.setColor(colorName);
                        System.out.println("设置颜色: " + colorName);
                    }
                }
                
                if (yoloResult.containsKey("predicted_part")) {
                    Map<String, Object> partInfo = (Map<String, Object>) yoloResult.get("predicted_part");
                    if (partInfo != null && partInfo.containsKey("name")) {
                        String partName = (String) partInfo.get("name");
                        diagnosis.setBodyPart(partName);
                        System.out.println("设置部位: " + partName);
                    }
                }
                
                // 保存更新后的诊断记录
                diagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
            }
            
            return ResponseEntity.ok()
                    .body(Map.of(
                        "message", "已启动生成诊断建议的任务，请稍后查看结果",
                        "diagnosis_id", id,
                        "yolo_result", yoloResult != null ? yoloResult : "无YOLO检测结果"
                    ));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "生成诊断建议失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据用户ID获取病例列表 - 支持前端"/api/images/my-images"请求
     * @param userId 用户ID或customId
     * @param status 可选的状态筛选
     * @return 用户的病例列表
     */
    @GetMapping("/by-user")
    public ResponseEntity<?> getDiagnosesByUser(
            @RequestParam(value = "userId", required = true) String userId,
            @RequestParam(value = "status", required = false) String status) {
        
        System.out.println("收到按用户查询诊断记录的请求 - 用户ID: " + userId + ", 状态: " + status);
        
        try {
            // 根据 custom_id 找到用户
            Optional<User> userOpt = userService.getUserByCustomId(userId);
            
            if (!userOpt.isPresent()) {
                System.err.println("找不到用户，ID: " + userId);
                return ResponseEntity.ok(new ArrayList<>());
            }
            
            // 根据用户和可选的状态参数查询病例
            List<HemangiomaDiagnosis> diagnoses;
            if (status != null && !status.isEmpty()) {
                try {
                    HemangiomaDiagnosis.Status statusEnum = HemangiomaDiagnosis.Status.valueOf(status);
                    diagnoses = hemangiomaDiagnosisRepository.findByUserAndStatus(userOpt.get(), statusEnum);
                } catch (IllegalArgumentException e) {
                    System.out.println("无效的状态参数: " + status + "，返回所有病例");
                    diagnoses = hemangiomaDiagnosisRepository.findByUser(userOpt.get());
                }
            } else {
                diagnoses = hemangiomaDiagnosisRepository.findByUser(userOpt.get());
            }
            
            System.out.println("找到 " + diagnoses.size() + " 条病例记录");
            return ResponseEntity.ok(diagnoses);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "获取病例列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取标签数据
     */
    @GetMapping("/{id}/tags")
    public ResponseEntity<?> getTagsForDiagnosis(@PathVariable Integer id) {
            Optional<HemangiomaDiagnosis> diagnosis = hemangiomaDiagnosisRepository.findById(id);
            if (diagnosis.isPresent()) {
            HemangiomaDiagnosis hemangiomaDiagnosis = diagnosis.get();
            List<Tag> tags = hemangiomaDiagnosis.getTags();
            return ResponseEntity.ok(tags);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "未找到指定ID的诊断记录"));
        }
    }
    
    /**
     * 临时修复方法 - 完整修复诊断记录，包括图像路径和标签
     */
    @GetMapping("/fix-diagnosis/{id}")
    public ResponseEntity<?> fixDiagnosis(@PathVariable Integer id) {
        Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(id);
        if (!diagnosisOpt.isPresent()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "未找到指定ID的诊断记录"));
        }
        
        HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
        Map<String, Object> result = new HashMap<>();
        
        // 1. 修复imagePath
        String tempDir = basePathConfig.getTempDir();
        String fileName = "hemangioma_example_" + id + ".jpg";
        String imagePath = "/medical/images/temp/" + fileName;
        
        // 更新诊断记录的imagePath
        diagnosis.setImagePath(imagePath);
        result.put("imagePath", imagePath);
        
        // 2. 创建示例图像文件
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists()) {
            tempDirFile.mkdirs();
        }
        
        File imageFile = new File(tempDir, fileName);
        try {
            if (!imageFile.exists()) {
                // 创建一个空白图像
                BufferedImage img = new BufferedImage(800, 600, BufferedImage.TYPE_INT_RGB);
                java.awt.Graphics2D g2d = img.createGraphics();
                
                // 填充白色背景
                g2d.setColor(java.awt.Color.WHITE);
                g2d.fillRect(0, 0, 800, 600);
                
                // 添加一些文本和形状
                g2d.setColor(java.awt.Color.RED);
                g2d.fillOval(300, 200, 200, 200); // 画一个红色圆形模拟血管瘤
                
                g2d.setColor(java.awt.Color.BLACK);
                g2d.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 20));
                g2d.drawString("示例血管瘤图像 ID: " + id, 50, 50);
                
                g2d.dispose();
                
                // 保存图像
                javax.imageio.ImageIO.write(img, "jpg", imageFile);
                result.put("imageCreated", true);
                result.put("imagePath", imageFile.getAbsolutePath());
            } else {
                result.put("imageCreated", false);
                result.put("imageExists", true);
            }
        } catch (Exception e) {
            result.put("imageError", e.getMessage());
            e.printStackTrace();
        }
        
        // 3. 创建示例标签
        List<Tag> existingTags = tagRepository.findByHemangiomaDiagnosisId(id);
        if (existingTags.isEmpty()) {
            // 创建示例标签
            List<Tag> newTags = new ArrayList<>();
            
            // 创建第一个标签 - 居中位置
            Tag tag1 = new Tag();
            tag1.setHemangiomaDiagnosis(diagnosis);
            tag1.setTagName("RICH-先天性快速消退型血管瘤");
            tag1.setX(0.4); // 相对X坐标，范围0-1
            tag1.setY(0.4); // 相对Y坐标，范围0-1
            tag1.setWidth(0.2); // 相对宽度，范围0-1
            tag1.setHeight(0.2); // 相对高度，范围0-1
            tag1.setConfidence(0.95); // 置信度
            newTags.add(tag1);
            
            // 创建第二个标签 - 右下角
            Tag tag2 = new Tag();
            tag2.setHemangiomaDiagnosis(diagnosis);
            tag2.setTagName("NICH-先天性不消退型血管瘤");
            tag2.setX(0.7);
            tag2.setY(0.7);
            tag2.setWidth(0.15);
            tag2.setHeight(0.15);
            tag2.setConfidence(0.85);
            newTags.add(tag2);
            
            // 保存标签
            List<Tag> savedTags = tagRepository.saveAll(newTags);
            result.put("tagsCreated", true);
            result.put("tagCount", savedTags.size());
        } else {
            result.put("tagsCreated", false);
            result.put("existingTagCount", existingTags.size());
        }
        
        // 4. 保存更新后的诊断记录
        HemangiomaDiagnosis savedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
        result.put("diagnosisUpdated", true);
        result.put("diagnosis", savedDiagnosis);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 根据用户ID获取最近的标注记录
     */
    @GetMapping("/recent-by-user/{userId}")
    public ResponseEntity<?> getRecentByUser(@PathVariable String userId) {
        System.out.println("获取用户最近诊断记录 - 用户ID: " + userId);
        
        try {
            // 根据 custom_id 找到用户
            Optional<User> userOpt = userService.getUserByCustomId(userId);
            
            if (!userOpt.isPresent()) {
                System.err.println("找不到用户，ID: " + userId);
                return ResponseEntity.ok(new ArrayList<>());
            }
            
            // 查询用户最近标注的记录，最多10条



