package com.medical.annotation.util;

import com.medical.annotation.model.ImagePair;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.service.FileService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * 图像路径工具类
 */
@Component
public class ImagePathUtil {
    
    /**
     * 生成标注图像路径
     * @param originalPath 原始图像路径
     * @return 标注图像路径
     */
    public static String generateAnnotatedImagePath(String originalPath) {
        if (originalPath == null) {
            return null;
        }
        
        // 获取FileService实例以访问目录路径
        FileService fileService = SpringUtils.getBean(FileService.class);
        String processedDir = fileService.getProcessedDirectoryPath();
        
        // 从原始路径中提取文件名
        String filename = new File(originalPath).getName();
        
        // 构建新的处理后文件路径
        return new File(processedDir, filename).getPath();
    }
    
    /**
     * 转换路径为Web可用路径
     * @param path 物理路径
     * @return Web路径
     */
    public static String convertToWebPath(String path) {
        if (path == null) {
            return null;
        }
        
        FileService fileService = SpringUtils.getBean(FileService.class);
        String tempDir = fileService.getTempDirectoryPath();
        String processedDir = fileService.getProcessedDirectoryPath();
        String originalDir = fileService.getOriginalDirectoryPath();
        
        // 规范化路径分隔符
        path = path.replace("\\", "/");
        tempDir = tempDir.replace("\\", "/");
        processedDir = processedDir.replace("\\", "/");
        originalDir = originalDir.replace("\\", "/");
        
        if (path.startsWith(tempDir)) {
            return WEB_TEMP_PREFIX + path.substring(tempDir.length());
        }
        if (path.startsWith(processedDir)) {
            return WEB_PROCESSED_PREFIX + path.substring(processedDir.length());
        }
        if (path.startsWith(originalDir)) {
             return WEB_ORIGINAL_PREFIX + path.substring(originalDir.length());
        }
        
        // 如果已经是Web路径，直接返回
        if (path.startsWith("/medical/images/")) {
            return path;
        }
        
        return path;
    }

    // Web路径前缀
    private static final String WEB_TEMP_PREFIX = "/medical/images/temp/";
    private static final String WEB_PROCESSED_PREFIX = "/medical/images/processed/";
    private static final String WEB_ORIGINAL_PREFIX = "/medical/images/original/";
    
    private static final String[] PHYSICAL_PATH_PREFIXES = {
        "\\\\", // UNC路径
        "C:", "D:", "E:", "F:", "G:", "H:", // Windows盘符
        "/", // Linux/macOS根目录
    };

    private static final List<String> COMMON_ENCODING_ISSUES = Arrays.asList(
        "%25", // double encoded %
        "%2B"  // encoded +
    );
    
    /**
     * 修复并规范化路径字符串
     * @param path 原始路径
     * @return 修复后的路径
     */
    public static String getFixedPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return path;
        }
        
        String fixedPath = path;
        
        // 1. URL解码，处理%20等编码
        try {
            fixedPath = URLDecoder.decode(fixedPath, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException | IllegalArgumentException e) {
            // 解码失败，可能路径本身就包含%，忽略错误
        }
        
        // 2. 移除 "file:/" 前缀
        if (fixedPath.startsWith("file:/")) {
            fixedPath = fixedPath.substring(fixedPath.startsWith("file:///") ? 8 : 6);
        }

        // 3. 统一路径分隔符为 /
        fixedPath = fixedPath.replace("\\", "/");
        
        return fixedPath;
    }
    
    /**
     * 将Web路径转换为物理路径
     * @param webPath Web路径
     * @return 物理路径
     */
    public static String webPathToPhysical(String webPath) {
        if (webPath == null) {
            return null;
        }

        FileService fileService = SpringUtils.getBean(FileService.class);
        String tempDir = fileService.getTempDirectoryPath().replace("\\", "/");
        String processedDir = fileService.getProcessedDirectoryPath().replace("\\", "/");
        String originalDir = fileService.getOriginalDirectoryPath().replace("\\", "/");

        String fixedWebPath = webPath.replace("\\", "/");

        if (fixedWebPath.startsWith(WEB_TEMP_PREFIX)) {
            return tempDir + fixedWebPath.substring(WEB_TEMP_PREFIX.length());
        }
        if (fixedWebPath.startsWith(WEB_PROCESSED_PREFIX)) {
            return processedDir + fixedWebPath.substring(WEB_PROCESSED_PREFIX.length());
        }
        if (fixedWebPath.startsWith(WEB_ORIGINAL_PREFIX)) {
            return originalDir + fixedWebPath.substring(WEB_ORIGINAL_PREFIX.length());
        }

        // 如果已经是物理路径，直接返回
        for (String prefix : PHYSICAL_PATH_PREFIXES) {
            if (fixedWebPath.toUpperCase().startsWith(prefix.toUpperCase())) {
                return webPath; // 返回原始路径以保留大小写
            }
        }
        
        // 默认情况下，返回null或抛出异常
        return null; 
    }
    
    /**
     * 将物理路径转换为Web路径
     * @param physicalPath 物理路径
     * @return Web路径
     */
    public static String physicalPathToWeb(String physicalPath) {
        return convertToWebPath(physicalPath);
    }
    
    /**
     * 从完整路径中提取文件名
     * @param path 路径字符串
     * @return 文件名
     */
    public static String getFilenameFromPath(String path) {
        if (path == null) {
            return null;
        }
        return new File(path).getName();
    }
    
    /**
     * 检查文件是否存在，考虑大小写不敏感的文件系统
     * @param path 文件路径
     * @return 如果文件存在则返回其标准路径，否则返回null
     */
    public static String findExistingFile(String path) {
        if (path == null) return null;
        
        File f = new File(path);
        if (f.exists()) {
            try {
                return f.getCanonicalPath();
            } catch (IOException e) {
                return f.getAbsolutePath();
            }
        }
        
        // 尝试在父目录中查找（处理大小写问题）
        File parent = f.getParentFile();
        if (parent != null && parent.exists() && parent.isDirectory()) {
            File[] files = parent.listFiles((dir, name) -> name.equalsIgnoreCase(f.getName()));
            if (files != null && files.length > 0) {
                try {
                    return files[0].getCanonicalPath();
                } catch (IOException e) {
                    return files[0].getAbsolutePath();
            }
        }
        }
        
        return null;
    }

    /**
     * 修复重复的路径片段
     * @param path 路径
     * @return 修复后的路径
     */
    public static String fixDuplicatePath(String path) {
        if (path == null) return null;
        
        String tempDir = SpringUtils.getBean(FileService.class).getTempDirectoryPath().replace("\\", "/");
        if (path.contains(tempDir)) {
            path = path.substring(path.lastIndexOf(tempDir));
        }
        
        return path;
    }

    /**
     * 获取正确的Web路径，避免重复拼接
     * @param path
     * @return
     */
    public static String getCorrectWebPath(String path) {
        if (path == null) {
            return null;
        }
        if (path.startsWith("/medical/images/")) {
            return path;
        }
        return convertToWebPath(path);
    }
} 