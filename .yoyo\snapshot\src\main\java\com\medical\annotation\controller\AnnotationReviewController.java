package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.User;
import com.medical.annotation.service.AnnotationReviewService;
import com.medical.annotation.service.ImageMetadataService;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/reviews")
public class AnnotationReviewController {

    @Autowired
    private AnnotationReviewService annotationReviewService;
    
    @Autowired
    private ImageMetadataService imageMetadataService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取待审核标注列表 - 无认证限制版本
     * 通过URL路径获取用户ID，避免认证问题
     */
    @GetMapping("/pending-unrestricted/{userId}")
    public ResponseEntity<?> getPendingReviewsUnrestricted(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "submittedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction) {
        
        try {
            // 根据用户ID获取用户信息
            Optional<User> userOpt = userService.getUserByCustomId(userId);
            
            if (!userOpt.isPresent()) {
                // 构建空响应
                Map<String, Object> emptyResponse = new HashMap<>();
                emptyResponse.put("content", new ArrayList<>());
                emptyResponse.put("totalElements", 0);
                emptyResponse.put("totalPages", 0);
                emptyResponse.put("currentPage", page);
                emptyResponse.put("size", size);
                
                return ResponseEntity.ok(emptyResponse);
            }
            
            User currentUser = userOpt.get();
            
            // 创建分页和排序参数
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
            PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sortBy));
            
            // 获取待审核标注列表
            Page<ImageMetadata> pendingReviews = annotationReviewService.getPendingReviews(currentUser, pageRequest);
            
            // 构建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("content", pendingReviews.getContent());
            response.put("totalElements", pendingReviews.getTotalElements());
            response.put("totalPages", pendingReviews.getTotalPages());
            response.put("currentPage", pendingReviews.getNumber());
            response.put("size", pendingReviews.getSize());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取待审核列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取待审核标注列表
     * 根据用户角色和团队关系过滤
     */
    @GetMapping("/pending")
    public ResponseEntity<?> getPendingReviews(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "submittedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) String userId) {
        
        try {
            // 首先尝试从URL参数获取用户ID
            User currentUser = null;
            
            if (userId != null && !userId.trim().isEmpty()) {
                // 使用用户ID参数查找用户
                Optional<User> userOpt = userService.getUserByCustomId(userId);
                if (userOpt.isPresent()) {
                    currentUser = userOpt.get();
                    System.out.println("通过URL参数userId找到用户: " + currentUser.getName());
                }
            }
            
            // 如果没有找到用户，尝试从认证信息中获取
            if (currentUser == null) {
                // 获取当前登录用户
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                
                // 如果是匿名用户或未登录，返回空列表
                if (authentication == null || authentication.getPrincipal() == null || 
                    "anonymousUser".equals(authentication.getPrincipal())) {
                    System.out.println("未找到有效的认证信息，返回空列表");
                    // 构建空响应
                    Map<String, Object> emptyResponse = new HashMap<>();
                    emptyResponse.put("content", new ArrayList<>());
                    emptyResponse.put("totalElements", 0);
                    emptyResponse.put("totalPages", 0);
                    emptyResponse.put("currentPage", page);
                    emptyResponse.put("size", size);
                    
                    return ResponseEntity.ok(emptyResponse);
                }
                
                Optional<User> userOpt = userService.getUserByEmail(authentication.getName());
                
                if (!userOpt.isPresent()) {
                    System.out.println("未找到认证用户，返回空列表");
                    // 构建空响应
                    Map<String, Object> emptyResponse = new HashMap<>();
                    emptyResponse.put("content", new ArrayList<>());
                    emptyResponse.put("totalElements", 0);
                    emptyResponse.put("totalPages", 0);
                    emptyResponse.put("currentPage", page);
                    emptyResponse.put("size", size);
                    
                    return ResponseEntity.ok(emptyResponse);
                }
                
                currentUser = userOpt.get();
                System.out.println("通过认证信息找到用户: " + currentUser.getName());
            }
            
            // 创建分页和排序参数
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
            PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sortBy));
            
            // 获取待审核标注列表
            System.out.println("查询待审核标注，用户: " + currentUser.getName() + ", 角色: " + currentUser.getRole() + ", 团队: " + (currentUser.getTeam() == null ? "无" : currentUser.getTeam().getName()));
            Page<ImageMetadata> pendingReviews = annotationReviewService.getPendingReviews(currentUser, pageRequest);
            System.out.println("查询结果: 共找到 " + pendingReviews.getTotalElements() + " 条待审核标注");
            
            // 构建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("content", pendingReviews.getContent());
            response.put("totalElements", pendingReviews.getTotalElements());
            response.put("totalPages", pendingReviews.getTotalPages());
            response.put("currentPage", pendingReviews.getNumber());
            response.put("size", pendingReviews.getSize());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("获取待审核列表失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body("获取待审核列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取标注详情（包含权限检查）
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getAnnotationForReview(@PathVariable Long id) {
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            // 如果是匿名用户或未登录，返回权限错误
            if (authentication == null || authentication.getPrincipal() == null || 
                "anonymousUser".equals(authentication.getPrincipal())) {
                return ResponseEntity.status(403).body("未登录或无权限查看");
            }
            
            Optional<User> userOpt = userService.getUserByEmail(authentication.getName());
            
            if (!userOpt.isPresent()) {
                return ResponseEntity.status(403).body("用户不存在或无权限");
            }
            
            User currentUser = userOpt.get();
            
            // 获取标注详情
            try {
                ImageMetadata metadata = imageMetadataService.getImageMetadata(id);
                
                // 检查审核权限
                if (!annotationReviewService.canReview(currentUser, metadata)) {
                    return ResponseEntity.status(403).body("无权查看此标注");
                }
                
                return ResponseEntity.ok(metadata);
            } catch (Exception e) {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取标注详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 提交审核结果
     */
    @PostMapping("/{id}/review")
    public ResponseEntity<?> submitReview(
            @PathVariable Long id,
            @RequestParam boolean approved,
            @RequestParam(required = false) String reviewNotes) {
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            // 如果是匿名用户或未登录，返回权限错误
            if (authentication == null || authentication.getPrincipal() == null || 
                "anonymousUser".equals(authentication.getPrincipal())) {
                return ResponseEntity.status(403).body("未登录或无权限进行审核");
            }
            
            Optional<User> userOpt = userService.getUserByEmail(authentication.getName());
            
            if (!userOpt.isPresent()) {
                return ResponseEntity.status(403).body("用户不存在或无权限");
            }
            
            User currentUser = userOpt.get();
            
            // 进行审核
            ImageMetadata updatedMetadata = annotationReviewService.reviewAnnotation(id, approved, reviewNotes, currentUser);
            
            return ResponseEntity.ok(updatedMetadata);
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.status(403).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("审核失败: " + e.getMessage());
        }
    }
} 