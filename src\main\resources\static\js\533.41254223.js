"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[533],{533:(e,r,n)=>{n.r(r),n.d(r,{default:()=>T});var a=n(641),t=n(33),s=n(3751),l={class:"container login-container"},o={class:"card"},i={class:"card-body p-4"},c={key:0,class:"alert alert-danger"},d={key:1,class:"alert alert-success"},u={class:"mb-3"},b=["disabled"],m={class:"mb-3"},p=["disabled"],v={class:"mb-3 form-check"},f=["disabled"],k={class:"d-grid gap-2"},g=["disabled"],h={key:0,class:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"},L={class:"mt-3 text-center"},_={class:"card-footer text-center"};function w(e,r,n,w,y,x){var C=(0,a.g2)("router-link");return(0,a.uX)(),(0,a.CE)("div",l,[(0,a.Lk)("div",o,[r[10]||(r[10]=(0,a.Lk)("div",{class:"card-header"},[(0,a.Lk)("h3",{class:"mb-0"},"血管瘤辅助系统 - 登录")],-1)),(0,a.Lk)("div",i,[w.error?((0,a.uX)(),(0,a.CE)("div",c,(0,t.v_)(w.error),1)):(0,a.Q3)("",!0),w.success?((0,a.uX)(),(0,a.CE)("div",d,(0,t.v_)(w.success),1)):(0,a.Q3)("",!0),(0,a.Lk)("form",{onSubmit:r[3]||(r[3]=(0,s.D$)((function(){return w.handleLogin&&w.handleLogin.apply(w,arguments)}),["prevent"]))},[(0,a.Lk)("div",u,[r[4]||(r[4]=(0,a.Lk)("label",{for:"email",class:"form-label"},"邮箱",-1)),(0,a.bo)((0,a.Lk)("input",{type:"email",class:"form-control",id:"email","onUpdate:modelValue":r[0]||(r[0]=function(e){return w.email=e}),required:"",disabled:w.loading},null,8,b),[[s.Jo,w.email]])]),(0,a.Lk)("div",m,[r[5]||(r[5]=(0,a.Lk)("label",{for:"password",class:"form-label"},"密码",-1)),(0,a.bo)((0,a.Lk)("input",{type:"password",class:"form-control",id:"password","onUpdate:modelValue":r[1]||(r[1]=function(e){return w.password=e}),required:"",disabled:w.loading},null,8,p),[[s.Jo,w.password]])]),(0,a.Lk)("div",v,[(0,a.bo)((0,a.Lk)("input",{type:"checkbox",class:"form-check-input",id:"remember","onUpdate:modelValue":r[2]||(r[2]=function(e){return w.remember=e}),disabled:w.loading},null,8,f),[[s.lH,w.remember]]),r[6]||(r[6]=(0,a.Lk)("label",{class:"form-check-label",for:"remember"},"记住我",-1))]),(0,a.Lk)("div",k,[(0,a.Lk)("button",{type:"submit",class:"btn btn-primary",disabled:w.loading},[w.loading?((0,a.uX)(),(0,a.CE)("span",h)):(0,a.Q3)("",!0),r[7]||(r[7]=(0,a.eW)(" 登录 "))],8,g)])],32),(0,a.Lk)("div",L,[(0,a.bF)(C,{to:"/register",class:"text-decoration-none"},{default:(0,a.k6)((function(){return r[8]||(r[8]=[(0,a.eW)("没有账号？点击注册")])})),_:1,__:[8]})])]),(0,a.Lk)("div",_,[(0,a.bF)(C,{to:"/",class:"btn btn-link"},{default:(0,a.k6)((function(){return r[9]||(r[9]=[(0,a.eW)("返回首页")])})),_:1,__:[9]})])])])}var y=n(4048),x=n(388),C=(n(4114),n(6031),n(953)),K=n(6278),R=n(5220);const E={name:"Login",setup:function(){var e=(0,K.Pj)(),r=(0,R.rd)(),n=(0,C.KR)(""),a=(0,C.KR)(""),t=(0,C.KR)(!1),s=(0,C.KR)(!1),l=(0,C.KR)(""),o=(0,C.KR)(""),i=function(){var t=(0,x.A)((0,y.A)().mark((function t(){return(0,y.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return s.value=!0,l.value="",o.value="",t.prev=3,t.next=6,e.dispatch("login",{email:n.value,password:a.value});case 6:o.value="登录成功，即将跳转...",setTimeout((function(){r.push("/")}),1500),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](3),console.error("Login error:",t.t0),l.value="string"===typeof t.t0?t.t0:"登录失败，请检查邮箱和密码";case 14:return t.prev=14,s.value=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,null,[[3,10,14,17]])})));return function(){return t.apply(this,arguments)}}();return{email:n,password:a,remember:t,loading:s,error:l,success:o,handleLogin:i}}};var I=n(6262);const A=(0,I.A)(E,[["render",w],["__scopeId","data-v-660a9568"]]),T=A},4599:(e,r,n)=>{var a=n(6518),t=n(4576),s=n(9472),l=s(t.setTimeout,!0);a({global:!0,bind:!0,forced:t.setTimeout!==l},{setTimeout:l})},5575:(e,r,n)=>{var a=n(6518),t=n(4576),s=n(9472),l=s(t.setInterval,!0);a({global:!0,bind:!0,forced:t.setInterval!==l},{setInterval:l})},6031:(e,r,n)=>{n(5575),n(4599)},9472:(e,r,n)=>{var a=n(4576),t=n(8745),s=n(4901),l=n(4215),o=n(2839),i=n(7680),c=n(2812),d=a.Function,u=/MSIE .\./.test(o)||"BUN"===l&&function(){var e=a.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}();e.exports=function(e,r){var n=r?2:1;return u?function(a,l){var o=c(arguments.length,1)>n,u=s(a)?a:d(a),b=o?i(arguments,n):[],m=o?function(){t(u,this,b)}:u;return r?e(m,l):e(m)}:e}}}]);