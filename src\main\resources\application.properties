# 应用服务端口
server.port=8085

# 数据库连接配置
spring.datasource.url=**************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# 上下文路径 - 确保与前端一致
server.servlet.context-path=/medical

# 上传文件配置
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
spring.servlet.multipart.enabled=true

# ===========================================
# 路径配置 - 全局设置区域
# ===========================================

# 项目基础目录 - 所有相对路径的基准点
# 默认为当前工作目录，可以指定绝对路径
# app.base.dir=/path/to/project

# 图片存储路径配置
# 1. 如果是绝对路径(如F:/xue或/home/<USER>/xue) - 则直接使用该路径
# 2. 如果是相对路径(如medical_images) - 则相对于项目根目录
app.upload.dir=medical_images

# 日志配置
logging.level.com.medical=INFO
logging.level.org.springframework.web=WARN
# 完全禁用SQL查询日志
logging.level.org.hibernate.SQL=OFF
# 禁用SQL参数绑定日志
logging.level.org.hibernate.type.descriptor.sql=OFF
# 禁用JPA相关日志
logging.level.org.hibernate=WARN
logging.level.org.springframework.orm.jpa=WARN
logging.level.org.hibernate.type=OFF

# 会话配置
server.servlet.session.timeout=3600
spring.main.allow-circular-references=true

# 启用Flyway自动迁移
spring.flyway.enabled=true
# 设置Flyway的迁移脚本位置，它会自动扫描这些路径
spring.flyway.locations=classpath:db/migration

# 禁用旧的初始化，避免与Flyway冲突
spring.datasource.initialization-mode=never
spring.sql.init.mode=never

# 字符编码设置
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
server.tomcat.uri-encoding=UTF-8

# 确保HTTP消息转换器使用UTF-8编码
spring.http.converters.preferred-json-mapper=jackson
spring.jackson.serialization.indent_output=true
spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=false
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8 

# 添加详细的路径处理日志配置 - 降低级别至WARN
logging.level.org.springframework.web.servlet.DispatcherServlet=WARN
logging.level.org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping=WARN
logging.level.com.medical.annotation.config.DuplicatePathConfig=WARN

# 添加自定义属性，用于标识是否处理双重路径
app.duplicate-path-handling.enabled=true

# 添加备用物理路径设置，用于文件系统访问
app.upload.alternate-dir=F:/医学辅助系统/medical_images

# 路径处理配置
app.path.fix-duplicate=true
app.path.normalize-encoding=true

# 请求路径日志 - 降低级别减少日志量
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=WARN
spring.mvc.log-request-details=false

# 添加文件上传错误处理配置
spring.servlet.multipart.resolve-lazily=true
server.tomcat.max-swallow-size=-1
server.max-http-header-size=20KB

# 静态资源处理
spring.resources.static-locations=classpath:/static/,file:${app.upload.dir}/,file:${app.upload.dir}/temp/,file:${app.upload.dir}/processed/,file:${app.upload.dir}/annotated/
spring.mvc.static-path-pattern=/images/**,/medical/images/**

# 路径匹配配置 - 处理双重路径问题
spring.mvc.pathmatch.use-suffix-pattern=true
spring.mvc.pathmatch.use-registered-suffix-pattern=true
spring.mvc.pathmatch.matching-strategy=ant-path-matcher

# 禁用上下文路径重写
server.use-forward-headers=true
server.forward-headers-strategy=native

# 启用直接处理双重路径
app.direct-path-handling.enabled=true 

# 邮件配置
spring.mail.host=smtp.qq.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=djkokmdjjlzffchd
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.ssl.enable=false
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=3000
spring.mail.properties.mail.smtp.writetimeout=5000
spring.mail.default-encoding=UTF-8
# 禁用SSL证书验证
spring.mail.properties.mail.smtp.ssl.trust=*
spring.mail.properties.mail.smtp.socketFactory.fallback=true
# 启用JavaMail调试
spring.mail.properties.mail.debug=true

# 应用名称
spring.application.name=血管瘤人工智能辅助治疗系统 