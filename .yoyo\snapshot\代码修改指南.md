# 代码修改指南

## 数据库结构变更

我们对数据库结构进行了以下变更：

1. 从 `image_metadata` 表中移除了以下冗余字段：
   - `path` 和 `image_two_path`（这些字段已经存在于 `image_pairs` 表中）
   - `modality`（未被使用的字段）
   - `uploaded_by_custom_id` 和 `reviewed_by_custom_id`（与外键冗余）

2. 改进了外键约束，为上传者(RESTRICT)和审核者(SET NULL)设置了不同的删除策略。

## 代码修改思路

由于删除了字段，我们需要修改代码中对这些字段的引用。主要有两种修改方式：

1. 对于 `path` 和 `image_two_path` 字段：
   - 使用 `ImagePair` 表中的 `image_one_path` 和 `image_two_path` 替代
   - 新增了 `ImagePathUtil` 工具类来简化这一转换

2. 对于 `uploaded_by_custom_id` 和 `reviewed_by_custom_id` 字段：
   - 直接使用关联的 `User` 对象的 `customId` 属性

## 主要修改文件

### 已完成的修改

1. `ImageMetadata.java`
   - 移除了冗余字段及其getter/setter方法

2. `ImageMetadataService.java`
   - 更新了 `uploadImage` 方法，不再设置path字段，而是创建 `ImagePair` 记录

3. `ImagePathUtil.java`
   - 新增工具类，提供了获取和设置图像路径的方法

### 需要进行的修改

1. `ApiController.java` 
   - 多处使用了已删除的字段，需要使用 `ImagePathUtil` 替代
   - 主要影响函数：`buildImageResponse`, `generateAnnotatedImagePath` 等

2. `FileUploadController.java`
   - 需要修改对 `path`, `uploaded_by_custom_id` 等字段的引用
   - 上传逻辑需要适配，直接操作 `ImagePair` 表

3. `TagApiController.java`
   - 需要修改对 `path` 字段的引用

4. `ImageController.java`
   - 需要修改对 `uploaded_by_custom_id` 字段的引用

5. 前端代码
   - `CaseView.vue` 和 `CaseDetailForm.vue` 需要适配

## 替换示例

### 获取图像路径

旧代码：
```java
String imagePath = imageMetadata.getPath();
```

新代码：
```java
String imagePath = ImagePathUtil.getOriginalPath(imageMetadata, imagePairRepository);
```

### 设置图像路径

旧代码：
```java
imageMetadata.setPath(path);
```

新代码：
```java
ImagePathUtil.setOriginalPath(imageMetadata, path, imagePairRepository);
```

### 获取上传者自定义ID

旧代码：
```java
String uploaderId = imageMetadata.getUploadedByCustomId();
```

新代码：
```java
String uploaderId = imageMetadata.getUploadedBy() != null ? imageMetadata.getUploadedBy().getCustomId() : null;
```

## 执行步骤

1. 首先运行SQL脚本更新数据库结构
2. 确保所有路径信息已正确迁移到 `image_pairs` 表
3. 逐个修改后端代码中对已删除字段的引用
4. 修改前端代码
5. 测试整个流程，确保功能正常

## 注意事项

- 修改过程中需要特别注意数据完整性，确保图像路径信息不会丢失
- 对于复杂的业务逻辑，可能需要重构相关代码
- 测试时应关注图像上传、标注和查看的完整流程 