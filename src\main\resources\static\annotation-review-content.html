<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>标注审核内容</title>
  <link rel="stylesheet" href="/css/element-plus.css">
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background-color: #f5f7fa;
    }
    #app {
      height: 100%;
      padding: 20px;
      box-sizing: border-box;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 4px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="container" id="review-container">
      <!-- 这里将通过脚本插入审核组件 -->
    </div>
  </div>

  <script src="/js/chunk-vendors.js"></script>
  <script src="/js/app.js"></script>
  <script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 等待一下确保Vue和组件都已加载
      setTimeout(() => {
        try {
          // 获取容器元素
          const container = document.getElementById('review-container');
          
          // 检查Vue和组件是否可用
          if (window.Vue && window.VueComponents && window.VueComponents.AnnotationReviewList) {
            // 创建一个简单的Vue应用实例
            const app = window.Vue.createApp({
              template: `
                <div>
                  <annotation-review-list standalone-mode="true"></annotation-review-list>
                </div>
              `,
              components: {
                'annotation-review-list': window.VueComponents.AnnotationReviewList
              }
            });
            
            // 挂载应用
            app.mount('#review-container');
            console.log('审核组件已成功挂载');
          } else {
            console.error('Vue或组件未加载:', { 
              Vue: !!window.Vue, 
              Components: !!window.VueComponents,
              AnnotationReviewList: window.VueComponents ? !!window.VueComponents.AnnotationReviewList : false
            });
            container.innerHTML = '<div style="color: red; text-align: center;">组件加载失败，请刷新页面重试</div>';
          }
        } catch (error) {
          console.error('初始化组件失败:', error);
          document.getElementById('review-container').innerHTML = 
            '<div style="color: red; text-align: center;">组件初始化失败: ' + error.message + '</div>';
        }
      }, 300);
    });
  </script>
</body>
</html> 