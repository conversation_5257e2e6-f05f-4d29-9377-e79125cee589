# 血管瘤辅助系统 - 唯一ID和文件名生成机制

## 1. 问题背景

系统在处理图像时需要生成唯一值用于：
- 数据库ID：必须在INT范围内(最大约21亿)
- 文件名：必须保持唯一，避免覆盖
- 原始文件名：保留原始信息的同时确保唯一

## 2. 解决方案

### 2.1 数据库ID生成 (DatabaseUtil类)

为了确保生成的ID在INT范围内，我们提取时间戳的后9位数字：

```java
public static int generateUniqueIntId() {
    // 获取当前时间戳
    long timestamp = System.currentTimeMillis();
    
    // 截取后9位数字，确保在Integer范围内
    String timestampStr = String.valueOf(timestamp);
    String lastNineDigits = timestampStr.substring(Math.max(0, timestampStr.length() - 9));
    
    // 转换为整数
    int uniqueId = Integer.parseInt(lastNineDigits);
    
    return uniqueId;
}
```

这样生成的ID格式如：`938646301`

### 2.2 文件名生成 (FileNameGenerator类)

文件名使用UUID和时间戳结合的方式确保唯一性：

```java
public static String generateUniqueFileName(String originalFileName) {
    // 生成UUID（去除连字符以缩短长度）
    String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 12);
    
    // 获取当前时间戳
    String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
    
    // 获取文件扩展名
    String extension = getFileExtension(originalFileName);
    
    // 组合成唯一文件名: uuid_timestamp.extension
    return uuid + "_" + timestamp + extension;
}
```

生成的文件名格式：`a1b2c3d4e5f6_20240722_103045.jpg`

### 2.3 原始文件名生成 (FileNameGenerator类)

保留原始文件名的同时，添加时间戳确保唯一：

```java
public static String generateUniqueOriginalName(String originalFileName) {
    // 获取文件名（不含扩展名）
    String nameWithoutExt = removeFileExtension(originalFileName);
    
    // 获取文件扩展名
    String extension = getFileExtension(originalFileName);
    
    // 获取当前时间戳
    String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
    
    // 组合成唯一原始文件名: name_timestamp.extension
    return nameWithoutExt + "_" + timestamp + extension;
}
```

生成的原始文件名格式：`patient001_20240722_103045.jpg`

## 3. 使用场景

### 3.1 上传新图像

当用户上传新图像时，系统会：
1. 使用`DatabaseUtil.generateUniqueIntId()`生成数据库ID
2. 使用`FileNameGenerator.generateUniqueFileName()`生成唯一文件名
3. 使用`FileNameGenerator.generateUniqueOriginalName()`生成唯一原始文件名

### 3.2 标注后保存图像

标注完成后保存图像时：
1. 使用`FileNameGenerator.generateUniqueFileName()`生成带"annotated_"前缀的唯一文件名
2. 保存到processed目录

## 4. 注意事项

- ID生成时取时间戳后9位，确保在INT范围内
- 所有生成的唯一值都包含时间戳，便于排序和追踪
- UUID确保即使在相同毫秒内创建的文件名也不会冲突
- 原始文件名保留了初始名称，同时添加时间戳确保唯一性 