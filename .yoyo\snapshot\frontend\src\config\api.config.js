// api.config.js - 集中管理所有API地址配置

/**
 * API配置从环境变量中读取 - 可通过vue.config.js或.env文件配置
 * 
 * 更改服务器地址时，只需修改以下环境变量:
 * 1. 创建或编辑.env.local文件，添加以下内容:
 *    VUE_APP_API_URL=http://新服务器地址:端口
 * 
 * 2. 或者直接修改vue.config.js中的默认值
 */

// 基础URL配置
export const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://192.168.2.43:8085';

// 上下文路径
export const API_CONTEXT_PATH = process.env.VUE_APP_CONTEXT_PATH || '/medical';

// API路径前缀
export const API_PREFIX = process.env.VUE_APP_API_PREFIX || '/api';

// 完整的API基础URL
export const API_URL = `${API_BASE_URL}${API_CONTEXT_PATH}${API_PREFIX}`;

// 静态资源URL (不包含API前缀)
export const STATIC_RESOURCE_URL = `${API_BASE_URL}${API_CONTEXT_PATH}`;

// 其他特定API路径
export const DASHBOARD_STATS_URL = `${API_URL}/stats-v2/dashboard-unrestricted`;

export default {
  API_BASE_URL,
  API_CONTEXT_PATH,
  API_PREFIX,
  API_URL,
  STATIC_RESOURCE_URL,
  DASHBOARD_STATS_URL,
}; 