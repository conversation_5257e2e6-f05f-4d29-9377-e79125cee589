<template>
  <el-container class="layout-container">
    <el-aside width="200px" class="sidebar">
      <div class="logo">血管瘤诊断平台</div>
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        background-color="#001529"
        text-color="#fff"
        active-text-color="#409EFF"
      >
        <!-- 工作台 - 所有角色可见 -->
        <el-menu-item index="/app/dashboard" @click="$router.push('/app/dashboard')">
          <el-icon><HomeFilled /></el-icon>
          <span>工作台</span>
        </el-menu-item>
        
        <!-- 病例标注 - 所有角色可见 -->
        <el-menu-item 
          index="/app/cases" 
          @click="$router.push('/app/cases')"
        >
          <el-icon><Document /></el-icon>
          <span>病例标注</span>
        </el-menu-item>
        
        <!-- 血管瘤诊断 - 所有角色可见 -->
        <el-menu-item 
          index="/app/hemangioma-diagnosis" 
          @click="navigateToHemangiomaDiagnosis"
        >
          <el-icon><Picture /></el-icon>
          <span>血管瘤诊断</span>
        </el-menu-item>
        
        <!-- 标注审核 - 管理员和审核医生可见 -->
        <el-menu-item 
          v-if="isAdmin || isReviewer"
          index="/app/annotation-reviews" 
          @click="$router.push('/app/annotation-reviews')"
        >
          <el-icon><Check /></el-icon>
          <span>标注审核</span>
        </el-menu-item>
        
        <!-- 团队 - 所有角色可见 -->
        <el-menu-item 
          index="/app/teams" 
          @click="$router.push('/app/teams')"
          class="team-menu-item"
        >
          <el-icon><UserFilled /></el-icon>
          <span>我的团队</span>
          <!-- 移除团队申请数量徽章 -->
        </el-menu-item>
        
        <!-- 部门成员 - 只有管理员可见 -->
        <el-menu-item 
          v-if="isAdmin"
          index="/app/users" 
          @click="$router.push('/app/users')"
        >
          <el-icon><User /></el-icon>
          <span>人员管理</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    
    <el-container>
      <el-header height="60px" class="header">
        <div class="header-left">
          <el-button 
            v-if="showDashboardButton" 
            type="primary" 
            icon="el-icon-s-home" 
            @click="$router.push('/app/dashboard')">
            返回工作台
          </el-button>
          <el-button 
            v-if="showBackButton" 
            type="info" 
            icon="el-icon-back" 
            @click="goBack">
            返回上一步
          </el-button>
        </div>
        <div class="header-right">
          <el-dropdown>
            <span class="user-info">
              <el-avatar size="small" :src="userAvatar" icon="el-icon-user"></el-avatar>
              {{ username }}
              <span :class="['role-tag', roleTagClass]">{{ userRoleText }}</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <router-link to="/app/profile" style="text-decoration: none; color: inherit;">
                    个人中心
                  </router-link>
                </el-dropdown-item>
                <el-dropdown-item v-if="isDoctor && !hasAppliedForReviewer" @click="handleApplyForReviewer">
                  申请升级权限
                </el-dropdown-item>
                <el-dropdown-item v-if="isDoctor && hasAppliedForReviewer" disabled>
                  权限申请审核中
                </el-dropdown-item>
                <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import { mapGetters } from 'vuex'
import { 
  HomeFilled, 
  Document, 
  Check, 
  User,
  UserFilled,
  Picture
} from '@element-plus/icons-vue'
import api from '@/utils/api'
import { API_BASE_URL } from '@/config/api.config'

export default {
  name: 'MainLayout',
  components: {
    HomeFilled,
    Document,
    Check,
    User,
    UserFilled,
    Picture
  },
  data() {
    return {
      username: '标注医生',
      showBackButton: false,
      showDashboardButton: false,
      hasAppliedForReviewer: false,
      pollTimer: null, // 用于存储团队申请定时器的ID
      userInfoTimer: null, // 用于存储用户信息检查定时器的ID
      userAvatar: '', // 用户头像URL
      userId: null, // 用户ID
      lastUserInfoHash: '' // 用于检测用户信息变化
    }
  },
  computed: {
    ...mapGetters({
      currentUser: 'getUser',
      isAdmin: 'isAdmin',
      isDoctor: 'isDoctor',
      isReviewer: 'isReviewer',
      // 添加团队申请相关的getter
      hasPendingApplications: 'teamApplications/hasPendingApplications',
      pendingApplicationsCount: 'teamApplications/getPendingApplicationsCount'
    }),
    // 当前激活的菜单项
    activeMenu() {
      return this.$route.path
    },
    // 用户角色文本
    userRoleText() {
      // 首先尝试使用Vuex中的角色标志
      if (this.isAdmin) return '管理员'
      if (this.isDoctor) return '标注医生'
      if (this.isReviewer) return '审核医生'
      
      // 如果Vuex中的角色判断失效，直接从localStorage获取
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (user && user.role) {
          // 根据用户角色返回对应文本
          switch(user.role) {
            case 'ADMIN': return '管理员';
            case 'DOCTOR': return '标注医生';
            case 'REVIEWER': return '审核医生';
            default: return '未知角色';
          }
        }
      } catch (err) {
        console.error('MainLayout: 获取用户角色失败:', err);
      }
      
      return '未知角色'
    },
    // 根据角色返回不同的CSS类
    roleTagClass() {
      if (this.isAdmin) return 'role-tag-admin'
      if (this.isReviewer) return 'role-tag-reviewer'
      if (this.isDoctor) return 'role-tag-doctor'
      
      // 如果Vuex中的角色判断失效，直接从localStorage获取
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (user && user.role) {
          switch(user.role) {
            case 'ADMIN': return 'role-tag-admin';
            case 'REVIEWER': return 'role-tag-reviewer';
            case 'DOCTOR': return 'role-tag-doctor';
            default: return '';
          }
        }
      } catch (err) {
        console.error('MainLayout: 获取用户角色样式失败:', err);
      }
      
      return ''
    }
  },
  watch: {
    $route(to) {
      // Only show back button on specific routes
      this.showBackButton = to.path === '/app/cases/structured-form';
      
      // Show dashboard button on all pages except dashboard
      this.showDashboardButton = to.path !== '/app/dashboard';
      
      // 如果是进入Dashboard，检查用户是否变更
      if (to.path === '/app/dashboard' || to.path === '/app' || to.path === '/app/') {
        // 获取当前用户ID
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const currentUserId = user.customId || user.id;
        
        // 如果存在lastUserId且与当前不同，说明用户已切换
        const lastUserId = localStorage.getItem('lastActiveUserId');
        if (lastUserId && lastUserId !== currentUserId) {
          console.log('MainLayout: 检测到用户ID变更，从', lastUserId, '到', currentUserId);
          
          // 强制刷新统计数据
          if (window.refreshDashboardStats) {
            console.log('MainLayout: 用户变更，强制刷新统计数据');
            setTimeout(() => window.refreshDashboardStats(), 0);
          }
        }
        
        // 更新最后活跃用户ID
        if (currentUserId) {
          localStorage.setItem('lastActiveUserId', currentUserId);
        }
      }
    },
    
    // 监听用户名变化
    username(newVal, oldVal) {
      if (newVal !== oldVal && newVal && oldVal) {
        console.log('MainLayout: 用户名变更，从', oldVal, '到', newVal);
        
        // 如果当前路径是Dashboard，强制刷新统计数据
        if (this.$route.path === '/app/dashboard' || this.$route.path === '/app' || this.$route.path === '/app/') {
          if (window.refreshDashboardStats) {
            console.log('MainLayout: 用户名变更，立即刷新统计数据');
            setTimeout(() => window.refreshDashboardStats(), 0);
          }
        }
        
        // 重新加载用户头像
        this.loadUserAvatar();
      }
    },
    
    // 监听用户ID变化
    userId(newVal, oldVal) {
      if (newVal !== oldVal && newVal) {
        console.log('MainLayout: 用户ID变更，重新加载头像');
        this.loadUserAvatar();
      }
    }
  },
  created() {
    // 获取当前用户信息
    this.loadUserInfo()
    
    // 如果有团队，获取团队申请数量
    this.fetchTeamApplicationsCount()
  },
  mounted() {
    // 定时获取团队申请数量（每5分钟获取一次）
    this.startPendingApplicationsPolling()
    
    // 定时检查用户信息更新（每10秒检查一次）
    this.startUserInfoPolling()
  },
  beforeUnmount() {
    // 清除定时器
    this.stopPendingApplicationsPolling()
    this.stopUserInfoPolling()
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        if (user && user.name) {
          this.username = user.name
        }
        
        // 保存用户ID用于获取头像
        if (user && user.id) {
          this.userId = user.id
          // 加载用户头像
          this.loadUserAvatar()
        }
        
        // 检查用户是否已申请成为审核医生
        if (this.isDoctor) {
          api.users.getReviewerApplicationStatus()
            .then(response => {
              if (response.data && response.data.status === 'PENDING') {
                this.hasAppliedForReviewer = true
              }
            })
            .catch(error => console.error('获取审核医生申请状态失败:', error))
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },
    
    // 加载用户头像
    async loadUserAvatar() {
      if (!this.userId) return;
      
      try {
        const response = await api.users.getUserAvatar(this.userId);
        if (response.data.avatarUrl) {
          // 添加API前缀
          this.userAvatar = API_BASE_URL + response.data.avatarUrl;
          console.log('加载用户头像成功:', this.userAvatar);
        }
      } catch (error) {
        console.error('获取用户头像失败:', error);
      }
    },
    
    // 获取团队申请数量
    fetchTeamApplicationsCount() {
      this.$store.dispatch('teamApplications/fetchPendingApplicationsCount')
    },
    
    // 启动定时获取申请数量
    startPendingApplicationsPolling() {
      this.pollTimer = setInterval(() => {
        this.fetchTeamApplicationsCount()
      }, 5 * 60 * 1000) // 每5分钟更新一次
    },
    
    // 停止定时获取
    stopPendingApplicationsPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
      }
    },
    
    // 开始定时检查用户信息
    startUserInfoPolling() {
      // 初始化lastUserInfoHash
      this.updateUserInfoHash();
      
      // 每5秒检查一次用户信息
      this.userInfoTimer = setInterval(() => {
        this.checkUserInfoUpdates();
      }, 5000);
    },
    
    // 停止定时检查用户信息
    stopUserInfoPolling() {
      if (this.userInfoTimer) {
        clearInterval(this.userInfoTimer);
      }
    },
    
    // 生成用户信息的哈希值，用于检测变化
    updateUserInfoHash() {
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        // 只关注可能在编辑资料页面更改的字段
        const relevantInfo = {
          name: user.name || '',
          customId: user.customId || '',
          email: user.email || '',
          hospital: user.hospital || '',
          department: user.department || ''
        };
        this.lastUserInfoHash = JSON.stringify(relevantInfo);
      } catch (error) {
        console.error('生成用户信息哈希值失败:', error);
      }
    },
    
    // 检查用户信息是否更新
    checkUserInfoUpdates() {
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        // 只关注可能在编辑资料页面更改的字段
        const relevantInfo = {
          name: user.name || '',
          customId: user.customId || '',
          email: user.email || '',
          hospital: user.hospital || '',
          department: user.department || ''
        };
        const currentHash = JSON.stringify(relevantInfo);
        
        // 如果哈希值不同，说明用户信息已更新
        if (currentHash !== this.lastUserInfoHash) {
          console.log('检测到用户信息更新，重新加载用户信息');
          this.loadUserInfo();
          this.lastUserInfoHash = currentHash;
        }
      } catch (error) {
        console.error('检查用户信息更新失败:', error);
      }
    },
    handleLogout() {
      console.log('[登出操作] 用户请求登出，当前URL:', window.location.href);
      
      // 清除所有导航状态标记
      sessionStorage.removeItem('isAppOperation');
      sessionStorage.removeItem('isNavigatingAfterSave');
      sessionStorage.removeItem('returningToWorkbench');
      
      // 设置明确的注销标记，确保API拦截器可以识别这是登出操作
      sessionStorage.setItem('isLogoutOperation', 'true');
      console.log('[登出操作] 已设置登出标记，准备清除用户会话');
      
      // 清除用户会话
      localStorage.removeItem('user');
      
      // 重定向到登录页
      console.log('[登出操作] 导航到登录页');
      this.$router.push('/login').then(() => {
        console.log('[登出操作] 导航到登录页成功');
      }).catch(err => {
        console.error('[登出操作] 导航失败:', err);
        // 备用导航方案
        window.location.href = '/login';
      });
      
      // 3秒后清除登出标记
      setTimeout(() => {
        console.log('[登出操作] 清除登出标记');
        sessionStorage.removeItem('isLogoutOperation');
      }, 3000);
    },
    goBack() {
      // For all pages, ask if user wants to go back because they might lose data
      this.$confirm('返回上一页将可能丢失当前未保存的数据，是否确认返回？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // Use browser history to go back, which is more reliable
        this.$router.go(-1);
      }).catch(() => {
        // User cancelled, do nothing
      });
    },
    // 检查是否已申请升级权限
    checkReviewerApplication() {
      // 只有标注医生需要检查
      if (!this.isDoctor) {
        return;
      }
      
      // 从API获取当前用户的申请状态
      api.users.getReviewerApplicationStatus()
        .then(response => {
          console.log('获取权限申请状态:', response.data);
          this.hasAppliedForReviewer = response.data && response.data.length > 0 && 
            response.data.some(app => app.status === 'PENDING');
        })
        .catch(error => {
          console.error('获取权限申请状态失败:', error);
        });
    },
    
    // 处理申请升级权限
    handleApplyForReviewer() {
      this.$prompt('请输入申请理由', '申请成为审核医生', {
        confirmButtonText: '提交申请',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请详细说明您申请成为审核医生的理由...'
      }).then(({ value }) => {
        if (!value || value.trim() === '') {
          this.$message.warning('申请理由不能为空');
          return;
        }
        
        // 提交申请
        api.users.applyForReviewer(value)
          .then(() => {
            this.$message.success('申请已提交，请等待管理员审核');
            this.hasAppliedForReviewer = true;
          })
          .catch(error => {
            if (error.response && error.response.data && error.response.data.message) {
              this.$message.error(error.response.data.message);
            } else {
              this.$message.error('申请提交失败，请稍后重试');
            }
          });
      }).catch(() => {
        // 用户取消输入，不做处理
      });
    },
    
    // 添加新的导航方法
    navigateToHemangiomaDiagnosis() {
      console.log('[导航] 准备导航到血管瘤诊断页面');
      try {
        this.$router.push('/app/hemangioma-diagnosis').then(() => {
          console.log('[导航] 成功导航到血管瘤诊断页面');
        }).catch(err => {
          console.error('[导航] 导航到血管瘤诊断页面失败:', err);
        });
      } catch (e) {
        console.error('[导航] 导航异常:', e);
      }
    },
    navigateToProfile() {
      console.log('[导航] 准备导航到个人中心页面');
      // 使用正确的URL格式，不要包含/medical前缀
      window.location.href = '/app/profile';
    }
  }
}
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #001529;
  height: 100%;
  overflow-x: hidden;
  z-index: 10;
}

.logo {
  height: 64px;
  line-height: 64px;
  text-align: center;
  font-size: 18px;
  color: white;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
  border-right: none !important;
  width: 100%;
}

.header {
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 9;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-info .el-avatar {
  margin-right: 8px;
}

.role-tag {
  margin-left: 8px;
  padding: 2px 6px;
  font-size: 12px;
  border-radius: 4px;
  color: white;
}

.role-tag-admin {
  background-color: #f56c6c;
}

.role-tag-reviewer {
  background-color: #e6a23c;
}

.role-tag-doctor {
  background-color: #409eff;
}

/* 团队菜单项样式 */
.team-menu-item {
  position: relative;
}

/* 团队申请数量徽章样式 */
.team-badge {
  position: absolute !important;
  top: 12px !important;
  right: 20px !important;
}

.team-badge .el-badge__content {
  height: 18px;
  min-width: 18px;
  line-height: 18px;
  padding: 0 6px;
  border-radius: 9px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 0 0 1px #fff;
  z-index: 11;
}
</style> 