{"version": 3, "file": "js/344.e01b8cd7.js", "mappings": "2NACOA,MAAM,mB,GACJA,MAAM,e,GAEJA,MAAM,kB,GA+BRA,MAAM,c,GAnCfC,IAAA,G,GAAAA,IAAA,G,GAAAA,IAAA,G,GAgGWD,MAAM,kB,GAhGjBC,IAAA,EA0G2CD,MAAM,kB,GAiBnCA,MAAM,iB,GAsCNA,MAAM,iB,GAmCNA,MAAM,iB,GA8BNA,MAAM,iB,2WAjOlBE,EAAAA,EAAAA,IAuOM,MAvONC,EAuOM,EAtOJC,EAAAA,EAAAA,IA8BM,MA9BNC,EA8BM,gBA7BJD,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IA2BM,MA3BNE,EA2BM,CAxBKC,EAAAC,0BAPjBC,EAAAA,EAAAA,IAAA,SAOyC,WADjCC,EAAAA,EAAAA,IAMYC,EAAA,CAZpBV,IAAA,EAQUW,KAAK,UACJC,QAAON,EAAAO,sB,CATlB,SAAAC,EAAAA,EAAAA,KAUS,kBAEDC,EAAA,MAAAA,EAAA,MAZRC,EAAAA,EAAAA,IAUS,W,IAVTC,EAAA,EAAAC,GAAA,M,gBAgBiBZ,EAAAC,2BAA6BD,EAAAa,UAAWb,EAAAc,YAhBzDZ,EAAAA,EAAAA,IAAA,SAgBmE,WAD3DC,EAAAA,EAAAA,IAMYC,EAAA,CArBpBV,IAAA,EAiBUW,KAAK,UACJC,QAAON,EAAAe,wB,CAlBlB,SAAAP,EAAAA,EAAAA,KAmBS,kBAEDC,EAAA,MAAAA,EAAA,MArBRC,EAAAA,EAAAA,IAmBS,W,IAnBTC,EAAA,EAAAC,GAAA,M,gBAyBgBZ,EAAAa,UAAO,WADfV,EAAAA,EAAAA,IAMYC,EAAA,CA9BpBV,IAAA,EA0BUW,KAAK,UACJC,QAAON,EAAAgB,0B,CA3BlB,SAAAR,EAAAA,EAAAA,KA4BS,kBAEDC,EAAA,MAAAA,EAAA,MA9BRC,EAAAA,EAAAA,IA4BS,a,IA5BTC,EAAA,EAAAC,GAAA,M,iBAAAV,EAAAA,EAAAA,IAAA,YAmCIL,EAAAA,EAAAA,IAmBM,MAnBNoB,EAmBM,EAlBJC,EAAAA,EAAAA,IAiBUC,EAAA,CAjBAC,QAAQ,EAAM3B,MAAM,e,CApCpC,SAAAe,EAAAA,EAAAA,KAqCQ,iBAIe,EAJfU,EAAAA,EAAAA,IAIeG,EAAA,CAJDC,MAAM,QAAM,CArClC,SAAAd,EAAAA,EAAAA,KAsCU,iBAEY,EAFZU,EAAAA,EAAAA,IAEYK,EAAA,CAxCtBC,WAsC8BC,EAAAC,iBAtC9B,sBAAAjB,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAsC8BF,EAAAC,iBAAgBC,CAAA,GAAEC,YAAY,OAAOC,UAAA,I,CAtCnE,SAAArB,EAAAA,EAAAA,KAuCuB,iBAA2B,gBAAtCb,EAAAA,EAAAA,IAA2FmC,EAAAA,GAAA,MAvCvGC,EAAAA,EAAAA,IAuCsCN,EAAAO,aAvCtC,SAuC8BC,G,kBAAlB9B,EAAAA,EAAAA,IAA2F+B,EAAA,CAAnDxC,IAAKuC,EAAOX,MAAOW,EAAOE,MAAOF,G,uCAvCrFtB,EAAA,G,sBAAAA,EAAA,KA0CQO,EAAAA,EAAAA,IAMeG,EAAA,CANDC,MAAM,QAAM,CA1ClC,SAAAd,EAAAA,EAAAA,KA2CU,iBAIY,EAJZU,EAAAA,EAAAA,IAIYK,EAAA,CA/CtBC,WA2C8BC,EAAAW,WA3C9B,sBAAA3B,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OA2C8BF,EAAAW,WAAUT,CAAA,GAAEC,YAAY,OAAOC,UAAA,I,CA3C7D,SAAArB,EAAAA,EAAAA,KA4CY,iBAAiD,EAAjDU,EAAAA,EAAAA,IAAiDgB,EAAA,CAAtCZ,MAAM,MAAMa,MAAM,WAC7BjB,EAAAA,EAAAA,IAAmDgB,EAAA,CAAxCZ,MAAM,OAAOa,MAAM,YAC9BjB,EAAAA,EAAAA,IAAqDgB,EAAA,CAA1CZ,MAAM,OAAOa,MAAM,a,IA9C1CxB,EAAA,G,sBAAAA,EAAA,KAiDQO,EAAAA,EAAAA,IAGeG,EAAA,MApDvB,SAAAb,EAAAA,EAAAA,KAkDU,iBAA8D,EAA9DU,EAAAA,EAAAA,IAA8Dd,EAAA,CAAnDC,KAAK,UAAWC,QAAON,EAAAqC,c,CAlD5C,SAAA7B,EAAAA,EAAAA,KAkD0D,kBAAEC,EAAA,MAAAA,EAAA,MAlD5DC,EAAAA,EAAAA,IAkD0D,O,IAlD1DC,EAAA,EAAAC,GAAA,M,gBAmDUM,EAAAA,EAAAA,IAA+Cd,EAAA,CAAnCE,QAAON,EAAAsC,cAAY,CAnDzC,SAAA9B,EAAAA,EAAAA,KAmD2C,kBAAEC,EAAA,MAAAA,EAAA,MAnD7CC,EAAAA,EAAAA,IAmD2C,O,IAnD3CC,EAAA,EAAAC,GAAA,M,mBAAAD,EAAA,I,IAAAA,EAAA,O,qBAwDIR,EAAAA,EAAAA,IAkBWoC,EAAA,CAlBoBC,KAAMxC,EAAAyC,aAAcC,MAAA,gB,CAxDvD,SAAAlC,EAAAA,EAAAA,KAyDM,iBAAsD,EAAtDU,EAAAA,EAAAA,IAAsDyB,EAAA,CAArCC,KAAK,OAAOtB,MAAM,KAAKuB,MAAM,SAC9C3B,EAAAA,EAAAA,IAA8DyB,EAAA,CAA7CC,KAAK,aAAatB,MAAM,OAAOuB,MAAM,SACtD3B,EAAAA,EAAAA,IAMkByB,EAAA,CANDC,KAAK,OAAOtB,MAAM,M,CACtBwB,SAAOtC,EAAAA,EAAAA,KAChB,SAESuC,GAHc,QACvB7B,EAAAA,EAAAA,IAES8B,EAAA,CAFA3C,KAAML,EAAAiD,YAAYF,EAAMG,IAAIC,O,CA7D/C,SAAA3C,EAAAA,EAAAA,KA8DY,iBAAiC,EA9D7CE,EAAAA,EAAAA,KAAA0C,EAAAA,EAAAA,IA8DepD,EAAAqD,YAAYN,EAAMG,IAAIC,OAAI,G,IA9DzCxC,EAAA,G,mBAAAA,EAAA,KAkEMO,EAAAA,EAAAA,IAAgDyB,EAAA,CAA/BC,KAAK,WAAWtB,MAAM,UACvCJ,EAAAA,EAAAA,IAMkByB,EAAA,CANDC,KAAK,OAAOtB,MAAM,OAAOuB,MAAM,O,CACnCC,SAAOtC,EAAAA,EAAAA,KAaK,SAIpBuC,GAjBsB,OACXA,EAAMG,IAAII,MAAQP,EAAMG,IAAII,KAAKC,OAAI,WAAjD5D,EAAAA,EAAAA,IAAmF,OArE7F6D,GAAAJ,EAAAA,EAAAA,IAqEgEL,EAAMG,IAAII,KAAKC,MAAI,IACxDR,EAAMG,IAAII,MAAkC,kBAAnBP,EAAMG,IAAII,OAAI,WAAxD3D,EAAAA,EAAAA,IAAkG,OAtE5G8D,GAAAL,EAAAA,EAAAA,IAsEoFL,EAAMG,IAAII,MAAI,iBACxF3D,EAAAA,EAAAA,IAAyB,OAvEnC+D,EAuEuB,U,IAvEvB/C,EAAA,I,IAAAA,EAAA,G,iBAwDyBc,EAAAkC,YAoBrBzC,EAAAA,EAAAA,IASE0C,EAAA,CARAC,WAAA,GACAC,OAAO,kCACNC,MAAOtC,EAAAuC,cAAcC,OACrB,YAAWxC,EAAAyC,SACX,aAAY,CAAC,GAAI,GAAI,GAAI,KAC1BzE,MAAM,aACL0E,gBAAgBnE,EAAAoE,iBAChBC,aAAarE,EAAAsE,kB,gEAIhBpD,EAAAA,EAAAA,IAwCYqD,EAAA,CAhIhB/C,WAyFeC,EAAA+C,cAzFf,sBAAA/D,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAyFeF,EAAA+C,cAAa7C,CAAA,GACtB8C,MAAM,OACN5B,MAAM,QACL,wBAAsB,EACtB6B,QAAO1E,EAAA2E,a,CA6BGC,QAAMpE,EAAAA,EAAAA,KACf,iBAGO,EAHPX,EAAAA,EAAAA,IAGO,OAHPgF,EAGO,EAFL3D,EAAAA,EAAAA,IAAwDd,EAAA,CAA5CE,QAAKG,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAAEF,EAAA+C,eAAgB,CAAH,I,CA5H1C,SAAAhE,EAAAA,EAAAA,KA4HoD,kBAAEC,EAAA,MAAAA,EAAA,MA5HtDC,EAAAA,EAAAA,IA4HoD,O,IA5HpDC,EAAA,EAAAC,GAAA,QA6HUM,EAAAA,EAAAA,IAA6Dd,EAAA,CAAlDC,KAAK,UAAWC,QAAON,EAAA8E,Y,CA7H5C,SAAAtE,EAAAA,EAAAA,KA6HwD,kBAAGC,EAAA,MAAAA,EAAA,MA7H3DC,EAAAA,EAAAA,IA6HwD,Q,IA7HxDC,EAAA,EAAAC,GAAA,M,qBAAA,SAAAJ,EAAAA,EAAAA,KAgGM,iBAOM,EAPNX,EAAAA,EAAAA,IAOM,MAPNkF,EAOM,EANJ7D,EAAAA,EAAAA,IAKY8D,EAAA,CAtGpBxD,WAkGmBC,EAAAwD,YAlGnB,sBAAAxE,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAkGmBF,EAAAwD,YAAWtD,CAAA,GACpBC,YAAY,iBACZC,UAAA,GACCqD,QAAOlF,EAAAmF,c,mCAKD1D,EAAA2D,cAAcnB,OAAS,IAAH,WAA/BtE,EAAAA,EAAAA,IAWM,MAXN0F,EAWM,EAVJnE,EAAAA,EAAAA,IASWqB,EAAA,CARRC,KAAMf,EAAA2D,cACP1C,MAAA,eACA,2BACC4C,WAAWtF,EAAAuF,gB,CA/GtB,SAAA/E,EAAAA,EAAAA,KAiHU,iBAAsE,EAAtEU,EAAAA,EAAAA,IAAsEyB,EAAA,CAArDC,KAAK,OAAOtB,MAAM,KAAKuB,MAAM,SAC9C3B,EAAAA,EAAAA,IAA4EyB,EAAA,CAA3DC,KAAK,aAAatB,MAAM,KAAKuB,MAAM,SACpD3B,EAAAA,EAAAA,IAAgEyB,EAAA,CAA/CC,KAAK,WAAWtB,MAAM,S,IAnHjDX,EAAA,G,6BAAAT,EAAAA,EAAAA,IAAA,OAwHwBuB,EAAA2D,cAAcnB,OAAS,IAAH,WAAtC9D,EAAAA,EAAAA,IAAyDqF,EAAA,CAxH/D9F,IAAA,MAAAQ,EAAAA,EAAAA,IAAA,O,IAAAS,EAAA,G,6BAmIIO,EAAAA,EAAAA,IAmCYqD,EAAA,CAtKhB/C,WAoIeC,EAAAgE,oBAAoBC,QApInC,sBAAAjF,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAoIeF,EAAAgE,oBAAoBC,QAAO/D,CAAA,GACpC8C,MAAM,OACN5B,MAAM,QACL6B,QAAKjE,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAAEF,EAAAgE,oBAAoBC,SAAU,CAAH,I,CAyBxBd,QAAMpE,EAAAA,EAAAA,KACf,iBAGO,EAHPX,EAAAA,EAAAA,IAGO,OAHP8F,EAGO,EAFLzE,EAAAA,EAAAA,IAAsEd,EAAA,CAA1DE,QAAKG,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAAEF,EAAAgE,oBAAoBC,SAAU,CAAH,I,CAlKxD,SAAAlF,EAAAA,EAAAA,KAkKkE,kBAAEC,EAAA,MAAAA,EAAA,MAlKpEC,EAAAA,EAAAA,IAkKkE,O,IAlKlEC,EAAA,EAAAC,GAAA,QAmKUM,EAAAA,EAAAA,IAAqEd,EAAA,CAA1DC,KAAK,UAAWC,QAAON,EAAA4F,qB,CAnK5C,SAAApF,EAAAA,EAAAA,KAmKiE,kBAAEC,EAAA,MAAAA,EAAA,MAnKnEC,EAAAA,EAAAA,IAmKiE,O,IAnKjEC,EAAA,EAAAC,GAAA,M,qBAAA,SAAAJ,EAAAA,EAAAA,KAyIM,iBAsBU,EAtBVU,EAAAA,EAAAA,IAsBUC,EAAA,CArBR0E,IAAI,oBACHC,MAAOrE,EAAAgE,oBAAoBM,KAC3BC,MAAOvE,EAAAwE,mBACR,cAAY,S,CA7IpB,SAAAzF,EAAAA,EAAAA,KA+IQ,iBAOe,EAPfU,EAAAA,EAAAA,IAOeG,EAAA,CAPDC,MAAM,MAAMsB,KAAK,Y,CA/IvC,SAAApC,EAAAA,EAAAA,KAgJU,iBAKY,EALZU,EAAAA,EAAAA,IAKY8D,EAAA,CArJtBxD,WAiJqBC,EAAAgE,oBAAoBM,KAAKG,SAjJ9C,sBAAAzF,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAiJqBF,EAAAgE,oBAAoBM,KAAKG,SAAQvE,CAAA,GAC1CtB,KAAK,WACLuB,YAAY,SACZ,oB,2BApJZjB,EAAA,KAuJQO,EAAAA,EAAAA,IAOeG,EAAA,CAPDC,MAAM,QAAQsB,KAAK,mB,CAvJzC,SAAApC,EAAAA,EAAAA,KAwJU,iBAKY,EALZU,EAAAA,EAAAA,IAKY8D,EAAA,CA7JtBxD,WAyJqBC,EAAAgE,oBAAoBM,KAAKI,gBAzJ9C,sBAAA1F,EAAA,KAAAA,EAAA,YAAAkB,GAAA,OAyJqBF,EAAAgE,oBAAoBM,KAAKI,gBAAexE,CAAA,GACjDtB,KAAK,WACLuB,YAAY,SACZ,oB,2BA5JZjB,EAAA,I,IAAAA,EAAA,G,yBAAAA,EAAA,G,mBAyKIO,EAAAA,EAAAA,IAkCYqD,EAAA,CA3MhB/C,WA0KeC,EAAA2E,qBAAqBV,QA1KpC,sBAAAjF,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OA0KeF,EAAA2E,qBAAqBV,QAAO/D,CAAA,GACrC8C,MAAM,OACN5B,MAAM,QACL6B,QAAKjE,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OAAEF,EAAA2E,qBAAqBV,SAAU,CAAH,I,CAsBzBd,QAAMpE,EAAAA,EAAAA,KACf,iBAKO,EALPX,EAAAA,EAAAA,IAKO,OALPwG,EAKO,EAJLnF,EAAAA,EAAAA,IAAuEd,EAAA,CAA3DE,QAAKG,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OAAEF,EAAA2E,qBAAqBV,SAAU,CAAH,I,CArMzD,SAAAlF,EAAAA,EAAAA,KAqMmE,kBAAEC,EAAA,MAAAA,EAAA,MArMrEC,EAAAA,EAAAA,IAqMmE,O,IArMnEC,EAAA,EAAAC,GAAA,QAsMUM,EAAAA,EAAAA,IAEYd,EAAA,CAFDC,KAAK,UAAWC,QAAON,EAAAsG,sB,CAtM5C,SAAA9F,EAAAA,EAAAA,KAuMY,iBAAsD,EAvMlEE,EAAAA,EAAAA,KAAA0C,EAAAA,EAAAA,IAuMe3B,EAAA2E,qBAAqBL,KAAKQ,OAAS,OAAS,MAAZ,G,IAvM/C5F,EAAA,G,qBAAA,SAAAH,EAAAA,EAAAA,KA+KM,iBAmBU,EAnBVU,EAAAA,EAAAA,IAmBUC,EAAA,CAnBA2E,MAAOrE,EAAA2E,qBAAqBL,KAAM,cAAY,Q,CA/K9D,SAAAvF,EAAAA,EAAAA,KAgLQ,iBASe,EATfU,EAAAA,EAAAA,IASeG,EAAA,CATDC,MAAM,MAAI,CAhLhC,SAAAd,EAAAA,EAAAA,KAiLU,iBAOY,EAPZU,EAAAA,EAAAA,IAOYK,EAAA,CAxLtBC,WAkLqBC,EAAA2E,qBAAqBL,KAAKS,WAlL/C,sBAAA/F,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OAkLqBF,EAAA2E,qBAAqBL,KAAKS,WAAU7E,CAAA,GAC7CC,YAAY,QACZc,MAAA,eACC+D,SAAQzG,EAAA0G,wB,CArLrB,SAAAlG,EAAAA,EAAAA,KAuLuB,iBAA2B,gBAAtCb,EAAAA,EAAAA,IAA2FmC,EAAAA,GAAA,MAvLvGC,EAAAA,EAAAA,IAuLsCN,EAAAO,aAvLtC,SAuL8BC,G,kBAAlB9B,EAAAA,EAAAA,IAA2F+B,EAAA,CAAnDxC,IAAKuC,EAAOX,MAAOW,EAAOE,MAAOF,G,uCAvLrFtB,EAAA,G,iCAAAA,EAAA,IA0L4Bc,EAAA2E,qBAAqBL,KAAKQ,SAAM,WAApDpG,EAAAA,EAAAA,IAOekB,EAAA,CAjMvB3B,IAAA,EA0L8D4B,MAAM,Q,CA1LpE,SAAAd,EAAAA,EAAAA,KA2LU,iBAKY,EALZU,EAAAA,EAAAA,IAKY8D,EAAA,CAhMtBxD,WA4LqBC,EAAA2E,qBAAqBL,KAAKY,OA5L/C,sBAAAlG,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OA4LqBF,EAAA2E,qBAAqBL,KAAKY,OAAMhF,CAAA,GACzCtB,KAAK,WACLuB,YAAY,mBACXgF,KAAM,G,2BA/LnBjG,EAAA,MAAAT,EAAAA,EAAAA,IAAA,O,IAAAS,EAAA,G,iBAAAA,EAAA,G,mBA8MIO,EAAAA,EAAAA,IAyBYqD,EAAA,CAvOhB/C,WA+MeC,EAAAoF,uBAAuBnB,QA/MtC,sBAAAjF,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OA+MeF,EAAAoF,uBAAuBnB,QAAO/D,CAAA,GACvC8C,MAAM,OACN5B,MAAM,QACL6B,QAAKjE,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OAAEF,EAAAoF,uBAAuBnB,SAAU,CAAH,I,CAe3Bd,QAAMpE,EAAAA,EAAAA,KACf,iBAGO,EAHPX,EAAAA,EAAAA,IAGO,OAHPiH,EAGO,EAFL5F,EAAAA,EAAAA,IAAyEd,EAAA,CAA7DE,QAAKG,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OAAEF,EAAAoF,uBAAuBnB,SAAU,CAAH,I,CAnO3D,SAAAlF,EAAAA,EAAAA,KAmOqE,kBAAEC,EAAA,MAAAA,EAAA,MAnOvEC,EAAAA,EAAAA,IAmOqE,O,IAnOrEC,EAAA,EAAAC,GAAA,QAoOUM,EAAAA,EAAAA,IAAwEd,EAAA,CAA7DC,KAAK,UAAWC,QAAON,EAAA+G,wB,CApO5C,SAAAvG,EAAAA,EAAAA,KAoOoE,kBAAEC,EAAA,MAAAA,EAAA,MApOtEC,EAAAA,EAAAA,IAoOoE,O,IApOpEC,EAAA,EAAAC,GAAA,M,qBAAA,SAAAJ,EAAAA,EAAAA,KAoNM,iBAYU,EAZVU,EAAAA,EAAAA,IAYUC,EAAA,CAZD0E,IAAI,uBAAwBC,MAAOrE,EAAAoF,uBAAuBd,KAAOC,MAAOvE,EAAAuF,gBAAiB,cAAY,Q,CApNpH,SAAAxG,EAAAA,EAAAA,KAqNQ,iBAEe,EAFfU,EAAAA,EAAAA,IAEeG,EAAA,CAFDC,MAAM,OAAOsB,KAAK,Q,CArNxC,SAAApC,EAAAA,EAAAA,KAsNU,iBAAsF,EAAtFU,EAAAA,EAAAA,IAAsF8D,EAAA,CAtNhGxD,WAsN6BC,EAAAoF,uBAAuBd,KAAKxC,KAtNzD,sBAAA9C,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OAsN6BF,EAAAoF,uBAAuBd,KAAKxC,KAAI5B,CAAA,GAAEC,YAAY,W,2BAtN3EjB,EAAA,KAwNQO,EAAAA,EAAAA,IAOeG,EAAA,CAPDC,MAAM,KAAKsB,KAAK,e,CAxNtC,SAAApC,EAAAA,EAAAA,KAyNU,iBAKY,EALZU,EAAAA,EAAAA,IAKY8D,EAAA,CA9NtBxD,WA0NqBC,EAAAoF,uBAAuBd,KAAKkB,YA1NjD,sBAAAxG,EAAA,MAAAA,EAAA,aAAAkB,GAAA,OA0NqBF,EAAAoF,uBAAuBd,KAAKkB,YAAWtF,CAAA,GAChDtB,KAAK,WACLuB,YAAY,UACXgF,KAAM,G,2BA7NnBjG,EAAA,I,IAAAA,EAAA,G,yBAAAA,EAAA,G,mZA+OA,SACE4C,KAAM,QACNf,KAAI,WAAG,IAAA0E,EAAA,KAECC,EAA0B,SAACC,EAAMjF,EAAOkF,GACxClF,IAAU+E,EAAKI,SAASpB,SAC1BmB,EAAS,IAAIE,MAAM,eAEnBF,GAEJ,EAGMG,EAA+B,SAACJ,EAAMjF,EAAOkF,GAC7ClF,IAAU+E,EAAKzB,oBAAoBM,KAAKG,SAC1CmB,EAAS,IAAIE,MAAM,eAEnBF,GAEJ,EAEA,MAAO,CACL1D,SAAS,EACT8D,MAAO,GACPzD,cAAe,GACfhC,YAAa,GACbN,iBAAkB,GAClBU,WAAY,GACZsF,YAAa,EACbxD,SAAU,GACVM,eAAe,EACfmD,YAAY,EACZ1C,YAAa,GACbG,cAAe,GACfkC,SAAU,CACRM,GAAI,KACJrE,KAAM,GACNJ,KAAM,SACNqD,WAAY,GACZqB,SAAU,GACVC,MAAO,GACP5B,SAAU,GACVC,gBAAiB,GACjB4B,QAAQ,EACRC,UAAW,MAEbC,UAAW,CACTC,SAAU,CACR,CAAEC,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,SAEzD9E,KAAM,CAAC,CAAE4E,UAAU,EAAMC,QAAS,QAASC,QAAS,SACpDlF,KAAM,CAAC,CAAEgF,UAAU,EAAMC,QAAS,QAASC,QAAS,WACpD7B,WAAY,CAAC,CAAE2B,UAAU,EAAMC,QAAS,WAAYC,QAAS,WAC7DR,SAAU,CAAC,CAAEM,UAAU,EAAMC,QAAS,UAAWC,QAAS,SAC1DP,MAAO,CACL,CAAEK,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEhI,KAAM,QAAS+H,QAAS,aAAcC,QAAS,SAEnDnC,SAAU,CACR,CAAEiC,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,IAAK,EAAGF,QAAS,aAAcC,QAAS,SAE5ClC,gBAAiB,CACf,CAAEgC,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEG,UAAWrB,EAAyBkB,QAAS,UAGnD5C,oBAAqB,CACnBC,SAAS,EACT+C,OAAQ,KACR1C,KAAM,CACJG,SAAU,GACVC,gBAAiB,KAGrBF,mBAAoB,CAClBC,SAAU,CACR,CAAEiC,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEC,IAAK,EAAGF,QAAS,aAAcC,QAAS,SAE5ClC,gBAAiB,CACf,CAAEgC,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEG,UAAWhB,EAA8Ba,QAAS,UAGxDK,aAAc,KAEdtC,qBAAsB,CACpBV,SAAS,EACTK,KAAM,CACJS,WAAY,GACZD,OAAQ,KACRI,OAAQ,KAIZE,uBAAwB,CACtBnB,SAAS,EACTK,KAAM,CACJxC,KAAM,GACN0D,YAAa,KAIjBD,gBAAiB,CACfzD,KAAM,CACJ,CAAE4E,UAAU,EAAMC,QAAS,UAAWC,QAAS,QAC/C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,UAG3DM,qBAAsB,GAE1B,EACAC,UAAQC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,IACHC,EAAAA,EAAAA,IAAW,CACZC,YAAa,aACb,IAEF9I,yBAAwB,WACtB,OAAO+I,KAAKD,aAAeC,KAAKD,YAAYvC,UAC9C,EAEA3F,QAAO,WACL,OAAOmI,KAAKD,aAAyC,UAA1BC,KAAKD,YAAY5F,IAC9C,EAEA8F,SAAQ,WACN,OAAOD,KAAKD,aAAyC,WAA1BC,KAAKD,YAAY5F,IAC9C,EAEArC,WAAU,WACR,OAAOkI,KAAKD,aAAyC,aAA1BC,KAAKD,YAAY5F,IAC9C,EAEAV,aAAY,WACV,IAAMyG,GAASF,KAAKtB,YAAc,GAAKsB,KAAK9E,SACtCiF,EAAMD,EAAQF,KAAK9E,SACzB,OAAO8E,KAAKhF,cAAcoF,MAAMF,EAAOC,EACzC,IAEFE,QAAO,WAUL,GARAL,KAAKM,aAGDN,KAAKnI,SACPmI,KAAKO,6BAIFP,KAAKD,YAAa,CACrB,IAAMS,EAAaC,aAAaC,QAAQ,QACxC,GAAIF,EACF,IACER,KAAKW,OAAOC,OAAO,UAAWC,KAAKC,MAAMN,GAC3C,CAAE,MAAOO,GACPC,QAAQC,MAAM,WAAYF,EAC5B,CAEJ,CACF,EACAG,QAAS,CACPjH,YAAW,SAACE,GACV,IAAMgH,EAAQ,CACZ,MAAS,SACT,OAAU,UACV,SAAY,WAEd,OAAOA,EAAMhH,IAAS,MACxB,EACAE,YAAW,SAACF,GACV,IAAMiH,EAAQ,CACZ,MAAS,MACT,OAAU,OACV,SAAY,QAEd,OAAOA,EAAMjH,IAAS,IACxB,EACAkH,WAAU,SAACC,GACT,IAAKA,EAAY,MAAO,KACxB,IAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,eAAe,QAC7B,EACArG,iBAAgB,SAACsG,GACf1B,KAAKtB,YAAcgD,CACrB,EACApG,iBAAgB,SAACqG,GACf3B,KAAK9E,SAAWyG,EAChB3B,KAAKtB,YAAc,CACrB,EACArF,aAAY,WACV2G,KAAK4B,cACL5B,KAAKtB,YAAc,CACrB,EACApF,aAAY,WACV0G,KAAKtH,iBAAmB,GACxBsH,KAAK5G,WAAa,GAClB4G,KAAK4B,cACL5B,KAAKtB,YAAc,CACrB,EACAkD,YAAW,WAAG,IAAAC,EAAA,KACZ,GAAK7B,KAAKtH,kBAAqBsH,KAAK5G,WAKpC,OAAK0I,MAAMC,QAAQ/B,KAAKvB,YAMxBuB,KAAKhF,cAAgBgF,KAAKvB,MAAMuD,QAAO,SAAAC,GACrC,IAAKA,EAAM,OAAO,EAClB,IAAMC,GAAmBL,EAAKnJ,kBAC3BuJ,EAAKzE,YAAcyE,EAAKzE,aAAeqE,EAAKnJ,iBACzCyJ,GAAaN,EAAKzI,YACrB6I,EAAK9H,MAAQ8H,EAAK9H,OAAS0H,EAAKzI,WACnC,OAAO8I,GAAmBC,CAC5B,MAZEnB,QAAQC,MAAM,iBAAkBjB,KAAKvB,YACrCuB,KAAKhF,cAAgB,KANrBgF,KAAKhF,cAAgB8G,MAAMC,QAAQ/B,KAAKvB,QAAK2D,EAAAA,EAAAA,GAAQpC,KAAKvB,OAAS,EAkBvE,EACA6B,WAAU,WAAG,IAAA+B,EAAA,KACXrC,KAAKrF,SAAU,EAGf2H,EAAAA,WAAI7D,MAAM8D,SACPC,MAAK,SAAAC,GAEAA,GAAYA,EAASjJ,KAEnBsI,MAAMC,QAAQU,EAASjJ,MACzB6I,EAAK5D,MAAQgE,EAASjJ,KACbiJ,EAASjJ,KAAKkJ,SAAWZ,MAAMC,QAAQU,EAASjJ,KAAKkJ,SAE9DL,EAAK5D,MAAQgE,EAASjJ,KAAKkJ,QAClBD,EAASjJ,KAAKA,MAAQsI,MAAMC,QAAQU,EAASjJ,KAAKA,MAE3D6I,EAAK5D,MAAQgE,EAASjJ,KAAKA,KAClBiJ,EAASjJ,KAAKmJ,OAASb,MAAMC,QAAQU,EAASjJ,KAAKmJ,OAE5DN,EAAK5D,MAAQgE,EAASjJ,KAAKmJ,MAClBF,EAASjJ,KAAKiF,OAASqD,MAAMC,QAAQU,EAASjJ,KAAKiF,OAE5D4D,EAAK5D,MAAQgE,EAASjJ,KAAKiF,MACO,YAAzBmE,EAAAA,EAAAA,GAAOH,EAASjJ,OAEzBwH,QAAQ6B,KAAK,iBAAkBJ,EAASjJ,MACxC6I,EAAK5D,MAAQ,KAGbuC,QAAQC,MAAM,eAAgBwB,EAASjJ,MACvC6I,EAAK5D,MAAQ,IAIf4D,EAAK5D,MAAQ,GAIf4D,EAAK5D,MAAQ4D,EAAK5D,MAAMqE,KAAI,SAAAb,GAa1B,OAXKA,EAAK3H,KAIoB,kBAAd2H,EAAK3H,KACnB2H,EAAK3H,KAAO,CAAEC,KAAM0H,EAAK3H,MAGG,YAArBsI,EAAAA,EAAAA,GAAOX,EAAK3H,OAAsB2H,EAAK3H,KAAKC,OACnD0H,EAAK3H,KAAKC,KAAO,QARjB0H,EAAK3H,KAAO,CAAEC,KAAM,SAUf0H,CACT,IAEAI,EAAKrH,eAAYoH,EAAAA,EAAAA,GAAQC,EAAK5D,OAG9B,IAAMsE,EAAc,IAAIC,IACxBX,EAAK5D,MAAMwE,SAAQ,SAAAhB,GACbA,GAAQA,EAAKzE,YACfuF,EAAYG,IAAIjB,EAAKzE,WAEzB,IAGA,IAAM2F,EAAgB,CAAC,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,OAS9D,OANAd,EAAKrJ,YAAU,GAAAoK,OACVD,GAAaf,EAAAA,EAAAA,GACbN,MAAMuB,KAAKN,GAAaf,QAAO,SAAA/I,GAAG,OAAMkK,EAAcG,SAASrK,EAAK,MAIlEqJ,EAAAA,WAAIiB,MAAMhB,QACnB,IACCC,MAAK,SAAAgB,GACJ,GAAIA,GAAiBA,EAAchK,KAAM,CACvC,IAAM+J,EAAQC,EAAchK,KAGtBiK,EAAeF,EAAMT,KAAI,SAAAxI,GAAG,OAChCgI,EAAAA,WAAIiB,MAAMG,eAAepJ,EAAKsE,IAC3B4D,MAAK,SAAAmB,GACJ,IAAMC,EAAUD,EAAgBnK,MAAQ,GACxC,MAAO,CAAE+D,OAAQjD,EAAKsE,GAAIiF,SAAUvJ,EAAKC,KAAMqJ,QAAAA,EACjD,IAAC,UACM,SAAA3C,GAEL,OADAD,QAAQC,MAAM,QAADmC,OAAS9I,EAAKsE,GAAE,UAAUqC,GAChC,CAAE1D,OAAQjD,EAAKsE,GAAIiF,SAAUvJ,EAAKC,KAAMqJ,QAAS,GAC1D,GAAC,IAGL,OAAOE,QAAQC,IAAIN,EACrB,CACA,MAAO,EACT,IACCjB,MAAK,SAAAwB,GAEJ,GAAIA,GAAoBA,EAAiB/I,OAAS,EAAG,CAEnD,IAAMgJ,EAAc,CAAC,EAGrBD,EAAiBf,SAAQ,SAAAiB,GACvB,IAAQ3G,EAA8B2G,EAA9B3G,OAAQsG,EAAsBK,EAAtBL,SAAUD,EAAYM,EAAZN,QAG1BA,EAAQX,SAAQ,SAAAkB,GACVA,GAAUA,EAAOvF,KACnBqF,EAAYE,EAAOvF,IAAM,CAAEA,GAAIrB,EAAQhD,KAAMsJ,GAEjD,GACF,IAGAxB,EAAK5D,MAAQ4D,EAAK5D,MAAMqE,KAAI,SAAAb,GAI1B,OAHIA,GAAQA,EAAKrD,IAAMqF,EAAYhC,EAAKrD,MACtCqD,EAAK3H,KAAO2J,EAAYhC,EAAKrD,KAExBqD,CACT,IAGAI,EAAKrH,eAAYoH,EAAAA,EAAAA,GAAQC,EAAK5D,MAChC,CACF,IAAC,UACM,SAAAwC,GACLD,QAAQC,MAAM,YAAaA,GAC3BoB,EAAK+B,SAASnD,MAAM,cAAgBA,EAAM7B,SAAW,SAErDiD,EAAK5D,MAAQ,GACb4D,EAAKrH,cAAgB,EACvB,IAAC,YACQ,WACPqH,EAAK1H,SAAU,CACjB,GACJ,EACA0J,cAAa,WACXrE,KAAK1B,SAAW,CACdM,GAAI,KACJrE,KAAM,GACNJ,KAAM,SACNqD,WAAY,GACZqB,SAAU,GACVC,MAAO,GACP5B,SAAU,GACVC,gBAAiB,GACjB4B,QAAQ,EACRC,UAAW,MAEbgB,KAAKrB,YAAa,EAClBqB,KAAK/D,YAAc,GACnB+D,KAAK5D,cAAgB,GACrB4D,KAAKxE,eAAgB,CACvB,EACA8I,eAAc,SAACpK,GACb8F,KAAK1B,SAAW,CACdM,GAAI1E,EAAI0E,GACRrE,KAAML,EAAIK,KACVJ,KAAMD,EAAIC,KACVqD,WAAYtD,EAAIsD,WAChBqB,SAAU3E,EAAI2E,SACdC,MAAO5E,EAAI4E,MACXC,QAAuB,IAAf7E,EAAI6E,OACZC,UAAW9E,EAAI8E,WAEjBgB,KAAKxE,eAAgB,CACvB,EACA+I,iBAAgB,SAACrK,GAAK,IAAAsK,EAAA,KACpBxE,KAAKrF,SAAU,EAEf2H,EAAAA,WAAI7D,MAAMgG,WAAWvK,EAAI0E,IACtB4D,MAAK,WACJgC,EAAKJ,SAASM,QAAQ,MAADtB,OAAOlJ,EAAIK,KAAI,SACpCiK,EAAKlE,YACP,IAAC,UACM,SAAAW,GACLD,QAAQC,MAAM,UAAWA,GACzBuD,EAAKJ,SAASnD,MAAM,UAAYA,EAAM7B,SAAW,QACnD,IAAC,YACQ,WACPoF,EAAK7J,SAAU,CACjB,GACJ,EACAgK,mBAAkB,SAACzK,EAAK0K,GAAQ,IAAAC,EAAA,KACxBC,GAAUjF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAS3F,GAAG,IAAE6E,OAAQ6F,IAEtCtC,EAAAA,WAAI7D,MAAMsG,WAAW7K,EAAI0E,GAAIkG,GAC1BtC,MAAK,WACJqC,EAAKT,SAASM,QAAQ,IAADtB,OAAKwB,EAAS,KAAO,KAAI,QAAAxB,OAAOlJ,EAAIK,MAC3D,IAAC,UACM,SAAA0G,GACLD,QAAQC,MAAM,YAAaA,GAC3B4D,EAAKT,SAASnD,MAAM,YAAcA,EAAM7B,SAAW,SACnDlF,EAAI6E,QAAU6F,CAChB,GACJ,EACAI,oBAAmB,SAAC9K,GAClB8F,KAAKvD,oBAAoBC,SAAU,EACnCsD,KAAKvD,oBAAoBgD,OAASvF,EAAI0E,GACtCoB,KAAKvD,oBAAoBM,KAAO,CAC9BG,SAAU,GACVC,gBAAiB,GAErB,EACAxB,YAAW,WACTqE,KAAKxE,eAAgB,EACjBwE,KAAKiF,MAAMC,aACblF,KAAKiF,MAAMC,YAAYC,aAE3B,EACAhJ,aAAY,WAAG,IAAAiJ,EAAA,KACRpF,KAAK/D,aAA2C,KAA5B+D,KAAK/D,YAAYoJ,QAMtCrF,KAAKsF,aACPC,aAAavF,KAAKsF,aAGpBtF,KAAKsF,YAAcE,YAAW,WAC5B,IAAMC,EAAQL,EAAKnJ,YAAYyJ,cAAcL,OAC7CD,EAAKhJ,cAAgBgJ,EAAK3G,MAAMuD,QAAO,SAAAC,GAErC,OAAQA,EAAK1H,MAAQ0H,EAAK1H,KAAKmL,cAAcpC,SAASmC,IAC/CxD,EAAKzE,YAAcyE,EAAKzE,WAAWkI,cAAcpC,SAASmC,IAC1DxD,EAAKpD,UAAYoD,EAAKpD,SAAS6G,cAAcpC,SAASmC,EAC/D,IAAGrF,MAAM,EAAG,GACd,GAAG,MAjBDJ,KAAK5D,cAAgB,EAkBzB,EACAN,WAAU,WACR,GAAIkE,KAAKN,aAUP,OARAM,KAAKoE,SAASM,QAAQ,UAADtB,OAAWpD,KAAKN,aAAanF,OAClDyF,KAAKxE,eAAgB,EACrBwE,KAAK/D,YAAc,GACnB+D,KAAK5D,cAAgB,GACrB4D,KAAKN,aAAe,UAGpBM,KAAKM,aAIFN,KAAK/D,aAA2C,KAA5B+D,KAAK/D,YAAYoJ,OAKR,IAA9BrF,KAAK5D,cAAcnB,OAMvB+E,KAAKoE,SAASuB,QAAQ,iBALpB3F,KAAKoE,SAASuB,QAAQ,YALtB3F,KAAKoE,SAASuB,QAAQ,UAW1B,EACA/I,oBAAmB,WAAG,IAAAgJ,EAAA,KACpB5F,KAAKiF,MAAMY,kBAAkBC,UAAS,SAAAC,GACpC,IAAIA,EAgBF,OAAO,EAfPH,EAAKjL,SAAU,EAEf2H,EAAAA,WAAI7D,MAAMuH,cAAcJ,EAAKnJ,oBAAoBgD,OAAQmG,EAAKnJ,oBAAoBM,KAAKG,UACpFsF,MAAK,WACJoD,EAAKxB,SAASM,QAAQ,UACtBkB,EAAKnJ,oBAAoBC,SAAU,CACrC,IAAC,UACM,SAAAuE,GACLD,QAAQC,MAAM,UAAWA,GACzB2E,EAAKxB,SAASnD,MAAM,YAAcA,EAAM7B,SAAW,QACrD,IAAC,YACQ,WACPwG,EAAKjL,SAAU,CACjB,GAIN,GACF,EACAsL,WAAU,SAAChE,GAETjC,KAAK1B,SAAW,CACd/D,KAAM0H,EAAK1H,KACXJ,KAAM8H,EAAK9H,KACXqD,WAAYyE,EAAKzE,WACjBqB,SAAUoD,EAAKpD,SACfC,MAAOmD,EAAKnD,MACZC,QAAQ,EACR7B,SAAU,GACVC,gBAAiB,GACjB6B,UAAW,MAIbgB,KAAKrB,YAAa,CACpB,EACApC,eAAc,SAACrC,GAEb8F,KAAKN,aAAexF,EACpB8F,KAAKoE,SAAS8B,KAAK,QAAD9C,OAASlJ,EAAIK,MACjC,EAEAhD,qBAAoB,WAAG,IAAA4O,EAAA,KACrBnG,KAAKrF,SAAU,EAGf2H,EAAAA,WAAIiB,MAAMhB,SACPC,MAAK,SAAAC,GACJ,IAAMc,EAAQd,EAASjJ,MAAQ,GAE/B2M,EAAKnN,YAAcuK,EAAMT,KAAI,SAAAxI,GAAG,OAAKA,EAAKC,IAAI,IAC9C4L,EAAK/I,qBAAqBV,SAAU,EACpCyJ,EAAK/I,qBAAqBL,KAAO,CAC/BS,WAAY,GACZD,OAAQ,KACRI,OAAQ,GAEZ,IAAC,UACM,SAAAsD,GACLD,QAAQC,MAAM,YAAaA,GAC3BkF,EAAK/B,SAASnD,MAAM,cAAgBA,EAAM7B,SAAW,QACvD,IAAC,YACQ,WACP+G,EAAKxL,SAAU,CACjB,GACJ,EAGA+C,uBAAsB,SAAC0I,GAAgB,IAAAC,EAAA,KAErC/D,EAAAA,WAAIiB,MAAMhB,SACPC,MAAK,SAAAC,GACJ,IAAMc,EAAQd,EAASjJ,MAAQ,GACzB8M,EAAe/C,EAAMgD,MAAK,SAAAjM,GAAG,OAAKA,EAAKC,OAAS6L,CAAc,IAChEE,IACFD,EAAKjJ,qBAAqBL,KAAKQ,OAAS+I,EAAa1H,GAEzD,IAAC,UACM,SAAAqC,GACLD,QAAQC,MAAM,YAAaA,EAC7B,GACJ,EAGA3D,qBAAoB,WAAG,IAAAkJ,EAAA,KACrB,GAAKxG,KAAK5C,qBAAqBL,KAAKS,WAApC,CAKAwC,KAAKrF,SAAU,EAGf,IAAM4C,EAASyC,KAAK5C,qBAAqBL,KAAKQ,OACxC6I,EAAiBpG,KAAK5C,qBAAqBL,KAAKS,WAEjDD,EAkCH+E,EAAAA,WAAIiB,MAAMkD,gBACRlJ,EACAyC,KAAK5C,qBAAqBL,KAAKY,QAAU,UAExC6E,MAAK,WACNgE,EAAKpC,SAASM,QAAQ,UAADtB,OAAWgD,EAAc,gBAC5CI,EAAKpJ,qBAAqBV,SAAU,EAEpC8J,EAAKlG,YACP,IAAC,UACM,SAAAW,GAAS,IAAAyF,EACd1F,QAAQC,MAAM,YAAaA,GAC7BuF,EAAKpC,SAASnD,MAAM,eAA8B,QAAdyF,EAAAzF,EAAMwB,gBAAQ,IAAAiE,GAAM,QAANA,EAAdA,EAAgBlN,YAAI,IAAAkN,OAAA,EAApBA,EAAsBtH,UAAW6B,EAAM7B,SAAW,QACtF,IAAC,YACQ,WACPoH,EAAK7L,SAAU,CACjB,IAhDF2H,EAAAA,WAAIiB,MAAMoD,OAAO,CACfpM,KAAM6L,EACNnI,YAAa,GAAFmF,OAAKgD,EAAc,QAE7B5D,MAAK,SAAAC,GACJ,IAAMmE,EAAUnE,EAASjJ,KAEzB,OAAO8I,EAAAA,WAAIiB,MAAMsD,SAASD,EAAQhI,GAAI4H,EAAKzG,YAAYnB,GACzD,IACC4D,MAAK,WAEJ,IAAMsE,GAAOjH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACR2G,EAAKzG,aAAW,IACnBvC,WAAY4I,IAEd,OAAOI,EAAK7F,OAAOoG,SAAS,oBAAqBD,EACnD,IACCtE,MAAK,WACJgE,EAAKpC,SAASM,QAAQ,QAADtB,OAASgD,EAAc,OAC5CI,EAAKpJ,qBAAqBV,SAAU,EAEpC8J,EAAKlG,YACP,IAAC,UACM,SAAAW,GACLD,QAAQC,MAAM,UAAWA,GACzBuF,EAAKpC,SAASnD,MAAM,YAAcA,EAAM7B,SAAW,QACrD,IAAC,YACQ,WACPoH,EAAK7L,SAAU,CACjB,GAvCJ,MAFEqF,KAAKoE,SAASuB,QAAQ,YA8D1B,EAGA5N,uBAAsB,WACpBiI,KAAKnC,uBAAuBnB,SAAU,EACtCsD,KAAKnC,uBAAuBd,KAAO,CACjCxC,KAAM,GACN0D,YAAa,GAEjB,EAGAF,uBAAsB,WAAG,IAAAiJ,EAAA,KACvBhH,KAAKiF,MAAMgC,qBAAqBnB,UAAS,SAAAC,GACvC,IAAIA,EA4CF,OAAO,EA3CPiB,EAAKrM,SAAU,EAGf,IAAMuJ,EAAW,CACf3J,KAAMyM,EAAKnJ,uBAAuBd,KAAKxC,KACvC0D,YAAa+I,EAAKnJ,uBAAuBd,KAAKkB,aAAU,GAAAmF,OAAQ4D,EAAKnJ,uBAAuBd,KAAKxC,KAAI,OAGvG+H,EAAAA,WAAIiB,MAAMoD,OAAOzC,GACd1B,MAAK,SAAAC,GACJ,IAAMmE,EAAUnE,EAASjJ,KAEzB,OAAO8I,EAAAA,WAAIiB,MAAMsD,SAASD,EAAQhI,GAAIoI,EAAKjH,YAAYnB,GACzD,IACC4D,MAAK,WAEJ,IAAMsE,GAAOjH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRmH,EAAKjH,aAAW,IACnBvC,WAAYwJ,EAAKnJ,uBAAuBd,KAAKxC,OAG/C,OAAOyM,EAAKrG,OAAOoG,SAAS,oBAAqBD,EACnD,IACCtE,MAAK,WACJwE,EAAK5C,SAASM,QAAQ,WAADtB,OAAY4D,EAAKnJ,uBAAuBd,KAAKxC,KAAI,OACtEyM,EAAKnJ,uBAAuBnB,SAAU,EAGjCsK,EAAKhO,YAAYsK,SAAS0D,EAAKnJ,uBAAuBd,KAAKxC,OAC9DyM,EAAKhO,YAAYkO,KAAKF,EAAKnJ,uBAAuBd,KAAKxC,MAIzDyM,EAAK1G,YACP,IAAC,UACM,SAAAW,GACLD,QAAQC,MAAM,UAAWA,GACzB+F,EAAK5C,SAASnD,MAAM,YAAcA,EAAM7B,SAAW,QACrD,IAAC,YACQ,WACP4H,EAAKrM,SAAU,CACjB,GAIN,GACF,EAEA4F,0BAAyB,WAAG,IAAA4G,EAAA,KACrBnH,KAAKnI,UAEVmI,KAAKrF,SAAU,EACf2H,EAAAA,WAAI7D,MAAM2I,iCACP5E,MAAK,SAAAC,GACJ0E,EAAKxH,qBAAuB8C,EAASjJ,MAAQ,EAC/C,IAAC,UACM,SAAAyH,GACLD,QAAQC,MAAM,cAAeA,GAC7BkG,EAAK/C,SAASnD,MAAM,gBAAkBA,EAAM7B,SAAW,QACzD,IAAC,YACQ,WACP+H,EAAKxM,SAAU,CACjB,IACJ,EAGA3C,yBAAwB,WAEtBgI,KAAKqH,QAAQH,KAAK,+BACpB,EAGAI,uBAAsB,SAAC7H,GACrB,OAAOO,KAAKL,qBAAqB4H,MAAK,SAAAC,GAAE,OAAKA,EAAI/H,SAAWA,CAAM,GACpE,EAGAgI,kBAAiB,SAACxF,GAAM,IAAAyF,EAAA,KACtB1H,KAAK2H,SAAS,OAADvE,OAAQnB,EAAK1H,KAAI,iBAAiB,KAAM,CACnDqN,kBAAmB,KACnBC,iBAAkB,KAClBxQ,KAAM,YACLmL,MAAK,WAENkF,EAAKI,QAAQ,cAAe,SAAU,CACpCF,kBAAmB,KACnBC,iBAAkB,KAClBE,iBAAkB,YACjBvF,MAAK,SAAAwF,GAAe,IAAZ7O,EAAI6O,EAAJ7O,MACTuO,EAAK/M,SAAU,EAGf2H,EAAAA,WAAI7D,MAAMwJ,2BAA2BhG,EAAKrD,GAAI,CAC5CsJ,UAAU,EACVC,QAAShP,GAAS,UAEjBqJ,MAAK,WACJkF,EAAKtD,SAASM,QAAQ,QAADtB,OAASnB,EAAK1H,KAAI,aAEvCmN,EAAKpH,aACLoH,EAAKnH,2BACP,IAAC,UACM,SAAAU,GAAS,IAAAmH,EACdpH,QAAQC,MAAM,UAAWA,GACzByG,EAAKtD,SAASnD,MAAM,aAA4B,QAAdmH,EAAAnH,EAAMwB,gBAAQ,IAAA2F,GAAM,QAANA,EAAdA,EAAgB5O,YAAI,IAAA4O,OAAA,EAApBA,EAAsBhJ,UAAW6B,EAAM7B,SAAW,QACtF,IAAC,YACQ,WACPsI,EAAK/M,SAAU,CACjB,GACJ,IAAE,UAAO,WACP,GAEJ,IAAE,UAAO,WACP,GAEJ,I,eCn+BJ,MAAM0N,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,G", "sources": ["webpack://medical-annotation-frontend/./src/views/Users.vue", "webpack://medical-annotation-frontend/./src/views/Users.vue?d380"], "sourcesContent": ["<template>\n  <div class=\"users-container\">\n    <div class=\"page-header\">\n      <h2>人员管理</h2>\n      <div class=\"header-buttons\">\n        <!-- 当用户还没有部门的时候，始终都有一个加入部门的按钮 -->\n        <el-button \n          v-if=\"!currentUserHasDepartment\" \n          type=\"primary\" \n          @click=\"handleJoinDepartment\"\n        >\n          加入部门\n        </el-button>\n        \n        <!-- 当用户还没有部门的时候且用户为管理员或者审核医生的时候，始终有一个创建部门的按钮 -->\n        <el-button \n          v-if=\"!currentUserHasDepartment && (isAdmin || isReviewer)\" \n          type=\"success\" \n          @click=\"handleCreateDepartment\"\n        >\n          创建部门\n        </el-button>\n        \n        <!-- 添加查看权限申请的按钮，仅管理员可见 -->\n        <el-button \n          v-if=\"isAdmin\" \n          type=\"warning\" \n          @click=\"viewReviewerApplications\"\n        >\n          查看权限申请\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 部门筛选器 -->\n    <div class=\"filter-bar\">\n      <el-form :inline=\"true\" class=\"filter-form\">\n        <el-form-item label=\"所属部门\">\n          <el-select v-model=\"departmentFilter\" placeholder=\"全部部门\" clearable>\n            <el-option v-for=\"dept in departments\" :key=\"dept\" :label=\"dept\" :value=\"dept\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"成员角色\">\n          <el-select v-model=\"roleFilter\" placeholder=\"全部角色\" clearable>\n            <el-option label=\"管理员\" value=\"ADMIN\"></el-option>\n            <el-option label=\"标注医生\" value=\"DOCTOR\"></el-option>\n            <el-option label=\"审核医生\" value=\"REVIEWER\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"applyFilters\">筛选</el-button>\n          <el-button @click=\"resetFilters\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <el-table v-loading=\"loading\" :data=\"displayUsers\" style=\"width: 100%\">\n      <el-table-column prop=\"name\" label=\"姓名\" width=\"120\" />\n      <el-table-column prop=\"department\" label=\"所属部门\" width=\"150\" />\n      <el-table-column prop=\"role\" label=\"角色\">\n        <template #default=\"scope\">\n          <el-tag :type=\"getRoleType(scope.row.role)\">\n            {{ getRoleName(scope.row.role) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"hospital\" label=\"所属医院\" />\n      <el-table-column prop=\"team\" label=\"所属团队\" width=\"180\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.team && scope.row.team.name\">{{ scope.row.team.name }}</span>\n          <span v-else-if=\"scope.row.team && typeof scope.row.team === 'string'\">{{ scope.row.team }}</span>\n          <span v-else>未加入团队</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <el-pagination\n      background\n      layout=\"total, sizes, prev, pager, next\"\n      :total=\"filteredUsers.length\"\n      :page-size=\"pageSize\"\n      :page-sizes=\"[10, 20, 50, 100]\"\n      class=\"pagination\"\n      @current-change=\"handlePageChange\"\n      @size-change=\"handleSizeChange\"\n    />\n\n    <!-- 用户表单对话框 -->\n    <el-dialog\n      v-model=\"dialogVisible\"\n      title=\"添加成员\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      @close=\"closeDialog\"\n    >\n      <!-- 搜索框 -->\n      <div class=\"search-wrapper\">\n        <el-input\n          v-model=\"searchQuery\"\n          placeholder=\"搜索用户名、姓名、部门、医院\"\n          clearable\n          @input=\"handleSearch\"\n        ></el-input>\n      </div>\n      \n      <!-- 搜索结果 -->\n      <div v-if=\"searchResults.length > 0\" class=\"search-results\">\n        <el-table \n          :data=\"searchResults\" \n          style=\"width: 100%\" \n          highlight-current-row\n          @row-click=\"handleRowClick\"\n        >\n          <el-table-column prop=\"name\" label=\"姓名\" width=\"120\"></el-table-column>\n          <el-table-column prop=\"department\" label=\"部门\" width=\"140\"></el-table-column>\n          <el-table-column prop=\"hospital\" label=\"所属医院\"></el-table-column>\n        </el-table>\n      </div>\n      \n      <!-- 水平分割线 -->\n      <el-divider v-if=\"searchResults.length > 0\"></el-divider>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 重置密码对话框 -->\n    <el-dialog\n      v-model=\"resetPasswordDialog.visible\"\n      title=\"重置密码\"\n      width=\"400px\"\n      @close=\"resetPasswordDialog.visible = false\"\n    >\n      <el-form\n        ref=\"resetPasswordForm\"\n        :model=\"resetPasswordDialog.form\"\n        :rules=\"resetPasswordRules\"\n        label-width=\"100px\"\n      >\n        <el-form-item label=\"新密码\" prop=\"password\">\n          <el-input\n            v-model=\"resetPasswordDialog.form.password\"\n            type=\"password\"\n            placeholder=\"请输入新密码\"\n            show-password\n          ></el-input>\n        </el-form-item>\n        <el-form-item label=\"确认新密码\" prop=\"confirmPassword\">\n          <el-input\n            v-model=\"resetPasswordDialog.form.confirmPassword\"\n            type=\"password\"\n            placeholder=\"请确认新密码\"\n            show-password\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"resetPasswordDialog.visible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitResetPassword\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 加入部门对话框 -->\n    <el-dialog\n      v-model=\"joinDepartmentDialog.visible\"\n      title=\"加入部门\"\n      width=\"500px\"\n      @close=\"joinDepartmentDialog.visible = false\"\n    >\n      <el-form :model=\"joinDepartmentDialog.form\" label-width=\"80px\">\n        <el-form-item label=\"部门\">\n          <el-select \n            v-model=\"joinDepartmentDialog.form.department\" \n            placeholder=\"请选择部门\" \n            style=\"width: 100%\"\n            @change=\"handleDepartmentChange\"\n          >\n            <el-option v-for=\"dept in departments\" :key=\"dept\" :label=\"dept\" :value=\"dept\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item v-if=\"joinDepartmentDialog.form.teamId\" label=\"申请理由\">\n          <el-input \n            v-model=\"joinDepartmentDialog.form.reason\" \n            type=\"textarea\" \n            placeholder=\"请简要说明加入部门的原因（可选）\"\n            :rows=\"3\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"joinDepartmentDialog.visible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitJoinDepartment\">\n            {{ joinDepartmentDialog.form.teamId ? '申请加入' : '确定' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 创建部门对话框 -->\n    <el-dialog\n      v-model=\"createDepartmentDialog.visible\"\n      title=\"创建部门\"\n      width=\"500px\"\n      @close=\"createDepartmentDialog.visible = false\"\n    >\n      <el-form ref=\"createDepartmentForm\" :model=\"createDepartmentDialog.form\" :rules=\"departmentRules\" label-width=\"80px\">\n        <el-form-item label=\"部门名称\" prop=\"name\">\n          <el-input v-model=\"createDepartmentDialog.form.name\" placeholder=\"请输入部门名称\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"描述\" prop=\"description\">\n          <el-input \n            v-model=\"createDepartmentDialog.form.description\" \n            type=\"textarea\" \n            placeholder=\"请输入部门描述\"\n            :rows=\"3\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"createDepartmentDialog.visible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitCreateDepartment\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport api from '@/utils/api'\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'Users',\n  data() {\n    // 验证密码确认是否一致\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.userForm.password) {\n        callback(new Error('两次输入密码不一致!'))\n      } else {\n        callback()\n      }\n    }\n    \n    // 验证重置密码确认是否一致\n    const validateResetConfirmPassword = (rule, value, callback) => {\n      if (value !== this.resetPasswordDialog.form.password) {\n        callback(new Error('两次输入密码不一致!'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      loading: false,\n      users: [],\n      filteredUsers: [],\n      departments: [],\n      departmentFilter: '',\n      roleFilter: '',\n      currentPage: 1,\n      pageSize: 10,\n      dialogVisible: false,\n      searchMode: true,\n      searchQuery: '',\n      searchResults: [],\n      userForm: {\n        id: null,\n        name: '',\n        role: 'DOCTOR', // 默认为标注医生\n        department: '',\n        hospital: '',\n        email: '',\n        password: '',\n        confirmPassword: '',\n        active: true,\n        createdAt: null\n      },\n      userRules: {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' },\n          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\n        ],\n        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],\n        role: [{ required: true, message: '请选择角色', trigger: 'change' }],\n        department: [{ required: true, message: '请选择或创建部门', trigger: 'change' }],\n        hospital: [{ required: true, message: '请输入所属医院', trigger: 'blur' }],\n        email: [\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\n          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      },\n      resetPasswordDialog: {\n        visible: false,\n        userId: null,\n        form: {\n          password: '',\n          confirmPassword: ''\n        }\n      },\n      resetPasswordRules: {\n        password: [\n          { required: true, message: '请输入新密码', trigger: 'blur' },\n          { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认新密码', trigger: 'blur' },\n          { validator: validateResetConfirmPassword, trigger: 'blur' }\n        ]\n      },\n      selectedUser: null,\n      // 新增加入部门对话框数据\n      joinDepartmentDialog: {\n        visible: false,\n        form: {\n          department: '',\n          teamId: null,\n          reason: ''\n        }\n      },\n      // 新增创建部门对话框数据\n      createDepartmentDialog: {\n        visible: false,\n        form: {\n          name: '',\n          description: ''\n        }\n      },\n      // 部门表单验证规则\n      departmentRules: {\n        name: [\n          { required: true, message: '请输入部门名称', trigger: 'blur' },\n          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }\n        ]\n      },\n      reviewerApplications: [], // 存储权限升级申请\n    }\n  },\n  computed: {\n    ...mapGetters({\n      currentUser: 'getUser'\n    }),\n    // 当前用户是否有部门\n    currentUserHasDepartment() {\n      return this.currentUser && this.currentUser.department;\n    },\n    // 当前用户是否为管理员\n    isAdmin() {\n      return this.currentUser && this.currentUser.role === 'ADMIN';\n    },\n    // 当前用户是否为标注医生\n    isDoctor() {\n      return this.currentUser && this.currentUser.role === 'DOCTOR';\n    },\n    // 当前用户是否为审核医生\n    isReviewer() {\n      return this.currentUser && this.currentUser.role === 'REVIEWER';\n    },\n    // 计算当前页的用户数据，实现客户端分页\n    displayUsers() {\n      const start = (this.currentPage - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      return this.filteredUsers.slice(start, end);\n    }\n  },\n  created() {\n    // 初始化时加载用户数据\n    this.fetchUsers();\n    \n    // 如果是管理员，加载权限升级申请\n    if (this.isAdmin) {\n      this.fetchReviewerApplications();\n    }\n    \n    // 如果Vuex中没有用户信息，尝试从localStorage恢复\n    if (!this.currentUser) {\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          this.$store.commit('setUser', JSON.parse(storedUser));\n        } catch (e) {\n          console.error('解析用户数据失败', e);\n        }\n      }\n    }\n  },\n  methods: {\n    getRoleType(role) {\n      const types = {\n        'ADMIN': 'danger',\n        'DOCTOR': 'primary',\n        'REVIEWER': 'success'\n      }\n      return types[role] || 'info'\n    },\n    getRoleName(role) {\n      const names = {\n        'ADMIN': '管理员',\n        'DOCTOR': '标注医生',\n        'REVIEWER': '审核医生'\n      }\n      return names[role] || '未知'\n    },\n    formatDate(dateString) {\n      if (!dateString) return '未知';\n      const date = new Date(dateString);\n      return date.toLocaleString('zh-CN');\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n    },\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1;\n    },\n    applyFilters() {\n      this.filterUsers();\n      this.currentPage = 1; // 筛选后重置页码\n    },\n    resetFilters() {\n      this.departmentFilter = '';\n      this.roleFilter = '';\n      this.filterUsers();\n      this.currentPage = 1;\n    },\n    filterUsers() {\n      if (!this.departmentFilter && !this.roleFilter) {\n        this.filteredUsers = Array.isArray(this.users) ? [...this.users] : [];\n        return;\n      }\n      \n      if (!Array.isArray(this.users)) {\n        console.error('users不是数组，无法筛选', this.users);\n        this.filteredUsers = [];\n        return;\n      }\n      \n      this.filteredUsers = this.users.filter(user => {\n        if (!user) return false;\n        const matchDepartment = !this.departmentFilter || \n          (user.department && user.department === this.departmentFilter);\n        const matchRole = !this.roleFilter || \n          (user.role && user.role === this.roleFilter);\n        return matchDepartment && matchRole;\n      });\n    },\n    fetchUsers() {\n      this.loading = true;\n      \n      // 使用API调用获取用户列表\n      api.users.getAll()\n        .then(response => {\n          // 确保response.data是数组\n          if (response && response.data) {\n            // 如果返回的是对象而不是数组，尝试获取对象中的数组属性\n            if (Array.isArray(response.data)) {\n              this.users = response.data;\n            } else if (response.data.content && Array.isArray(response.data.content)) {\n              // 有些后端API会返回{content: [...]}格式\n              this.users = response.data.content;\n            } else if (response.data.data && Array.isArray(response.data.data)) {\n              // 有些后端API会返回{data: [...]}格式\n              this.users = response.data.data;\n            } else if (response.data.items && Array.isArray(response.data.items)) {\n              // 有些后端API会返回{items: [...]}格式\n              this.users = response.data.items;\n            } else if (response.data.users && Array.isArray(response.data.users)) {\n              // 有些后端API会返回{users: [...]}格式\n              this.users = response.data.users;\n            } else if (typeof response.data === 'object') {\n              // 如果是其他对象格式，记录错误并尝试转换\n              console.warn('API返回了对象而不是数组:', response.data);\n              this.users = [];\n            } else {\n              // 不是数组也不是对象，设为空数组\n              console.error('API返回了非预期格式:', response.data);\n              this.users = [];\n            }\n          } else {\n            // response或response.data为空时设为空数组\n            this.users = [];\n          }\n          \n          // 处理用户数据，确保团队信息正确\n          this.users = this.users.map(user => {\n            // 如果用户没有team属性或team为null\n            if (!user.team) {\n              user.team = { name: '未加入团队' };\n            }\n            // 如果team是字符串，转换为对象\n            else if (typeof user.team === 'string') {\n              user.team = { name: user.team };\n            }\n            // 如果team是对象但没有name属性\n            else if (typeof user.team === 'object' && !user.team.name) {\n              user.team.name = '未知团队';\n            }\n            return user;\n          });\n          \n          this.filteredUsers = [...this.users];\n          \n          // 高效地提取和去重部门\n          const uniqueDepts = new Set();\n          this.users.forEach(user => {\n            if (user && user.department) {\n              uniqueDepts.add(user.department);\n            }\n          });\n          \n          // 标准部门列表\n          const standardDepts = ['内科', '外科', '影像科', '放射科', '急诊科', '脑科', '管理部'];\n          \n          // 合并标准部门和从用户数据中提取的部门\n          this.departments = [\n            ...standardDepts,\n            ...Array.from(uniqueDepts).filter(dept => !standardDepts.includes(dept))\n          ];\n          \n          // 获取所有团队信息\n          return api.teams.getAll();\n        })\n        .then(teamsResponse => {\n          if (teamsResponse && teamsResponse.data) {\n            const teams = teamsResponse.data;\n            \n            // 为每个团队获取成员信息\n            const teamPromises = teams.map(team => \n              api.teams.getTeamMembers(team.id)\n                .then(membersResponse => {\n                  const members = membersResponse.data || [];\n                  return { teamId: team.id, teamName: team.name, members };\n                })\n                .catch(error => {\n                  console.error(`获取团队 ${team.id} 成员失败:`, error);\n                  return { teamId: team.id, teamName: team.name, members: [] };\n                })\n            );\n            \n            return Promise.all(teamPromises);\n          }\n          return [];\n        })\n        .then(teamsWithMembers => {\n          // 更新用户的团队信息\n          if (teamsWithMembers && teamsWithMembers.length > 0) {\n            // 创建用户ID到团队的映射\n            const userTeamMap = {};\n            \n            // 遍历所有团队及其成员\n            teamsWithMembers.forEach(teamData => {\n              const { teamId, teamName, members } = teamData;\n              \n              // 为每个成员记录团队信息\n              members.forEach(member => {\n                if (member && member.id) {\n                  userTeamMap[member.id] = { id: teamId, name: teamName };\n                }\n              });\n            });\n            \n            // 更新用户的团队信息\n            this.users = this.users.map(user => {\n              if (user && user.id && userTeamMap[user.id]) {\n                user.team = userTeamMap[user.id];\n              }\n              return user;\n            });\n            \n            // 更新过滤后的用户列表\n            this.filteredUsers = [...this.users];\n          }\n        })\n        .catch(error => {\n          console.error('获取用户列表失败:', error);\n          this.$message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n          // 错误时设置为空数组\n          this.users = [];\n          this.filteredUsers = [];\n        })\n        .finally(() => {\n          this.loading = false;\n        });\n    },\n    handleAddUser() {\n      this.userForm = {\n        id: null,\n        name: '',\n        role: 'DOCTOR', // 默认为标注医生\n        department: '',\n        hospital: '',\n        email: '',\n        password: '',\n        confirmPassword: '',\n        active: true,\n        createdAt: null\n      };\n      this.searchMode = true;\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.dialogVisible = true;\n    },\n    handleEditUser(row) {\n      this.userForm = {\n        id: row.id,\n        name: row.name,\n        role: row.role,\n        department: row.department,\n        hospital: row.hospital,\n        email: row.email,\n        active: row.active !== false, // 确保 active 有默认值\n        createdAt: row.createdAt\n      };\n      this.dialogVisible = true;\n    },\n    handleDeleteUser(row) {\n      this.loading = true;\n      \n      api.users.deleteUser(row.id)\n        .then(() => {\n          this.$message.success(`成员 ${row.name} 已删除`);\n          this.fetchUsers();\n        })\n        .catch(error => {\n          console.error('删除用户失败:', error);\n          this.$message.error('删除失败: ' + (error.message || '未知错误'));\n        })\n        .finally(() => {\n          this.loading = false;\n        });\n    },\n    handleStatusChange(row, status) {\n      const updatedUser = { ...row, active: status };\n      \n      api.users.updateUser(row.id, updatedUser)\n        .then(() => {\n          this.$message.success(`已${status ? '启用' : '禁用'}成员: ${row.name}`);\n        })\n        .catch(error => {\n          console.error('更新用户状态失败:', error);\n          this.$message.error('更新状态失败: ' + (error.message || '未知错误'));\n          row.active = !status; // 恢复原状态\n        });\n    },\n    handleResetPassword(row) {\n      this.resetPasswordDialog.visible = true;\n      this.resetPasswordDialog.userId = row.id;\n      this.resetPasswordDialog.form = {\n        password: '',\n        confirmPassword: ''\n      };\n    },\n    closeDialog() {\n      this.dialogVisible = false;\n      if (this.$refs.userFormRef) {\n        this.$refs.userFormRef.resetFields();\n      }\n    },\n    handleSearch() {\n      if (!this.searchQuery || this.searchQuery.trim() === '') {\n        this.searchResults = [];\n        return;\n      }\n      \n      // 防抖处理，避免频繁搜索\n      if (this.searchTimer) {\n        clearTimeout(this.searchTimer);\n      }\n      \n      this.searchTimer = setTimeout(() => {\n        const query = this.searchQuery.toLowerCase().trim();\n        this.searchResults = this.users.filter(user => {\n          // 检查姓名、部门、医院是否包含查询文字\n          return (user.name && user.name.toLowerCase().includes(query)) ||\n                (user.department && user.department.toLowerCase().includes(query)) ||\n                (user.hospital && user.hospital.toLowerCase().includes(query));\n        }).slice(0, 10); // 限制搜索结果数量，提高性能\n      }, 300);\n    },\n    submitForm() {\n      if (this.selectedUser) {\n        // 如果选择了用户，将其添加到表中或进行其他操作\n        this.$message.success(`已添加成员: ${this.selectedUser.name}`);\n        this.dialogVisible = false;\n        this.searchQuery = '';\n        this.searchResults = [];\n        this.selectedUser = null;\n        \n        // 刷新成员列表\n        this.fetchUsers();\n        return;\n      }\n      \n      if (!this.searchQuery || this.searchQuery.trim() === '') {\n        this.$message.warning('请输入搜索内容');\n        return;\n      }\n      \n      if (this.searchResults.length === 0) {\n        this.$message.warning('未找到匹配的成员');\n        return;\n      }\n      \n      // 如果有搜索结果但没有选择，提示用户选择\n      this.$message.warning('请从搜索结果中选择一个成员');\n    },\n    submitResetPassword() {\n      this.$refs.resetPasswordForm.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          \n          api.users.resetPassword(this.resetPasswordDialog.userId, this.resetPasswordDialog.form.password)\n            .then(() => {\n              this.$message.success('密码重置成功');\n              this.resetPasswordDialog.visible = false;\n            })\n            .catch(error => {\n              console.error('密码重置失败:', error);\n              this.$message.error('密码重置失败: ' + (error.message || '未知错误'));\n            })\n            .finally(() => {\n              this.loading = false;\n            });\n        } else {\n          return false;\n        }\n      })\n    },\n    selectUser(user) {\n      // 填充表单数据，但不复制ID和密码相关字段\n      this.userForm = {\n        name: user.name,\n        role: user.role,\n        department: user.department,\n        hospital: user.hospital,\n        email: user.email,\n        active: true,\n        password: '',\n        confirmPassword: '',\n        createdAt: null\n      };\n      \n      // 关闭搜索模式\n      this.searchMode = false;\n    },\n    handleRowClick(row) {\n      // 保存选中的用户\n      this.selectedUser = row;\n      this.$message.info(`已选择: ${row.name}`);\n    },\n    // 处理加入部门按钮点击\n    handleJoinDepartment() {\n      this.loading = true;\n      \n      // 获取所有团队/部门\n      api.teams.getAll()\n        .then(response => {\n          const teams = response.data || [];\n          // 从团队数据中提取部门名称\n          this.departments = teams.map(team => team.name);\n          this.joinDepartmentDialog.visible = true;\n          this.joinDepartmentDialog.form = {\n            department: '',\n            teamId: null,\n            reason: ''\n          };\n        })\n        .catch(error => {\n          console.error('获取部门列表失败:', error);\n          this.$message.error('获取部门列表失败: ' + (error.message || '未知错误'));\n        })\n        .finally(() => {\n          this.loading = false;\n        });\n    },\n    \n    // 处理部门选择变化\n    handleDepartmentChange(departmentName) {\n      // 根据部门名称查找对应的团队ID\n      api.teams.getAll()\n        .then(response => {\n          const teams = response.data || [];\n          const selectedTeam = teams.find(team => team.name === departmentName);\n          if (selectedTeam) {\n            this.joinDepartmentDialog.form.teamId = selectedTeam.id;\n          }\n        })\n        .catch(error => {\n          console.error('获取团队ID失败:', error);\n        });\n    },\n    \n    // 提交加入部门请求\n    submitJoinDepartment() {\n      if (!this.joinDepartmentDialog.form.department) {\n        this.$message.warning('请选择要加入的部门');\n        return;\n      }\n      \n      this.loading = true;\n      \n      // 获取选中的团队ID\n      const teamId = this.joinDepartmentDialog.form.teamId;\n      const departmentName = this.joinDepartmentDialog.form.department;\n      \n      if (!teamId) {\n        // 如果没有找到对应的团队ID，使用部门名称创建新团队\n        api.teams.create({\n          name: departmentName,\n          description: `${departmentName}部门`\n        })\n          .then(response => {\n            const newTeam = response.data;\n            // 加入新创建的团队\n            return api.teams.joinTeam(newTeam.id, this.currentUser.id);\n          })\n          .then(() => {\n            // 更新用户信息中的部门字段\n            const userData = {\n              ...this.currentUser,\n              department: departmentName\n            };\n            return this.$store.dispatch('updateUserProfile', userData);\n          })\n          .then(() => {\n            this.$message.success(`已成功加入${departmentName}部门`);\n            this.joinDepartmentDialog.visible = false;\n            // 刷新用户列表\n            this.fetchUsers();\n          })\n          .catch(error => {\n            console.error('加入部门失败:', error);\n            this.$message.error('加入部门失败: ' + (error.message || '未知错误'));\n          })\n          .finally(() => {\n            this.loading = false;\n          });\n      } else {\n        // 如果找到了团队ID，直接申请加入\n        api.teams.applyToJoinTeam(\n          teamId, \n          this.joinDepartmentDialog.form.reason || '申请加入部门'\n        )\n          .then(() => {\n          this.$message.success(`已成功提交加入${departmentName}申请，请等待管理员审核`);\n            this.joinDepartmentDialog.visible = false;\n            // 刷新用户列表\n            this.fetchUsers();\n          })\n          .catch(error => {\n            console.error('申请加入部门失败:', error);\n          this.$message.error('申请加入部门失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n          })\n          .finally(() => {\n            this.loading = false;\n          });\n      }\n    },\n    \n    // 处理创建部门按钮点击\n    handleCreateDepartment() {\n      this.createDepartmentDialog.visible = true;\n      this.createDepartmentDialog.form = {\n        name: '',\n        description: ''\n      };\n    },\n    \n    // 提交创建部门请求\n    submitCreateDepartment() {\n      this.$refs.createDepartmentForm.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          \n          // 创建新部门/团队\n          const teamData = {\n            name: this.createDepartmentDialog.form.name,\n            description: this.createDepartmentDialog.form.description || `${this.createDepartmentDialog.form.name}部门`\n          };\n          \n          api.teams.create(teamData)\n            .then(response => {\n              const newTeam = response.data;\n              // 将当前用户加入到新创建的团队\n              return api.teams.joinTeam(newTeam.id, this.currentUser.id);\n            })\n            .then(() => {\n              // 更新当前用户信息，将其部门设置为新创建的部门\n              const userData = {\n                ...this.currentUser,\n                department: this.createDepartmentDialog.form.name\n              };\n              \n              return this.$store.dispatch('updateUserProfile', userData);\n            })\n            .then(() => {\n              this.$message.success(`已成功创建并加入${this.createDepartmentDialog.form.name}部门`);\n              this.createDepartmentDialog.visible = false;\n              \n              // 添加新部门到部门列表\n              if (!this.departments.includes(this.createDepartmentDialog.form.name)) {\n                this.departments.push(this.createDepartmentDialog.form.name);\n              }\n              \n              // 刷新用户列表\n              this.fetchUsers();\n            })\n            .catch(error => {\n              console.error('创建部门失败:', error);\n              this.$message.error('创建部门失败: ' + (error.message || '未知错误'));\n            })\n            .finally(() => {\n              this.loading = false;\n            });\n        } else {\n          return false;\n        }\n      });\n    },\n    // 获取权限升级申请\n    fetchReviewerApplications() {\n      if (!this.isAdmin) return;\n      \n      this.loading = true;\n      api.users.getPendingReviewerApplications()\n        .then(response => {\n          this.reviewerApplications = response.data || [];\n        })\n        .catch(error => {\n          console.error('获取权限升级申请失败:', error);\n          this.$message.error('获取权限升级申请失败: ' + (error.message || '未知错误'));\n        })\n        .finally(() => {\n          this.loading = false;\n        });\n    },\n    \n    // 添加查看权限申请的按钮点击\n    viewReviewerApplications() {\n      // 跳转到审核医生申请页面\n      this.$router.push('/admin/reviewer-applications');\n    },\n    \n    // 检查用户是否有待处理的权限升级申请\n    hasReviewerApplication(userId) {\n      return this.reviewerApplications.some(app => app.userId === userId);\n    },\n    \n    // 处理升级权限\n    handleUpgradeRole(user) {\n      this.$confirm(`确定将 ${user.name} 的角色升级为审核医生吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 显示备注输入框\n        this.$prompt('请输入批准备注（可选）', '批准权限升级', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          inputPlaceholder: '请输入备注信息'\n        }).then(({ value }) => {\n          this.loading = true;\n          \n          // 调用API处理权限升级\n          api.users.processReviewerApplication(user.id, {\n            approved: true,\n            remarks: value || '管理员批准'\n          })\n            .then(() => {\n              this.$message.success(`已成功将 ${user.name} 升级为审核医生`);\n              // 刷新用户列表和申请列表\n              this.fetchUsers();\n              this.fetchReviewerApplications();\n            })\n            .catch(error => {\n              console.error('升级权限失败:', error);\n              this.$message.error('升级权限失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n            })\n            .finally(() => {\n              this.loading = false;\n            });\n        }).catch(() => {\n          // 用户取消输入备注，不做处理\n        });\n      }).catch(() => {\n        // 用户取消操作，不做处理\n      });\n    },\n  }\n}\n</script>\n\n<style>\n.users-container {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  font-size: 20px;\n  color: #333;\n}\n\n.header-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.filter-bar {\n  background-color: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-bottom: 20px;\n}\n\n.filter-form .el-form-item {\n  margin-bottom: 0;\n  margin-right: 15px;\n}\n\n.pagination {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.required-field {\n  color: #F56C6C;\n  position: absolute;\n  left: -10px;\n  top: 5px;\n}\n\n.search-wrapper {\n  padding: 10px 0;\n}\n\n.search-results {\n  margin-top: 10px;\n  max-height: 250px;\n  overflow-y: auto;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n}\n\n:deep(.el-dialog__body) {\n  padding: 10px 20px;\n}\n\n:deep(.el-dialog__header) {\n  padding: 15px 20px;\n  border-bottom: 1px solid #eee;\n}\n\n:deep(.el-dialog__footer) {\n  padding: 10px 20px;\n  border-top: 1px solid #eee;\n  display: flex;\n  justify-content: center;\n}\n\n:deep(.el-table--border) {\n  border-radius: 4px;\n}\n\n:deep(.el-table__row) {\n  cursor: pointer;\n}\n\n:deep(.el-table__row:hover) {\n  background-color: #f5f7fa;\n}\n\n.full-width {\n  width: 100%;\n}\n\n.form-content {\n  margin-top: 15px;\n}\n\n.form-item {\n  position: relative;\n  margin-bottom: 20px;\n  display: flex;\n  align-items: center;\n}\n\n.label {\n  width: 80px;\n  text-align: right;\n  margin-right: 10px;\n  font-size: 14px;\n}\n\n.status-item .status-text {\n  margin-left: 10px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.hint {\n  font-size: 12px;\n  color: #999;\n  margin-top: 4px;\n}\n</style> ", "import { render } from \"./Users.vue?vue&type=template&id=0bc24689\"\nimport script from \"./Users.vue?vue&type=script&lang=js\"\nexport * from \"./Users.vue?vue&type=script&lang=js\"\n\nimport \"./Users.vue?vue&type=style&index=0&id=0bc24689&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "$options", "currentUserHasDepartment", "_createCommentVNode", "_createBlock", "_component_el_button", "type", "onClick", "handleJoinDepartment", "_withCtx", "_cache", "_createTextVNode", "_", "__", "isAdmin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleCreateDepartment", "viewReviewerApplications", "_hoisted_4", "_createVNode", "_component_el_form", "inline", "_component_el_form_item", "label", "_component_el_select", "modelValue", "$data", "<PERSON><PERSON><PERSON>er", "$event", "placeholder", "clearable", "_Fragment", "_renderList", "departments", "dept", "_component_el_option", "value", "<PERSON><PERSON><PERSON>er", "applyFilters", "resetFilters", "_component_el_table", "data", "displayUsers", "style", "_component_el_table_column", "prop", "width", "default", "scope", "_component_el_tag", "getRoleType", "row", "role", "_toDisplayString", "getRoleName", "team", "name", "_hoisted_5", "_hoisted_6", "_hoisted_7", "loading", "_component_el_pagination", "background", "layout", "total", "filteredUsers", "length", "pageSize", "onCurrentChange", "handlePageChange", "onSizeChange", "handleSizeChange", "_component_el_dialog", "dialogVisible", "title", "onClose", "closeDialog", "footer", "_hoisted_10", "submitForm", "_hoisted_8", "_component_el_input", "searchQuery", "onInput", "handleSearch", "searchResults", "_hoisted_9", "onRowClick", "handleRowClick", "_component_el_divider", "resetPasswordDialog", "visible", "_hoisted_11", "submitResetPassword", "ref", "model", "form", "rules", "resetPasswordRules", "password", "confirmPassword", "joinDepartmentDialog", "_hoisted_12", "submitJoinDepartment", "teamId", "department", "onChange", "handleDepartmentChange", "reason", "rows", "createDepartmentDialog", "_hoisted_13", "submitCreateDepartment", "departmentRules", "description", "_this", "validateConfirmPassword", "rule", "callback", "userForm", "Error", "validateResetConfirmPassword", "users", "currentPage", "searchMode", "id", "hospital", "email", "active", "createdAt", "userRules", "username", "required", "message", "trigger", "min", "max", "validator", "userId", "selected<PERSON>ser", "reviewerApplications", "computed", "_objectSpread", "mapGetters", "currentUser", "this", "isDoctor", "start", "end", "slice", "created", "fetchUsers", "fetchReviewerApplications", "storedUser", "localStorage", "getItem", "$store", "commit", "JSON", "parse", "e", "console", "error", "methods", "types", "names", "formatDate", "dateString", "date", "Date", "toLocaleString", "page", "size", "filterUsers", "_this2", "Array", "isArray", "filter", "user", "matchDepartment", "matchRole", "_toConsumableArray", "_this3", "api", "getAll", "then", "response", "content", "items", "_typeof", "warn", "map", "uniqueDepts", "Set", "for<PERSON>ach", "add", "standardDepts", "concat", "from", "includes", "teams", "teamsResponse", "teamPromises", "getTeamMembers", "membersResponse", "members", "teamName", "Promise", "all", "teamsWithMembers", "userTeamMap", "teamData", "member", "$message", "handleAddUser", "handleEditUser", "handleDeleteUser", "_this4", "deleteUser", "success", "handleStatusChange", "status", "_this5", "updatedUser", "updateUser", "handleResetPassword", "$refs", "userFormRef", "resetFields", "_this6", "trim", "searchTimer", "clearTimeout", "setTimeout", "query", "toLowerCase", "warning", "_this7", "resetPasswordForm", "validate", "valid", "resetPassword", "selectUser", "info", "_this8", "departmentName", "_this9", "selectedTeam", "find", "_this0", "applyToJoinTeam", "_error$response", "create", "newTeam", "joinTeam", "userData", "dispatch", "_this1", "createDepartmentForm", "push", "_this10", "getPendingReviewerApplications", "$router", "hasReviewerApplication", "some", "app", "handleUpgradeRole", "_this11", "$confirm", "confirmButtonText", "cancelButtonText", "$prompt", "inputPlaceholder", "_ref", "processReviewerApplication", "approved", "remarks", "_error$response2", "__exports__", "render"], "sourceRoot": ""}