import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import auth from './modules/auth'
import images from './modules/images'
import users from './modules/users'
import stats from './modules/stats'
import annotation from './modules/annotation'
import teamApplications from './modules/teamApplications' // 添加团队申请模块
import { storageUtils } from '@/utils/storeHelpers'

// 创建空的状态对象，所有功能都已移至相应的模块
export default createStore({
  state: {
    // 已移至annotation模块
  },
  getters: {
    isAuthenticated: state => state.auth.isAuthenticated,
  },
  mutations: {
    // 为了保持向后兼容性，提供与旧代码相同的接口，但实际委托给annotation模块
    saveAnnotationProgress(state, payload) {
      // 不在此处实现，只是一个调用annotation模块的接口
    },
    clearAnnotationProgress(state) {
      // 不在此处实现，只是一个调用annotation模块的接口
    }
  },
  actions: {
    // 保存当前标注进度 - 委托给annotation模块
    saveProgress({ dispatch }, payload) {
      return dispatch('annotation/saveAnnotationProgress', payload);
    },
    // 完成标注并清除进度 - 委托给annotation模块
    completeAnnotation({ dispatch }) {
      return dispatch('annotation/clearAnnotationProgress');
    }
  },
  modules: {
    auth,
    images,
    users,
    stats,
    annotation,
    teamApplications // 添加团队申请模块到Vuex存储中
  },
  plugins: [
    createPersistedState({
      paths: ['auth', 'annotation', 'teamApplications'],
      storage: {
        getItem: key => {
          try {
            return localStorage.getItem(key);
          } catch (err) {
            console.error('localStorage getItem error:', err);
            return null;
          }
        },
        setItem: (key, value) => {
          try {
            // 如果数据过大，先尝试清理一些旧数据
            if (value && value.length > 1000000) {
              console.warn('数据过大，尝试清理localStorage');
              localStorage.removeItem('vuex');
            }
            localStorage.setItem(key, value);
          } catch (err) {
            console.error('localStorage setItem error:', err);
            // 如果失败，尝试清理localStorage后再保存核心数据
            try {
              for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key !== 'user' && key !== 'token') {
                  localStorage.removeItem(key);
                }
              }
              localStorage.setItem(key, value);
            } catch (e) {
              console.error('无法保存数据到localStorage，即使在清理后:', e);
            }
          }
        },
        removeItem: key => {
          try {
            localStorage.removeItem(key);
          } catch (err) {
            console.error('localStorage removeItem error:', err);
          }
        }
      }
    })
  ]
}) 