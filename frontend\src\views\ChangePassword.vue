<template>
  <div class="change-password-container">
    <div class="header">
      <div class="back-button" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span>返回</span>
      </div>
      <h1>修改密码</h1>
    </div>
    
    <div class="content-card">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="输入原密码修改" name="original">
          <el-form :model="originalForm" :rules="originalRules" ref="originalForm" label-width="100px" class="password-form">
            <el-form-item label="原密码" prop="oldPassword">
              <el-input type="password" v-model="originalForm.oldPassword" placeholder="请输入原密码"></el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="newPassword">
              <el-input type="password" v-model="originalForm.newPassword" placeholder="请输入新密码" @input="checkPasswordStrength"></el-input>
              <div class="password-strength" v-if="originalForm.newPassword">
                <div class="strength-label">密码强度:</div>
                <div class="strength-meter">
                  <div class="strength-bar" :class="passwordStrengthClass"></div>
                </div>
                <div class="strength-text" :class="passwordStrengthClass">{{ passwordStrengthText }}</div>
              </div>
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input type="password" v-model="originalForm.confirmPassword" placeholder="请再次输入新密码"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="isSubmitting" @click="submitOriginalMethod" class="submit-button">确认修改</el-button>
            </el-form-item>
          </el-form>
          <div class="tips">
            <p><i class="el-icon-info"></i> 密码必须至少包含8个字符</p>
            <p><i class="el-icon-info"></i> 密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种</p>
            <p><i class="el-icon-info"></i> 请勿使用常见的简单密码</p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="通过验证码修改" name="verification">
          <p class="tab-description">如果忘记了原密码，可以通过邮箱验证码进行修改</p>
          <el-form :model="verificationForm" :rules="verificationRules" ref="verificationForm" label-width="100px" class="password-form">
            <!-- 邮箱输入（自动填充且不可修改） -->
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="verificationForm.email" placeholder="您的注册邮箱" :disabled="true"></el-input>
            </el-form-item>
            
            <!-- 验证码输入 -->
            <el-form-item label="验证码" prop="code">
              <div class="verification-code-container">
                <el-input v-model="verificationForm.code" placeholder="请输入验证码"></el-input>
                <el-button 
                  type="primary" 
                  :disabled="isSendingCode || countdown > 0" 
                  @click="sendVerificationCode"
                  class="send-code-button"
                >
                  {{ countdown > 0 ? `${countdown}秒后重新发送` : '获取验证码' }}
                </el-button>
              </div>
            </el-form-item>
            
            <!-- 新密码输入 -->
            <el-form-item label="新密码" prop="newPassword">
              <el-input type="password" v-model="verificationForm.newPassword" placeholder="请输入新密码" @input="checkVerificationPasswordStrength"></el-input>
              <div class="password-strength" v-if="verificationForm.newPassword">
                <div class="strength-label">密码强度:</div>
                <div class="strength-meter">
                  <div class="strength-bar" :class="verificationPasswordStrengthClass"></div>
                </div>
                <div class="strength-text" :class="verificationPasswordStrengthClass">{{ verificationPasswordStrengthText }}</div>
              </div>
            </el-form-item>
            
            <!-- 确认密码输入 -->
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input type="password" v-model="verificationForm.confirmPassword" placeholder="请再次输入新密码"></el-input>
            </el-form-item>
            
            <!-- 提交按钮 -->
            <el-form-item>
              <el-button type="primary" :loading="isSubmittingVerification" @click="submitVerificationMethod" class="submit-button">确认修改</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 提示信息 -->
          <div class="tips">
            <p><i class="el-icon-info"></i> 验证码将发送到您显示的邮箱地址</p>
            <p><i class="el-icon-info"></i> 验证码有效期为30分钟</p>
            <p><i class="el-icon-info"></i> 密码必须至少包含8个字符，且包含大写字母、小写字母、数字和特殊符号中的至少三种</p>
            <p><i class="el-icon-warning"></i> 如需修改邮箱，请联系管理员</p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api';

export default {
  name: 'ChangePassword',
  created() {
    // 从本地存储获取当前用户信息
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user && user.email) {
        this.verificationForm.email = user.email;
        console.log('已自动填充用户邮箱:', user.email);
      } else {
        console.warn('未找到用户邮箱信息');
      }
    } catch (error) {
      console.error('获取用户邮箱信息失败:', error);
    }
  },
  data() {
    // 确认密码的验证器
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.originalForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };
    
    // 密码复杂度验证器
    const validatePassword = (rule, value, callback) => {
      if (value.length < 8) {
        callback(new Error('密码长度必须不少于8位'));
        return;
      }
      
      // 检查密码复杂度
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumbers = /[0-9]/.test(value);
      const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value);
      
      const typesCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length;
      
      // 检查常见简单密码
      const commonPasswords = ['123456', 'password', 'abcdef', '12345678', 'qwerty', '111111'];
      if (commonPasswords.includes(value.toLowerCase())) {
        callback(new Error('请勿使用常见的简单密码'));
        return;
      }
      
      if (typesCount < 3) {
        callback(new Error('密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种'));
        return;
      }
      
      // 密码验证通过
      callback();
    };
    
    // 验证码确认密码的验证器
    const validateVerificationConfirmPassword = (rule, value, callback) => {
      if (value !== this.verificationForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };
    
    // 邮箱格式验证器
    const validateEmail = (rule, value, callback) => {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(value)) {
        callback(new Error('请输入有效的邮箱地址'));
      } else {
        callback();
      }
    };
    
    return {
      activeTab: 'original',
      isSubmitting: false,
      isSubmittingVerification: false,
      isSendingCode: false,
      countdown: 0, // 验证码倒计时
      countdownTimer: null, // 倒计时定时器
      passwordStrength: '',
      verificationPasswordStrength: '',
      
      // 原密码修改方式的表单
      originalForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      
      // 验证码修改方式的表单
      verificationForm: {
        email: '',
        code: '',
        newPassword: '',
        confirmPassword: ''
      },
      
      // 原密码修改方式的验证规则
      originalRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      
      // 验证码修改方式的验证规则
      verificationRules: {
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { min: 6, max: 6, message: '验证码长度应为6位', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateVerificationConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    passwordStrengthClass() {
      switch(this.passwordStrength) {
        case 'weak': return 'weak';
        case 'medium': return 'medium';
        case 'strong': return 'strong';
        default: return '';
      }
    },
    passwordStrengthText() {
      switch(this.passwordStrength) {
        case 'weak': return '弱';
        case 'medium': return '中';
        case 'strong': return '强';
        default: return '无';
      }
    },
    verificationPasswordStrengthClass() {
      switch(this.verificationPasswordStrength) {
        case 'weak': return 'weak';
        case 'medium': return 'medium';
        case 'strong': return 'strong';
        default: return '';
      }
    },
    verificationPasswordStrengthText() {
      switch(this.verificationPasswordStrength) {
        case 'weak': return '弱';
        case 'medium': return '中';
        case 'strong': return '强';
        default: return '无';
      }
    }
  },
  beforeUnmount() {
    // 组件销毁前清除定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 检查密码强度
    checkPasswordStrength() {
      const pwd = this.originalForm.newPassword;
      
      if (!pwd || pwd.length < 8) {
        this.passwordStrength = 'weak';
        return;
      }
      
      // 检查密码复杂度
      const hasUpperCase = /[A-Z]/.test(pwd);
      const hasLowerCase = /[a-z]/.test(pwd);
      const hasNumbers = /[0-9]/.test(pwd);
      const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd);
      
      const typesCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length;
      
      // 密码强度评估
      if (typesCount < 3) {
        this.passwordStrength = 'weak';
      } else if (typesCount === 3 && pwd.length < 10) {
        this.passwordStrength = 'medium';
      } else if (typesCount === 4 || (typesCount === 3 && pwd.length >= 10)) {
        this.passwordStrength = 'strong';
      }
    },
    
    // 检查验证码方式的密码强度
    checkVerificationPasswordStrength() {
      const pwd = this.verificationForm.newPassword;
      
      if (!pwd || pwd.length < 8) {
        this.verificationPasswordStrength = 'weak';
        return;
      }
      
      // 检查密码复杂度
      const hasUpperCase = /[A-Z]/.test(pwd);
      const hasLowerCase = /[a-z]/.test(pwd);
      const hasNumbers = /[0-9]/.test(pwd);
      const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd);
      
      const typesCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length;
      
      // 密码强度评估
      if (typesCount < 3) {
        this.verificationPasswordStrength = 'weak';
      } else if (typesCount === 3 && pwd.length < 10) {
        this.verificationPasswordStrength = 'medium';
      } else if (typesCount === 4 || (typesCount === 3 && pwd.length >= 10)) {
        this.verificationPasswordStrength = 'strong';
      }
    },
    
    // 发送验证码
    async sendVerificationCode() {
      // 邮箱已经自动填充且不可修改，无需验证
      if (!this.verificationForm.email) {
        this.$message.error('未获取到您的邮箱信息，请联系管理员');
        return;
      }
      
      this.isSendingCode = true;
      
      try {
        // 调用发送验证码API
        await api.users.sendResetPasswordEmail(this.verificationForm.email);
        
        // 显示成功消息
        this.$message.success('验证码已发送到您的邮箱，请注意查收');
        
        // 开始倒计时
        this.startCountdown();
      } catch (error) {
        console.error('发送验证码失败:', error);
        this.$message.error('发送验证码失败: ' + (error.response?.data?.message || error.message || '未知错误'));
      } finally {
        this.isSendingCode = false;
      }
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdown = 60; // 60秒倒计时
      
      // 清除可能存在的旧定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }
      
      // 创建新定时器
      this.countdownTimer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
      }, 1000);
    },
    
    // 提交原密码修改方式
    submitOriginalMethod() {
      this.$refs.originalForm.validate(async (valid) => {
        if (!valid) {
          return;
        }
        
        this.isSubmitting = true;
        
        try {
          // 获取当前用户信息
          const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
          
          await api.users.changePassword({
            oldPassword: this.originalForm.oldPassword,
            newPassword: this.originalForm.newPassword
          });
          
          this.$message.success('密码修改成功');
          
          // 清空表单
          this.$refs.originalForm.resetFields();
          
          // 延迟返回个人资料页面
          setTimeout(() => {
            this.goBack();
          }, 1500);
        } catch (error) {
          console.error('修改密码失败:', error);
          this.$message.error('修改密码失败: ' + (error.response?.data?.message || error.message || '未知错误'));
        } finally {
          this.isSubmitting = false;
        }
      });
    },
    
    // 提交验证码修改方式
    submitVerificationMethod() {
      this.$refs.verificationForm.validate(async (valid) => {
        if (!valid) {
          return;
        }
        
        this.isSubmittingVerification = true;
        
        try {
          // 调用验证码重置密码API
          await api.users.resetPassword(
            this.verificationForm.email,
            this.verificationForm.code,
            this.verificationForm.newPassword
          );
          
          this.$message.success('密码修改成功');
          
          // 清空表单
          this.$refs.verificationForm.resetFields();
          
          // 延迟返回个人资料页面
          setTimeout(() => {
            this.goBack();
          }, 1500);
        } catch (error) {
          console.error('通过验证码修改密码失败:', error);
          this.$message.error('修改密码失败: ' + (error.response?.data?.message || error.message || '未知错误'));
        } finally {
          this.isSubmittingVerification = false;
        }
      });
    }
  }
}
</script>

<style scoped>
.change-password-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  top: 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #409EFF;
}

.back-button i {
  margin-right: 5px;
}

.content-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.password-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px 0;
}

.submit-button {
  width: 100%;
  margin-top: 10px;
}

.tips {
  max-width: 500px;
  margin: 20px auto 0;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  color: #606266;
}

.tips p {
  margin: 8px 0;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.tips i {
  margin-right: 8px;
  color: #909399;
}

.tab-description {
  color: #606266;
  margin-bottom: 20px;
  text-align: center;
}

.placeholder-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.placeholder-message i {
  font-size: 40px;
  margin-bottom: 10px;
}

/* 密码强度指示器样式 */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.strength-label {
  margin-right: 8px;
  color: #606266;
}

.strength-meter {
  flex: 1;
  height: 4px;
  background-color: #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 8px;
}

.strength-bar {
  height: 100%;
  width: 0;
  transition: width 0.3s;
}

.strength-bar.weak {
  width: 33%;
  background-color: #f56c6c;
}

.strength-bar.medium {
  width: 66%;
  background-color: #e6a23c;
}

.strength-bar.strong {
  width: 100%;
  background-color: #67c23a;
}

.strength-text {
  width: 30px;
  text-align: center;
}

.strength-text.weak {
  color: #f56c6c;
}

.strength-text.medium {
  color: #e6a23c;
}

.strength-text.strong {
  color: #67c23a;
}

/* 验证码输入框样式 */
.verification-code-container {
  display: flex;
  align-items: center;
}

.verification-code-container .el-input {
  flex: 1;
  margin-right: 10px;
}

.send-code-button {
  width: 140px;
  white-space: nowrap;
}
</style> 