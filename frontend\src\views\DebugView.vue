<template>
  <div class="debug-container">
    <h1>标注调试工具</h1>
    
    <div class="card">
      <h2>标注数据测试</h2>
      
      <div class="form-group">
        <label>图像ID (metadata_id):</label>
        <input v-model="metadata_id" type="number" />
      </div>
      
      <div class="form-group">
        <label>标签类型 (tag):</label>
        <input v-model="tag" type="text" />
      </div>
      
      <div class="form-group">
        <label>用户ID (created_by):</label>
        <input v-model="created_by" type="number" />
      </div>
      
      <div class="form-row">
        <div class="form-group">
          <label>X坐标 (归一化0-1):</label>
          <input v-model="x" type="number" step="0.01" min="0" max="1" />
        </div>
        
        <div class="form-group">
          <label>Y坐标 (归一化0-1):</label>
          <input v-model="y" type="number" step="0.01" min="0" max="1" />
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group">
          <label>宽度 (归一化0-1):</label>
          <input v-model="width" type="number" step="0.01" min="0.01" max="1" />
        </div>
        
        <div class="form-group">
          <label>高度 (归一化0-1):</label>
          <input v-model="height" type="number" step="0.01" min="0.01" max="1" />
        </div>
      </div>
      
      <button @click="testSaveAnnotation" class="btn primary">测试保存标注</button>
      
      <div v-if="status" :class="['status', status.success ? 'success' : 'error']">
        {{ status.message }}
      </div>
      
      <div v-if="requestData" class="debug-section">
        <h3>请求数据:</h3>
        <pre>{{ JSON.stringify(requestData, null, 2) }}</pre>
      </div>
      
      <div v-if="responseData" class="debug-section">
        <h3>响应数据:</h3>
        <pre>{{ JSON.stringify(responseData, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="card">
      <h2>网络请求监控</h2>
      <button @click="enableMonitoring" class="btn" :class="{ primary: !isMonitoringEnabled }">{{ isMonitoringEnabled ? '已启用监控' : '启用请求监控' }}</button>
      <p class="hint">启用后，所有标注相关的网络请求将在控制台中详细显示</p>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api';
import { enableAnnotationDebug, displayTagData } from '@/utils/debug';

export default {
  name: 'DebugView',
  data() {
    // 从本地存储或用户信息中获取默认值
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userId = user.id || user.customId || 1;
    
    return {
      // 表单字段
      metadata_id: 1, // 默认图像ID
      tag: '血管瘤', // 默认标签类型
      created_by: userId, // 用户ID
      x: 0.5, // 默认X位置(中心点)
      y: 0.5, // 默认Y位置(中心点)
      width: 0.3, // 默认宽度
      height: 0.3, // 默认高度
      
      // 状态
      isMonitoringEnabled: false,
      requestData: null,
      responseData: null,
      status: null
    };
  },
  mounted() {
    // 检查是否已启用监控
    this.isMonitoringEnabled = window.ANNOTATION_DEBUG_ENABLED === true;
    
    // 自动填充图像ID
    const urlParams = new URLSearchParams(window.location.search);
    const imageId = urlParams.get('imageId');
    if (imageId) {
      this.metadata_id = parseInt(imageId, 10);
    }
  },
  methods: {
    /**
     * 测试保存标注数据
     */
    async testSaveAnnotation() {
      this.requestData = null;
      this.responseData = null;
      this.status = null;
      
      try {
        // 构建标注数据
        const tagData = {
          metadata_id: parseInt(this.metadata_id, 10),
          tag: this.tag,
          x: parseFloat(this.x),
          y: parseFloat(this.y),
          width: parseFloat(this.width),
          height: parseFloat(this.height),
          created_by: parseInt(this.created_by, 10)
        };
        
        // 显示请求数据
        this.requestData = tagData;
        console.log('请求标注数据:', tagData);
        displayTagData(tagData);
        
        // 发送API请求
        this.status = { success: false, message: '正在发送请求...' };
        const response = await api.tags.create(tagData);
        
        // 显示响应数据
        this.responseData = response.data;
        this.status = { success: true, message: `保存成功! 标注ID: ${response.data.id}` };
        console.log('标注保存成功:', response.data);
      } catch (error) {
        // 处理错误
        console.error('保存标注失败:', error);
        
        // 提取错误信息
        let errorMessage = '保存标注失败';
        if (error.response) {
          errorMessage += `: ${error.response.status} ${error.response.statusText}`;
          this.responseData = error.response.data;
        } else if (error.message) {
          errorMessage += `: ${error.message}`;
        }
        
        this.status = { success: false, message: errorMessage };
      }
    },
    
    /**
     * 启用网络请求监控
     */
    enableMonitoring() {
      if (this.isMonitoringEnabled) {
        return; // 已启用
      }
      
      try {
        enableAnnotationDebug();
        this.isMonitoringEnabled = true;
        this.status = { success: true, message: '请求监控已启用，请查看浏览器控制台' };
        
        // 持久化调试设置
        localStorage.setItem('enableDebug', 'true');
        
      } catch (error) {
        console.error('启用监控失败:', error);
        this.status = { success: false, message: `启用监控失败: ${error.message}` };
      }
    }
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 20px;
  margin-bottom: 20px;
}

h2 {
  margin-top: 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
  color: #444;
}

.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #666;
}

input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.btn {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  background-color: #f0f0f0;
  margin-top: 10px;
}

.btn.primary {
  background-color: #1976D2;
  color: white;
}

.status {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
}

.status.success {
  background-color: #E8F5E9;
  color: #2E7D32;
  border-left: 4px solid #2E7D32;
}

.status.error {
  background-color: #FFEBEE;
  color: #C62828;
  border-left: 4px solid #C62828;
}

.debug-section {
  margin-top: 20px;
  border-top: 1px dashed #ddd;
  padding-top: 15px;
}

.debug-section h3 {
  margin-top: 0;
  color: #666;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 13px;
}

.hint {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}
</style> 