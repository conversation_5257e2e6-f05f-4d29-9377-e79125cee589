<template>
  <div class="status-badge">
    <el-tag :type="tagType" :effect="effect" size="medium">{{ displayText }}</el-tag>
  </div>
</template>

<script>
export default {
  name: 'StatusBadge',
  props: {
    status: {
      type: String,
      required: true,
      validator: (value) => {
        return ['DRAFT', 'REVIEWED', 'SUBMITTED', 'APPROVED', 'REJECTED'].includes(value);
      }
    },
    plain: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    tagType() {
      const types = {
        'DRAFT': 'info',
        'REVIEWED': 'warning',
        'SUBMITTED': 'primary',
        'APPROVED': 'success',
        'REJECTED': 'danger'
      };
      return types[this.status] || 'info';
    },
    displayText() {
      const texts = {
        'DRAFT': '未标注',
        'REVIEWED': '已标注',
        'SUBMITTED': '待审核',
        'APPROVED': '已通过',
        'REJECTED': '已驳回'
      };
      
      console.log('StatusBadge 显示状态:', this.status, '映射为:', texts[this.status] || this.status);
      
      return texts[this.status] || this.status;
    },
    effect() {
      return this.plain ? 'plain' : 'light';
    }
  }
};
</script>

<style scoped>
.status-badge {
  display: inline-block;
}
</style> 