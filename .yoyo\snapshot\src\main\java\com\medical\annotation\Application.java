package com.medical.annotation;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import com.medical.annotation.service.FileService;

@SpringBootApplication
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
    
    @Bean
    CommandLineRunner init(FileService fileService) {
        return args -> {
            try {
                System.out.println("初始化文件系统目录...");
                fileService.init();
                System.out.println("文件系统目录初始化完成");
            } catch (Exception e) {
                System.err.println("文件系统目录初始化失败: " + e.getMessage());
                e.printStackTrace();
            }
        };
    }
} 