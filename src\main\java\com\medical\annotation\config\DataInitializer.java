package com.medical.annotation.config;

import com.medical.annotation.model.User;
import com.medical.annotation.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

@Configuration
public class DataInitializer {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    // 注释掉自动初始化，我们现在使用SQL脚本
    /*
    @Bean
    public CommandLineRunner initData() {
        return args -> {
            // 创建默认用户
            if (userRepository.count() == 0) {
                // 创建管理员用户
                User admin = new User();
                admin.setName("系统管理员");
                admin.setEmail("<EMAIL>");
                admin.setPassword(passwordEncoder.encode("admin123"));
                admin.setRole(User.Role.ADMIN);
                admin.setHospital("示范医院");
                admin.setDepartment("管理部");
                userRepository.save(admin);
                
                // 创建医生用户
                User doctor = new User();
                doctor.setName("测试医生");
                doctor.setEmail("<EMAIL>");
                doctor.setPassword(passwordEncoder.encode("doctor123"));
                doctor.setRole(User.Role.DOCTOR);
                doctor.setHospital("示范医院");
                doctor.setDepartment("内科");
                userRepository.save(doctor);
                
                // 创建审核人员
                User reviewer = new User();
                reviewer.setName("测试审核员");
                reviewer.setEmail("<EMAIL>");
                reviewer.setPassword(passwordEncoder.encode("reviewer123"));
                reviewer.setRole(User.Role.REVIEWER);
                reviewer.setHospital("示范医院");
                reviewer.setDepartment("影像科");
                userRepository.save(reviewer);
                
                System.out.println("=== 初始用户已创建 ===");
                System.out.println("管理员: <EMAIL> / admin123");
                System.out.println("医生: <EMAIL> / doctor123");
                System.out.println("审核员: <EMAIL> / reviewer123");
            }
        };
    }
    */
} 