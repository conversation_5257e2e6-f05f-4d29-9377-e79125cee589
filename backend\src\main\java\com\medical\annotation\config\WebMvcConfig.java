package com.medical.annotation.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    // 添加对/app/**路径的处理，使其能正确处理前端路由
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 将/app/**的请求转发到index.html，让前端路由处理
        registry.addViewController("/app/**").setViewName("forward:/index.html");
    }
} 