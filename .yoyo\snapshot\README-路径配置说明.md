# 血管瘤辅助系统路径配置说明

本系统支持灵活的路径配置，可以轻松地在不同环境下部署和运行。本文档详细说明如何配置系统的路径。

## 配置文件位置

系统支持通过不同环境的配置文件来设置路径：

- `application.properties`: 默认配置
- `application-dev.properties`: 开发环境配置
- `application-prod.properties`: 生产环境配置

## 关键配置项

系统使用两个主要配置项来确定文件路径：

### 1. 项目根目录 (app.base.dir)

这是所有相对路径的基准点。如果未设置，默认为系统当前工作目录。

```properties
# 设置绝对路径
app.base.dir=F:/xueguan
# 或
app.base.dir=/home/<USER>/medical-system
```

### 2. 图片存储路径 (app.upload.dir)

图片存储路径可以是相对于项目根目录的路径，也可以是完全独立的绝对路径。

```properties
# 相对路径 (相对于app.base.dir)
app.upload.dir=medical_images

# 或使用绝对路径
app.upload.dir=F:/血管瘤辅助系统/medical_images
app.upload.dir=/data/medical_images
```

## 路径工作原理

系统将自动根据配置创建以下目录结构：

- `${app.upload.dir}`: 图片主目录
  - `/original`: 原始图片
  - `/processed`: 处理后的图片
  - `/annotated`: 标注后的图片
  - `/temp`: 临时文件

## 示例配置

### 开发环境示例

```properties
# 项目位于F:/xueguan
app.base.dir=F:/xueguan
# 图片存储在项目根目录下的medical_images文件夹
app.upload.dir=medical_images
```

最终路径: `F:/xueguan/medical_images`

### 生产环境示例

```properties
# 项目位于/home/<USER>/血管瘤辅助系统
app.base.dir=/home/<USER>/血管瘤辅助系统
# 图片存储在独立的数据目录
app.upload.dir=/data/medical_images
```

最终路径: `/data/medical_images`

## 更换系统部署位置

当您需要将系统部署到新的位置时，只需要修改`app.base.dir`和/或`app.upload.dir`即可，无需修改代码。

### 仅更换项目位置，保持相对路径

```properties
# 新位置
app.base.dir=/new/project/path
# 保持相对路径不变
app.upload.dir=medical_images
```

### 更换图片存储位置到独立目录

```properties
# 设置绝对路径，独立于项目目录
app.upload.dir=/path/to/images
```

## 前端配置

前端的API地址配置位于`frontend/src/config/api.config.js`，由`vue.config.js`中的环境变量控制。要切换后端服务器地址，请修改`.env.local`文件或`vue.config.js`中的默认值。 