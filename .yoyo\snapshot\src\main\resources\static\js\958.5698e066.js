"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[958],{42958:(e,n,i)=>{i.r(n),i.d(n,{default:()=>f});var t=i(20641),a=i(90033),l={class:"review-container"},o={key:0,class:"review-dialog-content"},r={class:"dialog-footer"};function u(e,n,i,u,d,s){var c=(0,t.g2)("el-table-column"),g=(0,t.g2)("el-button"),f=(0,t.g2)("el-table"),p=(0,t.g2)("el-pagination"),v=(0,t.g2)("el-tab-pane"),b=(0,t.g2)("el-tag"),w=(0,t.g2)("el-tabs"),m=(0,t.g2)("el-descriptions-item"),h=(0,t.g2)("el-descriptions"),_=(0,t.g2)("el-radio"),k=(0,t.g2)("el-radio-group"),F=(0,t.g2)("el-form-item"),C=(0,t.g2)("el-input"),D=(0,t.g2)("el-form"),R=(0,t.g2)("el-dialog"),y=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",l,[n[12]||(n[12]=(0,t.Lk)("div",{class:"page-header"},[(0,t.Lk)("h2",null,"标注审核")],-1)),(0,t.bF)(w,{modelValue:d.activeTab,"onUpdate:modelValue":n[2]||(n[2]=function(e){return d.activeTab=e})},{default:(0,t.k6)((function(){return[(0,t.bF)(v,{label:"待审核",name:"pending"},{default:(0,t.k6)((function(){return[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(f,{data:d.pendingReviews,style:{width:"100%"}},{default:(0,t.k6)((function(){return[(0,t.bF)(c,{prop:"caseId",label:"病例编号",width:"180"}),(0,t.bF)(c,{prop:"patientInfo",label:"患者信息",width:"180"}),(0,t.bF)(c,{prop:"department",label:"部位"}),(0,t.bF)(c,{prop:"type",label:"类型"}),(0,t.bF)(c,{prop:"createTime",label:"标注时间"}),(0,t.bF)(c,{prop:"annotator",label:"标注人"}),(0,t.bF)(c,{label:"操作",width:"200"},{default:(0,t.k6)((function(e){return[(0,t.bF)(g,{type:"primary",size:"small",onClick:function(n){return s.handleReview(e.row)}},{default:(0,t.k6)((function(){return n[5]||(n[5]=[(0,t.eW)(" 审核 ")])})),_:2,__:[5]},1032,["onClick"]),(0,t.bF)(g,{type:"info",size:"small",onClick:function(n){return s.handleView(e.row)}},{default:(0,t.k6)((function(){return n[6]||(n[6]=[(0,t.eW)(" 查看 ")])})),_:2,__:[6]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[y,d.loading.pending]]),(0,t.bF)(p,{background:"",layout:"prev, pager, next",total:d.pagination.pending.total,"page-size":d.pagination.pending.pageSize,onCurrentChange:n[0]||(n[0]=function(e){return s.handlePageChange(e,"pending")}),class:"pagination"},null,8,["total","page-size"])]})),_:1}),(0,t.bF)(v,{label:"已审核",name:"reviewed"},{default:(0,t.k6)((function(){return[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(f,{data:d.reviewedCases,style:{width:"100%"}},{default:(0,t.k6)((function(){return[(0,t.bF)(c,{prop:"caseId",label:"病例编号",width:"180"}),(0,t.bF)(c,{prop:"patientInfo",label:"患者信息",width:"180"}),(0,t.bF)(c,{prop:"department",label:"部位"}),(0,t.bF)(c,{prop:"type",label:"类型"}),(0,t.bF)(c,{prop:"reviewTime",label:"审核时间"}),(0,t.bF)(c,{prop:"reviewer",label:"审核人"}),(0,t.bF)(c,{prop:"status",label:"审核结果"},{default:(0,t.k6)((function(e){return[(0,t.bF)(b,{type:"已通过"===e.row.status?"success":"danger"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,a.v_)(e.row.status),1)]})),_:2},1032,["type"])]})),_:1}),(0,t.bF)(c,{label:"操作",width:"120"},{default:(0,t.k6)((function(e){return[(0,t.bF)(g,{type:"info",size:"small",onClick:function(n){return s.handleView(e.row)}},{default:(0,t.k6)((function(){return n[7]||(n[7]=[(0,t.eW)(" 查看 ")])})),_:2,__:[7]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[y,d.loading.reviewed]]),(0,t.bF)(p,{background:"",layout:"prev, pager, next",total:d.pagination.reviewed.total,"page-size":d.pagination.reviewed.pageSize,onCurrentChange:n[1]||(n[1]=function(e){return s.handlePageChange(e,"reviewed")}),class:"pagination"},null,8,["total","page-size"])]})),_:1})]})),_:1},8,["modelValue"]),(0,t.bF)(R,{title:"标注审核",visible:d.reviewDialog.visible,width:"500px",onClose:s.closeReviewDialog},{footer:(0,t.k6)((function(){return[(0,t.Lk)("span",r,[(0,t.bF)(g,{onClick:s.closeReviewDialog},{default:(0,t.k6)((function(){return n[10]||(n[10]=[(0,t.eW)("取消")])})),_:1,__:[10]},8,["onClick"]),(0,t.bF)(g,{type:"primary",onClick:s.submitReview},{default:(0,t.k6)((function(){return n[11]||(n[11]=[(0,t.eW)("提交审核")])})),_:1,__:[11]},8,["onClick"])])]})),default:(0,t.k6)((function(){return[d.reviewDialog["case"]?((0,t.uX)(),(0,t.CE)("div",o,[(0,t.bF)(h,{column:1,border:""},{default:(0,t.k6)((function(){return[(0,t.bF)(m,{label:"病例编号"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,a.v_)(d.reviewDialog["case"].caseId),1)]})),_:1}),(0,t.bF)(m,{label:"患者信息"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,a.v_)(d.reviewDialog["case"].patientInfo),1)]})),_:1}),(0,t.bF)(m,{label:"部位"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,a.v_)(d.reviewDialog["case"].department),1)]})),_:1}),(0,t.bF)(m,{label:"类型"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,a.v_)(d.reviewDialog["case"].type),1)]})),_:1}),(0,t.bF)(m,{label:"标注人"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,a.v_)(d.reviewDialog["case"].annotator),1)]})),_:1}),(0,t.bF)(m,{label:"标注时间"},{default:(0,t.k6)((function(){return[(0,t.eW)((0,a.v_)(d.reviewDialog["case"].createTime),1)]})),_:1})]})),_:1}),(0,t.bF)(D,{model:d.reviewDialog.form,"label-width":"80px",class:"review-form"},{default:(0,t.k6)((function(){return[(0,t.bF)(F,{label:"审核结果"},{default:(0,t.k6)((function(){return[(0,t.bF)(k,{modelValue:d.reviewDialog.form.result,"onUpdate:modelValue":n[3]||(n[3]=function(e){return d.reviewDialog.form.result=e})},{default:(0,t.k6)((function(){return[(0,t.bF)(_,{label:"approve"},{default:(0,t.k6)((function(){return n[8]||(n[8]=[(0,t.eW)("通过")])})),_:1,__:[8]}),(0,t.bF)(_,{label:"reject"},{default:(0,t.k6)((function(){return n[9]||(n[9]=[(0,t.eW)("驳回")])})),_:1,__:[9]})]})),_:1},8,["modelValue"])]})),_:1}),(0,t.bF)(F,{label:"备注"},{default:(0,t.k6)((function(){return[(0,t.bF)(C,{type:"textarea",modelValue:d.reviewDialog.form.comment,"onUpdate:modelValue":n[4]||(n[4]=function(e){return d.reviewDialog.form.comment=e}),rows:3,placeholder:"请输入审核意见（选填）"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model"])])):(0,t.Q3)("",!0)]})),_:1},8,["visible","onClose"])])}i(28706),i(44114),i(26099),i(76031);var d=i(653);const s={name:"Review",data:function(){return{activeTab:"pending",loading:{pending:!1,reviewed:!1},pendingReviews:[],reviewedCases:[],pagination:{pending:{pageSize:10,currentPage:1,total:0},reviewed:{pageSize:10,currentPage:1,total:0}},reviewDialog:{visible:!1,case:null,form:{result:"approve",comment:""}}}},created:function(){this.fetchPendingReviews()},watch:{activeTab:function(e){"pending"===e?this.fetchPendingReviews():"reviewed"===e&&this.fetchReviewedCases()}},methods:{fetchPendingReviews:function(){var e=this;this.loading.pending=!0,setTimeout((function(){e.pendingReviews=[],e.pagination.pending.total=0,e.loading.pending=!1}),500)},fetchReviewedCases:function(){var e=this;this.loading.reviewed=!0,setTimeout((function(){e.reviewedCases=[],e.pagination.reviewed.total=0,e.loading.reviewed=!1}),500)},handlePageChange:function(e,n){this.pagination[n].currentPage=e,"pending"===n?this.fetchPendingReviews():this.fetchReviewedCases()},handleReview:function(e){this.reviewDialog.visible=!0,this.reviewDialog["case"]=e,this.reviewDialog.form={result:"approve",comment:""}},handleView:function(e){this.$router.push("/cases/view/".concat(e.id))},closeReviewDialog:function(){this.reviewDialog.visible=!1,this.reviewDialog["case"]=null,this.reviewDialog.form={result:"approve",comment:""}},submitReview:function(){var e=this;if(this.reviewDialog["case"]){var n=this.reviewDialog["case"].id,i="approve"===this.reviewDialog.form.result,t=this.reviewDialog.form.comment,a=this.$loading({lock:!0,text:"提交审核结果...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),l=i?d["default"].images.markAsApproved(n,t):d["default"].images.markAsRejected(n,t);l.then((function(){e.$message.success("审核".concat(i?"通过":"驳回","成功")),e.closeReviewDialog(),"pending"===e.activeTab?e.fetchPendingReviews():e.fetchReviewedCases()}))["catch"]((function(n){var i;e.$message.error("审核提交失败: "+((null===(i=n.response)||void 0===i?void 0:i.data)||n.message||"未知错误"))}))["finally"]((function(){a.close()}))}else this.$message.warning("没有选择审核的病例")}}};var c=i(66262);const g=(0,c.A)(s,[["render",u],["__scopeId","data-v-4b028a5c"]]),f=g}}]);