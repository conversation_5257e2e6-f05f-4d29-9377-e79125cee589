<template>
  <div class="users-container">
    <div class="page-header">
      <h2>人员管理</h2>
      <div class="header-buttons">
        <!-- 当用户还没有部门的时候，始终都有一个加入部门的按钮 -->
        <el-button 
          v-if="!currentUserHasDepartment" 
          type="primary" 
          @click="handleJoinDepartment"
        >
          加入部门
        </el-button>
        
        <!-- 当用户还没有部门的时候且用户为管理员或者审核医生的时候，始终有一个创建部门的按钮 -->
        <el-button 
          v-if="!currentUserHasDepartment && (isAdmin || isReviewer)" 
          type="success" 
          @click="handleCreateDepartment"
        >
          创建部门
        </el-button>
        
        <!-- 添加查看权限申请的按钮，仅管理员可见 -->
        <el-button 
          v-if="isAdmin" 
          type="warning" 
          @click="viewReviewerApplications"
        >
          查看权限申请
        </el-button>
      </div>
    </div>

    <!-- 部门筛选器 -->
    <div class="filter-bar">
      <el-form :inline="true" class="filter-form">
        <el-form-item label="所属部门">
          <el-select v-model="departmentFilter" placeholder="全部部门" clearable>
            <el-option v-for="dept in departments" :key="dept" :label="dept" :value="dept"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="成员角色">
          <el-select v-model="roleFilter" placeholder="全部角色" clearable>
            <el-option label="管理员" value="ADMIN"></el-option>
            <el-option label="标注医生" value="DOCTOR"></el-option>
            <el-option label="审核医生" value="REVIEWER"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="applyFilters">筛选</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table v-loading="loading" :data="displayUsers" style="width: 100%">
      <el-table-column prop="name" label="姓名" width="120" />
      <el-table-column prop="department" label="所属部门" width="150" />
      <el-table-column prop="role" label="角色">
        <template #default="scope">
          <el-tag :type="getRoleType(scope.row.role)">
            {{ getRoleName(scope.row.role) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="hospital" label="所属医院" />
      <el-table-column prop="team" label="所属团队" width="180">
        <template #default="scope">
          <span v-if="scope.row.team && scope.row.team.name">{{ scope.row.team.name }}</span>
          <span v-else-if="scope.row.team && typeof scope.row.team === 'string'">{{ scope.row.team }}</span>
          <span v-else>未加入团队</span>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      background
      layout="total, sizes, prev, pager, next"
      :total="filteredUsers.length"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      class="pagination"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="添加成员"
      width="500px"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <!-- 搜索框 -->
      <div class="search-wrapper">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户名、姓名、部门、医院"
          clearable
          @input="handleSearch"
        ></el-input>
      </div>
      
      <!-- 搜索结果 -->
      <div v-if="searchResults.length > 0" class="search-results">
        <el-table 
          :data="searchResults" 
          style="width: 100%" 
          highlight-current-row
          @row-click="handleRowClick"
        >
          <el-table-column prop="name" label="姓名" width="120"></el-table-column>
          <el-table-column prop="department" label="部门" width="140"></el-table-column>
          <el-table-column prop="hospital" label="所属医院"></el-table-column>
        </el-table>
      </div>
      
      <!-- 水平分割线 -->
      <el-divider v-if="searchResults.length > 0"></el-divider>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordDialog.visible"
      title="重置密码"
      width="400px"
      @close="resetPasswordDialog.visible = false"
    >
      <el-form
        ref="resetPasswordForm"
        :model="resetPasswordDialog.form"
        :rules="resetPasswordRules"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="resetPasswordDialog.form.password"
            type="password"
            placeholder="请输入新密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="resetPasswordDialog.form.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            show-password
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPasswordDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitResetPassword">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 加入部门对话框 -->
    <el-dialog
      v-model="joinDepartmentDialog.visible"
      title="加入部门"
      width="500px"
      @close="joinDepartmentDialog.visible = false"
    >
      <el-form :model="joinDepartmentDialog.form" label-width="80px">
        <el-form-item label="部门">
          <el-select 
            v-model="joinDepartmentDialog.form.department" 
            placeholder="请选择部门" 
            style="width: 100%"
            @change="handleDepartmentChange"
          >
            <el-option v-for="dept in departments" :key="dept" :label="dept" :value="dept"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="joinDepartmentDialog.form.teamId" label="申请理由">
          <el-input 
            v-model="joinDepartmentDialog.form.reason" 
            type="textarea" 
            placeholder="请简要说明加入部门的原因（可选）"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="joinDepartmentDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitJoinDepartment">
            {{ joinDepartmentDialog.form.teamId ? '申请加入' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建部门对话框 -->
    <el-dialog
      v-model="createDepartmentDialog.visible"
      title="创建部门"
      width="500px"
      @close="createDepartmentDialog.visible = false"
    >
      <el-form ref="createDepartmentForm" :model="createDepartmentDialog.form" :rules="departmentRules" label-width="80px">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="createDepartmentDialog.form.name" placeholder="请输入部门名称"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="createDepartmentDialog.form.description" 
            type="textarea" 
            placeholder="请输入部门描述"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDepartmentDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitCreateDepartment">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/utils/api'
import { mapGetters } from 'vuex'

export default {
  name: 'Users',
  data() {
    // 验证密码确认是否一致
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.userForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    
    // 验证重置密码确认是否一致
    const validateResetConfirmPassword = (rule, value, callback) => {
      if (value !== this.resetPasswordDialog.form.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      users: [],
      filteredUsers: [],
      departments: [],
      departmentFilter: '',
      roleFilter: '',
      currentPage: 1,
      pageSize: 10,
      dialogVisible: false,
      searchMode: true,
      searchQuery: '',
      searchResults: [],
      userForm: {
        id: null,
        name: '',
        role: 'DOCTOR', // 默认为标注医生
        department: '',
        hospital: '',
        email: '',
        password: '',
        confirmPassword: '',
        active: true,
        createdAt: null
      },
      userRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        role: [{ required: true, message: '请选择角色', trigger: 'change' }],
        department: [{ required: true, message: '请选择或创建部门', trigger: 'change' }],
        hospital: [{ required: true, message: '请输入所属医院', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      resetPasswordDialog: {
        visible: false,
        userId: null,
        form: {
          password: '',
          confirmPassword: ''
        }
      },
      resetPasswordRules: {
        password: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validateResetConfirmPassword, trigger: 'blur' }
        ]
      },
      selectedUser: null,
      // 新增加入部门对话框数据
      joinDepartmentDialog: {
        visible: false,
        form: {
          department: '',
          teamId: null,
          reason: ''
        }
      },
      // 新增创建部门对话框数据
      createDepartmentDialog: {
        visible: false,
        form: {
          name: '',
          description: ''
        }
      },
      // 部门表单验证规则
      departmentRules: {
        name: [
          { required: true, message: '请输入部门名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      },
      reviewerApplications: [], // 存储权限升级申请
    }
  },
  computed: {
    ...mapGetters({
      currentUser: 'getUser'
    }),
    // 当前用户是否有部门
    currentUserHasDepartment() {
      return this.currentUser && this.currentUser.department;
    },
    // 当前用户是否为管理员
    isAdmin() {
      return this.currentUser && this.currentUser.role === 'ADMIN';
    },
    // 当前用户是否为标注医生
    isDoctor() {
      return this.currentUser && this.currentUser.role === 'DOCTOR';
    },
    // 当前用户是否为审核医生
    isReviewer() {
      return this.currentUser && this.currentUser.role === 'REVIEWER';
    },
    // 计算当前页的用户数据，实现客户端分页
    displayUsers() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredUsers.slice(start, end);
    }
  },
  created() {
    // 初始化时加载用户数据
    this.fetchUsers();
    
    // 如果是管理员，加载权限升级申请
    if (this.isAdmin) {
      this.fetchReviewerApplications();
    }
    
    // 如果Vuex中没有用户信息，尝试从localStorage恢复
    if (!this.currentUser) {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          this.$store.commit('setUser', JSON.parse(storedUser));
        } catch (e) {
          console.error('解析用户数据失败', e);
        }
      }
    }
  },
  methods: {
    getRoleType(role) {
      const types = {
        'ADMIN': 'danger',
        'DOCTOR': 'primary',
        'REVIEWER': 'success'
      }
      return types[role] || 'info'
    },
    getRoleName(role) {
      const names = {
        'ADMIN': '管理员',
        'DOCTOR': '标注医生',
        'REVIEWER': '审核医生'
      }
      return names[role] || '未知'
    },
    formatDate(dateString) {
      if (!dateString) return '未知';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    },
    handlePageChange(page) {
      this.currentPage = page;
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
    },
    applyFilters() {
      this.filterUsers();
      this.currentPage = 1; // 筛选后重置页码
    },
    resetFilters() {
      this.departmentFilter = '';
      this.roleFilter = '';
      this.filterUsers();
      this.currentPage = 1;
    },
    filterUsers() {
      if (!this.departmentFilter && !this.roleFilter) {
        this.filteredUsers = Array.isArray(this.users) ? [...this.users] : [];
        return;
      }
      
      if (!Array.isArray(this.users)) {
        console.error('users不是数组，无法筛选', this.users);
        this.filteredUsers = [];
        return;
      }
      
      this.filteredUsers = this.users.filter(user => {
        if (!user) return false;
        const matchDepartment = !this.departmentFilter || 
          (user.department && user.department === this.departmentFilter);
        const matchRole = !this.roleFilter || 
          (user.role && user.role === this.roleFilter);
        return matchDepartment && matchRole;
      });
    },
    fetchUsers() {
      this.loading = true;
      
      // 使用API调用获取用户列表
      api.users.getAll()
        .then(response => {
          // 确保response.data是数组
          if (response && response.data) {
            // 如果返回的是对象而不是数组，尝试获取对象中的数组属性
            if (Array.isArray(response.data)) {
              this.users = response.data;
            } else if (response.data.content && Array.isArray(response.data.content)) {
              // 有些后端API会返回{content: [...]}格式
              this.users = response.data.content;
            } else if (response.data.data && Array.isArray(response.data.data)) {
              // 有些后端API会返回{data: [...]}格式
              this.users = response.data.data;
            } else if (response.data.items && Array.isArray(response.data.items)) {
              // 有些后端API会返回{items: [...]}格式
              this.users = response.data.items;
            } else if (response.data.users && Array.isArray(response.data.users)) {
              // 有些后端API会返回{users: [...]}格式
              this.users = response.data.users;
            } else if (typeof response.data === 'object') {
              // 如果是其他对象格式，记录错误并尝试转换
              console.warn('API返回了对象而不是数组:', response.data);
              this.users = [];
            } else {
              // 不是数组也不是对象，设为空数组
              console.error('API返回了非预期格式:', response.data);
              this.users = [];
            }
          } else {
            // response或response.data为空时设为空数组
            this.users = [];
          }
          
          // 处理用户数据，确保团队信息正确
          this.users = this.users.map(user => {
            // 如果用户没有team属性或team为null
            if (!user.team) {
              user.team = { name: '未加入团队' };
            }
            // 如果team是字符串，转换为对象
            else if (typeof user.team === 'string') {
              user.team = { name: user.team };
            }
            // 如果team是对象但没有name属性
            else if (typeof user.team === 'object' && !user.team.name) {
              user.team.name = '未知团队';
            }
            return user;
          });
          
          this.filteredUsers = [...this.users];
          
          // 高效地提取和去重部门
          const uniqueDepts = new Set();
          this.users.forEach(user => {
            if (user && user.department) {
              uniqueDepts.add(user.department);
            }
          });
          
          // 标准部门列表
          const standardDepts = ['内科', '外科', '影像科', '放射科', '急诊科', '脑科', '管理部'];
          
          // 合并标准部门和从用户数据中提取的部门
          this.departments = [
            ...standardDepts,
            ...Array.from(uniqueDepts).filter(dept => !standardDepts.includes(dept))
          ];
          
          // 获取所有团队信息
          return api.teams.getAll();
        })
        .then(teamsResponse => {
          if (teamsResponse && teamsResponse.data) {
            const teams = teamsResponse.data;
            
            // 为每个团队获取成员信息
            const teamPromises = teams.map(team => 
              api.teams.getTeamMembers(team.id)
                .then(membersResponse => {
                  const members = membersResponse.data || [];
                  return { teamId: team.id, teamName: team.name, members };
                })
                .catch(error => {
                  console.error(`获取团队 ${team.id} 成员失败:`, error);
                  return { teamId: team.id, teamName: team.name, members: [] };
                })
            );
            
            return Promise.all(teamPromises);
          }
          return [];
        })
        .then(teamsWithMembers => {
          // 更新用户的团队信息
          if (teamsWithMembers && teamsWithMembers.length > 0) {
            // 创建用户ID到团队的映射
            const userTeamMap = {};
            
            // 遍历所有团队及其成员
            teamsWithMembers.forEach(teamData => {
              const { teamId, teamName, members } = teamData;
              
              // 为每个成员记录团队信息
              members.forEach(member => {
                if (member && member.id) {
                  userTeamMap[member.id] = { id: teamId, name: teamName };
                }
              });
            });
            
            // 更新用户的团队信息
            this.users = this.users.map(user => {
              if (user && user.id && userTeamMap[user.id]) {
                user.team = userTeamMap[user.id];
              }
              return user;
            });
            
            // 更新过滤后的用户列表
            this.filteredUsers = [...this.users];
          }
        })
        .catch(error => {
          console.error('获取用户列表失败:', error);
          this.$message.error('获取用户列表失败: ' + (error.message || '未知错误'));
          // 错误时设置为空数组
          this.users = [];
          this.filteredUsers = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleAddUser() {
      this.userForm = {
        id: null,
        name: '',
        role: 'DOCTOR', // 默认为标注医生
        department: '',
        hospital: '',
        email: '',
        password: '',
        confirmPassword: '',
        active: true,
        createdAt: null
      };
      this.searchMode = true;
      this.searchQuery = '';
      this.searchResults = [];
      this.dialogVisible = true;
    },
    handleEditUser(row) {
      this.userForm = {
        id: row.id,
        name: row.name,
        role: row.role,
        department: row.department,
        hospital: row.hospital,
        email: row.email,
        active: row.active !== false, // 确保 active 有默认值
        createdAt: row.createdAt
      };
      this.dialogVisible = true;
    },
    handleDeleteUser(row) {
      this.loading = true;
      
      api.users.deleteUser(row.id)
        .then(() => {
          this.$message.success(`成员 ${row.name} 已删除`);
          this.fetchUsers();
        })
        .catch(error => {
          console.error('删除用户失败:', error);
          this.$message.error('删除失败: ' + (error.message || '未知错误'));
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleStatusChange(row, status) {
      const updatedUser = { ...row, active: status };
      
      api.users.updateUser(row.id, updatedUser)
        .then(() => {
          this.$message.success(`已${status ? '启用' : '禁用'}成员: ${row.name}`);
        })
        .catch(error => {
          console.error('更新用户状态失败:', error);
          this.$message.error('更新状态失败: ' + (error.message || '未知错误'));
          row.active = !status; // 恢复原状态
        });
    },
    handleResetPassword(row) {
      this.resetPasswordDialog.visible = true;
      this.resetPasswordDialog.userId = row.id;
      this.resetPasswordDialog.form = {
        password: '',
        confirmPassword: ''
      };
    },
    closeDialog() {
      this.dialogVisible = false;
      if (this.$refs.userFormRef) {
        this.$refs.userFormRef.resetFields();
      }
    },
    handleSearch() {
      if (!this.searchQuery || this.searchQuery.trim() === '') {
        this.searchResults = [];
        return;
      }
      
      // 防抖处理，避免频繁搜索
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      
      this.searchTimer = setTimeout(() => {
        const query = this.searchQuery.toLowerCase().trim();
        this.searchResults = this.users.filter(user => {
          // 检查姓名、部门、医院是否包含查询文字
          return (user.name && user.name.toLowerCase().includes(query)) ||
                (user.department && user.department.toLowerCase().includes(query)) ||
                (user.hospital && user.hospital.toLowerCase().includes(query));
        }).slice(0, 10); // 限制搜索结果数量，提高性能
      }, 300);
    },
    submitForm() {
      if (this.selectedUser) {
        // 如果选择了用户，将其添加到表中或进行其他操作
        this.$message.success(`已添加成员: ${this.selectedUser.name}`);
        this.dialogVisible = false;
        this.searchQuery = '';
        this.searchResults = [];
        this.selectedUser = null;
        
        // 刷新成员列表
        this.fetchUsers();
        return;
      }
      
      if (!this.searchQuery || this.searchQuery.trim() === '') {
        this.$message.warning('请输入搜索内容');
        return;
      }
      
      if (this.searchResults.length === 0) {
        this.$message.warning('未找到匹配的成员');
        return;
      }
      
      // 如果有搜索结果但没有选择，提示用户选择
      this.$message.warning('请从搜索结果中选择一个成员');
    },
    submitResetPassword() {
      this.$refs.resetPasswordForm.validate(valid => {
        if (valid) {
          this.loading = true;
          
          api.users.resetPassword(this.resetPasswordDialog.userId, this.resetPasswordDialog.form.password)
            .then(() => {
              this.$message.success('密码重置成功');
              this.resetPasswordDialog.visible = false;
            })
            .catch(error => {
              console.error('密码重置失败:', error);
              this.$message.error('密码重置失败: ' + (error.message || '未知错误'));
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      })
    },
    selectUser(user) {
      // 填充表单数据，但不复制ID和密码相关字段
      this.userForm = {
        name: user.name,
        role: user.role,
        department: user.department,
        hospital: user.hospital,
        email: user.email,
        active: true,
        password: '',
        confirmPassword: '',
        createdAt: null
      };
      
      // 关闭搜索模式
      this.searchMode = false;
    },
    handleRowClick(row) {
      // 保存选中的用户
      this.selectedUser = row;
      this.$message.info(`已选择: ${row.name}`);
    },
    // 处理加入部门按钮点击
    handleJoinDepartment() {
      this.loading = true;
      
      // 获取所有团队/部门
      api.teams.getAll()
        .then(response => {
          const teams = response.data || [];
          // 从团队数据中提取部门名称
          this.departments = teams.map(team => team.name);
          this.joinDepartmentDialog.visible = true;
          this.joinDepartmentDialog.form = {
            department: '',
            teamId: null,
            reason: ''
          };
        })
        .catch(error => {
          console.error('获取部门列表失败:', error);
          this.$message.error('获取部门列表失败: ' + (error.message || '未知错误'));
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // 处理部门选择变化
    handleDepartmentChange(departmentName) {
      // 根据部门名称查找对应的团队ID
      api.teams.getAll()
        .then(response => {
          const teams = response.data || [];
          const selectedTeam = teams.find(team => team.name === departmentName);
          if (selectedTeam) {
            this.joinDepartmentDialog.form.teamId = selectedTeam.id;
          }
        })
        .catch(error => {
          console.error('获取团队ID失败:', error);
        });
    },
    
    // 提交加入部门请求
    submitJoinDepartment() {
      if (!this.joinDepartmentDialog.form.department) {
        this.$message.warning('请选择要加入的部门');
        return;
      }
      
      this.loading = true;
      
      // 获取选中的团队ID
      const teamId = this.joinDepartmentDialog.form.teamId;
      const departmentName = this.joinDepartmentDialog.form.department;
      
      if (!teamId) {
        // 如果没有找到对应的团队ID，使用部门名称创建新团队
        api.teams.create({
          name: departmentName,
          description: `${departmentName}部门`
        })
          .then(response => {
            const newTeam = response.data;
            // 加入新创建的团队
            return api.teams.joinTeam(newTeam.id, this.currentUser.id);
          })
          .then(() => {
            // 更新用户信息中的部门字段
            const userData = {
              ...this.currentUser,
              department: departmentName
            };
            return this.$store.dispatch('updateUserProfile', userData);
          })
          .then(() => {
            this.$message.success(`已成功加入${departmentName}部门`);
            this.joinDepartmentDialog.visible = false;
            // 刷新用户列表
            this.fetchUsers();
          })
          .catch(error => {
            console.error('加入部门失败:', error);
            this.$message.error('加入部门失败: ' + (error.message || '未知错误'));
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        // 如果找到了团队ID，直接申请加入
        api.teams.applyToJoinTeam(
          teamId, 
          this.joinDepartmentDialog.form.reason || '申请加入部门'
        )
          .then(() => {
          this.$message.success(`已成功提交加入${departmentName}申请，请等待管理员审核`);
            this.joinDepartmentDialog.visible = false;
            // 刷新用户列表
            this.fetchUsers();
          })
          .catch(error => {
            console.error('申请加入部门失败:', error);
          this.$message.error('申请加入部门失败: ' + (error.response?.data?.message || error.message || '未知错误'));
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    
    // 处理创建部门按钮点击
    handleCreateDepartment() {
      this.createDepartmentDialog.visible = true;
      this.createDepartmentDialog.form = {
        name: '',
        description: ''
      };
    },
    
    // 提交创建部门请求
    submitCreateDepartment() {
      this.$refs.createDepartmentForm.validate(valid => {
        if (valid) {
          this.loading = true;
          
          // 创建新部门/团队
          const teamData = {
            name: this.createDepartmentDialog.form.name,
            description: this.createDepartmentDialog.form.description || `${this.createDepartmentDialog.form.name}部门`
          };
          
          api.teams.create(teamData)
            .then(response => {
              const newTeam = response.data;
              // 将当前用户加入到新创建的团队
              return api.teams.joinTeam(newTeam.id, this.currentUser.id);
            })
            .then(() => {
              // 更新当前用户信息，将其部门设置为新创建的部门
              const userData = {
                ...this.currentUser,
                department: this.createDepartmentDialog.form.name
              };
              
              return this.$store.dispatch('updateUserProfile', userData);
            })
            .then(() => {
              this.$message.success(`已成功创建并加入${this.createDepartmentDialog.form.name}部门`);
              this.createDepartmentDialog.visible = false;
              
              // 添加新部门到部门列表
              if (!this.departments.includes(this.createDepartmentDialog.form.name)) {
                this.departments.push(this.createDepartmentDialog.form.name);
              }
              
              // 刷新用户列表
              this.fetchUsers();
            })
            .catch(error => {
              console.error('创建部门失败:', error);
              this.$message.error('创建部门失败: ' + (error.message || '未知错误'));
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
    // 获取权限升级申请
    fetchReviewerApplications() {
      if (!this.isAdmin) return;
      
      this.loading = true;
      api.users.getPendingReviewerApplications()
        .then(response => {
          this.reviewerApplications = response.data || [];
        })
        .catch(error => {
          console.error('获取权限升级申请失败:', error);
          this.$message.error('获取权限升级申请失败: ' + (error.message || '未知错误'));
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // 添加查看权限申请的按钮点击
    viewReviewerApplications() {
      // 跳转到审核医生申请页面
      this.$router.push('/admin/reviewer-applications');
    },
    
    // 检查用户是否有待处理的权限升级申请
    hasReviewerApplication(userId) {
      return this.reviewerApplications.some(app => app.userId === userId);
    },
    
    // 处理升级权限
    handleUpgradeRole(user) {
      this.$confirm(`确定将 ${user.name} 的角色升级为审核医生吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 显示备注输入框
        this.$prompt('请输入批准备注（可选）', '批准权限升级', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入备注信息'
        }).then(({ value }) => {
          this.loading = true;
          
          // 调用API处理权限升级
          api.users.processReviewerApplication(user.id, {
            approved: true,
            remarks: value || '管理员批准'
          })
            .then(() => {
              this.$message.success(`已成功将 ${user.name} 升级为审核医生`);
              // 刷新用户列表和申请列表
              this.fetchUsers();
              this.fetchReviewerApplications();
            })
            .catch(error => {
              console.error('升级权限失败:', error);
              this.$message.error('升级权限失败: ' + (error.response?.data?.message || error.message || '未知错误'));
            })
            .finally(() => {
              this.loading = false;
            });
        }).catch(() => {
          // 用户取消输入备注，不做处理
        });
      }).catch(() => {
        // 用户取消操作，不做处理
      });
    },
  }
}
</script>

<style>
.users-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.filter-bar {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 15px;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.required-field {
  color: #F56C6C;
  position: absolute;
  left: -10px;
  top: 5px;
}

.search-wrapper {
  padding: 10px 0;
}

.search-results {
  margin-top: 10px;
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: center;
}

:deep(.el-table--border) {
  border-radius: 4px;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.full-width {
  width: 100%;
}

.form-content {
  margin-top: 15px;
}

.form-item {
  position: relative;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.label {
  width: 80px;
  text-align: right;
  margin-right: 10px;
  font-size: 14px;
}

.status-item .status-text {
  margin-left: 10px;
  color: #606266;
  font-size: 14px;
}

.hint {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style> 