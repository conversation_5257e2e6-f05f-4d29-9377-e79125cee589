# 软著后端代码更新说明

## 更新概述
根据项目的最新开发进展，我们对软著后端代码文件进行了全面更新，使其与当前的代码库保持一致。主要更新包括血管瘤诊断模型的完善、AI服务的集成、以及最新的控制器功能。

## 主要更新内容

### 1. **新增HemangiomaDiagnosis模型类**

#### 1.1 完整的实体模型
```java
@Entity
@Table(name = "hemangioma_diagnoses")
public class HemangiomaDiagnosis {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    // 基本患者信息
    private Integer patientAge;
    private String gender;
    private String originType;
    private String vesselTexture;
    private String bodyPart;
    private String color;
    
    // AI检测结果
    @Column(name = "detected_type")
    private String detectedType;
    
    @Column(name = "confidence")
    private Double confidence;
    
    @Column(name = "processed_image_path")
    private String processedImagePath;
    
    // LLM生成的结构化建议
    @Column(columnDefinition = "TEXT")
    private String diagnosticSummary;
    
    @Column(columnDefinition = "TEXT")
    private String treatmentSuggestion;
    
    @Column(columnDefinition = "TEXT")
    private String precautions;
    
    @Column(columnDefinition = "TEXT")
    private String disclaimer;
    
    // 状态管理
    @Enumerated(EnumType.STRING)
    private Status status = Status.DRAFT;
    
    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @OneToMany(mappedBy = "hemangiomaDiagnosis", cascade = CascadeType.ALL)
    private List<Tag> tags = new ArrayList<>();
}
```

#### 1.2 状态枚举
```java
public enum Status {
    DRAFT,      // 草稿
    SUBMITTED,  // 已提交
    REVIEWED,   // 已审核
    APPROVED,   // 已批准
    REJECTED    // 已拒绝
}
```

#### 1.3 标注数据支持
```java
// 标注数据和边界框
@Column(name = "annotation_data")
private String annotationData;

@ElementCollection
@CollectionTable(name = "diagnosis_bounding_boxes")
@Column(name = "bounding_box", columnDefinition="TEXT")
private List<String> boundingBoxes = new ArrayList<>();
```

### 2. **完整的AI服务集成**

#### 2.1 三大类血管瘤分类体系
```python
HEMANGIOMA_CATEGORIES = {
    "真性血管肿瘤": {
        "IH": "婴幼儿血管瘤",
        "RICH": "先天性快速消退型血管瘤",
        "PICH": "先天性部分消退型血管瘤",
        "NICH": "先天性不消退型血管瘤",
        "KHE": "卡波西型血管内皮细胞瘤",
        "TA": "丛状血管瘤",
        "PG": "化脓性肉芽肿",
        # ... 更多类型
    },
    "血管畸形": {
        "MVM": "微静脉畸形",
        "VM": "静脉畸形",
        "AVM": "动静脉畸形",
        "LM": "淋巴管畸形",
        # ... 更多类型
    },
    "血管假瘤/易混淆病变": {
        "HPC": "血管外皮细胞瘤",
        "GT": "血管球瘤",
        "AL": "血管平滑肌瘤",
        # ... 更多类型
    }
}
```

#### 2.2 YOLO目标检测
```python
def detect_objects_yolo(image):
    """使用YOLO模型进行目标检测"""
    if not yolo_model:
        raise HTTPException(status_code=500, detail="YOLO模型未加载")
    
    # 进行检测
    results = yolo_model(image)
    
    boxes_data = []
    detected_class_names = set()
    max_confidence = 0.0
    
    # 处理检测结果
    for result in results:
        boxes = result.boxes
        if boxes is not None:
            for box in boxes:
                # 提取边界框信息
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                confidence = float(box.conf[0].cpu().numpy())
                class_id = int(box.cls[0].cpu().numpy())
                
                # 构建检测结果
                boxes_data.append(DetectionBox(...))
    
    return boxes_data, detected_class_names, max_confidence, results
```

#### 2.3 LLM诊断建议生成
```python
async def generate_diagnosis_with_llm(
    patient_age, gender, origin_type, vessel_texture, 
    detected_types, confidence
) -> DiagnosisResponse:
    """使用LLM生成诊断建议"""
    
    # 构建专业提示词
    prompt = f"""
作为一名专业的血管瘤诊断医生，请根据以下信息提供详细的诊断建议：

患者信息：
- 年龄：{patient_age if patient_age else '未提供'}
- 性别：{gender if gender else '未提供'}
- 病变起源：{origin_type if origin_type else '未提供'}
- 血管触感：{vessel_texture if vessel_texture else '未提供'}

AI检测结果：
- 检测到的血管瘤类型：{detected_types}
- 检测置信度：{confidence:.2f}

请提供以下四个方面的建议，并以JSON格式返回：
1. diagnostic_summary: 诊断总结
2. treatment_suggestion: 治疗建议
3. precautions: 预防措施和日常护理建议
4. disclaimer: 医疗免责声明
"""
    
    # 调用Ollama LLM
    response = ollama.chat(model=LLM_MODEL_NAME, messages=[...])
    
    # 解析并返回结构化结果
    return DiagnosisResponse(...)
```

#### 2.4 异步后台任务
```python
async def run_llm_and_update_in_background(
    diagnosis_id, patient_age, gender, origin_type, 
    vessel_texture, confidence, detected_types, 
    predicted_color, predicted_part
):
    """后台任务：生成LLM诊断建议并回调Java后端更新数据库"""
    
    # 生成诊断建议
    diagnosis = await generate_diagnosis_with_llm(...)
    
    # 准备回调数据
    callback_data = {
        "diagnosticSummary": diagnosis.diagnostic_summary,
        "treatmentSuggestion": diagnosis.treatment_suggestion,
        "precautions": diagnosis.precautions,
        "disclaimer": diagnosis.disclaimer,
        "predictedColor": predicted_color,
        "predictedPart": predicted_part
    }
    
    # 回调Java后端
    async with httpx.AsyncClient() as client:
        response = await client.put(
            f"{JAVA_CALLBACK_URL}/{diagnosis_id}",
            json=callback_data
        )
```

### 3. **API端点完善**

#### 3.1 快速YOLO检测端点
```python
@app.post("/diagnose-yolo", response_model=DetectionResult)
async def diagnose_yolo_only(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    diagnosis_id: int = Form(...),
    patient_age: Optional[int] = Form(None),
    gender: Optional[str] = Form(None),
    origin_type: Optional[str] = Form(None),
    vessel_texture: Optional[str] = Form(None)
):
    """仅进行YOLO检测的快速诊断接口"""
    
    # 1. 图像预处理
    # 2. YOLO检测
    # 3. 结果处理
    # 4. 保存处理后图像
    # 5. 启动后台LLM任务
    # 6. 返回检测结果
```

#### 3.2 诊断建议生成端点
```python
@app.post("/diagnose", response_model=DiagnosisResponse)
async def generate_diagnosis(request: DiagnosisRequest):
    """基于检测结果生成诊断建议"""
    
    diagnosis = await generate_diagnosis_with_llm(
        request.patient_age,
        request.gender,
        request.origin_type,
        request.vessel_texture,
        request.detected_types,
        request.confidence
    )
    
    return diagnosis
```

### 4. **数据模型完善**

#### 4.1 检测结果模型
```python
class DetectionBox(BaseModel):
    class_id: int
    class_name: str
    confidence: float
    bbox: List[int]
    width: int
    height: int
    orig_width: int
    orig_height: int

class DetectionResult(BaseModel):
    detected: bool
    boxes: List[DetectionBox]
    predicted_color: Optional[PredictedClass] = None
    predicted_part: Optional[PredictedClass] = None
    processed_image_path: str = ""
    message: str = ""
    detected_types: str = ""
    confidence: float = 0.0
```

#### 4.2 诊断请求和响应模型
```python
class DiagnosisRequest(BaseModel):
    patient_age: Optional[int] = None
    gender: Optional[str] = None
    origin_type: Optional[str] = None
    vessel_texture: Optional[str] = None
    detected_types: str
    confidence: float

class DiagnosisResponse(BaseModel):
    diagnostic_summary: str
    treatment_suggestion: str
    precautions: str
    disclaimer: str
```

## 技术改进

### 1. **智能化诊断流程**
- **AI检测**：YOLO模型自动识别血管瘤类型
- **LLM分析**：大语言模型生成专业诊断建议
- **异步处理**：后台任务确保响应速度
- **结构化输出**：标准化的诊断报告格式

### 2. **完整的分类体系**
- **三大类分类**：真性血管肿瘤、血管畸形、血管假瘤/易混淆病变
- **30种子类型**：覆盖临床常见的血管瘤类型
- **中英文映射**：支持缩写和全称的转换

### 3. **健壮的错误处理**
- **模型加载检查**：确保AI模型正确加载
- **异常容错**：LLM调用失败时提供默认建议
- **JSON解析修复**：自动修复格式错误的LLM响应

### 4. **高性能架构**
- **异步处理**：FastAPI异步框架
- **后台任务**：BackgroundTasks处理耗时操作
- **模型缓存**：启动时预加载模型
- **图像优化**：高质量图像处理和保存

## 文件结构

更新后的软著后端代码文件包含：

1. **Application.java** - Spring Boot应用入口
2. **WebConfig.java** - Web配置
3. **SecurityConfig.java** - 安全配置
4. **TagController.java** - 标签管理控制器
5. **UserController.java** - 用户管理控制器
6. **TeamController.java** - 团队管理控制器
7. **HemangiomaDiagnosisController.java** - 血管瘤诊断控制器
8. **HemangiomaDiagnosis.java** - 血管瘤诊断模型（新增）
9. **ai_service.py** - AI服务完整实现（新增）

## 总结

本次更新使软著后端代码文件与当前项目代码库完全同步，包含了：

- **完整的血管瘤诊断模型**：支持AI检测结果存储和LLM建议管理
- **先进的AI服务集成**：YOLO目标检测 + LLM诊断建议生成
- **三大类分类体系**：符合医学标准的血管瘤分类
- **异步处理架构**：高性能的后台任务处理
- **结构化诊断报告**：标准化的医疗建议输出

更新后的代码具有更强的功能性、更好的可维护性和更高的医疗专业性。
