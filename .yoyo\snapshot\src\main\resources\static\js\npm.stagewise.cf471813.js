"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[846],{13421:(e,t,n)=>{n.d(t,{G:()=>cw});var r,o,i,a,s,l,c,u,d,p,f,m,h=n(20641),g={},v=[],w=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,b=Array.isArray;function y(e,t){for(var n in t)e[n]=t[n];return e}function _(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function x(e,t,n){var o,i,a,s={};for(a in t)"key"==a?o=t[a]:"ref"==a?i=t[a]:s[a]=t[a];if(arguments.length>2&&(s.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)null==s[a]&&(s[a]=e.defaultProps[a]);return k(e,s,o,i,null)}function k(e,t,n,r,a){var s={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==a?++i:a,__i:-1,__u:0};return null==a&&null!=o.vnode&&o.vnode(s),s}function E(){return{current:null}}function C(e){return e.children}function S(e,t){this.props=e,this.context=t}function T(e,t){if(null==t)return e.__?T(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?T(e):null}function N(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return N(e)}}function P(e){(!e.__d&&(e.__d=!0)&&a.push(e)&&!A.__r++||s!=o.debounceRendering)&&((s=o.debounceRendering)||l)(A)}function A(){for(var e,t,n,r,i,s,l,u=1;a.length;)a.length>u&&a.sort(c),e=a.shift(),u=a.length,e.__d&&(n=void 0,i=(r=(t=e).__v).__e,s=[],l=[],t.__P&&((n=y({},r)).__v=r.__v+1,o.vnode&&o.vnode(n),D(t.__P,n,r,t.__n,t.__P.namespaceURI,32&r.__u?[i]:null,s,null==i?T(r):i,!!(32&r.__u),l),n.__v=r.__v,n.__.__k[n.__i]=n,$(s,n,l),n.__e!=i&&N(n)));A.__r=0}function I(e,t,n,r,o,i,a,s,l,c,u){var d,p,f,m,h,w,b=r&&r.__k||v,y=t.length;for(l=O(n,t,b,l,y),d=0;d<y;d++)null!=(f=n.__k[d])&&(p=-1==f.__i?g:b[f.__i]||g,f.__i=d,w=D(e,f,p,o,i,a,s,l,c,u),m=f.__e,f.ref&&p.ref!=f.ref&&(p.ref&&U(p.ref,null,f),u.push(f.ref,f.__c||m,f)),null==h&&null!=m&&(h=m),4&f.__u||p.__k===f.__k?l=R(f,l,e):"function"==typeof f.type&&void 0!==w?l=w:m&&(l=m.nextSibling),f.__u&=-7);return n.__e=h,l}function O(e,t,n,r,o){var i,a,s,l,c,u=n.length,d=u,p=0;for(e.__k=new Array(o),i=0;i<o;i++)null!=(a=t[i])&&"boolean"!=typeof a&&"function"!=typeof a?(l=i+p,(a=e.__k[i]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?k(null,a,null,null,null):b(a)?k(C,{children:a},null,null,null):null==a.constructor&&a.__b>0?k(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=e,a.__b=e.__b+1,s=null,-1!=(c=a.__i=z(a,n,l,d))&&(d--,(s=n[c])&&(s.__u|=2)),null==s||null==s.__v?(-1==c&&(o>u?p--:o<u&&p++),"function"!=typeof a.type&&(a.__u|=4)):c!=l&&(c==l-1?p--:c==l+1?p++:(c>l?p--:p++,a.__u|=4))):e.__k[i]=null;if(d)for(i=0;i<u;i++)null!=(s=n[i])&&0==(2&s.__u)&&(s.__e==r&&(r=T(s)),B(s,s));return r}function R(e,t,n){var r,o;if("function"==typeof e.type){for(r=e.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=e,t=R(r[o],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=T(e)),n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8==t.nodeType);return t}function M(e,t){return t=t||[],null==e||"boolean"==typeof e||(b(e)?e.some((function(e){M(e,t)})):t.push(e)),t}function z(e,t,n,r){var o,i,a=e.key,s=e.type,l=t[n];if(null===l&&null==e.key||l&&a==l.key&&s==l.type&&0==(2&l.__u))return n;if(r>(null!=l&&0==(2&l.__u)?1:0))for(o=n-1,i=n+1;o>=0||i<t.length;){if(o>=0){if((l=t[o])&&0==(2&l.__u)&&a==l.key&&s==l.type)return o;o--}if(i<t.length){if((l=t[i])&&0==(2&l.__u)&&a==l.key&&s==l.type)return i;i++}}return-1}function L(e,t,n){"-"==t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||w.test(t)?n:n+"px"}function j(e,t,n,r,o){var i;e:if("style"==t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||L(e.style,t,"");if(n)for(t in n)r&&n[t]==r[t]||L(e.style,t,n[t])}else if("o"==t[0]&&"n"==t[1])i=t!=(t=t.replace(u,"$1")),t=t.toLowerCase()in e||"onFocusOut"==t||"onFocusIn"==t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r?n.u=r.u:(n.u=d,e.addEventListener(t,i?f:p,i)):e.removeEventListener(t,i?f:p,i);else{if("http://www.w3.org/2000/svg"==o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(a){}"function"==typeof n||(null==n||!1===n&&"-"!=t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function F(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=d++;else if(t.t<n.u)return;return n(o.event?o.event(t):t)}}}function D(e,t,n,r,i,a,s,l,c,u){var d,p,f,m,h,g,v,w,x,k,E,T,N,P,A,O,R,M=t.type;if(null!=t.constructor)return null;128&n.__u&&(c=!!(32&n.__u),a=[l=t.__e=n.__e]),(d=o.__b)&&d(t);e:if("function"==typeof M)try{if(w=t.props,x="prototype"in M&&M.prototype.render,k=(d=M.contextType)&&r[d.__c],E=d?k?k.props.value:d.__:r,n.__c?v=(p=t.__c=n.__c).__=p.__E:(x?t.__c=p=new M(w,E):(t.__c=p=new S(w,E),p.constructor=M,p.render=V),k&&k.sub(p),p.props=w,p.state||(p.state={}),p.context=E,p.__n=r,f=p.__d=!0,p.__h=[],p._sb=[]),x&&null==p.__s&&(p.__s=p.state),x&&null!=M.getDerivedStateFromProps&&(p.__s==p.state&&(p.__s=y({},p.__s)),y(p.__s,M.getDerivedStateFromProps(w,p.__s))),m=p.props,h=p.state,p.__v=t,f)x&&null==M.getDerivedStateFromProps&&null!=p.componentWillMount&&p.componentWillMount(),x&&null!=p.componentDidMount&&p.__h.push(p.componentDidMount);else{if(x&&null==M.getDerivedStateFromProps&&w!==m&&null!=p.componentWillReceiveProps&&p.componentWillReceiveProps(w,E),!p.__e&&null!=p.shouldComponentUpdate&&!1===p.shouldComponentUpdate(w,p.__s,E)||t.__v==n.__v){for(t.__v!=n.__v&&(p.props=w,p.state=p.__s,p.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some((function(e){e&&(e.__=t)})),T=0;T<p._sb.length;T++)p.__h.push(p._sb[T]);p._sb=[],p.__h.length&&s.push(p);break e}null!=p.componentWillUpdate&&p.componentWillUpdate(w,p.__s,E),x&&null!=p.componentDidUpdate&&p.__h.push((function(){p.componentDidUpdate(m,h,g)}))}if(p.context=E,p.props=w,p.__P=e,p.__e=!1,N=o.__r,P=0,x){for(p.state=p.__s,p.__d=!1,N&&N(t),d=p.render(p.props,p.state,p.context),A=0;A<p._sb.length;A++)p.__h.push(p._sb[A]);p._sb=[]}else do{p.__d=!1,N&&N(t),d=p.render(p.props,p.state,p.context),p.state=p.__s}while(p.__d&&++P<25);p.state=p.__s,null!=p.getChildContext&&(r=y(y({},r),p.getChildContext())),x&&!f&&null!=p.getSnapshotBeforeUpdate&&(g=p.getSnapshotBeforeUpdate(m,h)),O=d,null!=d&&d.type===C&&null==d.key&&(O=Z(d.props.children)),l=I(e,b(O)?O:[O],t,n,r,i,a,s,l,c,u),p.base=t.__e,t.__u&=-161,p.__h.length&&s.push(p),v&&(p.__E=p.__=null)}catch(z){if(t.__v=null,c||null!=a)if(z.then){for(t.__u|=c?160:128;l&&8==l.nodeType&&l.nextSibling;)l=l.nextSibling;a[a.indexOf(l)]=null,t.__e=l}else for(R=a.length;R--;)_(a[R]);else t.__e=n.__e,t.__k=n.__k;o.__e(z,t,n)}else null==a&&t.__v==n.__v?(t.__k=n.__k,t.__e=n.__e):l=t.__e=H(n.__e,t,n,r,i,a,s,c,u);return(d=o.diffed)&&d(t),128&t.__u?void 0:l}function $(e,t,n){for(var r=0;r<n.length;r++)U(n[r],n[++r],n[++r]);o.__c&&o.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(n){o.__e(n,t.__v)}}))}function Z(e){return"object"!=typeof e||null==e||e.__b&&e.__b>0?e:b(e)?e.map(Z):y({},e)}function H(e,t,n,i,a,s,l,c,u){var d,p,f,m,h,v,w,y=n.props,x=t.props,k=t.type;if("svg"==k?a="http://www.w3.org/2000/svg":"math"==k?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=s)for(d=0;d<s.length;d++)if((h=s[d])&&"setAttribute"in h==!!k&&(k?h.localName==k:3==h.nodeType)){e=h,s[d]=null;break}if(null==e){if(null==k)return document.createTextNode(x);e=document.createElementNS(a,k,x.is&&x),c&&(o.__m&&o.__m(t,s),c=!1),s=null}if(null==k)y===x||c&&e.data==x||(e.data=x);else{if(s=s&&r.call(e.childNodes),y=n.props||g,!c&&null!=s)for(y={},d=0;d<e.attributes.length;d++)y[(h=e.attributes[d]).name]=h.value;for(d in y)if(h=y[d],"children"==d);else if("dangerouslySetInnerHTML"==d)f=h;else if(!(d in x)){if("value"==d&&"defaultValue"in x||"checked"==d&&"defaultChecked"in x)continue;j(e,d,null,h,a)}for(d in x)h=x[d],"children"==d?m=h:"dangerouslySetInnerHTML"==d?p=h:"value"==d?v=h:"checked"==d?w=h:c&&"function"!=typeof h||y[d]===h||j(e,d,h,y[d],a);if(p)c||f&&(p.__html==f.__html||p.__html==e.innerHTML)||(e.innerHTML=p.__html),t.__k=[];else if(f&&(e.innerHTML=""),I("template"==t.type?e.content:e,b(m)?m:[m],t,n,i,"foreignObject"==k?"http://www.w3.org/1999/xhtml":a,s,l,s?s[0]:n.__k&&T(n,0),c,u),null!=s)for(d=s.length;d--;)_(s[d]);c||(d="value","progress"==k&&null==v?e.removeAttribute("value"):null!=v&&(v!==e[d]||"progress"==k&&!v||"option"==k&&v!=y[d])&&j(e,d,v,y[d],a),d="checked",null!=w&&w!=e[d]&&j(e,d,w,y[d],a))}return e}function U(e,t,n){try{if("function"==typeof e){var r="function"==typeof e.__u;r&&e.__u(),r&&null==t||(e.__u=e(t))}else e.current=t}catch(i){o.__e(i,n)}}function B(e,t,n){var r,i;if(o.unmount&&o.unmount(e),(r=e.ref)&&(r.current&&r.current!=e.__e||U(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(a){o.__e(a,t)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&B(r[i],t,n||"function"!=typeof e.type);n||_(e.__e),e.__c=e.__=e.__e=void 0}function V(e,t,n){return this.constructor(e,n)}function q(e,t,n){var i,a,s,l;t==document&&(t=document.documentElement),o.__&&o.__(e,t),a=(i="function"==typeof n)?null:n&&n.__k||t.__k,s=[],l=[],D(t,e=(!i&&n||t).__k=x(C,null,[e]),a||g,g,t.namespaceURI,!i&&n?[n]:a?null:t.firstChild?r.call(t.childNodes):null,s,!i&&n?n:a?a.__e:t.firstChild,i,l),$(s,e,l)}function W(e,t){q(e,t,W)}function G(e,t,n){var o,i,a,s,l=y({},e.props);for(a in e.type&&e.type.defaultProps&&(s=e.type.defaultProps),t)"key"==a?o=t[a]:"ref"==a?i=t[a]:l[a]=null==t[a]&&null!=s?s[a]:t[a];return arguments.length>2&&(l.children=arguments.length>3?r.call(arguments,2):n),k(e.type,l,o||e.key,i||e.ref,null)}function K(e){function t(e){var n,r;return this.getChildContext||(n=new Set,(r={})[t.__c]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!=e.value&&n.forEach((function(e){e.__e=!0,P(e)}))},this.sub=function(e){n.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.delete(e),t&&t.call(e)}}),e.children}return t.__c="__cC"+m++,t.__=e,t.Provider=t.__l=(t.Consumer=function(e,t){return e.children(t)}).contextType=t,t}r=v.slice,o={__e:function(e,t,n,r){for(var o,i,a;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),a=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,r||{}),a=o.__d),a)return o.__E=o}catch(s){e=s}throw e}},i=0,S.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=y({},this.state),"function"==typeof e&&(e=e(y({},n),this.props)),e&&y(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),P(this))},S.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),P(this))},S.prototype.render=C,a=[],l="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,c=function(e,t){return e.__v.__b-t.__v.__b},A.__r=0,u=/(PointerCapture)$|Capture$/i,d=0,p=F(!1),f=F(!0),m=0;const Y='/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */\n@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){stagewise-companion-anchor *,stagewise-companion-anchor :before,stagewise-companion-anchor :after,stagewise-companion-anchor ::backdrop{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial}}}@layer theme{stagewise-companion-anchor,stagewise-companion-anchor{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-600:oklch(57.7% .245 27.325);--color-green-600:oklch(62.7% .194 149.214);--color-teal-500:oklch(70.4% .14 182.503);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-indigo-700:oklch(45.7% .24 277.023);--color-indigo-950:oklch(25.7% .09 281.288);--color-rose-600:oklch(58.6% .253 17.585);--color-zinc-50:oklch(98.5% 0 0);--color-zinc-100:oklch(96.7% .001 286.375);--color-zinc-500:oklch(55.2% .016 285.938);--color-zinc-600:oklch(44.2% .017 285.786);--color-zinc-700:oklch(37% .013 285.805);--color-zinc-900:oklch(21% .006 285.885);--color-zinc-950:oklch(14.1% .005 285.823);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-sm:24rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height:calc(1.5/1);--font-weight-normal:400;--font-weight-semibold:600;--font-weight-bold:700;--radius-md:.375rem;--radius-lg:.5rem;--radius-xl:.75rem;--radius-2xl:1rem;--radius-3xl:1.5rem;--drop-shadow-xs:0 1px 1px #0000000d;--drop-shadow-md:0 3px 3px #0000001f;--drop-shadow-xl:0 9px 7px #0000001a;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--blur-xs:4px;--blur-sm:8px;--blur-md:12px;--blur-lg:16px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-background:var(--color-white);--color-foreground:var(--color-zinc-950);--color-muted-foreground:var(--color-zinc-700);--color-border:var(--color-zinc-500)}}@layer base{stagewise-companion-anchor *,stagewise-companion-anchor :after,stagewise-companion-anchor :before,stagewise-companion-anchor ::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}stagewise-companion-anchor ::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}:where(stagewise-companion-anchor),stagewise-companion-anchor{-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}stagewise-companion-anchor hr{height:0;color:inherit;border-top-width:1px}stagewise-companion-anchor abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}stagewise-companion-anchor h1,stagewise-companion-anchor h2,stagewise-companion-anchor h3,stagewise-companion-anchor h4,stagewise-companion-anchor h5,stagewise-companion-anchor h6{font-size:inherit;font-weight:inherit}stagewise-companion-anchor a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}stagewise-companion-anchor b,stagewise-companion-anchor strong{font-weight:bolder}stagewise-companion-anchor code,stagewise-companion-anchor kbd,stagewise-companion-anchor samp,stagewise-companion-anchor pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}stagewise-companion-anchor small{font-size:80%}stagewise-companion-anchor sub,stagewise-companion-anchor sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}stagewise-companion-anchor sub{bottom:-.25em}stagewise-companion-anchor sup{top:-.5em}stagewise-companion-anchor table{text-indent:0;border-color:inherit;border-collapse:collapse}stagewise-companion-anchor :-moz-focusring{outline:auto}stagewise-companion-anchor progress{vertical-align:baseline}stagewise-companion-anchor summary{display:list-item}stagewise-companion-anchor ol,stagewise-companion-anchor ul,stagewise-companion-anchor menu{list-style:none}stagewise-companion-anchor img,stagewise-companion-anchor svg,stagewise-companion-anchor video,stagewise-companion-anchor canvas,stagewise-companion-anchor audio,stagewise-companion-anchor iframe,stagewise-companion-anchor embed,stagewise-companion-anchor object{vertical-align:middle;display:block}stagewise-companion-anchor img,stagewise-companion-anchor video{max-width:100%;height:auto}stagewise-companion-anchor button,stagewise-companion-anchor input,stagewise-companion-anchor select,stagewise-companion-anchor optgroup,stagewise-companion-anchor textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor ::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup{font-weight:bolder}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}stagewise-companion-anchor ::file-selector-button{margin-inline-end:4px}stagewise-companion-anchor ::-moz-placeholder{opacity:1}stagewise-companion-anchor ::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){stagewise-companion-anchor ::-moz-placeholder{color:currentColor}stagewise-companion-anchor ::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor ::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}stagewise-companion-anchor ::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}stagewise-companion-anchor textarea{resize:vertical}stagewise-companion-anchor ::-webkit-search-decoration{-webkit-appearance:none}stagewise-companion-anchor ::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}stagewise-companion-anchor ::-webkit-datetime-edit{display:inline-flex}stagewise-companion-anchor ::-webkit-datetime-edit-fields-wrapper{padding:0}stagewise-companion-anchor ::-webkit-datetime-edit{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-year-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-month-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-day-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-hour-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-minute-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-second-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-millisecond-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-meridiem-field{padding-block:0}stagewise-companion-anchor :-moz-ui-invalid{box-shadow:none}stagewise-companion-anchor button,stagewise-companion-anchor input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::-webkit-inner-spin-button{height:auto}stagewise-companion-anchor ::-webkit-outer-spin-button{height:auto}stagewise-companion-anchor [hidden]:where(:not([hidden=until-found])){display:none!important}stagewise-companion-anchor stagewise-companion-anchor *{min-width:0;min-height:0;position:relative}}@layer components;@layer utilities{stagewise-companion-anchor .pointer-events-auto{pointer-events:auto!important}stagewise-companion-anchor .pointer-events-none{pointer-events:none!important}stagewise-companion-anchor .visible{visibility:visible!important}stagewise-companion-anchor .absolute{position:absolute!important}stagewise-companion-anchor .fixed{position:fixed!important}stagewise-companion-anchor .relative{position:relative!important}stagewise-companion-anchor .inset-0{inset:calc(var(--spacing)*0)!important}stagewise-companion-anchor .inset-4{inset:calc(var(--spacing)*4)!important}stagewise-companion-anchor .top-0{top:calc(var(--spacing)*0)!important}stagewise-companion-anchor .top-1\\/2{top:50%!important}stagewise-companion-anchor .top-\\[-20\\%\\]{top:-20%!important}stagewise-companion-anchor .top-\\[25\\%\\]{top:25%!important}stagewise-companion-anchor .-right-1{right:calc(var(--spacing)*-1)!important}stagewise-companion-anchor .right-0{right:calc(var(--spacing)*0)!important}stagewise-companion-anchor .right-1\\/2{right:50%!important}stagewise-companion-anchor .right-2{right:calc(var(--spacing)*2)!important}stagewise-companion-anchor .-bottom-0\\.5{bottom:calc(var(--spacing)*-.5)!important}stagewise-companion-anchor .bottom-1\\/2{bottom:50%!important}stagewise-companion-anchor .bottom-3{bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-0{left:calc(var(--spacing)*0)!important}stagewise-companion-anchor .left-1\\/2{left:50%!important}stagewise-companion-anchor .left-3{left:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-\\[-10\\%\\]{left:-10%!important}stagewise-companion-anchor .left-\\[25\\%\\]{left:25%!important}stagewise-companion-anchor .z-50{z-index:50!important}stagewise-companion-anchor .container{width:100%!important}@media (min-width:40rem){stagewise-companion-anchor .container{max-width:40rem!important}}@media (min-width:48rem){stagewise-companion-anchor .container{max-width:48rem!important}}@media (min-width:64rem){stagewise-companion-anchor .container{max-width:64rem!important}}@media (min-width:80rem){stagewise-companion-anchor .container{max-width:80rem!important}}@media (min-width:96rem){stagewise-companion-anchor .container{max-width:96rem!important}}stagewise-companion-anchor .block{display:block!important}stagewise-companion-anchor .contents{display:contents!important}stagewise-companion-anchor .flex{display:flex!important}stagewise-companion-anchor .hidden{display:none!important}stagewise-companion-anchor .inline{display:inline!important}stagewise-companion-anchor .aspect-square{aspect-ratio:1!important}stagewise-companion-anchor .size-0{width:calc(var(--spacing)*0)!important;height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .size-1\\.5{width:calc(var(--spacing)*1.5)!important;height:calc(var(--spacing)*1.5)!important}stagewise-companion-anchor .size-2\\/3{width:66.6667%!important;height:66.6667%!important}stagewise-companion-anchor .size-3{width:calc(var(--spacing)*3)!important;height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .size-4{width:calc(var(--spacing)*4)!important;height:calc(var(--spacing)*4)!important}stagewise-companion-anchor .size-5{width:calc(var(--spacing)*5)!important;height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .size-6{width:calc(var(--spacing)*6)!important;height:calc(var(--spacing)*6)!important}stagewise-companion-anchor .size-8{width:calc(var(--spacing)*8)!important;height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .size-9\\/12{width:75%!important;height:75%!important}stagewise-companion-anchor .size-12{width:calc(var(--spacing)*12)!important;height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .size-\\[120\\%\\]{width:120%!important;height:120%!important}stagewise-companion-anchor .size-full{width:100%!important;height:100%!important}stagewise-companion-anchor .h-4{height:calc(var(--spacing)*4)!important}stagewise-companion-anchor .h-5{height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .h-6{height:calc(var(--spacing)*6)!important}stagewise-companion-anchor .h-8{height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .h-\\[4\\.5em\\]{height:4.5em!important}stagewise-companion-anchor .h-\\[50\\%\\]{height:50%!important}stagewise-companion-anchor .h-\\[120\\%\\]{height:120%!important}stagewise-companion-anchor .h-auto{height:auto!important}stagewise-companion-anchor .h-fit{height:-moz-fit-content!important;height:fit-content!important}stagewise-companion-anchor .h-full{height:100%!important}stagewise-companion-anchor .h-screen{height:100vh!important}stagewise-companion-anchor .max-h-\\[50vh\\]{max-height:50vh!important}stagewise-companion-anchor .max-h-full{max-height:100%!important}stagewise-companion-anchor .w-0{width:calc(var(--spacing)*0)!important}stagewise-companion-anchor .w-6{width:calc(var(--spacing)*6)!important}stagewise-companion-anchor .w-80{width:calc(var(--spacing)*80)!important}stagewise-companion-anchor .w-96{width:calc(var(--spacing)*96)!important}stagewise-companion-anchor .w-\\[50\\%\\]{width:50%!important}stagewise-companion-anchor .w-fit{width:-moz-fit-content!important;width:fit-content!important}stagewise-companion-anchor .w-full{width:100%!important}stagewise-companion-anchor .w-max{width:-moz-max-content!important;width:max-content!important}stagewise-companion-anchor .w-screen{width:100vw!important}stagewise-companion-anchor .max-w-8{max-width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .max-w-48{max-width:calc(var(--spacing)*48)!important}stagewise-companion-anchor .max-w-90{max-width:calc(var(--spacing)*90)!important}stagewise-companion-anchor .max-w-\\[80\\%\\]{max-width:80%!important}stagewise-companion-anchor .max-w-\\[80vw\\]{max-width:80vw!important}stagewise-companion-anchor .max-w-sm{max-width:var(--container-sm)!important}stagewise-companion-anchor .min-w-4{min-width:calc(var(--spacing)*4)!important}stagewise-companion-anchor .min-w-24{min-width:calc(var(--spacing)*24)!important}stagewise-companion-anchor .flex-1{flex:1!important}stagewise-companion-anchor .flex-shrink-0,stagewise-companion-anchor .shrink-0{flex-shrink:0!important}stagewise-companion-anchor .origin-center{transform-origin:50%!important}stagewise-companion-anchor .transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)!important}stagewise-companion-anchor .animate-pulse{animation:var(--animate-pulse)!important}stagewise-companion-anchor .cursor-grab{cursor:grab!important}stagewise-companion-anchor .cursor-pointer{cursor:pointer!important}stagewise-companion-anchor .resize{resize:both!important}stagewise-companion-anchor .resize-none{resize:none!important}stagewise-companion-anchor .snap-start{scroll-snap-align:start!important}stagewise-companion-anchor .flex-col{flex-direction:column!important}stagewise-companion-anchor .flex-row{flex-direction:row!important}stagewise-companion-anchor .items-center{align-items:center!important}stagewise-companion-anchor .items-end{align-items:flex-end!important}stagewise-companion-anchor .items-start{align-items:flex-start!important}stagewise-companion-anchor .items-stretch{align-items:stretch!important}stagewise-companion-anchor .justify-between{justify-content:space-between!important}stagewise-companion-anchor .justify-center{justify-content:center!important}stagewise-companion-anchor .justify-end{justify-content:flex-end!important}stagewise-companion-anchor .justify-start{justify-content:flex-start!important}stagewise-companion-anchor .gap-1{gap:calc(var(--spacing)*1)!important}stagewise-companion-anchor .gap-2{gap:calc(var(--spacing)*2)!important}stagewise-companion-anchor .gap-3{gap:calc(var(--spacing)*3)!important}stagewise-companion-anchor .truncate{text-overflow:ellipsis!important;white-space:nowrap!important;overflow:hidden!important}stagewise-companion-anchor .overflow-hidden{overflow:hidden!important}stagewise-companion-anchor .overflow-visible{overflow:visible!important}stagewise-companion-anchor .overflow-x-auto{overflow-x:auto!important}stagewise-companion-anchor .overflow-x-hidden{overflow-x:hidden!important}stagewise-companion-anchor .overflow-y-auto{overflow-y:auto!important}stagewise-companion-anchor .overflow-y-visible{overflow-y:visible!important}stagewise-companion-anchor .rounded{border-radius:.25rem!important}stagewise-companion-anchor .rounded-2xl{border-radius:var(--radius-2xl)!important}stagewise-companion-anchor .rounded-3xl{border-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-full{border-radius:3.40282e38px!important}stagewise-companion-anchor .rounded-lg{border-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-md{border-radius:var(--radius-md)!important}stagewise-companion-anchor .rounded-xl{border-radius:var(--radius-xl)!important}stagewise-companion-anchor .rounded-t-3xl{border-top-left-radius:var(--radius-3xl)!important;border-top-right-radius:var(--radius-3xl)!important}stagewise-companion-anchor .border{border-style:var(--tw-border-style)!important;border-width:1px!important}stagewise-companion-anchor .border-2{border-style:var(--tw-border-style)!important;border-width:2px!important}stagewise-companion-anchor .border-x{border-inline-style:var(--tw-border-style)!important;border-inline-width:1px!important}stagewise-companion-anchor .border-t{border-top-style:var(--tw-border-style)!important;border-top-width:1px!important}stagewise-companion-anchor .border-r{border-right-style:var(--tw-border-style)!important;border-right-width:1px!important}stagewise-companion-anchor .border-solid{--tw-border-style:solid!important;border-style:solid!important}stagewise-companion-anchor .border-blue-600\\/80{border-color:#155dfccc!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .border-blue-600\\/80{border-color:color-mix(in oklab,var(--color-blue-600)80%,transparent)!important}}stagewise-companion-anchor .border-border\\/10{border-color:#71717b1a!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .border-border\\/10{border-color:color-mix(in oklab,var(--color-border)10%,transparent)!important}}stagewise-companion-anchor .border-border\\/20{border-color:#71717b33!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .border-border\\/20{border-color:color-mix(in oklab,var(--color-border)20%,transparent)!important}}stagewise-companion-anchor .border-border\\/30{border-color:#71717b4d!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .border-border\\/30{border-color:color-mix(in oklab,var(--color-border)30%,transparent)!important}}stagewise-companion-anchor .border-green-600\\/80{border-color:#00a544cc!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .border-green-600\\/80{border-color:color-mix(in oklab,var(--color-green-600)80%,transparent)!important}}stagewise-companion-anchor .border-r-border\\/30{border-right-color:#71717b4d!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .border-r-border\\/30{border-right-color:color-mix(in oklab,var(--color-border)30%,transparent)!important}}stagewise-companion-anchor .border-l-transparent{border-left-color:#0000!important}stagewise-companion-anchor .bg-background\\/40{background-color:#fff6!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .bg-background\\/40{background-color:color-mix(in oklab,var(--color-background)40%,transparent)!important}}stagewise-companion-anchor .bg-background\\/60{background-color:#fff9!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .bg-background\\/60{background-color:color-mix(in oklab,var(--color-background)60%,transparent)!important}}stagewise-companion-anchor .bg-blue-600{background-color:var(--color-blue-600)!important}stagewise-companion-anchor .bg-blue-600\\/20{background-color:#155dfc33!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .bg-blue-600\\/20{background-color:color-mix(in oklab,var(--color-blue-600)20%,transparent)!important}}stagewise-companion-anchor .bg-green-600\\/5{background-color:#00a5440d!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .bg-green-600\\/5{background-color:color-mix(in oklab,var(--color-green-600)5%,transparent)!important}}stagewise-companion-anchor .bg-rose-600{background-color:var(--color-rose-600)!important}stagewise-companion-anchor .bg-transparent{background-color:#0000!important}stagewise-companion-anchor .bg-white\\/60{background-color:#fff9!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .bg-white\\/60{background-color:color-mix(in oklab,var(--color-white)60%,transparent)!important}}stagewise-companion-anchor .bg-zinc-50\\/80{background-color:#fafafacc!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .bg-zinc-50\\/80{background-color:color-mix(in oklab,var(--color-zinc-50)80%,transparent)!important}}stagewise-companion-anchor .bg-zinc-500{background-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-zinc-500\\/10{background-color:#71717b1a!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .bg-zinc-500\\/10{background-color:color-mix(in oklab,var(--color-zinc-500)10%,transparent)!important}}stagewise-companion-anchor .bg-zinc-600{background-color:var(--color-zinc-600)!important}stagewise-companion-anchor .bg-zinc-950\\/5{background-color:#09090b0d!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .bg-zinc-950\\/5{background-color:color-mix(in oklab,var(--color-zinc-950)5%,transparent)!important}}stagewise-companion-anchor .bg-gradient-to-tr{--tw-gradient-position:to top right in oklab!important;background-image:linear-gradient(var(--tw-gradient-stops))!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(55\\,48\\,163\\,0\\)_55\\%\\,rgba\\(55\\,48\\,163\\,0\\.35\\)_73\\%\\)\\]{background-image:radial-gradient(circle,#3730a300 55%,#3730a359 73%)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(219\\,39\\,119\\,0\\.2\\)_0\\%\\,rgba\\(219\\,39\\,119\\,0\\)_100\\%\\)\\]{background-image:radial-gradient(circle,#db277733 0%,#db277700 100%)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(255\\,255\\,255\\,0\\)_60\\%\\,rgba\\(255\\,255\\,255\\,0\\.2\\)_70\\%\\)\\]{background-image:radial-gradient(circle,#fff0 60%,#fff3 70%)!important}stagewise-companion-anchor .from-indigo-700{--tw-gradient-from:var(--color-indigo-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .via-blue-500{--tw-gradient-via:var(--color-blue-500)!important;--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position)!important;--tw-gradient-stops:var(--tw-gradient-via-stops)!important}stagewise-companion-anchor .to-teal-500{--tw-gradient-to:var(--color-teal-500)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .fill-current{fill:currentColor!important}stagewise-companion-anchor .fill-white{fill:var(--color-white)!important}stagewise-companion-anchor .fill-zinc-500\\/50{fill:#71717b80!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .fill-zinc-500\\/50{fill:color-mix(in oklab,var(--color-zinc-500)50%,transparent)!important}}stagewise-companion-anchor .fill-zinc-950{fill:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-black\\/30{stroke:#0000004d!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .stroke-black\\/30{stroke:color-mix(in oklab,var(--color-black)30%,transparent)!important}}stagewise-companion-anchor .stroke-none{stroke:none!important}stagewise-companion-anchor .stroke-1{stroke-width:1px!important}stagewise-companion-anchor .p-0{padding:calc(var(--spacing)*0)!important}stagewise-companion-anchor .p-0\\.5{padding:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .p-1{padding:calc(var(--spacing)*1)!important}stagewise-companion-anchor .p-1\\.5{padding:calc(var(--spacing)*1.5)!important}stagewise-companion-anchor .p-2{padding:calc(var(--spacing)*2)!important}stagewise-companion-anchor .p-3{padding:calc(var(--spacing)*3)!important}stagewise-companion-anchor .px-0\\.5{padding-inline:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .px-1{padding-inline:calc(var(--spacing)*1)!important}stagewise-companion-anchor .px-2{padding-inline:calc(var(--spacing)*2)!important}stagewise-companion-anchor .px-3{padding-inline:calc(var(--spacing)*3)!important}stagewise-companion-anchor .py-0{padding-block:calc(var(--spacing)*0)!important}stagewise-companion-anchor .py-1{padding-block:calc(var(--spacing)*1)!important}stagewise-companion-anchor .pt-2{padding-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pr-6{padding-right:calc(var(--spacing)*6)!important}stagewise-companion-anchor .pb-0{padding-bottom:calc(var(--spacing)*0)!important}stagewise-companion-anchor .pb-2{padding-bottom:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pl-2{padding-left:calc(var(--spacing)*2)!important}stagewise-companion-anchor .text-base{font-size:var(--text-base)!important;line-height:var(--tw-leading,var(--text-base--line-height))!important}stagewise-companion-anchor .text-sm{font-size:var(--text-sm)!important;line-height:var(--tw-leading,var(--text-sm--line-height))!important}stagewise-companion-anchor .text-xs{font-size:var(--text-xs)!important;line-height:var(--tw-leading,var(--text-xs--line-height))!important}stagewise-companion-anchor .font-bold{--tw-font-weight:var(--font-weight-bold)!important;font-weight:var(--font-weight-bold)!important}stagewise-companion-anchor .font-normal{--tw-font-weight:var(--font-weight-normal)!important;font-weight:var(--font-weight-normal)!important}stagewise-companion-anchor .font-semibold{--tw-font-weight:var(--font-weight-semibold)!important;font-weight:var(--font-weight-semibold)!important}stagewise-companion-anchor .text-border\\/60{color:#71717b99!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .text-border\\/60{color:color-mix(in oklab,var(--color-border)60%,transparent)!important}}stagewise-companion-anchor .text-foreground{color:var(--color-foreground)!important}stagewise-companion-anchor .text-muted-foreground{color:var(--color-muted-foreground)!important}stagewise-companion-anchor .text-muted-foreground\\/30{color:#3f3f464d!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .text-muted-foreground\\/30{color:color-mix(in oklab,var(--color-muted-foreground)30%,transparent)!important}}stagewise-companion-anchor .text-transparent{color:#0000!important}stagewise-companion-anchor .text-white{color:var(--color-white)!important}stagewise-companion-anchor .text-zinc-50{color:var(--color-zinc-50)!important}stagewise-companion-anchor .text-zinc-950{color:var(--color-zinc-950)!important}stagewise-companion-anchor .text-zinc-950\\/50{color:#09090b80!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .text-zinc-950\\/50{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}}stagewise-companion-anchor .opacity-20{opacity:.2!important}stagewise-companion-anchor .opacity-50{opacity:.5!important}stagewise-companion-anchor .opacity-80{opacity:.8!important}stagewise-companion-anchor .opacity-100{opacity:1!important}stagewise-companion-anchor .shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-inner{--tw-shadow:inset 0 2px 4px 0 var(--tw-shadow-color,#0000000d)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .ring-2{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:#00000080!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-black)50%,transparent)var(--tw-shadow-alpha),transparent)!important}}stagewise-companion-anchor .shadow-blue-600\\/50{--tw-shadow-color:#155dfc80!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .shadow-blue-600\\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-blue-600)50%,transparent)var(--tw-shadow-alpha),transparent)!important}}stagewise-companion-anchor .ring-blue-600{--tw-ring-color:var(--color-blue-600)!important}stagewise-companion-anchor .blur{--tw-blur:blur(8px)!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-md{--tw-drop-shadow-size:drop-shadow(0 3px 3px var(--tw-drop-shadow-color,#0000001f))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xl{--tw-drop-shadow-size:drop-shadow(0 9px 7px var(--tw-drop-shadow-color,#0000001a))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xl))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xs{--tw-drop-shadow-size:drop-shadow(0 1px 1px var(--tw-drop-shadow-color,#0000000d))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xs))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:#000!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:color-mix(in oklab,var(--color-black)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:oklch(25.7% .09 281.288)!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:color-mix(in oklab,var(--color-indigo-950)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .backdrop-blur-lg{--tw-backdrop-blur:blur(var(--blur-lg))!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-blur-md{--tw-backdrop-blur:blur(var(--blur-md))!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-blur-xs{--tw-backdrop-blur:blur(var(--blur-xs))!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-saturate-150{--tw-backdrop-saturate:saturate(150%)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-all{transition-property:all!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .duration-0{--tw-duration:0s!important;transition-duration:0s!important}stagewise-companion-anchor .duration-100{--tw-duration:.1s!important;transition-duration:.1s!important}stagewise-companion-anchor .duration-150{--tw-duration:.15s!important;transition-duration:.15s!important}stagewise-companion-anchor .duration-500{--tw-duration:.5s!important;transition-duration:.5s!important}stagewise-companion-anchor .outline-none{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .select-none{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:#09090b80!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:#09090b80!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:#09090bb3!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:#09090bb3!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}}stagewise-companion-anchor .first\\:border-none:first-child{--tw-border-style:none!important;border-style:none!important}stagewise-companion-anchor .first\\:pl-0:first-child{padding-left:calc(var(--spacing)*0)!important}stagewise-companion-anchor .last\\:border-r-transparent:last-child{border-right-color:#0000!important}stagewise-companion-anchor .last\\:pr-0:last-child{padding-right:calc(var(--spacing)*0)!important}@media (hover:hover){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:#e40014cc!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:color-mix(in oklab,var(--color-red-600)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:#e4001433!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:color-mix(in oklab,var(--color-red-600)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:#09090b0d!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:color-mix(in oklab,var(--color-zinc-950)5%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:#09090b1a!important}@supports (color:color-mix(in lab, red, red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:color-mix(in oklab,var(--color-zinc-950)10%,transparent)!important}}stagewise-companion-anchor .hover\\:text-muted-foreground:hover{color:var(--color-muted-foreground)!important}stagewise-companion-anchor .hover\\:text-white:hover{color:var(--color-white)!important}stagewise-companion-anchor .hover\\:opacity-100:hover{opacity:1!important}stagewise-companion-anchor .hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .hover\\:backdrop-blur-sm:hover{--tw-backdrop-blur:blur(var(--blur-sm))!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}}stagewise-companion-anchor .focus\\:cursor-grabbing:focus{cursor:grabbing!important}stagewise-companion-anchor .focus\\:text-zinc-900:focus{color:var(--color-zinc-900)!important}stagewise-companion-anchor .focus\\:outline-none:focus,stagewise-companion-anchor .data-focus\\:outline-none[data-focus]{--tw-outline-style:none!important;outline-style:none!important}}stagewise-companion-anchor stagewise-companion-anchor{all:initial;interpolate-size:allow-keywords;transform:translate(0);color:var(--color-zinc-950)!important;letter-spacing:normal!important;text-rendering:auto!important;font-family:Inter,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important;font-weight:400!important;line-height:normal!important}@supports (font-variation-settings:normal){stagewise-companion-anchor stagewise-companion-anchor{font-optical-sizing:auto!important;font-family:InterVariable,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important}}stagewise-companion-anchor #headlessui-portal-root{z-index:50!important;width:100vw!important;height:100vh!important;position:fixed!important}stagewise-companion-anchor #headlessui-portal-root>*{pointer-events:auto!important}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@keyframes pulse{50%{opacity:.5}}\n';var X=0;function J(e,t,n,r,i,a){t||(t={});var s,l,c=t;if("ref"in c)for(l in c={},t)"ref"==l?s=t[l]:c[l]=t[l];var u={type:e,props:c,key:n,ref:s,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--X,__i:-1,__u:0,__source:i,__self:a};if("function"==typeof e&&(s=e.defaultProps))for(l in s)void 0===c[l]&&(c[l]=s[l]);return o.vnode&&o.vnode(u),u}var Q,ee,te,ne,re=0,oe=[],ie=o,ae=ie.__b,se=ie.__r,le=ie.diffed,ce=ie.__c,ue=ie.unmount,de=ie.__;function pe(e,t){ie.__h&&ie.__h(ee,e,re||t),re=0;var n=ee.__H||(ee.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function fe(e){return re=1,me(Ie,e)}function me(e,t,n){var r=pe(Q++,2);if(r.t=e,!r.__c&&(r.__=[n?n(t):Ie(void 0,t),function(e){var t=r.__N?r.__N[0]:r.__[0],n=r.t(t,e);t!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=ee,!ee.__f)){var o=function(e,t,n){if(!r.__c.__H)return!0;var o=r.__c.__H.__.filter((function(e){return!!e.__c}));if(o.every((function(e){return!e.__N})))return!i||i.call(this,e,t,n);var a=r.__c.props!==e;return o.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(a=!0)}})),i&&i.call(this,e,t,n)||a};ee.__f=!0;var i=ee.shouldComponentUpdate,a=ee.componentWillUpdate;ee.componentWillUpdate=function(e,t,n){if(this.__e){var r=i;i=void 0,o(e,t,n),i=r}a&&a.call(this,e,t,n)},ee.shouldComponentUpdate=o}return r.__N||r.__}function he(e,t){var n=pe(Q++,3);!ie.__s&&Ae(n.__H,t)&&(n.__=e,n.u=t,ee.__H.__h.push(n))}function ge(e,t){var n=pe(Q++,4);!ie.__s&&Ae(n.__H,t)&&(n.__=e,n.u=t,ee.__h.push(n))}function ve(e){return re=5,be((function(){return{current:e}}),[])}function we(e,t,n){re=6,ge((function(){if("function"==typeof e){var n=e(t());return function(){e(null),n&&"function"==typeof n&&n()}}if(e)return e.current=t(),function(){return e.current=null}}),null==n?n:n.concat(e))}function be(e,t){var n=pe(Q++,7);return Ae(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function ye(e,t){return re=8,be((function(){return e}),t)}function _e(e){var t=ee.context[e.__c],n=pe(Q++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(ee)),t.props.value):e.__}function xe(e,t){ie.useDebugValue&&ie.useDebugValue(t?t(e):e)}function ke(e){var t=pe(Q++,10),n=fe();return t.__=e,ee.componentDidCatch||(ee.componentDidCatch=function(e,r){t.__&&t.__(e,r),n[1](e)}),[n[0],function(){n[1](void 0)}]}function Ee(){var e=pe(Q++,11);if(!e.__){for(var t=ee.__v;null!==t&&!t.__m&&null!==t.__;)t=t.__;var n=t.__m||(t.__m=[0,0]);e.__="P"+n[0]+"-"+n[1]++}return e.__}function Ce(){for(var e;e=oe.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Ne),e.__H.__h.forEach(Pe),e.__H.__h=[]}catch(t){e.__H.__h=[],ie.__e(t,e.__v)}}ie.__b=function(e){ee=null,ae&&ae(e)},ie.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),de&&de(e,t)},ie.__r=function(e){se&&se(e),Q=0;var t=(ee=e.__c).__H;t&&(te===ee?(t.__h=[],ee.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.u=e.__N=void 0}))):(t.__h.forEach(Ne),t.__h.forEach(Pe),t.__h=[],Q=0)),te=ee},ie.diffed=function(e){le&&le(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==oe.push(t)&&ne===ie.requestAnimationFrame||((ne=ie.requestAnimationFrame)||Te)(Ce)),t.__H.__.forEach((function(e){e.u&&(e.__H=e.u),e.u=void 0}))),te=ee=null},ie.__c=function(e,t){t.some((function(e){try{e.__h.forEach(Ne),e.__h=e.__h.filter((function(e){return!e.__||Pe(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],ie.__e(n,e.__v)}})),ce&&ce(e,t)},ie.unmount=function(e){ue&&ue(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{Ne(e)}catch(n){t=n}})),n.__H=void 0,t&&ie.__e(t,n.__v))};var Se="function"==typeof requestAnimationFrame;function Te(e){var t,n=function(){clearTimeout(r),Se&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);Se&&(t=requestAnimationFrame(n))}function Ne(e){var t=ee,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),ee=t}function Pe(e){var t=ee;e.__c=e.__(),ee=t}function Ae(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function Ie(e,t){return"function"==typeof t?t(e):t}const Oe=K([]);function Re({children:e,plugins:t}){return J(Oe.Provider,{value:t,children:e})}function Me(){const e=_e(Oe);return e||[]}function ze(e,t){for(var n in t)e[n]=t[n];return e}function Le(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function je(e,t){var n=t(),r=fe({t:{__:n,u:t}}),o=r[0].t,i=r[1];return ge((function(){o.__=n,o.u=t,Fe(o)&&i({t:o})}),[e,n,t]),he((function(){return Fe(o)&&i({t:o}),e((function(){Fe(o)&&i({t:o})}))}),[e]),n}function Fe(e){var t,n,r=e.u,o=e.__;try{var i=r();return!((t=o)===(n=i)&&(0!==t||1/t==1/n)||t!=t&&n!=n)}catch(a){return!0}}function De(e){e()}function $e(e){return e}function Ze(){return[!1,De]}var He=ge;function Ue(e,t){this.props=e,this.context=t}function Be(e,t){function n(e){var n=this.props.ref,r=n==e.ref;return!r&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!r:Le(this.props,e)}function r(t){return this.shouldComponentUpdate=n,x(e,t)}return r.displayName="Memo("+(e.displayName||e.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r}(Ue.prototype=new S).isPureReactComponent=!0,Ue.prototype.shouldComponentUpdate=function(e,t){return Le(this.props,e)||Le(this.state,t)};var Ve=o.__b;o.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),Ve&&Ve(e)};var qe="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function We(e){function t(t){var n=ze({},t);return delete n.ref,e(n,t.ref||null)}return t.$$typeof=qe,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var Ge=function(e,t){return null==e?null:M(M(e).map(t))},Ke={map:Ge,forEach:Ge,count:function(e){return e?M(e).length:0},only:function(e){var t=M(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:M},Ye=o.__e;o.__e=function(e,t,n,r){if(e.then)for(var o,i=t;i=i.__;)if((o=i.__c)&&o.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),o.__c(e,t);Ye(e,t,n,r)};var Xe=o.unmount;function Je(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),e.__c.__H=null),null!=(e=ze({},e)).__c&&(e.__c.__P===n&&(e.__c.__P=t),e.__c.__e=!0,e.__c=null),e.__k=e.__k&&e.__k.map((function(e){return Je(e,t,n)}))),e}function Qe(e,t,n){return e&&n&&(e.__v=null,e.__k=e.__k&&e.__k.map((function(e){return Qe(e,t,n)})),e.__c&&e.__c.__P===t&&(e.__e&&n.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=n)),e}function et(){this.__u=0,this.o=null,this.__b=null}function tt(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function nt(e){var t,n,r;function o(o){if(t||(t=e()).then((function(e){n=e.default||e}),(function(e){r=e})),r)throw r;if(!n)throw t;return x(n,o)}return o.displayName="Lazy",o.__f=!0,o}function rt(){this.i=null,this.l=null}o.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),Xe&&Xe(e)},(et.prototype=new S).__c=function(e,t){var n=t.__c,r=this;null==r.o&&(r.o=[]),r.o.push(n);var o=tt(r.__v),i=!1,a=function(){i||(i=!0,n.__R=null,o?o(s):s())};n.__R=a;var s=function(){if(! --r.__u){if(r.state.__a){var e=r.state.__a;r.__v.__k[0]=Qe(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.o.pop();)t.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(a,a)},et.prototype.componentWillUnmount=function(){this.o=[]},et.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=Je(this.__b,n,r.__O=r.__P)}this.__b=null}var o=t.__a&&x(C,null,e.fallback);return o&&(o.__u&=-33),[x(C,null,t.__a?null:e.children),o]};var ot=function(e,t,n){if(++n[1]===n[0]&&e.l.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.l.size))for(n=e.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.i=n=n[2]}};function it(e){return this.getChildContext=function(){return e.context},e.children}function at(e){var t=this,n=e.h;if(t.componentWillUnmount=function(){q(null,t.v),t.v=null,t.h=null},t.h&&t.h!==n&&t.componentWillUnmount(),!t.v){for(var r=t.__v;null!==r&&!r.__m&&null!==r.__;)r=r.__;t.h=n,t.v={nodeType:1,parentNode:n,childNodes:[],__k:{__m:r.__m},contains:function(){return!0},appendChild:function(e){this.childNodes.push(e),t.h.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.h.insertBefore(e,n)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.h.removeChild(e)}}}q(x(it,{context:t.context},e.__v),t.v)}function st(e,t){var n=x(at,{__v:e,h:t});return n.containerInfo=t,n}(rt.prototype=new S).__a=function(e){var t=this,n=tt(t.__v),r=t.l.get(e);return r[0]++,function(o){var i=function(){t.props.revealOrder?(r.push(o),ot(t,e,r)):o()};n?n(i):i()}},rt.prototype.render=function(e){this.i=null,this.l=new Map;var t=M(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.l.set(t[n],this.i=[1,0,this.i]);return e.children},rt.prototype.componentDidUpdate=rt.prototype.componentDidMount=function(){var e=this;this.l.forEach((function(t,n){ot(e,n,t)}))};var lt="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,ct=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,ut=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,dt=/[A-Z0-9]/g,pt="undefined"!=typeof document,ft=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};function mt(e,t,n){return null==t.__k&&(t.textContent=""),q(e,t),"function"==typeof n&&n(),e?e.__c:null}function ht(e,t,n){return W(e,t),"function"==typeof n&&n(),e?e.__c:null}S.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(S.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var gt=o.event;function vt(){}function wt(){return this.cancelBubble}function bt(){return this.defaultPrevented}o.event=function(e){return gt&&(e=gt(e)),e.persist=vt,e.isPropagationStopped=wt,e.isDefaultPrevented=bt,e.nativeEvent=e};var yt,_t={enumerable:!1,configurable:!0,get:function(){return this.class}},xt=o.vnode;o.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,n=e.type,r={},o=-1===n.indexOf("-");for(var i in t){var a=t[i];if(!("value"===i&&"defaultValue"in t&&null==a||pt&&"children"===i&&"noscript"===n||"class"===i||"className"===i)){var s=i.toLowerCase();"defaultValue"===i&&"value"in t&&null==t.value?i="value":"download"===i&&!0===a?a="":"translate"===s&&"no"===a?a=!1:"o"===s[0]&&"n"===s[1]?"ondoubleclick"===s?i="ondblclick":"onchange"!==s||"input"!==n&&"textarea"!==n||ft(t.type)?"onfocus"===s?i="onfocusin":"onblur"===s?i="onfocusout":ut.test(i)&&(i=s):s=i="oninput":o&&ct.test(i)?i=i.replace(dt,"-$&").toLowerCase():null===a&&(a=void 0),"oninput"===s&&r[i=s]&&(i="oninputCapture"),r[i]=a}}"select"==n&&r.multiple&&Array.isArray(r.value)&&(r.value=M(t.children).forEach((function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)}))),"select"==n&&null!=r.defaultValue&&(r.value=M(t.children).forEach((function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value}))),t.class&&!t.className?(r.class=t.class,Object.defineProperty(r,"className",_t)):(t.className&&!t.class||t.class&&t.className)&&(r.class=r.className=t.className),e.props=r}(e),e.$$typeof=lt,xt&&xt(e)};var kt=o.__r;o.__r=function(e){kt&&kt(e),yt=e.__c};var Et=o.diffed;o.diffed=function(e){Et&&Et(e);var t=e.props,n=e.__e;null!=n&&"textarea"===e.type&&"value"in t&&t.value!==n.value&&(n.value=null==t.value?"":t.value),yt=null};var Ct={ReactCurrentDispatcher:{current:{readContext:function(e){return yt.__n[e.__c].props.value},useCallback:ye,useContext:_e,useDebugValue:xe,useDeferredValue:$e,useEffect:he,useId:Ee,useImperativeHandle:we,useInsertionEffect:He,useLayoutEffect:ge,useMemo:be,useReducer:me,useRef:ve,useState:fe,useSyncExternalStore:je,useTransition:Ze}}},St="18.3.1";function Tt(e){return x.bind(null,e)}function Nt(e){return!!e&&e.$$typeof===lt}function Pt(e){return Nt(e)&&e.type===C}function At(e){return!!e&&!!e.displayName&&("string"==typeof e.displayName||e.displayName instanceof String)&&e.displayName.startsWith("Memo(")}function It(e){return Nt(e)?G.apply(null,arguments):e}function Ot(e){return!!e.__k&&(q(null,e),!0)}function Rt(e){return e&&(e.base||1===e.nodeType&&e)||null}var Mt=function(e,t){return e(t)},zt=function(e,t){return e(t)},Lt=C,jt=Nt,Ft={useState:fe,useId:Ee,useReducer:me,useEffect:he,useLayoutEffect:ge,useInsertionEffect:He,useTransition:Ze,useDeferredValue:$e,useSyncExternalStore:je,startTransition:De,useRef:ve,useImperativeHandle:we,useMemo:be,useCallback:ye,useContext:_e,useDebugValue:xe,version:"18.3.1",Children:Ke,render:mt,hydrate:ht,unmountComponentAtNode:Ot,createPortal:st,createElement:x,createContext:K,createFactory:Tt,cloneElement:It,createRef:E,Fragment:C,isValidElement:Nt,isElement:jt,isFragment:Pt,isMemo:At,findDOMNode:Rt,Component:S,PureComponent:Ue,memo:Be,forwardRef:We,flushSync:zt,unstable_batchedUpdates:Mt,StrictMode:Lt,Suspense:et,SuspenseList:rt,lazy:nt,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Ct};const Dt=Object.freeze(Object.defineProperty({__proto__:null,Children:Ke,Component:S,Fragment:C,PureComponent:Ue,StrictMode:Lt,Suspense:et,SuspenseList:rt,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Ct,cloneElement:It,createContext:K,createElement:x,createFactory:Tt,createPortal:st,createRef:E,default:Ft,findDOMNode:Rt,flushSync:zt,forwardRef:We,hydrate:ht,isElement:jt,isFragment:Pt,isMemo:At,isValidElement:Nt,lazy:nt,memo:Be,render:mt,startTransition:De,unmountComponentAtNode:Ot,unstable_batchedUpdates:Mt,useCallback:ye,useContext:_e,useDebugValue:xe,useDeferredValue:$e,useEffect:he,useErrorBoundary:ke,useId:Ee,useImperativeHandle:we,useInsertionEffect:He,useLayoutEffect:ge,useMemo:be,useReducer:me,useRef:ve,useState:fe,useSyncExternalStore:je,useTransition:Ze,version:St},Symbol.toStringTag,{value:"Module"}));var $t=(e=>"undefined"!==typeof require?require:"undefined"!==typeof Proxy?new Proxy(e,{get:(e,t)=>("undefined"!==typeof require?require:e)[t]}):e)((function(e){if("undefined"!==typeof require)return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')})),Zt={maxReconnectAttempts:5,reconnectDelay:1e3,requestTimeout:3e4},Ht=class{constructor(e={}){this.ws=null,this.pendingRequests=new Map,this.reconnectAttempts=0,this.methods={},this.isIntentionalClose=!1,this.options={...Zt,...e}}register(e){Object.entries(e).forEach((([e,t])=>{this.methods[e]={handler:t}}))}callMethod(e,t,n){if(!this.ws)throw new Error("WebSocket is not connected");const r=crypto.randomUUID(),o={id:r,messageType:"request",method:e,payload:t};return new Promise(((t,i)=>{var a;const s=setTimeout((()=>{this.pendingRequests.delete(r),i(new Error(`Request timed out: ${e}`))}),this.options.requestTimeout);this.pendingRequests.set(r,{resolve:t,reject:i,timeout:s,onUpdate:n}),null==(a=this.ws)||a.send(JSON.stringify(o))}))}setupWebSocketHandlers(e){e.onmessage=e=>{try{const t=JSON.parse(e.data);this.handleMessage(t)}catch(t){}},e.onclose=()=>{this.handleDisconnect()},e.onerror=e=>{}}handleMessage(e){const{messageType:t,id:n}=e;switch(t){case"request":this.handleRequest(e);break;case"response":this.handleResponse(n,e.payload);break;case"update":this.handleUpdate(n,e.payload);break;case"error":this.handleError(n,e.error.message);break;default:}}async handleRequest(e){const{id:t,method:n,payload:r}=e;if(!n)return void this.sendError(t,"Method name is required");const o=this.methods[n];if(o)try{const e=e=>{this.sendUpdate(t,n,e)},i=await o.handler(r,e);this.sendResponse(t,n,i)}catch(i){this.sendError(t,i instanceof Error?i.message:String(i))}else this.sendError(t,`Method not found: ${n}`)}handleResponse(e,t){const n=this.pendingRequests.get(e);n&&(clearTimeout(n.timeout),this.pendingRequests.delete(e),n.resolve(t))}handleUpdate(e,t){const n=this.pendingRequests.get(e);n&&n.onUpdate&&n.onUpdate(t)}handleError(e,t){const n=this.pendingRequests.get(e);n&&(clearTimeout(n.timeout),this.pendingRequests.delete(e),n.reject(new Error(t)))}sendResponse(e,t,n){if(!this.ws)throw new Error("WebSocket is not connected");const r={id:e,messageType:"response",method:t,payload:n};this.ws.send(JSON.stringify(r))}sendUpdate(e,t,n){if(!this.ws)throw new Error("WebSocket is not connected");const r={id:e,messageType:"update",method:t,payload:n};this.ws.send(JSON.stringify(r))}sendError(e,t){if(!this.ws)throw new Error("WebSocket is not connected");const n={id:e,messageType:"error",error:{message:t}};this.ws.send(JSON.stringify(n))}handleDisconnect(){this.isIntentionalClose?this.clearPendingRequests(new Error("Connection closed by user")):this.reconnectAttempts<this.options.maxReconnectAttempts?(this.reconnectAttempts++,setTimeout((()=>this.reconnect()),this.options.reconnectDelay*this.reconnectAttempts)):this.clearPendingRequests(new Error("Connection closed"))}clearPendingRequests(e){this.pendingRequests.forEach((({reject:t})=>{t(e)})),this.pendingRequests.clear()}async close(){this.isIntentionalClose=!0,this.ws&&(this.ws.close(),this.ws=null),this.clearPendingRequests(new Error("Connection closed by user"))}};function Ut(e){return e}function Bt(e,t,n,r=!1){const o=e.safeParse(t);if(!o.success){const e=new Error(`Validation failed for ${n}: ${o.error.message}`);if(r)return t;throw e}return o.data}"undefined"!==typeof window?window.WebSocket:$t("ws").WebSocket;var Vt,qt,Wt="undefined"!==typeof window?window.WebSocket:$t("ws").WebSocket,Gt=class{constructor(e,t){this.bridge=e,this.contract=t,this.call=new Proxy({},{get:(e,t)=>(e,n)=>this.callMethod(t,e,n)})}async callMethod(e,t,n){const r=this.contract.consumes[e];if(!r)throw new Error(`Method ${String(e)} not found in contract`);const o=Bt(r.request,t,`request for method ${String(e)}`),i=(null==n?void 0:n.onUpdate)&&r.update?t=>{var o;if(r.update)try{const i=Bt(r.update,t,`update for method ${String(e)}`,!0);null==(o=n.onUpdate)||o.call(n,i)}catch(i){}}:void 0,a=await this.bridge.callMethod(e,o,i);return Bt(r.response,a,`response for method ${String(e)}`)}register(e){const t={};for(const[n,r]of Object.entries(e)){const e=this.contract.serves[n];if(!e)throw new Error(`Method ${n} not found in contract`);t[n]=async(t,o)=>{const i=Bt(e.request,t,`request for method ${n}`),a=e.update&&o?t=>{if(e.update)try{const r=Bt(e.update,t,`update for method ${n}`,!0);o(r)}catch(r){}}:void 0,s=await r(i,{sendUpdate:a});return Bt(e.response,s,`response for method ${n}`)}}this.bridge.register(t)}async close(){await this.bridge.close()}},Kt=class extends Ht{constructor(e,t){super(t),this.reconnectTimer=null,this.url=e}call(e,t,n){return this.callMethod(e,t,n)}reconnect(){this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=setTimeout((async()=>{try{await this.connect()}catch(e){this.reconnect()}}),this.options.reconnectDelay)}connect(){return new Promise(((e,t)=>{try{const n=new Wt(this.url);n.onopen=()=>{this.ws=n,this.setupWebSocketHandlers(n),e()},n.onerror=()=>{t(new Error("Failed to connect to WebSocket server"))}}catch(n){t(n)}}))}},Yt=class extends Gt{constructor(e,t,n){super(new Kt(e,n),{serves:t.client||{},consumes:t.server||{}})}connect(){return this.bridge.connect()}};function Xt(e,t,n){return new Yt(e,t,n)}(function(e){function t(e){}function n(e){throw new Error}function r(e,t=" | "){return e.map((e=>"string"===typeof e?`'${e}'`:e)).join(t)}e.assertEqual=e=>e,e.assertIs=t,e.assertNever=n,e.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},e.getValidEnumValues=t=>{const n=e.objectKeys(t).filter((e=>"number"!==typeof t[t[e]])),r={};for(const e of n)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map((function(e){return t[e]})),e.objectKeys="function"===typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},e.find=(e,t)=>{for(const n of e)if(t(n))return n},e.isInteger="function"===typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"===typeof e&&isFinite(e)&&Math.floor(e)===e,e.joinValues=r,e.jsonStringifyReplacer=(e,t)=>"bigint"===typeof t?t.toString():t})(Vt||(Vt={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(qt||(qt={}));const Jt=Vt.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Qt=e=>{const t=typeof e;switch(t){case"undefined":return Jt.undefined;case"string":return Jt.string;case"number":return isNaN(e)?Jt.nan:Jt.number;case"boolean":return Jt.boolean;case"function":return Jt.function;case"bigint":return Jt.bigint;case"symbol":return Jt.symbol;case"object":return Array.isArray(e)?Jt.array:null===e?Jt.null:e.then&&"function"===typeof e.then&&e.catch&&"function"===typeof e.catch?Jt.promise:"undefined"!==typeof Map&&e instanceof Map?Jt.map:"undefined"!==typeof Set&&e instanceof Set?Jt.set:"undefined"!==typeof Date&&e instanceof Date?Jt.date:Jt.object;default:return Jt.unknown}},en=Vt.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),tn=e=>{const t=JSON.stringify(e,null,2);return t.replace(/"([^"]+)":/g,"$1:")};class nn extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},n={_errors:[]},r=e=>{for(const o of e.issues)if("invalid_union"===o.code)o.unionErrors.map(r);else if("invalid_return_type"===o.code)r(o.returnTypeError);else if("invalid_arguments"===o.code)r(o.argumentsError);else if(0===o.path.length)n._errors.push(t(o));else{let e=n,r=0;while(r<o.path.length){const n=o.path[r],i=r===o.path.length-1;i?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(o))):e[n]=e[n]||{_errors:[]},e=e[n],r++}}};return r(this),n}static assert(e){if(!(e instanceof nn))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,Vt.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}nn.create=e=>{const t=new nn(e);return t};const rn=(e,t)=>{let n;switch(e.code){case en.invalid_type:n=e.received===Jt.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case en.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,Vt.jsonStringifyReplacer)}`;break;case en.unrecognized_keys:n=`Unrecognized key(s) in object: ${Vt.joinValues(e.keys,", ")}`;break;case en.invalid_union:n="Invalid input";break;case en.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${Vt.joinValues(e.options)}`;break;case en.invalid_enum_value:n=`Invalid enum value. Expected ${Vt.joinValues(e.options)}, received '${e.received}'`;break;case en.invalid_arguments:n="Invalid function arguments";break;case en.invalid_return_type:n="Invalid function return type";break;case en.invalid_date:n="Invalid date";break;case en.invalid_string:"object"===typeof e.validation?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,"number"===typeof e.validation.position&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:Vt.assertNever(e.validation):n="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case en.too_small:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case en.too_big:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case en.custom:n="Invalid input";break;case en.invalid_intersection_types:n="Intersection results could not be merged";break;case en.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case en.not_finite:n="Number must be finite";break;default:n=t.defaultError,Vt.assertNever(e)}return{message:n}};let on=rn;function an(e){on=e}function sn(){return on}const ln=e=>{const{data:t,path:n,errorMaps:r,issueData:o}=e,i=[...n,...o.path||[]],a={...o,path:i};if(void 0!==o.message)return{...o,path:i,message:o.message};let s="";const l=r.filter((e=>!!e)).slice().reverse();for(const c of l)s=c(a,{data:t,defaultError:s}).message;return{...o,path:i,message:s}},cn=[];function un(e,t){const n=sn(),r=ln({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===rn?void 0:rn].filter((e=>!!e))});e.common.issues.push(r)}class dn{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if("aborted"===r.status)return pn;"dirty"===r.status&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const e=await r.key,t=await r.value;n.push({key:e,value:t})}return dn.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:t,value:o}=r;if("aborted"===t.status)return pn;if("aborted"===o.status)return pn;"dirty"===t.status&&e.dirty(),"dirty"===o.status&&e.dirty(),"__proto__"===t.value||"undefined"===typeof o.value&&!r.alwaysSet||(n[t.value]=o.value)}return{status:e.value,value:n}}}const pn=Object.freeze({status:"aborted"}),fn=e=>({status:"dirty",value:e}),mn=e=>({status:"valid",value:e}),hn=e=>"aborted"===e.status,gn=e=>"dirty"===e.status,vn=e=>"valid"===e.status,wn=e=>"undefined"!==typeof Promise&&e instanceof Promise;function bn(e,t,n,r){if("function"===typeof t||!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(e)}function yn(e,t,n,r,o){if("function"===typeof t||!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var _n,xn,kn;"function"===typeof SuppressedError&&SuppressedError,function(e){e.errToObj=e=>"string"===typeof e?{message:e}:e||{},e.toString=e=>"string"===typeof e?e:null===e||void 0===e?void 0:e.message}(_n||(_n={}));class En{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Cn=(e,t)=>{if(vn(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new nn(e.common.issues);return this._error=t,this._error}}};function Sn(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:o}=e;if(t&&(n||r))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:o};const i=(t,o)=>{var i,a;const{message:s}=e;return"invalid_enum_value"===t.code?{message:null!==s&&void 0!==s?s:o.defaultError}:"undefined"===typeof o.data?{message:null!==(i=null!==s&&void 0!==s?s:r)&&void 0!==i?i:o.defaultError}:"invalid_type"!==t.code?{message:o.defaultError}:{message:null!==(a=null!==s&&void 0!==s?s:n)&&void 0!==a?a:o.defaultError}};return{errorMap:i,description:o}}class Tn{get description(){return this._def.description}_getType(e){return Qt(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:Qt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new dn,ctx:{common:e.parent.common,data:e.data,parsedType:Qt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(wn(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;const r={common:{issues:[],async:null!==(n=null===t||void 0===t?void 0:t.async)&&void 0!==n&&n,contextualErrorMap:null===t||void 0===t?void 0:t.errorMap},path:(null===t||void 0===t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Qt(e)},o=this._parseSync({data:e,path:r.path,parent:r});return Cn(r,o)}"~validate"(e){var t,n;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Qt(e)};if(!this["~standard"].async)try{const t=this._parseSync({data:e,path:[],parent:r});return vn(t)?{value:t.value}:{issues:r.common.issues}}catch(o){(null===(n=null===(t=null===o||void 0===o?void 0:o.message)||void 0===t?void 0:t.toLowerCase())||void 0===n?void 0:n.includes("encountered"))&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then((e=>vn(e)?{value:e.value}:{issues:r.common.issues}))}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:null===t||void 0===t?void 0:t.errorMap,async:!0},path:(null===t||void 0===t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Qt(e)},r=this._parse({data:e,path:n.path,parent:n}),o=await(wn(r)?r:Promise.resolve(r));return Cn(n,o)}refine(e,t){const n=e=>"string"===typeof t||"undefined"===typeof t?{message:t}:"function"===typeof t?t(e):t;return this._refinement(((t,r)=>{const o=e(t),i=()=>r.addIssue({code:en.custom,...n(t)});return"undefined"!==typeof Promise&&o instanceof Promise?o.then((e=>!!e||(i(),!1))):!!o||(i(),!1)}))}refinement(e,t){return this._refinement(((n,r)=>!!e(n)||(r.addIssue("function"===typeof t?t(n,r):t),!1)))}_refinement(e){return new Ar({schema:this,typeName:Ur.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return Ir.create(this,this._def)}nullable(){return Or.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return dr.create(this)}promise(){return Pr.create(this,this._def)}or(e){return mr.create([this,e],this._def)}and(e){return wr.create(this,e,this._def)}transform(e){return new Ar({...Sn(this._def),schema:this,typeName:Ur.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"===typeof e?e:()=>e;return new Rr({...Sn(this._def),innerType:this,defaultValue:t,typeName:Ur.ZodDefault})}brand(){return new jr({typeName:Ur.ZodBranded,type:this,...Sn(this._def)})}catch(e){const t="function"===typeof e?e:()=>e;return new Mr({...Sn(this._def),innerType:this,catchValue:t,typeName:Ur.ZodCatch})}describe(e){const t=this.constructor;return new t({...this._def,description:e})}pipe(e){return Fr.create(this,e)}readonly(){return Dr.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Nn=/^c[^\s-]{8,}$/i,Pn=/^[0-9a-z]+$/,An=/^[0-9A-HJKMNP-TV-Z]{26}$/i,In=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,On=/^[a-z0-9_-]{21}$/i,Rn=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Mn=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,zn=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Ln="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let jn;const Fn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Dn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,$n=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Zn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Hn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Un=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Bn="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Vn=new RegExp(`^${Bn}$`);function qn(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);const n=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}function Wn(e){return new RegExp(`^${qn(e)}$`)}function Gn(e){let t=`${Bn}T${qn(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function Kn(e,t){return!("v4"!==t&&t||!Fn.test(e))||!("v6"!==t&&t||!$n.test(e))}function Yn(e,t){if(!Rn.test(e))return!1;try{const[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),o=JSON.parse(atob(r));return"object"===typeof o&&null!==o&&(!(!o.typ||!o.alg)&&(!t||o.alg===t))}catch(n){return!1}}function Xn(e,t){return!("v4"!==t&&t||!Dn.test(e))||!("v6"!==t&&t||!Zn.test(e))}class Jn extends Tn{_parse(e){this._def.coerce&&(e.data=String(e.data));const t=this._getType(e);if(t!==Jt.string){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.string,received:t.parsedType}),pn}const n=new dn;let r;for(const i of this._def.checks)if("min"===i.kind)e.data.length<i.value&&(r=this._getOrReturnCtx(e,r),un(r,{code:en.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),n.dirty());else if("max"===i.kind)e.data.length>i.value&&(r=this._getOrReturnCtx(e,r),un(r,{code:en.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),n.dirty());else if("length"===i.kind){const t=e.data.length>i.value,o=e.data.length<i.value;(t||o)&&(r=this._getOrReturnCtx(e,r),t?un(r,{code:en.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):o&&un(r,{code:en.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),n.dirty())}else if("email"===i.kind)zn.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"email",code:en.invalid_string,message:i.message}),n.dirty());else if("emoji"===i.kind)jn||(jn=new RegExp(Ln,"u")),jn.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"emoji",code:en.invalid_string,message:i.message}),n.dirty());else if("uuid"===i.kind)In.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"uuid",code:en.invalid_string,message:i.message}),n.dirty());else if("nanoid"===i.kind)On.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"nanoid",code:en.invalid_string,message:i.message}),n.dirty());else if("cuid"===i.kind)Nn.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"cuid",code:en.invalid_string,message:i.message}),n.dirty());else if("cuid2"===i.kind)Pn.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"cuid2",code:en.invalid_string,message:i.message}),n.dirty());else if("ulid"===i.kind)An.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"ulid",code:en.invalid_string,message:i.message}),n.dirty());else if("url"===i.kind)try{new URL(e.data)}catch(o){r=this._getOrReturnCtx(e,r),un(r,{validation:"url",code:en.invalid_string,message:i.message}),n.dirty()}else if("regex"===i.kind){i.regex.lastIndex=0;const t=i.regex.test(e.data);t||(r=this._getOrReturnCtx(e,r),un(r,{validation:"regex",code:en.invalid_string,message:i.message}),n.dirty())}else if("trim"===i.kind)e.data=e.data.trim();else if("includes"===i.kind)e.data.includes(i.value,i.position)||(r=this._getOrReturnCtx(e,r),un(r,{code:en.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),n.dirty());else if("toLowerCase"===i.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===i.kind)e.data=e.data.toUpperCase();else if("startsWith"===i.kind)e.data.startsWith(i.value)||(r=this._getOrReturnCtx(e,r),un(r,{code:en.invalid_string,validation:{startsWith:i.value},message:i.message}),n.dirty());else if("endsWith"===i.kind)e.data.endsWith(i.value)||(r=this._getOrReturnCtx(e,r),un(r,{code:en.invalid_string,validation:{endsWith:i.value},message:i.message}),n.dirty());else if("datetime"===i.kind){const t=Gn(i);t.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{code:en.invalid_string,validation:"datetime",message:i.message}),n.dirty())}else if("date"===i.kind){const t=Vn;t.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{code:en.invalid_string,validation:"date",message:i.message}),n.dirty())}else if("time"===i.kind){const t=Wn(i);t.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{code:en.invalid_string,validation:"time",message:i.message}),n.dirty())}else"duration"===i.kind?Mn.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"duration",code:en.invalid_string,message:i.message}),n.dirty()):"ip"===i.kind?Kn(e.data,i.version)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"ip",code:en.invalid_string,message:i.message}),n.dirty()):"jwt"===i.kind?Yn(e.data,i.alg)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"jwt",code:en.invalid_string,message:i.message}),n.dirty()):"cidr"===i.kind?Xn(e.data,i.version)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"cidr",code:en.invalid_string,message:i.message}),n.dirty()):"base64"===i.kind?Hn.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"base64",code:en.invalid_string,message:i.message}),n.dirty()):"base64url"===i.kind?Un.test(e.data)||(r=this._getOrReturnCtx(e,r),un(r,{validation:"base64url",code:en.invalid_string,message:i.message}),n.dirty()):Vt.assertNever(i);return{status:n.value,value:e.data}}_regex(e,t,n){return this.refinement((t=>e.test(t)),{validation:t,code:en.invalid_string,..._n.errToObj(n)})}_addCheck(e){return new Jn({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",..._n.errToObj(e)})}url(e){return this._addCheck({kind:"url",..._n.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",..._n.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",..._n.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",..._n.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",..._n.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",..._n.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",..._n.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",..._n.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",..._n.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",..._n.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",..._n.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",..._n.errToObj(e)})}datetime(e){var t,n;return"string"===typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:"undefined"===typeof(null===e||void 0===e?void 0:e.precision)?null:null===e||void 0===e?void 0:e.precision,offset:null!==(t=null===e||void 0===e?void 0:e.offset)&&void 0!==t&&t,local:null!==(n=null===e||void 0===e?void 0:e.local)&&void 0!==n&&n,..._n.errToObj(null===e||void 0===e?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"===typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:"undefined"===typeof(null===e||void 0===e?void 0:e.precision)?null:null===e||void 0===e?void 0:e.precision,..._n.errToObj(null===e||void 0===e?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",..._n.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,..._n.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null===t||void 0===t?void 0:t.position,..._n.errToObj(null===t||void 0===t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,..._n.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,..._n.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,..._n.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,..._n.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,..._n.errToObj(t)})}nonempty(e){return this.min(1,_n.errToObj(e))}trim(){return new Jn({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Jn({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Jn({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find((e=>"datetime"===e.kind))}get isDate(){return!!this._def.checks.find((e=>"date"===e.kind))}get isTime(){return!!this._def.checks.find((e=>"time"===e.kind))}get isDuration(){return!!this._def.checks.find((e=>"duration"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>"email"===e.kind))}get isURL(){return!!this._def.checks.find((e=>"url"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>"emoji"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>"uuid"===e.kind))}get isNANOID(){return!!this._def.checks.find((e=>"nanoid"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>"cuid"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>"cuid2"===e.kind))}get isULID(){return!!this._def.checks.find((e=>"ulid"===e.kind))}get isIP(){return!!this._def.checks.find((e=>"ip"===e.kind))}get isCIDR(){return!!this._def.checks.find((e=>"cidr"===e.kind))}get isBase64(){return!!this._def.checks.find((e=>"base64"===e.kind))}get isBase64url(){return!!this._def.checks.find((e=>"base64url"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function Qn(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,o=n>r?n:r,i=parseInt(e.toFixed(o).replace(".","")),a=parseInt(t.toFixed(o).replace(".",""));return i%a/Math.pow(10,o)}Jn.create=e=>{var t;return new Jn({checks:[],typeName:Ur.ZodString,coerce:null!==(t=null===e||void 0===e?void 0:e.coerce)&&void 0!==t&&t,...Sn(e)})};class er extends Tn{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));const t=this._getType(e);if(t!==Jt.number){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.number,received:t.parsedType}),pn}let n;const r=new dn;for(const o of this._def.checks)if("int"===o.kind)Vt.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),un(n,{code:en.invalid_type,expected:"integer",received:"float",message:o.message}),r.dirty());else if("min"===o.kind){const t=o.inclusive?e.data<o.value:e.data<=o.value;t&&(n=this._getOrReturnCtx(e,n),un(n,{code:en.too_small,minimum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),r.dirty())}else if("max"===o.kind){const t=o.inclusive?e.data>o.value:e.data>=o.value;t&&(n=this._getOrReturnCtx(e,n),un(n,{code:en.too_big,maximum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),r.dirty())}else"multipleOf"===o.kind?0!==Qn(e.data,o.value)&&(n=this._getOrReturnCtx(e,n),un(n,{code:en.not_multiple_of,multipleOf:o.value,message:o.message}),r.dirty()):"finite"===o.kind?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),un(n,{code:en.not_finite,message:o.message}),r.dirty()):Vt.assertNever(o);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,_n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,_n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,_n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,_n.toString(t))}setLimit(e,t,n,r){return new er({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:_n.toString(r)}]})}_addCheck(e){return new er({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:_n.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:_n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:_n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:_n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:_n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:_n.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:_n.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:_n.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:_n.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>"int"===e.kind||"multipleOf"===e.kind&&Vt.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;"min"===n.kind?(null===t||n.value>t)&&(t=n.value):"max"===n.kind&&(null===e||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}er.create=e=>new er({checks:[],typeName:Ur.ZodNumber,coerce:(null===e||void 0===e?void 0:e.coerce)||!1,...Sn(e)});class tr extends Tn{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch(o){return this._getInvalidInput(e)}const t=this._getType(e);if(t!==Jt.bigint)return this._getInvalidInput(e);let n;const r=new dn;for(const i of this._def.checks)if("min"===i.kind){const t=i.inclusive?e.data<i.value:e.data<=i.value;t&&(n=this._getOrReturnCtx(e,n),un(n,{code:en.too_small,type:"bigint",minimum:i.value,inclusive:i.inclusive,message:i.message}),r.dirty())}else if("max"===i.kind){const t=i.inclusive?e.data>i.value:e.data>=i.value;t&&(n=this._getOrReturnCtx(e,n),un(n,{code:en.too_big,type:"bigint",maximum:i.value,inclusive:i.inclusive,message:i.message}),r.dirty())}else"multipleOf"===i.kind?e.data%i.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),un(n,{code:en.not_multiple_of,multipleOf:i.value,message:i.message}),r.dirty()):Vt.assertNever(i);return{status:r.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.bigint,received:t.parsedType}),pn}gte(e,t){return this.setLimit("min",e,!0,_n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,_n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,_n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,_n.toString(t))}setLimit(e,t,n,r){return new tr({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:_n.toString(r)}]})}_addCheck(e){return new tr({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:_n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:_n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:_n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:_n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:_n.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}tr.create=e=>{var t;return new tr({checks:[],typeName:Ur.ZodBigInt,coerce:null!==(t=null===e||void 0===e?void 0:e.coerce)&&void 0!==t&&t,...Sn(e)})};class nr extends Tn{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));const t=this._getType(e);if(t!==Jt.boolean){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.boolean,received:t.parsedType}),pn}return mn(e.data)}}nr.create=e=>new nr({typeName:Ur.ZodBoolean,coerce:(null===e||void 0===e?void 0:e.coerce)||!1,...Sn(e)});class rr extends Tn{_parse(e){this._def.coerce&&(e.data=new Date(e.data));const t=this._getType(e);if(t!==Jt.date){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.date,received:t.parsedType}),pn}if(isNaN(e.data.getTime())){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_date}),pn}const n=new dn;let r;for(const o of this._def.checks)"min"===o.kind?e.data.getTime()<o.value&&(r=this._getOrReturnCtx(e,r),un(r,{code:en.too_small,message:o.message,inclusive:!0,exact:!1,minimum:o.value,type:"date"}),n.dirty()):"max"===o.kind?e.data.getTime()>o.value&&(r=this._getOrReturnCtx(e,r),un(r,{code:en.too_big,message:o.message,inclusive:!0,exact:!1,maximum:o.value,type:"date"}),n.dirty()):Vt.assertNever(o);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new rr({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:_n.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:_n.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}rr.create=e=>new rr({checks:[],coerce:(null===e||void 0===e?void 0:e.coerce)||!1,typeName:Ur.ZodDate,...Sn(e)});class or extends Tn{_parse(e){const t=this._getType(e);if(t!==Jt.symbol){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.symbol,received:t.parsedType}),pn}return mn(e.data)}}or.create=e=>new or({typeName:Ur.ZodSymbol,...Sn(e)});class ir extends Tn{_parse(e){const t=this._getType(e);if(t!==Jt.undefined){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.undefined,received:t.parsedType}),pn}return mn(e.data)}}ir.create=e=>new ir({typeName:Ur.ZodUndefined,...Sn(e)});class ar extends Tn{_parse(e){const t=this._getType(e);if(t!==Jt.null){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.null,received:t.parsedType}),pn}return mn(e.data)}}ar.create=e=>new ar({typeName:Ur.ZodNull,...Sn(e)});class sr extends Tn{constructor(){super(...arguments),this._any=!0}_parse(e){return mn(e.data)}}sr.create=e=>new sr({typeName:Ur.ZodAny,...Sn(e)});class lr extends Tn{constructor(){super(...arguments),this._unknown=!0}_parse(e){return mn(e.data)}}lr.create=e=>new lr({typeName:Ur.ZodUnknown,...Sn(e)});class cr extends Tn{_parse(e){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.never,received:t.parsedType}),pn}}cr.create=e=>new cr({typeName:Ur.ZodNever,...Sn(e)});class ur extends Tn{_parse(e){const t=this._getType(e);if(t!==Jt.undefined){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.void,received:t.parsedType}),pn}return mn(e.data)}}ur.create=e=>new ur({typeName:Ur.ZodVoid,...Sn(e)});class dr extends Tn{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==Jt.array)return un(t,{code:en.invalid_type,expected:Jt.array,received:t.parsedType}),pn;if(null!==r.exactLength){const e=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(e||o)&&(un(t,{code:e?en.too_big:en.too_small,minimum:o?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(un(t,{code:en.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(un(t,{code:en.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map(((e,n)=>r.type._parseAsync(new En(t,e,t.path,n))))).then((e=>dn.mergeArray(n,e)));const o=[...t.data].map(((e,n)=>r.type._parseSync(new En(t,e,t.path,n))));return dn.mergeArray(n,o)}get element(){return this._def.type}min(e,t){return new dr({...this._def,minLength:{value:e,message:_n.toString(t)}})}max(e,t){return new dr({...this._def,maxLength:{value:e,message:_n.toString(t)}})}length(e,t){return new dr({...this._def,exactLength:{value:e,message:_n.toString(t)}})}nonempty(e){return this.min(1,e)}}function pr(e){if(e instanceof fr){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=Ir.create(pr(r))}return new fr({...e._def,shape:()=>t})}return e instanceof dr?new dr({...e._def,type:pr(e.element)}):e instanceof Ir?Ir.create(pr(e.unwrap())):e instanceof Or?Or.create(pr(e.unwrap())):e instanceof br?br.create(e.items.map((e=>pr(e)))):e}dr.create=(e,t)=>new dr({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Ur.ZodArray,...Sn(t)});class fr extends Tn{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=Vt.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){const t=this._getType(e);if(t!==Jt.object){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.object,received:t.parsedType}),pn}const{status:n,ctx:r}=this._processInputParams(e),{shape:o,keys:i}=this._getCached(),a=[];if(!(this._def.catchall instanceof cr&&"strip"===this._def.unknownKeys))for(const l in r.data)i.includes(l)||a.push(l);const s=[];for(const l of i){const e=o[l],t=r.data[l];s.push({key:{status:"valid",value:l},value:e._parse(new En(r,t,r.path,l)),alwaysSet:l in r.data})}if(this._def.catchall instanceof cr){const e=this._def.unknownKeys;if("passthrough"===e)for(const t of a)s.push({key:{status:"valid",value:t},value:{status:"valid",value:r.data[t]}});else if("strict"===e)a.length>0&&(un(r,{code:en.unrecognized_keys,keys:a}),n.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of a){const n=r.data[t];s.push({key:{status:"valid",value:t},value:e._parse(new En(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of s){const n=await t.key,r=await t.value;e.push({key:n,value:r,alwaysSet:t.alwaysSet})}return e})).then((e=>dn.mergeObjectSync(n,e))):dn.mergeObjectSync(n,s)}get shape(){return this._def.shape()}strict(e){return _n.errToObj,new fr({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{var r,o,i,a;const s=null!==(i=null===(o=(r=this._def).errorMap)||void 0===o?void 0:o.call(r,t,n).message)&&void 0!==i?i:n.defaultError;return"unrecognized_keys"===t.code?{message:null!==(a=_n.errToObj(e).message)&&void 0!==a?a:s}:{message:s}}}:{}})}strip(){return new fr({...this._def,unknownKeys:"strip"})}passthrough(){return new fr({...this._def,unknownKeys:"passthrough"})}extend(e){return new fr({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){const t=new fr({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Ur.ZodObject});return t}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new fr({...this._def,catchall:e})}pick(e){const t={};return Vt.objectKeys(e).forEach((n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])})),new fr({...this._def,shape:()=>t})}omit(e){const t={};return Vt.objectKeys(this.shape).forEach((n=>{e[n]||(t[n]=this.shape[n])})),new fr({...this._def,shape:()=>t})}deepPartial(){return pr(this)}partial(e){const t={};return Vt.objectKeys(this.shape).forEach((n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()})),new fr({...this._def,shape:()=>t})}required(e){const t={};return Vt.objectKeys(this.shape).forEach((n=>{if(e&&!e[n])t[n]=this.shape[n];else{const e=this.shape[n];let r=e;while(r instanceof Ir)r=r._def.innerType;t[n]=r}})),new fr({...this._def,shape:()=>t})}keyof(){return Sr(Vt.objectKeys(this.shape))}}fr.create=(e,t)=>new fr({shape:()=>e,unknownKeys:"strip",catchall:cr.create(),typeName:Ur.ZodObject,...Sn(t)}),fr.strictCreate=(e,t)=>new fr({shape:()=>e,unknownKeys:"strict",catchall:cr.create(),typeName:Ur.ZodObject,...Sn(t)}),fr.lazycreate=(e,t)=>new fr({shape:e,unknownKeys:"strip",catchall:cr.create(),typeName:Ur.ZodObject,...Sn(t)});class mr extends Tn{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;function r(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;const n=e.map((e=>new nn(e.ctx.common.issues)));return un(t,{code:en.invalid_union,unionErrors:n}),pn}if(t.common.async)return Promise.all(n.map((async e=>{const n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}}))).then(r);{let e;const r=[];for(const i of n){const n={...t,common:{...t.common,issues:[]},parent:null},o=i._parseSync({data:t.data,path:t.path,parent:n});if("valid"===o.status)return o;"dirty"!==o.status||e||(e={result:o,ctx:n}),n.common.issues.length&&r.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const o=r.map((e=>new nn(e)));return un(t,{code:en.invalid_union,unionErrors:o}),pn}}get options(){return this._def.options}}mr.create=(e,t)=>new mr({options:e,typeName:Ur.ZodUnion,...Sn(t)});const hr=e=>e instanceof Er?hr(e.schema):e instanceof Ar?hr(e.innerType()):e instanceof Cr?[e.value]:e instanceof Tr?e.options:e instanceof Nr?Vt.objectValues(e.enum):e instanceof Rr?hr(e._def.innerType):e instanceof ir?[void 0]:e instanceof ar?[null]:e instanceof Ir?[void 0,...hr(e.unwrap())]:e instanceof Or?[null,...hr(e.unwrap())]:e instanceof jr||e instanceof Dr?hr(e.unwrap()):e instanceof Mr?hr(e._def.innerType):[];class gr extends Tn{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Jt.object)return un(t,{code:en.invalid_type,expected:Jt.object,received:t.parsedType}),pn;const n=this.discriminator,r=t.data[n],o=this.optionsMap.get(r);return o?t.common.async?o._parseAsync({data:t.data,path:t.path,parent:t}):o._parseSync({data:t.data,path:t.path,parent:t}):(un(t,{code:en.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),pn)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const o of t){const t=hr(o.shape[e]);if(!t.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const n of t){if(r.has(n))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(n)}`);r.set(n,o)}}return new gr({typeName:Ur.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...Sn(n)})}}function vr(e,t){const n=Qt(e),r=Qt(t);if(e===t)return{valid:!0,data:e};if(n===Jt.object&&r===Jt.object){const n=Vt.objectKeys(t),r=Vt.objectKeys(e).filter((e=>-1!==n.indexOf(e))),o={...e,...t};for(const i of r){const n=vr(e[i],t[i]);if(!n.valid)return{valid:!1};o[i]=n.data}return{valid:!0,data:o}}if(n===Jt.array&&r===Jt.array){if(e.length!==t.length)return{valid:!1};const n=[];for(let r=0;r<e.length;r++){const o=e[r],i=t[r],a=vr(o,i);if(!a.valid)return{valid:!1};n.push(a.data)}return{valid:!0,data:n}}return n===Jt.date&&r===Jt.date&&+e===+t?{valid:!0,data:e}:{valid:!1}}class wr extends Tn{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(e,r)=>{if(hn(e)||hn(r))return pn;const o=vr(e.value,r.value);return o.valid?((gn(e)||gn(r))&&t.dirty(),{status:t.value,value:o.data}):(un(n,{code:en.invalid_intersection_types}),pn)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then((([e,t])=>r(e,t))):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}wr.create=(e,t,n)=>new wr({left:e,right:t,typeName:Ur.ZodIntersection,...Sn(n)});class br extends Tn{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==Jt.array)return un(n,{code:en.invalid_type,expected:Jt.array,received:n.parsedType}),pn;if(n.data.length<this._def.items.length)return un(n,{code:en.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),pn;const r=this._def.rest;!r&&n.data.length>this._def.items.length&&(un(n,{code:en.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const o=[...n.data].map(((e,t)=>{const r=this._def.items[t]||this._def.rest;return r?r._parse(new En(n,e,n.path,t)):null})).filter((e=>!!e));return n.common.async?Promise.all(o).then((e=>dn.mergeArray(t,e))):dn.mergeArray(t,o)}get items(){return this._def.items}rest(e){return new br({...this._def,rest:e})}}br.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new br({items:e,typeName:Ur.ZodTuple,rest:null,...Sn(t)})};class yr extends Tn{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==Jt.object)return un(n,{code:en.invalid_type,expected:Jt.object,received:n.parsedType}),pn;const r=[],o=this._def.keyType,i=this._def.valueType;for(const a in n.data)r.push({key:o._parse(new En(n,a,n.path,a)),value:i._parse(new En(n,n.data[a],n.path,a)),alwaysSet:a in n.data});return n.common.async?dn.mergeObjectAsync(t,r):dn.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new yr(t instanceof Tn?{keyType:e,valueType:t,typeName:Ur.ZodRecord,...Sn(n)}:{keyType:Jn.create(),valueType:e,typeName:Ur.ZodRecord,...Sn(t)})}}class _r extends Tn{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==Jt.map)return un(n,{code:en.invalid_type,expected:Jt.map,received:n.parsedType}),pn;const r=this._def.keyType,o=this._def.valueType,i=[...n.data.entries()].map((([e,t],i)=>({key:r._parse(new En(n,e,n.path,[i,"key"])),value:o._parse(new En(n,t,n.path,[i,"value"]))})));if(n.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const n of i){const r=await n.key,o=await n.value;if("aborted"===r.status||"aborted"===o.status)return pn;"dirty"!==r.status&&"dirty"!==o.status||t.dirty(),e.set(r.value,o.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const n of i){const r=n.key,o=n.value;if("aborted"===r.status||"aborted"===o.status)return pn;"dirty"!==r.status&&"dirty"!==o.status||t.dirty(),e.set(r.value,o.value)}return{status:t.value,value:e}}}}_r.create=(e,t,n)=>new _r({valueType:t,keyType:e,typeName:Ur.ZodMap,...Sn(n)});class xr extends Tn{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==Jt.set)return un(n,{code:en.invalid_type,expected:Jt.set,received:n.parsedType}),pn;const r=this._def;null!==r.minSize&&n.data.size<r.minSize.value&&(un(n,{code:en.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&n.data.size>r.maxSize.value&&(un(n,{code:en.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const o=this._def.valueType;function i(e){const n=new Set;for(const r of e){if("aborted"===r.status)return pn;"dirty"===r.status&&t.dirty(),n.add(r.value)}return{status:t.value,value:n}}const a=[...n.data.values()].map(((e,t)=>o._parse(new En(n,e,n.path,t))));return n.common.async?Promise.all(a).then((e=>i(e))):i(a)}min(e,t){return new xr({...this._def,minSize:{value:e,message:_n.toString(t)}})}max(e,t){return new xr({...this._def,maxSize:{value:e,message:_n.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}xr.create=(e,t)=>new xr({valueType:e,minSize:null,maxSize:null,typeName:Ur.ZodSet,...Sn(t)});class kr extends Tn{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Jt.function)return un(t,{code:en.invalid_type,expected:Jt.function,received:t.parsedType}),pn;function n(e,n){return ln({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,sn(),rn].filter((e=>!!e)),issueData:{code:en.invalid_arguments,argumentsError:n}})}function r(e,n){return ln({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,sn(),rn].filter((e=>!!e)),issueData:{code:en.invalid_return_type,returnTypeError:n}})}const o={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof Pr){const e=this;return mn((async function(...t){const a=new nn([]),s=await e._def.args.parseAsync(t,o).catch((e=>{throw a.addIssue(n(t,e)),a})),l=await Reflect.apply(i,this,s),c=await e._def.returns._def.type.parseAsync(l,o).catch((e=>{throw a.addIssue(r(l,e)),a}));return c}))}{const e=this;return mn((function(...t){const a=e._def.args.safeParse(t,o);if(!a.success)throw new nn([n(t,a.error)]);const s=Reflect.apply(i,this,a.data),l=e._def.returns.safeParse(s,o);if(!l.success)throw new nn([r(s,l.error)]);return l.data}))}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new kr({...this._def,args:br.create(e).rest(lr.create())})}returns(e){return new kr({...this._def,returns:e})}implement(e){const t=this.parse(e);return t}strictImplement(e){const t=this.parse(e);return t}static create(e,t,n){return new kr({args:e||br.create([]).rest(lr.create()),returns:t||lr.create(),typeName:Ur.ZodFunction,...Sn(n)})}}class Er extends Tn{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.getter();return n._parse({data:t.data,path:t.path,parent:t})}}Er.create=(e,t)=>new Er({getter:e,typeName:Ur.ZodLazy,...Sn(t)});class Cr extends Tn{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return un(t,{received:t.data,code:en.invalid_literal,expected:this._def.value}),pn}return{status:"valid",value:e.data}}get value(){return this._def.value}}function Sr(e,t){return new Tr({values:e,typeName:Ur.ZodEnum,...Sn(t)})}Cr.create=(e,t)=>new Cr({value:e,typeName:Ur.ZodLiteral,...Sn(t)});class Tr extends Tn{constructor(){super(...arguments),xn.set(this,void 0)}_parse(e){if("string"!==typeof e.data){const t=this._getOrReturnCtx(e),n=this._def.values;return un(t,{expected:Vt.joinValues(n),received:t.parsedType,code:en.invalid_type}),pn}if(bn(this,xn)||yn(this,xn,new Set(this._def.values)),!bn(this,xn).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return un(t,{received:t.data,code:en.invalid_enum_value,options:n}),pn}return mn(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Tr.create(e,{...this._def,...t})}exclude(e,t=this._def){return Tr.create(this.options.filter((t=>!e.includes(t))),{...this._def,...t})}}xn=new WeakMap,Tr.create=Sr;class Nr extends Tn{constructor(){super(...arguments),kn.set(this,void 0)}_parse(e){const t=Vt.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==Jt.string&&n.parsedType!==Jt.number){const e=Vt.objectValues(t);return un(n,{expected:Vt.joinValues(e),received:n.parsedType,code:en.invalid_type}),pn}if(bn(this,kn)||yn(this,kn,new Set(Vt.getValidEnumValues(this._def.values))),!bn(this,kn).has(e.data)){const e=Vt.objectValues(t);return un(n,{received:n.data,code:en.invalid_enum_value,options:e}),pn}return mn(e.data)}get enum(){return this._def.values}}kn=new WeakMap,Nr.create=(e,t)=>new Nr({values:e,typeName:Ur.ZodNativeEnum,...Sn(t)});class Pr extends Tn{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Jt.promise&&!1===t.common.async)return un(t,{code:en.invalid_type,expected:Jt.promise,received:t.parsedType}),pn;const n=t.parsedType===Jt.promise?t.data:Promise.resolve(t.data);return mn(n.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}Pr.create=(e,t)=>new Pr({type:e,typeName:Ur.ZodPromise,...Sn(t)});class Ar extends Tn{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Ur.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,o={addIssue:e=>{un(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(o.addIssue=o.addIssue.bind(o),"preprocess"===r.type){const e=r.transform(n.data,o);if(n.common.async)return Promise.resolve(e).then((async e=>{if("aborted"===t.value)return pn;const r=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===r.status?pn:"dirty"===r.status||"dirty"===t.value?fn(r.value):r}));{if("aborted"===t.value)return pn;const r=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===r.status?pn:"dirty"===r.status||"dirty"===t.value?fn(r.value):r}}if("refinement"===r.type){const e=e=>{const t=r.refinement(e,o);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===n.common.async){const r=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===r.status?pn:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then((n=>"aborted"===n.status?pn:("dirty"===n.status&&t.dirty(),e(n.value).then((()=>({status:t.value,value:n.value}))))))}if("transform"===r.type){if(!1===n.common.async){const e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!vn(e))return e;const i=r.transform(e.value,o);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:i}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then((e=>vn(e)?Promise.resolve(r.transform(e.value,o)).then((e=>({status:t.value,value:e}))):e))}Vt.assertNever(r)}}Ar.create=(e,t,n)=>new Ar({schema:e,typeName:Ur.ZodEffects,effect:t,...Sn(n)}),Ar.createWithPreprocess=(e,t,n)=>new Ar({schema:t,effect:{type:"preprocess",transform:e},typeName:Ur.ZodEffects,...Sn(n)});class Ir extends Tn{_parse(e){const t=this._getType(e);return t===Jt.undefined?mn(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ir.create=(e,t)=>new Ir({innerType:e,typeName:Ur.ZodOptional,...Sn(t)});class Or extends Tn{_parse(e){const t=this._getType(e);return t===Jt.null?mn(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Or.create=(e,t)=>new Or({innerType:e,typeName:Ur.ZodNullable,...Sn(t)});class Rr extends Tn{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===Jt.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Rr.create=(e,t)=>new Rr({innerType:e,typeName:Ur.ZodDefault,defaultValue:"function"===typeof t.default?t.default:()=>t.default,...Sn(t)});class Mr extends Tn{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return wn(r)?r.then((e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new nn(n.common.issues)},input:n.data})}))):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new nn(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Mr.create=(e,t)=>new Mr({innerType:e,typeName:Ur.ZodCatch,catchValue:"function"===typeof t.catch?t.catch:()=>t.catch,...Sn(t)});class zr extends Tn{_parse(e){const t=this._getType(e);if(t!==Jt.nan){const t=this._getOrReturnCtx(e);return un(t,{code:en.invalid_type,expected:Jt.nan,received:t.parsedType}),pn}return{status:"valid",value:e.data}}}zr.create=e=>new zr({typeName:Ur.ZodNaN,...Sn(e)});const Lr=Symbol("zod_brand");class jr extends Tn{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class Fr extends Tn{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async){const e=async()=>{const e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?pn:"dirty"===e.status?(t.dirty(),fn(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})};return e()}{const e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?pn:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(e,t){return new Fr({in:e,out:t,typeName:Ur.ZodPipeline})}}class Dr extends Tn{_parse(e){const t=this._def.innerType._parse(e),n=e=>(vn(e)&&(e.value=Object.freeze(e.value)),e);return wn(t)?t.then((e=>n(e))):n(t)}unwrap(){return this._def.innerType}}function $r(e,t){const n="function"===typeof e?e(t):"string"===typeof e?{message:e}:e,r="string"===typeof n?{message:n}:n;return r}function Zr(e,t={},n){return e?sr.create().superRefine(((r,o)=>{var i,a;const s=e(r);if(s instanceof Promise)return s.then((e=>{var i,a;if(!e){const e=$r(t,r),s=null===(a=null!==(i=e.fatal)&&void 0!==i?i:n)||void 0===a||a;o.addIssue({code:"custom",...e,fatal:s})}}));if(!s){const e=$r(t,r),s=null===(a=null!==(i=e.fatal)&&void 0!==i?i:n)||void 0===a||a;o.addIssue({code:"custom",...e,fatal:s})}})):sr.create()}Dr.create=(e,t)=>new Dr({innerType:e,typeName:Ur.ZodReadonly,...Sn(t)});const Hr={object:fr.lazycreate};var Ur;(function(e){e["ZodString"]="ZodString",e["ZodNumber"]="ZodNumber",e["ZodNaN"]="ZodNaN",e["ZodBigInt"]="ZodBigInt",e["ZodBoolean"]="ZodBoolean",e["ZodDate"]="ZodDate",e["ZodSymbol"]="ZodSymbol",e["ZodUndefined"]="ZodUndefined",e["ZodNull"]="ZodNull",e["ZodAny"]="ZodAny",e["ZodUnknown"]="ZodUnknown",e["ZodNever"]="ZodNever",e["ZodVoid"]="ZodVoid",e["ZodArray"]="ZodArray",e["ZodObject"]="ZodObject",e["ZodUnion"]="ZodUnion",e["ZodDiscriminatedUnion"]="ZodDiscriminatedUnion",e["ZodIntersection"]="ZodIntersection",e["ZodTuple"]="ZodTuple",e["ZodRecord"]="ZodRecord",e["ZodMap"]="ZodMap",e["ZodSet"]="ZodSet",e["ZodFunction"]="ZodFunction",e["ZodLazy"]="ZodLazy",e["ZodLiteral"]="ZodLiteral",e["ZodEnum"]="ZodEnum",e["ZodEffects"]="ZodEffects",e["ZodNativeEnum"]="ZodNativeEnum",e["ZodOptional"]="ZodOptional",e["ZodNullable"]="ZodNullable",e["ZodDefault"]="ZodDefault",e["ZodCatch"]="ZodCatch",e["ZodPromise"]="ZodPromise",e["ZodBranded"]="ZodBranded",e["ZodPipeline"]="ZodPipeline",e["ZodReadonly"]="ZodReadonly"})(Ur||(Ur={}));const Br=(e,t={message:`Input not instance of ${e.name}`})=>Zr((t=>t instanceof e),t),Vr=Jn.create,qr=er.create,Wr=zr.create,Gr=tr.create,Kr=nr.create,Yr=rr.create,Xr=or.create,Jr=ir.create,Qr=ar.create,eo=sr.create,to=lr.create,no=cr.create,ro=ur.create,oo=dr.create,io=fr.create,ao=fr.strictCreate,so=mr.create,lo=gr.create,co=wr.create,uo=br.create,po=yr.create,fo=_r.create,mo=xr.create,ho=kr.create,go=Er.create,vo=Cr.create,wo=Tr.create,bo=Nr.create,yo=Pr.create,_o=Ar.create,xo=Ir.create,ko=Or.create,Eo=Ar.createWithPreprocess,Co=Fr.create,So=()=>Vr().optional(),To=()=>qr().optional(),No=()=>Kr().optional(),Po={string:e=>Jn.create({...e,coerce:!0}),number:e=>er.create({...e,coerce:!0}),boolean:e=>nr.create({...e,coerce:!0}),bigint:e=>tr.create({...e,coerce:!0}),date:e=>rr.create({...e,coerce:!0})},Ao=pn;var Io=Object.freeze({__proto__:null,defaultErrorMap:rn,setErrorMap:an,getErrorMap:sn,makeIssue:ln,EMPTY_PATH:cn,addIssueToContext:un,ParseStatus:dn,INVALID:pn,DIRTY:fn,OK:mn,isAborted:hn,isDirty:gn,isValid:vn,isAsync:wn,get util(){return Vt},get objectUtil(){return qt},ZodParsedType:Jt,getParsedType:Qt,ZodType:Tn,datetimeRegex:Gn,ZodString:Jn,ZodNumber:er,ZodBigInt:tr,ZodBoolean:nr,ZodDate:rr,ZodSymbol:or,ZodUndefined:ir,ZodNull:ar,ZodAny:sr,ZodUnknown:lr,ZodNever:cr,ZodVoid:ur,ZodArray:dr,ZodObject:fr,ZodUnion:mr,ZodDiscriminatedUnion:gr,ZodIntersection:wr,ZodTuple:br,ZodRecord:yr,ZodMap:_r,ZodSet:xr,ZodFunction:kr,ZodLazy:Er,ZodLiteral:Cr,ZodEnum:Tr,ZodNativeEnum:Nr,ZodPromise:Pr,ZodEffects:Ar,ZodTransformer:Ar,ZodOptional:Ir,ZodNullable:Or,ZodDefault:Rr,ZodCatch:Mr,ZodNaN:zr,BRAND:Lr,ZodBranded:jr,ZodPipeline:Fr,ZodReadonly:Dr,custom:Zr,Schema:Tn,ZodSchema:Tn,late:Hr,get ZodFirstPartyTypeKind(){return Ur},coerce:Po,any:eo,array:oo,bigint:Gr,boolean:Kr,date:Yr,discriminatedUnion:lo,effect:_o,enum:wo,function:ho,instanceof:Br,intersection:co,lazy:go,literal:vo,map:fo,nan:Wr,nativeEnum:bo,never:no,null:Qr,nullable:ko,number:qr,object:io,oboolean:No,onumber:To,optional:xo,ostring:So,pipeline:Co,preprocess:Eo,promise:yo,record:po,set:mo,strictObject:ao,string:Vr,symbol:Xr,transformer:_o,tuple:uo,undefined:Jr,union:so,unknown:to,void:ro,NEVER:Ao,ZodIssueCode:en,quotelessJson:tn,ZodError:nn}),Oo=5746,Ro="/ping/stagewise",Mo="stagewise",zo=Ut({server:{triggerAgentPrompt:{request:Io.object({prompt:Io.string()}),response:Io.object({result:Io.object({success:Io.boolean(),error:Io.string().optional(),output:Io.string().optional()})}),update:Io.object({updateText:Io.string()})}}});async function Lo(e=10,t=300){for(let r=0;r<e;r++){const e=Oo+r;try{const r=new AbortController,o=setTimeout((()=>r.abort()),t);try{const t=await fetch(`http://localhost:${e}${Ro}`,{signal:r.signal});if(clearTimeout(o),t.ok){const n=await t.text();if(n===Mo)return e}}catch(n){clearTimeout(o);continue}}catch(n){continue}}return null}const jo=K({bridge:null,isConnecting:!1,error:null});function Fo({children:e}){const[t,n]=fe({bridge:null,isConnecting:!0,error:null});return he((()=>{async function e(){try{const e=await Lo(),t=Xt(`ws://localhost:${e}`,zo);await t.connect(),n({bridge:t,isConnecting:!1,error:null})}catch(e){n({bridge:null,isConnecting:!1,error:e instanceof Error?e:new Error(String(e))})}}e()}),[]),J(jo.Provider,{value:t,children:e})}function Do(){const e=_e(jo);if(!e)throw new Error("useSRPCBridge must be used within an SRPCBridgeProvider");return e}function $o(e){const t={},n=["id","class","name","type","href","src","alt","for","placeholder"],r=[];for(let o=0;o<e.attributes.length;o++){const i=e.attributes[o];i.name.startsWith("data-")?r.push({name:i.name,value:i.value}):(n.includes(i.name.toLowerCase())||"style"!==i.name.toLowerCase())&&(t[i.name]=i.value)}return r.forEach((e=>{t[e.name]=e.value})),t}function Zo(e,t){var n;let r=`<element index="${t+1}">\n`;r+=`  <tag>${e.tagName.toLowerCase()}</tag>\n`;const o=e.id;o&&(r+=`  <id>${o}</id>\n`);const i=Array.from(e.classList).join(", ");i&&(r+=`  <classes>${i}</classes>\n`);const a=$o(e);if(Object.keys(a).length>0){r+="  <attributes>\n";for(const[e,t]of Object.entries(a))"class"===e.toLowerCase()&&i||(r+=`    <${e}>${t}</${e}>\n`);r+="  </attributes>\n"}const s=null==(n=e.innerText)?void 0:n.trim();if(s){const e=100;r+=`  <text>${s.length>e?`${s.substring(0,e)}...`:s}</text>\n`}if(r+="  <structural_context>\n",e.parentElement){const t=e.parentElement;r+="    <parent>\n",r+=`      <tag>${t.tagName.toLowerCase()}</tag>\n`,t.id&&(r+=`      <id>${t.id}</id>\n`);const n=Array.from(t.classList).join(", ");n&&(r+=`      <classes>${n}</classes>\n`),r+="    </parent>\n"}else r+="    <parent>No parent element found (likely root or disconnected)</parent>\n";r+="  </structural_context>\n";try{const t=window.getComputedStyle(e),n={color:t.color,backgroundColor:t.backgroundColor,fontSize:t.fontSize,fontWeight:t.fontWeight,display:t.display};r+="  <styles>\n";for(const[e,o]of Object.entries(n))r+=`    <${e}>${o}</${e}>\n`;r+="  </styles>\n"}catch(l){r+="  <styles>Could not retrieve computed styles</styles>\n"}return r+="</element>\n",r}function Ho(e,t,n){if(!e||0===e.length)return`\n    <request>\n      <user_goal>${t}</user_goal>\n      <url>${n}</url>\n  <context>No specific element was selected on the page. Please analyze the page code in general or ask for clarification.</context>\n</request>`.trim();let r="";return e.forEach(((e,t)=>{r+=Zo(e,t)})),`\n<request>\n  <user_goal>${t}</user_goal>\n  <url>${n}</url>\n  <selected_elements>\n    ${r.trim()}\n  </selected_elements>\n</request>`.trim()}const Uo={BOTTOM_CENTER:4},Bo=e=>{let t;const n=new Set,r=(e,r)=>{const o="function"===typeof e?e(t):e;if(!Object.is(o,t)){const e=t;t=(null!=r?r:"object"!==typeof o||null===o)?o:Object.assign({},t,o),n.forEach((n=>n(t,e)))}},o=()=>t,i=()=>l,a=e=>(n.add(e),()=>n.delete(e)),s={setState:r,getState:o,getInitialState:i,subscribe:a},l=t=e(r,o,s);return s},Vo=e=>e?Bo(e):Bo,qo=e=>e;function Wo(e,t=qo){const n=Ft.useSyncExternalStore(e.subscribe,(()=>t(e.getState())),(()=>t(e.getInitialState())));return Ft.useDebugValue(n),n}const Go=e=>{const t=Vo(e),n=e=>Wo(t,e);return Object.assign(n,t),n},Ko=e=>e?Go(e):Go;class Yo{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class Xo{constructor(e){this.generateIdentifier=e,this.kv=new Yo}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}}class Jo extends Xo{constructor(){super((e=>e.name)),this.classToAllowedProps=new Map}register(e,t){"object"===typeof t?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}}function Qo(e){if("values"in Object)return Object.values(e);const t=[];for(const n in e)e.hasOwnProperty(n)&&t.push(e[n]);return t}function ei(e,t){const n=Qo(e);if("find"in n)return n.find(t);const r=n;for(let o=0;o<r.length;o++){const e=r[o];if(t(e))return e}}function ti(e,t){Object.entries(e).forEach((([e,n])=>t(n,e)))}function ni(e,t){return-1!==e.indexOf(t)}function ri(e,t){for(let n=0;n<e.length;n++){const r=e[n];if(t(r))return r}}class oi{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return ei(this.transfomers,(t=>t.isApplicable(e)))}findByName(e){return this.transfomers[e]}}const ii=e=>Object.prototype.toString.call(e).slice(8,-1),ai=e=>"undefined"===typeof e,si=e=>null===e,li=e=>"object"===typeof e&&null!==e&&(e!==Object.prototype&&(null===Object.getPrototypeOf(e)||Object.getPrototypeOf(e)===Object.prototype)),ci=e=>li(e)&&0===Object.keys(e).length,ui=e=>Array.isArray(e),di=e=>"string"===typeof e,pi=e=>"number"===typeof e&&!isNaN(e),fi=e=>"boolean"===typeof e,mi=e=>e instanceof RegExp,hi=e=>e instanceof Map,gi=e=>e instanceof Set,vi=e=>"Symbol"===ii(e),wi=e=>e instanceof Date&&!isNaN(e.valueOf()),bi=e=>e instanceof Error,yi=e=>"number"===typeof e&&isNaN(e),_i=e=>fi(e)||si(e)||ai(e)||pi(e)||di(e)||vi(e),xi=e=>"bigint"===typeof e,ki=e=>e===1/0||e===-1/0,Ei=e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),Ci=e=>e instanceof URL,Si=e=>e.replace(/\./g,"\\."),Ti=e=>e.map(String).map(Si).join("."),Ni=e=>{const t=[];let n="";for(let o=0;o<e.length;o++){let r=e.charAt(o);const i="\\"===r&&"."===e.charAt(o+1);if(i){n+=".",o++;continue}const a="."===r;a?(t.push(n),n=""):n+=r}const r=n;return t.push(r),t};function Pi(e,t,n,r){return{isApplicable:e,annotation:t,transform:n,untransform:r}}const Ai=[Pi(ai,"undefined",(()=>null),(()=>{})),Pi(xi,"bigint",(e=>e.toString()),(e=>"undefined"!==typeof BigInt?BigInt(e):e)),Pi(wi,"Date",(e=>e.toISOString()),(e=>new Date(e))),Pi(bi,"Error",((e,t)=>{const n={name:e.name,message:e.message};return t.allowedErrorProps.forEach((t=>{n[t]=e[t]})),n}),((e,t)=>{const n=new Error(e.message);return n.name=e.name,n.stack=e.stack,t.allowedErrorProps.forEach((t=>{n[t]=e[t]})),n})),Pi(mi,"regexp",(e=>""+e),(e=>{const t=e.slice(1,e.lastIndexOf("/")),n=e.slice(e.lastIndexOf("/")+1);return new RegExp(t,n)})),Pi(gi,"set",(e=>[...e.values()]),(e=>new Set(e))),Pi(hi,"map",(e=>[...e.entries()]),(e=>new Map(e))),Pi((e=>yi(e)||ki(e)),"number",(e=>yi(e)?"NaN":e>0?"Infinity":"-Infinity"),Number),Pi((e=>0===e&&1/e===-1/0),"number",(()=>"-0"),Number),Pi(Ci,"URL",(e=>e.toString()),(e=>new URL(e)))];function Ii(e,t,n,r){return{isApplicable:e,annotation:t,transform:n,untransform:r}}const Oi=Ii(((e,t)=>{if(vi(e)){const n=!!t.symbolRegistry.getIdentifier(e);return n}return!1}),((e,t)=>{const n=t.symbolRegistry.getIdentifier(e);return["symbol",n]}),(e=>e.description),((e,t,n)=>{const r=n.symbolRegistry.getValue(t[1]);if(!r)throw new Error("Trying to deserialize unknown symbol");return r})),Ri=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce(((e,t)=>(e[t.name]=t,e)),{}),Mi=Ii(Ei,(e=>["typed-array",e.constructor.name]),(e=>[...e]),((e,t)=>{const n=Ri[t[1]];if(!n)throw new Error("Trying to deserialize unknown typed array");return new n(e)}));function zi(e,t){if(null==e?void 0:e.constructor){const n=!!t.classRegistry.getIdentifier(e.constructor);return n}return!1}const Li=Ii(zi,((e,t)=>{const n=t.classRegistry.getIdentifier(e.constructor);return["class",n]}),((e,t)=>{const n=t.classRegistry.getAllowedProps(e.constructor);if(!n)return{...e};const r={};return n.forEach((t=>{r[t]=e[t]})),r}),((e,t,n)=>{const r=n.classRegistry.getValue(t[1]);if(!r)throw new Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(r.prototype),e)})),ji=Ii(((e,t)=>!!t.customTransformerRegistry.findApplicable(e)),((e,t)=>{const n=t.customTransformerRegistry.findApplicable(e);return["custom",n.name]}),((e,t)=>{const n=t.customTransformerRegistry.findApplicable(e);return n.serialize(e)}),((e,t,n)=>{const r=n.customTransformerRegistry.findByName(t[1]);if(!r)throw new Error("Trying to deserialize unknown custom value");return r.deserialize(e)})),Fi=[Li,Oi,ji,Mi],Di=(e,t)=>{const n=ri(Fi,(n=>n.isApplicable(e,t)));if(n)return{value:n.transform(e,t),type:n.annotation(e,t)};const r=ri(Ai,(n=>n.isApplicable(e,t)));return r?{value:r.transform(e,t),type:r.annotation}:void 0},$i={};Ai.forEach((e=>{$i[e.annotation]=e}));const Zi=(e,t,n)=>{if(!ui(t)){const r=$i[t];if(!r)throw new Error("Unknown transformation: "+t);return r.untransform(e,n)}switch(t[0]){case"symbol":return Oi.untransform(e,t,n);case"class":return Li.untransform(e,t,n);case"custom":return ji.untransform(e,t,n);case"typed-array":return Mi.untransform(e,t,n);default:throw new Error("Unknown transformation: "+t)}},Hi=(e,t)=>{if(t>e.size)throw new Error("index out of bounds");const n=e.keys();while(t>0)n.next(),t--;return n.next().value};function Ui(e){if(ni(e,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(ni(e,"prototype"))throw new Error("prototype is not allowed as a property");if(ni(e,"constructor"))throw new Error("constructor is not allowed as a property")}const Bi=(e,t)=>{Ui(t);for(let n=0;n<t.length;n++){const r=t[n];if(gi(e))e=Hi(e,+r);else if(hi(e)){const o=+r,i=0===+t[++n]?"key":"value",a=Hi(e,o);switch(i){case"key":e=a;break;case"value":e=e.get(a);break}}else e=e[r]}return e},Vi=(e,t,n)=>{if(Ui(t),0===t.length)return n(e);let r=e;for(let i=0;i<t.length-1;i++){const e=t[i];if(ui(r)){const t=+e;r=r[t]}else if(li(r))r=r[e];else if(gi(r)){const t=+e;r=Hi(r,t)}else if(hi(r)){const n=i===t.length-2;if(n)break;const o=+e,a=0===+t[++i]?"key":"value",s=Hi(r,o);switch(a){case"key":r=s;break;case"value":r=r.get(s);break}}}const o=t[t.length-1];if(ui(r)?r[+o]=n(r[+o]):li(r)&&(r[o]=n(r[o])),gi(r)){const e=Hi(r,+o),t=n(e);e!==t&&(r.delete(e),r.add(t))}if(hi(r)){const e=+t[t.length-2],i=Hi(r,e),a=0===+o?"key":"value";switch(a){case"key":{const e=n(i);r.set(e,r.get(i)),e!==i&&r.delete(i);break}case"value":r.set(i,n(r.get(i)));break}}return e};function qi(e,t,n=[]){if(!e)return;if(!ui(e))return void ti(e,((e,r)=>qi(e,t,[...n,...Ni(r)])));const[r,o]=e;o&&ti(o,((e,r)=>{qi(e,t,[...n,...Ni(r)])})),t(r,n)}function Wi(e,t,n){return qi(t,((t,r)=>{e=Vi(e,r,(e=>Zi(e,t,n)))})),e}function Gi(e,t){function n(t,n){const r=Bi(e,Ni(n));t.map(Ni).forEach((t=>{e=Vi(e,t,(()=>r))}))}if(ui(t)){const[r,o]=t;r.forEach((t=>{e=Vi(e,Ni(t),(()=>e))})),o&&ti(o,n)}else ti(t,n);return e}const Ki=(e,t)=>li(e)||ui(e)||hi(e)||gi(e)||zi(e,t);function Yi(e,t,n){const r=n.get(e);r?r.push(t):n.set(e,[t])}function Xi(e,t){const n={};let r;return e.forEach((e=>{if(e.length<=1)return;t||(e=e.map((e=>e.map(String))).sort(((e,t)=>e.length-t.length)));const[o,...i]=e;0===o.length?r=i.map(Ti):n[Ti(o)]=i.map(Ti)})),r?ci(n)?[r]:[r,n]:ci(n)?void 0:n}const Ji=(e,t,n,r,o=[],i=[],a=new Map)=>{const s=_i(e);if(!s){Yi(e,o,t);const n=a.get(e);if(n)return r?{transformedValue:null}:n}if(!Ki(e,n)){const t=Di(e,n),r=t?{transformedValue:t.value,annotations:[t.type]}:{transformedValue:e};return s||a.set(e,r),r}if(ni(i,e))return{transformedValue:null};const l=Di(e,n),c=(null==l?void 0:l.value)??e,u=ui(c)?[]:{},d={};ti(c,((s,l)=>{if("__proto__"===l||"constructor"===l||"prototype"===l)throw new Error(`Detected property ${l}. This is a prototype pollution risk, please remove it from your object.`);const c=Ji(s,t,n,r,[...o,l],[...i,e],a);u[l]=c.transformedValue,ui(c.annotations)?d[l]=c.annotations:li(c.annotations)&&ti(c.annotations,((e,t)=>{d[Si(l)+"."+t]=e}))}));const p=ci(d)?{transformedValue:u,annotations:l?[l.type]:void 0}:{transformedValue:u,annotations:l?[l.type,d]:d};return s||a.set(e,p),p};function Qi(e){return Object.prototype.toString.call(e).slice(8,-1)}function ea(e){return"Array"===Qi(e)}function ta(e){if("Object"!==Qi(e))return!1;const t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}function na(e,t,n,r,o){const i={}.propertyIsEnumerable.call(r,t)?"enumerable":"nonenumerable";"enumerable"===i&&(e[t]=n),o&&"nonenumerable"===i&&Object.defineProperty(e,t,{value:n,enumerable:!1,writable:!0,configurable:!0})}function ra(e,t={}){if(ea(e))return e.map((e=>ra(e,t)));if(!ta(e))return e;const n=Object.getOwnPropertyNames(e),r=Object.getOwnPropertySymbols(e);return[...n,...r].reduce(((n,r)=>{if(ea(t.props)&&!t.props.includes(r))return n;const o=e[r],i=ra(o,t);return na(n,r,i,e,t.nonenumerable),n}),{})}class oa{constructor({dedupe:e=!1}={}){this.classRegistry=new Jo,this.symbolRegistry=new Xo((e=>e.description??"")),this.customTransformerRegistry=new oi,this.allowedErrorProps=[],this.dedupe=e}serialize(e){const t=new Map,n=Ji(e,t,this,this.dedupe),r={json:n.transformedValue};n.annotations&&(r.meta={...r.meta,values:n.annotations});const o=Xi(t,this.dedupe);return o&&(r.meta={...r.meta,referentialEqualities:o}),r}deserialize(e){const{json:t,meta:n}=e;let r=ra(t);return(null==n?void 0:n.values)&&(r=Wi(r,n.values,this)),(null==n?void 0:n.referentialEqualities)&&(r=Gi(r,n.referentialEqualities)),r}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}}function ia(e,t){let n;try{n=e()}catch(o){return}const r={getItem:e=>{var t;const r=e=>null===e?null:JSON.parse(e,void 0),o=null!=(t=n.getItem(e))?t:null;return o instanceof Promise?o.then(r):r(o)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)};return r}oa.defaultInstance=new oa,oa.serialize=oa.defaultInstance.serialize.bind(oa.defaultInstance),oa.deserialize=oa.defaultInstance.deserialize.bind(oa.defaultInstance),oa.stringify=oa.defaultInstance.stringify.bind(oa.defaultInstance),oa.parse=oa.defaultInstance.parse.bind(oa.defaultInstance),oa.registerClass=oa.defaultInstance.registerClass.bind(oa.defaultInstance),oa.registerSymbol=oa.defaultInstance.registerSymbol.bind(oa.defaultInstance),oa.registerCustom=oa.defaultInstance.registerCustom.bind(oa.defaultInstance),oa.allowErrorProps=oa.defaultInstance.allowErrorProps.bind(oa.defaultInstance),oa.serialize,oa.deserialize,oa.stringify,oa.parse,oa.registerClass,oa.registerCustom,oa.registerSymbol,oa.allowErrorProps;const aa=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(e){return aa(e)(n)},catch(e){return this}}}catch(n){return{then(e){return this},catch(e){return aa(e)(n)}}}},sa=(e,t)=>(n,r,o)=>{let i={storage:ia((()=>localStorage)),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},a=!1;const s=new Set,l=new Set;let c=i.storage;if(!c)return e(((...e)=>{n(...e)}),r,o);const u=()=>{const e=i.partialize({...r()});return c.setItem(i.name,{state:e,version:i.version})},d=o.setState;o.setState=(e,t)=>{d(e,t),u()};const p=e(((...e)=>{n(...e),u()}),r,o);let f;o.getInitialState=()=>p;const m=()=>{var e,t;if(!c)return;a=!1,s.forEach((e=>{var t;return e(null!=(t=r())?t:p)}));const o=(null==(t=i.onRehydrateStorage)?void 0:t.call(i,null!=(e=r())?e:p))||void 0;return aa(c.getItem.bind(c))(i.name).then((e=>{if(e){if("number"!==typeof e.version||e.version===i.version)return[!1,e.state];if(i.migrate){const t=i.migrate(e.state,e.version);return t instanceof Promise?t.then((e=>[!0,e])):[!0,t]}}return[!1,void 0]})).then((e=>{var t;const[o,a]=e;if(f=i.merge(a,null!=(t=r())?t:p),n(f,!0),o)return u()})).then((()=>{null==o||o(f,void 0),f=r(),a=!0,l.forEach((e=>e(f)))})).catch((e=>{null==o||o(void 0,e)}))};return o.persist={setOptions:e=>{i={...i,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>m(),hasHydrated:()=>a,onHydrate:e=>(s.add(e),()=>{s.delete(e)}),onFinishHydration:e=>(l.add(e),()=>{l.delete(e)})},i.skipHydration||m(),f||p},la=sa,ca=e=>{const t=e;return{appBlockRequestList:[],appUnblockRequestList:[],lastBlockRequestNumber:0,lastUnblockRequestNumber:0,isMainAppBlocked:!1,requestMainAppBlock:()=>{let e=0;return t((t=>(e=t.lastBlockRequestNumber+1,{appBlockRequestList:[...t.appBlockRequestList,e],lastBlockRequestNumber:e,isMainAppBlocked:0===t.appUnblockRequestList.length}))),e},requestMainAppUnblock:()=>{let e=0;return t((t=>(e=t.lastUnblockRequestNumber+1,{appUnblockRequestList:[...t.appUnblockRequestList,e],lastUnblockRequestNumber:e,isMainAppBlocked:!1}))),e},discardMainAppBlock:e=>{t((t=>{const n=t.appBlockRequestList.filter((t=>t!==e));return{appBlockRequestList:n,isMainAppBlocked:n.length>0&&0===t.appUnblockRequestList.length}}))},discardMainAppUnblock:e=>{t((t=>{const n=t.appUnblockRequestList.filter((t=>t!==e));return{appUnblockRequestList:n,isMainAppBlocked:t.appBlockRequestList.length>0&&0===n.length}}))},toolbarPosition:Uo.BOTTOM_CENTER,setToolbarPosition:e=>t((()=>({toolbarPosition:e}))),toolbarBoxRef:E(),setToolbarBoxRef:e=>t((()=>({toolbarBoxRef:e}))),unsetToolbarBoxRef:()=>t((()=>({toolbarBoxRef:E()}))),minimized:!1,minimize:()=>t((()=>({minimized:!0}))),expand:()=>t((()=>({minimized:!1}))),promotedOnStartup:!1,promotionFinished:()=>t((()=>({promotedOnStartup:!0})))}};function ua(e){return{getItem:t=>{const n=e.getItem(t);return n?oa.parse(n):null},setItem:(t,n)=>{e.setItem(t,oa.stringify(n))},removeItem:t=>e.removeItem(t)}}const da=Ko(la(ca,{name:"stgws:companion",storage:ua(sessionStorage),partialize:e=>({toolbarPosition:e.toolbarPosition})})),pa=K({chats:[],currentChatId:null,createChat:()=>"",deleteChat:()=>{},setCurrentChat:()=>{},setChatInput:()=>{},addChatDomContext:()=>{},removeChatDomContext:()=>{},addMessage:()=>{},chatAreaState:"hidden",setChatAreaState:()=>{},isPromptCreationActive:!1,startPromptCreation:()=>{},stopPromptCreation:()=>{}}),fa=({children:e})=>{const[t,n]=fe([{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]),[r,o]=fe("new_chat"),[i,a]=fe("hidden"),[s,l]=fe(!1),c=da((e=>e.minimized));he((()=>{c&&(l(!1),a("hidden"))}),[c]);const{bridge:u}=Do(),d=ye((()=>{const e=crypto.randomUUID(),t={id:e,title:null,messages:[],inputValue:"",domContextElements:[]};return n((e=>[...e,t])),o(e),e}),[]),p=ye((e=>{n((t=>{const n=t.filter((t=>t.id!==e));return 0===n.length?[{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]:n})),r===e&&n((e=>(o(e[0].id),e)))}),[r]),f=ye((e=>{o(e)}),[]),m=ye(((e,t)=>{n((n=>n.map((n=>n.id===e?{...n,inputValue:t}:n))))}),[]),h=ye((()=>{l(!0),"hidden"===i&&a("compact")}),[i]),g=ye((()=>{l(!1),n((e=>e.map((e=>e.id===r?{...e,domContextElements:[]}:e)))),"compact"===i&&a("hidden")}),[r,i]),v=ye((e=>{a(e),"hidden"===e&&g()}),[a,g]),w=ye(((e,t)=>{n((n=>n.map((n=>n.id===e?{...n,domContextElements:[...n.domContextElements,t]}:n))))}),[]),b=ye(((e,t)=>{n((n=>n.map((n=>n.id===e?{...n,domContextElements:n.domContextElements.filter((e=>e!==t))}:n))))}),[]),y=ye(((e,r)=>{if(!r.trim())return;const o=t.find((t=>t.id===e)),s=Ho(null==o?void 0:o.domContextElements,r,window.location.href),c={id:crypto.randomUUID(),content:r.trim(),sender:"user",type:"regular",timestamp:new Date};async function d(){u&&await u.call.triggerAgentPrompt({prompt:s},{onUpdate:e=>{}})}d(),l(!1),"hidden"===i&&a("compact"),n((t=>t.map((t=>t.id===e?{...t,messages:[...t.messages,c],inputValue:"",domContextElements:[]}:t))))}),[i,u,t,l,a]),_={chats:t,currentChatId:r,createChat:d,deleteChat:p,setCurrentChat:f,setChatInput:m,addMessage:y,chatAreaState:i,setChatAreaState:v,isPromptCreationActive:s,startPromptCreation:h,stopPromptCreation:g,addChatDomContext:w,removeChatDomContext:b};return J(pa.Provider,{value:_,children:e})};function ma(){const e=_e(pa);if(!e)throw new Error("useChatState must be used within a ChatStateProvider");return e}function ha(e,t){const n=ve(void 0),r=be((()=>t&&t>0?1e3/t:0),[t]),o=ve(0),i=ye((t=>{t-o.current>=r&&(e(),o.current=t),n.current=requestAnimationFrame(i)}),[e,r]);he((()=>((!t||t>0)&&(n.current=requestAnimationFrame(i)),()=>{n.current&&(cancelAnimationFrame(n.current),n.current=void 0)})),[t,i])}const ga=K(new URL(window.location.href));function va({children:e}){const[t,n]=fe(new URL(window.location.href)),r=ye((()=>{n(new URL(window.location.href))}),[]);return ha(r,15),J(ga.Provider,{value:t,children:e})}function wa({children:e,config:t}){return J(va,{children:J(Fo,{children:J(Re,{plugins:(null==t?void 0:t.plugins)||[],children:J(fa,{children:e})})})})}function ba(e,t,n,r=window){he((()=>{if("undefined"!==typeof window&&r)return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t)}),[e,t,r,n])}function ya(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=ya(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function _a(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=ya(e))&&(r&&(r+=" "),r+=t);return r}const xa="-",ka=e=>{const t=Ta(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e,o=e=>{const n=e.split(xa);return""===n[0]&&1!==n.length&&n.shift(),Ea(n,t)||Sa(e)},i=(e,t)=>{const o=n[e]||[];return t&&r[e]?[...o,...r[e]]:o};return{getClassGroupId:o,getConflictingClassGroupIds:i}},Ea=(e,t)=>{var n;if(0===e.length)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),i=o?Ea(e.slice(1),o):void 0;if(i)return i;if(0===t.validators.length)return;const a=e.join(xa);return null==(n=t.validators.find((({validator:e})=>e(a))))?void 0:n.classGroupId},Ca=/^\[(.+)\]$/,Sa=e=>{if(Ca.test(e)){const t=Ca.exec(e)[1],n=null==t?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Ta=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const o in n)Na(n[o],r,o,t);return r},Na=(e,t,n,r)=>{e.forEach((e=>{if("string"!==typeof e){if("function"===typeof e)return Aa(e)?void Na(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach((([e,o])=>{Na(o,Pa(t,e),n,r)}))}else{const r=""===e?t:Pa(t,e);r.classGroupId=n}}))},Pa=(e,t)=>{let n=e;return t.split(xa).forEach((e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)})),n},Aa=e=>e.isThemeGetter,Ia=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(o,i)=>{n.set(o,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):o(e,t)}}},Oa="!",Ra=":",Ma=Ra.length,za=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=e=>{const t=[];let n,r=0,o=0,i=0;for(let u=0;u<e.length;u++){let a=e[u];if(0===r&&0===o){if(a===Ra){t.push(e.slice(i,u)),i=u+Ma;continue}if("/"===a){n=u;continue}}"["===a?r++:"]"===a?r--:"("===a?o++:")"===a&&o--}const a=0===t.length?e:e.substring(i),s=La(a),l=s!==a,c=n&&n>i?n-i:void 0;return{modifiers:t,hasImportantModifier:l,baseClassName:s,maybePostfixModifierPosition:c}};if(t){const e=t+Ra,n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){const e=r;r=t=>n({className:t,parseClassName:e})}return r},La=e=>e.endsWith(Oa)?e.substring(0,e.length-1):e.startsWith(Oa)?e.substring(1):e,ja=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map((e=>[e,!0]))),n=e=>{if(e.length<=1)return e;const n=[];let r=[];return e.forEach((e=>{const o="["===e[0]||t[e];o?(n.push(...r.sort(),e),r=[]):r.push(e)})),n.push(...r.sort()),n};return n},Fa=e=>({cache:Ia(e.cacheSize),parseClassName:za(e),sortModifiers:ja(e),...ka(e)}),Da=/\s+/,$a=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o,sortModifiers:i}=t,a=[],s=e.trim().split(Da);let l="";for(let c=s.length-1;c>=0;c-=1){const e=s[c],{isExternal:t,modifiers:u,hasImportantModifier:d,baseClassName:p,maybePostfixModifierPosition:f}=n(e);if(t){l=e+(l.length>0?" "+l:l);continue}let m=!!f,h=r(m?p.substring(0,f):p);if(!h){if(!m){l=e+(l.length>0?" "+l:l);continue}if(h=r(p),!h){l=e+(l.length>0?" "+l:l);continue}m=!1}const g=i(u).join(":"),v=d?g+Oa:g,w=v+h;if(a.includes(w))continue;a.push(w);const b=o(h,m);for(let n=0;n<b.length;++n){const e=b[n];a.push(v+e)}l=e+(l.length>0?" "+l:l)}return l};function Za(){let e,t,n=0,r="";while(n<arguments.length)(e=arguments[n++])&&(t=Ha(e))&&(r&&(r+=" "),r+=t);return r}const Ha=e=>{if("string"===typeof e)return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Ha(e[r]))&&(n&&(n+=" "),n+=t);return n};function Ua(e,...t){let n,r,o,i=a;function a(a){const l=t.reduce(((e,t)=>t(e)),e());return n=Fa(l),r=n.cache.get,o=n.cache.set,i=s,s(a)}function s(e){const t=r(e);if(t)return t;const i=$a(e,n);return o(e,i),i}return function(){return i(Za.apply(null,arguments))}}const Ba=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},Va=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,qa=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Wa=/^\d+\/\d+$/,Ga=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ka=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ya=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Xa=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Ja=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Qa=e=>Wa.test(e),es=e=>!!e&&!Number.isNaN(Number(e)),ts=e=>!!e&&Number.isInteger(Number(e)),ns=e=>e.endsWith("%")&&es(e.slice(0,-1)),rs=e=>Ga.test(e),os=()=>!0,is=e=>Ka.test(e)&&!Ya.test(e),as=()=>!1,ss=e=>Xa.test(e),ls=e=>Ja.test(e),cs=e=>!ds(e)&&!vs(e),us=e=>Es(e,Ns,as),ds=e=>Va.test(e),ps=e=>Es(e,Ps,is),fs=e=>Es(e,As,es),ms=e=>Es(e,Ss,as),hs=e=>Es(e,Ts,ls),gs=e=>Es(e,Os,ss),vs=e=>qa.test(e),ws=e=>Cs(e,Ps),bs=e=>Cs(e,Is),ys=e=>Cs(e,Ss),_s=e=>Cs(e,Ns),xs=e=>Cs(e,Ts),ks=e=>Cs(e,Os,!0),Es=(e,t,n)=>{const r=Va.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},Cs=(e,t,n=!1)=>{const r=qa.exec(e);return!!r&&(r[1]?t(r[1]):n)},Ss=e=>"position"===e||"percentage"===e,Ts=e=>"image"===e||"url"===e,Ns=e=>"length"===e||"size"===e||"bg-size"===e,Ps=e=>"length"===e,As=e=>"number"===e,Is=e=>"family-name"===e,Os=e=>"shadow"===e,Rs=()=>{const e=Ba("color"),t=Ba("font"),n=Ba("text"),r=Ba("font-weight"),o=Ba("tracking"),i=Ba("leading"),a=Ba("breakpoint"),s=Ba("container"),l=Ba("spacing"),c=Ba("radius"),u=Ba("shadow"),d=Ba("inset-shadow"),p=Ba("text-shadow"),f=Ba("drop-shadow"),m=Ba("blur"),h=Ba("perspective"),g=Ba("aspect"),v=Ba("ease"),w=Ba("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],_=()=>[...y(),vs,ds],x=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto","contain","none"],E=()=>[vs,ds,l],C=()=>[Qa,"full","auto",...E()],S=()=>[ts,"none","subgrid",vs,ds],T=()=>["auto",{span:["full",ts,vs,ds]},ts,vs,ds],N=()=>[ts,"auto",vs,ds],P=()=>["auto","min","max","fr",vs,ds],A=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],O=()=>["auto",...E()],R=()=>[Qa,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...E()],M=()=>[e,vs,ds],z=()=>[...y(),ys,ms,{position:[vs,ds]}],L=()=>["no-repeat",{repeat:["","x","y","space","round"]}],j=()=>["auto","cover","contain",_s,us,{size:[vs,ds]}],F=()=>[ns,ws,ps],D=()=>["","none","full",c,vs,ds],$=()=>["",es,ws,ps],Z=()=>["solid","dashed","dotted","double"],H=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],U=()=>[es,ns,ys,ms],B=()=>["","none",m,vs,ds],V=()=>["none",es,vs,ds],q=()=>["none",es,vs,ds],W=()=>[es,vs,ds],G=()=>[Qa,"full",...E()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[rs],breakpoint:[rs],color:[os],container:[rs],"drop-shadow":[rs],ease:["in","out","in-out"],font:[cs],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[rs],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[rs],shadow:[rs],spacing:["px",es],text:[rs],"text-shadow":[rs],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Qa,ds,vs,g]}],container:["container"],columns:[{columns:[es,ds,vs,s]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:_()}],overflow:[{overflow:x()}],"overflow-x":[{"overflow-x":x()}],"overflow-y":[{"overflow-y":x()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:C()}],"inset-x":[{"inset-x":C()}],"inset-y":[{"inset-y":C()}],start:[{start:C()}],end:[{end:C()}],top:[{top:C()}],right:[{right:C()}],bottom:[{bottom:C()}],left:[{left:C()}],visibility:["visible","invisible","collapse"],z:[{z:[ts,"auto",vs,ds]}],basis:[{basis:[Qa,"full","auto",s,...E()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[es,Qa,"auto","initial","none",ds]}],grow:[{grow:["",es,vs,ds]}],shrink:[{shrink:["",es,vs,ds]}],order:[{order:[ts,"first","last","none",vs,ds]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:T()}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:T()}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":P()}],"auto-rows":[{"auto-rows":P()}],gap:[{gap:E()}],"gap-x":[{"gap-x":E()}],"gap-y":[{"gap-y":E()}],"justify-content":[{justify:[...A(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...A()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":A()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:E()}],px:[{px:E()}],py:[{py:E()}],ps:[{ps:E()}],pe:[{pe:E()}],pt:[{pt:E()}],pr:[{pr:E()}],pb:[{pb:E()}],pl:[{pl:E()}],m:[{m:O()}],mx:[{mx:O()}],my:[{my:O()}],ms:[{ms:O()}],me:[{me:O()}],mt:[{mt:O()}],mr:[{mr:O()}],mb:[{mb:O()}],ml:[{ml:O()}],"space-x":[{"space-x":E()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":E()}],"space-y-reverse":["space-y-reverse"],size:[{size:R()}],w:[{w:[s,"screen",...R()]}],"min-w":[{"min-w":[s,"screen","none",...R()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...R()]}],h:[{h:["screen",...R()]}],"min-h":[{"min-h":["screen","none",...R()]}],"max-h":[{"max-h":["screen",...R()]}],"font-size":[{text:["base",n,ws,ps]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,vs,fs]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ns,ds]}],"font-family":[{font:[bs,ds,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,vs,ds]}],"line-clamp":[{"line-clamp":[es,"none",vs,fs]}],leading:[{leading:[i,...E()]}],"list-image":[{"list-image":["none",vs,ds]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",vs,ds]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:M()}],"text-color":[{text:M()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Z(),"wavy"]}],"text-decoration-thickness":[{decoration:[es,"from-font","auto",vs,ps]}],"text-decoration-color":[{decoration:M()}],"underline-offset":[{"underline-offset":[es,"auto",vs,ds]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",vs,ds]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",vs,ds]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:z()}],"bg-repeat":[{bg:L()}],"bg-size":[{bg:j()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ts,vs,ds],radial:["",vs,ds],conic:[ts,vs,ds]},xs,hs]}],"bg-color":[{bg:M()}],"gradient-from-pos":[{from:F()}],"gradient-via-pos":[{via:F()}],"gradient-to-pos":[{to:F()}],"gradient-from":[{from:M()}],"gradient-via":[{via:M()}],"gradient-to":[{to:M()}],rounded:[{rounded:D()}],"rounded-s":[{"rounded-s":D()}],"rounded-e":[{"rounded-e":D()}],"rounded-t":[{"rounded-t":D()}],"rounded-r":[{"rounded-r":D()}],"rounded-b":[{"rounded-b":D()}],"rounded-l":[{"rounded-l":D()}],"rounded-ss":[{"rounded-ss":D()}],"rounded-se":[{"rounded-se":D()}],"rounded-ee":[{"rounded-ee":D()}],"rounded-es":[{"rounded-es":D()}],"rounded-tl":[{"rounded-tl":D()}],"rounded-tr":[{"rounded-tr":D()}],"rounded-br":[{"rounded-br":D()}],"rounded-bl":[{"rounded-bl":D()}],"border-w":[{border:$()}],"border-w-x":[{"border-x":$()}],"border-w-y":[{"border-y":$()}],"border-w-s":[{"border-s":$()}],"border-w-e":[{"border-e":$()}],"border-w-t":[{"border-t":$()}],"border-w-r":[{"border-r":$()}],"border-w-b":[{"border-b":$()}],"border-w-l":[{"border-l":$()}],"divide-x":[{"divide-x":$()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":$()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Z(),"hidden","none"]}],"divide-style":[{divide:[...Z(),"hidden","none"]}],"border-color":[{border:M()}],"border-color-x":[{"border-x":M()}],"border-color-y":[{"border-y":M()}],"border-color-s":[{"border-s":M()}],"border-color-e":[{"border-e":M()}],"border-color-t":[{"border-t":M()}],"border-color-r":[{"border-r":M()}],"border-color-b":[{"border-b":M()}],"border-color-l":[{"border-l":M()}],"divide-color":[{divide:M()}],"outline-style":[{outline:[...Z(),"none","hidden"]}],"outline-offset":[{"outline-offset":[es,vs,ds]}],"outline-w":[{outline:["",es,ws,ps]}],"outline-color":[{outline:M()}],shadow:[{shadow:["","none",u,ks,gs]}],"shadow-color":[{shadow:M()}],"inset-shadow":[{"inset-shadow":["none",d,ks,gs]}],"inset-shadow-color":[{"inset-shadow":M()}],"ring-w":[{ring:$()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:M()}],"ring-offset-w":[{"ring-offset":[es,ps]}],"ring-offset-color":[{"ring-offset":M()}],"inset-ring-w":[{"inset-ring":$()}],"inset-ring-color":[{"inset-ring":M()}],"text-shadow":[{"text-shadow":["none",p,ks,gs]}],"text-shadow-color":[{"text-shadow":M()}],opacity:[{opacity:[es,vs,ds]}],"mix-blend":[{"mix-blend":[...H(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":H()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[es]}],"mask-image-linear-from-pos":[{"mask-linear-from":U()}],"mask-image-linear-to-pos":[{"mask-linear-to":U()}],"mask-image-linear-from-color":[{"mask-linear-from":M()}],"mask-image-linear-to-color":[{"mask-linear-to":M()}],"mask-image-t-from-pos":[{"mask-t-from":U()}],"mask-image-t-to-pos":[{"mask-t-to":U()}],"mask-image-t-from-color":[{"mask-t-from":M()}],"mask-image-t-to-color":[{"mask-t-to":M()}],"mask-image-r-from-pos":[{"mask-r-from":U()}],"mask-image-r-to-pos":[{"mask-r-to":U()}],"mask-image-r-from-color":[{"mask-r-from":M()}],"mask-image-r-to-color":[{"mask-r-to":M()}],"mask-image-b-from-pos":[{"mask-b-from":U()}],"mask-image-b-to-pos":[{"mask-b-to":U()}],"mask-image-b-from-color":[{"mask-b-from":M()}],"mask-image-b-to-color":[{"mask-b-to":M()}],"mask-image-l-from-pos":[{"mask-l-from":U()}],"mask-image-l-to-pos":[{"mask-l-to":U()}],"mask-image-l-from-color":[{"mask-l-from":M()}],"mask-image-l-to-color":[{"mask-l-to":M()}],"mask-image-x-from-pos":[{"mask-x-from":U()}],"mask-image-x-to-pos":[{"mask-x-to":U()}],"mask-image-x-from-color":[{"mask-x-from":M()}],"mask-image-x-to-color":[{"mask-x-to":M()}],"mask-image-y-from-pos":[{"mask-y-from":U()}],"mask-image-y-to-pos":[{"mask-y-to":U()}],"mask-image-y-from-color":[{"mask-y-from":M()}],"mask-image-y-to-color":[{"mask-y-to":M()}],"mask-image-radial":[{"mask-radial":[vs,ds]}],"mask-image-radial-from-pos":[{"mask-radial-from":U()}],"mask-image-radial-to-pos":[{"mask-radial-to":U()}],"mask-image-radial-from-color":[{"mask-radial-from":M()}],"mask-image-radial-to-color":[{"mask-radial-to":M()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[es]}],"mask-image-conic-from-pos":[{"mask-conic-from":U()}],"mask-image-conic-to-pos":[{"mask-conic-to":U()}],"mask-image-conic-from-color":[{"mask-conic-from":M()}],"mask-image-conic-to-color":[{"mask-conic-to":M()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:z()}],"mask-repeat":[{mask:L()}],"mask-size":[{mask:j()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",vs,ds]}],filter:[{filter:["","none",vs,ds]}],blur:[{blur:B()}],brightness:[{brightness:[es,vs,ds]}],contrast:[{contrast:[es,vs,ds]}],"drop-shadow":[{"drop-shadow":["","none",f,ks,gs]}],"drop-shadow-color":[{"drop-shadow":M()}],grayscale:[{grayscale:["",es,vs,ds]}],"hue-rotate":[{"hue-rotate":[es,vs,ds]}],invert:[{invert:["",es,vs,ds]}],saturate:[{saturate:[es,vs,ds]}],sepia:[{sepia:["",es,vs,ds]}],"backdrop-filter":[{"backdrop-filter":["","none",vs,ds]}],"backdrop-blur":[{"backdrop-blur":B()}],"backdrop-brightness":[{"backdrop-brightness":[es,vs,ds]}],"backdrop-contrast":[{"backdrop-contrast":[es,vs,ds]}],"backdrop-grayscale":[{"backdrop-grayscale":["",es,vs,ds]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[es,vs,ds]}],"backdrop-invert":[{"backdrop-invert":["",es,vs,ds]}],"backdrop-opacity":[{"backdrop-opacity":[es,vs,ds]}],"backdrop-saturate":[{"backdrop-saturate":[es,vs,ds]}],"backdrop-sepia":[{"backdrop-sepia":["",es,vs,ds]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":E()}],"border-spacing-x":[{"border-spacing-x":E()}],"border-spacing-y":[{"border-spacing-y":E()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",vs,ds]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[es,"initial",vs,ds]}],ease:[{ease:["linear","initial",v,vs,ds]}],delay:[{delay:[es,vs,ds]}],animate:[{animate:["none",w,vs,ds]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,vs,ds]}],"perspective-origin":[{"perspective-origin":_()}],rotate:[{rotate:V()}],"rotate-x":[{"rotate-x":V()}],"rotate-y":[{"rotate-y":V()}],"rotate-z":[{"rotate-z":V()}],scale:[{scale:q()}],"scale-x":[{"scale-x":q()}],"scale-y":[{"scale-y":q()}],"scale-z":[{"scale-z":q()}],"scale-3d":["scale-3d"],skew:[{skew:W()}],"skew-x":[{"skew-x":W()}],"skew-y":[{"skew-y":W()}],transform:[{transform:[vs,ds,"","none","gpu","cpu"]}],"transform-origin":[{origin:_()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:G()}],"translate-x":[{"translate-x":G()}],"translate-y":[{"translate-y":G()}],"translate-z":[{"translate-z":G()}],"translate-none":["translate-none"],accent:[{accent:M()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:M()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",vs,ds]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",vs,ds]}],fill:[{fill:["none",...M()]}],"stroke-w":[{stroke:[es,ws,ps,fs]}],stroke:[{stroke:["none",...M()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ms=(e,{cacheSize:t,prefix:n,experimentalParseClassName:r,extend:o={},override:i={}})=>(zs(e,"cacheSize",t),zs(e,"prefix",n),zs(e,"experimentalParseClassName",r),Ls(e.theme,i.theme),Ls(e.classGroups,i.classGroups),Ls(e.conflictingClassGroups,i.conflictingClassGroups),Ls(e.conflictingClassGroupModifiers,i.conflictingClassGroupModifiers),zs(e,"orderSensitiveModifiers",i.orderSensitiveModifiers),js(e.theme,o.theme),js(e.classGroups,o.classGroups),js(e.conflictingClassGroups,o.conflictingClassGroups),js(e.conflictingClassGroupModifiers,o.conflictingClassGroupModifiers),Fs(e,o,"orderSensitiveModifiers"),e),zs=(e,t,n)=>{void 0!==n&&(e[t]=n)},Ls=(e,t)=>{if(t)for(const n in t)zs(e,n,t[n])},js=(e,t)=>{if(t)for(const n in t)Fs(e,t,n)},Fs=(e,t,n)=>{const r=t[n];void 0!==r&&(e[n]=e[n]?e[n].concat(r):r)},Ds=(e,...t)=>"function"===typeof e?Ua(Rs,e,...t):Ua((()=>Ms(Rs(),e)),...t),$s="stagewise-companion-anchor";function Zs(e,t){const n=document.elementsFromPoint(e,t),r=n.find((n=>"STAGEWISE-COMPANION-ANCHOR"!==n.nodeName&&!n.closest($s)&&!n.closest("svg")&&Hs(n,e,t)))||document.body;return r}const Hs=(e,t,n)=>{const r=e.getBoundingClientRect(),o=t>r.left&&t<r.left+r.width,i=n>r.top&&n<r.top+r.height;return o&&i};var Us=(e=>(e[e["ESC"]=0]="ESC",e[e["CTRL_ALT_C"]=1]="CTRL_ALT_C",e))(Us||{});const Bs={[0]:{keyComboDefault:"Esc",keyComboMac:"esc",isEventMatching:e=>"Escape"===e.code},[1]:{keyComboDefault:"Ctrl+Alt+C",keyComboMac:"⌘+⌥+C",isEventMatching:e=>"KeyC"===e.code&&(e.ctrlKey||e.metaKey)&&e.altKey}},Vs=Ds({extend:{classGroups:{"bg-image":["bg-gradient","bg-gradient-light-1","bg-gradient-light-2","bg-gradient-light-3"]}}});function qs(...e){return Vs(_a(e))}function Ws(){const{startPromptCreation:e,stopPromptCreation:t}=ma(),n=be((()=>({[Us.CTRL_ALT_C]:()=>{e()},[Us.ESC]:()=>{t()}})),[e,t]),r=ye((e=>{for(const[t,r]of Object.entries(Bs))if(r.isEventMatching(e)){e.preventDefault(),e.stopPropagation(),n[t]();break}}),[n]);return ba("keydown",r,{capture:!0}),null}const Gs="undefined"!==typeof document?Ft.useLayoutEffect:()=>{};function Ks(e){const t=ve(null);return Gs((()=>{t.current=e}),[e]),ye(((...e)=>{const n=t.current;return null===n||void 0===n?void 0:n(...e)}),[])}const Ys=e=>{var t;return null!==(t=null===e||void 0===e?void 0:e.ownerDocument)&&void 0!==t?t:document},Xs=e=>{if(e&&"window"in e&&e.window===e)return e;const t=Ys(e);return t.defaultView||window};function Js(e){return null!==e&&"object"===typeof e&&"nodeType"in e&&"number"===typeof e.nodeType}function Qs(e){return Js(e)&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}let el=!1;function tl(){return el}function nl(e,t){if(!tl())return!(!t||!e)&&e.contains(t);if(!e||!t)return!1;let n=t;while(null!==n){if(n===e)return!0;n="SLOT"===n.tagName&&n.assignedSlot?n.assignedSlot.parentNode:Qs(n)?n.host:n.parentNode}return!1}const rl=(e=document)=>{var t;if(!tl())return e.activeElement;let n=e.activeElement;while(n&&"shadowRoot"in n&&(null===(t=n.shadowRoot)||void 0===t?void 0:t.activeElement))n=n.shadowRoot.activeElement;return n};function ol(e){return tl()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}function il(e){var t;return"undefined"!==typeof window&&null!=window.navigator&&((null===(t=window.navigator["userAgentData"])||void 0===t?void 0:t.brands.some((t=>e.test(t.brand))))||e.test(window.navigator.userAgent))}function al(e){var t;return"undefined"!==typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator["userAgentData"])||void 0===t?void 0:t.platform)||window.navigator.platform)}function sl(e){let t=null;return()=>(null==t&&(t=e()),t)}const ll=sl((function(){return al(/^Mac/i)})),cl=sl((function(){return al(/^iPhone/i)})),ul=sl((function(){return al(/^iPad/i)||ll()&&navigator.maxTouchPoints>1})),dl=sl((function(){return cl()||ul()}));sl((function(){return ll()||dl()})),sl((function(){return il(/AppleWebKit/i)&&!pl()}));const pl=sl((function(){return il(/Chrome/i)})),fl=sl((function(){return il(/Android/i)}));function ml(){let e=ve(new Map),t=ye(((t,n,r,o)=>{let i=(null===o||void 0===o?void 0:o.once)?(...t)=>{e.current.delete(r),r(...t)}:r;e.current.set(r,{type:n,eventTarget:t,fn:i,options:o}),t.addEventListener(n,i,o)}),[]),n=ye(((t,n,r,o)=>{var i;let a=(null===(i=e.current.get(r))||void 0===i?void 0:i.fn)||r;t.removeEventListener(n,a,o),e.current.delete(r)}),[]),r=ye((()=>{e.current.forEach(((e,t)=>{n(e.eventTarget,e.type,t,e.options)}))}),[n]);return he((()=>r),[r]),{addGlobalListener:t,removeGlobalListener:n,removeAllGlobalListeners:r}}function hl(e){return!(0!==e.mozInputSource||!e.isTrusted)||(fl()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function gl(e){let t=e;return t.nativeEvent=e,t.isDefaultPrevented=()=>t.defaultPrevented,t.isPropagationStopped=()=>t.cancelBubble,t.persist=()=>{},t}function vl(e,t){Object.defineProperty(e,"target",{value:t}),Object.defineProperty(e,"currentTarget",{value:t})}function wl(e){let t=ve({isFocused:!1,observer:null});Gs((()=>{const e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}}),[]);let n=Ks((t=>{null===e||void 0===e||e(t)}));return ye((e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let r=e.target,o=e=>{if(t.current.isFocused=!1,r.disabled){let t=gl(e);n(t)}t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};r.addEventListener("focusout",o,{once:!0}),t.current.observer=new MutationObserver((()=>{if(t.current.isFocused&&r.disabled){var e;null===(e=t.current.observer)||void 0===e||e.disconnect();let n=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:n})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:n}))}})),t.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}}),[n])}sl((function(){return il(/Firefox/i)}));let bl=!1,yl=null,_l=new Set,xl=new Map,kl=!1,El=!1;const Cl={Tab:!0,Escape:!0};function Sl(e,t){for(let n of _l)n(e,t)}function Tl(e){return!(e.metaKey||!ll()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key)}function Nl(e){kl=!0,Tl(e)&&(yl="keyboard",Sl("keyboard",e))}function Pl(e){yl="pointer","mousedown"!==e.type&&"pointerdown"!==e.type||(kl=!0,Sl("pointer",e))}function Al(e){hl(e)&&(kl=!0,yl="virtual")}function Il(e){e.target!==window&&e.target!==document&&!bl&&e.isTrusted&&(kl||El||(yl="virtual",Sl("virtual",e)),kl=!1,El=!1)}function Ol(){kl=!1,El=!0}function Rl(e){if("undefined"===typeof window||xl.get(Xs(e)))return;const t=Xs(e),n=Ys(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){kl=!0,r.apply(this,arguments)},n.addEventListener("keydown",Nl,!0),n.addEventListener("keyup",Nl,!0),n.addEventListener("click",Al,!0),t.addEventListener("focus",Il,!0),t.addEventListener("blur",Ol,!1),"undefined"!==typeof PointerEvent&&(n.addEventListener("pointerdown",Pl,!0),n.addEventListener("pointermove",Pl,!0),n.addEventListener("pointerup",Pl,!0)),t.addEventListener("beforeunload",(()=>{Ml(e)}),{once:!0}),xl.set(t,{focus:r})}const Ml=(e,t)=>{const n=Xs(e),r=Ys(e);t&&r.removeEventListener("DOMContentLoaded",t),xl.has(n)&&(n.HTMLElement.prototype.focus=xl.get(n).focus,r.removeEventListener("keydown",Nl,!0),r.removeEventListener("keyup",Nl,!0),r.removeEventListener("click",Al,!0),n.removeEventListener("focus",Il,!0),n.removeEventListener("blur",Ol,!1),"undefined"!==typeof PointerEvent&&(r.removeEventListener("pointerdown",Pl,!0),r.removeEventListener("pointermove",Pl,!0),r.removeEventListener("pointerup",Pl,!0)),xl.delete(n))};function zl(e){const t=Ys(e);let n;return"loading"!==t.readyState?Rl(e):(n=()=>{Rl(e)},t.addEventListener("DOMContentLoaded",n)),()=>Ml(e,n)}function Ll(){return"pointer"!==yl}"undefined"!==typeof document&&zl();const jl=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Fl(e,t,n){let r=Ys(null===n||void 0===n?void 0:n.target);const o="undefined"!==typeof window?Xs(null===n||void 0===n?void 0:n.target).HTMLInputElement:HTMLInputElement,i="undefined"!==typeof window?Xs(null===n||void 0===n?void 0:n.target).HTMLTextAreaElement:HTMLTextAreaElement,a="undefined"!==typeof window?Xs(null===n||void 0===n?void 0:n.target).HTMLElement:HTMLElement,s="undefined"!==typeof window?Xs(null===n||void 0===n?void 0:n.target).KeyboardEvent:KeyboardEvent;return e=e||r.activeElement instanceof o&&!jl.has(r.activeElement.type)||r.activeElement instanceof i||r.activeElement instanceof a&&r.activeElement.isContentEditable,!(e&&"keyboard"===t&&n instanceof s&&!Cl[n.key])}function Dl(e,t,n){Rl(),he((()=>{let t=(t,r)=>{Fl(!!(null===n||void 0===n?void 0:n.isTextInput),t,r)&&e(Ll())};return _l.add(t),()=>{_l.delete(t)}}),t)}function $l(e){let{isDisabled:t,onFocus:n,onBlur:r,onFocusChange:o}=e;const i=ye((e=>{if(e.target===e.currentTarget)return r&&r(e),o&&o(!1),!0}),[r,o]),a=wl(i),s=ye((e=>{const t=Ys(e.target),r=t?rl(t):rl();e.target===e.currentTarget&&r===ol(e.nativeEvent)&&(n&&n(e),o&&o(!0),a(e))}),[o,n,a]);return{focusProps:{onFocus:!t&&(n||o||r)?s:void 0,onBlur:t||!r&&!o?void 0:i}}}function Zl(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:r,onFocusWithinChange:o}=e,i=ve({isFocusWithin:!1}),{addGlobalListener:a,removeAllGlobalListeners:s}=ml(),l=ye((e=>{e.currentTarget.contains(e.target)&&i.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(i.current.isFocusWithin=!1,s(),n&&n(e),o&&o(!1))}),[n,o,i,s]),c=wl(l),u=ye((e=>{if(!e.currentTarget.contains(e.target))return;const t=Ys(e.target),n=rl(t);if(!i.current.isFocusWithin&&n===ol(e.nativeEvent)){r&&r(e),o&&o(!0),i.current.isFocusWithin=!0,c(e);let n=e.currentTarget;a(t,"focus",(e=>{if(i.current.isFocusWithin&&!nl(n,e.target)){let r=new t.defaultView.FocusEvent("blur",{relatedTarget:e.target});vl(r,n);let o=gl(r);l(o)}}),{capture:!0})}}),[r,o,c,a,l]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:u,onBlur:l}}}let Hl=!1,Ul=0;function Bl(){Hl=!0,setTimeout((()=>{Hl=!1}),50)}function Vl(e){"touch"===e.pointerType&&Bl()}function ql(){if("undefined"!==typeof document)return"undefined"!==typeof PointerEvent&&document.addEventListener("pointerup",Vl),Ul++,()=>{Ul--,Ul>0||"undefined"!==typeof PointerEvent&&document.removeEventListener("pointerup",Vl)}}function Wl(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:r,isDisabled:o}=e,[i,a]=fe(!1),s=ve({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;he(ql,[]);let{addGlobalListener:l,removeAllGlobalListeners:c}=ml(),{hoverProps:u,triggerHoverEnd:d}=be((()=>{let e=(e,r)=>{if(s.pointerType=r,o||"touch"===r||s.isHovered||!e.currentTarget.contains(e.target))return;s.isHovered=!0;let c=e.currentTarget;s.target=c,l(Ys(e.target),"pointerover",(e=>{s.isHovered&&s.target&&!nl(s.target,e.target)&&i(e,e.pointerType)}),{capture:!0}),t&&t({type:"hoverstart",target:c,pointerType:r}),n&&n(!0),a(!0)},i=(e,t)=>{let o=s.target;s.pointerType="",s.target=null,"touch"!==t&&s.isHovered&&o&&(s.isHovered=!1,c(),r&&r({type:"hoverend",target:o,pointerType:t}),n&&n(!1),a(!1))},u={};return"undefined"!==typeof PointerEvent&&(u.onPointerEnter=t=>{Hl&&"mouse"===t.pointerType||e(t,t.pointerType)},u.onPointerLeave=e=>{!o&&e.currentTarget.contains(e.target)&&i(e,e.pointerType)}),{hoverProps:u,triggerHoverEnd:i}}),[t,n,r,o,s,l,c]);return he((()=>{o&&d({currentTarget:s.target},s.pointerType)}),[o]),{hoverProps:u,isHovered:i}}function Gl(e={}){let{autoFocus:t=!1,isTextInput:n,within:r}=e,o=ve({isFocused:!1,isFocusVisible:t||Ll()}),[i,a]=fe(!1),[s,l]=fe((()=>o.current.isFocused&&o.current.isFocusVisible)),c=ye((()=>l(o.current.isFocused&&o.current.isFocusVisible)),[]),u=ye((e=>{o.current.isFocused=e,a(e),c()}),[c]);Dl((e=>{o.current.isFocusVisible=e,c()}),[],{isTextInput:n});let{focusProps:d}=$l({isDisabled:r,onFocusChange:u}),{focusWithinProps:p}=Zl({isDisabled:!r,onFocusWithinChange:u});return{isFocused:i,isFocusVisible:s,focusProps:r?p:d}}var Kl=Object.defineProperty,Yl=(e,t,n)=>t in e?Kl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Xl=(e,t,n)=>(Yl(e,"symbol"!=typeof t?t+"":t,n),n);let Jl=class{constructor(){Xl(this,"current",this.detect()),Xl(this,"handoffState","pending"),Xl(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},Ql=new Jl;function ec(e){var t,n;return Ql.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}function tc(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}function nc(){let e=[],t={addEventListener(e,n,r,o){return e.addEventListener(n,r,o),t.add((()=>e.removeEventListener(n,r,o)))},requestAnimationFrame(...e){let n=requestAnimationFrame(...e);return t.add((()=>cancelAnimationFrame(n)))},nextFrame(...e){return t.requestAnimationFrame((()=>t.requestAnimationFrame(...e)))},setTimeout(...e){let n=setTimeout(...e);return t.add((()=>clearTimeout(n)))},microTask(...e){let n={current:!0};return tc((()=>{n.current&&e[0]()})),t.add((()=>{n.current=!1}))},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:r})}))},group(e){let t=nc();return e(t),this.add((()=>t.dispose()))},add(t){return e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}},dispose(){for(let t of e.splice(0))t()}};return t}function rc(){let[e]=fe(nc);return he((()=>()=>e.dispose()),[e]),e}let oc=(e,t)=>{Ql.isServer?he(e,t):ge(e,t)};function ic(e){let t=ve(e);return oc((()=>{t.current=e}),[e]),t}let ac=function(e){let t=ic(e);return Ft.useCallback(((...e)=>t.current(...e)),[t])};function sc(e){let t=e.width/2,n=e.height/2;return{top:e.clientY-n,right:e.clientX+t,bottom:e.clientY+n,left:e.clientX-t}}function lc(e,t){return!(!e||!t||e.right<t.left||e.left>t.right||e.bottom<t.top||e.top>t.bottom)}function cc({disabled:e=!1}={}){let t=ve(null),[n,r]=fe(!1),o=rc(),i=ac((()=>{t.current=null,r(!1),o.dispose()})),a=ac((e=>{if(o.dispose(),null===t.current){t.current=e.currentTarget,r(!0);{let n=ec(e.currentTarget);o.addEventListener(n,"pointerup",i,!1),o.addEventListener(n,"pointermove",(e=>{if(t.current){let n=sc(e);r(lc(n,t.current.getBoundingClientRect()))}}),!1),o.addEventListener(n,"pointercancel",i,!1)}}}));return{pressed:n,pressProps:e?{}:{onPointerDown:a,onPointerUp:i,onClick:i}}}let uc=K(void 0);function dc(){return _e(uc)}function pc(...e){return Array.from(new Set(e.flatMap((e=>"string"==typeof e?e.split(" "):[])))).filter(Boolean).join(" ")}function fc(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,fc),r}var mc=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(mc||{}),hc=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(hc||{});function gc(){let e=bc();return ye((t=>vc({mergeRefs:e,...t})),[e])}function vc({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:i=!0,name:a,mergeRefs:s}){s=null!=s?s:yc;let l=_c(t,e);if(i)return wc(l,n,r,a,s);let c=null!=o?o:0;if(2&c){let{static:e=!1,...t}=l;if(e)return wc(t,n,r,a,s)}if(1&c){let{unmount:e=!0,...t}=l;return fc(e?0:1,{[0](){return null},[1](){return wc({...t,hidden:!0,style:{display:"none"}},n,r,a,s)}})}return wc(l,n,r,a,s)}function wc(e,t={},n,r,o){let{as:i=n,children:a,refName:s="ref",...l}=Cc(e,["unmount","static"]),c=void 0!==e.ref?{[s]:e.ref}:{},u="function"==typeof a?a(t):a;"className"in l&&l.className&&"function"==typeof l.className&&(l.className=l.className(t)),l["aria-labelledby"]&&l["aria-labelledby"]===l.id&&(l["aria-labelledby"]=void 0);let d={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)));if(e){d["data-headlessui-state"]=n.join(" ");for(let e of n)d[`data-${e}`]=""}}if(i===C&&(Object.keys(Ec(l)).length>0||Object.keys(Ec(d)).length>0)){if(Nt(u)&&!(Array.isArray(u)&&u.length>1)){let e=u.props,t=null==e?void 0:e.className,n="function"==typeof t?(...e)=>pc(t(...e),l.className):pc(t,l.className),r=n?{className:n}:{},i=_c(u.props,Ec(Cc(l,["ref"])));for(let o in d)o in i&&delete d[o];return It(u,Object.assign({},i,d,c,{ref:o(Sc(u),c.ref)},r))}if(Object.keys(Ec(l)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(Ec(l)).concat(Object.keys(Ec(d))).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"))}return x(i,Object.assign({},Cc(l,["ref"]),i!==C&&c,i!==C&&d),u)}function bc(){let e=ve([]),t=ye((t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)}),[]);return(...n)=>{if(!n.every((e=>null==e)))return e.current=n,t}}function yc(...e){return e.every((e=>null==e))?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function _c(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let r in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(n[r]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let r in n)Object.assign(t,{[r](e,...t){let o=n[r];for(let n of o){if((e instanceof Event||(null==e?void 0:e.nativeEvent)instanceof Event)&&e.defaultPrevented)return;n(e,...t)}}});return t}function xc(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];for(let r in n)Object.assign(t,{[r](...e){let t=n[r];for(let n of t)null==n||n(...e)}});return t}function kc(e){var t;return Object.assign(We(e),{displayName:null!=(t=e.displayName)?t:e.name})}function Ec(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function Cc(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function Sc(e){return Ft.version.split(".")[0]>="19"?e.props.ref:e.ref}let Tc="button";function Nc(e,t){var n;let r=dc(),{disabled:o=r||!1,autoFocus:i=!1,...a}=e,{isFocusVisible:s,focusProps:l}=Gl({autoFocus:i}),{isHovered:c,hoverProps:u}=Wl({isDisabled:o}),{pressed:d,pressProps:p}=cc({disabled:o}),f=xc({ref:t,type:null!=(n=a.type)?n:"button",disabled:o||void 0,autoFocus:i},l,u,p),m=be((()=>({disabled:o,hover:c,focus:s,active:d,autofocus:i})),[o,c,s,d,i]);return gc()({ourProps:f,theirProps:a,slot:m,defaultTag:Tc,name:"Button"})}let Pc=kc(Nc),Ac=K(void 0);function Ic(){return _e(Ac)}function Oc(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!Rc(n))&&r}function Rc(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}let Mc=Symbol();function zc(e,t=!0){return Object.assign(e,{[Mc]:t})}function Lc(...e){let t=ve(e);he((()=>{t.current=e}),[e]);let n=ac((e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)}));return e.every((e=>null==e||(null==e?void 0:e[Mc])))?void 0:n}let jc=K(null);function Fc(){let e=_e(jc);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Fc),e}return e}function Dc(){var e,t;return null!=(t=null==(e=_e(jc))?void 0:e.value)?t:void 0}function $c(){let[e,t]=fe([]);return[e.length>0?e.join(" "):void 0,be((()=>function(e){let n=ac((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),r=be((()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value})),[n,e.slot,e.name,e.props,e.value]);return Ft.createElement(jc.Provider,{value:r},e.children)}),[t])]}jc.displayName="DescriptionContext";let Zc="p";function Hc(e,t){let n=Ee(),r=dc(),{id:o=`headlessui-description-${n}`,...i}=e,a=Fc(),s=Lc(t);oc((()=>a.register(o)),[o,a.register]);let l=r||!1,c=be((()=>({...a.slot,disabled:l})),[a.slot,l]),u={ref:s,...a.props,id:o};return gc()({ourProps:u,theirProps:i,slot:c,defaultTag:Zc,name:a.name||"Description"})}let Uc=kc(Hc);Object.assign(Uc,{});var Bc=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(Bc||{});let Vc=K(null);function qc(){let e=_e(Vc);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,qc),e}return e}function Wc(e){var t,n,r;let o=null!=(n=null==(t=_e(Vc))?void 0:t.value)?n:void 0;return(null!=(r=void 0)?r:0)>0?[o,...e].filter(Boolean).join(" "):o}function Gc({inherit:e=!1}={}){let t=Wc(),[n,r]=fe([]),o=e?[t,...n].filter(Boolean):n;return[o.length>0?o.join(" "):void 0,be((()=>function(e){let t=ac((e=>(r((t=>[...t,e])),()=>r((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),n=be((()=>({register:t,slot:e.slot,name:e.name,props:e.props,value:e.value})),[t,e.slot,e.name,e.props,e.value]);return Ft.createElement(Vc.Provider,{value:n},e.children)}),[r])]}Vc.displayName="LabelContext";let Kc="label";function Yc(e,t){var n;let r=Ee(),o=qc(),i=Ic(),a=dc(),{id:s=`headlessui-label-${r}`,htmlFor:l=(null!=i?i:null==(n=o.props)?void 0:n.htmlFor),passive:c=!1,...u}=e,d=Lc(t);oc((()=>o.register(s)),[s,o.register]);let p=ac((e=>{let t=e.currentTarget;if(t instanceof HTMLLabelElement&&e.preventDefault(),o.props&&"onClick"in o.props&&"function"==typeof o.props.onClick&&o.props.onClick(e),t instanceof HTMLLabelElement){let e=document.getElementById(t.htmlFor);if(e){let t=e.getAttribute("disabled");if("true"===t||""===t)return;let n=e.getAttribute("aria-disabled");if("true"===n||""===n)return;(e instanceof HTMLInputElement&&("radio"===e.type||"checkbox"===e.type)||"radio"===e.role||"checkbox"===e.role||"switch"===e.role)&&e.click(),e.focus({preventScroll:!0})}}})),f=a||!1,m=be((()=>({...o.slot,disabled:f})),[o.slot,f]),h={ref:d,...o.props,id:s,htmlFor:l,onClick:p};return c&&("onClick"in h&&(delete h.htmlFor,delete h.onClick),"onClick"in u&&delete u.onClick),gc()({ourProps:h,theirProps:u,slot:m,defaultTag:l?Kc:"div",name:o.name||"Label"})}let Xc=kc(Yc);function Jc(e){if(null===e)return{width:0,height:0};let{width:t,height:n}=e.getBoundingClientRect();return{width:t,height:n}}function Qc(e,t=!1){let[n,r]=me((()=>({})),{}),o=be((()=>Jc(e)),[e,n]);return oc((()=>{if(!e)return;let t=new ResizeObserver(r);return t.observe(e),()=>{t.disconnect()}}),[e]),t?{width:`${o.width}px`,height:`${o.height}px`}:o}Object.assign(Xc,{});let eu=class extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}};function tu(e,t){let n=e(),r=new Set;return{getSnapshot(){return n},subscribe(e){return r.add(e),()=>r.delete(e)},dispatch(e,...o){let i=t[e].call(n,...o);i&&(n=i,r.forEach((e=>e())))}}}function nu(e){return je(e.subscribe,e.getSnapshot,e.getSnapshot)}let ru=new eu((()=>tu((()=>[]),{ADD(e){return this.includes(e)?this:[...this,e]},REMOVE(e){let t=this.indexOf(e);if(-1===t)return this;let n=this.slice();return n.splice(t,1),n}})));function ou(e,t){let n=ru.get(t),r=Ee(),o=nu(n);if(oc((()=>{if(e)return n.dispatch("ADD",r),()=>n.dispatch("REMOVE",r)}),[n,e]),!e)return!1;let i=o.indexOf(r),a=o.length;return-1===i&&(i=a,a+=1),i===a-1}let iu=new Map,au=new Map;function su(e){var t;let n=null!=(t=au.get(e))?t:0;return au.set(e,n+1),0!==n||(iu.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>lu(e)}function lu(e){var t;let n=null!=(t=au.get(e))?t:1;if(1===n?au.delete(e):au.set(e,n-1),1!==n)return;let r=iu.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,iu.delete(e))}function cu(e,{allowed:t,disallowed:n}={}){let r=ou(e,"inert-others");oc((()=>{var e,o;if(!r)return;let i=nc();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&i.add(su(t));let a=null!=(o=null==t?void 0:t())?o:[];for(let t of a){if(!t)continue;let e=ec(t);if(!e)continue;let n=t.parentElement;for(;n&&n!==e.body;){for(let e of n.children)a.some((t=>e.contains(t)))||i.add(su(e));n=n.parentElement}}return i.dispose}),[r,t,n])}function uu(e,t,n){let r=ic((e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&n()}));he((()=>{if(!e)return;let n=null===t?null:t instanceof HTMLElement?t:t.current;if(!n)return;let o=nc();if("undefined"!=typeof ResizeObserver){let e=new ResizeObserver((()=>r.current(n)));e.observe(n),o.add((()=>e.disconnect()))}if("undefined"!=typeof IntersectionObserver){let e=new IntersectionObserver((()=>r.current(n)));e.observe(n),o.add((()=>e.disconnect()))}return()=>o.dispose()}),[t,r,e])}let du=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(","),pu=["[data-autofocus]"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var fu=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(fu||{}),mu=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(mu||{}),hu=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(hu||{});function gu(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(du)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}function vu(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(pu)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}var wu=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(wu||{});function bu(e,t=0){var n;return e!==(null==(n=ec(e))?void 0:n.body)&&fc(t,{[0](){return e.matches(du)},[1](){let t=e;for(;null!==t;){if(t.matches(du))return!0;t=t.parentElement}return!1}})}function yu(e){let t=ec(e);nc().nextFrame((()=>{t&&!bu(t.activeElement,0)&&xu(e)}))}var _u=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(_u||{});function xu(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let ku=["textarea","input"].join(",");function Eu(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,ku))&&n}function Cu(e,t=e=>e){return e.slice().sort(((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let i=r.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}function Su(e,t){return Tu(gu(),t,{relativeTo:e})}function Tu(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,a=Array.isArray(e)?n?Cu(e):e:64&t?vu(e):gu(e);o.length>0&&a.length>1&&(a=a.filter((e=>!o.some((t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))))),r=null!=r?r:i.activeElement;let s,l=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,a.indexOf(r))-1;if(4&t)return Math.max(0,a.indexOf(r))+1;if(8&t)return a.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=32&t?{preventScroll:!0}:{},d=0,p=a.length;do{if(d>=p||d+p<=0)return 0;let e=c+d;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}s=a[e],null==s||s.focus(u),d+=l}while(s!==i.activeElement);return 6&t&&Eu(s)&&s.select(),2}function Nu(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Pu(){return/Android/gi.test(window.navigator.userAgent)}function Au(){return Nu()||Pu()}function Iu(e,t,n,r){let o=ic(n);he((()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){o.current(e)}}),[e,t,r])}function Ou(e,t,n,r){let o=ic(n);he((()=>{if(e)return window.addEventListener(t,n,r),()=>window.removeEventListener(t,n,r);function n(e){o.current(e)}}),[e,t,r])}const Ru=30;function Mu(e,t,n){let r=ou(e,"outside-click"),o=ic(n),i=ye((function(e,n){if(e.defaultPrevented)return;let r=n(e);if(null===r||!r.getRootNode().contains(r)||!r.isConnected)return;let i=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(t);for(let t of i)if(null!==t&&(t.contains(r)||e.composed&&e.composedPath().includes(t)))return;return!bu(r,wu.Loose)&&-1!==r.tabIndex&&e.preventDefault(),o.current(e,r)}),[o,t]),a=ve(null);Iu(r,"pointerdown",(e=>{var t,n;a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target}),!0),Iu(r,"mousedown",(e=>{var t,n;a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target}),!0),Iu(r,"click",(e=>{Au()||a.current&&(i(e,(()=>a.current)),a.current=null)}),!0);let s=ve({x:0,y:0});Iu(r,"touchstart",(e=>{s.current.x=e.touches[0].clientX,s.current.y=e.touches[0].clientY}),!0),Iu(r,"touchend",(e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-s.current.x)>=Ru||Math.abs(t.y-s.current.y)>=Ru))return i(e,(()=>e.target instanceof HTMLElement?e.target:null))}),!0),Ou(r,"blur",(e=>i(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}function zu(...e){return be((()=>ec(...e)),[...e])}function Lu(e,t){return be((()=>{var n;if(e.type)return e.type;let r=null!=(n=e.as)?n:"button";return"string"==typeof r&&"button"===r.toLowerCase()||"BUTTON"===(null==t?void 0:t.tagName)&&!t.hasAttribute("type")?"button":void 0}),[e.type,e.as,t])}function ju(){let e;return{before({doc:t}){var n;let r=t.documentElement,o=null!=(n=t.defaultView)?n:window;e=Math.max(0,o.innerWidth-r.clientWidth)},after({doc:t,d:n}){let r=t.documentElement,o=Math.max(0,r.clientWidth-r.offsetWidth),i=Math.max(0,e-o);n.style(r,"paddingRight",`${i}px`)}}}function Fu(){return Nu()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap((e=>e())).some((t=>t.contains(e)))}t.microTask((()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=nc();n.style(e.documentElement,"scrollBehavior","auto"),t.add((()=>t.microTask((()=>n.dispose()))))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,i=null;t.addEventListener(e,"click",(t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),a=e.querySelector(o);a&&!r(a)&&(i=a)}catch{}}),!0),t.addEventListener(e,"touchstart",(e=>{if(e.target instanceof HTMLElement)if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")})),t.addEventListener(e,"touchmove",(e=>{if(e.target instanceof HTMLElement){if("INPUT"===e.target.tagName)return;if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}}),{passive:!1}),t.add((()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)}))}))}}:{}}function Du(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function $u(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Zu=tu((()=>new Map),{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:nc(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:$u(n)},o=[Fu(),ju(),Du()];o.forEach((({before:e})=>null==e?void 0:e(r))),o.forEach((({after:e})=>null==e?void 0:e(r)))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});function Hu(e,t,n=()=>({containers:[]})){let r=nu(Zu),o=t?r.get(t):void 0,i=!!o&&o.count>0;return oc((()=>{if(t&&e)return Zu.dispatch("PUSH",t,n),()=>Zu.dispatch("POP",t,n)}),[e,t]),i}function Uu(e,t,n=()=>[document.body]){let r=ou(e,"scroll-lock");Hu(r,t,(e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}}))}function Bu(e){return[e.screenX,e.screenY]}function Vu(){let e=ve([-1,-1]);return{wasMoved(t){let n=Bu(t);return(e.current[0]!==n[0]||e.current[1]!==n[1])&&(e.current=n,!0)},update(t){e.current=Bu(t)}}}function qu(e=0){let[t,n]=fe(e),r=ye((e=>n(e)),[t]),o=ye((e=>n((t=>t|e))),[t]),i=ye((e=>(t&e)===e),[t]),a=ye((e=>n((t=>t&~e))),[n]),s=ye((e=>n((t=>t^e))),[n]);return{flags:t,setFlag:r,addFlag:o,hasFlag:i,removeFlag:a,toggleFlag:s}}var Wu,Gu;Zu.subscribe((()=>{let e=Zu.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&Zu.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&Zu.dispatch("TEARDOWN",n)}})),"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&"test"===(null==(Wu=null==process?void 0:{NODE_ENV:"production",BASE_URL:"/"})?void 0:Wu["NODE_ENV"])&&"undefined"==typeof(null==(Gu=null==Element?void 0:Element.prototype)?void 0:Gu.getAnimations)&&(Element.prototype.getAnimations=function(){return[]});var Ku=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(Ku||{});function Yu(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}function Xu(e,t,n,r){let[o,i]=fe(n),{hasFlag:a,addFlag:s,removeFlag:l}=qu(e&&o?3:0),c=ve(!1),u=ve(!1),d=rc();return oc((()=>{var o;if(e)return n&&i(!0),t?(null==(o=void 0)||o.call(r,n),Ju(t,{inFlight:c,prepare(){u.current?u.current=!1:u.current=c.current,c.current=!0,!u.current&&(n?(s(3),l(4)):(s(4),l(2)))},run(){u.current?n?(l(3),s(4)):(l(4),s(3)):n?l(1):s(1)},done(){var e;u.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(c.current=!1,l(7),n||i(!1),null==(e=void 0)||e.call(r,n))}})):void(n&&s(3))}),[e,n,t,d]),e?[o,{closed:a(1),enter:a(2),leave:a(4),transition:a(2)||a(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function Ju(e,{prepare:t,run:n,done:r,inFlight:o}){let i=nc();return ed(e,{prepare:t,inFlight:o}),i.nextFrame((()=>{n(),i.requestAnimationFrame((()=>{i.add(Qu(e,r))}))})),i.dispose}function Qu(e,t){var n,r;let o=nc();if(!e)return o.dispose;let i=!1;o.add((()=>{i=!0}));let a=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter((e=>e instanceof CSSTransition)))?r:[];return 0===a.length?(t(),o.dispose):(Promise.allSettled(a.map((e=>e.finished))).then((()=>{i||t()})),o.dispose)}function ed(e,{inFlight:t,prepare:n}){if(null!=t&&t.current)return void n();let r=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=r}function td(e,{container:t,accept:n,walk:r}){let o=ve(n),i=ve(r);he((()=>{o.current=n,i.current=r}),[n,r]),oc((()=>{if(!t||!e)return;let n=ec(t);if(!n)return;let r=o.current,a=i.current,s=Object.assign((e=>r(e)),{acceptNode:r}),l=n.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,s,!1);for(;l.nextNode();)a(l.currentNode)}),[t,e,o,i])}function nd(){return"undefined"!==typeof window}function rd(e){return ad(e)?(e.nodeName||"").toLowerCase():"#document"}function od(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function id(e){var t;return null==(t=(ad(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ad(e){return!!nd()&&(e instanceof Node||e instanceof od(e).Node)}function sd(e){return!!nd()&&(e instanceof Element||e instanceof od(e).Element)}function ld(e){return!!nd()&&(e instanceof HTMLElement||e instanceof od(e).HTMLElement)}function cd(e){return!(!nd()||"undefined"===typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof od(e).ShadowRoot)}function ud(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=vd(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function dd(e){return["table","td","th"].includes(rd(e))}function pd(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function fd(e){const t=hd(),n=sd(e)?vd(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function md(e){let t=bd(e);while(ld(t)&&!gd(t)){if(fd(t))return t;if(pd(t))return null;t=bd(t)}return null}function hd(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function gd(e){return["html","body","#document"].includes(rd(e))}function vd(e){return od(e).getComputedStyle(e)}function wd(e){return sd(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function bd(e){if("html"===rd(e))return e;const t=e.assignedSlot||e.parentNode||cd(e)&&e.host||id(e);return cd(t)?t.host:t}function yd(e){const t=bd(e);return gd(t)?e.ownerDocument?e.ownerDocument.body:e.body:ld(t)&&ud(t)?t:yd(t)}function _d(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=yd(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=od(o);if(i){const e=xd(a);return t.concat(a,a.visualViewport||[],ud(o)?o:[],e&&n?_d(e):[])}return t.concat(o,_d(o,[],n))}function xd(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function kd(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}const Ed=Math.min,Cd=Math.max,Sd=Math.round,Td=Math.floor,Nd=e=>({x:e,y:e}),Pd={left:"right",right:"left",bottom:"top",top:"bottom"},Ad={start:"end",end:"start"};function Id(e,t,n){return Cd(e,Ed(t,n))}function Od(e,t){return"function"===typeof e?e(t):e}function Rd(e){return e.split("-")[0]}function Md(e){return e.split("-")[1]}function zd(e){return"x"===e?"y":"x"}function Ld(e){return"y"===e?"height":"width"}function jd(e){return["top","bottom"].includes(Rd(e))?"y":"x"}function Fd(e){return zd(jd(e))}function Dd(e,t,n){void 0===n&&(n=!1);const r=Md(e),o=Fd(e),i=Ld(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Bd(a)),[a,Bd(a)]}function $d(e){const t=Bd(e);return[Zd(e),t,Zd(t)]}function Zd(e){return e.replace(/start|end/g,(e=>Ad[e]))}function Hd(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:a;default:return[]}}function Ud(e,t,n,r){const o=Md(e);let i=Hd(Rd(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(Zd)))),i}function Bd(e){return e.replace(/left|right|bottom|top/g,(e=>Pd[e]))}function Vd(e){return{top:0,right:0,bottom:0,left:0,...e}}function qd(e){return"number"!==typeof e?Vd(e):{top:e,right:e,bottom:e,left:e}}function Wd(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Gd(e,t,n){let{reference:r,floating:o}=e;const i=jd(t),a=Fd(t),s=Ld(a),l=Rd(t),c="y"===i,u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,p=r[s]/2-o[s]/2;let f;switch(l){case"top":f={x:u,y:r.y-o.height};break;case"bottom":f={x:u,y:r.y+r.height};break;case"right":f={x:r.x+r.width,y:d};break;case"left":f={x:r.x-o.width,y:d};break;default:f={x:r.x,y:r.y}}switch(Md(t)){case"start":f[a]-=p*(n&&c?-1:1);break;case"end":f[a]+=p*(n&&c?-1:1);break}return f}const Kd=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,s=i.filter(Boolean),l=await(null==a.isRTL?void 0:a.isRTL(t));let c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=Gd(c,r,l),p=r,f={},m=0;for(let h=0;h<s.length;h++){const{name:n,fn:i}=s[h],{x:g,y:v,data:w,reset:b}=await i({x:u,y:d,initialPlacement:r,placement:p,strategy:o,middlewareData:f,rects:c,platform:a,elements:{reference:e,floating:t}});u=null!=g?g:u,d=null!=v?v:d,f={...f,[n]:{...f[n],...w}},b&&m<=50&&(m++,"object"===typeof b&&(b.placement&&(p=b.placement),b.rects&&(c=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),({x:u,y:d}=Gd(c,p,l))),h=-1)}return{x:u,y:d,placement:p,strategy:o,middlewareData:f}};async function Yd(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:a,elements:s,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:p=!1,padding:f=0}=Od(t,e),m=qd(f),h="floating"===d?"reference":"floating",g=s[p?h:d],v=Wd(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:c,rootBoundary:u,strategy:l})),w="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await(null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),y=await(null==i.isElement?void 0:i.isElement(b))&&await(null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},_=Wd(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:w,offsetParent:b,strategy:l}):w);return{top:(v.top-_.top+m.top)/y.y,bottom:(_.bottom-v.bottom+m.bottom)/y.y,left:(v.left-_.left+m.left)/y.x,right:(_.right-v.right+m.right)/y.x}}const Xd=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:a,initialPlacement:s,platform:l,elements:c}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:h=!0,...g}=Od(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const v=Rd(o),w=jd(s),b=Rd(s)===s,y=await(null==l.isRTL?void 0:l.isRTL(c.floating)),_=p||(b||!h?[Bd(s)]:$d(s)),x="none"!==m;!p&&x&&_.push(...Ud(s,h,m,y));const k=[s,..._],E=await Yd(t,g),C=[];let S=(null==(r=i.flip)?void 0:r.overflows)||[];if(u&&C.push(E[v]),d){const e=Dd(o,a,y);C.push(E[e[0]],E[e[1]])}if(S=[...S,{placement:o,overflows:C}],!C.every((e=>e<=0))){var T,N;const e=((null==(T=i.flip)?void 0:T.index)||0)+1,t=k[e];if(t){var P;const n="alignment"===d&&w!==jd(t),r=(null==(P=S[0])?void 0:P.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:S},reset:{placement:t}}}let n=null==(N=S.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:N.placement;if(!n)switch(f){case"bestFit":{var A;const e=null==(A=S.filter((e=>{if(x){const t=jd(e.placement);return t===w||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:A[0];e&&(n=e);break}case"initialPlacement":n=s;break}if(o!==n)return{reset:{placement:n}}}return{}}}};async function Jd(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),a=Rd(n),s=Md(n),l="y"===jd(n),c=["left","top"].includes(a)?-1:1,u=i&&l?-1:1,d=Od(t,e);let{mainAxis:p,crossAxis:f,alignmentAxis:m}="number"===typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"===typeof m&&(f="end"===s?-1*m:m),l?{x:f*u,y:p*c}:{x:p*c,y:f*u}}const Qd=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:a,middlewareData:s}=t,l=await Jd(t,e);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:a}}}}},ep=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=Od(e,t),c={x:n,y:r},u=await Yd(t,l),d=jd(Rd(o)),p=zd(d);let f=c[p],m=c[d];if(i){const e="y"===p?"top":"left",t="y"===p?"bottom":"right",n=f+u[e],r=f-u[t];f=Id(n,f,r)}if(a){const e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=m+u[e],r=m-u[t];m=Id(n,m,r)}const h=s.fn({...t,[p]:f,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[p]:i,[d]:a}}}}}},tp=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:a,elements:s}=t,{apply:l=()=>{},...c}=Od(e,t),u=await Yd(t,c),d=Rd(o),p=Md(o),f="y"===jd(o),{width:m,height:h}=i.floating;let g,v;"top"===d||"bottom"===d?(g=d,v=p===(await(null==a.isRTL?void 0:a.isRTL(s.floating))?"start":"end")?"left":"right"):(v=d,g="end"===p?"top":"bottom");const w=h-u.top-u.bottom,b=m-u.left-u.right,y=Ed(h-u[g],w),_=Ed(m-u[v],b),x=!t.middlewareData.shift;let k=y,E=_;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(E=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=w),x&&!p){const e=Cd(u.left,0),t=Cd(u.right,0),n=Cd(u.top,0),r=Cd(u.bottom,0);f?E=m-2*(0!==e||0!==t?e+t:Cd(u.left,u.right)):k=h-2*(0!==n||0!==r?n+r:Cd(u.top,u.bottom))}await l({...t,availableWidth:E,availableHeight:k});const C=await a.getDimensions(s.floating);return m!==C.width||h!==C.height?{reset:{rects:!0}}:{}}}};function np(e){const t=vd(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=ld(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=Sd(n)!==i||Sd(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function rp(e){return sd(e)?e:e.contextElement}function op(e){const t=rp(e);if(!ld(t))return Nd(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=np(t);let a=(i?Sd(n.width):n.width)/r,s=(i?Sd(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}const ip=Nd(0);function ap(e){const t=od(e);return hd()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ip}function sp(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==od(e))&&t}function lp(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=rp(e);let a=Nd(1);t&&(r?sd(r)&&(a=op(r)):a=op(e));const s=sp(i,n,r)?ap(i):Nd(0);let l=(o.left+s.x)/a.x,c=(o.top+s.y)/a.y,u=o.width/a.x,d=o.height/a.y;if(i){const e=od(i),t=r&&sd(r)?od(r):r;let n=e,o=xd(n);while(o&&r&&t!==n){const e=op(o),t=o.getBoundingClientRect(),r=vd(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;l*=e.x,c*=e.y,u*=e.x,d*=e.y,l+=i,c+=a,n=od(o),o=xd(n)}}return Wd({width:u,height:d,x:l,y:c})}function cp(e,t){const n=wd(e).scrollLeft;return t?t.left+n:lp(id(e)).left+n}function up(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:cp(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function dp(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,a=id(r),s=!!t&&pd(t.floating);if(r===a||s&&i)return n;let l={scrollLeft:0,scrollTop:0},c=Nd(1);const u=Nd(0),d=ld(r);if((d||!d&&!i)&&(("body"!==rd(r)||ud(a))&&(l=wd(r)),ld(r))){const e=lp(r);c=op(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}const p=!a||d||i?Nd(0):up(a,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+u.x+p.x,y:n.y*c.y-l.scrollTop*c.y+u.y+p.y}}function pp(e){return Array.from(e.getClientRects())}function fp(e){const t=id(e),n=wd(e),r=e.ownerDocument.body,o=Cd(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Cd(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+cp(e);const s=-n.scrollTop;return"rtl"===vd(r).direction&&(a+=Cd(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}function mp(e,t){const n=od(e),r=id(e),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;const e=hd();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s,y:l}}function hp(e,t){const n=lp(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ld(e)?op(e):Nd(1),a=e.clientWidth*i.x,s=e.clientHeight*i.y,l=o*i.x,c=r*i.y;return{width:a,height:s,x:l,y:c}}function gp(e,t,n){let r;if("viewport"===t)r=mp(e,n);else if("document"===t)r=fp(id(e));else if(sd(t))r=hp(t,n);else{const n=ap(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return Wd(r)}function vp(e,t){const n=bd(e);return!(n===t||!sd(n)||gd(n))&&("fixed"===vd(n).position||vp(n,t))}function wp(e,t){const n=t.get(e);if(n)return n;let r=_d(e,[],!1).filter((e=>sd(e)&&"body"!==rd(e))),o=null;const i="fixed"===vd(e).position;let a=i?bd(e):e;while(sd(a)&&!gd(a)){const t=vd(a),n=fd(a);n||"fixed"!==t.position||(o=null);const s=i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||ud(a)&&!n&&vp(e,a);s?r=r.filter((e=>e!==a)):o=t,a=bd(a)}return t.set(e,r),r}function bp(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i="clippingAncestors"===n?pd(t)?[]:wp(t,this._c):[].concat(n),a=[...i,r],s=a[0],l=a.reduce(((e,n)=>{const r=gp(t,n,o);return e.top=Cd(r.top,e.top),e.right=Ed(r.right,e.right),e.bottom=Ed(r.bottom,e.bottom),e.left=Cd(r.left,e.left),e}),gp(t,s,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function yp(e){const{width:t,height:n}=np(e);return{width:t,height:n}}function _p(e,t,n){const r=ld(t),o=id(t),i="fixed"===n,a=lp(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const l=Nd(0);function c(){l.x=cp(o)}if(r||!r&&!i)if(("body"!==rd(t)||ud(o))&&(s=wd(t)),r){const e=lp(t,!0,i,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&c();i&&!r&&o&&c();const u=!o||r||i?Nd(0):up(o,s),d=a.left+s.scrollLeft-l.x-u.x,p=a.top+s.scrollTop-l.y-u.y;return{x:d,y:p,width:a.width,height:a.height}}function xp(e){return"static"===vd(e).position}function kp(e,t){if(!ld(e)||"fixed"===vd(e).position)return null;if(t)return t(e);let n=e.offsetParent;return id(e)===n&&(n=n.ownerDocument.body),n}function Ep(e,t){const n=od(e);if(pd(e))return n;if(!ld(e)){let t=bd(e);while(t&&!gd(t)){if(sd(t)&&!xp(t))return t;t=bd(t)}return n}let r=kp(e,t);while(r&&dd(r)&&xp(r))r=kp(r,t);return r&&gd(r)&&xp(r)&&!fd(r)?n:r||md(e)||n}const Cp=async function(e){const t=this.getOffsetParent||Ep,n=this.getDimensions,r=await n(e.floating);return{reference:_p(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Sp(e){return"rtl"===vd(e).direction}const Tp={convertOffsetParentRelativeRectToViewportRelativeRect:dp,getDocumentElement:id,getClippingRect:bp,getOffsetParent:Ep,getElementRects:Cp,getClientRects:pp,getDimensions:yp,getScale:op,isElement:sd,isRTL:Sp};function Np(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Pp(e,t){let n,r=null;const o=id(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}function a(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),i();const c=e.getBoundingClientRect(),{left:u,top:d,width:p,height:f}=c;if(s||t(),!p||!f)return;const m=Td(d),h=Td(o.clientWidth-(u+p)),g=Td(o.clientHeight-(d+f)),v=Td(u),w=-m+"px "+-h+"px "+-g+"px "+-v+"px",b={rootMargin:w,threshold:Cd(0,Ed(1,l))||1};let y=!0;function _(t){const r=t[0].intersectionRatio;if(r!==l){if(!y)return a();r?a(!1,r):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}1!==r||Np(c,e.getBoundingClientRect())||a(),y=!1}try{r=new IntersectionObserver(_,{...b,root:o.ownerDocument})}catch(x){r=new IntersectionObserver(_,b)}r.observe(e)}return a(!0),i}function Ap(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:a="function"===typeof ResizeObserver,layoutShift:s="function"===typeof IntersectionObserver,animationFrame:l=!1}=r,c=rp(e),u=o||i?[...c?_d(c):[],..._d(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const d=c&&s?Pp(c,n):null;let p,f=-1,m=null;a&&(m=new ResizeObserver((e=>{let[r]=e;r&&r.target===c&&m&&(m.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame((()=>{var e;null==(e=m)||e.observe(t)}))),n()})),c&&!l&&m.observe(c),m.observe(t));let h=l?lp(e):null;function g(){const t=lp(e);h&&!Np(h,t)&&n(),h=t,p=requestAnimationFrame(g)}return l&&g(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=m)||e.disconnect(),m=null,l&&cancelAnimationFrame(p)}}const Ip=Yd,Op=Qd,Rp=ep,Mp=Xd,zp=tp,Lp=(e,t,n)=>{const r=new Map,o={platform:Tp,...n},i={...o.platform,_c:r};return Kd(e,t,{...o,platform:i})};var jp="undefined"!==typeof document?ge:he;function Fp(e,t){if(e===t)return!0;if(typeof e!==typeof t)return!1;if("function"===typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"===typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!Fp(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!Fp(e[n],t[n]))return!1}return!0}return e!==e&&t!==t}function Dp(e){if("undefined"===typeof window)return 1;const t=e.ownerDocument.defaultView||window;return t.devicePixelRatio||1}function $p(e,t){const n=Dp(e);return Math.round(t*n)/n}function Zp(e){const t=ve(e);return jp((()=>{t.current=e})),t}function Hp(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:l,open:c}=e,[u,d]=fe({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,f]=fe(r);Fp(p,r)||f(r);const[m,h]=fe(null),[g,v]=fe(null),w=ye((e=>{e!==x.current&&(x.current=e,h(e))}),[]),b=ye((e=>{e!==k.current&&(k.current=e,v(e))}),[]),y=i||m,_=a||g,x=ve(null),k=ve(null),E=ve(u),C=null!=l,S=Zp(l),T=Zp(o),N=Zp(c),P=ye((()=>{if(!x.current||!k.current)return;const e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),Lp(x.current,k.current,e).then((e=>{const t={...e,isPositioned:!1!==N.current};A.current&&!Fp(E.current,t)&&(E.current=t,zt((()=>{d(t)})))}))}),[p,t,n,T,N]);jp((()=>{!1===c&&E.current.isPositioned&&(E.current.isPositioned=!1,d((e=>({...e,isPositioned:!1}))))}),[c]);const A=ve(!1);jp((()=>(A.current=!0,()=>{A.current=!1})),[]),jp((()=>{if(y&&(x.current=y),_&&(k.current=_),y&&_){if(S.current)return S.current(y,_,P);P()}}),[y,_,P,S,C]);const I=be((()=>({reference:x,floating:k,setReference:w,setFloating:b})),[w,b]),O=be((()=>({reference:y,floating:_})),[y,_]),R=be((()=>{const e={position:n,left:0,top:0};if(!O.floating)return e;const t=$p(O.floating,u.x),r=$p(O.floating,u.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...Dp(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}}),[n,s,O.floating,u.x,u.y]);return be((()=>({...u,update:P,refs:I,elements:O,floatingStyles:R})),[u,P,I,O,R])}const Up=(e,t)=>({...Op(e),options:[e,t]}),Bp=(e,t)=>({...Rp(e),options:[e,t]}),Vp=(e,t)=>({...Mp(e),options:[e,t]}),qp=(e,t)=>({...zp(e),options:[e,t]}),Wp={...Dt},Gp=Wp.useInsertionEffect,Kp=Gp||(e=>e());function Yp(e){const t=ve((()=>{0}));return Kp((()=>{t.current=e})),ye((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}var Xp="undefined"!==typeof document?ge:he;let Jp=!1,Qp=0;const ef=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Qp++;function tf(){const[e,t]=fe((()=>Jp?ef():void 0));return Xp((()=>{null==e&&t(ef())}),[]),he((()=>{Jp=!0}),[]),e}const nf=Wp.useId,rf=nf||tf;function of(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((e=>e!==n)))||[])}}}const af=K(null),sf=K(null),lf=()=>{var e;return(null==(e=_e(af))?void 0:e.id)||null},cf=()=>_e(sf),uf="data-floating-ui-focusable";function df(e){const{open:t=!1,onOpenChange:n,elements:r}=e,o=rf(),i=ve({}),[a]=fe((()=>of())),s=null!=lf();const[l,c]=fe(r.reference),u=Yp(((e,t,r)=>{i.current.openEvent=e?t:void 0,a.emit("openchange",{open:e,event:t,reason:r,nested:s}),null==n||n(e,t,r)})),d=be((()=>({setPositionReference:c})),[]),p=be((()=>({reference:l||r.reference||null,floating:r.floating||null,domReference:r.reference})),[l,r.reference,r.floating]);return be((()=>({dataRef:i,open:t,onOpenChange:u,elements:p,events:a,floatingId:o,refs:d})),[t,u,p,a,o,d])}function pf(e){void 0===e&&(e={});const{nodeId:t}=e,n=df({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,o=r.elements,[i,a]=fe(null),[s,l]=fe(null),c=null==o?void 0:o.domReference,u=c||i,d=ve(null),p=cf();Xp((()=>{u&&(d.current=u)}),[u]);const f=Hp({...e,elements:{...o,...s&&{reference:s}}}),m=ye((e=>{const t=sd(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;l(t),f.refs.setReference(t)}),[f.refs]),h=ye((e=>{(sd(e)||null===e)&&(d.current=e,a(e)),(sd(f.refs.reference.current)||null===f.refs.reference.current||null!==e&&!sd(e))&&f.refs.setReference(e)}),[f.refs]),g=be((()=>({...f.refs,setReference:h,setPositionReference:m,domReference:d})),[f.refs,h,m]),v=be((()=>({...f.elements,domReference:u})),[f.elements,u]),w=be((()=>({...f,...r,refs:g,elements:v,nodeId:t})),[f,g,v,t,r]);return Xp((()=>{r.dataRef.current.floatingContext=w;const e=null==p?void 0:p.nodesRef.current.find((e=>e.id===t));e&&(e.context=w)})),be((()=>({...f,context:w,refs:g,elements:v})),[f,g,v,w])}const ff="active",mf="selected";function hf(e,t,n){const r=new Map,o="item"===n;let i=e;if(o&&e){const{[ff]:t,[mf]:n,...r}=e;i=r}return{..."floating"===n&&{tabIndex:-1,[uf]:""},...i,...t.map((t=>{const r=t?t[n]:null;return"function"===typeof r?e?r(e):null:r})).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,i]=t;var a;o&&[ff,mf].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"===typeof i&&(null==(a=r.get(n))||a.push(i),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=i)})),e):e),{})}}function gf(e){void 0===e&&(e=[]);const t=e.map((e=>null==e?void 0:e.reference)),n=e.map((e=>null==e?void 0:e.floating)),r=e.map((e=>null==e?void 0:e.item)),o=ye((t=>hf(t,e,"reference")),t),i=ye((t=>hf(t,e,"floating")),n),a=ye((t=>hf(t,e,"item")),r);return be((()=>({getReferenceProps:o,getFloatingProps:i,getItemProps:a})),[o,i,a])}function vf(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}const wf=e=>({name:"inner",options:e,async fn(t){const{listRef:n,overflowRef:r,onFallbackChange:o,offset:i=0,index:a=0,minItemsVisible:s=4,referenceOverflowThreshold:l=0,scrollRef:c,...u}=Od(e,t),{rects:d,elements:{floating:p}}=t,f=n.current[a],m=(null==c?void 0:c.current)||p,h=p.clientTop||m.clientTop,g=0!==p.clientTop,v=0!==m.clientTop,w=p===m;if(!f)return{};const b={...t,...await Up(-f.offsetTop-p.clientTop-d.reference.height/2-f.offsetHeight/2-i).fn(t)},y=await Ip(vf(b,m.scrollHeight+h+p.clientTop),u),_=await Ip(b,{...u,elementContext:"reference"}),x=Cd(0,y.top),k=b.y+x,E=m.scrollHeight>m.clientHeight,C=E?e=>e:Sd,S=C(Cd(0,m.scrollHeight+(g&&w||v?2*h:0)-x-Cd(0,y.bottom)));if(m.style.maxHeight=S+"px",m.scrollTop=x,o){const e=m.offsetHeight<f.offsetHeight*Ed(s,n.current.length)-1||_.top>=-l||_.bottom>=-l;zt((()=>o(e)))}return r&&(r.current=await Ip(vf({...b,y:k},m.offsetHeight+h+p.clientTop),u)),{y:k}}});function bf(e,t){const{open:n,elements:r}=e,{enabled:o=!0,overflowRef:i,scrollRef:a,onChange:s}=t,l=Yp(s),c=ve(!1),u=ve(null),d=ve(null);he((()=>{if(!o)return;function e(e){if(e.ctrlKey||!t||null==i.current)return;const n=e.deltaY,r=i.current.top>=-.5,o=i.current.bottom>=-.5,a=t.scrollHeight-t.clientHeight,s=n<0?-1:1,c=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!r&&n>0||!o&&n<0?(e.preventDefault(),zt((()=>{l((e=>e+Math[c](n,a*s)))}))):/firefox/i.test(kd())&&(t.scrollTop+=n))}const t=(null==a?void 0:a.current)||r.floating;return n&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{u.current=t.scrollTop,null!=i.current&&(d.current={...i.current})})),()=>{u.current=null,d.current=null,t.removeEventListener("wheel",e)}):void 0}),[o,n,r.floating,i,a,l]);const p=be((()=>({onKeyDown(){c.current=!0},onWheel(){c.current=!1},onPointerMove(){c.current=!1},onScroll(){const e=(null==a?void 0:a.current)||r.floating;if(i.current&&e&&c.current){if(null!==u.current){const t=e.scrollTop-u.current;(i.current.bottom<-.5&&t<-1||i.current.top<-.5&&t>1)&&zt((()=>l((e=>e+t))))}requestAnimationFrame((()=>{u.current=e.scrollTop}))}}})),[r.floating,l,i,a]);return be((()=>o?{floating:p}:{}),[o,p])}let yf=K({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});yf.displayName="FloatingContext";let _f=K(null);function xf(e){return be((()=>e?"string"==typeof e?{to:e}:e:null),[e])}function kf(){return _e(yf).setReference}function Ef(){return _e(yf).getReferenceProps}function Cf(){let{getFloatingProps:e,slot:t}=_e(yf);return ye(((...n)=>Object.assign({},e(...n),{"data-anchor":t.anchor})),[e,t])}function Sf(e=null){!1===e&&(e=null),"string"==typeof e&&(e={to:e});let t=_e(_f),n=be((()=>e),[JSON.stringify(e,((e,t)=>{var n;return null!=(n=null==t?void 0:t.outerHTML)?n:t}))]);oc((()=>{null==t||t(null!=n?n:null)}),[t,n]);let r=_e(yf);return be((()=>[r.setFloating,e?r.styles:{}]),[r.setFloating,e,r.styles])}_f.displayName="PlacementContext";let Tf=4;function Nf({children:e,enabled:t=!0}){let[n,r]=fe(null),[o,i]=fe(0),a=ve(null),[s,l]=fe(null);Pf(s);let c=t&&null!==n&&null!==s,{to:u="bottom",gap:d=0,offset:p=0,padding:f=0,inner:m}=Af(n,s),[h,g="center"]=u.split(" ");oc((()=>{c&&i(0)}),[c]);let{refs:v,floatingStyles:w,context:b}=pf({open:c,placement:"selection"===h?"center"===g?"bottom":`bottom-${g}`:"center"===g?`${h}`:`${h}-${g}`,strategy:"absolute",transform:!1,middleware:[Up({mainAxis:"selection"===h?0:d,crossAxis:p}),Bp({padding:f}),"selection"!==h&&Vp({padding:f}),"selection"===h&&m?wf({...m,padding:f,overflowRef:a,offset:o,minItemsVisible:Tf,referenceOverflowThreshold:f,onFallbackChange(e){var t,n;if(!e)return;let r=b.elements.floating;if(!r)return;let o=parseFloat(getComputedStyle(r).scrollPaddingBottom)||0,a=Math.min(Tf,r.childElementCount),s=0,l=0;for(let i of null!=(n=null==(t=b.elements.floating)?void 0:t.childNodes)?n:[])if(i instanceof HTMLElement){let e=i.offsetTop,t=e+i.clientHeight+o,n=r.scrollTop,c=n+r.clientHeight;if(!(e>=n&&t<=c)){l=Math.max(0,Math.min(t,c)-Math.max(e,n)),s=i.clientHeight;break}a--}a>=1&&i((e=>{let t=s*a-l+o;return e>=t?e:t}))}}):null,qp({padding:f,apply({availableWidth:e,availableHeight:t,elements:n}){Object.assign(n.floating.style,{overflow:"auto",maxWidth:`${e}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${t}px)`})}})].filter(Boolean),whileElementsMounted:Ap}),[y=h,_=g]=b.placement.split("-");"selection"===h&&(y="selection");let k=be((()=>({anchor:[y,_].filter(Boolean).join(" ")})),[y,_]),E=bf(b,{overflowRef:a,onChange:i}),{getReferenceProps:C,getFloatingProps:S}=gf([E]),T=ac((e=>{l(e),v.setFloating(e)}));return x(_f.Provider,{value:r},x(yf.Provider,{value:{setFloating:T,setReference:v.setReference,styles:w,getReferenceProps:C,getFloatingProps:S,slot:k}},e))}function Pf(e){oc((()=>{if(!e)return;let t=new MutationObserver((()=>{let t=window.getComputedStyle(e).maxHeight,n=parseFloat(t);if(isNaN(n))return;let r=parseInt(t);isNaN(r)||n!==r&&(e.style.maxHeight=`${Math.ceil(n)}px`)}));return t.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{t.disconnect()}}),[e])}function Af(e,t){var n,r,o;let i=If(null!=(n=null==e?void 0:e.gap)?n:"var(--anchor-gap, 0)",t),a=If(null!=(r=null==e?void 0:e.offset)?r:"var(--anchor-offset, 0)",t),s=If(null!=(o=null==e?void 0:e.padding)?o:"var(--anchor-padding, 0)",t);return{...e,gap:i,offset:a,padding:s}}function If(e,t,n=void 0){let r=rc(),o=ac(((e,t)=>{if(null==e)return[n,null];if("number"==typeof e)return[e,null];if("string"==typeof e){if(!t)return[n,null];let o=Rf(e,t);return[o,n=>{let i=Of(e);{let a=i.map((e=>window.getComputedStyle(t).getPropertyValue(e)));r.requestAnimationFrame((function s(){r.nextFrame(s);let l=!1;for(let[e,n]of i.entries()){let r=window.getComputedStyle(t).getPropertyValue(n);if(a[e]!==r){a[e]=r,l=!0;break}}if(!l)return;let c=Rf(e,t);o!==c&&(n(c),o=c)}))}return r.dispose}]}return[n,null]})),i=be((()=>o(e,t)[0]),[e,t]),[a=i,s]=fe();return oc((()=>{let[n,r]=o(e,t);if(s(n),r)return r(s)}),[e,t]),a}function Of(e){let t=/var\((.*)\)/.exec(e);if(t){let e=t[1].indexOf(",");if(-1===e)return[t[1]];let n=t[1].slice(0,e).trim(),r=t[1].slice(e+1).trim();return r?[n,...Of(r)]:[n]}return[]}function Rf(e,t){let n=document.createElement("div");t.appendChild(n),n.style.setProperty("margin-top","0px","important"),n.style.setProperty("margin-top",e,"important");let r=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),r}let Mf=K(null);Mf.displayName="OpenClosedContext";var zf=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(zf||{});function Lf(){return _e(Mf)}function jf({value:e,children:t}){return Ft.createElement(Mf.Provider,{value:e},t)}var Ff,Df={exports:{}},$f={};function Zf(){if(Ff)return $f;Ff=1;var e=Dt;function t(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t}var n="function"===typeof Object.is?Object.is:t,r=e.useSyncExternalStore,o=e.useRef,i=e.useEffect,a=e.useMemo,s=e.useDebugValue;return $f.useSyncExternalStoreWithSelector=function(e,t,l,c,u){var d=o(null);if(null===d.current){var p={hasValue:!1,value:null};d.current=p}else p=d.current;d=a((function(){function e(e){if(!i){if(i=!0,r=e,e=c(e),void 0!==u&&p.hasValue){var t=p.value;if(u(t,e))return o=t}return o=e}if(t=o,n(r,e))return t;var a=c(e);return void 0!==u&&u(t,a)?(r=e,t):(r=e,o=a)}var r,o,i=!1,a=void 0===l?null:l;return[function(){return e(t())},null===a?void 0:function(){return e(a())}]}),[t,l,c,u]);var f=r(e,d[0],d[1]);return i((function(){p.hasValue=!0,p.value=f}),[f]),s(f),f},$f}var Hf;
/**
 * @license React
 * use-sync-external-store-with-selector.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function Uf(){return Hf||(Hf=1,Df.exports=Zf()),Df.exports}var Bf,Vf,qf,Wf=Uf(),Gf=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},Kf=(e,t,n)=>(Gf(e,t,"read from private field"),n?n.call(e):t.get(e)),Yf=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},Xf=(e,t,n,r)=>(Gf(e,t,"write to private field"),t.set(e,n),n);class Jf{constructor(e){Yf(this,Bf,{}),Yf(this,Vf,new eu((()=>new Set))),Yf(this,qf,new Set),Xf(this,Bf,e)}get state(){return Kf(this,Bf)}subscribe(e,t){let n={selector:e,callback:t,current:e(Kf(this,Bf))};return Kf(this,qf).add(n),()=>{Kf(this,qf).delete(n)}}on(e,t){return Kf(this,Vf).get(e).add(t),()=>{Kf(this,Vf).get(e).delete(t)}}send(e){Xf(this,Bf,this.reduce(Kf(this,Bf),e));for(let t of Kf(this,qf)){let e=t.selector(Kf(this,Bf));Qf(t.current,e)||(t.current=e,t.callback(e))}for(let t of Kf(this,Vf).get(e.type))t(Kf(this,Bf),e)}}function Qf(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&em(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&em(e.entries(),t.entries()):!(!tm(e)||!tm(t))&&em(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function em(e,t){do{let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}while(1)}function tm(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function nm(e){let[t,n]=e(),r=nc();return(...e)=>{t(...e),r.dispose(),r.microTask(n)}}function rm(e,t,n=Qf){return Wf.useSyncExternalStoreWithSelector(ac((t=>e.subscribe(om,t))),ac((()=>e.state)),ac((()=>e.state)),ac(t),n)}function om(e){return e}function im(e){throw new Error("Unexpected object: "+e)}Bf=new WeakMap,Vf=new WeakMap,qf=new WeakMap;var am=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(am||{});function sm(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 1:-1===o&&(o=n.length);for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 2:for(let e=o+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 4:for(let r=0;r<n.length;++r)if(t.resolveId(n[r],r,n)===e.id)return r;return r;case 5:return null;default:im(e)}}function lm(e){let t=ac(e),n=ve(!1);he((()=>(n.current=!1,()=>{n.current=!0,tc((()=>{n.current&&t()}))})),[t])}function cm(){let e="undefined"==typeof document;return"useSyncExternalStore"in Dt&&(e=>e.useSyncExternalStore)(Dt)((()=>()=>{}),(()=>!1),(()=>!e))}function um(){let e=cm(),[t,n]=fe(Ql.isHandoffComplete);return t&&!1===Ql.isHandoffComplete&&n(!1),he((()=>{!0!==t&&n(!0)}),[t]),he((()=>Ql.handoff()),[]),!e&&t}let dm=K(!1);function pm(){return _e(dm)}function fm(e){let t=pm(),n=_e(wm),[r,o]=fe((()=>{var r,o;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(Ql.isServer)return null;let i=null==e?void 0:e.getElementById("stagewise-toolbar-portal-root");if(i)return i;if(null===e)return null;let a=e.createElement("div");return a.setAttribute("id","stagewise-toolbar-portal-root"),a.style.pointerEvents="auto",(null!=(o=e.querySelector("stagewise-companion-anchor"))?o:e.body).appendChild(a)}));return he((()=>{var t;if(null===r)return;const n=null!=(t=null==e?void 0:e.querySelector("stagewise-companion-anchor"))?t:null==e?void 0:e.body;null!=n&&n.contains(r)||null==n||n.appendChild(r)}),[r,e]),he((()=>{t||null!==n&&o(n.current)}),[n,o,t]),r}let mm=C,hm=kc((function(e,t){let{ownerDocument:n=null,...r}=e,o=ve(null),i=Lc(zc((e=>{o.current=e})),t),a=zu(o),s=null!=n?n:a,l=fm(s),[c]=fe((()=>{var e;return Ql.isServer?null:null!=(e=null==s?void 0:s.createElement("div"))?e:null})),u=_e(ym),d=um();oc((()=>{!l||!c||l.contains(c)||(c.setAttribute("data-headlessui-portal",""),l.appendChild(c))}),[l,c]),oc((()=>{if(c&&u)return u.register(c)}),[u,c]),lm((()=>{var e;!l||!c||(c instanceof Node&&l.contains(c)&&l.removeChild(c),l.childNodes.length<=0&&(null==(e=l.parentElement)||e.removeChild(l)))}));let p=gc();return d&&l&&c?st(p({ourProps:{ref:i},theirProps:r,slot:{},defaultTag:mm,name:"Portal"}),c):null}));function gm(e,t){let n=Lc(t),{enabled:r=!0,ownerDocument:o,...i}=e,a=gc();return r?Ft.createElement(hm,{...i,ownerDocument:o,ref:n}):a({ourProps:{ref:n},theirProps:i,slot:{},defaultTag:mm,name:"Portal"})}let vm=C,wm=K(null);function bm(e,t){let{target:n,...r}=e,o={ref:Lc(t)},i=gc();return Ft.createElement(wm.Provider,{value:n},i({ourProps:o,theirProps:r,defaultTag:vm,name:"Popover.Group"}))}let ym=K(null),_m=kc(gm),xm=kc(bm),km=Object.assign(_m,{Group:xm});function Em(e,t){let n=ve({left:0,top:0});if(oc((()=>{if(!t)return;let e=t.getBoundingClientRect();e&&(n.current=e)}),[e,t]),null==t||!e||t===document.activeElement)return!1;let r=t.getBoundingClientRect();return r.top!==n.current.top||r.left!==n.current.left}let Cm=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function Sm(e){var t,n;let r=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return r;let i=!1;for(let s of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))s.remove(),i=!0;let a=i?null!=(n=o.innerText)?n:"":r;return Cm.test(a)&&(a=a.replace(Cm,"")),a}function Tm(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map((e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():Sm(t).trim()}return null})).filter(Boolean);if(e.length>0)return e.join(", ")}return Sm(e).trim()}function Nm(e){let t=ve(""),n=ve("");return ac((()=>{let r=e.current;if(!r)return"";let o=r.innerText;if(t.current===o)return n.current;let i=Tm(r).trim().toLowerCase();return t.current=o,n.current=i,i}))}var Pm=Object.defineProperty,Am=(e,t,n)=>t in e?Pm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Im=(e,t,n)=>(Am(e,"symbol"!=typeof t?t+"":t,n),n),Om=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Om||{}),Rm=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Rm||{}),Mm=(e=>(e[e.OpenMenu=0]="OpenMenu",e[e.CloseMenu=1]="CloseMenu",e[e.GoToItem=2]="GoToItem",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterItems=5]="RegisterItems",e[e.UnregisterItems=6]="UnregisterItems",e[e.SetButtonElement=7]="SetButtonElement",e[e.SetItemsElement=8]="SetItemsElement",e[e.SortItems=9]="SortItems",e))(Mm||{});function zm(e,t=e=>e){let n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=Cu(t(e.items.slice()),(e=>e.dataRef.current.domRef.current)),o=n?r.indexOf(n):null;return-1===o&&(o=null),{items:r,activeItemIndex:o}}let Lm={[1](e){return 1===e.menuState?e:{...e,activeItemIndex:null,pendingFocus:{focus:am.Nothing},menuState:1}},[0](e,t){return 0===e.menuState?e:{...e,__demoMode:!1,pendingFocus:t.focus,menuState:0}},[2]:(e,t)=>{var n,r,o,i,a;if(1===e.menuState)return e;let s={...e,searchQuery:"",activationTrigger:null!=(n=t.trigger)?n:1,__demoMode:!1};if(t.focus===am.Nothing)return{...s,activeItemIndex:null};if(t.focus===am.Specific)return{...s,activeItemIndex:e.items.findIndex((e=>e.id===t.id))};if(t.focus===am.Previous){let n=e.activeItemIndex;if(null!==n){let i=e.items[n].dataRef.current.domRef,a=sm(t,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==a){let t=e.items[a].dataRef.current.domRef;if((null==(r=i.current)?void 0:r.previousElementSibling)===t.current||null===(null==(o=t.current)?void 0:o.previousElementSibling))return{...s,activeItemIndex:a}}}}else if(t.focus===am.Next){let n=e.activeItemIndex;if(null!==n){let r=e.items[n].dataRef.current.domRef,o=sm(t,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==o){let t=e.items[o].dataRef.current.domRef;if((null==(i=r.current)?void 0:i.nextElementSibling)===t.current||null===(null==(a=t.current)?void 0:a.nextElementSibling))return{...s,activeItemIndex:o}}}}let l=zm(e),c=sm(t,{resolveItems:()=>l.items,resolveActiveIndex:()=>l.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...s,...l,activeItemIndex:c}},[3]:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find((e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled})),i=o?e.items.indexOf(o):-1;return-1===i||i===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:i,activationTrigger:1}},[4](e){return""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,t)=>{let n=e.items.concat(t.items.map((e=>e))),r=e.activeItemIndex;return e.pendingFocus.focus!==am.Nothing&&(r=sm(e.pendingFocus,{resolveItems:()=>n,resolveActiveIndex:()=>e.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled})),{...e,items:n,activeItemIndex:r,pendingFocus:{focus:am.Nothing},pendingShouldSort:!0}},[6]:(e,t)=>{let n=e.items,r=[],o=new Set(t.items);for(let[i,a]of n.entries())if(o.has(a.id)&&(r.push(i),o.delete(a.id),0===o.size))break;if(r.length>0){n=n.slice();for(let e of r.reverse())n.splice(e,1)}return{...e,items:n,activationTrigger:1}},[7]:(e,t)=>e.buttonElement===t.element?e:{...e,buttonElement:t.element},[8]:(e,t)=>e.itemsElement===t.element?e:{...e,itemsElement:t.element},[9]:e=>e.pendingShouldSort?{...e,...zm(e),pendingShouldSort:!1}:e};class jm extends Jf{constructor(e){super(e),Im(this,"actions",{registerItem:nm((()=>{let e=[],t=new Set;return[(n,r)=>{t.has(r)||(t.add(r),e.push({id:n,dataRef:r}))},()=>(t.clear(),this.send({type:5,items:e.splice(0)}))]})),unregisterItem:nm((()=>{let e=[];return[t=>e.push(t),()=>this.send({type:6,items:e.splice(0)})]}))}),Im(this,"selectors",{activeDescendantId(e){var t;let n=e.activeItemIndex,r=e.items;return null===n||null==(t=r[n])?void 0:t.id},isActive(e,t){var n;let r=e.activeItemIndex,o=e.items;return null!==r&&(null==(n=o[r])?void 0:n.id)===t},shouldScrollIntoView(e,t){return!e.__demoMode&&0===e.menuState&&0!==e.activationTrigger&&this.isActive(e,t)}}),this.on(5,(()=>{requestAnimationFrame((()=>{this.send({type:9})}))}))}static new({__demoMode:e=!1}={}){return new jm({__demoMode:e,menuState:e?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1,pendingShouldSort:!1,pendingFocus:{focus:am.Nothing}})}reduce(e,t){return fc(t.type,Lm,e,t)}}const Fm=K(null);function Dm(e){let t=_e(Fm);if(null===t){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,$m),t}return t}function $m({__demoMode:e=!1}={}){return be((()=>jm.new({__demoMode:e})),[])}let Zm=C;function Hm(e,t){let{__demoMode:n=!1,...r}=e,o=$m({__demoMode:n}),[i,a,s]=rm(o,(e=>[e.menuState,e.itemsElement,e.buttonElement])),l=Lc(t),c=i===Om.Open;Mu(c,[s,a],((e,t)=>{var n;o.send({type:Mm.CloseMenu}),bu(t,wu.Loose)||(e.preventDefault(),null==(n=o.state.buttonElement)||n.focus())}));let u=ac((()=>{o.send({type:Mm.CloseMenu})})),d=be((()=>({open:i===Om.Open,close:u})),[i,u]),p={ref:l},f=gc();return Ft.createElement(Nf,null,Ft.createElement(Fm.Provider,{value:o},Ft.createElement(jf,{value:fc(i,{[Om.Open]:zf.Open,[Om.Closed]:zf.Closed})},f({ourProps:p,theirProps:r,slot:d,defaultTag:Zm,name:"Menu"}))))}let Um="button";function Bm(e,t){let n=Dm("Menu.Button"),r=Ee(),{id:o=`headlessui-menu-button-${r}`,disabled:i=!1,autoFocus:a=!1,...s}=e,l=ve(null),c=Ef(),u=Lc(t,l,kf(),ac((e=>n.send({type:Mm.SetButtonElement,element:e})))),d=ac((e=>{switch(e.key){case Bc.Space:case Bc.Enter:case Bc.ArrowDown:e.preventDefault(),e.stopPropagation(),n.send({type:Mm.OpenMenu,focus:{focus:am.First}});break;case Bc.ArrowUp:e.preventDefault(),e.stopPropagation(),n.send({type:Mm.OpenMenu,focus:{focus:am.Last}});break}})),p=ac((e=>{switch(e.key){case Bc.Space:e.preventDefault();break}})),[f,m]=rm(n,(e=>[e.menuState,e.itemsElement])),h=ac((e=>{var t;if(0===e.button){if(Oc(e.currentTarget))return e.preventDefault();i||(f===Om.Open?(zt((()=>n.send({type:Mm.CloseMenu}))),null==(t=l.current)||t.focus({preventScroll:!0})):(e.preventDefault(),n.send({type:Mm.OpenMenu,focus:{focus:am.Nothing},trigger:Rm.Pointer})))}})),{isFocusVisible:g,focusProps:v}=Gl({autoFocus:a}),{isHovered:w,hoverProps:b}=Wl({isDisabled:i}),{pressed:y,pressProps:_}=cc({disabled:i}),x=be((()=>({open:f===Om.Open,active:y||f===Om.Open,disabled:i,hover:w,focus:g,autofocus:a})),[f,w,g,y,i,a]),k=xc(c(),{ref:u,id:o,type:Lu(e,l.current),"aria-haspopup":"menu","aria-controls":null==m?void 0:m.id,"aria-expanded":f===Om.Open,disabled:i||void 0,autoFocus:a,onKeyDown:d,onKeyUp:p,onMouseDown:h},v,b,_);return gc()({ourProps:k,theirProps:s,slot:x,defaultTag:Um,name:"Menu.Button"})}let Vm="div",qm=mc.RenderStrategy|mc.Static;function Wm(e,t){let n=Ee(),{id:r=`headlessui-menu-items-${n}`,anchor:o,portal:i=!1,modal:a=!0,transition:s=!1,...l}=e,c=xf(o),u=Dm("Menu.Items"),[d,p]=Sf(c),f=Cf(),[m,h]=fe(null),g=Lc(t,c?d:null,ac((e=>u.send({type:Mm.SetItemsElement,element:e}))),h),[v,w]=rm(u,(e=>[e.menuState,e.buttonElement])),b=zu(w),y=zu(m);c&&(i=!0);let _=Lf(),[x,k]=Xu(s,m,null!==_?(_&zf.Open)===zf.Open:v===Om.Open);uu(x,w,(()=>{u.send({type:Mm.CloseMenu})}));let E=rm(u,(e=>e.__demoMode)),C=!E&&(a&&v===Om.Open);Uu(C,y);let S=!E&&(a&&v===Om.Open);cu(S,{allowed:ye((()=>[w,m]),[w,m])});let T=v!==Om.Open,N=!Em(T,w)&&x;he((()=>{let e=m;e&&v===Om.Open&&e!==(null==y?void 0:y.activeElement)&&e.focus({preventScroll:!0})}),[v,m,y]),td(v===Om.Open,{container:m,accept(e){return"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute("role","none")}});let P=rc(),A=ac((e=>{var t,n,r;switch(P.dispose(),e.key){case Bc.Space:if(""!==u.state.searchQuery)return e.preventDefault(),e.stopPropagation(),u.send({type:Mm.Search,value:e.key});case Bc.Enter:if(e.preventDefault(),e.stopPropagation(),null!==u.state.activeItemIndex){let{dataRef:e}=u.state.items[u.state.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}u.send({type:Mm.CloseMenu}),yu(u.state.buttonElement);break;case Bc.ArrowDown:return e.preventDefault(),e.stopPropagation(),u.send({type:Mm.GoToItem,focus:am.Next});case Bc.ArrowUp:return e.preventDefault(),e.stopPropagation(),u.send({type:Mm.GoToItem,focus:am.Previous});case Bc.Home:case Bc.PageUp:return e.preventDefault(),e.stopPropagation(),u.send({type:Mm.GoToItem,focus:am.First});case Bc.End:case Bc.PageDown:return e.preventDefault(),e.stopPropagation(),u.send({type:Mm.GoToItem,focus:am.Last});case Bc.Escape:e.preventDefault(),e.stopPropagation(),zt((()=>u.send({type:Mm.CloseMenu}))),null==(r=u.state.buttonElement)||r.focus({preventScroll:!0});break;case Bc.Tab:e.preventDefault(),e.stopPropagation(),zt((()=>u.send({type:Mm.CloseMenu}))),Su(u.state.buttonElement,e.shiftKey?fu.Previous:fu.Next);break;default:1===e.key.length&&(u.send({type:Mm.Search,value:e.key}),P.setTimeout((()=>u.send({type:Mm.ClearSearch})),350));break}})),I=ac((e=>{switch(e.key){case Bc.Space:e.preventDefault();break}})),O=be((()=>({open:v===Om.Open})),[v]),R=xc(c?f():{},{"aria-activedescendant":rm(u,u.selectors.activeDescendantId),"aria-labelledby":rm(u,(e=>{var t;return null==(t=e.buttonElement)?void 0:t.id})),id:r,onKeyDown:A,onKeyUp:I,role:"menu",tabIndex:v===Om.Open?0:void 0,ref:g,style:{...l.style,...p,"--button-width":Qc(w,!0).width},...Yu(k)}),M=gc();return Ft.createElement(km,{enabled:!!i&&(e.static||x),ownerDocument:b},M({ourProps:R,theirProps:l,slot:O,defaultTag:Vm,features:qm,visible:N,name:"Menu.Items"}))}let Gm=C;function Km(e,t){let n=Ee(),{id:r=`headlessui-menu-item-${n}`,disabled:o=!1,...i}=e,a=Dm("Menu.Item"),s=rm(a,(e=>a.selectors.isActive(e,r))),l=ve(null),c=Lc(t,l),u=rm(a,(e=>a.selectors.shouldScrollIntoView(e,r)));oc((()=>{if(u)return nc().requestAnimationFrame((()=>{var e,t;null==(t=null==(e=l.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})}))}),[u,l]);let d=Nm(l),p=ve({disabled:o,domRef:l,get textValue(){return d()}});oc((()=>{p.current.disabled=o}),[p,o]),oc((()=>(a.actions.registerItem(r,p),()=>a.actions.unregisterItem(r))),[p,r]);let f=ac((()=>{a.send({type:Mm.CloseMenu})})),m=ac((e=>{if(o)return e.preventDefault();a.send({type:Mm.CloseMenu}),yu(a.state.buttonElement)})),h=ac((()=>{if(o)return a.send({type:Mm.GoToItem,focus:am.Nothing});a.send({type:Mm.GoToItem,focus:am.Specific,id:r})})),g=Vu(),v=ac((e=>{g.update(e),!o&&(s||a.send({type:Mm.GoToItem,focus:am.Specific,id:r,trigger:Rm.Pointer}))})),w=ac((e=>{g.wasMoved(e)&&(o||s||a.send({type:Mm.GoToItem,focus:am.Specific,id:r,trigger:Rm.Pointer}))})),b=ac((e=>{g.wasMoved(e)&&(o||s&&a.send({type:Mm.GoToItem,focus:am.Nothing}))})),[y,_]=Gc(),[x,k]=$c(),E=be((()=>({active:s,focus:s,disabled:o,close:f})),[s,o,f]),C={id:r,ref:c,role:"menuitem",tabIndex:!0===o?void 0:-1,"aria-disabled":!0===o||void 0,"aria-labelledby":y,"aria-describedby":x,disabled:void 0,onClick:m,onFocus:h,onPointerEnter:v,onMouseEnter:v,onPointerMove:w,onMouseMove:w,onPointerLeave:b,onMouseLeave:b},S=gc();return Ft.createElement(_,null,Ft.createElement(k,null,S({ourProps:C,theirProps:i,slot:E,defaultTag:Gm,name:"Menu.Item"})))}let Ym="div";function Xm(e,t){let[n,r]=Gc(),o=e,i={ref:t,"aria-labelledby":n,role:"group"},a=gc();return Ft.createElement(r,null,a({ourProps:i,theirProps:o,slot:{},defaultTag:Ym,name:"Menu.Section"}))}let Jm="header";function Qm(e,t){let n=Ee(),{id:r=`headlessui-menu-heading-${n}`,...o}=e,i=qc();oc((()=>i.register(r)),[r,i.register]);let a={id:r,ref:t,role:"presentation",...i.props};return gc()({ourProps:a,theirProps:o,slot:{},defaultTag:Jm,name:"Menu.Heading"})}let eh="div";function th(e,t){let n=e,r={ref:t,role:"separator"};return gc()({ourProps:r,theirProps:n,slot:{},defaultTag:eh,name:"Menu.Separator"})}let nh=kc(Hm),rh=kc(Bm),oh=kc(Wm),ih=kc(Km),ah=kc(Xm),sh=kc(Qm),lh=kc(th),ch=Object.assign(nh,{Button:rh,Items:oh,Item:ih,Section:ah,Heading:sh,Separator:lh}),uh="textarea";function dh(e,t){let n=Ee(),r=Ic(),o=dc(),{id:i=r||`headlessui-textarea-${n}`,disabled:a=o||!1,autoFocus:s=!1,invalid:l=!1,...c}=e,u=Wc(),d=Dc(),{isFocused:p,focusProps:f}=Gl({autoFocus:s}),{isHovered:m,hoverProps:h}=Wl({isDisabled:a}),g=xc({ref:t,id:i,"aria-labelledby":u,"aria-describedby":d,"aria-invalid":l?"true":void 0,disabled:a||void 0,autoFocus:s},f,h),v=be((()=>({disabled:a,invalid:l,hover:m,focus:p,autofocus:s})),[a,l,m,p,s]);return gc()({ourProps:g,theirProps:c,slot:v,defaultTag:uh,name:"Textarea"})}let ph=kc(dh);
/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fh=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mh=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,((e,t,n)=>n?n.toUpperCase():t.toLowerCase())),hh=e=>{const t=mh(e);return t.charAt(0).toUpperCase()+t.slice(1)},gh=(...e)=>e.filter(((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t)).join(" ").trim(),vh=e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};
/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
var wh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};
/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bh=We((({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:a,...s},l)=>x("svg",{ref:l,...wh,width:t,height:t,stroke:e,strokeWidth:r?24*Number(n)/Number(t):n,className:gh("lucide",o),...!i&&!vh(s)&&{"aria-hidden":"true"},...s},[...a.map((([e,t])=>x(e,t))),...Array.isArray(i)?i:[i]]))),yh=(e,t)=>{const n=We((({className:n,...r},o)=>x(bh,{ref:o,iconNode:t,className:gh(`lucide-${fh(hh(e))}`,`lucide-${e}`,n),...r})));return n.displayName=hh(e),n},_h=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],xh=yh("chevron-down",_h),kh=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Eh=yh("chevron-up",kh),Ch=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]],Sh=yh("ellipsis",Ch),Th=[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]],Nh=yh("minimize-2",Th),Ph=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Ah=yh("plus",Ph),Ih=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Oh=yh("send",Ih),Rh=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Mh=yh("trash-2",Rh),zh=Be((()=>{const{chatAreaState:e}=ma();return"hidden"===e?null:J(Lh,{children:[J(jh,{}),"compact"===e&&J(Fh,{}),"expanded"===e&&J(Dh,{})]})})),Lh=Be((({children:e})=>J("div",{className:"h-auto w-full overflow-x-hidden",children:e}))),jh=Be((()=>{const{chatAreaState:e,setChatAreaState:t,stopPromptCreation:n}=ma(),r=ye((()=>{t("compact"===e?"expanded":"compact")}),[e,t]),o=ye((()=>{n(),t("hidden")}),[t,n]);return J("div",{className:"flex w-full flex-row items-center justify-center rounded-t-3xl px-3 py-1",children:[J(Pc,{className:"size-5 bg-transparent text-muted-foreground/30 transition-colors duration-100 hover:text-muted-foreground",onClick:r,children:J("compact"===e?Eh:xh,{className:"size-5"})}),J(Pc,{className:"absolute right-2 flex h-fit w-fit flex-row items-center gap-1 bg-transparent p-1 text-xs text-zinc-950 opacity-50 transition-all duration-100 hover:opacity-100",onClick:o,children:["Close menu",J("div",{className:"rounded-md bg-zinc-600 px-0.5 py-0 text-xs text-zinc-50",children:"esc"})]})]})})),Fh=Be((()=>J("div",{className:"flex w-full flex-col gap-1 p-3",children:J("span",{className:"text-sm text-zinc-950/50",children:"This is the compact chat area... Showing just the last response from the assistant."})}))),Dh=Be((()=>{const{chats:e,currentChatId:t,setCurrentChat:n,createChat:r}=ma(),o=e.find((e=>e.id===t)),i=e.some((e=>"new_chat"===e.id)),a=ye((()=>{r()}),[r]),s=ye((e=>{n(e)}),[n]);return J("div",{className:"flex max-h-[50vh] w-full flex-col gap-3 p-3 pb-0",children:[J("div",{className:"flex flex-1 flex-col gap-2 overflow-y-auto p-1",children:null==o?void 0:o.messages.map((e=>J("div",{className:"flex "+("assistant"===e.sender?"justify-start":"justify-end"),children:J("div",{className:"max-w-[80%] rounded-xl px-2 py-1 text-sm "+("assistant"===e.sender?"bg-zinc-950/5 text-zinc-950":"bg-blue-600 text-white"),children:e.content})},e.id)))}),J("div",{className:"flex flex-row items-center justify-start gap-2 overflow-x-auto overflow-y-visible border-border/10 border-t pt-2 pb-2",children:[!i&&J(Pc,{className:"h-6 flex-shrink-0 rounded-full bg-zinc-950/5 px-2 font-semibold text-foreground text-xs",onClick:a,children:J(Ah,{className:"size-3"})}),e.map((e=>J(Pc,{className:qs("h-5 max-w-48 flex-shrink-0 overflow-hidden truncate rounded-full bg-zinc-950/5 px-2 text-muted-foreground text-xs",e.id===t&&"bg-white/60 text-zinc-950 shadow-blue-600/50 shadow-sm"),onClick:()=>s(e.id),children:e.title||"New chat"},e.id)))]})]})}));
/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var $h="2.0.3",Zh=500,Hh="user-agent",Uh="",Bh="?",Vh="function",qh="undefined",Wh="object",Gh="string",Kh="browser",Yh="cpu",Xh="device",Jh="engine",Qh="os",eg="result",tg="name",ng="type",rg="vendor",og="version",ig="architecture",ag="major",sg="model",lg="console",cg="mobile",ug="tablet",dg="smarttv",pg="wearable",fg="xr",mg="embedded",hg="inapp",gg="brands",vg="formFactors",wg="fullVersionList",bg="platform",yg="platformVersion",_g="bitness",xg="sec-ch-ua",kg=xg+"-full-version-list",Eg=xg+"-arch",Cg=xg+"-"+_g,Sg=xg+"-form-factors",Tg=xg+"-"+cg,Ng=xg+"-"+sg,Pg=xg+"-"+bg,Ag=Pg+"-version",Ig=[gg,wg,cg,sg,bg,yg,ig,vg,_g],Og="Amazon",Rg="Apple",Mg="ASUS",zg="BlackBerry",Lg="Google",jg="Huawei",Fg="Lenovo",Dg="Honor",$g="LG",Zg="Microsoft",Hg="Motorola",Ug="Nvidia",Bg="OnePlus",Vg="OPPO",qg="Samsung",Wg="Sharp",Gg="Sony",Kg="Xiaomi",Yg="Zebra",Xg="Chrome",Jg="Chromium",Qg="Chromecast",ev="Edge",tv="Firefox",nv="Opera",rv="Facebook",ov="Sogou",iv="Mobile ",av=" Browser",sv="Windows",lv=typeof window!==qh,cv=lv&&window.navigator?window.navigator:void 0,uv=cv&&cv.userAgentData?cv.userAgentData:void 0,dv=function(e,t){var n={},r=t;if(!mv(t))for(var o in r={},t)for(var i in t[o])r[i]=t[o][i].concat(r[i]?r[i]:[]);for(var a in e)n[a]=r[a]&&r[a].length%2===0?r[a].concat(e[a]):e[a];return n},pv=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},fv=function(e,t){if(typeof e===Wh&&e.length>0){for(var n in e)if(vv(e[n])==vv(t))return!0;return!1}return!!hv(e)&&-1!==vv(t).indexOf(vv(e))},mv=function(e,t){for(var n in e)return/^(browser|cpu|device|engine|os)$/.test(n)||!!t&&mv(e[n])},hv=function(e){return typeof e===Gh},gv=function(e){if(e){for(var t=[],n=yv(/\\?\"/g,e).split(","),r=0;r<n.length;r++)if(n[r].indexOf(";")>-1){var o=xv(n[r]).split(";v=");t[r]={brand:o[0],version:o[1]}}else t[r]=xv(n[r]);return t}},vv=function(e){return hv(e)?e.toLowerCase():e},wv=function(e){return hv(e)?yv(/[^\d\.]/g,e).split(".")[0]:void 0},bv=function(e){for(var t in e){var n=e[t];typeof n==Wh&&2==n.length?this[n[0]]=n[1]:this[n]=void 0}return this},yv=function(e,t){return hv(t)?t.replace(e,Uh):t},_v=function(e){return yv(/\\?\"/g,e)},xv=function(e,t){if(hv(e))return e=yv(/^\s\s*/,e),typeof t===qh?e:e.substring(0,Zh)},kv=function(e,t){if(e&&t){var n,r,o,i,a,s,l=0;while(l<t.length&&!a){var c=t[l],u=t[l+1];n=r=0;while(n<c.length&&!a){if(!c[n])break;if(a=c[n++].exec(e),a)for(o=0;o<u.length;o++)s=a[++r],i=u[o],typeof i===Wh&&i.length>0?2===i.length?typeof i[1]==Vh?this[i[0]]=i[1].call(this,s):this[i[0]]=i[1]:3===i.length?typeof i[1]!==Vh||i[1].exec&&i[1].test?this[i[0]]=s?s.replace(i[1],i[2]):void 0:this[i[0]]=s?i[1].call(this,s,i[2]):void 0:4===i.length&&(this[i[0]]=s?i[3].call(this,s.replace(i[1],i[2])):void 0):this[i]=s||void 0}l+=2}}},Ev=function(e,t){for(var n in t)if(typeof t[n]===Wh&&t[n].length>0){for(var r=0;r<t[n].length;r++)if(fv(t[n][r],e))return n===Bh?void 0:n}else if(fv(t[n],e))return n===Bh?void 0:n;return t.hasOwnProperty("*")?t["*"]:e},Cv={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Sv={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":void 0},Tv={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[og,[tg,iv+"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[og,[tg,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[tg,og],[/opios[\/ ]+([\w\.]+)/i],[og,[tg,nv+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[og,[tg,nv+" GX"]],[/\bopr\/([\w\.]+)/i],[og,[tg,nv]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[og,[tg,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[og,[tg,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon|otter|dooble|(?:lg |qute)browser)\/([-\w\.]+)/i,/(heytap|ovi|115|surf)browser\/([\d\.]+)/i,/(ecosia|weibo)(?:__| \w+@)([\d\.]+)/i],[tg,og],[/quark(?:pc)?\/([-\w\.]+)/i],[og,[tg,"Quark"]],[/\bddg\/([\w\.]+)/i],[og,[tg,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[og,[tg,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[og,[tg,"WeChat"]],[/konqueror\/([\w\.]+)/i],[og,[tg,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[og,[tg,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[og,[tg,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[og,[tg,"Smart "+Fg+av]],[/(avast|avg)\/([\w\.]+)/i],[[tg,/(.+)/,"$1 Secure"+av],og],[/\bfocus\/([\w\.]+)/i],[og,[tg,tv+" Focus"]],[/\bopt\/([\w\.]+)/i],[og,[tg,nv+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[og,[tg,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[og,[tg,"Dolphin"]],[/coast\/([\w\.]+)/i],[og,[tg,nv+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[og,[tg,"MIUI"+av]],[/fxios\/([\w\.-]+)/i],[og,[tg,iv+tv]],[/\bqihoobrowser\/?([\w\.]*)/i],[og,[tg,"360"]],[/\b(qq)\/([\w\.]+)/i],[[tg,/(.+)/,"$1Browser"],og],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[tg,/(.+)/,"$1"+av],og],[/samsungbrowser\/([\w\.]+)/i],[og,[tg,qg+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[og,[tg,ov+" Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[tg,ov+" Mobile"],og],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[tg,og],[/(lbbrowser|rekonq)/i],[tg],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[og,tg],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[tg,rv],og,[ng,hg]],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/(daum)apps[\/ ]([\w\.]+)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat)[\/ ]([-\w\.]+)/i],[tg,og,[ng,hg]],[/\bgsa\/([\w\.]+) .*safari\//i],[og,[tg,"GSA"],[ng,hg]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[og,[tg,"TikTok"],[ng,hg]],[/\[(linkedin)app\]/i],[tg,[ng,hg]],[/(chromium)[\/ ]([-\w\.]+)/i],[tg,og],[/headlesschrome(?:\/([\w\.]+)| )/i],[og,[tg,Xg+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[tg,Xg+" WebView"],og],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[og,[tg,"Android"+av]],[/chrome\/([\w\.]+) mobile/i],[og,[tg,iv+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[tg,og],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[og,[tg,iv+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[tg,iv+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[og,tg],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[tg,[og,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[tg,og],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[tg,iv+tv],og],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[tg,"Netscape"],og],[/(wolvic|librewolf)\/([\w\.]+)/i],[tg,og],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[og,[tg,tv+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[tg,[og,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[tg,[og,/[^\d\.]+./,Uh]]],cpu:[[/\b((amd|x|x86[-_]?|wow|win)64)\b/i],[[ig,"amd64"]],[/(ia32(?=;))/i,/\b((i[346]|x)86)(pc)?\b/i],[[ig,"ia32"]],[/\b(aarch64|arm(v?[89]e?l?|_?64))\b/i],[[ig,"arm64"]],[/\b(arm(v[67])?ht?n?[fl]p?)\b/i],[[ig,"armhf"]],[/( (ce|mobile); ppc;|\/[\w\.]+arm\b)/i],[[ig,"arm"]],[/((ppc|powerpc)(64)?)( mac|;|\))/i],[[ig,/ower/,Uh,vv]],[/ sun4\w[;\)]/i],[[ig,"sparc"]],[/\b(avr32|ia64(?=;)|68k(?=\))|\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\b|pa-risc)/i],[[ig,vv]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[sg,[rg,qg],[ng,ug]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[sg,[rg,qg],[ng,cg]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[sg,[rg,Rg],[ng,cg]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[sg,[rg,Rg],[ng,ug]],[/(macintosh);/i],[sg,[rg,Rg]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[sg,[rg,Wg],[ng,cg]],[/\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\)|;)/i],[sg,[rg,Dg],[ng,ug]],[/honor([-\w ]+)[;\)]/i],[sg,[rg,Dg],[ng,cg]],[/\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\w\. ]*(?= bui|\)))\b(?!.+d\/s)/i],[sg,[rg,jg],[ng,ug]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[sg,[rg,jg],[ng,cg]],[/oid[^\)]+; (2[\dbc]{4}(182|283|rp\w{2})[cgl]|m2105k81a?c)(?: bui|\))/i,/\b((?:red)?mi[-_ ]?pad[\w- ]*)(?: bui|\))/i],[[sg,/_/g," "],[rg,Kg],[ng,ug]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i,/ ([\w ]+) miui\/v?\d/i],[[sg,/_/g," "],[rg,Kg],[ng,cg]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[sg,[rg,Vg],[ng,cg]],[/\b(opd2(\d{3}a?))(?: bui|\))/i],[sg,[rg,Ev,{OnePlus:["304","403","203"],"*":Vg}],[ng,ug]],[/(vivo (5r?|6|8l?|go|one|s|x[il]?[2-4]?)[\w\+ ]*)(?: bui|\))/i],[sg,[rg,"BLU"],[ng,cg]],[/; vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[sg,[rg,"Vivo"],[ng,cg]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[sg,[rg,"Realme"],[ng,cg]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto(?! 360)[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[sg,[rg,Hg],[ng,cg]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[sg,[rg,Hg],[ng,ug]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[sg,[rg,$g],[ng,ug]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+(?!.*(?:browser|netcast|android tv|watch))(\w+)/i,/\blg-?([\d\w]+) bui/i],[sg,[rg,$g],[ng,cg]],[/(ideatab[-\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\d{3,4}(?:f[cu]|xu|[av])|yt\d?-[jx]?\d+[lfmx])( bui|;|\)|\/)/i,/lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\w- ]+?)|tb[\w-]{6,7})( bui|;|\)|\/)/i],[sg,[rg,Fg],[ng,ug]],[/(nokia) (t[12][01])/i],[rg,sg,[ng,ug]],[/(?:maemo|nokia).*(n900|lumia \d+|rm-\d+)/i,/nokia[-_ ]?(([-\w\. ]*))/i],[[sg,/_/g," "],[ng,cg],[rg,"Nokia"]],[/(pixel (c|tablet))\b/i],[sg,[rg,Lg],[ng,ug]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[sg,[rg,Lg],[ng,cg]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[sg,[rg,Gg],[ng,cg]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[sg,"Xperia Tablet"],[rg,Gg],[ng,ug]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[sg,[rg,Bg],[ng,cg]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[sg,[rg,Og],[ng,ug]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[sg,/(.+)/g,"Fire Phone $1"],[rg,Og],[ng,cg]],[/(playbook);[-\w\),; ]+(rim)/i],[sg,rg,[ng,ug]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[sg,[rg,zg],[ng,cg]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[sg,[rg,Mg],[ng,ug]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[sg,[rg,Mg],[ng,cg]],[/(nexus 9)/i],[sg,[rg,"HTC"],[ng,ug]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[rg,[sg,/_/g," "],[ng,cg]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[sg,[rg,"TCL"],[ng,ug]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[sg,[rg,"TCL"],[ng,cg]],[/(itel) ((\w+))/i],[[rg,vv],sg,[ng,Ev,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[sg,[rg,"Acer"],[ng,ug]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[sg,[rg,"Meizu"],[ng,cg]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[sg,[rg,"Ulefone"],[ng,cg]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[sg,[rg,"Energizer"],[ng,cg]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[sg,[rg,"Cat"],[ng,cg]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[sg,[rg,"Smartfren"],[ng,cg]],[/droid.+; (a(?:015|06[35]|142p?))/i],[sg,[rg,"Nothing"],[ng,cg]],[/; (x67 5g|tikeasy \w+|ac[1789]\d\w+)( b|\))/i,/archos ?(5|gamepad2?|([\w ]*[t1789]|hello) ?\d+[\w ]*)( b|\))/i],[sg,[rg,"Archos"],[ng,ug]],[/archos ([\w ]+)( b|\))/i,/; (ac[3-6]\d\w{2,8})( b|\))/i],[sg,[rg,"Archos"],[ng,cg]],[/(imo) (tab \w+)/i,/(infinix) (x1101b?)/i],[rg,sg,[ng,ug]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (blu|hmd|imo|tcl)[_ ]([\w\+ ]+?)(?: bui|\)|; r)/i,/(hp) ([\w ]+\w)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w ]+?)(?: bui|\)|\/)/i,/(oppo) ?([\w ]+) bui/i],[rg,sg,[ng,cg]],[/(kobo)\s(ereader|touch)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[rg,sg,[ng,ug]],[/(surface duo)/i],[sg,[rg,Zg],[ng,ug]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[sg,[rg,"Fairphone"],[ng,cg]],[/((?:tegranote|shield t(?!.+d tv))[\w- ]*?)(?: b|\))/i],[sg,[rg,Ug],[ng,ug]],[/(sprint) (\w+)/i],[rg,sg,[ng,cg]],[/(kin\.[onetw]{3})/i],[[sg,/\./g," "],[rg,Zg],[ng,cg]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[sg,[rg,Yg],[ng,ug]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[sg,[rg,Yg],[ng,cg]],[/smart-tv.+(samsung)/i],[rg,[ng,dg]],[/hbbtv.+maple;(\d+)/i],[[sg,/^/,"SmartTV"],[rg,qg],[ng,dg]],[/tcast.+(lg)e?. ([-\w]+)/i],[rg,sg,[ng,dg]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[rg,$g],[ng,dg]],[/(apple) ?tv/i],[rg,[sg,Rg+" TV"],[ng,dg]],[/crkey.*devicetype\/chromecast/i],[[sg,Qg+" Third Generation"],[rg,Lg],[ng,dg]],[/crkey.*devicetype\/([^/]*)/i],[[sg,/^/,"Chromecast "],[rg,Lg],[ng,dg]],[/fuchsia.*crkey/i],[[sg,Qg+" Nest Hub"],[rg,Lg],[ng,dg]],[/crkey/i],[[sg,Qg],[rg,Lg],[ng,dg]],[/(portaltv)/i],[sg,[rg,rv],[ng,dg]],[/droid.+aft(\w+)( bui|\))/i],[sg,[rg,Og],[ng,dg]],[/(shield \w+ tv)/i],[sg,[rg,Ug],[ng,dg]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[sg,[rg,Wg],[ng,dg]],[/(bravia[\w ]+)( bui|\))/i],[sg,[rg,Gg],[ng,dg]],[/(mi(tv|box)-?\w+) bui/i],[sg,[rg,Kg],[ng,dg]],[/Hbbtv.*(technisat) (.*);/i],[rg,sg,[ng,dg]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[rg,xv],[sg,xv],[ng,dg]],[/droid.+; ([\w- ]+) (?:android tv|smart[- ]?tv)/i],[sg,[ng,dg]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[ng,dg]],[/(ouya)/i,/(nintendo) (\w+)/i],[rg,sg,[ng,lg]],[/droid.+; (shield)( bui|\))/i],[sg,[rg,Ug],[ng,lg]],[/(playstation \w+)/i],[sg,[rg,Gg],[ng,lg]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[sg,[rg,Zg],[ng,lg]],[/\b(sm-[lr]\d\d[0156][fnuw]?s?|gear live)\b/i],[sg,[rg,qg],[ng,pg]],[/((pebble))app/i,/(asus|google|lg|oppo) ((pixel |zen)?watch[\w ]*)( bui|\))/i],[rg,sg,[ng,pg]],[/(ow(?:19|20)?we?[1-3]{1,3})/i],[sg,[rg,Vg],[ng,pg]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[sg,[rg,Rg],[ng,pg]],[/(opwwe\d{3})/i],[sg,[rg,Bg],[ng,pg]],[/(moto 360)/i],[sg,[rg,Hg],[ng,pg]],[/(smartwatch 3)/i],[sg,[rg,Gg],[ng,pg]],[/(g watch r)/i],[sg,[rg,$g],[ng,pg]],[/droid.+; (wt63?0{2,3})\)/i],[sg,[rg,Yg],[ng,pg]],[/droid.+; (glass) \d/i],[sg,[rg,Lg],[ng,fg]],[/(pico) (4|neo3(?: link|pro)?)/i],[rg,sg,[ng,fg]],[/(quest( \d| pro)?s?).+vr/i],[sg,[rg,rv],[ng,fg]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[rg,[ng,mg]],[/(aeobc)\b/i],[sg,[rg,Og],[ng,mg]],[/(homepod).+mac os/i],[sg,[rg,Rg],[ng,mg]],[/windows iot/i],[[ng,mg]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+?(mobile|vr|\d) safari/i],[sg,[ng,Ev,{mobile:"Mobile",xr:"VR","*":ug}]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[ng,ug]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[ng,cg]],[/droid .+?; ([\w\. -]+)( bui|\))/i],[sg,[rg,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[og,[tg,ev+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[tg,og],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[og,[tg,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[tg,og],[/ladybird\//i],[[tg,"LibWeb"]],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[og,tg]],os:[[/microsoft (windows) (vista|xp)/i],[tg,og],[/(windows (?:phone(?: os)?|mobile|iot))[\/ ]?([\d\.\w ]*)/i],[tg,[og,Ev,Cv]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[og,Ev,Cv],[tg,sv]],[/[adehimnop]{4,7}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[og,/_/g,"."],[tg,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[tg,"macOS"],[og,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[og,[tg,Qg+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[og,[tg,Qg+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[og,[tg,Qg+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[og,[tg,Qg+" Linux"]],[/crkey\/([\d\.]+)/i],[og,[tg,Qg]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[og,tg],[/(ubuntu) ([\w\.]+) like android/i],[[tg,/(.+)/,"$1 Touch"],og],[/(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen|webos)\w*[-\/\.; ]?([\d\.]*)/i],[tg,og],[/\(bb(10);/i],[og,[tg,zg]],[/(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\/ ]?([\w\.]*)/i],[og,[tg,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[og,[tg,tv+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[og,[tg,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[og,[tg,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[tg,"Chrome OS"],og],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux)(?: arm\w*| x86\w*| ?)([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[tg,og],[/(sunos) ?([\w\.\d]*)/i],[[tg,"Solaris"],og],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[tg,og]]},Nv=function(){var e={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}};return bv.call(e.init,[[Kh,[tg,og,ag,ng]],[Yh,[ig]],[Xh,[ng,sg,rg]],[Jh,[tg,og]],[Qh,[tg,og]]]),bv.call(e.isIgnore,[[Kh,[og,ag]],[Jh,[og]],[Qh,[og]]]),bv.call(e.isIgnoreRgx,[[Kh,/ ?browser$/i],[Qh,/ ?os$/i]]),bv.call(e.toString,[[Kh,[tg,og]],[Yh,[ig]],[Xh,[rg,sg]],[Jh,[tg,og]],[Qh,[tg,og]]]),e}(),Pv=function(e,t){var n=Nv.init[t],r=Nv.isIgnore[t]||0,o=Nv.isIgnoreRgx[t]||0,i=Nv.toString[t]||0;function a(){bv.call(this,n)}return a.prototype.getItem=function(){return e},a.prototype.withClientHints=function(){return uv?uv.getHighEntropyValues(Ig).then((function(t){return e.setCH(new Av(t,!1)).parseCH().get()})):e.parseCH().get()},a.prototype.withFeatureCheck=function(){return e.detectFeature().get()},t!=eg&&(a.prototype.is=function(e){var t=!1;for(var n in this)if(this.hasOwnProperty(n)&&!fv(r,n)&&vv(o?yv(o,this[n]):this[n])==vv(o?yv(o,e):e)){if(t=!0,e!=qh)break}else if(e==qh&&t){t=!t;break}return t},a.prototype.toString=function(){var e=Uh;for(var t in i)typeof this[i[t]]!==qh&&(e+=(e?" ":Uh)+this[i[t]]);return e||qh}),uv||(a.prototype.then=function(e){var t=this,n=function(){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e])};n.prototype={is:a.prototype.is,toString:a.prototype.toString};var r=new n;return e(r),r}),new a};function Av(e,t){if(e=e||{},bv.call(this,Ig),t)bv.call(this,[[gg,gv(e[xg])],[wg,gv(e[kg])],[cg,/\?1/.test(e[Tg])],[sg,_v(e[Ng])],[bg,_v(e[Pg])],[yg,_v(e[Ag])],[ig,_v(e[Eg])],[vg,gv(e[Sg])],[_g,_v(e[Cg])]]);else for(var n in e)this.hasOwnProperty(n)&&typeof e[n]!==qh&&(this[n]=e[n])}function Iv(e,t,n,r){return this.get=function(e){return e?this.data.hasOwnProperty(e)?this.data[e]:void 0:this.data},this.set=function(e,t){return this.data[e]=t,this},this.setCH=function(e){return this.uaCH=e,this},this.detectFeature=function(){if(cv&&cv.userAgent==this.ua)switch(this.itemType){case Kh:cv.brave&&typeof cv.brave.isBrave==Vh&&this.set(tg,"Brave");break;case Xh:!this.get(ng)&&uv&&uv[cg]&&this.set(ng,cg),"Macintosh"==this.get(sg)&&cv&&typeof cv.standalone!==qh&&cv.maxTouchPoints&&cv.maxTouchPoints>2&&this.set(sg,"iPad").set(ng,ug);break;case Qh:!this.get(tg)&&uv&&uv[bg]&&this.set(tg,uv[bg]);break;case eg:var e=this.data,t=function(t){return e[t].getItem().detectFeature().get()};this.set(Kh,t(Kh)).set(Yh,t(Yh)).set(Xh,t(Xh)).set(Jh,t(Jh)).set(Qh,t(Qh))}return this},this.parseUA=function(){return this.itemType!=eg&&kv.call(this.data,this.ua,this.rgxMap),this.itemType==Kh&&this.set(ag,wv(this.get(og))),this},this.parseCH=function(){var e=this.uaCH,t=this.rgxMap;switch(this.itemType){case Kh:case Jh:var n,r=e[wg]||e[gg];if(r)for(var o in r){var i=r[o].brand||r[o],a=r[o].version;this.itemType!=Kh||/not.a.brand/i.test(i)||n&&(!/chrom/i.test(n)||i==Jg)||(i=Ev(i,{Chrome:"Google Chrome",Edge:"Microsoft Edge","Chrome WebView":"Android WebView","Chrome Headless":"HeadlessChrome","Huawei Browser":"HuaweiBrowser","MIUI Browser":"Miui Browser","Opera Mobi":"OperaMobile",Yandex:"YaBrowser"}),this.set(tg,i).set(og,a).set(ag,wv(a)),n=i),this.itemType==Jh&&i==Jg&&this.set(og,a)}break;case Yh:var s=e[ig];s&&(s&&"64"==e[_g]&&(s+="64"),kv.call(this.data,s+";",t));break;case Xh:if(e[cg]&&this.set(ng,cg),e[sg]&&(this.set(sg,e[sg]),!this.get(ng)||!this.get(rg))){var l={};kv.call(l,"droid 9; "+e[sg]+")",t),!this.get(ng)&&l.type&&this.set(ng,l.type),!this.get(rg)&&l.vendor&&this.set(rg,l.vendor)}if(e[vg]){var c;if("string"!==typeof e[vg]){var u=0;while(!c&&u<e[vg].length)c=Ev(e[vg][u++],Sv)}else c=Ev(e[vg],Sv);this.set(ng,c)}break;case Qh:var d=e[bg];if(d){var p=e[yg];d==sv&&(p=parseInt(wv(p),10)>=13?"11":"10"),this.set(tg,d).set(og,p)}this.get(tg)==sv&&"Xbox"==e[sg]&&this.set(tg,"Xbox").set(og,void 0);break;case eg:var f=this.data,m=function(t){return f[t].getItem().setCH(e).parseCH().get()};this.set(Kh,m(Kh)).set(Yh,m(Yh)).set(Xh,m(Xh)).set(Jh,m(Jh)).set(Qh,m(Qh))}return this},bv.call(this,[["itemType",e],["ua",t],["uaCH",r],["rgxMap",n],["data",Pv(this,e)]]),this}function Ov(e,t,n){if(typeof e===Wh?(mv(e,!0)?(typeof t===Wh&&(n=t),t=e):(n=e,t=void 0),e=void 0):typeof e!==Gh||mv(t,!0)||(n=t,t=void 0),n&&typeof n.append===Vh){var r={};n.forEach((function(e,t){r[t]=e})),n=r}if(!(this instanceof Ov))return new Ov(e,t,n).getResult();var o=typeof e===Gh?e:n&&n[Hh]?n[Hh]:cv&&cv.userAgent?cv.userAgent:Uh,i=new Av(n,!0),a=t?dv(Tv,t):Tv,s=function(e){return e==eg?function(){return new Iv(e,o,a,i).set("ua",o).set(Kh,this.getBrowser()).set(Yh,this.getCPU()).set(Xh,this.getDevice()).set(Jh,this.getEngine()).set(Qh,this.getOS()).get()}:function(){return new Iv(e,o,a[e],i).parseUA().get()}};return bv.call(this,[["getBrowser",s(Kh)],["getCPU",s(Yh)],["getDevice",s(Xh)],["getEngine",s(Jh)],["getOS",s(Qh)],["getResult",s(eg)],["getUA",function(){return o}],["setUA",function(e){return hv(e)&&(o=e.length>Zh?xv(e,Zh):e),this}]]).setUA(o),this}Ov.VERSION=$h,Ov.BROWSER=pv([tg,og,ag,ng]),Ov.CPU=pv([ig]),Ov.DEVICE=pv([sg,rg,ng,lg,cg,dg,ug,pg,mg]),Ov.ENGINE=Ov.OS=pv([tg,og]);const Rv=()=>{const e=be((()=>{{const e=new Ov,t=e.getResult();return{browser:t.browser,engine:t.engine,os:t.os,device:t.device,cpu:t.cpu}}}),[]);return e};function Mv(e){const t=Rv();return t.os.name.toLowerCase().includes("mac")?Bs[e].keyComboMac:Bs[e].keyComboDefault}function zv(){const e=ma(),t=be((()=>e.chats.find((t=>t.id===e.currentChatId))),[e.chats,e.currentChatId]),n=be((()=>(null==t?void 0:t.inputValue)||""),[null==t?void 0:t.inputValue]),r=be((()=>n.split("\n").length>1||n.length>30),[n]),o=ye((t=>{e.setChatInput(e.currentChatId,t)}),[e.setChatInput,e.currentChatId]),i=ye((()=>{t&&n.trim()&&e.addMessage(t.id,n)}),[t,n,e.addMessage]),a=ye((e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),i())}),[i]),s=ve(null);he((()=>{var t,n,r;const o=()=>{var e;return null==(e=s.current)?void 0:e.focus()};return e.isPromptCreationActive?(null==(t=s.current)||t.focus(),null==(n=s.current)||n.addEventListener("blur",o)):null==(r=s.current)||r.blur(),()=>{var e;null==(e=s.current)||e.removeEventListener("blur",o)}}),[e.isPromptCreationActive]);const l=be((()=>qs("flex size-6 items-center justify-center rounded-full bg-transparent p-1 text-zinc-950 opacity-20",n.length>0&&"bg-blue-600 text-white opacity-100")),[n.length]),c=be((()=>qs("w-full flex-1 resize-none bg-transparent text-zinc-950 placeholder:text-zinc-950/50 focus:outline-none",r?"h-[4.5em]":"h-6")),[r]),u=Mv(Us.CTRL_ALT_C);return J("div",{className:qs("flex h-fit w-80 flex-1 flex-row items-end gap-1 rounded-2xl border border-border/10 bg-zinc-950/5 p-1.5 pl-2 text-sm text-zinc-950 shadow-inner transition-all duration-150 placeholder:text-zinc-950/70",e.isPromptCreationActive&&"ring-2 ring-blue-600"),onClick:()=>e.startPromptCreation(),role:"button",tabIndex:0,children:[J(ph,{ref:s,className:c,rows:r?4:1,value:n,onChange:e=>o(e.currentTarget.value),onKeyDown:a,placeholder:e.isPromptCreationActive?"Enter prompt...":`What do you want to change? (${u})`}),J(Pc,{className:l,disabled:0===n.length,onClick:i,children:J(Oh,{className:"size-3"})})]})}const Lv=ch,jv=We(((e,t)=>J(rh,{as:C,ref:t,...e}))),Fv=We(((e,t)=>J(oh,{ref:t,anchor:"bottom",transition:!0,portal:!0,...e,className:qs("z-50 flex w-fit min-w-24 max-w-90 flex-col items-stretch justify-start gap-1 rounded-lg border border-border/30 border-solid bg-background/60 p-1 shadow-black/50 shadow-lg outline-none backdrop-blur-md data-focus:outline-none",e.className)}))),Dv="w-full flex flex-row select-none items-center justify-start gap-2 p-2 pr-6 truncate overflow-hidden rounded-md hover:bg-zinc-950/10 focus:text-zinc-900 cursor-pointer transition-color duration-150 text-sm font-normal text-foreground",$v=We(((e,t)=>J(ih,{ref:t,children:J(Pc,{...e,className:qs(Dv,e.className)})})));function Zv(e){return J("div",{className:"flex h-full shrink-0 items-center justify-center",children:[e.children,e.badgeContent&&J("div",{className:qs("bg-blue-600 text-white",e.badgeClassName,"-bottom-0.5 -right-1 pointer-events-none absolute flex h-4 w-max min-w-4 max-w-8 select-none items-center justify-center truncate rounded-full px-1 font-semibold text-xs"),children:e.badgeContent}),e.statusDot&&J("div",{className:qs("bg-rose-600",e.statusDotClassName,"pointer-events-none absolute top-0 right-0 size-1.5 rounded-full")})]})}We(((e,t)=>J(ih,{ref:t,children:J("a",{...e,className:qs(Dv,e.className)})})));const Hv=We((({badgeContent:e,badgeClassName:t,statusDot:n,statusDotClassName:r,tooltipHint:o,variant:i="default",...a},s)=>{const l=J(Pc,{ref:s,...a,className:qs("flex items-center justify-center rounded-full p-1 text-zinc-950 hover:bg-zinc-950/5","default"===i?"size-8":"h-8 rounded-full")});return J(Zv,{badgeContent:e,badgeClassName:t,statusDot:n,statusDotClassName:r,children:l})}));function Uv({children:e}){return J("div",{className:"fade-in slide-in-from-bottom-2 flex max-h-full max-w-sm animate-in snap-start flex-row items-center justify-between gap-2 border-x border-r-border/30 border-l-transparent px-3 first:pl-0 last:border-r-transparent last:pr-0",children:e})}function Bv(){const e=da((e=>e.minimize)),t=Me(),n=be((()=>t.flatMap((e=>e.actions))),[t]);return J(Uv,{children:J(Lv,{children:[J(jv,{children:J(Hv,{children:J(Sh,{className:"size-4"})})}),J(Fv,{children:[n.map((e=>J($v,{onClick:e.execute,children:e.name}))),J($v,{onClick:e,children:[J(Nh,{className:"size-4"}),"Minimize companion"]})]})]})})}Hv.displayName="ToolbarButton";const Vv=K(null),qv=({containerRef:e,children:t,snapAreas:n,onDragStart:r,onDragEnd:o})=>{const[i,a]=fe({top:0,left:0,right:0,bottom:0}),s=ve(new Set),l=ve(new Set),c=ye((e=>(s.current.add(e),()=>s.current.delete(e))),[]),u=ye((e=>(l.current.add(e),()=>l.current.delete(e))),[]),d=ye((()=>{r&&r(),s.current.forEach((e=>e()))}),[r]),p=ye((()=>{o&&o(),l.current.forEach((e=>e()))}),[o]),f={borderLocation:i,snapAreas:n,registerDragStart:c,registerDragEnd:u,emitDragStart:d,emitDragEnd:p};return he((()=>{if(!e.current)return;const t=()=>{const t=e.current.getBoundingClientRect();a({top:t.top,left:t.left,right:t.right,bottom:t.bottom})};t();const n=new ResizeObserver(t);n.observe(e.current);const r=()=>{requestAnimationFrame(t)};window.addEventListener("scroll",r,!0);let o=e.current.parentElement;while(o)o.addEventListener("scroll",r),o=o.parentElement;return()=>{var t;n.disconnect(),window.removeEventListener("scroll",r,!0),o=null==(t=e.current)?void 0:t.parentElement;while(o)o.removeEventListener("scroll",r),o=o.parentElement}}),[e]),J(Vv.Provider,{value:f,children:t})};function Wv(e){const t=_e(Vv),n=ve(t);he((()=>{n.current=t}),[t]);const r=ve(null),o=ve(null),[i,a]=fe(null),[s,l]=fe(null),c=ve(null),u=ve(null),d=ve(null),p=ve(!1),f=ve(e.initialRelativeCenter),[m,h]=fe(null),{startThreshold:g=3,areaSnapThreshold:v=60,onDragStart:w,onDragEnd:b,initialSnapArea:y,springStiffness:_=.3,springDampness:x=.5}=e,k=ve(null),E=ve({x:0,y:0}),C=ve(!1);function S(e){const{top:t,left:n,right:r,bottom:o}=e,i=r-n,a=o-t;return{topLeft:{x:n,y:t},topCenter:{x:n+i/2,y:t},topRight:{x:r,y:t},centerLeft:{x:n,y:t+a/2},center:{x:n+i/2,y:t+a/2},centerRight:{x:r,y:t+a/2},bottomLeft:{x:n,y:o},bottomCenter:{x:n+i/2,y:o},bottomRight:{x:r,y:o}}}he((()=>{if(y&&t&&t.borderLocation&&t.snapAreas&&t.snapAreas[y]&&!p.current){const{top:e,left:n,right:r,bottom:o}=t.borderLocation,i=r-n,a=o-e,s={topLeft:{x:n,y:e},topCenter:{x:n+i/2,y:e},topRight:{x:r,y:e},centerLeft:{x:n,y:e+a/2},center:{x:n+i/2,y:e+a/2},centerRight:{x:r,y:e+a/2},bottomLeft:{x:n,y:o},bottomCenter:{x:n+i/2,y:o},bottomRight:{x:r,y:o}},l=s[y];if(l&&window.innerWidth>0&&window.innerHeight>0){const t=(l.x-n)/(r-n),i=(l.y-e)/(o-e);f.current={x:t,y:i}}}}),[y,t]);const T=ye((()=>{var e,t;const o=r.current;if(!o)return;const i=o.offsetWidth,a=o.offsetHeight,s=o.offsetParent;let l=0,u=0,m=window.innerWidth,g=window.innerHeight;if(s){const e=s.getBoundingClientRect();l=e.left,u=e.top,m=s.offsetWidth||window.innerWidth,g=s.offsetHeight||window.innerHeight}let w=null,b=null;const y=f.current;let N=null,P=null;const A=n.current;if(p.current&&c.current&&d.current&&A&&A.borderLocation&&A.snapAreas){const e={x:d.current.x-c.current.x,y:d.current.y-c.current.y},t=S(A.borderLocation);let n=Number.POSITIVE_INFINITY,r=null,o=null;for(const i in A.snapAreas)if(A.snapAreas[i]){const a=t[i];if(!a)continue;const s=Math.hypot(a.x-e.x,a.y-e.y);s<n&&(n=s,r=i,o=a)}r&&o&&n<=v&&(N=r,P=o)}if(p.current&&P)w=P.x,b=P.y,h(N);else if(p.current&&c.current&&d.current)w=d.current.x-c.current.x,b=d.current.y-c.current.y,h(null);else{if(!(y&&m>0&&g>0))return void(!(null==(e=r.current)?void 0:e.style.left)&&(null==(t=r.current)||t.style.top));{const e=y.y<=.5,t=y.x<=.5;if(t){const e=m*y.x;w=l+e}else{const e=m*(1-y.x);w=l+m-e}if(e){const e=g*y.y;b=u+e}else{const e=g*(1-y.y);b=u+g-e}}h(null)}if(null===w||null===b)return;const{borderLocation:I}=n.current||{borderLocation:void 0};if(I&&i>0&&a>0){const e=I.right-I.left,t=I.bottom-I.top;let n=w,r=b;if(i>=e)n=I.left+e/2;else{const e=I.left+i/2,t=I.right-i/2;n=Math.max(e,Math.min(n,t))}if(a>=t)r=I.top+t/2;else{const e=I.top+a/2,t=I.bottom-a/2;r=Math.max(e,Math.min(r,t))}w=n,b=r}if(!k.current){k.current={x:w,y:b},E.current={x:0,y:0};const e=w-i/2,t=b-a/2,n=!y||y.y<=.5,r=!y||y.x<=.5,s=o.style;if(s.right="",s.bottom="",s.left="",s.top="",r){const t=e-l;s.left=m>0?`${(t/m*100).toFixed(2)}%`:"0px",s.right=""}else{const t=l+m-(e+i);s.right=m>0?`${(t/m*100).toFixed(2)}%`:"0px",s.left=""}if(n){const e=t-u;s.top=g>0?`${(e/g*100).toFixed(2)}%`:"0px",s.bottom=""}else{const e=u+g-(t+a);s.bottom=g>0?`${(e/g*100).toFixed(2)}%`:"0px",s.top=""}return void(C.current=!0)}if(!C.current)return void(C.current=!0);const O=k.current,R=E.current,M=w-O.x,z=b-O.y,L=_*M-x*R.x,j=_*z-x*R.y;R.x+=L,R.y+=j,O.x+=R.x,O.y+=R.y;const F=.5;Math.abs(M)<F&&Math.abs(z)<F&&Math.abs(R.x)<F&&Math.abs(R.y)<F&&(O.x=w,O.y=b,R.x=0,R.y=0),k.current={...O},E.current={...R};const D=O.x-i/2,$=O.y-a/2,Z=!y||y.y<=.5,H=!y||y.x<=.5,U=o.style;if(U.right="",U.bottom="",U.left="",U.top="",H){const e=D-l;U.left=m>0?`${(e/m*100).toFixed(2)}%`:"0px",U.right=""}else{const e=l+m-(D+i);U.right=m>0?`${(e/m*100).toFixed(2)}%`:"0px",U.left=""}if(Z){const e=$-u;U.top=g>0?`${(e/g*100).toFixed(2)}%`:"0px",U.bottom=""}else{const e=u+g-($+a);U.bottom=g>0?`${(e/g*100).toFixed(2)}%`:"0px",U.top=""}(Math.abs(O.x-w)>F||Math.abs(O.y-b)>F||Math.abs(R.x)>F||Math.abs(R.y)>F||p.current)&&requestAnimationFrame(T)}),[v,_,x]),N=ye((e=>{var t;if(p.current){b&&b(),(null==(t=n.current)?void 0:t.emitDragEnd)&&n.current.emitDragEnd();const e=r.current,o=n.current;if(e&&o&&o.borderLocation){const t=e.offsetWidth,n=e.offsetHeight,r=e.offsetParent;let i=0,a=0,s=window.innerWidth,l=window.innerHeight;if(r){const e=r.getBoundingClientRect();i=e.left,a=e.top,s=r.offsetWidth||window.innerWidth,l=r.offsetHeight||window.innerHeight}let u=0,p=0;d.current&&c.current?(u=d.current.x-c.current.x,p=d.current.y-c.current.y):k.current&&(u=k.current.x,p=k.current.y);const m=o.borderLocation,g=m.left+t/2,w=m.right-t/2,b=m.top+n/2,y=m.bottom-n/2;u=Math.max(g,Math.min(u,w)),p=Math.max(b,Math.min(p,y));const _=S(m);let x=Number.POSITIVE_INFINITY,E=null,C=null;for(const e in o.snapAreas)if(o.snapAreas[e]){const t=_[e];if(!t)continue;const n=Math.hypot(t.x-u,t.y-p);n<x&&(x=n,E=e,C=t)}if(E&&C&&x<=v){h(E);const e=(C.x-i)/s,t=(C.y-a)/l;f.current={x:e,y:t}}else{h(null);const e=(u-i)/s,t=(p-a)/l;f.current={x:e,y:t}}}}u.current=null,p.current=!1,window.removeEventListener("mousemove",P,{capture:!0}),window.removeEventListener("mouseup",N,{capture:!0}),r.current&&(r.current.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor=""}),[b,v]),P=ye((e=>{var t;if(!u.current)return;const o=Math.hypot(e.clientX-u.current.x,e.clientY-u.current.y);o>g&&!p.current&&(p.current=!0,r.current&&(r.current.style.userSelect="none"),document.body.style.userSelect="none",document.body.style.cursor="grabbing",w&&w(),(null==(t=n.current)?void 0:t.emitDragStart)&&n.current.emitDragStart(),requestAnimationFrame(T)),d.current={x:e.clientX,y:e.clientY}}),[g,w,T]),A=ye((e=>{if(0!==e.button)return;const t=o.current,n=r.current;if(t){if(!t.contains(e.target)&&e.target!==t)return}else{if(!n)return;if(!n.contains(e.target)&&e.target!==n)return}if(u.current={x:e.clientX,y:e.clientY},!r.current)return;const i=r.current.getBoundingClientRect(),a=i.left+i.width/2,s=i.top+i.height/2;c.current={x:e.clientX-a,y:e.clientY-s},window.addEventListener("mousemove",P,{capture:!0}),window.addEventListener("mouseup",N,{capture:!0})}),[P,N]);he((()=>{const e=s||i;return e&&e.addEventListener("mousedown",A),()=>{e&&e.removeEventListener("mousedown",A),p.current&&(b&&b(),p.current=!1,i&&(i.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor="",window.removeEventListener("mousemove",P,{capture:!0}),window.removeEventListener("mouseup",N,{capture:!0}))}}),[i,s,A,b,P,N]),he((()=>{i&&f.current&&!p.current&&requestAnimationFrame(T)}),[i,t,f,T]);const I=ye((e=>{a(e),r.current=e}),[]),O=ye((e=>{l(e),o.current=e}),[]);return{draggableRef:I,handleRef:O,position:{snapArea:m,isTopHalf:!f.current||f.current.y<=.5,isLeftHalf:!f.current||f.current.x<=.5}}}function Gv(){const e=_e(Vv),t=null==e?void 0:e.borderLocation,n=!!t&&t.right-t.left>0&&t.bottom-t.top>0,r=Wv({startThreshold:10,initialSnapArea:"bottomCenter"});return n?J("div",{ref:r.draggableRef,className:"pointer-events-auto absolute p-0.5",children:J("div",{className:"pointer-events-auto flex w-96 max-w-[80vw] flex-col items-start justify-center rounded-3xl border border-border/30 border-solid bg-zinc-50/80 p-0 shadow-lg backdrop-blur-lg transition-colors",children:[J(zh,{}),J("div",{ref:r.handleRef,className:"flex w-full flex-row items-center justify-center rounded-3xl border-border/30 border-t bg-background/40 p-1.5 shadow-lg transition-colors first:border-none",children:[J(zv,{}),J(Bv,{})]})]})}):null}function Kv(){const e=ve(null);return J("div",{className:"absolute size-full",children:J("div",{className:"absolute inset-4",ref:e,children:J(qv,{containerRef:e,snapAreas:{topLeft:!0,topCenter:!0,topRight:!0,centerLeft:!0,center:!0,centerRight:!0,bottomLeft:!0,bottomCenter:!0,bottomRight:!0},children:J(Gv,{})})})})}const Yv=({color:e="default",loading:t=!1,loadingSpeed:n="slow",...r})=>{const o={default:"fill-stagewise-700 stroke-none",black:"fill-zinc-950 stroke-none",white:"fill-white stroke-none",gray:"fill-zinc-500/50 stroke-none",current:"fill-current stroke-none",gradient:"fill-white stroke-black/30 stroke-1"};return J("div",{className:`relative ${"gradient"===e?"overflow-hidden rounded-full":"overflow-visible"} ${r.className||""} ${t?"drop-shadow-xl":""} aspect-square`,children:["gradient"===e&&J("div",{className:"absolute inset-0",children:[J("div",{className:"absolute inset-0 size-full bg-gradient-to-tr from-indigo-700 via-blue-500 to-teal-500"}),J("div",{className:"absolute top-1/2 left-1/2 size-9/12 bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),J("div",{className:"absolute right-1/2 bottom-1/2 size-full bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),J("div",{className:"absolute top-0 left-[-10%] size-[120%] bg-[radial-gradient(circle,rgba(255,255,255,0)_60%,rgba(255,255,255,0.2)_70%)]"}),J("div",{className:"absolute top-[-20%] left-0 h-[120%] w-full bg-[radial-gradient(circle,rgba(55,48,163,0)_55%,rgba(55,48,163,0.35)_73%)]"})]}),J("svg",{className:"absolute overflow-visible "+("gradient"===e?"top-[25%] left-[25%] h-[50%] w-[50%] drop-shadow-indigo-950 drop-shadow-xs":"top-0 left-0 h-full w-full"),viewBox:"0 0 2048 2048",children:[J("title",{children:"stagewise"}),J("ellipse",{className:o[e]+(t?" animate-pulse":""),id:"path3",ry:"624",rx:"624",cy:"1024",cx:"1024"})]}),J("svg",{className:"absolute overflow-visible "+("gradient"===e?"top-[25%] left-[25%] h-[50%] w-[50%]":"top-0 left-0 h-full w-full"),viewBox:"0 0 2048 2048",children:J("path",{id:"path4",className:`origin-center ${o[e]}${t?"fast"===n?" animate-spin-fast":" animate-spin-slow":""}`,d:"M 1024 0 A 1024 1024 0 0 0 0 1024 A 1024 1024 0 0 0 1024 2048 L 1736 2048 L 1848 2048 C 1958.7998 2048 2048 1958.7998 2048 1848 L 2048 1736 L 2048 1024 A 1024 1024 0 0 0 1024 0 z M 1024.9414 200 A 824 824 0 0 1 1848.9414 1024 A 824 824 0 0 1 1024.9414 1848 A 824 824 0 0 1 200.94141 1024 A 824 824 0 0 1 1024.9414 200 z "})})]})};function Xv(){const e=da((e=>e.expand));return J("button",{type:"button",onClick:()=>e(),className:"pointer-events-auto absolute bottom-3 left-3 size-12 rounded-full bg-transparent opacity-80 shadow-sm transition-all duration-500 hover:opacity-100 hover:shadow-lg",children:J(Yv,{color:"gradient"})})}function Jv(e){const t=ve(null),n=ye((n=>{const r=n.target;if(r.closest(".companion"))return;const o=Zs(n.clientX,n.clientY);e.ignoreList.includes(o)||t.current!==o&&(t.current=o,e.onElementHovered(o))}),[e]),r=ye((()=>{t.current=null,e.onElementUnhovered()}),[e]),o=ye((()=>{t.current&&(e.ignoreList.includes(t.current)||e.onElementSelected(t.current))}),[e]);return J("div",{className:"pointer-events-auto fixed inset-0 h-screen w-screen",onMouseMove:n,onMouseLeave:r,onClick:o,role:"button",tabIndex:0})}function Qv(){const[e,t]=fe({width:window.innerWidth,height:window.innerHeight}),n=ye((()=>t({width:window.innerWidth,height:window.innerHeight})),[]);return ba("resize",n),e}function ew({refElement:e,...t}){const n=ve(null),r=Qv(),o=ye((()=>{if(n.current)if(e){const t=e.getBoundingClientRect();n.current.style.top=`${t.top}px`,n.current.style.left=`${t.left}px`,n.current.style.width=`${t.width}px`,n.current.style.height=`${t.height}px`,n.current.style.display=void 0}else n.current.style.height="0px",n.current.style.width="0px",n.current.style.top=r.height/2+"px",n.current.style.left=r.width/2+"px",n.current.style.display="none"}),[e,r.height,r.width]);return ha(o,30),J("div",{...t,className:"fixed flex items-center justify-center overflow-hidden rounded-lg border-2 border-blue-600/80 bg-blue-600/20 text-white backdrop-blur-xs transition-all duration-100",ref:n,children:J(Ah,{className:"size-6 drop-shadow-black drop-shadow-md"})})}function tw({refElement:e,...t}){const n=ve(null),r=Qv(),o=ye((()=>{if(n.current)if(e){const t=e.getBoundingClientRect();n.current.style.top=`${t.top}px`,n.current.style.left=`${t.left}px`,n.current.style.width=`${t.width}px`,n.current.style.height=`${t.height}px`,n.current.style.display=void 0}else n.current.style.height="0px",n.current.style.width="0px",n.current.style.top=r.height/2+"px",n.current.style.left=r.width/2+"px",n.current.style.display="none"}),[e,r.height,r.width]);ha(o,30);const i=ma(),a=ye((()=>{i.removeChatDomContext(i.currentChatId,e)}),[i,e]);return J("div",{...t,className:"pointer-events-auto fixed flex cursor-pointer items-center justify-center overflow-hidden rounded-lg border-2 border-green-600/80 bg-green-600/5 text-transparent transition-all duration-0 hover:border-red-600/80 hover:bg-red-600/20 hover:text-white hover:backdrop-blur-sm",ref:n,onClick:a,role:"button",tabIndex:0,children:J(Mh,{className:"size-6 drop-shadow-black drop-shadow-md"})})}function nw(){const{chats:e,currentChatId:t,addChatDomContext:n,isPromptCreationActive:r}=ma(),o=be((()=>e.find((e=>e.id===t))),[t,e]),i=r,a=be((()=>(null==o?void 0:o.domContextElements)||[]),[o]),[s,l]=fe(null),c=ye((e=>{n(t,e)}),[n,t]);return i?J(C,{children:[s&&J(ew,{refElement:s}),J(Jv,{ignoreList:a,onElementHovered:l,onElementSelected:c,onElementUnhovered:()=>l(null)}),a.map((e=>J(tw,{refElement:e})))]}):null}function rw(){const e=da((e=>e.minimized));return J("div",{className:qs("fixed inset-0 h-screen w-screen"),children:[J(nw,{}),!e&&J(Kv,{}),e&&J(Xv,{})]})}function ow(e){return J("div",{className:qs("undefined"===typeof e.enable||e.enable?"pointer-events-auto":"pointer-events-none",e.className),onClick:e.onClick,role:"button",tabIndex:0})}function iw(){const e=ve(!1);return he((()=>{const t=HTMLElement.prototype.focus;return HTMLElement.prototype.focus=function(...n){const r=this.getRootNode(),o=r instanceof ShadowRoot&&r.host instanceof HTMLElement&&"STAGEWISE-COMPANION-ANCHOR"===r.host.nodeName;!o&&e.current||t.apply(this,n)},()=>{HTMLElement.prototype.focus=t}}),[]),ba("focusin",(t=>{t.target.localName===$s&&(e.current=!0)}),{capture:!0}),ba("focusout",(t=>{t.target.localName===$s&&(e.current=!1)}),{capture:!0}),null}function aw({children:e}){return e}function sw(e){const t=da((e=>e.isMainAppBlocked));return J(C,{children:[J(iw,{}),J(ow,{className:"fixed inset-0 h-screen w-screen",enable:t}),J(wa,{config:e,children:[J(Ws,{}),J(aw,{children:J(rw,{})})]})]})}function lw(e){if(!document.body)throw new Error("stagewise companion cannot find document.body");if(document.body.querySelector($s))throw new Error("A stagewise companion anchor already exists.");const t=document.createElement($s);t.style.position="fixed",t.style.top="0px",t.style.left="0px",t.style.right="0px",t.style.bottom="0px",t.style.pointerEvents="none",t.style.zIndex="2147483647";const n=e=>{e.stopPropagation()};t.onclick=n,t.onmousedown=n,t.onmouseup=n,t.onmousemove=n,t.ondblclick=n,t.oncontextmenu=n,t.onwheel=n,t.onfocus=n,t.onblur=n,document.body.appendChild(t);const r=document.createElement("link");r.rel="stylesheet",r.href="https://rsms.me/inter/inter.css",document.head.appendChild(r);const o=document.createElement("style");o.append(document.createTextNode(Y)),document.head.appendChild(o),q(x(sw,e),t)}const cw=(0,h.pM)({__name:"StagewiseToolbar",props:{config:{}},setup(e){const t=e;return(0,h.sV)((()=>{lw(t.config)})),(0,h.wB)((()=>t.config),(e=>{lw(e)}),{deep:!0}),(e,t)=>null}})}}]);