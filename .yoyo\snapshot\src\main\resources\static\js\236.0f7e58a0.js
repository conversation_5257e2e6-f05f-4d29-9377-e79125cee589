"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[236],{35236:(e,t,a)=>{a.r(t),a.d(t,{default:()=>ie});a(28706),a(44114),a(62010);var s=a(20641),n=a(90033),r=a(53751),i={class:"container"},l={class:"row"},c={class:"col-md-3"},o={class:"list-group mb-4"},u=["onClick"],d={class:"col-md-9"},g={key:0,class:"card"},p={class:"card-header d-flex justify-content-between align-items-center"},m={class:"input-group",style:{"max-width":"300px"}},k={class:"card-body"},v={key:0,class:"text-center my-5"},h={key:1,class:"alert alert-danger"},b={key:2},f={class:"table table-striped table-hover"},L={class:"btn-group btn-group-sm"},C=["onClick","title"],y=["onClick"],E={key:0,"aria-label":"用户分页"},w={class:"pagination justify-content-center"},P=["onClick"],A={key:1,class:"card"},x={class:"card-header d-flex justify-content-between align-items-center"},U={class:"input-group",style:{"max-width":"300px"}},I={class:"card-body"},S={class:"mb-3"},D={key:0,class:"text-center my-5"},R={key:1,class:"alert alert-danger"},T={key:2},_={class:"table-responsive"},X={class:"table table-striped table-hover"},N=["src"],V={class:"btn-group btn-group-sm"},$=["onClick"],Q=["onClick"],F={key:0,"aria-label":"图像分页"},O={class:"pagination justify-content-center"},z=["onClick"],W={key:2,class:"card"},q={class:"card-body"},J={class:"mb-3"},M={class:"mb-3"},B={class:"form-check form-switch mb-3"},K={class:"form-check form-switch mb-3"},j=["disabled"],H={key:0},G={key:1};function Y(e,t,a,Y,Z,ee){return(0,s.uX)(),(0,s.CE)("div",i,[t[33]||(t[33]=(0,s.Lk)("h2",{class:"mt-4 mb-4"},"管理员控制台",-1)),(0,s.Lk)("div",l,[(0,s.Lk)("div",c,[(0,s.Lk)("div",o,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(Z.tabs,(function(e,t){return(0,s.uX)(),(0,s.CE)("button",{key:t,onClick:function(t){return Z.activeTab=e.id},class:(0,n.C4)(["list-group-item list-group-item-action",Z.activeTab===e.id?"active":""])},[(0,s.Lk)("i",{class:(0,n.C4)(e.icon)},null,2),(0,s.eW)(" "+(0,n.v_)(e.name),1)],10,u)})),128))])]),(0,s.Lk)("div",d,["users"===Z.activeTab?((0,s.uX)(),(0,s.CE)("div",g,[(0,s.Lk)("div",p,[t[16]||(t[16]=(0,s.Lk)("h5",{class:"mb-0"},"用户管理",-1)),(0,s.Lk)("div",m,[(0,s.bo)((0,s.Lk)("input",{type:"text",class:"form-control",placeholder:"搜索用户...","onUpdate:modelValue":t[0]||(t[0]=function(e){return Z.userSearchQuery=e})},null,512),[[r.Jo,Z.userSearchQuery]]),(0,s.Lk)("button",{class:"btn btn-outline-secondary",type:"button",onClick:t[1]||(t[1]=function(){return ee.searchUsers&&ee.searchUsers.apply(ee,arguments)})},t[15]||(t[15]=[(0,s.Lk)("i",{class:"bi bi-search"},null,-1)]))])]),(0,s.Lk)("div",k,[Z.loadingUsers?((0,s.uX)(),(0,s.CE)("div",v,t[17]||(t[17]=[(0,s.Lk)("div",{class:"spinner-border",role:"status"},[(0,s.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):Z.userError?((0,s.uX)(),(0,s.CE)("div",h,(0,n.v_)(Z.userError),1)):((0,s.uX)(),(0,s.CE)("div",b,[(0,s.Lk)("table",f,[t[19]||(t[19]=(0,s.Lk)("thead",null,[(0,s.Lk)("tr",null,[(0,s.Lk)("th",{scope:"col"},"ID"),(0,s.Lk)("th",{scope:"col"},"用户名"),(0,s.Lk)("th",{scope:"col"},"邮箱"),(0,s.Lk)("th",{scope:"col"},"角色"),(0,s.Lk)("th",{scope:"col"},"状态"),(0,s.Lk)("th",{scope:"col"},"注册时间"),(0,s.Lk)("th",{scope:"col"},"操作")])],-1)),(0,s.Lk)("tbody",null,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(Z.users,(function(e){return(0,s.uX)(),(0,s.CE)("tr",{key:e.id},[(0,s.Lk)("td",null,(0,n.v_)(e.id),1),(0,s.Lk)("td",null,(0,n.v_)(e.username),1),(0,s.Lk)("td",null,(0,n.v_)(e.email),1),(0,s.Lk)("td",null,[(0,s.Lk)("span",{class:(0,n.C4)(["badge","ADMIN"===e.role?"bg-danger":"REVIEWER"===e.role?"bg-primary":"bg-secondary"])},(0,n.v_)(e.role),3)]),(0,s.Lk)("td",null,[(0,s.Lk)("span",{class:(0,n.C4)(["badge",e.active?"bg-success":"bg-danger"])},(0,n.v_)(e.active?"已激活":"已禁用"),3)]),(0,s.Lk)("td",null,(0,n.v_)(ee.formatDate(e.createdAt)),1),(0,s.Lk)("td",null,[(0,s.Lk)("div",L,[(0,s.Lk)("button",{class:"btn btn-outline-primary",onClick:function(t){return ee.toggleUserStatus(e)},title:e.active?"禁用用户":"激活用户"},[(0,s.Lk)("i",{class:(0,n.C4)(e.active?"bi bi-x-circle":"bi bi-check-circle")},null,2)],8,C),(0,s.Lk)("button",{class:"btn btn-outline-secondary",onClick:function(t){return ee.editUserRole(e)},title:"修改角色"},t[18]||(t[18]=[(0,s.Lk)("i",{class:"bi bi-pencil"},null,-1)]),8,y)])])])})),128))])]),Z.userTotalPages>1?((0,s.uX)(),(0,s.CE)("nav",E,[(0,s.Lk)("ul",w,[(0,s.Lk)("li",{class:(0,n.C4)(["page-item",0===Z.userCurrentPage?"disabled":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:t[2]||(t[2]=(0,r.D$)((function(e){return ee.changePage(Z.userCurrentPage-1,"users")}),["prevent"]))},"上一页")],2),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(Z.userTotalPages,(function(e){return(0,s.uX)(),(0,s.CE)("li",{key:e,class:(0,n.C4)(["page-item",Z.userCurrentPage===e-1?"active":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:(0,r.D$)((function(t){return ee.changePage(e-1,"users")}),["prevent"])},(0,n.v_)(e),9,P)],2)})),128)),(0,s.Lk)("li",{class:(0,n.C4)(["page-item",Z.userCurrentPage===Z.userTotalPages-1?"disabled":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:t[3]||(t[3]=(0,r.D$)((function(e){return ee.changePage(Z.userCurrentPage+1,"users")}),["prevent"]))},"下一页")],2)])])):(0,s.Q3)("",!0)]))])])):(0,s.Q3)("",!0),"images"===Z.activeTab?((0,s.uX)(),(0,s.CE)("div",A,[(0,s.Lk)("div",x,[t[21]||(t[21]=(0,s.Lk)("h5",{class:"mb-0"},"图像管理",-1)),(0,s.Lk)("div",U,[(0,s.bo)((0,s.Lk)("input",{type:"text",class:"form-control",placeholder:"搜索图像...","onUpdate:modelValue":t[4]||(t[4]=function(e){return Z.imageSearchQuery=e})},null,512),[[r.Jo,Z.imageSearchQuery]]),(0,s.Lk)("button",{class:"btn btn-outline-secondary",type:"button",onClick:t[5]||(t[5]=function(){return ee.searchImages&&ee.searchImages.apply(ee,arguments)})},t[20]||(t[20]=[(0,s.Lk)("i",{class:"bi bi-search"},null,-1)]))])]),(0,s.Lk)("div",I,[(0,s.Lk)("div",S,[(0,s.bo)((0,s.Lk)("select",{class:"form-select w-auto","onUpdate:modelValue":t[6]||(t[6]=function(e){return Z.imageStatusFilter=e}),onChange:t[7]||(t[7]=function(e){return ee.fetchImages()})},t[22]||(t[22]=[(0,s.Fv)('<option value="" data-v-7a76f7c9>所有状态</option><option value="UPLOADED" data-v-7a76f7c9>已上传</option><option value="ANNOTATED" data-v-7a76f7c9>已标注</option><option value="REVIEWED" data-v-7a76f7c9>已审核</option><option value="APPROVED" data-v-7a76f7c9>已批准</option><option value="REJECTED" data-v-7a76f7c9>已拒绝</option>',6)]),544),[[r.u1,Z.imageStatusFilter]])]),Z.loadingImages?((0,s.uX)(),(0,s.CE)("div",D,t[23]||(t[23]=[(0,s.Lk)("div",{class:"spinner-border",role:"status"},[(0,s.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):Z.imageError?((0,s.uX)(),(0,s.CE)("div",R,(0,n.v_)(Z.imageError),1)):((0,s.uX)(),(0,s.CE)("div",T,[(0,s.Lk)("div",_,[(0,s.Lk)("table",X,[t[26]||(t[26]=(0,s.Lk)("thead",null,[(0,s.Lk)("tr",null,[(0,s.Lk)("th",{scope:"col"},"ID"),(0,s.Lk)("th",{scope:"col"},"缩略图"),(0,s.Lk)("th",{scope:"col"},"名称"),(0,s.Lk)("th",{scope:"col"},"上传者"),(0,s.Lk)("th",{scope:"col"},"状态"),(0,s.Lk)("th",{scope:"col"},"上传时间"),(0,s.Lk)("th",{scope:"col"},"操作")])],-1)),(0,s.Lk)("tbody",null,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(Z.images,(function(a){return(0,s.uX)(),(0,s.CE)("tr",{key:a.id},[(0,s.Lk)("td",null,(0,n.v_)(a.id),1),(0,s.Lk)("td",null,[(0,s.Lk)("img",{src:"".concat(Z.apiUrl,"/api/images/").concat(a.id,"/thumbnail"),class:"admin-thumbnail",alt:"缩略图"},null,8,N)]),(0,s.Lk)("td",null,(0,n.v_)(a.name),1),(0,s.Lk)("td",null,(0,n.v_)(a.uploaderName),1),(0,s.Lk)("td",null,[(0,s.Lk)("span",{class:(0,n.C4)(["badge","APPROVED"===a.status?"bg-success":"REJECTED"===a.status?"bg-danger":"ANNOTATED"===a.status?"bg-primary":"REVIEWED"===a.status?"bg-info":"bg-secondary"])},(0,n.v_)(ee.getStatusText(a.status)),3)]),(0,s.Lk)("td",null,(0,n.v_)(ee.formatDate(a.uploadTime)),1),(0,s.Lk)("td",null,[(0,s.Lk)("div",V,[(0,s.Lk)("button",{class:"btn btn-outline-info",onClick:function(t){return e.$router.push("/images/".concat(a.id))},title:"查看详情"},t[24]||(t[24]=[(0,s.Lk)("i",{class:"bi bi-eye"},null,-1)]),8,$),(0,s.Lk)("button",{class:"btn btn-outline-danger",onClick:function(e){return ee.deleteImage(a.id)},title:"删除图像"},t[25]||(t[25]=[(0,s.Lk)("i",{class:"bi bi-trash"},null,-1)]),8,Q)])])])})),128))])])]),Z.imageTotalPages>1?((0,s.uX)(),(0,s.CE)("nav",F,[(0,s.Lk)("ul",O,[(0,s.Lk)("li",{class:(0,n.C4)(["page-item",0===Z.imageCurrentPage?"disabled":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:t[8]||(t[8]=(0,r.D$)((function(e){return ee.changePage(Z.imageCurrentPage-1,"images")}),["prevent"]))},"上一页")],2),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(Z.imageTotalPages,(function(e){return(0,s.uX)(),(0,s.CE)("li",{key:e,class:(0,n.C4)(["page-item",Z.imageCurrentPage===e-1?"active":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:(0,r.D$)((function(t){return ee.changePage(e-1,"images")}),["prevent"])},(0,n.v_)(e),9,z)],2)})),128)),(0,s.Lk)("li",{class:(0,n.C4)(["page-item",Z.imageCurrentPage===Z.imageTotalPages-1?"disabled":""])},[(0,s.Lk)("a",{class:"page-link",href:"#",onClick:t[9]||(t[9]=(0,r.D$)((function(e){return ee.changePage(Z.imageCurrentPage+1,"images")}),["prevent"]))},"下一页")],2)])])):(0,s.Q3)("",!0)]))])])):(0,s.Q3)("",!0),"settings"===Z.activeTab?((0,s.uX)(),(0,s.CE)("div",W,[t[32]||(t[32]=(0,s.Lk)("div",{class:"card-header"},[(0,s.Lk)("h5",{class:"mb-0"},"系统设置")],-1)),(0,s.Lk)("div",q,[(0,s.Lk)("form",{onSubmit:t[14]||(t[14]=(0,r.D$)((function(){return ee.saveSettings&&ee.saveSettings.apply(ee,arguments)}),["prevent"]))},[(0,s.Lk)("div",J,[t[27]||(t[27]=(0,s.Lk)("label",{for:"siteName",class:"form-label"},"系统名称",-1)),(0,s.bo)((0,s.Lk)("input",{type:"text",class:"form-control",id:"siteName","onUpdate:modelValue":t[10]||(t[10]=function(e){return Z.settings.siteName=e})},null,512),[[r.Jo,Z.settings.siteName]])]),(0,s.Lk)("div",M,[t[28]||(t[28]=(0,s.Lk)("label",{for:"maxImageSize",class:"form-label"},"最大图像大小(MB)",-1)),(0,s.bo)((0,s.Lk)("input",{type:"number",class:"form-control",id:"maxImageSize","onUpdate:modelValue":t[11]||(t[11]=function(e){return Z.settings.maxImageSizeMB=e})},null,512),[[r.Jo,Z.settings.maxImageSizeMB]])]),(0,s.Lk)("div",B,[(0,s.bo)((0,s.Lk)("input",{class:"form-check-input",type:"checkbox",id:"allowRegistration","onUpdate:modelValue":t[12]||(t[12]=function(e){return Z.settings.allowPublicRegistration=e})},null,512),[[r.lH,Z.settings.allowPublicRegistration]]),t[29]||(t[29]=(0,s.Lk)("label",{class:"form-check-label",for:"allowRegistration"},"允许公开注册",-1))]),(0,s.Lk)("div",K,[(0,s.bo)((0,s.Lk)("input",{class:"form-check-input",type:"checkbox",id:"reviewRequired","onUpdate:modelValue":t[13]||(t[13]=function(e){return Z.settings.requireReview=e})},null,512),[[r.lH,Z.settings.requireReview]]),t[30]||(t[30]=(0,s.Lk)("label",{class:"form-check-label",for:"reviewRequired"},"标注需要审核",-1))]),(0,s.Lk)("button",{type:"submit",class:"btn btn-primary",disabled:Z.savingSettings},[Z.savingSettings?((0,s.uX)(),(0,s.CE)("span",H,t[31]||(t[31]=[(0,s.Lk)("span",{class:"spinner-border spinner-border-sm",role:"status","aria-hidden":"true"},null,-1),(0,s.eW)(" 保存中... ")]))):((0,s.uX)(),(0,s.CE)("span",G,"保存设置"))],8,j)],32)])])):(0,s.Q3)("",!0)])])])}var Z=a(14048),ee=a(30388),te=(a(74423),a(23288),a(72505)),ae=a.n(te);const se={name:"Admin",data:function(){return{apiUrl:{NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL,activeTab:"users",tabs:[{id:"users",name:"用户管理",icon:"bi bi-people"},{id:"images",name:"图像管理",icon:"bi bi-images"},{id:"settings",name:"系统设置",icon:"bi bi-gear"}],users:[],loadingUsers:!1,userError:null,userCurrentPage:0,userTotalPages:0,userSearchQuery:"",images:[],loadingImages:!1,imageError:null,imageCurrentPage:0,imageTotalPages:0,imageSearchQuery:"",imageStatusFilter:"",settings:{siteName:"血管瘤辅助标注系统",maxImageSizeMB:10,allowPublicRegistration:!0,requireReview:!0},savingSettings:!1}},methods:{fetchUsers:function(){var e=arguments,t=this;return(0,ee.A)((0,Z.A)().mark((function a(){var s,n,r,i;return(0,Z.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return s=e.length>0&&void 0!==e[0]?e[0]:0,t.loadingUsers=!0,t.userError=null,a.prev=3,n={page:s,size:10,query:t.userSearchQuery},a.next=7,ae().get("".concat(t.apiUrl,"/api/admin/users"),{params:n});case 7:r=a.sent,t.users=r.data.content,t.userCurrentPage=r.data.number,t.userTotalPages=r.data.totalPages,a.next=16;break;case 13:a.prev=13,a.t0=a["catch"](3),t.userError="加载用户列表失败: "+((null===(i=a.t0.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.message)||a.t0.message);case 16:return a.prev=16,t.loadingUsers=!1,a.finish(16);case 19:case"end":return a.stop()}}),a,null,[[3,13,16,19]])})))()},fetchImages:function(){var e=arguments,t=this;return(0,ee.A)((0,Z.A)().mark((function a(){var s,n,r,i;return(0,Z.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return s=e.length>0&&void 0!==e[0]?e[0]:0,t.loadingImages=!0,t.imageError=null,a.prev=3,n={page:s,size:10,query:t.imageSearchQuery,status:t.imageStatusFilter},a.next=7,ae().get("".concat(t.apiUrl,"/api/admin/images"),{params:n});case 7:r=a.sent,t.images=r.data.content,t.imageCurrentPage=r.data.number,t.imageTotalPages=r.data.totalPages,a.next=16;break;case 13:a.prev=13,a.t0=a["catch"](3),t.imageError="加载图像列表失败: "+((null===(i=a.t0.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.message)||a.t0.message);case 16:return a.prev=16,t.loadingImages=!1,a.finish(16);case 19:case"end":return a.stop()}}),a,null,[[3,13,16,19]])})))()},fetchSettings:function(){var e=this;return(0,ee.A)((0,Z.A)().mark((function t(){var a,s;return(0,Z.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,ae().get("".concat(e.apiUrl,"/api/admin/settings"));case 3:a=t.sent,e.settings=a.data,t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),e.$store.dispatch("showAlert",{type:"error",message:"加载系统设置失败: "+((null===(s=t.t0.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.message)||t.t0.message)});case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},saveSettings:function(){var e=this;return(0,ee.A)((0,Z.A)().mark((function t(){var a;return(0,Z.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.savingSettings=!0,t.prev=1,t.next=4,ae().post("".concat(e.apiUrl,"/api/admin/settings"),e.settings);case 4:e.$store.dispatch("showAlert",{type:"success",message:"系统设置保存成功"}),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](1),e.$store.dispatch("showAlert",{type:"error",message:"保存系统设置失败: "+((null===(a=t.t0.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||t.t0.message)});case 10:return t.prev=10,e.savingSettings=!1,t.finish(10);case 13:case"end":return t.stop()}}),t,null,[[1,7,10,13]])})))()},changePage:function(e,t){"users"===t?e>=0&&e<this.userTotalPages&&this.fetchUsers(e):"images"===t&&e>=0&&e<this.imageTotalPages&&this.fetchImages(e)},searchUsers:function(){this.fetchUsers(0)},searchImages:function(){this.fetchImages(0)},toggleUserStatus:function(e){var t=this;return(0,ee.A)((0,Z.A)().mark((function a(){var s;return(0,Z.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,ae().post("".concat(t.apiUrl,"/api/admin/users/").concat(e.id,"/toggle-status"));case 3:t.$store.dispatch("showAlert",{type:"success",message:"用户 ".concat(e.username," 已").concat(e.active?"禁用":"激活")}),t.fetchUsers(t.userCurrentPage),a.next=10;break;case 7:a.prev=7,a.t0=a["catch"](0),t.$store.dispatch("showAlert",{type:"error",message:"更改用户状态失败: "+((null===(s=a.t0.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.message)||a.t0.message)});case 10:case"end":return a.stop()}}),a,null,[[0,7]])})))()},editUserRole:function(e){var t=this;return(0,ee.A)((0,Z.A)().mark((function a(){var s,n;return(0,Z.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(s=prompt("请为用户 ".concat(e.username," 选择新角色 (USER, REVIEWER, ADMIN)"),e.role),!s||!["USER","REVIEWER","ADMIN"].includes(s.toUpperCase())){a.next=14;break}return a.prev=2,a.next=5,ae().post("".concat(t.apiUrl,"/api/admin/users/").concat(e.id,"/change-role"),{role:s.toUpperCase()});case 5:t.$store.dispatch("showAlert",{type:"success",message:"用户 ".concat(e.username," 的角色已更改为 ").concat(s.toUpperCase())}),t.fetchUsers(t.userCurrentPage),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](2),t.$store.dispatch("showAlert",{type:"error",message:"更改用户角色失败: "+((null===(n=a.t0.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||a.t0.message)});case 12:a.next=15;break;case 14:s&&t.$store.dispatch("showAlert",{type:"error",message:"无效的角色值"});case 15:case"end":return a.stop()}}),a,null,[[2,9]])})))()},deleteImage:function(e){var t=this;return(0,ee.A)((0,Z.A)().mark((function a(){var s;return(0,Z.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!confirm("确定要删除这张图像吗？此操作不可撤销。")){a.next=11;break}return a.prev=1,a.next=4,ae()["delete"]("".concat(t.apiUrl,"/api/admin/images/").concat(e));case 4:t.$store.dispatch("showAlert",{type:"success",message:"图像已成功删除"}),t.fetchImages(t.imageCurrentPage),a.next=11;break;case 8:a.prev=8,a.t0=a["catch"](1),t.$store.dispatch("showAlert",{type:"error",message:"删除图像失败: "+((null===(s=a.t0.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.message)||a.t0.message)});case 11:case"end":return a.stop()}}),a,null,[[1,8]])})))()},formatDate:function(e){if(!e)return"";var t=new Date(e);return t.toLocaleString("zh-CN")},getStatusText:function(e){var t={UPLOADED:"已上传",ANNOTATED:"已标注",REVIEWED:"已审核",APPROVED:"已批准",REJECTED:"已拒绝"};return t[e]||e}},mounted:function(){this.fetchUsers(),this.fetchImages(),this.fetchSettings()},watch:{activeTab:function(e){"users"===e?this.fetchUsers():"images"===e?this.fetchImages():"settings"===e&&this.fetchSettings()}}};var ne=a(66262);const re=(0,ne.A)(se,[["render",Y],["__scopeId","data-v-7a76f7c9"]]),ie=re}}]);