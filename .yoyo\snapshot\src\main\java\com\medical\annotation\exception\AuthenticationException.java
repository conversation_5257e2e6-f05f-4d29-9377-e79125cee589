package com.medical.annotation.exception;

public class AuthenticationException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    public AuthenticationException(String message) {
        super(message);
    }
    
    public AuthenticationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public static AuthenticationException notLoggedIn() {
        return new AuthenticationException("用户未登录或会话已过期，请重新登录");
    }
    
    public static AuthenticationException invalidToken() {
        return new AuthenticationException("无效的认证令牌，请重新登录");
    }
} 