package com.medical.annotation.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.*;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;

@Entity
@Table(name = "hemangioma_diagnoses")
public class HemangiomaDiagnosis {
    
    public enum Status {
        DRAFT,
        REVIEWED,
        SUBMITTED,
        APPROVED,
        REJECTED
    }
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    private Integer patientAge;
    
    private String gender;
    
    private String originType;
    
    @Column(name = "vessel_texture")
    private String vesselTexture;
    
    @Column(name = "body_part")
    private String bodyPart;

    @Column(name = "color")
    private String color;
    
    @Column(name = "image_path")
    private String imagePath;
    
    @Column(nullable = false)
    private LocalDateTime createdAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @Column(name = "detected_type")
    private String detectedType;
    
    @Column(name = "confidence")
    private Double confidence;
    
    @Column(name = "processed_image_path")
    private String processedImagePath;
    
    // --> 新增: 结构化LLM建议字段
    @Column(columnDefinition = "TEXT")
    private String diagnosticSummary;

    @Column(columnDefinition = "TEXT")
    private String treatmentSuggestion;

    @Column(columnDefinition = "TEXT")
    private String precautions;
    
    @Column(columnDefinition = "TEXT")
    private String disclaimer;
    
    @Column(columnDefinition = "TEXT")
    private String reviewNotes;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private Status status = Status.DRAFT;

    @OneToMany(mappedBy = "hemangiomaDiagnosis", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonManagedReference
    private List<Tag> tags = new ArrayList<>();
    
    // 默认构造函数
    public HemangiomaDiagnosis() {
        this.createdAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPatientAge() {
        return patientAge;
    }

    public void setPatientAge(Integer patientAge) {
        this.patientAge = patientAge;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getOriginType() {
        return originType;
    }

    public void setOriginType(String originType) {
        this.originType = originType;
    }

    public String getVesselTexture() {
        return vesselTexture;
    }

    public void setVesselTexture(String vesselTexture) {
        this.vesselTexture = vesselTexture;
    }

    public String getBodyPart() {
        return bodyPart;
    }

    public void setBodyPart(String bodyPart) {
        this.bodyPart = bodyPart;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
    
    public String getDetectedType() {
        return detectedType;
    }

    public void setDetectedType(String detectedType) {
        this.detectedType = detectedType;
    }

    public Double getConfidence() {
        return confidence;
    }

    public void setConfidence(Double confidence) {
        this.confidence = confidence;
    }

    public String getProcessedImagePath() {
        return processedImagePath;
    }
    
    public void setProcessedImagePath(String processedImagePath) {
        this.processedImagePath = processedImagePath;
    }

    public String getDiagnosticSummary() {
        return diagnosticSummary;
    }

    public void setDiagnosticSummary(String diagnosticSummary) {
        this.diagnosticSummary = diagnosticSummary;
    }

    public String getTreatmentSuggestion() {
        return treatmentSuggestion;
    }

    public void setTreatmentSuggestion(String treatmentSuggestion) {
        this.treatmentSuggestion = treatmentSuggestion;
    }

    public String getPrecautions() {
        return precautions;
    }

    public void setPrecautions(String precautions) {
        this.precautions = precautions;
    }

    public String getDisclaimer() {
        return disclaimer;
    }

    public void setDisclaimer(String disclaimer) {
        this.disclaimer = disclaimer;
    }
    
    public String getReviewNotes() {
        return reviewNotes;
    }

    public void setReviewNotes(String reviewNotes) {
        this.reviewNotes = reviewNotes;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    // 添加标签辅助方法
    public void addTag(Tag tag) {
        tags.add(tag);
        tag.setHemangiomaDiagnosis(this);
    }
    
    // 移除标签辅助方法
    public void removeTag(Tag tag) {
        tags.remove(tag);
        tag.setHemangiomaDiagnosis(null);
    }
    
    // 清除所有标签辅助方法
    public void clearTags() {
        for (Tag tag : new ArrayList<>(tags)) {
            removeTag(tag);
        }
    }
    
    @Column(name = "annotation_data")
    private String annotationData;

    @ElementCollection
    @CollectionTable(name = "diagnosis_bounding_boxes", joinColumns = @JoinColumn(name = "diagnosis_id"))
    @Column(name = "bounding_box", columnDefinition="TEXT")
    private List<String> boundingBoxes = new ArrayList<>();

    // Getter and Setter for annotationData
    public String getAnnotationData() {
        return annotationData;
    }

    public void setAnnotationData(String annotationData) {
        this.annotationData = annotationData;
    }

    // Getter and Setter for boundingBoxes
    public List<String> getBoundingBoxes() {
        return boundingBoxes;
    }

    public void setBoundingBoxes(List<String> boundingBoxes) {
        this.boundingBoxes = boundingBoxes;
    }
}