"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[277],{38658:(e,t,r)=>{r.r(t),r.d(t,{default:()=>g});var s=r(20641),a=r(90033),o={class:"container login-container"},i={key:0,class:"card"},n={class:"card-body p-4 text-center"};function u(e,t,r,u,c,d){return(0,s.uX)(),(0,s.CE)("div",o,[u.showRedirectionInfo?((0,s.uX)(),(0,s.CE)("div",i,[t[1]||(t[1]=(0,s.Lk)("div",{class:"card-header"},[(0,s.Lk)("h3",{class:"mb-0"},"正在重定向...")],-1)),(0,s.Lk)("div",n,[t[0]||(t[0]=(0,s.Lk)("div",{class:"spinner-border text-primary mb-3",role:"status"},[(0,s.Lk)("span",{class:"visually-hidden"},"Loading...")],-1)),(0,s.Lk)("p",null,(0,a.v_)(u.redirectMessage),1)])])):(0,s.Q3)("",!0)])}r(28706),r(74423),r(44114),r(2892),r(79432),r(21699),r(76031);var c=r(50953),d=r(40834),l=r(75220);const m={name:"Login",setup:function(){var e=(0,d.Pj)(),t=(0,l.rd)(),r=(0,l.lq)(),a=(0,c.KR)(!0),o=(0,c.KR)("正在处理您的登录请求，请稍候..."),i=(0,c.KR)(null);return(0,s.sV)((function(){var s=sessionStorage.getItem("preservedUser");if(s){localStorage.setItem("user",s),sessionStorage.removeItem("preservedUser");var a=sessionStorage.getItem("redirectAfterLogin")||"/app/dashboard";return o.value="检测到已保存的会话，正在恢复...",void setTimeout((function(){t.push(a)}),1e3)}var n="true"===localStorage.getItem("isSaveAndExit");if(n)return localStorage.removeItem("isSaveAndExit"),o.value="检测到您之前已保存工作，正在跳转到工作台...",void setTimeout((function(){t.push("/app/dashboard")}),1e3);r.query.imageId&&(i.value=r.query.imageId);var u=JSON.parse(localStorage.getItem("user"));if(u){if(i.value)return o.value="您已登录，正在返回标注页面...",sessionStorage.setItem("isNavigatingAfterSave","true"),e.dispatch("saveProgress",{step:2,imageId:Number(i.value),formData:null}),void setTimeout((function(){t.push({path:"/cases/structured-form",query:{imageId:i.value,direct:1}})}),1500);var c=sessionStorage.getItem("redirectPath"),d=sessionStorage.getItem("redirectAfterLogin");sessionStorage.removeItem("redirectPath"),sessionStorage.removeItem("redirectAfterLogin"),d?(o.value="您已登录，正在返回之前的页面...",setTimeout((function(){if(d.includes("/annotations")||d.includes("/cases/form")||d.includes("/cases/structured-form")){var e=d.includes("?")?"&":"?";window.location.href="".concat(d).concat(e,"direct=1")}else window.location.href=d}),1500)):c?(o.value="您已登录，正在跳转到请求的页面...",setTimeout((function(){t.push(c)}),1500)):(o.value="您已登录，正在跳转到工作台...",setTimeout((function(){t.push("/app/dashboard")}),1500))}else o.value="请使用完整版登录",i.value?sessionStorage.setItem("redirectAfterLogin","/cases/structured-form?imageId=".concat(i.value)):r.query.redirect?sessionStorage.setItem("redirectAfterLogin",r.query.redirect):r.query.from&&sessionStorage.setItem("redirectPath",r.query.from),setTimeout((function(){r.query.from?window.location.href="/?from="+encodeURIComponent(r.query.from):window.location.href="/"}),2e3)})),{showRedirectionInfo:a,redirectMessage:o}}};var v=r(66262);const f=(0,v.A)(m,[["render",u],["__scopeId","data-v-143aa1da"]]),g=f}}]);