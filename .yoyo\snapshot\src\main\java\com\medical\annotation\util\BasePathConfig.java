package com.medical.annotation.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.File;
import javax.annotation.PostConstruct;

/**
 * 全局基础路径配置类
 * 所有系统中使用的路径都应该从这个类获取，而不是硬编码
 */
@Component
public class BasePathConfig {
    /**
     * 项目根目录 - 所有文件相对路径的基准点
     * 通过application.properties中的app.base.dir配置
     * 默认为当前工作目录
     */
    @Value("${app.base.dir:#{systemProperties['user.dir']}}")
    private String baseDir;
    
    /**
     * 图片存储的根目录
     * 如果指定了绝对路径则使用绝对路径，否则相对于项目根目录
     */
    @Value("${app.upload.dir:medical_images}")
    private String uploadDirConfig;
    
    // 图片相关子目录名称
    private static final String ORIGINAL_DIR = "original";
    private static final String PROCESSED_DIR = "processed";
    private static final String ANNOTATED_DIR = "annotated";
    private static final String TEMP_DIR = "temp";
    
    // 完整路径，初始化后填充
    private String uploadDir;
    private String originalDir;
    private String processedDir;
    private String annotatedDir;
    private String tempDir;

    @PostConstruct
    public void init() {
        // 处理上传目录路径
        if (new File(uploadDirConfig).isAbsolute()) {
            // 如果是绝对路径，直接使用
            uploadDir = uploadDirConfig;
        } else {
            // 否则相对于项目根目录
            uploadDir = new File(baseDir, uploadDirConfig).getAbsolutePath();
        }
        
        // 初始化子目录路径
        originalDir = new File(uploadDir, ORIGINAL_DIR).getAbsolutePath();
        processedDir = new File(uploadDir, PROCESSED_DIR).getAbsolutePath();
        annotatedDir = new File(uploadDir, ANNOTATED_DIR).getAbsolutePath();
        tempDir = new File(uploadDir, TEMP_DIR).getAbsolutePath();
        
        // 创建必要的目录
        createDirIfNotExist(uploadDir);
        createDirIfNotExist(originalDir);
        createDirIfNotExist(processedDir);
        createDirIfNotExist(annotatedDir);
        createDirIfNotExist(tempDir);
        
        System.out.println("========= 基础路径配置 =========");
        System.out.println("项目根目录: " + baseDir);
        System.out.println("上传根目录: " + uploadDir);
        System.out.println("原始图片目录: " + originalDir);
        System.out.println("处理图片目录: " + processedDir);
        System.out.println("标注图片目录: " + annotatedDir);
        System.out.println("临时图片目录: " + tempDir);
        System.out.println("==============================");
    }
    
    private void createDirIfNotExist(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            System.out.println("创建目录 " + dirPath + ": " + (created ? "成功" : "失败"));
        }
    }
    
    /**
     * 获取项目根目录
     */
    public String getBaseDir() {
        return baseDir;
    }
    
    /**
     * 获取图片上传根目录
     */
    public String getUploadDir() {
        return uploadDir;
    }
    
    /**
     * 获取原始图片目录
     */
    public String getOriginalDir() {
        return originalDir;
    }
    
    /**
     * 获取处理后图片目录
     */
    public String getProcessedDir() {
        return processedDir;
    }
    
    /**
     * 获取标注图片目录
     */
    public String getAnnotatedDir() {
        return annotatedDir;
    }
    
    /**
     * 获取临时图片目录
     */
    public String getTempDir() {
        return tempDir;
    }
    
    /**
     * 根据类型获取相应的图片目录
     * @param type 目录类型："original", "processed", "annotated", "temp"
     * @return 对应的目录路径
     */
    public String getDirByType(String type) {
        switch (type.toLowerCase()) {
            case "original":
                return originalDir;
            case "processed":
                return processedDir;
            case "annotated":
                return annotatedDir;
            case "temp":
                return tempDir;
            default:
                return uploadDir;
        }
    }
    
    /**
     * 将相对路径转换为绝对路径
     * @param relativePath 相对于项目根目录的路径
     * @return 绝对路径
     */
    public String toAbsolutePath(String relativePath) {
        if (relativePath == null) return null;
        if (new File(relativePath).isAbsolute()) {
            return relativePath;
        }
        return new File(baseDir, relativePath).getAbsolutePath();
    }
    
    /**
     * 获取图片文件的完整路径
     * @param type 目录类型("original", "processed", "annotated", "temp")
     * @param filename 文件名
     * @return 完整的文件路径
     */
    public String getImageFilePath(String type, String filename) {
        return new File(getDirByType(type), filename).getAbsolutePath();
    }
    
    /**
     * 获取文件的Web访问路径
     * @param type 目录类型("original", "processed", "annotated", "temp")
     * @param filename 文件名
     * @return 适合Web访问的路径
     */
    public String getImageWebPath(String type, String filename) {
        return "/medical/images/" + type + "/" + filename;
    }
} 