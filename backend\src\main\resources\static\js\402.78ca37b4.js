"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[402],{83402:(n,a,t)=>{t.r(a),t.d(a,{default:()=>v});var e=t(61431),o={class:"annotation-review-container"};function i(n,a,t,i,r,c){var s=(0,e.g2)("AnnotationReviewList");return(0,e.uX)(),(0,e.CE)("div",o,[(0,e.bF)(s)])}var r=t(25069);const c={name:"AnnotationReview",components:{AnnotationReviewList:r.A}};var s=t(66262);const d=(0,s.A)(c,[["render",i],["__scopeId","data-v-11a02caa"]]),v=d}}]);
//# sourceMappingURL=402.78ca37b4.js.map