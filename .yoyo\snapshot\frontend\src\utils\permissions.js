// 权限配置文件
// 定义系统中各角色的权限

// 功能权限列表
export const PERMISSIONS = {
  // 系统管理权限
  MANAGE_USERS: 'manage_users',         // 用户管理
  MANAGE_TEAMS: 'manage_teams',         // 团队管理
  VIEW_STATS: 'view_stats',             // 查看统计
  
  // 团队权限
  VIEW_TEAM: 'view_team',               // 查看团队
  JOIN_TEAM: 'join_team',               // 加入团队
  LEAVE_TEAM: 'leave_team',             // 离开团队
  
  // 病例权限
  CREATE_CASE: 'create_case',           // 创建病例
  EDIT_CASE: 'edit_case',               // 编辑病例
  DELETE_CASE: 'delete_case',           // 删除病例
  VIEW_ALL_CASES: 'view_all_cases',     // 查看所有病例
  VIEW_OWN_CASES: 'view_own_cases',     // 查看自己的病例
  
  // 审核权限
  REVIEW_CASES: 'review_cases',         // 审核病例
  APPROVE_CASES: 'approve_cases',       // 批准病例
  
  // 标注权限
  ANNOTATE_IMAGES: 'annotate_images',   // 标注图像
  VIEW_OWN_ANNOTATIONS: 'view_own_annotations', // 查看自己的标注
  EDIT_OWN_ANNOTATIONS: 'edit_own_annotations', // 编辑自己的标注
  
  // 图像管理
  UPLOAD_IMAGES: 'upload_images',       // 上传图像
  DELETE_IMAGES: 'delete_images'        // 删除图像
}

// 角色权限映射
export const ROLE_PERMISSIONS = {
  // 管理员拥有所有权限
  ADMIN: [
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.MANAGE_TEAMS,
    PERMISSIONS.VIEW_STATS,
    PERMISSIONS.VIEW_TEAM,
    PERMISSIONS.JOIN_TEAM,
    PERMISSIONS.LEAVE_TEAM,
    PERMISSIONS.CREATE_CASE,
    PERMISSIONS.EDIT_CASE,
    PERMISSIONS.DELETE_CASE,
    PERMISSIONS.VIEW_ALL_CASES,
    PERMISSIONS.VIEW_OWN_CASES,
    PERMISSIONS.REVIEW_CASES,
    PERMISSIONS.APPROVE_CASES,
    PERMISSIONS.ANNOTATE_IMAGES,
    PERMISSIONS.VIEW_OWN_ANNOTATIONS,
    PERMISSIONS.EDIT_OWN_ANNOTATIONS,
    PERMISSIONS.UPLOAD_IMAGES,
    PERMISSIONS.DELETE_IMAGES
  ],
  
  // 标注医生权限 - 只能处理自己的标注
  DOCTOR: [
    PERMISSIONS.CREATE_CASE,
    PERMISSIONS.EDIT_CASE,
    PERMISSIONS.VIEW_OWN_CASES,
    PERMISSIONS.ANNOTATE_IMAGES,
    PERMISSIONS.VIEW_OWN_ANNOTATIONS,
    PERMISSIONS.EDIT_OWN_ANNOTATIONS,
    PERMISSIONS.UPLOAD_IMAGES,
    PERMISSIONS.VIEW_TEAM,
    PERMISSIONS.JOIN_TEAM,
    PERMISSIONS.LEAVE_TEAM
  ],
  
  // 审核医生权限 - 可以查看所有人的标注，但只能编辑自己的
  REVIEWER: [
    PERMISSIONS.VIEW_ALL_CASES,
    PERMISSIONS.VIEW_OWN_CASES,
    PERMISSIONS.REVIEW_CASES,
    PERMISSIONS.APPROVE_CASES,
    PERMISSIONS.ANNOTATE_IMAGES,
    PERMISSIONS.VIEW_OWN_ANNOTATIONS,
    PERMISSIONS.EDIT_OWN_ANNOTATIONS,
    PERMISSIONS.CREATE_CASE,
    PERMISSIONS.EDIT_CASE,
    PERMISSIONS.VIEW_TEAM,
    PERMISSIONS.JOIN_TEAM,
    PERMISSIONS.LEAVE_TEAM
  ]
}

// 路由权限映射（哪些角色可以访问哪些路由）
export const ROUTE_PERMISSIONS = {
  // 仪表盘 - 所有角色
  '/app/dashboard': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  
  // 用户管理 - 仅管理员
  '/app/users': ['ADMIN'],
  
  // 团队管理 - 所有角色
  '/app/teams': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  '/app/teams/join': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  '/app/teams/view': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  
  // 病例管理 - 所有角色但视图不同
  '/app/cases': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  '/app/cases/new': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  '/app/cases/edit': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  '/app/cases/view': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  '/app/cases/form': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  
  // 审核页面 - 管理员和审核医生
  '/app/review': ['ADMIN', 'REVIEWER'],
  
  // 标注审核页面 - 管理员和审核医生
  '/app/annotation-reviews': ['ADMIN', 'REVIEWER'],
  
  // 图片上传 - 现在所有角色都可以
  '/app/images/upload': ['ADMIN', 'DOCTOR', 'REVIEWER'],
  
  // 管理页面 - 仅管理员
  '/admin': ['ADMIN']
}

// 检查用户是否有特定权限
export function hasPermission(userRole, permission) {
  if (!userRole) return false
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false
}

// 检查用户是否可以访问指定路由
export function canAccessRoute(userRole, routePath) {
  if (!userRole || !routePath) return false
  
  // 精确匹配
  if (ROUTE_PERMISSIONS[routePath]) {
    return ROUTE_PERMISSIONS[routePath].includes(userRole)
  }
  
  // 模糊匹配（针对带参数的路由，如 /app/cases/edit/123）
  for (const route in ROUTE_PERMISSIONS) {
    if (routePath.startsWith(route) && ROUTE_PERMISSIONS[route].includes(userRole)) {
      return true
    }
  }
  
  return false
}

// 检查用户是否可以访问特定资源（根据资源所有者ID）
export function canAccessResource(currentUserId, resourceOwnerId, userRole) {
  // 如果是当前用户自己的资源，允许访问
  if (currentUserId === resourceOwnerId) {
    return true
  }
  
  // 如果用户是管理员，允许访问任何资源
  if (userRole === 'ADMIN') {
    return true
  }
  
  // 如果用户是审核人员，且具有查看所有病例的权限，允许访问
  if (userRole === 'REVIEWER' && hasPermission(userRole, PERMISSIONS.VIEW_ALL_CASES)) {
    return true
  }
  
  // 其他情况不允许访问
  return false
} 