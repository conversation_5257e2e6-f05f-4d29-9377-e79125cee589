# 血管瘤辅助系统部署指南 - 阿里云服务器

本文档提供将血管瘤辅助系统从本地环境迁移到阿里云服务器的详细指南，包括所有必要的端口配置和环境设置。

## 端口配置信息

系统使用以下端口，在阿里云服务器上需要确保这些端口开放并可访问：

| 服务 | 端口 | 说明 |
|------|------|------|
| 后端服务 (Spring Boot) | 8085 | 主要API服务端口 |
| 前端服务 (Vue.js) | 8080 | 开发环境端口，生产环境可使用Nginx代理 |
| MySQL数据库 | 3306 | 数据库服务端口 |
| MySQL X Protocol | 33060 | MySQL额外服务端口 |

## 1. 后端服务配置

### 1.1 配置文件修改

在部署到阿里云服务器时，需要修改`src/main/resources/application.properties`文件中的以下配置：

```properties
# 应用服务端口 - 保持不变或根据需要调整
server.port=8085

# 数据库连接配置 - 修改为阿里云服务器上的MySQL配置
spring.datasource.url=jdbc:mysql://localhost:3306/medical_annotations?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&characterEncoding=UTF-8
spring.datasource.username=root
spring.datasource.password=<新密码>  # 修改为安全的数据库密码

# 上传文件配置 - 确保路径在阿里云服务器上存在且有写权限
app.upload.dir=/path/to/medical_images  # 修改为阿里云服务器上的绝对路径
```

### 1.2 部署步骤

1. 在阿里云服务器上安装JDK 8或更高版本
2. 使用Maven打包应用：`mvn clean package`
3. 将生成的JAR文件上传到阿里云服务器
4. 创建上传目录：`mkdir -p /path/to/medical_images`
5. 启动应用：`java -jar medical-annotation-system.jar`

## 2. 前端服务配置

### 2.1 配置文件修改

在部署到阿里云服务器时，需要修改`frontend/vue.config.js`文件中的代理配置：

```javascript
devServer: {
  port: 8080,
  proxy: {
    '/api': {
      target: 'http://localhost:8085/medical',  // 修改为阿里云服务器IP或域名
      changeOrigin: true,
      pathRewrite: {
        '^/api': '/api'
      }
    },
    '/medical': {
      target: 'http://localhost:8085',  // 修改为阿里云服务器IP或域名
      changeOrigin: true
    }
  }
}
```

### 2.2 生产环境部署

对于生产环境，建议使用Nginx来托管前端静态文件并配置代理：

1. 构建前端项目：`npm run build`
2. 将生成的`dist`目录内容上传到阿里云服务器
3. 配置Nginx：

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名

    # 前端静态文件
    location / {
        root /path/to/dist;
        index index.html;
        try_files $uri $uri/ /index.html;  # 支持Vue路由
    }

    # API代理
    location /medical/ {
        proxy_pass http://localhost:8085/medical/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 3. 数据库配置

### 3.1 MySQL安装与配置

1. 在阿里云服务器上安装MySQL：
   ```
   sudo apt update
   sudo apt install mysql-server
   ```

2. 配置MySQL安全设置：
   ```
   sudo mysql_secure_installation
   ```

3. 创建数据库和用户：
   ```sql
   CREATE DATABASE medical_annotations CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'medical_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON medical_annotations.* TO 'medical_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. 导入数据库架构和数据：
   ```
   mysql -u medical_user -p medical_annotations < database_dump.sql
   ```

### 3.2 数据库表结构详解

以下是系统中所有数据库表的详细创建语句和字段说明：

#### 3.2.1 用户表 (users)

```sql
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL COMMENT '用户姓名',
  email VARCHAR(100) NOT NULL UNIQUE COMMENT '用户邮箱，作为登录账号',
  password VARCHAR(255) NOT NULL COMMENT '用户密码（加密存储）',
  role ENUM('DOCTOR', 'REVIEWER', 'ADMIN') DEFAULT 'DOCTOR' COMMENT '用户角色：医生、审核员、管理员',
  department VARCHAR(100) COMMENT '所属科室',
  hospital VARCHAR(100) NOT NULL COMMENT '医院，用于控制审核员访问权限',
  reset_password_token VARCHAR(255) COMMENT '重置密码令牌',
  reset_password_expire DATETIME COMMENT '重置密码令牌过期时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  team_id INT NULL COMMENT '所属团队ID',
  reviewer_application_status VARCHAR(20) NULL COMMENT '审核员申请状态',
  reviewer_application_reason TEXT NULL COMMENT '审核员申请理由',
  reviewer_application_date DATETIME NULL COMMENT '审核员申请日期',
  reviewer_application_processed_by INT NULL COMMENT '审核员申请处理人ID',
  reviewer_application_processed_date DATETIME NULL COMMENT '审核员申请处理日期',
  FOREIGN KEY (team_id) REFERENCES teams(id),
  FOREIGN KEY (reviewer_application_processed_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### 3.2.2 图像元数据表 (image_metadata)

```sql
CREATE TABLE image_metadata (
  id INT AUTO_INCREMENT PRIMARY KEY,
  filename VARCHAR(255) NOT NULL COMMENT '文件名',
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  path MEDIUMTEXT NOT NULL COMMENT '文件路径',
  mimetype VARCHAR(100) NOT NULL COMMENT '文件类型',
  size INT NOT NULL COMMENT '文件大小',
  width INT COMMENT '图像宽度',
  height INT COMMENT '图像高度',
  uploaded_by INT NOT NULL COMMENT '上传者ID',
  patient_name VARCHAR(100) NULL COMMENT '患者姓名',
  patient_age INT NULL COMMENT '患者年龄',
  patient_gender VARCHAR(10) NULL COMMENT '患者性别',
  case_number VARCHAR(50) NULL UNIQUE COMMENT '病例编号',
  lesion_location VARCHAR(255) NULL COMMENT '病变位置',
  lesion_size VARCHAR(100) NULL COMMENT '病变大小',
  lesion_color VARCHAR(50) NULL COMMENT '病变颜色',
  border_clarity VARCHAR(50) NULL COMMENT '边界清晰度',
  blood_flow VARCHAR(50) NULL COMMENT '血流情况',
  diagnosis_category VARCHAR(100) NULL COMMENT '诊断类别',
  disease_stage VARCHAR(50) NULL COMMENT '疾病阶段',
  morphological_features TEXT NULL COMMENT '形态特征',
  symptoms TEXT NULL COMMENT '症状',
  symptom_details TEXT NULL COMMENT '症状详情',
  complications TEXT NULL COMMENT '并发症',
  complication_details TEXT NULL COMMENT '并发症详情',
  diagnosis_icd_code VARCHAR(50) NULL COMMENT '诊断ICD编码',
  treatment_priority VARCHAR(50) NULL COMMENT '治疗优先级',
  recommended_treatment TEXT NULL COMMENT '推荐治疗',
  treatment_plan TEXT NULL COMMENT '治疗计划',
  contraindications TEXT NULL COMMENT '禁忌症',
  follow_up_schedule VARCHAR(100) NULL COMMENT '随访计划',
  prognosis_rating INT NULL COMMENT '预后评级',
  patient_education TEXT NULL COMMENT '患者教育',
  acquisition_date DATE NULL COMMENT '采集日期',
  modality VARCHAR(50) NULL COMMENT '成像模态',
  study_description TEXT NULL COMMENT '研究描述',
  status ENUM('draft', 'submitted', 'reviewed', 'approved', 'rejected') DEFAULT 'draft' COMMENT '标注状态',
  reviewed_by INT NULL COMMENT '审核者ID',
  review_notes TEXT NULL COMMENT '审核意见',
  review_date TIMESTAMP NULL COMMENT '审核时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  team_id INT NULL COMMENT '所属团队ID',
  submitted_at DATETIME NULL COMMENT '提交时间',
  submitted_by INT NULL COMMENT '提交人ID',
  image_two_path MEDIUMTEXT COMMENT '标注后的图像路径',
  FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (team_id) REFERENCES teams(id),
  FOREIGN KEY (submitted_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### 3.2.3 标签表 (tags)

```sql
CREATE TABLE tags (
  id INT AUTO_INCREMENT PRIMARY KEY,
  metadata_id INT NOT NULL COMMENT '关联到image_metadata表的ID',
  tag VARCHAR(50) NOT NULL COMMENT '标签内容',
  x DOUBLE NOT NULL COMMENT '归一化后的中心点X坐标',
  y DOUBLE NOT NULL COMMENT '归一化后的中心点Y坐标',
  width DOUBLE NOT NULL COMMENT '归一化后的宽度',
  height DOUBLE NOT NULL COMMENT '归一化后的高度',
  created_by INT COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (metadata_id) REFERENCES image_metadata(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3.2.4 图像对表 (image_pairs)

```sql
CREATE TABLE image_pairs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  metadata_id INT NOT NULL COMMENT '关联到image_metadata表的ID',
  image_one_path MEDIUMTEXT NOT NULL COMMENT '第一张图片路径（处理后的图像路径，temp目录）',
  image_two_path MEDIUMTEXT COMMENT '第二张图片路径（标注后的图像路径，annotate目录）',
  description TEXT COMMENT '图像对描述',
  created_by INT COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (metadata_id) REFERENCES image_metadata(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3.2.5 团队表 (teams)

```sql
CREATE TABLE teams (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL COMMENT '团队名称',
  description TEXT COMMENT '团队描述',
  created_by INT NOT NULL COMMENT '创建者ID',
  created_at DATETIME NOT NULL COMMENT '创建时间',
  updated_at DATETIME COMMENT '更新时间',
  FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3.2.6 审核医生申请表 (reviewer_applications)

```sql
CREATE TABLE reviewer_applications (
  id INT NOT NULL AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '申请用户ID',
  reason TEXT NOT NULL COMMENT '申请理由',
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING, APPROVED, REJECTED',
  processed_by INT NULL COMMENT '处理人ID',
  processed_at DATETIME NULL COMMENT '处理时间',
  remarks TEXT NULL COMMENT '处理备注',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  CONSTRAINT fk_reviewer_applications_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT fk_reviewer_applications_processor FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核医生申请表';
```

#### 3.2.7 角色变更审计日志表 (role_change_logs)

```sql
CREATE TABLE role_change_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  old_role VARCHAR(20) NULL COMMENT '原角色',
  new_role VARCHAR(20) NOT NULL COMMENT '新角色',
  changed_by INT NOT NULL COMMENT '操作人ID',
  reason TEXT COMMENT '变更原因',
  changed_at DATETIME NOT NULL COMMENT '变更时间',
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (changed_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3.2.8 团队成员变更审计日志表 (team_member_logs)

```sql
CREATE TABLE team_member_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  team_id INT NOT NULL COMMENT '团队ID',
  action ENUM('ADD', 'REMOVE') NOT NULL COMMENT '操作类型：添加或移除',
  performed_by INT NOT NULL COMMENT '操作人ID',
  performed_at DATETIME NOT NULL COMMENT '操作时间',
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (team_id) REFERENCES teams(id),
  FOREIGN KEY (performed_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3.3 数据库初始化脚本

系统包含以下数据库初始化脚本，需要按顺序执行：

1. `create_database.sql` - 创建基本数据库结构
2. `full_medical_database.sql` - 完整的数据库架构
3. `update_database_structure.sql` - 数据库结构更新脚本
4. `update_database_index.sql` - 数据库索引优化脚本

执行方法：
```bash
# 连接到MySQL
mysql -u medical_user -p

# 在MySQL命令行中执行
USE medical_annotations;
SOURCE /path/to/create_database.sql;
SOURCE /path/to/full_medical_database.sql;
SOURCE /path/to/update_database_structure.sql;
SOURCE /path/to/update_database_index.sql;
```

此外，系统使用`DatabaseInitializer.java`在应用启动时自动检查和初始化必要的数据库结构。确保应用有足够的数据库权限来执行这些操作。

### 3.4 数据库关系图

```
┌─────────────┐       ┌───────────────────┐       ┌──────────────────┐
│    users    │       │  image_metadata   │       │       tags       │
├─────────────┤       ├───────────────────┤       ├──────────────────┤
│ id          │       │ id                │       │ id               │
│ name        │       │ filename          │       │ metadata_id      │◄─┐
│ email       │       │ path              │       │ tag              │  │
│ password    │       │ uploaded_by       │◄──┐   │ x                │  │
│ role        │       │ patient_name      │   │   │ y                │  │
│ department  │       │ diagnosis_category│   │   │ width            │  │
│ hospital    │       │ status            │   │   │ height           │  │
│ team_id     │◄────┐ │ reviewed_by       │◄──┼───┤ created_by       │◄─┼──┐
└─────────────┘     │ │ team_id           │◄──┼───┤ created_at       │  │  │
                    │ └───────────────────┘   │   └──────────────────┘  │  │
                    │                         │                         │  │
┌─────────────┐     │                         │   ┌──────────────────┐  │  │
│    teams    │     │                         │   │   image_pairs    │  │  │
├─────────────┤     │                         │   ├──────────────────┤  │  │
│ id          │◄────┘                         │   │ id               │  │  │
│ name        │                               │   │ metadata_id      │◄─┘  │
│ description │                               │   │ image_one_path   │     │
│ created_by  │◄────────────────────────────────┐ │ image_two_path   │     │
│ created_at  │                               │ │ │ description      │     │
│ updated_at  │                               │ │ │ created_by       │◄────┘
└─────────────┘                               │ │ │ created_at       │
                                              │ │ └──────────────────┘
┌────────────────────────┐                    │ │
│  reviewer_applications │                    │ │
├────────────────────────┤                    │ │
│ id                     │                    │ │
│ user_id                │◄────────────────────┘ │
│ reason                 │                      │
│ status                 │                      │
│ processed_by           │◄──────────────────────┘
│ processed_at           │
│ remarks                │
│ created_at             │
└────────────────────────┘
```

### 3.5 MySQL安全配置

确保MySQL只允许必要的连接：

1. 编辑MySQL配置文件：`sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf`
2. 设置绑定地址：`bind-address = 127.0.0.1`（仅本地访问）或指定IP
3. 重启MySQL服务：`sudo systemctl restart mysql`

## 4. 阿里云安全组配置

在阿里云控制台中，为您的ECS实例配置安全组规则：

1. 对外开放端口：
   - 80/443 (HTTP/HTTPS) - 用于Web访问
   - 22 (SSH) - 用于远程管理

2. 内部端口（仅在需要外部直接访问时开放）：
   - 8085 (后端API) - 建议通过Nginx代理访问，不直接开放
   - 3306 (MySQL) - 强烈建议不要直接开放，使用SSH隧道或VPN访问

## 5. 系统维护

### 5.1 日志管理

配置日志轮转以避免磁盘空间耗尽：

```
sudo nano /etc/logrotate.d/medical-system
```

添加配置：

```
/path/to/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 user group
}
```

### 5.2 系统监控

使用阿里云监控服务或安装Prometheus + Grafana来监控系统运行状态。

### 5.3 自动启动

创建systemd服务以确保系统重启后自动启动：

```
sudo nano /etc/systemd/system/medical-system.service
```

添加配置：

```
[Unit]
Description=Blood Vessel Tumor Annotation System
After=network.target mysql.service

[Service]
User=your-user
WorkingDirectory=/path/to/application
ExecStart=/usr/bin/java -jar medical-annotation-system.jar
SuccessExitStatus=143
TimeoutStopSec=10
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启用服务：

```
sudo systemctl enable medical-system.service
sudo systemctl start medical-system.service
```

## 6. 备份策略

### 6.1 数据库备份

设置每日自动备份：

```
crontab -e
```

添加定时任务：

```
0 2 * * * mysqldump -u medical_user -p'secure_password' medical_annotations > /path/to/backups/medical_db_$(date +\%Y\%m\%d).sql
0 3 * * * find /path/to/backups/ -name "medical_db_*.sql" -type f -mtime +7 -delete
```

### 6.2 上传文件备份

设置定期备份上传的医学图像：

```
0 4 * * * tar -czf /path/to/backups/medical_images_$(date +\%Y\%m\%d).tar.gz /path/to/medical_images
0 5 * * * find /path/to/backups/ -name "medical_images_*.tar.gz" -type f -mtime +7 -delete
```

## 7. 故障排除

### 7.1 常见问题

1. 连接数据库失败：
   - 检查MySQL服务是否运行：`systemctl status mysql`
   - 验证数据库凭据是否正确
   - 检查数据库用户权限

2. 图像上传失败：
   - 检查上传目录权限：`ls -la /path/to/medical_images`
   - 确保应用有写入权限：`chown -R your-user:your-group /path/to/medical_images`

3. 前端无法连接后端：
   - 检查Nginx配置是否正确
   - 验证后端服务是否运行：`systemctl status medical-system`
   - 检查防火墙规则：`sudo ufw status`

### 7.2 日志查看

- 应用日志：`tail -f /path/to/application.log`
- Nginx日志：`tail -f /var/log/nginx/access.log`
- MySQL日志：`tail -f /var/log/mysql/error.log`

## 联系信息

如有部署问题，请联系系统管理员。 