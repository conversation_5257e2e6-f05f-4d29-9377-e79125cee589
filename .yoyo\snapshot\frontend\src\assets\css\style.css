/* 自定义全局样式 */
.page-container {
  padding: 20px 0;
}

/* Element Plus风格的卡片 */
.card {
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-radius: 4px;
  background: #fff;
  padding: 20px;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 表单样式 */
.form-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 图像容器样式 */
.image-container {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  margin-bottom: 15px;
}

.image-container img {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

/* 禁用标注界面的图片缩放效果 */
.annotation-area .image-container img,
.annotation-area .image-wrapper .image-container img,
.annotation-workspace .image-container img {
  transition: none;
}

.annotation-area .image-container:hover img,
.annotation-area .image-wrapper .image-container:hover img,
.annotation-workspace .image-container:hover img {
  transform: none;
}

/* 对其他非标注界面的图片保留悬停放大效果 */
.image-container:hover img {
  transform: scale(1.05);
}

/* 加载指示器 */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 图像标注区域 */
.annotation-area {
  border: 1px solid #ebeef5;
  padding: 15px;
  border-radius: 5px;
  background-color: #f8f9fa;
}

/* 错误提示 */
.error-text {
  color: #f56c6c;
}

/* 替代Bootstrap的常用工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-center {
  justify-content: center;
}

.align-items-center {
  align-items: center;
}

.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 16px; }
.mt-4 { margin-top: 24px; }
.mt-5 { margin-top: 32px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 16px; }
.mb-4 { margin-bottom: 24px; }
.mb-5 { margin-bottom: 32px; }

.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 16px; }
.ml-4 { margin-left: 24px; }
.ml-5 { margin-left: 32px; }

.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 16px; }
.mr-4 { margin-right: 24px; }
.mr-5 { margin-right: 32px; }

.my-1 { margin-top: 4px; margin-bottom: 4px; }
.my-2 { margin-top: 8px; margin-bottom: 8px; }
.my-3 { margin-top: 16px; margin-bottom: 16px; }
.my-4 { margin-top: 24px; margin-bottom: 24px; }
.my-5 { margin-top: 32px; margin-bottom: 32px; }

.mx-1 { margin-left: 4px; margin-right: 4px; }
.mx-2 { margin-left: 8px; margin-right: 8px; }
.mx-3 { margin-left: 16px; margin-right: 16px; }
.mx-4 { margin-left: 24px; margin-right: 24px; }
.mx-5 { margin-left: 32px; margin-right: 32px; }

.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 16px; }
.p-4 { padding: 24px; }
.p-5 { padding: 32px; }

.py-1 { padding-top: 4px; padding-bottom: 4px; }
.py-2 { padding-top: 8px; padding-bottom: 8px; }
.py-3 { padding-top: 16px; padding-bottom: 16px; }
.py-4 { padding-top: 24px; padding-bottom: 24px; }
.py-5 { padding-top: 32px; padding-bottom: 32px; }

.px-1 { padding-left: 4px; padding-right: 4px; }
.px-2 { padding-left: 8px; padding-right: 8px; }
.px-3 { padding-left: 16px; padding-right: 16px; }
.px-4 { padding-left: 24px; padding-right: 24px; }
.px-5 { padding-left: 32px; padding-right: 32px; }

/* 图标替代 */
.icon-sm {
  font-size: 14px;
}

.icon-md {
  font-size: 18px;
}

.icon-lg {
  font-size: 24px;
} 