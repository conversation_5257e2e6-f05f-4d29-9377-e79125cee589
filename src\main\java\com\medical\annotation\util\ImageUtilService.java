package com.medical.annotation.util;

import com.medical.annotation.model.ImagePair;
import com.medical.annotation.model.Tag;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.service.FileService;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Arrays;

/**
 * 统一的图像处理工具类
 * 整合了ImageUtil和ImagePathUtil的功能
 */
@Component
public class ImageUtilService {

    @Autowired
    private FileService fileService;
    
    /**
     * 在图像上绘制标注框
     * @param imagePath 原始图像路径
     * @param x 标注框左上角X坐标
     * @param y 标注框左上角Y坐标
     * @param width 标注框宽度
     * @param height 标注框高度
     * @param label 标注标签
     * @return 带标注的图像
     * @throws IOException 如果图像处理失败
     */
    public BufferedImage drawAnnotation(String imagePath, int x, int y, int width, int height, String label) throws IOException {
        // 读取原始图像
        BufferedImage originalImage = ImageIO.read(new File(imagePath));
        
        // 创建图形上下文
        Graphics2D g = originalImage.createGraphics();
        
        // 开启抗锯齿
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // 设置绘制属性
        g.setColor(Color.RED);
        g.setStroke(new BasicStroke(2));
        
        // 绘制矩形框
        g.drawRect(x, y, width, height);
        
        // 绘制标签
        if (label != null && !label.isEmpty()) {
            g.setFont(new Font("SimHei", Font.PLAIN, 16));
            g.drawString(label, x, y - 5);
        }
        
        g.dispose();
        
        return originalImage;
    }
    
    /**
     * 在图像上绘制标注框
     * 
     * @param originalImage 原始图像
     * @param tags 标注列表
     * @return 绘制好标注框的图像
     */
    public BufferedImage drawAnnotations(BufferedImage originalImage, List<Tag> tags) {
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();
        
        // 创建新的图像，使用原始尺寸
        BufferedImage annotatedImage = new BufferedImage(
                width, height, BufferedImage.TYPE_INT_RGB);
        
        // 复制原始图像
        Graphics2D g2d = annotatedImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);
        
        // 开启抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // 设置绘图属性
        g2d.setColor(Color.RED);
        g2d.setStroke(new BasicStroke(2.0f));
        g2d.setFont(new Font("SimHei", Font.PLAIN, 16));
        
        // 为每个标注绘制边界框
        for (Tag tag : tags) {
            if (tag.getX() == null || tag.getY() == null || 
                tag.getWidth() == null || tag.getHeight() == null) {
                continue; // 跳过无效标注
            }
            
            // 获取归一化坐标
            double normalizedX = tag.getX();
            double normalizedY = tag.getY();
            double normalizedWidth = tag.getWidth();
            double normalizedHeight = tag.getHeight();
            String label = tag.getTagName() != null ? tag.getTagName() : "标注";
            
            // 转换为像素坐标 - 归一化坐标表示矩形中心点坐标和宽高比例
            // 计算左上角坐标
            int x = (int) ((normalizedX - normalizedWidth/2) * width);
            int y = (int) ((normalizedY - normalizedHeight/2) * height);
            int w = (int) (normalizedWidth * width);
            int h = (int) (normalizedHeight * height);
            
            // 绘制矩形框
            g2d.drawRect(x, y, w, h);
            
            // 绘制标签
            g2d.drawString(label, x, y - 5);
        }
        
        g2d.dispose();
        return annotatedImage;
    }
    
    /**
     * 生成标注图像路径
     * @param originalPath 原始图像路径
     * @return 标注图像路径
     */
    public String generateAnnotatedImagePath(String originalPath) {
        if (originalPath == null) {
            return null;
        }
        
        String processedDir = fileService.getProcessedDirectoryPath();
        
        // 从原始路径中提取文件名
        String filename = new File(originalPath).getName();
        
        // 构建新的处理后文件路径
        return new File(processedDir, filename).getPath();
    }
    
    /**
     * 转换路径为Web可用路径
     * @param path 物理路径
     * @return Web路径
     */
    public String convertToWebPath(String path) {
        if (path == null) {
            return null;
        }
        
        String tempDir = fileService.getTempDirectoryPath();
        String processedDir = fileService.getProcessedDirectoryPath();
        String originalDir = fileService.getOriginalDirectoryPath();
        
        // 规范化路径分隔符
        path = path.replace("\\", "/");
        tempDir = tempDir.replace("\\", "/");
        processedDir = processedDir.replace("\\", "/");
        originalDir = originalDir.replace("\\", "/");
        
        // Web路径前缀
        final String WEB_TEMP_PREFIX = "/medical/images/temp/";
        final String WEB_PROCESSED_PREFIX = "/medical/images/processed/";
        final String WEB_ORIGINAL_PREFIX = "/medical/images/original/";
        
        if (path.startsWith(tempDir)) {
            return WEB_TEMP_PREFIX + path.substring(tempDir.length());
        }
        if (path.startsWith(processedDir)) {
            return WEB_PROCESSED_PREFIX + path.substring(processedDir.length());
        }
        if (path.startsWith(originalDir)) {
             return WEB_ORIGINAL_PREFIX + path.substring(originalDir.length());
        }
        
        // 如果已经是Web路径，直接返回
        if (path.startsWith("/medical/images/")) {
            return path;
        }
        
        return path;
    }
    
    /**
     * 缩放图像
     * @param originalImage 原始图像
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 缩放后的图像
     */
    public BufferedImage resizeImage(BufferedImage originalImage, int maxWidth, int maxHeight) {
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        
        // 如果图像已经比最大尺寸小，直接返回原图
        if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
            return originalImage;
        }
        
        // 计算缩放比例
        double widthRatio = (double) maxWidth / originalWidth;
        double heightRatio = (double) maxHeight / originalHeight;
        double ratio = Math.min(widthRatio, heightRatio);
        
        // 计算新尺寸
        int newWidth = (int) (originalWidth * ratio);
        int newHeight = (int) (originalHeight * ratio);
        
        // 创建缩放后的图像
        BufferedImage resizedImage = new BufferedImage(
                newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        
        // 绘制缩放后的图像
        Graphics2D g = resizedImage.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g.dispose();
        
        return resizedImage;
    }
    
    /**
     * 保存图像到文件
     * @param image 图像
     * @param outputPath 输出路径
     * @param formatName 格式名称
     * @return 是否保存成功
     */
    public boolean saveImage(BufferedImage image, String outputPath, String formatName) {
        try {
            File outputFile = new File(outputPath);
            
            // 确保目录存在
            outputFile.getParentFile().mkdirs();
            
            // 保存图像
            ImageIO.write(image, formatName, outputFile);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 从路径中提取文件名
     * @param path 路径
     * @return 文件名
     */
    public String getFilenameFromPath(String path) {
        if (path == null || path.isEmpty()) {
            return "";
        }
        
        // 替换不同的路径分隔符为统一格式
        path = path.replace('\\', '/');
        
        // 从路径中提取文件名
        int lastIndex = path.lastIndexOf('/');
        if (lastIndex >= 0 && lastIndex < path.length() - 1) {
            return path.substring(lastIndex + 1);
        }
        
        return path;
    }
} 