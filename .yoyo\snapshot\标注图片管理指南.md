# 血管瘤辅助标注系统 - 图片管理与问题排查指南

## 一、系统结构

系统中图片存储在以下几个目录中：

1. **原始图片目录**：`medical_images/original` - 存储上传的原始图片
2. **临时处理目录**：`medical_images/temp` - 存储处理中的临时图片
3. **标注后图片目录**：`medical_images/processed` - 存储带有标注框的最终图片
4. **标注图片目录**：`medical_images/annotate` - 存储特殊用途的标注图片

## 二、图片处理流程

1. **上传阶段**：
   - 原始图片保存到 `original` 目录
   - 处理后的图片（调整大小等）保存到 `temp` 目录

2. **标注阶段**：
   - 显示来自 `temp` 目录的图片
   - 标注保存后，绘制标注框的图片保存到 `processed` 目录
   - 数据库中记录标注图片路径

3. **重新标注**：
   - 返回上一步时，删除旧的标注图片并清空数据库路径
   - 重新保存标注时，创建新的标注图片并更新数据库路径

## 三、常见问题及解决方法

### 1. 重复图片问题

**表现**：多次标注同一图片导致存储大量相似图片

**解决方法**：
- 使用提供的清理脚本删除重复图片：`clean_duplicates.ps1`
- 脚本按照图片ID分组，只保留每组最新的标注图片

### 2. 标注图片删除失败

**表现**：数据库中清除了路径但实际文件未被删除

**原因与解决**：
- 路径格式不匹配：修改`FileService.deleteFileByWebPath`支持多种路径格式
- 文件访问权限问题：检查目录权限，确保系统有写入/删除权限
- 文件被占用：关闭可能使用该文件的应用程序

### 3. 数据库与实际文件不一致

**表现**：数据库中记录的图片文件实际不存在

**解决方法**：
- 使用`check_missing_files.java`工具检测并修正数据库中的不一致记录
- 执行命令：`./mvnw spring-boot:run -Dspring-boot.run.profiles=check-files`

## 四、日常维护建议

1. **定期清理**：
   - 每周运行一次`clean_duplicates.ps1`脚本清理重复图片
   - 每月检查一次数据库与文件的一致性

2. **存储空间监控**：
   - 定期检查`medical_images`目录大小
   - 当空间超过80%时考虑扩容或清理旧数据

3. **日志管理**：
   - 系统日志保存在`logs`目录
   - API请求日志包含所有图片操作记录
   - 每月归档一次日志文件

## 五、故障排查步骤

1. **图片上传失败**：
   - 检查目录权限
   - 验证存储空间是否充足
   - 检查日志中的具体错误信息

2. **图片无法删除**：
   - 检查数据库中的路径格式是否正确
   - 验证文件是否被其他程序占用
   - 尝试手动删除文件验证权限

3. **图片无法显示**：
   - 检查数据库中路径是否正确
   - 验证文件是否实际存在
   - 检查前端请求路径是否正确（是否使用了正确的context-path）

## 六、技术联系人

如遇到无法解决的问题，请联系系统管理员或开发人员。 