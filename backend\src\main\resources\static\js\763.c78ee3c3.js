"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[763],{59763:(e,t,s)=>{s.r(t),s.d(t,{default:()=>ee});var i=s(61431),a={class:"container"},n={class:"diagnosis-panel"},l={key:0,class:"form-content"},o={class:"adaptive-fields-container"},r={class:"field-item"},d={class:"form-field"},u={class:"field-item"},c={class:"form-field"},g={class:"field-item"},p={class:"form-field"},m={class:"field-item"},f={class:"form-field"},v={class:"form-group"},k={class:"form-field"},h={class:"upload-area"},b={key:0,class:"upload-placeholder"},R={key:1,class:"image-preview-container"},y=["src"],_={class:"preview-overlay"},L={class:"form-buttons"},C={key:1,class:"diagnosis-results"},F={class:"result-header"},D={key:0,style:{color:"#F56C6C",padding:"6px 12px"}},I={key:1,style:{color:"#67C23A",padding:"6px 12px"}},w={class:"result-image-container"},P=["src"],E={class:"detection-info"},T={key:0,style:{color:"#F56C6C"}},A={key:1,style:{color:"#67C23A"}},S={class:"diagnosis-recommendations"},X={class:"recommendation-title"},W={key:0,style:{"font-size":"14px",color:"#909399"}},x={key:0,class:"recommendation-section"},U={key:1,class:"recommendation-placeholder"},V={key:1},G={key:2,class:"recommendation-section"},$={key:3,class:"recommendation-placeholder"},O={key:1},M={class:"disclaimer"},Q={class:"result-actions"};function N(e,t,s,N,B,z){var J=(0,i.g2)("el-input"),j=(0,i.g2)("el-radio"),q=(0,i.g2)("el-radio-group"),H=(0,i.g2)("el-option"),K=(0,i.g2)("el-select"),Y=(0,i.g2)("i-ep-upload-filled"),Z=(0,i.g2)("el-icon"),ee=(0,i.g2)("el-button"),te=(0,i.g2)("el-upload"),se=(0,i.g2)("i-ep-loading"),ie=(0,i.g2)("el-descriptions-item"),ae=(0,i.g2)("el-descriptions"),ne=(0,i.g2)("el-divider"),le=(0,i.g2)("el-skeleton"),oe=(0,i.g2)("el-alert"),re=(0,i.g2)("el-card");return(0,i.uX)(),(0,i.CE)("div",a,[t[27]||(t[27]=(0,i.Lk)("h2",{class:"blood-vessel-title"},"血管瘤智能诊断",-1)),(0,i.Lk)("div",n,[t[26]||(t[26]=(0,i.Lk)("div",{class:"panel-heading"},"上传图像进行血管瘤检测",-1)),B.diagnosisResults.processed?(0,i.Q3)("",!0):((0,i.uX)(),(0,i.CE)("div",l,[(0,i.Lk)("div",o,[(0,i.Lk)("div",r,[t[4]||(t[4]=(0,i.Lk)("div",{class:"form-label"},"患者年龄",-1)),(0,i.Lk)("div",d,[(0,i.bF)(J,{modelValue:B.formData.patientAge,"onUpdate:modelValue":t[0]||(t[0]=function(e){return B.formData.patientAge=e}),type:"number",placeholder:"请输入年龄"},null,8,["modelValue"])])]),(0,i.Lk)("div",u,[t[7]||(t[7]=(0,i.Lk)("div",{class:"form-label"},"性别",-1)),(0,i.Lk)("div",c,[(0,i.bF)(q,{modelValue:B.formData.patientGender,"onUpdate:modelValue":t[1]||(t[1]=function(e){return B.formData.patientGender=e})},{default:(0,i.k6)((function(){return[(0,i.bF)(j,{label:"男"},{default:(0,i.k6)((function(){return t[5]||(t[5]=[(0,i.eW)("男")])})),_:1,__:[5]}),(0,i.bF)(j,{label:"女"},{default:(0,i.k6)((function(){return t[6]||(t[6]=[(0,i.eW)("女")])})),_:1,__:[6]})]})),_:1},8,["modelValue"])])]),(0,i.Lk)("div",g,[t[10]||(t[10]=(0,i.Lk)("div",{class:"form-label"},"类型",-1)),(0,i.Lk)("div",p,[(0,i.bF)(q,{modelValue:B.formData.originType,"onUpdate:modelValue":t[2]||(t[2]=function(e){return B.formData.originType=e})},{default:(0,i.k6)((function(){return[(0,i.bF)(j,{label:"先天性"},{default:(0,i.k6)((function(){return t[8]||(t[8]=[(0,i.eW)("先天性")])})),_:1,__:[8]}),(0,i.bF)(j,{label:"后天性"},{default:(0,i.k6)((function(){return t[9]||(t[9]=[(0,i.eW)("后天性")])})),_:1,__:[9]})]})),_:1},8,["modelValue"])])]),(0,i.Lk)("div",m,[t[11]||(t[11]=(0,i.Lk)("div",{class:"form-label"},"血管质地",-1)),(0,i.Lk)("div",f,[(0,i.bF)(K,{modelValue:B.formData.vesselTexture,"onUpdate:modelValue":t[3]||(t[3]=function(e){return B.formData.vesselTexture=e}),placeholder:"请选择血管质地"},{default:(0,i.k6)((function(){return[(0,i.bF)(H,{label:"质软",value:"soft"}),(0,i.bF)(H,{label:"质韧",value:"elastic"}),(0,i.bF)(H,{label:"质硬",value:"hard"}),(0,i.bF)(H,{label:"囊性",value:"cystic"}),(0,i.bF)(H,{label:"可压缩",value:"compressible"})]})),_:1},8,["modelValue"])])])]),(0,i.Lk)("div",v,[t[15]||(t[15]=(0,i.Lk)("div",{class:"form-label"},"上传血管瘤图像",-1)),(0,i.Lk)("div",k,[(0,i.Lk)("div",h,[(0,i.bF)(te,{drag:"",action:"#","auto-upload":!1,"on-change":z.handleFileChange,limit:1,"show-file-list":!1},{default:(0,i.k6)((function(){return[B.imagePreview?((0,i.uX)(),(0,i.CE)("div",R,[(0,i.Lk)("img",{src:B.imagePreview,class:"preview-image",alt:"图像预览"},null,8,y),(0,i.Lk)("div",_,[(0,i.bF)(ee,{type:"primary",size:"small",onClick:(0,i.D$)(z.resetImage,["stop"])},{default:(0,i.k6)((function(){return t[14]||(t[14]=[(0,i.eW)("重新上传")])})),_:1,__:[14]},8,["onClick"])])])):((0,i.uX)(),(0,i.CE)("div",b,[(0,i.bF)(Z,{class:"upload-icon"},{default:(0,i.k6)((function(){return[(0,i.bF)(Y)]})),_:1}),t[12]||(t[12]=(0,i.Lk)("div",null,[(0,i.eW)("拖拽文件到此处或 "),(0,i.Lk)("em",null,"点击上传")],-1)),t[13]||(t[13]=(0,i.Lk)("div",{class:"upload-tip"},"支持JPG、PNG、BMP等常见图像格式，最大文件大小10MB",-1))]))]})),_:1},8,["on-change"])])])]),(0,i.Lk)("div",L,[(0,i.bF)(ee,{type:"primary",disabled:B.isProcessing||!B.selectedFile,onClick:z.processDiagnosis},{default:(0,i.k6)((function(){return[B.isProcessing?((0,i.uX)(),(0,i.Wv)(Z,{key:0},{default:(0,i.k6)((function(){return[(0,i.bF)(se)]})),_:1})):(0,i.Q3)("",!0),(0,i.Lk)("span",null,(0,i.v_)(B.isProcessing?"处理中...":"开始诊断"),1)]})),_:1},8,["disabled","onClick"]),(0,i.bF)(ee,{onClick:z.resetForm},{default:(0,i.k6)((function(){return t[16]||(t[16]=[(0,i.eW)("重置")])})),_:1,__:[16]},8,["onClick"])])])),B.diagnosisResults.processed?((0,i.uX)(),(0,i.CE)("div",C,[(0,i.bF)(re,null,{header:(0,i.k6)((function(){return[(0,i.Lk)("div",F,[t[17]||(t[17]=(0,i.Lk)("span",{class:"result-title"},"血管瘤诊断结果",-1)),B.diagnosisResults.detected?((0,i.uX)(),(0,i.CE)("span",D," 检测到疑似血管瘤 ")):((0,i.uX)(),(0,i.CE)("span",I,"未检测到血管瘤"))])]})),default:(0,i.k6)((function(){return[(0,i.Lk)("div",w,[(0,i.Lk)("img",{src:B.diagnosisResults.resultImage,class:"result-image",alt:"诊断结果图像"},null,8,P)]),(0,i.Lk)("div",E,[(0,i.bF)(ae,{column:2,border:""},{default:(0,i.k6)((function(){return[(0,i.bF)(ie,{label:"检测状态"},{default:(0,i.k6)((function(){return[B.diagnosisResults.detected?((0,i.uX)(),(0,i.CE)("span",T,"已检测到")):((0,i.uX)(),(0,i.CE)("span",A,"未检测到"))]})),_:1}),(0,i.bF)(ie,{label:"置信度"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,i.v_)(Math.round(100*B.diagnosisResults.confidence))+"% ",1)]})),_:1}),B.diagnosisResults.detected?((0,i.uX)(),(0,i.Wv)(ie,{key:0,label:"检测到的类型"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,i.v_)(B.diagnosisResults.detectedType),1)]})),_:1})):(0,i.Q3)("",!0),B.diagnosisResults.predictedColor?((0,i.uX)(),(0,i.Wv)(ie,{key:1,label:"病灶颜色"},{default:(0,i.k6)((function(){return[(0,i.Lk)("span",null,(0,i.v_)(B.diagnosisResults.predictedColor),1)]})),_:1})):(0,i.Q3)("",!0),B.diagnosisResults.predictedPart?((0,i.uX)(),(0,i.Wv)(ie,{key:2,label:"病灶部位"},{default:(0,i.k6)((function(){return[(0,i.Lk)("span",null,(0,i.v_)(B.diagnosisResults.predictedPart),1)]})),_:1})):(0,i.Q3)("",!0)]})),_:1})]),(0,i.Lk)("div",S,[(0,i.bF)(ne,null,{default:(0,i.k6)((function(){return[(0,i.Lk)("div",X,[t[19]||(t[19]=(0,i.Lk)("span",null,"AI诊断建议",-1)),B.diagnosisResults.isLoadingRecommendation?((0,i.uX)(),(0,i.CE)("span",W,t[18]||(t[18]=[(0,i.Lk)("i",{class:"el-icon-loading"},null,-1),(0,i.eW)(" 正在生成详细建议... ")]))):(0,i.Q3)("",!0)])]})),_:1}),B.diagnosisResults.treatmentSuggestion?((0,i.uX)(),(0,i.CE)("div",x,[t[20]||(t[20]=(0,i.Lk)("h3",null,"治疗与注意事项",-1)),(0,i.Lk)("p",null,(0,i.v_)(B.diagnosisResults.treatmentSuggestion),1)])):((0,i.uX)(),(0,i.CE)("div",U,[B.diagnosisResults.isLoadingRecommendation?((0,i.uX)(),(0,i.Wv)(le,{key:0,rows:1,animated:""})):((0,i.uX)(),(0,i.CE)("p",V,"暂无治疗建议"))])),B.diagnosisResults.precautions?((0,i.uX)(),(0,i.CE)("div",G,[t[21]||(t[21]=(0,i.Lk)("h3",null,"注意事项",-1)),(0,i.Lk)("p",null,(0,i.v_)(B.diagnosisResults.precautions),1)])):((0,i.uX)(),(0,i.CE)("div",$,[B.diagnosisResults.isLoadingRecommendation?((0,i.uX)(),(0,i.Wv)(le,{key:0,rows:1,animated:""})):((0,i.uX)(),(0,i.CE)("p",O,"暂无注意事项"))])),(0,i.Lk)("div",M,[(0,i.bF)(oe,{title:"AI生成免责声明",type:"info",description:"本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。","show-icon":"",closable:!1})])]),(0,i.Lk)("div",Q,[(0,i.bF)(ee,{type:"primary",onClick:z.modifyReport},{default:(0,i.k6)((function(){return t[22]||(t[22]=[(0,i.eW)("修改")])})),_:1,__:[22]},8,["onClick"]),(0,i.bF)(ee,{type:"success",onClick:z.saveReport},{default:(0,i.k6)((function(){return t[23]||(t[23]=[(0,i.eW)("保存")])})),_:1,__:[23]},8,["onClick"]),(0,i.bF)(ee,{type:"warning",onClick:z.confirmReport},{default:(0,i.k6)((function(){return t[24]||(t[24]=[(0,i.eW)("确认")])})),_:1,__:[24]},8,["onClick"]),(0,i.bF)(ee,{onClick:z.resetDiagnosis},{default:(0,i.k6)((function(){return t[25]||(t[25]=[(0,i.eW)("返回")])})),_:1,__:[25]},8,["onClick"])])]})),_:1})])):(0,i.Q3)("",!0)])])}var B=s(24059),z=s(698),J=(s(76918),s(74423),s(44114),s(23288),s(62010),s(79432),s(26099),s(38781),s(21699),s(76031),s(48548)),j=s(80142),q=s(72505),H=s.n(q);const K={name:"HemangiomaDiagnosis",components:{"i-ep-upload-filled":J.UploadFilled,"i-ep-loading":J.Loading},data:function(){return{formData:{patientAge:null,patientGender:"男",originType:"先天性",vesselTexture:""},colorOptions:[{value:"褐色",label:"褐色"},{value:"黑色",label:"黑色"},{value:"红色",label:"红色"},{value:"白色",label:"白色"},{value:"正常",label:"正常"},{value:"玫红",label:"玫红"}],partOptions:[{value:"唇部",label:"唇部"},{value:"脑部",label:"脑部"},{value:"颈部",label:"颈部"},{value:"脸部",label:"脸部"},{value:"掌部",label:"掌部"},{value:"手臂",label:"手臂"},{value:"胸部",label:"胸部"},{value:"腹部",label:"腹部"},{value:"腿部",label:"腿部"},{value:"阴部",label:"阴部"},{value:"背部",label:"背部"},{value:"耳部",label:"耳部"},{value:"枕部",label:"枕部"},{value:"眼部",label:"眼部"},{value:"脚底",label:"脚底"},{value:"脚背",label:"脚背"},{value:"肩部",label:"肩部"},{value:"舌部",label:"舌部"},{value:"屁股",label:"屁股"},{value:"口腔",label:"口腔"},{value:"鼻部",label:"鼻部"}],selectedFile:null,imagePreview:null,isProcessing:!1,diagnosisResults:(0,i.Kh)({processed:!1,detected:!1,detectedType:"",confidence:0,resultImage:"",isLoadingRecommendation:!1,treatmentSuggestion:"",precautions:"",emergencyInstructions:"",disclaimer:"",predictedColor:"",predictedPart:""}),isLoadingRecommendation:!1,activeCollapseItems:["1","2"],pollingInterval:null,diagnosisError:"",isEditing:!1}},computed:{confidenceColor:function(){var e=this.diagnosisResults.confidence;return e>.8?"#F56C6C":e>.5?"#E6A23C":"#67C23A"}},created:function(){console.log("[血管瘤诊断] 组件created钩子触发")},mounted:function(){console.log("[血管瘤诊断] 组件mounted钩子触发，DOM已挂载"),document.title="血管瘤智能诊断",this.$nextTick((function(){if(console.log("[血管瘤诊断] 组件完全渲染完成"),window.parent)try{window.parent.postMessage({type:"PAGE_LOADED",page:"hemangioma-diagnosis"},"*")}catch(e){console.error("[血管瘤诊断] 发送页面加载消息失败:",e)}}))},methods:{handleFileChange:function(e){var t=this;if(!e)return this.selectedFile=null,void(this.imagePreview=null);if(e.raw.size>10485760)return j.nk.error("文件大小超过限制(10MB)"),this.selectedFile=null,void(this.imagePreview=null);this.selectedFile=e.raw;var s=new FileReader;s.onload=function(e){t.imagePreview=e.target.result},s.readAsDataURL(e.raw)},processDiagnosis:function(){var e,t=this;if(this.isProcessing=!0,this.diagnosisError="",this.diagnosisResults={processed:!1,detected:!1,confidence:0,detectedType:"",treatmentSuggestion:"",precautions:"",emergencyInstructions:"",resultImage:"",id:null,isLoadingRecommendation:!1,predictedColor:"",predictedPart:""},!this.selectedFile)return this.diagnosisError="请上传血管瘤图像",void(this.isProcessing=!1);try{if(this.$store&&this.$store.getters.getUserId)e=this.$store.getters.getUserId;else{var s=localStorage.getItem("user");if(s){var i=JSON.parse(s);e=i.id,!e&&i.customId&&console.log("警告: 用户没有数字ID，仅有自定义ID:",i.customId)}}}catch(n){console.error("获取用户ID失败:",n)}if(!e)return this.isProcessing=!1,void j.nk.error("未检测到登录用户，请先登录系统");var a=new FormData;a.append("file",this.selectedFile),this.formData.patientAge&&a.append("patient_age",this.formData.patientAge.toString()),this.formData.patientGender&&a.append("gender",this.formData.patientGender),this.formData.originType&&a.append("origin_type",this.formData.originType),this.formData.vesselTexture&&a.append("vessel_texture",this.formData.vesselTexture),a.append("user_id",e.toString()),console.log("发送诊断请求，用户ID:",e),H().post("/medical/api/hemangioma-diagnoses/upload-and-diagnose",a,{headers:{"Content-Type":"multipart/form-data","X-User-ID":e.toString()},timeout:6e4}).then((function(e){console.log("诊断数据上传成功:",e.data),t.diagnosisResults.processed=!0,t.diagnosisResults.id=e.data.id;var s=e.data.detectedType;if(t.diagnosisResults.detected=s&&s.length>0,t.diagnosisResults.confidence=e.data.confidence||0,t.diagnosisResults.detectedType=s||"",e.data.processedImagePath){var i=window.location.origin;t.diagnosisResults.resultImage=i+e.data.processedImagePath}t.diagnosisResults.predictedColor=e.data.color||"",t.diagnosisResults.predictedPart=e.data.bodyPart||"",e.data.treatmentSuggestion?(t.diagnosisResults.treatmentSuggestion=e.data.treatmentSuggestion,t.diagnosisResults.precautions=e.data.precautions||e.data.emergencyInstructions,t.diagnosisResults.emergencyInstructions=e.data.emergencyInstructions):(t.diagnosisResults.isLoadingRecommendation=!0,t.startPolling(e.data.id)),t.isProcessing=!1}))["catch"]((function(e){console.error("诊断处理失败:",e),t.diagnosisError="诊断处理失败，请重试: ".concat(e.message),t.isProcessing=!1}))},startPolling:function(e){var t=this;console.log("开始轮询获取诊断建议，ID:",e),this.pollingInterval=setInterval((function(){t.pollForResults(e)}),3e3),setTimeout((function(){t.pollingInterval&&(clearInterval(t.pollingInterval),t.pollingInterval=null,t.diagnosisResults.isLoadingRecommendation&&(t.diagnosisResults.isLoadingRecommendation=!1,j.nk.info("已获取部分诊断建议，更多详细内容稍后可能会继续更新。")))}),18e4)},pollForResults:function(e){var t=this;H().get("/medical/api/hemangioma-diagnoses/".concat(e)).then((function(e){var s=e.data;console.log("轮询获取诊断结果:",s);s.treatmentSuggestion&&!t.diagnosisResults.treatmentSuggestion&&(t.diagnosisResults.treatmentSuggestion=s.treatmentSuggestion),s.precautions&&!t.diagnosisResults.precautions&&(t.diagnosisResults.precautions=s.precautions),s.treatmentSuggestion&&s.precautions&&t.pollingInterval&&(clearInterval(t.pollingInterval),t.pollingInterval=null,t.diagnosisResults.isLoadingRecommendation=!1)}))["catch"]((function(e){console.error("轮询获取诊断结果失败:",e)}))},backToUpload:function(){this.diagnosisResults.processed=!1},resetForm:function(){this.formData.patientAge="",this.formData.patientGender="",this.formData.originType="",this.formData.vesselTexture="",this.selectedFile=null,this.imagePreview=null},modifyReport:function(){var e=this.diagnosisResults.id;e?this.$router.push({path:"/app/case/".concat(e,"/annotate-and-form"),query:{mode:"edit",from:"diagnosis-page"}}):j.nk.error("无法修改报告：缺少诊断ID")},saveReport:function(){var e=this;return(0,z.A)((0,B.A)().m((function t(){var s,i;return(0,B.A)().w((function(t){while(1)switch(t.n){case 0:if(e.diagnosisResults.id){t.n=1;break}return j.nk.error("诊断ID丢失，无法保存报告"),t.a(2);case 1:return s={patientAge:e.formData.patientAge,gender:e.formData.patientGender,originType:e.formData.originType,vesselTexture:e.formData.vesselTexture,color:e.diagnosisResults.predictedColor?e.diagnosisResults.predictedColor.name:"",bodyPart:e.diagnosisResults.predictedPart?e.diagnosisResults.predictedPart.name:"",reviewNotes:e.diagnosisResults.reviewNotes,treatmentSuggestion:e.diagnosisResults.treatmentSuggestion,precautions:e.diagnosisResults.precautions||e.diagnosisResults.emergencyInstructions,status:"REVIEWED"},t.p=2,t.n=3,H().put("/medical/api/hemangioma-diagnoses/".concat(e.diagnosisResults.id),s);case 3:t.v,e.isEditing=!1,setTimeout((function(){e.$router.push("/app/dashboard")}),1500),t.n=5;break;case 4:t.p=4,i=t.v,console.error("保存报告失败:",i),j.nk.error("保存报告失败，请稍后重试");case 5:return t.a(2)}}),t,null,[[2,4]])})))()},getCurrentUserRole:function(){try{if(this.$store&&this.$store.getters.getUserRole)return this.$store.getters.getUserRole;var e=localStorage.getItem("user");if(e){var t=JSON.parse(e);return t.role||t.userRole}}catch(s){return console.error("获取用户角色失败:",s),null}return null},confirmReport:function(){var e=this,t=this.diagnosisResults.id;if(t){var s=this.getCurrentUserRole();console.log("当前用户角色:",s);var i="SUBMITTED";s&&(s.toLowerCase().includes("admin")||s.toLowerCase().includes("reviewer"))&&(i="APPROVED","已完成"),H().put("/medical/api/hemangioma-diagnoses/".concat(t,"/status"),null,{params:{status:i}}).then((function(t){console.log("更新状态成功:",t.data),e.resetDiagnosis(),e.resetForm()}))["catch"]((function(t){console.error("更新状态失败:",t),j.nk.error("更新状态失败: ".concat(t.message)),e.resetDiagnosis(),e.resetForm()}))}else j.nk.error("无法确认报告：缺少诊断ID")},resetImage:function(e){e&&e.stopPropagation(),this.selectedFile=null,this.imagePreview=null},getVesselTextureLabel:function(e){var t={soft:"质软",elastic:"质韧",hard:"质硬",cystic:"囊性",compressible:"可压缩"};return t[e]||e},resetDiagnosis:function(){this.resetForm(),this.isEditing=!1,Object.assign(this.diagnosisResults,{processed:!1,detected:!1,isLoadingRecommendation:!1,id:null,resultImage:"",detectedType:"",confidence:0,predictedColor:"",predictedPart:"",vesselTexture:"",treatmentSuggestion:"",precautions:"",reviewNotes:""})}}};var Y=s(66262);const Z=(0,Y.A)(K,[["render",N],["__scopeId","data-v-3c2b4d8f"]]),ee=Z}}]);
//# sourceMappingURL=763.c78ee3c3.js.map