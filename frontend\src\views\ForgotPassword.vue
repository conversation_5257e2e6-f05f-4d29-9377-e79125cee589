<template>
  <div class="forgot-password-page forgot-password-background">
    <div class="forgot-password-container">
      <div class="forgot-password-form-container">
        <div class="forgot-password-form-box">
          <div class="forgot-password-tabs">
            <div class="tab-item active">密码找回</div>
          </div>
          
          <div class="forgot-password-form">
            <div v-if="error" class="alert alert-danger">
              {{ error }}
            </div>
            <div v-if="success" class="alert alert-success">
              {{ success }}
            </div>
            
            <!-- 步骤1: 输入邮箱 -->
            <div v-if="step === 1">
              <p class="form-description">请输入您注册时使用的电子邮箱，我们将向该邮箱发送验证码</p>
              <form @submit.prevent="handleSendVerificationCode">
                <div class="form-group">
                  <div class="input-with-icon">
                    <i class="icon-email"></i>
                    <input 
                      id="email" 
                      v-model="email" 
                      type="email" 
                      placeholder="请输入邮箱地址"
                      required
                      :disabled="loading"
                    >
                  </div>
                </div>
                
                <div class="form-actions">
                  <button 
                    type="submit" 
                    class="submit-btn" 
                    :disabled="loading"
                  >
                    <span v-if="loading" class="spinner"></span>
                    <span>{{ loading ? '发送中...' : '发送验证邮件' }}</span>
                  </button>
                </div>
              </form>
            </div>
            
            <!-- 步骤2: 输入验证码 -->
            <div v-if="step === 2">
              <p class="form-description">验证邮件已发送，请查收邮箱并输入验证码</p>
              <form @submit.prevent="handleVerifyCode">
                <div class="form-group">
                  <div class="input-with-icon">
                    <i class="icon-lock"></i>
                    <input 
                      id="verificationCode" 
                      v-model="verificationCode" 
                      type="text" 
                      placeholder="请输入验证码"
                      required
                      :disabled="loading"
                    >
                  </div>
                </div>
                
                <div class="form-actions">
                  <button 
                    type="submit" 
                    class="submit-btn" 
                    :disabled="loading"
                  >
                    <span v-if="loading" class="spinner"></span>
                    <span>{{ loading ? '验证中...' : '验证' }}</span>
                  </button>
                </div>
                
                <div class="resend-code">
                  <span v-if="countdown > 0">{{ countdown }}秒后可重新发送</span>
                  <a href="#" v-else @click.prevent="handleSendVerificationCode">重新发送验证码</a>
                </div>
              </form>
            </div>
            
            <!-- 步骤3: 设置新密码 -->
            <div v-if="step === 3">
              <p class="form-description">请设置新密码</p>
              <form @submit.prevent="handleResetPassword">
                <div class="form-group">
                  <div class="input-with-icon">
                    <i class="icon-lock"></i>
                    <input 
                      id="newPassword" 
                      v-model="newPassword" 
                      type="password" 
                      placeholder="请设置新密码"
                      required
                      :disabled="loading"
                      @blur="validatePassword"
                    >
                  </div>
                  <div v-if="passwordError" class="field-error">{{ passwordError }}</div>
                  <div v-if="passwordStrength && !passwordError" class="password-strength">
                    <div class="strength-label">密码强度：</div>
                    <div class="strength-indicator">
                      <div 
                        class="strength-bar" 
                        :class="{
                          'weak': passwordStrength === 'weak',
                          'medium': passwordStrength === 'medium',
                          'strong': passwordStrength === 'strong'
                        }"
                      ></div>
                    </div>
                    <div class="strength-text" :class="passwordStrength">
                      {{ 
                        passwordStrength === 'weak' ? '弱' : 
                        passwordStrength === 'medium' ? '中' : 
                        passwordStrength === 'strong' ? '强' : ''
                      }}
                    </div>
                  </div>
                  <div class="password-hint">
                    密码长度至少8位，必须包含大写字母、小写字母、数字和特殊符号中的至少三种
                  </div>
                </div>
                
                <div class="form-group">
                  <div class="input-with-icon">
                    <i class="icon-lock"></i>
                    <input 
                      id="confirmPassword" 
                      v-model="confirmPassword" 
                      type="password" 
                      placeholder="请确认新密码"
                      required
                      :disabled="loading"
                    >
                  </div>
                </div>
                
                <div class="form-actions">
                  <button 
                    type="submit" 
                    class="submit-btn" 
                    :disabled="loading || !!passwordError"
                  >
                    <span v-if="loading" class="spinner"></span>
                    <span>{{ loading ? '重置中...' : '重置密码' }}</span>
                  </button>
                </div>
              </form>
            </div>
            
            <div class="login-link">
              <span>记起密码了?</span>
              <router-link to="/login" class="login-btn">返回登录</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import api from '@/utils/api'

export default {
  name: 'ForgotPassword',
  setup() {
    const router = useRouter()
    
    // 表单数据
    const email = ref('')
    const verificationCode = ref('')
    const newPassword = ref('')
    const confirmPassword = ref('')
    
    // 状态管理
    const loading = ref(false)
    const error = ref('')
    const success = ref('')
    const step = ref(1)
    const countdown = ref(0)
    let countdownTimer = null
    const passwordError = ref('')
    const passwordStrength = ref('')
    
    // 清理定时器
    onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer)
      }
    })
    
    // 开始倒计时
    const startCountdown = () => {
      countdown.value = 60
      countdownTimer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(countdownTimer)
        }
      }, 1000)
    }
    
    // 密码强度验证
    const validatePassword = () => {
      const pwd = newPassword.value
      
      // 检查密码长度
      if (pwd.length < 8) {
        passwordError.value = '密码长度必须不少于8位'
        passwordStrength.value = 'weak'
        return false
      }
      
      // 检查密码复杂度
      const hasUpperCase = /[A-Z]/.test(pwd)
      const hasLowerCase = /[a-z]/.test(pwd)
      const hasNumbers = /[0-9]/.test(pwd)
      const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd)
      
      const typesCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length
      
      // 检查常见简单密码
      const commonPasswords = ['123456', 'password', 'abcdef', '12345678', 'qwerty', '111111']
      if (commonPasswords.includes(pwd.toLowerCase())) {
        passwordError.value = '请勿使用常见的简单密码'
        passwordStrength.value = 'weak'
        return false
      }
      
      if (typesCount < 3) {
        passwordError.value = '密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种'
        passwordStrength.value = 'weak'
        return false
      }
      
      // 密码强度评估
      if (typesCount === 3 && pwd.length < 10) {
        passwordStrength.value = 'medium'
      } else if (typesCount === 4 || (typesCount === 3 && pwd.length >= 10)) {
        passwordStrength.value = 'strong'
      }
      
      passwordError.value = ''
      return true
    }
    
    // 监听密码变化
    watch(newPassword, () => {
      if (newPassword.value) {
        validatePassword()
      } else {
        passwordError.value = ''
        passwordStrength.value = ''
      }
    })
    
    // 发送验证码
    const handleSendVerificationCode = async () => {
      if (!email.value) {
        error.value = '请输入邮箱地址'
        return
      }
      
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        await api.users.sendResetPasswordEmail(email.value)
        success.value = '验证邮件已发送，请注意查收'
        step.value = 2
        startCountdown()
      } catch (err) {
        console.error('发送验证码错误:', err)
        error.value = typeof err === 'string' ? err : '发送验证码失败，请检查邮箱是否正确'
      } finally {
        loading.value = false
      }
    }
    
    // 验证验证码
    const handleVerifyCode = async () => {
      if (!verificationCode.value) {
        error.value = '请输入验证码'
        return
      }
      
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        await api.users.verifyResetCode(email.value, verificationCode.value)
        success.value = '验证成功，请设置新密码'
        step.value = 3
      } catch (err) {
        console.error('验证码验证错误:', err)
        error.value = typeof err === 'string' ? err : '验证码错误或已过期'
      } finally {
        loading.value = false
      }
    }
    
    // 重置密码
    const handleResetPassword = async () => {
      if (!newPassword.value) {
        error.value = '请输入新密码'
        return
      }
      
      if (!validatePassword()) {
        return
      }
      
      if (newPassword.value !== confirmPassword.value) {
        error.value = '两次输入的密码不一致'
        return
      }
      
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        await api.users.resetPassword(email.value, verificationCode.value, newPassword.value)
        success.value = '密码重置成功，即将跳转到登录页面'
        
        // 3秒后跳转到登录页
        setTimeout(() => {
          router.push('/login')
        }, 3000)
      } catch (err) {
        console.error('重置密码错误:', err)
        error.value = typeof err === 'string' ? err : '重置密码失败，请重试'
      } finally {
        loading.value = false
      }
    }
    
    return {
      email,
      verificationCode,
      newPassword,
      confirmPassword,
      loading,
      error,
      success,
      step,
      countdown,
      passwordError,
      passwordStrength,
      handleSendVerificationCode,
      handleVerifyCode,
      handleResetPassword,
      validatePassword
    }
  }
}
</script>

<style scoped>
.forgot-password-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  width: 100%;
  overflow: hidden;
}

.forgot-password-page.forgot-password-background {
  /* 使用全局CSS定义的背景 */
}

.forgot-password-container {
  width: 450px;
  max-width: 90%;
  display: flex;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: auto;
  margin: 20px;
}

.forgot-password-form-container {
  width: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.forgot-password-form-box {
  padding: 40px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
}

.forgot-password-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 25px;
  justify-content: center;
}

.tab-item {
  padding: 10px 0;
  margin-right: 0;
  font-size: 18px;
  color: #1890ff;
  font-weight: 500;
  position: relative;
}

.tab-item.active {
  color: #1890ff;
  font-weight: 500;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1890ff;
}

.forgot-password-form {
  flex: 1;
}

.form-description {
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.form-group {
  margin-bottom: 20px;
}

.input-with-icon {
  position: relative;
}

.input-with-icon i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #bfbfbf;
}

.input-with-icon input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s;
}

.input-with-icon input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

.form-actions {
  margin-bottom: 20px;
}

.field-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  padding-left: 8px;
}

/* 密码强度相关样式 */
.password-strength {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
}

.strength-label {
  margin-right: 8px;
  color: #666;
}

.strength-indicator {
  flex: 1;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 8px;
}

.strength-bar {
  height: 100%;
  width: 0;
  transition: all 0.3s;
}

.strength-bar.weak {
  width: 33%;
  background-color: #ff4d4f;
}

.strength-bar.medium {
  width: 66%;
  background-color: #faad14;
}

.strength-bar.strong {
  width: 100%;
  background-color: #52c41a;
}

.strength-text {
  font-weight: 500;
}

.strength-text.weak {
  color: #ff4d4f;
}

.strength-text.medium {
  color: #faad14;
}

.strength-text.strong {
  color: #52c41a;
}

.password-hint {
  color: #888;
  font-size: 12px;
  margin-top: 4px;
  padding-left: 8px;
  line-height: 1.4;
}

.submit-btn {
  width: 100%;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-btn:hover {
  background-color: #40a9ff;
}

.submit-btn:disabled {
  background-color: #91d5ff;
  cursor: not-allowed;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.resend-code {
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.resend-code a {
  color: #1890ff;
  text-decoration: none;
}

.resend-code a:hover {
  text-decoration: underline;
}

.login-link {
  text-align: center;
  margin-top: 16px;
  font-size: 14px;
}

.login-link span {
  color: #666;
  margin-right: 5px;
}

.login-link a {
  color: #1890ff;
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
}

.alert {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
}

.alert-danger {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.alert-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

/* 图标样式 */
.icon-email:before {
  content: "✉️";
}

.icon-lock:before {
  content: "🔒";
}

/* 响应式调整 */
@media (max-width: 576px) {
  .forgot-password-container {
    max-width: 100%;
  }
  
  .forgot-password-form-box {
    padding: 20px;
  }
}
</style> 