package com.medical.annotation.repository;

import com.medical.annotation.model.TeamMemberLog;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 团队成员日志仓库接口
 */
@Repository
public interface TeamMemberLogRepository extends JpaRepository<TeamMemberLog, Integer> {
    
    /**
     * 根据团队ID查找团队成员日志
     * @param teamId 团队ID
     * @return 团队成员日志列表
     */
    List<TeamMemberLog> findByTeamId(Integer teamId);
    
    /**
     * 根据用户ID查找团队成员日志
     * @param userId 用户ID
     * @return 团队成员日志列表
     */
    List<TeamMemberLog> findByUserId(Integer userId);
    
    /**
     * 根据用户ID和团队ID查找团队成员日志
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 团队成员日志列表
     */
    List<TeamMemberLog> findByUserIdAndTeamId(Integer userId, Integer teamId);
} 