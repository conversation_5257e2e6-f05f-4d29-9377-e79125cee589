"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[359],{26359:(e,a,o)=>{o.r(a),o.d(a,{default:()=>q});var r=o(61431),s={class:"login-page login-background"},t={class:"login-container"},i={class:"login-form-container"},n={class:"login-form-box"},l={class:"login-form"},d={key:0,class:"alert alert-danger"},c={key:1,class:"alert alert-success"},u={class:"form-group"},v={class:"input-with-icon"},g=["disabled"],m={class:"form-group"},p={class:"input-with-icon"},f=["disabled"],b={class:"form-options"},k={class:"remember-me"},h=["disabled"],I={class:"form-actions"},L=["disabled"],S={key:0,class:"spinner"},w={class:"register-link"},y={key:2,class:"redirect-notice"};function A(e,a,o,A,_,R){var U=(0,r.g2)("router-link");return(0,r.uX)(),(0,r.CE)("div",s,[(0,r.Lk)("div",t,[a[12]||(a[12]=(0,r.Fv)('<div class="login-info" data-v-bdd56362><h1 class="system-name" data-v-bdd56362>血管瘤人工智能<br data-v-bdd56362>辅助治疗系统</h1><p class="system-slogan" data-v-bdd56362>即刻体验智能诊断与专业医疗支持</p><div class="feature-list" data-v-bdd56362><div class="feature-item" data-v-bdd56362>AI智能诊断平台</div><div class="feature-item" data-v-bdd56362>精准血管瘤识别</div><div class="feature-item" data-v-bdd56362>专业医疗数据平台</div><div class="feature-item" data-v-bdd56362>智慧医疗辅助系统</div></div><div class="learn-more" data-v-bdd56362><a href="#" class="learn-more-link" data-v-bdd56362>了解更多 ›</a></div></div>',1)),(0,r.Lk)("div",i,[(0,r.Lk)("div",n,[a[10]||(a[10]=(0,r.Lk)("div",{class:"login-tabs"},[(0,r.Lk)("div",{class:"tab-item active"},"账号密码登录")],-1)),(0,r.Lk)("div",l,[A.error?((0,r.uX)(),(0,r.CE)("div",d,(0,r.v_)(A.error),1)):(0,r.Q3)("",!0),A.success?((0,r.uX)(),(0,r.CE)("div",c,(0,r.v_)(A.success),1)):(0,r.Q3)("",!0),(0,r.Lk)("form",{onSubmit:a[3]||(a[3]=(0,r.D$)((function(){return A.handleLogin&&A.handleLogin.apply(A,arguments)}),["prevent"]))},[(0,r.Lk)("div",u,[(0,r.Lk)("div",v,[a[4]||(a[4]=(0,r.Lk)("i",{class:"icon-user"},null,-1)),(0,r.bo)((0,r.Lk)("input",{id:"email","onUpdate:modelValue":a[0]||(a[0]=function(e){return A.email=e}),type:"email",placeholder:"请输入邮箱地址",required:"",disabled:A.loading},null,8,g),[[r.Jo,A.email]])])]),(0,r.Lk)("div",m,[(0,r.Lk)("div",p,[a[5]||(a[5]=(0,r.Lk)("i",{class:"icon-lock"},null,-1)),(0,r.bo)((0,r.Lk)("input",{id:"password","onUpdate:modelValue":a[1]||(a[1]=function(e){return A.password=e}),type:"password",placeholder:"请输入密码",required:"",disabled:A.loading},null,8,f),[[r.Jo,A.password]])])]),(0,r.Lk)("div",b,[(0,r.Lk)("div",k,[(0,r.bo)((0,r.Lk)("input",{id:"remember","onUpdate:modelValue":a[2]||(a[2]=function(e){return A.remember=e}),type:"checkbox",disabled:A.loading},null,8,h),[[r.lH,A.remember]]),a[6]||(a[6]=(0,r.Lk)("label",{for:"remember"},"下次自动登录",-1))]),(0,r.bF)(U,{to:"/forgot-password",class:"forgot-password"},{default:(0,r.k6)((function(){return a[7]||(a[7]=[(0,r.eW)("忘记密码?")])})),_:1,__:[7]})]),(0,r.Lk)("div",I,[(0,r.Lk)("button",{type:"submit",class:"login-btn",disabled:A.loading},[A.loading?((0,r.uX)(),(0,r.CE)("span",S)):(0,r.Q3)("",!0),(0,r.Lk)("span",null,(0,r.v_)(A.loading?"登录中...":"登 录"),1)],8,L)])],32),(0,r.Lk)("div",w,[a[9]||(a[9]=(0,r.Lk)("span",null,"没有账号?",-1)),(0,r.bF)(U,{to:"/register",class:"register-btn"},{default:(0,r.k6)((function(){return a[8]||(a[8]=[(0,r.eW)("立即注册")])})),_:1,__:[8]})]),A.imageId?((0,r.uX)(),(0,r.CE)("div",y," 检测到您之前正在标注图片(ID: "+(0,r.v_)(A.imageId)+")，登录后将自动跳转回表单页面。 ",1)):(0,r.Q3)("",!0)]),a[11]||(a[11]=(0,r.Lk)("div",{class:"login-footer"},[(0,r.Lk)("span")],-1))])])])])}var _=o(24059),R=o(55593),U=o(698),E=(o(2008),o(51629),o(74423),o(48598),o(44114),o(18111),o(7588),o(2892),o(79432),o(26099),o(21699),o(23500),o(76031),o(66278)),C=o(60455);o(36149);const D={name:"Login",setup:function(){var e=(0,E.Pj)(),a=(0,C.rd)(),o=(0,C.lq)(),s=(0,r.KR)(""),t=(0,r.KR)(""),i=(0,r.KR)(!1),n=(0,r.KR)(!1),l=(0,r.KR)(""),d=(0,r.KR)(""),c=(0,r.KR)(null);(0,r.sV)((function(){var e="true"===sessionStorage.getItem("isAppOperation"),r=sessionStorage.getItem("preservedUser");if(e||r||(console.log("执行用户数据清理逻辑"),v()),sessionStorage.removeItem("isAppOperation"),r){console.log("检测到保存的用户会话，尝试恢复"),localStorage.setItem("user",r),sessionStorage.removeItem("preservedUser");var s=sessionStorage.getItem("redirectAfterLogin")||"/app/dashboard";return console.log("恢复用户会话后将重定向到:",s),void setTimeout((function(){a.push(s)}),100)}var t="true"===localStorage.getItem("isSaveAndExit");if(t)return console.log("登录页面检测到保存并退出标记，将自动重定向到工作台"),localStorage.removeItem("isSaveAndExit"),void a.push("/app/dashboard");o.query.imageId&&(c.value=o.query.imageId,console.log("检测到从标注页面跳转，图片ID: ".concat(c.value)))}));var u=function(){var o=(0,U.A)((0,_.A)().m((function o(){var r,i,u,v,g,m,p,f;return(0,_.A)().w((function(o){while(1)switch(o.n){case 0:for(n.value=!0,l.value="",d.value="",o.p=1,console.log("登录前清除所有Dashboard相关缓存..."),localStorage.removeItem("dashboardStats"),localStorage.removeItem("tempApiData"),localStorage.removeItem("dashboardApiResponse"),r=[],i=0;i<localStorage.length;i++)u=localStorage.key(i),u&&(u.includes("dashboard")||u.includes("stats"))&&r.push(u);return r.forEach((function(e){console.log("清除缓存项:",e),localStorage.removeItem(e)})),o.n=2,e.dispatch("login",{email:s.value,password:t.value});case 2:if(v=o.v,"string"!==typeof v&&v&&"object"===(0,R.A)(v)){o.n=3;break}return l.value="string"===typeof v?"Invalid credentials"===v?"用户名或密码错误":v:"登录失败，请检查邮箱和密码",o.a(2);case 3:console.log("登录成功，检查用户信息:",v),window.fixUserPermission&&(g=window.fixUserPermission(),console.log("登录后执行权限修复:",g)),sessionStorage.setItem("forceRefreshDashboard","true"),c.value?(d.value="登录成功，即将返回标注页面...",sessionStorage.setItem("isNavigatingAfterSave","true"),e.dispatch("saveProgress",{step:2,imageId:Number(c.value),formData:null}),setTimeout((function(){a.push({path:"/cases/structured-form",query:{imageId:c.value,direct:1}})}),1500)):(m=sessionStorage.getItem("redirectPath"),p=sessionStorage.getItem("redirectAfterLogin"),sessionStorage.removeItem("redirectPath"),sessionStorage.removeItem("redirectAfterLogin"),sessionStorage.setItem("fromLoginPage","true"),d.value="登录成功，正在跳转到工作台...",setTimeout((function(){var e=m||p||"/app/dashboard";window.location.href=e}),1500)),o.n=5;break;case 4:o.p=4,f=o.v,console.error("Login error:",f),l.value="string"===typeof f?f:"登录失败，请检查邮箱和密码";case 5:return o.p=5,n.value=!1,o.f(5);case 6:return o.a(2)}}),o,null,[[1,4,5,6]])})));return function(){return o.apply(this,arguments)}}(),v=function(){try{var e="true"===sessionStorage.getItem("navigatingFromForm"),a="true"===sessionStorage.getItem("isNavigatingAfterSave");if(e||a)return void console.log("检测到从表单页面导航，保留用户数据");var o=localStorage.getItem("user");if(!o)return;var r=JSON.parse(o);console.log("检查存储的用户数据:",r);var s=["id","name","email","role"],t=s.filter((function(e){return!r[e]}));t.length>0?(console.warn("存储的用户数据缺少必要字段: ".concat(t.join(", "))),console.log("清除不完整的用户数据"),localStorage.removeItem("user"),sessionStorage.removeItem("preservedUser")):console.log("存储的用户数据完整，包含必要字段")}catch(l){console.error("检查用户数据时出错:",l),localStorage.removeItem("user"),sessionStorage.removeItem("preservedUser")}};return{email:s,password:t,remember:i,loading:n,error:l,success:d,imageId:c,handleLogin:u}}};var K=o(66262);const P=(0,K.A)(D,[["render",A],["__scopeId","data-v-bdd56362"]]),q=P}}]);
//# sourceMappingURL=359.7209d510.js.map