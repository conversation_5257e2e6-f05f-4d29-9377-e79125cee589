# 软件设计说明书

## 1. 引言

### 1.1 编写目的
本说明书旨在阐述 **"血管瘤人工智能辅助治疗系统"** 的系统架构、模块设计、数据库设计等技术实现细节，为后续的开发、测试和维护工作提供清晰、统一的技术指导。

### 1.2 项目背景
本项目旨在开发一套集医疗影像管理、智能辅助诊断和多用户协作为一体的B/S架构软件系统。系统通过前后端分离的模式，为医生及研究人员提供血管瘤病例的在线标注、结构化信息管理、AI辅助诊断及团队审核等功能，以提升诊断效率与准确性。

---

## 2. 系统总体设计

### 2.1 设计思想
本系统遵循 **前后端分离** 的现代Web应用设计思想。
- **前端（Frontend）**：作为用户交互的入口，负责页面渲染、用户操作响应和数据可视化。采用组件化的方式构建，实现了功能的高度复用和可维护性。
- **后端（Backend）**：作为核心业务逻辑和数据处理中心，通过提供标准化的 RESTful API 接口为前端提供服务。后端负责处理所有业务逻辑、数据持久化、用户认证与授权等。
- **AI服务（AI Service）**：作为一个独立的Python服务（根据项目文件推断），通过API与主后端进行通信，负责执行图像识别和诊断等计算密集型任务。

这种分离的设计模式使得前后端可以独立开发、部署和扩展，极大地提高了开发效率和系统的灵活性。

### 2.2 系统架构图
```mermaid
graph TD;
    subgraph 用户端
        A[用户浏览器]
    end

    subgraph 前端 (Vue.js)
        B[Nginx/Web服务器] --> C{前端应用};
        C -->|HTTP/HTTPS API请求| D[后端API网关];
    end

    subgraph 后端 (Spring Boot)
        D --> E{业务逻辑层 (Services)};
        E --> F[数据访问层 (JPA Repositories)];
        F --> G[(MySQL数据库)];
        E -->|调用AI分析| H[AI模型服务 (Python)];
    end

    subgraph AI服务
        H --> I[YOLO/ONNX模型];
    end

    A --> B;
```

### 2.3 技术选型 (Technology Stack)

| 层次 | 技术 | 描述 |
|---|---|---|
| **前端** | Vue.js (v3) | 核心UI框架，用于构建用户界面。 |
| | Vue Router | 提供前端路由功能，实现单页面应用（SPA）。 |
| | Vuex | 集中式状态管理，用于管理组件间的共享数据。 |
| | Element Plus | 高质量的UI组件库，用于快速构建美观的界面。 |
| | Axios | 基于Promise的HTTP客户端，用于与后端API通信。 |
| | Nginx | 高性能Web服务器，用于部署前端静态资源。 |
| **后端** | Spring Boot (v2.7.8) | 核心开发框架，简化了Java应用的开发与部署。 |
| | Spring Data JPA | 数据持久化框架，简化了数据库操作。 |
| | Spring Security | 提供强大的认证和授权功能。 |
| | MySQL | 主力关系型数据库，用于存储业务数据。 |
| | Maven | 项目管理和构建工具。 |
| | Java 8 | 主要编程语言。 |
| **AI服务** | Python | 用于AI模型推理。 |
| | ONNX / YOLO | （根据文件名推断）深度学习模型格式和框架。 |


### 2.4 功能模块划分
（请列出软件的主要功能模块）
- 用户管理模块
- 图像标注模块
- 数据分析模块
- ...

---

## 3. 模块详细设计

### 3.1 后端模块设计
后端采用经典的三层架构：Controller（接口层）、Service（业务逻辑层）、Repository（数据访问层）。

#### 3.1.1 用户认证模块 (AuthController)
- **功能描述：** 负责处理用户的注册和登录，并生成用于后续请求身份验证的凭证（如JWT Token）。
- **主要接口：**
    - `POST /api/auth/register`: 接收用户信息（姓名、邮箱、密码、医院等）进行新用户注册。
    - `POST /api/auth/login`: 验证用户凭据，成功后返回Token。

#### 3.1.2 病例管理与诊断模块 (HemangiomaDiagnosisController)
- **功能描述：** 这是系统的核心业务模块，处理从病例创建、图像标注、表单填写到提交审核、再到最终审批的完整生命周期。
- **主要接口：**
    - `POST /api/diagnoses`: 创建一个新的诊断病例记录。
    - `GET /api/diagnoses/{id}`: 获取指定ID的病例详情。
    - `PUT /api/diagnoses/{id}`: 更新病例信息（包括标注数据和表单内容）。
    - `POST /api/diagnoses/{id}/submit`: 将病例提交以供审核。
    - `POST /api/diagnoses/{id}/approve`: (审核员/管理员) 批准病例。
    - `POST /api/diagnoses/{id}/reject`: (审核员/管理员) 驳回病例。

#### 3.1.3 图像文件模块 (ImageFileController)
- **功能描述：** 负责处理所有与图像文件相关的操作，主要是上传、获取和删除。
- **主要接口：**
    - `POST /api/images/upload`: 上传单张或多张图像文件。
    - `GET /api/images/{filename}`: 根据文件名获取图像文件。
    - `DELETE /api/images/{id}`: 删除指定ID的图像记录及其文件。

#### 3.1.4 用户与团队模块 (UserController, TeamController)
- **功能描述：** 负责管理系统中的用户和团队。包括获取用户列表、修改用户角色、创建团队、添加/移除团队成员等。
- **主要接口：**
    - `GET /api/users`: 获取所有用户列表。
    - `PUT /api/users/{id}/role`: (管理员) 修改指定用户的角色。
    - `GET /api/teams`: 获取团队列表。
    - `POST /api/teams`: 创建一个新团队。
    - `POST /api/teams/{id}/members`: 向指定团队中添加成员。

#### 3.1.5 审核与申请模块 (ReviewerApplicationController)
- **功能描述：** 处理普通用户想成为审核员的权限升级申请。
- **主要接口：**
    - `POST /api/reviewer-applications`: 用户提交成为审核员的申请。
    - `GET /api/reviewer-applications`: (管理员) 获取所有待处理的申请。
    - `POST /api/reviewer-applications/{id}/approve`: (管理员) 批准申请。

### 3.2 前端模块设计
前端基于 Vue.js 生态系统构建，具有清晰的模块化结构。

#### 3.2.1 路由与视图 (router/index.js, views/)
- **描述：** 使用 `Vue Router` 管理应用的页面导航。每个 `.vue` 文件在 `views` 目录下代表一个独立的页面（或页面的一部分），如登录页(`Login.vue`)、病例管理页(`Cases.vue`)、标注页(`AnnotationAndForm.vue`)等。

#### 3.2.2 可复用组件 (components/)
- **描述：** 将UI中可复用的部分抽象为独立的组件，存放在 `components` 目录下。例如，权限控制的按钮(`PermissionControl.vue`)、统一的状态徽章(`StatusBadge.vue`)等。这提高了代码的可维护性和一致性。

#### 3.2.3 状态管理 (store/modules/)
- **描述：** 使用 `Vuex` 进行全局状态管理。不同业务模块的数据和状态被分割到不同的 `modules` 中，如 `auth.js` (处理用户信息和Token)、`images.js` (管理图像列表)、`annotation.js` (处理标注数据)等。这种模式避免了组件之间复杂的属性传递，使得数据流更加清晰。

#### 3.2.4 API服务 (utils/api.js)
- **描述：** 将所有与后端API的交互统一封装在一个或多个服务文件中。使用 `Axios` 发起HTTP请求，并集中处理如请求头携带Token、错误处理等逻辑。

#### 3.2.5 响应式布局
- **描述：** 系统使用了Flexbox布局和媒体查询，实现了响应式UI设计。例如，基本信息部分的表单字段（患者年龄、性别、类型、血管质地、病变部位、颜色等）根据屏幕宽度自动调整排列，确保在不同设备上都能获得良好的用户体验。

---

## 4. 数据库设计

### 4.1 E-R 图
（可以在这里绘制或粘贴实体关系图）
```mermaid
erDiagram
    USERS ||--o{ TEAMS : "创建"
    USERS ||--|{ TEAMS : "拥有"
    USERS }o--|| TEAMS : "属于"
    USERS ||--o{ HEMANGIOMA_DIAGNOSES : "创建"
    HEMANGIOMA_DIAGNOSES ||--|{ TAGS : "包含"

    USERS {
        int id PK
        varchar name
        varchar email
        varchar password
        enum role
        int team_id FK
    }
    TEAMS {
        int id PK
        varchar name
        text description
        int created_by_id FK
        int owner_id FK
    }
    HEMANGIOMA_DIAGNOSES {
        int id PK
        int user_id FK
        varchar image_path
        enum status
        text diagnostic_summary
        text treatment_suggestion
        text precautions
    }
    TAGS {
        int id PK
        int diagnosis_id FK
        varchar tag_name
        float x
        float y
        float width
        float height
    }
```

### 4.2 数据表结构
以下是系统的核心数据表结构。

**表1：用户表 (users)**
| 字段名 | 数据类型 | 约束 | 描述 |
|---|---|---|---|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | 用户唯一标识 |
| `name` | VARCHAR(255) | NOT NULL | 用户姓名 |
| `email` | VARCHAR(255) | NOT NULL, UNIQUE | 登录邮箱，唯一 |
| `password` | VARCHAR(255) | NOT NULL | 加密存储的密码哈希 |
| `role` | ENUM('ADMIN', 'DOCTOR', 'REVIEWER', 'USER') | NOT NULL | 用户角色 |
| `hospital` | VARCHAR(255) | | 所属医院 |
| `department` | VARCHAR(255) | | 所属科室 |
| `team_id` | INT | FOREIGN KEY (references teams.id) | 所属团队的外键 |
| `created_at` | DATETIME | | 创建时间 |

**表2：团队表 (teams)**
| 字段名 | 数据类型 | 约束 | 描述 |
|---|---|---|---|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | 团队唯一标识 |
| `name` | VARCHAR(255) | NOT NULL | 团队名称 |
| `description` | TEXT | | 团队描述 |
| `created_by` | INT | FOREIGN KEY (references users.id) | 创建者的外键 |
| `owner_id` | INT | FOREIGN KEY (references users.id) | 团队所有者的外键 |
| `created_at`| DATETIME | | 创建时间 |

**表3：血管瘤诊断信息表 (hemangioma_diagnoses)**
| 字段名 | 数据类型 | 约束 | 描述 |
|---|---|---|---|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | 诊断记录唯一标识 |
| `user_id` | INT | FOREIGN KEY (references users.id) | 创建该记录用户的外键 |
| `patient_age` | INT | | 患者年龄 |
| `gender` | VARCHAR(50) | | 患者性别 |
| `origin_type` | VARCHAR(255) | | 类型（先天性/后天性） |
| `vessel_texture` | VARCHAR(255) | | 血管质地 |
| `body_part` | VARCHAR(255) | | 病变部位 |
| `color` | VARCHAR(255) | | 颜色 |
| `image_path` | VARCHAR(255) | | 原始图片存储路径 |
| `processed_image_path` | VARCHAR(255) | | AI处理或标注后图片路径 |
| `status` | ENUM('DRAFT', 'SUBMITTED', 'APPROVED', 'REJECTED') | NOT NULL | 记录状态 |
| `diagnostic_summary` | TEXT | | 诊断摘要 |
| `treatment_suggestion` | TEXT | | 治疗建议 |
| `precautions` | TEXT | | 注意事项 |
| `disclaimer` | TEXT | | 免责声明 |
| `review_notes` | TEXT | | (审核员填写)审核意见 |
| `created_at`| DATETIME | | 创建时间 |

---

## 5. 非功能性设计

- **安全性设计：**
    - **用户认证：** 所有需要登录的接口均通过 Spring Security 进行保护。用户登录成功后，后端会签发 Token，前端在后续请求的 Header 中携带此 Token 进行身份验证。
    - **用户授权：** 后端在关键操作接口上（如修改用户角色、批准审核等）进行了权限校验，确保只有拥有特定角色的用户（如`ADMIN`, `REVIEWER`）才能访问。
    - **密码存储：** 用户密码在存入数据库前，会经过哈希加密（如BCrypt），确保即使数据库泄露，密码原文也不会暴露。
    - **防SQL注入：** 采用 JPA 和参数化查询，从根本上防止了SQL注入攻击。

- **性能设计：**
    - **数据库索引：** 为频繁用于查询条件的字段（如`users.email`, `hemangioma_diagnoses.status`）添加了数据库索引，以提高查询速度。
    - **前后端分离：** B/S架构和前后端分离使得浏览器可以缓存前端静态资源，减轻了服务器的渲染压力。
    - **异步处理：** AI图像处理等耗时操作可以设计为异步任务，避免长时间阻塞用户请求。

- **可维护性设计：**
    - **模块化代码：** 前后端均遵循了高内聚、低耦合的模块化原则，代码结构清晰，便于分工协作和后期维护。
    - **统一编码规范：** 项目遵循标准的Java和Vue.js编码规范，提高了代码的可读性。
    - **日志记录：** 在后端的关键业务逻辑处添加了日志记录，方便快速定位和排查线上问题。
    
- **用户体验设计：**
    - **自适应界面布局：** 使用响应式设计，确保系统在不同屏幕尺寸下都能提供良好的用户体验。
    - **直观的操作流程：** 通过清晰的页面结构和操作指引，减少用户学习成本。
    - **表单验证与即时反馈：** 前端实现表单验证，提供即时的错误提示，提高用户输入的准确性和效率。 