{"version": 3, "file": "js/556.eb2b18ef.js", "mappings": "0VAsGO,SAASA,EAAaC,GAC3B,IAAMC,EAAY,MAAHC,OAASC,KAAKC,OAC7B,OAAIJ,EAAIK,SAAS,KACR,GAAPH,OAAUF,EAAG,KAAAE,OAAID,GAEV,GAAPC,OAAUF,EAAG,KAAAE,OAAID,EAErB,CC3FO,SAASK,EAAYN,GAC1B,IAAKA,EAAK,MAAO,GAKjB,GAHAO,QAAQC,IAAI,wBAAyBR,GAGjC,eAAeS,KAAKT,GAAM,CAC5BO,QAAQC,IAAI,2BACZ,IAAME,EAAcC,mBAAmBX,GAGjCY,EAAUC,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAiB,GAClD,OAAOf,EAAa,GAADG,OAAIU,EAAO,oCAAAV,OAAmCQ,GACnE,CAGA,GAAIV,EAAIe,WAAW,YAAcf,EAAIe,WAAW,YAC9C,OAAOhB,EAAaC,GAItB,GAAIA,EAAIe,WAAW,SACjB,OAAOf,EAIT,IAAIgB,EAAWhB,EAaf,OAXIA,EAAIe,WAAW,aACjBR,QAAQC,IAAI,mCAEZQ,EAAWH,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAYZ,OAAGF,GAAQA,GAC1CA,EAAIe,WAAW,OACxBR,QAAQC,IAAI,2CAEZQ,EAAWH,EAAAA,GAAc,GAAHX,OAAMY,EAAAA,IAAYZ,OAAGe,EAAAA,IAAgBf,OAAGF,GAAG,GAAAE,OAAQe,EAAAA,IAAgBf,OAAGF,IAG9FO,QAAQC,IAAI,yBAA0BQ,GAC/BjB,EAAaiB,EACtB,C,kECzDOE,MAAM,6B,GADbC,IAAA,EAWwBD,MAAM,oB,GAX9BC,IAAA,EAiBgBD,MAAM,gB,GACXA,MAAM,a,GAWJA,MAAM,gB,GAEJA,MAAM,6B,GAiDRA,MAAM,gB,GACLA,MAAM,iB,GAMLA,MAAM,a,GAWRA,MAAM,gB,4TAjGjBE,EAAAA,EAAAA,IAyGM,MAzGNC,EAyGM,gBAxGJC,EAAAA,EAAAA,IAOM,OAPDJ,MAAM,eAAa,EACtBI,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,kBAAgB,EACzBI,EAAAA,EAAAA,IAAe,UAAX,aAENA,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,qBAAgB,IAKlBK,EAAAC,UAAO,WAAlBJ,EAAAA,EAAAA,IAIM,MAJNK,EAIMC,EAAA,KAAAA,EAAA,KAHJJ,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,iBAAiBS,KAAK,U,EAC/BL,EAAAA,EAAAA,IAA2C,QAArCJ,MAAM,mBAAkB,YAAM,oBAIxCE,EAAAA,EAAAA,IAwFM,MAxFNQ,EAwFM,EAvFJN,EAAAA,EAAAA,IAOM,MAPNO,EAOM,EANJC,EAAAA,EAAAA,IAKEC,EAAA,CAJAC,MAAM,gDACNC,KAAK,OACJC,UAAU,EACX,oBAGJJ,EAAAA,EAAAA,IA8EUK,EAAA,CA9EDC,IAAI,WAAYC,MAAOd,EAAAe,SAAU,iBAAe,MAAOC,MAAOhB,EAAAgB,O,CA1B7E,SAAAC,EAAAA,EAAAA,KA6BQ,iBAgDM,EAhDNlB,EAAAA,EAAAA,IAgDM,MAhDNmB,EAgDM,gBA/CJnB,EAAAA,EAAAA,IAAmC,MAA/BJ,MAAM,iBAAgB,QAAI,KAC9BI,EAAAA,EAAAA,IA6CM,MA7CNoB,EA6CM,EA5CJZ,EAAAA,EAAAA,IAEea,EAAA,CAFDC,MAAM,OAAOC,KAAK,c,CAhC5C,SAAAL,EAAAA,EAAAA,KAiCc,iBAAqE,EAArEV,EAAAA,EAAAA,IAAqEgB,EAAA,CAjCnFC,WAiCwCxB,EAAAe,SAASU,WAjCjD,sBAAAtB,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OAiCwC1B,EAAAe,SAASU,WAAUC,CAAA,GAAGC,IAAK,EAAIC,IAAK,K,2BAjC5EC,EAAA,KAmCYtB,EAAAA,EAAAA,IAKea,EAAA,CALDC,MAAM,KAAKC,KAAK,U,CAnC1C,SAAAL,EAAAA,EAAAA,KAoCc,iBAGiB,EAHjBV,EAAAA,EAAAA,IAGiBuB,EAAA,CAvC/BN,WAoCuCxB,EAAAe,SAASgB,OApChD,sBAAA5B,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OAoCuC1B,EAAAe,SAASgB,OAAML,CAAA,I,CApCtD,SAAAT,EAAAA,EAAAA,KAqCgB,iBAAgC,EAAhCV,EAAAA,EAAAA,IAAgCyB,EAAA,CAAtBX,MAAM,KAAG,CArCnC,SAAAJ,EAAAA,EAAAA,KAqCoC,kBAACd,EAAA,MAAAA,EAAA,MArCrC8B,EAAAA,EAAAA,IAqCoC,M,IArCpCJ,EAAA,EAAAK,GAAA,QAsCgB3B,EAAAA,EAAAA,IAAgCyB,EAAA,CAAtBX,MAAM,KAAG,CAtCnC,SAAAJ,EAAAA,EAAAA,KAsCoC,kBAACd,EAAA,MAAAA,EAAA,MAtCrC8B,EAAAA,EAAAA,IAsCoC,M,IAtCpCJ,EAAA,EAAAK,GAAA,O,IAAAL,EAAA,G,sBAAAA,EAAA,KAyCatB,EAAAA,EAAAA,IAKca,EAAA,CALAC,MAAM,KAAKC,KAAK,c,CAzC3C,SAAAL,EAAAA,EAAAA,KA0Cc,iBAGiB,EAHjBV,EAAAA,EAAAA,IAGiBuB,EAAA,CA7C/BN,WA0CuCxB,EAAAe,SAASoB,WA1ChD,sBAAAhC,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OA0CuC1B,EAAAe,SAASoB,WAAUT,CAAA,I,CA1C1D,SAAAT,EAAAA,EAAAA,KA2CgB,iBAAoC,EAApCV,EAAAA,EAAAA,IAAoCyB,EAAA,CAA1BX,MAAM,OAAK,CA3CrC,SAAAJ,EAAAA,EAAAA,KA2CsC,kBAAGd,EAAA,MAAAA,EAAA,MA3CzC8B,EAAAA,EAAAA,IA2CsC,Q,IA3CtCJ,EAAA,EAAAK,GAAA,QA4CgB3B,EAAAA,EAAAA,IAAoCyB,EAAA,CAA1BX,MAAM,OAAK,CA5CrC,SAAAJ,EAAAA,EAAAA,KA4CsC,kBAAGd,EAAA,MAAAA,EAAA,MA5CzC8B,EAAAA,EAAAA,IA4CsC,Q,IA5CtCJ,EAAA,EAAAK,GAAA,O,IAAAL,EAAA,G,sBAAAA,EAAA,KA+CYtB,EAAAA,EAAAA,IASea,EAAA,CATDC,MAAM,OAAOC,KAAK,Y,CA/C5C,SAAAL,EAAAA,EAAAA,KAgDc,iBAOY,EAPZV,EAAAA,EAAAA,IAOY6B,EAAA,CAvD1BZ,WAgDkCxB,EAAAe,SAASsB,SAhD3C,sBAAAlC,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OAgDkC1B,EAAAe,SAASsB,SAAQX,CAAA,GAAEY,YAAY,UAAUC,WAAA,I,CAhD3E,SAAAtB,EAAAA,EAAAA,KAkDkB,iBAA2B,gBAD7BpB,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAtD5BC,EAAAA,EAAAA,IAkDiCzC,EAAA0C,aAlDjC,SAkDyBC,G,kBADTC,EAAAA,EAAAA,IAKYC,EAAA,CAHTjD,IAAK+C,EAAKG,MACVzB,MAAOsB,EAAKtB,MACZyB,MAAOH,EAAKG,O,uCArD/BjB,EAAA,G,sBAAAA,EAAA,KAyDYtB,EAAAA,EAAAA,IASea,EAAA,CATDC,MAAM,KAAKC,KAAK,S,CAzD1C,SAAAL,EAAAA,EAAAA,KA0Dc,iBAOY,EAPZV,EAAAA,EAAAA,IAOY6B,EAAA,CAjE1BZ,WA0DkCxB,EAAAe,SAASgC,MA1D3C,sBAAA5C,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OA0DkC1B,EAAAe,SAASgC,MAAKrB,CAAA,GAAEY,YAAY,UAAUC,WAAA,I,CA1DxE,SAAAtB,EAAAA,EAAAA,KA4DkB,iBAA4B,gBAD9BpB,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAhE5BC,EAAAA,EAAAA,IA4DiCzC,EAAAgD,cA5DjC,SA4DyBL,G,kBADTC,EAAAA,EAAAA,IAKYC,EAAA,CAHTjD,IAAK+C,EAAKG,MACVzB,MAAOsB,EAAKtB,MACZyB,MAAOH,EAAKG,O,uCA/D/BjB,EAAA,G,sBAAAA,EAAA,KAmEYtB,EAAAA,EAAAA,IAQea,EAAA,CARDC,MAAM,OAAOC,KAAK,iB,CAnE5C,SAAAL,EAAAA,EAAAA,KAoEc,iBAMY,EANZV,EAAAA,EAAAA,IAMY6B,EAAA,CA1E1BZ,WAoEkCxB,EAAAe,SAASkC,cApE3C,sBAAA9C,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OAoEkC1B,EAAAe,SAASkC,cAAavB,CAAA,GAAEY,YAAY,W,CApEtE,SAAArB,EAAAA,EAAAA,KAqEgB,iBAAqC,EAArCV,EAAAA,EAAAA,IAAqCsC,EAAA,CAA1BxB,MAAM,KAAKyB,MAAM,UAC5BvC,EAAAA,EAAAA,IAAwCsC,EAAA,CAA7BxB,MAAM,KAAKyB,MAAM,aAC5BvC,EAAAA,EAAAA,IAAqCsC,EAAA,CAA1BxB,MAAM,KAAKyB,MAAM,UAC5BvC,EAAAA,EAAAA,IAAuCsC,EAAA,CAA5BxB,MAAM,KAAKyB,MAAM,YAC5BvC,EAAAA,EAAAA,IAA8CsC,EAAA,CAAnCxB,MAAM,MAAMyB,MAAM,iB,IAzE7CjB,EAAA,G,sBAAAA,EAAA,SAgFQ9B,EAAAA,EAAAA,IAeM,MAfNmD,EAeM,EAdJnD,EAAAA,EAAAA,IAKK,KALLoD,EAKK,gBAtFflB,EAAAA,EAAAA,IAiFoC,cAChB,eAAAlC,EAAAA,EAAAA,IAAyC,QAAnCJ,MAAM,sBAAqB,KAAC,IAC5BK,EAAAoD,0BAAuB,WAArCR,EAAAA,EAAAA,IAESS,EAAA,CArFrBzD,IAAA,EAmFmDc,KAAK,UAAU4C,KAAK,S,CAnFvE,SAAArC,EAAAA,EAAAA,KAoFc,kBAA+Bd,EAAA,MAAAA,EAAA,MAA/BJ,EAAAA,EAAAA,IAA+B,KAA5BJ,MAAM,mBAAiB,UApFxCsC,EAAAA,EAAAA,IAoF6C,kB,IApF7CJ,EAAA,EAAAK,GAAA,SAAAqB,EAAAA,EAAAA,IAAA,UAuFUxD,EAAAA,EAAAA,IAOM,MAPNyD,EAOM,EANJjD,EAAAA,EAAAA,IAEea,EAAA,CAFDC,MAAM,SAASC,KAAK,uB,CAxF9C,SAAAL,EAAAA,EAAAA,KAyFc,iBAAgH,EAAhHV,EAAAA,EAAAA,IAAgHkD,EAAA,CAzF9HjC,WAyFiCxB,EAAAe,SAAS2C,oBAzF1C,sBAAAvD,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OAyFiC1B,EAAAe,SAAS2C,oBAAmBhC,CAAA,GAAEhB,KAAK,WAAYiD,KAAM,EAAGrB,YAAY,e,2BAzFrGT,EAAA,KA2FYtB,EAAAA,EAAAA,IAEea,EAAA,CAFDC,MAAM,SAASC,KAAK,e,CA3F9C,SAAAL,EAAAA,EAAAA,KA4Fc,iBAAqH,EAArHV,EAAAA,EAAAA,IAAqHkD,EAAA,CA5FnIjC,WA4FiCxB,EAAAe,SAAS6C,YA5F1C,sBAAAzD,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OA4FiC1B,EAAAe,SAAS6C,YAAWlC,CAAA,GAAEhB,KAAK,WAAYiD,KAAM,EAAGrB,YAAY,4B,2BA5F7FT,EAAA,SAkGQ9B,EAAAA,EAAAA,IAIM,MAJN8D,EAIM,EAHJtD,EAAAA,EAAAA,IAA+FuD,EAAA,CAApFpD,KAAK,UAAWqD,QAAK5D,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OAAEsC,EAAAC,SAAS,WAAD,GAAehE,QAASD,EAAAkE,a,CAnG5E,SAAAjD,EAAAA,EAAAA,KAmGyF,kBAAId,EAAA,MAAAA,EAAA,MAnG7F8B,EAAAA,EAAAA,IAmGyF,S,IAnGzFJ,EAAA,EAAAK,GAAA,M,gBAoGU3B,EAAAA,EAAAA,IAAoFuD,EAAA,CAAzEpD,KAAK,UAAWqD,QAAOC,EAAAG,WAAalE,QAASD,EAAAoE,Y,CApGlE,SAAAnD,EAAAA,EAAAA,KAoG8E,kBAAId,EAAA,MAAAA,EAAA,MApGlF8B,EAAAA,EAAAA,IAoG8E,S,IApG9EJ,EAAA,EAAAK,GAAA,M,yBAqG2BlC,EAAAqE,YAAS,WAA1BzB,EAAAA,EAAAA,IAA8EkB,EAAA,CArGxFlE,IAAA,EAqGsCc,KAAK,UAAWqD,QAAOC,EAAAM,W,CArG7D,SAAArD,EAAAA,EAAAA,KAqGwE,kBAAId,EAAA,MAAAA,EAAA,MArG5E8B,EAAAA,EAAAA,IAqGwE,S,IArGxEJ,EAAA,EAAAK,GAAA,M,iBAAAqB,EAAAA,EAAAA,IAAA,S,IAAA1B,EAAA,G,2HCCA,SAAS0C,EAAmBC,GAC1B,GAAI,MAAQA,EAAG,CACb,IAAIC,EAAID,EAAE,mBAAqBE,QAAUA,OAAOC,UAAY,cAC1DC,EAAI,EACN,GAAIH,EAAG,OAAOA,EAAEI,KAAKL,GACrB,GAAI,mBAAqBA,EAAEM,KAAM,OAAON,EACxC,IAAKO,MAAMP,EAAEQ,QAAS,MAAO,CAC3BF,KAAM,WACJ,OAAON,GAAKI,GAAKJ,EAAEQ,SAAWR,OAAI,GAAS,CACzC1B,MAAO0B,GAAKA,EAAEI,KACdK,MAAOT,EAEX,EAEJ,CACA,MAAM,IAAIU,WAAUC,EAAAA,EAAAA,GAAQX,GAAK,mBACnC,C,qSCVS7E,MAAM,kB,GAPfC,IAAA,EAQuBD,MAAM,e,GAGlBA,MAAM,kB,GAKLA,MAAM,iB,8FAfhBiD,EAAAA,EAAAA,IAoBYwC,EAAA,CAnBFC,QAASrB,EAAAsB,cAFrB,mBAAAnF,EAAA,KAAAA,EAAA,YAAAuB,GAAA,OAEqBsC,EAAAsB,cAAa5D,CAAA,GAC7BjB,MAAO8E,EAAA9E,MACP+E,MAAOD,EAAAC,MACP,eAAcxB,EAAAyB,a,CAUJC,QAAMzE,EAAAA,EAAAA,KACf,iBAGO,EAHPlB,EAAAA,EAAAA,IAGO,OAHPO,EAGO,EAFLC,EAAAA,EAAAA,IAA6DuD,EAAA,CAAjDC,QAAOC,EAAA2B,cAAY,CAjBvC,SAAA1E,EAAAA,EAAAA,KAiByC,iBAAgB,EAjBzDgB,EAAAA,EAAAA,KAAA2D,EAAAA,EAAAA,IAiB4CL,EAAAM,YAAU,G,IAjBtDhE,EAAA,G,gBAkBQtB,EAAAA,EAAAA,IAAmFuD,EAAA,CAAvEpD,KAAM6E,EAAAO,YAAc/B,QAAOC,EAAA+B,e,CAlB/C,SAAA9E,EAAAA,EAAAA,KAkB8D,iBAAiB,EAlB/EgB,EAAAA,EAAAA,KAAA2D,EAAAA,EAAAA,IAkBiEL,EAAAS,aAAW,G,IAlB5EnE,EAAA,G,4BAAA,SAAAZ,EAAAA,EAAAA,KAOI,iBAOM,EAPNlB,EAAAA,EAAAA,IAOM,MAPND,EAOM,CANOyF,EAAAU,OAAI,WAAfpG,EAAAA,EAAAA,IAEM,MAFNK,EAEM,EADJH,EAAAA,EAAAA,IAA0B,KAAtBJ,OATZuG,EAAAA,EAAAA,IASmBlC,EAAAmC,Y,YATnB5C,EAAAA,EAAAA,IAAA,QAWMxD,EAAAA,EAAAA,IAEM,MAFNM,EAEM,EADJ+F,EAAAA,EAAAA,IAA0BC,EAAAC,OAAA,cAA1B,iBAA0B,EAZlCrE,EAAAA,EAAAA,KAAA2D,EAAAA,EAAAA,IAYiBL,EAAAgB,SAAO,G,eAZxB1E,EAAA,G,8CAyBA,SACE2E,KAAM,gBACNC,MAAO,CACLhG,MAAO,CACLC,KAAMgG,OACNC,QAAS,MAEXJ,QAAS,CACP7F,KAAMgG,OACNC,QAAS,cAEXX,YAAa,CACXtF,KAAMgG,OACNC,QAAS,MAEXd,WAAY,CACVnF,KAAMgG,OACNC,QAAS,MAEXb,YAAa,CACXpF,KAAMgG,OACNC,QAAS,WAEXV,KAAM,CACJvF,KAAMgG,OACNC,QAAS,IAEXnB,MAAO,CACL9E,KAAMgG,OACNC,QAAS,OAEX7D,MAAO,CACLpC,KAAMkG,QACND,SAAS,IAGbE,SAAU,CACRvB,cAAe,CACbwB,IAAG,WACD,OAAOC,KAAKjE,KACd,EACAkE,IAAG,SAAClE,GACFiE,KAAKE,MAAM,QAASnE,EACtB,GAEFqD,UAAS,WACP,OAAQY,KAAKd,MACX,IAAK,UACH,MAAO,0BACT,IAAK,QACH,MAAO,gBACT,IAAK,UACH,MAAO,kBACT,IAAK,OACH,MAAO,eACT,QACE,OAAOc,KAAKd,KAElB,GAEFiB,QAAS,CACPzB,YAAW,SAACR,GACV8B,KAAKE,MAAM,UACXhC,GACF,EACAU,aAAY,WACVoB,KAAKzB,eAAgB,EACrByB,KAAKE,MAAM,SACb,EACAlB,cAAa,WACXgB,KAAKzB,eAAgB,EACrByB,KAAKE,MAAM,UACb,I,eC1FJ,MAAME,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,I,iBCHO,SAASC,EAAWC,GAAwB,IAAlBC,EAAQC,UAAAvC,OAAA,QAAAwC,IAAAD,UAAA,IAAAA,UAAA,GACvC,IAAKF,EAAM,MAAO,GAElB,IAAMI,EAA0B,kBAATJ,EAAoB,IAAIzI,KAAKyI,GAAQA,EAG5D,GAAItC,MAAM0C,EAAQC,WAChB,MAAO,GAGT,IAAMC,EAAOF,EAAQG,cACfC,EAAQnB,OAAOe,EAAQK,WAAa,GAAGC,SAAS,EAAG,KACnDC,EAAMtB,OAAOe,EAAQQ,WAAWF,SAAS,EAAG,KAE5CG,EAAU,GAAHvJ,OAAMgJ,EAAI,KAAAhJ,OAAIkJ,EAAK,KAAAlJ,OAAIqJ,GAEpC,GAAIV,EAAU,CACZ,IAAMa,EAAQzB,OAAOe,EAAQW,YAAYL,SAAS,EAAG,KAC/CM,EAAU3B,OAAOe,EAAQa,cAAcP,SAAS,EAAG,KACnDQ,EAAU7B,OAAOe,EAAQe,cAAcT,SAAS,EAAG,KAEzD,MAAO,GAAPpJ,OAAUuJ,EAAO,KAAAvJ,OAAIwJ,EAAK,KAAAxJ,OAAI0J,EAAO,KAAA1J,OAAI4J,EAC3C,CAEA,OAAOL,CACT,C,kCJuFA,SACE1B,KAAM,qBACNiC,WAAY,CACVC,cAAAA,GAEFC,KAAI,WACF,MAAO,CACL1I,SAAS,EACT2I,QAAS,KACTC,YAAa,KACbC,mBAAmB,EACnBC,gBAAiB,KACjB3F,yBAAyB,EACzBc,aAAa,EACbE,YAAY,EACZC,WAAW,EACX3B,YAAa,CACX,CAAEI,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,OAExB2B,aAAc,CACZ,CAAEF,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,MACtB,CAAEyB,MAAO,KAAMzB,MAAO,OAExBN,SAAU,CACRU,WAAY,KACZM,OAAQ,GACRI,WAAY,GACZE,SAAU,GACVU,MAAO,GACPE,cAAe,GACfS,oBAAqB,GACrBE,YAAa,IAEf5C,MAAO,CACLS,WAAY,CAAC,CAAEuH,UAAU,EAAOzC,QAAS,UAAW0C,QAAS,SAC7DlH,OAAQ,CAAC,CAAEiH,UAAU,EAAOzC,QAAS,QAAS0C,QAAS,WACvD9G,WAAY,CAAC,CAAE6G,UAAU,EAAOzC,QAAS,QAAS0C,QAAS,WAC3D5G,SAAU,CAAC,CAAE2G,UAAU,EAAOzC,QAAS,UAAW0C,QAAS,SAC3DlG,MAAO,CAAC,CAAEiG,UAAU,EAAOzC,QAAS,QAAS0C,QAAS,SAEtDvF,oBAAqB,CAAC,CAAEsF,UAAU,EAAMzC,QAAS,UAAW0C,QAAS,SACrErF,YAAa,CAAC,CAAEoF,UAAU,EAAMzC,QAAS,UAAW0C,QAAS,UAE/DC,mBAAoB,GAExB,EACArC,UAAQsC,EAAAA,EAAAA,GAAA,IACHC,EAAAA,EAAAA,IAAW,CAAC,2BAEjBC,QAAO,WAAG,IAAAC,EAAA,KAEFT,EAAc9B,KAAKwC,OAAOC,MAAMX,aAAe9B,KAAKwC,OAAOE,OAAOC,IAAM3C,KAAKwC,OAAOC,MAAMZ,QAC5FC,GACF7J,QAAQC,IAAI,kBAAmB4J,GAC/B9B,KAAK8B,YAAcA,EACnB9B,KAAK6B,QAAUC,EAEf9B,KAAK4C,WAAU,WAEbL,EAAKM,wBAAwBf,GAAagB,MAAK,WAC7CP,EAAKQ,aAAajB,EACpB,GACF,MAEA9B,KAAK9G,SAAU,EACf8G,KAAKgD,SAASC,MAAM,oBAExB,EACA9C,SAAOiC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,IACFc,EAAAA,EAAAA,IAAW,CAAC,eAAgB,wBAAsB,IAAAC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACrD9C,WAAAA,EAGMwC,wBAAuB,SAACf,GAAa,IAAAsB,EAAA,YAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAR,EAAAA,EAAAA,KAAAS,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OACzChM,QAAQC,IAAI,iBACNuL,EAAY,CAAC,qCAAD7L,OACqBkK,GAAW,uBAAAlK,OACzBkK,EAAW,qBAIpCsB,EAAKjB,mBAAqB,GAAEuB,EAAA,EAAAC,EAELF,EAAS,YAAAC,EAAAC,EAAA1F,QAAA,CAAA+F,EAAAC,EAAA,QAAd,OAAPL,EAAOD,EAAAD,GAAAM,EAAAE,EAAA,EAAAF,EAAAC,EAAA,EAGSE,IAAAA,QAAcP,GAAS,OAAxCC,EAAOG,EAAAI,EACbnM,QAAQC,IAAI,MAADN,OAAOgM,EAAQ,cAAcC,EAASQ,QAAQ,UAAY,kBACrEjB,EAAKjB,mBAAmBmC,KAAK,CAC3B5M,IAAKkM,EACLzD,SAAU0D,EAASQ,QAAQ,UAAY,kBAAkBE,MAAM,QAC/DP,EAAAC,EAAA,sBAAAD,EAAAE,EAAA,EAAAJ,EAAAE,EAAAI,EAAAJ,EAAAE,EAAA,EAAAF,EAAAC,EAAA,EAI2BE,IAAAA,KAAWP,GAAS,OAA9BI,EAAAI,EACjBnM,QAAQC,IAAI,MAADN,OAAOgM,EAAQ,mBAC1BR,EAAKjB,mBAAmBmC,KAAK,CAC3B5M,IAAKkM,EACLzD,QAAS,CAAC,SACV6D,EAAAC,EAAA,eAAAD,EAAAE,EAAA,EAAAF,EAAAI,EAEFnM,QAAQC,IAAI,MAADN,OAAOgM,EAAQ,SAASE,EAAMtE,SAAQ,OAAAkE,IAAAM,EAAAC,EAAA,eAKvDhM,QAAQC,IAAI,YAAakL,EAAKjB,oBAAmB,eAAA6B,EAAAQ,EAAA,MAAAhB,EAAA,uBAlCRH,EAmC3C,EACMN,aAAY,SAACjB,GAAa,IAAA2C,EAAA,YAAApB,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAmB,IAAA,IAAA/M,EAAAgN,EAAAN,EAAAO,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAArB,EAAAjC,EAAAuD,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAjC,EAAAA,EAAAA,KAAAS,GAAA,SAAAyB,GAAA,eAAAA,EAAAvB,GAAA,OAC9BQ,EAAKvL,SAAU,EACfjB,QAAQC,IAAI,kBAAmB4J,GAGzBnK,EAAYE,KAAKC,MACnB6M,GAAU,EAGRN,EAAU,CAAC,EACXO,EAAOa,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDf,EAAQc,aAAaC,QAAQ,SAE/Bf,IACFR,EAAQ,iBAAmB,UAAJzM,OAAciN,IAGnCD,IAASA,EAAKjC,IAAMiC,EAAKiB,YAC3BxB,EAAQ,aAAeO,EAAKjC,IAAMiC,EAAKiB,SACvCxB,EAAQ,eAAiBO,EAAKvL,MAAQ,QAIlCyL,EAAW,CAAC,qCAADlN,OACsBkK,GAAW,0BAAAlK,OACtBkK,GAAW,6BAAAlK,OACRkK,GAAW,yBAAAlK,OACfkK,GAAW,kBAAAlK,OAClBkK,IAGnBiD,GAAAzB,EAAAA,EAAAA,KAAAC,GAAA,SAAAwB,IAAA,IAAAe,EAAAjC,EAAAjC,EAAAuD,EAAAY,EAAA,OAAAzC,EAAAA,EAAAA,KAAAS,GAAA,SAAAiC,GAAA,eAAAA,EAAA/B,GAAA,OAEa,GAAH6B,EAAGZ,EAAAD,IACRN,EAAS,CAAFqB,EAAA/B,EAAA,eAAA+B,EAAAxB,EAAA,YAGuC,OAHvCwB,EAAA9B,EAAA,EAGTjM,QAAQC,IAAI,cAADN,OAAekO,EAAI,OAAAlO,OAAMD,IAAYqO,EAAA/B,EAAA,EACzBE,IAAAA,IAAU,GAADvM,OAAIkO,EAAI,OAAAlO,OAAMD,GAAa,CAAE0M,QAAAA,IAAU,OAA1D,GAAPR,EAAOmC,EAAA5B,GAETP,IAAYA,EAASjC,KAAI,CAAAoE,EAAA/B,EAAA,QA+C3B,OA9CMrC,EAAOiC,EAASjC,KACtB3J,QAAQC,IAAI,YAAa0J,GACzB6C,EAAK3C,YAAcF,EAAKe,IAAMb,EAGxBqD,EAAc,CAElBzK,WAAY,KACZM,OAAQ,GACRI,WAAY,GACZE,SAAU,GACVU,MAAO,GACPE,cAAe,GACfS,oBAAqB,GACrBE,YAAa,IAIfoJ,OAAOC,KAAKf,GAAagB,SAAQ,SAAAtN,QACb4H,IAAdmB,EAAK/I,KACPsM,EAAYtM,GAAO+I,EAAK/I,GAE5B,SAGyB4H,IAArBmB,EAAKwE,cAA2BjB,EAAYzK,WAAakH,EAAKwE,kBACtC3F,IAAxBmB,EAAKyE,iBAA8BlB,EAAYjJ,cAAgB0F,EAAKyE,qBACtC5F,IAA9BmB,EAAK0E,uBAAoCnB,EAAYxI,oBAAsBiF,EAAK0E,2BAC3D7F,IAArBmB,EAAK/E,cAA2BsI,EAAYtI,YAAc+E,EAAK/E,aAGnE4H,EAAKzK,SAAWmL,EAGhBV,EAAK7B,WAAU,WACb3K,QAAQC,IAAI,kBAAmBuM,EAAKzK,UACpCyK,EAAK8B,cACP,IAEA9B,EAAKzB,SAAS2B,QAAQ,mBACtBA,GAAU,EAGL/C,EAAKjF,qBAAwBiF,EAAK/E,cACrC4H,EAAKpI,yBAA0B,EAC/BoI,EAAK+B,aAAa1E,IACpBkE,EAAAxB,EAAA,YAAAwB,EAAA/B,EAAA,eAAA+B,EAAA9B,EAAA,EAAA6B,EAAAC,EAAA5B,EAKFnM,QAAQgL,MAAM,OAADrL,OAAQkO,EAAI,UAAAC,GAAgB,cAAAC,EAAAxB,EAAA,MAAAO,EAAA,iBAAAE,EAAA,EAAAC,EA3D1BJ,EAAQ,YAAAG,EAAAC,EAAAjH,QAAA,CAAAuH,EAAAvB,EAAA,eAAAuB,EAAAiB,EAAAjJ,EAAAuH,KAAA,aAAAC,EAAAQ,EAAApB,EAAA,IAAAY,EAAA,CAAAQ,EAAAvB,EAAA,eAAAuB,EAAAhB,EAAA,YAAAS,IAAAO,EAAAvB,EAAA,kBAgEtBU,EAAS,CAAFa,EAAAvB,EAAA,QAE8B,OAF9BuB,EAAAtB,EAAA,EAERjM,QAAQC,IAAI,2BAA0BsN,EAAAvB,EAAA,EACfyC,EAAAA,WAAI3G,IAAI,qCAADnI,OAAsCkK,IAAc,OAA5E+B,EAAO2B,EAAApB,EACTP,GAAYA,EAASjC,OACjBA,EAAOiC,EAASjC,KACtB3J,QAAQC,IAAI,mBAAoB0J,GAG1BuD,GAAU/C,EAAAA,EAAAA,GAAA,GAASqC,EAAKzK,UAG9BiM,OAAOC,KAAKf,GAAagB,SAAQ,SAAAtN,QACb4H,IAAdmB,EAAK/I,KACPsM,EAAYtM,GAAO+I,EAAK/I,GAE5B,SAGyB4H,IAArBmB,EAAKwE,cAA2BjB,EAAYzK,WAAakH,EAAKwE,kBACtC3F,IAAxBmB,EAAKyE,iBAA8BlB,EAAYjJ,cAAgB0F,EAAKyE,qBACtC5F,IAA9BmB,EAAK0E,uBAAoCnB,EAAYxI,oBAAsBiF,EAAK0E,2BAC3D7F,IAArBmB,EAAK/E,cAA2BsI,EAAYtI,YAAc+E,EAAK/E,aAEnE4H,EAAKzK,SAAWmL,EAChBV,EAAK8B,eACL9B,EAAKzB,SAAS2B,QAAQ,mBACtBA,GAAU,GACZa,EAAAvB,EAAA,eAAAuB,EAAAtB,EAAA,EAAAqB,EAAAC,EAAApB,EAEAnM,QAAQgL,MAAM,iBAAgBsC,GAAW,OAK7C,IAAKZ,EACH,IACQS,EAAQ,mBAAAxN,OAAuBkK,GAC/BuD,EAAaM,aAAaC,QAAQR,GAEpCC,IACFpN,QAAQC,IAAI,iBACNoN,EAAeG,KAAKC,MAAML,GAChCZ,EAAKzK,SAAWsL,EAChBb,EAAK8B,eACL9B,EAAKzB,SAAS2D,QAAQ,sBACtBhC,GAAU,EAEd,CAAE,MAAOiC,GACP3O,QAAQgL,MAAM,YAAa2D,EAC7B,CAIGjC,IACH1M,QAAQgL,MAAM,qBACdwB,EAAKoC,uBACLpC,EAAKzB,SAASC,MAAM,6BAGtBwB,EAAKvL,SAAU,EAAK,cAAAsM,EAAAhB,EAAA,MAAAE,EAAA,iBA7JUrB,EA8JhC,EACMjG,WAAU,WAAG,IAAA0J,EAAA,YAAAzD,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAwD,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAvC,EAAAwC,EAAAC,EAAA,OAAA/D,EAAAA,EAAAA,KAAAS,GAAA,SAAAuD,GAAA,eAAAA,EAAArD,GAAA,cAAAqD,EAAApD,EAAA,EAAAoD,EAAArD,EAAA,EAGT6C,EAAKS,MAAMC,SAASC,WAAU,OAQuC,GALrET,EAAiB,CACrB,sBACA,eAGIC,EAAgBD,EAAeU,QAAO,SAAAC,GAAI,OAAMb,EAAK9M,SAAS2N,EAAM,MAEtEV,EAAchJ,OAAS,GAAC,CAAAqJ,EAAArD,EAAA,QAQqC,OANzDiD,EAAa,CACjBvK,oBAAqB,OACrBE,YAAa,QAGTsK,EAAoBF,EAAcW,KAAI,SAAAD,GAAI,OAAKT,EAAWS,EAAM,IACtEb,EAAK9D,SAAS2D,QAAQ,WAAD/O,OAAYuP,EAAkBU,KAAK,OAAOP,EAAA9C,EAAA,UAYjE,OARMI,EAAOa,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAClDwB,EAAWxC,EAAOA,EAAKvL,KAAO,KAIlCgO,EADe,UAAbD,GAAqC,aAAbA,EACZ,WAEA,YAChBE,EAAArD,EAAA,EAEM6C,EAAK5J,SAASmK,GAAY,OAAAC,EAAArD,EAAA,eAEO,OAFPqD,EAAApD,EAAA,EAAAoD,EAAAlD,EAEhC0C,EAAK9D,SAAS2D,QAAQ,kBAAiBW,EAAA9C,EAAA,iBAAA8C,EAAA9C,EAAA,MAAAuC,EAAA,iBArCxB1D,EAwCnB,EACMnG,SAAQ,SAAC4K,GAAQ,IAAAC,EAAA,YAAA1E,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAyE,IAAA,IAAA9O,EAAAmL,EAAAQ,EAAAD,EAAAqD,EAAAvQ,EAAAmM,EAAArE,EAAA0I,EAAAC,EAAAC,EAAA,OAAA9E,EAAAA,EAAAA,KAAAS,GAAA,SAAAsE,GAAA,eAAAA,EAAApE,GAAA,UAChB8D,EAAKjG,YAAa,CAAFuG,EAAApE,EAAA,QACmB,OAAtC8D,EAAK/E,SAASC,MAAM,mBAAkBoF,EAAA7D,EAAA,UAoCC,MAhC1B,cAAXsD,GAAqC,aAAXA,EAC5BC,EAAK1K,YAAa,EAElB0K,EAAK5K,aAAc,EAErB4K,EAAKzK,WAAY,EAEjBrF,QAAQC,IAAI,gBAADN,OAAiBmQ,EAAKjG,YAAW,UAAAlK,OAASkQ,IAC/C5O,EAAU6O,EAAKO,SAAS,CAAEC,MAAM,EAAMC,KAAM,YAE5CnE,EAAU,CACd,eAAgB,mBAChB,OAAU,oBAENQ,EAAQc,aAAaC,QAAQ,SAC/Bf,IACFR,EAAQ,iBAAmB,UAAJzM,OAAciN,IAEjCD,EAAOa,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MACpDhB,IAASA,EAAKjC,IAAMiC,EAAKiB,YAC3BxB,EAAQ,aAAeO,EAAKjC,IAAMiC,EAAKiB,SACvCxB,EAAQ,eAAiBO,EAAKvL,MAAQ,QAGlC4O,GAAiB7F,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClB2F,EAAK/N,UAAQ,IAChB8N,OAAQA,IAGJpQ,EAAE,qCAAAE,OAAyCmQ,EAAKjG,aAAWuG,EAAAnE,EAAA,EAG/DjM,QAAQC,IAAI,qBAADN,OAAsBF,IAAM2Q,EAAApE,EAAA,EAChBE,IAAAA,IAAUzM,EAAKuQ,EAAoB,CAAE5D,QAAAA,IAAU,OAAhER,EAAOwE,EAAAjE,EACbnM,QAAQC,IAAI,cAAe2L,EAASjC,MAEpC1I,EAAQuP,QACRV,EAAK5K,aAAc,EACnB4K,EAAK1K,YAAa,EAElBsI,aAAa+C,WAAW,mBAAD9Q,OAAoBmQ,EAAKjG,cAI9CtC,EADa,aAAXsI,EACQ,eACU,cAAXA,EACC,gBAEA,gBAEZC,EAAK/E,SAAS2B,QAAQnF,GAEtBmJ,YAAW,WACTZ,EAAKa,QAAQtE,KAAK,iBACpB,GAAG,MAAK+D,EAAApE,EAAA,eAAAoE,EAAAnE,EAAA,EAAAkE,EAAAC,EAAAjE,EAGRnM,QAAQgL,MAAM,SAADrL,OAAUF,EAAG,QAAQ0Q,EAAMvE,SAAWuE,EAAMvE,SAASjC,KAAOwG,EAAM5I,SAE/EtG,EAAQuP,QACRV,EAAK5K,aAAc,EACnB4K,EAAK1K,YAAa,EAClB0K,EAAKzK,WAAY,EAEX6K,GAA6B,QAAdD,EAAAE,EAAMvE,gBAAQ,IAAAqE,GAAM,QAANA,EAAdA,EAAgBtG,YAAI,IAAAsG,OAAA,EAApBA,EAAsBjF,QAASmF,EAAM5I,SAAW,OACrEuI,EAAK/E,SAASC,MAAM,SAADrL,OAAUuQ,IAE7B,IACExC,aAAakD,QAAQ,mBAADjR,OAAoBmQ,EAAKjG,aAAe2D,KAAKqD,UAAUf,EAAK/N,WAChF+N,EAAK/E,SAAS2D,QAAQ,gCACxB,CAAE,MAAOlJ,GACPxF,QAAQgL,MAAM,WAAYxF,EAC5B,eAAA4K,EAAA7D,EAAA,MAAAwD,EAAA,iBA9EmB3E,EAgFvB,EACA0F,mBAAkB,WAChB/I,KAAK4I,QAAQI,MACf,EAEAzL,UAAS,WACPyC,KAAK9C,SAAS,WAChB,EAEAlF,YAAW,SAAC8N,GAEV,GAAIA,GAAQA,EAAKrN,WAAW,SAC1B,OAAOqN,EAIT,IAAMjE,EAAU7B,KAAK6B,QACrB,GAAIA,EAAS,CACX,IAAMoH,EAAetD,aAAaC,QAAQ,iBAADhO,OAAkBiK,IAC3D,GAAIoH,EAEF,OADAhR,QAAQC,IAAI,oBACL+Q,CAEX,CAGA,OAAOjR,EAAAA,EAAAA,IAAY8N,EACrB,EAEAoD,eAAc,WAAG,IAAAC,EAAA,KACf,GAAKnJ,KAAK6B,QAAV,CAMA,IAAMuH,EAA4C,SAA/BpJ,KAAKwC,OAAOC,MAAM4G,SAC/BC,EAAsD,SAAxC3D,aAAaC,QAAQ,eAEzC,GAAIwD,GAAcE,EAAa,CAC7BrR,QAAQC,IAAI,mBAGZ,IAAM+Q,EAAetD,aAAaC,QAAQ,iBAADhO,OAAkBoI,KAAK6B,UAGhE7B,KAAKuJ,UAAY,CACf5G,GAAI6G,SAASxJ,KAAK6B,SAClB4H,cAAe,QAAF7R,OAAUoI,KAAK6B,QAAO,QACnC6H,YAAY,IAAI7R,MAAO8R,cACvB7D,KAAMmD,GAAgB,IAIxB,IAAMW,EAAgB5J,KAAK6J,sBAO3B,OANID,GAAiBA,EAAc5P,WACjCgG,KAAKhG,UAAOoI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAASpC,KAAKhG,UAAa4P,EAAc5P,UACrDgG,KAAKgD,SAAS8G,KAAK,sBAGrB9J,KAAK+J,QAAS,EAEhB,CAGA/J,KAAK9G,SAAU,EACfwN,EAAAA,WAAIsD,OAAOC,OAAOjK,KAAK6B,SACpBiB,MAAK,SAAAe,GACJsF,EAAKI,UAAY1F,EAASjC,KAC1BuH,EAAKjQ,SAAU,EACfiQ,EAAKY,QAAS,EAGdZ,EAAKe,mBAAmBf,EAAKI,UAC/B,IAAC,UACM,SAAAtG,GAIL,GAHAhL,QAAQgL,MAAM,WAAYA,GAGkB,SAAxC0C,aAAaC,QAAQ,eAA2B,CAClD3N,QAAQkS,KAAK,wBAEb,IAAMlB,EAAetD,aAAaC,QAAQ,iBAADhO,OAAkBuR,EAAKtH,UAE5DoH,GACFE,EAAKI,UAAY,CACf5G,GAAI6G,SAASL,EAAKtH,SAClB4H,cAAe,QAAF7R,OAAUuR,EAAKtH,QAAO,QACnC6H,YAAY,IAAI7R,MAAO8R,cACvB7D,KAAMmD,GAGRE,EAAKnG,SAAS2D,QAAQ,sBACtBwC,EAAKY,QAAS,IAEdZ,EAAKlG,MAAQ,WACbkG,EAAKnG,SAASC,MAAM,iBAExB,KAAO,KAAAmH,EACLjB,EAAKlG,OAAsB,QAAdmH,EAAAnH,EAAMY,gBAAQ,IAAAuG,OAAA,EAAdA,EAAgBxI,OAAQqB,EAAMzD,QAC3C2J,EAAKnG,SAASC,MAAM,SAAWkG,EAAKlG,MACtC,CAEAkG,EAAKjQ,SAAU,CACjB,GAvEF,MAFE8G,KAAKgD,SAASC,MAAM,iBA0ExB,EAEAoH,cAAa,SAACC,GACZ,IAAMC,GAAKnI,EAAAA,EAAAA,GAAA,GAASpC,KAAKhG,UAGzB,IAAK,IAAMnB,KAAOyR,EACZrE,OAAOuE,UAAUC,eAAe3M,KAAKwM,EAAWzR,KACpB,YAA1BuF,EAAAA,EAAAA,GAAOkM,EAAUzR,KAAwC,OAAnByR,EAAUzR,IACzB,YAAvBuF,EAAAA,EAAAA,GAAOmM,EAAO1R,KAAqC,OAAhB0R,EAAO1R,GAE5C0R,EAAO1R,GAAOmH,KAAKqK,cAAcC,EAAUzR,IAG3C0R,EAAO1R,GAAOyR,EAAUzR,IAK9B,OAAO0R,CACT,EAEAG,aAAY,WAAG,IAAAC,EAAA,KACb3K,KAAKuH,MAAMC,SAASC,UAAS,SAAAmD,GAC3B,GAAKA,EAAL,CAKAD,EAAKE,QAAS,EAGd,IAAMvB,EAAsD,SAAxC3D,aAAaC,QAAQ,eAEzC,GAAI0D,EAAJ,CACErR,QAAQC,IAAI,iBAGZ,IACEyN,aAAakD,QAAQ,gBAADjR,OAAiB+S,EAAK9I,SAAW4D,KAAKqD,UAAU6B,EAAK3Q,WAGzE2Q,EAAKG,qBAELH,EAAK3H,SAAS2B,QAAQ,eACtBgE,YAAW,WACTgC,EAAK/B,QAAQtE,KAAK,iBACpB,GAAG,IACL,CAAE,MAAO7G,GACPxF,QAAQgL,MAAM,WAAYxF,GAC1BkN,EAAK3H,SAASC,MAAM,SAAWxF,EAAE+B,QACnC,CAEAmL,EAAKE,QAAS,CAEhB,KArBA,CAwBA,IAAM3R,EAAUyR,EAAKrC,SAAS,CAC5BC,MAAM,EACNC,KAAM,WACNuC,QAAS,kBACTC,WAAY,6BAIR/C,GAAiB7F,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClBuI,EAAK3Q,UAAQ,IAChBiR,OAAQ,SACRtT,WAAW,IAAIE,MAAO8R,gBAIxB,IACE,IAAMuB,EAAUvF,aAAaC,QAAQ,QACrC,GAAIsF,EAAS,CACX,IAAMtG,EAAOa,KAAKC,MAAMwF,GAEpBtG,EAAKvL,OACP4O,EAAmBb,SAAWxC,EAAKvL,KACnCpB,QAAQC,IAAI,eAAgB0M,EAAKvL,OAE/BuL,EAAKjC,KACPsF,EAAmBkD,OAASvG,EAAKjC,IAE/BiC,EAAKiB,WACPoC,EAAmBmD,aAAexG,EAAKiB,SAE3C,CACF,CAAE,MAAOpI,GACPxF,QAAQgL,MAAM,YAAaxF,EAC7B,CAEAxF,QAAQC,IAAI,UAAW+P,GAGvBvB,EAAAA,WAAIsD,OAAOqB,uBAAuBV,EAAK9I,QAASoG,GAC7CnF,MAAK,SAAAe,GACJ3K,EAAQuP,QACRkC,EAAK3H,SAAS2B,QAAQ,UAEtBgG,EAAKG,qBAELnC,YAAW,WACTgC,EAAK/B,QAAQtE,KAAK,iBACpB,GAAG,IACL,IAAC,UACM,SAAArB,GAAS,IAAAqI,EACdpS,EAAQuP,QACRxQ,QAAQgL,MAAM,SAAUA,GAExB,IAAMkF,GAA6B,QAAdmD,EAAArI,EAAMY,gBAAQ,IAAAyH,OAAA,EAAdA,EAAgB1J,OAAQqB,EAAMzD,SAAW,OAC9DmL,EAAK3H,SAASC,MAAM,SAAWkF,GAG1BmB,GACHqB,EAAKY,SAAS,qBAAsB,OAAQ,CAC1CC,kBAAmB,SACnBC,iBAAkB,OAClB9R,KAAM,YACLmJ,MAAK,WACN6C,aAAakD,QAAQ,cAAe,QACpC8B,EAAKD,cACP,IAAE,UAAO,WACPC,EAAK3H,SAAS8G,KAAK,QACrB,GAEJ,IAAC,YACQ,WACPa,EAAKE,QAAS,CAChB,GA3EF,CA5BA,MAFEF,EAAK3H,SAAS2D,QAAQ,gBA0G1B,GACF,EACA+E,0BAAyB,WAEvB,EAGFC,iBAAgB,WACd3L,KAAK4L,aAAa,CAChBC,KAAM,EACNhK,QAAS7B,KAAK6B,QACd7H,SAAUgG,KAAKhG,UAEnB,EAEA8R,YAAW,WAET9L,KAAK2L,mBAGL3L,KAAK+B,mBAAoB,CAC3B,EAEAgK,mBAAkB,WAAG,IAAAC,EAAA,KAEnBC,eAAepD,QAAQ,wBAAyB,QAChDoD,eAAepD,QAAQ,qBAAsB,QAG7C,IAAM3P,EAAU8G,KAAKsI,SAAS,CAC5BC,MAAM,EACNC,KAAM,WACNuC,QAAS,kBACTC,WAAY,6BAGd,IAEE,IAAM/C,GAAiB7F,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClBpC,KAAKhG,UAAQ,IAChBiR,OAAQ,OACRtT,WAAW,IAAIE,MAAO8R,gBAIxBjD,EAAAA,WAAIsD,OAAOqB,uBAAuBrL,KAAK6B,QAASoG,GAC7CnF,MAAK,SAAAe,GACJmI,EAAKhJ,SAAS2B,QAAQ,eACtBsH,eAAepD,QAAQ,qBAAsB,QAC7C,IACEmD,EAAKpD,QAAQtE,KAAK,iBACpB,CAAE,MAAO7G,GACPxF,QAAQgL,MAAM,6BAA8BxF,GAC5CyO,OAAOC,SAASC,KAAO,gBACzB,CACF,IAAC,SACK,eAAAC,GAAAhJ,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAC,SAAA+I,EAAMrJ,GAAI,IAAAsJ,EAAA,OAAAjJ,EAAAA,EAAAA,KAAAS,GAAA,SAAAyI,GAAA,eAAAA,EAAAvI,GAAA,OACfhM,QAAQgL,MAAM,YAAaA,GAC3B+I,EAAKhJ,SAASC,MAAM,WAA0B,QAAdsJ,EAAAtJ,EAAMY,gBAAQ,IAAA0I,OAAA,EAAdA,EAAgB3K,OAAQqB,EAAMzD,SAAW,SAEzE,IACEmG,aAAakD,QAAQ,gBAADjR,OAAiBoU,EAAKnK,SAAW4D,KAAKqD,UAAUkD,EAAKhS,WACzEgS,EAAKhJ,SAAS2D,QAAQ,sBACxB,CAAE,MAAOlJ,GACPxF,QAAQgL,MAAM,WAAYxF,EAC5B,CACA,OAAA+O,EAAAtI,EAAA,EAAAsI,EAAAvI,EAAA,EAEQ+H,EAAKT,SAAS,0BAA2B,OAAQ,CACrDC,kBAAmB,KACnBC,iBAAkB,SAClB9R,KAAM,YACN,OAEFqS,EAAKpD,QAAQtE,KAAK,kBAAiBkI,EAAAvI,EAAA,eAAAuI,EAAAtI,EAAA,EAAAsI,EAAApI,EAGnC4H,EAAKhJ,SAAS8G,KAAK,cAAa,cAAA0C,EAAAhI,EAAA,MAAA8H,EAAA,kBAEnC,gBAAAG,GAAA,OAAAJ,EAAAK,MAAA,KAAAlM,UAAA,EAvBK,IAuBL,YACQ,WACPtH,EAAQuP,OACV,GACJ,CAAE,MAAOxF,GACPhL,QAAQgL,MAAM,aAAcA,GAC5BjD,KAAKgD,SAASC,MAAM,SAAWA,EAAMzD,SACrCtG,EAAQuP,QAGRyD,OAAOC,SAASC,KAAO,gBACzB,CACF,EAEAO,sBAAqB,WACnB3M,KAAK8K,qBACL9K,KAAKgD,SAAS8G,KAAK,WACnB9J,KAAK4I,QAAQtE,KAAK,aACpB,GAAC,iCAICtE,KAAK2L,mBAGL3L,KAAK4I,QAAQtE,KAAK,CAChBwB,KAAM,SAAW9F,KAAK8B,YAAc,qBACpCW,MAAO,CAAEZ,QAAS7B,KAAK6B,UAE3B,IAAC,sCAGC,IAAM+C,EAAOa,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,IAAKhB,EAAM,CAET,IAAMgI,EAAgBX,eAAerG,QAAQ,iBACzCgH,IACF3U,QAAQC,IAAI,mBACZyN,aAAakD,QAAQ,OAAQ+D,GAC7BX,eAAevD,WAAW,iBAE9B,CACF,IAAC,+BAEkBa,GAGjB,GAFAtR,QAAQC,IAAI,UAAWqR,GAElBA,EAAL,CAMA,IAAM3H,GAAGQ,EAAAA,EAAAA,GAAA,GAASmH,GAGZsD,EAAgB,CACpB,gBAAmB,iBACnB,YAAe,aACf,cAAiB,eACjB,uBAA0B,wBAC1B,WAAc,YACd,gBAAmB,iBACnB,qBAAwB,sBACxB,mBAAsB,oBACtB,mBAAsB,mBACtB,mBAAsB,oBACtB,eAAkB,gBAClB,sBAAyB,uBACzB,mBAAsB,mBACtB,iBAAoB,kBACpB,kBAAqB,oBAYvB,GARA5G,OAAOC,KAAK2G,GAAe1G,SAAQ,SAAAtN,QACf4H,IAAdmB,EAAK/I,SAAmD4H,IAA7BmB,EAAKiL,EAAchU,MAChD+I,EAAKiL,EAAchU,IAAQ+I,EAAK/I,GAChCZ,QAAQC,IAAI,SAADN,OAAUiB,EAAG,QAAAjB,OAAOiV,EAAchU,KAEjD,IAGI+I,EAAKkL,eAEP,GAAmC,kBAAxBlL,EAAKkL,eAA6B,CAC3C,IAAMC,EAAgBnL,EAAKkL,eAAevI,MAAM,KAAKmD,QAAO,SAAAxD,GAAA,OAAKA,EAAE8I,MAAM,IACrED,EAAc9O,OAAS,IACzB+B,KAAKhG,SAASsB,SAAWyR,EACzB9U,QAAQC,IAAI,iBAAkB8H,KAAKhG,SAASsB,UAEhD,MAAW2R,MAAMC,QAAQtL,EAAKkL,kBAC5B9M,KAAKhG,SAASsB,SAAWsG,EAAKkL,eAC9B7U,QAAQC,IAAI,cAAe8H,KAAKhG,SAASsB,WAK7C,KAAK0E,KAAKhG,SAASsB,UAA8C,IAAlC0E,KAAKhG,SAASsB,SAAS2C,SAChDsL,EAAU4D,iBAC6B,kBAA9B5D,EAAU4D,gBAA8B,CACjD,IAAMJ,EAAgBxD,EAAU4D,gBAAgB5I,MAAM,KAAKmD,QAAO,SAAAxD,GAAA,OAAKA,EAAE8I,MAAM,IAC3ED,EAAc9O,OAAS,IACzB+B,KAAKhG,SAASsB,SAAWyR,EACzB9U,QAAQC,IAAI,iBAAkB8H,KAAKhG,SAASsB,UAEhD,CAeJ,GAXwB,OAApBsG,EAAKlH,iBAA2C+F,IAApBmB,EAAKlH,aACnCsF,KAAKhG,SAASU,WAAa8O,SAAS5H,EAAKlH,YACzCzC,QAAQC,IAAI,UAAW8H,KAAKhG,SAASU,aAGnCkH,EAAKwL,eACPpN,KAAKhG,SAASqT,MAAQzL,EAAKwL,aAC3BnV,QAAQC,IAAI,UAAW8H,KAAKhG,SAASqT,QAInCzL,EAAK0L,sBAAuB,CAC9B,IAAMC,EAAgB3L,EAAK0L,uBAAyB,GAE9CE,EAAaD,EAAcE,QAAQ,KAEzC,GAAID,GAAc,EAAG,CACnB,IAAME,EAAWH,EAAcI,UAAU,EAAGH,GAAYjJ,MAAM,KAC9DvE,KAAKhG,SAAS4T,WAAaF,EAC3B1N,KAAKhG,SAAS6T,eAAiBN,EAAcI,UAAUH,EAAa,GAAGR,MACzE,MAEEhN,KAAKhG,SAAS4T,WAAaL,EAAchJ,MAAM,KAAKmD,QAAO,SAAAxD,GAAA,MAAkB,KAAbA,EAAE8I,MAAa,IAGjF/U,QAAQC,IAAI,UAAW8H,KAAKhG,SAAS4T,YACrC3V,QAAQC,IAAI,UAAW8H,KAAKhG,SAAS6T,eACvC,CAOA,GALIjM,EAAKkM,YACP9N,KAAKhG,SAAS8T,UAAYlM,EAAKkM,UAC/B7V,QAAQC,IAAI,UAAW8H,KAAKhG,SAAS8T,YAGnClM,EAAKmM,SAAU,CACjB,IAAMC,EAAepM,EAAKmM,UAAY,GAChCP,EAAaQ,EAAaP,QAAQ,KAExC,GAAID,GAAc,EAAG,CACnB,IAAMO,EAAWC,EAAaL,UAAU,EAAGH,GAAYjJ,MAAM,KAC7DvE,KAAKhG,SAAS+T,SAAWA,EACzB/N,KAAKhG,SAASiU,mBAAqBD,EAAaL,UAAUH,EAAa,GAAGR,MAC5E,MACEhN,KAAKhG,SAAS+T,SAAWC,EAAazJ,MAAM,KAAKmD,QAAO,SAAAwG,GAAA,MAAkB,KAAbA,EAAElB,MAAa,IAG9E/U,QAAQC,IAAI,QAAS8H,KAAKhG,SAAS+T,SACrC,CAOA,GALInM,EAAKuM,iBACPnO,KAAKhG,SAASiU,mBAAqBrM,EAAKuM,eACxClW,QAAQC,IAAI,UAAW8H,KAAKhG,SAASiU,qBAGnCrM,EAAKwM,cAAe,CACtB,IAAMA,EAAgBxM,EAAKwM,eAAiB,GAC5CpO,KAAKhG,SAASoU,cAAgBA,EAAc7J,MAAM,KAAKmD,QAAO,SAAA2G,GAAA,MAAkB,KAAbA,EAAErB,MAAa,IAClF/U,QAAQC,IAAI,SAAU8H,KAAKhG,SAASoU,cACtC,CAuBA,GArBIxM,EAAK0M,sBACPtO,KAAKhG,SAASsU,oBAAsB1M,EAAK0M,oBACzCrW,QAAQC,IAAI,WAAY8H,KAAKhG,SAASsU,sBAIpC1M,EAAK2M,oBACPvO,KAAKhG,SAASwU,UAAY5M,EAAK2M,kBAC/BtW,QAAQC,IAAI,UAAW8H,KAAKhG,SAASwU,YAGnC5M,EAAK6M,mBACPzO,KAAKhG,SAAS0U,cAAgB9M,EAAK6M,iBACnCxW,QAAQC,IAAI,WAAY8H,KAAKhG,SAAS0U,gBAGpC9M,EAAK+M,oBACP3O,KAAKhG,SAAS2U,kBAAoB/M,EAAK+M,kBACvC1W,QAAQC,IAAI,WAAY8H,KAAKhG,SAAS2U,oBAGpC/M,EAAKgN,cAAe,CACtB,IAAMA,EAAgBhN,EAAKgN,eAAiB,GAC5C5O,KAAKhG,SAAS4U,cAAgBA,EAAcrK,MAAM,KAAKmD,QAAO,SAAAxD,GAAA,MAAkB,KAAbA,EAAE8I,MAAa,IAClF/U,QAAQC,IAAI,UAAW8H,KAAKhG,SAAS4U,cACvC,CAaA,GAXIhN,EAAKiN,uBACP7O,KAAKhG,SAAS2C,oBAAsBiF,EAAKiN,qBACzC5W,QAAQC,IAAI,UAAW8H,KAAKhG,SAAS2C,sBAGnCiF,EAAKkN,oBACP9O,KAAKhG,SAAS8U,kBAAoBlN,EAAKkN,kBACvC7W,QAAQC,IAAI,SAAU8H,KAAKhG,SAAS8U,oBAIlClN,EAAKmN,iBAAkB,CACzB,IAAMC,EAAWpN,EAAKmN,iBAGhBE,EAAkB,CAAC,MAAO,MAAO,OACnCA,EAAgBlX,SAASiX,GAC3BhP,KAAKhG,SAASkV,eAAiBF,GAE/BhP,KAAKhG,SAASkV,eAAiB,SAC/BlP,KAAKhG,SAASmV,eAAiBH,GAGjC/W,QAAQC,IAAI,UAAW8H,KAAKhG,SAASkV,eAAgBlP,KAAKhG,SAASmV,eACrE,CAOA,GAL6B,OAAzBvN,EAAKwN,sBAAqD3O,IAAzBmB,EAAKwN,kBACxCpP,KAAKhG,SAASoV,gBAAkB5F,SAAS5H,EAAKwN,iBAC9CnX,QAAQC,IAAI,UAAW8H,KAAKhG,SAASoV,kBAGnCxN,EAAKyN,iBAAkB,CACzB,IAAMC,EAAY1N,EAAKyN,kBAAoB,GACrC7B,EAAa8B,EAAU7B,QAAQ,KAErC,GAAID,GAAc,EAAG,CACnB,IAAM+B,EAAWD,EAAU3B,UAAU,EAAGH,GAAYjJ,MAAM,KAC1DvE,KAAKhG,SAASqV,iBAAmBE,EACjCvP,KAAKhG,SAASwV,oBAAsBF,EAAU3B,UAAUH,EAAa,GAAGR,MAC1E,MACEhN,KAAKhG,SAASqV,iBAAmBC,EAAU/K,MAAM,KAAKmD,QAAO,SAAAjK,GAAA,MAAkB,KAAbA,EAAEuP,MAAa,IAGnF/U,QAAQC,IAAI,UAAW8H,KAAKhG,SAASqV,kBACrCpX,QAAQC,IAAI,UAAW8H,KAAKhG,SAASwV,oBACvC,CAGAxP,KAAKyP,sBAELzP,KAAKgD,SAAS2B,QAAQ,iBArMtB,MAFE1M,QAAQkS,KAAK,kBAwMjB,IAAC,kCAEqB,IAAAuF,EAAA,KACdC,EAAc,CAAC,aAAc,WAAY,gBAAiB,gBAAiB,mBAAoB,YAErGA,EAAYxJ,SAAQ,SAAAwB,GAClB,IAAM5L,EAAQ2T,EAAK1V,SAAS2N,GAG5B,GAAc,OAAV5L,QAA4B0E,IAAV1E,EAKtB,GAAqB,kBAAVA,EACT,IAEE,GAAIA,EAAMiR,OAAOvU,WAAW,MAAQsD,EAAMiR,OAAO4C,SAAS,KAAM,CAChE,IAAMC,EAAapK,KAAKC,MAAM3J,GAC1BkR,MAAMC,QAAQ2C,KAChBH,EAAK1V,SAAS2N,GAASkI,EACvB5X,QAAQC,IAAI,QAADN,OAAS+P,EAAK,aAAakI,GAExC,MAEK,GAAI9T,EAAMhE,SAAS,KAAM,CAC5B,IAAM8X,EAAa9T,EAAMwI,MAAM,KAAKqD,KAAI,SAAAhM,GAAG,OAAKA,EAAKoR,MAAM,IAC3D0C,EAAK1V,SAAS2N,GAASkI,EACvB5X,QAAQC,IAAI,QAADN,OAAS+P,EAAK,iBAAiBkI,EAC5C,CACF,CAAE,MAAOpS,GACPxF,QAAQgL,MAAM,aAADrL,OAAc+P,EAAK,KAAK5L,EAAO0B,GAEvCwP,MAAMC,QAAQwC,EAAK1V,SAAS2N,MAC/B+H,EAAK1V,SAAS2N,GAAS,GAE7B,MAGUsF,MAAMC,QAAQwC,EAAK1V,SAAS2N,MACpC1P,QAAQkS,KAAK,MAADvS,OAAO+P,EAAK,mBACxB+H,EAAK1V,SAAS2N,GAAS,GAE3B,IAGA1P,QAAQC,IAAI,YAAauN,KAAKqD,UAAU9I,KAAKhG,SAAU,KAAM,GAC/D,IAAC,sCAEyB,IAAA8V,EAAA,KAExBnH,YAAW,WAIT,GAHA1Q,QAAQC,IAAI,aAAc4X,EAAK9V,SAASsB,UAGpCwU,EAAK9V,SAASsB,UAAYwU,EAAK9V,SAASsB,SAAS2C,OAAS,EAAG,CAE/D,IAAM8R,EAAWD,EAAKvI,MAAMyI,iBACxBD,EACGA,EAAStV,YAA6C,IAA/BsV,EAAStV,WAAWwD,QAEhD6R,EAAKlN,WAAU,WACX,IAEF,IAAMqN,EAAgBxK,KAAKC,MAAMD,KAAKqD,UAAUgH,EAAK9V,SAASsB,WAE9DwU,EAAK9V,SAASsB,SAAW,GACzBwU,EAAKlN,WAAU,WACbkN,EAAK9V,SAASsB,SAAW2U,EACzBhY,QAAQC,IAAI,cAAe+X,EAC7B,GACE,CAAE,MAAOhN,GACPhL,QAAQgL,MAAM,eAAgBA,EAChC,CACJ,IAGAhL,QAAQC,IAAI,mBAEhB,CACF,GAAG,IACL,IAAC,yBAEY4J,GAAa,IAAAoO,EAAA,KACxBjY,QAAQC,IAAI,iBAAkB4J,GAC9B9B,KAAKgC,gBAAkBmO,aAAY,WACjCD,EAAKE,eAAetO,EACtB,GAAG,KAGH6G,YAAW,WACLuH,EAAKlO,kBACPqO,cAAcH,EAAKlO,iBACnBkO,EAAKlO,gBAAkB,KACpBkO,EAAK7T,0BACN6T,EAAKlN,SAAS2D,QAAQ,oBACtBuJ,EAAK7T,yBAA0B,EAG/B6T,EAAKrJ,wBAGX,GAAG,IACL,IAAC,2BAEoB/E,GAAa,IAAAwO,EAAA,YAAAjN,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAgN,IAAA,IAAA1M,EAAAjC,EAAA4O,EAAAC,EAAAC,EAAA,OAAApN,EAAAA,EAAAA,KAAAS,GAAA,SAAA4M,GAAA,eAAAA,EAAA1M,GAAA,OAG9B,OAH8B0M,EAAAzM,EAAA,EAE9BjM,QAAQC,IAAI,6CAADN,OAA8CkK,IACzD6O,EAAA1M,EAAA,EACuByC,EAAAA,WAAI3G,IAAI,qCAADnI,OAAsCkK,IAAc,OAArE,GAAP+B,EAAO8M,EAAAvM,EAERP,GAAaA,EAASjC,KAAI,CAAA+O,EAAA1M,EAAA,QACP,OAAtBhM,QAAQC,IAAI,WAAUyY,EAAAnM,EAAA,UAIlB5C,EAAOiC,EAASjC,KACtB3J,QAAQC,IAAI,cAAe0J,GAGrB4O,EAAyB5O,IAASA,EAAKjF,qBAAuBiF,EAAK0E,sBACnEmK,EAAiB7O,IAASA,EAAK/E,aAAe+E,EAAK/E,cAErD2T,GAA0BC,KAExBD,IACJF,EAAKtW,SAAS2C,oBAAsBiF,EAAKjF,qBAAuBiF,EAAK0E,sBAAwBgK,EAAKtW,SAAS2C,qBAGvG8T,IACJH,EAAKtW,SAAS6C,YAAc+E,EAAK/E,aAAe+E,EAAK/E,aAAeyT,EAAKtW,SAAS6C,aAI9EyT,EAAKtW,SAAS2C,qBACd2T,EAAKtW,SAAS6C,cAEhB5E,QAAQC,IAAI,oBACdmY,cAAcC,EAAKtO,iBACnBsO,EAAKtO,gBAAkB,KACrBsO,EAAKjU,yBAA0B,EAC/BiU,EAAKtN,SAAS2B,QAAQ,kBAE1BgM,EAAA1M,EAAA,eAAA0M,EAAAzM,EAAA,EAAAwM,EAAAC,EAAAvM,EAEAnM,QAAQgL,MAAM,WAAUyN,GAAQ,cAAAC,EAAAnM,EAAA,MAAA+L,EAAA,iBAxCFlN,EA0ClC,IAAC,wBACiBuN,GAAW,IAAAC,EAAA,YAAAxN,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAuN,IAAA,IAAAC,EAAA,OAAAzN,EAAAA,EAAAA,KAAAS,GAAA,SAAAiN,GAAA,eAAAA,EAAA/M,GAAA,cAAA+M,EAAA9M,EAAA,EAAA8M,EAAA/M,EAAA,EAEf4M,EAAKjI,QAAQtE,KAAK,CACpB7E,KAAM,qBACNiD,OAAQ,CAAEuO,OAAQJ,EAAKrO,OAAOE,OAAOuO,OAAQC,UAAWN,KAC1D,OACFC,EAAKM,iBAAmBP,EAASI,EAAA/M,EAAA,eAAA+M,EAAA9M,EAAA,EAAA6M,EAAAC,EAAA5M,EAEjCnM,QAAQgL,MAAM,qBAAoB8N,GAClCF,EAAK7N,SAASC,MAAM,YAAW,cAAA+N,EAAAxM,EAAA,MAAAsM,EAAA,iBATVzN,EAW7B,IAAC,mCAGCpL,QAAQC,IAAI,oBAGP8H,KAAKhG,SAAS2C,sBACjBqD,KAAKhG,SAAS2C,oBAAsB,iDAIjCqD,KAAKhG,SAAS6C,cACjBmD,KAAKhG,SAAS6C,YAAc,6CAG9BmD,KAAKgD,SAAS2D,QAAQ,oBACxB,KAEFyK,cAAa,WAEPpR,KAAKgC,kBACPqO,cAAcrQ,KAAKgC,iBACnBhC,KAAKgC,gBAAkB,KACvB/J,QAAQC,IAAI,WAEhB,GKvvCI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASmZ,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/utils/apiHelpers.js", "webpack://medical-annotation-frontend/./src/utils/imageHelper.js", "webpack://medical-annotation-frontend/./src/views/CaseStructuredForm.vue", "webpack://medical-annotation-frontend/./node_modules/@babel/runtime/helpers/esm/regeneratorValues.js", "webpack://medical-annotation-frontend/./src/components/common/ConfirmDialog.vue", "webpack://medical-annotation-frontend/./src/components/common/ConfirmDialog.vue?4a25", "webpack://medical-annotation-frontend/./src/utils/formatters.js", "webpack://medical-annotation-frontend/./src/views/CaseStructuredForm.vue?2703"], "sourcesContent": ["/**\r\n * API 工具函数\r\n * 提供API请求相关的通用功能\r\n */\r\nimport { storageUtils } from './storeHelpers';\r\n\r\n/**\r\n * 标准化API路径，确保路径格式一致\r\n * @param {string} url 原始URL\r\n * @returns {string} 标准化后的URL\r\n */\r\nexport function normalizePath(url) {\r\n  if (!url) return '';\r\n  \r\n  // 确保URL以/开头\r\n  if (!url.startsWith('/')) {\r\n    url = '/' + url;\r\n  }\r\n  \r\n  // 处理双斜杠的情况\r\n  url = url.replace(/\\/+/g, '/');\r\n  \r\n  return url;\r\n}\r\n\r\n/**\r\n * 获取用户ID参数，优先使用数字ID\r\n * @param {string|number} userId 用户ID\r\n * @returns {string|number} 处理后的用户ID\r\n */\r\nexport function getUserIdParam(userId) {\r\n  return userId || '';\r\n}\r\n\r\n/**\r\n * 确保对象中的指定字段为数字类型\r\n * @param {object} obj 要处理的对象\r\n * @param {array} fields 要确保为数字的字段列表\r\n * @returns {object} 处理后的对象\r\n */\r\nexport function ensureNumericFields(obj, fields) {\r\n  if (!obj || typeof obj !== 'object') return obj;\r\n  \r\n  const result = { ...obj };\r\n  \r\n  fields.forEach(field => {\r\n    if (result[field] !== undefined) {\r\n      const parsedValue = parseInt(result[field], 10);\r\n      if (!isNaN(parsedValue)) {\r\n        result[field] = parsedValue;\r\n      }\r\n    }\r\n  });\r\n  \r\n  return result;\r\n}\r\n\r\n/**\r\n * 获取认证头信息\r\n * @returns {object} 包含认证信息的头部对象\r\n */\r\nexport function getAuthHeaders() {\r\n  const headers = {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json'\r\n  };\r\n  \r\n  // 从本地存储获取用户信息\r\n  const user = storageUtils.getFromStorage('user');\r\n  if (user) {\r\n    // 使用用户ID生成简化的认证头\r\n    const userId = user.id || user.customId || '';\r\n    headers['Authorization'] = `Bearer user_${userId}`;\r\n    headers['X-User-ID'] = userId;\r\n  }\r\n  \r\n  return headers;\r\n}\r\n\r\n/**\r\n * 处理API错误\r\n * @param {Error} error 错误对象\r\n * @returns {object} 格式化的错误信息\r\n */\r\nexport function handleApiError(error) {\r\n  const errorInfo = {\r\n    message: error.message || '未知错误',\r\n    status: error.response ? error.response.status : null,\r\n    data: error.response ? error.response.data : null\r\n  };\r\n  \r\n  // 记录错误信息\r\n  console.error('[API错误]', errorInfo);\r\n  \r\n  return errorInfo;\r\n}\r\n\r\n/**\r\n * 向URL添加时间戳，防止缓存\r\n * @param {string} url 原始URL\r\n * @returns {string} 添加时间戳后的URL\r\n */\r\nexport function addTimestamp(url) {\r\n  const timestamp = `_t=${Date.now()}`;\r\n  if (url.includes('?')) {\r\n    return `${url}&${timestamp}`;\r\n  } else {\r\n    return `${url}?${timestamp}`;\r\n  }\r\n}\r\n\r\nexport default {\r\n  normalizePath,\r\n  getUserIdParam,\r\n  ensureNumericFields,\r\n  getAuthHeaders,\r\n  handleApiError,\r\n  addTimestamp\r\n}; ", "/**\n * 图像路径处理工具\n * 用于统一处理图像URL，解决路径不一致、中文编码等问题\n */\n\nimport { API_BASE_URL, API_CONTEXT_PATH, isLocalhost } from '../config/api.config';\nimport { storageUtils } from './storeHelpers';\nimport { addTimestamp } from './apiHelpers';\n\n// 图像大小限制\nconst MAX_IMAGE_WIDTH = 700;\nconst MAX_IMAGE_HEIGHT = 700;\n\n/**\n * 获取图像URL，添加防缓存参数并处理不同的路径格式\n * @param {string} url 原始图像URL\n * @returns {string} 处理后的URL\n */\nexport function getImageUrl(url) {\n  if (!url) return '';\n  \n  console.log('[ImageHelper] 处理图像路径:', url);\n  \n  // 处理文件系统路径\n  if (/^[a-zA-Z]:\\\\/.test(url)) {\n    console.log('[ImageHelper] 检测到文件系统路径');\n    const encodedPath = encodeURIComponent(url);\n    \n    // 使用当前域名而非硬编码地址\n    const baseUrl = isLocalhost ? `${API_BASE_URL}` : '';\n    return addTimestamp(`${baseUrl}/medical/image/system-path?path=${encodedPath}`);\n  }\n  \n  // 如果URL已经是完整的HTTP(S)地址，直接使用\n  if (url.startsWith('http://') || url.startsWith('https://')) {\n    return addTimestamp(url);\n  }\n  \n  // 如果URL是data:开头的数据URI，直接返回\n  if (url.startsWith('data:')) {\n    return url;\n  }\n  \n  // 处理相对路径\n  let finalUrl = url;\n  \n  if (url.startsWith('/medical')) {\n    console.log('[ImageHelper] 检测到相对路径，添加后端服务器地址');\n    // 根据环境使用适当的基础URL\n    finalUrl = isLocalhost ? `${API_BASE_URL}${url}` : url;\n  } else if (url.startsWith('/')) {\n    console.log('[ImageHelper] 检测到其他相对路径，添加后端服务器地址和上下文路径');\n    // 根据环境使用适当的基础URL\n    finalUrl = isLocalhost ? `${API_BASE_URL}${API_CONTEXT_PATH}${url}` : `${API_CONTEXT_PATH}${url}`;\n  }\n  \n  console.log('[ImageHelper] 处理后的URL:', finalUrl);\n  return addTimestamp(finalUrl);\n}\n\n/**\n * 从图像路径中提取文件名\n * @param {string} path - 图像路径\n * @returns {string} 文件名\n */\nexport function getImageFilename(path) {\n  if (!path) return '';\n  \n  const parts = path.split('/');\n  return parts[parts.length - 1];\n}\n\n/**\n * 判断是否是图像文件\n * @param {string} filename - 文件名\n * @returns {boolean} 是否是图像文件\n */\nexport function isImageFile(filename) {\n  if (!filename) return false;\n  \n  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\n  const lowerFilename = filename.toLowerCase();\n  \n  return imageExtensions.some(ext => lowerFilename.endsWith(ext));\n}\n\n/**\n * 调整图像大小，确保不超过最大宽高限制\n * @param {HTMLImageElement|File} imageSource - 图像元素或文件对象\n * @param {Function} callback - 回调函数，接收处理后的图像数据(DataURL或Blob)和尺寸信息\n * @param {Object} options - 选项\n * @param {number} options.maxWidth - 最大宽度，默认700px\n * @param {number} options.maxHeight - 最大高度，默认700px\n * @param {string} options.outputFormat - 输出格式，'dataUrl'或'blob'，默认'dataUrl'\n * @param {string} options.imageType - 图像类型，默认'image/jpeg'\n * @param {number} options.quality - 压缩质量，0-1之间，默认0.9\n */\nexport function resizeImage(imageSource, callback, options = {}) {\n  const settings = {\n    maxWidth: options.maxWidth || MAX_IMAGE_WIDTH,\n    maxHeight: options.maxHeight || MAX_IMAGE_HEIGHT,\n    outputFormat: options.outputFormat || 'dataUrl',\n    imageType: options.imageType || 'image/jpeg',\n    quality: options.quality || 0.9\n  };\n  \n  // 处理File对象\n  if (imageSource instanceof File) {\n    const reader = new FileReader();\n    reader.onload = function(e) {\n      const img = new Image();\n      img.onload = function() {\n        processImageResize(img, settings, callback);\n      };\n      img.src = e.target.result;\n    };\n    reader.readAsDataURL(imageSource);\n    return;\n  }\n  \n  // 处理Image对象\n  if (imageSource instanceof HTMLImageElement) {\n    if (imageSource.complete) {\n      processImageResize(imageSource, settings, callback);\n    } else {\n      imageSource.onload = function() {\n        processImageResize(imageSource, settings, callback);\n      };\n    }\n    return;\n  }\n  \n  // 处理其他情况（字符串URL或DataURL）\n  if (typeof imageSource === 'string') {\n    const img = new Image();\n    img.onload = function() {\n      processImageResize(img, settings, callback);\n    };\n    img.src = imageSource;\n    return;\n  }\n  \n  // 不支持的类型\n  console.error('不支持的图像源类型:', imageSource);\n  callback(null, { error: '不支持的图像源类型' });\n}\n\n/**\n * 处理图像大小调整的核心逻辑\n * @private\n */\nfunction processImageResize(img, settings, callback) {\n  // 获取原始尺寸\n  const originalWidth = img.naturalWidth || img.width;\n  const originalHeight = img.naturalHeight || img.height;\n  \n  // 计算调整后的尺寸，保持宽高比\n  let newWidth = originalWidth;\n  let newHeight = originalHeight;\n  \n  // 如果图像超出最大限制，按比例缩小\n  if (originalWidth > settings.maxWidth || originalHeight > settings.maxHeight) {\n    const widthRatio = settings.maxWidth / originalWidth;\n    const heightRatio = settings.maxHeight / originalHeight;\n    const ratio = Math.min(widthRatio, heightRatio);\n    \n    newWidth = Math.floor(originalWidth * ratio);\n    newHeight = Math.floor(originalHeight * ratio);\n  }\n  \n  // 创建Canvas绘制调整后的图像\n  const canvas = document.createElement('canvas');\n  const ctx = canvas.getContext('2d');\n  canvas.width = newWidth;\n  canvas.height = newHeight;\n  \n  // 绘制图像\n  ctx.drawImage(img, 0, 0, newWidth, newHeight);\n  \n  // 输出数据\n  const sizeInfo = {\n    originalWidth,\n    originalHeight,\n    newWidth,\n    newHeight,\n    resized: (originalWidth !== newWidth || originalHeight !== newHeight)\n  };\n  \n  // 根据需要的输出格式返回结果\n  if (settings.outputFormat === 'blob') {\n    canvas.toBlob(\n      (blob) => callback(blob, sizeInfo),\n      settings.imageType,\n      settings.quality\n    );\n  } else {\n    // 默认返回DataURL\n    const dataUrl = canvas.toDataURL(settings.imageType, settings.quality);\n    callback(dataUrl, sizeInfo);\n  }\n}\n\n/**\n * 转换相对坐标到绝对坐标\n * @param {Object} annotation 标注数据\n * @param {number} imageWidth 图像宽度\n * @param {number} imageHeight 图像高度\n * @returns {Object} 转换后的标注数据\n */\nexport function convertToAbsoluteCoordinates(annotation, imageWidth, imageHeight) {\n  // 判断是否是相对坐标（0-1范围内）\n  const isRelativeCoordinates = \n    annotation.x <= 1 && annotation.x >= 0 && \n    annotation.y <= 1 && annotation.y >= 0 && \n    annotation.width <= 1 && annotation.width >= 0 && \n    annotation.height <= 1 && annotation.height >= 0;\n  \n  if (!isRelativeCoordinates) {\n    return annotation; // 已经是绝对坐标，直接返回\n  }\n  \n  // 计算绝对坐标\n  const x = Math.round(annotation.x * imageWidth);\n  const y = Math.round(annotation.y * imageHeight);\n  const width = Math.round(annotation.width * imageWidth);\n  const height = Math.round(annotation.height * imageHeight);\n  \n  return {\n    ...annotation,\n    x,\n    y,\n    width,\n    height,\n    // 保存原始的相对坐标\n    normalizedX: annotation.x,\n    normalizedY: annotation.y,\n    normalizedWidth: annotation.width,\n    normalizedHeight: annotation.height\n  };\n}\n\n/**\n * 创建用户ID一致性处理工具\n */\nexport const UserIdConsistencyFixer = {\n  /**\n   * 获取一致的用户ID，优先使用customId\n   * @param {Object} user 用户对象\n   * @returns {string|number} 用户ID\n   */\n  getConsistentUserId(user) {\n    if (!user) return null;\n    return user.customId || user.id || null;\n  },\n  \n  /**\n   * 获取用户对象，确保包含一致的ID\n   * @returns {Object} 用户对象\n   */\n  getCurrentUser() {\n    const user = storageUtils.getFromStorage('user', {});\n      return {\n        ...user,\n        consistentId: this.getConsistentUserId(user)\n      };\n  }\n};\n\nexport default {\n  getImageUrl,\n  getImageFilename,\n  isImageFile,\n  resizeImage,\n  MAX_IMAGE_WIDTH,\n  MAX_IMAGE_HEIGHT\n}; ", "<template>\n  <div class=\"structured-form-container\">\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <h2>病例信息填写</h2>\n      </div>\n      <div class=\"header-actions\">\n        <!-- 删除顶部的按钮，只保留底部的按钮 -->\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"text-center my-5\">\n      <div class=\"spinner-border\" role=\"status\">\n        <span class=\"visually-hidden\">加载中...</span>\n      </div>\n    </div>\n\n    <div v-else class=\"form-content\">\n      <div class=\"form-note\">\n        <el-alert\n          title=\"注意：带有 * 标记的字段为必填项，其他字段为选填项。治疗与注意事项中的四个字段必须填写。\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        />\n      </div>\n      <el-form ref=\"caseForm\" :model=\"formData\" label-position=\"top\" :rules=\"rules\">\n        \n        <!-- 基础信息 -->\n        <div class=\"form-section\">\n          <h3 class=\"section-title\">基础信息</h3>\n          <div class=\"adaptive-fields-container\">\n            <el-form-item label=\"患者年龄\" prop=\"patientAge\">\n              <el-input-number v-model=\"formData.patientAge\" :min=\"0\" :max=\"120\" />\n            </el-form-item>\n            <el-form-item label=\"性别\" prop=\"gender\">\n              <el-radio-group v-model=\"formData.gender\">\n                <el-radio label=\"男\">男</el-radio>\n                <el-radio label=\"女\">女</el-radio>\n              </el-radio-group>\n            </el-form-item>\n             <el-form-item label=\"类型\" prop=\"originType\">\n              <el-radio-group v-model=\"formData.originType\">\n                <el-radio label=\"先天性\">先天性</el-radio>\n                <el-radio label=\"后天性\">后天性</el-radio>\n              </el-radio-group>\n            </el-form-item>\n            <el-form-item label=\"病变部位\" prop=\"bodyPart\">\n              <el-select v-model=\"formData.bodyPart\" placeholder=\"请选择病变部位\" filterable>\n                <el-option\n                  v-for=\"item in partOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"颜色\" prop=\"color\">\n              <el-select v-model=\"formData.color\" placeholder=\"请选择病灶颜色\" filterable>\n                <el-option\n                  v-for=\"item in colorOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"血管质地\" prop=\"vesselTexture\">\n              <el-select v-model=\"formData.vesselTexture\" placeholder=\"请选择血管质地\">\n                <el-option label=\"质软\" value=\"soft\" />\n                <el-option label=\"质韧\" value=\"elastic\" />\n                <el-option label=\"质硬\" value=\"hard\" />\n                <el-option label=\"囊性\" value=\"cystic\" />\n                <el-option label=\"可压缩\" value=\"compressible\" />\n              </el-select>\n            </el-form-item>\n          </div>\n        </div>\n        \n        <!-- 治疗与注意事项 -->\n        <div class=\"form-section\">\n          <h3 class=\"section-title\">\n            治疗与注意事项 <span class=\"required-indicator\">*</span>\n            <el-tag v-if=\"isLoadingRecommendation\" type=\"warning\" size=\"small\">\n              <i class=\"el-icon-loading\"></i> 正在获取AI建议...\n            </el-tag>\n          </h3>\n          <div class=\"form-grid\">\n            <el-form-item label=\"治疗建议 *\" prop=\"treatmentSuggestion\">\n              <el-input v-model=\"formData.treatmentSuggestion\" type=\"textarea\" :rows=\"5\" placeholder=\"医生填写的最终治疗方案\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"注意事项 *\" prop=\"precautions\">\n              <el-input v-model=\"formData.precautions\" type=\"textarea\" :rows=\"5\" placeholder=\"需要患者注意的事项（如破溃出血处理、日常注意等）\"></el-input>\n            </el-form-item>\n          </div>\n        </div>\n        \n        <!-- 操作按钮 -->\n        <div class=\"form-actions\">\n          <el-button type=\"primary\" @click=\"saveData('REVIEWED')\" :loading=\"savingDraft\">保存草稿</el-button>\n          <el-button type=\"success\" @click=\"submitForm\" :loading=\"submitting\">提交完成</el-button>\n          <el-button v-if=\"saveError\" type=\"warning\" @click=\"retrySave\">重试保存</el-button>\n        </div>\n        \n      </el-form>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapActions, mapGetters } from 'vuex';\nimport api from '@/utils/api';\nimport ConfirmDialog from '@/components/common/ConfirmDialog.vue';\nimport { formatDate } from '../utils/formatters';\nimport { getImageUrl } from '../utils/imageHelper';\nimport { ElMessage } from 'element-plus';\nimport axios from 'axios';\n\nexport default {\n  name: 'CaseStructuredForm',\n  components: {\n    ConfirmDialog\n  },\n  data() {\n    return {\n      loading: true,\n      imageId: null,\n      diagnosisId: null,\n      saveDialogVisible: false,\n      pollingInterval: null,\n      isLoadingRecommendation: false,\n      savingDraft: false,\n      submitting: false,\n      saveError: false,\n      partOptions: [\n        { value: '唇部', label: '唇部' },\n        { value: '脑部', label: '脑部' },\n        { value: '颈部', label: '颈部' },\n        { value: '脸部', label: '脸部' },\n        { value: '掌部', label: '掌部' },\n        { value: '手臂', label: '手臂' },\n        { value: '胸部', label: '胸部' },\n        { value: '腹部', label: '腹部' },\n        { value: '腿部', label: '腿部' },\n        { value: '阴部', label: '阴部' },\n        { value: '背部', label: '背部' },\n        { value: '耳部', label: '耳部' },\n        { value: '枕部', label: '枕部' },\n        { value: '眼部', label: '眼部' },\n        { value: '脚底', label: '脚底' },\n        { value: '脚背', label: '脚背' },\n        { value: '肩部', label: '肩部' },\n        { value: '舌部', label: '舌部' },\n        { value: '屁股', label: '屁股' },\n        { value: '口腔', label: '口腔' },\n        { value: '鼻部', label: '鼻部' }\n      ],\n      colorOptions: [\n        { value: '褐色', label: '褐色' },\n        { value: '黑色', label: '黑色' },\n        { value: '红色', label: '红色' },\n        { value: '白色', label: '白色' },\n        { value: '正常', label: '正常' },\n        { value: '玫红', label: '玫红' }\n      ],\n      formData: {\n        patientAge: null,\n        gender: '',\n        originType: '',\n        bodyPart: '',\n        color: '',\n        vesselTexture: '',\n        treatmentSuggestion: '',\n        precautions: ''\n      },\n      rules: {\n        patientAge: [{ required: false, message: '请输入患者年龄', trigger: 'blur' }],\n        gender: [{ required: false, message: '请选择性别', trigger: 'change' }],\n        originType: [{ required: false, message: '请选择类型', trigger: 'change' }],\n        bodyPart: [{ required: false, message: '请输入病变部位', trigger: 'blur' }],\n        color: [{ required: false, message: '请输入颜色', trigger: 'blur' }],\n        // 治疗与注意事项必填字段\n        treatmentSuggestion: [{ required: true, message: '请填写治疗建议', trigger: 'blur' }],\n        precautions: [{ required: true, message: '请填写注意事项', trigger: 'blur' }]\n      },\n      availableEndpoints: []\n    };\n  },\n  computed: {\n    ...mapGetters(['getAnnotationProgress'])\n  },\n  created() {\n    // 检查URL中是否有诊断ID参数 (同时检查query和params，增强兼容性)\n    const diagnosisId = this.$route.query.diagnosisId || this.$route.params.id || this.$route.query.imageId;\n    if (diagnosisId) {\n      console.log('从URL获取到诊断/图像ID:', diagnosisId);\n      this.diagnosisId = diagnosisId;\n      this.imageId = diagnosisId;\n      // 将数据加载推迟到下一个Tick，以避免与应用初始化（如身份验证）产生竞态条件\n      this.$nextTick(() => {\n        // 先检查可用的API端点\n        this.checkAvailableEndpoints(diagnosisId).then(() => {\n          this.loadCaseData(diagnosisId);\n        });\n      });\n    } else {\n      this.loading = false;\n      this.$message.error(\"缺少诊断ID，无法加载病例数据。\");\n    }\n  },\n  methods: {\n    ...mapActions(['saveProgress', 'completeAnnotation']),\n    formatDate,\n    \n    // 检查可用的API端点\n    async checkAvailableEndpoints(diagnosisId) {\n      console.log('检查可用的API端点...');\n      const endpoints = [\n        `/medical/api/hemangioma-diagnoses/${diagnosisId}`,\n        `/medical/api/images/${diagnosisId}/structured-form`\n      ];\n      \n      // 存储可用的端点\n      this.availableEndpoints = [];\n      \n      for (const endpoint of endpoints) {\n        try {\n          // 使用OPTIONS请求检查端点是否支持\n          const response = await axios.options(endpoint);\n          console.log(`端点 ${endpoint} 可用，支持的方法:`, response.headers['allow'] || 'GET, POST, PUT');\n          this.availableEndpoints.push({\n            url: endpoint,\n            methods: (response.headers['allow'] || 'GET, POST, PUT').split(', ')\n          });\n        } catch (error) {\n          // 如果OPTIONS请求失败，尝试HEAD请求\n          try {\n            const headResponse = await axios.head(endpoint);\n            console.log(`端点 ${endpoint} 可用 (通过HEAD请求)`);\n            this.availableEndpoints.push({\n              url: endpoint,\n              methods: ['GET']  // 假设至少支持GET\n            });\n          } catch (headError) {\n            console.log(`端点 ${endpoint} 不可用:`, error.message);\n          }\n        }\n      }\n      \n      console.log('可用的API端点:', this.availableEndpoints);\n    },\n    async loadCaseData(diagnosisId) {\n      this.loading = true;\n      console.log('开始加载病例数据, 诊断ID:', diagnosisId);\n      \n      // 添加时间戳以避免缓存问题\n      const timestamp = Date.now();\n      let success = false;\n      \n      // 准备认证头\n      const headers = {};\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      const token = localStorage.getItem('token');\n      \n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      \n      if (user && (user.id || user.customId)) {\n        headers['X-User-Id'] = user.id || user.customId;\n        headers['X-User-Role'] = user.role || 'USER';\n      }\n      \n      // 尝试不同的API路径获取数据\n      const apiPaths = [\n        `/medical/api/hemangioma-diagnoses/${diagnosisId}`,\n        `/medical/api/diagnoses/${diagnosisId}`,\n        `/api/hemangioma-diagnoses/${diagnosisId}`,\n        `/hemangioma-diagnoses/${diagnosisId}`,\n        `/api/diagnoses/${diagnosisId}`\n        // 移除不存在的API路径，避免404错误\n        // `/medical/api/images/${diagnosisId}/structured-form`\n      ];\n      \n      for (const path of apiPaths) {\n        if (success) break;\n        \n        try {\n          console.log(`尝试从路径获取数据: ${path}?t=${timestamp}`);\n          const response = await axios.get(`${path}?t=${timestamp}`, { headers });\n          \n          if (response && response.data) {\n            const data = response.data;\n            console.log('成功获取诊断数据:', data);\n            this.diagnosisId = data.id || diagnosisId;\n            \n            // 更新表单数据\n            const newFormData = {\n              // 先设置默认值\n              patientAge: null,\n              gender: '',\n              originType: '',\n              bodyPart: '',\n              color: '',\n              vesselTexture: '',\n              treatmentSuggestion: '',\n              precautions: ''\n            };\n            \n            // 然后用API返回的数据覆盖默认值\n            Object.keys(newFormData).forEach(key => {\n              if (data[key] !== undefined) {\n                newFormData[key] = data[key];\n              }\n            });\n            \n            // 特殊处理可能的不同命名\n            if (data.patient_age !== undefined) newFormData.patientAge = data.patient_age;\n            if (data.vessel_texture !== undefined) newFormData.vesselTexture = data.vessel_texture;\n            if (data.treatment_suggestion !== undefined) newFormData.treatmentSuggestion = data.treatment_suggestion;\n            if (data.precautions !== undefined) newFormData.precautions = data.precautions;\n            \n            // 替换整个表单数据对象，确保Vue能检测到变化\n            this.formData = newFormData;\n            \n            // 强制组件重新渲染\n            this.$nextTick(() => {\n              console.log('表单数据已更新，强制重新渲染:', this.formData);\n              this.$forceUpdate();\n            });\n            \n            this.$message.success(\"病例数据已加载，请核对并完善。\");\n            success = true;\n            \n            // 如果缺少大模型生成的建议字段，开始轮询获取\n            if (!data.treatmentSuggestion || !data.precautions) {\n              this.isLoadingRecommendation = true;\n              this.startPolling(diagnosisId);\n            }\n            \n            break; // 成功获取数据后跳出循环\n          }\n        } catch (error) {\n          console.error(`从路径 ${path} 加载失败:`, error);\n        }\n      }\n      \n      // 如果所有API路径都失败，尝试使用api.get方法\n      if (!success) {\n        try {\n          console.log('所有直接路径均失败，尝试使用api.get方法');\n          const response = await api.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);\n          if (response && response.data) {\n            const data = response.data;\n            console.log('使用api.get成功获取数据:', data);\n            \n            // 更新表单数据（同上）\n            const newFormData = { ...this.formData };\n            \n            // 从API响应中提取数据\n            Object.keys(newFormData).forEach(key => {\n              if (data[key] !== undefined) {\n                newFormData[key] = data[key];\n              }\n            });\n            \n            // 特殊处理可能的不同命名\n            if (data.patient_age !== undefined) newFormData.patientAge = data.patient_age;\n            if (data.vessel_texture !== undefined) newFormData.vesselTexture = data.vessel_texture;\n            if (data.treatment_suggestion !== undefined) newFormData.treatmentSuggestion = data.treatment_suggestion;\n            if (data.precautions !== undefined) newFormData.precautions = data.precautions;\n            \n            this.formData = newFormData;\n            this.$forceUpdate();\n            this.$message.success(\"病例数据已加载，请核对并完善。\");\n            success = true;\n          }\n        } catch (apiError) {\n          console.error('使用api.get加载失败:', apiError);\n        }\n      }\n      \n      // 如果所有尝试都失败，检查是否有本地备份\n      if (!success) {\n        try {\n          const backupKey = `formData_backup_${diagnosisId}`;\n          const backupData = localStorage.getItem(backupKey);\n          \n          if (backupData) {\n            console.log('找到本地备份数据，尝试恢复');\n            const parsedBackup = JSON.parse(backupData);\n            this.formData = parsedBackup;\n            this.$forceUpdate();\n            this.$message.warning('从本地备份恢复了表单数据，请注意保存');\n            success = true;\n          }\n        } catch (backupError) {\n          console.error('恢复本地备份失败:', backupError);\n        }\n      }\n      \n      // 如果所有尝试都失败，提供默认值\n      if (!success) {\n        console.error('所有数据获取尝试均失败，提供默认值');\n        this.provideDefaultValues();\n        this.$message.error('无法从服务器加载数据，已提供默认值，请手动填写。');\n      }\n      \n      this.loading = false;\n    },\n    async submitForm() {\n      try {\n        // 表单验证\n        await this.$refs.caseForm.validate();\n        \n        // 额外检查治疗与注意事项的必填字段\n        const requiredFields = [\n          'treatmentSuggestion', \n          'precautions'\n        ];\n        \n        const missingFields = requiredFields.filter(field => !this.formData[field]);\n        \n        if (missingFields.length > 0) {\n          // 构建错误消息\n          const fieldNames = {\n            treatmentSuggestion: '治疗建议',\n            precautions: '注意事项'\n          };\n          \n          const missingFieldNames = missingFields.map(field => fieldNames[field]);\n          this.$message.warning(`请填写必填项: ${missingFieldNames.join('、')}`);\n          return;\n        }\n\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        const userRole = user ? user.role : null;\n\n        let finalStatus;\n        if (userRole === 'ADMIN' || userRole === 'REVIEWER') {\n          finalStatus = 'APPROVED'; // 已通过\n        } else {\n          finalStatus = 'SUBMITTED'; // 待审核\n        }\n        \n        await this.saveData(finalStatus);\n      } catch (error) {\n        this.$message.warning('表单验证失败，请检查必填项。');\n        return;\n      }\n    },\n    async saveData(status) {\n      if (!this.diagnosisId) {\n        this.$message.error(\"未找到诊断记录ID，无法保存。\");\n        return;\n      }\n\n      if (status === 'SUBMITTED' || status === 'APPROVED') {\n        this.submitting = true;\n      } else {\n        this.savingDraft = true;\n      }\n      this.saveError = false;\n      \n      console.log(`开始保存数据，诊断ID: ${this.diagnosisId}, 状态: ${status}`);\n      const loading = this.$loading({ lock: true, text: '正在保存...' });\n      \n      const headers = { \n        'Content-Type': 'application/json', \n        'Accept': 'application/json' \n      };\n      const token = localStorage.getItem('token');\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      if (user && (user.id || user.customId)) {\n        headers['X-User-Id'] = user.id || user.customId;\n        headers['X-User-Role'] = user.role || 'USER';\n      }\n\n      const formDataWithAction = {\n        ...this.formData,\n        status: status\n      };\n\n      const url = `/medical/api/hemangioma-diagnoses/${this.diagnosisId}`;\n      \n      try {\n        console.log(`尝试使用 PUT 方法保存到端点: ${url}`);\n        const response = await axios.put(url, formDataWithAction, { headers });\n        console.log(`成功保存数据, 响应:`, response.data);\n\n        loading.close();\n        this.savingDraft = false;\n        this.submitting = false;\n\n        localStorage.removeItem(`formData_backup_${this.diagnosisId}`);\n        \n        let message;\n        if (status === 'APPROVED') {\n          message = \"病例信息已直接审核通过！\";\n        } else if (status === 'SUBMITTED') {\n          message = \"病例信息已提交，等待审核！\";\n        } else { // REVIEWED\n          message = \"信息已保存并标记为已标注！\";\n        }\n        this.$message.success(message);\n\n        setTimeout(() => {\n          this.$router.push('/app/dashboard');\n        }, 1500);\n\n      } catch (error) {\n        console.error(`保存到端点 ${url} 失败:`, error.response ? error.response.data : error.message);\n        \n        loading.close();\n        this.savingDraft = false;\n        this.submitting = false;\n        this.saveError = true;\n        \n        const errorMessage = error.response?.data?.error || error.message || \"未知错误\";\n        this.$message.error(`保存失败: ${errorMessage}`);\n        \n        try {\n          localStorage.setItem(`formData_backup_${this.diagnosisId}`, JSON.stringify(this.formData));\n          this.$message.warning('已在本地备份您的表单数据，可点击\"重试保存\"按钮再次尝试。');\n        } catch (e) {\n          console.error(\"本地备份也失败:\", e);\n        }\n      }\n    },\n    returnToAnnotation() {\n      this.$router.back();\n    },\n    // 重试保存\n    retrySave() {\n      this.saveData('REVIEWED');\n    },\n    // 获取用于显示的图像URL，支持离线模式\n    getImageUrl(path) {\n      // 检查是否为离线模式URL\n      if (path && path.startsWith('blob:')) {\n        return path;\n      }\n      \n      // 检查localStorage中是否有离线图片\n      const imageId = this.imageId;\n      if (imageId) {\n        const offlineImage = localStorage.getItem(`offline_image_${imageId}`);\n        if (offlineImage) {\n          console.log('表单页面：使用本地存储的离线图片');\n          return offlineImage;\n        }\n      }\n      \n      // 使用原始URL\n      return getImageUrl(path);\n    },\n    // 加载图像数据\n    fetchImageData() {\n      if (!this.imageId) {\n        this.$message.error('未指定图像ID，无法加载数据');\n        return;\n      }\n      \n      // 检查是否为测试模式或离线模式\n      const isTestMode = this.$route.query.testMode === 'true';\n      const offlineMode = localStorage.getItem('offlineMode') === 'true';\n      \n      if (isTestMode || offlineMode) {\n        console.log('使用测试模式或离线模式加载数据');\n        \n        // 尝试从localStorage获取离线图片\n        const offlineImage = localStorage.getItem(`offline_image_${this.imageId}`);\n        \n        // 创建模拟数据\n        this.imageData = {\n          id: parseInt(this.imageId),\n          original_name: `离线图片_${this.imageId}.jpg`,\n          created_at: new Date().toISOString(),\n          path: offlineImage || ''\n        };\n        \n        // 如果有之前保存的进度，恢复已填写的表单数据\n        const savedProgress = this.getAnnotationProgress;\n        if (savedProgress && savedProgress.formData) {\n          this.formData = { ...this.formData, ...savedProgress.formData };\n          this.$message.info('已恢复之前填写的表单数据');\n        }\n        \n        this.loaded = true;\n        return;\n      }\n      \n      // 正常API请求\n      this.loading = true;\n      api.images.getOne(this.imageId)\n        .then(response => {\n          this.imageData = response.data;\n          this.loading = false;\n          this.loaded = true;\n          \n          // 从后端返回的独立字段中映射数据到表单\n          this.mapImageDataToForm(this.imageData);\n        })\n        .catch(error => {\n          console.error('加载图像数据失败', error);\n          \n          // 如果API失败但是离线模式启用，使用模拟数据\n          if (localStorage.getItem('offlineMode') === 'true') {\n            console.warn('API失败但启用了离线模式，使用模拟数据');\n            \n            const offlineImage = localStorage.getItem(`offline_image_${this.imageId}`);\n            \n            if (offlineImage) {\n              this.imageData = {\n                id: parseInt(this.imageId),\n                original_name: `离线图片_${this.imageId}.jpg`,\n                created_at: new Date().toISOString(),\n                path: offlineImage\n              };\n              \n              this.$message.warning('API请求失败，使用离线模式显示表单');\n              this.loaded = true;\n            } else {\n              this.error = '无法获取图像数据';\n              this.$message.error('加载失败，无法获取图像数据');\n            }\n          } else {\n            this.error = error.response?.data || error.message;\n            this.$message.error('加载失败: ' + this.error);\n          }\n          \n          this.loading = false;\n        });\n    },\n    // 深度合并表单数据，避免结构不一致问题\n    mergeFormData(savedData) {\n      const result = { ...this.formData };\n      \n      // 遍历保存的数据，将其合并到默认表单结构中\n      for (const key in savedData) {\n        if (Object.prototype.hasOwnProperty.call(savedData, key)) {\n          if (typeof savedData[key] === 'object' && savedData[key] !== null && \n              typeof result[key] === 'object' && result[key] !== null) {\n            // 递归合并嵌套对象\n            result[key] = this.mergeFormData(savedData[key]);\n          } else {\n            // 直接替换值\n            result[key] = savedData[key];\n          }\n        }\n      }\n      \n      return result;\n    },\n    // 表单提交\n    handleSubmit() {\n      this.$refs.caseForm.validate(valid => {\n        if (!valid) {\n          this.$message.warning('表单验证失败，请检查必填项');\n          return;\n        }\n        \n        this.saving = true;\n        \n        // 检查是否为离线模式\n        const offlineMode = localStorage.getItem('offlineMode') === 'true';\n        \n        if (offlineMode) {\n          console.log('离线模式：模拟保存表单数据');\n          \n          // 存储到localStorage\n          try {\n            localStorage.setItem(`offline_form_${this.imageId}`, JSON.stringify(this.formData));\n            \n            // 更新进度\n            this.completeAnnotation();\n            \n            this.$message.success('表单已保存（离线模式）');\n            setTimeout(() => {\n              this.$router.push('/app/dashboard');\n            }, 1000);\n          } catch (e) {\n            console.error('离线保存表单失败', e);\n            this.$message.error('保存失败: ' + e.message);\n          }\n          \n          this.saving = false;\n          return;\n        }\n        \n        // 显示加载提示\n        const loading = this.$loading({\n          lock: true,\n          text: '提交数据中...',\n          spinner: 'el-icon-loading',\n          background: 'rgba(255, 255, 255, 0.7)'\n        });\n        \n        // 添加action参数，指定为提交操作\n        const formDataWithAction = {\n          ...this.formData,\n          action: 'submit', // 指定为提交操作，状态将设为SUBMITTED(已提交)\n          timestamp: new Date().toISOString() // 添加当前时间戳，使后端可以使用前端时间\n        };\n        \n        // 添加用户信息，确保后端能正确识别角色\n        try {\n          const userStr = localStorage.getItem('user');\n          if (userStr) {\n            const user = JSON.parse(userStr);\n            // 直接在表单数据中包含用户角色信息\n            if (user.role) {\n              formDataWithAction.userRole = user.role;\n              console.log('添加用户角色到表单数据:', user.role);\n            }\n            if (user.id) {\n              formDataWithAction.userId = user.id;\n            }\n            if (user.customId) {\n              formDataWithAction.userCustomId = user.customId;\n            }\n          }\n        } catch (e) {\n          console.error('获取用户信息失败:', e);\n        }\n        \n        console.log('提交表单数据:', formDataWithAction);\n        \n        // 正常API请求（已改为简单请求，不带自定义头和 _role 参数）\n        api.images.saveStructuredFormData(this.imageId, formDataWithAction)\n          .then(response => {\n            loading.close();\n            this.$message.success('表单提交成功');\n            // 完成标注流程，清除进度\n            this.completeAnnotation();\n            // 延迟跳转到首页\n            setTimeout(() => {\n              this.$router.push('/app/dashboard');\n            }, 1000);\n          })\n          .catch(error => {\n            loading.close();\n            console.error('提交表单失败', error);\n            \n            const errorMessage = error.response?.data || error.message || '未知错误';\n            this.$message.error('提交失败: ' + errorMessage);\n            \n            // 如果API失败，使用离线模式\n            if (!offlineMode) {\n              this.$confirm('提交到服务器失败，是否启用离线模式?', '提交失败', {\n                confirmButtonText: '启用离线模式',\n                cancelButtonText: '再试一次',\n                type: 'warning'\n              }).then(() => {\n                localStorage.setItem('offlineMode', 'true');\n                this.handleSubmit(); // 递归调用，使用离线模式\n              }).catch(() => {\n                this.$message.info('请稍后重试');\n              });\n            }\n          })\n          .finally(() => {\n            this.saving = false;\n          });\n      });\n    },\n    prefilFormFromAnnotations() {\n      // 这里可以根据标注数据预填其他表单字段\n      // 例如根据标注位置自动推断病变部位等\n    },\n    // 保存表单数据到进度中\n    saveFormProgress() {\n      this.saveProgress({\n        step: 2, // 病例信息填写步骤\n        imageId: this.imageId,\n        formData: this.formData\n      });\n    },\n    // 保存并退出\n    saveAndExit() {\n      // 保存当前表单数据\n      this.saveFormProgress();\n      \n      // 显示确认对话框\n      this.saveDialogVisible = true;\n    },\n    // 处理保留填写进度\n    handleSaveProgress() {\n      // 保存会话状态，防止401错误导致重定向到登录页面\n      sessionStorage.setItem('isNavigatingAfterSave', 'true');\n      sessionStorage.setItem('allowFormOperation', 'true');\n      \n      // 显示加载提示\n      const loading = this.$loading({\n        lock: true,\n        text: '保存数据中...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(255, 255, 255, 0.7)'\n      });\n      \n      try {\n        // 添加action参数，指定为保存操作\n        const formDataWithAction = {\n          ...this.formData,\n          action: 'save', // 指定为保存操作，状态将设为REVIEWED(已标注)\n          timestamp: new Date().toISOString() // 添加当前时间戳，使后端可以使用前端时间\n        };\n        \n        // 调用API将数据保存到数据库（已改为简单请求，不带自定义头和 _role 参数）\n        api.images.saveStructuredFormData(this.imageId, formDataWithAction)\n          .then(response => {\n            this.$message.success('表单数据已保存到数据库');\n            sessionStorage.setItem('navigatingFromForm', 'true');\n            try {\n              this.$router.push('/app/dashboard');\n            } catch (e) {\n              console.error('路由导航错误，尝试使用window.location', e);\n              window.location.href = '/app/dashboard';\n            }\n          })\n          .catch(async error => {\n            console.error('保存表单数据失败:', error);\n            this.$message.error('保存失败: ' + (error.response?.data || error.message || '未知错误'));\n            // 1. 本地保存表单数据\n            try {\n              localStorage.setItem(`offline_form_${this.imageId}`, JSON.stringify(this.formData));\n              this.$message.warning('已将表单数据保存到本地，稍后可继续填写');\n            } catch (e) {\n              console.error('本地保存表单失败', e);\n            }\n            // 2. 允许用户选择是否继续\n            try {\n              await this.$confirm('保存到服务器失败，是否仅本地保存并返回工作台？', '保存失败', {\n                confirmButtonText: '继续',\n                cancelButtonText: '留在当前页面',\n                type: 'warning'\n              });\n              // 用户选择继续，跳转\n              this.$router.push('/app/dashboard');\n            } catch (e) {\n              // 用户选择留在当前页面\n              this.$message.info('请稍后重试或检查网络');\n            }\n          })\n          .finally(() => {\n            loading.close();\n          });\n      } catch (error) {\n        console.error('处理保存进度时出错:', error);\n        this.$message.error('保存失败: ' + error.message);\n        loading.close();\n        \n        // 如果出错，也尝试重定向到工作台\n        window.location.href = '/app/dashboard';\n      }\n    },\n    // 处理丢弃填写进度\n    handleDiscardProgress() {\n      this.completeAnnotation();\n      this.$message.info('已清除填写进度');\n      this.$router.push('/cases/new');\n    },\n    // 返回上一步（图像标注页面）\n    returnToAnnotation() {\n      // 保存当前表单数据\n      this.saveFormProgress();\n      \n      // 返回图像标注页面\n      this.$router.push({\n        path: '/case/' + this.diagnosisId + '/annotate-and-form',\n        query: { imageId: this.imageId }\n      });\n    },\n    // 添加用户认证检查方法\n    checkUserAuthentication() {\n      const user = JSON.parse(localStorage.getItem('user'));\n      if (!user) {\n        // 尝试从会话存储恢复用户信息\n        const preservedUser = sessionStorage.getItem('preservedUser');\n        if (preservedUser) {\n          console.log('尝试使用保存的用户信息恢复会话');\n          localStorage.setItem('user', preservedUser);\n          sessionStorage.removeItem('preservedUser');\n        }\n      }\n    },\n    // 从后端返回的独立字段中映射数据到表单\n    mapImageDataToForm(imageData) {\n      console.log('原始图像数据:', imageData);\n      \n      if (!imageData) {\n        console.warn('未找到图像数据，无法映射到表单');\n        return;\n      }\n      \n      // 处理下划线命名的字段 - 创建兼容对象\n      const data = { ...imageData };\n      \n      // 可能的下划线风格字段映射\n      const fieldMappings = {\n        'lesion_location': 'lesionLocation',\n        'patient_age': 'patientAge',\n        'disease_stage': 'diseaseStage',\n        'morphological_features': 'morphologicalFeatures',\n        'blood_flow': 'bloodFlow',\n        'symptom_details': 'symptomDetails',\n        'complication_details': 'complicationDetails',\n        'diagnosis_category': 'diagnosisCategory', \n        'diagnosis_icd_code': 'diagnosisIcdCode',\n        'treatment_priority': 'treatmentPriority',\n        'treatment_plan': 'treatmentPlan',\n        'recommended_treatment': 'recommendedTreatment',\n        'follow_up_schedule': 'followUpSchedule',\n        'prognosis_rating': 'prognosisRating',\n        'patient_education': 'patientEducation'\n      };\n      \n      // 创建兼容对象，支持两种字段命名风格\n      Object.keys(fieldMappings).forEach(key => {\n        if (data[key] !== undefined && data[fieldMappings[key]] === undefined) {\n          data[fieldMappings[key]] = data[key];\n          console.log(`字段映射: ${key} -> ${fieldMappings[key]}`);\n        }\n      });\n      \n      // 基础信息映射\n      if (data.lesionLocation) {\n        // 如果是单个字符串，尝试按路径拆分\n        if (typeof data.lesionLocation === 'string') {\n          const locationParts = data.lesionLocation.split('/').filter(p => p.trim());\n          if (locationParts.length > 0) {\n            this.formData.bodyPart = locationParts;\n            console.log('设置病变部位(字符串分割):', this.formData.bodyPart);\n          }\n        } else if (Array.isArray(data.lesionLocation)) {\n          this.formData.bodyPart = data.lesionLocation;\n          console.log('设置病变部位(数组):', this.formData.bodyPart);\n        }\n      }\n      \n      // 尝试从lesion_location字段读取(下划线格式)\n      if (!this.formData.bodyPart || this.formData.bodyPart.length === 0) {\n        if (imageData.lesion_location) {\n          if (typeof imageData.lesion_location === 'string') {\n            const locationParts = imageData.lesion_location.split('/').filter(p => p.trim());\n            if (locationParts.length > 0) {\n              this.formData.bodyPart = locationParts;\n              console.log('设置病变部位(下划线字段):', this.formData.bodyPart);\n            }\n          }\n        }\n      }\n      \n      if (data.patientAge !== null && data.patientAge !== undefined) {\n        this.formData.patientAge = parseInt(data.patientAge);\n        console.log('设置患者年龄:', this.formData.patientAge);\n      }\n      \n      if (data.diseaseStage) {\n        this.formData.stage = data.diseaseStage;\n        console.log('设置病程阶段:', this.formData.stage);\n      }\n      \n      // 形态与临床特征映射\n      if (data.morphologicalFeatures) {\n        const morphFeatures = data.morphologicalFeatures || '';\n        // 如果包含冒号，前面部分是形态特征，后面是描述\n        const colonIndex = morphFeatures.indexOf(':');\n        \n        if (colonIndex > -1) {\n          const features = morphFeatures.substring(0, colonIndex).split('/');\n          this.formData.morphology = features;\n          this.formData.morphologyDesc = morphFeatures.substring(colonIndex + 1).trim();\n        } else {\n          // 如果没有冒号，全部作为形态特征\n          this.formData.morphology = morphFeatures.split('/').filter(p => p.trim() !== '');\n        }\n        \n        console.log('设置形态特征:', this.formData.morphology);\n        console.log('设置形态描述:', this.formData.morphologyDesc);\n      }\n      \n      if (data.bloodFlow) {\n        this.formData.bloodFlow = data.bloodFlow;\n        console.log('设置血流信号:', this.formData.bloodFlow);\n      }\n      \n      if (data.symptoms) {\n        const symptomsData = data.symptoms || '';\n        const colonIndex = symptomsData.indexOf(':');\n        \n        if (colonIndex > -1) {\n          const symptoms = symptomsData.substring(0, colonIndex).split('/');\n          this.formData.symptoms = symptoms;\n          this.formData.symptomDescription = symptomsData.substring(colonIndex + 1).trim();\n        } else {\n          this.formData.symptoms = symptomsData.split('/').filter(s => s.trim() !== '');\n        }\n        \n        console.log('设置症状:', this.formData.symptoms);\n      }\n      \n      if (data.symptomDetails) {\n        this.formData.symptomDescription = data.symptomDetails;\n        console.log('设置症状详情:', this.formData.symptomDescription);\n      }\n      \n      if (data.complications) {\n        const complications = data.complications || '';\n        this.formData.complications = complications.split('/').filter(c => c.trim() !== '');\n        console.log('设置并发症:', this.formData.complications);\n      }\n      \n      if (data.complicationDetails) {\n        this.formData.complicationDetails = data.complicationDetails;\n        console.log('设置并发症详情:', this.formData.complicationDetails);\n      }\n      \n      // 诊断与治疗建议映射\n      if (data.diagnosisCategory) {\n        this.formData.diagnosis = data.diagnosisCategory;\n        console.log('设置诊断结论:', this.formData.diagnosis);\n      }\n      \n      if (data.diagnosisIcdCode) {\n        this.formData.diagnosisCode = data.diagnosisIcdCode;\n        console.log('设置ICD编码:', this.formData.diagnosisCode);\n      }\n      \n      if (data.treatmentPriority) {\n        this.formData.treatmentPriority = data.treatmentPriority;\n        console.log('设置治疗优先级:', this.formData.treatmentPriority);\n      }\n      \n      if (data.treatmentPlan) {\n        const treatmentPlan = data.treatmentPlan || '';\n        this.formData.treatmentPlan = treatmentPlan.split('/').filter(p => p.trim() !== '');\n        console.log('设置治疗方案:', this.formData.treatmentPlan);\n      }\n      \n      if (data.recommendedTreatment) {\n        this.formData.treatmentSuggestion = data.recommendedTreatment;\n        console.log('设置治疗详情:', this.formData.treatmentSuggestion);\n      }\n      \n      if (data.contraindications) {\n        this.formData.contraindications = data.contraindications;\n        console.log('设置禁忌症:', this.formData.contraindications);\n      }\n      \n      // 随访与预后映射\n      if (data.followUpSchedule) {\n        const followUp = data.followUpSchedule;\n        \n        // 检查是否是标准选项之一\n        const standardOptions = ['1个月', '3个月', '6个月'];\n        if (standardOptions.includes(followUp)) {\n          this.formData.followUpPeriod = followUp;\n        } else {\n          this.formData.followUpPeriod = 'custom';\n          this.formData.customFollowUp = followUp;\n        }\n        \n        console.log('设置随访周期:', this.formData.followUpPeriod, this.formData.customFollowUp);\n      }\n      \n      if (data.prognosisRating !== null && data.prognosisRating !== undefined) {\n        this.formData.prognosisRating = parseInt(data.prognosisRating);\n        console.log('设置预后评估:', this.formData.prognosisRating);\n      }\n      \n      if (data.patientEducation) {\n        const education = data.patientEducation || '';\n        const colonIndex = education.indexOf(':');\n        \n        if (colonIndex > -1) {\n          const eduItems = education.substring(0, colonIndex).split('/');\n          this.formData.patientEducation = eduItems;\n          this.formData.lifestyleAdjustment = education.substring(colonIndex + 1).trim();\n        } else {\n          this.formData.patientEducation = education.split('/').filter(e => e.trim() !== '');\n        }\n        \n        console.log('设置患者教育:', this.formData.patientEducation);\n        console.log('设置教育详情:', this.formData.lifestyleAdjustment);\n      }\n      \n      // 转换字符串数组（如\"[]\"）为实际数组\n      this.convertStringArrays();\n      \n      this.$message.success('已从数据库加载保存的表单数据');\n    },\n    // 处理字符串形式的数组\n    convertStringArrays() {\n      const arrayFields = ['morphology', 'symptoms', 'complications', 'treatmentPlan', 'patientEducation', 'bodyPart'];\n      \n      arrayFields.forEach(field => {\n        const value = this.formData[field];\n        \n        // 跳过null或undefined值\n        if (value === null || value === undefined) {\n          return;\n        }\n        \n        // 处理字符串形式的数组 \"[item1,item2]\"\n        if (typeof value === 'string') {\n          try {\n            // 检查是否是JSON数组字符串\n            if (value.trim().startsWith('[') && value.trim().endsWith(']')) {\n            const arrayValue = JSON.parse(value);\n            if (Array.isArray(arrayValue)) {\n              this.formData[field] = arrayValue;\n              console.log(`转换字段 ${field} 从字符串到数组:`, arrayValue);\n            }\n            }\n            // 如果是逗号分隔的字符串，也转换为数组\n            else if (value.includes(',')) {\n              const arrayValue = value.split(',').map(item => item.trim());\n              this.formData[field] = arrayValue;\n              console.log(`转换字段 ${field} 从逗号分隔字符串到数组:`, arrayValue);\n            }\n          } catch (e) {\n            console.error(`无法解析字符串数组 ${field}:`, value, e);\n            // 确保字段至少是一个空数组，而不是无效的字符串\n            if (!Array.isArray(this.formData[field])) {\n              this.formData[field] = [];\n          }\n        }\n      }\n        // 确保字段是数组类型\n        else if (!Array.isArray(this.formData[field])) {\n          console.warn(`字段 ${field} 不是数组类型，设置为空数组`);\n          this.formData[field] = [];\n        }\n      });\n      \n      // 输出处理后的完整表单数据用于调试\n      console.log('处理后的表单数据:', JSON.stringify(this.formData, null, 2));\n    },\n    // 确保级联选择器数据正确显示\n    ensureCascaderDataValid() {\n      // 添加一个延时，确保在组件完全挂载后执行\n      setTimeout(() => {\n        console.log('检查级联选择器数据:', this.formData.bodyPart);\n        \n        // 如果bodyPart已有值但级联选择器没有正确显示\n        if (this.formData.bodyPart && this.formData.bodyPart.length > 0) {\n          // 检查级联选择器引用是否存在\n          const cascader = this.$refs.bodyPartCascader;\n          if (cascader) {\n            if (!cascader.modelValue || cascader.modelValue.length === 0) {\n            // 尝试重新设置值\n            this.$nextTick(() => {\n                try {\n              // 深拷贝防止引用问题\n              const locationValue = JSON.parse(JSON.stringify(this.formData.bodyPart));\n              // 先清空再设置\n              this.formData.bodyPart = [];\n              this.$nextTick(() => {\n                this.formData.bodyPart = locationValue;\n                console.log('重新设置级联选择器值:', locationValue);\n              });\n                } catch (error) {\n                  console.error('重设级联选择器值时出错:', error);\n                }\n            });\n            }\n          } else {\n            console.log('级联选择器引用不存在，跳过重设值');\n          }\n        }\n      }, 500);\n    },\n    // 开始轮询获取大模型生成的建议\n    startPolling(diagnosisId) {\n      console.log('开始轮询获取诊断建议，ID:', diagnosisId);\n      this.pollingInterval = setInterval(() => {\n        this.pollForResults(diagnosisId);\n      }, 5000); // 每5秒轮询一次\n\n      // 设置一个超时，比如5分钟后停止轮询，防止无限循环\n      setTimeout(() => {\n        if (this.pollingInterval) {\n          clearInterval(this.pollingInterval);\n          this.pollingInterval = null;\n          if(this.isLoadingRecommendation) {\n            this.$message.warning('获取诊断建议超时，将使用默认值。');\n            this.isLoadingRecommendation = false;\n            \n            // 轮询超时，提供默认值\n            this.provideDefaultValues();\n          }\n        }\n      }, 60000); // 改为1分钟，加快测试速度\n    },\n    // 轮询获取诊断结果\n    async pollForResults(diagnosisId) {\n      try {\n        console.log(`开始轮询端点: /medical/api/hemangioma-diagnoses/${diagnosisId}`);\n        // 修正：直接使用 api.get，而不是 api.default.get\n        const response = await api.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);\n        \n        if (!response || !response.data) {\n          console.log(\"轮询返回空数据\");\n          return;\n        }\n        \n        const data = response.data;\n        console.log(\"轮询成功，获取到数据:\", data);\n        \n        // 检查返回的数据是否包含AI建议\n        const hasTreatmentSuggestion = data && (data.treatmentSuggestion || data.treatment_suggestion);\n        const hasPrecautions = data && (data.precautions || data.precautions);\n        \n        if (hasTreatmentSuggestion || hasPrecautions) {\n          // 更新模型数据，支持不同的命名风格，并确保不会设置为undefined\n          if (hasTreatmentSuggestion) {\n          this.formData.treatmentSuggestion = data.treatmentSuggestion || data.treatment_suggestion || this.formData.treatmentSuggestion;\n          }\n          \n          if (hasPrecautions) {\n          this.formData.precautions = data.precautions || data.precautions || this.formData.precautions;\n          }\n          \n          // 如果所有必要的字段都已填充，停止轮询\n          if (this.formData.treatmentSuggestion && \n              this.formData.precautions) {\n            \n            console.log(\"所有AI建议字段已填充，停止轮询\");\n          clearInterval(this.pollingInterval);\n          this.pollingInterval = null;\n            this.isLoadingRecommendation = false;\n            this.$message.success('已获取AI生成的诊断建议');\n          }\n        }\n      } catch (error) {\n        console.error(\"轮询过程中出错:\", error);\n      }\n    },\n    async tryNextPath(nextIndex) {\n        try {\n            await this.$router.push({\n                name: 'CaseStructuredForm',\n                params: { caseId: this.$route.params.caseId, stepIndex: nextIndex }\n            });\n            this.currentStepIndex = nextIndex;\n        } catch (error) {\n            console.error('Navigation failed:', error);\n            this.$message.error('表单步骤跳转失败');\n        }\n    },\n    // 提供默认值的辅助方法\n    provideDefaultValues() {\n      console.log('无法从API获取数据，提供默认值');\n      \n      // 设置默认的治疗建议\n      if (!this.formData.treatmentSuggestion) {\n        this.formData.treatmentSuggestion = '根据患者情况，建议采用观察等待策略，定期复查。如有明显生长或症状加重，可考虑口服药物治疗。';\n      }\n      \n      // 设置默认的注意事项\n      if (!this.formData.precautions) {\n        this.formData.precautions = '保持患处清洁干燥，避免外伤和摩擦。避免剧烈运动导致的碰撞。注意保暖，避免患处受凉。';\n      }\n      \n      this.$message.warning('使用默认建议值，请根据实际情况修改');\n    },\n  },\n  beforeDestroy() {\n    // 组件销毁前清理轮询\n    if (this.pollingInterval) {\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n      console.log('清理轮询定时器');\n    }\n  },\n};\n</script>\n\n<style scoped>\n.structured-form-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: #fff;\n  border-radius: 4px;\n}\n\n.form-note {\n  margin-bottom: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.header-content h2 {\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.form-section {\n  margin-bottom: 20px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #EBEEF5;\n}\n\n.form-section:last-child {\n  border-bottom: none;\n}\n\n.section-title {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n  font-weight: 600;\n  color: #409EFF;\n  padding-bottom: 8px;\n  border-bottom: 1px dashed #EBEEF5;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 15px;\n}\n\n/* 自适应字段容器 - 灵活布局 */\n.adaptive-fields-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 15px;\n}\n\n.adaptive-fields-container .el-form-item {\n  flex: 1 1 150px; /* 灵活增长，最小宽度150px */\n  min-width: 150px;\n  max-width: calc(100% / 3 - 10px); /* 在较小屏幕上最多3个一行 */\n  margin-bottom: 10px;\n}\n\n/* 大屏幕上最多6个一行 */\n@media (min-width: 1200px) {\n  .adaptive-fields-container .el-form-item {\n    max-width: calc(100% / 6 - 13px);\n  }\n}\n\n/* 中等屏幕上最多4个一行 */\n@media (min-width: 900px) and (max-width: 1199px) {\n  .adaptive-fields-container .el-form-item {\n    max-width: calc(100% / 4 - 12px);\n  }\n}\n\n.form-actions {\n  display: flex;\n  justify-content: center;\n  margin-top: 25px;\n}\n\n.form-actions button {\n  min-width: 120px;\n}\n\n.required-indicator {\n  color: #f56c6c;\n  font-size: 12px;\n  margin-left: 5px;\n}\n</style>\n", "import _typeof from \"./typeof.js\";\nfunction _regeneratorValues(e) {\n  if (null != e) {\n    var t = e[\"function\" == typeof Symbol && Symbol.iterator || \"@@iterator\"],\n      r = 0;\n    if (t) return t.call(e);\n    if (\"function\" == typeof e.next) return e;\n    if (!isNaN(e.length)) return {\n      next: function next() {\n        return e && r >= e.length && (e = void 0), {\n          value: e && e[r++],\n          done: !e\n        };\n      }\n    };\n  }\n  throw new TypeError(_typeof(e) + \" is not iterable\");\n}\nexport { _regeneratorValues as default };", "<template>\r\n  <el-dialog\r\n    v-model:visible=\"dialogVisible\"\r\n    :title=\"title\"\r\n    :width=\"width\"\r\n    :before-close=\"handleClose\"\r\n  >\r\n    <div class=\"dialog-content\">\r\n      <div v-if=\"icon\" class=\"dialog-icon\">\r\n        <i :class=\"iconClass\"></i>\r\n      </div>\r\n      <div class=\"dialog-message\">\r\n        <slot>{{ message }}</slot>\r\n      </div>\r\n    </div>\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"handleCancel\">{{ cancelText }}</el-button>\r\n        <el-button :type=\"confirmType\" @click=\"handleConfirm\">{{ confirmText }}</el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ConfirmDialog',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: '确认'\r\n    },\r\n    message: {\r\n      type: String,\r\n      default: '确定要执行此操作吗？'\r\n    },\r\n    confirmText: {\r\n      type: String,\r\n      default: '确定'\r\n    },\r\n    cancelText: {\r\n      type: String,\r\n      default: '取消'\r\n    },\r\n    confirmType: {\r\n      type: String,\r\n      default: 'primary'\r\n    },\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '30%'\r\n    },\r\n    value: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  computed: {\r\n    dialogVisible: {\r\n      get() {\r\n        return this.value;\r\n      },\r\n      set(value) {\r\n        this.$emit('input', value);\r\n      }\r\n    },\r\n    iconClass() {\r\n      switch (this.icon) {\r\n        case 'warning':\r\n          return 'el-icon-warning-outline';\r\n        case 'error':\r\n          return 'el-icon-error';\r\n        case 'success':\r\n          return 'el-icon-success';\r\n        case 'info':\r\n          return 'el-icon-info';\r\n        default:\r\n          return this.icon;\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleClose(done) {\r\n      this.$emit('cancel');\r\n      done();\r\n    },\r\n    handleCancel() {\r\n      this.dialogVisible = false;\r\n      this.$emit('cancel');\r\n    },\r\n    handleConfirm() {\r\n      this.dialogVisible = false;\r\n      this.$emit('confirm');\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-content {\r\n  display: flex;\r\n  padding: 10px 0;\r\n}\r\n\r\n.dialog-icon {\r\n  font-size: 24px;\r\n  margin-right: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.dialog-icon i {\r\n  color: #e6a23c;\r\n}\r\n\r\n.dialog-icon i.el-icon-error {\r\n  color: #f56c6c;\r\n}\r\n\r\n.dialog-icon i.el-icon-success {\r\n  color: #67c23a;\r\n}\r\n\r\n.dialog-icon i.el-icon-info {\r\n  color: #909399;\r\n}\r\n\r\n.dialog-message {\r\n  flex: 1;\r\n  font-size: 16px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n</style> ", "import { render } from \"./ConfirmDialog.vue?vue&type=template&id=5546d4a6&scoped=true\"\nimport script from \"./ConfirmDialog.vue?vue&type=script&lang=js\"\nexport * from \"./ConfirmDialog.vue?vue&type=script&lang=js\"\n\nimport \"./ConfirmDialog.vue?vue&type=style&index=0&id=5546d4a6&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-5546d4a6\"]])\n\nexport default __exports__", "/**\r\n * 格式化日期\r\n * @param {string|Date} date - 日期字符串或Date对象\r\n * @param {boolean} withTime - 是否包含时间\r\n * @returns {string} 格式化后的日期字符串\r\n */\r\nexport function formatDate(date, withTime = false) {\r\n  if (!date) return '';\r\n  \r\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n  \r\n  // 检查日期是否有效\r\n  if (isNaN(dateObj.getTime())) {\r\n    return '';\r\n  }\r\n  \r\n  const year = dateObj.getFullYear();\r\n  const month = String(dateObj.getMonth() + 1).padStart(2, '0');\r\n  const day = String(dateObj.getDate()).padStart(2, '0');\r\n  \r\n  const dateStr = `${year}-${month}-${day}`;\r\n  \r\n  if (withTime) {\r\n    const hours = String(dateObj.getHours()).padStart(2, '0');\r\n    const minutes = String(dateObj.getMinutes()).padStart(2, '0');\r\n    const seconds = String(dateObj.getSeconds()).padStart(2, '0');\r\n    \r\n    return `${dateStr} ${hours}:${minutes}:${seconds}`;\r\n  }\r\n  \r\n  return dateStr;\r\n}\r\n\r\n/**\r\n * 格式化文件大小\r\n * @param {number} bytes - 字节数\r\n * @returns {string} 格式化后的文件大小\r\n */\r\nexport function formatFileSize(bytes) {\r\n  if (bytes === 0) return '0 B';\r\n  \r\n  const units = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  \r\n  return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];\r\n}\r\n\r\n/**\r\n * 将数值转换为百分比格式\r\n * @param {number} value - 0-1之间的小数值\r\n * @param {number} decimals - 小数位数\r\n * @returns {string} 格式化后的百分比\r\n */\r\nexport function toPercentage(value, decimals = 1) {\r\n  if (typeof value !== 'number') return '';\r\n  \r\n  return (value * 100).toFixed(decimals) + '%';\r\n}\r\n\r\n/**\r\n * 截断文本\r\n * @param {string} text - 原始文本\r\n * @param {number} maxLength - 最大长度\r\n * @returns {string} 截断后的文本\r\n */\r\nexport function truncateText(text, maxLength = 30) {\r\n  if (!text || typeof text !== 'string' || text.length <= maxLength) {\r\n    return text;\r\n  }\r\n  \r\n  return text.substring(0, maxLength) + '...';\r\n} ", "import { render } from \"./CaseStructuredForm.vue?vue&type=template&id=819b44ee&scoped=true\"\nimport script from \"./CaseStructuredForm.vue?vue&type=script&lang=js\"\nexport * from \"./CaseStructuredForm.vue?vue&type=script&lang=js\"\n\nimport \"./CaseStructuredForm.vue?vue&type=style&index=0&id=819b44ee&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-819b44ee\"]])\n\nexport default __exports__"], "names": ["addTimestamp", "url", "timestamp", "concat", "Date", "now", "includes", "getImageUrl", "console", "log", "test", "encodedPath", "encodeURIComponent", "baseUrl", "isLocalhost", "API_BASE_URL", "startsWith", "finalUrl", "API_CONTEXT_PATH", "class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "$data", "loading", "_hoisted_2", "_cache", "role", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_alert", "title", "type", "closable", "_component_el_form", "ref", "model", "formData", "rules", "_withCtx", "_hoisted_5", "_hoisted_6", "_component_el_form_item", "label", "prop", "_component_el_input_number", "modelValue", "patientAge", "$event", "min", "max", "_", "_component_el_radio_group", "gender", "_component_el_radio", "_createTextVNode", "__", "originType", "_component_el_select", "bodyPart", "placeholder", "filterable", "_Fragment", "_renderList", "partOptions", "item", "_createBlock", "_component_el_option", "value", "color", "colorOptions", "vesselTexture", "_hoisted_7", "_hoisted_8", "isLoadingRecommendation", "_component_el_tag", "size", "_createCommentVNode", "_hoisted_9", "_component_el_input", "treatmentSuggestion", "rows", "precautions", "_hoisted_10", "_component_el_button", "onClick", "$options", "saveData", "savingDraft", "submitForm", "submitting", "saveError", "retrySave", "_regeneratorValues", "e", "t", "Symbol", "iterator", "r", "call", "next", "isNaN", "length", "done", "TypeError", "_typeof", "_component_el_dialog", "visible", "dialogVisible", "$props", "width", "handleClose", "footer", "handleCancel", "_toDisplayString", "cancelText", "confirmType", "handleConfirm", "confirmText", "icon", "_normalizeClass", "iconClass", "_renderSlot", "_ctx", "$slots", "message", "name", "props", "String", "default", "Boolean", "computed", "get", "this", "set", "$emit", "methods", "__exports__", "formatDate", "date", "withTime", "arguments", "undefined", "date<PERSON><PERSON>j", "getTime", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "dateStr", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "components", "ConfirmDialog", "data", "imageId", "diagnosisId", "saveDialogVisible", "pollingInterval", "required", "trigger", "availableEndpoints", "_objectSpread", "mapGetters", "created", "_this", "$route", "query", "params", "id", "$nextTick", "checkAvailableEndpoints", "then", "loadCaseData", "$message", "error", "mapActions", "_defineProperty", "_this2", "_asyncToGenerator", "_regenerator", "m", "_callee", "endpoints", "_i", "_endpoints", "endpoint", "response", "_t", "w", "_context", "n", "p", "axios", "v", "headers", "push", "split", "a", "_this3", "_callee2", "success", "user", "token", "api<PERSON><PERSON><PERSON>", "_loop", "_ret", "_i2", "_apiPaths", "newFormData", "<PERSON><PERSON><PERSON>", "backupData", "parsedBackup", "_t4", "_context3", "JSON", "parse", "localStorage", "getItem", "customId", "path", "_t3", "_context2", "Object", "keys", "for<PERSON>ach", "patient_age", "vessel_texture", "treatment_suggestion", "$forceUpdate", "startPolling", "d", "api", "warning", "backup<PERSON><PERSON>r", "provideDefaultValues", "_this4", "_callee3", "requiredFields", "missingFields", "fieldNames", "missingFieldNames", "userRole", "finalStatus", "_context4", "$refs", "caseForm", "validate", "filter", "field", "map", "join", "status", "_this5", "_callee4", "formDataWithAction", "_error$response", "errorMessage", "_t6", "_context5", "$loading", "lock", "text", "close", "removeItem", "setTimeout", "$router", "setItem", "stringify", "returnToAnnotation", "back", "offlineImage", "fetchImageData", "_this6", "isTestMode", "testMode", "offlineMode", "imageData", "parseInt", "original_name", "created_at", "toISOString", "savedProgress", "getAnnotationProgress", "info", "loaded", "images", "getOne", "mapImageDataToForm", "warn", "_error$response2", "mergeFormData", "savedData", "result", "prototype", "hasOwnProperty", "handleSubmit", "_this7", "valid", "saving", "completeAnnotation", "spinner", "background", "action", "userStr", "userId", "userCustomId", "saveStructuredFormData", "_error$response3", "$confirm", "confirmButtonText", "cancelButtonText", "prefilFormFromAnnotations", "saveFormProgress", "saveProgress", "step", "saveAndExit", "handleSaveProgress", "_this8", "sessionStorage", "window", "location", "href", "_ref", "_callee5", "_error$response4", "_context6", "_x", "apply", "handleDiscardProgress", "preservedUser", "fieldMappings", "lesionLocation", "locationParts", "trim", "Array", "isArray", "lesion_location", "diseaseStage", "stage", "morphologicalFeatures", "morphFeatures", "colonIndex", "indexOf", "features", "substring", "morphology", "morphologyDesc", "bloodFlow", "symptoms", "symptomsData", "symptomDescription", "s", "symptomDetails", "complications", "c", "complicationDetails", "diagnosisCategory", "diagnosis", "diagnosisIcdCode", "diagnosisCode", "treatmentPriority", "treatmentPlan", "recommendedTreatment", "contraindications", "followUpSchedule", "followUp", "standardOptions", "followUpPeriod", "customFollowUp", "prognosisRating", "patientEducation", "education", "eduItems", "lifestyleAdjustment", "convertStringArrays", "_this9", "arrayFields", "endsWith", "arrayValue", "_this0", "cascader", "bodyPartCascader", "locationValue", "_this1", "setInterval", "pollForResults", "clearInterval", "_this10", "_callee6", "hasTreatmentSuggestion", "hasPrecautions", "_t8", "_context7", "nextIndex", "_this11", "_callee7", "_t9", "_context8", "caseId", "stepIndex", "currentStepIndex", "<PERSON><PERSON><PERSON><PERSON>", "render"], "sourceRoot": ""}