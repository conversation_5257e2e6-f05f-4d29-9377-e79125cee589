@echo off
chcp 65001
echo ========================================
echo 血管瘤AI智能诊断平台 - Docker部署脚本
echo ========================================

:: 设置颜色
color 0B

:: 检查Docker环境
echo [1/5] 检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose未安装
    pause
    exit /b 1
)
echo ✅ Docker环境检查通过

:: 停止现有容器
echo [2/5] 停止现有容器...
docker-compose down
echo ✅ 现有容器已停止

:: 清理旧镜像（可选）
echo [3/5] 清理旧镜像...
set /p cleanup="是否清理旧镜像？(y/N): "
if /i "%cleanup%"=="y" (
    docker system prune -f
    echo ✅ 旧镜像清理完成
) else (
    echo ⏭️ 跳过镜像清理
)

:: 构建并启动服务
echo [4/5] 构建并启动服务...
docker-compose up --build -d

:: 检查服务状态
echo [5/5] 检查服务状态...
timeout /t 30 /nobreak >nul
docker-compose ps

echo.
echo ========================================
echo 🎉 Docker部署完成！
echo ========================================
echo.
echo 📱 应用访问地址: http://localhost
echo 🔧 后端API: http://localhost/medical/
echo 🤖 AI服务: http://localhost/ai-api/
echo 📊 服务状态: docker-compose ps
echo 📋 服务日志: docker-compose logs -f [service_name]
echo.
echo 💡 常用命令：
echo - 查看所有日志: docker-compose logs -f
echo - 重启服务: docker-compose restart
echo - 停止服务: docker-compose down
echo - 更新服务: docker-compose up --build -d
echo.

:: 等待Ollama服务启动并下载模型
echo [额外] 初始化Ollama模型...
echo 正在等待Ollama服务启动...
timeout /t 60 /nobreak >nul

echo 下载LLM模型（这可能需要几分钟）...
docker-compose exec ollama ollama pull deepseek-r1:8b

echo.
echo ✅ 模型下载完成！系统已完全就绪。
echo.
pause
