<template>
  <div class="case-view-container">
    <div class="page-header">
      <h2>病例详情</h2>
      <div class="header-actions">
        <el-button v-if="hasPermission" type="primary" size="small" @click="redirectToAnnotate">编辑</el-button>
        <el-tooltip content="刷新病例数据" placement="top">
          <el-button icon="el-icon-refresh" size="small" circle :loading="loading" @click="refreshData"></el-button>
        </el-tooltip>
        <el-button link @click="$router.back()">返回</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <!-- 标注后的图像 - 基本信息上方 -->
      <div v-if="annotatedImageUrl" class="annotated-image-section">
        <div class="section-header">
          <h3>病变标注图像</h3>
          <small v-if="lastRefreshTime" class="refresh-info">最后更新: {{ formatTime(lastRefreshTime) }}</small>
        </div>
        <div class="annotated-image-container">
          <el-image :src="annotatedImageUrl" fit="contain" class="annotated-image" :preview-src-list="[annotatedImageUrl]" @error="handleImageError">
            <template #error>
              <div class="image-error">
                <i class="el-icon-picture-outline"></i>
                <p>无法加载标注图像</p>
                <div v-if="imageLoadError" class="debug-info">
                  <h4>图片调试信息</h4>
                  <p><b>数据库图片路径:</b> {{ debugInfo.rawPath }}</p>
                  <p><b>完整访问URL:</b> {{ debugInfo.fullUrl }}</p>
                  <small>请检查此路径是否正确，以及后端是否提供了静态文件访问</small>
                </div>
              </div>
            </template>
          </el-image>
        </div>
      </div>
      <div v-else class="no-image-message">
        <i class="el-icon-picture-outline"></i>
        <p>此病例暂无标注图像</p>
      </div>
      
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag :type="getStatusType(caseDetail.status)">{{ caseDetail.status }}</el-tag>
        </div>
      </template>
      
      <!-- 先显示病例基本信息 -->
      <div class="case-info">
        <el-descriptions :column="4" border>
          <el-descriptions-item label="病例编号">{{ caseDetail.caseId || '无' }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.patientInfo" label="患者信息">{{ caseDetail.patientInfo }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.department" label="部位">{{ caseDetail.department }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.type" label="血管瘤类型">{{ caseDetail.type }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ caseDetail.createTime || '无' }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ caseDetail.updateTime || '无' }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.lesionColor" label="病变颜色">{{ caseDetail.lesionColor }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.detectedType" label="检测到的血管瘤类型">{{ caseDetail.detectedType }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.vesselTexture" label="血管质地">{{ getVesselTextureLabel(caseDetail.vesselTexture) }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.note" label="备注" :span="4">
            {{ caseDetail.note }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 详细信息 -->
      <div class="detailed-case-info">
        <div class="section-header">
          <h3>详细信息</h3>
          <small v-if="dataVersion > 0" class="version-info">版本: {{ dataVersion }}</small>
        </div>
        
        <!-- 血管瘤特有字段 -->
        <div v-if="caseDetail.treatmentSuggestion || caseDetail.precautions" class="hemangioma-specific-section">
          <h4>血管瘤诊断建议</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item v-if="caseDetail.diagnosticSummary" label="诊断摘要">{{ caseDetail.diagnosticSummary }}</el-descriptions-item>
            <el-descriptions-item v-if="caseDetail.treatmentSuggestion" label="治疗建议">{{ caseDetail.treatmentSuggestion }}</el-descriptions-item>
            <el-descriptions-item v-if="caseDetail.precautions" label="注意事项">{{ caseDetail.precautions }}</el-descriptions-item>
            <el-descriptions-item v-if="caseDetail.reviewNotes" label="审核备注">{{ caseDetail.reviewNotes }}</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 其他详细信息 -->
        <el-descriptions :column="3" border class="mt-4">
          <!-- 以下是从image_metadata中获取的其他已填写的字段 -->
          <el-descriptions-item v-if="caseDetail.lesionSize" label="病变大小">{{ caseDetail.lesionSize }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.bloodFlow" label="血流信号">{{ caseDetail.bloodFlow }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.borderClarity" label="边界清晰度">{{ caseDetail.borderClarity }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.diseaseStage" label="病程阶段">{{ caseDetail.diseaseStage }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.morphologicalFeatures" label="形态特征">{{ caseDetail.morphologicalFeatures }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.symptoms" label="症状表现">{{ caseDetail.symptoms }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.symptomDetails" label="症状详情">{{ caseDetail.symptomDetails }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.complications" label="并发症">{{ caseDetail.complications }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.complicationDetails" label="并发症详情">{{ caseDetail.complicationDetails }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.diagnosisCode" label="诊断编码">{{ caseDetail.diagnosisCode }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.treatmentPriority" label="治疗优先级">{{ caseDetail.treatmentPriority }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.recommendedTreatment" label="推荐治疗">{{ caseDetail.recommendedTreatment }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.contraindications" label="禁忌症">{{ caseDetail.contraindications }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.followUpSchedule" label="随访周期">{{ caseDetail.followUpSchedule }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.prognosisRating" label="预后评级">{{ caseDetail.prognosisRating }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.patientEducation" label="患者教育重点">{{ caseDetail.patientEducation }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script>
import api from '@/utils/api'
import { getImageUrl } from '@/utils/imageHelper'
import { API_BASE_URL } from '../config/api.config'
import axios from 'axios'

export default {
  name: 'CaseView',
  data() {
    return {
      caseId: this.$route.params.id,
      loading: true,
      caseDetail: {
        caseId: '',
        patientInfo: '',
        department: '',
        type: '',
        status: '',
        note: '',
        createTime: '',
        updateTime: '',
        // 新增字段
        lesionSize: '',
        lesionColor: '',
        bloodFlow: '',
        borderClarity: '',
        diseaseStage: '',
        morphologicalFeatures: '',
        symptoms: '',
        symptomDetails: '',
        complications: '',
        complicationDetails: '',
        diagnosisCode: '',
        treatmentPriority: '',
        treatmentPlan: '',
        recommendedTreatment: '',
        contraindications: '',
        followUpSchedule: '',
        prognosisRating: '',
        patientEducation: '',
        // 血管瘤特有字段
        diagnosticSummary: '',
        dietaryAdvice: '',
        emergencyInstructions: '',
        reviewNotes: '',
        // 添加权限检查相关字段
        createdBy: '',
        teamId: '',
        detectedType: '', // 新增：检测到的血管瘤类型
        vesselTexture: '' // 新增：血管质地
      },
      tags: [],
      annotatedImageUrl: null,
      imageLoadError: false,
      debugInfo: {
        rawPath: '',
        fullUrl: ''
      },
      // 新增数据刷新控制
      currentUser: null,
      lastRefreshTime: null,
      refreshInterval: 60000, // 刷新间隔，默认1分钟
      hasPermission: false,   // 用户权限标志
      dataVersion: 0          // 数据版本号，用于判断是否需要刷新
    }
  },
  created() {
    // 将axios实例挂载到Vue实例上，方便调用
    this.$axios = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    this.getCurrentUser();
    this.fetchCaseDetail();
    // 监听数据更新事件
    window.addEventListener('case-data-updated', this.handleDataUpdate);
  },
  beforeUnmount() {
    // 清理事件监听器
    window.removeEventListener('case-data-updated', this.handleDataUpdate);
  },
  methods: {
    // 获取当前登录用户信息
    getCurrentUser() {
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          this.currentUser = JSON.parse(userStr);
          console.log('当前用户:', this.currentUser);
        } else {
          console.warn('未找到登录用户信息');
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    },

    // 检查用户权限
    checkPermission() {
      if (!this.currentUser) {
        console.warn('未登录，无法验证权限');
        return false;
      }
      
      // 检查是否为系统管理员或标注医生
      const isAdmin = this.currentUser.role === 'ADMIN';
      const isAnnotator = this.currentUser.role === 'ANNOTATOR' || this.currentUser.role === 'DOCTOR';
      
      // 检查是否是病例创建者或所属团队成员
      const isCreator = this.caseDetail.createdBy === this.currentUser.id || 
                        this.caseDetail.createdBy === this.currentUser.customId;
      const isSameTeam = this.caseDetail.teamId && 
                        this.currentUser.teamId && 
                        this.caseDetail.teamId === this.currentUser.teamId;
      
      // 设置权限标志
      this.hasPermission = isAdmin || (isAnnotator && (isCreator || isSameTeam));
      
      console.log('权限检查结果:', this.hasPermission, {
        isAdmin, isAnnotator, isCreator, isSameTeam
      });
      
      return this.hasPermission;
    },
    
    // 处理数据更新事件
    handleDataUpdate(event) {
      // 检查是否是当前病例的更新
      if (event && event.detail && event.detail.caseId === this.caseId) {
        console.log('接收到病例数据更新事件:', event.detail);
        
        // 检查是否是当前用户的更新
        if (
          !event.detail.userId || // 如果没有指定用户ID，认为是系统级更新
          event.detail.userId === this.currentUser?.id || 
          event.detail.userId === this.currentUser?.customId
        ) {
          // 增加数据版本号，触发刷新
          this.dataVersion++;
          
          // 判断是否需要立即刷新
          const shouldRefreshNow = event.detail.immediate === true || 
                                 (Date.now() - this.lastRefreshTime) > this.refreshInterval;
          
          if (shouldRefreshNow) {
            console.log('刷新病例数据和图片');
            this.fetchCaseDetail();
          } else {
            console.log('延迟刷新，等待下次刷新周期');
          }
        }
      }
    },

    // 数据刷新控制
    shouldRefreshData() {
      // 如果没有上次刷新时间，说明是首次加载
      if (!this.lastRefreshTime) return true;
      
      // 检查是否超过刷新间隔
      const now = Date.now();
      const timeSinceLastRefresh = now - this.lastRefreshTime;
      
      return timeSinceLastRefresh > this.refreshInterval;
    },
    
    // 更新刷新时间
    updateRefreshTime() {
      this.lastRefreshTime = Date.now();
    },
    
    redirectToAnnotate() {
      // 跳转到标注页面，而不是编辑页面
      // 设置标志表明这是编辑操作，以便表单组件知道是更新现有数据而不是创建新数据
      localStorage.setItem('isEditingCase', 'true');
      
      this.$router.push({
        path: '/case/' + this.caseData.id + '/annotate-and-form',
        query: { 
          imageId: this.caseId,
          edit: 'true' 
        }
      });
    },
    
    getStatusType(status) {
      const types = {
        '待标注': 'info',
        '已标注': 'success',
        '审核中': 'warning',
        '已通过': 'success',
        '已驳回': 'danger'
      }
      return types[status] || 'info'
    },
    
    // 获取血管质地标签
    getVesselTextureLabel(value) {
      const textureMap = {
        'soft': '质软',
        'elastic': '质韧',
        'hard': '质硬',
        'cystic': '囊性',
        'compressible': '可压缩'
      };
      return textureMap[value] || value;
    },
    
    async fetchCaseDetail() {
      this.loading = true;
      try {
        console.log('开始获取血管瘤诊断数据，ID:', this.caseId);
        
        // 直接使用axios，并构造完整的URL，避免api.js中的潜在问题
        const response = await this.$axios.get(`/medical/api/hemangioma-diagnoses/${this.caseId}`);
        const diagnosisData = response.data;
        
        console.log('获取到的诊断数据:', diagnosisData);
        
        if (diagnosisData) {
          // 更新数据版本号和刷新时间
          this.dataVersion++;
          this.lastRefreshTime = Date.now();
          
          // 准备基本信息
          this.caseDetail = {
            caseId: `CASE-${diagnosisData.id}`,
            patientInfo: `${diagnosisData.patientAge || '未知'}岁 ${diagnosisData.gender || '未知'}`,
            department: diagnosisData.bodyPart || '未知',
            type: diagnosisData.originType || '未知',
            status: this.getStatusText(diagnosisData.status || 'DRAFT'),
            note: '无',
            createTime: this.formatDate(diagnosisData.createdAt),
            updateTime: this.formatDate(diagnosisData.updatedAt || diagnosisData.createdAt),
            
            // 详细信息字段
            lesionColor: diagnosisData.color || '无',
            vesselTexture: diagnosisData.vesselTexture || '无',
            detectedType: diagnosisData.detectedType || '未知', // 设置检测到的类型
            
            // LLM生成的建议
            treatmentSuggestion: diagnosisData.treatmentSuggestion || '无',
            precautions: diagnosisData.precautions || '无',
            diagnosticSummary: diagnosisData.diagnosticSummary || '无',
            reviewNotes: diagnosisData.reviewNotes || '无', // 新增：审核备注

            // 权限相关字段
            createdBy: diagnosisData.user ? diagnosisData.user.id : null,
            teamId: diagnosisData.user && diagnosisData.user.team ? diagnosisData.user.team.id : null
          };

          // 设置标注后的图像URL
          if (diagnosisData.processedImagePath) {
            this.annotatedImageUrl = this.getImageUrl(diagnosisData.processedImagePath);
            this.debugInfo.rawPath = diagnosisData.processedImagePath;
            this.debugInfo.fullUrl = this.annotatedImageUrl;
          } else {
            this.annotatedImageUrl = null;
          }
          
          // 检查用户权限
          this.checkPermission();
          
          // 获取相关的标签信息
          this.fetchTags();
        } else {
          this.$message.error('未找到有效的病例数据');
        }
      } catch (error) {
        console.error('获取病例详情失败:', error);
        this.$message.error('获取病例详情失败: ' + (error.response?.data?.error || error.message));
      } finally {
        this.loading = false;
      }
    },
    
    getStatusText(status) {
      const statusMap = {
        'DRAFT': '待标注',
        'SUBMITTED': '已标注',
        'PENDING': '审核中', 
        'APPROVED': '已通过',
        'REJECTED': '已驳回'
      }
      return statusMap[status] || '未知';
    },
    
    async fetchTags() {
      try {
        const response = await api.get(`/hemangioma-diagnoses/${this.caseId}/tags`);
        this.tags = response.data;
      } catch (error) {
        console.error('获取标签数据失败:', error);
      }
    },
    
    formatDate(dateString) {
      if (!dateString) return '无';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    },
    handleImageError() {
      console.error('标注图片加载失败');
      this.imageLoadError = true;
      console.log('图片加载失败的调试信息:', this.debugInfo);
    },
    
    getImageUrl,
    
    // 新增: 手动刷新数据的方法
    refreshData() {
      console.log('手动刷新数据');
      this.fetchCaseDetail();
    },

    // 格式化时间为友好格式
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }
}
</script>

<style scoped>
.case-view-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.annotated-image-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.annotated-image-section h3 {
  margin-bottom: 15px;
  margin-top: 0;
  font-size: 18px;
  color: #333;
}

.annotated-image-container {
  width: 100%;
  max-width: 300px;
  height: auto;
  overflow: visible;
  border-radius: 4px;
  border: 1px solid #eee;
  margin: 0 auto;
  box-shadow: 0 2px 4px rgba(0,0,0,.05);
  padding-bottom: 10px;
}

.annotated-image {
  width: 100%;
  max-width: 300px;
  height: auto;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.image-error, .no-image-message {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 150px;
  background-color: #f5f7fa;
  color: #909399;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

.detailed-case-info {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.detailed-case-info h3 {
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.case-info {
  margin-bottom: 20px;
}

.debug-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border: 1px dashed #ccc;
  border-radius: 4px;
  text-align: left;
  font-size: 12px;
  color: #666;
  max-width: 100%;
}

.debug-info h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.debug-info h5 {
  margin: 15px 0 5px 0;
  font-size: 13px;
  color: #444;
}

.debug-info p {
  margin: 5px 0;
  word-break: break-all;
}

.path-options {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.path-option {
  display: flex;
  flex-direction: column;
}

.path-option small {
  margin-top: 2px;
  color: #888;
}

.actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.full-paths {
  margin-top: 15px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.full-path {
  margin: 5px 0;
  word-break: break-all;
}

.hint {
  margin-top: 10px;
  font-style: italic;
  color: #909399;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.refresh-info, .version-info {
  color: #909399;
  font-size: 12px;
}

.version-info {
  background-color: #f0f9eb;
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid #e1f3d8;
}

.hemangioma-specific-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.hemangioma-specific-section h4 {
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.mt-4 {
  margin-top: 20px;
}
</style> 