{"version": 3, "file": "js/191.7d6c1f07.js", "mappings": "gMACOA,MAAM,Y,GAIJA,MAAM,c,GACJA,MAAM,c,GAKNA,MAAM,c,GAKNA,MAAM,c,GAKNA,MAAM,c,GAKNA,MAAM,c,GAKNA,MAAM,c,GAKNA,MAAM,c,GAMRA,MAAM,e,GAMNA,MAAM,e,GAhDfC,IAAA,G,GAAAA,IAAA,EAqDwBD,MAAM,S,0CApD5BE,EAAAA,EAAAA,IAyDM,MAzDNC,EAyDM,gBAxDJC,EAAAA,EAAAA,IAAe,UAAX,UAAM,mBACVA,EAAAA,EAAAA,IAA0B,SAAvB,uBAAmB,KAEtBA,EAAAA,EAAAA,IAmCM,MAnCNC,EAmCM,EAlCJD,EAAAA,EAAAA,IAGM,MAHNE,EAGM,gBAFJF,EAAAA,EAAAA,IAAiC,aAA1B,sBAAkB,cACzBA,EAAAA,EAAAA,IAAkE,SAA3DG,KAAK,SARpB,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAQsCC,EAAAC,YAAWF,CAAA,GAAEG,YAAY,U,iBAAzBF,EAAAC,kBAGhCP,EAAAA,EAAAA,IAGM,MAHNS,EAGM,gBAFJT,EAAAA,EAAAA,IAAyB,aAAlB,cAAU,cACjBA,EAAAA,EAAAA,IAAwD,SAAjDG,KAAK,OAbpB,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAaoCC,EAAAI,IAAGL,CAAA,GAAEG,YAAY,U,iBAAjBF,EAAAI,UAG9BV,EAAAA,EAAAA,IAGM,MAHNW,EAGM,gBAFJX,EAAAA,EAAAA,IAAiC,aAA1B,sBAAkB,cACzBA,EAAAA,EAAAA,IAAkE,SAA3DG,KAAK,SAlBpB,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAkBsCC,EAAAM,WAAUP,CAAA,GAAEG,YAAY,W,iBAAxBF,EAAAM,iBAGhCZ,EAAAA,EAAAA,IAGM,MAHNa,EAGM,gBAFJb,EAAAA,EAAAA,IAAsB,aAAf,WAAO,cACdA,EAAAA,EAAAA,IAAsE,SAA/DG,KAAK,SAvBpB,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAuBsCC,EAAAQ,EAACT,CAAA,GAAEU,KAAK,OAAOP,YAAY,Y,iBAA3BF,EAAAQ,QAGhCd,EAAAA,EAAAA,IAGM,MAHNgB,EAGM,gBAFJhB,EAAAA,EAAAA,IAAsB,aAAf,WAAO,cACdA,EAAAA,EAAAA,IAAsE,SAA/DG,KAAK,SA5BpB,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA4BsCC,EAAAW,EAACZ,CAAA,GAAEU,KAAK,OAAOP,YAAY,Y,iBAA3BF,EAAAW,QAGhCjB,EAAAA,EAAAA,IAGM,MAHNkB,EAGM,gBAFJlB,EAAAA,EAAAA,IAAyB,aAAlB,cAAU,cACjBA,EAAAA,EAAAA,IAAyE,SAAlEG,KAAK,SAjCpB,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAiCsCC,EAAAa,MAAKd,CAAA,GAAEU,KAAK,OAAOP,YAAY,W,iBAA/BF,EAAAa,YAGhCnB,EAAAA,EAAAA,IAGM,MAHNoB,EAGM,gBAFJpB,EAAAA,EAAAA,IAA0B,aAAnB,eAAW,cAClBA,EAAAA,EAAAA,IAA0E,SAAnEG,KAAK,SAtCpB,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAsCsCC,EAAAe,OAAMhB,CAAA,GAAEU,KAAK,OAAOP,YAAY,W,iBAAhCF,EAAAe,eAIlCrB,EAAAA,EAAAA,IAIM,MAJNsB,EAIM,EAHJtB,EAAAA,EAAAA,IAAiD,UAAxCuB,QAAKnB,EAAA,KAAAA,EAAA,qBAAEoB,EAAAC,WAAAD,EAAAC,UAAAC,MAAAF,EAAAG,UAAS,IAAE,kBAC3B3B,EAAAA,EAAAA,IAA6C,UAApCuB,QAAKnB,EAAA,KAAAA,EAAA,qBAAEoB,EAAAI,WAAAJ,EAAAI,UAAAF,MAAAF,EAAAG,UAAS,IAAE,cAC3B3B,EAAAA,EAAAA,IAAyC,UAAhCuB,QAAKnB,EAAA,KAAAA,EAAA,qBAAEoB,EAAAK,SAAAL,EAAAK,QAAAH,MAAAF,EAAAG,UAAO,IAAE,cAG3B3B,EAAAA,EAAAA,IASM,MATN8B,EASM,gBARJ9B,EAAAA,EAAAA,IAAa,UAAT,QAAI,IACGM,EAAAyB,SAAM,WAAjBjC,EAAAA,EAAAA,IAEM,MApDZkC,EAAA,EAmDQhC,EAAAA,EAAAA,IAAuB,YAAAiC,EAAAA,EAAAA,IAAf3B,EAAAyB,QAAM,OAnDtBG,EAAAA,EAAAA,IAAA,OAqDiB5B,EAAA6B,QAAK,WAAhBrC,EAAAA,EAAAA,IAGM,MAHNsC,EAGM,gBAFJpC,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAAsB,YAAAiC,EAAAA,EAAAA,IAAd3B,EAAA6B,OAAK,OAvDrBD,EAAAA,EAAAA,IAAA,U,kJAgEA,SACEG,KAAM,UACNC,KAAI,WACF,MAAO,CACL/B,YAAa,EACbG,IAAK,MACLE,WAAY,EACZE,EAAG,GACHG,EAAG,GACHE,MAAO,GACPE,OAAQ,GACRU,OAAQ,KACRI,MAAO,KAEX,EACAI,QAAS,CACDd,UAAS,WAAG,IAAAe,EAAA,YAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAP,EAAAQ,EAAAC,EAAAC,EAAAjB,EAAAkB,EAAA,OAAAP,EAAAA,EAAAA,KAAAQ,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OAgCd,GA/BFZ,EAAKT,OAAS,KACdS,EAAKL,MAAQ,KAAIgB,EAAAE,EAAA,EAGTR,EAAOS,KAAKC,MAAMC,aAAaC,QAAQ,SAAW,MAGlDnB,EAAO,CACX/B,YAAamD,SAASlB,EAAKjC,aAC3BG,IAAK8B,EAAK9B,IACVE,WAAY8C,SAASlB,EAAK5B,YAC1BE,EAAG6C,WAAWnB,EAAK1B,GACnBG,EAAG0C,WAAWnB,EAAKvB,GACnBE,MAAOwC,WAAWnB,EAAKrB,OACvBE,OAAQsC,WAAWnB,EAAKnB,SAIpByB,EAAU,CACd,eAAgB,mBAChB,OAAU,qBAGRD,EAAKe,IAAMf,EAAKgB,YACZd,EAASF,EAAKgB,UAAYhB,EAAKe,GACrCd,EAAQ,iBAAmB,eAAJgB,OAAmBf,IAI5CgB,QAAQC,IAAI,0BAA2B1B,GAGlC2B,QAAQ,iCAADH,OAAkCR,KAAKY,UAAU5B,EAAM,KAAM,GAAE,YAAY,CAAFa,EAAAC,EAAA,QAC5D,OAAvBZ,EAAKT,OAAS,UAASoB,EAAAgB,EAAA,iBAAAhB,EAAAC,EAAA,EAKFgB,MAAM,mBAAoB,CAC/CC,OAAQ,OACRvB,QAASA,EACTwB,KAAMhB,KAAKY,UAAU5B,GACrBiC,YAAa,YACb,OALW,GAAPvB,EAAOG,EAAAqB,EAQRxB,EAASyB,GAAI,CAAFtB,EAAAC,EAAA,cACR,IAAIsB,MAAM,uBAADZ,OAAwBd,EAAS2B,SAAS,cAAAxB,EAAAC,EAAA,EAItCJ,EAAS4B,OAAM,OAA9B7C,EAAKoB,EAAAqB,EAGXhC,EAAKT,OAASuB,KAAKY,UAAUnC,EAAQ,KAAM,GAC3C8C,MAAM,cAAa1B,EAAAC,EAAA,eAAAD,EAAAE,EAAA,EAAAJ,EAAAE,EAAAqB,EAEnBT,QAAQ5B,MAAM,QAAOc,GACrBT,EAAKL,MAAQc,EAAM6B,WACnBD,MAAM,SAAW5B,EAAM6B,YAAW,cAAA3B,EAAAgB,EAAA,MAAAvB,EAAA,iBA5DpBH,EA8DlB,EAEMb,UAAS,WAAG,IAAAmD,EAAA,YAAAtC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAqC,IAAA,IAAA1C,EAAAU,EAAAiC,EAAA,OAAAvC,EAAAA,EAAAA,KAAAQ,GAAA,SAAAgC,GAAA,eAAAA,EAAA9B,GAAA,OAmBd,GAlBF2B,EAAKhD,OAAS,KACdgD,EAAK5C,MAAQ,KAAI+C,EAAA7B,EAAA,EAITf,EAAO,CACX/B,YAAamD,SAASqB,EAAKxE,aAC3BG,IAAKqE,EAAKrE,IACVE,WAAY8C,SAASqB,EAAKnE,YAC1BE,EAAG6C,WAAWoB,EAAKjE,GACnBG,EAAG0C,WAAWoB,EAAK9D,GACnBE,MAAOwC,WAAWoB,EAAK5D,OACvBE,OAAQsC,WAAWoB,EAAK1D,SAI1B0C,QAAQC,IAAI,iCAAkC1B,GAGzC2B,QAAQ,wCAADH,OAAyCR,KAAKY,UAAU5B,EAAM,KAAM,GAAE,YAAY,CAAF4C,EAAA9B,EAAA,QACnE,OAAvB2B,EAAKhD,OAAS,UAASmD,EAAAf,EAAA,iBAAAe,EAAA9B,EAAA,EAKF+B,IAAAA,KAAW,mBAAoB7C,EAAM,CAC1DQ,QAAS,CACP,eAAgB,mBAChB,OAAU,oBAEZsC,iBAAiB,IACjB,OANIpC,EAAOkC,EAAAV,EASbO,EAAKhD,OAASuB,KAAKY,UAAUlB,EAASV,KAAM,KAAM,GAClDuC,MAAM,cAAaK,EAAA9B,EAAA,eAAA8B,EAAA7B,EAAA,EAAA4B,EAAAC,EAAAV,EAEnBT,QAAQ5B,MAAM,aAAY8C,GAC1BF,EAAK5C,MAAQ8C,EAAMH,WACfG,EAAMjC,WACR+B,EAAK5C,OAAS,sBAAwBmB,KAAKY,UAAUe,EAAMjC,SAASV,KAAM,KAAM,IAElFuC,MAAM,SAAWI,EAAMH,YAAW,cAAAI,EAAAf,EAAA,MAAAa,EAAA,iBA3CpBvC,EA6ClB,EAEAZ,QAAO,WAAG,IAAAwD,EAAA,KACRC,KAAKvD,OAAS,KACduD,KAAKnD,MAAQ,KAEb,IAEE,IAAMG,EAAO,CACX/B,YAAamD,SAAS4B,KAAK/E,aAC3BG,IAAK4E,KAAK5E,IACVE,WAAY8C,SAAS4B,KAAK1E,YAC1BE,EAAG6C,WAAW2B,KAAKxE,GACnBG,EAAG0C,WAAW2B,KAAKrE,GACnBE,MAAOwC,WAAW2B,KAAKnE,OACvBE,OAAQsC,WAAW2B,KAAKjE,SAO1B,GAHA0C,QAAQC,IAAI,+BAAgC1B,IAGvC2B,QAAQ,sCAADH,OAAuCR,KAAKY,UAAU5B,EAAM,KAAM,GAAE,YAE9E,YADAgD,KAAKvD,OAAS,WAKhB,IAAMwD,EAAM,IAAIC,eAChBD,EAAIE,KAAK,OAAQ,oBAAoB,GACrCF,EAAIG,iBAAiB,eAAgB,oBACrCH,EAAIG,iBAAiB,SAAU,oBAC/BH,EAAIH,iBAAkB,EAEtBG,EAAII,OAAS,WACPJ,EAAIZ,QAAU,KAAOY,EAAIZ,OAAS,KACpCU,EAAKtD,OAASuB,KAAKY,UAAUZ,KAAKC,MAAMgC,EAAIK,cAAe,KAAM,GACjEf,MAAM,gBAENQ,EAAKlD,MAAI,cAAA2B,OAAkByB,EAAIZ,OAAM,KAAAb,OAAIyB,EAAIM,WAAU,MAAA/B,OAAKyB,EAAIK,cAChEf,MAAM,SAAWU,EAAIZ,OAAS,IAAMY,EAAIM,YAE5C,EAEAN,EAAIO,QAAU,WACZT,EAAKlD,MAAQ,UACb0C,MAAM,UACR,EAGAU,EAAIQ,KAAKzC,KAAKY,UAAU5B,GAC1B,CAAE,MAAOH,GACP4B,QAAQ5B,MAAM,WAAYA,GAC1BmD,KAAKnD,MAAQA,EAAM2C,WACnBD,MAAM,SAAW1C,EAAM2C,WACzB,CACF,I,eC9OJ,MAAMkB,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/components/TagTest.vue", "webpack://medical-annotation-frontend/./src/components/TagTest.vue?41d7"], "sourcesContent": ["<template>\r\n  <div class=\"tag-test\">\r\n    <h2>标注测试工具</h2>\r\n    <p>这个组件用于测试直接向后端发送标注请求</p>\r\n    \r\n    <div class=\"input-area\">\r\n      <div class=\"form-group\">\r\n        <label>图像ID (metadata_id)</label>\r\n        <input type=\"number\" v-model=\"metadata_id\" placeholder=\"输入图像ID\" />\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>标签名称 (tag)</label>\r\n        <input type=\"text\" v-model=\"tag\" placeholder=\"输入标签名称\" />\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>创建者ID (created_by)</label>\r\n        <input type=\"number\" v-model=\"created_by\" placeholder=\"输入创建者ID\" />\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>X坐标 (x)</label>\r\n        <input type=\"number\" v-model=\"x\" step=\"0.01\" placeholder=\"输入归一化X坐标\" />\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>Y坐标 (y)</label>\r\n        <input type=\"number\" v-model=\"y\" step=\"0.01\" placeholder=\"输入归一化Y坐标\" />\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>宽度 (width)</label>\r\n        <input type=\"number\" v-model=\"width\" step=\"0.01\" placeholder=\"输入归一化宽度\" />\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label>高度 (height)</label>\r\n        <input type=\"number\" v-model=\"height\" step=\"0.01\" placeholder=\"输入归一化高度\" />\r\n      </div>\r\n    </div>\r\n    \r\n    <div class=\"action-area\">\r\n      <button @click=\"testFetch\">使用Fetch API测试</button>\r\n      <button @click=\"testAxios\">使用Axios测试</button>\r\n      <button @click=\"testXHR\">使用XHR测试</button>\r\n    </div>\r\n    \r\n    <div class=\"result-area\">\r\n      <h3>测试结果</h3>\r\n      <div v-if=\"result\">\r\n        <pre>{{ result }}</pre>\r\n      </div>\r\n      <div v-if=\"error\" class=\"error\">\r\n        <h4>错误信息</h4>\r\n        <pre>{{ error }}</pre>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios';\r\n\r\nexport default {\r\n  name: 'TagTest',\r\n  data() {\r\n    return {\r\n      metadata_id: 1,\r\n      tag: '血管瘤',\r\n      created_by: 1,\r\n      x: 0.5,\r\n      y: 0.5,\r\n      width: 0.2,\r\n      height: 0.2,\r\n      result: null,\r\n      error: null\r\n    };\r\n  },\r\n  methods: {\r\n    async testFetch() {\r\n      this.result = null;\r\n      this.error = null;\r\n      \r\n      try {\r\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n        \r\n        // 构建请求数据\r\n        const data = {\r\n          metadata_id: parseInt(this.metadata_id),\r\n          tag: this.tag,\r\n          created_by: parseInt(this.created_by),\r\n          x: parseFloat(this.x),\r\n          y: parseFloat(this.y),\r\n          width: parseFloat(this.width),\r\n          height: parseFloat(this.height)\r\n        };\r\n        \r\n        // 添加认证信息\r\n        const headers = {\r\n          'Content-Type': 'application/json',\r\n          'Accept': 'application/json'\r\n        };\r\n        \r\n        if (user.id || user.customId) {\r\n          const userId = user.customId || user.id;\r\n          headers['Authorization'] = `Bearer user_${userId}`;\r\n        }\r\n        \r\n        // 记录请求详情\r\n        console.log('发送请求到: /medical/api/api', data);\r\n        \r\n        // 显示确认框\r\n        if (!confirm(`即将发送请求到 /medical/api/api\\n数据: ${JSON.stringify(data, null, 2)}\\n确认发送?`)) {\r\n          this.result = \"用户取消了请求\";\r\n          return;\r\n        }\r\n        \r\n        // 发送请求\r\n        const response = await fetch('/medical/api/api', {\r\n          method: 'POST',\r\n          headers: headers,\r\n          body: JSON.stringify(data),\r\n          credentials: 'include'\r\n        });\r\n        \r\n        // 检查响应状态\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! Status: ${response.status}`);\r\n        }\r\n        \r\n        // 解析响应\r\n        const result = await response.json();\r\n        \r\n        // 显示结果\r\n        this.result = JSON.stringify(result, null, 2);\r\n        alert('请求成功，查看结果!');\r\n      } catch (error) {\r\n        console.error('测试失败:', error);\r\n        this.error = error.toString();\r\n        alert('请求失败: ' + error.toString());\r\n      }\r\n    },\r\n    \r\n    async testAxios() {\r\n      this.result = null;\r\n      this.error = null;\r\n      \r\n      try {\r\n        // 构建请求数据\r\n        const data = {\r\n          metadata_id: parseInt(this.metadata_id),\r\n          tag: this.tag,\r\n          created_by: parseInt(this.created_by),\r\n          x: parseFloat(this.x),\r\n          y: parseFloat(this.y),\r\n          width: parseFloat(this.width),\r\n          height: parseFloat(this.height)\r\n        };\r\n        \r\n        // 记录请求详情\r\n        console.log('使用Axios发送请求到: /medical/api/api', data);\r\n        \r\n        // 显示确认框\r\n        if (!confirm(`即将使用Axios发送请求到 /medical/api/api\\n数据: ${JSON.stringify(data, null, 2)}\\n确认发送?`)) {\r\n          this.result = \"用户取消了请求\";\r\n          return;\r\n        }\r\n        \r\n        // 发送请求\r\n        const response = await axios.post('/medical/api/api', data, {\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Accept': 'application/json'\r\n          },\r\n          withCredentials: true\r\n        });\r\n        \r\n        // 显示结果\r\n        this.result = JSON.stringify(response.data, null, 2);\r\n        alert('请求成功，查看结果!');\r\n      } catch (error) {\r\n        console.error('Axios测试失败:', error);\r\n        this.error = error.toString();\r\n        if (error.response) {\r\n          this.error += '\\n\\nResponse data: ' + JSON.stringify(error.response.data, null, 2);\r\n        }\r\n        alert('请求失败: ' + error.toString());\r\n      }\r\n    },\r\n    \r\n    testXHR() {\r\n      this.result = null;\r\n      this.error = null;\r\n      \r\n      try {\r\n        // 构建请求数据\r\n        const data = {\r\n          metadata_id: parseInt(this.metadata_id),\r\n          tag: this.tag,\r\n          created_by: parseInt(this.created_by),\r\n          x: parseFloat(this.x),\r\n          y: parseFloat(this.y),\r\n          width: parseFloat(this.width),\r\n          height: parseFloat(this.height)\r\n        };\r\n        \r\n        // 记录请求详情\r\n        console.log('使用XHR发送请求到: /medical/api/api', data);\r\n        \r\n        // 显示确认框\r\n        if (!confirm(`即将使用XHR发送请求到 /medical/api/api\\n数据: ${JSON.stringify(data, null, 2)}\\n确认发送?`)) {\r\n          this.result = \"用户取消了请求\";\r\n          return;\r\n        }\r\n        \r\n        // 创建XHR请求\r\n        const xhr = new XMLHttpRequest();\r\n        xhr.open('POST', '/medical/api/api', true);\r\n        xhr.setRequestHeader('Content-Type', 'application/json');\r\n        xhr.setRequestHeader('Accept', 'application/json');\r\n        xhr.withCredentials = true;\r\n        \r\n        xhr.onload = () => {\r\n          if (xhr.status >= 200 && xhr.status < 300) {\r\n            this.result = JSON.stringify(JSON.parse(xhr.responseText), null, 2);\r\n            alert('请求成功，查看结果!');\r\n          } else {\r\n            this.error = `XHR Error: ${xhr.status} ${xhr.statusText}\\n${xhr.responseText}`;\r\n            alert('请求失败: ' + xhr.status + ' ' + xhr.statusText);\r\n          }\r\n        };\r\n        \r\n        xhr.onerror = () => {\r\n          this.error = 'XHR请求失败';\r\n          alert('XHR请求失败');\r\n        };\r\n        \r\n        // 发送请求\r\n        xhr.send(JSON.stringify(data));\r\n      } catch (error) {\r\n        console.error('XHR测试失败:', error);\r\n        this.error = error.toString();\r\n        alert('请求失败: ' + error.toString());\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.tag-test {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n  font-family: Arial, sans-serif;\r\n}\r\n\r\n.input-area {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\nlabel {\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\ninput {\r\n  padding: 8px;\r\n  border: 1px solid #ccc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.action-area {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\nbutton {\r\n  padding: 10px 15px;\r\n  background-color: #4CAF50;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n}\r\n\r\nbutton:hover {\r\n  background-color: #45a049;\r\n}\r\n\r\n.result-area {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background-color: #f5f5f5;\r\n  border-radius: 4px;\r\n}\r\n\r\npre {\r\n  white-space: pre-wrap;\r\n  background-color: #eee;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  overflow: auto;\r\n}\r\n\r\n.error {\r\n  margin-top: 10px;\r\n  color: #d32f2f;\r\n}\r\n</style> ", "import { render } from \"./TagTest.vue?vue&type=template&id=08dc852c&scoped=true\"\nimport script from \"./TagTest.vue?vue&type=script&lang=js\"\nexport * from \"./TagTest.vue?vue&type=script&lang=js\"\n\nimport \"./TagTest.vue?vue&type=style&index=0&id=08dc852c&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-08dc852c\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "type", "_cache", "$event", "$data", "metadata_id", "placeholder", "_hoisted_4", "tag", "_hoisted_5", "created_by", "_hoisted_6", "x", "step", "_hoisted_7", "y", "_hoisted_8", "width", "_hoisted_9", "height", "_hoisted_10", "onClick", "$options", "testFetch", "apply", "arguments", "testAxios", "testXHR", "_hoisted_11", "result", "_hoisted_12", "_toDisplayString", "_createCommentVNode", "error", "_hoisted_13", "name", "data", "methods", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "user", "headers", "userId", "response", "_t", "w", "_context", "n", "p", "JSON", "parse", "localStorage", "getItem", "parseInt", "parseFloat", "id", "customId", "concat", "console", "log", "confirm", "stringify", "a", "fetch", "method", "body", "credentials", "v", "ok", "Error", "status", "json", "alert", "toString", "_this2", "_callee2", "_t2", "_context2", "axios", "withCredentials", "_this3", "this", "xhr", "XMLHttpRequest", "open", "setRequestHeader", "onload", "responseText", "statusText", "onerror", "send", "__exports__", "render"], "sourceRoot": ""}