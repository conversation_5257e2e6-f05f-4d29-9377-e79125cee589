package com.medical.annotation.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * Web配置类，用于解决字符编码问题
 */
@Configuration
@Primary
public class WebConfig implements WebMvcConfigurer {

    // 注入AppConfig中配置的图片存储路径
    @Autowired
    @Qualifier("getUploadDir")
    private String uploadDir;
    
    @Autowired
    @Qualifier("getAnnotatedDir")
    private String annotatedDir;
    
    @Autowired
    @Qualifier("getProcessedDir")
    private String processedDir;
    
    @Autowired
    @Qualifier("getTempImagesDir")
    private String tempImagesDir;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 确保目录存在
        createDirectoryIfNotExists(uploadDir);
        createDirectoryIfNotExists(annotatedDir);
        createDirectoryIfNotExists(processedDir);
        createDirectoryIfNotExists(tempImagesDir);
        // 确保头像目录存在
        createDirectoryIfNotExists("medical_images/head_portrait");
        
        // 使用绝对路径而不是相对路径
        File uploadDirFile = new File(uploadDir);
        File processedDirFile = new File(processedDir);
        File annotatedDirFile = new File(annotatedDir);
        File tempImagesDirFile = new File(tempImagesDir);
        
        // 确保使用绝对路径
        String uploadDirFullPath = uploadDirFile.getAbsolutePath();
        String processedDirFullPath = processedDirFile.getAbsolutePath();
        String annotatedDirFullPath = annotatedDirFile.getAbsolutePath();
        String tempImagesDirFullPath = tempImagesDirFile.getAbsolutePath();
        
        // 格式化为文件URL格式
        String uploadDirUrl = "file:" + ensureCorrectPathFormat(uploadDirFullPath) + "/";
        String processedDirUrl = "file:" + ensureCorrectPathFormat(processedDirFullPath) + "/";
        String annotatedDirUrl = "file:" + ensureCorrectPathFormat(annotatedDirFullPath) + "/";
        String tempImagesDirUrl = "file:" + ensureCorrectPathFormat(tempImagesDirFullPath) + "/";
        
        // 添加备用目录 - 使前端更容易找到图片文件
        // 使用注入的uploadDir作为备用路径
        String backupImageUrl = "file:" + ensureCorrectPathFormat(uploadDir) + "/";
        
        System.out.println("\n===== 增强的静态资源映射配置 =====");
        
        // 简化路径映射 - 不带/medical前缀
        System.out.println("配置简化路径映射 - 不带/medical前缀");
        
        // 上传原始图片和公共路径 - 简化路径
        registry.addResourceHandler("/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        // 处理后的图片路径 - 简化路径
        registry.addResourceHandler("/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/images/processed/** -> " + processedDirUrl);
        
        // 标注后的图片路径 - 简化路径
        registry.addResourceHandler("/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/images/annotated/** -> " + annotatedDirUrl);
        
        // 临时图像目录映射 - 简化路径
        registry.addResourceHandler("/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 标准路径映射 - 带/medical前缀
        System.out.println("\n配置标准路径映射 - 带/medical前缀");
        
        // 上传原始图片和公共路径 - 标准路径
        registry.addResourceHandler("/medical/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/medical/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        // 处理后的图片路径 - 标准路径
        registry.addResourceHandler("/medical/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/medical/images/processed/** -> " + processedDirUrl);
        
        // 标注后的图片路径 - 标准路径
        registry.addResourceHandler("/medical/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/medical/images/annotated/** -> " + annotatedDirUrl);
        
        // 临时图像目录映射 - 标准路径
        registry.addResourceHandler("/medical/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/medical/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 双重路径问题处理 - 添加对/medical/medical/images/**的支持
        System.out.println("\n配置双重路径映射处理");
        registry.addResourceHandler("/medical/medical/images/**")
                .addResourceLocations(uploadDirUrl, backupImageUrl);
        System.out.println("/medical/medical/images/** -> " + uploadDirUrl + ", " + backupImageUrl);
        
        registry.addResourceHandler("/medical/medical/images/processed/**")
                .addResourceLocations(processedDirUrl);
        System.out.println("/medical/medical/images/processed/** -> " + processedDirUrl);
        
        registry.addResourceHandler("/medical/medical/images/annotated/**")
                .addResourceLocations(annotatedDirUrl);
        System.out.println("/medical/medical/images/annotated/** -> " + annotatedDirUrl);
        
        registry.addResourceHandler("/medical/medical/images/temp/**")
                .addResourceLocations(tempImagesDirUrl, backupImageUrl + "temp/");
        System.out.println("/medical/medical/images/temp/** -> " + tempImagesDirUrl + ", " + backupImageUrl + "temp/");
        
        // 临时目录映射
        String tempDir = System.getProperty("java.io.tmpdir");
        String tempDirUrl = "file:" + ensureCorrectPathFormat(tempDir) + "/";
        registry.addResourceHandler("/temp/**")
                .addResourceLocations(tempDirUrl);
        System.out.println("/temp/** -> " + tempDirUrl);
        
        // 配置头像文件的访问路径
        registry.addResourceHandler("/medical_images/**")
                .addResourceLocations("file:medical_images/");
        
        // 新增头像文件访问路径 - 确保可以通过/medical/images/head_portrait访问头像
        registry.addResourceHandler("/medical/images/head_portrait/**")
                .addResourceLocations("file:medical_images/head_portrait/");
        System.out.println("/medical/images/head_portrait/** -> file:medical_images/head_portrait/");
        
        // 验证目录是否存在并有正确权限
        System.out.println("\n===== 目录权限检查 =====");
        System.out.println("上传目录: " + uploadDirFullPath);
        System.out.println("  - 存在: " + uploadDirFile.exists());
        System.out.println("  - 可读: " + uploadDirFile.canRead());
        System.out.println("  - 可写: " + uploadDirFile.canWrite());
        
        System.out.println("处理目录: " + processedDirFullPath);
        System.out.println("  - 存在: " + processedDirFile.exists());
        System.out.println("  - 可读: " + processedDirFile.canRead());
        System.out.println("  - 可写: " + processedDirFile.canWrite());
        
        System.out.println("标注目录: " + annotatedDirFullPath);
        System.out.println("  - 存在: " + annotatedDirFile.exists());
        System.out.println("  - 可读: " + annotatedDirFile.canRead());
        System.out.println("  - 可写: " + annotatedDirFile.canWrite());
        
        System.out.println("临时目录: " + tempImagesDirFullPath);
        System.out.println("  - 存在: " + tempImagesDirFile.exists());
        System.out.println("  - 可读: " + tempImagesDirFile.canRead());
        System.out.println("  - 可写: " + tempImagesDirFile.canWrite());
        System.out.println("===============================");
    }
    
    /**
     * 确保路径格式正确（适用于文件URL）
     */
    private String ensureCorrectPathFormat(String path) {
        // 确保路径使用正斜杠
        String formattedPath = path.replace('\\', '/');
        
        // 确保路径不以斜杠结尾
        if (formattedPath.endsWith("/")) {
            formattedPath = formattedPath.substring(0, formattedPath.length() - 1);
        }
        
        return formattedPath;
    }
    
    /**
     * 确保目录存在
     */
    private void createDirectoryIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            System.out.println("创建目录 " + dirPath + ": " + (created ? "成功" : "失败"));
            if (!created) {
                System.err.println("无法创建目录: " + dir.getAbsolutePath());
                // 检查父目录权限
                File parentDir = dir.getParentFile();
                if (parentDir != null) {
                    System.err.println("父目录存在: " + parentDir.exists());
                    if (parentDir.exists()) {
                        System.err.println("父目录可写: " + parentDir.canWrite());
                    }
                }
            }
        }
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 设置所有Jackson消息转换器使用UTF-8编码
        converters.stream()
            .filter(converter -> converter instanceof MappingJackson2HttpMessageConverter)
            .forEach(converter -> ((MappingJackson2HttpMessageConverter) converter)
                .setDefaultCharset(StandardCharsets.UTF_8));
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        
        // --- 核心修复：设置正确的来源 ---
        // 允许通过 Nginx 访问的所有可能来源 (IP地址, localhost, 127.0.0.1)
        // 注意：这里没有端口号，因为Nginx监听的是80端口
        config.setAllowedOrigins(Arrays.asList("http://localhost", "http://127.0.0.1", "http://************"));
        
        // 允许所有HTTP方法
        config.addAllowedMethod("*");
        
        // 允许所有头
        config.addAllowedHeader("*");
        
        // 允许发送凭证 (如 cookie)
        config.setAllowCredentials(true);
        
        source.registerCorsConfiguration("/**", config);
        System.out.println("CORS 过滤器已配置，统一处理所有跨域请求。");
        return new CorsFilter(source);
    }
} 