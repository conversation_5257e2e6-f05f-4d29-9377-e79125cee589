package com.medical.annotation.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;

@Service
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Value("${spring.application.name:血管瘤人工智能辅助治疗系统}")
    private String appName;
    
    /**
     * 发送密码重置验证码邮件
     * @param to 接收者邮箱
     * @param code 验证码
     * @throws MessagingException 邮件发送异常
     * @throws UnsupportedEncodingException 编码异常
     */
    public void sendPasswordResetEmail(String to, String code) throws MessagingException, UnsupportedEncodingException {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            // 设置发件人，添加发件人名称
            helper.setFrom(fromEmail, "血管瘤人工智能辅助治疗系统");
            helper.setTo(to);
            helper.setSubject("血管瘤人工智能辅助治疗系统 - 密码重置");
            
            // 构建邮件内容
            String content = "<div style='font-family: Microsoft YaHei, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;'>"
                    + "<h2 style='color: #1890ff; text-align: center;'>血管瘤人工智能辅助治疗系统 - 密码重置</h2>"
                    + "<p>您好，</p>"
                    + "<p>您收到此邮件是因为您（或其他人）请求重置您在血管瘤人工智能辅助治疗系统的账户密码。</p>"
                    + "<p>您的验证码是：</p>"
                    + "<div style='background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;'>" 
                    + code 
                    + "</div>"
                    + "<p>此验证码将在30分钟后失效。如果您没有请求重置密码，请忽略此邮件。</p>"
                    + "<p>谢谢！</p>"
                    + "<p style='color: #888; font-size: 12px; margin-top: 30px; text-align: center;'>"
                    + "此邮件由系统自动发送，请勿回复。"
                    + "</p>"
                    + "</div>";
            
            helper.setText(content, true);
            
            System.out.println("准备发送邮件到: " + to);
            System.out.println("发件人: " + fromEmail);
            System.out.println("主题: 血管瘤人工智能辅助治疗系统 - 密码重置");
            
            mailSender.send(message);
            System.out.println("邮件已成功发送到: " + to);
        } catch (Exception e) {
            System.err.println("发送邮件时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 使用简单邮件发送验证码（备用方法）
     * @param to 接收者邮箱
     * @param code 验证码
     */
    public void sendSimplePasswordResetEmail(String to, String code) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject("血管瘤人工智能辅助治疗系统 - 密码重置");
            
            String content = "您好，\n\n" +
                    "您收到此邮件是因为您（或其他人）请求重置您在血管瘤人工智能辅助治疗系统的账户密码。\n\n" +
                    "您的验证码是：" + code + "\n\n" +
                    "此验证码将在30分钟后失效。如果您没有请求重置密码，请忽略此邮件。\n\n" +
                    "谢谢！\n\n" +
                    "此邮件由系统自动发送，请勿回复。";
            
            message.setText(content);
            
            System.out.println("准备发送简单邮件到: " + to);
            mailSender.send(message);
            System.out.println("简单邮件已成功发送到: " + to);
        } catch (Exception e) {
            System.err.println("发送简单邮件时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
} 