package com.medical.annotation.controller;

import com.medical.annotation.util.BasePathConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.io.File;
// ... 其他导入保持不变 ...

@RestController
@RequestMapping("/api")
public class ImageMetadataController {

    @Autowired
    private BasePathConfig basePathConfig;
    
    // ... 其他字段和方法保持不变，但要替换所有硬编码的路径 ... 
    
    // 将物理路径转换为Web访问路径
    private String getWebPath(String filePath) {
        if (filePath == null) return null;
        
        // 将物理路径转换为Web路径
        String uploadDir = basePathConfig.getUploadDir();
        String processedDir = basePathConfig.getProcessedDir();
        String tempDir = basePathConfig.getTempDir();
        String annotatedDir = basePathConfig.getAnnotatedDir();
        String originalDir = basePathConfig.getOriginalDir();
        
        // 转换逻辑
        String webPath = null;
        
        if (filePath.startsWith(processedDir)) {
            // 处理后的图片路径
            String relativePath = filePath.substring(processedDir.length());
            webPath = "/medical/images/processed" + normalizePath(relativePath);
        } else if (filePath.startsWith(tempDir)) {
            // 临时图片路径
            String relativePath = filePath.substring(tempDir.length());
            webPath = "/medical/images/temp" + normalizePath(relativePath);
        } else if (filePath.startsWith(annotatedDir)) {
            // 标注后的图片路径
            String relativePath = filePath.substring(annotatedDir.length());
            webPath = "/medical/images/annotated" + normalizePath(relativePath);
        } else if (filePath.startsWith(originalDir)) {
            // 原始图片路径
            String relativePath = filePath.substring(originalDir.length());
            webPath = "/medical/images/original" + normalizePath(relativePath);
        } else if (filePath.startsWith(uploadDir)) {
            // 上传根目录的其他文件
            String relativePath = filePath.substring(uploadDir.length());
            webPath = "/medical/images" + normalizePath(relativePath);
        } else {
            // 其他情况，返回原始路径
            webPath = filePath;
        }
        
        return webPath;
    }
    
    // 确保路径以斜杠开头
    private String normalizePath(String path) {
        if (path == null) return "";
        
        // 替换Windows反斜杠为正斜杠
        path = path.replace("\\", "/");
        
        // 确保路径以斜杠开头
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        
        return path;
    }
} 