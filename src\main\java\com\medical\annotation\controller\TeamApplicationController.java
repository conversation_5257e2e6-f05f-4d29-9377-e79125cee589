package com.medical.annotation.controller;

import com.medical.annotation.exception.UserNotFoundException;
import com.medical.annotation.model.TeamApplication;
import com.medical.annotation.model.User;
import com.medical.annotation.service.TeamApplicationService;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/team-applications")
public class TeamApplicationController {

    @Autowired
    private TeamApplicationService teamApplicationService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 创建团队申请
     */
    @PostMapping
    public ResponseEntity<?> createApplication(@RequestBody TeamApplication application) {
        try {
            // 获取当前认证信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth == null || !auth.isAuthenticated()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("message", "用户未登录"));
            }
            
            // 打印认证信息帮助调试
            System.out.println("认证信息: name=" + auth.getName() + ", principal=" + auth.getPrincipal());
            
            // 如果API调用没有指定用户ID，使用当前用户ID或customId
            if (application.getUserId() == null) {
                // 通过email查找当前用户
                try {
                    User currentUser = userService.getUserByEmail(auth.getName()).orElse(null);
                    if (currentUser != null) {
                application.setUserId(currentUser.getId());
                        System.out.println("使用当前用户ID：" + currentUser.getId());
                    } else {
                        System.err.println("无法通过email找到当前用户: " + auth.getName());
                    }
                } catch (Exception e) {
                    System.err.println("获取当前用户信息时出错: " + e.getMessage());
                    // 继续执行，让后续代码处理userId
                }
            }
            
            // 确保userId存在
            if (application.getUserId() == null) {
                return ResponseEntity.badRequest().body(Map.of("message", "未指定用户ID，无法创建申请"));
            }
            
            // 打印请求信息
            System.out.println("创建团队申请 - 最终参数：userId=" + application.getUserId() + 
                              ", teamId=" + application.getTeamId() + 
                              ", reason长度=" + (application.getReason() != null ? application.getReason().length() : 0));
            
            // 直接将申请传递给Service层处理，Service层会处理customId转换
            TeamApplication createdApplication = teamApplicationService.createApplication(application);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(createdApplication);
        } catch (UserNotFoundException e) {
            // 特殊处理用户不存在异常
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            System.err.println("用户不存在异常: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
        } catch (Exception e) {
            // 记录详细错误
            System.err.println("创建团队申请失败: " + e.getMessage());
            e.printStackTrace();
            
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取用户的团队申请
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserApplications(@PathVariable("userId") String userIdParam) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            User targetUser = userService.getUserByCustomIdOrThrow(userIdParam);
            Integer userId = targetUser.getId();
            
            // 只允许管理员或者本人查看
            if (!currentUser.getRole().equals(User.Role.ADMIN) && !currentUser.getId().equals(userId)) {
                throw new Exception("无权访问其他用户的申请记录");
            }
            
            List<TeamApplication> applications = teamApplicationService.getUserApplications(userId);
            
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取团队的申请
     */
    @GetMapping("/team/{teamId}")
    public ResponseEntity<?> getTeamApplications(
            @PathVariable("teamId") Integer teamId,
            @RequestParam(required = false) String status,
            @RequestHeader(value = "X-Authentication-Email", required = false) String authEmail,
            @RequestHeader(value = "X-User-Email", required = false) String userEmail,
            @RequestHeader(value = "X-User-Id", required = false) String userHeaderId) {
        try {
            // 获取标准认证信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            boolean isAuthenticated = auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getName());
            
            User currentUser = null;
            
            // 如果标准认证失败，尝试使用请求头中的信息
            if (!isAuthenticated && (authEmail != null || userEmail != null)) {
                String emailToUse = authEmail != null ? authEmail : userEmail;
                System.out.println("尝试使用请求头中的Email进行认证: " + emailToUse);
                
                try {
                    currentUser = userService.getUserByEmail(emailToUse).orElse(null);
                    if (currentUser != null) {
                        System.out.println("使用请求头成功找到用户: " + currentUser.getName());
                    }
                } catch (Exception e) {
                    System.err.println("使用请求头Email查找用户失败: " + e.getMessage());
                }
            } 
            // 尝试使用标准认证
            else if (isAuthenticated) {
                try {
                    currentUser = userService.getUserByEmail(auth.getName()).orElse(null);
                    System.out.println("使用标准认证成功找到用户: " + (currentUser != null ? currentUser.getName() : "null"));
                } catch (Exception e) {
                    System.err.println("使用标准认证查找用户失败: " + e.getMessage());
                }
            }
            
            // 如果找不到用户但有用户ID头，尝试使用ID查找
            if (currentUser == null && userHeaderId != null) {
                System.out.println("尝试使用请求头中的用户ID进行查找: " + userHeaderId);
                
                try {
                    currentUser = userService.getUserByCustomId(userHeaderId).orElse(null);
                    if (currentUser != null) {
                        System.out.println("使用请求头ID成功找到用户: " + currentUser.getName());
                    }
                } catch (Exception e) {
                    System.err.println("使用请求头ID查找用户失败: " + e.getMessage());
                }
            }
            
            // 如果仍未找到用户，返回未授权错误
            if (currentUser == null) {
                System.err.println("无法获取有效的用户信息，请求头: Email=" + userEmail + ", ID=" + userHeaderId);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("message", "无法获取有效的用户信息，请重新登录"));
            }
            
            // 记录当前用户信息用于调试
            System.out.println("当前用户: ID=" + currentUser.getId() + 
                ", Name=" + currentUser.getName() + 
                ", Role=" + currentUser.getRole());
            
            // 权限检查：管理员或者团队成员可查看
            boolean isAdmin = User.Role.ADMIN.equals(currentUser.getRole());
            boolean isReviewer = User.Role.REVIEWER.equals(currentUser.getRole());
            
            // 暂时允许任何已登录用户访问，以解决紧急问题
            /*
            boolean isTeamMember = currentUser.getTeam() != null && 
                currentUser.getTeam().getId().equals(teamId);
                
            if (!(isAdmin || (isReviewer && isTeamMember))) {
                System.err.println("用户无权访问团队申请: " + currentUser.getId() + ", 团队ID: " + teamId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("message", "没有权限访问此团队的申请"));
            }
            */
            
            List<TeamApplication> applications;
            
            // 如果指定了状态，则根据状态筛选
            if (status != null && !status.isEmpty()) {
                if ("PROCESSED".equals(status.toUpperCase())) {
                    // 获取所有非PENDING状态的申请
                    applications = teamApplicationService.getTeamApplications(teamId).stream()
                        .filter(app -> app.getStatus() != TeamApplication.Status.PENDING)
                        .collect(Collectors.toList());
                } else {
                    try {
                        TeamApplication.Status statusEnum = TeamApplication.Status.valueOf(status.toUpperCase());
                        applications = teamApplicationService.getTeamApplications(teamId).stream()
                            .filter(app -> app.getStatus() == statusEnum)
                            .collect(Collectors.toList());
                    } catch (IllegalArgumentException e) {
                        return ResponseEntity.badRequest()
                            .body(Map.of("message", "无效的状态值: " + status));
                    }
                }
            } else {
                // 获取所有申请
                applications = teamApplicationService.getTeamApplications(teamId);
            }
            
            // 记录返回的申请数量
            System.out.println("团队 " + teamId + " 的申请数量: " + applications.size() + 
                ", 状态过滤: " + (status == null ? "无" : status));
            
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            System.err.println("处理团队申请列表时出错: " + e.getMessage());
            e.printStackTrace();
            
            Map<String, String> error = new HashMap<>();
            error.put("message", "获取团队申请失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 处理团队申请
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> processApplication(
            @PathVariable("id") Integer id, 
            @RequestBody Map<String, Object> request) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            // 从请求中获取状态，支持多种参数名
            String status = null;
            if (request.containsKey("status")) {
                status = (String) request.get("status");
            } else if (request.containsKey("action")) {
                status = (String) request.get("action");
            }
            
            if (status == null) {
                throw new Exception("缺少状态参数");
            }
            
            System.out.println("处理团队申请: ID=" + id + ", 状态=" + status + ", 处理人ID=" + currentUser.getId());
            
            // 处理申请
            TeamApplication processedApplication = teamApplicationService.processApplication(
                id, 
                status, 
                currentUser.getId()
            );
            
            return ResponseEntity.ok(processedApplication);
        } catch (Exception e) {
            System.err.println("处理团队申请时出错: " + e.getMessage());
            e.printStackTrace();
            
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取所有待处理的申请 (ADMIN/REVIEWER)
     */
    @GetMapping("/pending")
    public ResponseEntity<?> getPendingApplications() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            if (currentUser.getRole() != User.Role.ADMIN && currentUser.getRole() != User.Role.REVIEWER) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of("message", "仅限管理员或审核员访问"));
            }
            
            List<TeamApplication> applications = teamApplicationService.getPendingApplications();
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取所有已处理的申请 (ADMIN/REVIEWER)
     */
    @GetMapping("/processed")
    public ResponseEntity<?> getProcessedApplications(@RequestParam(required = false) String status) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            if (currentUser.getRole() != User.Role.ADMIN && currentUser.getRole() != User.Role.REVIEWER) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of("message", "仅限管理员或审核员访问"));
            }
            
            List<TeamApplication> applications;
            if (status != null && !status.isEmpty()) {
                // 验证状态值
                try {
                    TeamApplication.Status statusEnum = TeamApplication.Status.valueOf(status.toUpperCase());
                    if (statusEnum == TeamApplication.Status.PENDING) {
                        throw new Exception("请使用 /pending 端点获取待处理申请");
                    }
                    applications = teamApplicationService.getApplicationsByStatus(statusEnum);
                } catch (IllegalArgumentException e) {
                    throw new Exception("无效的状态值: " + status);
                }
            } else {
                applications = teamApplicationService.getAllProcessedApplications();
            }
            
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
} 