// 统一的本地存储处理工具
export const storageUtils = {
  // 保存数据到本地存储
  saveToStorage(key, data) {
    try {
      localStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (e) {
      console.error(`保存数据到本地存储失败: ${key}`, e);
      return false;
    }
  },
  
  // 从本地存储获取数据
  getFromStorage(key, defaultValue = null) {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : defaultValue;
    } catch (e) {
      console.error(`从本地存储获取数据失败: ${key}`, e);
      return defaultValue;
    }
  },
  
  // 从本地存储中移除数据
  removeFromStorage(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (e) {
      console.error(`从本地存储中移除数据失败: ${key}`, e);
      return false;
    }
  }
};

// 异步操作处理帮助函数
export const asyncActionHandler = {
  // 使用开始和结束loading的mutation名称
  start(commit, loadingMutation = 'setLoading', errorMutation = 'setError') {
    commit(loadingMutation, true);
    if (errorMutation) commit(errorMutation, null);
  },
  
  // 结束异步操作
  end(commit, loadingMutation = 'setLoading') {
    commit(loadingMutation, false);
  },
  
  // 处理错误
  error(commit, error, errorMutation = 'setError', loadingMutation = 'setLoading') {
    let errorMessage;
    
    // 处理不同格式的错误响应
    if (error.response) {
      // 服务器返回了错误响应
      const responseData = error.response.data;
      
      // 处理不同格式的错误数据
      if (typeof responseData === 'string') {
        // 直接是字符串错误信息
        errorMessage = responseData;
      } else if (responseData && responseData.message) {
        // 包含message字段的对象
        errorMessage = responseData.message;
      } else if (responseData && responseData.error) {
        // 包含error字段的对象
        errorMessage = responseData.error;
      } else {
        // 其他情况，使用HTTP状态文本
        errorMessage = error.response.statusText;
      }
    } else if (error.message) {
      // 请求没有到达服务器，或者其他客户端错误
      errorMessage = error.message;
    } else {
      // 未知错误
      errorMessage = '未知错误';
    }
    
    // 设置错误状态
    commit(errorMutation, errorMessage);
    commit(loadingMutation, false);
    
    console.error('操作失败:', error);
    if (error.response) {
      console.error('错误响应:', error.response.status, error.response.data);
    }
    
    return errorMessage;
  }
}; 