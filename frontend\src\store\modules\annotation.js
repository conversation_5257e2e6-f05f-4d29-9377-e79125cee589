import { storageUtils } from '@/utils/storeHelpers';

const state = {
  step: 0, // 0: 未开始, 1: 图像标注, 2: 病例信息填写
  imageId: null,
  formData: null,
  testMode: false,
  imagePath: null,  // 添加imagePath属性保存图片路径，支持离线模式
  lastUpdated: null
};

const getters = {
  getAnnotationProgress: state => {
    return {
      step: state.step,
      imageId: state.imageId,
      formData: state.formData,
      testMode: state.testMode,
      imagePath: state.imagePath,
      lastUpdated: state.lastUpdated
    };
  },
  hasUnfinishedAnnotation: state => {
    return state.step > 0 && state.imageId !== null;
  },
  getCurrentStep: state => state.step,
  getImageId: state => state.imageId,
  getFormData: state => state.formData,
  isTestMode: state => state.testMode,
  getImagePath: state => state.imagePath, // 获取图片路径的getter
  getStructuredFormProgress: state => {
    return {
      formData: state.formData
    };
  }
};

const actions = {
  saveProgress({ commit }, progress) {
    commit('SET_PROGRESS', progress);
  },
  completeAnnotation({ commit }) {
    commit('COMPLETE_ANNOTATION');
  },
  updateFormData({ commit }, formData) {
    commit('UPDATE_FORM_DATA', formData);
  },
  // 从index.js添加的兼容方法
  saveAnnotationProgress({ commit }, { step, imageId, formData }) {
    commit('SET_PROGRESS', { step, imageId, formData });
  },
  clearAnnotationProgress({ commit }) {
    commit('COMPLETE_ANNOTATION');
  }
};

const mutations = {
  SET_PROGRESS(state, progress) {
    state.step = progress.step || state.step;
    state.imageId = progress.imageId || state.imageId;
    
    // 保存表单数据
    if (progress.formData) {
      // 如果传入的是完整的formData对象，进行简化处理
      // 这部分逻辑从index.js中的saveAnnotationProgress迁移而来
      let simplifiedFormData = null;
      if (progress.formData) {
        // 过滤掉大型对象属性或代理对象，只保留简单数据
        simplifiedFormData = {};
        Object.keys(progress.formData).forEach(key => {
          // 检查是否为代理对象或复杂对象
          const value = progress.formData[key];
          if (typeof value !== 'object' || value === null) {
            simplifiedFormData[key] = value;
          } else if (Array.isArray(value)) {
            // 对于数组，保留其字符串值
            simplifiedFormData[key] = value.map(item => 
              typeof item === 'string' ? item : JSON.stringify(item)
            );
          } else {
            // 对于对象，转换为字符串
            try {
              simplifiedFormData[key] = JSON.stringify(value);
            } catch(e) {
              console.warn('无法序列化表单数据字段:', key);
            }
          }
        });
      }
      
      state.formData = simplifiedFormData || progress.formData;
    }
    
    // 标记测试模式
    if (typeof progress.testMode !== 'undefined') {
      state.testMode = progress.testMode;
    }
    
    // 保存图片路径
    if (progress.imagePath) {
      state.imagePath = progress.imagePath;
    }
    
    // 离线模式也同步保存到localStorage
    if ((progress.testMode || localStorage.getItem('offlineMode') === 'true') && progress.imageId) {
      try {
        storageUtils.saveToStorage(`offline_progress_${progress.imageId}`, {
          step: state.step,
          formData: state.formData,
          testMode: state.testMode,
          imagePath: state.imagePath
        });
      } catch (e) {
        console.error('保存离线进度失败', e);
      }
    }
    
    state.lastUpdated = new Date().toISOString();
    console.log('保存标注进度:', state);
  },
  
  UPDATE_FORM_DATA(state, formData) {
    state.formData = formData;
    state.lastUpdated = new Date().toISOString();
    
    // 离线模式也同步保存表单数据
    if ((state.testMode || localStorage.getItem('offlineMode') === 'true') && state.imageId) {
      try {
        storageUtils.saveToStorage(`offline_form_${state.imageId}`, formData);
        
        // 同步更新进度
        storageUtils.saveToStorage(`offline_progress_${state.imageId}`, {
          step: state.step,
          formData: formData,
          testMode: state.testMode,
          imagePath: state.imagePath
        });
      } catch (e) {
        console.error('保存离线表单数据失败', e);
      }
    }
  },
  
  COMPLETE_ANNOTATION(state) {
    // 清除本地存储中的进度
    if (state.imageId) {
      try {
        storageUtils.removeFromStorage(`offline_progress_${state.imageId}`);
      } catch (e) {
        console.error('清除离线进度失败', e);
      }
    }
    
    // 重置状态
    state.step = 0;
    state.imageId = null;
    state.formData = null;
    state.testMode = false;
    state.imagePath = null;
    state.lastUpdated = new Date().toISOString();
    console.log('清除标注进度');
  }
};

export default {
  state,
  getters,
  actions,
  mutations
}; 