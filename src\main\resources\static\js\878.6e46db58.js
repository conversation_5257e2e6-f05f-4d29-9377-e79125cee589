"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[878],{11878:(e,t,a)=>{a.r(t),a.d(t,{default:()=>C});var o=a(20641),n={class:"structured-form-container"},i={class:"page-header"},r={class:"header-actions"},l={key:0,class:"text-center my-5"},s={key:1,class:"form-content"},u={class:"form-section"},c={class:"form-grid"},m={class:"form-section"},d={class:"form-grid"},f={class:"form-section"},g={class:"form-grid"},p={class:"form-section"},h={class:"form-grid"},b={class:"form-actions"};function v(e,t,a,v,_,D){var k=(0,o.g2)("el-button"),F=(0,o.g2)("el-cascader"),y=(0,o.g2)("el-form-item"),V=(0,o.g2)("el-input-number"),S=(0,o.g2)("el-radio"),I=(0,o.g2)("el-radio-group"),A=(0,o.g2)("el-checkbox"),w=(0,o.g2)("el-checkbox-group"),C=(0,o.g2)("el-input"),W=(0,o.g2)("el-option"),P=(0,o.g2)("el-select"),U=(0,o.g2)("el-rate"),x=(0,o.g2)("el-form"),$=(0,o.g2)("ConfirmDialog");return(0,o.uX)(),(0,o.CE)(o.FK,null,[(0,o.Lk)("div",n,[(0,o.Lk)("div",i,[t[25]||(t[25]=(0,o.Lk)("div",{class:"header-content"},[(0,o.Lk)("h2",null,"病例信息填写")],-1)),(0,o.Lk)("div",r,[(0,o.bF)(k,{type:"success",onClick:D.submitForm},{default:(0,o.k6)((function(){return t[22]||(t[22]=[(0,o.eW)("提交")])})),_:1,__:[22]},8,["onClick"]),(0,o.bF)(k,{type:"primary",onClick:D.saveAndExit},{default:(0,o.k6)((function(){return t[23]||(t[23]=[(0,o.eW)("保存并退出")])})),_:1,__:[23]},8,["onClick"]),(0,o.bF)(k,{onClick:D.returnToAnnotation},{default:(0,o.k6)((function(){return t[24]||(t[24]=[(0,o.eW)("返回上一步")])})),_:1,__:[24]},8,["onClick"])])]),_.loading?((0,o.uX)(),(0,o.CE)("div",l,t[26]||(t[26]=[(0,o.Lk)("div",{class:"spinner-border",role:"status"},[(0,o.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):((0,o.uX)(),(0,o.CE)("div",s,[(0,o.bF)(x,{ref:"caseForm",model:_.formData,"label-position":"top",rules:_.rules},{default:(0,o.k6)((function(){return[(0,o.Lk)("div",u,[t[30]||(t[30]=(0,o.Lk)("h3",{class:"section-title"},"基础信息",-1)),(0,o.Lk)("div",c,[(0,o.bF)(y,{label:"病变部位",prop:"location"},{default:(0,o.k6)((function(){return[(0,o.bF)(F,{modelValue:_.formData.location,"onUpdate:modelValue":t[0]||(t[0]=function(e){return _.formData.location=e}),ref:"locationCascader",options:_.locationOptions,props:{expandTrigger:"hover"},placeholder:"请选择病变部位"},null,8,["modelValue","options"])]})),_:1}),(0,o.bF)(y,{label:"患者年龄",prop:"patientAge"},{default:(0,o.k6)((function(){return[(0,o.bF)(V,{modelValue:_.formData.patientAge,"onUpdate:modelValue":t[1]||(t[1]=function(e){return _.formData.patientAge=e}),min:0,max:100},null,8,["modelValue"])]})),_:1}),(0,o.bF)(y,{label:"病程阶段",prop:"stage"},{default:(0,o.k6)((function(){return[(0,o.bF)(I,{modelValue:_.formData.stage,"onUpdate:modelValue":t[2]||(t[2]=function(e){return _.formData.stage=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(S,{value:"初期",label:"初期（<6个月）"},{default:(0,o.k6)((function(){return t[27]||(t[27]=[(0,o.eW)("初期（<6个月）")])})),_:1,__:[27]}),(0,o.bF)(S,{value:"进展期",label:"进展期"},{default:(0,o.k6)((function(){return t[28]||(t[28]=[(0,o.eW)("进展期")])})),_:1,__:[28]}),(0,o.bF)(S,{value:"稳定期",label:"稳定期"},{default:(0,o.k6)((function(){return t[29]||(t[29]=[(0,o.eW)("稳定期")])})),_:1,__:[29]})]})),_:1},8,["modelValue"])]})),_:1})])]),(0,o.Lk)("div",m,[t[43]||(t[43]=(0,o.Lk)("h3",{class:"section-title"},"形态与临床特征",-1)),(0,o.Lk)("div",d,[(0,o.bF)(y,{label:"形态特征",prop:"morphology"},{default:(0,o.k6)((function(){return[(0,o.bF)(w,{modelValue:_.formData.morphology,"onUpdate:modelValue":t[3]||(t[3]=function(e){return _.formData.morphology=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(A,{value:"结节状",label:"结节状"},{default:(0,o.k6)((function(){return t[31]||(t[31]=[(0,o.eW)("结节状")])})),_:1,__:[31]}),(0,o.bF)(A,{value:"团块状",label:"团块状"},{default:(0,o.k6)((function(){return t[32]||(t[32]=[(0,o.eW)("团块状")])})),_:1,__:[32]}),(0,o.bF)(A,{value:"弥漫性",label:"弥漫性"},{default:(0,o.k6)((function(){return t[33]||(t[33]=[(0,o.eW)("弥漫性")])})),_:1,__:[33]}),(0,o.bF)(A,{value:"溃疡形成",label:"溃疡形成"},{default:(0,o.k6)((function(){return t[34]||(t[34]=[(0,o.eW)("溃疡形成")])})),_:1,__:[34]})]})),_:1},8,["modelValue"]),(0,o.bF)(C,{modelValue:_.formData.morphologyDesc,"onUpdate:modelValue":t[4]||(t[4]=function(e){return _.formData.morphologyDesc=e}),type:"textarea",placeholder:"补充形态描述",rows:2,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1}),(0,o.bF)(y,{label:"血流信号",prop:"bloodFlow"},{default:(0,o.k6)((function(){return[(0,o.bF)(P,{modelValue:_.formData.bloodFlow,"onUpdate:modelValue":t[5]||(t[5]=function(e){return _.formData.bloodFlow=e}),placeholder:"请选择血流信号强度"},{default:(0,o.k6)((function(){return[(0,o.bF)(W,{label:"丰富",value:"丰富"}),(0,o.bF)(W,{label:"中等",value:"中等"}),(0,o.bF)(W,{label:"稀疏",value:"稀疏"}),(0,o.bF)(W,{label:"无",value:"无"})]})),_:1},8,["modelValue"])]})),_:1}),(0,o.bF)(y,{label:"症状表现",prop:"symptoms"},{default:(0,o.k6)((function(){return[(0,o.bF)(w,{modelValue:_.formData.symptoms,"onUpdate:modelValue":t[6]||(t[6]=function(e){return _.formData.symptoms=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(A,{value:"疼痛",label:"疼痛"},{default:(0,o.k6)((function(){return t[35]||(t[35]=[(0,o.eW)("疼痛")])})),_:1,__:[35]}),(0,o.bF)(A,{value:"瘙痒",label:"瘙痒"},{default:(0,o.k6)((function(){return t[36]||(t[36]=[(0,o.eW)("瘙痒")])})),_:1,__:[36]}),(0,o.bF)(A,{value:"出血",label:"出血"},{default:(0,o.k6)((function(){return t[37]||(t[37]=[(0,o.eW)("出血")])})),_:1,__:[37]}),(0,o.bF)(A,{value:"功能障碍",label:"功能障碍"},{default:(0,o.k6)((function(){return t[38]||(t[38]=[(0,o.eW)("功能障碍")])})),_:1,__:[38]})]})),_:1},8,["modelValue"]),(0,o.bF)(C,{modelValue:_.formData.symptomsDesc,"onUpdate:modelValue":t[7]||(t[7]=function(e){return _.formData.symptomsDesc=e}),type:"textarea",placeholder:"症状详细描述",rows:2,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1}),(0,o.bF)(y,{label:"并发症",prop:"complications"},{default:(0,o.k6)((function(){return[(0,o.bF)(w,{modelValue:_.formData.complications,"onUpdate:modelValue":t[8]||(t[8]=function(e){return _.formData.complications=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(A,{value:"感染",label:"感染"},{default:(0,o.k6)((function(){return t[39]||(t[39]=[(0,o.eW)("感染")])})),_:1,__:[39]}),(0,o.bF)(A,{value:"血栓",label:"血栓"},{default:(0,o.k6)((function(){return t[40]||(t[40]=[(0,o.eW)("血栓")])})),_:1,__:[40]}),(0,o.bF)(A,{value:"压迫邻近器官",label:"压迫邻近器官"},{default:(0,o.k6)((function(){return t[41]||(t[41]=[(0,o.eW)("压迫邻近器官")])})),_:1,__:[41]}),(0,o.bF)(A,{value:"其他",label:"其他"},{default:(0,o.k6)((function(){return t[42]||(t[42]=[(0,o.eW)("其他")])})),_:1,__:[42]})]})),_:1},8,["modelValue"]),(0,o.bF)(C,{modelValue:_.formData.complicationsDesc,"onUpdate:modelValue":t[9]||(t[9]=function(e){return _.formData.complicationsDesc=e}),type:"textarea",placeholder:"并发症详细描述",rows:2,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1})])]),(0,o.Lk)("div",f,[t[51]||(t[51]=(0,o.Lk)("h3",{class:"section-title"},"诊断与治疗建议",-1)),(0,o.Lk)("div",g,[(0,o.bF)(y,{label:"诊断结论",prop:"diagnosis"},{default:(0,o.k6)((function(){return[(0,o.bF)(C,{modelValue:_.formData.diagnosis,"onUpdate:modelValue":t[10]||(t[10]=function(e){return _.formData.diagnosis=e}),placeholder:"请输入诊断结论"},null,8,["modelValue"]),(0,o.bF)(C,{modelValue:_.formData.diagnosisCode,"onUpdate:modelValue":t[11]||(t[11]=function(e){return _.formData.diagnosisCode=e}),placeholder:"ICD-11编码",style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1}),(0,o.bF)(y,{label:"治疗优先级",prop:"treatmentPriority"},{default:(0,o.k6)((function(){return[(0,o.bF)(I,{modelValue:_.formData.treatmentPriority,"onUpdate:modelValue":t[12]||(t[12]=function(e){return _.formData.treatmentPriority=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(S,{value:"紧急",label:"紧急（24h内处理）"},{default:(0,o.k6)((function(){return t[44]||(t[44]=[(0,o.eW)("紧急（24h内处理）")])})),_:1,__:[44]}),(0,o.bF)(S,{value:"常规",label:"常规（1-2周）"},{default:(0,o.k6)((function(){return t[45]||(t[45]=[(0,o.eW)("常规（1-2周）")])})),_:1,__:[45]}),(0,o.bF)(S,{value:"观察",label:"观察"},{default:(0,o.k6)((function(){return t[46]||(t[46]=[(0,o.eW)("观察")])})),_:1,__:[46]})]})),_:1},8,["modelValue"])]})),_:1}),(0,o.bF)(y,{label:"推荐方案",prop:"treatmentPlan"},{default:(0,o.k6)((function(){return[(0,o.bF)(w,{modelValue:_.formData.treatmentPlan,"onUpdate:modelValue":t[13]||(t[13]=function(e){return _.formData.treatmentPlan=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(A,{value:"手术切除",label:"手术切除"},{default:(0,o.k6)((function(){return t[47]||(t[47]=[(0,o.eW)("手术切除")])})),_:1,__:[47]}),(0,o.bF)(A,{value:"激光治疗",label:"激光治疗"},{default:(0,o.k6)((function(){return t[48]||(t[48]=[(0,o.eW)("激光治疗")])})),_:1,__:[48]}),(0,o.bF)(A,{value:"硬化剂注射",label:"硬化剂注射"},{default:(0,o.k6)((function(){return t[49]||(t[49]=[(0,o.eW)("硬化剂注射")])})),_:1,__:[49]}),(0,o.bF)(A,{value:"药物治疗",label:"药物治疗"},{default:(0,o.k6)((function(){return t[50]||(t[50]=[(0,o.eW)("药物治疗")])})),_:1,__:[50]})]})),_:1},8,["modelValue"]),(0,o.bF)(C,{modelValue:_.formData.treatmentDetail,"onUpdate:modelValue":t[14]||(t[14]=function(e){return _.formData.treatmentDetail=e}),type:"textarea",placeholder:"请输入详细治疗方案，包括剂量、注意事项等",rows:3,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1}),(0,o.bF)(y,{label:"禁忌症提示",prop:"contraindications"},{default:(0,o.k6)((function(){return[(0,o.bF)(C,{modelValue:_.formData.contraindications,"onUpdate:modelValue":t[15]||(t[15]=function(e){return _.formData.contraindications=e}),type:"textarea",placeholder:"请输入禁忌症提示",rows:3},null,8,["modelValue"])]})),_:1})])]),(0,o.Lk)("div",p,[t[61]||(t[61]=(0,o.Lk)("h3",{class:"section-title"},"随访与预后",-1)),(0,o.Lk)("div",h,[(0,o.bF)(y,{label:"复查周期",prop:"followUpPeriod"},{default:(0,o.k6)((function(){return[(0,o.bF)(I,{modelValue:_.formData.followUpPeriod,"onUpdate:modelValue":t[16]||(t[16]=function(e){return _.formData.followUpPeriod=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(S,{value:"1个月",label:"1个月"},{default:(0,o.k6)((function(){return t[52]||(t[52]=[(0,o.eW)("1个月")])})),_:1,__:[52]}),(0,o.bF)(S,{value:"3个月",label:"3个月"},{default:(0,o.k6)((function(){return t[53]||(t[53]=[(0,o.eW)("3个月")])})),_:1,__:[53]}),(0,o.bF)(S,{value:"6个月",label:"6个月"},{default:(0,o.k6)((function(){return t[54]||(t[54]=[(0,o.eW)("6个月")])})),_:1,__:[54]}),(0,o.bF)(S,{value:"custom",label:"自定义"},{default:(0,o.k6)((function(){return t[55]||(t[55]=[(0,o.eW)("自定义")])})),_:1,__:[55]})]})),_:1},8,["modelValue"]),"custom"===_.formData.followUpPeriod?((0,o.uX)(),(0,o.Wv)(C,{key:0,modelValue:_.formData.customFollowUp,"onUpdate:modelValue":t[17]||(t[17]=function(e){return _.formData.customFollowUp=e}),placeholder:"请输入自定义时间",style:{"margin-top":"10px",width:"200px"}},null,8,["modelValue"])):(0,o.Q3)("",!0)]})),_:1}),(0,o.bF)(y,{label:"预后评估",prop:"prognosisRating"},{default:(0,o.k6)((function(){return[(0,o.bF)(U,{modelValue:_.formData.prognosisRating,"onUpdate:modelValue":t[18]||(t[18]=function(e){return _.formData.prognosisRating=e}),texts:["易复发","有复发风险","一般","较好","完全根治"],"show-text":""},null,8,["modelValue"])]})),_:1}),(0,o.bF)(y,{label:"患者教育重点",prop:"patientEducation"},{default:(0,o.k6)((function(){return[(0,o.bF)(w,{modelValue:_.formData.patientEducation,"onUpdate:modelValue":t[19]||(t[19]=function(e){return _.formData.patientEducation=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(A,{value:"避免日晒",label:"避免日晒"},{default:(0,o.k6)((function(){return t[56]||(t[56]=[(0,o.eW)("避免日晒")])})),_:1,__:[56]}),(0,o.bF)(A,{value:"禁止抓挠",label:"禁止抓挠"},{default:(0,o.k6)((function(){return t[57]||(t[57]=[(0,o.eW)("禁止抓挠")])})),_:1,__:[57]}),(0,o.bF)(A,{value:"定期服药提醒",label:"定期服药提醒"},{default:(0,o.k6)((function(){return t[58]||(t[58]=[(0,o.eW)("定期服药提醒")])})),_:1,__:[58]}),(0,o.bF)(A,{value:"防护建议",label:"防护建议"},{default:(0,o.k6)((function(){return t[59]||(t[59]=[(0,o.eW)("防护建议")])})),_:1,__:[59]}),(0,o.bF)(A,{value:"生活方式调整",label:"生活方式调整"},{default:(0,o.k6)((function(){return t[60]||(t[60]=[(0,o.eW)("生活方式调整")])})),_:1,__:[60]})]})),_:1},8,["modelValue"]),(0,o.bF)(C,{modelValue:_.formData.patientEducationDetail,"onUpdate:modelValue":t[20]||(t[20]=function(e){return _.formData.patientEducationDetail=e}),type:"textarea",placeholder:"详细教育指导内容",rows:3,style:{"margin-top":"10px"}},null,8,["modelValue"])]})),_:1})])]),(0,o.Lk)("div",b,[(0,o.bF)(k,{type:"success",onClick:D.submitForm},{default:(0,o.k6)((function(){return t[62]||(t[62]=[(0,o.eW)("提交")])})),_:1,__:[62]},8,["onClick"])])]})),_:1},8,["model","rules"])]))]),(0,o.bF)($,{modelValue:_.saveDialogVisible,"onUpdate:modelValue":t[21]||(t[21]=function(e){return _.saveDialogVisible=e}),title:"保存并退出",message:"是否保留当前填写进度？下次可以继续完成","confirm-text":"保留进度","cancel-text":"不保留",icon:"warning",onConfirm:D.handleSaveProgress,onCancel:D.handleDiscardProgress},null,8,["modelValue","onConfirm","onCancel"])],64)}var _=a(54119),D=a(41034),k=(a(28706),a(2008),a(51629),a(74423),a(25276),a(64346),a(62062),a(44114),a(1688),a(60739),a(23288),a(18111),a(22489),a(7588),a(61701),a(33110),a(79432),a(26099),a(58940),a(27495),a(99449),a(21699),a(25440),a(11392),a(42762),a(23500),a(76031),a(40834)),F=a(653),y=a(60424),V=a(49586),S=a(37620);const I={name:"CaseStructuredForm",components:{ConfirmDialog:y.A},data:function(){return{loading:!1,annotationData:null,imageId:null,saveDialogVisible:!1,formData:{location:[],patientAge:null,stage:"",morphology:[],morphologyDesc:"",bloodFlow:"",symptoms:[],symptomsDesc:"",complications:[],complicationsDesc:"",diagnosis:"",diagnosisCode:"",treatmentPriority:"",treatmentPlan:[],treatmentDetail:"",contraindications:"",followUpPeriod:"",customFollowUp:"",prognosisRating:3,patientEducation:[],patientEducationDetail:""},rules:{location:[{required:!0,message:"请选择病变部位",trigger:"change"}],patientAge:[{required:!0,message:"请输入患者年龄",trigger:"blur"}],stage:[{required:!0,message:"请选择病程阶段",trigger:"change"}],bloodFlow:[{required:!0,message:"请选择血流信号",trigger:"change"}],diagnosis:[{required:!0,message:"请输入诊断结论",trigger:"blur"}],treatmentPriority:[{required:!0,message:"请选择治疗优先级",trigger:"change"}],followUpPeriod:[{required:!0,message:"请选择复查周期",trigger:"change"}]},locationOptions:[{value:"皮肤",label:"皮肤",children:[{value:"面部",label:"面部",children:[{value:"额头",label:"额头"},{value:"眼睑",label:"眼睑"},{value:"鼻翼",label:"鼻翼"},{value:"唇部",label:"唇部"},{value:"耳朵",label:"耳朵"},{value:"其他",label:"其他"}]},{value:"颈部",label:"颈部"},{value:"躯干",label:"躯干"},{value:"四肢",label:"四肢",children:[{value:"手臂",label:"手臂"},{value:"腿部",label:"腿部"},{value:"足部",label:"足部"}]}]},{value:"内脏",label:"内脏",children:[{value:"肝脏",label:"肝脏",children:[{value:"左叶",label:"左叶"},{value:"右叶",label:"右叶"}]},{value:"脾脏",label:"脾脏"},{value:"肺部",label:"肺部"},{value:"肠道",label:"肠道"}]}]}},computed:(0,D.A)({},(0,k.L8)(["getAnnotationProgress"])),created:function(){this.loading=!0;var e=localStorage.getItem("annotations");e&&(this.annotationData=JSON.parse(e),this.prefilFormFromAnnotations());var t=localStorage.getItem("skipAuthCheck");t&&localStorage.removeItem("skipAuthCheck");var a=this.$route.query.imageId;a?(this.imageId=parseInt(a),this.fetchImageData(),sessionStorage.setItem("isNavigatingAfterSave","true")):this.getAnnotationProgress.imageId&&(this.imageId=this.getAnnotationProgress.imageId,this.fetchImageData(),this.getAnnotationProgress.formData&&(this.formData=(0,D.A)((0,D.A)({},this.formData),this.getAnnotationProgress.formData))),this.saveProgress({step:2,imageId:this.imageId,formData:this.formData}),this.checkUserAuthentication(),this.ensureCascaderDataValid()},methods:(0,D.A)((0,D.A)({},(0,k.i0)(["saveProgress","completeAnnotation"])),{},{formatDate:V.Yq,getImageUrl:function(e){if(e&&e.startsWith("blob:"))return e;var t=this.imageId;if(t){var a=localStorage.getItem("offline_image_".concat(t));if(a)return a}return(0,S.VG)(e)},fetchImageData:function(){var e=this;if(this.imageId){var t="true"===this.$route.query.testMode,a="true"===localStorage.getItem("offlineMode");if(t||a){var o=localStorage.getItem("offline_image_".concat(this.imageId));this.imageData={id:parseInt(this.imageId),original_name:"离线图片_".concat(this.imageId,".jpg"),created_at:(new Date).toISOString(),path:o||""};var n=this.getAnnotationProgress;return n&&n.formData&&(this.formData=(0,D.A)((0,D.A)({},this.formData),n.formData),this.$message.info("已恢复之前填写的表单数据")),void(this.loaded=!0)}this.loading=!0,F["default"].images.getOne(this.imageId).then((function(t){e.imageData=t.data,e.loading=!1,e.loaded=!0,e.mapImageDataToForm(e.imageData)}))["catch"]((function(t){if("true"===localStorage.getItem("offlineMode")){var a=localStorage.getItem("offline_image_".concat(e.imageId));a?(e.imageData={id:parseInt(e.imageId),original_name:"离线图片_".concat(e.imageId,".jpg"),created_at:(new Date).toISOString(),path:a},e.$message.warning("API请求失败，使用离线模式显示表单"),e.loaded=!0):(e.error="无法获取图像数据",e.$message.error("加载失败，无法获取图像数据"))}else{var o;e.error=(null===(o=t.response)||void 0===o?void 0:o.data)||t.message,e.$message.error("加载失败: "+e.error)}e.loading=!1}))}else this.$message.error("未指定图像ID，无法加载数据")},mergeFormData:function(e){var t=(0,D.A)({},this.formData);for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&("object"===(0,_.A)(e[a])&&null!==e[a]&&"object"===(0,_.A)(t[a])&&null!==t[a]?t[a]=this.mergeFormData(e[a]):t[a]=e[a]);return t},handleSubmit:function(){var e=this;this.$refs.caseForm.validate((function(t){if(t){e.saving=!0;var a="true"===localStorage.getItem("offlineMode");if(a){try{localStorage.setItem("offline_form_".concat(e.imageId),JSON.stringify(e.formData)),e.completeAnnotation(),e.$message.success("表单已保存（离线模式）"),setTimeout((function(){e.$router.push("/app/dashboard")}),1e3)}catch(i){e.$message.error("保存失败: "+i.message)}e.saving=!1}else{var o=e.$loading({lock:!0,text:"提交数据中...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.7)"}),n=(0,D.A)((0,D.A)({},e.formData),{},{action:"submit"});F["default"].images.saveStructuredFormData(e.imageId,n).then((function(t){o.close(),e.$message.success("表单提交成功"),e.completeAnnotation(),setTimeout((function(){e.$router.push("/app/dashboard")}),1e3)}))["catch"]((function(t){var n;o.close();var i=(null===(n=t.response)||void 0===n?void 0:n.data)||t.message||"未知错误";e.$message.error("提交失败: "+i),a||e.$confirm("提交到服务器失败，是否启用离线模式?","提交失败",{confirmButtonText:"启用离线模式",cancelButtonText:"再试一次",type:"warning"}).then((function(){localStorage.setItem("offlineMode","true"),e.handleSubmit()}))["catch"]((function(){e.$message.info("请稍后重试")}))}))["finally"]((function(){e.saving=!1}))}}else e.$message.warning("表单验证失败，请检查必填项")}))},prefilFormFromAnnotations:function(){},saveFormProgress:function(){this.saveProgress({step:2,imageId:this.imageId,formData:this.formData})},saveAndExit:function(){this.saveFormProgress(),this.saveDialogVisible=!0},handleSaveProgress:function(){var e=this;sessionStorage.setItem("isNavigatingAfterSave","true"),sessionStorage.setItem("allowFormOperation","true");var t=this.$loading({lock:!0,text:"保存数据中...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.7)"});try{var a=(0,D.A)((0,D.A)({},this.formData),{},{action:"save"});F["default"].images.saveStructuredFormData(this.imageId,a).then((function(t){e.$message.success("表单数据已保存到数据库"),sessionStorage.setItem("navigatingFromForm","true");try{e.$router.push("/app/dashboard")}catch(a){window.location.href="/app/dashboard"}}))["catch"]((function(t){var a;e.$message.error("保存失败: "+((null===(a=t.response)||void 0===a?void 0:a.data)||t.message||"未知错误"));try{e.$router.push("/app/dashboard")}catch(o){window.location.href="/app/dashboard"}}))["finally"]((function(){t.close()}))}catch(o){this.$message.error("保存失败: "+o.message),t.close(),window.location.href="/app/dashboard"}},handleDiscardProgress:function(){this.completeAnnotation(),this.$message.info("已清除填写进度"),this.$router.push("/cases/new")},returnToAnnotation:function(){this.saveFormProgress(),this.$router.push({path:"/cases/form",query:{imageId:this.imageId}})},checkUserAuthentication:function(){var e=JSON.parse(localStorage.getItem("user"));if(!e){var t=sessionStorage.getItem("preservedUser");t&&(localStorage.setItem("user",t),sessionStorage.removeItem("preservedUser"))}},mapImageDataToForm:function(e){if(e){var t=(0,D.A)({},e),a={lesion_location:"lesionLocation",patient_age:"patientAge",disease_stage:"diseaseStage",morphological_features:"morphologicalFeatures",blood_flow:"bloodFlow",symptom_details:"symptomDetails",complication_details:"complicationDetails",diagnosis_category:"diagnosisCategory",diagnosis_icd_code:"diagnosisIcdCode",treatment_priority:"treatmentPriority",treatment_plan:"treatmentPlan",recommended_treatment:"recommendedTreatment",follow_up_schedule:"followUpSchedule",prognosis_rating:"prognosisRating",patient_education:"patientEducation"};if(Object.keys(a).forEach((function(e){void 0!==t[e]&&void 0===t[a[e]]&&(t[a[e]]=t[e])})),t.lesionLocation)if("string"===typeof t.lesionLocation){var o=t.lesionLocation.split("/").filter((function(e){return e.trim()}));o.length>0&&(this.formData.location=o)}else Array.isArray(t.lesionLocation)&&(this.formData.location=t.lesionLocation);if((!this.formData.location||0===this.formData.location.length)&&e.lesion_location&&"string"===typeof e.lesion_location){var n=e.lesion_location.split("/").filter((function(e){return e.trim()}));n.length>0&&(this.formData.location=n)}if(null!==t.patientAge&&void 0!==t.patientAge&&(this.formData.patientAge=parseInt(t.patientAge)),t.diseaseStage&&(this.formData.stage=t.diseaseStage),t.morphologicalFeatures){var i=t.morphologicalFeatures||"",r=i.indexOf(":");if(r>-1){var l=i.substring(0,r).split("/");this.formData.morphology=l,this.formData.morphologyDesc=i.substring(r+1).trim()}else this.formData.morphology=i.split("/").filter((function(e){return""!==e.trim()}))}if(t.bloodFlow&&(this.formData.bloodFlow=t.bloodFlow),t.symptoms){var s=t.symptoms||"",u=s.indexOf(":");if(u>-1){var c=s.substring(0,u).split("/");this.formData.symptoms=c,this.formData.symptomsDesc=s.substring(u+1).trim()}else this.formData.symptoms=s.split("/").filter((function(e){return""!==e.trim()}))}if(t.symptomDetails&&(this.formData.symptomsDesc=t.symptomDetails),t.complications){var m=t.complications||"";this.formData.complications=m.split("/").filter((function(e){return""!==e.trim()}))}if(t.complicationDetails&&(this.formData.complicationsDesc=t.complicationDetails),t.diagnosisCategory&&(this.formData.diagnosis=t.diagnosisCategory),t.diagnosisIcdCode&&(this.formData.diagnosisCode=t.diagnosisIcdCode),t.treatmentPriority&&(this.formData.treatmentPriority=t.treatmentPriority),t.treatmentPlan){var d=t.treatmentPlan||"";this.formData.treatmentPlan=d.split("/").filter((function(e){return""!==e.trim()}))}if(t.recommendedTreatment&&(this.formData.treatmentDetail=t.recommendedTreatment),t.contraindications&&(this.formData.contraindications=t.contraindications),t.followUpSchedule){var f=t.followUpSchedule,g=["1个月","3个月","6个月"];g.includes(f)?this.formData.followUpPeriod=f:(this.formData.followUpPeriod="custom",this.formData.customFollowUp=f)}if(null!==t.prognosisRating&&void 0!==t.prognosisRating&&(this.formData.prognosisRating=parseInt(t.prognosisRating)),t.patientEducation){var p=t.patientEducation||"",h=p.indexOf(":");if(h>-1){var b=p.substring(0,h).split("/");this.formData.patientEducation=b,this.formData.patientEducationDetail=p.substring(h+1).trim()}else this.formData.patientEducation=p.split("/").filter((function(e){return""!==e.trim()}))}this.convertStringArrays(),this.$message.success("已从数据库加载保存的表单数据")}},convertStringArrays:function(){var e=this,t=["morphology","symptoms","complications","treatmentPlan","patientEducation","location"];if(t.forEach((function(t){var a=e.formData[t];if("string"===typeof a&&a.startsWith("[")&&a.endsWith("]"))try{var o=JSON.parse(a);Array.isArray(o)&&(e.formData[t]=o)}catch(n){}})),this.formData.location&&"string"===typeof this.formData.location&&(this.formData.location.includes('"')||this.formData.location.includes(",")))try{if(this.formData.location.startsWith("[")&&this.formData.location.endsWith("]")){var a=JSON.parse(this.formData.location);Array.isArray(a)&&(this.formData.location=a)}else{var o=this.formData.location.split(",").map((function(e){return e.trim().replace(/"/g,"")}));o.length>0&&(this.formData.location=o)}}catch(n){}},ensureCascaderDataValid:function(){var e=this;setTimeout((function(){if(e.formData.location&&e.formData.location.length>0){var t=e.$refs.locationCascader;!t||t.modelValue&&0!==t.modelValue.length||e.$nextTick((function(){var t=JSON.parse(JSON.stringify(e.formData.location));e.formData.location=[],e.$nextTick((function(){e.formData.location=t}))}))}}),500)},submitForm:function(){var e=this;this.$refs.caseForm.validate((function(t){if(!t)return e.$message.error("表单验证失败，请检查填写的信息"),!1;e.$confirm("确认提交此病例信息?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"}).then((function(){e.handleSubmit()}))["catch"]((function(){}))}))}})};var A=a(66262);const w=(0,A.A)(I,[["render",v],["__scopeId","data-v-66151808"]]),C=w},37620:(e,t,a)=>{a.d(t,{VG:()=>n});a(54119),a(28706),a(74423),a(8921),a(15086),a(26099),a(27495),a(99449),a(21699),a(71761),a(90744),a(11392);var o="http://localhost:8085";function n(e){if(!e)return"";var t=String(e);if(t.startsWith("http://")||t.startsWith("https://"))return t;if(t.startsWith("data:"))return t;if(t.includes("medical_images")){var a=/medical_images[\/\\]([^\/\\]+)(?:[\/\\](.*))?/,n=t.match(a);if(n){var i=n[1],r=n[2]||"",l="/medical/images/".concat(i,"/").concat(r),s=o+l;return s}}if(t.startsWith("/medical/"))return o+t;if(t.match(/^[a-zA-Z]:\\/)){var u=t.split(/[\/\\]/).pop(),c="";t.includes("\\original\\")||t.includes("/original/")?c="original":t.includes("\\processed\\")||t.includes("/processed/")?c="processed":t.includes("\\annotate\\")||t.includes("/annotate/")?c="annotate":(t.includes("\\temp\\")||t.includes("/temp/"))&&(c="temp");var m="/medical/images/".concat(c,"/").concat(u);return o+m}var d=t.startsWith("/")?t:"/"+t;d.startsWith("/medical")||(d="/medical"+d);try{var f=d.lastIndexOf("/");if(-1!==f){var g=d.substring(0,f+1),p=d.substring(f+1);d=g+encodeURIComponent(p)}}catch(h){}return o+d}},49586:(e,t,a)=>{a.d(t,{Yq:()=>o});a(28706),a(23288),a(9868),a(68156);function o(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"";var a="string"===typeof e?new Date(e):e;if(isNaN(a.getTime()))return"";var o=a.getFullYear(),n=String(a.getMonth()+1).padStart(2,"0"),i=String(a.getDate()).padStart(2,"0"),r="".concat(o,"-").concat(n,"-").concat(i);if(t){var l=String(a.getHours()).padStart(2,"0"),s=String(a.getMinutes()).padStart(2,"0"),u=String(a.getSeconds()).padStart(2,"0");return"".concat(r," ").concat(l,":").concat(s,":").concat(u)}return r}},60424:(e,t,a)=>{a.d(t,{A:()=>f});var o=a(20641),n=a(90033),i={class:"dialog-content"},r={key:0,class:"dialog-icon"},l={class:"dialog-message"},s={class:"dialog-footer"};function u(e,t,a,u,c,m){var d=(0,o.g2)("el-button"),f=(0,o.g2)("el-dialog");return(0,o.uX)(),(0,o.Wv)(f,{title:a.title,visible:m.dialogVisible,width:a.width,"before-close":m.handleClose},{footer:(0,o.k6)((function(){return[(0,o.Lk)("span",s,[(0,o.bF)(d,{onClick:m.handleCancel},{default:(0,o.k6)((function(){return[(0,o.eW)((0,n.v_)(a.cancelText),1)]})),_:1},8,["onClick"]),(0,o.bF)(d,{type:a.confirmType,onClick:m.handleConfirm},{default:(0,o.k6)((function(){return[(0,o.eW)((0,n.v_)(a.confirmText),1)]})),_:1},8,["type","onClick"])])]})),default:(0,o.k6)((function(){return[(0,o.Lk)("div",i,[a.icon?((0,o.uX)(),(0,o.CE)("div",r,[(0,o.Lk)("i",{class:(0,n.C4)(m.iconClass)},null,2)])):(0,o.Q3)("",!0),(0,o.Lk)("div",l,[(0,o.RG)(e.$slots,"default",{},(function(){return[(0,o.eW)((0,n.v_)(a.message),1)]}),!0)])])]})),_:3},8,["title","visible","width","before-close"])}const c={name:"ConfirmDialog",props:{title:{type:String,default:"确认"},message:{type:String,default:"确定要执行此操作吗？"},confirmText:{type:String,default:"确定"},cancelText:{type:String,default:"取消"},confirmType:{type:String,default:"primary"},icon:{type:String,default:""},width:{type:String,default:"30%"},value:{type:Boolean,default:!1}},computed:{dialogVisible:{get:function(){return this.value},set:function(e){this.$emit("input",e)}},iconClass:function(){switch(this.icon){case"warning":return"el-icon-warning-outline";case"error":return"el-icon-error";case"success":return"el-icon-success";case"info":return"el-icon-info";default:return this.icon}}},methods:{handleClose:function(e){this.$emit("cancel"),e()},handleCancel:function(){this.dialogVisible=!1,this.$emit("cancel")},handleConfirm:function(){this.dialogVisible=!1,this.$emit("confirm")}}};var m=a(66262);const d=(0,m.A)(c,[["render",u],["__scopeId","data-v-1e9c7025"]]),f=d}}]);