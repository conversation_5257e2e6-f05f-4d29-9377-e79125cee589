<template>
  <div class="register-page register-background">
    <div class="register-container">
      <!-- 右侧注册框 -->
      <div class="register-form-container">
        <div class="register-form-box">
          <div class="register-tabs">
            <div class="tab-item active">用户注册</div>
      </div>
          
          <div class="register-form">
        <div v-if="error" class="alert alert-danger">
          <div class="error-content">
            <div class="error-icon">⚠️</div>
            <div class="error-message">{{ error }}</div>
          </div>
        </div>
        <div v-if="success" class="alert alert-success">
          {{ success }}
        </div>
        
        <form @submit.prevent="handleRegister">
              <div class="form-group">
                <div class="input-with-icon">
                  <i class="icon-user"></i>
            <input 
              id="name" 
              v-model="name" 
              type="text" 
                    placeholder="请输入您的姓名"
              required
              :disabled="loading"
            >
          </div>
              </div>
              
              <div class="form-group">
                <div class="input-with-icon">
                  <i class="icon-email"></i>
            <input 
              id="email" 
              v-model="email" 
              type="email" 
                    placeholder="请输入邮箱地址"
              required
              :disabled="loading"
              @blur="validateEmail"
            >
          </div>
                <div v-if="emailError" class="field-error">{{ emailError }}</div>
              </div>
              
              <div class="form-group">
                <div class="input-with-icon">
                  <i class="icon-lock"></i>
            <input 
              id="password" 
              v-model="password" 
              type="password" 
                    placeholder="请设置密码"
              required
              :disabled="loading"
              @blur="validatePassword"
            >
          </div>
                <div v-if="passwordError" class="field-error">{{ passwordError }}</div>
                <div v-if="passwordStrength && !passwordError" class="password-strength">
                  <div class="strength-label">密码强度：</div>
                  <div class="strength-indicator">
                    <div 
                      class="strength-bar" 
                      :class="{
                        'weak': passwordStrength === 'weak',
                        'medium': passwordStrength === 'medium',
                        'strong': passwordStrength === 'strong'
                      }"
                    ></div>
                  </div>
                  <div class="strength-text" :class="passwordStrength">
                    {{ 
                      passwordStrength === 'weak' ? '弱' : 
                      passwordStrength === 'medium' ? '中' : 
                      passwordStrength === 'strong' ? '强' : ''
                    }}
                  </div>
                </div>
                <div class="password-hint">
                  密码长度至少8位，必须包含大写字母、小写字母、数字和特殊符号中的至少三种
          </div>
              </div>
              
              <div class="form-group">
                <div class="input-with-icon">
                  <i class="icon-lock"></i>
            <input 
              id="confirmPassword" 
              v-model="confirmPassword" 
              type="password" 
                    placeholder="请再次输入密码"
              required
              :disabled="loading"
            >
          </div>
              </div>
              
              <div class="form-group">
                <div class="input-with-icon">
                  <i class="icon-hospital"></i>
            <input 
              id="hospital" 
              v-model="hospital" 
              type="text" 
                    placeholder="请输入所在医院"
              required
              :disabled="loading"
            >
          </div>
              </div>
              
              <div class="form-group">
                <div class="input-with-icon">
                  <i class="icon-department"></i>
            <input 
              id="department" 
              v-model="department" 
              type="text" 
                    placeholder="请输入所在科室"
              :disabled="loading"
            >
          </div>
              </div>
              
              <div class="form-actions">
            <button 
              type="submit" 
                  class="register-btn" 
              :disabled="loading || !!emailError || !!passwordError"
            >
                  <span v-if="loading" class="spinner"></span>
                  <span>{{ loading ? '注册中...' : '立即注册' }}</span>
            </button>
          </div>
        </form>
        
            <div class="login-link">
              <span>已有账号?</span>
              <router-link to="/login" class="login-btn">立即登录</router-link>
            </div>
          </div>
          
          <div class="register-footer">
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Register',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const name = ref('')
    const email = ref('')
    const password = ref('')
    const confirmPassword = ref('')
    const hospital = ref('')
    const department = ref('')
    const loading = ref(false)
    const error = ref('')
    const success = ref('')
    const emailError = ref('')
    const passwordError = ref('')
    const passwordStrength = ref('')
    
    // 邮箱格式验证
    const validateEmail = () => {
      if (!email.value.trim()) {
        emailError.value = '邮箱地址不能为空'
        return false
      }
      
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      if (!emailRegex.test(email.value)) {
        emailError.value = '请输入有效的邮箱地址'
        return false
      }
      
      emailError.value = ''
      return true
    }
    
    // 密码强度验证
    const validatePassword = () => {
      const pwd = password.value
      
      // 检查密码长度
      if (pwd.length < 8) {
        passwordError.value = '密码长度必须不少于8位'
        passwordStrength.value = 'weak'
        return false
      }
      
      // 检查密码复杂度
      const hasUpperCase = /[A-Z]/.test(pwd)
      const hasLowerCase = /[a-z]/.test(pwd)
      const hasNumbers = /[0-9]/.test(pwd)
      const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd)
      
      const typesCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length
      
      // 检查常见简单密码
      const commonPasswords = ['123456', 'password', 'abcdef', '12345678', 'qwerty', '111111']
      if (commonPasswords.includes(pwd.toLowerCase())) {
        passwordError.value = '请勿使用常见的简单密码'
        passwordStrength.value = 'weak'
        return false
      }
      
      if (typesCount < 3) {
        passwordError.value = '密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种'
        passwordStrength.value = 'weak'
        return false
      }
      
      // 密码强度评估
      if (typesCount === 3 && pwd.length < 10) {
        passwordStrength.value = 'medium'
      } else if (typesCount === 4 || (typesCount === 3 && pwd.length >= 10)) {
        passwordStrength.value = 'strong'
      }
      
      passwordError.value = ''
      return true
    }
    
    // 监听邮箱变化，清除错误
    watch(email, () => {
      if (emailError.value) {
        emailError.value = ''
      }
      // 清除与邮箱相关的通用错误提示
      if (error.value && (
        error.value.includes('邮箱已被注册') || 
        error.value.includes('邮箱') || 
        error.value.includes('已存在')
      )) {
        error.value = ''
      }
    })
    
    // 监听密码变化
    watch(password, () => {
      if (password.value) {
        validatePassword()
      } else {
        passwordError.value = ''
        passwordStrength.value = ''
      }
    })
    
    // 监听确认密码变化
    watch(confirmPassword, () => {
      if (confirmPassword.value && password.value !== confirmPassword.value) {
        error.value = '两次输入的密码不一致'
      } else {
        error.value = ''
      }
    })
    
    const handleRegister = async () => {
      // 邮箱验证
      if (!validateEmail()) {
        return
      }
      
      // 密码验证
      if (!validatePassword()) {
        return
      }
      
      // 验证密码一致性
      if (password.value !== confirmPassword.value) {
        error.value = '两次输入的密码不一致'
        return
      }
      
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        const result = await store.dispatch('register', {
          name: name.value,
          email: email.value,
          password: password.value,
          hospital: hospital.value,
          department: department.value,
          role: 'DOCTOR' // 默认注册为医生角色
        })
        
        // 检查返回值是否为错误信息
        if (typeof result === 'string' && (
          result.includes('邮箱已被注册') || 
          result.includes('email already exists') || 
          result.includes('已存在') || 
          result.includes('already registered')
        )) {
          error.value = '该邮箱已被注册，请更换邮箱'
          return
        }
        
        // 注册成功才执行以下代码
        success.value = '注册成功，请登录'
        
        // 重置表单
        name.value = ''
        email.value = ''
        password.value = ''
        confirmPassword.value = ''
        hospital.value = ''
        department.value = ''
        passwordStrength.value = ''
        
        // 3秒后跳转到登录页
        setTimeout(() => {
          router.push('/login')
        }, 3000)
      } catch (err) {
        console.error('Registration error:', err)
        
        // 检查是否是邮箱已存在的错误
        const isEmailTaken = (data) => {
            if (!data) return false;
            const message = (typeof data === 'string') ? data : 
                           (data.message || '');
            return message.includes('邮箱已被注册') || 
                   message.includes('email already exists') || 
                   message.includes('已存在') || 
                   message.includes('already registered');
        };
        
        // 检查各种可能的错误响应格式
        if (err.response && err.response.data && isEmailTaken(err.response.data)) {
            error.value = '该邮箱已被注册，请更换邮箱';
        } else if (err.message && isEmailTaken(err.message)) {
            error.value = '该邮箱已被注册，请更换邮箱';
        } else {
            error.value = '注册失败，请检查填写信息';
        }
      } finally {
        loading.value = false
      }
    }
    
    return {
      name,
      email,
      password,
      confirmPassword,
      hospital,
      department,
      loading,
      error,
      success,
      emailError,
      passwordError,
      passwordStrength,
      validateEmail,
      validatePassword,
      handleRegister
    }
  }
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  width: 100%;
  overflow: hidden;
}

/* 此类由全局CSS定义，这里只是备用 */
.register-page.register-background {
  /* background: linear-gradient(135deg, #0d47a1, #4a148c, #6a1b9a); */
}

.register-container {
  width: 450px;
  max-width: 90%;
  display: flex;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: auto;
  margin: 20px;
}

/* 右侧注册框样式 */
.register-form-container {
  width: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.register-form-box {
  padding: 40px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
}

.register-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 25px;
  justify-content: center;
}

.tab-item {
  padding: 10px 0;
  margin-right: 0;
  font-size: 18px;
  color: #1890ff;
  font-weight: 500;
  position: relative;
}

.tab-item.active {
  color: #1890ff;
  font-weight: 500;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1890ff;
}

.register-form {
  flex: 1;
}

.form-group {
  margin-bottom: 20px;
}

.input-with-icon {
  position: relative;
}

.input-with-icon i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #bfbfbf;
}

.input-with-icon input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s;
}

.input-with-icon input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

.field-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  padding-left: 8px;
}

/* 密码强度相关样式 */
.password-strength {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
}

.strength-label {
  margin-right: 8px;
  color: #666;
}

.strength-indicator {
  flex: 1;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 8px;
}

.strength-bar {
  height: 100%;
  width: 0;
  transition: all 0.3s;
}

.strength-bar.weak {
  width: 33%;
  background-color: #ff4d4f;
}

.strength-bar.medium {
  width: 66%;
  background-color: #faad14;
}

.strength-bar.strong {
  width: 100%;
  background-color: #52c41a;
}

.strength-text {
  font-weight: 500;
}

.strength-text.weak {
  color: #ff4d4f;
}

.strength-text.medium {
  color: #faad14;
}

.strength-text.strong {
  color: #52c41a;
}

.password-hint {
  color: #888;
  font-size: 12px;
  margin-top: 4px;
  padding-left: 8px;
  line-height: 1.4;
}

/* 错误提示相关样式 */
.alert {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
}

.alert-danger {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.alert-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.error-content {
  display: flex;
  align-items: flex-start;
}

.error-icon {
  margin-right: 8px;
  font-size: 16px;
}

.error-message {
  flex: 1;
  line-height: 1.4;
}

.error-action {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed rgba(255, 77, 79, 0.3);
  text-align: right;
}

.login-now-btn {
  display: inline-block;
  background-color: #ff4d4f;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  text-decoration: none;
  transition: background-color 0.3s;
}

.login-now-btn:hover {
  background-color: #ff7875;
  text-decoration: none;
}

.form-actions {
  margin-bottom: 20px;
}

.register-btn {
  width: 100%;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}

.register-btn:hover {
  background-color: #40a9ff;
}

.register-btn:disabled {
  background-color: #91d5ff;
  cursor: not-allowed;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.login-link {
  text-align: center;
  margin-top: 16px;
  font-size: 14px;
}

.login-link span {
  color: #666;
  margin-right: 5px;
}

.login-link a {
  color: #1890ff;
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
}

.register-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.home-link {
  color: #1890ff;
  text-decoration: none;
}

.home-link:hover {
  text-decoration: underline;
}

.copyright {
  color: #999;
}

/* 图标样式 */
.icon-user:before {
  content: "👤";
}

.icon-email:before {
  content: "✉️";
}

.icon-lock:before {
  content: "🔒";
}

.icon-hospital:before {
  content: "🏥";
}

.icon-department:before {
  content: "🔬";
}

/* 响应式调整 */
@media (max-width: 992px) {
  .register-container {
    flex-direction: column;
  }
  
  .register-form-container {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .register-container {
    max-width: 100%;
  }
  
  .register-form-box {
    padding: 20px;
  }
  
  .system-name {
    font-size: 24px;
  }
  
  .feature-list {
    gap: 10px;
  }
  
  .feature-item {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style> 