package com.medical.annotation.controller;

import com.medical.annotation.model.ImagePair;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.service.FileService;
import com.medical.annotation.model.Tag;
import com.medical.annotation.repository.TagRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.cors.CorsConfiguration;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;

import javax.imageio.ImageIO;

@RestController
@CrossOrigin(origins = {"http://localhost:8080", "http://127.0.0.1:8080"}, allowCredentials = "true")
public class ImageFileController {

    @Autowired
    private ImagePairRepository imagePairRepository;
    
    @Autowired
    private FileService fileService;
    
    @Autowired
    private TagRepository tagRepository;

    /**
     * 通用的图像处理接口 - 处理所有图像请求
     * 注意：添加单数形式的image路径匹配
     */
    @GetMapping({"/image/**", "/images/**", "/medical/image/**", "/medical/images/**"})
    public ResponseEntity<Resource> getImage(HttpServletRequest request, HttpServletResponse response) {
        // 添加CORS响应头
        response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
        
        try {
            String requestPath = request.getRequestURI();
            System.out.println("请求图片路径: " + requestPath);
            
            // 先尝试提取文件名
            String filename;
            if (requestPath.contains("/")) {
                filename = requestPath.substring(requestPath.lastIndexOf("/") + 1);
            } else {
                filename = requestPath;
            }
            
            System.out.println("提取的文件名: " + filename);
            
            // 首先尝试从数据库中查找相关记录
            List<ImagePair> imagePairs = imagePairRepository.findByImageOnePathContaining(filename);
            System.out.println("数据库中相关记录数: " + imagePairs.size());
            
            if (!imagePairs.isEmpty()) {
                String actualPath = imagePairs.get(0).getImageOnePath();
                System.out.println("数据库中的实际路径: " + actualPath);
                
                // 检查路径是否包含问号或乱码
                boolean hasEncoding = actualPath.contains("?") || !actualPath.equals(new String(actualPath.getBytes("UTF-8"), "UTF-8"));
                System.out.println("路径可能存在编码问题: " + hasEncoding);
                
                // 尝试正确编码路径
                String decodedPath = actualPath;
                try {
                    // 尝试不同的编码方式
                    if (hasEncoding) {
                        System.out.println("尝试修复路径编码...");
                        decodedPath = new String(actualPath.getBytes("ISO-8859-1"), "UTF-8");
                        System.out.println("修复后的路径: " + decodedPath);
                    }
                } catch (Exception e) {
                    System.err.println("编码转换失败: " + e.getMessage());
                }
                
                // 1. 首先尝试使用FileService的增强方法查找文件
                String targetPath = fileService.convertWebPathToFilePath(actualPath);
                File file = new File(targetPath);
                System.out.println("使用增强路径查找: " + targetPath);
                System.out.println("文件是否存在: " + file.exists() + ", 路径: " + file.getAbsolutePath());

                // 如果文件不存在，尝试数据库中的路径
                if (!file.exists()) {
                    file = new File(actualPath);
                System.out.println("文件是否存在(原路径): " + file.exists() + ", 路径: " + file.getAbsolutePath());
                }
                
                // 如果原路径不存在，尝试使用修复后的路径
                if (!file.exists() && hasEncoding) {
                    File decodedFile = new File(decodedPath);
                    System.out.println("文件是否存在(修复路径): " + decodedFile.exists() + ", 路径: " + decodedFile.getAbsolutePath());
                    
                    if (decodedFile.exists()) {
                        file = decodedFile;
                    }
                }
                
                // 如果文件仍不存在，尝试根据文件名在F:/血管瘤辅助系统/medical_images/temp/目录下查找
                if (!file.exists()) {
                    String alternativePath = "F:/血管瘤辅助系统/medical_images/temp/" + filename;
                    File alternativeFile = new File(alternativePath);
                    System.out.println("尝试替代路径: " + alternativePath);
                    System.out.println("替代文件是否存在: " + alternativeFile.exists());
                    if (alternativeFile.exists()) {
                        file = alternativeFile;
                    }
                }
                
                if (file.exists()) {
                    System.out.println("文件存在，返回文件内容: " + file.getAbsolutePath());
                    try {
                    Resource resource = new FileSystemResource(file);
                    String contentType = Files.probeContentType(file.toPath());
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                            .body(resource);
                    } catch (Exception e) {
                        System.err.println("读取文件失败: " + e.getMessage());
                        e.printStackTrace();
                        // 直接返回文本错误，而不是让全局异常处理器处理
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .contentType(MediaType.TEXT_PLAIN)
                                .body(new ByteArrayResource(("图片文件加载失败: " + e.getMessage()).getBytes()));
                    }
                } else {
                    System.out.println("文件不存在: " + file.getAbsolutePath());
                    // 明确返回文本错误，不进入全局异常处理
                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .contentType(MediaType.TEXT_PLAIN)
                            .body(new ByteArrayResource(("未找到图片文件: " + filename).getBytes()));
                }
            }
            
            // 尝试使用增强的FileService方法
            String webPath = request.getRequestURI();
            if (request.getContextPath() != null && !request.getContextPath().isEmpty()) {
                // 如果有上下文路径，需要排除
                if (webPath.startsWith(request.getContextPath())) {
                    webPath = webPath.substring(request.getContextPath().length());
                }
            }
            
            System.out.println("尝试使用Web路径查找文件: " + webPath);
            String filePath = fileService.convertWebPathToFilePath(webPath);
            File fileFromWebPath = new File(filePath);
            if (fileFromWebPath.exists()) {
                System.out.println("通过Web路径找到文件: " + filePath);
                Resource resource = new FileSystemResource(fileFromWebPath);
                String contentType = Files.probeContentType(fileFromWebPath.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileFromWebPath.getName() + "\"")
                        .body(resource);
            }
            
            // 如果数据库中没有找到或文件不存在，尝试在各个目录中查找
            // 1. 尝试在temp目录中查找
            String tempDir = fileService.getTempDirectoryPath();
            File tempFile = new File(tempDir, filename);
            System.out.println("检查temp目录: " + tempFile.getAbsolutePath());
            
            if (tempFile.exists()) {
                System.out.println("在temp目录找到文件: " + tempFile.getAbsolutePath());
                Resource resource = new FileSystemResource(tempFile);
                String contentType = Files.probeContentType(tempFile.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + tempFile.getName() + "\"")
                        .body(resource);
            }
            
            // 2. 尝试在processed目录中查找
            String processedDir = fileService.getProcessedDirectoryPath();
            File processedFile = new File(processedDir, filename);
            System.out.println("检查processed目录: " + processedFile.getAbsolutePath());
            
            if (processedFile.exists()) {
                System.out.println("在processed目录找到文件: " + processedFile.getAbsolutePath());
                Resource resource = new FileSystemResource(processedFile);
                String contentType = Files.probeContentType(processedFile.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + processedFile.getName() + "\"")
                        .body(resource);
            }
            
            // 3. 尝试在original目录中查找
            String originalDir = fileService.getOriginalDirectoryPath();
            File originalFile = new File(originalDir, filename);
            System.out.println("检查original目录: " + originalFile.getAbsolutePath());
            
            if (originalFile.exists()) {
                System.out.println("在original目录找到文件: " + originalFile.getAbsolutePath());
                Resource resource = new FileSystemResource(originalFile);
                String contentType = Files.probeContentType(originalFile.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + originalFile.getName() + "\"")
                        .body(resource);
            }
            
            // 4. 检查请求URL是否包含temp或processed指示
            if (requestPath.contains("/temp/")) {
                // 如果URL包含/temp/，尝试更具体的路径构建
                String possiblePath = fileService.getTempDirectoryPath() + File.separator + filename;
                File tempPathFile = new File(possiblePath);
                System.out.println("URL中含有temp，尝试特定路径: " + possiblePath);
                
                if (tempPathFile.exists()) {
                    System.out.println("找到文件: " + tempPathFile.getAbsolutePath());
                    Resource resource = new FileSystemResource(tempPathFile);
                    String contentType = Files.probeContentType(tempPathFile.toPath());
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + tempPathFile.getName() + "\"")
                            .body(resource);
                }
            } else if (requestPath.contains("/processed/")) {
                // 如果URL包含/processed/，尝试更具体的路径构建
                String possiblePath = fileService.getProcessedDirectoryPath() + File.separator + filename;
                File processedPathFile = new File(possiblePath);
                System.out.println("URL中含有processed，尝试特定路径: " + possiblePath);
                
                if (processedPathFile.exists()) {
                    System.out.println("找到文件: " + processedPathFile.getAbsolutePath());
                    Resource resource = new FileSystemResource(processedPathFile);
                    String contentType = Files.probeContentType(processedPathFile.toPath());
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + processedPathFile.getName() + "\"")
                            .body(resource);
                }
            }
            
            // 5. 尝试检查文件名是否带有前缀信息
            if (filename.contains("temp_")) {
                // 如果文件名以temp_开头，优先检查temp目录
                String possiblePath = fileService.getTempDirectoryPath() + File.separator + filename;
                File tempPrefixFile = new File(possiblePath);
                System.out.println("文件名带有temp_前缀，尝试temp目录: " + possiblePath);
                
                if (tempPrefixFile.exists()) {
                    System.out.println("找到文件: " + tempPrefixFile.getAbsolutePath());
                    Resource resource = new FileSystemResource(tempPrefixFile);
                    String contentType = Files.probeContentType(tempPrefixFile.toPath());
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + tempPrefixFile.getName() + "\"")
                            .body(resource);
                }
            } else if (filename.contains("processed_")) {
                // 如果文件名以processed_开头，优先检查processed目录
                String possiblePath = fileService.getProcessedDirectoryPath() + File.separator + filename;
                File processedPrefixFile = new File(possiblePath);
                System.out.println("文件名带有processed_前缀，尝试processed目录: " + possiblePath);
                
                if (processedPrefixFile.exists()) {
                    System.out.println("找到文件: " + processedPrefixFile.getAbsolutePath());
                    Resource resource = new FileSystemResource(processedPrefixFile);
                    String contentType = Files.probeContentType(processedPrefixFile.toPath());
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + processedPrefixFile.getName() + "\"")
                            .body(resource);
                }
            }
            
            // 6. 特殊处理标注图像文件（含_annotated）
            if (filename.contains("_annotated")) {
                System.out.println("检测到标注图像请求: " + filename);
                
                // 尝试找到原始图像，移除_annotated后缀
                String originalFilename = filename.replace("_annotated", "");
                
                // 在各目录中查找原始图像
                File originalImageFile = null;
                
                // 先在temp目录查找
                File tempOriginal = new File(fileService.getTempDirectoryPath() + File.separator + originalFilename);
                if (tempOriginal.exists()) {
                    originalImageFile = tempOriginal;
                    System.out.println("找到原始图像(temp): " + originalImageFile.getAbsolutePath());
                }
                
                // 如果没找到，在original目录查找
                if (originalImageFile == null) {
                    File origDirFile = new File(fileService.getOriginalDirectoryPath() + File.separator + originalFilename);
                    if (origDirFile.exists()) {
                        originalImageFile = origDirFile;
                        System.out.println("找到原始图像(original): " + originalImageFile.getAbsolutePath());
                    }
                }
                
                // 如果找到了原始图像，自动创建标注图像的空白副本
                if (originalImageFile != null) {
                    try {
                        // 构建标注图像的路径
                        String annotatedFilePath = fileService.getTempDirectoryPath() + File.separator + filename;
                        File annotatedFile = new File(annotatedFilePath);
                        
                        // 如果标注图像不存在，创建一个带有标注框的副本
                        if (!annotatedFile.exists()) {
                            System.out.println("标注图像不存在，从原始图像创建带标注框的副本: " + annotatedFilePath);
                            
                            // 读取原始图像
                            BufferedImage originalImage = ImageIO.read(originalImageFile);
                            
                            // 尝试从文件名中提取元数据ID，通常格式为 temp_{原始文件名}_annotated.{扩展名}
                            Long metadataId = null;
                            
                            // 尝试从数据库中查找ImagePair记录，通过原始图像路径查找
                            List<ImagePair> pairRecords = imagePairRepository.findByImageOnePathContaining(originalFilename);
                            if (!pairRecords.isEmpty()) {
                                ImagePair pair = pairRecords.get(0);
                                metadataId = pair.getMetadataId();
                                System.out.println("从ImagePair中获取元数据ID: " + metadataId);
                            } else {
                                // 如果找不到，尝试从文件名中提取ID
                                try {
                                    // 从文件名中提取数字部分
                                    String idPart = originalFilename.replaceAll("[^0-9]", "");
                                    if (!idPart.isEmpty()) {
                                        metadataId = Long.parseLong(idPart);
                                        System.out.println("从文件名中提取元数据ID: " + metadataId);
                                    }
                                } catch (Exception e) {
                                    System.err.println("无法从文件名中提取元数据ID: " + e.getMessage());
                                }
                            }
                            
                            // 如果找到了元数据ID，获取标注信息并绘制
                            BufferedImage annotatedImage;
                            if (metadataId != null) {
                                List<Tag> tags = tagRepository.findByMetadataId(metadataId);
                                System.out.println("找到 " + tags.size() + " 个标注");
                                
                                if (!tags.isEmpty()) {
                                    // 绘制标注框
                                    annotatedImage = drawAnnotations(originalImage, tags);
                                } else {
                                    // 如果没有标注，使用原始图像
                                    annotatedImage = originalImage;
                                }
                            } else {
                                // 如果找不到元数据ID，使用原始图像
                                annotatedImage = originalImage;
                            }
                            
                            // 将标注后的图像写入到标注图像文件中
                            String extension = getFileExtension(filename);
                            ImageIO.write(annotatedImage, extension, annotatedFile);
                            
                            System.out.println("已创建标注图像副本: " + annotatedFile.getAbsolutePath());
                        }
                        
                        // 检查文件是否创建成功
                        if (annotatedFile.exists()) {
                            System.out.println("返回标注图像: " + annotatedFile.getAbsolutePath());
                            Resource resource = new FileSystemResource(annotatedFile);
                            String contentType = Files.probeContentType(annotatedFile.toPath());
                            if (contentType == null) {
                                contentType = "application/octet-stream";
                            }
                            
                            return ResponseEntity.ok()
                                    .contentType(MediaType.parseMediaType(contentType))
                                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + annotatedFile.getName() + "\"")
                                    .body(resource);
                        }
                    } catch (Exception e) {
                        System.err.println("创建标注图像失败: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            }
            
            System.out.println("无法找到请求的文件: " + filename);
            return ResponseEntity.notFound().build();
            
        } catch (Exception e) {
            System.err.println("获取图像时发生错误: " + e.getMessage());
            e.printStackTrace();
            // 明确返回文本错误，避免交给全局异常处理器处理
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body(new ByteArrayResource(("处理图片请求时出错: " + e.getMessage()).getBytes()));
        }
    }

    /**
     * 获取处理后的图像文件（来自temp目录）
     */
    @GetMapping("/medical/images/processed/**")
    public ResponseEntity<Resource> getProcessedImage(HttpServletRequest request) {
        try {
            String requestPath = request.getRequestURI();
            String filename = requestPath.substring(requestPath.lastIndexOf("/") + 1);
            System.out.println("请求图片文件: " + filename);
            
            // 首先尝试从image_pairs表中查找对应的路径
            List<ImagePair> imagePairs = imagePairRepository.findByImageOnePathContaining(filename);
            
            if (!imagePairs.isEmpty()) {
                // 找到了数据库记录，使用数据库中存储的实际文件路径
                String actualPath = imagePairs.get(0).getImageOnePath();
                System.out.println("从数据库找到的路径: " + actualPath);
                File file = new File(actualPath);
                
                if (file.exists()) {
                    // 文件存在，返回文件内容
                    System.out.println("文件存在: " + file.getAbsolutePath());
                    Resource resource = new FileSystemResource(file);
                    String contentType = Files.probeContentType(file.toPath());
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                            .body(resource);
                } else {
                    System.out.println("文件不存在: " + file.getAbsolutePath());
                }
            }
            
            // 如果在数据库中没找到或文件不存在，尝试直接从文件系统查找
            // 使用File而不是Paths.get来避免斜杠问题
            String processedDir = fileService.getProcessedDirectoryPath();
            File filePath = new File(processedDir, filename);
            if (filePath.exists()) {
                System.out.println("从processed目录找到文件: " + filePath.getAbsolutePath());
                Resource resource = new FileSystemResource(filePath);
                String contentType = Files.probeContentType(filePath.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filePath.getName() + "\"")
                        .body(resource);
            }
            
            // 最后尝试从temp目录查找
            String tempDir = fileService.getTempDirectoryPath();
            String tempFilename = filename.replace("processed_", "temp_");
            File tempFile = new File(tempDir, tempFilename);
            if (tempFile.exists()) {
                System.out.println("从temp目录找到文件: " + tempFile.getAbsolutePath());
                Resource resource = new FileSystemResource(tempFile);
                String contentType = Files.probeContentType(tempFile.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + tempFile.getName() + "\"")
                        .body(resource);
            }
            
            System.out.println("未找到文件: " + filename);
            return ResponseEntity.notFound().build();
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 获取temp目录下的图像
     */
    @GetMapping("/medical/images/temp/**")
    public ResponseEntity<Resource> getTempImage(HttpServletRequest request) {
        try {
            String requestPath = request.getRequestURI();
            String filename = requestPath.substring(requestPath.lastIndexOf("/") + 1);
            System.out.println("请求temp图片文件: " + filename);
            
            // 首先尝试从image_pairs表中查找对应的路径
            List<ImagePair> imagePairs = imagePairRepository.findByImageOnePathContaining(filename);
            
            if (!imagePairs.isEmpty()) {
                // 找到了数据库记录，使用数据库中存储的实际文件路径
                String actualPath = imagePairs.get(0).getImageOnePath();
                System.out.println("从数据库找到的路径: " + actualPath);
                File file = new File(actualPath);
                
                if (file.exists()) {
                    // 文件存在，返回文件内容
                    System.out.println("文件存在: " + file.getAbsolutePath());
                    Resource resource = new FileSystemResource(file);
                    String contentType = Files.probeContentType(file.toPath());
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                            .body(resource);
                } else {
                    System.out.println("文件不存在: " + file.getAbsolutePath());
                }
            }
            
            // 如果在数据库中没找到或文件不存在，尝试直接从文件系统查找
            String tempDir = fileService.getTempDirectoryPath();
            File filePath = new File(tempDir, filename);
            if (filePath.exists()) {
                System.out.println("从temp目录找到文件: " + filePath.getAbsolutePath());
                Resource resource = new FileSystemResource(filePath);
                String contentType = Files.probeContentType(filePath.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filePath.getName() + "\"")
                        .body(resource);
            }
            
            System.out.println("未找到文件: " + filename);
            return ResponseEntity.notFound().build();
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 处理直接请求根路径的情况
     */
    @GetMapping("/medical/images/{filename:.+}")
    public ResponseEntity<Resource> getRootImage(@PathVariable String filename) {
        try {
            System.out.println("请求根路径图片文件: " + filename);
            
            // 尝试从数据库查找
            List<ImagePair> imagePairs = imagePairRepository.findByImageOnePathContaining(filename);
            if (!imagePairs.isEmpty()) {
                String actualPath = imagePairs.get(0).getImageOnePath();
                System.out.println("从数据库找到的路径: " + actualPath);
                File file = new File(actualPath);
                
                if (file.exists()) {
                    Resource resource = new FileSystemResource(file);
                    String contentType = Files.probeContentType(file.toPath());
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                            .body(resource);
                }
            }
            
            // 尝试在各个目录中查找
            // 先查找temp目录
            String tempDir = fileService.getTempDirectoryPath();
            File tempFile = new File(tempDir, filename);
            if (tempFile.exists()) {
                Resource resource = new FileSystemResource(tempFile);
                String contentType = Files.probeContentType(tempFile.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + tempFile.getName() + "\"")
                        .body(resource);
            }
            
            // 查找processed目录
            String processedDir = fileService.getProcessedDirectoryPath();
            File processedFile = new File(processedDir, filename);
            if (processedFile.exists()) {
                Resource resource = new FileSystemResource(processedFile);
                String contentType = Files.probeContentType(processedFile.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + processedFile.getName() + "\"")
                        .body(resource);
            }
            
            // 查找original目录
            String originalDir = fileService.getOriginalDirectoryPath();
            File originalFile = new File(originalDir, filename);
            if (originalFile.exists()) {
                Resource resource = new FileSystemResource(originalFile);
                String contentType = Files.probeContentType(originalFile.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + originalFile.getName() + "\"")
                        .body(resource);
            }
            
            System.out.println("未找到文件: " + filename);
            return ResponseEntity.notFound().build();
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 直接映射文件系统路径到图片访问URL
     */
    @GetMapping("/image/file/**")
    public ResponseEntity<Resource> getImageByFilePath(HttpServletRequest request, HttpServletResponse response) {
        // 添加CORS响应头
        response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
        
        try {
            // 提取请求路径
            String requestPath = request.getRequestURI();
            String pathPrefix = "/medical/image/file/";
            String relativePath = requestPath.substring(requestPath.indexOf(pathPrefix) + pathPrefix.length());
            
            System.out.println("请求图片文件路径: " + relativePath);
            
            // 构建绝对路径
            File file = new File(fileService.getTempDirectoryPath(), relativePath);
            if (!file.exists()) {
                file = new File(fileService.getProcessedDirectoryPath(), relativePath);
            }
            
            System.out.println("查找文件: " + file.getAbsolutePath());
            System.out.println("文件是否存在: " + file.exists() + ", 大小: " + (file.exists() ? file.length() : 0));
            
            if (file.exists()) {
                // 直接读取字节数据返回
                byte[] data = Files.readAllBytes(file.toPath());
                
                ByteArrayResource resource = new ByteArrayResource(data);
                String contentType = Files.probeContentType(file.toPath());
                if (contentType == null) {
                    contentType = "image/jpeg";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .contentLength(data.length)
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                        .body(resource);
            } else {
                System.out.println("文件不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.err.println("处理图片请求出错: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 根据完整文件系统路径获取图片
     */
    @GetMapping("/image/system-path")
    public ResponseEntity<Resource> getImageBySystemPath(@RequestParam("path") String path, HttpServletRequest request, HttpServletResponse response) {
        // 添加CORS响应头
        response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
        
        try {
            System.out.println("根据系统路径获取图片: " + path);
            
            // 尝试检查并修正路径格式问题
            String decodedPath = path;
            try {
                // 检查路径是否需要解码
                if (path.contains("%")) {
                    decodedPath = java.net.URLDecoder.decode(path, "UTF-8");
                    System.out.println("解码后的路径: " + decodedPath);
                }
            } catch (Exception e) {
                System.err.println("路径解码错误: " + e.getMessage());
            }
            
            // 尝试处理斜杠方向
            String normalizedPath = decodedPath.replace('/', '\\');
            System.out.println("规范化后的路径: " + normalizedPath);
            
            // 首先尝试直接访问提供的路径
            File file = new File(decodedPath);
            if (!file.exists()) {
                // 如果不存在，尝试规范化路径
                file = new File(normalizedPath);
                System.out.println("尝试规范化路径: " + file.getAbsolutePath());
            }
            
            System.out.println("文件是否存在: " + file.exists() + ", 大小: " + (file.exists() ? file.length() : 0));
            
            if (file.exists()) {
                // 直接读取字节数据返回
                byte[] data = Files.readAllBytes(file.toPath());
                
                ByteArrayResource resource = new ByteArrayResource(data);
                String contentType = Files.probeContentType(file.toPath());
                if (contentType == null) {
                    contentType = "image/jpeg";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .contentLength(data.length)
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                        .body(resource);
            } else {
                System.out.println("文件不存在，尝试构建替代路径");
                
                // 尝试从路径提取文件名
                String filename = normalizedPath.substring(normalizedPath.lastIndexOf('\\') + 1);
                System.out.println("提取的文件名: " + filename);
                
                // 尝试在医疗图像temp目录下查找
                String altPath = fileService.getTempDirectoryPath() + File.separator + filename;
                File altFile = new File(altPath);
                System.out.println("尝试替代路径: " + altPath);
                System.out.println("替代文件是否存在: " + altFile.exists());
                
                if (altFile.exists()) {
                    byte[] data = Files.readAllBytes(altFile.toPath());
                    
                    ByteArrayResource resource = new ByteArrayResource(data);
                    String contentType = Files.probeContentType(altFile.toPath());
                    if (contentType == null) {
                        contentType = "image/jpeg";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .contentLength(data.length)
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + altFile.getName() + "\"")
                            .body(resource);
                }
                
                System.out.println("文件不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.err.println("处理图片请求出错: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    private String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < filename.length() - 1) {
            return filename.substring(dotIndex + 1);
        }
        return "jpg"; // Default extension if none is found
    }

    private BufferedImage drawAnnotations(BufferedImage originalImage, List<Tag> tags) {
        // 创建一个新的图像，支持透明度
        BufferedImage annotatedImage = new BufferedImage(
            originalImage.getWidth(), 
            originalImage.getHeight(), 
            BufferedImage.TYPE_INT_ARGB
        );
        
        // 获取Graphics2D对象用于绘图
        java.awt.Graphics2D g2d = annotatedImage.createGraphics();
        
        // 绘制原始图像作为背景
        g2d.drawImage(originalImage, 0, 0, null);
        
        // 设置标注框的样式
        g2d.setColor(java.awt.Color.RED); // 使用红色作为标注框颜色
        g2d.setStroke(new java.awt.BasicStroke(3)); // 设置线宽为3像素
        
        // 绘制每个标注
        for (Tag tag : tags) {
            try {
                // 获取标注的坐标和尺寸，转换为像素坐标
                // Tag中的坐标通常是相对坐标(0-1)，需要转换为绝对像素坐标
                int x = (int)(tag.getX() * originalImage.getWidth());
                int y = (int)(tag.getY() * originalImage.getHeight());
                int width = (int)(tag.getWidth() * originalImage.getWidth());
                int height = (int)(tag.getHeight() * originalImage.getHeight());
                
                // 绘制矩形框
                g2d.drawRect(x, y, width, height);
                
                // 绘制标签文本
                String tagText = tag.getTag() != null ? tag.getTag() : "未标记";
                
                // 创建文本背景
                g2d.setColor(java.awt.Color.WHITE);
                int textPadding = 2;
                int textWidth = g2d.getFontMetrics().stringWidth(tagText) + 2 * textPadding;
                int textHeight = g2d.getFontMetrics().getHeight();
                g2d.fillRect(x, y - textHeight, textWidth, textHeight);
                
                // 绘制文本
                g2d.setColor(java.awt.Color.BLACK);
                g2d.drawString(tagText, x + textPadding, y - textPadding);
                
                // 重置为红色以绘制下一个标注框
                g2d.setColor(java.awt.Color.RED);
                
                System.out.println("绘制标注框: " + tagText + " 在 " + x + "," + y + " 尺寸 " + width + "x" + height);
            } catch (Exception e) {
                System.err.println("绘制标注时出错: " + e.getMessage());
            }
        }
        
        // 释放资源
        g2d.dispose();
        
        return annotatedImage;
    }
} 