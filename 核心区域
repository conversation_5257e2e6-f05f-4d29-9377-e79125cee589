血管瘤AI智能诊断平台
软件操作手册




软件名称：血管瘤AI智能诊断平台
版本号：V2.0
编写日期：2025-7-28
编写单位：[您的单位名称]

 
 
目录
1. 引言	3
1.1 编写目的	3
1.2 软件简介	3
1.3 主要特点	3
1.4 目标用户	4
1.5 文档约定	4
2. 系统概述	5
2.1 系统设计理念与目标	5
2.2 用户角色与核心职责	5
2.3 系统核心工作流程	6
3. 系统环境要求与准备	7
3.1 硬件环境要求	7
3.2 软件环境要求	7
3.3 网络环境建议	7
4. 系统访问与登录	8
4.1 访问系统	8
4.2 用户注册详解	9
4.2.1 步骤一：导航至注册页面	9
4.2.2 步骤二：逐项填写注册信息	10
4.2.3 步骤三：提交注册申请并处理反馈	12
4.3 用户登录详解	13
4.3.1 步骤一：输入登录凭据	13
4.3.2 步骤二（可选）：选择"下次自动登录"	14
4.3.3 步骤三：执行登录	14
4.4 密码找回	15
4.5 安全退出	16
5. 核心功能模块详解	17
5.1 工作台与核心功能模块	17
5.1.1 "工作台"主界面	17
5.1.2 "我的病例标注"管理界面	20
5.1.3 "血管瘤智能诊断"界面	22
5.1.4 血管瘤AI诊断结果界面	25
5.1.5 血管瘤AI诊断完整操作流程	26
5.1.6 传统病例标注功能（可选）	28
5.2 病例审核模块 (审核员/管理员角色)	27
5.2.1 访问"标注审核"工作台	27
5.2.2 步骤二：批阅病例	28
5.2.3 步骤三：执行审核操作并做出决策	29
5.3 后台管理模块 (管理员角色)	31
5.3.1 访问后台管理控制台	31
5.3.2 用户管理	31
5.3.3 团队管理	32
5.3.4 图像管理	33
6. 故障与排除 (FAQ)	33
7. 术语表	36
8. 附录A：场景演练	38
8.1 演练目标	38
8.2 演练步骤	38
9. 附录B：常见错误与提示信息	40
10. 附录C：角色权限矩阵详表	42
11. 附录D：系统模块交互简述	43
11.1 系统整体架构概览	44
11.2 核心模块职责与交互流程	44
11.2.1 前端模块 (浏览器)	45
11.2.2 后端模块 (应用服务器)	45
11.2.3 数据库模块 (MySQL)	45
11.2.4 AI服务模块 (Python)	46
11.3 数据流与处理流程示例	46
11.3.1 用户上传图像并进行标注的数据流	46
11.3.2 用户提交病例进行审核的数据流	47
12. 联系我们	47

 
 1. 引言
 1.1 编写目的
本文档是 "血管瘤AI智能诊断平台" (以下简称"本系统")的官方操作指南。其编写的核心目的在于提供一份全面、详尽、清晰的用户操作说明，确保每一位使用者都能准确、高效地利用本系统的AI诊断功能和各项管理功能。

通过本手册，用户可以：
-   了解系统的设计目标、核心价值和主要功能构成。
-   熟悉系统的运行环境要求和访问方式。
-   掌握从注册、登录到完成核心业务操作的全过程。
-   在遇到操作疑问或常见问题时，能够快速找到解决方案。

本手册力求图文并茂、步骤清晰，是您使用本系统不可或缺的参考资料。
 1.2 软件简介
本系统是一套基于B/S（浏览器/服务器）架构的血管瘤智能诊断平台，集成了先进的人工智能（AI）辅助诊断功能。系统专注于血管瘤及脉管畸形的智能诊断，为临床医生提供高效、准确、便捷的AI诊断辅助工具。

系统核心功能包括：
- **血管瘤AI智能诊断**: 基于YOLO深度学习模型的自动检测和三大类分类体系
- **智能分类系统**: 支持真性血管肿瘤、血管畸形、血管假瘤/易混淆病变三大类30种子类型
- **LLM诊断建议**: 集成大语言模型生成结构化的专业诊断报告和治疗建议
- **AI自动标注**: 检测结果自动填写分类标签，提高标注效率
- **工作台数据概览**: 直观展示诊断统计和工作进度
- **病例管理系统**: 完整的病例创建、编辑、审核工作流
- **异步处理架构**: 快速响应的YOLO检测 + 后台LLM建议生成
- **多角色协作**: 医生、审核员、管理员的分级权限管理
- **团队协作**: 支持医疗团队的协同工作和知识共享
 1.3 主要特点
-   **AI智能诊断**：集成先进的YOLO深度学习模型，可对上传的医学影像进行自动化分析，智能识别血管瘤特征，自动生成标注框和置信度评分，支持15种血管瘤类型的精确识别，为医生提供量化的、客观的诊断建议，显著提升诊断效率和准确性。

-   **三大类分类体系**：采用国际标准的血管瘤分类方法，将血管瘤分为真性血管肿瘤（15种）、血管畸形（8种）、血管假瘤/易混淆病变（6种）三大类，共计30种子类型，覆盖临床常见的所有血管瘤类型。

-   **LLM智能建议**：集成大语言模型（LLM），基于AI检测结果和患者信息，自动生成结构化的专业诊断报告，包括诊断总结、治疗建议、预防措施和医疗免责声明四个部分，为临床决策提供全面支持。

-   **AI自动分类**：当用户进入标注界面时，系统自动根据AI检测结果设置对应的血管瘤分类标签，用户只需在需要时进行微调，大幅提高标注效率和准确性。

-   **异步处理架构**：采用快速YOLO检测 + 后台LLM建议生成的异步处理模式，用户可立即获得检测结果，详细的诊断建议在后台生成完成后自动更新，确保系统响应速度。
-   **智能工作台**：提供直观的数据概览界面，实时显示病例统计、工作进度和快捷操作入口，帮助医生高效管理日常诊断工作。
-   **AI诊断建议生成**：基于大语言模型技术，结合检测结果和患者信息，自动生成专业的治疗方案、随访建议和注意事项，为临床决策提供有力支持。
-   **完整的病例管理**：提供从病例创建、AI诊断、结果审核到最终归档的完整工作流程，支持病例状态追踪和历史记录查询。
-   **高效的团队协作**：支持多位医生组成线上医疗团队，共同对病例进行管理、讨论和审核。系统的流程化设计促进了医疗团队内部的沟通、协作与质量控制。
-   **严格的权限管理**：预设了医生、审核员、管理员等多种用户角色，每种角色拥有严格定义的操作权限，确保了医疗数据的安全、保密和操作流程的规范性。

 1.4 目标用户
本手册主要面向以下用户群体：
-   临床医生：特别是皮肤科、儿科、血管外科等相关科室的医生，是本系统的主要使用者。
-   医学研究人员：需要进行血管瘤相关数据收集、整理和分析的研究者。
-   科室或医院管理员：负责管理本科室或本单位的用户、数据和团队。
-   医学生或实习医生：在指导下使用本系统进行学习和实践。
 1.5 文档约定
为方便阅读，本手册采用以下书写约定：
-   【按钮/菜单】：使用加粗和方括号表示界面上可以点击的按钮、菜单项或链接。例如：【登 录】按钮。
-   "页面/模块"：使用加粗和引号表示系统的功能模块或页面名称。例如："我的病例标注"页面。
-   `输入内容`：使用斜体和反引号表示需要用户输入的文本内容。
-   [截图 X-X: 描述]：作为截图的占位符和图注，描述了该截图应展示的界面内容。
 2. 系统概述
 2.1 系统设计理念与目标
本系统的设计核心是"技术赋能医疗"，旨在利用现代信息技术与人工智能，优化传统血管瘤诊疗流程。我们的目标是：
1.  效率提升: 将医生从重复、繁琐的阅片和记录工作中解放出来。
2.  精度辅助: 为医生的诊断提供客观数据参考，辅助决策。
3.  数据资产化: 将零散的病例资料转化为结构化、可用于研究的宝贵数据资产。
4.  协作无界: 打破物理空间限制，实现高效的远程团队协作和会诊。
 2.2 用户角色与核心职责
本系统预设了三种核心用户角色，各角色职责分明，权限逐级递增。
| 角色 | 核心职责 | 详细权限说明 |
| :--- | :--- | :--- |
| 医生 (DOCTOR) | 病例的创建者和执行者 | - 基础权限：注册、登录、退出、修改个人信息。<br>- 病例管理：创建新病例、上传图像、编辑和保存草稿。<br>- 核心操作：对图像进行病灶标注、填写详细的病例信息表单。<br>- 流程操作：将完成的病例提交审核。<br>- 权限申请：可向管理员申请升级为"审核员"角色。|
| 审核员 (REVIEWER) | 诊断的审核与质量控制者 | - 包含"医生"角色的全部权限。<br>- 审核工作：查看并批阅其他医生提交的"待审核"病例。<br>- 审核决策：可对病例进行【通过】或【驳回】操作，并填写审核意见。<br>- 团队管理：可以创建医疗团队，并邀请其他用户加入。 |
| 管理员 (ADMIN) | 系统的最高管理者 | - 包含"审核员"角色的全部权限。<br>- 用户管理：管理系统中所有用户账号的增、删、改、查，可直接分配用户角色。<br>- 权限审批：处理"医生"角色提交的权限升级申请。<br>- 团队管理：管理系统中的所有团队。<br>- 全局数据管理：可查看和管理系统中的所有病例及图像文件。 |
 2.3 系统核心工作流程
本系统的核心业务围绕血管瘤AI诊断的"上传 -> AI分析 -> 结果确认"这一智能化流程展开。
```mermaid
graph TD
    A(开始) --> B{用户登录};
    B --> C[进入工作台];
    C --> D[点击【血管瘤诊断】];
    D --> E[填写患者信息];
    E --> F[上传血管瘤图像];
    F --> G[点击【开始诊断】];
    G --> H[AI自动分析];
    H --> I[生成检测结果];
    I --> J[显示标注框和置信度];
    J --> K[生成AI诊断建议];
    K --> L[医生查看结果];
    L --> M{需要修改?};
    M -- 是 --> N[编辑诊断信息];
    N --> O[保存修改];
    M -- 否 --> P[确认诊断结果];
    O --> P;
    P --> Q[生成诊断报告];
    Q --> R(诊断完成);
```
(上图为血管瘤AI诊断核心工作流程示意图)
 3. 系统环境要求与准备
 3.1 硬件环境要求
为了保证流畅、高效地使用本系统，我们建议您的计算机硬件配置不低于以下标准：
-   处理器 (CPU): Intel Core i5 或 AMD Ryzen 5 系列及以上。
-   内存 (RAM): 推荐 8 GB 及以上。更大的内存有助于在处理高分辨率图像时获得更流畅的体验。
-   显卡 (GPU): 无特殊要求，主流集成显卡即可满足日常使用。
-   显示器分辨率: 为了获得最佳的视觉效果和操作空间，推荐使用分辨率为 1920x1080 (全高清) 或更高的显示器。
-   输入设备: 标准键盘和鼠标。
 3.2 软件环境要求
-   操作系统:
    -   Windows: 推荐 Windows 10 或 Windows 11。
    -   macOS: 推荐 macOS 11 Big Sur 或更高版本。
    -   Linux: 主流发行版，如 Ubuntu 20.04 LTS 或更高版本。
-   浏览器: 本系统基于现代Web技术构建，为获得最佳的兼容性、性能和安全性，请务必使用以下浏览器的最新稳定版本：
    -   Google Chrome (谷歌浏览器) (强烈推荐)
    -   Microsoft Edge (基于Chromium内核)
    -   Mozilla Firefox (火狐浏览器)
    -   Safari (仅限macOS)
> 重要说明：请勿使用 Internet Explorer (IE) 或其他过时的浏览器，否则可能导致功能异常或无法使用。
 3.3 网络环境建议
-   网络连接: 系统所有功能均需在联网环境下使用，请确保您的计算机已连接到互联网。
-   网络速度: 由于系统涉及医学影像的上传和加载，为保证操作流畅，推荐使用下行速度不低于 20Mbps 的宽带连接。网络速度越快，图像加载时间越短，体验越好。
 4. 系统访问与登录
本章节将以最详尽的步骤，分解说明用户如何从零开始进入并使用本系统，涵盖了从访问、注册、登录到安全退出的全过程。每一个步骤都将描述到原子级别，以确保用户对系统的每一个响应都有清晰的预期。
 4.1 访问系统
本系统为B/S（浏览器/服务器）架构的应用程序，用户无需在本地计算机上安装任何客户端软件，仅需通过标准的网页浏览器即可访问。

操作场景: 任何用户在开始一切工作前的第一个步骤。

1.  前置条件:
    -   用户的计算机已根据 "3. 系统环境要求与准备" 章节完成配置。
    -   计算机已成功连接到互联网。
    -   用户已从系统管理员处获得了本系统的唯一访问地址（URL）。

2.  操作步骤:
    1.  启动浏览器: 在您的计算机操作系统中，找到并双击打开一个符合要求的浏览器程序图标，例如 Google Chrome。
    2.  定位地址栏: 在浏览器窗口的顶部，找到用于输入网址的地址栏。
    3.  输入系统地址: 使用键盘，在地址栏中准确无误地输入由系统管理员提供的系统访问URL。
        -   地址格式示: `http://<服务器IP地址或域名>:<端口号>`
    4.  确认访问: 按下键盘上的 【Enter】 键。

3.  系统响应与预期结果:
    -   按下回车键后，浏览器将向指定地址的服务器发送页面请求。
    -   页面开始加载，您可能会短暂地看到一个空白页面。
    -   数秒内（取决于网络速度），页面加载完成，您将看到本系统的 "用户登录" 页面。页面的标题通常为 "血管瘤人工智能辅助治疗系统"，中央区域会有一个清晰的登录表单。
    -   至此，您已成功访问本系统。

 
 4.2 用户注册详解
对于初次使用的用户，必须先注册一个个人专属账号，这是使用系统所有功能的前提。

操作场景: 新加入的医生、研究员或管理员首次使用本系统。

 4.2.1 步骤一：导航至注册页面
[截图 4.2.1-1: 系统登录页面的完整视图，用红色箭头或高亮框醒目地标示出位于登录框下方的【立即注册】文本链接。]

1.  前置条件: 您已成功访问本系统，当前停留在 "用户登录" 页面。
2.  界面定位: 将您的目光聚焦在登录表单的下方区域。您会找到一个内容为 【立即注册】 的蓝色可点击文本链接。
3.  用户操作:
    -   将鼠标光标移动到 【立即注册】 文本链接上。
    -   光标的样式会从默认的箭头变为一只小手形状，表示此链接可以点击。
    -   单击鼠标左键。
4.  系统响应:
    -   成功: 页面内容会发生即时切换，从"登录"视图平滑过渡到 "用户注册" 视图。您会看到一组用于信息录入的全新空白表单，页面标题或主提示文字会变为"用户注册"或类似内容。
    -   失败: 此操作无典型失败场景。如果链接无法点击，请检查浏览器是否禁用了JavaScript，或尝试刷新页面后重试。

 4.2.2 步骤二：逐项填写注册信息
进入注册页面后，您需要准确、完整地填写表单中的每一个字段。

  ******* 字段：姓名
-   功能描述: 此姓名将作为您在系统内的身份标识，会出现在您的个人资料、操作日志、病例报告等所有与您身份相关的位置。
-   界面元素: 一个标准的单行文本输入框，左侧有"姓名"标签。
-   操作:
    1.  用鼠标左键单击该输入框，框内会出现闪烁的光标。
    2.  通过键盘输入您的真实姓名。
-   填写要求:
    -   必填项。
    -   建议使用您在医疗机构中常用的职业姓名，以便于同事和管理员进行识别和管理。
    -   长度限制通常为2至20个中英文字符。
-   示例: `张三`
-   客户端校验: 当您离开此输入框时，如果内容为空，会在填写栏下面显示 "请填写此字段"的提示。
 ******* 字段：邮箱地址
-   功能描述: 此邮箱极为重要，它将作为您登录系统的唯一账号，同时也是接收系统通知（如审核结果）和找回密码的唯一渠道。
-   界面元素: 一个标准的单行文本输入框，左侧有"邮箱地址"标签，通常包含一个"@"符号图标作为提示。
-   操作: 在输入框内，输入一个您本人拥有、常用且能正常收发邮件的电子邮箱地址。
-   填写要求:
    -   必填项。
    -   必须是真实、有效的邮箱格式，例如 `<EMAIL>`。
-   示例: `<EMAIL>`
-   客户端校验:
    -   若输入为空，会提示"邮箱地址不能为空"。
    -   若输入的格式不正确（例如缺少"@"符号或域名），会提示"请输入有效的邮箱地址"。
-   重要提示: 请务必确保此邮箱的安全性，并牢记其密码，因为它是您账户所有权的最终凭证。
 4.2.2.3 字段：设置密码
-   功能描述: 为您的系统账户设置一个安全的登录密码。
-   界面元素: 一个密码类型的输入框。为保护您的隐私，您输入的字符将不会明文显示，而是以实心圆点（●）或星号（）代替。
-   操作: 在输入框内，输入您构思好的账户密码。
-   填写要求:
    -   必填项。
    -   为保障账户数据安全，密码长度必须不少于8位。
    -   强烈建议使用包含大写字母、小写字母、数字和特殊符号（如 `!@$%^&`）的组合密码。
-   安全警告: 严禁使用过于简单的、可被轻易猜到的密码（如`123456`、`abcdef`、`password`）。请勿使用与其他网站或应用相同的密码，以防范"撞库攻击"带来的风险。一个强密码是您数据安全的第一道、也是最重要的防线。
 4.2.2.4 字段：再次输入密码
-   功能描述: 防止您因误操作输错密码，要求您再次输入以进行确认。
-   界面元素: 同样是一个密码类型的输入框。
-   操作: 在此输入框内，重新输入一遍您刚才在"设置密码"框中输入的密码。
-   填写要求:
    -   必填项。
    -   内容必须与上一个"设置密码"框中输入的内容完全一致。
-   客户端校验: 系统会实时或在提交时自动比对两个密码框的内容。如果两次输入不匹配，会给出明确的错误提示，例如"两次输入的密码不一致"。
 4.2.2.5 字段：所在医院
-   功能描述: 用于记录您当前所属的医疗机构或单位。此信息有助于团队管理和数据统计。
-   界面元素: 标准的单行文本输入框。
-   操作: 在输入框内，输入您当前就职的医院完整官方名称。
-   示例: `XX省第一人民医院`
 4.2.2.6 字段：所在科室
-   功能描述: 用于记录您所属的具体科室。
-   界面元素: 标准的单行文本输入框。
-   操作: 在输入框内，输入您当前所在的具体科室名称。
-   示例: `皮肤科` or `血管瘤与脉管畸形中心`
 4.2.3 步骤三：提交注册申请并处理反馈
1.  前置条件: 您已完成上述所有必填表单字段的填写，并确认所有信息准确无误。
2.  界面定位: 在注册表单的最下方，有一个视觉上非常显著的 【立即注册】 按钮。
3.  用户操作: 使用鼠标单击 【立即注册】 按钮。

4.  预期系统反馈（包含所有场景）:
    -   场景一：注册成功 (最理想情况)
        -   系统行为:
            1.  前端JavaScript对所有字段进行最终格式校验。
            2.  校验通过后，将您的注册信息加密并通过网络发送到服务器。
            3.  服务器创建账户成功后，会向浏览器返回一个成功的信号。
        -   界面变化:
            1.  浏览器界面会弹出一个内容为"注册成功！"的模态对话框或短暂的顶部提示条。
            2.  在您手动关闭提示或数秒后，页面将自动跳转回本系统的"用户登录"页面。
        -   后续操作: 您现在可以使用刚刚注册的邮箱和密码，在登录页面进行首次登录。

    -   场景二：注册失败（前端客户端校验）
        -   系统行为: 如果您填写的信息不符合基本格式（例如邮箱格式错误，两次密码不一致，或有必填项为空），在您点击按钮的瞬间，前端JavaScript会立刻进行校验并阻止向服务器发送任何请求。
        -   界面变化:
            1.  在不符合要求的输入框下方或旁边，会立即出现红色的错误提示文字，例如"请输入有效的邮箱地址"或"两次输入的密码不一致"。
            2.  【立即注册】按钮可能会短暂地左右晃动，以一种视觉上的方式提醒您存在错误。
        -   后续操作: 您需要仔细阅读错误提示，定位到错误的输入框，修正其中的内容，直到所有红色提示消失。然后再次点击【立即注册】按钮。
         
 

    -   场景三：注册失败（后端服务器校验）
        -   系统行为: 如果您填写的信息格式完全正确，但业务逻辑上存在冲突（最常见的情况是：该邮箱已被其他用户注册），信息会成功发送到服务器。服务器进行业务逻辑校验后，发现冲突，并向浏览器返回一个明确的错误信息。
        -   界面变化: 页面顶部或表单附近会显示一个来自服务器的错误提示条，内容为："该邮箱已被注册，请更换邮箱"。
        -   后续操作: 您需要根据服务器的提示，更换一个新的、未被注册过的邮箱地址，或者如果您想起自己可能已注册过，可以直接前往登录页面尝试登录。
 
 4.3 用户登录详解
操作场景：已经成功注册并拥有账户的用户，访问系统以开始日常工作。

  4.3.1 步骤一：输入登录凭据
1.  前置条件：您已经拥有一个本系统的账户，并已成功打开了系统的登录页面。
2.  界面元素与操作:
    -   邮箱地址输入框:
        -   定位: 找到标有"邮箱地址"的输入框。
        -   操作: 用鼠标左键单击该输入框，当光标闪烁时，通过键盘输入您注册时使用的完整电子邮箱地址。
    -   密码输入框:
        -   定位: 找到标有"密码"的输入框。
        -   操作: 用鼠标左键单击该输入框，当光标闪烁时，通过键盘输入您的账户密码。请特别注意密码的大小写是否正确，以及键盘的大写锁定键（Caps Lock）是否意外开启。

 4.3.2 步骤二（可选）：选择"下次自动登录"
-   功能描述: 勾选此项后，系统会在您的浏览器中保存一个安全的身份凭证。在凭证有效期内（例如7天），您使用同一台计算机的同一浏览器再次访问本系统时，将无需重复输入密码，系统会自动识别您的身份并直接进入主界面。
-   界面元素: 在登录按钮附近，有一个标签为"下次自动登录"或"保持登录"的小复选框。
-   操作: 如果您当前是在个人且受信任的计算机上操作，为了方便，可以用鼠标左键单击该复选框，框内出现一个"✓"符号表示已成功选中。
-   安全警告: 为了您的账户和敏感医疗数据的安全，请绝对不要在任何公共计算机（如图书馆、网吧、酒店商务中心）或他人的计算机上使用此功能。否则可能导致您的账户信息泄露或被未授权使用。

 4.3.3 步骤三：执行登录
1.  前置条件: 您已确保"邮箱地址"和"密码"均已正确填写。
2.  界面定位: 在输入框下方，找到那个唯一的、颜色突出的 【登 录】 按钮。
3.  用户操作: 使用鼠标左键单击 【登 录】 按钮。
4.  预期系统反馈:
    -   场景一：登录成功
        -   系统行为: 前端将您输入的凭据发送至服务器。服务器验证通过后，会生成一个具有时效性的身份令牌（Token）并返回给前端。前端浏览器会自动将此令牌存储起来，作为您后续所有操作的"通行证"。
        -   界面变化: 页面将自动跳转至系统的主功能界面。对于"医生"角色的用户，默认进入的通常是 "我的病例标注" 工作台页面。您会看到一个全新的、功能丰富的界面布局，包括左侧的导航菜单栏和页面右上角显示的您的用户名。
        -   后续操作: 您可以正式开始使用系统的各项功能。
         

    -   场景二：登录失败
        -   系统行为: 服务器验证后发现您的邮箱或密码不匹配，会返回一个认证失败的错误信号。
        -   界面变化: 页面不会发生跳转，仍停留在登录页。在【登 录】按钮的上方或表单附近，会出现一条红色的错误提示信息，内容通常为 "用户名或密码错误"。您之前输入的邮箱和密码通常会被保留，以便您进行修改。
        -   后续操作:
            1.  请仔细重新检查您输入的邮箱和密码是否准确无误，特别注意：
                -   键盘的大小写锁定是否开启？
                -   输入法是否处于英文半角状态？
                -   邮箱地址是否完整，有没有多余的空格？
            2.  修正输入后，再次点击【登 录】按钮尝试。
            3.  如果多次尝试均失败，您可能需要使用 "4.4 密码找回" 功能来重置您的密码。
         

 4.4 密码找回
操作场景：用户因长时间未使用或其他原因，忘记了自己的账户登录密码，导致无法登录系统。

操作全流程详解:
1.  定位入口: 在系统登录页面的密码输入框附近，或登录按钮下方，找到并用鼠标左键点击内容为 【忘记密码?】 的文本链接。
2.  进入重置流程: 系统将跳转至一个标题为 "密码找回" 或 "重置密码" 的新页面。
3.  输入验证邮箱: 该页面会有一个输入框，要求您输入注册时使用的电子邮箱地址。请在此输入框中准确输入您的邮箱。
4.  请求验证邮件: 点击页面上的 【发送验证邮件】 或 【获取验证码】 按钮。
5.  系统响应: 系统会提示"验证邮件已发送，请注意查收"。同时，后台服务会向您填写的邮箱地址发送一封包含有时效性的密码重置链接或数字验证码的邮件。
6.  查收邮件:
    -   登录您自己的个人电子邮箱（例如QQ邮箱、163邮箱等）。
    -   在收件箱中找到一封来自本系统（发件人名称通常为"血管瘤人工智能辅助治疗系统"）的标题为"密码重置"的邮件。
    -   > 提示：如果收件箱中没有找到，请务必检查该邮件是否被误判进入了 "垃圾邮件"、"广告邮件" 或 "订阅邮件" 文件夹。
7.  执行重置:
    -   情况A：邮件中是重置链接：直接用鼠标左键点击邮件正文中的密码重置链接。
    -   情况B：邮件中是验证码：复制该数字验证码，返回到系统的密码找回页面，在指定的验证码输入框中粘贴或输入该验证码，然后点击【下一步】。
8.  设置新密码: 浏览器会自动打开一个新的密码设置页面。该页面通常包含两个输入框："新密码"和"确认新密码"。请在这两个框中输入您想要设置的全新密码，然后点击 【确认修改】 或 【重置密码】 按钮。
9.  完成与重新登录: 页面会提示"密码重置成功"，并自动跳转回系统的登录页面。此时，您必须使用您的邮箱和刚刚设置的 新密码 进行登录。旧密码已永久失效。

 4.5 安全退出
操作场景：在您完成当天的工作，或需要中途离开您的工作电脑时，或在公共计算机上完成操作后，都必须执行安全退出。这是一个至关重要的安全操作，能有效防止他人未经授权访问您的账户和其中敏感的医疗数据。


操作全流程详解:
1.  定位用户菜单触发器: 在您登录系统后的任何功能页面，将您的目光移至界面的右上角。您会看到一个显示您用户名的区域。
2.  触发下拉菜单: 使用鼠标，将光标移动到该用户名区域上，并单击鼠标左键。
3.  系统响应 - 菜单出现: 单击后，一个包含若干选项的下拉式菜单会从该区域正下方优雅地展开。
    
4.  定位并选择退出选项: 在这个刚刚出现的下拉菜单中，找到内容为 【退出登录】 的菜单项，并用鼠标左键单击它。
5.  执行退出与系统最终反馈:
    -   系统行为: 在您点击的瞬间，前端浏览器会立刻向服务器发送一个退出登录的请求。服务器收到请求后，会立即销毁您本次登录的会话（Session）状态和身份令牌（Token）。
    -   界面变化: 浏览器页面会被强制刷新，并立即跳转回本系统的"用户登录"页面。
    -   状态确认: 回到登录页面，即表示您已成功、彻底地从系统中退出了。此时，如果您尝试通过浏览器的"后退"按钮返回之前的工作页面，系统将因检测不到有效的登录状态而拒绝访问，并很可能再次将您强制重定向回登录页。
- 最佳实践: 养成每次使用完系统后都执行【退出登录】的习惯，是保障信息安全的强制性、必须遵守的最佳实践。
 
 5. 核心功能模块详解
 5.1 工作台与核心功能模块
这是系统的主要工作区域，包含了血管瘤诊断、病例管理、团队协作等核心功能。用户登录后首先进入工作台，可以快速访问各项功能。

 5.1.1 "工作台"主界面
用户成功登录后，系统会自动跳转到"工作台"主界面。工作台是所有功能的入口，提供了直观的数据概览和快捷操作。

  5.1.1.1 工作台界面元素详解
工作台界面采用卡片式布局，直观展示系统的核心功能和数据统计。整个界面分为以下几个关键区域：

-   A区 - 数据统计概览区:
    -   位置: 位于页面顶部，以四个统计卡片的形式展示
    -   功能: 实时显示用户的工作数据统计，帮助快速了解当前工作状态
    -   统计卡片详解:
        -   【病例总数】:
            -   描述: 显示用户创建的所有病例总数（如图中显示"5"）
            -   功能: 快速了解累计工作量
        -   【已标注】:
            -   描述: 显示已完成标注的病例数量（如图中显示"2"）
            -   功能: 追踪已完成的工作进度
        -   【待审核】:
            -   描述: 显示当前等待审核的病例数量（如图中显示"0"）
            -   功能: 了解待处理的审核任务
        -   【已通过】:
            -   描述: 显示审核通过的病例数量（如图中显示"1"）
            -   功能: 查看最终完成的工作成果

-   B区 - 快速操作区:
    -   位置: 位于统计区域下方，以大型功能卡片形式展示
    -   功能: 提供系统核心功能的快速访问入口
    -   主要功能卡片:
        -   【血管瘤诊断】:
            -   描述: "使用AI辅助诊断血管瘤"
            -   功能: 直接进入血管瘤智能诊断功能
            -   操作: 点击卡片进入AI诊断页面
        -   【团队标注】:
            -   描述: "查看团队标注任务"
            -   功能: 访问团队协作和标注管理
            -   操作: 点击卡片进入团队工作区

 5.1.2 "我的病例标注"管理界面
通过点击左侧导航栏的【病例标注】菜单项，可以进入详细的病例管理界面。该界面提供了完整的病例管理功能。

  5.1.2.1 病例列表界面元素详解
-   A区 - 状态筛选标签:
    -   位置: 位于页面顶部，以标签页形式展示
    -   功能: 根据病例状态进行快速筛选
    -   筛选选项:
        -   【查询】: 默认视图，显示所有病例
        -   【查看】: 查看特定状态的病例

-   B区 - 病例列表区:
    -   位置: 占据页面主体区域
    -   功能: 以表格形式展示病例信息
    -   列表字段详解:
        -   病例编号: 系统自动生成的唯一标识符（如CASE-245、CASE-244等）
        -   部位: 病变部位信息（如"未知"表示待完善）
        -   类型: 诊断类型信息
        -   状态: 病例当前状态，用不同颜色标识：
            -   橙色【已标注】: 已完成标注待提交
            -   灰色【未标注】: 尚未完成标注
            -   绿色【已通过】: 审核通过
        -   创建时间: 精确到秒的创建时间戳
        -   操作: 根据病例状态和用户权限动态显示操作按钮：

**按钮权限与状态对应关系详解：**

| 病例状态 | 显示按钮 | 权限要求 | 点击后跳转 | 状态变化 |
|---------|---------|---------|-----------|---------|
| 未标注 | 【编辑】 | 病例创建者 | 编辑界面 | 无变化（编辑中可保存草稿或提交审核） |
| 未标注 | 【删除】 | 病例创建者 | 确认对话框 | 删除后从列表移除 |
| 已驳回 | 【编辑】 | 病例创建者 | 编辑界面 | 无变化（编辑中可保存草稿或提交审核） |
| 已驳回 | 【删除】 | 病例创建者 | 确认对话框 | 删除后从列表移除 |
| 待审核 | 【查看】 | 病例创建者/审核员/管理员 | 查看界面 | 无变化 |
| 已通过 | 【查看】 | 病例创建者/审核员/管理员 | 查看界面 | 无变化 |

**重要说明：**
- **【编辑】按钮**: 仅在"未标注"和"已驳回"状态下显示，只有病例创建者可以点击
- **【查看】按钮**: 在"待审核"和"已通过"状态下显示，多种角色都可以点击
- **【删除】按钮**: 仅在"未标注"和"已驳回"状态下显示，需要二次确认，操作不可逆
- **权限控制**: 系统会根据当前登录用户的身份和权限动态显示可用按钮
 5.1.3 "血管瘤智能诊断"界面
这是系统的核心AI诊断功能，通过先进的YOLO深度学习模型和大语言模型为血管瘤诊断提供智能辅助。系统采用异步处理架构，提供快速的检测响应和全面的诊断建议。用户可以通过工作台的【血管瘤诊断】卡片或左侧导航栏的【血管瘤诊断】菜单项进入此界面。

  5.1.3.1 血管瘤诊断界面布局详解
血管瘤诊断界面专为AI辅助诊断设计，界面简洁高效，分为以下几个核心区域：

-   A区 - 患者信息输入区:
    -   位置: 页面上方，水平排列的表单字段
    -   功能: 输入影响AI诊断准确性的关键患者信息
    -   主要字段:
        -   患者年龄: 数字输入框，对血管瘤类型判断至关重要
        -   性别: 单选按钮（男/女），某些血管瘤类型有性别倾向
        -   类型: 单选按钮（先天性/后天性），血管瘤分类的重要依据
        -   血管质地: 下拉选择框，选择血管的触感特征
    -   **重要说明**: 这些信息将用于LLM生成个性化的诊断建议

-   B区 - 图像上传与AI诊断区:
    -   位置: 页面中央，占据主要空间
    -   功能: 图像上传和AI智能分析的核心区域
    -   界面元素:
        -   上传区域: 支持点击上传或拖拽上传
        -   文件格式提示: 支持JPG、PNG、BMP等格式，最大文件不超过10MB
        -   AI处理状态: 实时显示YOLO检测进度
        -   【开始诊断】按钮: 启动AI诊断流程
    -   **处理流程**:
        -   第一阶段: 快速YOLO检测（1-3秒）
        -   第二阶段: 后台LLM建议生成（30-60秒）

-   C区 - 操作按钮区:
    -   位置: 页面底部
    -   功能: 提供诊断流程控制选项
    -   按钮选项:
        -   【开始诊断】: 启动AI分析（蓝色主按钮）
        -   【重置】: 清空当前输入，重新开始

 5.1.4 血管瘤AI诊断结果界面
当YOLO检测完成后，系统会立即跳转到诊断结果展示界面，该界面详细展示了AI的分析结果。LLM生成的详细诊断建议会在后台处理完成后自动更新显示。

  5.1.4.1 诊断结果界面布局详解
-   A区 - 诊断图像展示区:
    -   位置: 页面左侧，占据较大空间
    -   功能: 显示上传的原始图像和AI检测结果
    -   界面元素:
        -   原始图像: 清晰显示患者上传的血管瘤图像
        -   AI标注框: 红色矩形框标出YOLO检测到的血管瘤区域
        -   置信度标识: 在标注框附近显示AI的置信度评分
        -   类型标签: 显示检测到的血管瘤类型缩写（如IH、AVM等）

-   B区 - 诊断结果信息区:
    -   位置: 页面右侧，垂直排列的信息卡片
    -   功能: 展示AI诊断的详细结果
    -   主要信息项:
        -   检测状态: 显示"已检测到"等状态信息
        -   检测到的类型: AI识别的血管瘤类型（支持15种类型）
        -   分类归属: 显示属于三大类中的哪一类
        -   置信度: AI对诊断结果的信心程度（如"93%"）
        -   检测框数量: 显示检测到的病灶数量
        -   处理状态: 显示LLM建议生成状态

-   C区 - LLM诊断建议区:
    -   位置: 页面下方，结构化的信息展示区域
    -   功能: 显示大语言模型生成的专业诊断建议
    -   **初始状态**: 显示"正在生成详细诊断建议..."
    -   **完成后内容结构**:
        -   **诊断总结**: 基于AI检测结果和患者信息的综合分析
        -   **治疗建议**: 具体的治疗方案和医疗建议
        -   **预防措施**: 日常护理和预防注意事项
        -   **医疗免责声明**: 专业的医疗免责说明
    -   **更新机制**: 后台LLM处理完成后自动刷新显示
  5.1.4.2 LLM诊断建议内容示例
基于大语言模型生成的结构化诊断建议通常包含以下四个部分：

1. **诊断总结** (Diagnostic Summary):
   - 基于AI检测结果的专业医学分析
   - 结合患者年龄、性别等信息的综合判断
   - 检测置信度的临床意义解释
   - 示例："基于AI检测结果，发现AVM（动静脉畸形）类型的血管瘤病变，置信度为93%。结合患者年龄和病变特征，符合典型的动静脉畸形表现。"

2. **治疗建议** (Treatment Suggestion):
   - 个性化的治疗方案推荐
   - 不同治疗方法的适应症说明
   - 治疗时机和优先级建议
   - 示例："若无明显症状或出血，可考虑保守治疗并定期观察。对于有症状或出血的AVM，建议考虑介入治疗或手术治疗。建议咨询血管外科专家制定个性化治疗方案。"

3. **预防措施** (Precautions):
   - 日常护理和注意事项
   - 预防并发症的具体措施
   - 生活方式调整建议
   - 示例："避免外伤，防止出血；破损时立即就医，避免感染；定期随访观察病变变化；避免剧烈运动和碰撞。"

4. **医疗免责声明** (Disclaimer):
   - 专业的医疗免责说明
   - AI辅助诊断的局限性说明
   - 专业医生诊断的重要性强调
   - 示例："此建议仅供参考，不能替代专业医生的诊断和治疗建议。请及时就医，由专业医生进行详细检查和最终诊断。"
 5.1.5 血管瘤AI诊断完整操作流程

  5.1.5.1 访问血管瘤诊断功能
1.  **通过工作台快捷入口**:
    -   在工作台主界面点击"血管瘤诊断"功能卡片
    -   卡片显示"使用AI辅助诊断血管瘤"的描述
    -   点击后直接进入血管瘤诊断页面

2.  **通过导航菜单访问**:
    -   在系统左侧导航栏中点击【血管瘤诊断】菜单项
    -   系统会跳转到血管瘤智能诊断页面

  5.1.5.2 步骤一：患者信息填写与图像上传
1.  **填写患者基本信息**（推荐填写以提高AI诊断准确性）:
    -   **患者年龄**: 在年龄输入框中输入患者实际年龄
        - 对血管瘤类型判断极为重要
        - 不同年龄段的血管瘤特征差异显著
    -   **性别选择**: 点击相应的单选按钮
        - 男性：某些血管瘤类型在男性中更常见
        - 女性：部分血管瘤类型有女性倾向
    -   **类型选择**: 选择血管瘤的起源类型
        - 先天性：出生时即存在的血管瘤
        - 后天性：后天形成的血管瘤
    -   **血管质地**: 从下拉菜单选择触诊感受
        - 影响AI对血管瘤类型的判断
        - 提供重要的临床体征信息

2.  **图像上传操作**:
    -   **上传方式一**: 点击上传区域的文件选择按钮
        - 在弹出的文件对话框中选择血管瘤图像
        - 支持常见医学影像格式
    -   **上传方式二**: 直接拖拽图像文件到上传区域
        - 更便捷的上传方式
        - 支持多种图像格式
    -   **文件要求**:
        - 支持格式：JPG、PNG、BMP等
        - 文件大小：最大不超过10MB
        - 图像质量：建议高清晰度以获得更好的AI分析效果

  5.1.5.3 步骤二：启动AI诊断分析
1.  **点击开始诊断**:
    -   确认患者信息填写完整后，点击页面底部的【开始诊断】按钮
    -   系统开始启动AI分析流程
    -   页面显示"正在分析中..."的状态提示

2.  **第一阶段：YOLO快速检测**（1-3秒）:
    -   **图像预处理**: 系统对上传的图像进行标准化处理
    -   **YOLO目标检测**: 使用训练好的YOLO模型进行快速检测
    -   **特征识别**: AI识别血管瘤的位置、大小、形态特征
    -   **类型分类**: 根据特征判断血管瘤的具体类型（支持15种类型）
    -   **置信度计算**: 为每个检测结果计算可信度评分
    -   **边界框生成**: 生成精确的检测边界框和标注

3.  **快速结果展示**:
    -   YOLO检测完成后，系统立即跳转到诊断结果页面
    -   显示完整的检测结果和AI标注框
    -   展示基本的诊断信息（类型、置信度、检测框等）
    -   显示"正在生成详细诊断建议..."的提示

4.  **第二阶段：LLM建议生成**（后台异步，30-60秒）:
    -   **数据整合**: 整合YOLO检测结果和患者信息
    -   **LLM分析**: 大语言模型基于检测结果生成专业建议
    -   **结构化输出**: 生成包含四个部分的结构化诊断报告
    -   **自动更新**: 完成后自动更新诊断结果页面的建议区域

  5.1.5.4 步骤三：查看AI诊断结果
1.  **YOLO检测结果查看**（立即可见）:
    -   **标注显示**: 在原始图像上显示红色矩形标注框
    -   **病灶定位**: 精确标出YOLO检测到的血管瘤区域
    -   **类型标签**: 在标注框附近显示检测类型（如IH、AVM等）
    -   **置信度显示**: 显示每个检测框的置信度评分
    -   **视觉确认**: 医生可直观查看AI检测的准确性

2.  **基本诊断信息确认**:
    -   **检测状态**: 确认显示"已检测到"状态
    -   **类型识别**: 查看AI识别的血管瘤类型（支持15种类型）
    -   **分类归属**: 确认属于三大类中的哪一类
    -   **置信度评估**: 查看AI对诊断结果的信心程度
    -   **检测数量**: 了解检测到的病灶数量
    -   **处理状态**: 查看LLM建议生成进度

3.  **等待LLM诊断建议**（30-60秒后自动更新）:
    -   **初始状态**: 显示"正在生成详细诊断建议..."
    -   **自动刷新**: 系统会自动更新显示完整建议
    -   **无需手动刷新**: 采用自动更新机制，用户无需操作

4.  **LLM诊断建议研读**（生成完成后）:
    -   **诊断总结**: 仔细阅读基于AI检测结果的专业医学分析
    -   **治疗建议**: 了解个性化的治疗方案推荐
        - 保守治疗的适应症和条件
        - 介入治疗的指征和时机
        - 专科医生咨询建议
    -   **预防措施**: 查看日常护理和预防注意事项
        - 生活方式调整建议
        - 预防并发症的具体措施
    -   **免责声明**: 了解AI辅助诊断的局限性
    -   **临床参考**: 将LLM建议作为临床决策的重要参考依据

5.  **结果保存与后续操作**:
    -   **自动保存**: 系统自动保存完整的AI诊断结果和LLM建议
    -   **诊断记录**: 可在工作台查看历史诊断记录
    -   **报告导出**: 可生成标准化的诊断报告
    -   **继续标注**: 可进入标注界面进行手动标注和分类
    -   **返回工作台**: 可返回工作台开始新的诊断

 5.1.6 病例编辑与标注功能
系统提供完整的病例编辑功能，支持对现有病例进行修改、标注编辑和信息更新。通过病例列表的【编辑】按钮可以进入编辑界面。

  5.1.6.1 进入编辑界面的方式
-   **从病例列表进入**: 在"我的病例标注"页面，点击状态为"未标注"或"已驳回"的病例的【编辑】按钮
-   **权限要求**: 只有病例创建者或具有相应权限的用户才能编辑病例
-   **状态限制**: 只有"未标注"和"已驳回"状态的病例可以编辑，"待审核"和"已通过"状态的病例只能查看

  5.1.6.2 病例编辑界面布局详解
病例编辑界面采用上下分区布局，提供完整的标注和信息编辑功能：

**A区 - 图像标注区域**（页面上半部分）：
-   **左侧工具栏**：
    -   **血管瘤分类系统**: 采用三大类分级选择
        - **主分类选择**: 真性血管肿瘤、血管畸形、血管假瘤/易混淆病变
        - **子分类选择**: 根据主分类动态显示对应的具体类型
        - **AI自动分类**: 如果有AI检测结果，系统会自动设置对应分类
        - **手动调整**: 用户可根据需要修改AI自动设置的分类
    -   **标注工具**: 提供矩形框等标注工具，默认选中矩形框工具
    -   **已添加标注列表**: 实时显示当前图像上的所有标注，包含编号、标签、坐标等信息
-   **右侧图像显示区**: 显示病例图像和标注框，支持标注的绘制、编辑和删除
    -   **AI检测框显示**: 如果有AI检测结果，会显示AI生成的检测框
    -   **手动标注**: 支持在AI检测基础上进行手动调整和补充

**B区 - 病例信息表单区域**（页面下半部分）：
-   **病例基础信息**: 显示病例编号、患者信息、创建时间等不可编辑信息
-   **详细信息表单**: 包含完整的病例信息填写表单，支持编辑修改
-   **血管瘤诊断建议**: 如果有AI诊断结果，会显示相关建议信息

 5.1.7 血管瘤三大类分类体系详解
系统采用国际标准的血管瘤分类方法，将血管瘤分为三大类，共计30种子类型，为临床诊断提供标准化的分类依据。

  5.1.7.1 真性血管肿瘤（15种类型）
这类血管瘤具有内皮细胞增殖的特点，多数在婴幼儿期出现，具有一定的生长周期。

**常见类型**：
-   **婴幼儿血管瘤 (IH)**: 最常见的血管瘤类型，多在出生后数周内出现
-   **先天性快速消退型血管瘤 (RICH)**: 出生时即存在，通常在1-2年内快速消退
-   **先天性部分消退型血管瘤 (PICH)**: 出生时存在，部分消退后保持稳定
-   **先天性不消退型血管瘤 (NICH)**: 出生时存在，不会自然消退
-   **卡波西型血管内皮细胞瘤 (KHE)**: 罕见但严重的血管瘤类型
-   **丛状血管瘤 (TA)**: 呈丛状分布的血管瘤
-   **化脓性肉芽肿 (PG)**: 快速生长的血管性病变

**其他类型**：梭形细胞血管瘤、上皮样血管内皮瘤、网状血管内皮瘤、假肌源性血管内皮瘤、多形性血管内皮瘤、血管肉瘤、上皮样血管肉瘤、卡波西肉瘤

  5.1.7.2 血管畸形（8种类型）
这类病变是血管发育异常导致的结构性畸形，通常在出生时即存在，不会自然消退。

**主要类型**：
-   **微静脉畸形 (MVM)**: 微小静脉的发育异常
-   **静脉畸形 (VM)**: 静脉系统的结构异常
-   **动静脉畸形 (AVM)**: 动脉和静脉之间的异常连接
-   **淋巴管畸形 (LM)**: 淋巴管系统的发育异常
-   **球细胞静脉畸形 (GVM)**: 含有球细胞的静脉畸形

**复合类型**：
-   **毛细血管-淋巴管-静脉畸形 (CLVM)**: 多种血管系统的复合畸形
-   **毛细血管-动静脉畸形 (CAVM)**: 毛细血管和动静脉的复合畸形
-   **淋巴管-静脉畸形 (LVM)**: 淋巴管和静脉的复合畸形

  5.1.7.3 血管假瘤/易混淆病变（6种类型）
这类病变在临床表现上可能与血管瘤相似，但病理机制不同，需要仔细鉴别诊断。

**主要类型**：
-   **血管外皮细胞瘤 (HPC)**: 起源于血管外皮细胞的肿瘤
-   **血管球瘤 (GT)**: 血管球细胞增生形成的良性肿瘤
-   **血管平滑肌瘤 (AL)**: 血管平滑肌细胞增生形成的肿瘤
-   **血管纤维瘤 (AF)**: 血管和纤维组织混合的良性肿瘤
-   **靶样含铁血黄素沉积性血管瘤 (THH)**: 含有铁血黄素沉积的特殊类型
-   **鞋钉样血管瘤 (HH)**: 形态特殊的血管性病变

  5.1.7.4 AI自动分类机制
系统的AI自动分类功能基于以上三大类分类体系：

1. **检测阶段**: YOLO模型识别血管瘤类型，返回类型缩写（如IH、AVM等）
2. **映射转换**: 系统将缩写转换为完整的中文名称
3. **分类匹配**: 在三大类体系中查找对应的主分类和子分类
4. **自动设置**: 在标注界面自动设置对应的分类选项
5. **用户确认**: 用户可根据需要调整AI自动设置的分类

**优势特点**：
-   **标准化**: 基于国际医学标准的分类体系
-   **全面性**: 覆盖30种常见血管瘤类型
-   **智能化**: AI自动识别和分类，提高效率
-   **灵活性**: 支持用户手动调整和修正
-   **一致性**: 确保诊断分类的标准化和一致性

  5.1.6.3 标注编辑操作详解
1. **新增标注**：
   - **选择标签**: 在左侧"标签分类"下拉菜单中选择合适的血管瘤类型标签
   - **绘制标注**: 确保矩形框工具已选中，在图像上按住鼠标左键拖动绘制矩形标注框
   - **自动添加**: 标注会自动添加到"已添加标注"列表中，显示编号、标签和坐标信息

2. **编辑现有标注**：
   - **移动标注**: 将鼠标移到标注框内部，光标变为四向箭头时拖动调整位置
   - **调整大小**: 将鼠标移到标注框边缘，光标变为双向箭头时拖动调整大小
   - **修改标签**: 在标注列表中点击对应标注，可修改其血管瘤类型标签
   - **删除标注**: 点击标注列表中对应行的删除按钮（垃圾桶图标）

3. **标注列表管理**：
   - **信息显示**: 列表显示所有标注的编号、标签、坐标等详细信息
   - **高亮联动**: 鼠标悬停在列表项上时，对应的图像标注框会高亮显示
   - **精确删除**: 支持单个标注的精确删除，不影响其他标注

  5.1.6.4 病例信息编辑
1. **基础信息查看**：
   - **病例编号**: CASE-XXX格式，系统自动生成，不可修改
   - **患者基本信息**: 显示患者年龄、性别等信息
   - **时间信息**: 显示创建时间和更新时间

2. **详细信息编辑**：
   - **病变部位**: 可选择或输入病变的具体部位
   - **血管瘤类型**: 根据标注结果选择相应的血管瘤类型
   - **临床表现**: 填写患者的临床症状和体征
   - **诊断建议**: 可编辑或补充AI生成的诊断建议

  5.1.6.5 编辑界面按钮功能与权限控制
编辑界面底部提供多个操作按钮，不同按钮对应不同的权限要求和状态变化：

1. **【保存草稿】按钮**：
   - **权限要求**: 病例创建者或具有编辑权限的用户
   - **适用状态**: 当前病例状态为"未标注"或"已驳回"
   - **操作结果**:
     - 保存当前编辑内容到数据库
     - 病例状态保持为"未标注"（如果是新建）或"已驳回"（如果是驳回后修改）
     - 用户可以稍后继续编辑
   - **界面反馈**: 显示"保存成功"提示，页面保持在编辑状态

2. **【提交审核】按钮**：
   - **权限要求**: 病例创建者或具有编辑权限的用户
   - **适用状态**: 当前病例状态为"未标注"或"已驳回"
   - **前置条件**: 必须完成必要的标注和信息填写
   - **操作结果**:
     - 病例状态从"未标注"或"已驳回"变更为"待审核"
     - 病例进入审核队列，审核员可以看到该病例
     - 提交后病例变为只读状态，创建者无法再编辑
   - **界面反馈**: 显示"提交成功"提示，自动跳转到病例列表页面

3. **【取消】按钮**：
   - **权限要求**: 所有用户
   - **操作结果**:
     - 放弃当前未保存的修改
     - 返回到病例列表页面
     - 不改变病例状态
   - **安全提示**: 如有未保存内容，会弹出确认对话框

  5.1.6.6 状态变化流程图
```
未标注 ──【保存草稿】──> 未标注
未标注 ──【提交审核】──> 待审核
已驳回 ──【保存草稿】──> 已驳回
已驳回 ──【提交审核】──> 待审核
待审核 ──【审核通过】──> 已通过 (仅审核员可操作)
待审核 ──【审核驳回】──> 已驳回 (仅审核员可操作)
```

 5.1.7 病例查看功能
对于"待审核"和"已通过"状态的病例，系统提供只读查看功能，用户可以查看完整的病例信息但无法进行修改。

  5.1.7.1 进入查看界面的方式
-   **从病例列表进入**: 点击状态为"待审核"或"已通过"的病例的【查看】按钮
-   **权限要求**: 病例创建者、审核员或管理员都可以查看
-   **界面特点**: 所有内容均为只读模式，无法进行任何编辑操作

  5.1.7.2 查看界面内容
1. **病变标注图像**：
   - 显示完整的病例图像和所有标注框
   - 标注框显示血管瘤类型标签和相关信息
   - 支持图像的缩放和平移查看

2. **病例详细信息**：
   - **基础信息**: 病例编号、患者信息、创建时间、更新时间
   - **标注信息**: 显示所有标注的详细信息列表
   - **诊断结果**: 血管瘤类型、部位、AI诊断建议等
   - **审核信息**: 如果已审核，显示审核员意见和审核时间

  5.1.7.3 查看界面按钮功能
1. **【返回】按钮**：
   - **权限要求**: 所有用户
   - **操作结果**: 返回到病例列表页面
   - **状态变化**: 无状态变化

2. **【打印/导出】按钮**（如果有）：
   - **权限要求**: 根据系统配置确定
   - **操作结果**: 生成病例报告的打印版本或导出文件

 5.2 病例审核模块 (审核员/管理员角色)
病例审核是系统质量控制的重要环节，由具有审核权限的用户对提交的病例进行专业审查。

  5.2.1 访问审核功能
-   **权限要求**: 用户角色必须为"审核员"或"管理员"
-   **访问方式**: 通过左侧导航栏的【标注审核】菜单项进入
-   **界面特点**: 显示所有"待审核"状态的病例列表

  5.2.2 审核操作流程
1. **选择待审核病例**：
   - 在审核列表中选择需要审核的病例
   - 点击【批阅】按钮进入详细审核页面

2. **审核内容检查**：
   - **标注质量**: 检查标注框的位置、大小是否准确
   - **标签正确性**: 验证血管瘤类型标签是否符合医学标准
   - **信息完整性**: 确认病例信息填写是否完整、准确
   - **AI建议合理性**: 评估AI诊断建议的专业性和合理性

3. **审核决定**：
   - **【通过】**: 病例状态变为"已通过"，完成整个流程
   - **【驳回】**: 病例状态变为"已驳回"，需填写驳回意见

  5.2.3 审核按钮权限与状态变化
| 操作按钮 | 权限要求 | 前置状态 | 结果状态 | 备注 |
|---------|---------|---------|---------|------|
| 【通过】 | 审核员/管理员 | 待审核 | 已通过 | 病例完成，不可再编辑 |
| 【驳回】 | 审核员/管理员 | 待审核 | 已驳回 | 需填写驳回意见，病例可重新编辑 |

 5.3 系统权限与状态控制总结

  5.3.1 完整的状态流转图
```
创建病例 ──> 未标注 ──【保存草稿】──> 未标注
                ↓
              【提交审核】
                ↓
             待审核 ──【审核通过】──> 已通过 (终态)
                ↓
              【审核驳回】
                ↓
             已驳回 ──【重新编辑】──> 未标注
```

  5.3.2 按钮权限控制矩阵
| 用户角色 | 病例状态 | 可见按钮 | 可执行操作 | 操作结果 |
|---------|---------|---------|-----------|---------|
| 医生(创建者) | 未标注 | 编辑、删除 | 编辑内容、保存草稿、提交审核、删除病例 | 状态可变为"待审核" |
| 医生(创建者) | 已驳回 | 编辑、删除 | 编辑内容、保存草稿、提交审核、删除病例 | 状态可变为"待审核" |
| 医生(创建者) | 待审核 | 查看 | 仅查看，无编辑权限 | 无状态变化 |
| 医生(创建者) | 已通过 | 查看 | 仅查看，无编辑权限 | 无状态变化 |
| 审核员/管理员 | 待审核 | 批阅 | 审核通过、审核驳回 | 状态变为"已通过"或"已驳回" |
| 审核员/管理员 | 其他状态 | 查看 | 仅查看 | 无状态变化 |

  5.3.3 重要权限说明
1. **编辑权限**: 仅病例创建者可编辑自己创建的病例
2. **状态限制**: 只有"未标注"和"已驳回"状态的病例可编辑
3. **审核权限**: 只有审核员和管理员可以审核病例
4. **删除权限**: 只有病例创建者可删除自己的病例，且仅限于"未标注"和"已驳回"状态
5. **查看权限**: 所有相关用户都可以查看病例，但编辑权限受限

在完成AI检测结果审查和标注调整后，需要完善详细的血管瘤诊断信息，形成完整的电子病历记录。

[截图 5.1.4-1: 血管瘤诊断信息表单区域，展示了专门针对血管瘤诊断的各项字段。]

1.  血管瘤诊断表单定位: 将页面向下滚动，找到"血管瘤诊断信息"表单区域。
2.  基于AI建议完善诊断: 结合AI生成的诊断建议和个人临床判断，完善各项诊断信息。

 5.1.4.1 血管瘤诊断表单的专业字段
-   **患者基本信息**:
    -   患者年龄: 精确到月龄（对婴幼儿血管瘤诊断尤为重要）
    -   性别: 某些血管瘤类型存在性别倾向性
    -   起源类型: 先天性/后天性（影响治疗方案选择）
    -   血管质地: 软/硬/有搏动等（重要的体征信息）

-   **血管瘤特征描述**:
    -   病变部位: 详细描述血管瘤的解剖位置（如"左侧面颊部浅表血管瘤"）
    -   病灶大小: 基于AI检测结果记录准确的尺寸信息
    -   病灶数量: 单发/多发
    -   颜色特征: 红色/紫红色/蓝色等
    -   表面特征: 光滑/粗糙/溃疡等

-   **AI辅助诊断结果**:
    -   AI检测类型: 系统自动填入AI识别的血管瘤类型
    -   置信度评分: 显示AI对诊断的置信度
    -   检测框数量: 记录AI检测到的病灶数量

-   **临床诊断与治疗**:
    -   最终诊断: 结合AI建议和临床判断的最终诊断结论
    -   诊断依据: 详细记录诊断的依据和理由
    -   治疗方案: 基于AI建议制定的个性化治疗计划
    -   预后评估: 对治疗效果和病程发展的预期

-   **随访计划**:
    -   随访时间: 根据血管瘤类型制定的随访计划
    -   观察要点: 需要重点观察的指标和变化
    -   家长教育: 对患者家属的健康教育内容

> 智能填写提示: 系统会根据AI检测结果和诊断建议自动预填部分字段，医生可在此基础上进行调整和完善。所有带有红色星号（*）的字段为必填项，确保诊断记录的完整性。
 5.1.5 步骤六：血管瘤诊断结果的保存与提交
在完成AI检测结果审查、标注调整和诊断信息填写后，可以选择保存草稿或提交审核。

[截图 5.1.5-1: 页面底部的血管瘤诊断操作栏，显示【保存草稿】和【提交审核】按钮。]

 5.1.5.1 功能：【保存草稿】- 血管瘤诊断进度保存
-   适用场景:
    -   血管瘤诊断复杂，需要进一步查阅资料或咨询同事。
    -   AI检测结果需要更多时间进行仔细分析和验证。
    -   患者信息不完整，需要补充更多临床资料。
    -   需要等待其他检查结果来辅助血管瘤诊断。
    -   临时中断工作，希望保存当前的诊断进度。
-   操作: 点击灰色的【保存草稿】按钮。
-   系统反馈:
    -   完整保存所有血管瘤诊断数据：AI检测结果、标注调整、诊断信息、治疗建议等。
    -   诊断状态标记为"草稿"，可继续编辑和完善。
    -   在"血管瘤诊断工作台"中可找到该草稿，点击【继续诊断】可恢复工作。
    -   AI生成的诊断建议和检测数据会完整保留，无需重新生成。

 5.1.5.2 功能：【提交审核】- 血管瘤诊断结果提交
-   适用场景: 血管瘤AI检测结果已经过仔细审查，标注调整完成，诊断信息填写完整，确信诊断结果达到审核标准。
-   操作: 点击蓝色的【提交审核】按钮。
-   系统反馈与确认流程:
    1.  **血管瘤诊断完整性校验**:
        -   验证AI检测结果是否已审查
        -   检查血管瘤类型分类是否明确
        -   确认必填的诊断信息是否完整
        -   验证治疗建议是否已制定
        -   如有缺失，系统会高亮显示需要补充的内容

    2.  **AI诊断质量评估**:
        -   系统会评估AI检测结果的置信度
        -   检查标注调整的合理性
        -   评估诊断结论与AI建议的一致性
        -   生成诊断质量评分供审核参考

    3.  **提交确认对话框**:
        -   显示血管瘤诊断摘要信息
        -   展示AI检测的主要结果
        -   确认提交后将进入审核流程，无法修改
        [截图 5.1.5-2: 血管瘤诊断提交确认对话框，显示诊断摘要。]

    4.  **最终提交反馈**:
        -   诊断状态更新为"待审核"
        -   推送至血管瘤诊断审核队列
        -   生成诊断报告预览
        -   返回"血管瘤诊断工作台"
        -   状态显示为"待审核"，仅可查看不可编辑
        -   完成一个完整的血管瘤AI辅助诊断流程

 5.1.6 血管瘤诊断完成后的修改与编辑
血管瘤诊断完成后，医生往往需要对诊断内容进行修改和完善。系统提供了两种便捷的修改路径，满足不同场景下的编辑需求。

 5.1.6.1 修改路径一：通过"病例标注"工作台进入编辑
这是最常用的修改路径，适用于需要系统性管理和查看所有病例的场景。

1.  **访问工作台**:
    -   点击左侧导航栏的【病例标注】菜单项
    -   进入病例管理工作台页面

2.  **定位目标病例**:
    -   在病例列表中找到需要修改的血管瘤诊断病例
    -   可以通过状态筛选（如【未标注】、【已驳回】等）快速定位
    -   病例列表显示诊断编号、患者信息、AI检测结果等关键信息

3.  **启动编辑操作**:
    -   点击目标病例操作栏中的【编辑】按钮
    -   系统会自动跳转到完整的血管瘤诊断编辑页面
    -   路径格式：`/app/case/{诊断ID}/annotate-and-form`

4.  **编辑模式特点**:
    -   保留原有的AI检测结果和标注信息
    -   可以重新调整AI标注的位置和类型
    -   可以修改诊断信息和治疗建议
    -   支持重新保存草稿或提交审核

 5.1.6.2 修改路径二：从血管瘤诊断结果页面直接跳转
这是最快捷的修改路径，适用于刚完成诊断后立即发现需要调整的场景。

1.  **诊断结果页面操作**:
    -   在血管瘤诊断完成后的结果展示页面
    -   页面显示AI检测结果、诊断建议和处理后的图像
    -   底部操作区域包含多个功能按钮

2.  **直接修改操作**:
    -   点击结果页面底部的【修改】按钮
    -   系统立即跳转到编辑页面，无需返回工作台
    -   跳转时携带编辑模式标识和来源页面信息

3.  **快速编辑优势**:
    -   无缝衔接，保持工作连续性
    -   自动保留当前诊断上下文
    -   支持快速调整后重新确认

 5.1.6.3 编辑页面的功能特性
无论通过哪种路径进入，编辑页面都提供完整的血管瘤诊断编辑功能：

1.  **AI检测结果编辑**:
    -   查看和调整AI生成的血管瘤标注框
    -   修改血管瘤类型分类
    -   删除误检的标注区域
    -   调整标注框的位置和大小

2.  **诊断信息完善**:
    -   修改患者基本信息（年龄、性别、起源类型等）
    -   完善血管瘤特征描述
    -   调整临床诊断和治疗方案
    -   更新随访计划和注意事项

3.  **AI建议重新生成**:
    -   标注调整后，AI会重新生成诊断建议
    -   可以对比修改前后的AI建议差异
    -   支持基于新的检测结果优化治疗方案

4.  **保存和提交选项**:
    -   【保存草稿】：保存修改进度，稍后继续编辑
    -   【提交审核】：完成修改后重新提交审核流程
    -   【返回】：取消修改，返回上一页面

 5.1.6.4 修改操作的最佳实践
1.  **及时修改**：发现问题时立即使用快速修改功能
2.  **系统管理**：定期通过工作台检查和完善历史病例
3.  **版本对比**：注意对比修改前后的AI建议变化
4.  **审核准备**：修改完成后确保信息完整再提交审核

> 注意事项：修改操作会触发AI重新分析，可能产生不同的诊断建议。建议医生仔细对比修改前后的差异，确保最终诊断的准确性。

 5.2 血管瘤诊断审核模块 (审核员/管理员角色)
此模块是血管瘤AI辅助诊断系统的质量控制核心，专门用于审核医生基于AI检测结果完成的血管瘤诊断。审核员将重点评估AI检测结果的应用、标注调整的合理性以及最终诊断的准确性。

操作场景: 审核员或管理员需要对医生提交的"血管瘤AI诊断结果"进行专业审查和质量控制。

 5.2.1 访问"血管瘤诊断审核"工作台
1.  前置条件: 您已使用拥有"审核员"或"管理员"权限的账号成功登录血管瘤诊断系统。
2.  界面定位: 在系统左侧主导航栏中找到血管瘤诊断相关菜单。
3.  用户操作: 点击【标注审核】菜单项。
4.  系统响应: 进入"血管瘤诊断审核"工作台，显示所有待审核的血管瘤AI诊断病例列表。

[截图 5.2.1-1: "血管瘤诊断审核"工作台页面，显示待审核的AI辅助诊断病例列表，包含AI检测信息和诊断状态。]

 5.2.1.1 "血管瘤诊断审核"工作台界面元素详解
血管瘤诊断审核工作台专门针对AI辅助诊断的特点进行了优化设计，重点展示AI检测结果和医生的诊断决策。

-   A区 - 血管瘤诊断审核列表:
    -   功能: 展示所有待审核的血管瘤AI诊断病例，包含AI检测的关键信息和医生的诊断结论。
    -   列表列项: 专门针对血管瘤诊断设计的字段：
        -   诊断编号: 血管瘤诊断的唯一标识符
        -   患者信息: 年龄、性别等关键信息（血管瘤诊断的重要因素）
        -   AI检测结果: 显示AI识别的血管瘤类型和置信度
        -   检测框数量: AI检测到的血管瘤病灶数量
        -   诊断医生: 完成AI结果审查和诊断的医生
        -   提交时间: 诊断完成并提交审核的时间
        -   诊断状态: 显示为"待审核"
        -   操作栏: 【审核诊断】按钮，进入详细的血管瘤诊断审核页面

-   B区 - 血管瘤诊断筛选工具:
    -   AI检测类型筛选: 按血管瘤类型（IH、RICH、KHE等）筛选病例
    -   置信度筛选: 按AI检测置信度范围筛选
    -   诊断医生筛选: 按提交诊断的医生筛选
    -   时间范围筛选: 按诊断提交时间筛选

 5.2.2 步骤二：血管瘤诊断审核
1.  前置条件: 已成功访问"血管瘤诊断审核"工作台，且列表中有待审核的AI诊断病例。
2.  选择审核病例: 在审核列表中浏览血管瘤诊断病例，重点关注AI检测结果和置信度信息。
3.  启动审核: 点击目标病例的【审核诊断】按钮。
4.  系统响应: 进入血管瘤诊断审核详情页面，该页面包含：
    -   **AI检测结果展示区**: 显示原始AI检测的血管瘤标注和分类结果
    -   **医生调整记录区**: 显示医生对AI结果的调整和修改记录
    -   **诊断信息区**: 显示最终的血管瘤诊断信息和治疗建议
    -   **AI建议对比区**: 对比AI生成的建议与医生的最终诊断
    -   **审核操作区**: 位于页面底部的审核决策区域

[截图 5.2.2-1: 血管瘤诊断审核详情页，展示AI检测结果、医生调整、诊断信息和审核操作区域。]

 5.2.3 步骤三：血管瘤AI诊断结果的专业审核
在血管瘤诊断审核页面，审核员需要从AI应用、医学准确性和诊断质量三个维度进行综合评估。

血管瘤诊断审核要点:
1.  **AI检测结果评估**:
    -   **AI标注准确性**: 检查AI生成的血管瘤标注框是否准确覆盖病灶区域
    -   **类型识别正确性**: 验证AI识别的血管瘤类型（IH、RICH、KHE等）是否符合医学标准
    -   **置信度合理性**: 评估AI置信度评分是否与实际病灶特征相符
    -   **漏检误检分析**: 检查是否存在AI漏检的病灶或误检的区域

2.  **医生调整的合理性审查**:
    -   **标注调整评估**: 检查医生对AI标注的调整是否合理和必要
    -   **类型修正验证**: 验证医生对AI识别类型的修正是否正确
    -   **删除标注审查**: 评估医生删除AI标注的决策是否恰当
    -   **调整依据分析**: 检查医生调整的医学依据是否充分

3.  **最终诊断质量评估**:
    -   **诊断准确性**: 评估最终的血管瘤诊断是否准确和完整
    -   **治疗建议合理性**: 检查治疗方案是否符合血管瘤诊疗指南
    -   **AI建议应用**: 评估医生是否合理参考了AI生成的诊断建议
    -   **信息完整性**: 确认所有必要的诊断信息都已完整填写

4.  **AI辅助诊断效果评估**:
    -   **AI应用效果**: 评估AI检测对诊断效率和准确性的提升作用
    -   **人机协作质量**: 分析医生与AI系统协作的效果
    -   **诊断一致性**: 检查最终诊断与AI建议的一致性和差异原因

5.  **审核决策准备**: 完成上述评估后，滚动到页面底部的"血管瘤诊断审核"操作区域。

[截图 5.2.3-1: 血管瘤诊断审核操作区域，包含AI评估摘要、审核意见输入框和审核决策按钮。]

 5.2.3.1 功能：血管瘤诊断审核意见
-   功能描述: 专门针对血管瘤AI辅助诊断的审核意见输入框，支持对AI应用效果、诊断准确性和医生决策的专业评价。
-   审核意见内容建议:
    -   **AI检测评价**: 对AI血管瘤检测结果的准确性和实用性进行评价
    -   **医生调整评估**: 评价医生对AI结果调整的合理性和专业性
    -   **诊断质量评价**: 对最终血管瘤诊断的准确性和完整性进行评估
    -   **改进建议**: 针对AI应用或诊断过程提出具体的改进建议
-   最佳实践: 审核意见应体现对AI辅助诊断效果的专业判断，有助于提升血管瘤诊断质量和AI应用水平。

 5.2.3.2 功能：【通过】- 血管瘤诊断审核通过
-   适用场景: 血管瘤AI检测结果应用合理，医生调整恰当，最终诊断准确完整，符合血管瘤诊疗标准。
-   操作: 填写审核意见后，点击蓝色的【通过】按钮。
-   系统反馈:
    -   成功提示: "血管瘤诊断已通过审核"
    -   状态更新: 诊断状态从"待审核"变为"已通过"
    -   数据归档: 诊断结果正式归档，可用于医学研究和AI模型优化
    -   质量统计: 更新AI辅助诊断的质量统计数据
    -   医生反馈: 原诊断医生可查看审核结果和专业意见

 5.2.3.3 功能：【驳回】- 血管瘤诊断审核驳回
-   适用场景: 发现AI检测结果应用不当、医生调整不合理、诊断信息不完整或存在医学错误等问题。
-   常见驳回原因:
    -   AI检测结果未得到充分验证或错误应用
    -   血管瘤类型分类错误或不够准确
    -   标注调整缺乏医学依据或过度修改
    -   诊断信息不完整或治疗建议不合理
    -   未合理参考AI生成的诊断建议
-   操作: 在审核意见中详细说明驳回原因和改进要求，然后点击红色的【驳回】按钮。
-   系统反馈:
    -   成功提示: "血管瘤诊断已驳回，请查看审核意见"
    -   状态更新: 诊断状态变为"已驳回"
    -   退回处理: 诊断退回给原医生，可重新编辑和提交
    -   学习机会: 为医生提供AI应用和血管瘤诊断的学习改进机会

5.3 后台管理模块 (管理员角色)
后台管理模块是系统的最高权限区域，仅供拥有"管理员 (ADMIN)"角色的用户访问和操作。它提供了对系统用户、团队以及所有核心数据的全局管理能力，确保系统的稳定运行和数据安全。
操作场景: 系统管理员进行日常的用户维护、团队管理或数据核查。
 5.3.1 访问后台管理控制台
1.  前置条件: 您已使用拥有"管理员"权限的账号成功登录本系统。
2.  界面定位: 将您的目光移至系统界面的左侧主导航栏。
3.  用户操作: 在导航栏中，找到并用鼠标左键单击内容为 【管理员控制台】 的菜单项（或类似的命名，如【系统管理】）。
4.  系统响应: 页面内容会立即切换到 "管理员控制台" 页面。此页面通常包含多个标签页（Tabs），分别对应不同的管理子模块，例如"用户管理"、"团队管理"、"图像管理"等。

[截图 5.3.1-1: "管理员控制台"页面完整视图。顶部有多个标签页（如"用户管理"、"团队管理"），当前显示的是用户管理列表。]

 5.3.2 用户管理
此子模块是管理员维护系统用户账号的核心区域，能够对系统中所有用户的基本信息、角色和状态进行全面管理。

1.  访问: 在"管理员控制台"页面，点击 【用户管理】 标签页。
2.  界面元素与功能:
    -   用户列表: 以表格形式展示系统中所有注册用户的详细信息，包括用户ID、姓名、邮箱、当前角色、注册时间、状态（启用/禁用）等。
    -   搜索框: 通常位于用户列表上方，允许管理员通过用户的姓名、邮箱或ID进行快速搜索，以定位特定用户。
    -   操作栏: 用户列表的每一行末尾会提供针对该用户的管理操作按钮，例如：
        -   【编辑】: 点击后弹出用户编辑对话框，可修改用户的姓名、医院、科室等基本信息。
        -   【修改角色】: 这是一个关键功能。管理员可直接在此更改用户的角色，例如将某位普通"医生"提升为"审核员"，或将"审核员"降级为"医生"。通常会通过一个下拉菜单或单选框选择新角色。
        -   【禁用/激活】: 管理员可以临时冻结或解冻某个用户的登录权限。被禁用的用户将无法登录系统。这在处理异常账户时非常有用。
        -   【删除】: 极端操作，点击后会弹出二次确认对话框，确认后将永久删除用户账户及其所有相关数据。操作不可逆，需极其谨慎。
    -   权限审批入口: 在某些设计中，可能会有一个独立的区域或按钮，用于处理医生角色提交的"申请成为审核员"的申请。管理员可在此查看申请列表，并选择【批准】或【拒绝】这些申请。

[截图 5.3.2-1: 用户管理界面的详细视图，展示了用户列表、搜索框、以及用户行末尾的编辑、修改角色、禁用/激活等按钮。]

 5.3.3 团队管理
此子模块允许管理员创建、修改、解散医疗团队，并管理团队内部的成员。

1.  访问: 在"管理员控制台"页面，点击 【团队管理】 标签页。
2.  界面元素与功能:
    -   团队列表: 以表格形式展示系统中所有已创建的医疗团队信息，包括团队ID、团队名称、描述、创建者、成员数量等。
    -   【创建团队】按钮: 通常位于团队列表上方，点击后弹出"创建新团队"对话框，管理员可在此输入团队名称和描述来建立一个新团队。
    -   搜索框: 用于按团队名称或描述进行快速搜索。
    -   操作栏: 团队列表的每一行末尾会提供针对该团队的管理操作按钮，例如：
        -   【编辑】: 点击后弹出"编辑团队信息"对话框，可修改团队的名称、描述。
        -   【管理成员】: 这是一个重要的入口。点击后会进入该团队的成员管理页面，管理员可在此将系统中的现有用户添加到团队，或将团队中的成员移除。通常以列表形式显示成员，并提供添加/移除按钮。
        -   【解散团队】: 极端操作，点击后会弹出二次确认对话框，确认后将永久删除该团队及其所有关联关系。操作不可逆，请务必谨慎。

[截图 5.3.3-1: 团队管理界面的详细视图，展示了团队列表、【创建团队】按钮、以及团队行末尾的编辑、管理成员、解散团队等按钮。]

 5.3.4 图像管理
此子模块允许管理员查看并管理系统中所有用户上传的原始医学图像、处理后的图像以及标注后的图像文件。这对于系统维护和存储空间管理非常重要。

1.  访问: 在"管理员控制台"页面，点击 【图像管理】 标签页。
2.  界面元素与功能:
    -   图像列表: 以缩略图或列表形式展示系统中所有已上传的图像文件，并显示其关键元数据，如文件名、上传者、上传时间、文件大小、图像尺寸等。
    -   筛选/搜索: 提供多种筛选条件（如按上传者、图像类型、时间范围）和搜索功能（按文件名），方便管理员快速定位特定图像。
    -   【查看详情】: 点击图像缩略图或详情按钮，可查看图像的原始大小、元数据，甚至可能关联到该图像的所有标注信息。
    -   【删除】: 极端操作，点击后会弹出二次确认对话框，确认后将永久删除该图像文件及其在系统中的所有相关记录。此操作不可逆，请务必谨慎。

[截图 5.3.4-1: 图像管理界面的详细视图，展示了图像缩略图列表、筛选/搜索功能、以及每张图像对应的管理操作。]



 
 6. 故障与排除 (FAQ)
本章节提供了用户在使用"血管瘤人工智能辅助治疗系统"过程中可能会遇到的一些常见问题、它们可能的原因以及详细的解决方案。当您遇到操作上的困惑或系统提示错误信息时，建议首先查阅本章节。

-   Q1: 无法登录系统，提示"用户名或密码错误"，该怎么办？
    -   可能的原因:
        1.  您输入的邮箱地址或密码有误。这可能是最常见的原因，通常是由于打字失误、忘记密码或密码被更改。
        2.  密码输入时键盘大写锁定（Caps Lock）键意外开启，导致大小写不匹配。
        3.  您使用的输入法是中文全角状态，导致输入了非英文字符或全角标点。
        4.  邮箱地址前后有多余的不可见空格。
        5.  您的账户可能因多次错误尝试被暂时锁定，或被系统管理员禁用。
    -   建议解决方案:
        1.  仔细检查输入内容: 在重新尝试登录前，请务必仔细核对您输入的邮箱地址和密码。建议先在一个文本编辑器（如记事本）中输入密码，确认无误后再复制粘贴到密码输入框中。
        2.  检查键盘状态: 确保键盘上的Caps Lock键处于关闭状态。同时，确认输入法为英文半角模式。
        3.  尝试找回密码: 如果您确认输入无误但仍无法登录，很可能是您记错了密码。请立即使用登录页面上的 【忘记密码?】 功能进行密码重置（详见 4.4 密码找回）。
        4.  联系系统管理员: 如果以上所有方法都无法解决问题，请及时联系您的系统管理员。他们可以帮助您检查账户状态、重置密码，或提供进一步的技术支持。

-   Q2: 注册时提示"该邮箱已被注册"怎么办？
    -   可能的原因:
        1.  您之前可能已经使用此邮箱地址在本系统中注册过账号，但您忘记了。
        2.  其他人（例如您的同事或家人）可能使用您的邮箱地址或类似的地址进行了注册。
    -   建议解决方案:
        1.  尝试直接登录: 请先回忆一下您是否曾注册过，并直接前往登录页面，使用该邮箱地址尝试登录。
        2.  使用新邮箱注册: 如果您确定不是自己注册的，或者您需要使用一个全新的身份，请更换一个您本人拥有且尚未在本系统注册过的电子邮箱地址来完成新用户的注册流程。

-   Q3: AI诊断结果不准确或没有检测到血管瘤，该怎么办？
    -   可能的原因:
        1.  上传的图像质量不佳，如模糊、光线不足、角度不当等。
        2.  血管瘤特征不明显，或属于AI模型训练数据中较少见的类型。
        3.  图像中包含多个病灶，AI可能只检测到部分区域。
        4.  患者信息填写不完整，影响了AI的判断准确性。
    -   建议解决方案:
        1.  重新上传高质量图像: 确保图像清晰、光线充足、角度适当，病灶区域清晰可见。
        2.  完善患者信息: 确保年龄、性别、类型等关键信息填写准确完整。
        3.  尝试多角度拍摄: 如果可能，从不同角度拍摄多张图像进行诊断。
        4.  联系专业医生: 对于AI无法准确识别的复杂病例，建议咨询血管瘤专科医生。

-   Q4: AI诊断建议与我的临床判断不一致，应该如何处理？
    -   可能的原因:
        1.  AI模型基于大数据训练，可能与个体病例的特殊情况存在差异。
        2.  患者的具体病史、症状等信息AI无法完全获取。
        3.  AI建议是基于图像特征的初步判断，需要结合临床经验。
    -   建议解决方案:
        1.  综合判断: 将AI建议作为参考，结合自己的临床经验和患者的具体情况进行综合判断。
        2.  二次确认: 可以重新检查患者信息填写是否准确，或尝试上传更清晰的图像。
        3.  专家会诊: 对于疑难病例，建议进行多学科会诊或咨询血管瘤专家。
        4.  记录差异: 在病例记录中注明AI建议与临床判断的差异及原因，有助于后续研究和改进。

-   Q5: 为什么我看不到【编辑】按钮，只能看到【查看】按钮？
    -   可能的原因:
        1.  病例当前状态为"待审核"或"已通过"，这些状态下的病例不允许编辑。
        2.  您不是该病例的创建者，没有编辑权限。
        3.  病例已经提交审核，在审核过程中无法编辑。
    -   建议解决方案:
        1.  检查病例状态: 只有"未标注"和"已驳回"状态的病例才显示【编辑】按钮。
        2.  确认权限: 只有病例创建者才能编辑自己创建的病例。
        3.  等待审核结果: 如果病例被驳回，会重新显示【编辑】按钮供您修改。
        4.  联系管理员: 如果确实需要修改已通过的病例，请联系系统管理员。

-   Q6: 编辑病例时，为什么【提交审核】按钮是灰色的，无法点击？
    -   可能的原因:
        1.  必填的病例信息尚未完整填写。
        2.  图像上没有添加任何标注。
        3.  标注信息不完整，如缺少血管瘤类型标签。
    -   建议解决方案:
        1.  检查表单: 确保所有标记为必填的字段都已填写完整。
        2.  添加标注: 在图像上至少添加一个有效的标注框。
        3.  完善标注: 确保每个标注都有正确的血管瘤类型标签。
        4.  保存草稿: 可以先点击【保存草稿】保存当前进度，稍后继续完善。

-   Q7: 标注时画错了框，或者框选区域不准确，如何删除或修改？
    -   可能的原因: 在图像标注过程中，由于鼠标操作失误或对病灶范围判断不准，导致绘制的标注框有误。
    -   建议解决方案:
        -   删除错误的标注框: 您完全不需要为此烦恼或重新开始整个标注。在"图像标注与表单填写"界面的左侧，有一个 "已添加标注"列表（详见 5.1.2.2 节 C区）。您画的每一个标注框都在那里有一条对应的记录。找到您画错或不想要的那个标注，点击该记录最右侧的 【删除】 图标按钮（通常是一个垃圾桶的标志）。点击后，那个错误的标注框就会立刻从图像上被移除。
        -   微调已有的标注框: 如果您想调整已经画好的标注框的大小或位置，可以参考 5.1.3.4 步骤四：编辑已绘制的标注框 的详细说明。系统支持直接拖动框体移动位置，或拖动边缘/角落调整大小。

-   Q4: 我想微调一下已经画好的标注框的大小或位置，可以吗？
    -   可能的原因: 绘制的标注框不够精确，需要进行细节上的调整。
    -   建议解决方案: 是的，可以。本系统提供了灵活的编辑功能。请参考 5.1.3.4 步骤四：编辑已绘制的标注框 的详细说明。简而言之，将鼠标移动到框体内部拖动可移动位置，移动到边缘或角落拖动可调整大小。

-   Q5: 病例的"状态"都有什么含义？为什么我的病例状态变了就不能编辑了？
    -   可能的原因: 对病例在工作流中的不同状态含义不了解，以及不同状态下权限的变化。
    -   建议解决方案: 病例的状态代表了它在整个工作流中的当前位置，不同状态对应不同的可操作权限。具体含义如下（详见 5.1.1.1 节 B区：状态筛选器）：
        -   未标注: 这是您的草稿状态。您可以对其进行任何编辑、修改，甚至删除。当您点击【保存草稿】时，病例会保持此状态。
        -   待审核: 您已确认完成并点击【提交审核】后的状态。此时病例正在等待审核员的批阅。为确保审核流程的严谨性，在此状态下，您不能再对病例进行任何修改（只能【查看】）。
        -   已驳回: 审核员认为病例存在问题并将其退回给您的状态。您可以根据审核意见，再次点击【编辑】按钮进行修改，修改完成后可以再次【提交审核】。
        -   已通过: 审核流程圆满结束，该病例已由审核员审核通过并归档。此状态下的病例是最终成果，通常您只能查看，无法再做任何修改，以保证数据的最终性。

-   Q6: 为什么我的导航栏里没有"标注审核"和"管理员控制台"这两个菜单？
    -   可能的原因: 您当前登录的用户账号权限不足以访问这些功能模块。
    -   建议解决方案: 这是由系统的权限控制机制决定的。这两个功能模块分别需要"审核员 (REVIEWER)"和"管理员 (ADMIN)"角色才能访问。如果您当前是以普通"医生 (DOCTOR)"角色登录的，那么看不到这些菜单是正常的。如果您确实因为工作需要这些权限，可以向系统管理员提出申请（请参考 Q7）。

-   Q7: 我想成为审核医生，应该如何操作？
    -   可能的原因: 普通医生用户希望承担更多职责，参与病例审核工作。
    -   建议解决方案: 本系统设计了权限升级申请的功能。通常您可以在"个人中心"、"用户管理"或类似的个人设置页面中找到一个 【申请成为审核员】 的按钮或入口。点击后提交您的申请，然后等待系统管理员的审批即可。一旦管理员批准了您的申请，您的账户角色将被提升，并获得相应的"标注审核"权限。

-   Q8: 上传图像时失败或长时间没有反应，可能是什么原因？
    -   可能的原因:
        1.  网络连接问题: 您的计算机网络连接不稳定或已中断。
        2.  图像文件格式不兼容: 您上传的图像文件不是系统支持的常见格式（如`.jpg`, `.jpeg`, `.png`）。
        3.  图像文件过大: 您上传的单张图像文件大小超过了系统配置的上传上限。
        4.  服务器端问题: 后端服务器异常或存储空间不足。
    -   建议解决方案:
        1.  检查网络: 确保您的计算机网络连接正常，可以尝试访问其他网站测试网络连通性。如果网络不稳定，请尝试切换网络环境。
        2.  检查文件格式: 确认图像文件格式。如果不是支持的格式，请使用图像编辑工具将其转换为`.jpg`或`.png`格式。
        3.  压缩图像文件: 如果图像文件过大（通常超过20MB），请尝试使用图像处理软件（如Photoshop、在线图片压缩工具）在不严重影响清晰度的前提下适当压缩图像文件大小，然后再重新上传。
        4.  联系管理员: 如果排查了以上所有问题仍无法解决，请将具体的错误提示和操作过程告知系统管理员。
 7. 术语表
本章节旨在提供"血管瘤人工智能辅助治疗系统"操作手册中所有专业术语、缩写和特定概念的清晰定义。这将帮助用户更好地理解文档内容，并确保在团队协作和交流中，大家对核心概念的理解保持一致。

| 术语/缩写 | 中文全称/解释 | 详细说明与系统上下文关联 |
| :--- | :--- | :--- |
| AI | Artificial Intelligence (人工智能) | 在本系统中，特指用于血管瘤辅助诊断的深度学习模型，能够对医学影像进行智能分析，自动识别血管瘤病灶并提供诊断建议。 |
| AI诊断建议 | AI生成的诊断建议 | 系统基于大语言模型技术，结合AI检测结果和患者信息，自动生成的专业治疗方案、随访建议和注意事项。 |
| 置信度 | AI置信度评分 | AI模型对其检测结果可靠性的评估，通常以百分比形式显示（如93%），数值越高表示AI对该诊断结果越有信心。 |
| 工作台 | 系统主界面 | 用户登录后的主要工作界面，提供数据统计概览、快捷功能入口和工作进度展示。 |
| 病例状态 | 病例当前所处的流程阶段 | 包括未标注、已驳回、待审核、已通过四种状态，决定了用户可以执行的操作。 |
| 编辑权限 | 病例编辑的权限控制 | 只有病例创建者可以编辑自己创建的病例，且仅限于未标注和已驳回状态。 |
| 标注框 | 图像上的矩形标记区域 | 用于标识血管瘤病灶的位置和范围，包含类型标签和坐标信息。 |
| B/S架构 | Browser/Server (浏览器/服务器架构) | 一种软件系统架构模式。用户通过标准网页浏览器（客户端）访问应用程序，而无需在本地安装额外的软件。所有业务逻辑和数据存储都在服务器端完成。本系统采用此架构，确保了便捷性和跨平台兼容性。 |
| UI | User Interface (用户界面) | 指用户与软件进行交互的图形化界面。本系统的UI设计注重直观性和易用性，确保医生能够高效完成标注和管理任务。 |
| FAQ | Frequently Asked Questions (常见问题解答) | 文档中专门用于回答用户在使用软件过程中可能遇到的常见操作疑问和技术问题的章节。 |
| 病例 | 病例 | 在本系统中，指一个完整的、结构化的患者诊断记录单元。它包含患者的基本信息、医学影像文件、医生绘制的标注、填写的详细表单（诊断摘要、治疗建议等），以及最终的审核结果。是系统核心的数据实体。 |
| 标注 | 图像标注 | 指医生在医学影像（如图片）上，使用系统提供的绘图工具（如矩形框、圆形等），精确地圈定病灶区域，并为其赋予特定的医学分类标签（如`IH-婴幼儿血管瘤`）的过程。这是AI模型学习和系统诊断结果可视化的基础。 |
| 标签 | 诊断标签 / 分类标签 | 指用于分类和识别病灶类型的医学术语。在标注过程中，医生会从预设的标签列表中选择，例如"婴幼儿血管瘤"、"静脉畸形"等。这些标签是系统进行统计、检索和AI诊断的关键数据。 |
| 工作台 | 工作台 / Workbench | 指为特定用户角色（如医生、审核员）设计的主功能聚合页面。它集中显示了该角色最常需要处理的任务列表、状态概览和快速操作入口，旨在提供一个高效、便捷的日常工作环境。例如"我的病例标注"工作台和"标注审核"工作台。 |
| 角色 | 用户角色 | 系统中用于定义用户权限和可访问功能集合的身份标识。本系统主要有：医生 (DOCTOR)、审核员 (REVIEWER) 和管理员 (ADMIN)。不同角色对应不同的操作权限，确保数据安全和流程规范。 |
| 权限 | 操作权限 | 指特定用户角色被系统允许执行的特定操作集合。例如，只有审核员才能进行"批阅"操作，只有管理员才能进行"用户管理"操作。 |
| IH | Infantile Hemangioma (婴幼儿血管瘤) | 一种常见的血管瘤分类的医学缩写。在系统标注标签中出现。 |
| RICH | Rapidly Involuting Congenital Hemangioma (先天性快速消退型血管瘤) | 一种先天性血管瘤分类的医学缩写。在系统标注标签中出现。 |
| PICH | Partially Involuting Congenital Hemangioma (先天性部分消退型血管瘤) | 一种先天性血管瘤分类的医学缩写。在系统标注标签中出现。 |
| NICH | Non-Involuting Congenital Hemangioma (先天性不消退型血管瘤) | 一种先天性血管瘤分类的医学缩写。在系统标注标签中出现。 |
| KHE | Kaposiform Hemangioendothelioma (卡泊西型血管内皮细胞瘤) | 一种血管内皮细胞瘤的医学缩写。在系统标注标签中出现。 |
| VM | Venous Malformation (静脉畸形) | 一种脉管畸形分类的医学缩写。在系统标注标签中出现。 |
| AVM | Arteriovenous Malformation (动静脉畸形) | 一种脉管畸形分类的医学缩写。在系统标注标签中出现。 |
 8. 附录A：场景演练
本章节旨在通过一个完整、端到端的操作场景，指导用户亲身体验"血管瘤人工智能辅助治疗系统"的核心功能。通过本演练，用户将直观了解从创建新病例到最终提交审核的每一个环节，加深对系统操作流程的理解。

 8.1 演练目标
完成一个模拟的"左侧面颊部婴幼儿血管瘤"病例的创建、图像上传与专业标注、详细信息表单填写，并最终成功提交审核的全过程。此演练假设用户已拥有"医生"角色。

 8.2 演练步骤
1.  步骤一：登录系统
    -   操作: 参照 4.3 用户登录详解 章节，使用您分配到的"医生"账号（邮箱和密码）成功登录本系统。请确保登录后进入"我的病例标注"工作台。
    -   预期结果: 页面成功跳转至"我的病例标注"工作台主界面。

2.  步骤二：新建病例
    -   前置条件: 您已位于"我的病例标注"工作台页面。
    -   操作: 在工作台页面的右上角区域，找到并用鼠标左键单击醒目的 【新建标注】 按钮。
    -   预期结果: 系统页面将跳转至"图像标注与表单填写"界面，上方图像显示区将为空白，下方病例信息表单为空白，等待您进行数据录入。

3.  步骤三：上传待标注的医学影像
    -   前置条件: 您已位于"图像标注与表单填写"界面。
    -   操作: 参照 5.1.3.1 步骤一：上传待标注的医学影像 章节，在右侧图像显示区的上传提示区域单击，并在弹出的文件选择对话框中，选择一张模拟的患者左侧面颊部的血管瘤照片文件（例如 `patient_face_hemangioma.jpg`）。点击【打开】上传。
    -   预期结果: 图像文件成功上传，并完整显示在右侧的图像画布区域中。同时，系统可能会在左侧工具栏的"已添加标注"列表中显示AI的初步检测结果（如果有集成AI功能）。

4.  步骤四：选择诊断标签
    -   前置条件: 图像已成功加载，且您已准备进行首次标注。
    -   操作: 参照 5.1.3.2 步骤二：选择诊断标签 章节，在左侧工具栏的 【标签分类】 下拉菜单处用鼠标左键单击，然后从展开的列表中选择最符合当前图像病灶特征的标签，例如选择 `IH-婴幼儿血管瘤`。
    -   预期结果: 您选择的标签（`IH-婴幼儿血管瘤`）显示在下拉菜单框中，表示当前标注工具已绑定此标签。

5.  步骤五：使用矩形框工具进行标注
    -   前置条件: 已选择正确的诊断标签。
    -   操作: 参照 5.1.3.3 步骤三：使用矩形框工具进行标注 章节，将鼠标光标移动到右侧图像画布中病灶的左上角，按住鼠标左键不放，并拖动鼠标至病灶的右下角，精确地框出照片中的血管瘤区域。松开鼠标左键完成绘制。
    -   预期结果: 一个带有 `IH-婴幼儿血管瘤` 标签的清晰矩形框将固定显示在图像上。同时，左侧的"已添加标注"列表中会新增一条对应的记录。如果您愿意，可以重复此步骤，对图像上的其他病灶进行标注。

6.  步骤六：填写详细的病例信息表单
    -   前置条件: 已完成图像标注工作。
    -   操作: 将页面向下滚动，参照 5.1.4 步骤五：填写详细的病例信息表单 章节，根据模拟的病例信息，在下方的表单中逐项、完整、准确地填写所有必填字段（带红色星号的项目）。
        -   患者年龄: `1` (输入数字1，表示1岁)
        -   性别: 选择"女"
        -   病变部位: `左侧面颊部` (输入文本)
        -   诊断摘要: `患儿1岁，女性。左侧面颊部可见一约2cm x 3cm红色斑块，边界清晰，表面皮温略高，初步诊断为婴幼儿血管瘤（增殖期）。` (输入详细诊断描述)
        -   治疗建议: `建议密切观察，必要时进行口服普萘洛尔治疗。` (输入具体治疗建议)
        -   ... (确保填写所有其他必填项)
    -   预期结果: 所有必填字段均已填充有效信息，不再有任何红色错误提示。

7.  步骤七：提交审核
    -   前置条件: 所有图像标注和表单信息均已准确无误地完成。
    -   操作: 参照 5.1.5.2 功能：【提交审核】 章节，将页面滚动到最底部，找到并用鼠标左键单击蓝色的 【提交审核】 按钮。系统会弹出一个二次确认对话框，请在对话框中点击 【确定】 按钮。
    -   预期结果: 系统将显示"提交成功"的提示，并自动跳转回"我的病例标注"工作台页面。在列表中，您会看到刚刚创建的病例其"当前状态"已从"未标注"变为"待审核"，并且其操作按钮已变为只读的【查看】。至此，一个完整的病例创建、标注和提交流程演练圆满完成。

---

 9. 附录B：常见错误与提示信息
本附录汇总了"血管瘤人工智能辅助治疗系统"在用户操作过程中，系统可能出现的各类错误提示信息及其详尽的解释与推荐的解决方案。当您在操作界面上看到任何系统提示时，都可以在此快速查找并获得帮助。

| 错误/提示信息 | 可能的原因 | 建议解决方案 |
| :--- | :--- | :--- |
| "用户名或密码错误" | 1.  您输入的邮箱地址或密码不正确（打字失误、忘记密码或密码被他人更改）。<br>2.  密码输入时键盘大写锁定（Caps Lock）键意外开启，导致大小写不匹配。<br>3.  您使用的输入法处于中文全角状态，导致输入了非预期的全角字符或标点。<br>4.  邮箱地址或密码输入框中，有多余的不可见空格。<br>5.  您的账户可能因多次错误尝试被暂时锁定，或已被系统管理员禁用。 | 1.  仔细检查输入内容: 在重新尝试登录前，请务必仔细核对您输入的邮箱地址和密码。建议先在一个文本编辑器（如记事本）中输入密码，确认无误后再复制粘贴到密码输入框中，以避免输入错误。
2.  检查键盘状态: 确保键盘上的Caps Lock键处于关闭状态。同时，切换输入法至英文半角模式。
3.  使用找回密码功能: 如果您确认输入无误但仍无法登录，强烈建议使用登录页面上的 【忘记密码?】 功能进行密码重置（详见 4.4 密码找回 章节）。
4.  联系系统管理员: 如果以上所有方法都无法解决问题，请立即联系您的系统管理员，寻求进一步的帮助，他们可以协助您检查账户状态或进行手动重置。 |
| "该邮箱已被注册" | 1.  您之前可能已经使用此邮箱地址在本系统中注册过账号，但您忘记了。<br>2.  其他人（例如您的同事或家人）可能无意中使用了您的邮箱地址或一个非常相似的地址进行了注册。 | 1.  尝试直接登录: 请先回忆一下您是否曾注册过该邮箱。如果是，请直接前往登录页面，使用该邮箱地址和您记忆中的密码尝试登录。
2.  更换新邮箱注册: 如果您确定自己从未注册过，或者您希望使用一个新的身份，请更换一个您本人拥有且尚未在本系统注册过的电子邮箱地址来完成新用户的注册流程。 |
| "两次输入的密码不一致" | 在用户注册或修改密码时，"设置密码"和"再次输入密码"这两个密码输入框中的内容未能完全匹配。 | 1.  重新输入: 请确保两次在密码输入框中输入的内容完全一致，包括大小写、数字和特殊符号。
2.  仔细核对: 您可以在第一个密码框输入后，仔细检查，然后以同样的方式再次输入到确认框。 |
| "XX为必填项" | 在您提交表单（如注册、病例信息填写）时，某个被系统标记为"必须填写"（通常有红色星号）的字段为空或未填写。 | 1.  定位错误字段: 根据系统提示，找到并补充所有带有红色星号的必填字段。系统通常会自动将页面滚动到第一个未填写的字段并进行高亮。
2.  重新提交: 确保所有必填项都已填充有效信息后，再次点击提交按钮。 |
| "上传文件格式不支持" | 您尝试上传的图像文件不是系统允许的图像格式（如系统可能只支持.jpg, .jpeg, .png等）。 | 1.  检查文件扩展名: 核对您要上传的图像文件的扩展名，确保它在系统支持的格式列表中。
2.  转换文件格式: 如果文件格式不支持，请使用图像编辑工具（如Windows画图、Photoshop、在线图片转换工具）将其转换为系统支持的格式（如`.jpg`或`.png`），然后重新上传。 |
| "上传文件过大" | 您上传的单张图像文件大小超过了系统配置的上传上限（例如，系统可能限制单张图片最大为20MB）。 | 1.  检查文件大小: 查看您要上传的图像文件的实际大小。
2.  压缩图像文件: 如果文件大小超过了系统允许的上限，请使用图像处理软件或在线图片压缩工具，在不严重影响图像清晰度的前提下，适当压缩图像文件的大小，然后再重新上传。 |
 10. 附录C：角色权限矩阵详表
本附录通过一个详细的矩阵表格，清晰地列出了"血管瘤人工智能辅助治疗系统"中各个用户角色（医生、审核员、管理员）在不同功能模块及子模块下所拥有的具体操作权限。这将帮助用户明确自己在系统中的职责范围，并理解权限控制的逻辑。

| 功能模块 | 子模块 | 具体操作权限 | 医生 (DOCTOR) | 审核员 (REVIEWER) | 管理员 (ADMIN) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 账户管理 | 注册 | 注册新账户 | ✅ | ✅ | ✅ |
| | 登录 | 登录系统 | ✅ | ✅ | ✅ |
| | 退出 | 退出系统 | ✅ | ✅ | ✅ |
| | 密码找回 | 重置自己的密码 | ✅ | ✅ | ✅ |
| | 个人信息 | 查看自己的个人信息 | ✅ | ✅ | ✅ |
| | | 修改自己的个人信息 | ✅ | ✅ | ✅ |
| 病例管理 | 病例列表 | 查看自己创建的所有病例 | ✅ | ✅ | ✅ |
| | | 查看所有用户的病例 (仅限管理员/审核员在其团队内) | ❌ | ✅ (团队内) | ✅ |
| | 创建 | 新建病例记录 | ✅ | ✅ | ✅ |
| | 编辑 | 编辑自己处于"草稿"状态的病例 | ✅ | ✅ | ✅ |
| | | 编辑自己处于"已驳回"状态的病例 | ✅ | ✅ | ✅ |
| | | 编辑他人病例 (需特定授权或仅限管理员) | ❌ | ❌ | ✅ |
| | 删除 | 删除自己处于"草稿"状态的病例 | ✅ | ✅ | ✅ |
| | | 删除任何状态的任何病例 (仅限管理员) | ❌ | ❌ | ✅ |
| | 提交 | 提交病例至审核流程 | ✅ | ✅ | ✅ |
| 图像标注 | 图像上传 | 上传医学影像文件 | ✅ | ✅ | ✅ |
| | 图像查看 | 查看已上传的医学影像 | ✅ | ✅ | ✅ |
| | 标注操作 | 添加新的标注框 | ✅ | ✅ | ✅ |
| | | 修改自己已绘制的标注 | ✅ | ✅ | ✅ |
| | | 删除自己已绘制的标注 | ✅ | ✅ | ✅ |
| 病例审核 | 审核列表 | 查看所有待审核病例列表 | ❌ | ✅ | ✅ |
| | 审核操作 | 批阅具体病例详情 | ❌ | ✅ | ✅ |
| | | 通过审核，将病例状态置为"已通过" | ❌ | ✅ | ✅ |
| | | 驳回审核，将病例状态置为"已驳回" | ❌ | ✅ | ✅ |
| 团队管理 | 团队列表 | 查看自己所属的团队信息 | ✅ | ✅ | ✅ |
| | | 查看系统内所有团队信息 (仅限管理员) | ❌ | ❌ | ✅ |
| | 创建团队 | 创建新的医疗团队 | ❌ | ✅ | ✅ |
| | 成员管理 | 邀请成员加入自己管理的团队 | ❌ | ✅ | ✅ |
| | | 从自己管理的团队移除成员 | ❌ | ✅ | ✅ |
| | | 编辑团队信息 (名称、描述等) | ❌ | ✅ | ✅ |
| | | 解散团队 (仅限管理员/团队创建者) | ❌ | ❌ | ✅ |
| | 团队所有权转让 | 转让团队所有权给其他用户 (仅限管理员/团队所有者) | ❌ | ❌ | ✅ |
| 后台管理 | 用户管理 | 查看用户列表 | ❌ | ❌ | ✅ |
| | | 修改用户基本信息 | ❌ | ❌ | ✅ |
| | | 修改用户角色 | ❌ | ❌ | ✅ |
| | | 禁用/激活用户账户 | ❌ | ❌ | ✅ |
| | 权限审批 | 查看权限申请列表 (如医生申请成为审核员) | ❌ | ❌ | ✅ |
| | | 批准/拒绝权限申请 | ❌ | ❌ | ✅ |
| | 图像管理 | 查看所有上传图像列表 | ❌ | ❌ | ✅ |
| | | 删除任何图像文件 | ❌ | ❌ | ✅ |

 11. 附录D：系统模块交互简述
本附录将以简洁明了的方式，概述"血管瘤人工智能辅助治疗系统"的整体技术架构和核心模块之间的交互关系。这将帮助读者从技术层面理解系统的高效运作原理。

 11.1 系统整体架构概览
本系统采用先进的B/S（浏览器/服务器）前后端分离架构，并整合了独立运行的AI服务模块，确保了系统的灵活性、可扩展性和高可用性。

```mermaid
graph TD
    A[用户] --> B{浏览器 (前端)};
    B --> C[应用服务器 (后端)];
    C --> D[数据库 (MySQL)];
    C --> E[AI服务模块 (Python)];

    subgraph Frontend
        B
    end

    subgraph Backend
        C
        D
    end

    subgraph AI Service
        E
    end

    style A fill:f9f,stroke:333,stroke-width:2px
    style B fill:bbf,stroke:333,stroke-width:2px
    style C fill:bfb,stroke:333,stroke-width:2px
    style D fill:fbb,stroke:333,stroke-width:2px
    style E fill:ffb,stroke:333,stroke-width:2px
```
（上图为系统整体架构示意图）

 11.2 核心模块职责与交互流程
 11.2.1 前端模块 (浏览器)
-   主要技术: 基于主流的 Vue.js 框架进行开发，结合 Element Plus 组件库构建美观、响应式的用户界面（UI）。
-   核心职责: 负责用户交互、页面渲染、数据展示和用户输入收集。所有用户在界面上的操作，如点击按钮、填写表单、绘制标注，都由前端代码处理。
-   与后端交互: 当用户需要获取或提交数据时（如登录、创建病例、保存标注），前端会通过 RESTful API（HTTP请求）向后端发送请求，接收并解析后端返回的数据，然后更新界面。

 11.2.2 后端模块 (应用服务器)
-   主要技术: 使用 Java 语言和 Spring Boot 框架进行构建，提供了一系列符合RESTful风格的API接口。
-   核心职责: 作为整个系统的中枢，负责处理前端发来的所有业务请求，执行核心业务逻辑（如用户认证、权限管理、病例管理、数据校验），协调数据存储和AI服务调用。
-   与数据库交互: 通过 JPA/Hibernate 等持久化框架，与MySQL数据库进行数据存取操作，确保数据的安全、一致和可靠。
-   与AI服务交互: 当需要进行医学影像分析或生成智能诊断建议时，后端会作为客户端，通过 HTTP/RPC 调用独立的AI服务模块。

 11.2.3 数据库模块 (MySQL)
-   主要技术: 采用 MySQL 关系型数据库管理系统。
-   核心职责: 负责所有系统数据的持久化存储，包括但不限于用户信息、用户角色、医疗团队信息、病例详情、原始医学影像的元数据、医生绘制的标注数据，以及AI生成的诊断建议等。通过其强大的事务处理能力和数据完整性约束，确保系统数据的安全、完整和可靠。
-   与后端交互: 仅与后端模块进行数据交互，前端不直接访问数据库。

 11.2.4 AI服务模块 (Python)
-   **主要技术**: 使用 Python 语言开发，基于 FastAPI 异步框架，集成先进的深度学习框架（Ultralytics YOLO）和大语言模型（Ollama）。采用异步处理架构，支持高并发请求。

-   **核心职责**: 作为独立运行的微服务，专注于处理所有与人工智能相关的计算密集型任务：
    -   **YOLO目标检测**: 快速识别血管瘤位置、类型和置信度（1-3秒）
    -   **三大类分类**: 支持30种血管瘤类型的精确分类
    -   **LLM诊断建议**: 生成结构化的专业诊断报告（30-60秒）
    -   **异步处理**: 快速检测 + 后台建议生成的双阶段处理

-   **服务架构**:
    -   **快速检测端点** (/diagnose-yolo): 提供实时YOLO检测结果
    -   **诊断建议端点** (/diagnose): 生成LLM诊断建议
    -   **健康检查端点** (/health): 监控服务和模型状态
    -   **后台任务系统**: 使用BackgroundTasks处理LLM生成

-   **与后端交互**:
    -   **请求阶段**: 接收后端发送的图像文件和患者元数据
    -   **快速响应**: 立即返回YOLO检测结果（检测框、类型、置信度）
    -   **异步回调**: 通过HTTP回调更新LLM生成的诊断建议
    -   **数据格式**: 返回结构化的JSON数据，包含检测结果和诊断建议

-   **处理流程**:
    1. **图像预处理**: 标准化图像格式和尺寸
    2. **YOLO检测**: 快速目标检测和分类
    3. **结果返回**: 立即返回检测结果给后端
    4. **LLM分析**: 后台生成详细诊断建议
    5. **回调更新**: 完成后自动更新数据库记录

 11.3 数据流与处理流程示例
 11.3.1 用户上传图像并进行标注的数据流
1.  前端: 用户在"图像标注与表单填写"界面通过点击【上传】按钮或拖拽操作，将本地医学影像文件发送至后端服务器。
2.  后端: 接收到前端上传的图像文件后，将其保存到指定的文件存储路径（如 `medical_images/original`），并在数据库中创建相应的图像元数据记录。
3.  后端 -> AI服务: 后端会立即将新上传的图像文件发送给AI服务模块，请求进行初步的YOLO目标检测。
4.  AI服务: 接收图像，利用预训练的YOLO模型对图像进行快速分析，识别出潜在的血管瘤病灶区域，并返回标注框的坐标、置信度及类别信息给后端。
5.  后端: 接收AI服务的YOLO检测结果，将其中的标注数据保存到数据库的标签（Tag）表中，并更新病例记录中的AI检测结果字段。同时，可能会异步触发大型语言模型（LLM）的调用，生成更详细的诊断建议。
6.  前端: 接收到后端返回的包含AI检测结果（如果存在）的病例数据后，在图像显示区渲染图像，并根据数据库中已保存的标注数据（包括医生手动标注和AI自动生成的）在图像上绘制标注框。
7.  前端: 用户在图像上进行手动标注操作，每当绘制或修改一个标注框，前端会实时更新"已添加标注"列表，并通过API将这些标注数据发送回后端。
8.  后端: 接收前端发送的标注数据，将其更新或保存到数据库的标签（Tag）表中。

 11.3.2 用户提交病例进行审核的数据流
1.  前端: 医生完成所有标注和表单填写后，点击【提交审核】按钮，将病例的完整数据发送至后端。
2.  后端: 接收病例数据，进行严格的业务逻辑校验（如必填项检查、数据格式合法性），并将病例的状态更新为"待审核"，保存到数据库。
3.  后端: 同时，该病例将出现在拥有"审核员"或"管理员"权限用户的"标注审核"工作台中。
4.  前端: 审核员在"标注审核"工作台选择一个"待审核"病例并点击【批阅】，前端向后端请求该病例的详细数据（包括原始图像、所有标注和表单信息）。
5.  后端: 从数据库中检索并返回该病例的完整详细数据给前端。
6.  前端: 审核员在详情页查看病例，进行审查。点击【通过】或【驳回】按钮，并将审核意见发送给后端。
7.  后端: 接收审核结果和意见，更新数据库中病例的状态（"已通过"或"已驳回"），并将审核意见保存到对应的字段。
8.  后端: 如果病例被驳回，系统会通知原提交医生（例如通过消息提醒），使其可以在"我的病例标注"工作台中看到被驳回的病例状态，并根据审核意见进行修改后再次提交。

 12. 联系我们
如果您在使用"血管瘤人工智能辅助诊断系统"的过程中遇到本手册未能解答的疑难问题，或者有任何关于AI诊断功能改进的宝贵建议，我们非常欢迎您通过以下官方渠道与我们取得联系。我们的技术支持团队将竭诚为您提供帮助。

**技术支持渠道：**

-   **AI诊断技术支持**: `<EMAIL>`
    - 专门处理AI诊断相关问题：诊断结果异常、置信度疑问、AI建议不准确等
    - 请在邮件中详细描述患者信息、上传图像特征、AI诊断结果和您的疑问

-   **系统技术支持**: `<EMAIL>`
    - 处理系统操作问题：登录异常、界面错误、功能故障等
    - 请在邮件中包含截图、错误提示和详细操作步骤

-   **紧急联系电话**: `XXX-XXXXXXXX`
    - 工作时间：周一至周五 9:00-18:00
    - 用于紧急技术问题和系统故障的实时支持

-   **系统管理员**: [请填写您的机构系统管理员联系方式]
    - 优先联系内部管理员处理权限、账户和配置问题
    - 协助向技术支持团队反馈复杂问题

**反馈建议：**
我们特别欢迎您对AI诊断准确性、用户体验和功能改进的建议，这将帮助我们不断优化系统，为血管瘤诊断提供更好的AI辅助工具。

