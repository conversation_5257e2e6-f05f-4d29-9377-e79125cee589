package com.medical.annotation.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class SinglePageAppController {

    /**
     * 处理前端路由，将所有不在API或其他明确定义的路径下的请求转发到index.html
     * 这样可以让Vue Router的history模式正常工作
     */
    @GetMapping({"/app/**", "/login", "/register", "/standalone-review"})
    public String forwardToIndex() {
        return "forward:/index.html";
    }
} 