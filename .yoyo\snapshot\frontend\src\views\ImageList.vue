<template>
  <div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>图像管理</h2>
      <div>
        <router-link to="/images/upload" class="btn btn-success">
          <i class="bi bi-plus-circle me-1"></i> 上传新图像
        </router-link>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-3 mb-2">
            <label for="statusFilter" class="form-label">状态</label>
            <select id="statusFilter" class="form-select" v-model="filters.status">
              <option value="">全部</option>
              <option value="DRAFT">草稿</option>
              <option value="SUBMITTED">待审核</option>
              <option value="APPROVED">已通过</option>
              <option value="REJECTED">未通过</option>
            </select>
          </div>
          <div class="col-md-3 mb-2">
            <label for="patientFilter" class="form-label">患者名称</label>
            <input type="text" id="patientFilter" class="form-control" v-model="filters.patientName">
          </div>
          <div class="col-md-3 mb-2">
            <label for="diagnosticFilter" class="form-label">诊断类别</label>
            <input type="text" id="diagnosticFilter" class="form-control" v-model="filters.diagnosisCategory">
          </div>
          <div class="col-md-3 mb-2">
            <label for="sortBy" class="form-label">排序方式</label>
            <select id="sortBy" class="form-select" v-model="sortBy">
              <option value="createdAt">上传时间</option>
              <option value="patientName">患者姓名</option>
              <option value="status">状态</option>
            </select>
          </div>
        </div>
        <div class="d-flex justify-content-end mt-2">
          <button class="btn btn-primary me-2" @click="applyFilters">
            <i class="bi bi-funnel me-1"></i> 筛选
          </button>
          <button class="btn btn-outline-secondary" @click="resetFilters">
            <i class="bi bi-arrow-repeat me-1"></i> 重置
          </button>
        </div>
      </div>
    </div>

    <!-- 加载中状态 -->
    <div v-if="loading" class="loader-container">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>
    
    <!-- 错误提示 -->
    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="images.length === 0" class="text-center py-5">
      <div class="mb-3">
        <i class="bi bi-images" style="font-size: 3rem;"></i>
      </div>
      <h4>暂无图像数据</h4>
      <p class="text-muted">点击上方"上传新图像"按钮添加图像</p>
    </div>
    
    <!-- 图像列表 -->
    <div v-else class="row">
      <div v-for="image in images" :key="image.id" class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="image-container">
            <img :src="image.path" :alt="image.filename" class="card-img-top">
          </div>
          <div class="card-body">
            <h5 class="card-title">{{ image.filename }}</h5>
            <p class="card-text">
              <small class="text-muted">
                患者: {{ image.patientName || '未知' }}, 
                {{ image.patientAge || '?' }}岁, 
                {{ image.patientGender || '未知' }}
              </small>
            </p>
            <p class="card-text">
              <span :class="getStatusBadgeClass(image.status)">
                {{ getStatusText(image.status) }}
              </span>
              <span class="badge bg-secondary ms-2">{{ image.mimetype }}</span>
            </p>
            <p class="card-text">
              <small>
                上传时间: {{ formatDate(image.createdAt) }}
              </small>
            </p>
          </div>
          <div class="card-footer bg-transparent">
            <div class="d-flex justify-content-between">
              <router-link :to="`/images/${image.id}`" class="btn btn-primary">
                <i class="bi bi-eye me-1"></i> 查看
              </router-link>
              <div>
                <button v-if="image.status === 'DRAFT'" 
                        class="btn btn-warning me-2"
                        @click="submitImage(image.id)">
                  <i class="bi bi-send me-1"></i> 提交
                </button>
                <button class="btn btn-danger" @click="confirmDelete(image.id)">
                  <i class="bi bi-trash me-1"></i> 删除
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页控件 -->
    <nav v-if="images.length > 0" class="mt-4">
      <ul class="pagination justify-content-center">
        <li class="page-item" :class="{ disabled: currentPage === 1 }">
          <a class="page-link" href="#" @click.prevent="changePage(currentPage - 1)">上一页</a>
        </li>
        <li class="page-item" :class="{ active: page === currentPage }" v-for="page in totalPages" :key="page">
          <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
        </li>
        <li class="page-item" :class="{ disabled: currentPage === totalPages }">
          <a class="page-link" href="#" @click.prevent="changePage(currentPage + 1)">下一页</a>
        </li>
      </ul>
    </nav>
  </div>
</template>

<script>
import { computed, ref, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'

export default {
  name: 'ImageList',
  setup() {
    const store = useStore()
    const route = useRoute()
    const router = useRouter()
    
    const images = computed(() => store.getters.getAllImages)
    const loading = computed(() => store.getters.isLoading)
    const error = computed(() => store.getters.getError)
    
    // 分页相关
    const currentPage = ref(1)
    const pageSize = ref(12)
    const totalPages = computed(() => Math.ceil(images.value.length / pageSize.value))
    
    // 筛选和排序
    const filters = ref({
      status: route.query.status || '',
      patientName: '',
      diagnosisCategory: ''
    })
    const sortBy = ref('createdAt')
    
    // 监听路由参数变化
    watch(() => route.query.status, (newStatus) => {
      if (newStatus !== filters.value.status) {
        filters.value.status = newStatus || ''
        applyFilters()
      }
    })
    
    // 初始化
    onMounted(async () => {
      try {
        // 如果URL中有status参数，使用该参数加载图像
        if (route.query.status) {
          await store.dispatch('fetchImagesByStatus', route.query.status)
        } else {
          await store.dispatch('fetchImages')
        }
      } catch (error) {
        console.error('Failed to load images:', error)
      }
    })
    
    // 应用筛选器
    const applyFilters = async () => {
      try {
        // 如果有状态筛选，使用专门的API
        if (filters.value.status) {
          await store.dispatch('fetchImagesByStatus', filters.value.status)
          
          // 更新URL参数，但不重新加载页面
          router.replace({ 
            query: { ...route.query, status: filters.value.status } 
          }).catch(() => {})
        } else {
          await store.dispatch('fetchImages')
          
          // 移除URL中的status参数
          const query = { ...route.query }
          delete query.status
          router.replace({ query }).catch(() => {})
        }
        
        // 重置页码
        currentPage.value = 1
      } catch (error) {
        console.error('Failed to apply filters:', error)
      }
    }
    
    // 重置筛选器
    const resetFilters = () => {
      filters.value = {
        status: '',
        patientName: '',
        diagnosisCategory: ''
      }
      sortBy.value = 'createdAt'
      applyFilters()
    }
    
    // 更改页码
    const changePage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }
    
    // 提交图像审核
    const submitImage = async (imageId) => {
      if (confirm('确定要提交此图像进行审核吗？')) {
        try {
          await store.dispatch('updateImageStatus', {
            id: imageId,
            status: 'SUBMITTED'
          })
        } catch (error) {
          console.error('Failed to submit image:', error)
        }
      }
    }
    
    // 确认删除
    const confirmDelete = async (imageId) => {
      if (confirm('确定要删除此图像吗？此操作不可撤销！')) {
        try {
          // 显示加载中
          const loading = ElLoading.service({
            lock: true,
            text: '删除中...',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          
          const result = await store.dispatch('deleteImage', imageId);
          loading.close();
          
          if (result.success) {
            ElMessage.success('删除成功');
          } else {
            ElMessage({
              type: 'error',
              message: result.error || '删除失败',
              duration: 5000
            });
          }
        } catch (error) {
          console.error('删除图像失败:', error);
          ElMessage.error('删除失败: ' + (error.message || '未知错误'));
        }
      }
    }
    
    // 根据状态获取对应的样式类
    const getStatusBadgeClass = (status) => {
      switch (status) {
        case 'DRAFT': return 'badge bg-primary'
        case 'SUBMITTED': return 'badge bg-warning'
        case 'APPROVED': return 'badge bg-success'
        case 'REJECTED': return 'badge bg-danger'
        default: return 'badge bg-secondary'
      }
    }
    
    // 根据状态获取中文文本
    const getStatusText = (status) => {
      switch (status) {
        case 'DRAFT': return '草稿'
        case 'SUBMITTED': return '待审核'
        case 'APPROVED': return '已通过'
        case 'REJECTED': return '未通过'
        default: return '未知'
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    return {
      images,
      loading,
      error,
      currentPage,
      totalPages,
      filters,
      sortBy,
      applyFilters,
      resetFilters,
      changePage,
      submitImage,
      confirmDelete,
      getStatusBadgeClass,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
.card-img-top {
  height: 200px;
  object-fit: cover;
}
</style> 