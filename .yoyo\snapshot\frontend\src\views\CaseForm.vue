<template>
  <div class="case-form-container">
    <div class="page-header">
      <h2>新建标注</h2>
    </div>

    <el-form ref="form" :model="caseData" label-width="100px">
      <el-form-item label="医学影像" prop="images">
        <div class="upload-section">
          <div class="upload-box" @click="triggerFileInput" :class="{'has-image': imagePreview}">
            <input type="file" id="standardFileInput" accept="image/*" @change="handleFileSelect" style="display:none" />
            
            <div v-if="!imagePreview" class="upload-placeholder">
              <i class="el-icon-upload"></i>
              <div class="upload-text">点击选择图片或拖拽图片到此处</div>
              <div class="upload-hint">支持JPG、PNG格式，图片将自动调整至最佳大小</div>
        </div>
        
            <div v-else class="image-preview-wrapper">
              <img :src="imagePreview" class="preview-image" />
              <div class="image-info">
                <div class="file-name">{{ selectedFileName }}</div>
                <div class="file-size">{{ formatFileSize(selectedFileSize) }}</div>
        </div>
              <div class="image-overlay">
                <div class="overlay-content">
                  <el-button type="text" icon="el-icon-refresh-right" @click.stop="triggerFileInput">重新选择</el-button>
              </div>
            </div>
            </div>
        </div>
        
          <div class="action-bar" v-if="imagePreview">
            <el-button type="primary" @click="handleNext" :disabled="isUploading" class="action-button">
              <i class="el-icon-right"></i> 下一步
                </el-button>
            <el-button @click="clearSelectedImage" :disabled="isUploading" class="action-button">
              <i class="el-icon-delete"></i> 清除
            </el-button>
        </div>
        
          <el-progress 
            v-if="uploadProgress > 0 && uploadProgress < 100" 
            :percentage="uploadProgress" 
            :stroke-width="6"
            status="success"
            class="upload-progress"
          ></el-progress>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleReturn">返回首页</el-button>
      </el-form-item>
    </el-form>
    
    <el-dialog
      title="图片处理中"
      :visible.sync="processingDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      width="400px"
      center
    >
      <div class="processing-content">
        <i class="el-icon-loading loading-icon"></i>
        <p class="processing-text">正在处理图片，请稍候...</p>
        <el-progress :percentage="processingProgress" :stroke-width="6"></el-progress>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '../utils/api'
import { mapActions } from 'vuex'
import axios from 'axios'

export default {
  name: 'CaseForm',
  data() {
    return {
      caseData: {
        images: []
      },
      dialogVisible: false,
      dialogImageUrl: '',
      uploadedImageId: null,
      imagePreview: null,
      selectedFileName: '',
      selectedFileSize: 0,
      selectedFile: null,
      isUploading: false,
      uploadProgress: 0,
      processingDialog: false,
      processingProgress: 0,
      maxImageWidth: 700,
      maxImageHeight: 700,
      processedFilePath: null,
      lastServerResponse: null,
      uploadedImageUrl: null,
      imageUploaded: false,
      uploadStatus: null
            }
  },
  mounted() {
    // 添加拖放支持
    const dropArea = document.querySelector('.upload-box');
    if (dropArea) {
      ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, this.preventDefaults, false);
      });

      ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, this.highlight, false);
      });

      ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, this.unhighlight, false);
      });

      dropArea.addEventListener('drop', this.handleDrop, false);
    }
  },
  methods: {
    ...mapActions(['saveProgress']),
    
    // 拖放相关方法
    preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    },
    
    highlight() {
      document.querySelector('.upload-box').classList.add('highlight');
    },
    
    unhighlight() {
      document.querySelector('.upload-box').classList.remove('highlight');
    },
    
    handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;
      if (files && files.length) {
        this.handleFiles(files[0]);
      }
    },
    
    handleFiles(file) {
      if (!file) return;
      
      if (!file.type.match('image.*')) {
        this.$message.error('请选择图片文件');
        return;
      }
      
      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.selectedFileSize = file.size;
      
      this.processImage(file);
    },
    
    triggerFileInput() {
      document.getElementById('standardFileInput').click();
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      this.handleFiles(file);
    },
    
    clearSelectedImage() {
      this.imagePreview = null;
      this.selectedFile = null;
        this.selectedFileName = '';
        this.selectedFileSize = 0;
      document.getElementById('standardFileInput').value = '';
    },
    
    // 处理图片（调整大小和预览）
    processImage(file) {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          // 检查图片是否需要调整大小
          let width = img.width;
          let height = img.height;
          
          if (width > this.maxImageWidth || height > this.maxImageHeight) {
            this.processingDialog = true;
            this.processingProgress = 10;
          
            // 调整图片大小
            setTimeout(() => {
              this.processingProgress = 30;
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              
              // 计算新的尺寸，保持宽高比
              const ratio = Math.min(this.maxImageWidth / width, this.maxImageHeight / height);
              const newWidth = width * ratio;
              const newHeight = height * ratio;
              
              canvas.width = newWidth;
              canvas.height = newHeight;
              
              // 绘制调整后的图片
              this.processingProgress = 60;
              ctx.drawImage(img, 0, 0, newWidth, newHeight);
              
              // 转换为DataURL
              this.processingProgress = 90;
              const resizedDataUrl = canvas.toDataURL(file.type);
              this.imagePreview = resizedDataUrl;
                
              // 将调整后的图像转换为Blob
              canvas.toBlob(blob => {
                this.selectedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: new Date().getTime()
                });
                this.processingProgress = 100;
                this.processingDialog = false;
              }, file.type);
            }, 300);
          } else {
            // 不需要调整大小，直接显示
            this.imagePreview = e.target.result;
      }
        };
        
        img.src = e.target.result;
      };
      
      reader.onerror = (error) => {
        this.$message.error('读取文件失败');
        console.error('读取文件失败:', error);
      };
      
      reader.readAsDataURL(file);
    },
    
    formatFileSize(bytes) {
      if (bytes < 1024) return bytes + ' bytes';
      else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
      else return (bytes / 1048576).toFixed(2) + ' MB';
    },
    
    // 处理图像文件，确保大小不超过700px
    async processImageFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const img = new Image();
          img.onload = () => {
            try {
              // 检查图片是否需要调整大小
              let width = img.width;
              let height = img.height;
              
              const maxWidth = 700;
              const maxHeight = 700;
              
              if (width > maxWidth || height > maxHeight) {
                // 计算新的尺寸，保持宽高比
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                const newWidth = Math.floor(width * ratio);
                const newHeight = Math.floor(height * ratio);
                
                // 创建Canvas绘制调整后的图片
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = newWidth;
                canvas.height = newHeight;
                
                // 绘制调整后的图片
                ctx.drawImage(img, 0, 0, newWidth, newHeight);
                
                // 转换为Blob - 使用压缩参数降低文件大小
                const imageQuality = 0.85; // 85%质量，平衡大小和清晰度
                canvas.toBlob((blob) => {
                  if (!blob) {
                    console.error('Canvas转换为Blob失败');
                    reject(new Error('图像处理失败'));
                    return;
                  }
                  
                  // 创建新的文件对象
                  const resizedFile = new File([blob], `temp_${file.name}`, {
                    type: file.type || 'image/jpeg',
                    lastModified: new Date().getTime()
                  });
                  
                  console.log(`图片已调整大小: ${width}x${height} => ${newWidth}x${newHeight}，大小: ${resizedFile.size}字节`);
                  
                  // 如果文件太大，再次尝试压缩
                  if (resizedFile.size > 3 * 1024 * 1024) { // 大于3MB
                    console.warn('处理后的图片仍然太大，尝试进一步压缩');
                    // 降低质量参数重新尝试
                    canvas.toBlob((smallerBlob) => {
                      const smallerFile = new File([smallerBlob], `temp_${file.name}`, {
                        type: 'image/jpeg', // 强制JPEG以减小尺寸
                        lastModified: new Date().getTime()
                      });
                      
                      this.saveProcessedFile(smallerFile, resolve, reject);
                    }, 'image/jpeg', 0.7); // 进一步降低质量到70%
                  } else {
                    this.saveProcessedFile(resizedFile, resolve, reject);
                  }
                }, file.type || 'image/jpeg', imageQuality);
              } else {
                console.log('图片尺寸在限制范围内，无需调整');
                this.saveProcessedFile(file, resolve, reject);
              }
            } catch (error) {
              console.error('处理图片失败:', error);
              reject(error);
            }
          };
          
          img.onerror = () => {
            reject(new Error('图片加载失败'));
          };
          
          img.src = e.target.result;
        };
        
        reader.onerror = () => {
          reject(new Error('读取文件失败'));
        };
      
        reader.readAsDataURL(file);
      });
    },
    
    // 辅助方法：保存处理后的文件到服务器
    async saveProcessedFile(file, resolve, reject) {
      console.log('[CaseForm] 开始保存处理后的文件到服务器, 文件名:', file.name, '大小:', file.size);
      
      // 保存到服务器指定的temp目录
      const formData = new FormData();
      formData.append('file', file);
      formData.append('originalFilename', file.name);
      formData.append('targetPath', 'temp'); // 使用相对路径而非绝对路径
      
      // 添加时间戳防止缓存问题
      formData.append('timestamp', new Date().getTime());
      
      // 获取当前用户信息
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const user = JSON.parse(userStr);
          if (user && user.id) {
            formData.append('userId', user.id);
            console.log('[CaseForm] 添加用户ID到上传请求:', user.id);
          }
        }
      } catch (e) {
        console.error('[CaseForm] 读取用户信息失败:', e);
      }
      
      try {
        console.log('[CaseForm] 发送上传请求到: /api/files/upload');
        const response = await axios.post('/api/files/upload', formData);
        console.log('[CaseForm] 上传响应:', response.data);
        
        // 使用相对路径
        this.processedFilePath = response.data.path || `/images/temp/temp_${file.name}`;
        this.uploadedImageUrl = response.data.url || this.processedFilePath;
        this.imageUploaded = true;
        this.uploadStatus = '上传成功';
        
        // 保存服务器返回的所有信息
        this.lastServerResponse = response.data;
        
        // 检查是否包含ID信息
        if (response.data.id) {
          console.log('[CaseForm] 图片已在服务器创建数据库记录, ID:', response.data.id);
        } else if (response.data.metadataId) {
          console.log('[CaseForm] 图片已在服务器创建数据库记录, metadataId:', response.data.metadataId);
        } else {
          console.warn('[CaseForm] 服务器未返回任何ID信息，可能需要手动创建数据库记录');
        }
        
        resolve(file);
      } catch (error) {
        console.error('[CaseForm] 保存图片到指定路径失败:', error);
        this.$message.error(`上传失败: ${error.message || '未知错误'}`);
        // 即使保存到服务器失败，也返回调整后的文件对象以便继续流程
        resolve(file);
      }
    },
    
    // 直接进入下一步，替代之前的上传按钮
    async handleNext() {
      try {
        if (!this.selectedFile) {
          this.$message.warning('请先选择图片');
          return;
        }
      
        this.isUploading = true;
        this.uploadProgress = 0;
        
        // 获取当前用户信息
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const userId = user.id || null;
        const userCustomId = user.customId || null;
        
        console.log('上传图片 - 当前用户信息:', {
          userId: userId,
          userCustomId: userCustomId,
          userName: user.name,
          userRole: user.role
        });
        
        // 获取用户团队信息
        let teamId = null;
        if (user.team && user.team.id) {
          teamId = user.team.id;
          console.log('从用户对象获取团队ID:', teamId);
        } else if (user.teamId) {
          teamId = user.teamId;
          console.log('从用户对象获取团队ID:', teamId);
        }
        
        // 先处理图片，确保大小适合并保存到指定路径
        // processImageFile方法中已经上传并创建了记录，不需要再次上传
        const processedFile = await this.processImageFile(this.selectedFile);
        console.log('图片处理完成，准备进入标注');
        
        // 记录图像上传操作日志
        const logData = {
          operation: 'UPLOAD',
          userId: userId,
          userCustomId: userCustomId,
          teamId: teamId,
          details: {
            fileName: processedFile.name,
            fileSize: processedFile.size,
            fileType: processedFile.type,
            timestamp: new Date().toISOString()
          }
        };
        
        // 调用日志API
        api.imageLogs.logOperation(logData)
          .then(response => {
            console.log('图像上传日志记录成功:', response.data);
          })
          .catch(error => {
            console.error('图像上传日志记录失败:', error);
          });
        
        // 使用第一次上传时服务器已返回的数据，不需要再次上传
        // 检查是否有服务器响应数据
        if (!this.lastServerResponse) {
          console.error('未找到服务器返回的图像数据，无法继续');
          this.$message.error('处理失败：未找到服务器返回的图像数据');
          this.isUploading = false;
          return;
        }
        
        // 保存物理路径到localStorage中，供标注页面使用
        if (this.processedFilePath) {
          localStorage.setItem('processedFilePath', this.processedFilePath);
        }
        
        // 使用服务器已经创建的记录信息
        // 优先使用数字ID，如果是格式化ID则转换为数字
        let imageId = null;
        if (this.lastServerResponse.id) {
          imageId = this.lastServerResponse.id;
        } else if (this.lastServerResponse.metadataId) {
          // 检查是否是数字ID或格式化ID
          const metadataId = this.lastServerResponse.metadataId;
          if (typeof metadataId === 'number') {
            imageId = metadataId;
          } else {
            // 尝试将格式化ID转换为数字
            try {
              imageId = parseInt(metadataId, 10);
              if (isNaN(imageId)) {
                imageId = metadataId; // 如果转换失败，使用原始值
              }
            } catch (e) {
              imageId = metadataId;
            }
          }
        }
        
        const responseData = {
          id: imageId,
          metadataId: imageId,
          imagePairId: this.lastServerResponse.imagePairId,
          path: this.lastServerResponse.webPath || this.lastServerResponse.path,
          filePath: this.lastServerResponse.filePath
        };
        console.log('使用服务器返回的ID:', responseData.metadataId, '类型:', typeof responseData.metadataId);
        
        // 保存到本地存储以便离线使用
        try {
          // 检查文件大小是否超过限制 - 设置2MB限制以避免localstorage配额问题
          const MAX_SIZE_MB = 2;
          const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;
          
          if (processedFile.size > MAX_SIZE_BYTES) {
            console.warn(`图片大小超过${MAX_SIZE_MB}MB，将不保存到localStorage`);
            // 仅使用临时URL，不保存到localStorage
            // 记录大小信息用于调试
            localStorage.setItem(`offline_image_info_${responseData.metadataId}`, JSON.stringify({
              size: processedFile.size,
              name: processedFile.name,
              type: processedFile.type,
              useTempUrl: true,
              serverMetadataId: responseData.metadataId
            }));
            this.uploadProgress = 100;
          } else {
            // 文件较小，可以安全保存到localStorage
            const reader = new FileReader();
            reader.onload = (e) => {
              try {
                localStorage.setItem(`offline_image_${responseData.metadataId}`, e.target.result);
                console.log('离线图片已保存到本地，ID:', responseData.metadataId);
              } catch (storageError) {
                console.error('localStorage存储错误:', storageError);
                // 移除可能已保存的部分数据
                localStorage.removeItem(`offline_image_${responseData.metadataId}`);
              }
              // 设置进度为100%
              this.uploadProgress = 100;
            };
            reader.readAsDataURL(processedFile);
          }
        } catch (e) {
          console.error('保存离线图像失败', e);
          // 失败也设置进度为100%以继续流程
          this.uploadProgress = 100;
        }
        
        // 保存图片ID到Vuex和localStorage
        this.uploadedImageId = imageId;
        
        // 保存为最后上传的图像ID
        localStorage.setItem('lastUploadedImageId', imageId);
        console.log('保存到localStorage的图像ID:', imageId, '类型:', typeof imageId);
        
        this.saveProgress({
          step: 1, // 回到第1步（标注步骤）
          imageId: imageId,
          formData: null,
          testMode: false,  // 不再使用测试模式
          imagePath: responseData.path,
          filePath: responseData.filePath // 保存物理文件路径
        });
          
        // 上传成功提示
        this.$message.success('图像处理成功，正在进入标注界面...');
        
        // 延迟跳转，等待进度条显示完成
        setTimeout(() => {
          // 确保ID是字符串类型，避免数字类型在URL中可能导致的问题
          const imageIdStr = String(imageId);
          
          // 跳转到标注页面 - 修改为跳转到完整标注界面
          this.$router.push({
            path: '/app/cases/form', // 修改路径：从'/annotations'改为'/app/cases/form'
            query: { 
              imageId: imageIdStr,
              testMode: 'false',  // 不再使用测试模式
              filePath: encodeURIComponent(responseData.filePath), // 传递文件路径
              ts: new Date().getTime() // 添加时间戳防止缓存
            }
          });
        }, 500);
      } catch (error) {
        console.error('上传处理失败:', error);
        this.$message.error('处理失败，请重试: ' + (error.message || '未知错误'));
        this.isUploading = false;
      }
    },
    
    handleReturn() {
      this.$router.push('/app/dashboard');
    }
  }
}
</script>

<style scoped>
.case-form-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h2 {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

.upload-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-box {
  width: 100%;
  max-width: 700px;
  height: 350px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  background-color: #f7f8fa;
}

.upload-box:hover {
  border-color: #409EFF;
  background-color: #f0f6ff;
}

.upload-box.highlight {
  border-color: #409EFF;
  background-color: #ecf5ff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.upload-box.has-image {
  border-style: solid;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #909399;
  padding: 30px;
}

.upload-placeholder i {
  font-size: 60px;
  margin-bottom: 20px;
  color: #c0c4cc;
}

.upload-text {
  font-size: 18px;
  margin-bottom: 10px;
}

.upload-hint {
  font-size: 14px;
  color: #c0c4cc;
}

.image-preview-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
}

.file-name {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview-wrapper:hover .image-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
}

.overlay-content .el-button {
  color: white;
  font-size: 16px;
}

.action-bar {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
  width: 100%;
}

.action-button {
  min-width: 120px;
}

.upload-progress {
  margin-top: 20px;
  width: 100%;
  max-width: 700px;
}

.processing-content {
  text-align: center;
  padding: 20px 0;
}

.loading-icon {
  font-size: 40px;
  color: #409EFF;
  margin-bottom: 20px;
}

.processing-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}
</style> 