<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :width="width"
    :before-close="handleClose"
  >
    <div class="dialog-content">
      <div v-if="icon" class="dialog-icon">
        <i :class="iconClass"></i>
      </div>
      <div class="dialog-message">
        <slot>{{ message }}</slot>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ cancelText }}</el-button>
        <el-button :type="confirmType" @click="handleConfirm">{{ confirmText }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'ConfirmDialog',
  props: {
    title: {
      type: String,
      default: '确认'
    },
    message: {
      type: String,
      default: '确定要执行此操作吗？'
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    confirmType: {
      type: String,
      default: 'primary'
    },
    icon: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '30%'
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);
      }
    },
    iconClass() {
      switch (this.icon) {
        case 'warning':
          return 'el-icon-warning-outline';
        case 'error':
          return 'el-icon-error';
        case 'success':
          return 'el-icon-success';
        case 'info':
          return 'el-icon-info';
        default:
          return this.icon;
      }
    }
  },
  methods: {
    handleClose(done) {
      this.$emit('cancel');
      done();
    },
    handleCancel() {
      this.dialogVisible = false;
      this.$emit('cancel');
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit('confirm');
    }
  }
}
</script>

<style scoped>
.dialog-content {
  display: flex;
  padding: 10px 0;
}

.dialog-icon {
  font-size: 24px;
  margin-right: 15px;
  display: flex;
  align-items: center;
}

.dialog-icon i {
  color: #e6a23c;
}

.dialog-icon i.el-icon-error {
  color: #f56c6c;
}

.dialog-icon i.el-icon-success {
  color: #67c23a;
}

.dialog-icon i.el-icon-info {
  color: #909399;
}

.dialog-message {
  flex: 1;
  font-size: 16px;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}
</style> 