# 数据库结构更新指南

## 更新摘要

我们对数据库结构进行了优化，移除了以下冗余字段：

1. `image_metadata` 表中的 `path` 和 `image_two_path` 字段（这些字段已经存在于 `image_pairs` 表中）
2. `image_metadata` 表中的 `modality` 字段（未被使用）
3. `image_metadata` 表中的 `uploaded_by_custom_id` 和 `reviewed_by_custom_id` 字段（与外键冗余）

同时，我们改进了外键约束，为上传者(RESTRICT)和审核者(SET NULL)设置了不同的删除策略。

## 代码修改指南

### 已完成的修改

1. `ImageMetadata.java` 模型类
   - 移除了 `path`、`image_two_path`、`modality`、`uploaded_by_custom_id` 和 `reviewed_by_custom_id` 字段
   - 修改了 `@PrePersist` 和 `@PreUpdate` 方法，移除了对自定义ID的处理

2. `ImageMetadataService.java` 服务类
   - 移除了对 `path` 和 `modality` 字段的设置
   - 适配了相关方法，不再使用已删除的字段

3. `AnnotationController.java` 控制器
   - 更新了 `generateAndSaveAnnotatedImage` 方法，现在从 `image_pairs` 表获取图像路径
   - 添加了对 `ImagePairRepository` 的引用

4. 创建了 `ImagePairsService.java` 服务类
   - 提供对 `ImagePair` 实体的业务逻辑处理

### 需要进一步修改的地方

1. 前端代码
   - `CaseView.vue` 文件中的 `fallbackToImageMetadata` 方法需要修改，移除对 `imageData.path` 的引用
   - `CaseDetailForm.vue` 文件中的 `tryLoadFromMetadata` 和 `createImageRecord` 方法需要修改，不再使用 `path` 字段
   - 其他可能引用已删除字段的地方

2. 其他控制器和服务类
   - 检查其他可能使用已删除字段的地方，修改相应代码

## 迁移步骤

1. 确保数据库已完成更新，验证外键约束是否正确设置
2. 完成后端代码的修改，确保编译通过
3. 完成前端代码的修改，测试功能是否正常
4. 部署更新后的应用

## 注意事项

- 在进行代码修改时，始终从 `image_pairs` 表获取图像路径信息
- 对于需要保存图像路径的操作，确保正确更新 `image_pairs` 表
- 验证所有涉及用户关联的操作是否受到新外键约束的影响 