<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>标注审核系统</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
    .container {
      height: 100vh;
      width: 100vw;
      display: flex;
      flex-direction: column;
    }
    .header {
      background-color: #409EFF;
      color: white;
      text-align: center;
      padding: 15px 0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .header h1 {
      margin: 0;
      font-size: 20px;
    }
    .iframe-container {
      flex: 1;
      overflow: hidden;
      position: relative;
    }
    iframe {
      width: 100%;
      height: 100%;
      border: none;
      position: absolute;
      top: 0;
      left: 0;
    }
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      font-size: 16px;
      color: #909399;
    }
    .spinner {
      width: 40px;
      height: 40px;
      margin-right: 10px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #409EFF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>标注审核系统</h1>
    </div>
    <div class="iframe-container" id="iframe-container">
      <div class="loading" id="loading">
        <div class="spinner"></div>
        <span>加载中...</span>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 获取用户信息
      const userStr = localStorage.getItem('user');
      if (!userStr) {
        // 如果未登录，重定向到登录页面
        window.location.href = '/login?redirect=/review-standalone';
        return;
      }

      // 解析用户信息
      try {
        const user = JSON.parse(userStr);
        // 检查用户角色是否有权限
        if (user.role !== 'ADMIN' && user.role !== 'REVIEWER') {
          alert('您没有访问权限！');
          window.location.href = '/app/dashboard';
          return;
        }

        // 延迟一下创建iframe，确保页面渲染完毕
        setTimeout(() => {
          const iframeContainer = document.getElementById('iframe-container');
          const loading = document.getElementById('loading');
          
          // 创建iframe元素
          const iframe = document.createElement('iframe');
          iframe.src = '/annotation-review-content';
          iframe.onload = function() {
            // iframe加载完成后隐藏加载指示器
            loading.style.display = 'none';
          };
          
          // 添加iframe到容器
          iframeContainer.appendChild(iframe);
        }, 500);
      } catch (error) {
        console.error('初始化页面失败:', error);
        alert('初始化页面失败，请重新登录！');
        window.location.href = '/login';
      }
    });
  </script>
</body>
</html> 