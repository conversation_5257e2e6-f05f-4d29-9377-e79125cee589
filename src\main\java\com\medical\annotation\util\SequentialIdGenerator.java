package com.medical.annotation.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.BufferedReader;
import java.io.BufferedWriter;

/**
 * 序列化ID生成器
 * 生成从000000001开始的9位递增ID
 */
@Component
public class SequentialIdGenerator {
    
    private final ReentrantLock lock = new ReentrantLock();
    private static final int START_ID = 1; // 起始ID为1，格式化后为000000001
    private static final int ID_LENGTH = 9; // 9位ID
    private int currentId = 0; // 当前ID值，初始化为0
    private static final String BACKUP_FILE_PATH = "sequential_id_backup.txt"; // 备份文件路径
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @PostConstruct
    public void init() {
        System.out.println("初始化SequentialIdGenerator...");
        try {
            // 尝试从备份文件恢复ID
            int idFromBackup = loadIdFromBackupFile();
            if (idFromBackup > 0) {
                System.out.println("从备份文件恢复ID: " + idFromBackup);
                currentId = idFromBackup;
            } else {
                // 如果备份文件中没有有效ID，尝试从数据库获取
                currentId = getMaxIdFromDb();
                System.out.println("从数据库恢复ID: " + currentId);
            }
            
            // 确保ID至少从2开始(因为000000001和000000002已经存在)
            if (currentId < 2) {
                currentId = 2;  // 下一个ID将是3
                System.out.println("当前ID小于2，设置为2，下一个ID将是3");
            }
            
            // 记录初始化信息
            System.out.println("初始化当前最大ID: " + currentId + ", 下一个ID将是: " + (currentId + 1) + 
                              ", 格式化后: " + formatId(currentId + 1));
            
            // 更新备份文件
            saveIdToBackupFile(currentId);
        } catch (Exception e) {
            System.err.println("初始化ID生成器失败: " + e.getMessage());
            e.printStackTrace();
            System.out.println("使用默认ID起始值: 2"); // 设为2，下一个ID是3
            currentId = 2;
            
            // 尽管初始化失败，仍然尝试更新备份文件
            try {
                saveIdToBackupFile(currentId);
            } catch (Exception ex) {
                System.err.println("无法更新备份文件: " + ex.getMessage());
            }
        }
    }
    
    /**
     * 生成下一个9位格式的ID
     * 从000000001开始严格递增
     *
     * @return 9位格式的ID字符串
     */
    public synchronized String nextId() {
        lock.lock();
        try {
            // 如果当前ID为0，表示数据库中没有记录，从1开始
            if (currentId == 0) {
                currentId = START_ID;
            } else {
                // 否则递增当前ID
                currentId++;
            }
            
            // 保存到备份文件
            saveIdToBackupFile(currentId);
            
            String formattedId = formatId(currentId);
            System.out.println("生成新ID: " + currentId + ", 格式化后: " + formattedId);
            return formattedId;
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 从备份文件加载ID
     */
    private int loadIdFromBackupFile() {
        File backupFile = new File(BACKUP_FILE_PATH);
        if (!backupFile.exists()) {
            System.out.println("备份文件不存在: " + BACKUP_FILE_PATH);
            return 0;
        }
        
        try (BufferedReader reader = new BufferedReader(new FileReader(backupFile))) {
            String line = reader.readLine();
            if (line != null && !line.trim().isEmpty()) {
                try {
                    int id = Integer.parseInt(line.trim());
                    System.out.println("从备份文件读取ID: " + id);
                    return id;
                } catch (NumberFormatException e) {
                    System.err.println("备份文件包含无效ID: " + line);
                }
            }
        } catch (Exception e) {
            System.err.println("读取备份文件失败: " + e.getMessage());
        }
        
        return 0;
    }
    
    /**
     * 保存ID到备份文件
     */
    private void saveIdToBackupFile(int id) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(BACKUP_FILE_PATH))) {
            writer.write(String.valueOf(id));
            System.out.println("ID已保存到备份文件: " + id);
        } catch (Exception e) {
            System.err.println("保存ID到备份文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 从数据库获取当前最大的ID编号
     */
    private int getMaxIdFromDb() {
        // 首先检查表是否存在
        boolean tableExists = checkTableExists("image_metadata");
        if (!tableExists) {
            System.out.println("表image_metadata不存在，返回起始值2");
            return 2; // 返回2作为起始值，下一个ID将是3
        }
        
        // 然后检查列是否存在
        boolean columnExists = checkColumnExists("image_metadata", "formatted_id");
        if (!columnExists) {
            System.out.println("列formatted_id不存在，返回起始值2");
            return 2; // 返回2作为起始值，下一个ID将是3
        }
        
        try {
            // 查询数据库中现有记录的数量
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM image_metadata", Integer.class);
            if (count == null || count == 0) {
                System.out.println("数据库中没有记录，从ID=2开始");
                return 2; // 返回2作为起始值，下一个ID将是3
            }
            
            // 使用直接的SQL查询获取最大formatted_id
            String directSql = "SELECT MAX(CAST(REPLACE(formatted_id, '0', '') AS UNSIGNED)) FROM image_metadata WHERE formatted_id REGEXP '^[0-9]+$'";
            System.out.println("执行直接查询最大ID: " + directSql);
            
            Integer maxIdNumber = jdbcTemplate.queryForObject(directSql, Integer.class);
            if (maxIdNumber != null && maxIdNumber > 0) {
                System.out.println("从数据库查询到的最大ID数值: " + maxIdNumber);
                return maxIdNumber;
            }
            
            // 如果直接查询失败，尝试多种查询方法
            int maxId = tryMultipleQueries();
            if (maxId > 0) {
                return Math.max(maxId, 2); // 确保至少从2开始
            }
            
            // 如果所有方法都失败，从2开始
            System.out.println("所有查询方法都失败，从ID=2开始");
            return 2;
        } catch (Exception e) {
            System.err.println("查询数据库失败: " + e.getMessage());
            e.printStackTrace();
            // 如果查询失败，返回2
            return 2;
        }
    }
    
    /**
     * 尝试多种查询方法获取最大ID
     */
    private int tryMultipleQueries() {
        // 方法1: 使用CAST函数转换为无符号整数
        try {
            String sql1 = "SELECT formatted_id FROM image_metadata WHERE formatted_id IS NOT NULL ORDER BY CAST(formatted_id AS UNSIGNED) DESC LIMIT 1";
            System.out.println("尝试查询方法1: " + sql1);
            String maxFormattedId = jdbcTemplate.queryForObject(sql1, String.class);
            if (maxFormattedId != null && !maxFormattedId.isEmpty()) {
                return parseFormattedId(maxFormattedId);
            }
        } catch (Exception e) {
            System.out.println("查询方法1失败: " + e.getMessage());
        }
        
        // 方法2: 使用字符串排序
        try {
            String sql2 = "SELECT formatted_id FROM image_metadata WHERE formatted_id IS NOT NULL ORDER BY formatted_id DESC LIMIT 1";
            System.out.println("尝试查询方法2: " + sql2);
            String maxFormattedId = jdbcTemplate.queryForObject(sql2, String.class);
            if (maxFormattedId != null && !maxFormattedId.isEmpty()) {
                return parseFormattedId(maxFormattedId);
            }
        } catch (Exception e) {
            System.out.println("查询方法2失败: " + e.getMessage());
        }
        
        // 方法3: 使用LENGTH和数字字符比较
        try {
            String sql3 = "SELECT formatted_id FROM image_metadata WHERE formatted_id REGEXP '^[0-9]+$' ORDER BY LENGTH(formatted_id) DESC, formatted_id DESC LIMIT 1";
            System.out.println("尝试查询方法3: " + sql3);
            String maxFormattedId = jdbcTemplate.queryForObject(sql3, String.class);
            if (maxFormattedId != null && !maxFormattedId.isEmpty()) {
                return parseFormattedId(maxFormattedId);
            }
        } catch (Exception e) {
            System.out.println("查询方法3失败: " + e.getMessage());
        }
        
        // 方法4: 使用原始ID排序，然后获取对应的formatted_id
        try {
            String sql4 = "SELECT formatted_id FROM image_metadata WHERE formatted_id IS NOT NULL ORDER BY id DESC LIMIT 1";
            System.out.println("尝试查询方法4: " + sql4);
            String maxFormattedId = jdbcTemplate.queryForObject(sql4, String.class);
            if (maxFormattedId != null && !maxFormattedId.isEmpty()) {
                return parseFormattedId(maxFormattedId);
            }
        } catch (Exception e) {
            System.out.println("查询方法4失败: " + e.getMessage());
        }
        
        return 0;
    }
    
    /**
     * 解析格式化ID字符串为整数
     */
    private int parseFormattedId(String formattedId) {
        if (formattedId == null || formattedId.isEmpty()) {
            return 0;
        }
        
        try {
            // 移除前导零再解析
            String idWithoutLeadingZeros = formattedId.replaceFirst("^0+", "");
            if (idWithoutLeadingZeros.isEmpty()) {
                // 如果全是零，则为0
                System.out.println("ID全是零，返回0");
                return 0;
            }
            int maxId = Integer.parseInt(idWithoutLeadingZeros);
            System.out.println("解析的最大ID: " + maxId + "，原始ID字符串: " + formattedId);
            return maxId;
        } catch (NumberFormatException e) {
            System.out.println("无法解析formatted_id: " + formattedId + "，返回0");
            return 0;
        }
    }
    
    /**
     * 检查表是否存在
     */
    private boolean checkTableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables " +
                       "WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            boolean exists = count != null && count > 0;
            System.out.println("检查表 " + tableName + " 是否存在: " + exists);
            return exists;
        } catch (Exception e) {
            System.err.println("检查表是否存在时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查列是否存在
     */
    private boolean checkColumnExists(String tableName, String columnName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.columns " +
                       "WHERE table_schema = DATABASE() AND table_name = ? AND column_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName, columnName);
            boolean exists = count != null && count > 0;
            System.out.println("检查列 " + tableName + "." + columnName + " 是否存在: " + exists);
            return exists;
        } catch (Exception e) {
            System.err.println("检查列是否存在时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 将数字格式化为指定长度的字符串，前面补零
     */
    public static String formatId(int id) {
        return String.format("%0" + ID_LENGTH + "d", id);
    }
} 