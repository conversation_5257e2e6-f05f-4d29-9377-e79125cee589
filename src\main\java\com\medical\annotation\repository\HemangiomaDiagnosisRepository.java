package com.medical.annotation.repository;

import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.model.Team;
import com.medical.annotation.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface HemangiomaDiagnosisRepository extends JpaRepository<HemangiomaDiagnosis, Integer>, JpaSpecificationExecutor<HemangiomaDiagnosis> {
    List<HemangiomaDiagnosis> findByStatusAndUser_Team(HemangiomaDiagnosis.Status status, Team team);
    
    // 根据用户查询病例
    List<HemangiomaDiagnosis> findByUser(User user);
    
    // 根据用户和状态查询病例
    List<HemangiomaDiagnosis> findByUserAndStatus(User user, HemangiomaDiagnosis.Status status);
    
    // 获取用户最近的10条诊断记录，按创建时间降序排列
    List<HemangiomaDiagnosis> findTop10ByUserOrderByCreatedAtDesc(User user);

    // 根据用户ID和状态查找
    @Query("SELECT h FROM HemangiomaDiagnosis h WHERE h.user.id = :userId AND h.status = :status")
    List<HemangiomaDiagnosis> findByUserIdAndStatus(@Param("userId") Integer userId, @Param("status") String status);
    
    // 获取用户最近的5条诊断记录
    @Query("SELECT h FROM HemangiomaDiagnosis h WHERE h.user.id = :userId ORDER BY h.createdAt DESC")
    List<HemangiomaDiagnosis> findTop5ByUserIdOrderByCreatedAtDesc(@Param("userId") Integer userId, Pageable pageable);
    
    // 新增：为审核员获取待审核列表 (根据其团队ID 或 无团队)
    @Query(value = "SELECT hd.*, u.team_id FROM hemangioma_diagnoses hd LEFT JOIN users u ON hd.user_id = u.id WHERE hd.status = 'SUBMITTED' AND (u.team_id = :teamId OR u.team_id IS NULL) ORDER BY hd.created_at DESC",
           countQuery = "SELECT count(*) FROM hemangioma_diagnoses hd LEFT JOIN users u ON hd.user_id = u.id WHERE hd.status = 'SUBMITTED' AND (u.team_id = :teamId OR u.team_id IS NULL)",
           nativeQuery = true)
    Page<HemangiomaDiagnosis> findAllPendingForReviewer(@Param("teamId") Integer teamId, Pageable pageable);

    // 新增：为管理员获取所有待审核列表
    @Query(value = "SELECT hd.*, u.team_id FROM hemangioma_diagnoses hd LEFT JOIN users u ON hd.user_id = u.id WHERE hd.status = 'SUBMITTED' ORDER BY hd.created_at DESC",
           countQuery = "SELECT count(*) FROM hemangioma_diagnoses hd WHERE hd.status = 'SUBMITTED'",
           nativeQuery = true)
    Page<HemangiomaDiagnosis> findAllPendingForAdmin(Pageable pageable);
} 