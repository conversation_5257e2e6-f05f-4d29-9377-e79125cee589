package com.medical.annotation.model;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@Entity
@Table(name = "tags")
public class Tag {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "metadata_id")
    private Long metadataId;
    
    @Column(name = "tag")
    private String tag;
    
    @Column(name = "x")
    private Double x;
    
    @Column(name = "y")
    private Double y;
    
    @Column(name = "width")
    private Double width;
    
    @Column(name = "height")
    private Double height;
    
    @Column(name = "created_by")
    private Integer createdBy;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 构造函数
    public Tag() {
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        this.createdAt = chinaTime.toLocalDateTime();
        this.updatedAt = chinaTime.toLocalDateTime();
    }
    
    @PrePersist
    protected void onCreate() {
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        if (createdAt == null) {
            createdAt = chinaTime.toLocalDateTime();
        }
        updatedAt = chinaTime.toLocalDateTime();
    }
    
    @PreUpdate
    protected void onUpdate() {
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        updatedAt = chinaTime.toLocalDateTime();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    // 为向后兼容添加Integer类型id的setter
    public void setId(Integer id) {
        this.id = id != null ? id.longValue() : null;
    }
    
    public Long getMetadataId() {
        return metadataId;
    }
    
    public void setMetadataId(Long metadataId) {
        this.metadataId = metadataId;
    }
    
    // 为向后兼容添加Integer类型metadataId的setter
    public void setMetadataId(Integer metadataId) {
        this.metadataId = metadataId != null ? metadataId.longValue() : null;
    }
    
    public String getTag() {
        return tag;
    }
    
    public void setTag(String tag) {
        this.tag = tag;
    }
    
    public Double getX() {
        return x;
    }
    
    public void setX(Double x) {
        this.x = x;
    }
    
    public Double getY() {
        return y;
    }
    
    public void setY(Double y) {
        this.y = y;
    }
    
    public Double getWidth() {
        return width;
    }
    
    public void setWidth(Double width) {
        this.width = width;
    }
    
    public Double getHeight() {
        return height;
    }
    
    public void setHeight(Double height) {
        this.height = height;
    }
    
    public Integer getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
} 