package com.medical.annotation.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.Team;
import com.medical.annotation.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ImageMetadataRepository extends JpaRepository<ImageMetadata, Long>, JpaSpecificationExecutor<ImageMetadata> {
    // 按状态查询图像
    List<ImageMetadata> findByStatus(ImageMetadata.Status status);
    
    // 查询特定用户上传的图像
    List<ImageMetadata> findByUploadedBy(User uploadedBy);
    
    // 查询特定用户上传的特定状态的图像
    List<ImageMetadata> findByUploadedByAndStatus(User uploadedBy, ImageMetadata.Status status);
    
    // 检查格式化ID是否已存在
    boolean existsByFormattedId(String formattedId);
    
    // 按格式化ID查找图像元数据
    Optional<ImageMetadata> findByFormattedId(String formattedId);
    
    // 按文件名查找图像元数据
    Optional<ImageMetadata> findByFilename(String filename);
    
    // 按上传者ID查找图像元数据
    List<ImageMetadata> findByUploadedBy_Id(Integer uploadedById);
    
    // 按上传者ID查找图像元数据 (别名方法，与findByUploadedBy_Id相同)
    default List<ImageMetadata> findByUploadedById(Integer uploadedById) {
        return findByUploadedBy_Id(uploadedById);
    }
    
    // 按上传者ID和状态查找图像元数据
    List<ImageMetadata> findByUploadedBy_IdAndStatus(Integer uploadedById, ImageMetadata.Status status);
    
    // 按团队ID查找图像元数据
    List<ImageMetadata> findByTeam_Id(Integer teamId);
    
    // 按团队对象查找图像元数据
    List<ImageMetadata> findByTeam(Team team);
    
    // 按状态和团队ID查找图像元数据
    List<ImageMetadata> findByStatusAndTeam_Id(ImageMetadata.Status status, Integer teamId);
    
    // 按状态和团队ID查找图像元数据 (别名方法)
    default List<ImageMetadata> findByStatusAndTeamId(ImageMetadata.Status status, Integer teamId) {
        return findByStatusAndTeam_Id(status, teamId);
    }
    
    // 按审核者查找图像元数据
    List<ImageMetadata> findByReviewedBy(User reviewedBy);
    
    // 查找尚未分配团队的图像元数据
    List<ImageMetadata> findByTeamIsNull();

    List<ImageMetadata> findTop5ByUploadedBy_IdOrderByUpdatedAtDesc(Integer uploadedById);
} 