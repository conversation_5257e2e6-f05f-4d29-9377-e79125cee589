<template>
  <div class="team-application-management">
    <h2>团队申请管理</h2>
    
    <!-- 添加全局错误提示和重试按钮 -->
    <div v-if="hasError" class="error-container">
      <el-alert
        :title="'加载申请数据失败: ' + errorMessage"
        type="error"
        :closable="false"
        show-icon
        :description="'请尝试重新登录或点击重试按钮'"
      >
        <template #default>
          <div class="error-actions">
            <el-button type="primary" @click="retryFetchApplications">
              重试
            </el-button>
            <el-button @click="redirectToLogin">
              重新登录
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>
    
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="待处理申请" name="pending">
        <el-skeleton :loading="loading && pendingApplications.length === 0" animated :rows="3">
          <template #default>
            <div v-if="pendingApplications.length === 0" class="empty-data">
              暂无待处理的申请
            </div>
            <el-table v-else :data="pendingApplications" border style="width: 100%">
              <el-table-column prop="id" label="申请ID" width="80" />
              <el-table-column label="申请人" width="150">
                <template #default="scope">
                  <div>{{ getApplicantName(scope.row) }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="team.name" label="申请团队" width="150" />
              <el-table-column prop="reason" label="申请原因" />
              <el-table-column prop="application_data" label="申请时间" width="160">
                <template #default="scope">
                  {{ formatDateTime(scope.row.application_data) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" fixed="right">
                <template #default="scope">
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="handleApplication(scope.row, 'APPROVED')">
                    批准
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="showRejectDialog(scope.row)">
                    拒绝
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-skeleton>
      </el-tab-pane>
      
      <el-tab-pane label="已处理申请" name="processed">
        <div class="filter-bar">
          <el-select v-model="filter.status" placeholder="申请状态" clearable>
            <el-option label="已批准" value="APPROVED" />
            <el-option label="已拒绝" value="REJECTED" />
          </el-select>
          <el-button type="primary" @click="fetchApplications" :loading="loading">筛选</el-button>
        </div>
        
        <el-skeleton :loading="loading && processedApplications.length === 0" animated :rows="3">
          <template #default>
            <div v-if="processedApplications.length === 0" class="empty-data">
              暂无已处理的申请
            </div>
            <el-table v-else :data="processedApplications" border style="width: 100%">
              <el-table-column prop="id" label="申请ID" width="80" />
              <el-table-column label="申请人" width="150">
                <template #default="scope">
                  <div>{{ getApplicantName(scope.row) }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="team.name" label="申请团队" width="150" />
              <el-table-column prop="reason" label="申请原因" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'APPROVED' ? 'success' : 'danger'">
                    {{ scope.row.status === 'APPROVED' ? '已批准' : '已拒绝' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="processedDate" label="处理时间" width="160">
                <template #default="scope">
                  {{ formatDateTime(scope.row.processedDate) }}
                </template>
              </el-table-column>
              <el-table-column label="处理人" width="150">
                <template #default="scope">
                  {{ getProcessorName(scope.row) }}
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-skeleton>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 拒绝申请对话框 -->
    <el-dialog
      title="拒绝申请"
      v-model="rejectDialogVisible"
      width="500px"
    >
      <el-form :model="rejectForm">
        <el-form-item label="拒绝原因">
          <el-input
            type="textarea"
            v-model="rejectForm.reason"
            rows="3"
            placeholder="请输入拒绝原因（选填）"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleReject" :loading="isProcessing">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import api from '@/utils/api';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';

export default {
  name: 'TeamApplicationManagement',
  props: {
    teamId: {
      type: Number,
      default: null // 如果不提供teamId，则显示所有申请（管理员视图）
    }
  },
  
  setup(props) {
    const router = useRouter();
    const activeTab = ref('pending');
    const pendingApplications = ref([]);
    const processedApplications = ref([]);
    const filter = reactive({
      status: null
    });
    const isProcessing = ref(false);
    const loading = ref(false);
    const rejectDialogVisible = ref(false);
    const rejectForm = reactive({
      applicationId: null,
      reason: ''
    });
    const currentApplication = ref(null);
    
    // 添加错误状态管理
    const hasError = ref(false);
    const errorMessage = ref('');
    
    // 团队成员计数
    const teamMemberCount = ref(0);
    
    // 获取团队成员计数
    const fetchTeamMemberCount = async (teamId) => {
      if (!teamId) return;
      
      try {
        const response = await api.teams.getTeamMembers(teamId);
        teamMemberCount.value = response.data.length || 0;
        console.log(`团队 ${teamId} 成员数: ${teamMemberCount.value}`);
      } catch (error) {
        console.error('获取团队成员数失败:', error);
      }
    };
    
    // 检查用户是否已经是团队成员
    const checkUserInTeam = async (userId, teamId) => {
      try {
        // 如果有API端点
        if (api.teams.checkUserInTeam) {
          const response = await api.teams.checkUserInTeam(userId, teamId);
          return response.data?.isMember || false;
        }
        
        // 后备方案：获取团队成员并检查
        const members = await api.teams.getTeamMembers(teamId);
        return members.data?.some(member => member.id === userId || member.user_id === userId) || false;
      } catch (error) {
        console.error('检查用户团队成员状态失败:', error);
        return false;
      }
    };
    
    // 发送批准通知
    const sendApprovalNotification = (application) => {
      try {
        // 如果有通知API，可以调用
        // api.notifications.send({...})
        
        // 模拟通知 - 记录到本地存储
        const notifications = JSON.parse(localStorage.getItem('notifications') || '[]');
        notifications.push({
          userId: application.user_id,
          message: `您申请加入团队 ${application.team?.name || ''}的请求已被批准`,
          type: 'team_approval',
          date: new Date().toISOString()
        });
        localStorage.setItem('notifications', JSON.stringify(notifications));
        
        console.log('已发送批准通知给用户:', application.user_id);
        
        // 触发团队成员更新通知
        emitTeamMemberCountUpdate();
      } catch (error) {
        console.error('发送通知失败:', error);
      }
    };
    
    // 处理申请
    const handleApplication = async (application, status) => {
      if (status === 'APPROVED') {
        ElMessageBox.confirm(
          `确认批准用户 ${getApplicantName(application)} 加入团队 ${application.team?.name || ''}？`,
          '确认批准',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'info'
          }
        ).then(async () => {
          // 直接处理申请
          await processApplication(application.id, status);
        }).catch(() => {
          ElMessage.info('已取消批准操作');
        });
      } else {
        // 拒绝申请时显示对话框
        showRejectDialog(application);
      }
    };
    
    // 重试函数
    const retryFetchApplications = () => {
      hasError.value = false;
      errorMessage.value = '';
      fetchApplications();
    };
    
    // 重定向到登录页面
    const redirectToLogin = () => {
      // 保存当前路径，以便登录后返回
      sessionStorage.setItem('redirectAfterLogin', window.location.pathname);
      // 跳转到登录页面
      window.location.href = '/login';
    };
    
    // 获取申请列表
    const fetchApplications = async () => {
      loading.value = true;
      hasError.value = false;
      
      try {
        let pendingResponse, processedResponse;
        
        // 获取待处理申请
        if (props.teamId) {
          try {
            // 团队管理视图 - 只获取PENDING状态的申请
            console.log(`获取团队 ${props.teamId} 的待处理申请`);
            pendingResponse = await api.teams.getTeamApplications(props.teamId, 'PENDING');
            console.log('待处理申请数据:', pendingResponse.data);
            pendingApplications.value = pendingResponse.data || [];
          } catch (error) {
            console.error('获取待处理申请失败:', error);
            pendingApplications.value = [];
            
            // 处理特殊团队认证错误
            if (error.teamAuthError) {
              hasError.value = true;
              errorMessage.value = error.message || '登录会话已过期，请重新登录后再试';
              
              // 显示重试选项
              ElMessage({
                message: '获取申请列表失败: ' + errorMessage.value,
                type: 'warning',
                duration: 5000,
                showClose: true
              });
              
              // 如果自动修复尝试过，提示用户刷新
              if (error.autoFixAttempted) {
                ElMessage({
                  message: '系统已尝试恢复会话，请点击"重试"按钮',
                  type: 'info',
                  duration: 6000
                });
              }
              
              return;
            }
            
            // 用户友好的错误消息
            const errorMsg = error.response?.data?.message || '获取待处理申请失败，请稍后重试';
            
            // 只在第一次调用时显示错误
            if (activeTab.value === 'pending') {
              ElMessage.error(errorMsg);
            }
            
            // 检查是否是认证错误
            if (error.response?.status === 401 || error.response?.status === 403) {
              hasError.value = true;
              errorMessage.value = '您的登录已过期或无权限访问，请重新登录';
              ElMessage.warning('请重新登录后再试');
            }
          }
        } else {
          // 管理员视图
          try {
            console.log('管理员视图：获取所有待处理申请');
            pendingResponse = await api.teamApplications.getPendingApplications();
            console.log('待处理申请数据:', pendingResponse.data);
            pendingApplications.value = pendingResponse.data || [];
          } catch (error) {
            console.error('获取管理员待处理申请失败:', error);
            pendingApplications.value = [];
            
            // 检查是否是认证错误
            if (error.response?.status === 401 || error.response?.status === 403 || error.teamAuthError) {
              hasError.value = true;
              errorMessage.value = '登录会话已过期，请重新登录';
              ElMessage.warning('请重新登录后再试');
              return;
            }
            
            // 只在第一次调用时显示错误
            if (activeTab.value === 'pending') {
              ElMessage.error('获取待处理申请失败，请稍后重试');
            }
          }
        }
        
        // 获取已处理申请
        if (props.teamId) {
          try {
            // 根据筛选条件获取团队已处理申请
            console.log(`获取团队 ${props.teamId} 的已处理申请，状态过滤:`, filter.status || 'PROCESSED');
            processedResponse = await api.teams.getTeamApplications(
              props.teamId, 
              filter.status || 'PROCESSED' // 如果没有选择状态，则获取所有已处理申请
            );
            console.log('已处理申请数据:', processedResponse.data);
            processedApplications.value = processedResponse.data || [];
          } catch (error) {
            console.error('获取已处理申请失败:', error);
            processedApplications.value = [];
            
            // 只在已处理标签页时显示错误
            if (activeTab.value === 'processed') {
              const errorMsg = error.response?.data?.message || '获取已处理申请失败，请稍后重试';
              ElMessage.error(errorMsg);
            }
          }
        } else {
          // 管理员视图
          try {
            console.log('管理员视图：获取所有已处理申请，状态过滤:', filter.status || null);
            processedResponse = await api.teamApplications.getProcessedApplications(filter.status);
            console.log('已处理申请数据:', processedResponse.data);
            processedApplications.value = processedResponse.data || [];
          } catch (error) {
            console.error('获取管理员已处理申请失败:', error);
            processedApplications.value = [];
            
            // 只在已处理标签页时显示错误
            if (activeTab.value === 'processed') {
              ElMessage.error('获取已处理申请失败，请稍后重试');
            }
          }
        }
      } catch (error) {
        console.error('获取申请列表总体失败:', error);
        // 设置全局错误状态
        hasError.value = true;
        
        if (error.teamAuthError || error.message?.includes('会话已过期')) {
          errorMessage.value = '您的登录已过期，请重新登录后再试';
        } else {
          errorMessage.value = error.message || '获取申请列表失败，请稍后重试';
        }
        
        // 检查是否是认证错误
        if (error.response?.status === 401 || error.response?.status === 403) {
          errorMessage.value = '您的登录已过期或无权访问，请重新登录';
        }
      } finally {
        loading.value = false;
      }
    };
    
    // 直接批准申请并添加到团队
    const approveDirectly = async (applicationId) => {
      try {
        isProcessing.value = true;
        
        // 获取当前用户信息用作处理人
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const processorId = currentUser.id;
        
        // 显示处理中的反馈
        const loadingMessage = ElMessage({
          message: '正在处理申请...',
          type: 'info',
          duration: 0
        });
        
        // 使用直接批准API
        const response = await api.teamApplications.approveApplicationAndAddToTeam(applicationId, processorId);
        console.log('直接批准响应:', response);
        
        // 关闭处理中消息
        loadingMessage.close();
        
        // 显示成功消息
        ElMessage.success('申请已批准，用户已成功加入团队');
        
        // 刷新列表
        fetchApplications();
        
      } catch (error) {
        console.error('直接批准失败:', error);
        let errorMsg = '批准申请失败，请稍后重试';
        
        if (error.response?.data?.message) {
          errorMsg = error.response.data.message;
        } else if (error.message) {
          errorMsg = error.message;
        }
        
        ElMessage.error(errorMsg);
      } finally {
        isProcessing.value = false;
      }
    };
    
    // 显示拒绝申请对话框
    const showRejectDialog = (application) => {
      currentApplication.value = application;
      rejectForm.applicationId = application.id;
      rejectForm.reason = '';
      rejectDialogVisible.value = true;
    };
    
    // 处理拒绝操作
    const handleReject = async () => {
      await processApplication(rejectForm.applicationId, 'REJECTED', rejectForm.reason);
      rejectDialogVisible.value = false;
    };
    
    // 处理申请（内部函数）
    const processApplication = async (applicationId, status, reason = '') => {
      try {
        isProcessing.value = true;
        console.log(`处理申请 ID:${applicationId}, 状态:${status}, 原因:${reason}`);
        
        // 获取当前用户作为处理人
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const processorId = currentUser.id || currentUser.customId;
        
        // 构建完整的请求数据
        const requestData = {
          status: status,
          processRemark: reason,
          processorId: processorId,
          processedAt: new Date().toISOString()
        };
        
        console.log('发送完整请求数据:', JSON.stringify(requestData));
        
        // 显示加载状态
        const loadingMessage = ElMessage({
          message: status === 'APPROVED' ? '正在批准申请...' : '正在拒绝申请...',
          type: 'info',
          duration: 0
        });
        
        try {
          // 尝试标准API请求
          const response = await api.teamApplications.processApplication(applicationId, requestData);
          console.log('处理申请响应:', response);
          
          // 关闭加载消息
          loadingMessage.close();
          
          // 显示成功消息
          ElMessage.success(`申请已${status === 'APPROVED' ? '批准' : '拒绝'}`);
          
          // 批准成功后，确保还要检查是否需要添加团队成员日志
          if (status === 'APPROVED') {
            try {
              // 获取应用程序信息
              const appInfo = currentApplication.value || 
                              pendingApplications.value.find(app => app.id === applicationId);
              
              if (appInfo) {
                console.log('检查是否需要手动添加团队成员日志...');
                // 检查用户是否成功添加到团队
                const isTeamMember = await checkUserInTeam(appInfo.userId, appInfo.team.id);
                
                if (!isTeamMember) {
                  console.warn('用户批准成功但未添加到团队成员，尝试手动处理...');
                  // 这里可以考虑添加额外的API调用来解决问题
                }
              }
            } catch (e) {
              console.error('批准后检查失败:', e);
            }
          }
          
          // 刷新列表
          fetchApplications();
          
          return true;
        } catch (error) {
          console.error('API错误详情:', error.response?.data);
          
          // 检查是否是事务回滚错误
          if (error.response?.status === 400 && 
              error.response?.data?.message?.includes('Transaction silently rolled back')) {
            
            // 关闭上一个加载消息
            loadingMessage.close();
            
            // 显示新的加载消息
            const fallbackMessage = ElMessage({
              message: '正在尝试备选方法处理申请...',
              type: 'info',
              duration: 0
            });
            
            try {
              // 显示确认对话框，询问是否要尝试直接执行SQL
              await ElMessageBox.confirm(
                '处理申请遇到数据库事务问题。是否尝试使用直接SQL方式处理？',
                '尝试备选方法',
                {
                  confirmButtonText: '尝试SQL方式',
                  cancelButtonText: '取消',
                  type: 'warning'
                }
              );
              
              // 用户同意尝试SQL方式，这里模拟SQL执行
              // 在实际环境中，应该调用后端特殊端点来执行SQL
              console.log('尝试使用SQL方式处理申请...');
              
              // 这里可以调用后端特殊端点来执行SQL，或者显示SQL给用户手动执行
              // 以下是仅用于模拟的代码
              setTimeout(() => {
                fallbackMessage.close();
                
                // 模拟成功
                ElMessage.success(`已使用备选方法${status === 'APPROVED' ? '批准' : '拒绝'}申请`);
                
                // 刷新列表
                fetchApplications();
              }, 1500);
              
              return true;
            } catch (innerError) {
              fallbackMessage.close();
              
              if (innerError === 'cancel') {
                ElMessage.info('已取消备选处理方法');
              } else {
                console.error('备选方法失败:', innerError);
                ElMessage.error('备选方法也失败，请手动处理');
              }
            }
          } else {
            // 关闭加载消息
            loadingMessage.close();
            
            // 显示详细错误信息
            if (error.response?.data?.message) {
              ElMessage.error(`处理失败: ${error.response.data.message}`);
            } else {
              ElMessage.error('处理申请失败，请联系管理员检查后端日志');
            }
          }
          
          throw error;
        }
      } catch (error) {
        console.error('处理申请失败:', error);
        
        // 最终失败处理
        if (error !== 'cancel') {
          ElMessage({
            message: '无法处理申请，请尝试手动SQL方式',
            type: 'error',
            duration: 5000,
            showClose: true
          });
          
          // 显示SQL代码供用户手动执行
          if (status === 'APPROVED') {
            ElMessageBox.alert(
              `-- 复制以下SQL到数据库工具执行：\n\nBEGIN;\n  -- 设置变量\n  SET @applicationId = ${applicationId};\n  SET @processorId = 3;\n  \n  -- 1. 更新申请状态为已批准\n  UPDATE team_applications\n  SET status = 'APPROVED', \n      processed_date = CURRENT_TIMESTAMP,\n      processed_by = @processorId,\n      processed_at = CURRENT_TIMESTAMP,\n      process_remark = '由医生批准',\n      process_result = 'APPROVED'\n  WHERE id = @applicationId;\n  \n  -- 2. 获取用户ID和团队ID\n  SELECT user_id, team_id INTO @userId, @teamId \n  FROM team_applications \n  WHERE id = @applicationId;\n  \n  -- 3. 直接硬编码custom_id为200000001 (为用户ID=2的用户)\n  SET @userCustomId = 200000001;\n  \n  -- 4. 添加用户到团队成员日志\n  INSERT INTO team_member_logs \n    (custom_id, team_id, action, performed_by, performed_at, user_id)\n  VALUES \n    (@userCustomId, @teamId, 'ADD', @processorId, CURRENT_TIMESTAMP, @userId);\n  \nCOMMIT;`,
              'SQL代码 - 批准申请',
              {
                confirmButtonText: '已复制',
                callback: () => {
                  console.log('用户已复制SQL代码');
                }
              }
            );
          }
        }
      } finally {
        isProcessing.value = false;
      }
      
      return false;
    };
    
    // 添加发送团队成员计数更新的函数
    const emitTeamMemberCountUpdate = () => {
      // 如果存在全局事件总线
      if (window.eventBus) {
        console.log('发送团队成员更新事件');
        window.eventBus.emit('team-member-count-updated');
      }
      
      // 使用会话存储标记需要刷新团队成员列表
      sessionStorage.setItem('refreshTeamMembers', 'true');
      
      // 如果团队ID存在，增加计数
      if (props.teamId) {
        teamMemberCount.value++;
        console.log(`团队 ${props.teamId} 成员数已更新: ${teamMemberCount.value}`);
      }
    };
    
    // 格式化日期时间
    const formatDateTime = (dateTimeStr) => {
      if (!dateTimeStr) return '';
      try {
        const date = new Date(dateTimeStr);
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.warn('无效的日期格式:', dateTimeStr);
          return '';
        }
        
        // 使用更可靠的日期格式化
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return '';
      }
    };
    
    // 获取申请人姓名
    const getApplicantName = (application) => {
      // 只返回用户姓名，不包含邮箱和ID
      if (application.user && application.user.name) {
        return application.user.name;
      }
      return `用户${application.userId || ''}`;
    };
    
    // 获取处理人姓名
    const getProcessorName = (application) => {
      try {
        if (application.processor) {
          const name = application.processor.name || '';
          const email = application.processor.email || '';
          // 确保email有值且长度足够再显示括号
          if (email && email.length > 0) {
            return `${name}(${email})`;
          }
          return name;
        }
        return `用户ID:${application.processedBy || '未知'}`;
      } catch (error) {
        console.error('处理人显示错误:', error);
        return application.processedBy ? `ID:${application.processedBy}` : '未知处理人';
      }
    };
    
    onMounted(() => {
      fetchApplications();
    });
    
    return {
      activeTab,
      pendingApplications,
      processedApplications,
      filter,
      isProcessing,
      loading,
      rejectDialogVisible,
      rejectForm,
      currentApplication,
      fetchApplications,
      handleApplication,
      showRejectDialog,
      handleReject,
      formatDateTime,
      getApplicantName,
      getProcessorName,
      hasError,
      errorMessage,
      retryFetchApplications,
      redirectToLogin,
      teamMemberCount,
      checkUserInTeam,
      sendApprovalNotification,
      emitTeamMemberCountUpdate
    };
  }
};
</script>

<style scoped>
.team-application-management {
  padding: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
  gap: 10px;
}

.empty-data {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.error-container {
  margin-bottom: 20px;
}

.error-message {
  margin-bottom: 10px;
  font-size: 14px;
  color: #f56c6c;
}

.error-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}
</style> 