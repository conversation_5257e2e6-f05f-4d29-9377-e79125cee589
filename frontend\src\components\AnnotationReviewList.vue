<template>
  <div class="annotation-review-list" :class="{'standalone-mode': isStandalone}">
    <!-- 标注列表视图 -->
    <div v-if="!isViewingDetail">
      <h2>标注审核</h2>
      
      <!-- 审核人信息和说明 -->
      <el-alert
        v-if="currentUser"
        type="info"
        :closable="false"
      >
        <template #title>
          <span>当前审核人: <strong>{{ currentUser.name }}</strong></span>
          <el-tag size="small" :type="currentUser.role === 'ADMIN' ? 'danger' : 'warning'" style="margin-left: 10px">
            {{ currentUser.role === 'ADMIN' ? '管理员' : '审核医生' }}
          </el-tag>
          <span v-if="currentUser.team" style="margin-left: 10px">
            团队: <el-tag size="small" type="success">{{ currentUser.team.name }}</el-tag>
          </span>
        </template>
      </el-alert>
      
      <div class="filters">
        <el-input
          v-model="searchQuery"
          placeholder="搜索标注"
          prefix-icon="el-icon-search"
          clearable
          class="search-input"
          @change="loadAnnotations"
        />
        
        <el-select v-model="filters.team" placeholder="按团队筛选" clearable @change="loadAnnotations">
          <el-option
            v-for="team in teams"
            :key="team.id"
            :label="team.name"
            :value="team.id"
          />
        </el-select>
      </div>
      
      <!-- 标注列表 -->
      <el-table
        v-loading="loading"
        :data="annotations"
        :empty-text="loading ? '加载中...' : '暂无待审核的标注'"
        style="width: 100%"
        border
        stripe
        highlight-current-row
        :row-class-name="getRowClassName"
        @row-click="showAnnotationDetail"
      >
        <!-- ID和提交时间 -->
        <el-table-column prop="id" label="ID" width="70" />
        <el-table-column label="提交时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.submittedAt) }}
          </template>
        </el-table-column>
        
        <!-- 标注医生 -->
        <el-table-column label="标注医生" width="150">
          <template #default="scope">
            <span v-if="scope.row.submittedBy && typeof scope.row.submittedBy === 'object'">
              {{ scope.row.submittedBy.name || '未知' }}
            </span>
            <span v-else-if="scope.row.submittedBy">
              {{ getDoctorNameById(scope.row.submittedBy) || '医生' + scope.row.submittedBy }}
            </span>
            <span v-else-if="scope.row.uploadedBy && typeof scope.row.uploadedBy === 'object'">
              {{ scope.row.uploadedBy.name || '未知' }}
            </span>
            <span v-else-if="scope.row.uploadedBy">
              {{ getDoctorNameById(scope.row.uploadedBy) || '医生' + scope.row.uploadedBy }}
            </span>
            <span v-else>
              未知
            </span>
          </template>
        </el-table-column>
        
        <!-- 团队信息 -->
        <el-table-column label="所属团队" width="180">
          <template #default="scope">
            <div>
              <!-- 调试信息 -->
              <div v-if="false" style="font-size: 10px; color: #999;">
                team: {{ scope.row.team ? '有' : '无' }}, 
                team_id: {{ scope.row.team_id || '无' }},
                user: {{ scope.row.user ? '有' : '无' }},
                user.team: {{ scope.row.user && scope.row.user.team ? '有' : '无' }}
              </div>
              
              <!-- 显示逻辑 - 优先使用用户的团队信息 -->
              <template v-if="scope.row.user && scope.row.user.team && scope.row.user.team.name">
                <el-tag type="success">{{ scope.row.user.team.name }}</el-tag>
              </template>
              <template v-else-if="scope.row.user && scope.row.user.team_id">
                <el-tag type="success">{{ getTeamNameById(scope.row.user.team_id) }}</el-tag>
              </template>
              <template v-else-if="scope.row.team && scope.row.team.name">
                <el-tag type="success">{{ scope.row.team.name }}</el-tag>
              </template>
              <template v-else-if="scope.row.team_id">
                <el-tag type="success">{{ getTeamNameById(scope.row.team_id) }}</el-tag>
              </template>
              <template v-else>
                <el-tag type="warning" effect="dark">
                  <el-tooltip content="无团队标注，所有审核医生可审核" placement="top">
                    <span><i class="el-icon-warning-outline"></i> 无团队</span>
                  </el-tooltip>
                </el-tag>
              </template>
            </div>
          </template>
        </el-table-column>
        
        <!-- 病例信息 -->
        <el-table-column prop="patientName" label="患者姓名" width="120" />
        <el-table-column prop="diagnosis" label="诊断" />
        
        <!-- 状态列 -->
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusDisplayText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button
              size="small"
              :type="scope.row.status === 'APPROVED' ? 'info' : 'primary'"
              @click.stop="showAnnotationDetail(scope.row)"
            >
              {{ scope.row.status === 'APPROVED' ? '查看' : '批阅' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页控件 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 标注详情视图 -->
    <div v-else class="annotation-detail-view">
      <div class="top-actions">
        <el-button type="primary" size="medium" class="back-button" @click="backToList">
          {{ isStandalone ? '返回列表' : '返回标注列表' }}
        </el-button>
      </div>
      
      <div class="detail-title">
        <h2>病例详情</h2>
      </div>
      
      <div v-if="selectedAnnotation" class="annotation-detail">
        <!-- 标注图像 - 移动到详情前面 -->
        <div class="section">
          <h3>病变标注图像</h3>
          <!-- 移除调试信息 -->
          <div class="image-container">
            <el-image
              v-if="selectedAnnotation && (selectedAnnotation.image_two_path || selectedAnnotation.processedImagePath || selectedAnnotation.imagePath)"
              :src="getFullImageUrl(selectedAnnotation.image_two_path || selectedAnnotation.processedImagePath || selectedAnnotation.imagePath)"
              :preview-src-list="[]"
              fit="contain"
              style="max-width: 350px; max-height: 350px; width: auto; height: auto;"
              :z-index="9999"
              :preview-teleported="false"
              :initial-index="0"
              hide-on-click-modal
            >
              <template #error>
                <div class="image-error">
                  <i class="el-icon-picture-outline"></i>
                  <p>无法加载图像</p>
                  <small>路径: {{ selectedAnnotation.image_two_path || selectedAnnotation.processedImagePath || selectedAnnotation.imagePath }}</small>
                </div>
              </template>
            </el-image>
            <div v-else class="no-image">
              <i class="el-icon-picture-outline"></i>
              <p>该标注无图像</p>
            </div>
          </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="ID">{{ selectedAnnotation.formattedId || selectedAnnotation.id }}</el-descriptions-item>
            <el-descriptions-item label="标注状态">
              <el-tag :type="getStatusTagType(selectedAnnotation.status)">
                {{ getStatusDisplayText(selectedAnnotation.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="标注医生">
              {{ (selectedAnnotation.user && selectedAnnotation.user.name) || 
                (selectedAnnotation.submittedBy && selectedAnnotation.submittedBy.name) || 
                getDoctorNameById(selectedAnnotation.user?.id || selectedAnnotation.submittedBy) || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="所属团队">
              <template v-if="selectedAnnotation.user && selectedAnnotation.user.team && selectedAnnotation.user.team.name">
                <el-tag type="success">{{ selectedAnnotation.user.team.name }}</el-tag>
              </template>
              <template v-else-if="selectedAnnotation.user && selectedAnnotation.user.team_id">
                <el-tag type="success">{{ getTeamNameById(selectedAnnotation.user.team_id) }}</el-tag>
              </template>
              <template v-else-if="selectedAnnotation.team && selectedAnnotation.team.name">
                <el-tag type="success">{{ selectedAnnotation.team.name }}</el-tag>
              </template>
              <template v-else-if="selectedAnnotation.team_id">
                <el-tag type="success">{{ getTeamNameById(selectedAnnotation.team_id) }}</el-tag>
              </template>
              <template v-else>
                <el-tag type="warning">无团队</el-tag>
              </template>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(selectedAnnotation.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDateTime(selectedAnnotation.updatedAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="病例编号">
              {{ selectedAnnotation.caseNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="血管瘤类型">
              {{ selectedAnnotation.detectedType || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 患者信息 -->
        <div class="section">
          <h3>患者信息</h3>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="姓名">{{ selectedAnnotation.patientName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ selectedAnnotation.patientAge || '-' }}岁</el-descriptions-item>
            <el-descriptions-item label="性别">{{ selectedAnnotation.gender || '-' }}</el-descriptions-item>
            <el-descriptions-item label="病例编号">{{ selectedAnnotation.caseNumber || '-' }}</el-descriptions-item>
            <el-descriptions-item label="病灶位置" :span="2">{{ selectedAnnotation.bodyPart || selectedAnnotation.lesionLocation || '-' }}</el-descriptions-item>
            <el-descriptions-item label="颜色">{{ selectedAnnotation.color || '-' }}</el-descriptions-item>
            <el-descriptions-item label="血管质地">{{ selectedAnnotation.vesselTexture || '-' }}</el-descriptions-item>
            <el-descriptions-item label="症状描述">{{ selectedAnnotation.symptomDescription || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 诊断信息 -->
        <div class="section">
          <h3>诊断信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="诊断摘要">{{ selectedAnnotation.diagnosticSummary || '-' }}</el-descriptions-item>
            <el-descriptions-item label="治疗建议">{{ selectedAnnotation.treatmentSuggestion || '-' }}</el-descriptions-item>
            <el-descriptions-item label="注意事项">{{ selectedAnnotation.precautions || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 审核意见输入框 -->
        <div class="section">
          <h3>审核意见</h3>
          <el-input
            v-model="reviewNotes"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见..."
            :disabled="selectedAnnotation.status !== 'SUBMITTED'"
          ></el-input>
          <div v-if="selectedAnnotation.status !== 'SUBMITTED'" class="approved-notice">
            <i class="el-icon-warning"></i> 
            {{ selectedAnnotation.status === 'APPROVED' ? '该标注已通过审核，无法修改审核意见' : 
               selectedAnnotation.status === 'REJECTED' ? '该标注已被拒绝，无法修改审核意见' : 
               '该标注不是待审核状态，无法进行审核操作' }}
          </div>
          <div v-if="selectedAnnotation.reviewNotes && (selectedAnnotation.status === 'APPROVED' || selectedAnnotation.status === 'REJECTED')" class="review-notes">
            <strong>审核备注:</strong> {{ selectedAnnotation.reviewNotes }}
          </div>
        </div>
        
        <!-- 审核操作按钮 -->
        <div class="action-buttons">
          <el-button plain @click="backToList">返回</el-button>
          <template v-if="selectedAnnotation.status === 'SUBMITTED'">
            <el-button
              type="success"
              plain
              @click="approveAnnotation(selectedAnnotation)"
            >
              批准
            </el-button>
            <el-button
              type="danger"
              plain
              @click="showRejectDialog(selectedAnnotation)"
            >
              拒绝
            </el-button>
          </template>
        </div>
      </div>
    </div>
    
    <!-- 拒绝原因对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="拒绝标注"
      width="400px"
    >
      <el-form>
        <el-form-item label="拒绝原因">
          <el-input
            v-model="reviewNotes"
            type="textarea"
            rows="4"
            placeholder="请输入拒绝原因..."
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="danger" :loading="submitting" @click="rejectAnnotation">确认拒绝</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import api from '@/utils/api'; // 修改为使用utils/api而不是@/api
import axios from 'axios';
import { useRouter, useRoute } from 'vue-router';

export default {
  name: 'AnnotationReviewList',
  
  props: {
    standaloneMode: {
      type: Boolean,
      default: false
    }
  },
  
  setup(props) {
    // 状态数据
    const annotations = ref([]);
    const loading = ref(false);
    const submitting = ref(false);
    const currentPage = ref(1);
    const pageSize = ref(20);
    const total = ref(0);
    const searchQuery = ref('');
    const teams = ref([]);
    const currentUser = ref(null);
    
    const filters = reactive({
      team: null
    });
    
    // 拒绝标注对话框状态
    const rejectDialogVisible = ref(false);
    const selectedAnnotation = ref(null);
    
    // 审核意见
    const reviewNotes = ref('');
    
    // 详情视图状态
    const isViewingDetail = ref(false);
    
    const router = useRouter();
    const route = useRoute();
    
    // 检查是否在独立页面中
    const isStandalone = computed(() => {
      return props.standaloneMode || route.path === '/standalone-review';
    });
    
    // 加载用户信息
    const loadCurrentUser = async () => {
      try {
        // 获取当前用户信息
        const userData = localStorage.getItem('user');
        if (userData) {
          currentUser.value = JSON.parse(userData);
        } else {
          // 如果本地没有用户信息，尝试从服务器获取当前登录用户信息
          const response = await api.user.getCurrentUser();
          currentUser.value = response.data;
          localStorage.setItem('user', JSON.stringify(currentUser.value));
        }
      } catch (error) {
        console.error('获取当前用户信息失败', error);
        ElMessage.error('获取用户信息失败，请重新登录');
      }
    };
    
    // 加载团队信息
    const loadTeams = async () => {
      try {
        const response = await api.teams.getAllTeams();
        teams.value = response.data;
      } catch (error) {
        console.error('获取团队列表失败', error);
        ElMessage.warning('获取团队列表失败');
      }
    };
    
    // 加载待审核标注
    const loadAnnotations = async () => {
      loading.value = true;
      try {
        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
        const params = {
          page: currentPage.value - 1,
          size: pageSize.value,
          sort: 'created_at,desc', // 按创建时间降序排序
          search: searchQuery.value.trim(),
          teamId: filters.team
        };

        // 只有在非管理员时才添加userId，管理员可以看所有的
        if (userInfo.role !== 'ADMIN') {
          params.userId = userInfo.id || userInfo.customId;
        }

        console.log('加载待审核的血管瘤诊断记录，参数:', params);

        // 直接请求新的API端点
        const response = await axios.get('/medical/api/hemangioma-diagnoses/pending', { params });

        if (response.data && response.data.content) {
          annotations.value = response.data.content.map(item => {
            // 确保用户对象完整，包含团队信息
            const user = item.user || {};
            if (user && !user.team && user.team_id) {
              // 如果用户有team_id但没有team对象，尝试从teams中获取
              const team = teams.value.find(t => t.id === user.team_id);
              if (team) {
                user.team = team;
              }
            }
            
            return {
              ...item,
              // 兼容旧的字段名，以防模板中还在使用
              submittedAt: item.updatedAt, 
              submittedBy: user,
              user: user
            };
          });
          total.value = response.data.totalElements;
        } else {
          annotations.value = [];
          total.value = 0;
        }
      } catch (error) {
        console.error('加载待审核标注失败:', error);
        ElMessage.error('加载待审核列表失败，请稍后重试');
        annotations.value = [];
        total.value = 0;
      } finally {
        loading.value = false;
      }
    };
    
    // 分页处理
    const handleSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1; // 重置页码为第一页
      loadAnnotations();
    };
    
    const handleCurrentChange = (page) => {
      currentPage.value = page;
      loadAnnotations();
    };
    
    // 判断是否可以审核
    const canReview = (annotation) => {
      // 从用户信息判断权限，而不是硬编码返回值
      if (!currentUser.value) return false;
      
      // 管理员可以审核所有标注
      if (currentUser.value.role === 'ADMIN') return true;
      
      // 非审核医生不可以审核
      if (currentUser.value.role !== 'REVIEWER') return false;
      
      // 无团队标注，所有审核医生可以审核
      if (!annotation.team && !annotation.team_id) return true;
      
      // 获取团队ID (考虑不同格式)
      const annotationTeamId = 
        (annotation.team && annotation.team.id) || annotation.team_id;
      
      const userTeamId = 
        (currentUser.value.team && currentUser.value.team.id) || currentUser.value.team_id;
      
      // 有团队标注，只有同团队审核医生可以审核
      return userTeamId && userTeamId === annotationTeamId;
    };
    
    // 获取行的类名，用于样式
    const getRowClassName = ({ row }) => {
      if (!row.team) {
        return 'no-team-row';
      }
      return '';
    };
    
    // 查看标注详情
    const showAnnotationDetail = async (annotation) => {
      console.log('查看标注详情', annotation);
      
      try {
        // 先显示基本信息
        selectedAnnotation.value = annotation;
        
        // 切换到详情视图
        isViewingDetail.value = true;
        
        // 重置审核意见
        reviewNotes.value = '';
        
        // 然后加载完整的诊断记录
        const diagnosisId = annotation.id;
        if (diagnosisId) {
          try {
            console.log(`加载完整的诊断记录，ID: ${diagnosisId}`);
            loading.value = true;
            
            // 获取完整的诊断记录
            const response = await axios.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);
            
            if (response.data) {
              console.log('获取到完整的诊断记录:', response.data);
              
              // 处理用户和团队信息
              const user = response.data.user || selectedAnnotation.value.user || selectedAnnotation.value.submittedBy || {};
              
              // 如果用户有team_id但没有team对象，尝试从teams中获取
              if (user && !user.team && user.team_id) {
                const team = teams.value.find(t => t.id === user.team_id);
                if (team) {
                  user.team = team;
                }
              }
              
              // 合并数据，保留原有数据，添加新获取的详细信息
              selectedAnnotation.value = {
                ...selectedAnnotation.value,
                ...response.data,
                // 确保用户信息正确
                user: user,
                submittedBy: user
              };
              
              console.log('合并后的完整诊断记录:', selectedAnnotation.value);
            }
          } catch (error) {
            console.error('获取完整诊断记录失败:', error);
            ElMessage.error('获取诊断详情失败，请稍后重试');
          } finally {
            loading.value = false;
          }
        }
      } catch (error) {
        console.error('查看标注详情失败:', error);
        ElMessage.error('加载标注详情失败');
      }
    };
    
    // 返回列表
    const backToList = () => {
      isViewingDetail.value = false;
      selectedAnnotation.value = null;
    };
    
    // 批准标注
    const approveAnnotation = async (annotation) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm(
          `确认批准此标注？`,
          '确认批准',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'info'
          }
        );
        
        // 执行批准操作
        submitting.value = true;
        
        // 获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
        const userId = userInfo.customId || userInfo.id || '';
        const userRole = userInfo.role || 'REVIEWER';
        
        console.log('使用认证信息:', userId, userRole);
        
        try {
          // 使用更新后的API服务
          const response = await api.reviews.approveReview(
            annotation.id, 
            {
              reviewNotes: reviewNotes.value || '审核通过'
            }
          );
          
          console.log('审核成功响应:', response.data);
          
          // 更新本地状态
          annotation.status = 'APPROVED';
          
          ElMessage.success('标注已批准，状态已更新为已通过');
          
          // 返回列表并重新加载
          backToList();
          loadAnnotations();
        } catch (apiError) {
          console.error('API调用失败:', apiError);
          console.log('错误详情:', apiError.response?.data);
          ElMessage.error(`审核失败: ${apiError.response?.data?.message || apiError.message}`);
          
          // 尝试使用备用方法 - 直接使用axios
          try {
            console.log('尝试使用备用方法...');
            const backupResponse = await axios.put(`/medical/api/hemangioma-diagnoses/${annotation.id}`, {
              status: 'APPROVED',
              reviewNotes: reviewNotes.value || '审核通过'
            });
            
            console.log('备用方法成功:', backupResponse.data);
            
            // 更新本地状态
            annotation.status = 'APPROVED';
            
            ElMessage.success('标注已批准（备用方法），状态已更新为已通过');
            
            // 返回列表并重新加载
            backToList();
            loadAnnotations();
          } catch (backupError) {
            console.error('备用方法也失败:', backupError);
            ElMessage.error('所有审核方法均失败，请稍后重试');
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批准标注失败', error);
          ElMessage.error(error.response?.data || '批准标注失败');
        }
      } finally {
        submitting.value = false;
      }
    };
    
    // 显示拒绝对话框
    const showRejectDialog = (annotation) => {
      selectedAnnotation.value = annotation;
      rejectDialogVisible.value = true;
    };
    
    // 拒绝标注
    const rejectAnnotation = async () => {
      if (!reviewNotes.value) {
        ElMessage.warning('请输入拒绝原因');
        return;
      }
      
      try {
        submitting.value = true;
        
        // 获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
        const userId = userInfo.customId || userInfo.id || '';
        const userRole = userInfo.role || 'REVIEWER';
        
        console.log('使用认证信息:', userId, userRole);
        
        try {
          // 使用更新后的API服务
          const response = await api.reviews.rejectReview(
            selectedAnnotation.value.id, 
            {
              reviewNotes: reviewNotes.value
            }
          );
          
          console.log('拒绝成功响应:', response.data);
          
          // 关闭对话框
          rejectDialogVisible.value = false;
          
          // 更新本地状态
          selectedAnnotation.value.status = 'REJECTED';
          
          ElMessage.success('标注已拒绝，状态已更新为已拒绝');
          
          // 如果在详情视图，返回列表
          if (isViewingDetail.value) {
            backToList();
          }
          
          // 重新加载列表
          loadAnnotations();
        } catch (apiError) {
          console.error('API调用失败:', apiError);
          console.log('错误详情:', apiError.response?.data);
          ElMessage.error(`拒绝失败: ${apiError.response?.data?.message || apiError.message}`);
          
          // 尝试使用备用方法
          try {
            console.log('尝试使用备用方法...');
            
            // 使用API服务的备用方法
            const backupResponse = await api.reviews.rejectReviewBackup(
              selectedAnnotation.value.id, 
              reviewNotes.value
            );
            
            console.log('备用方法成功:', backupResponse);
            
            // 关闭对话框
            rejectDialogVisible.value = false;
            
            // 更新本地状态
            selectedAnnotation.value.status = 'REJECTED';
            
            ElMessage.success('标注已拒绝（备用方法），状态已更新为已拒绝');
            
            // 如果在详情视图，返回列表
            if (isViewingDetail.value) {
              backToList();
            }
            
            // 重新加载列表
            loadAnnotations();
          } catch (backupError) {
            console.error('备用方法也失败:', backupError);
            ElMessage.error('所有拒绝方法均失败，请稍后重试');
          }
        }
      } catch (error) {
        console.error('拒绝标注失败', error);
        ElMessage.error(error.response?.data || '拒绝标注失败');
      } finally {
        submitting.value = false;
      }
    };
    
    // 格式化日期时间
    const formatDateTime = (dateTimeString) => {
      if (!dateTimeString) return '-';
      
      try {
        const date = new Date(dateTimeString);
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '-';
        }
        
        return new Intl.DateTimeFormat('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }).format(date);
      } catch (error) {
        console.warn('日期格式化错误:', dateTimeString, error);
        return '-';
      }
    };
    
    // 获取图像路径，尝试多个可能的字段
    const getImagePath = (annotation) => {
      if (!annotation) return null;
      
      console.log('获取图像路径, annotation对象:', annotation);
      
      // 如果是图像对数据，直接优先使用image_two_path
      if (annotation.image_two_path) {
        console.log('直接使用annotation.image_two_path:', annotation.image_two_path);
        return annotation.image_two_path;
      }
      
      // 如果有图像对属性，优先使用其image_two_path
      if (annotation.imagePair && annotation.imagePair.image_two_path) {
        console.log('使用annotation.imagePair.image_two_path:', annotation.imagePair.image_two_path);
        return annotation.imagePair.image_two_path;
      }
      
      // 优先级顺序尝试 - 优先使用带标注框的图像
      const possiblePaths = [
        // 1. 直接在对象上的属性 - 优先使用image_two_path
        annotation.image_two_path,
        annotation.imageTwoPath,
        
        // 2. 嵌套在imagePair对象中的属性
        annotation.imagePair?.image_two_path,
        annotation.imagePair?.imageTwoPath,
        
        // 3. 尝试其他可能的字段
        annotation.annotated_image_path,
        annotation.annotatedImagePath,
        
        // 4. 最后才考虑原始图像路径
        annotation.image_path,
        annotation.imagePath,
        annotation.path
      ];
      
      // 返回第一个非空的路径
      for (const path of possiblePaths) {
        if (path) {
          console.log('找到图像路径:', path);
          return path;
        }
      }
      
      console.log('未找到任何图像路径');
      return null;
    };
    
    // 获取完整的图片URL
    const getFullImageUrl = (path) => {
      if (!path) return '';
      
      // 检查路径是否已经是完整URL
      if (path.startsWith('http://') || path.startsWith('https://')) {
        return path;
      }
      
      // 从配置文件或当前窗口位置动态获取基础URL，避免硬编码
      let baseUrl;
      
      // 尝试从API配置获取
      try {
        // 导入API配置可能无法在这里工作，所以使用备用方案
        const apiConfig = window.apiConfig || {};
        baseUrl = apiConfig.API_BASE_URL;
      } catch (e) {
        console.log('无法从配置获取API基础URL，使用当前窗口位置');
      }
      
      // 如果无法从配置获取，使用当前窗口的origin
      if (!baseUrl) {
        // 使用当前窗口的origin（协议+主机名+端口）
        baseUrl = window.location.origin;
        console.log('使用当前窗口origin作为基础URL:', baseUrl);
      }
      
      // 确保路径以/开头
      let normalizedPath = path.startsWith('/') ? path : `/${path}`;
      
      // 获取用户信息用于认证
      const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = userInfo.customId || userInfo.id || '';
      
      // 添加认证参数到URL
      const separator = normalizedPath.includes('?') ? '&' : '?';
      normalizedPath = `${normalizedPath}${separator}userId=${userId}&t=${new Date().getTime()}`;
      
      return `${baseUrl}${normalizedPath}`;
    };
    
    // 生命周期钩子
    onMounted(() => {
      console.log('========== 页面组件挂载完成 ==========');
      console.log('当前环境:', process.env.NODE_ENV);
      console.log('baseURL:', axios.defaults.baseURL);
      
      // 添加到window对象，方便控制台调试
      window.debugAnnotationReview = {
        loadAnnotations,
        getUserInfo: () => {
          const userStr = localStorage.getItem('user');
          return userStr ? JSON.parse(userStr) : null;
        },
        directFetchApi: async () => {
          try {
            // 从localStorage获取用户信息
            const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
            const userId = userInfo.customId || userInfo.id;
            
            // 直接通过fetch调用API
            const response = await fetch(`/api/reviews/pending?userId=${userId}&page=0&size=20`);
            const data = await response.json();
            console.log('直接API调用结果:', data);
            
            // 显示总数和内容
            console.log(`找到${data.totalElements}条记录，当前页${data.content?.length || 0}条数据`);
            return data;
          } catch (error) {
            console.error('直接API调用失败:', error);
          }
        }
      };
      
      loadCurrentUser();
      loadTeams();
      loadAnnotations();
    });
    
    // 根据ID获取团队名称
    const getTeamNameById = (teamId) => {
      if (!teamId) return '无团队';
      
      // 从已加载的团队列表中查找
      const team = teams.value.find(t => t.id === teamId);
      if (team) {
        return team.name;
      }
      
      // 使用泛化的格式而非硬编码
      return `团队${teamId}`;
    };
    
    // 根据ID获取医生姓名
    const getDoctorNameById = (doctorId) => {
      if (!doctorId) return '未知';
      
      // 查找当前用户，如果ID匹配则使用当前用户名
      if (currentUser.value && (currentUser.value.id === doctorId || currentUser.value.customId === doctorId)) {
        return currentUser.value.name;
      }
      
      // 使用泛化的格式而非硬编码
      return `医生${doctorId}`;
    };
    
    // 获取状态显示文本
    const getStatusDisplayText = (status) => {
      if (!status) return '未知';
      
      const statusMap = {
        'DRAFT': '草稿',
        'SUBMITTED': '待审核',
        'REVIEWED': '已审阅',
        'REJECTED': '已拒绝',
        'APPROVED': '已通过'
      };
      
      return statusMap[status] || status;
    };
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      if (!status) return 'info';
      
      const typeMap = {
        'DRAFT': 'info',
        'SUBMITTED': 'warning',
        'REVIEWED': 'success',
        'REJECTED': 'danger',
        'APPROVED': 'success'
      };
      
      return typeMap[status] || 'info';
    };
    
    return {
      annotations,
      loading,
      submitting,
      currentPage,
      pageSize,
      total,
      searchQuery,
      filters,
      teams,
      rejectDialogVisible,
      selectedAnnotation,
      isViewingDetail,
      loadAnnotations,
      handleSizeChange,
      handleCurrentChange,
      canReview,
      showAnnotationDetail,
      backToList,
      approveAnnotation,
      showRejectDialog,
      rejectAnnotation,
      formatDateTime,
      getRowClassName,
      currentUser,
      getTeamNameById,
      getDoctorNameById,
      getFullImageUrl,
      getImagePath,
      isStandalone,
      reviewNotes,
      // 添加状态显示相关方法
      getStatusDisplayText,
      getStatusTagType
    };
  }
};
</script>

<style scoped>
.annotation-review-list {
  padding: 20px;
}

/* 独立模式样式 */
.standalone-mode {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.standalone-mode h2 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
  color: #303133;
}

.filters {
  display: flex;
  margin-top: 20px;
  margin-bottom: 20px;
  align-items: center;
  gap: 10px;
}

.search-input {
  width: 300px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 团队标签样式 */
.el-tag--warning {
  font-weight: bold;
}

/* 审核规则样式 */
.review-rules {
  margin-top: 8px;
  font-size: 14px;
}

.review-rules ul {
  margin-top: 5px;
  padding-left: 20px;
}

.review-rules li {
  margin-bottom: 4px;
}

/* 高亮无团队标签 */
.el-tag--warning.el-tag--dark {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格行样式 */
:deep(.el-table__row) {
  cursor: pointer;
}

/* 为无团队的标注行添加背景色标识 */
:deep(.el-table__row.no-team-row) {
  background-color: rgba(230, 162, 60, 0.08);
}

/* 详情视图样式 */
.annotation-detail-view {
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.top-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.back-button {
  font-weight: 500;
}

.detail-title {
  padding: 10px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.detail-title h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.annotation-detail {
  padding: 20px;
}

.section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
}

.section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  min-height: 350px;
  max-height: 350px;
  width: 100%;
  overflow: visible;
  text-align: center;
  position: relative;
}

:deep(.el-image) {
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none; /* 禁用所有鼠标事件 */
}

:deep(.el-image__inner) {
  max-width: 350px !important;
  max-height: 350px !important;
  object-fit: contain;
  cursor: default !important; /* 强制使用默认光标 */
  pointer-events: none; /* 禁用所有鼠标事件 */
}

.image-error,
.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  font-size: 14px;
}

.image-error i,
.no-image i {
  font-size: 40px;
  margin-bottom: 10px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
  padding-top: 20px;
  padding-bottom: 20px;
  border-top: 1px solid #ebeef5;
}

.action-buttons .el-button {
  min-width: 80px;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 已通过标注的提示样式 */
.approved-notice {
  margin-top: 10px;
  font-size: 12px;
  color: #909399;
}

/* 审核意见样式 */
.review-notes {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border-left: 3px solid #409EFF;
  border-radius: 4px;
}
</style> 