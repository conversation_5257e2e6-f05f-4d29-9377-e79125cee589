<template>
  <div class="case-view-container">
    <div class="page-header">
      <h2>病例详情</h2>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="redirectToAnnotate" v-if="hasPermission">编辑</el-button>
        <el-tooltip content="刷新病例数据" placement="top">
          <el-button icon="el-icon-refresh" size="small" circle @click="refreshData" :loading="loading"></el-button>
        </el-tooltip>
        <el-button link @click="$router.back()">返回</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <!-- 标注后的图像 - 基本信息上方 -->
      <div class="annotated-image-section" v-if="annotatedImageUrl">
        <div class="section-header">
          <h3>病变标注图像</h3>
          <small v-if="lastRefreshTime" class="refresh-info">最后更新: {{ formatTime(lastRefreshTime) }}</small>
        </div>
        <div class="annotated-image-container">
          <el-image :src="annotatedImageUrl" fit="contain" class="annotated-image" :preview-src-list="[annotatedImageUrl]" @error="handleImageError">
            <template #error>
              <div class="image-error">
                <i class="el-icon-picture-outline"></i>
                <p>无法加载标注图像</p>
                <div class="debug-info" v-if="imageLoadError">
                  <h4>图片调试信息</h4>
                  <p><b>数据库图片路径:</b> {{ debugInfo.rawPath }}</p>
                  <p><b>完整访问URL:</b> {{ debugInfo.fullUrl }}</p>
                  <small>请检查此路径是否正确，以及后端是否提供了静态文件访问</small>
                </div>
              </div>
            </template>
          </el-image>
        </div>
      </div>
      <div v-else class="no-image-message">
        <i class="el-icon-picture-outline"></i>
        <p>此病例暂无标注图像</p>
      </div>
      
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag :type="getStatusType(caseDetail.status)">{{ caseDetail.status }}</el-tag>
        </div>
      </template>
      
      <!-- 先显示病例基本信息 -->
      <div class="case-info">
        <el-descriptions :column="4" border>
          <el-descriptions-item label="病例编号">{{ caseDetail.caseId || '无' }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.patientInfo" label="患者信息">{{ caseDetail.patientInfo }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.department" label="部位">{{ caseDetail.department }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.type" label="类型">{{ caseDetail.type }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ caseDetail.createTime || '无' }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ caseDetail.updateTime || '无' }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.note" label="备注" :span="4">
            {{ caseDetail.note }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 详细信息 -->
      <div class="detailed-case-info">
        <div class="section-header">
          <h3>详细信息</h3>
          <small v-if="dataVersion > 0" class="version-info">版本: {{ dataVersion }}</small>
        </div>
        <el-descriptions :column="3" border>
          <!-- 以下是从image_metadata中获取的其他已填写的字段 -->
          <el-descriptions-item v-if="caseDetail.lesionSize" label="病变大小">{{ caseDetail.lesionSize }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.lesionColor" label="病变颜色">{{ caseDetail.lesionColor }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.bloodFlow" label="血流信号">{{ caseDetail.bloodFlow }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.borderClarity" label="边界清晰度">{{ caseDetail.borderClarity }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.diseaseStage" label="病程阶段">{{ caseDetail.diseaseStage }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.morphologicalFeatures" label="形态特征">{{ caseDetail.morphologicalFeatures }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.symptoms" label="症状表现">{{ caseDetail.symptoms }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.symptomDetails" label="症状详情">{{ caseDetail.symptomDetails }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.complications" label="并发症">{{ caseDetail.complications }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.complicationDetails" label="并发症详情">{{ caseDetail.complicationDetails }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.diagnosisCode" label="诊断编码">{{ caseDetail.diagnosisCode }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.treatmentPriority" label="治疗优先级">{{ caseDetail.treatmentPriority }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.treatmentPlan" label="治疗方案">{{ caseDetail.treatmentPlan }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.recommendedTreatment" label="推荐治疗">{{ caseDetail.recommendedTreatment }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.contraindications" label="禁忌症">{{ caseDetail.contraindications }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.followUpSchedule" label="随访周期">{{ caseDetail.followUpSchedule }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.prognosisRating" label="预后评级">{{ caseDetail.prognosisRating }}</el-descriptions-item>
          <el-descriptions-item v-if="caseDetail.patientEducation" label="患者教育重点">{{ caseDetail.patientEducation }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script>
import api from '@/utils/api'
import { getImageUrl } from '@/utils/imageHelper'
import { API_BASE_URL } from '../config/api.config'

export default {
  name: 'CaseView',
  data() {
    return {
      caseId: this.$route.params.id,
      loading: true,
      caseDetail: {
        caseId: '',
        patientInfo: '',
        department: '',
        type: '',
        status: '',
        note: '',
        createTime: '',
        updateTime: '',
        // 新增字段
        lesionSize: '',
        lesionColor: '',
        bloodFlow: '',
        borderClarity: '',
        diseaseStage: '',
        morphologicalFeatures: '',
        symptoms: '',
        symptomDetails: '',
        complications: '',
        complicationDetails: '',
        diagnosisCode: '',
        treatmentPriority: '',
        treatmentPlan: '',
        recommendedTreatment: '',
        contraindications: '',
        followUpSchedule: '',
        prognosisRating: '',
        patientEducation: '',
        // 添加权限检查相关字段
        createdBy: '',
        teamId: ''
      },
      tags: [],
      annotatedImageUrl: null,
      imageLoadError: false,
      debugInfo: {
        rawPath: '',
        fullUrl: ''
      },
      // 新增数据刷新控制
      currentUser: null,
      lastRefreshTime: null,
      refreshInterval: 60000, // 刷新间隔，默认1分钟
      hasPermission: false,   // 用户权限标志
      dataVersion: 0          // 数据版本号，用于判断是否需要刷新
    }
  },
  created() {
    this.getCurrentUser();
    this.fetchCaseDetail();
    // 监听数据更新事件
    window.addEventListener('case-data-updated', this.handleDataUpdate);
  },
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('case-data-updated', this.handleDataUpdate);
  },
  methods: {
    // 获取当前登录用户信息
    getCurrentUser() {
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          this.currentUser = JSON.parse(userStr);
          console.log('当前用户:', this.currentUser);
        } else {
          console.warn('未找到登录用户信息');
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    },

    // 检查用户权限
    checkPermission() {
      if (!this.currentUser) {
        console.warn('未登录，无法验证权限');
        return false;
      }
      
      // 检查是否为系统管理员或标注医生
      const isAdmin = this.currentUser.role === 'ADMIN';
      const isAnnotator = this.currentUser.role === 'ANNOTATOR' || this.currentUser.role === 'DOCTOR';
      
      // 检查是否是病例创建者或所属团队成员
      const isCreator = this.caseDetail.createdBy === this.currentUser.id || 
                        this.caseDetail.createdBy === this.currentUser.customId;
      const isSameTeam = this.caseDetail.teamId && 
                        this.currentUser.teamId && 
                        this.caseDetail.teamId === this.currentUser.teamId;
      
      // 设置权限标志
      this.hasPermission = isAdmin || (isAnnotator && (isCreator || isSameTeam));
      
      console.log('权限检查结果:', this.hasPermission, {
        isAdmin, isAnnotator, isCreator, isSameTeam
      });
      
      return this.hasPermission;
    },
    
    // 处理数据更新事件
    handleDataUpdate(event) {
      // 检查是否是当前病例的更新
      if (event && event.detail && event.detail.caseId === this.caseId) {
        console.log('接收到病例数据更新事件:', event.detail);
        
        // 检查是否是当前用户的更新
        if (
          !event.detail.userId || // 如果没有指定用户ID，认为是系统级更新
          event.detail.userId === this.currentUser?.id || 
          event.detail.userId === this.currentUser?.customId
        ) {
          // 增加数据版本号，触发刷新
          this.dataVersion++;
          
          // 判断是否需要立即刷新
          const shouldRefreshNow = event.detail.immediate === true || 
                                 (Date.now() - this.lastRefreshTime) > this.refreshInterval;
          
          if (shouldRefreshNow) {
            console.log('刷新病例数据和图片');
            this.fetchCaseDetail();
          } else {
            console.log('延迟刷新，等待下次刷新周期');
          }
        }
      }
    },

    // 数据刷新控制
    shouldRefreshData() {
      // 如果没有上次刷新时间，说明是首次加载
      if (!this.lastRefreshTime) return true;
      
      // 检查是否超过刷新间隔
      const now = Date.now();
      const timeSinceLastRefresh = now - this.lastRefreshTime;
      
      return timeSinceLastRefresh > this.refreshInterval;
    },
    
    // 更新刷新时间
    updateRefreshTime() {
      this.lastRefreshTime = Date.now();
    },
    
    redirectToAnnotate() {
      // 跳转到标注页面，而不是编辑页面
      // 设置标志表明这是编辑操作，以便表单组件知道是更新现有数据而不是创建新数据
      localStorage.setItem('isEditingCase', 'true');
      
      this.$router.push({
        path: '/cases/form',
        query: { 
          imageId: this.caseId,
          edit: 'true' 
        }
      });
    },
    
    getStatusType(status) {
      const types = {
        '待标注': 'info',
        '已标注': 'success',
        '审核中': 'warning',
        '已通过': 'success',
        '已驳回': 'danger'
      }
      return types[status] || 'info'
    },
    
    async fetchCaseDetail() {
      // 检查是否需要刷新数据
      if (!this.shouldRefreshData() && this.annotatedImageUrl) {
        console.log('数据刷新间隔未到，跳过刷新');
        return;
      }
      
      this.loading = true;
      try {
        // 获取图像元数据
        const response = await api.images.getOne(this.caseId);
        const imageData = response.data;
        
        if (imageData) {
          // 准备基本信息
          this.caseDetail = {
            caseId: `CASE-${imageData.id}`,
            patientInfo: imageData.patientName || '',
            department: imageData.lesionLocation || '',
            type: imageData.diagnosisCategory || '',
            status: this.getStatusText(imageData.status),
            note: imageData.reviewNotes || '',
            createTime: this.formatDate(imageData.createdAt),
            updateTime: this.formatDate(imageData.updatedAt),
            // 新增字段
            lesionSize: imageData.lesionSize || '',
            lesionColor: imageData.lesionColor || '',
            bloodFlow: imageData.bloodFlow || '',
            borderClarity: imageData.borderClarity || '',
            diseaseStage: imageData.diseaseStage || '',
            morphologicalFeatures: imageData.morphologicalFeatures || '',
            symptoms: imageData.symptoms || '',
            symptomDetails: imageData.symptomDetails || '',
            complications: imageData.complications || '',
            complicationDetails: imageData.complicationDetails || '',
            diagnosisCode: imageData.diagnosisIcdCode || '',
            treatmentPriority: imageData.treatmentPriority || '',
            treatmentPlan: imageData.treatmentPlan || '',
            recommendedTreatment: imageData.recommendedTreatment || '',
            contraindications: imageData.contraindications || '',
            followUpSchedule: imageData.followUpSchedule || '',
            prognosisRating: imageData.prognosisRating ? imageData.prognosisRating + '分' : '',
            patientEducation: imageData.patientEducation || '',
            
            // 添加权限检查相关字段
            createdBy: imageData.createdBy || imageData.userId,
            teamId: imageData.teamId
          };
          
          // 检查用户权限
          this.checkPermission();
          
          // 继续原来的逻辑获取标注数据
          await this.fetchAnnotations();
          
          // 获取图像对，以获取标注后的图像路径
          await this.fetchImagePairs();
          
          // 更新刷新时间
          this.updateRefreshTime();
        }
      } catch (error) {
        console.error('获取病例详情失败:', error);
        this.$message.error('获取病例详情失败');
      } finally {
        this.loading = false;
      }
    },
    
    getStatusText(status) {
      const statusMap = {
        'DRAFT': '待标注',
        'SUBMITTED': '已标注',
        'PENDING': '审核中', 
        'APPROVED': '已通过',
        'REJECTED': '已驳回'
      }
      return statusMap[status] || '未知';
    },
    
    async fetchAnnotations() {
      try {
        // 获取标注数据
        const response = await api.tags.getByImageId(this.caseId);
        this.tags = response.data;
      } catch (error) {
        console.error('获取标注数据失败:', error);
      }
    },
    
    async fetchImagePairs() {
      try {
        console.log('开始获取标注图片路径，caseId:', this.caseId);
        
        // 从image_pairs表获取标注后的图像路径
        const response = await api.imagePairs.getByMetadataId(this.caseId);
        console.log('获取到image_pairs数据:', response.data);
        
        const imagePairs = response.data;
        
        if (imagePairs && imagePairs.length > 0) {
          // 获取第一个图像对的标注后图像路径
          const firstPair = imagePairs[0];
          const rawPath = firstPair.image_two_path; // 使用image_two_path
          
          // 记录原始路径
          this.debugInfo.rawPath = rawPath || 'null';
          console.log('从数据库获取的原始image_two_path:', rawPath);
          
          if (rawPath) {
            // 直接使用数据库中的原始路径
            // 不做任何路径转换，保留数据库中的路径格式：/medical/images/processed/filename.jpg
            
            // 添加服务器地址和端口
            const backendPort = process.env.VUE_APP_BACKEND_PORT || '8085';
            
            // 确保路径以斜杠开头
            const normalizedPath = rawPath.startsWith('/') ? rawPath : '/' + rawPath;
            const fullUrl = `${API_BASE_URL}${normalizedPath}`;
            
            this.debugInfo.fullUrl = fullUrl;
            console.log('最终访问URL:', fullUrl);
            
            // 使用构建的URL
            this.annotatedImageUrl = fullUrl;
          } else {
            console.warn('未找到标注后的图像路径，image_two_path为空');
            this.annotatedImageUrl = null;
          }
        } else {
          console.warn('未找到图像对记录');
          this.annotatedImageUrl = null;
          
          // 尝试从metadata获取
          await this.fallbackToImageMetadata();
        }
      } catch (error) {
        console.error('获取图像对失败:', error);
        console.error('错误详情:', error.message);
        
        // 尝试从metadata获取
        await this.fallbackToImageMetadata();
      }
    },
    
    // 从图像元数据获取图片路径（降级方法）
    async fallbackToImageMetadata() {
      try {
        console.log('尝试从image_metadata获取图片路径');
        const imageResponse = await api.images.getOne(this.caseId);
        const imageData = imageResponse.data;
        
        console.log('获取到的image_metadata数据:', imageData);
        
        if (imageData && imageData.image_two_path) {
          const rawPath = imageData.image_two_path;
          console.log('从image_metadata获取到的原始路径:', rawPath);
          
          // ===== 同样直接使用原始路径 =====
          // 添加服务器地址和端口
          const backendPort = process.env.VUE_APP_BACKEND_PORT || '8085';
          
          // 确保路径以斜杠开头
          const normalizedPath = rawPath.startsWith('/') ? rawPath : '/' + rawPath;
          const fullUrl = `${API_BASE_URL}${normalizedPath}`;
          
          this.debugInfo.rawPath = rawPath;
          this.debugInfo.fullUrl = fullUrl;
          
          console.log('最终访问URL:', fullUrl);
          this.annotatedImageUrl = fullUrl;
        }
      } catch (fallbackError) {
        console.error('降级获取图像也失败:', fallbackError.message);
        this.annotatedImageUrl = null;
      }
    },
    
    formatDate(dateString) {
      if (!dateString) return '无';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    },
    handleImageError() {
      console.error('标注图片加载失败');
      this.imageLoadError = true;
      console.log('图片加载失败的调试信息:', this.debugInfo);
    },
    
    getImageUrl,
    
    // 新增: 手动刷新数据的方法
    refreshData() {
      console.log('手动刷新数据');
      this.fetchCaseDetail();
    },

    // 格式化时间为友好格式
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }
}
</script>

<style scoped>
.case-view-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.annotated-image-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.annotated-image-section h3 {
  margin-bottom: 15px;
  margin-top: 0;
  font-size: 18px;
  color: #333;
}

.annotated-image-container {
  width: 100%;
  max-width: 300px;
  height: auto;
  overflow: visible;
  border-radius: 4px;
  border: 1px solid #eee;
  margin: 0 auto;
  box-shadow: 0 2px 4px rgba(0,0,0,.05);
  padding-bottom: 10px;
}

.annotated-image {
  width: 100%;
  max-width: 300px;
  height: auto;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.image-error, .no-image-message {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 150px;
  background-color: #f5f7fa;
  color: #909399;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

.detailed-case-info {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.detailed-case-info h3 {
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.case-info {
  margin-bottom: 20px;
}

.debug-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border: 1px dashed #ccc;
  border-radius: 4px;
  text-align: left;
  font-size: 12px;
  color: #666;
  max-width: 100%;
}

.debug-info h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.debug-info h5 {
  margin: 15px 0 5px 0;
  font-size: 13px;
  color: #444;
}

.debug-info p {
  margin: 5px 0;
  word-break: break-all;
}

.path-options {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.path-option {
  display: flex;
  flex-direction: column;
}

.path-option small {
  margin-top: 2px;
  color: #888;
}

.actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.full-paths {
  margin-top: 15px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.full-path {
  margin: 5px 0;
  word-break: break-all;
}

.hint {
  margin-top: 10px;
  font-style: italic;
  color: #909399;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.refresh-info, .version-info {
  color: #909399;
  font-size: 12px;
}

.version-info {
  background-color: #f0f9eb;
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid #e1f3d8;
}
</style> 