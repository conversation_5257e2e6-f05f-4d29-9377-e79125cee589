import api from '../../utils/api';
import { hasPermission, canAccessRoute, canAccessResource } from '../../utils/permissions';
import { storageUtils, asyncActionHandler } from '@/utils/storeHelpers';

const state = {
  user: storageUtils.getFromStorage('user') || null,
  isAuthenticated: !!storageUtils.getFromStorage('user'),
  users: [], // 添加用户列表数据，整合users.js功能
  currentUserDetails: null, // 添加当前查看的用户详情
  loading: false,
  error: null
}

const getters = {
  getUser: state => state.user,
  // 获取用户角色
  getUserRole: state => state.user ? state.user.role : null,
  // 获取用户ID - 确保只返回数字ID
  getUserId: state => state.user ? state.user.id : null,
  // 判断是否为管理员
  isAdmin: state => state.user && state.user.role === 'ADMIN',
  // 判断是否为标注医生
  isDoctor: state => state.user && state.user.role === 'DOCTOR',
  // 判断是否为审核医生
  isReviewer: state => state.user && state.user.role === 'REVIEWER',
  // 检查是否有特定权限
  hasPermission: state => permission => {
    return hasPermission(state.user?.role, permission)
  },
  // 检查是否可以访问特定路由
  canAccessRoute: state => route => {
    return canAccessRoute(state.user?.role, route)
  },
  // 检查是否可以访问特定资源
  canAccessResource: state => resourceOwnerId => {
    if (!state.user) return false
    return canAccessResource(state.user.id, resourceOwnerId, state.user.role)
  },
  // 添加用户列表的getter (从users.js)
  getAllUsers: state => state.users,
  getCurrentUserDetails: state => state.currentUserDetails,
  isUsersLoading: state => state.loading,
  getUsersError: state => state.error
}

const mutations = {
  setUser(state, user) {
    state.user = user
    state.isAuthenticated = !!user
  },
  // 添加用户列表相关的mutations (从users.js)
  setUsers(state, users) {
    state.users = users
  },
  setCurrentUserDetails(state, user) {
    state.currentUserDetails = user
  },
  setLoading(state, status) {
    state.loading = status
  },
  setError(state, error) {
    state.error = error
  }
}

const actions = {
  // 登录
  async login({ commit }, credentials) {
    asyncActionHandler.start(commit);
    
    try {
      console.log('登录请求:', credentials);
      
      // 移除硬编码的用户处理逻辑，完全依赖API返回的用户信息
      const response = await api.auth.login(credentials);
      
      // 打印原始响应
      console.log('登录API原始响应:', response);
      
      // 安全获取用户数据
      const rawData = response.data;
      console.log('登录成功，原始用户数据:', rawData);
      
      // 直接获取顶层属性，避免深层解析导致的循环引用问题
      const user = {
        id: rawData.id,
        customId: rawData.customId,
        name: rawData.name,
        email: rawData.email,
        role: rawData.role,
        department: rawData.department,
        hospital: rawData.hospital
      };
      
      // 如果有team属性，只提取必要信息
      if (rawData.team) {
        try {
          user.team = {
            id: rawData.team.id,
            name: rawData.team.name,
            description: rawData.team.description
          };
        } catch (e) {
          console.warn('提取team信息时出错:', e);
          user.team = null;
        }
      } else {
        user.team = null;
      }
      
      console.log('处理后的用户数据:', user);
      console.log('用户信息详情 - ID:', user.id, '姓名:', user.name, '角色:', user.role, 'customId:', user.customId);
      
      // 确保用户数据完整
      if (!user.name) {
        console.warn('警告: 登录响应中缺少用户姓名!');
        // 尝试从email或其他属性中获取一个默认名称
        if (user.email) {
          user.name = user.email.split('@')[0];
          console.log('使用邮箱用户名作为默认名称:', user.name);
        } else {
          user.name = '未命名用户';
        }
      }
      
      // 检查用户角色是否正确
      if (!user.role) {
        console.error('错误: 用户角色为空!');
        // 根据用户ID判断角色 - 使用通用逻辑，不再硬编码特定ID
        if (user.customId && user.customId.startsWith('1')) {
          user.role = 'ADMIN';
        } else if (user.customId && user.customId.startsWith('3')) {
          user.role = 'REVIEWER';
        } else {
          user.role = 'DOCTOR';
        }
        console.log('已根据用户ID设置默认角色:', user.role);
      }
      
      // 保存到本地存储
      storageUtils.saveToStorage('user', user);
      
      // 更新状态
      commit('setUser', user);
      
      return user;
    } catch (error) {
      console.error('登录错误详情:', error);
      return asyncActionHandler.error(commit, error);
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 注册
  async register({ commit }, userData) {
    asyncActionHandler.start(commit);
    
    try {
      const response = await api.auth.register(userData);
      return response.data;
    } catch (error) {
      return asyncActionHandler.error(commit, error);
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 注销
  logout({ commit }) {
    storageUtils.removeFromStorage('user');
    commit('setUser', null);
  },
  
  // 获取用户信息 - 重命名为fetchUserProfile以区分功能
  async fetchUserProfile({ commit }, userId) {
    asyncActionHandler.start(commit);
    
    try {
      const response = await api.users.getUser(userId);
      commit('setCurrentUserDetails', response.data);
      return response.data;
    } catch (error) {
      return asyncActionHandler.error(commit, error);
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 更新用户信息
  async updateUserProfile({ commit, state }, userData) {
    asyncActionHandler.start(commit);
    
    try {
      const response = await api.users.updateUser(state.user.id, userData);
      const updatedUser = response.data;
      
      // 更新本地存储
      storageUtils.saveToStorage('user', updatedUser);
      
      // 更新状态
      commit('setUser', updatedUser);
      
      return updatedUser;
    } catch (error) {
      return asyncActionHandler.error(commit, error);
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 从users.js整合的功能
  // 获取用户列表
  async fetchUsers({ commit }) {
    asyncActionHandler.start(commit);
    
    try {
      const response = await api.users.getAll();
      commit('setUsers', response.data);
      return response.data;
    } catch (error) {
      return asyncActionHandler.error(commit, error);
    } finally {
      asyncActionHandler.end(commit);
    }
  }
}

export default {
  state,
  getters,
  mutations,
  actions
} 