/**
 * API 工具函数
 * 提供API请求相关的通用功能
 */
import { storageUtils } from './storeHelpers';

/**
 * 标准化API路径，确保路径格式一致
 * @param {string} url 原始URL
 * @returns {string} 标准化后的URL
 */
export function normalizePath(url) {
  if (!url) return '';
  
  // 确保URL以/开头
  if (!url.startsWith('/')) {
    url = '/' + url;
  }
  
  // 处理双斜杠的情况
  url = url.replace(/\/+/g, '/');
  
  return url;
}

/**
 * 获取用户ID参数，优先使用数字ID
 * @param {string|number} userId 用户ID
 * @returns {string|number} 处理后的用户ID
 */
export function getUserIdParam(userId) {
  return userId || '';
}

/**
 * 确保对象中的指定字段为数字类型
 * @param {object} obj 要处理的对象
 * @param {array} fields 要确保为数字的字段列表
 * @returns {object} 处理后的对象
 */
export function ensureNumericFields(obj, fields) {
  if (!obj || typeof obj !== 'object') return obj;
  
  const result = { ...obj };
  
  fields.forEach(field => {
    if (result[field] !== undefined) {
      const parsedValue = parseInt(result[field], 10);
      if (!isNaN(parsedValue)) {
        result[field] = parsedValue;
      }
    }
  });
  
  return result;
}

/**
 * 获取认证头信息
 * @returns {object} 包含认证信息的头部对象
 */
export function getAuthHeaders() {
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };
  
  // 从本地存储获取用户信息
  const user = storageUtils.getFromStorage('user');
  if (user) {
    // 使用用户ID生成简化的认证头
    const userId = user.id || user.customId || '';
    headers['Authorization'] = `Bearer user_${userId}`;
    headers['X-User-ID'] = userId;
  }
  
  return headers;
}

/**
 * 处理API错误
 * @param {Error} error 错误对象
 * @returns {object} 格式化的错误信息
 */
export function handleApiError(error) {
  const errorInfo = {
    message: error.message || '未知错误',
    status: error.response ? error.response.status : null,
    data: error.response ? error.response.data : null
  };
  
  // 记录错误信息
  console.error('[API错误]', errorInfo);
  
  return errorInfo;
}

/**
 * 向URL添加时间戳，防止缓存
 * @param {string} url 原始URL
 * @returns {string} 添加时间戳后的URL
 */
export function addTimestamp(url) {
  const timestamp = `_t=${Date.now()}`;
  if (url.includes('?')) {
    return `${url}&${timestamp}`;
  } else {
    return `${url}?${timestamp}`;
  }
}

export default {
  normalizePath,
  getUserIdParam,
  ensureNumericFields,
  getAuthHeaders,
  handleApiError,
  addTimestamp
}; 