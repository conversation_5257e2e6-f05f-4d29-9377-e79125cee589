package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.repository.ImagePairRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/image-pairs")
public class ImagePairController {

    @Autowired
    private ImagePairRepository imagePairRepository;
    
    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    // 获取特定图像的图像对
    @GetMapping("/metadata/{metadataId}")
    public ResponseEntity<?> getImagePairsByMetadataId(@PathVariable String metadataId) {
        try {
            // 记录原始请求ID
            System.out.println("获取图像对: 原始metadataId=" + metadataId);
            
            // 使用优化过的统一处理方法查询图像对
            List<ImagePair> imagePairs = findImagePairsByStringId(metadataId);
            
            if (imagePairs.isEmpty()) {
                System.out.println("未找到ID为 " + metadataId + " 的图像对");
                return ResponseEntity.ok(Collections.emptyList());
            }
            
            System.out.println("找到 " + imagePairs.size() + " 个图像对，ID: " + metadataId);
            return processAndReturnImagePairs(imagePairs);
            
        } catch (Exception e) {
            System.err.println("获取图像对时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("获取图像对失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理和返回图像对数据
     */
    private ResponseEntity<List<Map<String, Object>>> processAndReturnImagePairs(List<ImagePair> imagePairs) {
        List<Map<String, Object>> processedPairs = new ArrayList<>();
        
        for (ImagePair pair : imagePairs) {
            Map<String, Object> pairData = new HashMap<>();
            pairData.put("id", pair.getId());
            
            // 处理metadata_id - 优先使用formattedId
            if (pair.getMetadata() != null && pair.getMetadata().getFormattedId() != null) {
                pairData.put("metadata_id", pair.getMetadata().getFormattedId());
                pairData.put("real_id", pair.getMetadataId()); // 保留实际数据库ID作为real_id
            } else {
                pairData.put("metadata_id", pair.getMetadataId());
            }
            
            pairData.put("image_one_path", convertFilePathToUrl(pair.getImageOnePath()));
            pairData.put("image_two_path", convertFilePathToUrl(pair.getImageTwoPath()));
            pairData.put("description", pair.getDescription());
            
            // 添加图像元数据信息，包括formattedId
            if (pair.getMetadata() != null) {
                Map<String, Object> metadataInfo = new HashMap<>();
                metadataInfo.put("id", pair.getMetadata().getFormattedId() != null ? 
                        pair.getMetadata().getFormattedId() : pair.getMetadata().getId());
                metadataInfo.put("formatted_id", pair.getMetadata().getFormattedId());
                metadataInfo.put("real_id", pair.getMetadata().getId());
                metadataInfo.put("filename", pair.getMetadata().getFilename());
                pairData.put("metadata", metadataInfo);
            }
            
            processedPairs.add(pairData);
        }
        
        return ResponseEntity.ok(processedPairs);
    }
    
    /**
     * 将文件系统路径转换为URL路径
     */
    private String convertFilePathToUrl(String filePath) {
        if (filePath == null) return null;
        
        // 转换为前端可访问的URL
        String url = filePath;
        
        // 处理Windows路径分隔符
        url = url.replace('\\', '/');
        
        // 提取文件名
        String fileName = url.substring(url.lastIndexOf('/') + 1);
        
        // 如果路径包含以下关键词，则根据实际存放位置构建URL
        if (url.contains("/medical_images/") || url.contains("\\medical_images\\")) {
            // 检查是在哪个子目录下
            if (url.contains("/original/") || url.contains("\\original\\")) {
                url = "/medical/images/original/" + fileName;
            } else if (url.contains("/processed/") || url.contains("\\processed\\")) {
                url = "/medical/images/processed/" + fileName;
            } else if (url.contains("/annotate/") || url.contains("\\annotate\\")) {
                url = "/medical/images/annotate/" + fileName;
            } else if (url.contains("/temp/") || url.contains("\\temp\\")) {
                url = "/medical/images/temp/" + fileName;
            } else {
                // 默认放在根目录
                url = "/medical/images/" + fileName;
            }
        }
        
        return url;
    }
    
    // 获取单个图像对
    @GetMapping("/{id}")
    public ResponseEntity<ImagePair> getImagePairById(@PathVariable Long id) {
        Optional<ImagePair> imagePair = imagePairRepository.findById(id);
        if (imagePair.isPresent()) {
            return ResponseEntity.ok(imagePair.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    // 创建新图像对
    @PostMapping
    public ResponseEntity<?> createImagePair(@RequestBody ImagePair imagePair) {
        try {
            ImagePair savedImagePair = imagePairRepository.save(imagePair);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedImagePair);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("创建图像对失败: " + e.getMessage());
        }
    }
    
    // 更新图像对
    @PutMapping("/{id}")
    public ResponseEntity<?> updateImagePair(@PathVariable Long id, @RequestBody ImagePair imagePair) {
        if (!imagePairRepository.existsById(id)) {
            return ResponseEntity.notFound().build();
        }
        
        imagePair.setId(id);
        ImagePair updatedImagePair = imagePairRepository.save(imagePair);
        return ResponseEntity.ok(updatedImagePair);
    }
    
    // 删除图像对
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteImagePair(@PathVariable Long id) {
        if (!imagePairRepository.existsById(id)) {
            return ResponseEntity.notFound().build();
        }
        
        imagePairRepository.deleteById(id);
        return ResponseEntity.ok().build();
    }
    
    // 删除与特定图像相关的所有图像对
    @DeleteMapping("/metadata/{metadataId}")
    @Transactional
    public ResponseEntity<?> deleteImagePairsByMetadataId(@PathVariable String metadataId) {
        try {
            // 尝试转换ID为长整型
            Long convertedId;
            
            try {
                // 处理各种格式的ID
                if (metadataId.contains("_")) {
                    // 如果是新格式的ID (包含时间戳和UUID)，直接查找完整匹配的图像记录
                    Optional<ImageMetadata> exactMatch = imageMetadataRepository.findByFilename(metadataId);
                    if (exactMatch.isPresent()) {
                        convertedId = exactMatch.get().getId();
                    } else {
                        // 如果找不到精确匹配，尝试数值转换
                        if (metadataId.length() > 10) {
                            // 可能是时间戳ID，转换为Long
                            convertedId = Long.valueOf(metadataId);
                        } else {
                            convertedId = Long.valueOf(metadataId);
                        }
                    }
                } else {
                    convertedId = Long.valueOf(metadataId);
                }
            } catch (NumberFormatException e) {
                return ResponseEntity.badRequest().body("无效的图像ID: " + metadataId);
            }
            
            imagePairRepository.deleteByMetadataId(convertedId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("删除图像对失败: " + e.getMessage());
        }
    }

    // 添加一个新的API端点，使用查询参数处理大型ID
    @GetMapping("/by-metadata")
    public ResponseEntity<?> getImagePairsByMetadataIdAsQueryParam(@RequestParam("id") String metadataId) {
        System.out.println("使用查询参数获取图像对: id=" + metadataId);
        try {
            // 直接将字符串ID传递给查询服务
            List<ImagePair> imagePairs = findImagePairsByStringId(metadataId);
            
            // 处理路径，确保前端可以访问
            for (ImagePair pair : imagePairs) {
                // 转换路径为Web可访问路径
                if (pair.getImageOnePath() != null) {
                    pair.setImageOnePath(getWebAccessiblePath(pair.getImageOnePath()));
                }
                if (pair.getImageTwoPath() != null) {
                    pair.setImageTwoPath(getWebAccessiblePath(pair.getImageTwoPath()));
                }
            }
            
            return processAndReturnImagePairs(imagePairs);
        } catch (Exception e) {
            System.err.println("通过查询参数获取图像对时出错: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Failed to get image pairs: " + e.getMessage());
        }
    }

    // 通用方法处理字符串ID查询
    private List<ImagePair> findImagePairsByStringId(String metadataId) {
        try {
            System.out.println("处理字符串ID查询: " + metadataId);
            
            // 首先尝试通过formattedId查找ImageMetadata
            Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findByFormattedId(metadataId);
            
            if (metadataOpt.isPresent()) {
                // 如果找到了ImageMetadata，使用其ID查找ImagePair
                Long actualId = metadataOpt.get().getId();
                System.out.println("通过formattedId找到图像元数据，使用实际ID查询: " + actualId);
                
                List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(actualId);
                System.out.println("找到 " + imagePairs.size() + " 个图像对");
                
                // 检查并处理重复记录
                if (imagePairs.size() > 1) {
                    System.out.println("警告：发现重复的ImagePair记录，进行清理...");
                    imagePairs = cleanupDuplicateImagePairs(imagePairs, actualId);
                }
                
                return imagePairs;
            }
            
            // 如果未通过formattedId找到，尝试去除前导零
            if (metadataId.startsWith("0")) {
                String idWithoutLeadingZeros = metadataId.replaceFirst("^0+", "");
                if (!idWithoutLeadingZeros.isEmpty()) {
                    try {
                        // 尝试将不带前导零的ID转换为Long，作为实际的数据库ID
                        Long numericId = Long.parseLong(idWithoutLeadingZeros);
                        System.out.println("尝试使用去除前导零后的ID: " + numericId);
                        
                        // 先尝试直接用作数据库ID查询
                        List<ImagePair> imagePairsByNumericId = imagePairRepository.findByMetadataId(numericId);
                        if (!imagePairsByNumericId.isEmpty()) {
                            System.out.println("通过去除前导零后的ID找到 " + imagePairsByNumericId.size() + " 个图像对");
                            
                            // 检查并处理重复记录
                            if (imagePairsByNumericId.size() > 1) {
                                System.out.println("警告：发现重复的ImagePair记录，进行清理...");
                                imagePairsByNumericId = cleanupDuplicateImagePairs(imagePairsByNumericId, numericId);
                            }
                            
                            return imagePairsByNumericId;
                        }
                        
                        // 如果仍未找到，尝试用作formattedId查询
                        Optional<ImageMetadata> metadataByStrippedId = imageMetadataRepository.findByFormattedId(idWithoutLeadingZeros);
                        if (metadataByStrippedId.isPresent()) {
                            Long actualId = metadataByStrippedId.get().getId();
                            System.out.println("通过去除前导零后的formattedId找到元数据，使用实际ID: " + actualId);
                            
                            List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(actualId);
                            System.out.println("找到 " + imagePairs.size() + " 个图像对");
                            
                            // 检查并处理重复记录
                            if (imagePairs.size() > 1) {
                                System.out.println("警告：发现重复的ImagePair记录，进行清理...");
                                imagePairs = cleanupDuplicateImagePairs(imagePairs, actualId);
                            }
                            
                            return imagePairs;
                        }
                    } catch (NumberFormatException e) {
                        System.out.println("无法将ID转换为数字: " + idWithoutLeadingZeros);
                    }
                }
            }
            
            // 最后尝试直接将ID转换为Long
            try {
                Long numericId = Long.parseLong(metadataId);
                System.out.println("尝试直接使用数字ID: " + numericId);
                
                List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(numericId);
                System.out.println("找到 " + imagePairs.size() + " 个图像对");
                
                // 检查并处理重复记录
                if (imagePairs.size() > 1) {
                    System.out.println("警告：发现重复的ImagePair记录，进行清理...");
                    imagePairs = cleanupDuplicateImagePairs(imagePairs, numericId);
                }
                
                return imagePairs;
            } catch (NumberFormatException e) {
                System.out.println("无效的ID格式，无法转换为数字: " + metadataId);
            }
            
            // 如果所有尝试都失败，返回空列表
            System.out.println("未找到ID为 " + metadataId + " 的图像对");
            return Collections.emptyList();
        } catch (Exception e) {
            System.err.println("查询图像对时出错: " + e.getMessage());
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    /**
     * 清理重复的ImagePair记录，保留最新的一个，删除其他的
     * @param imagePairs 重复的ImagePair记录列表
     * @param metadataId 元数据ID
     * @return 清理后的ImagePair记录列表
     */
    private List<ImagePair> cleanupDuplicateImagePairs(List<ImagePair> imagePairs, Long metadataId) {
        if (imagePairs == null || imagePairs.size() <= 1) {
            return imagePairs;
        }
        
        try {
            System.out.println("开始清理重复的ImagePair记录，共 " + imagePairs.size() + " 条，metadataId=" + metadataId);
            
            // 按创建时间降序排序，保留最新的一条
            imagePairs.sort((a, b) -> {
                if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
                if (a.getCreatedAt() == null) return 1;
                if (b.getCreatedAt() == null) return -1;
                return b.getCreatedAt().compareTo(a.getCreatedAt());
            });
            
            // 获取最新的记录
            ImagePair latestPair = imagePairs.get(0);
            System.out.println("保留最新记录，ID: " + latestPair.getId() + ", 创建时间: " + latestPair.getCreatedAt());
            
            // 删除其他记录
            for (int i = 1; i < imagePairs.size(); i++) {
                ImagePair pair = imagePairs.get(i);
                System.out.println("删除重复记录，ID: " + pair.getId() + ", 创建时间: " + pair.getCreatedAt());
                imagePairRepository.deleteById(pair.getId());
            }
            
            // 返回只包含最新记录的列表
            return Collections.singletonList(latestPair);
            
        } catch (Exception e) {
            System.err.println("清理重复ImagePair记录时出错: " + e.getMessage());
            e.printStackTrace();
            // 出错时返回原始列表
            return imagePairs;
        }
    }

    // getWebAccessiblePath方法
    private String getWebAccessiblePath(String path) {
        if (path == null) return null;
        
        // 如果已经是web路径，直接返回
        if (path.startsWith("/medical") || path.startsWith("http")) {
            return path;
        }
        
        // 转换本地路径为web路径
        String webPath = path;
        
        // 处理Windows路径
        if (path.contains("\\")) {
            // 提取文件名
            String fileName = path.substring(path.lastIndexOf("\\") + 1);
            
            // 根据路径确定子目录
            if (path.contains("\\original\\")) {
                webPath = "/medical/images/original/" + fileName;
            } else if (path.contains("\\processed\\")) {
                webPath = "/medical/images/processed/" + fileName;
            } else if (path.contains("\\annotate\\")) {
                webPath = "/medical/images/annotate/" + fileName;
            } else if (path.contains("\\temp\\")) {
                webPath = "/medical/images/temp/" + fileName;
            } else {
                // 默认为temp目录
                webPath = "/medical/images/temp/" + fileName;
            }
        }
        
        return webPath;
    }
} 