-- 创建图像元数据表 (如果不存在)
CREATE TABLE IF NOT EXISTS image_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    formatted_id VARCHAR(9) UNIQUE,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    path MEDIUMTEXT NOT NULL,
    mimetype VARCHAR(50) NOT NULL,
    size INT NOT NULL,
    width INT,
    height INT,
    uploaded_by INT NOT NULL,
    uploaded_by_custom_id VARCHAR(9) COMMENT '上传用户的9位数自定义ID',
    patient_name VARCHAR(100),
    patient_age INT,
    patient_gender VARCHAR(10),
    case_number VARCHAR(50) UNIQUE,
    lesion_location VARCHAR(100),
    lesion_size VARCHAR(50),
    lesion_color VARCHAR(50),
    border_clarity VARCHAR(50),
    blood_flow VARCHAR(50),
    diagnosis_category VARCHAR(100),
    disease_stage VARCHAR(50),
    morphological_features TEXT,
    symptoms TEXT,
    symptom_details TEXT,
    complications TEXT,
    complication_details TEXT,
    diagnosis_icd_code VARCHAR(20),
    treatment_priority VARCHAR(20),
    recommended_treatment TEXT,
    treatment_plan TEXT,
    contraindications TEXT,
    follow_up_schedule VARCHAR(100),
    prognosis_rating INT,
    patient_education TEXT,
    acquisition_date DATE,
    modality VARCHAR(50),
    study_description TEXT,
    status VARCHAR(20) DEFAULT 'DRAFT',
    reviewed_by INT,
    reviewed_by_custom_id VARCHAR(9) COMMENT '审核用户的9位数自定义ID',
    review_notes TEXT,
    review_date DATETIME,
    created_at DATETIME,
    updated_at DATETIME,
    image_two_path MEDIUMTEXT COMMENT '标注后的图像路径',
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id)
);

-- 创建标签表 (如果不存在)
CREATE TABLE IF NOT EXISTS tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metadata_id BIGINT NOT NULL,
    tag VARCHAR(50) NOT NULL,
    x INT NOT NULL,
    y INT NOT NULL,
    width INT NOT NULL,
    height INT NOT NULL,
    created_by INT,
    created_by_custom_id VARCHAR(9) COMMENT '创建者的9位数自定义ID',
    created_at DATETIME,
    FOREIGN KEY (metadata_id) REFERENCES image_metadata(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 创建图像对表 (如果不存在)
CREATE TABLE IF NOT EXISTS image_pairs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metadata_id BIGINT NOT NULL,
    image_one_path MEDIUMTEXT NOT NULL COMMENT '处理后的图像路径(temp目录)',
    image_two_path MEDIUMTEXT COMMENT '标注后的图像路径(annotate目录)',
    description VARCHAR(255) COMMENT '描述',
    created_by INT COMMENT '创建用户ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (metadata_id) REFERENCES image_metadata(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;