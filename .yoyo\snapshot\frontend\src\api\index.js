// 导入axios和API配置
import axios from 'axios';
import { API_URL } from '../config/api.config';

// 创建一个配置好baseURL的axios实例
const apiClient = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 认证相关API
const auth = {
  login(credentials) {
    return apiClient.post('/auth/login', credentials);
  },
  register(userData) {
    return apiClient.post('/auth/register', userData);
  },
  logout() {
    return apiClient.post('/auth/logout');
  }
};

// 用户相关API
const user = {
  getCurrentUser() {
    return apiClient.get('/users/current');
  },
  getUserByEmail(email) {
    return apiClient.get(`/users/email/${email}`);
  },
  getUserById(id) {
    return apiClient.get(`/users/${id}`);
  },
  getAllUsers() {
    return apiClient.get('/users');
  }
};

// 团队相关API
const teams = {
  getAllTeams() {
    return apiClient.get('/teams');
  },
  getTeamById(id) {
    return apiClient.get(`/teams/${id}`);
  },
  createTeam(teamData) {
    return apiClient.post('/teams', teamData);
  },
  updateTeam(id, teamData) {
    return apiClient.put(`/teams/${id}`, teamData);
  }
};

// 上传相关API
const uploads = {
  uploadImage(formData) {
    return apiClient.post('/uploads/image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }
};

// 元数据相关API
const metadata = {
  getMetadata(id) {
    return apiClient.get(`/metadata/${id}`);
  },
  updateMetadata(id, data) {
    return apiClient.put(`/metadata/${id}`, data);
  }
};

// 标注相关API
const annotations = {
  getUserAnnotations() {
    return apiClient.get('/annotations/user');
  },
  getTeamAnnotations() {
    return apiClient.get('/annotations/team');
  }
};

// 标签相关API
const tags = {
  getAllTags() {
    return apiClient.get('/tags');
  },
  createTag(tagData) {
    return apiClient.post('/tags', tagData);
  }
};

// 图像对相关API
const imagePairs = {
  createImagePair(data) {
    return apiClient.post('/image-pairs', data);
  },
  getImagePairsByMetadata(metadataId) {
    return apiClient.get(`/image-pairs/metadata/${metadataId}`);
  }
};

// 团队申请相关API
const teamApplications = {
  getUserApplications() {
    return apiClient.get('/team-applications/user');
  },
  createApplication(data) {
    return apiClient.post('/team-applications', data);
  },
  approveApplication(id) {
    return apiClient.post(`/team-applications/${id}/approve`);
  },
  rejectApplication(id, reason) {
    return apiClient.post(`/team-applications/${id}/reject`, null, {
      params: { reason }
    });
  },
  getPendingApplications() {
    return apiClient.get('/team-applications/pending');
  }
};

// 添加标注审核相关API
const reviews = {
  // 获取待审核标注列表
  getPendingReviews(params) {
    return apiClient.get('/reviews/pending', { params });
  },
  
  // 获取标注详情（用于审核）
  getAnnotationForReview(id) {
    return apiClient.get(`/reviews/${id}`);
  },
  
  // 提交审核结果
  submitReview(id, data) {
    return apiClient.post(`/reviews/${id}/review`, null, {
      params: {
        approved: data.approved,
        reviewNotes: data.reviewNotes,
        updateStatus: data.updateStatus || false,
        status: data.status || ''
      }
    });
  }
};

// 导出API对象
export default {
  auth,
  user,
  teams,
  uploads,
  metadata,
  annotations,
  tags,
  imagePairs,
  teamApplications,
  reviews
}; 