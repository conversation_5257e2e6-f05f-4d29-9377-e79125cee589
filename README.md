# 血管瘤诊断平台

## 项目结构

```
xueguan2/
├── src/                  # Java后端代码
├── frontend/             # 前端代码
├── ai-service/           # AI服务代码和模型
├── config/               # 配置文件
│   ├── dev/              # 开发环境配置
│   └── prod/             # 生产环境配置
├── docs/                 # 文档
│   ├── user/             # 用户文档
│   ├── dev/              # 开发文档
│   └── legal/            # 法律文档
├── resources/            # 资源文件
│   ├── images/           # 图片资源
│   ├── models/           # 模型文件
│   └── data/             # 数据文件
├── scripts/              # 脚本文件
├── logs/                 # 日志文件
├── target/               # 编译输出
├── start_all.bat         # 启动所有服务
└── pom.xml               # Maven配置
```

## 快速开始

1. 执行 `start_all.bat` 启动所有服务
2. 后端服务将在端口 8080 上运行
3. AI服务将在端口 8086 上运行
4. 前端将自动打开浏览器

## 系统组件

- **后端**: Spring Boot应用，处理数据存储、用户认证和业务逻辑
- **前端**: Vue.js应用，提供用户界面和交互
- **AI服务**: Python应用，提供血管瘤诊断能力

## 开发指南

详细的开发文档请参考 `docs/dev/` 目录下的文件：
- 软件设计说明书
- 路径配置说明
- 图像处理逻辑

## 用户指南

用户操作手册位于 `docs/user/` 目录 