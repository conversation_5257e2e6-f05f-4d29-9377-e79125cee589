package com.medical.annotation.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件名和图像元数据生成工具类
 * 用于生成唯一的文件名和原始文件名
 */
public class FileNameGenerator {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    /**
     * 生成唯一的文件名
     * 使用UUID + 时间戳 + 原始文件名的组合确保唯一性
     * 
     * @param originalFileName 原始文件名
     * @return 唯一的文件名
     */
    public static String generateUniqueFileName(String originalFileName) {
        // 生成UUID（去除连字符以缩短长度）
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 12);
        
        // 获取当前时间戳
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        
        // 获取文件扩展名
        String extension = getFileExtension(originalFileName);
        
        // 组合成唯一文件名: uuid_timestamp.extension
        return uuid + "_" + timestamp + extension;
    }
    
    /**
     * 生成唯一的原始文件名
     * 保留原文件名但加上时间戳前缀确保唯一
     * 
     * @param originalFileName 原始文件名
     * @return 唯一的原始文件名
     */
    public static String generateUniqueOriginalName(String originalFileName) {
        // 获取文件名（不含扩展名）
        String nameWithoutExt = removeFileExtension(originalFileName);
        
        // 获取文件扩展名
        String extension = getFileExtension(originalFileName);
        
        // 获取当前时间戳
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        
        // 组合成唯一原始文件名: name_timestamp.extension
        return nameWithoutExt + "_" + timestamp + extension;
    }
    
    /**
     * 为测试目的生成唯一ID
     * 基于当前时间的毫秒时间戳，适合用作数据库ID
     * 
     * @return 作为ID使用的时间戳
     */
    public static long generateUniqueId() {
        return System.currentTimeMillis();
    }
    
    /**
     * 获取文件扩展名（包括点号）
     * 
     * @param fileName 文件名
     * @return 文件扩展名（例如 ".jpg"）
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty() || !fileName.contains(".")) {
            return ".jpg"; // 默认扩展名
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }
    
    /**
     * 移除文件扩展名，只保留文件名部分
     * 
     * @param fileName 文件名
     * @return 不含扩展名的文件名
     */
    private static String removeFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty() || !fileName.contains(".")) {
            return fileName;
        }
        return fileName.substring(0, fileName.lastIndexOf("."));
    }
} 