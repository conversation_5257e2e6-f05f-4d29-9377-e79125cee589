// 在浏览器控制台中执行这个脚本，用于调试"最近标注任务"不显示的问题

// 第一步：检查当前状态
console.log("===== 调试信息 =====");
const store = window.__VUE_DEVTOOLS_GLOBAL_HOOK__ ? 
  window.__VUE_DEVTOOLS_GLOBAL_HOOK__.Vue.$store : null;

if (!store) {
  console.log("无法访问Vuex store，请确保在Vue应用的页面运行此脚本");
} else {
  // 检查store中的images数据
  const images = store.state.images ? store.state.images.images : [];
  console.log("Store中图像数量:", images.length);
  
  if (images.length > 0) {
    console.log("第一条图像数据:", images[0]);
  } else {
    console.log("Store中没有图像数据");
  }
}

// 第二步：手动拉取最新数据
console.log("\n===== 重新获取数据 =====");
// 1. 清除缓存
localStorage.removeItem('vuex');
// 2. 手动调用API获取最新数据
fetch('http://192.168.2.43:8085/medical/api/images')
  .then(response => response.json())
  .then(data => {
    console.log("API返回数据数量:", data.length);
    if (data.length > 0) {
      console.log("API返回的第一条数据:", data[0]);
      console.log("所有数据的status字段值:", data.map(item => item.status).join(', '));
      
      // 检查数据中是否包含测试数据
      const testData = data.filter(item => item.id >= 101 && item.id <= 105);
      console.log("测试数据数量:", testData.length);
    }
  })
  .catch(error => console.error("获取数据失败:", error));

// 第三步：指导如何修复
console.log("\n===== 修复建议 =====");
console.log("1. 尝试刷新页面，清除缓存后重新登录");
console.log("2. 检查测试数据字段是否与代码中的字段名称匹配");
console.log("3. 注意观察上方API返回的数据结构，确认必须字段是否存在");
console.log("4. 如有必要，请执行下面的重置代码");

console.log("\n要强制刷新并清除Dashboard组件的缓存，可以在控制台执行:");
console.log('setTimeout(() => { window.location.reload(true); }, 1000);');

// 该脚本将帮助诊断问题并提供解决方案 