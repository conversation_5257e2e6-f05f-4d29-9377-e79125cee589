<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>血管瘤图像注册工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
        }
    </style>
</head>
<body>
    <h1>血管瘤图像注册工具</h1>
    <p>此工具用于为已存在的图像创建数据库记录</p>
    
    <div class="form-group">
        <label for="filename">图像文件名:</label>
        <input type="text" id="filename" placeholder="例如: avm_025_007.jpg">
    </div>
    
    <div class="form-group">
        <label for="userId">用户ID:</label>
        <input type="text" id="userId" value="2">
    </div>
    
    <button onclick="registerImage()">创建数据库记录</button>
    
    <div id="result"></div>

    <script>
        function registerImage() {
            const filename = document.getElementById('filename').value;
            const userId = document.getElementById('userId').value;
            const resultDiv = document.getElementById('result');
            
            if (!filename) {
                showResult('请输入图像文件名', 'error');
                return;
            }
            
            // 准备请求数据
            const data = {
                filename: filename,
                user_id: parseInt(userId) || 2
            };
            
            // 发送POST请求
            fetch('http://localhost:8080/api/images/register-medical', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应失败');
                }
                return response.json();
            })
            .then(data => {
                console.log('成功:', data);
                showResult(`成功创建数据库记录! ID: ${data.id}, 图像路径: ${data.path}`, 'success');
            })
            .catch(error => {
                console.error('错误:', error);
                showResult('创建数据库记录失败: ' + error.message, 'error');
            });
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = type;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html> 