// 修复webpack中的模块解析问题
import 'core-js/stable';
import 'regenerator-runtime/runtime';

// 全局处理Node.js内置模块
if (typeof window !== 'undefined') {
  window.process = window.process || {};
  window.process.browser = true;
  window.process.env = window.process.env || {};
  window.Buffer = window.Buffer || require('buffer').Buffer;
}

// 修复util._extend弃用警告
if (typeof global !== 'undefined' && global.util && global.util._extend) {
  global.util._extend = Object.assign;
}

// 导出一个空对象，表明这是一个模块
export default {}; 