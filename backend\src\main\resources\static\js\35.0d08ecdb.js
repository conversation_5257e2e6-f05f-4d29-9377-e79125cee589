"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[35],{6035:(e,t,a)=>{a.r(t),a.d(t,{default:()=>O});var n=a(61431),s={class:"container mt-4"},r={class:"d-flex justify-content-between align-items-center mb-4"},l={class:"card mb-4"},i={class:"card-body"},c={class:"row"},u={class:"col-md-3 mb-2"},o={class:"col-md-3 mb-2"},d={class:"col-md-3 mb-2"},p={class:"col-md-3 mb-2"},f={class:"d-flex justify-content-end mt-2"},k={key:0,class:"loader-container"},m={key:1,class:"alert alert-danger"},g={key:2,class:"text-center py-5"},b={key:3,class:"row"},v={class:"card h-100"},L={class:"image-container"},y=["src","alt"],h={class:"card-body"},C={class:"card-title"},E={class:"card-text"},A={class:"text-muted"},w={class:"card-text"},F={class:"badge bg-secondary ms-2"},D={class:"card-text"},_={class:"card-footer bg-transparent"},P={class:"d-flex justify-content-between"},I=["onClick"],T=["onClick"],x={key:4,class:"mt-4"},B={class:"pagination justify-content-center"},R=["onClick"];function S(e,t,a,S,X,W){var q=(0,n.g2)("router-link");return(0,n.uX)(),(0,n.CE)("div",s,[(0,n.Lk)("div",r,[t[9]||(t[9]=(0,n.Lk)("h2",null,"图像管理",-1)),(0,n.Lk)("div",null,[(0,n.bF)(q,{to:"/images/upload",class:"btn btn-success"},{default:(0,n.k6)((function(){return t[8]||(t[8]=[(0,n.Lk)("i",{class:"bi bi-plus-circle me-1"},null,-1),(0,n.eW)(" 上传新图像 ")])})),_:1,__:[8]})])]),(0,n.Lk)("div",l,[(0,n.Lk)("div",i,[(0,n.Lk)("div",c,[(0,n.Lk)("div",u,[t[11]||(t[11]=(0,n.Lk)("label",{for:"statusFilter",class:"form-label"},"状态",-1)),(0,n.bo)((0,n.Lk)("select",{id:"statusFilter","onUpdate:modelValue":t[0]||(t[0]=function(e){return S.filters.status=e}),class:"form-select"},t[10]||(t[10]=[(0,n.Fv)('<option value="" data-v-2280d7ce>全部</option><option value="DRAFT" data-v-2280d7ce>草稿</option><option value="SUBMITTED" data-v-2280d7ce>待审核</option><option value="APPROVED" data-v-2280d7ce>已通过</option><option value="REJECTED" data-v-2280d7ce>未通过</option>',5)]),512),[[n.u1,S.filters.status]])]),(0,n.Lk)("div",o,[t[12]||(t[12]=(0,n.Lk)("label",{for:"patientFilter",class:"form-label"},"患者名称",-1)),(0,n.bo)((0,n.Lk)("input",{id:"patientFilter","onUpdate:modelValue":t[1]||(t[1]=function(e){return S.filters.patientName=e}),type:"text",class:"form-control"},null,512),[[n.Jo,S.filters.patientName]])]),(0,n.Lk)("div",d,[t[13]||(t[13]=(0,n.Lk)("label",{for:"diagnosticFilter",class:"form-label"},"诊断类别",-1)),(0,n.bo)((0,n.Lk)("input",{id:"diagnosticFilter","onUpdate:modelValue":t[2]||(t[2]=function(e){return S.filters.diagnosisCategory=e}),type:"text",class:"form-control"},null,512),[[n.Jo,S.filters.diagnosisCategory]])]),(0,n.Lk)("div",p,[t[15]||(t[15]=(0,n.Lk)("label",{for:"sortBy",class:"form-label"},"排序方式",-1)),(0,n.bo)((0,n.Lk)("select",{id:"sortBy","onUpdate:modelValue":t[3]||(t[3]=function(e){return S.sortBy=e}),class:"form-select"},t[14]||(t[14]=[(0,n.Lk)("option",{value:"createdAt"},"上传时间",-1),(0,n.Lk)("option",{value:"patientName"},"患者姓名",-1),(0,n.Lk)("option",{value:"status"},"状态",-1)]),512),[[n.u1,S.sortBy]])])]),(0,n.Lk)("div",f,[(0,n.Lk)("button",{class:"btn btn-primary me-2",onClick:t[4]||(t[4]=function(){return S.applyFilters&&S.applyFilters.apply(S,arguments)})},t[16]||(t[16]=[(0,n.Lk)("i",{class:"bi bi-funnel me-1"},null,-1),(0,n.eW)(" 筛选 ")])),(0,n.Lk)("button",{class:"btn btn-outline-secondary",onClick:t[5]||(t[5]=function(){return S.resetFilters&&S.resetFilters.apply(S,arguments)})},t[17]||(t[17]=[(0,n.Lk)("i",{class:"bi bi-arrow-repeat me-1"},null,-1),(0,n.eW)(" 重置 ")]))])])]),S.loading?((0,n.uX)(),(0,n.CE)("div",k,t[18]||(t[18]=[(0,n.Lk)("div",{class:"spinner-border text-primary",role:"status"},[(0,n.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):S.error?((0,n.uX)(),(0,n.CE)("div",m,(0,n.v_)(S.error),1)):0===S.images.length?((0,n.uX)(),(0,n.CE)("div",g,t[19]||(t[19]=[(0,n.Lk)("div",{class:"mb-3"},[(0,n.Lk)("i",{class:"bi bi-images",style:{"font-size":"3rem"}})],-1),(0,n.Lk)("h4",null,"暂无图像数据",-1),(0,n.Lk)("p",{class:"text-muted"},'点击上方"上传新图像"按钮添加图像',-1)]))):((0,n.uX)(),(0,n.CE)("div",b,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(S.images,(function(e){return(0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"col-md-4 mb-4"},[(0,n.Lk)("div",v,[(0,n.Lk)("div",L,[(0,n.Lk)("img",{src:e.path,alt:e.filename,class:"card-img-top"},null,8,y)]),(0,n.Lk)("div",h,[(0,n.Lk)("h5",C,(0,n.v_)(e.filename),1),(0,n.Lk)("p",E,[(0,n.Lk)("small",A," 患者: "+(0,n.v_)(e.patientName||"未知")+", "+(0,n.v_)(e.patientAge||"?")+"岁, "+(0,n.v_)(e.patientGender||"未知"),1)]),(0,n.Lk)("p",w,[(0,n.Lk)("span",{class:(0,n.C4)(S.getStatusBadgeClass(e.status))},(0,n.v_)(S.getStatusText(e.status)),3),(0,n.Lk)("span",F,(0,n.v_)(e.mimetype),1)]),(0,n.Lk)("p",D,[(0,n.Lk)("small",null," 上传时间: "+(0,n.v_)(S.formatDate(e.createdAt)),1)])]),(0,n.Lk)("div",_,[(0,n.Lk)("div",P,[(0,n.bF)(q,{to:"/images/".concat(e.id),class:"btn btn-primary"},{default:(0,n.k6)((function(){return t[20]||(t[20]=[(0,n.Lk)("i",{class:"bi bi-eye me-1"},null,-1),(0,n.eW)(" 查看 ")])})),_:2,__:[20]},1032,["to"]),(0,n.Lk)("div",null,["DRAFT"===e.status?((0,n.uX)(),(0,n.CE)("button",{key:0,class:"btn btn-warning me-2",onClick:function(t){return S.submitImage(e.id)}},t[21]||(t[21]=[(0,n.Lk)("i",{class:"bi bi-send me-1"},null,-1),(0,n.eW)(" 提交 ")]),8,I)):(0,n.Q3)("",!0),(0,n.Lk)("button",{class:"btn btn-danger",onClick:function(t){return S.confirmDelete(e.id)}},t[22]||(t[22]=[(0,n.Lk)("i",{class:"bi bi-trash me-1"},null,-1),(0,n.eW)(" 删除 ")]),8,T)])])])])])})),128))])),S.images.length>0?((0,n.uX)(),(0,n.CE)("nav",x,[(0,n.Lk)("ul",B,[(0,n.Lk)("li",{class:(0,n.C4)(["page-item",{disabled:1===S.currentPage}])},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:t[6]||(t[6]=(0,n.D$)((function(e){return S.changePage(S.currentPage-1)}),["prevent"]))},"上一页")],2),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(S.totalPages,(function(e){return(0,n.uX)(),(0,n.CE)("li",{key:e,class:(0,n.C4)(["page-item",{active:e===S.currentPage}])},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:(0,n.D$)((function(t){return S.changePage(e)}),["prevent"])},(0,n.v_)(e),9,R)],2)})),128)),(0,n.Lk)("li",{class:(0,n.C4)(["page-item",{disabled:S.currentPage===S.totalPages}])},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:t[7]||(t[7]=(0,n.D$)((function(e){return S.changePage(S.currentPage+1)}),["prevent"]))},"下一页")],2)])])):(0,n.Q3)("",!0)])}var X=a(88844),W=a(24059),q=a(698),U=(a(23288),a(27495),a(25440),a(66278)),V=a(60455),K=a(16653),N=a(80142);const j={name:"ImageList",setup:function(){var e=(0,U.Pj)(),t=(0,V.lq)(),a=(0,V.rd)(),s=(0,n.EW)((function(){return e.getters.getAllImages})),r=(0,n.EW)((function(){return e.getters.isLoading})),l=(0,n.EW)((function(){return e.getters.getError})),i=(0,n.KR)(1),c=(0,n.KR)(12),u=(0,n.EW)((function(){return Math.ceil(s.value.length/c.value)})),o=(0,n.KR)({status:t.query.status||"",patientName:"",diagnosisCategory:""}),d=(0,n.KR)("createdAt");(0,n.wB)((function(){return t.query.status}),(function(e){e!==o.value.status&&(o.value.status=e||"",p())})),(0,n.sV)((0,q.A)((0,W.A)().m((function a(){var n;return(0,W.A)().w((function(a){while(1)switch(a.n){case 0:if(a.p=0,!t.query.status){a.n=2;break}return a.n=1,e.dispatch("fetchImagesByStatus",t.query.status);case 1:a.n=3;break;case 2:return a.n=3,e.dispatch("fetchImages");case 3:a.n=5;break;case 4:a.p=4,n=a.v,console.error("Failed to load images:",n);case 5:return a.a(2)}}),a,null,[[0,4]])}))));var p=function(){var n=(0,q.A)((0,W.A)().m((function n(){var s,r;return(0,W.A)().w((function(n){while(1)switch(n.n){case 0:if(n.p=0,!o.value.status){n.n=2;break}return n.n=1,e.dispatch("fetchImagesByStatus",o.value.status);case 1:a.replace({query:(0,X.A)((0,X.A)({},t.query),{},{status:o.value.status})})["catch"]((function(){})),n.n=4;break;case 2:return n.n=3,e.dispatch("fetchImages");case 3:s=(0,X.A)({},t.query),delete s.status,a.replace({query:s})["catch"]((function(){}));case 4:i.value=1,n.n=6;break;case 5:n.p=5,r=n.v,console.error("Failed to apply filters:",r);case 6:return n.a(2)}}),n,null,[[0,5]])})));return function(){return n.apply(this,arguments)}}(),f=function(){o.value={status:"",patientName:"",diagnosisCategory:""},d.value="createdAt",p()},k=function(e){e>=1&&e<=u.value&&(i.value=e)},m=function(){var t=(0,q.A)((0,W.A)().m((function t(a){var n;return(0,W.A)().w((function(t){while(1)switch(t.n){case 0:if(!confirm("确定要提交此图像进行审核吗？")){t.n=4;break}return t.p=1,t.n=2,e.dispatch("updateImageStatus",{id:a,status:"SUBMITTED"});case 2:t.n=4;break;case 3:t.p=3,n=t.v,console.error("Failed to submit image:",n);case 4:return t.a(2)}}),t,null,[[1,3]])})));return function(e){return t.apply(this,arguments)}}(),g=function(){var t=(0,q.A)((0,W.A)().m((function t(a){var n,s,r;return(0,W.A)().w((function(t){while(1)switch(t.n){case 0:if(!confirm("确定要删除此图像吗？此操作不可撤销！")){t.n=4;break}return t.p=1,n=K.Ks.service({lock:!0,text:"删除中...",background:"rgba(0, 0, 0, 0.7)"}),t.n=2,e.dispatch("deleteImage",a);case 2:s=t.v,n.close(),s.success?N.nk.success("删除成功"):(0,N.nk)({type:"error",message:s.error||"删除失败",duration:5e3}),t.n=4;break;case 3:t.p=3,r=t.v,console.error("删除图像失败:",r),N.nk.error("删除失败: "+(r.message||"未知错误"));case 4:return t.a(2)}}),t,null,[[1,3]])})));return function(e){return t.apply(this,arguments)}}(),b=function(e){switch(e){case"DRAFT":return"badge bg-primary";case"SUBMITTED":return"badge bg-warning";case"APPROVED":return"badge bg-success";case"REJECTED":return"badge bg-danger";default:return"badge bg-secondary"}},v=function(e){switch(e){case"DRAFT":return"草稿";case"SUBMITTED":return"待审核";case"APPROVED":return"已通过";case"REJECTED":return"未通过";default:return"未知"}},L=function(e){if(!e)return"未知";var t=new Date(e);return t.toLocaleString("zh-CN")};return{images:s,loading:r,error:l,currentPage:i,totalPages:u,filters:o,sortBy:d,applyFilters:p,resetFilters:f,changePage:k,submitImage:m,confirmDelete:g,getStatusBadgeClass:b,getStatusText:v,formatDate:L}}};var J=a(66262);const M=(0,J.A)(j,[["render",S],["__scopeId","data-v-2280d7ce"]]),O=M}}]);
//# sourceMappingURL=35.0d08ecdb.js.map