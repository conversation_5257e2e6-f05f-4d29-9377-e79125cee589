/**
 * 前端调试工具 - 用于监控和显示标注相关的网络请求
 */

// 保存原始的fetch和XMLHttpRequest
const originalFetch = window.fetch;
const originalXHR = window.XMLHttpRequest;

// 监控标注相关的API请求
export const enableAnnotationDebug = () => {
  // 开启调试模式
  window.ANNOTATION_DEBUG_ENABLED = true;
  console.log('%c标注调试模式已启用', 'background: #4CAF50; color: white; font-size: 16px; padding: 3px 8px; border-radius: 4px;');
  
  // 监控fetch请求
  window.fetch = function(url, options) {
    if (typeof url === 'string' && (url.includes('/api/tags') || url.includes('/api/api'))) {
      console.group('%c[标注调试] Fetch 请求', 'background: #2196F3; color: white; font-size: 14px; padding: 2px 5px;');
      console.log('URL:', url);
      console.log('选项:', options);
      
      // 获取请求体
      if (options && options.body) {
        try {
          const body = JSON.parse(options.body);
          console.log('请求体:', body);
        } catch (e) {
          console.log('请求体 (无法解析):', options.body);
        }
      }
      
      console.groupEnd();
    }
    
    // 调用原始fetch并监控响应
    return originalFetch.apply(this, arguments)
      .then(response => {
        // 仅处理标注相关请求
        if (typeof url === 'string' && (url.includes('/api/tags') || url.includes('/api/api'))) {
          console.group('%c[标注调试] Fetch 响应', 'background: #4CAF50; color: white; font-size: 14px; padding: 2px 5px;');
          console.log('状态:', response.status, response.statusText);
          console.log('响应头:', response.headers);
          
          // 克隆响应以避免消耗流
          const clonedResponse = response.clone();
          clonedResponse.json().then(data => {
            console.log('响应数据:', data);
            console.groupEnd();
          }).catch(() => {
            console.log('响应数据: 无法解析为JSON');
            console.groupEnd();
          });
        }
        return response;
      })
      .catch(error => {
        if (typeof url === 'string' && (url.includes('/api/tags') || url.includes('/api/api'))) {
          console.group('%c[标注调试] Fetch 错误', 'background: #F44336; color: white; font-size: 14px; padding: 2px 5px;');
          console.error('错误信息:', error);
          console.groupEnd();
        }
        throw error;
      });
  };
  
  // 监控XMLHttpRequest
  window.XMLHttpRequest = function() {
    const xhr = new originalXHR();
    const originalOpen = xhr.open;
    const originalSend = xhr.send;
    
    let requestUrl = '';
    let requestMethod = '';
    let requestBody = null;
    
    xhr.open = function(method, url) {
      requestUrl = url;
      requestMethod = method;
      if (typeof url === 'string' && (url.includes('/api/tags') || url.includes('/api/api'))) {
        console.group('%c[标注调试] XHR 请求', 'background: #673AB7; color: white; font-size: 14px; padding: 2px 5px;');
        console.log('URL:', url);
        console.log('方法:', method);
      }
      return originalOpen.apply(this, arguments);
    };
    
    xhr.send = function(body) {
      requestBody = body;
      if (typeof requestUrl === 'string' && (requestUrl.includes('/api/tags') || requestUrl.includes('/api/api'))) {
        if (body) {
          try {
            const parsedBody = JSON.parse(body);
            console.log('请求体:', parsedBody);
          } catch (e) {
            console.log('请求体 (无法解析):', body);
          }
        }
      }
      return originalSend.apply(this, arguments);
    };
    
    // 监听加载完成事件
    xhr.addEventListener('load', function() {
      if (typeof requestUrl === 'string' && (requestUrl.includes('/api/tags') || requestUrl.includes('/api/api'))) {
        console.log('状态:', xhr.status, xhr.statusText);
        if (xhr.responseText) {
          try {
            const responseData = JSON.parse(xhr.responseText);
            console.log('响应数据:', responseData);
          } catch (e) {
            console.log('响应数据 (无法解析):', xhr.responseText);
          }
        }
        console.groupEnd();
      }
    });
    
    // 监听错误事件
    xhr.addEventListener('error', function() {
      if (typeof requestUrl === 'string' && (requestUrl.includes('/api/tags') || requestUrl.includes('/api/api'))) {
        console.group('%c[标注调试] XHR 错误', 'background: #F44336; color: white; font-size: 14px; padding: 2px 5px;');
        console.error('请求失败:', {
          url: requestUrl,
          method: requestMethod,
          status: xhr.status,
          statusText: xhr.statusText,
          responseText: xhr.responseText
        });
        console.groupEnd();
      }
    });
    
    return xhr;
  };
  
  return true;
};

// 显示标注数据
export const displayTagData = (tagData) => {
  console.group('%c标注数据详情', 'background: #FF9800; color: white; font-size: 14px; padding: 3px 8px;');
  console.log('标签类型:', tagData.tag);
  console.log('坐标:', `(${tagData.x}, ${tagData.y})`);
  console.log('尺寸:', `${tagData.width} x ${tagData.height}`);
  console.log('图像ID:', tagData.metadata_id);
  console.log('创建者:', tagData.created_by);
  console.groupEnd();
  
  return {
    标签类型: tagData.tag,
    中心点X坐标: tagData.x,
    中心点Y坐标: tagData.y, 
    宽度: tagData.width,
    高度: tagData.height,
    图像ID: tagData.metadata_id,
    创建者ID: tagData.created_by
  };
};

// 导出工具函数
export default {
  enableAnnotationDebug,
  displayTagData
}; 