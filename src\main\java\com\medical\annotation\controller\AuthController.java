package com.medical.annotation.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class AuthController {

    @GetMapping("/login")
    @ResponseBody
    public ResponseEntity<String> login() {
        // 返回前端处理，不尝试渲染模板
        return new ResponseEntity<>("{\"message\":\"Please login via the frontend application\"}", HttpStatus.OK);
    }
} 