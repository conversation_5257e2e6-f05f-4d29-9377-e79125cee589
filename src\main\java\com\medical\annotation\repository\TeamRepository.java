package com.medical.annotation.repository;

import com.medical.annotation.model.Team;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TeamRepository extends JpaRepository<Team, Integer> {
    
    // 按名称查找团队
    Team findByName(String name);
    
    // 按创建者ID查找团队
    List<Team> findByCreatedById(Integer userId);
    
    // 按名称模糊查询
    List<Team> findByNameContainingIgnoreCase(String keyword);
    
    // 检查团队名是否已存在
    boolean existsByName(String name);
} 