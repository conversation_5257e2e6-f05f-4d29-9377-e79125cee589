package com.medical.annotation.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Value("${spring.datasource.url}")
    private String dataSourceUrl;
    
    @Value("${spring.datasource.username}")
    private String dataSourceUsername;
    
    @Value("${spring.datasource.password}")
    private String dataSourcePassword;
    
    @Value("${spring.datasource.driver-class-name:com.mysql.cj.jdbc.Driver}")
    private String driverClassName;

    @Override
    public void run(String... args) throws Exception {
        System.out.println("数据库初始化开始...");

        try {
            // Drop existing tables
            dropAllTables();
            
            // Create new tables
            createUsersTable();
            createTeamsTable();
            createTeamMemberLogsTable();
            createReviewerApplicationsTable();
            createHemangiomaDiagnosesTable();
            createImagePairsTable();
            createTagsTable();
            createRoleChangeLogsTable();
            
            // Insert initial data
            insertInitialUsers();
            
            System.out.println("数据库初始化完成！");
        } catch (Exception e) {
            System.err.println("数据库初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 创建数据库（如果不存在）
     */
    private void createDatabaseIfNotExists() {
        // 从JDBC URL中提取数据库名称
        String url = dataSourceUrl;
        String databaseName = extractDatabaseNameFromJdbcUrl(url);
        
        if (databaseName == null || databaseName.isEmpty()) {
            System.out.println("无法从JDBC URL提取数据库名: " + url);
            return;
        }
        
        // 构建不带数据库名称的基本连接URL
        String baseUrl = extractBaseUrlFromJdbcUrl(url);
        
        System.out.println("尝试创建数据库（如果不存在）: " + databaseName);
        System.out.println("使用基础URL: " + baseUrl);
        
        try (Connection conn = DriverManager.getConnection(baseUrl, dataSourceUsername, dataSourcePassword);
             Statement stmt = conn.createStatement()) {
            
            // 创建数据库
            String sql = "CREATE DATABASE IF NOT EXISTS " + databaseName + " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            stmt.executeUpdate(sql);
            System.out.println("数据库已创建或已存在: " + databaseName);
            
        } catch (SQLException e) {
            System.err.println("创建数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 从JDBC URL中提取数据库名称
     */
    private String extractDatabaseNameFromJdbcUrl(String url) {
        // 典型的JDBC URL格式: ***************************/database_name?param=value
        try {
            int slashIndex = url.lastIndexOf('/');
            if (slashIndex < 0) return null;
            
            String dbNameWithParams = url.substring(slashIndex + 1);
            int questionMarkIndex = dbNameWithParams.indexOf('?');
            
            if (questionMarkIndex < 0) {
                return dbNameWithParams; // 没有参数
            } else {
                return dbNameWithParams.substring(0, questionMarkIndex); // 提取数据库名
            }
        } catch (Exception e) {
            System.err.println("从URL提取数据库名称失败: " + e.getMessage());
            e.printStackTrace();
            return "medical_annotations"; // 默认数据库名
        }
    }
    
    /**
     * 从JDBC URL提取基本URL（不包含数据库名和参数）
     */
    private String extractBaseUrlFromJdbcUrl(String url) {
        try {
            // ***************************/database_name?param=value
            // 转换为 ***************************/
            int slashIndex = url.lastIndexOf('/');
            if (slashIndex < 0) return url;
            
            String baseUrl = url.substring(0, slashIndex);
            return baseUrl + '/';
        } catch (Exception e) {
            System.err.println("从URL提取基本URL失败: " + e.getMessage());
            e.printStackTrace();
            return "***************************/";
        }
    }
    
    private void createUsersTable() {
        try {
            System.out.println("检查并创建users表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS users (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(255) NOT NULL UNIQUE, " +
                "password VARCHAR(255) NOT NULL, " +
                "role VARCHAR(50) NOT NULL, " +
                "custom_id VARCHAR(9), " +
                "hospital VARCHAR(255), " +
                "department VARCHAR(255), " +
                "specialty VARCHAR(255), " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "reset_password_token VARCHAR(255), " +
                "reset_password_expire DATETIME, " +
                "reviewer_application_date DATETIME, " +
                "reviewer_application_processed_date DATETIME, " +
                "reviewer_application_reason TEXT, " +
                "reviewer_application_status VARCHAR(50), " +
                "reviewer_application_processed_by INT, " +
                "team_id INT, " +
                "FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE SET NULL, " +
                "FOREIGN KEY (reviewer_application_processed_by) REFERENCES users(id) ON DELETE SET NULL, " +
                "INDEX(email), " +
                "INDEX(role), " +
                "INDEX(custom_id)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            
            // 检查是否有默认用户，如果没有则创建
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM users", Integer.class);
            if (count != null && count == 0) {
                System.out.println("创建默认用户...");
                jdbcTemplate.update(
                    "INSERT INTO users (name, email, password, role, custom_id, created_at, updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                    "管理员", "<EMAIL>", "$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG", 
                    "ADMIN", "200000001"
                );
                System.out.println("默认用户已创建");
            }
            
            System.out.println("users表已创建或已存在");
        } catch (Exception e) {
            System.err.println("创建users表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建users表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS users (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "email VARCHAR(255) NOT NULL UNIQUE, " +
                    "password VARCHAR(255) NOT NULL, " +
                    "role VARCHAR(50) NOT NULL, " +
                    "custom_id VARCHAR(9), " +
                    "hospital VARCHAR(255), " +
                    "department VARCHAR(255), " +
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("users表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版users表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
    
    private void createTeamsTable() {
        try {
            System.out.println("检查并创建teams表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS teams (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "description TEXT, " +
                "created_by INT, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "INDEX(name), " +
                "FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            System.out.println("teams表已创建或已存在");
        } catch (Exception e) {
            System.err.println("创建teams表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建teams表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS teams (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "description TEXT, " +
                    "created_by INT, " +
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("teams表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版teams表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
    
    private void createTeamMemberLogsTable() {
        try {
            System.out.println("检查并创建team_member_logs表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS team_member_logs (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "team_id INT NOT NULL, " +
                "user_id INT NOT NULL, " +
                "action VARCHAR(255) NOT NULL, " +
                "action_date DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            System.out.println("team_member_logs表已创建或已存在");
        } catch (Exception e) {
            System.err.println("创建team_member_logs表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建team_member_logs表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS team_member_logs (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "team_id INT NOT NULL, " +
                    "user_id INT NOT NULL, " +
                    "action VARCHAR(255) NOT NULL, " +
                    "action_date DATETIME DEFAULT CURRENT_TIMESTAMP" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("team_member_logs表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版team_member_logs表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
    
    private void createReviewerApplicationsTable() {
        try {
            System.out.println("检查并创建reviewer_applications表...");
            jdbcTemplate.execute(
                "CREATE TABLE IF NOT EXISTS reviewer_applications (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "user_id INT NOT NULL, " +
                "application_date DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "processed_date DATETIME, " +
                "reason TEXT, " +
                "status VARCHAR(50), " +
                "processed_by INT, " +
                "FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
            );
            System.out.println("reviewer_applications表已创建或已存在");
        } catch (Exception e) {
            System.err.println("创建reviewer_applications表时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试修复外键约束问题
            try {
                System.out.println("尝试在没有外键约束的情况下创建reviewer_applications表...");
                jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS reviewer_applications (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "user_id INT NOT NULL, " +
                    "application_date DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "processed_date DATETIME, " +
                    "reason TEXT, " +
                    "status VARCHAR(50)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                );
                System.out.println("reviewer_applications表已创建(简化版)");
            } catch (Exception e2) {
                System.err.println("创建简化版reviewer_applications表失败: " + e2.getMessage());
                e2.printStackTrace();
            }
        }
    }
    
    private void createHemangiomaDiagnosesTable() {
        String sql = "CREATE TABLE IF NOT EXISTS hemangioma_diagnoses (" +
            "id INT AUTO_INCREMENT PRIMARY KEY, " +
            "patient_age INT, " +
            "emergency_instructions TEXT, " +
            "status VARCHAR(255) DEFAULT 'DRAFT', " +
            "user_id INT, " +
            "created_at TIMESTAMP, " +
            "FOREIGN KEY (user_id) REFERENCES users(id)" +
            ");";
        jdbcTemplate.execute(sql);
        System.out.println("hemangioma_diagnoses 表已创建。");
    }
    
    private void createTagsTable() {
        String sql = "CREATE TABLE IF NOT EXISTS tags (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "tag VARCHAR(255), " +
                "width DOUBLE PRECISION, " +
                "height DOUBLE PRECISION, " +
                "created_by INT, " +
                "diagnosis_id INT, " +
                "created_at TIMESTAMP, " +
                "updated_at TIMESTAMP, " +
                "confidence DOUBLE PRECISION, " +
                "FOREIGN KEY (diagnosis_id) REFERENCES hemangioma_diagnoses(id)" +
                ");";
        jdbcTemplate.execute(sql);
        System.out.println("tags 表已创建。");
    }
    
    private void createImagePairsTable() {
        String sql = "CREATE TABLE IF NOT EXISTS image_pairs (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "image_one_path VARCHAR(255) NOT NULL, " +
                "image_two_path VARCHAR(255), " +
                "description TEXT, " +
                "created_by INT, " +
                "created_at TIMESTAMP, " +
                "updated_at TIMESTAMP, " +
                "diagnosis_id INT" +
                ");";
        jdbcTemplate.execute(sql);
        System.out.println("image_pairs 表已创建。");
    }
    
    private void createRoleChangeLogsTable() {
        String sql = "CREATE TABLE IF NOT EXISTS role_change_logs (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "user_id INT NOT NULL, " +
                "old_role VARCHAR(50) NOT NULL, " +
                "new_role VARCHAR(50) NOT NULL, " +
                "change_date DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE" +
                ");";
        jdbcTemplate.execute(sql);
        System.out.println("role_change_logs 表已创建。");
    }
    
    private void dropAllTables() {
        jdbcTemplate.execute("DROP TABLE IF EXISTS image_metadata CASCADE");
        jdbcTemplate.execute("DROP TABLE IF EXISTS team_member_logs CASCADE");
        jdbcTemplate.execute("DROP TABLE IF EXISTS reviewer_applications CASCADE");
        jdbcTemplate.execute("DROP TABLE IF EXISTS role_change_logs CASCADE");
        System.out.println("所有表格已删除。");
    }
    
    private void insertInitialUsers() {
        // Implementation of insertInitialUsers method
    }
} 