"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[301],{1573:(e,t,a)=>{a.d(t,{VG:()=>i});a(88844),a(28706),a(15086),a(26099),a(27495),a(90906),a(99449),a(11392);var n=a(81052);a(68039),a(55593),a(51629),a(74423),a(59089),a(18111),a(7588),a(58940),a(21699),a(25440),a(23500);function r(e){var t="_t=".concat(Date.now());return e.includes("?")?"".concat(e,"&").concat(t):"".concat(e,"?").concat(t)}function i(e){if(!e)return"";if(console.log("[ImageHelper] 处理图像路径:",e),/^[a-zA-Z]:\\/.test(e)){console.log("[ImageHelper] 检测到文件系统路径");var t=encodeURIComponent(e),a=n.M8?"".concat(n.JR):"";return r("".concat(a,"/medical/image/system-path?path=").concat(t))}if(e.startsWith("http://")||e.startsWith("https://"))return r(e);if(e.startsWith("data:"))return e;var i=e;return e.startsWith("/medical")?(console.log("[ImageHelper] 检测到相对路径，添加后端服务器地址"),i=n.M8?"".concat(n.JR).concat(e):e):e.startsWith("/")&&(console.log("[ImageHelper] 检测到其他相对路径，添加后端服务器地址和上下文路径"),i=n.M8?"".concat(n.JR).concat(n.T_).concat(e):"".concat(n.T_).concat(e)),console.log("[ImageHelper] 处理后的URL:",i),r(i)}},54301:(e,t,a)=>{a.r(t),a.d(t,{default:()=>Q});var n=a(61431),r={class:"case-view-container"},i={class:"page-header"},s={class:"header-actions"},l={key:0,class:"annotated-image-section"},o={class:"section-header"},c={key:0,class:"refresh-info"},u={class:"annotated-image-container"},d={class:"image-error"},f={key:0,class:"debug-info"},m={key:1,class:"no-image-message"},g={class:"card-header"},v={class:"case-info"},h={class:"detailed-case-info"},k={class:"section-header"},p={key:0,class:"version-info"},D={key:0,class:"hemangioma-specific-section"};function _(e,t,a,_,y,b){var W=(0,n.g2)("el-button"),I=(0,n.g2)("el-tooltip"),T=(0,n.g2)("el-image"),w=(0,n.g2)("el-tag"),C=(0,n.g2)("el-descriptions-item"),U=(0,n.g2)("el-descriptions"),L=(0,n.g2)("el-card"),X=(0,n.gN)("loading");return(0,n.uX)(),(0,n.CE)("div",r,[(0,n.Lk)("div",i,[t[3]||(t[3]=(0,n.Lk)("h2",null,"病例详情",-1)),(0,n.Lk)("div",s,[y.hasPermission?((0,n.uX)(),(0,n.Wv)(W,{key:0,type:"primary",size:"small",onClick:b.redirectToAnnotate},{default:(0,n.k6)((function(){return t[1]||(t[1]=[(0,n.eW)("编辑")])})),_:1,__:[1]},8,["onClick"])):(0,n.Q3)("",!0),(0,n.bF)(I,{content:"刷新病例数据",placement:"top"},{default:(0,n.k6)((function(){return[(0,n.bF)(W,{icon:"el-icon-refresh",size:"small",circle:"",loading:y.loading,onClick:b.refreshData},null,8,["loading","onClick"])]})),_:1}),(0,n.bF)(W,{link:"",onClick:t[0]||(t[0]=function(t){return e.$router.back()})},{default:(0,n.k6)((function(){return t[2]||(t[2]=[(0,n.eW)("返回")])})),_:1,__:[2]})])]),(0,n.bo)(((0,n.uX)(),(0,n.Wv)(L,null,{header:(0,n.k6)((function(){return[(0,n.Lk)("div",g,[t[12]||(t[12]=(0,n.Lk)("span",null,"基本信息",-1)),(0,n.bF)(w,{type:b.getStatusType(y.caseDetail.status)},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.status),1)]})),_:1},8,["type"])])]})),default:(0,n.k6)((function(){return[y.annotatedImageUrl?((0,n.uX)(),(0,n.CE)("div",l,[(0,n.Lk)("div",o,[t[4]||(t[4]=(0,n.Lk)("h3",null,"病变标注图像",-1)),y.lastRefreshTime?((0,n.uX)(),(0,n.CE)("small",c,"最后更新: "+(0,n.v_)(b.formatTime(y.lastRefreshTime)),1)):(0,n.Q3)("",!0)]),(0,n.Lk)("div",u,[(0,n.bF)(T,{src:y.annotatedImageUrl,fit:"contain",class:"annotated-image","preview-src-list":[y.annotatedImageUrl],onError:b.handleImageError},{error:(0,n.k6)((function(){return[(0,n.Lk)("div",d,[t[9]||(t[9]=(0,n.Lk)("i",{class:"el-icon-picture-outline"},null,-1)),t[10]||(t[10]=(0,n.Lk)("p",null,"无法加载标注图像",-1)),y.imageLoadError?((0,n.uX)(),(0,n.CE)("div",f,[t[7]||(t[7]=(0,n.Lk)("h4",null,"图片调试信息",-1)),(0,n.Lk)("p",null,[t[5]||(t[5]=(0,n.Lk)("b",null,"数据库图片路径:",-1)),(0,n.eW)(" "+(0,n.v_)(y.debugInfo.rawPath),1)]),(0,n.Lk)("p",null,[t[6]||(t[6]=(0,n.Lk)("b",null,"完整访问URL:",-1)),(0,n.eW)(" "+(0,n.v_)(y.debugInfo.fullUrl),1)]),t[8]||(t[8]=(0,n.Lk)("small",null,"请检查此路径是否正确，以及后端是否提供了静态文件访问",-1))])):(0,n.Q3)("",!0)])]})),_:1},8,["src","preview-src-list","onError"])])])):((0,n.uX)(),(0,n.CE)("div",m,t[11]||(t[11]=[(0,n.Lk)("i",{class:"el-icon-picture-outline"},null,-1),(0,n.Lk)("p",null,"此病例暂无标注图像",-1)]))),(0,n.Lk)("div",v,[(0,n.bF)(U,{column:4,border:""},{default:(0,n.k6)((function(){return[(0,n.bF)(C,{label:"病例编号"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.caseId||"无"),1)]})),_:1}),y.caseDetail.patientInfo?((0,n.uX)(),(0,n.Wv)(C,{key:0,label:"患者信息"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.patientInfo),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.department?((0,n.uX)(),(0,n.Wv)(C,{key:1,label:"部位"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.department),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.type?((0,n.uX)(),(0,n.Wv)(C,{key:2,label:"血管瘤类型"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.type),1)]})),_:1})):(0,n.Q3)("",!0),(0,n.bF)(C,{label:"创建时间"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.createTime||"无"),1)]})),_:1}),(0,n.bF)(C,{label:"更新时间"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.updateTime||"无"),1)]})),_:1}),y.caseDetail.lesionColor?((0,n.uX)(),(0,n.Wv)(C,{key:3,label:"病变颜色"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.lesionColor),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.detectedType?((0,n.uX)(),(0,n.Wv)(C,{key:4,label:"检测到的血管瘤类型"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.detectedType),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.vesselTexture?((0,n.uX)(),(0,n.Wv)(C,{key:5,label:"血管质地"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(b.getVesselTextureLabel(y.caseDetail.vesselTexture)),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.note?((0,n.uX)(),(0,n.Wv)(C,{key:6,label:"备注",span:4},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.note),1)]})),_:1})):(0,n.Q3)("",!0)]})),_:1})]),(0,n.Lk)("div",h,[(0,n.Lk)("div",k,[t[13]||(t[13]=(0,n.Lk)("h3",null,"详细信息",-1)),y.dataVersion>0?((0,n.uX)(),(0,n.CE)("small",p,"版本: "+(0,n.v_)(y.dataVersion),1)):(0,n.Q3)("",!0)]),y.caseDetail.treatmentSuggestion||y.caseDetail.precautions?((0,n.uX)(),(0,n.CE)("div",D,[t[14]||(t[14]=(0,n.Lk)("h4",null,"血管瘤诊断建议",-1)),(0,n.bF)(U,{column:1,border:""},{default:(0,n.k6)((function(){return[y.caseDetail.diagnosticSummary?((0,n.uX)(),(0,n.Wv)(C,{key:0,label:"诊断摘要"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.diagnosticSummary),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.treatmentSuggestion?((0,n.uX)(),(0,n.Wv)(C,{key:1,label:"治疗建议"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.treatmentSuggestion),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.precautions?((0,n.uX)(),(0,n.Wv)(C,{key:2,label:"注意事项"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.precautions),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.reviewNotes?((0,n.uX)(),(0,n.Wv)(C,{key:3,label:"审核备注"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.reviewNotes),1)]})),_:1})):(0,n.Q3)("",!0)]})),_:1})])):(0,n.Q3)("",!0),(0,n.bF)(U,{column:3,border:"",class:"mt-4"},{default:(0,n.k6)((function(){return[y.caseDetail.lesionSize?((0,n.uX)(),(0,n.Wv)(C,{key:0,label:"病变大小"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.lesionSize),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.bloodFlow?((0,n.uX)(),(0,n.Wv)(C,{key:1,label:"血流信号"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.bloodFlow),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.borderClarity?((0,n.uX)(),(0,n.Wv)(C,{key:2,label:"边界清晰度"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.borderClarity),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.diseaseStage?((0,n.uX)(),(0,n.Wv)(C,{key:3,label:"病程阶段"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.diseaseStage),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.morphologicalFeatures?((0,n.uX)(),(0,n.Wv)(C,{key:4,label:"形态特征"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.morphologicalFeatures),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.symptoms?((0,n.uX)(),(0,n.Wv)(C,{key:5,label:"症状表现"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.symptoms),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.symptomDetails?((0,n.uX)(),(0,n.Wv)(C,{key:6,label:"症状详情"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.symptomDetails),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.complications?((0,n.uX)(),(0,n.Wv)(C,{key:7,label:"并发症"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.complications),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.complicationDetails?((0,n.uX)(),(0,n.Wv)(C,{key:8,label:"并发症详情"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.complicationDetails),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.diagnosisCode?((0,n.uX)(),(0,n.Wv)(C,{key:9,label:"诊断编码"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.diagnosisCode),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.treatmentPriority?((0,n.uX)(),(0,n.Wv)(C,{key:10,label:"治疗优先级"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.treatmentPriority),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.recommendedTreatment?((0,n.uX)(),(0,n.Wv)(C,{key:11,label:"推荐治疗"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.recommendedTreatment),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.contraindications?((0,n.uX)(),(0,n.Wv)(C,{key:12,label:"禁忌症"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.contraindications),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.followUpSchedule?((0,n.uX)(),(0,n.Wv)(C,{key:13,label:"随访周期"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.followUpSchedule),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.prognosisRating?((0,n.uX)(),(0,n.Wv)(C,{key:14,label:"预后评级"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.prognosisRating),1)]})),_:1})):(0,n.Q3)("",!0),y.caseDetail.patientEducation?((0,n.uX)(),(0,n.Wv)(C,{key:15,label:"患者教育重点"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,n.v_)(y.caseDetail.patientEducation),1)]})),_:1})):(0,n.Q3)("",!0)]})),_:1})])]})),_:1})),[[X,y.loading]])])}var y=a(24059),b=a(698),W=(a(28706),a(44114),a(59089),a(23288),a(79432),a(36149)),I=a(1573),T=a(81052),w=a(72505),C=a.n(w);const U={name:"CaseView",data:function(){return{caseId:this.$route.params.id,loading:!0,caseDetail:{caseId:"",patientInfo:"",department:"",type:"",status:"",note:"",createTime:"",updateTime:"",lesionSize:"",lesionColor:"",bloodFlow:"",borderClarity:"",diseaseStage:"",morphologicalFeatures:"",symptoms:"",symptomDetails:"",complications:"",complicationDetails:"",diagnosisCode:"",treatmentPriority:"",treatmentPlan:"",recommendedTreatment:"",contraindications:"",followUpSchedule:"",prognosisRating:"",patientEducation:"",diagnosticSummary:"",dietaryAdvice:"",emergencyInstructions:"",reviewNotes:"",createdBy:"",teamId:"",detectedType:"",vesselTexture:""},tags:[],annotatedImageUrl:null,imageLoadError:!1,debugInfo:{rawPath:"",fullUrl:""},currentUser:null,lastRefreshTime:null,refreshInterval:6e4,hasPermission:!1,dataVersion:0}},created:function(){this.$axios=C().create({baseURL:T.JR,timeout:1e4,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))}}),this.getCurrentUser(),this.fetchCaseDetail(),window.addEventListener("case-data-updated",this.handleDataUpdate)},beforeUnmount:function(){window.removeEventListener("case-data-updated",this.handleDataUpdate)},methods:{getCurrentUser:function(){try{var e=localStorage.getItem("user");e?(this.currentUser=JSON.parse(e),console.log("当前用户:",this.currentUser)):console.warn("未找到登录用户信息")}catch(t){console.error("获取用户信息失败:",t)}},checkPermission:function(){if(!this.currentUser)return console.warn("未登录，无法验证权限"),!1;var e="ADMIN"===this.currentUser.role,t="ANNOTATOR"===this.currentUser.role||"DOCTOR"===this.currentUser.role,a=this.caseDetail.createdBy===this.currentUser.id||this.caseDetail.createdBy===this.currentUser.customId,n=this.caseDetail.teamId&&this.currentUser.teamId&&this.caseDetail.teamId===this.currentUser.teamId;return this.hasPermission=e||t&&(a||n),console.log("权限检查结果:",this.hasPermission,{isAdmin:e,isAnnotator:t,isCreator:a,isSameTeam:n}),this.hasPermission},handleDataUpdate:function(e){var t,a;if(e&&e.detail&&e.detail.caseId===this.caseId&&(console.log("接收到病例数据更新事件:",e.detail),!e.detail.userId||e.detail.userId===(null===(t=this.currentUser)||void 0===t?void 0:t.id)||e.detail.userId===(null===(a=this.currentUser)||void 0===a?void 0:a.customId))){this.dataVersion++;var n=!0===e.detail.immediate||Date.now()-this.lastRefreshTime>this.refreshInterval;n?(console.log("刷新病例数据和图片"),this.fetchCaseDetail()):console.log("延迟刷新，等待下次刷新周期")}},shouldRefreshData:function(){if(!this.lastRefreshTime)return!0;var e=Date.now(),t=e-this.lastRefreshTime;return t>this.refreshInterval},updateRefreshTime:function(){this.lastRefreshTime=Date.now()},redirectToAnnotate:function(){localStorage.setItem("isEditingCase","true"),this.$router.push({path:"/case/"+this.caseData.id+"/annotate-and-form",query:{imageId:this.caseId,edit:"true"}})},getStatusType:function(e){var t={待标注:"info",已标注:"success",审核中:"warning",已通过:"success",已驳回:"danger"};return t[e]||"info"},getVesselTextureLabel:function(e){var t={soft:"质软",elastic:"质韧",hard:"质硬",cystic:"囊性",compressible:"可压缩"};return t[e]||e},fetchCaseDetail:function(){var e=this;return(0,b.A)((0,y.A)().m((function t(){var a,n,r,i;return(0,y.A)().w((function(t){while(1)switch(t.n){case 0:return e.loading=!0,t.p=1,console.log("开始获取血管瘤诊断数据，ID:",e.caseId),t.n=2,e.$axios.get("/medical/api/hemangioma-diagnoses/".concat(e.caseId));case 2:a=t.v,n=a.data,console.log("获取到的诊断数据:",n),n?(e.dataVersion++,e.lastRefreshTime=Date.now(),e.caseDetail={caseId:"CASE-".concat(n.id),patientInfo:"".concat(n.patientAge||"未知","岁 ").concat(n.gender||"未知"),department:n.bodyPart||"未知",type:n.originType||"未知",status:e.getStatusText(n.status||"DRAFT"),note:"无",createTime:e.formatDate(n.createdAt),updateTime:e.formatDate(n.updatedAt||n.createdAt),lesionColor:n.color||"无",vesselTexture:n.vesselTexture||"无",detectedType:n.detectedType||"未知",treatmentSuggestion:n.treatmentSuggestion||"无",precautions:n.precautions||"无",diagnosticSummary:n.diagnosticSummary||"无",reviewNotes:n.reviewNotes||"无",createdBy:n.user?n.user.id:null,teamId:n.user&&n.user.team?n.user.team.id:null},n.processedImagePath?(e.annotatedImageUrl=e.getImageUrl(n.processedImagePath),e.debugInfo.rawPath=n.processedImagePath,e.debugInfo.fullUrl=e.annotatedImageUrl):e.annotatedImageUrl=null,e.checkPermission(),e.fetchTags()):e.$message.error("未找到有效的病例数据"),t.n=4;break;case 3:t.p=3,i=t.v,console.error("获取病例详情失败:",i),e.$message.error("获取病例详情失败: "+((null===(r=i.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.error)||i.message));case 4:return t.p=4,e.loading=!1,t.f(4);case 5:return t.a(2)}}),t,null,[[1,3,4,5]])})))()},getStatusText:function(e){var t={DRAFT:"待标注",SUBMITTED:"已标注",PENDING:"审核中",APPROVED:"已通过",REJECTED:"已驳回"};return t[e]||"未知"},fetchTags:function(){var e=this;return(0,b.A)((0,y.A)().m((function t(){var a,n;return(0,y.A)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,t.n=1,W["default"].get("/hemangioma-diagnoses/".concat(e.caseId,"/tags"));case 1:a=t.v,e.tags=a.data,t.n=3;break;case 2:t.p=2,n=t.v,console.error("获取标签数据失败:",n);case 3:return t.a(2)}}),t,null,[[0,2]])})))()},formatDate:function(e){if(!e)return"无";var t=new Date(e);return t.toLocaleString("zh-CN")},handleImageError:function(){console.error("标注图片加载失败"),this.imageLoadError=!0,console.log("图片加载失败的调试信息:",this.debugInfo)},getImageUrl:I.VG,refreshData:function(){console.log("手动刷新数据"),this.fetchCaseDetail()},formatTime:function(e){if(!e)return"";var t=new Date(e);return t.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"})}}};var L=a(66262);const X=(0,L.A)(U,[["render",_],["__scopeId","data-v-b7093354"]]),Q=X}}]);
//# sourceMappingURL=301.13bfe164.js.map