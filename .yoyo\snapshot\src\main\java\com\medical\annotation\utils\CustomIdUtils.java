package com.medical.annotation.utils;

import org.springframework.stereotype.Component;

/**
 * 自定义ID工具类 - 处理customId相关功能
 */
@Component
public class CustomIdUtils {

    /**
     * 判断一个字符串是否是customId格式（9位数字字符串）
     * @param id 要检查的ID字符串
     * @return 是否符合customId格式
     */
    public static boolean isCustomId(String id) {
        return id != null && id.matches("^\\d{9}$");
    }
    
    /**
     * 判断一个数值是否可能是customId（大于10^8的数字）
     * @param id 要检查的ID
     * @return 是否可能是customId
     */
    public static boolean isCustomId(Number id) {
        if (id == null) {
            return false;
        }
        return id.toString().length() >= 9;
    }
    
    /**
     * 尝试将任意类型的ID转换为customId字符串，如果不是customId则返回null
     * @param id 任意类型的ID
     * @return 如果是customId则返回格式化后的字符串，否则返回null
     */
    public static String tryFormatCustomId(Object id) {
        if (id == null) {
            return null;
        }
        
        String idStr = id.toString();
        if (isCustomId(idStr)) {
            return idStr;
        }
        
        if (id instanceof Number && isCustomId((Number) id)) {
            return idStr;
        }
        
        return null;
    }
} 