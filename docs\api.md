# 血管瘤AI智能诊断平台 - API文档

## 📋 目录
- [API概览](#api概览)
- [认证接口](#认证接口)
- [用户管理接口](#用户管理接口)
- [血管瘤诊断接口](#血管瘤诊断接口)
- [AI服务接口](#ai服务接口)
- [系统配置接口](#系统配置接口)
- [错误码说明](#错误码说明)

## 🌐 API概览

### 基础信息
- **Base URL**: `http://localhost:8085/medical/api`
- **AI Service URL**: `http://localhost:8086`
- **Content-Type**: `application/json` (除文件上传外)
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 状态码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 🔐 认证接口

### 用户登录
**POST** `/auth/login`

**请求参数：**
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "realName": "系统管理员",
      "role": "ADMIN",
      "email": "<EMAIL>"
    }
  }
}
```

### 用户登出
**POST** `/auth/logout`

**请求头：**
```
Authorization: Bearer <token>
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登出成功"
}
```

### 刷新Token
**POST** `/auth/refresh`

**请求头：**
```
Authorization: Bearer <refresh_token>
```

## 👥 用户管理接口

### 获取用户信息
**GET** `/users/{id}`

**请求头：**
```
Authorization: Bearer <token>
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "username": "admin",
    "realName": "系统管理员",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "role": "ADMIN",
    "status": 1,
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 创建用户
**POST** `/users`

**请求参数：**
```json
{
  "username": "doctor01",
  "password": "password123",
  "realName": "张医生",
  "email": "<EMAIL>",
  "phone": "13800138001",
  "role": "DOCTOR"
}
```

### 更新用户信息
**PUT** `/users/{id}`

**请求参数：**
```json
{
  "realName": "李医生",
  "email": "<EMAIL>",
  "phone": "13800138002"
}
```

## 🩺 血管瘤诊断接口

### 创建诊断记录
**POST** `/hemangioma-diagnoses`

**请求类型：** `multipart/form-data`

**请求参数：**
- `file`: 图像文件 (必需)
- `userId`: 用户ID (必需)
- `originalFilename`: 原始文件名 (可选)

**响应示例：**
```json
{
  "code": 200,
  "message": "诊断记录创建成功",
  "data": {
    "id": 123,
    "userId": 1,
    "imagePath": "/medical_images/original/20240101_120000_image.jpg",
    "originalFilename": "hemangioma_sample.jpg",
    "processingStatus": "PENDING",
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 获取诊断记录列表
**GET** `/hemangioma-diagnoses`

**查询参数：**
- `page`: 页码 (默认: 0)
- `size`: 每页大小 (默认: 20)
- `userId`: 用户ID (可选)
- `status`: 处理状态 (可选)
- `startDate`: 开始日期 (可选)
- `endDate`: 结束日期 (可选)

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "content": [
      {
        "id": 123,
        "userId": 1,
        "imagePath": "/medical_images/original/image.jpg",
        "hemangiomaType": "婴幼儿血管瘤",
        "primaryCategory": "真性血管肿瘤",
        "processingStatus": "COMPLETED",
        "aiProcessingTime": 2500,
        "createdAt": "2024-01-01T12:00:00Z"
      }
    ],
    "totalElements": 100,
    "totalPages": 5,
    "size": 20,
    "number": 0
  }
}
```

### 获取诊断记录详情
**GET** `/hemangioma-diagnoses/{id}`

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "id": 123,
    "userId": 1,
    "imagePath": "/medical_images/original/image.jpg",
    "originalFilename": "sample.jpg",
    "imageSize": "1024x768",
    "fileSize": 2048576,
    "aiDetectionResult": {
      "detections": [
        {
          "class": 0,
          "className": "IH",
          "confidence": 0.85,
          "bbox": [100, 100, 200, 200]
        }
      ],
      "processingTime": 2500
    },
    "detectedTypes": ["婴幼儿血管瘤"],
    "confidenceScores": [0.85],
    "primaryCategory": "真性血管肿瘤",
    "secondaryCategory": "婴幼儿血管瘤",
    "hemangiomaType": "婴幼儿血管瘤",
    "llmRecommendation": {
      "treatmentSuggestion": "建议观察随访，必要时激光治疗",
      "precautions": "避免外伤，注意观察变化",
      "disclaimer": "本建议仅供参考，请咨询专业医生"
    },
    "processingStatus": "COMPLETED",
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 更新诊断记录
**PUT** `/hemangioma-diagnoses/{id}`

**请求参数：**
```json
{
  "doctorDiagnosis": "确诊为婴幼儿血管瘤",
  "doctorNotes": "建议定期随访观察",
  "isConfirmed": true
}
```

### 更新LLM推荐结果
**POST** `/hemangioma-diagnoses/{id}/update-recommendation`

**请求参数：**
```json
{
  "treatmentSuggestion": "建议激光治疗",
  "precautions": "治疗后注意防晒",
  "disclaimer": "本建议仅供参考"
}
```

## 🤖 AI服务接口

### 血管瘤检测
**POST** `/detect`

**请求类型：** `multipart/form-data`

**请求参数：**
- `file`: 图像文件 (必需)
- `confidence`: 置信度阈值 (可选, 默认: 0.5)
- `max_detections`: 最大检测数量 (可选, 默认: 10)

**响应示例：**
```json
{
  "success": true,
  "detections": [
    {
      "class": 0,
      "className": "IH",
      "confidence": 0.85,
      "bbox": [100, 100, 200, 200]
    }
  ],
  "processingTime": 2.5,
  "imageInfo": {
    "width": 1024,
    "height": 768,
    "channels": 3
  }
}
```

### LLM诊断建议
**POST** `/diagnose`

**请求参数：**
```json
{
  "detectionResults": [
    {
      "className": "IH",
      "confidence": 0.85,
      "bbox": [100, 100, 200, 200]
    }
  ],
  "imageInfo": {
    "width": 1024,
    "height": 768
  },
  "patientInfo": {
    "age": "6个月",
    "gender": "女"
  }
}
```

**响应示例：**
```json
{
  "success": true,
  "recommendation": {
    "treatmentSuggestion": "建议观察随访，如快速增长可考虑激光治疗",
    "precautions": "避免外伤，注意观察颜色和大小变化",
    "disclaimer": "本建议仅供参考，请咨询专业医生获取最终诊断"
  },
  "processingTime": 45.2
}
```

### YOLO检测 + LLM诊断
**POST** `/diagnose-yolo`

**请求类型：** `multipart/form-data`

**请求参数：**
- `file`: 图像文件 (必需)
- `confidence`: 置信度阈值 (可选)
- `callback_url`: 回调URL (可选)
- `diagnosis_id`: 诊断记录ID (可选)

**响应示例：**
```json
{
  "success": true,
  "message": "检测完成，LLM建议生成中",
  "detections": [
    {
      "class": 0,
      "className": "IH",
      "confidence": 0.85,
      "bbox": [100, 100, 200, 200]
    }
  ],
  "processingTime": 2.5,
  "llmProcessing": true
}
```

### 健康检查
**GET** `/health`

**响应示例：**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "services": {
    "yolo_model": "loaded",
    "llm_service": "available"
  }
}
```

## ⚙️ 系统配置接口

### 获取血管瘤类型列表
**GET** `/hemangioma-types`

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "category": "真性血管肿瘤",
      "typeCode": "IH",
      "typeName": "婴幼儿血管瘤",
      "description": "最常见的婴幼儿良性血管肿瘤",
      "isActive": true,
      "sortOrder": 1
    }
  ]
}
```

### 获取系统配置
**GET** `/system-config`

**查询参数：**
- `key`: 配置键 (可选)

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "configKey": "ai.confidence.threshold",
      "configValue": "0.5",
      "configType": "NUMBER",
      "description": "AI检测置信度阈值"
    }
  ]
}
```

### 更新系统配置
**PUT** `/system-config/{id}`

**请求参数：**
```json
{
  "configValue": "0.6",
  "description": "更新后的AI检测置信度阈值"
}
```

## ❌ 错误码说明

### 业务错误码
- `1001`: 用户名或密码错误
- `1002`: 用户不存在
- `1003`: 用户已被禁用
- `1004`: Token已过期
- `1005`: Token无效

- `2001`: 文件上传失败
- `2002`: 文件格式不支持
- `2003`: 文件大小超限
- `2004`: 图像处理失败

- `3001`: AI服务不可用
- `3002`: 模型加载失败
- `3003`: 检测超时
- `3004`: LLM服务不可用

- `4001`: 数据库连接失败
- `4002`: 数据不存在
- `4003`: 数据已存在
- `4004`: 数据更新失败

### 错误响应示例
```json
{
  "code": 1001,
  "message": "用户名或密码错误",
  "data": null,
  "timestamp": "2024-01-01T12:00:00Z",
  "path": "/medical/api/auth/login"
}
```

## 📝 使用示例

### JavaScript/Axios示例
```javascript
// 登录
const login = async (username, password) => {
  try {
    const response = await axios.post('/medical/api/auth/login', {
      username,
      password
    });
    
    const { token } = response.data.data;
    localStorage.setItem('token', token);
    
    return response.data;
  } catch (error) {
    console.error('登录失败:', error.response.data);
    throw error;
  }
};

// 上传图像进行诊断
const uploadImage = async (file, userId) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('userId', userId);
  
  try {
    const response = await axios.post('/medical/api/hemangioma-diagnoses', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('上传失败:', error.response.data);
    throw error;
  }
};
```

### Python/Requests示例
```python
import requests

# 登录
def login(username, password):
    url = 'http://localhost:8085/medical/api/auth/login'
    data = {
        'username': username,
        'password': password
    }
    
    response = requests.post(url, json=data)
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.json()['message']}")

# AI检测
def detect_hemangioma(image_path, confidence=0.5):
    url = 'http://localhost:8086/detect'
    
    with open(image_path, 'rb') as f:
        files = {'file': f}
        data = {'confidence': confidence}
        
        response = requests.post(url, files=files, data=data)
        
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"检测失败: {response.text}")
```

---

**更新时间**: 2024-01-01  
**版本**: V2.0
