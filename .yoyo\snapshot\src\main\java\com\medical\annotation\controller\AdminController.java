package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.util.SequentialIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin")
public class AdminController {

    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    @Autowired
    private SequentialIdGenerator idGenerator;
    
    /**
     * 修复所有没有formatted_id的记录
     * 为它们从000000001开始分配顺序ID
     */
    @GetMapping("/fix-formatted-ids")
    public ResponseEntity<?> fixFormattedIds() {
        System.out.println("开始修复formatted_id...");
        
        // 获取所有图像记录
        List<ImageMetadata> allImages = imageMetadataRepository.findAll();
        
        int fixedCount = 0;
        int alreadyHasIdCount = 0;
        
        for (ImageMetadata image : allImages) {
            if (image.getFormattedId() == null || image.getFormattedId().trim().isEmpty()) {
                // 生成新的顺序ID
                String formattedId = idGenerator.nextId();
                image.setFormattedId(formattedId);
                imageMetadataRepository.save(image);
                System.out.println("已为ID=" + image.getId() + "的图像设置formatted_id=" + formattedId);
                fixedCount++;
            } else {
                System.out.println("ID=" + image.getId() + "的图像已有formatted_id=" + image.getFormattedId());
                alreadyHasIdCount++;
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("total_images", allImages.size());
        result.put("fixed_count", fixedCount);
        result.put("already_has_id_count", alreadyHasIdCount);
        result.put("success", true);
        result.put("message", "完成formatted_id修复");
        
        System.out.println("修复完成: 总共" + allImages.size() + "条记录, 修复了" + fixedCount + "条, 已有ID的" + alreadyHasIdCount + "条");
        return ResponseEntity.ok(result);
    }
} 