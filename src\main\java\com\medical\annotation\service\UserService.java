package com.medical.annotation.service;

import com.medical.annotation.model.Team;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.TeamRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.exception.UserNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.HashMap;
import java.util.Map;

@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TeamRepository teamRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private EmailService emailService;
    
    private final Random random = new Random();

    /**
     * 生成9位数随机用户ID
     * 不再将用户角色信息编码到ID中，而是生成纯粹的9位随机ID
     * @return 随机生成的9位数ID
     */
    private String generateCustomId() {
        // 生成9位随机数
        int randomNum = 100000000 + random.nextInt(900000000); // 生成9位数
        String customId = String.valueOf(randomNum);
        
        // 检查ID是否已存在，如果存在则重新生成
        while (userRepository.existsByCustomId(customId)) {
            randomNum = 100000000 + random.nextInt(900000000);
            customId = String.valueOf(randomNum);
        }
        
        return customId;
    }

    /**
     * 创建新用户
     * @param user 用户信息
     * @return 创建的用户
     */
    @Transactional
    public User createUser(User user) throws Exception {
        // 验证邮箱是否已被使用
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new Exception("邮箱已被使用");
        }

        // 设置默认角色和创建时间
        if (user.getRole() == null) {
            user.setRole(User.Role.DOCTOR);
        }

        // 生成自定义ID
        user.setCustomId(generateCustomId());

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置创建时间
        user.setCreatedAt(LocalDateTime.now());

        return userRepository.save(user);
    }

    /**
     * 更新用户信息
     * @param user 更新的用户信息
     * @return 更新后的用户
     */
    @Transactional
    public User updateUser(User user) throws Exception {
        System.out.println("开始更新用户信息: ID=" + user.getId() + 
                           ", customId=" + user.getCustomId() + 
                           ", email=" + user.getEmail() +
                           ", name=" + user.getName());
        
        // 验证用户是否存在
        Optional<User> existingUserOpt = userRepository.findById(user.getId());
        if (!existingUserOpt.isPresent()) {
            System.out.println("用户不存在: ID=" + user.getId());
            throw new Exception("用户不存在");
        }

        User existingUser = existingUserOpt.get();
        System.out.println("找到现有用户: ID=" + existingUser.getId() + 
                           ", customId=" + existingUser.getCustomId() + 
                           ", email=" + existingUser.getEmail());
        
        // 检查customId是否符合规则（9-12位数字和字母）
        if (user.getCustomId() != null && !user.getCustomId().equals(existingUser.getCustomId())) {
            System.out.println("尝试更新customId: " + existingUser.getCustomId() + " -> " + user.getCustomId());
            
            // 验证customId格式
            if (!user.getCustomId().matches("^[a-zA-Z0-9]{9,20}$")) {
                System.out.println("customId格式无效: " + user.getCustomId());
                throw new Exception("用户ID必须为9-20位数字和字母");
            }
            
            // 检查customId是否已被使用
            Optional<User> userWithSameCustomId = userRepository.findByCustomId(user.getCustomId());
            if (userWithSameCustomId.isPresent() && !userWithSameCustomId.get().getId().equals(user.getId())) {
                System.out.println("customId已被使用: " + user.getCustomId() + " 被用户ID=" + userWithSameCustomId.get().getId() + "使用");
                throw new Exception("该用户ID已被使用");
            }
            
            System.out.println("更新customId: " + existingUser.getCustomId() + " -> " + user.getCustomId());
            existingUser.setCustomId(user.getCustomId());
        } else {
            System.out.println("customId未更改或为空");
        }
        
        // 检查邮箱是否已被使用
        if (user.getEmail() != null && !user.getEmail().equals(existingUser.getEmail())) {
            System.out.println("尝试更新email: " + existingUser.getEmail() + " -> " + user.getEmail());
            
            Optional<User> userWithSameEmail = userRepository.findByEmail(user.getEmail());
            if (userWithSameEmail.isPresent() && !userWithSameEmail.get().getId().equals(user.getId())) {
                System.out.println("email已被使用: " + user.getEmail() + " 被用户ID=" + userWithSameEmail.get().getId() + "使用");
                throw new Exception("该邮箱已被使用");
            }
            
            System.out.println("更新email: " + existingUser.getEmail() + " -> " + user.getEmail());
            existingUser.setEmail(user.getEmail());
        } else {
            System.out.println("email未更改或为空");
        }

        // 更新用户基本信息，但保留密码和角色不变
        if (user.getName() != null) {
            existingUser.setName(user.getName());
        }
        if (user.getDepartment() != null) {
            existingUser.setDepartment(user.getDepartment());
        }
        if (user.getHospital() != null) {
            existingUser.setHospital(user.getHospital());
        }

        User savedUser = userRepository.save(existingUser);
        System.out.println("用户更新完成: ID=" + savedUser.getId() + 
                           ", customId=" + savedUser.getCustomId() + 
                           ", email=" + savedUser.getEmail() +
                           ", name=" + savedUser.getName());
        return savedUser;
    }

    /**
     * 更改用户密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 更新后的用户
     */
    @Transactional
    public User changePassword(Integer userId, String oldPassword, String newPassword) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new Exception("旧密码不正确");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));

        return userRepository.save(user);
    }
    
    // 用于存储密码重置验证码的Map
    private final Map<String, PasswordResetToken> passwordResetTokens = new HashMap<>();
    
    // 密码重置令牌类
    private static class PasswordResetToken {
        private final String code;
        private final LocalDateTime expiryDate;
        
        public PasswordResetToken(String code) {
            this.code = code;
            this.expiryDate = LocalDateTime.now().plusMinutes(30); // 30分钟有效期
        }
        
        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiryDate);
        }
        
        public boolean isValid(String code) {
            return this.code.equals(code) && !isExpired();
        }
    }
    
    /**
     * 生成密码重置验证码
     * @param email 用户邮箱
     * @return 生成的验证码
     */
    public String generatePasswordResetCode(String email) {
        // 生成6位随机数字验证码
        String code = String.format("%06d", random.nextInt(1000000));
        
        // 保存验证码
        passwordResetTokens.put(email, new PasswordResetToken(code));
        
        return code;
    }
    
    /**
     * 发送密码重置邮件
     * @param email 用户邮箱
     * @param code 验证码
     */
    public void sendPasswordResetEmail(String email, String code) {
        try {
            // 尝试使用简单邮件发送
            emailService.sendSimplePasswordResetEmail(email, code);
            System.out.println("发送密码重置验证码到邮箱: " + email);
            System.out.println("验证码: " + code);
        } catch (Exception e) {
            System.err.println("发送密码重置邮件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证密码重置验证码
     * @param email 用户邮箱
     * @param code 验证码
     * @return 验证结果
     */
    public boolean verifyPasswordResetCode(String email, String code) {
        PasswordResetToken token = passwordResetTokens.get(email);
        if (token == null) {
            return false;
        }
        
        return token.isValid(code);
    }
    
    /**
     * 重置密码
     * @param email 用户邮箱
     * @param newPassword 新密码
     * @return 更新后的用户
     */
    @Transactional
    public User resetPassword(String email, String newPassword) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        // 验证密码复杂度
        String passwordError = validatePasswordComplexity(newPassword);
        if (passwordError != null) {
            throw new Exception(passwordError);
        }

        User user = userOpt.get();

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        
        // 清除验证码
        passwordResetTokens.remove(email);

        return userRepository.save(user);
    }

    /**
     * 申请升级为审核医生
     * @param userId 申请用户ID
     * @param reason 申请理由
     */
    @Transactional
    public void applyForReviewer(Integer userId, String reason) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 验证用户是否为标注医生
        if (user.getRole() != User.Role.DOCTOR) {
            throw new Exception("只有标注医生可以申请升级为审核医生");
        }

        // 验证用户是否已经提交过申请
        if (user.getReviewerApplicationStatus() != null && 
            user.getReviewerApplicationStatus() == User.ReviewerApplicationStatus.PENDING) {
            throw new Exception("您已提交过申请，请等待审核");
        }

        // 设置申请状态
        user.setReviewerApplicationStatus(User.ReviewerApplicationStatus.PENDING);
        user.setReviewerApplicationReason(reason);
        user.setReviewerApplicationDate(LocalDateTime.now());

        userRepository.save(user);
    }

    /**
     * 处理审核医生申请
     * @param userId 申请用户ID
     * @param approved 是否批准
     * @param adminId 处理管理员ID
     */
    @Transactional
    public void processReviewerApplication(Integer userId, boolean approved, Integer adminId) throws Exception {
        // 验证管理员是否有权限
        Optional<User> adminOpt = userRepository.findById(adminId);
        if (!adminOpt.isPresent() || adminOpt.get().getRole() != User.Role.ADMIN) {
            throw new Exception("无权限执行此操作");
        }

        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 验证用户是否有待处理的申请
        if (user.getReviewerApplicationStatus() != User.ReviewerApplicationStatus.PENDING) {
            throw new Exception("该用户没有待处理的申请");
        }

        // 更新申请状态
        if (approved) {
            user.setReviewerApplicationStatus(User.ReviewerApplicationStatus.APPROVED);
            user.setRole(User.Role.REVIEWER);
        } else {
            user.setReviewerApplicationStatus(User.ReviewerApplicationStatus.REJECTED);
        }

        user.setReviewerApplicationProcessedBy(adminOpt.get());
        user.setReviewerApplicationProcessedDate(LocalDateTime.now());

        userRepository.save(user);
    }

    /**
     * 设置用户所属团队
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param operatorId 操作者ID
     */
    @Transactional
    public void setUserTeam(Integer userId, Integer teamId, Integer operatorId) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 验证团队是否存在
        Optional<Team> teamOpt = null;
        if (teamId != null) {
            teamOpt = teamRepository.findById(teamId);
            if (!teamOpt.isPresent()) {
                throw new Exception("团队不存在");
            }
        }

        // 验证操作者是否有权限
        Optional<User> operatorOpt = userRepository.findById(operatorId);
        if (!operatorOpt.isPresent()) {
            throw new Exception("操作者不存在");
        }

        User operator = operatorOpt.get();
        if (operator.getRole() != User.Role.ADMIN && 
            (teamOpt != null && !teamOpt.get().getCreatedBy().getId().equals(operatorId)) &&
            operator.getRole() != User.Role.REVIEWER) {
            throw new Exception("无权更改用户所属团队");
        }

        // 更新用户所属团队
        user.setTeam(teamId != null ? teamOpt.get() : null);

        userRepository.save(user);
    }

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    public User getUserById(Integer userId) throws Exception {
        return userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException("用户不存在: " + userId));
    }

    public Optional<User> getUserByCustomId(String customId) {
        return userRepository.findByCustomId(customId);
    }

    public User getUserByCustomIdOrThrow(String customId) {
        return userRepository.findByCustomId(customId)
                .orElseThrow(() -> new UserNotFoundException("通过customId未找到用户: " + customId));
    }

    /**
     * 根据邮箱获取用户信息
     * @param email 邮箱
     * @return 用户信息
     */
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * 获取所有用户
     * @return 用户列表
     */
    @Transactional
    public List<User> getAllUsers() {
        List<User> users = userRepository.findAll();
        
        // 确保所有用户都有customId
        for (User user : users) {
            if (user.getCustomId() == null || user.getCustomId().isEmpty()) {
                user.setCustomId(generateCustomId());
                userRepository.save(user);
            }
        }
        
        return users;
    }

    /**
     * 获取所有审核医生
     * @return 审核医生列表
     */
    public List<User> getAllReviewers() {
        return userRepository.findByRole(User.Role.REVIEWER);
    }

    /**
     * 获取所有管理员
     * @return 管理员列表
     */
    public List<User> getAllAdmins() {
        return userRepository.findByRole(User.Role.ADMIN);
    }

    /**
     * 获取所有待处理的审核医生申请
     * @return 待处理申请列表
     */
    public List<User> getPendingReviewerApplications() {
        return userRepository.findByReviewerApplicationStatus(User.ReviewerApplicationStatus.PENDING);
    }

    /**
     * 获取团队中的所有用户
     * @param teamId 团队ID
     * @return 用户列表
     */
    public List<User> getUsersByTeam(Integer teamId) {
        return userRepository.findByTeam_Id(teamId);
    }

    /**
     * 获取团队中的所有审核医生
     * @param teamId 团队ID
     * @return 审核医生列表
     */
    public List<User> getReviewersByTeam(Integer teamId) {
        return userRepository.findByTeam_IdAndRole(teamId, User.Role.REVIEWER);
    }

    /**
     * 获取没有团队的用户
     * @return 无团队用户列表
     */
    public List<User> getUsersWithoutTeam() {
        return userRepository.findByTeamIsNull();
    }

    /**
     * 验证密码复杂度
     * @param password 密码
     * @return 验证结果，如果密码不符合要求，返回错误信息，否则返回null
     */
    public String validatePasswordComplexity(String password) {
        if (password == null || password.length() < 8) {
            return "密码长度必须不少于8位";
        }
        
        // 检查密码复杂度
        boolean hasUpperCase = false;
        boolean hasLowerCase = false;
        boolean hasDigit = false;
        boolean hasSpecialChar = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpperCase = true;
            } else if (Character.isLowerCase(c)) {
                hasLowerCase = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (!Character.isLetterOrDigit(c) && !Character.isWhitespace(c)) {
                hasSpecialChar = true;
            }
        }
        
        int typesCount = 0;
        if (hasUpperCase) typesCount++;
        if (hasLowerCase) typesCount++;
        if (hasDigit) typesCount++;
        if (hasSpecialChar) typesCount++;
        
        if (typesCount < 3) {
            return "密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种";
        }
        
        return null;
    }
} 