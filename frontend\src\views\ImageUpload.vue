<template>
  <div class="container">
    <h2 class="mt-4 mb-4">上传血管瘤图像</h2>
    
    <div class="card mb-4">
      <div class="card-body">
        <form @submit.prevent="uploadImage">
          <div class="mb-3">
            <label for="imageName" class="form-label">图像名称</label>
            <input 
              id="imageName" 
              v-model="formData.name" 
              type="text" 
              class="form-control"
              required 
              placeholder="请输入图像名称"
            >
          </div>
          
          <div class="mb-3">
            <label for="description" class="form-label">描述</label>
            <textarea 
              id="description" 
              v-model="formData.description" 
              class="form-control" 
              rows="3" 
              placeholder="请输入图像描述（可选）"
            ></textarea>
          </div>
          
          <div class="mb-3">
            <label for="patientInfo" class="form-label">患者信息</label>
            <input 
              id="patientInfo" 
              v-model="formData.patientInfo" 
              type="text" 
              class="form-control"
              placeholder="请输入患者信息（可选）"
            >
          </div>
          
          <div class="mb-4">
            <label for="imageFile" class="form-label">选择图像文件</label>
            <input 
              id="imageFile" 
              type="file" 
              class="form-control" 
              accept="image/*"
              required
              @change="handleFileChange"
            >
            <div class="form-text">支持JPG、PNG、BMP等常见图像格式，最大文件大小10MB</div>
          </div>
          
          <div v-if="imagePreview" class="mb-4">
            <h5>图像预览</h5>
            <div class="image-preview-container">
              <img :src="imagePreview" class="img-fluid" alt="图像预览">
            </div>
          </div>
          
          <div class="d-flex justify-content-between">
            <button type="submit" class="btn btn-primary" :disabled="isUploading">
              <span v-if="isUploading">
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                正在上传...
              </span>
              <span v-else>上传图像</span>
            </button>
            <button type="button" class="btn btn-secondary" @click="$router.push('/images')">返回图像列表</button>
          </div>
        </form>
      </div>
    </div>
    
    <div v-if="uploadProgress > 0 && uploadProgress < 100" class="progress mt-3">
      <div 
        class="progress-bar progress-bar-striped progress-bar-animated" 
        role="progressbar" 
        :style="{width: uploadProgress + '%'}" 
        :aria-valuenow="uploadProgress" 
        aria-valuemin="0" 
        aria-valuemax="100">
        {{ uploadProgress }}%
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ImageUpload',
  data() {
    return {
      formData: {
        name: '',
        description: '',
        patientInfo: ''
      },
      selectedFile: null,
      imagePreview: null,
      isUploading: false,
      uploadProgress: 0,
    };
  },
  methods: {
    handleFileChange(event) {
      const file = event.target.files[0];
      if (!file) {
        this.selectedFile = null;
        this.imagePreview = null;
        return;
      }
      
      // 验证文件大小（10MB最大）
      if (file.size > 10 * 1024 * 1024) {
        this.$store.dispatch('showAlert', {
          type: 'error',
          message: '文件大小超过限制(10MB)'
        });
        event.target.value = '';
        this.selectedFile = null;
        this.imagePreview = null;
        return;
      }
      
      this.selectedFile = file;
      
      // 创建图片预览
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imagePreview = e.target.result;
      };
      reader.readAsDataURL(file);
    },
    async uploadImage() {
      if (!this.selectedFile) {
        this.$store.dispatch('showAlert', {
          type: 'error',
          message: '请选择要上传的图像文件'
        });
        return;
      }
      
      this.isUploading = true;
      this.uploadProgress = 0;
      
      const formData = new FormData();
      formData.append('file', this.selectedFile);
      formData.append('name', this.formData.name);
      formData.append('description', this.formData.description || '');
      formData.append('patientInfo', this.formData.patientInfo || '');
      
      try {
        await axios.post(`${process.env.VUE_APP_API_URL}/medical/api/images/upload`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          }
        });
        
        this.$store.dispatch('showAlert', {
          type: 'success',
          message: '图像上传成功'
        });
        
        // 重置表单
        this.formData = {
          name: '',
          description: '',
          patientInfo: ''
        };
        this.selectedFile = null;
        this.imagePreview = null;
        document.getElementById('imageFile').value = '';
        
        // 导航到图像列表页面
        this.$router.push('/images');
      } catch (error) {
        this.$store.dispatch('showAlert', {
          type: 'error',
          message: '上传失败: ' + (error.response?.data?.message || error.message)
        });
      } finally {
        this.isUploading = false;
      }
    }
  }
};
</script>

<style scoped>
.image-preview-container {
  max-height: 300px;
  overflow: hidden;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  margin-top: 10px;
}
</style> 