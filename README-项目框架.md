# 血管瘤AI智能诊断平台 V2.0

## 🩺 项目简介

血管瘤AI智能诊断平台是一个基于深度学习的医疗影像分析系统，专门用于血管瘤的智能检测、分类和诊断建议生成。系统采用YOLO + LLM的先进AI技术架构，支持30种血管瘤类型的精确识别和专业诊断建议生成。

### ✨ 核心特性

- **🤖 AI智能检测**: 基于YOLO深度学习模型的快速血管瘤检测（1-3秒）
- **📊 三大类分类体系**: 支持真性血管肿瘤、血管畸形、血管假瘤/易混淆病变三大类30种子类型
- **🧠 LLM诊断建议**: 集成大语言模型生成结构化的专业诊断报告（30-60秒）
- **⚡ 异步处理架构**: 快速YOLO检测 + 后台LLM建议生成的双阶段处理
- **🎯 AI自动分类**: 检测结果自动填写分类标签，提高标注效率
- **📝 医学影像标注**: 专业的医学影像标注和管理系统
- **👥 用户权限管理**: 多角色用户权限管理和团队协作
- **📱 响应式设计**: 支持桌面端和移动端访问

## 🛠 技术架构

### 前端技术栈
- **Vue.js 3.2.13**: 现代化的前端框架
- **Element Plus 2.9.10**: 企业级UI组件库
- **Vue Router 4.0.15**: 单页面应用路由
- **Vuex 4.0.2**: 状态管理
- **Axios 0.27.2**: HTTP客户端

### 后端技术栈
- **Spring Boot 2.7.8**: Java企业级应用框架
- **Spring Security**: 安全认证和授权
- **Spring Data JPA**: 数据持久化
- **MySQL 8.0.28**: 关系型数据库
- **Maven**: 项目构建和依赖管理

### AI服务技术栈
- **FastAPI**: 高性能异步Python Web框架
- **Ultralytics YOLO**: 目标检测模型
- **Ollama**: 大语言模型服务
- **OpenCV**: 计算机视觉库
- **NumPy**: 科学计算库

### 部署技术栈
- **Docker**: 容器化部署
- **Nginx**: 反向代理和静态文件服务
- **Docker Compose**: 多容器编排

## 🚀 快速开始

### 📋 环境要求

- **Java**: 8 或更高版本
- **Node.js**: 16 或更高版本  
- **Python**: 3.8 或更高版本
- **MySQL**: 8.0 或更高版本
- **Docker**: 20.10+ (可选，用于容器化部署)
- **Ollama**: 用于LLM服务 (需要单独安装)

### 🔧 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd xueguan2
```

#### 2. 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE hemangioma_diagnosis DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入初始化脚本
mysql -u root -p hemangioma_diagnosis < mysql/init.sql
```

#### 3. 启动后端服务
```bash
# 配置数据库连接
cp config/application-template.properties config/application.properties
# 编辑config/application.properties，配置数据库连接信息

# 启动Spring Boot应用
mvn clean install
mvn spring-boot:run
```

#### 4. 启动AI服务
```bash
cd ai-service

# 安装Python依赖
pip install -r requirements.txt

# 配置模型路径（编辑ai_service.py中的路径配置）
# 启动AI服务
python ai_service.py
```

#### 5. 启动前端服务
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

#### 6. 启动Ollama服务
```bash
# 安装Ollama (参考官方文档)
# 下载并启动LLM模型
ollama pull deepseek-r1:8b
ollama serve
```

### 🌐 访问应用

- **前端应用**: http://localhost:8080
- **后端API**: http://localhost:8085
- **AI服务**: http://localhost:8086
- **API文档**: http://localhost:8086/docs

## 📁 项目结构

```
xueguan2/
├── 📁 frontend/                    # Vue.js前端应用
│   ├── 📁 src/
│   │   ├── 📁 components/          # Vue组件
│   │   ├── 📁 views/              # 页面视图
│   │   ├── 📁 router/             # 路由配置
│   │   ├── 📁 store/              # Vuex状态管理
│   │   ├── 📁 utils/              # 工具函数
│   │   └── 📁 assets/             # 静态资源
│   ├── 📄 package.json            # 前端依赖配置
│   └── 📄 vue.config.js           # Vue配置文件
├── 📁 src/                        # Spring Boot后端应用
│   └── 📁 main/
│       ├── 📁 java/com/medical/
│       │   ├── 📁 controller/     # 控制器层
│       │   ├── 📁 service/        # 服务层
│       │   ├── 📁 entity/         # 实体类
│       │   ├── 📁 repository/     # 数据访问层
│       │   ├── 📁 config/         # 配置类
│       │   └── 📁 dto/            # 数据传输对象
│       └── 📁 resources/          # 资源文件
├── 📁 ai-service/                 # Python AI服务
│   ├── 📄 ai_service.py           # 主服务文件
│   ├── 📄 requirements.txt        # Python依赖
│   ├── 📁 models/                 # AI模型文件
│   ├── 📁 utils/                  # 工具函数
│   └── 📁 config/                 # 配置文件
├── 📁 mysql/                      # 数据库脚本
│   ├── 📄 init.sql               # 初始化脚本
│   ├── 📄 schema.sql             # 表结构
│   └── 📄 data.sql               # 初始数据
├── 📁 config/                     # 配置文件
│   ├── 📄 application.properties  # Spring Boot配置
│   ├── 📄 nginx.conf             # Nginx配置
│   └── 📁 docker/                # Docker配置
├── 📁 docs/                       # 项目文档
│   ├── 📄 development.md         # 开发指南
│   ├── 📄 deployment.md          # 部署指南
│   ├── 📄 api.md                 # API文档
│   └── 📄 user-manual.md         # 用户手册
├── 📁 resources/                  # 资源文件
│   ├── 📁 models/                # AI模型文件
│   ├── 📁 data/                  # 数据文件
│   └── 📁 images/                # 图片资源
├── 📁 medical_images/             # 医学影像存储
│   ├── 📁 original/              # 原始图像
│   ├── 📁 processed/             # 处理后图像
│   └── 📁 annotated/             # 标注图像
├── 📄 pom.xml                     # Maven配置文件
├── 📄 docker-compose.yml          # Docker编排文件
├── 📄 Dockerfile                  # Docker镜像构建文件
└── 📄 README.md                   # 项目说明文档
```

## 🔧 开发指南

### 代码规范
- **Java**: 遵循Google Java Style Guide
- **JavaScript**: 遵循ESLint标准配置
- **Python**: 遵循PEP 8规范

### 分支管理
- **main**: 主分支，用于生产环境
- **develop**: 开发分支，用于集成测试
- **feature/***: 功能分支，用于新功能开发
- **hotfix/***: 热修复分支，用于紧急修复

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📚 文档链接

- [开发指南](docs/development.md)
- [部署指南](docs/deployment.md)
- [API文档](docs/api.md)
- [用户手册](docs/user-manual.md)
- [核心区域操作手册](核心区域)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目维护者: [项目团队]
- 邮箱: [联系邮箱]
- 项目地址: [GitHub仓库地址]

---

**注意**: 这是一个医疗辅助诊断系统，AI生成的建议仅供参考，不能替代专业医生的诊断。请务必咨询专业医生获取最终诊断和治疗方案。
