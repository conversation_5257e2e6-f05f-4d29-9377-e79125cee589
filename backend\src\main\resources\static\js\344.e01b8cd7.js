"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[344],{49344:(e,t,r)=>{r.r(t),r.d(t,{default:()=>U});r(52675),r(89463),r(62010);var a=r(61431),n={class:"users-container"},i={class:"page-header"},s={class:"header-buttons"},o={class:"filter-bar"},l={key:0},u={key:1},c={key:2},d={class:"search-wrapper"},m={key:0,class:"search-results"},f={class:"dialog-footer"},p={class:"dialog-footer"},h={class:"dialog-footer"},g={class:"dialog-footer"};function b(e,t,r,b,D,v){var w=(0,a.g2)("el-button"),k=(0,a.g2)("el-option"),F=(0,a.g2)("el-select"),y=(0,a.g2)("el-form-item"),C=(0,a.g2)("el-form"),_=(0,a.g2)("el-table-column"),U=(0,a.g2)("el-tag"),A=(0,a.g2)("el-table"),R=(0,a.g2)("el-pagination"),V=(0,a.g2)("el-input"),P=(0,a.g2)("el-divider"),$=(0,a.g2)("el-dialog"),I=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",n,[(0,a.Lk)("div",i,[t[23]||(t[23]=(0,a.Lk)("h2",null,"人员管理",-1)),(0,a.Lk)("div",s,[v.currentUserHasDepartment?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(w,{key:0,type:"primary",onClick:v.handleJoinDepartment},{default:(0,a.k6)((function(){return t[20]||(t[20]=[(0,a.eW)(" 加入部门 ")])})),_:1,__:[20]},8,["onClick"])),v.currentUserHasDepartment||!v.isAdmin&&!v.isReviewer?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(w,{key:1,type:"success",onClick:v.handleCreateDepartment},{default:(0,a.k6)((function(){return t[21]||(t[21]=[(0,a.eW)(" 创建部门 ")])})),_:1,__:[21]},8,["onClick"])),v.isAdmin?((0,a.uX)(),(0,a.Wv)(w,{key:2,type:"warning",onClick:v.viewReviewerApplications},{default:(0,a.k6)((function(){return t[22]||(t[22]=[(0,a.eW)(" 查看权限申请 ")])})),_:1,__:[22]},8,["onClick"])):(0,a.Q3)("",!0)])]),(0,a.Lk)("div",o,[(0,a.bF)(C,{inline:!0,class:"filter-form"},{default:(0,a.k6)((function(){return[(0,a.bF)(y,{label:"所属部门"},{default:(0,a.k6)((function(){return[(0,a.bF)(F,{modelValue:D.departmentFilter,"onUpdate:modelValue":t[0]||(t[0]=function(e){return D.departmentFilter=e}),placeholder:"全部部门",clearable:""},{default:(0,a.k6)((function(){return[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(D.departments,(function(e){return(0,a.uX)(),(0,a.Wv)(k,{key:e,label:e,value:e},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),(0,a.bF)(y,{label:"成员角色"},{default:(0,a.k6)((function(){return[(0,a.bF)(F,{modelValue:D.roleFilter,"onUpdate:modelValue":t[1]||(t[1]=function(e){return D.roleFilter=e}),placeholder:"全部角色",clearable:""},{default:(0,a.k6)((function(){return[(0,a.bF)(k,{label:"管理员",value:"ADMIN"}),(0,a.bF)(k,{label:"标注医生",value:"DOCTOR"}),(0,a.bF)(k,{label:"审核医生",value:"REVIEWER"})]})),_:1},8,["modelValue"])]})),_:1}),(0,a.bF)(y,null,{default:(0,a.k6)((function(){return[(0,a.bF)(w,{type:"primary",onClick:v.applyFilters},{default:(0,a.k6)((function(){return t[24]||(t[24]=[(0,a.eW)("筛选")])})),_:1,__:[24]},8,["onClick"]),(0,a.bF)(w,{onClick:v.resetFilters},{default:(0,a.k6)((function(){return t[25]||(t[25]=[(0,a.eW)("重置")])})),_:1,__:[25]},8,["onClick"])]})),_:1})]})),_:1})]),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(A,{data:v.displayUsers,style:{width:"100%"}},{default:(0,a.k6)((function(){return[(0,a.bF)(_,{prop:"name",label:"姓名",width:"120"}),(0,a.bF)(_,{prop:"department",label:"所属部门",width:"150"}),(0,a.bF)(_,{prop:"role",label:"角色"},{default:(0,a.k6)((function(e){return[(0,a.bF)(U,{type:v.getRoleType(e.row.role)},{default:(0,a.k6)((function(){return[(0,a.eW)((0,a.v_)(v.getRoleName(e.row.role)),1)]})),_:2},1032,["type"])]})),_:1}),(0,a.bF)(_,{prop:"hospital",label:"所属医院"}),(0,a.bF)(_,{prop:"team",label:"所属团队",width:"180"},{default:(0,a.k6)((function(e){return[e.row.team&&e.row.team.name?((0,a.uX)(),(0,a.CE)("span",l,(0,a.v_)(e.row.team.name),1)):e.row.team&&"string"===typeof e.row.team?((0,a.uX)(),(0,a.CE)("span",u,(0,a.v_)(e.row.team),1)):((0,a.uX)(),(0,a.CE)("span",c,"未加入团队"))]})),_:1})]})),_:1},8,["data"])),[[I,D.loading]]),(0,a.bF)(R,{background:"",layout:"total, sizes, prev, pager, next",total:D.filteredUsers.length,"page-size":D.pageSize,"page-sizes":[10,20,50,100],class:"pagination",onCurrentChange:v.handlePageChange,onSizeChange:v.handleSizeChange},null,8,["total","page-size","onCurrentChange","onSizeChange"]),(0,a.bF)($,{modelValue:D.dialogVisible,"onUpdate:modelValue":t[4]||(t[4]=function(e){return D.dialogVisible=e}),title:"添加成员",width:"500px","close-on-click-modal":!1,onClose:v.closeDialog},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",f,[(0,a.bF)(w,{onClick:t[3]||(t[3]=function(e){return D.dialogVisible=!1})},{default:(0,a.k6)((function(){return t[26]||(t[26]=[(0,a.eW)("取消")])})),_:1,__:[26]}),(0,a.bF)(w,{type:"primary",onClick:v.submitForm},{default:(0,a.k6)((function(){return t[27]||(t[27]=[(0,a.eW)("确 定")])})),_:1,__:[27]},8,["onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.Lk)("div",d,[(0,a.bF)(V,{modelValue:D.searchQuery,"onUpdate:modelValue":t[2]||(t[2]=function(e){return D.searchQuery=e}),placeholder:"搜索用户名、姓名、部门、医院",clearable:"",onInput:v.handleSearch},null,8,["modelValue","onInput"])]),D.searchResults.length>0?((0,a.uX)(),(0,a.CE)("div",m,[(0,a.bF)(A,{data:D.searchResults,style:{width:"100%"},"highlight-current-row":"",onRowClick:v.handleRowClick},{default:(0,a.k6)((function(){return[(0,a.bF)(_,{prop:"name",label:"姓名",width:"120"}),(0,a.bF)(_,{prop:"department",label:"部门",width:"140"}),(0,a.bF)(_,{prop:"hospital",label:"所属医院"})]})),_:1},8,["data","onRowClick"])])):(0,a.Q3)("",!0),D.searchResults.length>0?((0,a.uX)(),(0,a.Wv)(P,{key:1})):(0,a.Q3)("",!0)]})),_:1},8,["modelValue","onClose"]),(0,a.bF)($,{modelValue:D.resetPasswordDialog.visible,"onUpdate:modelValue":t[8]||(t[8]=function(e){return D.resetPasswordDialog.visible=e}),title:"重置密码",width:"400px",onClose:t[9]||(t[9]=function(e){return D.resetPasswordDialog.visible=!1})},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",p,[(0,a.bF)(w,{onClick:t[7]||(t[7]=function(e){return D.resetPasswordDialog.visible=!1})},{default:(0,a.k6)((function(){return t[28]||(t[28]=[(0,a.eW)("取消")])})),_:1,__:[28]}),(0,a.bF)(w,{type:"primary",onClick:v.submitResetPassword},{default:(0,a.k6)((function(){return t[29]||(t[29]=[(0,a.eW)("确定")])})),_:1,__:[29]},8,["onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.bF)(C,{ref:"resetPasswordForm",model:D.resetPasswordDialog.form,rules:D.resetPasswordRules,"label-width":"100px"},{default:(0,a.k6)((function(){return[(0,a.bF)(y,{label:"新密码",prop:"password"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:D.resetPasswordDialog.form.password,"onUpdate:modelValue":t[5]||(t[5]=function(e){return D.resetPasswordDialog.form.password=e}),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]})),_:1}),(0,a.bF)(y,{label:"确认新密码",prop:"confirmPassword"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:D.resetPasswordDialog.form.confirmPassword,"onUpdate:modelValue":t[6]||(t[6]=function(e){return D.resetPasswordDialog.form.confirmPassword=e}),type:"password",placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"]),(0,a.bF)($,{modelValue:D.joinDepartmentDialog.visible,"onUpdate:modelValue":t[13]||(t[13]=function(e){return D.joinDepartmentDialog.visible=e}),title:"加入部门",width:"500px",onClose:t[14]||(t[14]=function(e){return D.joinDepartmentDialog.visible=!1})},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",h,[(0,a.bF)(w,{onClick:t[12]||(t[12]=function(e){return D.joinDepartmentDialog.visible=!1})},{default:(0,a.k6)((function(){return t[30]||(t[30]=[(0,a.eW)("取消")])})),_:1,__:[30]}),(0,a.bF)(w,{type:"primary",onClick:v.submitJoinDepartment},{default:(0,a.k6)((function(){return[(0,a.eW)((0,a.v_)(D.joinDepartmentDialog.form.teamId?"申请加入":"确定"),1)]})),_:1},8,["onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.bF)(C,{model:D.joinDepartmentDialog.form,"label-width":"80px"},{default:(0,a.k6)((function(){return[(0,a.bF)(y,{label:"部门"},{default:(0,a.k6)((function(){return[(0,a.bF)(F,{modelValue:D.joinDepartmentDialog.form.department,"onUpdate:modelValue":t[10]||(t[10]=function(e){return D.joinDepartmentDialog.form.department=e}),placeholder:"请选择部门",style:{width:"100%"},onChange:v.handleDepartmentChange},{default:(0,a.k6)((function(){return[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(D.departments,(function(e){return(0,a.uX)(),(0,a.Wv)(k,{key:e,label:e,value:e},null,8,["label","value"])})),128))]})),_:1},8,["modelValue","onChange"])]})),_:1}),D.joinDepartmentDialog.form.teamId?((0,a.uX)(),(0,a.Wv)(y,{key:0,label:"申请理由"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:D.joinDepartmentDialog.form.reason,"onUpdate:modelValue":t[11]||(t[11]=function(e){return D.joinDepartmentDialog.form.reason=e}),type:"textarea",placeholder:"请简要说明加入部门的原因（可选）",rows:3},null,8,["modelValue"])]})),_:1})):(0,a.Q3)("",!0)]})),_:1},8,["model"])]})),_:1},8,["modelValue"]),(0,a.bF)($,{modelValue:D.createDepartmentDialog.visible,"onUpdate:modelValue":t[18]||(t[18]=function(e){return D.createDepartmentDialog.visible=e}),title:"创建部门",width:"500px",onClose:t[19]||(t[19]=function(e){return D.createDepartmentDialog.visible=!1})},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",g,[(0,a.bF)(w,{onClick:t[17]||(t[17]=function(e){return D.createDepartmentDialog.visible=!1})},{default:(0,a.k6)((function(){return t[31]||(t[31]=[(0,a.eW)("取消")])})),_:1,__:[31]}),(0,a.bF)(w,{type:"primary",onClick:v.submitCreateDepartment},{default:(0,a.k6)((function(){return t[32]||(t[32]=[(0,a.eW)("确定")])})),_:1,__:[32]},8,["onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.bF)(C,{ref:"createDepartmentForm",model:D.createDepartmentDialog.form,rules:D.departmentRules,"label-width":"80px"},{default:(0,a.k6)((function(){return[(0,a.bF)(y,{label:"部门名称",prop:"name"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:D.createDepartmentDialog.form.name,"onUpdate:modelValue":t[15]||(t[15]=function(e){return D.createDepartmentDialog.form.name=e}),placeholder:"请输入部门名称"},null,8,["modelValue"])]})),_:1}),(0,a.bF)(y,{label:"描述",prop:"description"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:D.createDepartmentDialog.form.description,"onUpdate:modelValue":t[16]||(t[16]=function(e){return D.createDepartmentDialog.form.description=e}),type:"textarea",placeholder:"请输入部门描述",rows:3},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"])])}var D=r(55593),v=r(59158),w=r(88844),k=(r(16280),r(76918),r(28706),r(2008),r(50113),r(51629),r(23418),r(74423),r(64346),r(62062),r(44114),r(34782),r(15086),r(23288),r(18111),r(22489),r(20116),r(7588),r(61701),r(13579),r(79432),r(26099),r(31415),r(17642),r(58004),r(33853),r(45876),r(32475),r(15024),r(31698),r(21699),r(47764),r(42762),r(23500),r(62953),r(76031),r(36149)),F=r(66278);const y={name:"Users",data:function(){var e=this,t=function(t,r,a){r!==e.userForm.password?a(new Error("两次输入密码不一致!")):a()},r=function(t,r,a){r!==e.resetPasswordDialog.form.password?a(new Error("两次输入密码不一致!")):a()};return{loading:!1,users:[],filteredUsers:[],departments:[],departmentFilter:"",roleFilter:"",currentPage:1,pageSize:10,dialogVisible:!1,searchMode:!0,searchQuery:"",searchResults:[],userForm:{id:null,name:"",role:"DOCTOR",department:"",hospital:"",email:"",password:"",confirmPassword:"",active:!0,createdAt:null},userRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],department:[{required:!0,message:"请选择或创建部门",trigger:"change"}],hospital:[{required:!0,message:"请输入所属医院",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:t,trigger:"blur"}]},resetPasswordDialog:{visible:!1,userId:null,form:{password:"",confirmPassword:""}},resetPasswordRules:{password:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度至少6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:r,trigger:"blur"}]},selectedUser:null,joinDepartmentDialog:{visible:!1,form:{department:"",teamId:null,reason:""}},createDepartmentDialog:{visible:!1,form:{name:"",description:""}},departmentRules:{name:[{required:!0,message:"请输入部门名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]},reviewerApplications:[]}},computed:(0,w.A)((0,w.A)({},(0,F.L8)({currentUser:"getUser"})),{},{currentUserHasDepartment:function(){return this.currentUser&&this.currentUser.department},isAdmin:function(){return this.currentUser&&"ADMIN"===this.currentUser.role},isDoctor:function(){return this.currentUser&&"DOCTOR"===this.currentUser.role},isReviewer:function(){return this.currentUser&&"REVIEWER"===this.currentUser.role},displayUsers:function(){var e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredUsers.slice(e,t)}}),created:function(){if(this.fetchUsers(),this.isAdmin&&this.fetchReviewerApplications(),!this.currentUser){var e=localStorage.getItem("user");if(e)try{this.$store.commit("setUser",JSON.parse(e))}catch(t){console.error("解析用户数据失败",t)}}},methods:{getRoleType:function(e){var t={ADMIN:"danger",DOCTOR:"primary",REVIEWER:"success"};return t[e]||"info"},getRoleName:function(e){var t={ADMIN:"管理员",DOCTOR:"标注医生",REVIEWER:"审核医生"};return t[e]||"未知"},formatDate:function(e){if(!e)return"未知";var t=new Date(e);return t.toLocaleString("zh-CN")},handlePageChange:function(e){this.currentPage=e},handleSizeChange:function(e){this.pageSize=e,this.currentPage=1},applyFilters:function(){this.filterUsers(),this.currentPage=1},resetFilters:function(){this.departmentFilter="",this.roleFilter="",this.filterUsers(),this.currentPage=1},filterUsers:function(){var e=this;if(this.departmentFilter||this.roleFilter)return Array.isArray(this.users)?void(this.filteredUsers=this.users.filter((function(t){if(!t)return!1;var r=!e.departmentFilter||t.department&&t.department===e.departmentFilter,a=!e.roleFilter||t.role&&t.role===e.roleFilter;return r&&a}))):(console.error("users不是数组，无法筛选",this.users),void(this.filteredUsers=[]));this.filteredUsers=Array.isArray(this.users)?(0,v.A)(this.users):[]},fetchUsers:function(){var e=this;this.loading=!0,k["default"].users.getAll().then((function(t){t&&t.data?Array.isArray(t.data)?e.users=t.data:t.data.content&&Array.isArray(t.data.content)?e.users=t.data.content:t.data.data&&Array.isArray(t.data.data)?e.users=t.data.data:t.data.items&&Array.isArray(t.data.items)?e.users=t.data.items:t.data.users&&Array.isArray(t.data.users)?e.users=t.data.users:"object"===(0,D.A)(t.data)?(console.warn("API返回了对象而不是数组:",t.data),e.users=[]):(console.error("API返回了非预期格式:",t.data),e.users=[]):e.users=[],e.users=e.users.map((function(e){return e.team?"string"===typeof e.team?e.team={name:e.team}:"object"!==(0,D.A)(e.team)||e.team.name||(e.team.name="未知团队"):e.team={name:"未加入团队"},e})),e.filteredUsers=(0,v.A)(e.users);var r=new Set;e.users.forEach((function(e){e&&e.department&&r.add(e.department)}));var a=["内科","外科","影像科","放射科","急诊科","脑科","管理部"];return e.departments=[].concat(a,(0,v.A)(Array.from(r).filter((function(e){return!a.includes(e)})))),k["default"].teams.getAll()})).then((function(e){if(e&&e.data){var t=e.data,r=t.map((function(e){return k["default"].teams.getTeamMembers(e.id).then((function(t){var r=t.data||[];return{teamId:e.id,teamName:e.name,members:r}}))["catch"]((function(t){return console.error("获取团队 ".concat(e.id," 成员失败:"),t),{teamId:e.id,teamName:e.name,members:[]}}))}));return Promise.all(r)}return[]})).then((function(t){if(t&&t.length>0){var r={};t.forEach((function(e){var t=e.teamId,a=e.teamName,n=e.members;n.forEach((function(e){e&&e.id&&(r[e.id]={id:t,name:a})}))})),e.users=e.users.map((function(e){return e&&e.id&&r[e.id]&&(e.team=r[e.id]),e})),e.filteredUsers=(0,v.A)(e.users)}}))["catch"]((function(t){console.error("获取用户列表失败:",t),e.$message.error("获取用户列表失败: "+(t.message||"未知错误")),e.users=[],e.filteredUsers=[]}))["finally"]((function(){e.loading=!1}))},handleAddUser:function(){this.userForm={id:null,name:"",role:"DOCTOR",department:"",hospital:"",email:"",password:"",confirmPassword:"",active:!0,createdAt:null},this.searchMode=!0,this.searchQuery="",this.searchResults=[],this.dialogVisible=!0},handleEditUser:function(e){this.userForm={id:e.id,name:e.name,role:e.role,department:e.department,hospital:e.hospital,email:e.email,active:!1!==e.active,createdAt:e.createdAt},this.dialogVisible=!0},handleDeleteUser:function(e){var t=this;this.loading=!0,k["default"].users.deleteUser(e.id).then((function(){t.$message.success("成员 ".concat(e.name," 已删除")),t.fetchUsers()}))["catch"]((function(e){console.error("删除用户失败:",e),t.$message.error("删除失败: "+(e.message||"未知错误"))}))["finally"]((function(){t.loading=!1}))},handleStatusChange:function(e,t){var r=this,a=(0,w.A)((0,w.A)({},e),{},{active:t});k["default"].users.updateUser(e.id,a).then((function(){r.$message.success("已".concat(t?"启用":"禁用","成员: ").concat(e.name))}))["catch"]((function(a){console.error("更新用户状态失败:",a),r.$message.error("更新状态失败: "+(a.message||"未知错误")),e.active=!t}))},handleResetPassword:function(e){this.resetPasswordDialog.visible=!0,this.resetPasswordDialog.userId=e.id,this.resetPasswordDialog.form={password:"",confirmPassword:""}},closeDialog:function(){this.dialogVisible=!1,this.$refs.userFormRef&&this.$refs.userFormRef.resetFields()},handleSearch:function(){var e=this;this.searchQuery&&""!==this.searchQuery.trim()?(this.searchTimer&&clearTimeout(this.searchTimer),this.searchTimer=setTimeout((function(){var t=e.searchQuery.toLowerCase().trim();e.searchResults=e.users.filter((function(e){return e.name&&e.name.toLowerCase().includes(t)||e.department&&e.department.toLowerCase().includes(t)||e.hospital&&e.hospital.toLowerCase().includes(t)})).slice(0,10)}),300)):this.searchResults=[]},submitForm:function(){if(this.selectedUser)return this.$message.success("已添加成员: ".concat(this.selectedUser.name)),this.dialogVisible=!1,this.searchQuery="",this.searchResults=[],this.selectedUser=null,void this.fetchUsers();this.searchQuery&&""!==this.searchQuery.trim()?0!==this.searchResults.length?this.$message.warning("请从搜索结果中选择一个成员"):this.$message.warning("未找到匹配的成员"):this.$message.warning("请输入搜索内容")},submitResetPassword:function(){var e=this;this.$refs.resetPasswordForm.validate((function(t){if(!t)return!1;e.loading=!0,k["default"].users.resetPassword(e.resetPasswordDialog.userId,e.resetPasswordDialog.form.password).then((function(){e.$message.success("密码重置成功"),e.resetPasswordDialog.visible=!1}))["catch"]((function(t){console.error("密码重置失败:",t),e.$message.error("密码重置失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1}))}))},selectUser:function(e){this.userForm={name:e.name,role:e.role,department:e.department,hospital:e.hospital,email:e.email,active:!0,password:"",confirmPassword:"",createdAt:null},this.searchMode=!1},handleRowClick:function(e){this.selectedUser=e,this.$message.info("已选择: ".concat(e.name))},handleJoinDepartment:function(){var e=this;this.loading=!0,k["default"].teams.getAll().then((function(t){var r=t.data||[];e.departments=r.map((function(e){return e.name})),e.joinDepartmentDialog.visible=!0,e.joinDepartmentDialog.form={department:"",teamId:null,reason:""}}))["catch"]((function(t){console.error("获取部门列表失败:",t),e.$message.error("获取部门列表失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1}))},handleDepartmentChange:function(e){var t=this;k["default"].teams.getAll().then((function(r){var a=r.data||[],n=a.find((function(t){return t.name===e}));n&&(t.joinDepartmentDialog.form.teamId=n.id)}))["catch"]((function(e){console.error("获取团队ID失败:",e)}))},submitJoinDepartment:function(){var e=this;if(this.joinDepartmentDialog.form.department){this.loading=!0;var t=this.joinDepartmentDialog.form.teamId,r=this.joinDepartmentDialog.form.department;t?k["default"].teams.applyToJoinTeam(t,this.joinDepartmentDialog.form.reason||"申请加入部门").then((function(){e.$message.success("已成功提交加入".concat(r,"申请，请等待管理员审核")),e.joinDepartmentDialog.visible=!1,e.fetchUsers()}))["catch"]((function(t){var r;console.error("申请加入部门失败:",t),e.$message.error("申请加入部门失败: "+((null===(r=t.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||t.message||"未知错误"))}))["finally"]((function(){e.loading=!1})):k["default"].teams.create({name:r,description:"".concat(r,"部门")}).then((function(t){var r=t.data;return k["default"].teams.joinTeam(r.id,e.currentUser.id)})).then((function(){var t=(0,w.A)((0,w.A)({},e.currentUser),{},{department:r});return e.$store.dispatch("updateUserProfile",t)})).then((function(){e.$message.success("已成功加入".concat(r,"部门")),e.joinDepartmentDialog.visible=!1,e.fetchUsers()}))["catch"]((function(t){console.error("加入部门失败:",t),e.$message.error("加入部门失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1}))}else this.$message.warning("请选择要加入的部门")},handleCreateDepartment:function(){this.createDepartmentDialog.visible=!0,this.createDepartmentDialog.form={name:"",description:""}},submitCreateDepartment:function(){var e=this;this.$refs.createDepartmentForm.validate((function(t){if(!t)return!1;e.loading=!0;var r={name:e.createDepartmentDialog.form.name,description:e.createDepartmentDialog.form.description||"".concat(e.createDepartmentDialog.form.name,"部门")};k["default"].teams.create(r).then((function(t){var r=t.data;return k["default"].teams.joinTeam(r.id,e.currentUser.id)})).then((function(){var t=(0,w.A)((0,w.A)({},e.currentUser),{},{department:e.createDepartmentDialog.form.name});return e.$store.dispatch("updateUserProfile",t)})).then((function(){e.$message.success("已成功创建并加入".concat(e.createDepartmentDialog.form.name,"部门")),e.createDepartmentDialog.visible=!1,e.departments.includes(e.createDepartmentDialog.form.name)||e.departments.push(e.createDepartmentDialog.form.name),e.fetchUsers()}))["catch"]((function(t){console.error("创建部门失败:",t),e.$message.error("创建部门失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1}))}))},fetchReviewerApplications:function(){var e=this;this.isAdmin&&(this.loading=!0,k["default"].users.getPendingReviewerApplications().then((function(t){e.reviewerApplications=t.data||[]}))["catch"]((function(t){console.error("获取权限升级申请失败:",t),e.$message.error("获取权限升级申请失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1})))},viewReviewerApplications:function(){this.$router.push("/admin/reviewer-applications")},hasReviewerApplication:function(e){return this.reviewerApplications.some((function(t){return t.userId===e}))},handleUpgradeRole:function(e){var t=this;this.$confirm("确定将 ".concat(e.name," 的角色升级为审核医生吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$prompt("请输入批准备注（可选）","批准权限升级",{confirmButtonText:"确定",cancelButtonText:"取消",inputPlaceholder:"请输入备注信息"}).then((function(r){var a=r.value;t.loading=!0,k["default"].users.processReviewerApplication(e.id,{approved:!0,remarks:a||"管理员批准"}).then((function(){t.$message.success("已成功将 ".concat(e.name," 升级为审核医生")),t.fetchUsers(),t.fetchReviewerApplications()}))["catch"]((function(e){var r;console.error("升级权限失败:",e),t.$message.error("升级权限失败: "+((null===(r=e.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||e.message||"未知错误"))}))["finally"]((function(){t.loading=!1}))}))["catch"]((function(){}))}))["catch"]((function(){}))}}};var C=r(66262);const _=(0,C.A)(y,[["render",b]]),U=_}}]);
//# sourceMappingURL=344.e01b8cd7.js.map