<template>
  <div class="structured-form-container">
    <div class="page-header">
      <div class="header-content">
        <h2>病例信息填写</h2>
      </div>
      <div class="header-actions">
        <el-button type="success" @click="submitForm">提交</el-button>
        <el-button type="primary" @click="saveAndExit">保存并退出</el-button>
        <el-button @click="returnToAnnotation">返回上一步</el-button>
      </div>
    </div>

    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>

    <div v-else class="form-content">
      <el-form ref="caseForm" :model="formData" label-position="top" :rules="rules">
        <!-- 基础信息 -->
        <div class="form-section">
          <h3 class="section-title">基础信息</h3>
          <div class="form-grid">
            <el-form-item label="病变部位" prop="location">
              <el-cascader
                v-model="formData.location"
                ref="locationCascader"
                :options="locationOptions"
                :props="{ expandTrigger: 'hover' }"
                placeholder="请选择病变部位"
              ></el-cascader>
            </el-form-item>
            
            <el-form-item label="患者年龄" prop="patientAge">
              <el-input-number v-model="formData.patientAge" :min="0" :max="100" />
            </el-form-item>
            
            <el-form-item label="病程阶段" prop="stage">
              <el-radio-group v-model="formData.stage">
                <el-radio value="初期" label="初期（<6个月）">初期（<6个月）</el-radio>
                <el-radio value="进展期" label="进展期">进展期</el-radio>
                <el-radio value="稳定期" label="稳定期">稳定期</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
        
        <!-- 形态与临床特征 -->
        <div class="form-section">
          <h3 class="section-title">形态与临床特征</h3>
          <div class="form-grid">
            <el-form-item label="形态特征" prop="morphology">
              <el-checkbox-group v-model="formData.morphology">
                <el-checkbox value="结节状" label="结节状">结节状</el-checkbox>
                <el-checkbox value="团块状" label="团块状">团块状</el-checkbox>
                <el-checkbox value="弥漫性" label="弥漫性">弥漫性</el-checkbox>
                <el-checkbox value="溃疡形成" label="溃疡形成">溃疡形成</el-checkbox>
              </el-checkbox-group>
              <el-input 
                v-model="formData.morphologyDesc" 
                type="textarea" 
                placeholder="补充形态描述" 
                :rows="2"
                style="margin-top: 10px;"
              ></el-input>
            </el-form-item>
            
            <el-form-item label="血流信号" prop="bloodFlow">
              <el-select v-model="formData.bloodFlow" placeholder="请选择血流信号强度">
                <el-option label="丰富" value="丰富" />
                <el-option label="中等" value="中等" />
                <el-option label="稀疏" value="稀疏" />
                <el-option label="无" value="无" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="症状表现" prop="symptoms">
              <el-checkbox-group v-model="formData.symptoms">
                <el-checkbox value="疼痛" label="疼痛">疼痛</el-checkbox>
                <el-checkbox value="瘙痒" label="瘙痒">瘙痒</el-checkbox>
                <el-checkbox value="出血" label="出血">出血</el-checkbox>
                <el-checkbox value="功能障碍" label="功能障碍">功能障碍</el-checkbox>
              </el-checkbox-group>
              <el-input 
                v-model="formData.symptomsDesc" 
                type="textarea" 
                placeholder="症状详细描述" 
                :rows="2"
                style="margin-top: 10px;"
              ></el-input>
            </el-form-item>
            
            <el-form-item label="并发症" prop="complications">
              <el-checkbox-group v-model="formData.complications">
                <el-checkbox value="感染" label="感染">感染</el-checkbox>
                <el-checkbox value="血栓" label="血栓">血栓</el-checkbox>
                <el-checkbox value="压迫邻近器官" label="压迫邻近器官">压迫邻近器官</el-checkbox>
                <el-checkbox value="其他" label="其他">其他</el-checkbox>
              </el-checkbox-group>
              <el-input 
                v-model="formData.complicationsDesc" 
                type="textarea" 
                placeholder="并发症详细描述" 
                :rows="2"
                style="margin-top: 10px;"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        
        <!-- 诊断与治疗建议 -->
        <div class="form-section">
          <h3 class="section-title">诊断与治疗建议</h3>
          <div class="form-grid">
            <el-form-item label="诊断结论" prop="diagnosis">
              <el-input v-model="formData.diagnosis" placeholder="请输入诊断结论"></el-input>
              <el-input v-model="formData.diagnosisCode" placeholder="ICD-11编码" style="margin-top: 10px;"></el-input>
            </el-form-item>
            
            <el-form-item label="治疗优先级" prop="treatmentPriority">
              <el-radio-group v-model="formData.treatmentPriority">
                <el-radio value="紧急" label="紧急（24h内处理）">紧急（24h内处理）</el-radio>
                <el-radio value="常规" label="常规（1-2周）">常规（1-2周）</el-radio>
                <el-radio value="观察" label="观察">观察</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="推荐方案" prop="treatmentPlan">
              <el-checkbox-group v-model="formData.treatmentPlan">
                <el-checkbox value="手术切除" label="手术切除">手术切除</el-checkbox>
                <el-checkbox value="激光治疗" label="激光治疗">激光治疗</el-checkbox>
                <el-checkbox value="硬化剂注射" label="硬化剂注射">硬化剂注射</el-checkbox>
                <el-checkbox value="药物治疗" label="药物治疗">药物治疗</el-checkbox>
              </el-checkbox-group>
              <el-input 
                v-model="formData.treatmentDetail" 
                type="textarea" 
                placeholder="请输入详细治疗方案，包括剂量、注意事项等" 
                :rows="3"
                style="margin-top: 10px;"
              ></el-input>
            </el-form-item>
            
            <el-form-item label="禁忌症提示" prop="contraindications">
              <el-input 
                v-model="formData.contraindications" 
                type="textarea" 
                placeholder="请输入禁忌症提示" 
                :rows="3"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        
        <!-- 随访与预后 -->
        <div class="form-section">
          <h3 class="section-title">随访与预后</h3>
          <div class="form-grid">
            <el-form-item label="复查周期" prop="followUpPeriod">
              <el-radio-group v-model="formData.followUpPeriod">
                <el-radio value="1个月" label="1个月">1个月</el-radio>
                <el-radio value="3个月" label="3个月">3个月</el-radio>
                <el-radio value="6个月" label="6个月">6个月</el-radio>
                <el-radio value="custom" label="自定义">自定义</el-radio>
              </el-radio-group>
              <el-input v-model="formData.customFollowUp" placeholder="请输入自定义时间" 
                v-if="formData.followUpPeriod === 'custom'"
                style="margin-top: 10px; width: 200px;"
              ></el-input>
            </el-form-item>
            
            <el-form-item label="预后评估" prop="prognosisRating">
              <el-rate 
                v-model="formData.prognosisRating" 
                :texts="['易复发', '有复发风险', '一般', '较好', '完全根治']"
                show-text
              ></el-rate>
            </el-form-item>
            
            <el-form-item label="患者教育重点" prop="patientEducation">
              <el-checkbox-group v-model="formData.patientEducation">
                <el-checkbox value="避免日晒" label="避免日晒">避免日晒</el-checkbox>
                <el-checkbox value="禁止抓挠" label="禁止抓挠">禁止抓挠</el-checkbox>
                <el-checkbox value="定期服药提醒" label="定期服药提醒">定期服药提醒</el-checkbox>
                <el-checkbox value="防护建议" label="防护建议">防护建议</el-checkbox>
                <el-checkbox value="生活方式调整" label="生活方式调整">生活方式调整</el-checkbox>
              </el-checkbox-group>
              <el-input 
                v-model="formData.patientEducationDetail" 
                type="textarea" 
                placeholder="详细教育指导内容" 
                :rows="3"
                style="margin-top: 10px;"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        
        <!-- 提交按钮 -->
        <div class="form-actions">
          <el-button type="success" @click="submitForm">提交</el-button>
        </div>
      </el-form>
    </div>
  </div>
  
  <!-- 保存并退出确认对话框 -->
  <ConfirmDialog
    v-model="saveDialogVisible"
    title="保存并退出"
    message="是否保留当前填写进度？下次可以继续完成"
    confirm-text="保留进度"
    cancel-text="不保留"
    icon="warning"
    @confirm="handleSaveProgress"
    @cancel="handleDiscardProgress"
  />
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import api from '@/utils/api';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import { formatDate } from '../utils/formatters';
import { getImageUrl } from '../utils/imageHelper';

export default {
  name: 'CaseStructuredForm',
  components: {
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      annotationData: null,
      imageId: null,
      saveDialogVisible: false,
      formData: {
        // 基础信息
        location: [],
        patientAge: null,
        stage: '',
        
        // 形态与临床特征
        morphology: [],
        morphologyDesc: '',
        bloodFlow: '',
        symptoms: [],
        symptomsDesc: '',
        complications: [],
        complicationsDesc: '',
        
        // 诊断与治疗建议
        diagnosis: '',
        diagnosisCode: '',
        treatmentPriority: '',
        treatmentPlan: [],
        treatmentDetail: '',
        contraindications: '',
        
        // 随访与预后
        followUpPeriod: '',
        customFollowUp: '',
        prognosisRating: 3,
        patientEducation: [],
        patientEducationDetail: ''
      },
      
      // 表单验证规则
      rules: {
        location: [{ required: true, message: '请选择病变部位', trigger: 'change' }],
        patientAge: [{ required: true, message: '请输入患者年龄', trigger: 'blur' }],
        stage: [{ required: true, message: '请选择病程阶段', trigger: 'change' }],
        bloodFlow: [{ required: true, message: '请选择血流信号', trigger: 'change' }],
        diagnosis: [{ required: true, message: '请输入诊断结论', trigger: 'blur' }],
        treatmentPriority: [{ required: true, message: '请选择治疗优先级', trigger: 'change' }],
        followUpPeriod: [{ required: true, message: '请选择复查周期', trigger: 'change' }]
      },
      
      // 病变部位层级选项
      locationOptions: [
        {
          value: '皮肤',
          label: '皮肤',
          children: [
            {
              value: '面部',
              label: '面部',
              children: [
                { value: '额头', label: '额头' },
                { value: '眼睑', label: '眼睑' },
                { value: '鼻翼', label: '鼻翼' },
                { value: '唇部', label: '唇部' },
                { value: '耳朵', label: '耳朵' },
                { value: '其他', label: '其他' }
              ]
            },
            {
              value: '颈部',
              label: '颈部'
            },
            {
              value: '躯干',
              label: '躯干'
            },
            {
              value: '四肢',
              label: '四肢',
              children: [
                { value: '手臂', label: '手臂' },
                { value: '腿部', label: '腿部' },
                { value: '足部', label: '足部' }
              ]
            }
          ]
        },
        {
          value: '内脏',
          label: '内脏',
          children: [
            {
              value: '肝脏',
              label: '肝脏',
              children: [
                { value: '左叶', label: '左叶' },
                { value: '右叶', label: '右叶' }
              ]
            },
            { value: '脾脏', label: '脾脏' },
            { value: '肺部', label: '肺部' },
            { value: '肠道', label: '肠道' }
          ]
        }
      ]
    };
  },
  computed: {
    ...mapGetters(['getAnnotationProgress'])
  },
  created() {
    // 设置页面加载状态
    this.loading = true;
    
    // 获取来自标注页面的数据
    const annotationData = localStorage.getItem('annotations');
    if (annotationData) {
      this.annotationData = JSON.parse(annotationData);
      // 可以根据标注数据预填表单的某些字段
      this.prefilFormFromAnnotations();
    }
    
    // 检查是否有跳过认证的标志
    const skipAuthCheck = localStorage.getItem('skipAuthCheck');
    if (skipAuthCheck) {
      console.log('检测到skipAuthCheck标记，跳过身份验证');
      localStorage.removeItem('skipAuthCheck');
    }
    
    // 检查是否从图像标注页面跳转而来
    const imageId = this.$route.query.imageId;
    if (imageId) {
      console.log('从URL参数获取图像ID:', imageId);
      this.imageId = parseInt(imageId);
      this.fetchImageData();
      
      // 设置会话标记，防止API请求被重定向
      sessionStorage.setItem('isNavigatingAfterSave', 'true');
    } else if (this.getAnnotationProgress.imageId) {
      // 从进度恢复
      this.imageId = this.getAnnotationProgress.imageId;
      this.fetchImageData();
      
      // 如果有保存的表单数据，恢复它
      if (this.getAnnotationProgress.formData) {
        this.formData = { ...this.formData, ...this.getAnnotationProgress.formData };
      }
    }
    
    // 保存当前进度为第2步
    this.saveProgress({
      step: 2,
      imageId: this.imageId,
      formData: this.formData
    });
    
    // 确保用户信息已加载
    this.checkUserAuthentication();
    
    // 确保级联选择器数据正确显示
    this.ensureCascaderDataValid();
  },
  methods: {
    ...mapActions(['saveProgress', 'completeAnnotation']),
    formatDate,
    
    // 获取用于显示的图像URL，支持离线模式
    getImageUrl(path) {
      // 检查是否为离线模式URL
      if (path && path.startsWith('blob:')) {
        return path;
      }
      
      // 检查localStorage中是否有离线图片
      const imageId = this.imageId;
      if (imageId) {
        const offlineImage = localStorage.getItem(`offline_image_${imageId}`);
        if (offlineImage) {
          console.log('表单页面：使用本地存储的离线图片');
          return offlineImage;
        }
      }
      
      // 使用原始URL
      return getImageUrl(path);
    },
    
    // 加载图像数据
    fetchImageData() {
      if (!this.imageId) {
        this.$message.error('未指定图像ID，无法加载数据');
        return;
      }
      
      // 检查是否为测试模式或离线模式
      const isTestMode = this.$route.query.testMode === 'true';
      const offlineMode = localStorage.getItem('offlineMode') === 'true';
      
      if (isTestMode || offlineMode) {
        console.log('使用测试模式或离线模式加载数据');
        
        // 尝试从localStorage获取离线图片
        const offlineImage = localStorage.getItem(`offline_image_${this.imageId}`);
        
        // 创建模拟数据
        this.imageData = {
          id: parseInt(this.imageId),
          original_name: `离线图片_${this.imageId}.jpg`,
          created_at: new Date().toISOString(),
          path: offlineImage || ''
        };
        
        // 如果有之前保存的进度，恢复已填写的表单数据
        const savedProgress = this.getAnnotationProgress;
        if (savedProgress && savedProgress.formData) {
          this.formData = { ...this.formData, ...savedProgress.formData };
          this.$message.info('已恢复之前填写的表单数据');
        }
        
        this.loaded = true;
        return;
      }
      
      // 正常API请求
      this.loading = true;
      api.images.getOne(this.imageId)
        .then(response => {
          this.imageData = response.data;
          this.loading = false;
          this.loaded = true;
          
          // 从后端返回的独立字段中映射数据到表单
          this.mapImageDataToForm(this.imageData);
        })
        .catch(error => {
          console.error('加载图像数据失败', error);
          
          // 如果API失败但是离线模式启用，使用模拟数据
          if (localStorage.getItem('offlineMode') === 'true') {
            console.warn('API失败但启用了离线模式，使用模拟数据');
            
            const offlineImage = localStorage.getItem(`offline_image_${this.imageId}`);
            
            if (offlineImage) {
              this.imageData = {
                id: parseInt(this.imageId),
                original_name: `离线图片_${this.imageId}.jpg`,
                created_at: new Date().toISOString(),
                path: offlineImage
              };
              
              this.$message.warning('API请求失败，使用离线模式显示表单');
              this.loaded = true;
            } else {
              this.error = '无法获取图像数据';
              this.$message.error('加载失败，无法获取图像数据');
            }
          } else {
            this.error = error.response?.data || error.message;
            this.$message.error('加载失败: ' + this.error);
          }
          
          this.loading = false;
        });
    },
    
    // 深度合并表单数据，避免结构不一致问题
    mergeFormData(savedData) {
      const result = { ...this.formData };
      
      // 遍历保存的数据，将其合并到默认表单结构中
      for (const key in savedData) {
        if (Object.prototype.hasOwnProperty.call(savedData, key)) {
          if (typeof savedData[key] === 'object' && savedData[key] !== null && 
              typeof result[key] === 'object' && result[key] !== null) {
            // 递归合并嵌套对象
            result[key] = this.mergeFormData(savedData[key]);
          } else {
            // 直接替换值
            result[key] = savedData[key];
          }
        }
      }
      
      return result;
    },
    
    // 表单提交
    handleSubmit() {
      this.$refs.caseForm.validate(valid => {
        if (!valid) {
          this.$message.warning('表单验证失败，请检查必填项');
          return;
        }
        
        this.saving = true;
        
        // 检查是否为离线模式
        const offlineMode = localStorage.getItem('offlineMode') === 'true';
        
        if (offlineMode) {
          console.log('离线模式：模拟保存表单数据');
          
          // 存储到localStorage
          try {
            localStorage.setItem(`offline_form_${this.imageId}`, JSON.stringify(this.formData));
            
            // 更新进度
            this.completeAnnotation();
            
            this.$message.success('表单已保存（离线模式）');
            setTimeout(() => {
              this.$router.push('/app/dashboard');
            }, 1000);
          } catch (e) {
            console.error('离线保存表单失败', e);
            this.$message.error('保存失败: ' + e.message);
          }
          
          this.saving = false;
          return;
        }
        
        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '提交数据中...',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, 0.7)'
        });
        
        // 添加action参数，指定为提交操作
        const formDataWithAction = {
          ...this.formData,
          action: 'submit', // 指定为提交操作，状态将设为SUBMITTED(已提交)
          timestamp: new Date().toISOString() // 添加当前时间戳，使后端可以使用前端时间
        };
        
        // 添加用户信息，确保后端能正确识别角色
        try {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const user = JSON.parse(userStr);
            // 直接在表单数据中包含用户角色信息
            if (user.role) {
              formDataWithAction.userRole = user.role;
              console.log('添加用户角色到表单数据:', user.role);
            }
            if (user.id) {
              formDataWithAction.userId = user.id;
            }
            if (user.customId) {
              formDataWithAction.userCustomId = user.customId;
            }
          }
        } catch (e) {
          console.error('获取用户信息失败:', e);
        }
        
        console.log('提交表单数据:', formDataWithAction);
        
        // 正常API请求（已改为简单请求，不带自定义头和 _role 参数）
        api.images.saveStructuredFormData(this.imageId, formDataWithAction)
          .then(response => {
            loading.close();
            this.$message.success('表单提交成功');
            // 完成标注流程，清除进度
            this.completeAnnotation();
            // 延迟跳转到首页
            setTimeout(() => {
              this.$router.push('/app/dashboard');
            }, 1000);
          })
          .catch(error => {
            loading.close();
            console.error('提交表单失败', error);
            
            const errorMessage = error.response?.data || error.message || '未知错误';
            this.$message.error('提交失败: ' + errorMessage);
            
            // 如果API失败，使用离线模式
            if (!offlineMode) {
              this.$confirm('提交到服务器失败，是否启用离线模式?', '提交失败', {
                confirmButtonText: '启用离线模式',
                cancelButtonText: '再试一次',
                type: 'warning'
              }).then(() => {
                localStorage.setItem('offlineMode', 'true');
                this.handleSubmit(); // 递归调用，使用离线模式
              }).catch(() => {
                this.$message.info('请稍后重试');
              });
            }
          })
          .finally(() => {
            this.saving = false;
          });
      });
    },
    
    prefilFormFromAnnotations() {
      // 这里可以根据标注数据预填其他表单字段
      // 例如根据标注位置自动推断病变部位等
    },
    
    // 保存表单数据到进度中
    saveFormProgress() {
      this.saveProgress({
        step: 2, // 病例信息填写步骤
        imageId: this.imageId,
        formData: this.formData
      });
    },
    
    // 保存并退出
    saveAndExit() {
      // 保存当前表单数据
      this.saveFormProgress();
      
      // 显示确认对话框
      this.saveDialogVisible = true;
    },
    
    // 处理保留填写进度
    handleSaveProgress() {
      // 保存会话状态，防止401错误导致重定向到登录页面
      sessionStorage.setItem('isNavigatingAfterSave', 'true');
      sessionStorage.setItem('allowFormOperation', 'true');
      
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '保存数据中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      });
      
      try {
        // 添加action参数，指定为保存操作
        const formDataWithAction = {
          ...this.formData,
          action: 'save', // 指定为保存操作，状态将设为REVIEWED(已标注)
          timestamp: new Date().toISOString() // 添加当前时间戳，使后端可以使用前端时间
        };
        
        // 调用API将数据保存到数据库（已改为简单请求，不带自定义头和 _role 参数）
        api.images.saveStructuredFormData(this.imageId, formDataWithAction)
          .then(response => {
            this.$message.success('表单数据已保存到数据库');
            sessionStorage.setItem('navigatingFromForm', 'true');
            try {
              this.$router.push('/app/dashboard');
            } catch (e) {
              console.error('路由导航错误，尝试使用window.location', e);
              window.location.href = '/app/dashboard';
            }
          })
          .catch(async error => {
            console.error('保存表单数据失败:', error);
            this.$message.error('保存失败: ' + (error.response?.data || error.message || '未知错误'));
            // 1. 本地保存表单数据
            try {
              localStorage.setItem(`offline_form_${this.imageId}`, JSON.stringify(this.formData));
              this.$message.warning('已将表单数据保存到本地，稍后可继续填写');
            } catch (e) {
              console.error('本地保存表单失败', e);
            }
            // 2. 允许用户选择是否继续
            try {
              await this.$confirm('保存到服务器失败，是否仅本地保存并返回工作台？', '保存失败', {
                confirmButtonText: '继续',
                cancelButtonText: '留在当前页面',
                type: 'warning'
              });
              // 用户选择继续，跳转
              this.$router.push('/app/dashboard');
            } catch (e) {
              // 用户选择留在当前页面
              this.$message.info('请稍后重试或检查网络');
            }
          })
          .finally(() => {
            loading.close();
          });
      } catch (error) {
        console.error('处理保存进度时出错:', error);
        this.$message.error('保存失败: ' + error.message);
        loading.close();
        
        // 如果出错，也尝试重定向到工作台
        window.location.href = '/app/dashboard';
      }
    },
    
    // 处理丢弃填写进度
    handleDiscardProgress() {
      this.completeAnnotation();
      this.$message.info('已清除填写进度');
      this.$router.push('/cases/new');
    },
    
    // 返回上一步（图像标注页面）
    returnToAnnotation() {
      // 保存当前表单数据
      this.saveFormProgress();
      
      // 返回图像标注页面
      this.$router.push({
        path: '/cases/form',
        query: { imageId: this.imageId }
      });
    },
    
    // 添加用户认证检查方法
    checkUserAuthentication() {
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user) {
        // 尝试从会话存储恢复用户信息
        const preservedUser = sessionStorage.getItem('preservedUser');
        if (preservedUser) {
          console.log('尝试使用保存的用户信息恢复会话');
          localStorage.setItem('user', preservedUser);
          sessionStorage.removeItem('preservedUser');
        }
      }
    },
    
    // 从后端返回的独立字段中映射数据到表单
    mapImageDataToForm(imageData) {
      console.log('原始图像数据:', imageData);
      
      if (!imageData) {
        console.warn('未找到图像数据，无法映射到表单');
        return;
      }
      
      // 处理下划线命名的字段 - 创建兼容对象
      const data = { ...imageData };
      
      // 可能的下划线风格字段映射
      const fieldMappings = {
        'lesion_location': 'lesionLocation',
        'patient_age': 'patientAge',
        'disease_stage': 'diseaseStage',
        'morphological_features': 'morphologicalFeatures',
        'blood_flow': 'bloodFlow',
        'symptom_details': 'symptomDetails',
        'complication_details': 'complicationDetails',
        'diagnosis_category': 'diagnosisCategory', 
        'diagnosis_icd_code': 'diagnosisIcdCode',
        'treatment_priority': 'treatmentPriority',
        'treatment_plan': 'treatmentPlan',
        'recommended_treatment': 'recommendedTreatment',
        'follow_up_schedule': 'followUpSchedule',
        'prognosis_rating': 'prognosisRating',
        'patient_education': 'patientEducation'
      };
      
      // 创建兼容对象，支持两种字段命名风格
      Object.keys(fieldMappings).forEach(key => {
        if (data[key] !== undefined && data[fieldMappings[key]] === undefined) {
          data[fieldMappings[key]] = data[key];
          console.log(`字段映射: ${key} -> ${fieldMappings[key]}`);
        }
      });
      
      // 基础信息映射
      if (data.lesionLocation) {
        // 如果是单个字符串，尝试按路径拆分
        if (typeof data.lesionLocation === 'string') {
          const locationParts = data.lesionLocation.split('/').filter(p => p.trim());
          if (locationParts.length > 0) {
            this.formData.location = locationParts;
            console.log('设置病变部位(字符串分割):', this.formData.location);
          }
        } else if (Array.isArray(data.lesionLocation)) {
          this.formData.location = data.lesionLocation;
          console.log('设置病变部位(数组):', this.formData.location);
        }
      }
      
      // 尝试从lesion_location字段读取(下划线格式)
      if (!this.formData.location || this.formData.location.length === 0) {
        if (imageData.lesion_location) {
          if (typeof imageData.lesion_location === 'string') {
            const locationParts = imageData.lesion_location.split('/').filter(p => p.trim());
            if (locationParts.length > 0) {
              this.formData.location = locationParts;
              console.log('设置病变部位(下划线字段):', this.formData.location);
            }
          }
        }
      }
      
      if (data.patientAge !== null && data.patientAge !== undefined) {
        this.formData.patientAge = parseInt(data.patientAge);
        console.log('设置患者年龄:', this.formData.patientAge);
      }
      
      if (data.diseaseStage) {
        this.formData.stage = data.diseaseStage;
        console.log('设置病程阶段:', this.formData.stage);
      }
      
      // 形态与临床特征映射
      if (data.morphologicalFeatures) {
        const morphFeatures = data.morphologicalFeatures || '';
        // 如果包含冒号，前面部分是形态特征，后面是描述
        const colonIndex = morphFeatures.indexOf(':');
        
        if (colonIndex > -1) {
          const features = morphFeatures.substring(0, colonIndex).split('/');
          this.formData.morphology = features;
          this.formData.morphologyDesc = morphFeatures.substring(colonIndex + 1).trim();
        } else {
          // 如果没有冒号，全部作为形态特征
          this.formData.morphology = morphFeatures.split('/').filter(p => p.trim() !== '');
        }
        
        console.log('设置形态特征:', this.formData.morphology);
        console.log('设置形态描述:', this.formData.morphologyDesc);
      }
      
      if (data.bloodFlow) {
        this.formData.bloodFlow = data.bloodFlow;
        console.log('设置血流信号:', this.formData.bloodFlow);
      }
      
      if (data.symptoms) {
        const symptomsData = data.symptoms || '';
        const colonIndex = symptomsData.indexOf(':');
        
        if (colonIndex > -1) {
          const symptoms = symptomsData.substring(0, colonIndex).split('/');
          this.formData.symptoms = symptoms;
          this.formData.symptomsDesc = symptomsData.substring(colonIndex + 1).trim();
        } else {
          this.formData.symptoms = symptomsData.split('/').filter(s => s.trim() !== '');
        }
        
        console.log('设置症状:', this.formData.symptoms);
      }
      
      if (data.symptomDetails) {
        this.formData.symptomsDesc = data.symptomDetails;
        console.log('设置症状详情:', this.formData.symptomsDesc);
      }
      
      if (data.complications) {
        const complications = data.complications || '';
        this.formData.complications = complications.split('/').filter(c => c.trim() !== '');
        console.log('设置并发症:', this.formData.complications);
      }
      
      if (data.complicationDetails) {
        this.formData.complicationsDesc = data.complicationDetails;
        console.log('设置并发症详情:', this.formData.complicationsDesc);
      }
      
      // 诊断与治疗建议映射
      if (data.diagnosisCategory) {
        this.formData.diagnosis = data.diagnosisCategory;
        console.log('设置诊断结论:', this.formData.diagnosis);
      }
      
      if (data.diagnosisIcdCode) {
        this.formData.diagnosisCode = data.diagnosisIcdCode;
        console.log('设置ICD编码:', this.formData.diagnosisCode);
      }
      
      if (data.treatmentPriority) {
        this.formData.treatmentPriority = data.treatmentPriority;
        console.log('设置治疗优先级:', this.formData.treatmentPriority);
      }
      
      if (data.treatmentPlan) {
        const treatmentPlan = data.treatmentPlan || '';
        this.formData.treatmentPlan = treatmentPlan.split('/').filter(p => p.trim() !== '');
        console.log('设置治疗方案:', this.formData.treatmentPlan);
      }
      
      if (data.recommendedTreatment) {
        this.formData.treatmentDetail = data.recommendedTreatment;
        console.log('设置治疗详情:', this.formData.treatmentDetail);
      }
      
      if (data.contraindications) {
        this.formData.contraindications = data.contraindications;
        console.log('设置禁忌症:', this.formData.contraindications);
      }
      
      // 随访与预后映射
      if (data.followUpSchedule) {
        const followUp = data.followUpSchedule;
        
        // 检查是否是标准选项之一
        const standardOptions = ['1个月', '3个月', '6个月'];
        if (standardOptions.includes(followUp)) {
          this.formData.followUpPeriod = followUp;
        } else {
          this.formData.followUpPeriod = 'custom';
          this.formData.customFollowUp = followUp;
        }
        
        console.log('设置随访周期:', this.formData.followUpPeriod, this.formData.customFollowUp);
      }
      
      if (data.prognosisRating !== null && data.prognosisRating !== undefined) {
        this.formData.prognosisRating = parseInt(data.prognosisRating);
        console.log('设置预后评估:', this.formData.prognosisRating);
      }
      
      if (data.patientEducation) {
        const education = data.patientEducation || '';
        const colonIndex = education.indexOf(':');
        
        if (colonIndex > -1) {
          const eduItems = education.substring(0, colonIndex).split('/');
          this.formData.patientEducation = eduItems;
          this.formData.patientEducationDetail = education.substring(colonIndex + 1).trim();
        } else {
          this.formData.patientEducation = education.split('/').filter(e => e.trim() !== '');
        }
        
        console.log('设置患者教育:', this.formData.patientEducation);
        console.log('设置教育详情:', this.formData.patientEducationDetail);
      }
      
      // 转换字符串数组（如"[]"）为实际数组
      this.convertStringArrays();
      
      this.$message.success('已从数据库加载保存的表单数据');
    },
    
    // 处理字符串形式的数组
    convertStringArrays() {
      const arrayFields = ['morphology', 'symptoms', 'complications', 'treatmentPlan', 'patientEducation', 'location'];
      
      arrayFields.forEach(field => {
        const value = this.formData[field];
        
        // 处理字符串形式的数组 "[item1,item2]"
        if (typeof value === 'string' && value.startsWith('[') && value.endsWith(']')) {
          try {
            const arrayValue = JSON.parse(value);
            if (Array.isArray(arrayValue)) {
              this.formData[field] = arrayValue;
              console.log(`转换字段 ${field} 从字符串到数组:`, arrayValue);
            }
          } catch (e) {
            console.error(`无法解析字符串数组 ${field}:`, value, e);
          }
        }
      });
      
      // 特殊处理location字段，确保级联选择器正确加载
      if (this.formData.location) {
        // 如果字符串形式包含引号和逗号，可能是JSON数组字符串
        if (typeof this.formData.location === 'string' && 
            (this.formData.location.includes('"') || this.formData.location.includes(','))) {
          try {
            // 尝试解析为JSON
            if (this.formData.location.startsWith('[') && this.formData.location.endsWith(']')) {
              const locationArray = JSON.parse(this.formData.location);
              if (Array.isArray(locationArray)) {
                this.formData.location = locationArray;
                console.log('病变部位JSON解析成功:', locationArray);
              }
            } else {
              // 尝试按逗号分隔
              const locationParts = this.formData.location.split(',').map(p => p.trim().replace(/"/g, ''));
              if (locationParts.length > 0) {
                this.formData.location = locationParts;
                console.log('病变部位逗号分隔成功:', locationParts);
              }
            }
          } catch (e) {
            console.error('病变部位解析失败:', e);
          }
        }
      }
      
      // 输出处理后的完整表单数据用于调试
      console.log('处理后的表单数据:', JSON.stringify(this.formData, null, 2));
    },
    
    // 确保级联选择器数据正确显示
    ensureCascaderDataValid() {
      // 添加一个延时，确保在组件完全挂载后执行
      setTimeout(() => {
        console.log('检查级联选择器数据:', this.formData.location);
        
        // 如果location已有值但级联选择器没有正确显示
        if (this.formData.location && this.formData.location.length > 0) {
          const cascader = this.$refs.locationCascader;
          if (cascader && (!cascader.modelValue || cascader.modelValue.length === 0)) {
            // 尝试重新设置值
            this.$nextTick(() => {
              // 深拷贝防止引用问题
              const locationValue = JSON.parse(JSON.stringify(this.formData.location));
              // 先清空再设置
              this.formData.location = [];
              this.$nextTick(() => {
                this.formData.location = locationValue;
                console.log('重新设置级联选择器值:', locationValue);
              });
            });
          }
        }
      }, 500);
    },
    
    // 提交表单
    submitForm() {
      this.$refs.caseForm.validate((valid) => {
        if (valid) {
          // 显示提交确认
          this.$confirm('确认提交此病例信息?', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'info'
          }).then(() => {
            // 执行提交逻辑
            this.handleSubmit();
          }).catch(() => {
            // 用户取消提交
          });
        } else {
          this.$message.error('表单验证失败，请检查填写的信息');
          return false;
        }
      });
    }
  }
};
</script>

<style scoped>
.structured-form-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 4px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-content h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #409EFF;
  padding-bottom: 10px;
  border-bottom: 1px dashed #EBEEF5;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
}

.form-actions button {
  min-width: 120px;
}

/* 在小屏幕上切换为单列 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}
</style> 