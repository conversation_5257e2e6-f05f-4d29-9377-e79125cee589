"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[697],{28697:(e,t,r)=>{r.r(t),r.d(t,{default:()=>C});r(52675),r(89463),r(62010);var a=r(20641),n=r(90033),s={class:"users-container"},i={class:"page-header"},o={class:"header-buttons"},l={class:"filter-bar"},u={class:"search-wrapper"},d={key:0,class:"search-results"},c={class:"dialog-footer"},m={class:"dialog-footer"},f={class:"dialog-footer"},p={class:"dialog-footer"};function h(e,t,r,h,g,b){var D=(0,a.g2)("el-button"),v=(0,a.g2)("el-option"),w=(0,a.g2)("el-select"),k=(0,a.g2)("el-form-item"),y=(0,a.g2)("el-form"),F=(0,a.g2)("el-table-column"),C=(0,a.g2)("el-tag"),U=(0,a.g2)("el-table"),_=(0,a.g2)("el-pagination"),V=(0,a.g2)("el-input"),A=(0,a.g2)("el-divider"),P=(0,a.g2)("el-dialog"),R=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",s,[(0,a.Lk)("div",i,[t[23]||(t[23]=(0,a.Lk)("h2",null,"部门成员",-1)),(0,a.Lk)("div",o,[b.currentUserHasDepartment?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(D,{key:0,type:"primary",onClick:b.handleJoinDepartment},{default:(0,a.k6)((function(){return t[20]||(t[20]=[(0,a.eW)("加入部门")])})),_:1,__:[20]},8,["onClick"])),b.currentUserHasDepartment||!b.isAdmin&&!b.isReviewer?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(D,{key:1,type:"success",onClick:b.handleCreateDepartment},{default:(0,a.k6)((function(){return t[21]||(t[21]=[(0,a.eW)("创建部门")])})),_:1,__:[21]},8,["onClick"])),(0,a.bF)(D,{type:"primary",onClick:b.handleAddUser},{default:(0,a.k6)((function(){return t[22]||(t[22]=[(0,a.eW)("添加成员")])})),_:1,__:[22]},8,["onClick"])])]),(0,a.Lk)("div",l,[(0,a.bF)(y,{inline:!0,class:"filter-form"},{default:(0,a.k6)((function(){return[(0,a.bF)(k,{label:"所属部门"},{default:(0,a.k6)((function(){return[(0,a.bF)(w,{modelValue:g.departmentFilter,"onUpdate:modelValue":t[0]||(t[0]=function(e){return g.departmentFilter=e}),placeholder:"全部部门",clearable:""},{default:(0,a.k6)((function(){return[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(g.departments,(function(e){return(0,a.uX)(),(0,a.Wv)(v,{key:e,label:e,value:e},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),(0,a.bF)(k,{label:"成员角色"},{default:(0,a.k6)((function(){return[(0,a.bF)(w,{modelValue:g.roleFilter,"onUpdate:modelValue":t[1]||(t[1]=function(e){return g.roleFilter=e}),placeholder:"全部角色",clearable:""},{default:(0,a.k6)((function(){return[(0,a.bF)(v,{label:"管理员",value:"ADMIN"}),(0,a.bF)(v,{label:"标注医生",value:"DOCTOR"}),(0,a.bF)(v,{label:"审核医生",value:"REVIEWER"})]})),_:1},8,["modelValue"])]})),_:1}),(0,a.bF)(k,null,{default:(0,a.k6)((function(){return[(0,a.bF)(D,{type:"primary",onClick:b.applyFilters},{default:(0,a.k6)((function(){return t[24]||(t[24]=[(0,a.eW)("筛选")])})),_:1,__:[24]},8,["onClick"]),(0,a.bF)(D,{onClick:b.resetFilters},{default:(0,a.k6)((function(){return t[25]||(t[25]=[(0,a.eW)("重置")])})),_:1,__:[25]},8,["onClick"])]})),_:1})]})),_:1})]),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(U,{data:b.displayUsers,style:{width:"100%"}},{default:(0,a.k6)((function(){return[(0,a.bF)(F,{prop:"name",label:"姓名",width:"120"}),(0,a.bF)(F,{prop:"department",label:"所属部门",width:"150"}),(0,a.bF)(F,{prop:"role",label:"角色"},{default:(0,a.k6)((function(e){return[(0,a.bF)(C,{type:b.getRoleType(e.row.role)},{default:(0,a.k6)((function(){return[(0,a.eW)((0,n.v_)(b.getRoleName(e.row.role)),1)]})),_:2},1032,["type"])]})),_:1}),(0,a.bF)(F,{prop:"hospital",label:"所属医院"})]})),_:1},8,["data"])),[[R,g.loading]]),(0,a.bF)(_,{background:"",layout:"total, sizes, prev, pager, next",total:g.filteredUsers.length,"page-size":g.pageSize,"page-sizes":[10,20,50,100],onCurrentChange:b.handlePageChange,onSizeChange:b.handleSizeChange,class:"pagination"},null,8,["total","page-size","onCurrentChange","onSizeChange"]),(0,a.bF)(P,{title:"添加成员",modelValue:g.dialogVisible,"onUpdate:modelValue":t[4]||(t[4]=function(e){return g.dialogVisible=e}),width:"500px",onClose:b.closeDialog,"close-on-click-modal":!1},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",c,[(0,a.bF)(D,{onClick:t[3]||(t[3]=function(e){return g.dialogVisible=!1})},{default:(0,a.k6)((function(){return t[26]||(t[26]=[(0,a.eW)("取消")])})),_:1,__:[26]}),(0,a.bF)(D,{type:"primary",onClick:b.submitForm},{default:(0,a.k6)((function(){return t[27]||(t[27]=[(0,a.eW)("确 定")])})),_:1,__:[27]},8,["onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.Lk)("div",u,[(0,a.bF)(V,{modelValue:g.searchQuery,"onUpdate:modelValue":t[2]||(t[2]=function(e){return g.searchQuery=e}),placeholder:"搜索用户名、姓名、部门、医院",clearable:"",onInput:b.handleSearch},null,8,["modelValue","onInput"])]),g.searchResults.length>0?((0,a.uX)(),(0,a.CE)("div",d,[(0,a.bF)(U,{data:g.searchResults,style:{width:"100%"},onRowClick:b.handleRowClick,"highlight-current-row":""},{default:(0,a.k6)((function(){return[(0,a.bF)(F,{prop:"name",label:"姓名",width:"120"}),(0,a.bF)(F,{prop:"department",label:"部门",width:"140"}),(0,a.bF)(F,{prop:"hospital",label:"所属医院"})]})),_:1},8,["data","onRowClick"])])):(0,a.Q3)("",!0),g.searchResults.length>0?((0,a.uX)(),(0,a.Wv)(A,{key:1})):(0,a.Q3)("",!0)]})),_:1},8,["modelValue","onClose"]),(0,a.bF)(P,{title:"重置密码",modelValue:g.resetPasswordDialog.visible,"onUpdate:modelValue":t[8]||(t[8]=function(e){return g.resetPasswordDialog.visible=e}),width:"400px",onClose:t[9]||(t[9]=function(e){return g.resetPasswordDialog.visible=!1})},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",m,[(0,a.bF)(D,{onClick:t[7]||(t[7]=function(e){return g.resetPasswordDialog.visible=!1})},{default:(0,a.k6)((function(){return t[28]||(t[28]=[(0,a.eW)("取消")])})),_:1,__:[28]}),(0,a.bF)(D,{type:"primary",onClick:b.submitResetPassword},{default:(0,a.k6)((function(){return t[29]||(t[29]=[(0,a.eW)("确定")])})),_:1,__:[29]},8,["onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.bF)(y,{model:g.resetPasswordDialog.form,rules:g.resetPasswordRules,ref:"resetPasswordForm","label-width":"100px"},{default:(0,a.k6)((function(){return[(0,a.bF)(k,{label:"新密码",prop:"password"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:g.resetPasswordDialog.form.password,"onUpdate:modelValue":t[5]||(t[5]=function(e){return g.resetPasswordDialog.form.password=e}),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]})),_:1}),(0,a.bF)(k,{label:"确认新密码",prop:"confirmPassword"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:g.resetPasswordDialog.form.confirmPassword,"onUpdate:modelValue":t[6]||(t[6]=function(e){return g.resetPasswordDialog.form.confirmPassword=e}),type:"password",placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"]),(0,a.bF)(P,{title:"加入部门",modelValue:g.joinDepartmentDialog.visible,"onUpdate:modelValue":t[13]||(t[13]=function(e){return g.joinDepartmentDialog.visible=e}),width:"500px",onClose:t[14]||(t[14]=function(e){return g.joinDepartmentDialog.visible=!1})},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",f,[(0,a.bF)(D,{onClick:t[12]||(t[12]=function(e){return g.joinDepartmentDialog.visible=!1})},{default:(0,a.k6)((function(){return t[30]||(t[30]=[(0,a.eW)("取消")])})),_:1,__:[30]}),(0,a.bF)(D,{type:"primary",onClick:b.submitJoinDepartment},{default:(0,a.k6)((function(){return[(0,a.eW)((0,n.v_)(g.joinDepartmentDialog.form.teamId?"申请加入":"确定"),1)]})),_:1},8,["onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.bF)(y,{model:g.joinDepartmentDialog.form,"label-width":"80px"},{default:(0,a.k6)((function(){return[(0,a.bF)(k,{label:"部门"},{default:(0,a.k6)((function(){return[(0,a.bF)(w,{modelValue:g.joinDepartmentDialog.form.department,"onUpdate:modelValue":t[10]||(t[10]=function(e){return g.joinDepartmentDialog.form.department=e}),placeholder:"请选择部门",style:{width:"100%"},onChange:b.handleDepartmentChange},{default:(0,a.k6)((function(){return[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(g.departments,(function(e){return(0,a.uX)(),(0,a.Wv)(v,{key:e,label:e,value:e},null,8,["label","value"])})),128))]})),_:1},8,["modelValue","onChange"])]})),_:1}),g.joinDepartmentDialog.form.teamId?((0,a.uX)(),(0,a.Wv)(k,{key:0,label:"申请理由"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:g.joinDepartmentDialog.form.reason,"onUpdate:modelValue":t[11]||(t[11]=function(e){return g.joinDepartmentDialog.form.reason=e}),type:"textarea",placeholder:"请简要说明加入部门的原因（可选）",rows:3},null,8,["modelValue"])]})),_:1})):(0,a.Q3)("",!0)]})),_:1},8,["model"])]})),_:1},8,["modelValue"]),(0,a.bF)(P,{title:"创建部门",modelValue:g.createDepartmentDialog.visible,"onUpdate:modelValue":t[18]||(t[18]=function(e){return g.createDepartmentDialog.visible=e}),width:"500px",onClose:t[19]||(t[19]=function(e){return g.createDepartmentDialog.visible=!1})},{footer:(0,a.k6)((function(){return[(0,a.Lk)("span",p,[(0,a.bF)(D,{onClick:t[17]||(t[17]=function(e){return g.createDepartmentDialog.visible=!1})},{default:(0,a.k6)((function(){return t[31]||(t[31]=[(0,a.eW)("取消")])})),_:1,__:[31]}),(0,a.bF)(D,{type:"primary",onClick:b.submitCreateDepartment},{default:(0,a.k6)((function(){return t[32]||(t[32]=[(0,a.eW)("确定")])})),_:1,__:[32]},8,["onClick"])])]})),default:(0,a.k6)((function(){return[(0,a.bF)(y,{model:g.createDepartmentDialog.form,rules:g.departmentRules,ref:"createDepartmentForm","label-width":"80px"},{default:(0,a.k6)((function(){return[(0,a.bF)(k,{label:"部门名称",prop:"name"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:g.createDepartmentDialog.form.name,"onUpdate:modelValue":t[15]||(t[15]=function(e){return g.createDepartmentDialog.form.name=e}),placeholder:"请输入部门名称"},null,8,["modelValue"])]})),_:1}),(0,a.bF)(k,{label:"描述",prop:"description"},{default:(0,a.k6)((function(){return[(0,a.bF)(V,{modelValue:g.createDepartmentDialog.form.description,"onUpdate:modelValue":t[16]||(t[16]=function(e){return g.createDepartmentDialog.form.description=e}),type:"textarea",placeholder:"请输入部门描述",rows:3},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"])])}var g=r(54119),b=r(59258),D=r(41034),v=(r(16280),r(76918),r(28706),r(2008),r(50113),r(51629),r(23418),r(74423),r(64346),r(62062),r(44114),r(34782),r(23288),r(18111),r(22489),r(20116),r(7588),r(61701),r(79432),r(26099),r(53796),r(17642),r(58004),r(33853),r(45876),r(32475),r(15024),r(31698),r(21699),r(47764),r(42762),r(23500),r(62953),r(76031),r(653)),w=r(40834);const k={name:"Users",data:function(){var e=this,t=function(t,r,a){r!==e.userForm.password?a(new Error("两次输入密码不一致!")):a()},r=function(t,r,a){r!==e.resetPasswordDialog.form.password?a(new Error("两次输入密码不一致!")):a()};return{loading:!1,users:[],filteredUsers:[],departments:[],departmentFilter:"",roleFilter:"",currentPage:1,pageSize:10,dialogVisible:!1,searchMode:!0,searchQuery:"",searchResults:[],userForm:{id:null,name:"",role:"DOCTOR",department:"",hospital:"",email:"",password:"",confirmPassword:"",active:!0,createdAt:null},userRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],department:[{required:!0,message:"请选择或创建部门",trigger:"change"}],hospital:[{required:!0,message:"请输入所属医院",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:t,trigger:"blur"}]},resetPasswordDialog:{visible:!1,userId:null,form:{password:"",confirmPassword:""}},resetPasswordRules:{password:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度至少6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:r,trigger:"blur"}]},selectedUser:null,joinDepartmentDialog:{visible:!1,form:{department:"",teamId:null,reason:""}},createDepartmentDialog:{visible:!1,form:{name:"",description:""}},departmentRules:{name:[{required:!0,message:"请输入部门名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]}}},computed:(0,D.A)((0,D.A)({},(0,w.L8)({currentUser:"getUser"})),{},{currentUserHasDepartment:function(){return this.currentUser&&this.currentUser.department},isAdmin:function(){return this.currentUser&&"ADMIN"===this.currentUser.role},isDoctor:function(){return this.currentUser&&"DOCTOR"===this.currentUser.role},isReviewer:function(){return this.currentUser&&"REVIEWER"===this.currentUser.role},displayUsers:function(){var e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredUsers.slice(e,t)}}),created:function(){if(this.fetchUsers(),!this.currentUser){var e=localStorage.getItem("user");if(e)try{this.$store.commit("setUser",JSON.parse(e))}catch(t){}}},methods:{getRoleType:function(e){var t={ADMIN:"danger",DOCTOR:"primary",REVIEWER:"success"};return t[e]||"info"},getRoleName:function(e){var t={ADMIN:"管理员",DOCTOR:"标注医生",REVIEWER:"审核医生"};return t[e]||"未知"},formatDate:function(e){if(!e)return"未知";var t=new Date(e);return t.toLocaleString("zh-CN")},handlePageChange:function(e){this.currentPage=e},handleSizeChange:function(e){this.pageSize=e,this.currentPage=1},applyFilters:function(){this.filterUsers(),this.currentPage=1},resetFilters:function(){this.departmentFilter="",this.roleFilter="",this.filterUsers(),this.currentPage=1},filterUsers:function(){var e=this;this.departmentFilter||this.roleFilter?Array.isArray(this.users)?this.filteredUsers=this.users.filter((function(t){if(!t)return!1;var r=!e.departmentFilter||t.department&&t.department===e.departmentFilter,a=!e.roleFilter||t.role&&t.role===e.roleFilter;return r&&a})):this.filteredUsers=[]:this.filteredUsers=Array.isArray(this.users)?(0,b.A)(this.users):[]},fetchUsers:function(){var e=this;this.loading=!0,v["default"].users.getAll().then((function(t){t&&t.data?Array.isArray(t.data)?e.users=t.data:t.data.content&&Array.isArray(t.data.content)?e.users=t.data.content:t.data.data&&Array.isArray(t.data.data)?e.users=t.data.data:t.data.items&&Array.isArray(t.data.items)?e.users=t.data.items:t.data.users&&Array.isArray(t.data.users)?e.users=t.data.users:((0,g.A)(t.data),e.users=[]):e.users=[],e.filteredUsers=(0,b.A)(e.users);var r=new Set;e.users.forEach((function(e){e&&e.department&&r.add(e.department)}));var a=["内科","外科","影像科","放射科","急诊科","脑科","管理部"];e.departments=[].concat(a,(0,b.A)(Array.from(r).filter((function(e){return!a.includes(e)}))))}))["catch"]((function(t){e.$message.error("获取用户列表失败: "+(t.message||"未知错误")),e.users=[],e.filteredUsers=[]}))["finally"]((function(){e.loading=!1}))},handleAddUser:function(){this.userForm={id:null,name:"",role:"DOCTOR",department:"",hospital:"",email:"",password:"",confirmPassword:"",active:!0,createdAt:null},this.searchMode=!0,this.searchQuery="",this.searchResults=[],this.dialogVisible=!0},handleEditUser:function(e){this.userForm={id:e.id,name:e.name,role:e.role,department:e.department,hospital:e.hospital,email:e.email,active:!1!==e.active,createdAt:e.createdAt},this.dialogVisible=!0},handleDeleteUser:function(e){var t=this;this.loading=!0,v["default"].users.deleteUser(e.id).then((function(){t.$message.success("成员 ".concat(e.name," 已删除")),t.fetchUsers()}))["catch"]((function(e){t.$message.error("删除失败: "+(e.message||"未知错误"))}))["finally"]((function(){t.loading=!1}))},handleStatusChange:function(e,t){var r=this,a=(0,D.A)((0,D.A)({},e),{},{active:t});v["default"].users.updateUser(e.id,a).then((function(){r.$message.success("已".concat(t?"启用":"禁用","成员: ").concat(e.name))}))["catch"]((function(a){r.$message.error("更新状态失败: "+(a.message||"未知错误")),e.active=!t}))},handleResetPassword:function(e){this.resetPasswordDialog.visible=!0,this.resetPasswordDialog.userId=e.id,this.resetPasswordDialog.form={password:"",confirmPassword:""}},closeDialog:function(){this.dialogVisible=!1,this.$refs.userFormRef&&this.$refs.userFormRef.resetFields()},handleSearch:function(){var e=this;this.searchQuery&&""!==this.searchQuery.trim()?(this.searchTimer&&clearTimeout(this.searchTimer),this.searchTimer=setTimeout((function(){var t=e.searchQuery.toLowerCase().trim();e.searchResults=e.users.filter((function(e){return e.name&&e.name.toLowerCase().includes(t)||e.department&&e.department.toLowerCase().includes(t)||e.hospital&&e.hospital.toLowerCase().includes(t)})).slice(0,10)}),300)):this.searchResults=[]},submitForm:function(){if(this.selectedUser)return this.$message.success("已添加成员: ".concat(this.selectedUser.name)),this.dialogVisible=!1,this.searchQuery="",this.searchResults=[],this.selectedUser=null,void this.fetchUsers();this.searchQuery&&""!==this.searchQuery.trim()?0!==this.searchResults.length?this.$message.warning("请从搜索结果中选择一个成员"):this.$message.warning("未找到匹配的成员"):this.$message.warning("请输入搜索内容")},submitResetPassword:function(){var e=this;this.$refs.resetPasswordForm.validate((function(t){if(!t)return!1;e.loading=!0,v["default"].users.resetPassword(e.resetPasswordDialog.userId,e.resetPasswordDialog.form.password).then((function(){e.$message.success("密码重置成功"),e.resetPasswordDialog.visible=!1}))["catch"]((function(t){e.$message.error("密码重置失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1}))}))},selectUser:function(e){this.userForm={name:e.name,role:e.role,department:e.department,hospital:e.hospital,email:e.email,active:!0,password:"",confirmPassword:"",createdAt:null},this.searchMode=!1},handleRowClick:function(e){this.selectedUser=e,this.$message.info("已选择: ".concat(e.name))},handleJoinDepartment:function(){var e=this;this.loading=!0,v["default"].teams.getAll().then((function(t){var r=t.data||[];e.departments=r.map((function(e){return e.name})),e.joinDepartmentDialog.visible=!0,e.joinDepartmentDialog.form={department:"",teamId:null,reason:""}}))["catch"]((function(t){e.$message.error("获取部门列表失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1}))},handleDepartmentChange:function(e){var t=this;v["default"].teams.getAll().then((function(r){var a=r.data||[],n=a.find((function(t){return t.name===e}));n&&(t.joinDepartmentDialog.form.teamId=n.id)}))["catch"]((function(e){}))},submitJoinDepartment:function(){var e=this;if(this.joinDepartmentDialog.form.department){this.loading=!0;var t=this.joinDepartmentDialog.form.teamId,r=this.joinDepartmentDialog.form.department;t?v["default"].teams.applyToJoinTeam(t,this.joinDepartmentDialog.form.reason||"申请加入部门").then((function(){e.$message.success("已成功提交加入".concat(r,"申请，请等待管理员审核")),e.joinDepartmentDialog.visible=!1,e.fetchUsers()}))["catch"]((function(t){var r;e.$message.error("申请加入部门失败: "+((null===(r=t.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||t.message||"未知错误"))}))["finally"]((function(){e.loading=!1})):v["default"].teams.create({name:r,description:"".concat(r,"部门")}).then((function(t){var r=t.data;return v["default"].teams.joinTeam(r.id,e.currentUser.id)})).then((function(){var t=(0,D.A)((0,D.A)({},e.currentUser),{},{department:r});return e.$store.dispatch("updateUserProfile",t)})).then((function(){e.$message.success("已成功加入".concat(r,"部门")),e.joinDepartmentDialog.visible=!1,e.fetchUsers()}))["catch"]((function(t){e.$message.error("加入部门失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1}))}else this.$message.warning("请选择要加入的部门")},handleCreateDepartment:function(){this.createDepartmentDialog.visible=!0,this.createDepartmentDialog.form={name:"",description:""}},submitCreateDepartment:function(){var e=this;this.$refs.createDepartmentForm.validate((function(t){if(!t)return!1;e.loading=!0;var r={name:e.createDepartmentDialog.form.name,description:e.createDepartmentDialog.form.description||"".concat(e.createDepartmentDialog.form.name,"部门")};v["default"].teams.create(r).then((function(t){var r=t.data;return v["default"].teams.joinTeam(r.id,e.currentUser.id)})).then((function(){var t=(0,D.A)((0,D.A)({},e.currentUser),{},{department:e.createDepartmentDialog.form.name});return e.$store.dispatch("updateUserProfile",t)})).then((function(){e.$message.success("已成功创建并加入".concat(e.createDepartmentDialog.form.name,"部门")),e.createDepartmentDialog.visible=!1,e.departments.includes(e.createDepartmentDialog.form.name)||e.departments.push(e.createDepartmentDialog.form.name),e.fetchUsers()}))["catch"]((function(t){e.$message.error("创建部门失败: "+(t.message||"未知错误"))}))["finally"]((function(){e.loading=!1}))}))}}};var y=r(66262);const F=(0,y.A)(k,[["render",h]]),C=F},59258:(e,t,r)=>{r.d(t,{A:()=>l});r(64346);var a=r(6562);function n(e){if(Array.isArray(e))return(0,a.A)(e)}r(52675),r(89463),r(2259),r(23418),r(26099),r(47764),r(62953);function s(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}var i=r(12635);r(16280),r(76918);function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e){return n(e)||s(e)||(0,i.A)(e)||o()}}}]);