{"version": 3, "file": "js/9.a8cdb7fa.js", "mappings": "8LACOA,MAAM,mC,GACJA,MAAM,e,GAEJA,MAAM,kB,GAJjBC,IAAA,EAa4BD,MAAM,qB,GAblCC,IAAA,EAiB0DD,MAAM,e,GAjBhEC,IAAA,G,GAAAA,IAAA,EAkD4BD,MAAM,qB,GAlDlCC,IAAA,EAsD4DD,MAAM,e,GAtDlEC,IAAA,G,iPACEC,EAAAA,EAAAA,IAmFM,MAnFNC,EAmFM,EAlFJC,EAAAA,EAAAA,IAOM,MAPNC,EAOM,cANJD,EAAAA,EAAAA,IAAiB,UAAb,YAAQ,KACZA,EAAAA,EAAAA,IAIM,MAJNE,EAIM,EAHJC,EAAAA,EAAAA,IAEYC,EAAA,CAFDC,KAAK,UAAWC,QAAOC,EAAAC,Q,CAL1C,SAAAC,EAAAA,EAAAA,KAMU,kBAAgCC,EAAA,KAAAA,EAAA,KAAhCV,EAAAA,EAAAA,IAAgC,KAA7BJ,MAAM,oBAAkB,UANrCe,EAAAA,EAAAA,IAM0C,S,IAN1CC,EAAA,EAAAC,GAAA,K,oBAWIV,EAAAA,EAAAA,IAwEUW,EAAA,CAnFdC,WAWsBC,EAAAC,UAXtB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAWsBF,EAAAC,UAASC,CAAA,I,CAX/B,SAAAT,EAAAA,EAAAA,KAYM,iBAmCc,EAnCdN,EAAAA,EAAAA,IAmCcgB,EAAA,CAnCDC,MAAM,QAAQC,KAAK,W,CAZtC,SAAAZ,EAAAA,EAAAA,KAY+C,iBAEtB,CADNO,EAAAM,UAAO,WAAlBxB,EAAAA,EAAAA,IAEM,MAFNyB,EAEM,EADJpB,EAAAA,EAAAA,IAAkCqB,EAAA,CAApBC,KAAM,EAAGC,SAAA,QAGsB,IAA/BV,EAAAW,oBAAoBC,SAAM,WAA1C9B,EAAAA,EAAAA,IAEM,MAFN+B,EAEM,EADJ1B,EAAAA,EAAAA,IAAmC2B,EAAA,CAAzBC,YAAY,kBAAU,WAGlCjC,EAAAA,EAAAA,IAyBM,MA9CdkC,EAAA,EAsBU7B,EAAAA,EAAAA,IAuBW8B,EAAA,CAvBAC,KAAMlB,EAAAW,oBAAqBQ,OAAA,GAAOC,MAAA,gB,CAtBvD,SAAA3B,EAAAA,EAAAA,KAuBY,iBAAsD,EAAtDN,EAAAA,EAAAA,IAAsDkC,EAAA,CAArCC,KAAK,KAAKlB,MAAM,OAAOmB,MAAM,SAC9CpC,EAAAA,EAAAA,IAIkBkC,EAAA,CAJDjB,MAAM,MAAMmB,MAAM,O,CACtBC,SAAO/B,EAAAA,EAAAA,KAChB,SAAmCgC,GADZ,QAzBvC9B,EAAAA,EAAAA,KAAA+B,EAAAA,EAAAA,IA0BmBnC,EAAAoC,YAAYF,EAAMG,IAAIC,SAAM,G,IA1B/CjC,EAAA,KA6BYT,EAAAA,EAAAA,IAAoEkC,EAAA,CAAnDC,KAAK,SAASlB,MAAM,OAAO,8BAC5CjB,EAAAA,EAAAA,IAIkBkC,EAAA,CAJDC,KAAK,YAAYlB,MAAM,OAAOmB,MAAM,O,CACxCC,SAAO/B,EAAAA,EAAAA,KAChB,SAAqCgC,GADd,QA/BvC9B,EAAAA,EAAAA,KAAA+B,EAAAA,EAAAA,IAgCmBnC,EAAAuC,WAAWL,EAAMG,IAAIG,YAAS,G,IAhCjDnC,EAAA,KAmCYT,EAAAA,EAAAA,IASkBkC,EAAA,CATDjB,MAAM,KAAKmB,MAAM,O,CACrBC,SAAO/B,EAAAA,EAAAA,KAChB,SAEYgC,GAHW,QACvBtC,EAAAA,EAAAA,IAEYC,EAAA,CAFDC,KAAK,UAAU2C,KAAK,QAAS1C,QAAK,SAAAY,GAAA,OAAEX,EAAA0C,cAAcR,EAAMG,IAAG,G,CArCtF,SAAAnC,EAAAA,EAAAA,KAqCyF,kBAEzEC,EAAA,KAAAA,EAAA,KAvChBC,EAAAA,EAAAA,IAqCyF,S,IArCzFC,EAAA,EAAAC,GAAA,K,mBAwCgBV,EAAAA,EAAAA,IAEYC,EAAA,CAFDC,KAAK,SAAS2C,KAAK,QAAS1C,QAAK,SAAAY,GAAA,OAAEX,EAAA2C,aAAaT,EAAMG,IAAG,G,CAxCpF,SAAAnC,EAAAA,EAAAA,KAwCuF,kBAEvEC,EAAA,KAAAA,EAAA,KA1ChBC,EAAAA,EAAAA,IAwCuF,S,IAxCvFC,EAAA,EAAAC,GAAA,K,sBAAAD,EAAA,I,IAAAA,EAAA,G,mBAAAA,EAAA,KAiDMT,EAAAA,EAAAA,IAiCcgB,EAAA,CAjCDC,MAAM,QAAQC,KAAK,a,CAjDtC,SAAAZ,EAAAA,EAAAA,KAiEY,iBAKI,CApBGO,EAAAM,UAAO,WAAlBxB,EAAAA,EAAAA,IAEM,MAFNqD,EAEM,EADJhD,EAAAA,EAAAA,IAAkCqB,EAAA,CAApBC,KAAM,EAAGC,SAAA,QAGwB,IAAjCV,EAAAoC,sBAAsBxB,SAAM,WAA5C9B,EAAAA,EAAAA,IAEM,MAFNuD,EAEM,EADJlD,EAAAA,EAAAA,IAAmC2B,EAAA,CAAzBC,YAAY,kBAAU,WAGlCjC,EAAAA,EAAAA,IAuBM,MAjFdwD,EAAA,EA2DUnD,EAAAA,EAAAA,IAqBW8B,EAAA,CArBAC,KAAMlB,EAAAoC,sBAAuBjB,OAAA,GAAOC,MAAA,gB,CA3DzD,SAAA3B,EAAAA,EAAAA,KA4DY,iBAAsD,EAAtDN,EAAAA,EAAAA,IAAsDkC,EAAA,CAArCC,KAAK,KAAKlB,MAAM,OAAOmB,MAAM,SAC9CpC,EAAAA,EAAAA,IAIkBkC,EAAA,CAJDjB,MAAM,MAAMmB,MAAM,O,CACtBC,SAAO/B,EAAAA,EAAAA,KAChB,SAAmCgC,GADZ,QA9DvC9B,EAAAA,EAAAA,KAAA+B,EAAAA,EAAAA,IA+DmBnC,EAAAoC,YAAYF,EAAMG,IAAIC,SAAM,G,IA/D/CjC,EAAA,KAkEYT,EAAAA,EAAAA,IAAoEkC,EAAA,CAAnDC,KAAK,SAASlB,MAAM,OAAO,8BAC5CjB,EAAAA,EAAAA,IAMkBkC,EAAA,CANDC,KAAK,SAASlB,MAAM,KAAKmB,MAAM,O,CACnCC,SAAO/B,EAAAA,EAAAA,KAChB,SAESgC,GAHc,QACvBtC,EAAAA,EAAAA,IAESoD,EAAA,CAFAlD,KAA2B,aAArBoC,EAAMG,IAAIY,OAAwB,UAAY,U,CArE7E,SAAA/C,EAAAA,EAAAA,KAsEkB,iBAAqD,EAtEvEE,EAAAA,EAAAA,KAAA+B,EAAAA,EAAAA,IAsE0C,aAArBD,EAAMG,IAAIY,OAAwB,MAAQ,OAA1B,G,IAtErC5C,EAAA,G,mBAAAA,EAAA,KA0EYT,EAAAA,EAAAA,IAAmEkC,EAAA,CAAlDC,KAAK,UAAUlB,MAAM,KAAK,8BAC3CjB,EAAAA,EAAAA,IAIkBkC,EAAA,CAJDC,KAAK,cAAclB,MAAM,OAAOmB,MAAM,O,CAC1CC,SAAO/B,EAAAA,EAAAA,KAChB,SAAuCgC,GADhB,QA5EvC9B,EAAAA,EAAAA,KAAA+B,EAAAA,EAAAA,IA6EmBnC,EAAAuC,WAAWL,EAAMG,IAAIa,cAAW,G,IA7EnD7C,EAAA,I,IAAAA,EAAA,G,mBAAAA,EAAA,I,IAAAA,EAAA,G,8IA0FA,SACES,KAAM,uBACNa,KAAI,WACF,MAAO,CACLjB,UAAW,UACXK,SAAS,EACTK,oBAAqB,GACrByB,sBAAuB,GACvBM,MAAO,CAAC,EAEZ,EACAC,QAAO,WACLC,KAAKC,mBACP,EACAC,QAAS,CAEPD,kBAAiB,WAAG,IAAAE,EAAA,KAClBH,KAAKtC,SAAU,EAGf0C,EAAAA,WAAIN,MAAMO,iCACPC,MAAK,SAAAC,GAIJ,OAHAJ,EAAKpC,oBAAsBwC,EAASjC,MAAQ,GAGrC8B,EAAAA,WAAII,SAASC,0BACtB,IACCH,MAAK,SAAAC,GAIJ,OAHAJ,EAAKX,sBAAwBe,EAASjC,MAAQ,GAGvC6B,EAAKO,YACd,IAAC,UACM,SAAAC,GACLC,QAAQD,MAAM,YAAaA,GAC3BR,EAAKU,SAASF,MAAM,WACtB,IAAC,YACQ,WACPR,EAAKzC,SAAU,CACjB,GACJ,EAGAgD,WAAU,WAAG,IAAAI,EAAA,KAEX,OAAOV,EAAAA,WAAIN,MAAMiB,SACdT,MAAK,SAAAC,GACJ,GAAIA,GAAYA,EAASjC,KAAM,CAC7B,IAAM0C,EAAWC,MAAMC,QAAQX,EAASjC,MAAQiC,EAASjC,KAAO,GAGhE0C,EAASG,SAAQ,SAAAC,GACfN,EAAKO,KAAKP,EAAKhB,MAAOsB,EAAKE,GAAIF,EACjC,IAEAR,QAAQW,IAAI,cAAeC,OAAOC,KAAKX,EAAKhB,OAAO9B,OACrD,CACF,IAAC,UACM,SAAA2C,GACLC,QAAQD,MAAM,cAAeA,EAC/B,GACJ,EAGA5B,YAAW,SAACE,GAAQ,IAAAyC,EAAA,KAElB,GAAI1B,KAAKF,MAAMb,IAAWe,KAAKF,MAAMb,GAAQxB,KAC3C,OAAOuC,KAAKF,MAAMb,GAAQxB,KAI5B,IAAMkE,EAAc,CAClB,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,GAAI,OAIN,OAAIA,EAAY1C,GACP0C,EAAY1C,IAIrBmB,EAAAA,WAAIN,MAAM8B,QAAQ3C,GACfqB,MAAK,SAAAC,GACAA,GAAYA,EAASjC,MACvBoD,EAAKL,KAAKK,EAAK5B,MAAOb,EAAQsB,EAASjC,KAE3C,IAAC,UACM,SAAAqC,GACLC,QAAQD,MAAM,QAADkB,OAAS5C,EAAM,UAAU0B,EACxC,IAGK,MAAPkB,OAAa5C,GACf,EAGAC,WAAU,SAAC4C,GACT,IAAKA,EAAY,MAAO,GAExB,IAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,eAAe,QAAS,CAClCC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,UACRC,OAAQ,WAEZ,EAGAlD,cAAa,SAACmD,GAAa,IAAAC,EAAA,KACzBzC,KAAK0C,SAAS,kBAAmB,KAAM,CACrCC,kBAAmB,KACnBC,iBAAkB,KAClBnG,KAAM,SACL6D,MAAK,WACNmC,EAAKI,mBAAmBL,EAAYvD,QAAQ,EAC9C,IAAE,UAAO,WACP,GAEJ,EAGAK,aAAY,SAACkD,GAAa,IAAAM,EAAA,KACxB9C,KAAK+C,QAAQ,UAAW,OAAQ,CAC9BJ,kBAAmB,KACnBC,iBAAkB,KAClBI,UAAW,WACXC,iBAAkB,eACjB3C,MAAK,SAAA4C,GAAe,IAAZC,EAAID,EAAJC,MACTL,EAAKD,mBAAmBL,EAAYvD,QAAQ,EAAOkE,EACrD,IAAE,UAAO,WACP,GAEJ,EAGAN,mBAAkB,SAAC5D,EAAQmE,GAAwB,IAAAC,EAAA,KAAdC,EAAMC,UAAAvF,OAAA,QAAAwF,IAAAD,UAAA,GAAAA,UAAA,GAAI,GAC7CvD,KAAKtC,SAAU,EAGf,IAAM8E,EAAcxC,KAAKjC,oBAAoB0F,MAAK,SAAAC,GAAE,OAAKA,EAAIzE,SAAWA,CAAM,IAE9E,IAAKuD,EAGH,OAFAxC,KAAKa,SAASF,MAAM,oBACpBX,KAAKtC,SAAU,GAIjB,IAAMY,EAAO,CACXsB,OAAQwD,EAAW,WAAa,WAChCE,QAASA,GAAW,IAItBlD,EAAAA,WAAIN,MAAM6D,2BAA2BnB,EAAYlB,GAAIhD,GAClDgC,MAAK,WACJ+C,EAAKxC,SAAS+C,QAAQR,EAAW,QAAU,SAC3CC,EAAKpD,mBACP,IAAC,UACM,SAAAU,GACLC,QAAQD,MAAM,UAAWA,GACrBA,EAAMJ,UAAYI,EAAMJ,SAASjC,MAAQqC,EAAMJ,SAASjC,KAAKuF,QAC/DR,EAAKxC,SAASF,MAAMA,EAAMJ,SAASjC,KAAKuF,SAExCR,EAAKxC,SAASF,MAAM,SAExB,IAAC,YACQ,WACP0C,EAAK3F,SAAU,CACjB,GACJ,EACAd,OAAM,WAEJoD,KAAK8D,QAAQC,KAAK,aACpB,I,eC5QJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/admin/ReviewerApplications.vue", "webpack://medical-annotation-frontend/./src/views/admin/ReviewerApplications.vue?e17f"], "sourcesContent": ["<template>\r\n  <div class=\"reviewer-applications-container\">\r\n    <div class=\"page-header\">\r\n      <h1>审核医生申请管理</h1>\r\n      <div class=\"header-actions\">\r\n        <el-button type=\"primary\" @click=\"goBack\">\r\n          <i class=\"bi bi-arrow-left\"></i> 返回\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n    \r\n    <el-tabs v-model=\"activeTab\">\r\n      <el-tab-pane label=\"待处理申请\" name=\"pending\">\r\n        <div v-if=\"loading\" class=\"loading-container\">\r\n          <el-skeleton :rows=\"5\" animated />\r\n        </div>\r\n        \r\n        <div v-else-if=\"pendingApplications.length === 0\" class=\"empty-state\">\r\n          <el-empty description=\"暂无待处理的申请\" />\r\n        </div>\r\n        \r\n        <div v-else>\r\n          <el-table :data=\"pendingApplications\" border style=\"width: 100%\">\r\n            <el-table-column prop=\"id\" label=\"申请ID\" width=\"100\" />\r\n            <el-table-column label=\"申请人\" width=\"150\">\r\n              <template #default=\"scope\">\r\n                {{ getUserName(scope.row.userId) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"reason\" label=\"申请理由\" show-overflow-tooltip />\r\n            <el-table-column prop=\"createdAt\" label=\"申请时间\" width=\"180\">\r\n              <template #default=\"scope\">\r\n                {{ formatDate(scope.row.createdAt) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"200\">\r\n              <template #default=\"scope\">\r\n                <el-button type=\"success\" size=\"small\" @click=\"handleApprove(scope.row)\">\r\n                  批准\r\n                </el-button>\r\n                <el-button type=\"danger\" size=\"small\" @click=\"handleReject(scope.row)\">\r\n                  拒绝\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </el-tab-pane>\r\n      \r\n      <el-tab-pane label=\"已处理申请\" name=\"processed\">\r\n        <div v-if=\"loading\" class=\"loading-container\">\r\n          <el-skeleton :rows=\"5\" animated />\r\n        </div>\r\n        \r\n        <div v-else-if=\"processedApplications.length === 0\" class=\"empty-state\">\r\n          <el-empty description=\"暂无已处理的申请\" />\r\n        </div>\r\n        \r\n        <div v-else>\r\n          <el-table :data=\"processedApplications\" border style=\"width: 100%\">\r\n            <el-table-column prop=\"id\" label=\"申请ID\" width=\"100\" />\r\n            <el-table-column label=\"申请人\" width=\"150\">\r\n              <template #default=\"scope\">\r\n                {{ getUserName(scope.row.userId) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"reason\" label=\"申请理由\" show-overflow-tooltip />\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"120\">\r\n              <template #default=\"scope\">\r\n                <el-tag :type=\"scope.row.status === 'APPROVED' ? 'success' : 'danger'\">\r\n                  {{ scope.row.status === 'APPROVED' ? '已批准' : '已拒绝' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remarks\" label=\"备注\" show-overflow-tooltip />\r\n            <el-table-column prop=\"processedAt\" label=\"处理时间\" width=\"180\">\r\n              <template #default=\"scope\">\r\n                {{ formatDate(scope.row.processedAt) }}\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/utils/api'\r\n\r\nexport default {\r\n  name: 'ReviewerApplications',\r\n  data() {\r\n    return {\r\n      activeTab: 'pending',\r\n      loading: false,\r\n      pendingApplications: [],\r\n      processedApplications: [],\r\n      users: {}\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchApplications();\r\n  },\r\n  methods: {\r\n    // 获取申请列表\r\n    fetchApplications() {\r\n      this.loading = true;\r\n      \r\n      // 获取待处理申请\r\n      api.users.getPendingReviewerApplications()\r\n        .then(response => {\r\n          this.pendingApplications = response.data || [];\r\n          \r\n          // 获取已处理申请\r\n          return api.reviewer.getProcessedApplications();\r\n        })\r\n        .then(response => {\r\n          this.processedApplications = response.data || [];\r\n          \r\n          // 获取所有用户信息\r\n          return this.fetchUsers();\r\n        })\r\n        .catch(error => {\r\n          console.error('获取申请列表失败:', error);\r\n          this.$message.error('获取申请列表失败');\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    \r\n    // 获取用户信息\r\n    fetchUsers() {\r\n      // 获取所有用户信息\r\n      return api.users.getAll()\r\n        .then(response => {\r\n          if (response && response.data) {\r\n            const allUsers = Array.isArray(response.data) ? response.data : [];\r\n            \r\n            // 将用户数组转换为以ID为键的对象\r\n            allUsers.forEach(user => {\r\n              this.$set(this.users, user.id, user);\r\n            });\r\n            \r\n            console.log('成功获取所有用户信息:', Object.keys(this.users).length);\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error('获取所有用户信息失败:', error);\r\n        });\r\n    },\r\n    \r\n    // 获取用户名\r\n    getUserName(userId) {\r\n      // 检查是否已经加载了用户信息\r\n      if (this.users[userId] && this.users[userId].name) {\r\n        return this.users[userId].name;\r\n      }\r\n      \r\n      // 常用用户ID的备用名称映射\r\n      const backupNames = {\r\n        1: '管理员',\r\n        2: '张医生',\r\n        3: '李审核',\r\n        4: '王医生',\r\n        5: '赵医生',\r\n        6: '钱医生',\r\n        7: '孙医生',\r\n        8: '周医生',\r\n        9: '吴医生',\r\n        10: '郑医生'\r\n      };\r\n      \r\n      // 如果有备用名称，使用备用名称\r\n      if (backupNames[userId]) {\r\n        return backupNames[userId];\r\n      }\r\n      \r\n      // 如果没有加载用户信息，尝试单独加载\r\n      api.users.getUser(userId)\r\n        .then(response => {\r\n          if (response && response.data) {\r\n            this.$set(this.users, userId, response.data);\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(`获取用户 ${userId} 信息失败:`, error);\r\n        });\r\n      \r\n      // 返回临时显示内容\r\n      return `用户 ${userId}`;\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return '';\r\n      \r\n      const date = new Date(dateString);\r\n      return date.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      });\r\n    },\r\n    \r\n    // 处理批准申请\r\n    handleApprove(application) {\r\n      this.$confirm('确定批准该用户成为审核医生吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'info'\r\n      }).then(() => {\r\n        this.processApplication(application.userId, true);\r\n      }).catch(() => {\r\n        // 用户取消，不做处理\r\n      });\r\n    },\r\n    \r\n    // 处理拒绝申请\r\n    handleReject(application) {\r\n      this.$prompt('请输入拒绝理由', '拒绝申请', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputType: 'textarea',\r\n        inputPlaceholder: '请输入拒绝理由...'\r\n      }).then(({ value }) => {\r\n        this.processApplication(application.userId, false, value);\r\n      }).catch(() => {\r\n        // 用户取消，不做处理\r\n      });\r\n    },\r\n    \r\n    // 处理申请\r\n    processApplication(userId, approved, remarks = '') {\r\n      this.loading = true;\r\n      \r\n      // 根据用户ID查找对应的申请记录\r\n      const application = this.pendingApplications.find(app => app.userId === userId);\r\n      \r\n      if (!application) {\r\n        this.$message.error('未找到该用户的申请记录');\r\n        this.loading = false;\r\n        return;\r\n      }\r\n      \r\n      const data = {\r\n        status: approved ? 'APPROVED' : 'REJECTED',\r\n        remarks: remarks || ''\r\n      };\r\n      \r\n      // 使用申请ID而不是用户ID\r\n      api.users.processReviewerApplication(application.id, data)\r\n        .then(() => {\r\n          this.$message.success(approved ? '已批准申请' : '已拒绝申请');\r\n          this.fetchApplications();\r\n        })\r\n        .catch(error => {\r\n          console.error('处理申请失败:', error);\r\n          if (error.response && error.response.data && error.response.data.message) {\r\n            this.$message.error(error.response.data.message);\r\n          } else {\r\n            this.$message.error('处理申请失败');\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    goBack() {\r\n      // 返回部门成员页面\r\n      this.$router.push('/app/users');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.reviewer-applications-container {\r\n  padding: 20px;\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.loading-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n.empty-state {\r\n  padding: 40px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style> ", "import { render } from \"./ReviewerApplications.vue?vue&type=template&id=e3a894ca&scoped=true\"\nimport script from \"./ReviewerApplications.vue?vue&type=script&lang=js\"\nexport * from \"./ReviewerApplications.vue?vue&type=script&lang=js\"\n\nimport \"./ReviewerApplications.vue?vue&type=style&index=0&id=e3a894ca&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-e3a894ca\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_button", "type", "onClick", "$options", "goBack", "_withCtx", "_cache", "_createTextVNode", "_", "__", "_component_el_tabs", "modelValue", "$data", "activeTab", "$event", "_component_el_tab_pane", "label", "name", "loading", "_hoisted_4", "_component_el_skeleton", "rows", "animated", "pendingApplications", "length", "_hoisted_5", "_component_el_empty", "description", "_hoisted_6", "_component_el_table", "data", "border", "style", "_component_el_table_column", "prop", "width", "default", "scope", "_toDisplayString", "getUserName", "row", "userId", "formatDate", "createdAt", "size", "handleApprove", "handleReject", "_hoisted_7", "processedApplications", "_hoisted_8", "_hoisted_9", "_component_el_tag", "status", "processedAt", "users", "created", "this", "fetchApplications", "methods", "_this", "api", "getPendingReviewerApplications", "then", "response", "reviewer", "getProcessedApplications", "fetchUsers", "error", "console", "$message", "_this2", "getAll", "allUsers", "Array", "isArray", "for<PERSON>ach", "user", "$set", "id", "log", "Object", "keys", "_this3", "<PERSON><PERSON><PERSON><PERSON>", "getUser", "concat", "dateString", "date", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "application", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "processApplication", "_this5", "$prompt", "inputType", "inputPlaceholder", "_ref", "value", "approved", "_this6", "remarks", "arguments", "undefined", "find", "app", "processReviewerApplication", "success", "message", "$router", "push", "__exports__", "render"], "sourceRoot": ""}