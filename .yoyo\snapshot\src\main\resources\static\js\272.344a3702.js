"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[272],{44272:(e,a,l)=>{l.r(a),l.d(a,{default:()=>W});l(62010);var r=l(20641),s=l(90033),n=l(53751),t={class:"container login-container"},o={class:"card"},d={class:"card-body p-4"},i={key:0,class:"alert alert-danger"},u={key:1,class:"alert alert-success"},c={class:"mb-3"},p=["disabled"],m={class:"mb-3"},b=["disabled"],f={class:"mb-3"},v=["disabled"],k={class:"mb-3"},L=["disabled"],g={class:"mb-3"},h=["disabled"],w={class:"mb-3"},y=["disabled"],R={class:"d-grid gap-2"},_=["disabled"],x={key:0,class:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"},K={class:"mt-3 text-center"},C={class:"card-footer text-center"};function J(e,a,l,J,P,U){var V=(0,r.g2)("router-link");return(0,r.uX)(),(0,r.CE)("div",t,[(0,r.Lk)("div",o,[a[16]||(a[16]=(0,r.Lk)("div",{class:"card-header"},[(0,r.Lk)("h3",{class:"mb-0"},"血管瘤辅助系统 - 注册")],-1)),(0,r.Lk)("div",d,[J.error?((0,r.uX)(),(0,r.CE)("div",i,(0,s.v_)(J.error),1)):(0,r.Q3)("",!0),J.success?((0,r.uX)(),(0,r.CE)("div",u,(0,s.v_)(J.success),1)):(0,r.Q3)("",!0),(0,r.Lk)("form",{onSubmit:a[6]||(a[6]=(0,n.D$)((function(){return J.handleRegister&&J.handleRegister.apply(J,arguments)}),["prevent"]))},[(0,r.Lk)("div",c,[a[7]||(a[7]=(0,r.Lk)("label",{for:"name",class:"form-label"},"姓名",-1)),(0,r.bo)((0,r.Lk)("input",{type:"text",class:"form-control",id:"name","onUpdate:modelValue":a[0]||(a[0]=function(e){return J.name=e}),required:"",disabled:J.loading},null,8,p),[[n.Jo,J.name]])]),(0,r.Lk)("div",m,[a[8]||(a[8]=(0,r.Lk)("label",{for:"email",class:"form-label"},"邮箱",-1)),(0,r.bo)((0,r.Lk)("input",{type:"email",class:"form-control",id:"email","onUpdate:modelValue":a[1]||(a[1]=function(e){return J.email=e}),required:"",disabled:J.loading},null,8,b),[[n.Jo,J.email]])]),(0,r.Lk)("div",f,[a[9]||(a[9]=(0,r.Lk)("label",{for:"password",class:"form-label"},"密码",-1)),(0,r.bo)((0,r.Lk)("input",{type:"password",class:"form-control",id:"password","onUpdate:modelValue":a[2]||(a[2]=function(e){return J.password=e}),required:"",disabled:J.loading},null,8,v),[[n.Jo,J.password]])]),(0,r.Lk)("div",k,[a[10]||(a[10]=(0,r.Lk)("label",{for:"confirmPassword",class:"form-label"},"确认密码",-1)),(0,r.bo)((0,r.Lk)("input",{type:"password",class:"form-control",id:"confirmPassword","onUpdate:modelValue":a[3]||(a[3]=function(e){return J.confirmPassword=e}),required:"",disabled:J.loading},null,8,L),[[n.Jo,J.confirmPassword]])]),(0,r.Lk)("div",g,[a[11]||(a[11]=(0,r.Lk)("label",{for:"hospital",class:"form-label"},"医院",-1)),(0,r.bo)((0,r.Lk)("input",{type:"text",class:"form-control",id:"hospital","onUpdate:modelValue":a[4]||(a[4]=function(e){return J.hospital=e}),required:"",disabled:J.loading},null,8,h),[[n.Jo,J.hospital]])]),(0,r.Lk)("div",w,[a[12]||(a[12]=(0,r.Lk)("label",{for:"department",class:"form-label"},"科室",-1)),(0,r.bo)((0,r.Lk)("input",{type:"text",class:"form-control",id:"department","onUpdate:modelValue":a[5]||(a[5]=function(e){return J.department=e}),disabled:J.loading},null,8,y),[[n.Jo,J.department]])]),(0,r.Lk)("div",R,[(0,r.Lk)("button",{type:"submit",class:"btn btn-primary",disabled:J.loading},[J.loading?((0,r.uX)(),(0,r.CE)("span",x)):(0,r.Q3)("",!0),a[13]||(a[13]=(0,r.eW)(" 注册 "))],8,_)])],32),(0,r.Lk)("div",K,[(0,r.bF)(V,{to:"/login",class:"text-decoration-none"},{default:(0,r.k6)((function(){return a[14]||(a[14]=[(0,r.eW)("已有账号？点击登录")])})),_:1,__:[14]})])]),(0,r.Lk)("div",C,[(0,r.bF)(V,{to:"/",class:"btn btn-link"},{default:(0,r.k6)((function(){return a[15]||(a[15]=[(0,r.eW)("返回首页")])})),_:1,__:[15]})])])])}var P=l(14048),U=l(30388),V=(l(44114),l(76031),l(50953)),q=l(40834),A=l(75220);const E={name:"Register",setup:function(){var e=(0,q.Pj)(),a=(0,A.rd)(),l=(0,V.KR)(""),r=(0,V.KR)(""),s=(0,V.KR)(""),n=(0,V.KR)(""),t=(0,V.KR)(""),o=(0,V.KR)(""),d=(0,V.KR)(!1),i=(0,V.KR)(""),u=(0,V.KR)(""),c=function(){var c=(0,U.A)((0,P.A)().mark((function c(){return(0,P.A)().wrap((function(c){while(1)switch(c.prev=c.next){case 0:if(s.value===n.value){c.next=3;break}return i.value="两次输入的密码不一致",c.abrupt("return");case 3:return d.value=!0,i.value="",u.value="",c.prev=6,c.next=9,e.dispatch("register",{name:l.value,email:r.value,password:s.value,hospital:t.value,department:o.value,role:"DOCTOR"});case 9:u.value="注册成功，请登录",l.value="",r.value="",s.value="",n.value="",t.value="",o.value="",setTimeout((function(){a.push("/login")}),3e3),c.next=23;break;case 19:c.prev=19,c.t0=c["catch"](6),i.value="string"===typeof c.t0?c.t0:"注册失败，请检查填写信息";case 23:return c.prev=23,d.value=!1,c.finish(23);case 26:case"end":return c.stop()}}),c,null,[[6,19,23,26]])})));return function(){return c.apply(this,arguments)}}();return{name:l,email:r,password:s,confirmPassword:n,hospital:t,department:o,loading:d,error:i,success:u,handleRegister:c}}};var X=l(66262);const Q=(0,X.A)(E,[["render",J],["__scopeId","data-v-3692b039"]]),W=Q}}]);