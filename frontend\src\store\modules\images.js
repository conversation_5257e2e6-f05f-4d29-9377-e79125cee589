import axios from 'axios'
import { API_URL } from '../../config/api.config'
import api from '@/utils/api'
import { asyncActionHandler } from '@/utils/storeHelpers'

// 添加认证头
const getAuthHeader = () => {
  const user = JSON.parse(localStorage.getItem('user'))
  return user ? { Authorization: `Basic ${btoa(`${user.email}:${user.password}`)}` } : {}
}

const state = {
  images: [],
  currentImage: null,
  loading: false,
  error: null
}

const getters = {
  getAllImages: state => state.images,
  getCurrentImage: state => state.currentImage,
  isImagesLoading: state => state.loading,
  getImagesError: state => state.error
}

const mutations = {
  setImages(state, images) {
    state.images = images
  },
  setCurrentImage(state, image) {
    state.currentImage = image
  },
  setLoading(state, status) {
    state.loading = status
  },
  setError(state, error) {
    state.error = error
  },
  addImage(state, image) {
    state.images.unshift(image)
  },
  updateImage(state, updatedImage) {
    const index = state.images.findIndex(img => img.id === updatedImage.id)
    if (index !== -1) {
      state.images.splice(index, 1, updatedImage)
    }
    if (state.currentImage && state.currentImage.id === updatedImage.id) {
      state.currentImage = updatedImage
    }
  },
  removeImage(state, imageId) {
    state.images = state.images.filter(img => img.id !== imageId)
    if (state.currentImage && state.currentImage.id === imageId) {
      state.currentImage = null
    }
  }
}

const actions = {
  // 获取所有图像
  async fetchImages({ commit }) {
    asyncActionHandler.start(commit);
    
    try {
      console.log('开始从API获取图像数据...')
      const response = await axios.get(`${API_URL}/images`, { headers: getAuthHeader() })
      console.log('API响应成功, 状态码:', response.status)
      
      // 确保返回的数据是数组
      let images = []
      if (Array.isArray(response.data)) {
        console.log('响应数据是数组，长度:', response.data.length)
        images = response.data
        // 记录前三条数据示例
        if (images.length > 0) {
          console.log('数据样例:', images.slice(0, 3))
        }
      } else if (typeof response.data === 'object' && response.data !== null) {
        console.log('响应不是数组，尝试提取数组数据')
        // 常见的REST API响应格式
        if (Array.isArray(response.data.content)) {
          images = response.data.content
        } else if (Array.isArray(response.data.data)) {
          images = response.data.data
        } else if (Array.isArray(response.data.items)) {
          images = response.data.items
        } else {
          // 如果都不是，尝试转换对象为数组
          images = Object.values(response.data).filter(item => item && typeof item === 'object')
        }
      }
      
      console.log('处理后的图像数据数量:', images.length)
      commit('setImages', images)
      return images
    } catch (error) {
      console.error('获取图像数据失败:', error)
      asyncActionHandler.error(commit, error);
      throw error
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 按状态获取图像
  async fetchImagesByStatus({ commit }, status) {
    asyncActionHandler.start(commit);
    
    try {
      const response = await axios.get(`${API_URL}/images/status/${status}`, { headers: getAuthHeader() })
      // 确保返回的数据是数组
      const images = Array.isArray(response.data) ? response.data : []
      commit('setImages', images)
      return images
    } catch (error) {
      asyncActionHandler.error(commit, error);
      throw error
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 获取单个图像详情
  async fetchImageById({ commit }, id) {
    asyncActionHandler.start(commit);
    
    try {
      const response = await axios.get(`${API_URL}/images/${id}`, { headers: getAuthHeader() })
      commit('setCurrentImage', response.data)
      return response.data
    } catch (error) {
      asyncActionHandler.error(commit, error);
      throw error
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 创建新图像
  async createImage({ commit }, imageData) {
    asyncActionHandler.start(commit);
    
    try {
      const response = await axios.post(`${API_URL}/images`, imageData, { headers: getAuthHeader() })
      commit('addImage', response.data)
      return response.data
    } catch (error) {
      asyncActionHandler.error(commit, error);
      throw error
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 更新图像
  async updateImage({ commit }, { id, imageData }) {
    asyncActionHandler.start(commit);
    
    try {
      const response = await axios.put(`${API_URL}/images/${id}`, imageData, { headers: getAuthHeader() })
      commit('updateImage', response.data)
      return response.data
    } catch (error) {
      asyncActionHandler.error(commit, error);
      throw error
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 更新图像状态
  async updateImageStatus({ commit }, { id, status, reviewerId, reviewNotes }) {
    asyncActionHandler.start(commit);
    
    try {
      await axios.put(
        `${API_URL}/images/${id}/status`, 
        null, 
        { 
          params: { status, reviewerId, reviewNotes },
          headers: getAuthHeader() 
        }
      )
      
      // 获取更新后的图像数据
      const response = await axios.get(`${API_URL}/images/${id}`, { headers: getAuthHeader() })
      commit('updateImage', response.data)
      return response.data
    } catch (error) {
      asyncActionHandler.error(commit, error);
      throw error
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 删除图像
  async deleteImage({ commit }, id) {
    asyncActionHandler.start(commit);
    
    try {
      // 使用封装的API客户端而不是直接使用axios
      await api.images.delete(id)
      commit('removeImage', id)
      return { success: true }
    } catch (error) {
      let errorMessage = '删除失败';
      
      if (error.response && error.response.data) {
        // 如果后端返回了详细的错误信息
        errorMessage = typeof error.response.data === 'string' 
          ? error.response.data 
          : JSON.stringify(error.response.data);
      } else {
        errorMessage += ': ' + (error.message || '未知错误');
      }
      
      commit('setError', errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      asyncActionHandler.end(commit);
    }
  },
  
  // 获取统计数据 - 使用stats模块而不是直接调用API
  async fetchStats({ dispatch, rootState, commit }) {
    // 委托给stats模块处理
    return dispatch('fetchStats', null, { root: true });
  }
}

export default {
  state,
  getters,
  mutations,
  actions
} 