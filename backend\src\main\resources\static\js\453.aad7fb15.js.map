{"version": 3, "file": "js/453.aad7fb15.js", "mappings": "yMACOA,MAAM,a,GADbC,IAAA,EAIwBD,MAAM,oB,GAJ9BC,IAAA,EAU2BD,MAAM,sB,GAVjCC,IAAA,EAcgBD,MAAM,O,GACXA,MAAM,Y,GACJA,MAAM,qC,EAhBnB,Q,GA2BaA,MAAM,uC,GAWRA,MAAM,Y,GACJA,MAAM,Q,GAIJA,MAAM,a,GASJA,MAAM,kB,EApDvB,Y,0CACEE,EAAAA,EAAAA,IAmEM,MAnENC,EAmEM,gBAlEJC,EAAAA,EAAAA,IAA+B,MAA3BJ,MAAM,aAAY,QAAI,IAEfK,EAAAC,UAAO,WAAlBJ,EAAAA,EAAAA,IAIM,MAJNK,EAIMC,EAAA,KAAAA,EAAA,KAHJJ,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,iBAAiBS,KAAK,U,EAC/BL,EAAAA,EAAAA,IAA2C,QAArCJ,MAAM,mBAAkB,YAAM,OAIxBK,EAAAK,QAAK,WAArBR,EAAAA,EAAAA,IAEM,MAFNS,GAEMC,EAAAA,EAAAA,IADDP,EAAAK,OAAK,iBAGVR,EAAAA,EAAAA,IAqDM,MArDNW,EAqDM,EApDJT,EAAAA,EAAAA,IAqBM,MArBNU,EAqBM,EApBJV,EAAAA,EAAAA,IAUM,MAVNW,EAUM,EATJX,EAAAA,EAAAA,IAAiG,OAA5FY,IAAI,eAAgBC,IAAKC,EAAAC,SAAUnB,MAAM,YAAYoB,IAAI,QAASC,OAAIb,EAAA,KAAAA,EAAA,qBAAEU,EAAAI,kBAAAJ,EAAAI,iBAAAC,MAAAL,EAAAM,UAAgB,I,QAjBvGC,IAkBUrB,EAAAA,EAAAA,IAOS,UANPY,IAAI,mBACJhB,MAAM,oDACL0B,YAASlB,EAAA,KAAAA,EAAA,qBAAEU,EAAAS,cAAAT,EAAAS,aAAAJ,MAAAL,EAAAM,UAAY,GACvBI,YAASpB,EAAA,KAAAA,EAAA,qBAAEU,EAAAW,MAAAX,EAAAW,KAAAN,MAAAL,EAAAM,UAAI,GACfM,UAAOtB,EAAA,KAAAA,EAAA,qBAAEU,EAAAa,YAAAb,EAAAa,WAAAR,MAAAL,EAAAM,UAAU,GACnBQ,aAAUxB,EAAA,KAAAA,EAAA,qBAAEU,EAAAa,YAAAb,EAAAa,WAAAR,MAAAL,EAAAM,UAAU,I,aAG3BpB,EAAAA,EAAAA,IAQM,MARN6B,EAQM,EAPJ7B,EAAAA,EAAAA,IAGM,aAFJA,EAAAA,EAAAA,IAAoF,UAA5EJ,MAAM,+BAAgCkC,QAAK1B,EAAA,KAAAA,EAAA,qBAAEU,EAAAiB,kBAAAjB,EAAAiB,iBAAAZ,MAAAL,EAAAM,UAAgB,IAAE,SACvEpB,EAAAA,EAAAA,IAAsE,UAA9DJ,MAAM,kBAAmBkC,QAAK1B,EAAA,KAAAA,EAAA,qBAAEU,EAAAkB,iBAAAlB,EAAAkB,gBAAAb,MAAAL,EAAAM,UAAe,IAAE,WAE3DpB,EAAAA,EAAAA,IAEM,aADJA,EAAAA,EAAAA,IAAqE,UAA7DJ,MAAM,oBAAqBkC,QAAK1B,EAAA,KAAAA,EAAA,YAAA6B,GAAA,OAAEC,EAAAC,QAAQC,IAAI,EAAF,IAAM,aAKhEpC,EAAAA,EAAAA,IA4BM,MA5BNqC,EA4BM,EA3BJrC,EAAAA,EAAAA,IA0BM,MA1BNsC,EA0BM,gBAzBJtC,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,eAAa,EACtBI,EAAAA,EAAAA,IAAqC,MAAjCJ,MAAM,mBAAkB,UAAI,KAElCI,EAAAA,EAAAA,IAqBM,MArBNuC,EAqBM,EApBJvC,EAAAA,EAAAA,IAA4C,yBAAzCA,EAAAA,EAAAA,IAAoB,cAAZ,OAAG,KA5C1BwC,EAAAA,EAAAA,IA4CmC,KAAChC,EAAAA,EAAAA,IAAGP,EAAAwC,MAAMC,MAAI,MACrC1C,EAAAA,EAAAA,IAAqD,yBAAlDA,EAAAA,EAAAA,IAAqB,cAAb,QAAI,KA7C3BwC,EAAAA,EAAAA,IA6CoC,KAAChC,EAAAA,EAAAA,IAAGP,EAAAwC,MAAME,cAAY,MAC9C3C,EAAAA,EAAAA,IAAgE,yBAA7DA,EAAAA,EAAAA,IAAsB,cAAd,SAAK,KA9C5BwC,EAAAA,EAAAA,IA8CqC,KAAChC,EAAAA,EAAAA,IAAGM,EAAA8B,WAAW3C,EAAAwC,MAAMI,aAAU,MACxD7C,EAAAA,EAAAA,IAA8E,yBAA3EA,EAAAA,EAAAA,IAAoB,cAAZ,OAAG,mBA/C1BwC,EAAAA,EAAAA,QA+CoCxC,EAAAA,EAAAA,IAAkD,QAA3CJ,OA/C3CkD,EAAAA,EAAAA,IA+CkDhC,EAAAiC,e,QAAgBjC,EAAAkC,YAAU,oBAEhEhD,EAAAA,EAAAA,IAAM,mCAENA,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAQM,MARNiD,EAQM,gBAPJnD,EAAAA,EAAAA,IAMSoD,EAAAA,GAAA,MA3DvBC,EAAAA,EAAAA,IAsD+BlD,EAAAmD,cAtD/B,SAsDuBC,G,kBADTvD,EAAAA,EAAAA,IAMS,UAJND,IAAKwD,EAAKC,KACV1D,OAxDjBkD,EAAAA,EAAAA,IAAA,OAwDgC7C,EAAAsD,cAAgBF,EAAKC,KAAO,cAAgB,wBAC3DxB,QAAK,SAAAG,GAAA,OAAEnB,EAAA0C,WAAWH,EAAKC,KAAI,G,EAC5BtD,EAAAA,EAAAA,IAA0B,KAAtBJ,OA1DpBkD,EAAAA,EAAAA,IA0D2BO,EAAKI,O,SA1DhCjB,EAAAA,EAAAA,IA0D0C,KAAChC,EAAAA,EAAAA,IAAG6C,EAAKK,OAAK,OA1DxDC,E,2BA8DY3D,EAAAA,EAAAA,IAAW,UAAP,MAAE,cACNA,EAAAA,EAAAA,IAAgG,YA/D5G,sBAAAI,EAAA,KAAAA,EAAA,YAAA6B,GAAA,OA+D+BhC,EAAA2D,MAAK3B,CAAA,GAAErC,MAAM,oBAAoBiE,KAAK,IAAIC,YAAY,a,iBAAtD7D,EAAA2D,mB,iJAW/B,SACElB,KAAM,cACNqB,KAAI,WACF,MAAO,CACLtB,MAAO,CAAC,EACRvC,SAAS,EACTI,MAAO,KACP0D,YAAa,GACbC,kBAAmB,KACnBC,WAAW,EACXX,YAAa,WACbK,MAAO,GACPR,aAAc,CACZ,CAAEE,KAAM,WAAYI,MAAO,OAAQD,KAAM,gBACzC,CAAEH,KAAM,YAAaI,MAAO,KAAMD,KAAM,gBACxC,CAAEH,KAAM,SAAUI,MAAO,KAAMD,KAAM,iBAG3C,EACAU,SAAU,CACRC,QAAO,WACL,OAAOC,KAAKC,OAAOC,OAAOC,EAC5B,EACAzD,SAAQ,WACN,MAAO,GAAP0D,OAAUC,GAA2B,gBAAAD,OAAeJ,KAAKD,QAAO,QAClE,EACApB,WAAU,WACR,IAAM2B,EAAY,CAChB,SAAY,MACZ,UAAa,MACb,SAAY,MACZ,SAAY,MACZ,SAAY,OAEd,OAAOA,EAAUN,KAAK5B,MAAMmC,SAAWP,KAAK5B,MAAMmC,MACpD,EACA7B,YAAW,WACT,IAAM8B,EAAW,CACf,SAAY,iBACZ,UAAa,eACb,SAAY,YACZ,SAAY,eACZ,SAAY,eAEd,OAAOA,EAASR,KAAK5B,MAAMmC,SAAW,EACxC,GAEFE,QAAO,WACLT,KAAKU,iBAELC,OAAOC,iBAAiB,SAAUZ,KAAKnD,iBACzC,EACAgE,cAAa,WACXF,OAAOG,oBAAoB,SAAUd,KAAKnD,iBAC5C,EACAkE,QAAS,CACDL,eAAc,WAAG,IAAAM,EAAA,YAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAL,EAAAA,EAAAA,KAAAM,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,OACF,OAAnBV,EAAKnF,SAAU,EAAI4F,EAAAE,EAAA,EAAAF,EAAAC,EAAA,EAEME,IAAAA,IAAU,GAADxB,OAAIC,GAA2B,gBAAAD,OAAeY,EAAKjB,UAAU,OAAvFsB,EAAOI,EAAAI,EACbb,EAAK5C,MAAQiD,EAAS3B,KAGlBsB,EAAK5C,MAAM0D,iBACbd,EAAKrB,YAAcoC,KAAKC,MAAMhB,EAAK5C,MAAM0D,gBACzCd,EAAKzB,MAAQyB,EAAK5C,MAAMmB,OAAS,IACnCkC,EAAAC,EAAA,eAAAD,EAAAE,EAAA,EAAAJ,EAAAE,EAAAI,EAEAb,EAAK/E,MAAQ,eAA4B,QAAZqF,EAAAC,EAAIF,gBAAQ,IAAAC,GAAM,QAANA,EAAZA,EAAc5B,YAAI,IAAA4B,OAAA,EAAlBA,EAAoBW,UAAWV,EAAIU,SAAQ,OAEpD,OAFoDR,EAAAE,EAAA,EAExEX,EAAKnF,SAAU,EAAK4F,EAAAS,EAAA,iBAAAT,EAAAU,EAAA,MAAAf,EAAA,qBAdDH,EAgBvB,EACA1C,WAAU,SAAC6D,GACT,IAAKA,EAAY,MAAO,GACxB,IAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,eAAe,QAC7B,EACA1F,iBAAgB,WACd,IAAM2F,EAAMxC,KAAKyC,MAAMC,aACjBC,EAAS3C,KAAKyC,MAAMG,iBAEtBJ,GAAOG,IAETA,EAAOE,MAAQL,EAAIM,YACnBH,EAAOI,OAASP,EAAIQ,aAGpBhD,KAAKiD,oBAET,EACAA,kBAAiB,WAAG,IAAAC,EAAA,KACZP,EAAS3C,KAAKyC,MAAMG,iBAC1B,GAAKD,EAAL,CAEA,IAAMQ,EAAMR,EAAOS,WAAW,MAC9BD,EAAIE,UAAU,EAAG,EAAGV,EAAOE,MAAOF,EAAOI,QAGzC/C,KAAKL,YAAY2D,SAAQ,SAAAC,GACvBL,EAAKM,eAAeL,EAAKI,EAC3B,GARmB,CASrB,EACAC,eAAc,SAACL,EAAKI,GAIlB,GAHAJ,EAAIM,YAAc,UAClBN,EAAIO,UAAY,EAEQ,aAApBH,EAAWtE,KACbkE,EAAIQ,YACJJ,EAAWK,OAAON,SAAQ,SAACO,EAAOC,GAClB,IAAVA,EACFX,EAAIY,OAAOF,EAAMG,EAAGH,EAAMI,GAE1Bd,EAAIe,OAAOL,EAAMG,EAAGH,EAAMI,EAE9B,IACAd,EAAIgB,cACC,GAAwB,cAApBZ,EAAWtE,KAAsB,CAC1C,IAAM4D,EAAQU,EAAWa,IAAIJ,EAAIT,EAAWc,MAAML,EAC5CjB,EAASQ,EAAWa,IAAIH,EAAIV,EAAWc,MAAMJ,EACnDd,EAAImB,WAAWf,EAAWc,MAAML,EAAGT,EAAWc,MAAMJ,EAAGpB,EAAOE,EAChE,MAAO,GAAwB,WAApBQ,EAAWtE,KAAmB,CACvC,IAAMsF,EAASC,KAAKC,KAClBD,KAAKE,IAAInB,EAAWa,IAAIJ,EAAIT,EAAWc,MAAML,EAAG,GAChDQ,KAAKE,IAAInB,EAAWa,IAAIH,EAAIV,EAAWc,MAAMJ,EAAG,IAElDd,EAAIQ,YACJR,EAAIwB,IAAIpB,EAAWc,MAAML,EAAGT,EAAWc,MAAMJ,EAAGM,EAAQ,EAAG,EAAIC,KAAKI,IACpEzB,EAAIgB,QACN,CACF,EACAjH,aAAY,SAAC2H,GACX7E,KAAKH,WAAY,EACjB,IAAM8C,EAAS3C,KAAKyC,MAAMG,iBACpBkC,EAAOnC,EAAOoC,wBACdf,EAAIa,EAAMG,QAAUF,EAAKG,KACzBhB,EAAIY,EAAMK,QAAUJ,EAAKK,IAEN,aAArBnF,KAAKd,YACPc,KAAKJ,kBAAoB,CACvBX,KAAM,WACN2E,OAAQ,CAAC,CAAEI,EAAAA,EAAGC,EAAAA,KAEc,cAArBjE,KAAKd,aAAoD,WAArBc,KAAKd,cAClDc,KAAKJ,kBAAoB,CACvBX,KAAMe,KAAKd,YACXmF,MAAO,CAAEL,EAAAA,EAAGC,EAAAA,GACZG,IAAK,CAAEJ,EAAAA,EAAGC,EAAAA,IAGhB,EACA7G,KAAI,SAACyH,GACH,GAAK7E,KAAKH,WAAcG,KAAKJ,kBAA7B,CAEA,IAAM+C,EAAS3C,KAAKyC,MAAMG,iBACpBO,EAAMR,EAAOS,WAAW,MACxB0B,EAAOnC,EAAOoC,wBACdf,EAAIa,EAAMG,QAAUF,EAAKG,KACzBhB,EAAIY,EAAMK,QAAUJ,EAAKK,IAE/B,GAAyB,aAArBnF,KAAKd,YAA4B,CACnCc,KAAKJ,kBAAkBgE,OAAOwB,KAAK,CAAEpB,EAAAA,EAAGC,EAAAA,IAGxCd,EAAIM,YAAc,UAClBN,EAAIO,UAAY,EAChBP,EAAIQ,YACJ,IAAMC,EAAS5D,KAAKJ,kBAAkBgE,OAChCyB,EAAYzB,EAAOA,EAAO0B,OAAS,GACnCC,EAAW3B,EAAOA,EAAO0B,OAAS,GACxCnC,EAAIY,OAAOsB,EAAUrB,EAAGqB,EAAUpB,GAClCd,EAAIe,OAAOqB,EAASvB,EAAGuB,EAAStB,GAChCd,EAAIgB,QACN,MAEEnE,KAAKJ,kBAAkBwE,IAAM,CAAEJ,EAAAA,EAAGC,EAAAA,GAClCjE,KAAKiD,oBACLjD,KAAKwD,eAAeL,EAAKnD,KAAKJ,kBAzBsB,CA2BxD,EACAtC,WAAU,WACJ0C,KAAKH,WAAaG,KAAKJ,oBACzBI,KAAKL,YAAYyF,KAAKpF,KAAKJ,mBAC3BI,KAAKJ,kBAAoB,KACzBI,KAAKH,WAAY,EAErB,EACAnC,iBAAgB,WACd,GAAI8H,QAAQ,eAAgB,CAC1BxF,KAAKL,YAAc,GACnB,IAAMgD,EAAS3C,KAAKyC,MAAMG,iBACpBO,EAAMR,EAAOS,WAAW,MAC9BD,EAAIE,UAAU,EAAG,EAAGV,EAAOE,MAAOF,EAAOI,OAC3C,CACF,EACMpF,gBAAe,WAAG,IAAA8H,EAAA,YAAAxE,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAA,SAAAuE,IAAA,IAAA5D,EAAA6D,EAAAC,EAAA,OAAA1E,EAAAA,EAAAA,KAAAM,GAAA,SAAAqE,GAAA,eAAAA,EAAAnE,GAAA,OAEmC,OAFnCmE,EAAAlE,EAAA,EAEdG,EAAiBC,KAAK+D,UAAUL,EAAK9F,aAAYkG,EAAAnE,EAAA,EACjDE,IAAAA,KAAW,GAADxB,OAAIC,GAA2B,gBAAAD,OAAeqF,EAAK1F,QAAO,aAAa,CACrF+B,eAAAA,EACAvC,MAAOkG,EAAKlG,QACZ,OACFkG,EAAKM,OAAOC,SAAS,YAAa,CAAE/G,KAAM,UAAWgD,QAAS,WAAW4D,EAAAnE,EAAA,eAAAmE,EAAAlE,EAAA,EAAAiE,EAAAC,EAAAhE,EAEzE4D,EAAKM,OAAOC,SAAS,YAAa,CAChC/G,KAAM,QACNgD,QAAS,aAA0B,QAAZ0D,EAAAC,EAAIvE,gBAAQ,IAAAsE,GAAM,QAANA,EAAZA,EAAcjG,YAAI,IAAAiG,OAAA,EAAlBA,EAAoB1D,UAAW2D,EAAI3D,WAC1D,cAAA4D,EAAA1D,EAAA,MAAAuD,EAAA,iBAZkBzE,EAcxB,EACA9B,WAAU,SAACH,GACTgB,KAAKd,YAAcF,CACrB,I,eCvRJ,MAAMiH,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/ImageDetail.vue", "webpack://medical-annotation-frontend/./src/views/ImageDetail.vue?0c26"], "sourcesContent": ["<template>\n  <div class=\"container\">\n    <h2 class=\"mt-4 mb-3\">图像详情</h2>\n    \n    <div v-if=\"loading\" class=\"text-center my-5\">\n      <div class=\"spinner-border\" role=\"status\">\n        <span class=\"visually-hidden\">加载中...</span>\n      </div>\n    </div>\n    \n    <div v-else-if=\"error\" class=\"alert alert-danger\">\n      {{ error }}\n    </div>\n    \n    <div v-else class=\"row\">\n      <div class=\"col-md-8\">\n        <div class=\"image-container position-relative\">\n          <img ref=\"imageElement\" :src=\"imageUrl\" class=\"img-fluid\" alt=\"血管瘤图像\" @load=\"initializeCanvas\" />\n          <canvas \n            ref=\"annotationCanvas\" \n            class=\"annotation-canvas position-absolute top-0 start-0\" \n            @mousedown=\"startDrawing\" \n            @mousemove=\"draw\" \n            @mouseup=\"endDrawing\"\n            @mouseleave=\"endDrawing\">\n          </canvas>\n        </div>\n        <div class=\"mt-3 d-flex justify-content-between\">\n          <div>\n            <button class=\"btn btn-outline-primary me-2\" @click=\"clearAnnotations\">清除标注</button>\n            <button class=\"btn btn-primary\" @click=\"saveAnnotations\">保存标注</button>\n          </div>\n          <div>\n            <button class=\"btn btn-secondary\" @click=\"$router.go(-1)\">返回</button>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"col-md-4\">\n        <div class=\"card\">\n          <div class=\"card-header\">\n            <h5 class=\"card-title mb-0\">图像信息</h5>\n          </div>\n          <div class=\"card-body\">\n            <p><strong>名称：</strong> {{ image.name }}</p>\n            <p><strong>上传者：</strong> {{ image.uploaderName }}</p>\n            <p><strong>上传时间：</strong> {{ formatDate(image.uploadTime) }}</p>\n            <p><strong>状态：</strong> <span :class=\"statusClass\">{{ statusText }}</span></p>\n            \n            <hr />\n            \n            <h6>标注工具</h6>\n            <div class=\"btn-group mb-3\">\n              <button \n                v-for=\"tool in drawingTools\" \n                :key=\"tool.type\"\n                :class=\"['btn', currentTool === tool.type ? 'btn-primary' : 'btn-outline-primary']\" \n                @click=\"selectTool(tool.type)\">\n                <i :class=\"tool.icon\"></i> {{ tool.label }}\n              </button>\n            </div>\n            \n            <h6>注释</h6>\n            <textarea v-model=\"notes\" class=\"form-control mb-3\" rows=\"3\" placeholder=\"添加标注说明...\"></textarea>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport axios from 'axios';\n\nexport default {\n  name: 'ImageDetail',\n  data() {\n    return {\n      image: {},\n      loading: true,\n      error: null,\n      annotations: [],\n      currentAnnotation: null,\n      isDrawing: false,\n      currentTool: 'freehand',\n      notes: '',\n      drawingTools: [\n        { type: 'freehand', label: '自由绘制', icon: 'bi bi-pencil' },\n        { type: 'rectangle', label: '矩形', icon: 'bi bi-square' },\n        { type: 'circle', label: '圆形', icon: 'bi bi-circle' }\n      ]\n    };\n  },\n  computed: {\n    imageId() {\n      return this.$route.params.id;\n    },\n    imageUrl() {\n      return `${process.env.VUE_APP_API_URL}/api/images/${this.imageId}/file`;\n    },\n    statusText() {\n      const statusMap = {\n        'UPLOADED': '已上传',\n        'ANNOTATED': '已标注',\n        'REVIEWED': '已审核',\n        'APPROVED': '已批准',\n        'REJECTED': '已拒绝'\n      };\n      return statusMap[this.image.status] || this.image.status;\n    },\n    statusClass() {\n      const classMap = {\n        'UPLOADED': 'text-secondary',\n        'ANNOTATED': 'text-primary',\n        'REVIEWED': 'text-info',\n        'APPROVED': 'text-success',\n        'REJECTED': 'text-danger'\n      };\n      return classMap[this.image.status] || '';\n    }\n  },\n  mounted() {\n    this.fetchImageData();\n    // 当窗口大小改变时，重新调整画布大小\n    window.addEventListener('resize', this.initializeCanvas);\n  },\n  beforeUnmount() {\n    window.removeEventListener('resize', this.initializeCanvas);\n  },\n  methods: {\n    async fetchImageData() {\n      this.loading = true;\n      try {\n        const response = await axios.get(`${process.env.VUE_APP_API_URL}/api/images/${this.imageId}`);\n        this.image = response.data;\n        \n        // 如果有标注数据，加载它\n        if (this.image.annotationData) {\n          this.annotations = JSON.parse(this.image.annotationData);\n          this.notes = this.image.notes || '';\n        }\n      } catch (err) {\n        this.error = '加载图像详情失败: ' + (err.response?.data?.message || err.message);\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleString('zh-CN');\n    },\n    initializeCanvas() {\n      const img = this.$refs.imageElement;\n      const canvas = this.$refs.annotationCanvas;\n      \n      if (img && canvas) {\n        // 设置canvas大小与图像一致\n        canvas.width = img.clientWidth;\n        canvas.height = img.clientHeight;\n        \n        // 绘制已有的标注\n        this.redrawAnnotations();\n      }\n    },\n    redrawAnnotations() {\n      const canvas = this.$refs.annotationCanvas;\n      if (!canvas) return;\n      \n      const ctx = canvas.getContext('2d');\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      // 绘制所有已保存的标注\n      this.annotations.forEach(annotation => {\n        this.drawAnnotation(ctx, annotation);\n      });\n    },\n    drawAnnotation(ctx, annotation) {\n      ctx.strokeStyle = '#FF0000';\n      ctx.lineWidth = 2;\n      \n      if (annotation.type === 'freehand') {\n        ctx.beginPath();\n        annotation.points.forEach((point, index) => {\n          if (index === 0) {\n            ctx.moveTo(point.x, point.y);\n          } else {\n            ctx.lineTo(point.x, point.y);\n          }\n        });\n        ctx.stroke();\n      } else if (annotation.type === 'rectangle') {\n        const width = annotation.end.x - annotation.start.x;\n        const height = annotation.end.y - annotation.start.y;\n        ctx.strokeRect(annotation.start.x, annotation.start.y, width, height);\n      } else if (annotation.type === 'circle') {\n        const radius = Math.sqrt(\n          Math.pow(annotation.end.x - annotation.start.x, 2) + \n          Math.pow(annotation.end.y - annotation.start.y, 2)\n        );\n        ctx.beginPath();\n        ctx.arc(annotation.start.x, annotation.start.y, radius, 0, 2 * Math.PI);\n        ctx.stroke();\n      }\n    },\n    startDrawing(event) {\n      this.isDrawing = true;\n      const canvas = this.$refs.annotationCanvas;\n      const rect = canvas.getBoundingClientRect();\n      const x = event.clientX - rect.left;\n      const y = event.clientY - rect.top;\n      \n      if (this.currentTool === 'freehand') {\n        this.currentAnnotation = {\n          type: 'freehand',\n          points: [{ x, y }]\n        };\n      } else if (this.currentTool === 'rectangle' || this.currentTool === 'circle') {\n        this.currentAnnotation = {\n          type: this.currentTool,\n          start: { x, y },\n          end: { x, y }\n        };\n      }\n    },\n    draw(event) {\n      if (!this.isDrawing || !this.currentAnnotation) return;\n      \n      const canvas = this.$refs.annotationCanvas;\n      const ctx = canvas.getContext('2d');\n      const rect = canvas.getBoundingClientRect();\n      const x = event.clientX - rect.left;\n      const y = event.clientY - rect.top;\n      \n      if (this.currentTool === 'freehand') {\n        this.currentAnnotation.points.push({ x, y });\n        \n        // 绘制当前线段\n        ctx.strokeStyle = '#FF0000';\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        const points = this.currentAnnotation.points;\n        const lastPoint = points[points.length - 2];\n        const newPoint = points[points.length - 1];\n        ctx.moveTo(lastPoint.x, lastPoint.y);\n        ctx.lineTo(newPoint.x, newPoint.y);\n        ctx.stroke();\n      } else {\n        // 矩形或圆形，需要重新绘制所有内容\n        this.currentAnnotation.end = { x, y };\n        this.redrawAnnotations();\n        this.drawAnnotation(ctx, this.currentAnnotation);\n      }\n    },\n    endDrawing() {\n      if (this.isDrawing && this.currentAnnotation) {\n        this.annotations.push(this.currentAnnotation);\n        this.currentAnnotation = null;\n        this.isDrawing = false;\n      }\n    },\n    clearAnnotations() {\n      if (confirm('确定要清除所有标注吗？')) {\n        this.annotations = [];\n        const canvas = this.$refs.annotationCanvas;\n        const ctx = canvas.getContext('2d');\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n      }\n    },\n    async saveAnnotations() {\n      try {\n        const annotationData = JSON.stringify(this.annotations);\n        await axios.post(`${process.env.VUE_APP_API_URL}/api/images/${this.imageId}/annotate`, {\n          annotationData,\n          notes: this.notes\n        });\n        this.$store.dispatch('showAlert', { type: 'success', message: '标注保存成功' });\n      } catch (err) {\n        this.$store.dispatch('showAlert', { \n          type: 'error', \n          message: '保存标注失败: ' + (err.response?.data?.message || err.message)\n        });\n      }\n    },\n    selectTool(tool) {\n      this.currentTool = tool;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.image-container {\n  max-height: 70vh;\n  overflow: hidden;\n}\n\n.annotation-canvas {\n  pointer-events: all;\n  z-index: 10;\n}\n</style> ", "import { render } from \"./ImageDetail.vue?vue&type=template&id=04771bcf&scoped=true\"\nimport script from \"./ImageDetail.vue?vue&type=script&lang=js\"\nexport * from \"./ImageDetail.vue?vue&type=script&lang=js\"\n\nimport \"./ImageDetail.vue?vue&type=style&index=0&id=04771bcf&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-04771bcf\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "$data", "loading", "_hoisted_2", "_cache", "role", "error", "_hoisted_3", "_toDisplayString", "_hoisted_4", "_hoisted_5", "_hoisted_6", "ref", "src", "$options", "imageUrl", "alt", "onLoad", "initializeCanvas", "apply", "arguments", "_hoisted_7", "onMousedown", "startDrawing", "onMousemove", "draw", "onMouseup", "endDrawing", "onMouseleave", "_hoisted_8", "onClick", "clearAnnotations", "saveAnnotations", "$event", "_ctx", "$router", "go", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_createTextVNode", "image", "name", "uploaderName", "formatDate", "uploadTime", "_normalizeClass", "statusClass", "statusText", "_hoisted_12", "_Fragment", "_renderList", "drawingTools", "tool", "type", "currentTool", "selectTool", "icon", "label", "_hoisted_13", "notes", "rows", "placeholder", "data", "annotations", "currentAnnotation", "isDrawing", "computed", "imageId", "this", "$route", "params", "id", "concat", "process", "statusMap", "status", "classMap", "mounted", "fetchImageData", "window", "addEventListener", "beforeUnmount", "removeEventListener", "methods", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "response", "_err$response", "_t", "w", "_context", "n", "p", "axios", "v", "annotationData", "JSON", "parse", "message", "f", "a", "dateString", "date", "Date", "toLocaleString", "img", "$refs", "imageElement", "canvas", "annotationCanvas", "width", "clientWidth", "height", "clientHeight", "redrawAnnotations", "_this2", "ctx", "getContext", "clearRect", "for<PERSON>ach", "annotation", "drawAnnotation", "strokeStyle", "lineWidth", "beginPath", "points", "point", "index", "moveTo", "x", "y", "lineTo", "stroke", "end", "start", "strokeRect", "radius", "Math", "sqrt", "pow", "arc", "PI", "event", "rect", "getBoundingClientRect", "clientX", "left", "clientY", "top", "push", "lastPoint", "length", "newPoint", "confirm", "_this3", "_callee2", "_err$response2", "_t2", "_context2", "stringify", "$store", "dispatch", "__exports__", "render"], "sourceRoot": ""}