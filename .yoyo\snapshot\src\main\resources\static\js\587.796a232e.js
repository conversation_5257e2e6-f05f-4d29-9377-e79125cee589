"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[587],{1587:(e,a,r)=>{r.r(a),r.d(a,{default:()=>_});r(2675),r(9463),r(4114),r(2010);var t=r(641),n=r(3751),s=r(33),l={class:"container"},o={class:"card mb-4"},i={class:"card-body"},d={class:"mb-3"},u={class:"mb-3"},p={class:"mb-3"},c={class:"mb-4"},m={key:0,class:"mb-4"},f={class:"image-preview-container"},g=["src"],v={class:"d-flex justify-content-between"},b=["disabled"],h={key:0},k={key:1},L={key:0,class:"progress mt-3"},w=["aria-valuenow"];function y(e,a,r,y,P,D){return(0,t.uX)(),(0,t.CE)("div",l,[a[13]||(a[13]=(0,t.Lk)("h2",{class:"mt-4 mb-4"},"上传血管瘤图像",-1)),(0,t.Lk)("div",o,[(0,t.Lk)("div",i,[(0,t.Lk)("form",{onSubmit:a[5]||(a[5]=(0,n.D$)((function(){return D.uploadImage&&D.uploadImage.apply(D,arguments)}),["prevent"]))},[(0,t.Lk)("div",d,[a[6]||(a[6]=(0,t.Lk)("label",{for:"imageName",class:"form-label"},"图像名称",-1)),(0,t.bo)((0,t.Lk)("input",{type:"text",class:"form-control",id:"imageName","onUpdate:modelValue":a[0]||(a[0]=function(e){return P.formData.name=e}),required:"",placeholder:"请输入图像名称"},null,512),[[n.Jo,P.formData.name]])]),(0,t.Lk)("div",u,[a[7]||(a[7]=(0,t.Lk)("label",{for:"description",class:"form-label"},"描述",-1)),(0,t.bo)((0,t.Lk)("textarea",{class:"form-control",id:"description","onUpdate:modelValue":a[1]||(a[1]=function(e){return P.formData.description=e}),rows:"3",placeholder:"请输入图像描述（可选）"},null,512),[[n.Jo,P.formData.description]])]),(0,t.Lk)("div",p,[a[8]||(a[8]=(0,t.Lk)("label",{for:"patientInfo",class:"form-label"},"患者信息",-1)),(0,t.bo)((0,t.Lk)("input",{type:"text",class:"form-control",id:"patientInfo","onUpdate:modelValue":a[2]||(a[2]=function(e){return P.formData.patientInfo=e}),placeholder:"请输入患者信息（可选）"},null,512),[[n.Jo,P.formData.patientInfo]])]),(0,t.Lk)("div",c,[a[9]||(a[9]=(0,t.Lk)("label",{for:"imageFile",class:"form-label"},"选择图像文件",-1)),(0,t.Lk)("input",{type:"file",class:"form-control",id:"imageFile",onChange:a[3]||(a[3]=function(){return D.handleFileChange&&D.handleFileChange.apply(D,arguments)}),accept:"image/*",required:""},null,32),a[10]||(a[10]=(0,t.Lk)("div",{class:"form-text"},"支持JPG、PNG、BMP等常见图像格式，最大文件大小10MB",-1))]),P.imagePreview?((0,t.uX)(),(0,t.CE)("div",m,[a[11]||(a[11]=(0,t.Lk)("h5",null,"图像预览",-1)),(0,t.Lk)("div",f,[(0,t.Lk)("img",{src:P.imagePreview,class:"img-fluid",alt:"图像预览"},null,8,g)])])):(0,t.Q3)("",!0),(0,t.Lk)("div",v,[(0,t.Lk)("button",{type:"submit",class:"btn btn-primary",disabled:P.isUploading},[P.isUploading?((0,t.uX)(),(0,t.CE)("span",h,a[12]||(a[12]=[(0,t.Lk)("span",{class:"spinner-border spinner-border-sm",role:"status","aria-hidden":"true"},null,-1),(0,t.eW)(" 正在上传... ")]))):((0,t.uX)(),(0,t.CE)("span",k,"上传图像"))],8,b),(0,t.Lk)("button",{type:"button",class:"btn btn-secondary",onClick:a[4]||(a[4]=function(a){return e.$router.push("/images")})},"返回图像列表")])],32)])]),P.uploadProgress>0&&P.uploadProgress<100?((0,t.uX)(),(0,t.CE)("div",L,[(0,t.Lk)("div",{class:"progress-bar progress-bar-striped progress-bar-animated",role:"progressbar",style:(0,s.Tr)({width:P.uploadProgress+"%"}),"aria-valuenow":P.uploadProgress,"aria-valuemin":"0","aria-valuemax":"100"},(0,s.v_)(P.uploadProgress)+"% ",13,w)])):(0,t.Q3)("",!0)])}var P=r(4048),D=r(388),F=r(2505),I=r.n(F);const U={name:"ImageUpload",data:function(){return{formData:{name:"",description:"",patientInfo:""},selectedFile:null,imagePreview:null,isUploading:!1,uploadProgress:0}},methods:{handleFileChange:function(e){var a=this,r=e.target.files[0];if(!r)return this.selectedFile=null,void(this.imagePreview=null);if(r.size>10485760)return this.$store.dispatch("showAlert",{type:"error",message:"文件大小超过限制(10MB)"}),e.target.value="",this.selectedFile=null,void(this.imagePreview=null);this.selectedFile=r;var t=new FileReader;t.onload=function(e){a.imagePreview=e.target.result},t.readAsDataURL(r)},uploadImage:function(){var e=this;return(0,D.A)((0,P.A)().mark((function a(){var r,t;return(0,P.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.selectedFile){a.next=3;break}return e.$store.dispatch("showAlert",{type:"error",message:"请选择要上传的图像文件"}),a.abrupt("return");case 3:return e.isUploading=!0,e.uploadProgress=0,r=new FormData,r.append("file",e.selectedFile),r.append("name",e.formData.name),r.append("description",e.formData.description||""),r.append("patientInfo",e.formData.patientInfo||""),a.prev=10,a.next=13,I().post("".concat({NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL,"/api/images/upload"),r,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:function(a){e.uploadProgress=Math.round(100*a.loaded/a.total)}});case 13:e.$store.dispatch("showAlert",{type:"success",message:"图像上传成功"}),e.formData={name:"",description:"",patientInfo:""},e.selectedFile=null,e.imagePreview=null,document.getElementById("imageFile").value="",e.$router.push("/images"),a.next=24;break;case 21:a.prev=21,a.t0=a["catch"](10),e.$store.dispatch("showAlert",{type:"error",message:"上传失败: "+((null===(t=a.t0.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||a.t0.message)});case 24:return a.prev=24,e.isUploading=!1,a.finish(24);case 27:case"end":return a.stop()}}),a,null,[[10,21,24,27]])})))()}}};var C=r(6262);const A=(0,C.A)(U,[["render",y],["__scopeId","data-v-8212ec64"]]),_=A}}]);