"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[556],{1573:(e,t,a)=>{a.d(t,{VG:()=>r});a(88844),a(28706),a(15086),a(26099),a(27495),a(90906),a(99449),a(11392);var o=a(81052);a(68039),a(55593),a(51629),a(74423),a(59089),a(18111),a(7588),a(58940),a(21699),a(25440),a(23500);function n(e){var t="_t=".concat(Date.now());return e.includes("?")?"".concat(e,"&").concat(t):"".concat(e,"?").concat(t)}function r(e){if(!e)return"";if(console.log("[ImageHelper] 处理图像路径:",e),/^[a-zA-Z]:\\/.test(e)){console.log("[ImageHelper] 检测到文件系统路径");var t=encodeURIComponent(e),a=o.M8?"".concat(o.JR):"";return n("".concat(a,"/medical/image/system-path?path=").concat(t))}if(e.startsWith("http://")||e.startsWith("https://"))return n(e);if(e.startsWith("data:"))return e;var r=e;return e.startsWith("/medical")?(console.log("[ImageHelper] 检测到相对路径，添加后端服务器地址"),r=o.M8?"".concat(o.JR).concat(e):e):e.startsWith("/")&&(console.log("[ImageHelper] 检测到其他相对路径，添加后端服务器地址和上下文路径"),r=o.M8?"".concat(o.JR).concat(o.T_).concat(e):"".concat(o.T_).concat(e)),console.log("[ImageHelper] 处理后的URL:",r),n(r)}},44556:(e,t,a)=>{a.r(t),a.d(t,{default:()=>R});var o=a(61431),n={class:"structured-form-container"},r={key:0,class:"text-center my-5"},i={key:1,class:"form-content"},s={class:"form-note"},l={class:"form-section"},c={class:"adaptive-fields-container"},u={class:"form-section"},g={class:"section-title"},m={class:"form-grid"},d={class:"form-actions"};function f(e,t,a,f,p,h){var v=(0,o.g2)("el-alert"),b=(0,o.g2)("el-input-number"),D=(0,o.g2)("el-form-item"),y=(0,o.g2)("el-radio"),I=(0,o.g2)("el-radio-group"),S=(0,o.g2)("el-option"),_=(0,o.g2)("el-select"),A=(0,o.g2)("el-tag"),k=(0,o.g2)("el-input"),w=(0,o.g2)("el-button"),F=(0,o.g2)("el-form");return(0,o.uX)(),(0,o.CE)("div",n,[t[21]||(t[21]=(0,o.Lk)("div",{class:"page-header"},[(0,o.Lk)("div",{class:"header-content"},[(0,o.Lk)("h2",null,"病例信息填写")]),(0,o.Lk)("div",{class:"header-actions"})],-1)),p.loading?((0,o.uX)(),(0,o.CE)("div",r,t[9]||(t[9]=[(0,o.Lk)("div",{class:"spinner-border",role:"status"},[(0,o.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):((0,o.uX)(),(0,o.CE)("div",i,[(0,o.Lk)("div",s,[(0,o.bF)(v,{title:"注意：带有 * 标记的字段为必填项，其他字段为选填项。治疗与注意事项中的四个字段必须填写。",type:"info",closable:!1,"show-icon":""})]),(0,o.bF)(F,{ref:"caseForm",model:p.formData,"label-position":"top",rules:p.rules},{default:(0,o.k6)((function(){return[(0,o.Lk)("div",l,[t[14]||(t[14]=(0,o.Lk)("h3",{class:"section-title"},"基础信息",-1)),(0,o.Lk)("div",c,[(0,o.bF)(D,{label:"患者年龄",prop:"patientAge"},{default:(0,o.k6)((function(){return[(0,o.bF)(b,{modelValue:p.formData.patientAge,"onUpdate:modelValue":t[0]||(t[0]=function(e){return p.formData.patientAge=e}),min:0,max:120},null,8,["modelValue"])]})),_:1}),(0,o.bF)(D,{label:"性别",prop:"gender"},{default:(0,o.k6)((function(){return[(0,o.bF)(I,{modelValue:p.formData.gender,"onUpdate:modelValue":t[1]||(t[1]=function(e){return p.formData.gender=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(y,{label:"男"},{default:(0,o.k6)((function(){return t[10]||(t[10]=[(0,o.eW)("男")])})),_:1,__:[10]}),(0,o.bF)(y,{label:"女"},{default:(0,o.k6)((function(){return t[11]||(t[11]=[(0,o.eW)("女")])})),_:1,__:[11]})]})),_:1},8,["modelValue"])]})),_:1}),(0,o.bF)(D,{label:"类型",prop:"originType"},{default:(0,o.k6)((function(){return[(0,o.bF)(I,{modelValue:p.formData.originType,"onUpdate:modelValue":t[2]||(t[2]=function(e){return p.formData.originType=e})},{default:(0,o.k6)((function(){return[(0,o.bF)(y,{label:"先天性"},{default:(0,o.k6)((function(){return t[12]||(t[12]=[(0,o.eW)("先天性")])})),_:1,__:[12]}),(0,o.bF)(y,{label:"后天性"},{default:(0,o.k6)((function(){return t[13]||(t[13]=[(0,o.eW)("后天性")])})),_:1,__:[13]})]})),_:1},8,["modelValue"])]})),_:1}),(0,o.bF)(D,{label:"病变部位",prop:"bodyPart"},{default:(0,o.k6)((function(){return[(0,o.bF)(_,{modelValue:p.formData.bodyPart,"onUpdate:modelValue":t[3]||(t[3]=function(e){return p.formData.bodyPart=e}),placeholder:"请选择病变部位",filterable:""},{default:(0,o.k6)((function(){return[((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(p.partOptions,(function(e){return(0,o.uX)(),(0,o.Wv)(S,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),(0,o.bF)(D,{label:"颜色",prop:"color"},{default:(0,o.k6)((function(){return[(0,o.bF)(_,{modelValue:p.formData.color,"onUpdate:modelValue":t[4]||(t[4]=function(e){return p.formData.color=e}),placeholder:"请选择病灶颜色",filterable:""},{default:(0,o.k6)((function(){return[((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(p.colorOptions,(function(e){return(0,o.uX)(),(0,o.Wv)(S,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),(0,o.bF)(D,{label:"血管质地",prop:"vesselTexture"},{default:(0,o.k6)((function(){return[(0,o.bF)(_,{modelValue:p.formData.vesselTexture,"onUpdate:modelValue":t[5]||(t[5]=function(e){return p.formData.vesselTexture=e}),placeholder:"请选择血管质地"},{default:(0,o.k6)((function(){return[(0,o.bF)(S,{label:"质软",value:"soft"}),(0,o.bF)(S,{label:"质韧",value:"elastic"}),(0,o.bF)(S,{label:"质硬",value:"hard"}),(0,o.bF)(S,{label:"囊性",value:"cystic"}),(0,o.bF)(S,{label:"可压缩",value:"compressible"})]})),_:1},8,["modelValue"])]})),_:1})])]),(0,o.Lk)("div",u,[(0,o.Lk)("h3",g,[t[16]||(t[16]=(0,o.eW)(" 治疗与注意事项 ")),t[17]||(t[17]=(0,o.Lk)("span",{class:"required-indicator"},"*",-1)),p.isLoadingRecommendation?((0,o.uX)(),(0,o.Wv)(A,{key:0,type:"warning",size:"small"},{default:(0,o.k6)((function(){return t[15]||(t[15]=[(0,o.Lk)("i",{class:"el-icon-loading"},null,-1),(0,o.eW)(" 正在获取AI建议... ")])})),_:1,__:[15]})):(0,o.Q3)("",!0)]),(0,o.Lk)("div",m,[(0,o.bF)(D,{label:"治疗建议 *",prop:"treatmentSuggestion"},{default:(0,o.k6)((function(){return[(0,o.bF)(k,{modelValue:p.formData.treatmentSuggestion,"onUpdate:modelValue":t[6]||(t[6]=function(e){return p.formData.treatmentSuggestion=e}),type:"textarea",rows:5,placeholder:"医生填写的最终治疗方案"},null,8,["modelValue"])]})),_:1}),(0,o.bF)(D,{label:"注意事项 *",prop:"precautions"},{default:(0,o.k6)((function(){return[(0,o.bF)(k,{modelValue:p.formData.precautions,"onUpdate:modelValue":t[7]||(t[7]=function(e){return p.formData.precautions=e}),type:"textarea",rows:5,placeholder:"需要患者注意的事项（如破溃出血处理、日常注意等）"},null,8,["modelValue"])]})),_:1})])]),(0,o.Lk)("div",d,[(0,o.bF)(w,{type:"primary",onClick:t[8]||(t[8]=function(e){return h.saveData("REVIEWED")}),loading:p.savingDraft},{default:(0,o.k6)((function(){return t[18]||(t[18]=[(0,o.eW)("保存草稿")])})),_:1,__:[18]},8,["loading"]),(0,o.bF)(w,{type:"success",onClick:h.submitForm,loading:p.submitting},{default:(0,o.k6)((function(){return t[19]||(t[19]=[(0,o.eW)("提交完成")])})),_:1,__:[19]},8,["onClick","loading"]),p.saveError?((0,o.uX)(),(0,o.Wv)(w,{key:0,type:"warning",onClick:h.retrySave},{default:(0,o.k6)((function(){return t[20]||(t[20]=[(0,o.eW)("重试保存")])})),_:1,__:[20]},8,["onClick"])):(0,o.Q3)("",!0)])]})),_:1},8,["model","rules"])]))])}var p=a(50139),h=a(55593);a(52675),a(89463),a(2259),a(16280),a(76918),a(26099),a(47764),a(62953);function v(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],a=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&a>=e.length&&(e=void 0),{value:e&&e[a++],done:!e}}}}throw new TypeError((0,h.A)(e)+" is not iterable")}var b=a(24059),D=a(698),y=a(88844),I=(a(28706),a(2008),a(51629),a(74423),a(25276),a(64346),a(48598),a(62062),a(44114),a(59089),a(1688),a(60739),a(23288),a(18111),a(22489),a(7588),a(61701),a(33110),a(79432),a(58940),a(99449),a(21699),a(11392),a(42762),a(23500),a(76031),a(66278)),S=a(36149),_={class:"dialog-content"},A={key:0,class:"dialog-icon"},k={class:"dialog-message"},w={class:"dialog-footer"};function F(e,t,a,n,r,i){var s=(0,o.g2)("el-button"),l=(0,o.g2)("el-dialog");return(0,o.uX)(),(0,o.Wv)(l,{visible:i.dialogVisible,"onUpdate:visible":t[0]||(t[0]=function(e){return i.dialogVisible=e}),title:a.title,width:a.width,"before-close":i.handleClose},{footer:(0,o.k6)((function(){return[(0,o.Lk)("span",w,[(0,o.bF)(s,{onClick:i.handleCancel},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(a.cancelText),1)]})),_:1},8,["onClick"]),(0,o.bF)(s,{type:a.confirmType,onClick:i.handleConfirm},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(a.confirmText),1)]})),_:1},8,["type","onClick"])])]})),default:(0,o.k6)((function(){return[(0,o.Lk)("div",_,[a.icon?((0,o.uX)(),(0,o.CE)("div",A,[(0,o.Lk)("i",{class:(0,o.C4)(i.iconClass)},null,2)])):(0,o.Q3)("",!0),(0,o.Lk)("div",k,[(0,o.RG)(e.$slots,"default",{},(function(){return[(0,o.eW)((0,o.v_)(a.message),1)]}),!0)])])]})),_:3},8,["visible","title","width","before-close"])}const $={name:"ConfirmDialog",props:{title:{type:String,default:"确认"},message:{type:String,default:"确定要执行此操作吗？"},confirmText:{type:String,default:"确定"},cancelText:{type:String,default:"取消"},confirmType:{type:String,default:"primary"},icon:{type:String,default:""},width:{type:String,default:"30%"},value:{type:Boolean,default:!1}},computed:{dialogVisible:{get:function(){return this.value},set:function(e){this.$emit("input",e)}},iconClass:function(){switch(this.icon){case"warning":return"el-icon-warning-outline";case"error":return"el-icon-error";case"success":return"el-icon-success";case"info":return"el-icon-info";default:return this.icon}}},methods:{handleClose:function(e){this.$emit("cancel"),e()},handleCancel:function(){this.dialogVisible=!1,this.$emit("cancel")},handleConfirm:function(){this.dialogVisible=!1,this.$emit("confirm")}}};var P=a(66262);const T=(0,P.A)($,[["render",F],["__scopeId","data-v-5546d4a6"]]),E=T;a(9868),a(68156);function C(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"";var a="string"===typeof e?new Date(e):e;if(isNaN(a.getTime()))return"";var o=a.getFullYear(),n=String(a.getMonth()+1).padStart(2,"0"),r=String(a.getDate()).padStart(2,"0"),i="".concat(o,"-").concat(n,"-").concat(r);if(t){var s=String(a.getHours()).padStart(2,"0"),l=String(a.getMinutes()).padStart(2,"0"),c=String(a.getSeconds()).padStart(2,"0");return"".concat(i," ").concat(s,":").concat(l,":").concat(c)}return i}var V=a(1573),x=a(72505),U=a.n(x);const O={name:"CaseStructuredForm",components:{ConfirmDialog:E},data:function(){return{loading:!0,imageId:null,diagnosisId:null,saveDialogVisible:!1,pollingInterval:null,isLoadingRecommendation:!1,savingDraft:!1,submitting:!1,saveError:!1,partOptions:[{value:"唇部",label:"唇部"},{value:"脑部",label:"脑部"},{value:"颈部",label:"颈部"},{value:"脸部",label:"脸部"},{value:"掌部",label:"掌部"},{value:"手臂",label:"手臂"},{value:"胸部",label:"胸部"},{value:"腹部",label:"腹部"},{value:"腿部",label:"腿部"},{value:"阴部",label:"阴部"},{value:"背部",label:"背部"},{value:"耳部",label:"耳部"},{value:"枕部",label:"枕部"},{value:"眼部",label:"眼部"},{value:"脚底",label:"脚底"},{value:"脚背",label:"脚背"},{value:"肩部",label:"肩部"},{value:"舌部",label:"舌部"},{value:"屁股",label:"屁股"},{value:"口腔",label:"口腔"},{value:"鼻部",label:"鼻部"}],colorOptions:[{value:"褐色",label:"褐色"},{value:"黑色",label:"黑色"},{value:"红色",label:"红色"},{value:"白色",label:"白色"},{value:"正常",label:"正常"},{value:"玫红",label:"玫红"}],formData:{patientAge:null,gender:"",originType:"",bodyPart:"",color:"",vesselTexture:"",treatmentSuggestion:"",precautions:""},rules:{patientAge:[{required:!1,message:"请输入患者年龄",trigger:"blur"}],gender:[{required:!1,message:"请选择性别",trigger:"change"}],originType:[{required:!1,message:"请选择类型",trigger:"change"}],bodyPart:[{required:!1,message:"请输入病变部位",trigger:"blur"}],color:[{required:!1,message:"请输入颜色",trigger:"blur"}],treatmentSuggestion:[{required:!0,message:"请填写治疗建议",trigger:"blur"}],precautions:[{required:!0,message:"请填写注意事项",trigger:"blur"}]},availableEndpoints:[]}},computed:(0,y.A)({},(0,I.L8)(["getAnnotationProgress"])),created:function(){var e=this,t=this.$route.query.diagnosisId||this.$route.params.id||this.$route.query.imageId;t?(console.log("从URL获取到诊断/图像ID:",t),this.diagnosisId=t,this.imageId=t,this.$nextTick((function(){e.checkAvailableEndpoints(t).then((function(){e.loadCaseData(t)}))}))):(this.loading=!1,this.$message.error("缺少诊断ID，无法加载病例数据。"))},methods:(0,y.A)((0,y.A)({},(0,I.i0)(["saveProgress","completeAnnotation"])),{},(0,p.A)((0,p.A)((0,p.A)((0,p.A)((0,p.A)((0,p.A)((0,p.A)((0,p.A)((0,p.A)({formatDate:C,checkAvailableEndpoints:function(e){var t=this;return(0,D.A)((0,b.A)().m((function a(){var o,n,r,i,s,l;return(0,b.A)().w((function(a){while(1)switch(a.n){case 0:console.log("检查可用的API端点..."),o=["/medical/api/hemangioma-diagnoses/".concat(e),"/medical/api/images/".concat(e,"/structured-form")],t.availableEndpoints=[],n=0,r=o;case 1:if(!(n<r.length)){a.n=9;break}return i=r[n],a.p=2,a.n=3,U().options(i);case 3:s=a.v,console.log("端点 ".concat(i," 可用，支持的方法:"),s.headers["allow"]||"GET, POST, PUT"),t.availableEndpoints.push({url:i,methods:(s.headers["allow"]||"GET, POST, PUT").split(", ")}),a.n=8;break;case 4:return a.p=4,l=a.v,a.p=5,a.n=6,U().head(i);case 6:a.v,console.log("端点 ".concat(i," 可用 (通过HEAD请求)")),t.availableEndpoints.push({url:i,methods:["GET"]}),a.n=8;break;case 7:a.p=7,a.v,console.log("端点 ".concat(i," 不可用:"),l.message);case 8:n++,a.n=1;break;case 9:console.log("可用的API端点:",t.availableEndpoints);case 10:return a.a(2)}}),a,null,[[5,7],[2,4]])})))()},loadCaseData:function(e){var t=this;return(0,D.A)((0,b.A)().m((function a(){var o,n,r,i,s,l,c,u,g,m,d,f,p,h,D,I,_;return(0,b.A)().w((function(a){while(1)switch(a.n){case 0:t.loading=!0,console.log("开始加载病例数据, 诊断ID:",e),o=Date.now(),n=!1,r={},i=JSON.parse(localStorage.getItem("user")||"{}"),s=localStorage.getItem("token"),s&&(r["Authorization"]="Bearer ".concat(s)),i&&(i.id||i.customId)&&(r["X-User-Id"]=i.id||i.customId,r["X-User-Role"]=i.role||"USER"),l=["/medical/api/hemangioma-diagnoses/".concat(e),"/medical/api/diagnoses/".concat(e),"/api/hemangioma-diagnoses/".concat(e),"/hemangioma-diagnoses/".concat(e),"/api/diagnoses/".concat(e)],c=(0,b.A)().m((function a(){var i,s,l,c,u;return(0,b.A)().w((function(a){while(1)switch(a.n){case 0:if(i=m[g],!n){a.n=1;break}return a.a(2,0);case 1:return a.p=1,console.log("尝试从路径获取数据: ".concat(i,"?t=").concat(o)),a.n=2,U().get("".concat(i,"?t=").concat(o),{headers:r});case 2:if(s=a.v,!s||!s.data){a.n=3;break}return l=s.data,console.log("成功获取诊断数据:",l),t.diagnosisId=l.id||e,c={patientAge:null,gender:"",originType:"",bodyPart:"",color:"",vesselTexture:"",treatmentSuggestion:"",precautions:""},Object.keys(c).forEach((function(e){void 0!==l[e]&&(c[e]=l[e])})),void 0!==l.patient_age&&(c.patientAge=l.patient_age),void 0!==l.vessel_texture&&(c.vesselTexture=l.vessel_texture),void 0!==l.treatment_suggestion&&(c.treatmentSuggestion=l.treatment_suggestion),void 0!==l.precautions&&(c.precautions=l.precautions),t.formData=c,t.$nextTick((function(){console.log("表单数据已更新，强制重新渲染:",t.formData),t.$forceUpdate()})),t.$message.success("病例数据已加载，请核对并完善。"),n=!0,l.treatmentSuggestion&&l.precautions||(t.isLoadingRecommendation=!0,t.startPolling(e)),a.a(2,0);case 3:a.n=5;break;case 4:a.p=4,u=a.v,console.error("从路径 ".concat(i," 加载失败:"),u);case 5:return a.a(2)}}),a,null,[[1,4]])})),g=0,m=l;case 1:if(!(g<m.length)){a.n=4;break}return a.d(v(c()),2);case 2:if(u=a.v,0!==u){a.n=3;break}return a.a(3,4);case 3:g++,a.n=1;break;case 4:if(n){a.n=8;break}return a.p=5,console.log("所有直接路径均失败，尝试使用api.get方法"),a.n=6,S["default"].get("/medical/api/hemangioma-diagnoses/".concat(e));case 6:d=a.v,d&&d.data&&(f=d.data,console.log("使用api.get成功获取数据:",f),p=(0,y.A)({},t.formData),Object.keys(p).forEach((function(e){void 0!==f[e]&&(p[e]=f[e])})),void 0!==f.patient_age&&(p.patientAge=f.patient_age),void 0!==f.vessel_texture&&(p.vesselTexture=f.vessel_texture),void 0!==f.treatment_suggestion&&(p.treatmentSuggestion=f.treatment_suggestion),void 0!==f.precautions&&(p.precautions=f.precautions),t.formData=p,t.$forceUpdate(),t.$message.success("病例数据已加载，请核对并完善。"),n=!0),a.n=8;break;case 7:a.p=7,_=a.v,console.error("使用api.get加载失败:",_);case 8:if(!n)try{h="formData_backup_".concat(e),D=localStorage.getItem(h),D&&(console.log("找到本地备份数据，尝试恢复"),I=JSON.parse(D),t.formData=I,t.$forceUpdate(),t.$message.warning("从本地备份恢复了表单数据，请注意保存"),n=!0)}catch(A){console.error("恢复本地备份失败:",A)}n||(console.error("所有数据获取尝试均失败，提供默认值"),t.provideDefaultValues(),t.$message.error("无法从服务器加载数据，已提供默认值，请手动填写。")),t.loading=!1;case 9:return a.a(2)}}),a,null,[[5,7]])})))()},submitForm:function(){var e=this;return(0,D.A)((0,b.A)().m((function t(){var a,o,n,r,i,s,l;return(0,b.A)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,t.n=1,e.$refs.caseForm.validate();case 1:if(a=["treatmentSuggestion","precautions"],o=a.filter((function(t){return!e.formData[t]})),!(o.length>0)){t.n=2;break}return n={treatmentSuggestion:"治疗建议",precautions:"注意事项"},r=o.map((function(e){return n[e]})),e.$message.warning("请填写必填项: ".concat(r.join("、"))),t.a(2);case 2:return i=JSON.parse(localStorage.getItem("user")||"{}"),s=i?i.role:null,l="ADMIN"===s||"REVIEWER"===s?"APPROVED":"SUBMITTED",t.n=3,e.saveData(l);case 3:t.n=5;break;case 4:return t.p=4,t.v,e.$message.warning("表单验证失败，请检查必填项。"),t.a(2);case 5:return t.a(2)}}),t,null,[[0,4]])})))()},saveData:function(e){var t=this;return(0,D.A)((0,b.A)().m((function a(){var o,n,r,i,s,l,c,u,g,m,d;return(0,b.A)().w((function(a){while(1)switch(a.n){case 0:if(t.diagnosisId){a.n=1;break}return t.$message.error("未找到诊断记录ID，无法保存。"),a.a(2);case 1:return"SUBMITTED"===e||"APPROVED"===e?t.submitting=!0:t.savingDraft=!0,t.saveError=!1,console.log("开始保存数据，诊断ID: ".concat(t.diagnosisId,", 状态: ").concat(e)),o=t.$loading({lock:!0,text:"正在保存..."}),n={"Content-Type":"application/json",Accept:"application/json"},r=localStorage.getItem("token"),r&&(n["Authorization"]="Bearer ".concat(r)),i=JSON.parse(localStorage.getItem("user")||"{}"),i&&(i.id||i.customId)&&(n["X-User-Id"]=i.id||i.customId,n["X-User-Role"]=i.role||"USER"),s=(0,y.A)((0,y.A)({},t.formData),{},{status:e}),l="/medical/api/hemangioma-diagnoses/".concat(t.diagnosisId),a.p=2,console.log("尝试使用 PUT 方法保存到端点: ".concat(l)),a.n=3,U().put(l,s,{headers:n});case 3:c=a.v,console.log("成功保存数据, 响应:",c.data),o.close(),t.savingDraft=!1,t.submitting=!1,localStorage.removeItem("formData_backup_".concat(t.diagnosisId)),u="APPROVED"===e?"病例信息已直接审核通过！":"SUBMITTED"===e?"病例信息已提交，等待审核！":"信息已保存并标记为已标注！",t.$message.success(u),setTimeout((function(){t.$router.push("/app/dashboard")}),1500),a.n=5;break;case 4:a.p=4,d=a.v,console.error("保存到端点 ".concat(l," 失败:"),d.response?d.response.data:d.message),o.close(),t.savingDraft=!1,t.submitting=!1,t.saveError=!0,m=(null===(g=d.response)||void 0===g||null===(g=g.data)||void 0===g?void 0:g.error)||d.message||"未知错误",t.$message.error("保存失败: ".concat(m));try{localStorage.setItem("formData_backup_".concat(t.diagnosisId),JSON.stringify(t.formData)),t.$message.warning('已在本地备份您的表单数据，可点击"重试保存"按钮再次尝试。')}catch(f){console.error("本地备份也失败:",f)}case 5:return a.a(2)}}),a,null,[[2,4]])})))()},returnToAnnotation:function(){this.$router.back()},retrySave:function(){this.saveData("REVIEWED")},getImageUrl:function(e){if(e&&e.startsWith("blob:"))return e;var t=this.imageId;if(t){var a=localStorage.getItem("offline_image_".concat(t));if(a)return console.log("表单页面：使用本地存储的离线图片"),a}return(0,V.VG)(e)},fetchImageData:function(){var e=this;if(this.imageId){var t="true"===this.$route.query.testMode,a="true"===localStorage.getItem("offlineMode");if(t||a){console.log("使用测试模式或离线模式加载数据");var o=localStorage.getItem("offline_image_".concat(this.imageId));this.imageData={id:parseInt(this.imageId),original_name:"离线图片_".concat(this.imageId,".jpg"),created_at:(new Date).toISOString(),path:o||""};var n=this.getAnnotationProgress;return n&&n.formData&&(this.formData=(0,y.A)((0,y.A)({},this.formData),n.formData),this.$message.info("已恢复之前填写的表单数据")),void(this.loaded=!0)}this.loading=!0,S["default"].images.getOne(this.imageId).then((function(t){e.imageData=t.data,e.loading=!1,e.loaded=!0,e.mapImageDataToForm(e.imageData)}))["catch"]((function(t){if(console.error("加载图像数据失败",t),"true"===localStorage.getItem("offlineMode")){console.warn("API失败但启用了离线模式，使用模拟数据");var a=localStorage.getItem("offline_image_".concat(e.imageId));a?(e.imageData={id:parseInt(e.imageId),original_name:"离线图片_".concat(e.imageId,".jpg"),created_at:(new Date).toISOString(),path:a},e.$message.warning("API请求失败，使用离线模式显示表单"),e.loaded=!0):(e.error="无法获取图像数据",e.$message.error("加载失败，无法获取图像数据"))}else{var o;e.error=(null===(o=t.response)||void 0===o?void 0:o.data)||t.message,e.$message.error("加载失败: "+e.error)}e.loading=!1}))}else this.$message.error("未指定图像ID，无法加载数据")},mergeFormData:function(e){var t=(0,y.A)({},this.formData);for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&("object"===(0,h.A)(e[a])&&null!==e[a]&&"object"===(0,h.A)(t[a])&&null!==t[a]?t[a]=this.mergeFormData(e[a]):t[a]=e[a]);return t},handleSubmit:function(){var e=this;this.$refs.caseForm.validate((function(t){if(t){e.saving=!0;var a="true"===localStorage.getItem("offlineMode");if(a){console.log("离线模式：模拟保存表单数据");try{localStorage.setItem("offline_form_".concat(e.imageId),JSON.stringify(e.formData)),e.completeAnnotation(),e.$message.success("表单已保存（离线模式）"),setTimeout((function(){e.$router.push("/app/dashboard")}),1e3)}catch(s){console.error("离线保存表单失败",s),e.$message.error("保存失败: "+s.message)}e.saving=!1}else{var o=e.$loading({lock:!0,text:"提交数据中...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.7)"}),n=(0,y.A)((0,y.A)({},e.formData),{},{action:"submit",timestamp:(new Date).toISOString()});try{var r=localStorage.getItem("user");if(r){var i=JSON.parse(r);i.role&&(n.userRole=i.role,console.log("添加用户角色到表单数据:",i.role)),i.id&&(n.userId=i.id),i.customId&&(n.userCustomId=i.customId)}}catch(s){console.error("获取用户信息失败:",s)}console.log("提交表单数据:",n),S["default"].images.saveStructuredFormData(e.imageId,n).then((function(t){o.close(),e.$message.success("表单提交成功"),e.completeAnnotation(),setTimeout((function(){e.$router.push("/app/dashboard")}),1e3)}))["catch"]((function(t){var n;o.close(),console.error("提交表单失败",t);var r=(null===(n=t.response)||void 0===n?void 0:n.data)||t.message||"未知错误";e.$message.error("提交失败: "+r),a||e.$confirm("提交到服务器失败，是否启用离线模式?","提交失败",{confirmButtonText:"启用离线模式",cancelButtonText:"再试一次",type:"warning"}).then((function(){localStorage.setItem("offlineMode","true"),e.handleSubmit()}))["catch"]((function(){e.$message.info("请稍后重试")}))}))["finally"]((function(){e.saving=!1}))}}else e.$message.warning("表单验证失败，请检查必填项")}))},prefilFormFromAnnotations:function(){},saveFormProgress:function(){this.saveProgress({step:2,imageId:this.imageId,formData:this.formData})},saveAndExit:function(){this.saveFormProgress(),this.saveDialogVisible=!0},handleSaveProgress:function(){var e=this;sessionStorage.setItem("isNavigatingAfterSave","true"),sessionStorage.setItem("allowFormOperation","true");var t=this.$loading({lock:!0,text:"保存数据中...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.7)"});try{var a=(0,y.A)((0,y.A)({},this.formData),{},{action:"save",timestamp:(new Date).toISOString()});S["default"].images.saveStructuredFormData(this.imageId,a).then((function(t){e.$message.success("表单数据已保存到数据库"),sessionStorage.setItem("navigatingFromForm","true");try{e.$router.push("/app/dashboard")}catch(a){console.error("路由导航错误，尝试使用window.location",a),window.location.href="/app/dashboard"}}))["catch"](function(){var t=(0,D.A)((0,b.A)().m((function t(a){var o;return(0,b.A)().w((function(t){while(1)switch(t.n){case 0:console.error("保存表单数据失败:",a),e.$message.error("保存失败: "+((null===(o=a.response)||void 0===o?void 0:o.data)||a.message||"未知错误"));try{localStorage.setItem("offline_form_".concat(e.imageId),JSON.stringify(e.formData)),e.$message.warning("已将表单数据保存到本地，稍后可继续填写")}catch(n){console.error("本地保存表单失败",n)}return t.p=1,t.n=2,e.$confirm("保存到服务器失败，是否仅本地保存并返回工作台？","保存失败",{confirmButtonText:"继续",cancelButtonText:"留在当前页面",type:"warning"});case 2:e.$router.push("/app/dashboard"),t.n=4;break;case 3:t.p=3,t.v,e.$message.info("请稍后重试或检查网络");case 4:return t.a(2)}}),t,null,[[1,3]])})));return function(e){return t.apply(this,arguments)}}())["finally"]((function(){t.close()}))}catch(o){console.error("处理保存进度时出错:",o),this.$message.error("保存失败: "+o.message),t.close(),window.location.href="/app/dashboard"}},handleDiscardProgress:function(){this.completeAnnotation(),this.$message.info("已清除填写进度"),this.$router.push("/cases/new")}},"returnToAnnotation",(function(){this.saveFormProgress(),this.$router.push({path:"/case/"+this.diagnosisId+"/annotate-and-form",query:{imageId:this.imageId}})})),"checkUserAuthentication",(function(){var e=JSON.parse(localStorage.getItem("user"));if(!e){var t=sessionStorage.getItem("preservedUser");t&&(console.log("尝试使用保存的用户信息恢复会话"),localStorage.setItem("user",t),sessionStorage.removeItem("preservedUser"))}})),"mapImageDataToForm",(function(e){if(console.log("原始图像数据:",e),e){var t=(0,y.A)({},e),a={lesion_location:"lesionLocation",patient_age:"patientAge",disease_stage:"diseaseStage",morphological_features:"morphologicalFeatures",blood_flow:"bloodFlow",symptom_details:"symptomDetails",complication_details:"complicationDetails",diagnosis_category:"diagnosisCategory",diagnosis_icd_code:"diagnosisIcdCode",treatment_priority:"treatmentPriority",treatment_plan:"treatmentPlan",recommended_treatment:"recommendedTreatment",follow_up_schedule:"followUpSchedule",prognosis_rating:"prognosisRating",patient_education:"patientEducation"};if(Object.keys(a).forEach((function(e){void 0!==t[e]&&void 0===t[a[e]]&&(t[a[e]]=t[e],console.log("字段映射: ".concat(e," -> ").concat(a[e])))})),t.lesionLocation)if("string"===typeof t.lesionLocation){var o=t.lesionLocation.split("/").filter((function(e){return e.trim()}));o.length>0&&(this.formData.bodyPart=o,console.log("设置病变部位(字符串分割):",this.formData.bodyPart))}else Array.isArray(t.lesionLocation)&&(this.formData.bodyPart=t.lesionLocation,console.log("设置病变部位(数组):",this.formData.bodyPart));if((!this.formData.bodyPart||0===this.formData.bodyPart.length)&&e.lesion_location&&"string"===typeof e.lesion_location){var n=e.lesion_location.split("/").filter((function(e){return e.trim()}));n.length>0&&(this.formData.bodyPart=n,console.log("设置病变部位(下划线字段):",this.formData.bodyPart))}if(null!==t.patientAge&&void 0!==t.patientAge&&(this.formData.patientAge=parseInt(t.patientAge),console.log("设置患者年龄:",this.formData.patientAge)),t.diseaseStage&&(this.formData.stage=t.diseaseStage,console.log("设置病程阶段:",this.formData.stage)),t.morphologicalFeatures){var r=t.morphologicalFeatures||"",i=r.indexOf(":");if(i>-1){var s=r.substring(0,i).split("/");this.formData.morphology=s,this.formData.morphologyDesc=r.substring(i+1).trim()}else this.formData.morphology=r.split("/").filter((function(e){return""!==e.trim()}));console.log("设置形态特征:",this.formData.morphology),console.log("设置形态描述:",this.formData.morphologyDesc)}if(t.bloodFlow&&(this.formData.bloodFlow=t.bloodFlow,console.log("设置血流信号:",this.formData.bloodFlow)),t.symptoms){var l=t.symptoms||"",c=l.indexOf(":");if(c>-1){var u=l.substring(0,c).split("/");this.formData.symptoms=u,this.formData.symptomDescription=l.substring(c+1).trim()}else this.formData.symptoms=l.split("/").filter((function(e){return""!==e.trim()}));console.log("设置症状:",this.formData.symptoms)}if(t.symptomDetails&&(this.formData.symptomDescription=t.symptomDetails,console.log("设置症状详情:",this.formData.symptomDescription)),t.complications){var g=t.complications||"";this.formData.complications=g.split("/").filter((function(e){return""!==e.trim()})),console.log("设置并发症:",this.formData.complications)}if(t.complicationDetails&&(this.formData.complicationDetails=t.complicationDetails,console.log("设置并发症详情:",this.formData.complicationDetails)),t.diagnosisCategory&&(this.formData.diagnosis=t.diagnosisCategory,console.log("设置诊断结论:",this.formData.diagnosis)),t.diagnosisIcdCode&&(this.formData.diagnosisCode=t.diagnosisIcdCode,console.log("设置ICD编码:",this.formData.diagnosisCode)),t.treatmentPriority&&(this.formData.treatmentPriority=t.treatmentPriority,console.log("设置治疗优先级:",this.formData.treatmentPriority)),t.treatmentPlan){var m=t.treatmentPlan||"";this.formData.treatmentPlan=m.split("/").filter((function(e){return""!==e.trim()})),console.log("设置治疗方案:",this.formData.treatmentPlan)}if(t.recommendedTreatment&&(this.formData.treatmentSuggestion=t.recommendedTreatment,console.log("设置治疗详情:",this.formData.treatmentSuggestion)),t.contraindications&&(this.formData.contraindications=t.contraindications,console.log("设置禁忌症:",this.formData.contraindications)),t.followUpSchedule){var d=t.followUpSchedule,f=["1个月","3个月","6个月"];f.includes(d)?this.formData.followUpPeriod=d:(this.formData.followUpPeriod="custom",this.formData.customFollowUp=d),console.log("设置随访周期:",this.formData.followUpPeriod,this.formData.customFollowUp)}if(null!==t.prognosisRating&&void 0!==t.prognosisRating&&(this.formData.prognosisRating=parseInt(t.prognosisRating),console.log("设置预后评估:",this.formData.prognosisRating)),t.patientEducation){var p=t.patientEducation||"",h=p.indexOf(":");if(h>-1){var v=p.substring(0,h).split("/");this.formData.patientEducation=v,this.formData.lifestyleAdjustment=p.substring(h+1).trim()}else this.formData.patientEducation=p.split("/").filter((function(e){return""!==e.trim()}));console.log("设置患者教育:",this.formData.patientEducation),console.log("设置教育详情:",this.formData.lifestyleAdjustment)}this.convertStringArrays(),this.$message.success("已从数据库加载保存的表单数据")}else console.warn("未找到图像数据，无法映射到表单")})),"convertStringArrays",(function(){var e=this,t=["morphology","symptoms","complications","treatmentPlan","patientEducation","bodyPart"];t.forEach((function(t){var a=e.formData[t];if(null!==a&&void 0!==a)if("string"===typeof a)try{if(a.trim().startsWith("[")&&a.trim().endsWith("]")){var o=JSON.parse(a);Array.isArray(o)&&(e.formData[t]=o,console.log("转换字段 ".concat(t," 从字符串到数组:"),o))}else if(a.includes(",")){var n=a.split(",").map((function(e){return e.trim()}));e.formData[t]=n,console.log("转换字段 ".concat(t," 从逗号分隔字符串到数组:"),n)}}catch(r){console.error("无法解析字符串数组 ".concat(t,":"),a,r),Array.isArray(e.formData[t])||(e.formData[t]=[])}else Array.isArray(e.formData[t])||(console.warn("字段 ".concat(t," 不是数组类型，设置为空数组")),e.formData[t]=[])})),console.log("处理后的表单数据:",JSON.stringify(this.formData,null,2))})),"ensureCascaderDataValid",(function(){var e=this;setTimeout((function(){if(console.log("检查级联选择器数据:",e.formData.bodyPart),e.formData.bodyPart&&e.formData.bodyPart.length>0){var t=e.$refs.bodyPartCascader;t?t.modelValue&&0!==t.modelValue.length||e.$nextTick((function(){try{var t=JSON.parse(JSON.stringify(e.formData.bodyPart));e.formData.bodyPart=[],e.$nextTick((function(){e.formData.bodyPart=t,console.log("重新设置级联选择器值:",t)}))}catch(a){console.error("重设级联选择器值时出错:",a)}})):console.log("级联选择器引用不存在，跳过重设值")}}),500)})),"startPolling",(function(e){var t=this;console.log("开始轮询获取诊断建议，ID:",e),this.pollingInterval=setInterval((function(){t.pollForResults(e)}),5e3),setTimeout((function(){t.pollingInterval&&(clearInterval(t.pollingInterval),t.pollingInterval=null,t.isLoadingRecommendation&&(t.$message.warning("获取诊断建议超时，将使用默认值。"),t.isLoadingRecommendation=!1,t.provideDefaultValues()))}),6e4)})),"pollForResults",(function(e){var t=this;return(0,D.A)((0,b.A)().m((function a(){var o,n,r,i,s;return(0,b.A)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,console.log("开始轮询端点: /medical/api/hemangioma-diagnoses/".concat(e)),a.n=1,S["default"].get("/medical/api/hemangioma-diagnoses/".concat(e));case 1:if(o=a.v,o&&o.data){a.n=2;break}return console.log("轮询返回空数据"),a.a(2);case 2:n=o.data,console.log("轮询成功，获取到数据:",n),r=n&&(n.treatmentSuggestion||n.treatment_suggestion),i=n&&(n.precautions||n.precautions),(r||i)&&(r&&(t.formData.treatmentSuggestion=n.treatmentSuggestion||n.treatment_suggestion||t.formData.treatmentSuggestion),i&&(t.formData.precautions=n.precautions||n.precautions||t.formData.precautions),t.formData.treatmentSuggestion&&t.formData.precautions&&(console.log("所有AI建议字段已填充，停止轮询"),clearInterval(t.pollingInterval),t.pollingInterval=null,t.isLoadingRecommendation=!1,t.$message.success("已获取AI生成的诊断建议"))),a.n=4;break;case 3:a.p=3,s=a.v,console.error("轮询过程中出错:",s);case 4:return a.a(2)}}),a,null,[[0,3]])})))()})),"tryNextPath",(function(e){var t=this;return(0,D.A)((0,b.A)().m((function a(){var o;return(0,b.A)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,t.$router.push({name:"CaseStructuredForm",params:{caseId:t.$route.params.caseId,stepIndex:e}});case 1:t.currentStepIndex=e,a.n=3;break;case 2:a.p=2,o=a.v,console.error("Navigation failed:",o),t.$message.error("表单步骤跳转失败");case 3:return a.a(2)}}),a,null,[[0,2]])})))()})),"provideDefaultValues",(function(){console.log("无法从API获取数据，提供默认值"),this.formData.treatmentSuggestion||(this.formData.treatmentSuggestion="根据患者情况，建议采用观察等待策略，定期复查。如有明显生长或症状加重，可考虑口服药物治疗。"),this.formData.precautions||(this.formData.precautions="保持患处清洁干燥，避免外伤和摩擦。避免剧烈运动导致的碰撞。注意保暖，避免患处受凉。"),this.$message.warning("使用默认建议值，请根据实际情况修改")}))),beforeDestroy:function(){this.pollingInterval&&(clearInterval(this.pollingInterval),this.pollingInterval=null,console.log("清理轮询定时器"))}},L=(0,P.A)(O,[["render",f],["__scopeId","data-v-819b44ee"]]),R=L}}]);
//# sourceMappingURL=556.eb2b18ef.js.map