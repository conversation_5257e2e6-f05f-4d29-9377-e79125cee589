package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.model.Tag;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.service.FileService;
import com.medical.annotation.util.FileNameGenerator;
import com.medical.annotation.util.ImagePathUtil;
import com.medical.annotation.util.ImageUtil;
import com.medical.annotation.util.TypeConverter;
import com.medical.annotation.util.BasePathConfig;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;
import javax.annotation.PostConstruct;

@RestController
@RequestMapping("/api")
public class TagApiController {

    @Autowired
    private TagRepository tagRepository;
    
    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private FileService fileService;
    
    @Autowired
    private ImagePairRepository imagePairRepository;
    
    @Autowired
    private BasePathConfig basePathConfig;
    
    private String uploadDir;
    
    @PostConstruct
    public void init() {
        uploadDir = basePathConfig.getUploadDir();
    }
    
    // 获取某个图像的标注元数据
    @GetMapping("/tags/metadata/{imageId}")
    public ResponseEntity<?> getImageTags(@PathVariable String imageId) {
        System.out.println("获取图像标注: imageId=" + imageId);
        
        try {
            // 使用实际传入的imageId，而不是固定值
            Integer metadataId;
            try {
                metadataId = Integer.parseInt(imageId);
            } catch (NumberFormatException e) {
                System.err.println("无效的图像ID格式: " + imageId);
                return ResponseEntity.badRequest().body("无效的图像ID格式，需要整数: " + imageId);
            }
            
            System.out.println("使用图像ID: " + metadataId);
            
            // 验证图像是否存在
            Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(TypeConverter.toLong(metadataId));
            if (!imageMetadataOpt.isPresent()) {
                System.err.println("图像元数据不存在: metadataId=" + metadataId);
                return ResponseEntity.ok(List.of()); // 返回空列表
            }
            
            // 直接使用repository方法查询特定图像的标注
            List<Tag> tags = tagRepository.findByMetadataId(TypeConverter.toLong(metadataId));
            
            System.out.println("找到标注数量: " + tags.size() + " 用于图像ID: " + metadataId);
            return ResponseEntity.ok(tags);
            
        } catch (Exception e) {
            System.err.println("获取标注出错: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("获取标注出错: " + e.getMessage());
        }
    }
    
    /**
     * 创建标注
     */
    @PostMapping
    public ResponseEntity<?> createTag(@RequestBody Map<String, Object> tagData) {
        try {
            // 验证必要字段
            if (!tagData.containsKey("tag") ||
                !tagData.containsKey("x") || !tagData.containsKey("y") ||
                !tagData.containsKey("width") || !tagData.containsKey("height") ||
                !tagData.containsKey("metadata_id") || !tagData.containsKey("created_by")) {
                
                return ResponseEntity.badRequest().body("缺少必要字段: 必须包含tag, x, y, width, height, metadata_id, created_by");
            }
            
            // 解析数据
            String tagName = (String) tagData.get("tag");
            if (tagName == null || tagName.trim().isEmpty()) {
                return ResponseEntity.badRequest().body("标签名称不能为空");
            }

            // 解析数值类型数据
            Integer metadataId = null;
            Integer createdBy = null;
            Double normalizedX = null;
            Double normalizedY = null;
            Double normalizedWidth = null;
            Double normalizedHeight = null;

            try {
                metadataId = convertToInteger(tagData.get("metadata_id"));
                createdBy = convertToInteger(tagData.get("created_by"));
                normalizedX = convertToDouble(tagData.get("x"));
                normalizedY = convertToDouble(tagData.get("y"));
                normalizedWidth = convertToDouble(tagData.get("width"));
                normalizedHeight = convertToDouble(tagData.get("height"));
            } catch (NumberFormatException e) {
                System.out.println("标注数据格式错误: " + e.getMessage());
                return ResponseEntity.badRequest().body("数值格式错误: " + e.getMessage());
            }
            
            // 验证图像元数据存在
            Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(TypeConverter.toLong(metadataId));
            if (!imageMetadataOpt.isPresent()) {
                System.out.println("图像元数据不存在: metadataId=" + metadataId);
                return ResponseEntity.badRequest().body("图像元数据不存在，ID: " + metadataId);
            }
            
            ImageMetadata imageMetadata = imageMetadataOpt.get();
            
            // 检查图像状态，只允许在草稿状态下进行标注
            if (imageMetadata.getStatus() != null && 
                imageMetadata.getStatus() != ImageMetadata.Status.DRAFT) {
                // 获取状态名称用于错误消息
                String statusName = imageMetadata.getStatus().toString();
                System.out.println("图像状态为" + statusName + "，不允许标注: metadataId=" + metadataId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body("不允许在" + statusName + "状态下修改标注，只能在草稿状态下进行标注");
            }
            
            // 验证用户存在并获取角色信息
            Optional<User> userOpt = userRepository.findById(createdBy);
            if (!userOpt.isPresent()) {
                System.out.println("用户不存在: userId=" + createdBy);
                return ResponseEntity.badRequest().body("用户不存在，ID: " + createdBy);
            }
            
            // 获取用户角色信息
            User user = userOpt.get();
            String userRole = user.getRole().toString();
            
            // 验证归一化坐标的有效性
            if (normalizedX < 0 || normalizedX > 1 || normalizedY < 0 || normalizedY > 1 || 
                normalizedWidth <= 0 || normalizedWidth > 1 || normalizedHeight <= 0 || normalizedHeight > 1) {
                return ResponseEntity.badRequest().body("归一化坐标必须在有效范围内: x,y应在[0,1]之间，width,height应在(0,1]之间");
            }
            
            // 创建标注对象
            Tag tag = new Tag();
            tag.setMetadataId(TypeConverter.toLong(metadataId));
            tag.setTag(tagName);
            tag.setX(normalizedX);
            tag.setY(normalizedY);
            tag.setWidth(normalizedWidth);
            tag.setHeight(normalizedHeight);
            tag.setCreatedBy(createdBy);
            
            // 保存到数据库
            Tag savedTag = tagRepository.save(tag);
            System.out.println("标注保存成功: " + savedTag.getId() + ", 关联图像ID: " + metadataId + 
                              ", 标注者: " + user.getName() + ", 角色: " + userRole);

            // 构建返回数据，包含用户角色信息
            Map<String, Object> response = new HashMap<>();
            response.put("id", savedTag.getId());
            response.put("metadata_id", savedTag.getMetadataId());
            response.put("tag", savedTag.getTag());
            response.put("x", savedTag.getX());
            response.put("y", savedTag.getY());
            response.put("width", savedTag.getWidth());
            response.put("height", savedTag.getHeight());
            response.put("created_by", savedTag.getCreatedBy());
            response.put("created_at", savedTag.getCreatedAt());
            response.put("user_role", userRole);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (Exception e) {
            System.err.println("创建标注失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("创建标注失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取图像的所有标注
     */
    @GetMapping("/tags/image/{imageId}")
    public ResponseEntity<?> getTagsByImage(
            @PathVariable String imageId,
            @RequestParam(required = false) String userId) {
        
        System.out.println("获取图像标注(整合版): imageId=" + imageId + ", userId=" + userId);
        
        try {
            // 返回空列表作为默认安全响应
            List<Map<String, Object>> result = new ArrayList<>();
            
            // 解析用户ID
            Integer userIdInt = null;
            if (userId != null && !userId.isEmpty()) {
                try {
                    userIdInt = Integer.parseInt(userId);
                    System.out.println("将过滤标注，只显示用户ID: " + userIdInt + " 的标注");
                } catch (NumberFormatException e) {
                    System.out.println("无效的用户ID格式: " + userId + ", 将返回所有标注");
                }
            }
            
            // 1. 先按格式化ID查找
            if (imageId != null && imageId.length() == 9 && imageId.startsWith("0")) {
                try {
                    System.out.println("尝试按格式化ID查找: " + imageId);
                    Optional<ImageMetadata> imageOpt = imageMetadataRepository.findByFormattedId(imageId);
                    if (imageOpt.isPresent()) {
                        ImageMetadata image = imageOpt.get();
                        System.out.println("找到图像, 数据库ID: " + image.getId());
                        List<Tag> tags = tagRepository.findByMetadataId(image.getId());
                        System.out.println("找到标签数量: " + tags.size());
                        
                        // 为每个标签补充用户信息
                        List<Map<String, Object>> enrichedTags = new ArrayList<>();
                        for (Tag tag : tags) {
                            // 如果指定了用户ID，只返回该用户的标注
                            if (userIdInt != null && !userIdInt.equals(tag.getCreatedBy())) {
                                continue;
                            }
                            
                            Map<String, Object> tagMap = new HashMap<>();
                            tagMap.put("id", tag.getId());
                            tagMap.put("metadata_id", tag.getMetadataId());
                            tagMap.put("formatted_id", image.getFormattedId());
                            tagMap.put("tag", tag.getTag());
                            tagMap.put("x", tag.getX());
                            tagMap.put("y", tag.getY());
                            tagMap.put("width", tag.getWidth());
                            tagMap.put("height", tag.getHeight());
                            tagMap.put("created_by", tag.getCreatedBy());
                            tagMap.put("created_at", tag.getCreatedAt());
                            
                            // 获取用户角色信息
                            Integer createdBy = tag.getCreatedBy();
                            if (createdBy != null) {
                                Optional<User> userOpt = userRepository.findById(createdBy);
                                if (userOpt.isPresent()) {
                                    User user = userOpt.get();
                                    tagMap.put("user_role", user.getRole().toString());
                                    tagMap.put("user_name", user.getName());
                                }
                            }
                            
                            enrichedTags.add(tagMap);
                        }
                        
                        System.out.println("过滤后返回标签数量: " + enrichedTags.size());
                        return ResponseEntity.ok(enrichedTags);
                    } else {
                        System.out.println("通过格式化ID未找到图像: " + imageId);
                    }
                } catch (Exception e) {
                    System.err.println("按格式化ID查询失败: " + e.getMessage());
                }
            }
            
            // 2. 尝试按数字ID查找
            try {
                if (imageId != null) {
                    String cleanId = imageId.replaceFirst("^0+", "");
                    if (cleanId.isEmpty()) cleanId = "0";
                    
                    Long numId = Long.parseLong(cleanId);
                    System.out.println("尝试按数字ID查找: " + numId);
                    
                    Optional<ImageMetadata> imageOpt = imageMetadataRepository.findById(numId);
                    if (imageOpt.isPresent()) {
                        ImageMetadata image = imageOpt.get();
                        List<Tag> tags = tagRepository.findByMetadataId(numId);
                        System.out.println("找到标签数量: " + tags.size());
                        
                        // 为每个标签补充用户信息
                        List<Map<String, Object>> enrichedTags = new ArrayList<>();
                        for (Tag tag : tags) {
                            // 如果指定了用户ID，只返回该用户的标注
                            if (userIdInt != null && !userIdInt.equals(tag.getCreatedBy())) {
                                continue;
                            }
                            
                            Map<String, Object> tagMap = new HashMap<>();
                            tagMap.put("id", tag.getId());
                            tagMap.put("metadata_id", tag.getMetadataId());
                            if (image.getFormattedId() != null) {
                                tagMap.put("formatted_id", image.getFormattedId());
                            }
                            tagMap.put("tag", tag.getTag());
                            tagMap.put("x", tag.getX());
                            tagMap.put("y", tag.getY());
                            tagMap.put("width", tag.getWidth());
                            tagMap.put("height", tag.getHeight());
                            tagMap.put("created_by", tag.getCreatedBy());
                            tagMap.put("created_at", tag.getCreatedAt());
                            
                            // 获取用户角色信息
                            Integer createdBy = tag.getCreatedBy();
                            if (createdBy != null) {
                                Optional<User> userOpt = userRepository.findById(createdBy);
                                if (userOpt.isPresent()) {
                                    User user = userOpt.get();
                                    tagMap.put("user_role", user.getRole().toString());
                                    tagMap.put("user_name", user.getName());
                                }
                            }
                            
                            enrichedTags.add(tagMap);
                        }
                        
                        System.out.println("过滤后返回标签数量: " + enrichedTags.size());
                        return ResponseEntity.ok(enrichedTags);
                    } else {
                        System.out.println("通过数字ID未找到图像: " + numId);
                    }
                }
            } catch (NumberFormatException e) {
                System.out.println("ID不是有效数字: " + imageId);
            } catch (Exception e) {
                System.err.println("按数字ID查询失败: " + e.getMessage());
            }
            
            // 3. 尝试按文件名查找
            try {
                Optional<ImageMetadata> metadataByFilename = imageMetadataRepository.findByFilename(imageId);
                if (metadataByFilename.isPresent()) {
                    ImageMetadata image = metadataByFilename.get();
                    List<Tag> tags = tagRepository.findByMetadataId(image.getId());
                    System.out.println("通过文件名找到图像: " + imageId + ", 标签数量: " + tags.size());
                    
                    // 为每个标签补充用户信息
                    List<Map<String, Object>> enrichedTags = new ArrayList<>();
                    for (Tag tag : tags) {
                        // 如果指定了用户ID，只返回该用户的标注
                        if (userIdInt != null && !userIdInt.equals(tag.getCreatedBy())) {
                            continue;
                        }
                        
                        Map<String, Object> tagMap = new HashMap<>();
                        tagMap.put("id", tag.getId());
                        tagMap.put("metadata_id", tag.getMetadataId());
                        if (image.getFormattedId() != null) {
                            tagMap.put("formatted_id", image.getFormattedId());
                        }
                        tagMap.put("tag", tag.getTag());
                        tagMap.put("x", tag.getX());
                        tagMap.put("y", tag.getY());
                        tagMap.put("width", tag.getWidth());
                        tagMap.put("height", tag.getHeight());
                        tagMap.put("created_by", tag.getCreatedBy());
                        tagMap.put("created_at", tag.getCreatedAt());
                        
                        // 获取用户角色信息
                        Integer createdBy = tag.getCreatedBy();
                        if (createdBy != null) {
                            Optional<User> userOpt = userRepository.findById(createdBy);
                            if (userOpt.isPresent()) {
                                User user = userOpt.get();
                                tagMap.put("user_role", user.getRole().toString());
                                tagMap.put("user_name", user.getName());
                            }
                        }
                        
                        enrichedTags.add(tagMap);
                    }
                    
                    System.out.println("过滤后返回标签数量: " + enrichedTags.size());
                    return ResponseEntity.ok(enrichedTags);
                }
            } catch (Exception e) {
                System.err.println("按文件名查询失败: " + e.getMessage());
            }
            
            // 4. 如果都找不到，返回空列表
            System.out.println("未能找到图像标签，返回空列表");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            // 捕获所有异常，确保前端能收到响应
            System.err.println("处理标签请求时发生严重错误: " + e.getMessage());
            e.printStackTrace();
            
            // 返回空数组，而不是错误状态
            return ResponseEntity.ok(new ArrayList<>());
        }
    }
    
    /**
     * 获取用户的所有标注
     */
    @GetMapping("/tags/user/{userId}")
    public ResponseEntity<?> getTagsByUser(@PathVariable Integer userId) {
        // 验证用户存在
        if (!userRepository.existsById(userId)) {
            return ResponseEntity.notFound().build();
        }
        
        try {
            List<Tag> tags = tagRepository.findByCreatedBy(userId);
            return ResponseEntity.ok(tags);
        } catch (Exception e) {
            System.err.println("获取用户标注失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Failed to get user tags: " + e.getMessage());
        }
    }
    
    /**
     * 保存带标注的图像
     */
    @PostMapping("/tags/save-annotated-image")
    public ResponseEntity<?> saveAnnotatedImage(@RequestBody Map<String, Object> requestData) {
        try {
            // 使用固定的图像元数据ID
            Integer metadataId = 1;
            
            // 获取图像元数据
            Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(TypeConverter.toLong(metadataId));
            if (!imageMetadataOpt.isPresent()) {
                return ResponseEntity.badRequest().body("Image metadata not found for ID: " + metadataId);
            }
            
            ImageMetadata imageMetadata = imageMetadataOpt.get();
            // 使用ImagePathUtil获取图像路径
            String processedImagePath = ImagePathUtil.getPath(imageMetadata);
            
            // 获取标注数据
            List<Map<String, Object>> tagsData = (List<Map<String, Object>>) requestData.get("tags");
            if (tagsData == null || tagsData.isEmpty()) {
                return ResponseEntity.badRequest().body("No tags provided for annotation");
            }
            
            // 准备标注数据
            Object[][] annotations = new Object[tagsData.size()][5];
            for (int i = 0; i < tagsData.size(); i++) {
                Map<String, Object> tag = tagsData.get(i);
                
                // 从归一化坐标转换为实际像素坐标
                double normalizedX = convertToDouble(tag.get("x"));
                double normalizedY = convertToDouble(tag.get("y"));
                double normalizedWidth = convertToDouble(tag.get("width"));
                double normalizedHeight = convertToDouble(tag.get("height"));
                
                int actualWidth = imageMetadata.getWidth();
                int actualHeight = imageMetadata.getHeight();
                
                // 计算实际像素坐标（中心点坐标转为左上角坐标）
                int x = (int) ((normalizedX - normalizedWidth / 2) * actualWidth);
                int y = (int) ((normalizedY - normalizedHeight / 2) * actualHeight);
                int width = (int) (normalizedWidth * actualWidth);
                int height = (int) (normalizedHeight * actualHeight);
                
                String label = (String) tag.get("tag");
                
                annotations[i] = new Object[]{x, y, width, height, label};
            }
            
            // 绘制标注
            BufferedImage annotatedImage = ImageUtil.drawMultipleAnnotations(processedImagePath, annotations);
            
            // 生成唯一的文件名
            String originalFilename = imageMetadata.getFilename();
            String filenameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf('.'));
            String uniqueAnnotatedFileName = FileNameGenerator.generateUniqueFileName("annotated_" + filenameWithoutExt + fileExtension);
            
            // 构造Web访问URL
            String annotatedImageWebPath = "/medical/images/processed/" + uniqueAnnotatedFileName;
            
            // 实际保存图片
            String processedDir = fileService.getProcessedDirectoryPath();
            File processedDirFile = new File(processedDir);
            if (!processedDirFile.exists()) {
                processedDirFile.mkdirs();
            }
            String annotatedFilePath = processedDir + File.separator + uniqueAnnotatedFileName;
            File annotatedFile = new File(annotatedFilePath);
            
            // 保存图片到annotatedFilePath
            ImageIO.write(annotatedImage, fileExtension.substring(1), annotatedFile);
            System.out.println("标注图像已保存: " + annotatedFilePath);
            
            // 用annotatedImageWebPath写入image_two_path
            ImagePair imagePair = new ImagePair();
            imagePair.setMetadataId(imageMetadata.getId());
            imagePair.setImageTwoPath(annotatedImageWebPath);
            imagePairRepository.save(imagePair);
            
            // 返回前端的路径和数据库一致
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "标注图像已保存");
            response.put("annotated_image_path", annotatedImageWebPath);
            response.put("image_pair_id", imagePair.getId());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("保存标注图像失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Failed to save annotated image: " + e.getMessage());
        }
    }
    
    /**
     * 标注后保存图像 - 新方法
     * 使用现有标注数据自动生成标注图像，并保存到processed目录
     */
    @PostMapping("/tags/save-image-after-annotation")
    public ResponseEntity<?> saveImageAfterAnnotation(@RequestBody Map<String, Object> requestData) {
        try {
            // 验证必要字段
            if (!requestData.containsKey("metadata_id")) {
                return ResponseEntity.badRequest().body("缺少必要字段: metadata_id");
            }
            
            // 解析元数据ID
            Integer metadataId = null;
            try {
                Object idObj = requestData.get("metadata_id");
                if (idObj instanceof Integer) {
                    metadataId = (Integer) idObj;
                } else if (idObj instanceof String) {
                    // 尝试将字符串转换为Integer
                    metadataId = Integer.valueOf((String) idObj);
                } else if (idObj instanceof Long) {
                    // 将Long转换为Integer，可能有精度损失
                    metadataId = ((Long) idObj).intValue();
                }
            } catch (Exception e) {
                System.err.println("解析metadata_id失败: " + e.getMessage());
                return ResponseEntity.badRequest().body("无效的metadata_id，无法解析为整数");
            }
            
            if (metadataId == null) {
                return ResponseEntity.badRequest().body("未找到有效的metadata_id");
            }
            
            System.out.println("处理图像标注，metadata_id: " + metadataId);
            
            // 获取图像元数据
            Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(TypeConverter.toLong(metadataId));
            if (!imageMetadataOpt.isPresent()) {
                System.err.println("图像元数据不存在: " + metadataId);
                return ResponseEntity.badRequest().body("图像不存在，ID: " + metadataId);
            }
            
            ImageMetadata imageMetadata = imageMetadataOpt.get();
            
            // 获取图像物理路径 - 使用ImagePathUtil
            String imagePath = ImagePathUtil.getPath(imageMetadata);
            String originalFilename = imageMetadata.getFilename();
            
            // 转换为物理路径
            String physicalPath = convertUrlToFilePath(imagePath);
            System.out.println("图像物理路径: " + physicalPath);
            
            // 检查物理文件是否存在
            File sourceImageFile = new File(physicalPath);
            if (!sourceImageFile.exists() || !sourceImageFile.isFile()) {
                System.err.println("源图像文件不存在: " + physicalPath);
                return ResponseEntity.badRequest().body("源图像文件不存在");
            }
            
            // 获取该图像的所有标注
            List<Tag> tags = tagRepository.findByMetadataId(TypeConverter.toLong(metadataId));
            System.out.println("找到标注数量: " + tags.size());
            
            if (tags.isEmpty()) {
                System.err.println("图像没有标注，无法生成标注后图像");
                return ResponseEntity.badRequest().body("图像没有标注，无法生成标注后图像");
            }
            
            try {
                // 读取原始图像
                BufferedImage originalImage = ImageIO.read(sourceImageFile);
                if (originalImage == null) {
                    System.err.println("读取源图像失败: " + physicalPath);
                    return ResponseEntity.badRequest().body("读取源图像失败");
                }
                
                // 在图像上绘制标注框
                BufferedImage annotatedImage = ImageUtil.drawAnnotations(originalImage, tags);
                
                // 使用FileNameGenerator生成唯一文件名
                String filenameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
                String extension = originalFilename.substring(originalFilename.lastIndexOf('.'));
                
                // 生成唯一的文件名 - 加入时间戳
                String uniqueAnnotatedFileName = FileNameGenerator.generateUniqueFileName("annotated_" + filenameWithoutExt + extension);
                
                // 确保处理后图像目录存在
                String processedDir = uploadDir + File.separator + "processed";
                File processedDirFile = new File(processedDir);
                if (!processedDirFile.exists()) {
                    processedDirFile.mkdirs();
                }
                
                // 保存标注后的图像
                String annotatedFilePath = processedDir + File.separator + uniqueAnnotatedFileName;
                File annotatedFile = new File(annotatedFilePath);
                
                // 确定文件格式
                String formatName = extension.substring(1); // 去掉点号
                ImageIO.write(annotatedImage, formatName, annotatedFile);
                
                System.out.println("标注图像已保存: " + annotatedFilePath);
                
                // 构造Web访问URL
                String annotatedImageWebPath = "/medical/images/processed/" + uniqueAnnotatedFileName;
                
                // 查找或创建 ImagePair
                List<ImagePair> existingPairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
                ImagePair imagePair;
                if (!existingPairs.isEmpty()) {
                    if (existingPairs.size() > 1) {
                        existingPairs.sort((a, b) -> {
                            if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
                            if (a.getCreatedAt() == null) return 1;
                            if (b.getCreatedAt() == null) return -1;
                            return b.getCreatedAt().compareTo(a.getCreatedAt());
                        });
                        imagePair = existingPairs.get(0);
                        for (int i = 1; i < existingPairs.size(); i++) {
                            imagePairRepository.deleteById(existingPairs.get(i).getId());
                        }
                    } else {
                        imagePair = existingPairs.get(0);
                    }
                    // ========== 新增：如果已有image_two_path，先删除本地旧图片 ==========
                    String oldWebPath = imagePair.getImageTwoPath();
                    if (oldWebPath != null && !oldWebPath.isEmpty()) {
                        String baseDir = uploadDir;
                        if (oldWebPath.startsWith("/medical/images/")) {
                            String relativePath = oldWebPath.replace("/medical/images", "").replace("/", java.io.File.separator);
                            String localPath = baseDir + relativePath;
                            java.io.File oldFile = new java.io.File(localPath);
                            if (oldFile.exists()) {
                                boolean deleted = oldFile.delete();
                                System.out.println("已删除旧标注图片: " + localPath + "，结果: " + deleted);
                            }
                        }
                    }
                } else {
                    imagePair = new ImagePair();
                    imagePair.setMetadataId(imageMetadata.getId());
                    imagePair.setDescription("原始图像ID:" + metadataId);
                    imagePair.setImageOnePath(imagePath);
                }
                imagePair.setImageTwoPath(annotatedImageWebPath);
                imagePairRepository.save(imagePair);
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "标注图像已保存");
                response.put("annotated_image_path", annotatedImageWebPath);
                response.put("image_pair_id", imagePair.getId());
                return ResponseEntity.ok(response);
                
            } catch (IOException e) {
                System.err.println("处理标注图像失败: " + e.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("处理标注图像失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("处理图像标注失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("处理图像标注失败: " + e.getMessage());
        }
    }
    
    /**
     * 将URL路径转换为物理文件路径
     */
    private String convertUrlToFilePath(String url) {
        // 如果是绝对路径，直接返回
        if (new File(url).isAbsolute()) {
            return url;
        }
        
        // 处理/tmp/路径 - 不使用系统临时目录，改用自定义临时目录
        if (url.startsWith("/tmp/")) {
            String filename = url.substring("/tmp/".length());
            // 首先尝试在原始图片目录查找
            File originalFile = new File(fileService.getOriginalDirectoryPath(), filename);
            if (originalFile.exists()) {
                System.out.println("在原始图片目录找到文件: " + originalFile.getAbsolutePath());
                return originalFile.getAbsolutePath();
            }
            // 然后尝试在标注目录查找
            File annotateFile = new File(fileService.getAnnotateDirectoryPath(), filename);
            if (annotateFile.exists()) {
                System.out.println("在标注目录找到文件: " + annotateFile.getAbsolutePath());
                return annotateFile.getAbsolutePath();
            }
            // 再尝试在temp目录查找
            File tempFile = new File(fileService.getTempDirectoryPath(), filename);
            if (tempFile.exists()) {
                System.out.println("在临时处理目录找到文件: " + tempFile.getAbsolutePath());
                return tempFile.getAbsolutePath();
            }
            // 再尝试在upload根目录查找
            File rootFile = new File(fileService.getUploadDirectoryPath(), filename);
            if (rootFile.exists()) {
                System.out.println("在上传根目录找到文件: " + rootFile.getAbsolutePath());
                return rootFile.getAbsolutePath();
            }
            // 最后使用自定义临时目录，不使用系统临时目录
            String customTempPath = fileService.getCustomTempDirectoryPath() + File.separator + filename;
            System.out.println("使用自定义临时目录: " + customTempPath);
            return customTempPath;
        }
        
        // 处理URL路径格式转换为物理路径
        if (url.startsWith("/medical/images/temp/")) {
            String filename = url.substring("/medical/images/temp/".length());
            return fileService.getTempDirectoryPath() + File.separator + filename;
        } else if (url.startsWith("/medical/images/processed/")) {
            String filename = url.substring("/medical/images/processed/".length());
            return fileService.getProcessedDirectoryPath() + File.separator + filename;
        } else if (url.startsWith("/medical/images/original/")) {
            String filename = url.substring("/medical/images/original/".length());
            return fileService.getOriginalDirectoryPath() + File.separator + filename;
        } else if (url.startsWith("/medical/images/annotate/")) {
            String filename = url.substring("/medical/images/annotate/".length());
            return fileService.getAnnotateDirectoryPath() + File.separator + filename;
        } else if (url.startsWith("/medical/images/")) {
            String filename = url.substring("/medical/images/".length());
            return fileService.getUploadDirectoryPath() + File.separator + filename;
        } else if (url.startsWith("/temp/")) {
            String filename = url.substring("/temp/".length());
            // 使用自定义临时目录代替系统临时目录
            return fileService.getCustomTempDirectoryPath() + File.separator + filename;
        }
        
        // 默认情况，尝试从当前项目根目录查找
        return new File("").getAbsolutePath() + File.separator + url;
    }
    
    /**
     * 删除标注
     */
    @DeleteMapping("/tags/{id}")
    public ResponseEntity<?> deleteTag(@PathVariable Long id) {
        try {
            // 检查标注是否存在
            Optional<Tag> tagOpt = tagRepository.findById(id);
            if (!tagOpt.isPresent()) {
                System.out.println("标注不存在: id=" + id);
                return ResponseEntity.notFound().build();
            }
            
            Tag tag = tagOpt.get();
            Long metadataId = tag.getMetadataId();
            
            // 验证图像元数据存在
            Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(metadataId);
            if (!imageMetadataOpt.isPresent()) {
                System.out.println("图像元数据不存在: metadataId=" + metadataId);
                return ResponseEntity.badRequest().body("图像元数据不存在，ID: " + metadataId);
            }
            
            // 检查图像状态，只允许在草稿状态下删除标注
            ImageMetadata imageMetadata = imageMetadataOpt.get();
            if (imageMetadata.getStatus() != null && 
                imageMetadata.getStatus() != ImageMetadata.Status.DRAFT) {
                // 获取状态名称用于错误消息
                String statusName = imageMetadata.getStatus().toString();
                System.out.println("图像状态为" + statusName + "，不允许删除标注: metadataId=" + metadataId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body("不允许在" + statusName + "状态下删除标注，只能在草稿状态下进行标注管理");
            }
            
            // 删除标注
            tagRepository.deleteById(id);
            System.out.println("标注删除成功: id=" + id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "标注已成功删除");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("删除标注失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("删除标注失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除图像的所有标注
     */
    @DeleteMapping("/tags/image/{imageId}")
    public ResponseEntity<?> deleteTagsByImage(@PathVariable Long imageId) {
        try {
            // 验证图像元数据存在
            Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(imageId);
            if (!imageMetadataOpt.isPresent()) {
                System.out.println("图像元数据不存在: metadataId=" + imageId);
                return ResponseEntity.badRequest().body("图像元数据不存在，ID: " + imageId);
            }
            
            // 检查图像状态，只允许在草稿状态下删除标注
            ImageMetadata imageMetadata = imageMetadataOpt.get();
            if (imageMetadata.getStatus() != null && 
                imageMetadata.getStatus() != ImageMetadata.Status.DRAFT) {
                // 获取状态名称用于错误消息
                String statusName = imageMetadata.getStatus().toString();
                System.out.println("图像状态为" + statusName + "，不允许删除标注: metadataId=" + imageId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body("不允许在" + statusName + "状态下删除标注，只能在草稿状态下进行标注管理");
            }
            
            // 获取该图像的所有标注
            List<Tag> tags = tagRepository.findByMetadataId(imageId);
            if (tags.isEmpty()) {
                System.out.println("图像没有标注: metadataId=" + imageId);
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "没有找到需要删除的标注",
                    "count", 0
                ));
            }
            
            // 删除所有标注
            int count = tags.size();
            tagRepository.deleteByMetadataId(imageId);
            System.out.println("成功删除图像的所有标注: metadataId=" + imageId + ", 共" + count + "个");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "已成功删除图像的所有标注");
            response.put("count", count);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("删除图像标注失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Failed to delete image tags: " + e.getMessage());
        }
    }
    
    /**
     * 更新标注
     */
    @PutMapping("/tags/{id}")
    public ResponseEntity<?> updateTag(@PathVariable Long id, @RequestBody Map<String, Object> tagData) {
        try {
            // 查找要更新的标注
            Optional<Tag> existingTagOpt = tagRepository.findById(id);
            if (!existingTagOpt.isPresent()) {
                System.out.println("标注不存在: id=" + id);
                return ResponseEntity.notFound().build();
            }
            
            Tag existingTag = existingTagOpt.get();
            Long metadataId = existingTag.getMetadataId();
            
            // 验证图像元数据存在
            Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(metadataId);
            if (!imageMetadataOpt.isPresent()) {
                System.out.println("图像元数据不存在: metadataId=" + metadataId);
                return ResponseEntity.badRequest().body("图像元数据不存在，ID: " + metadataId);
            }
            
            // 检查图像状态，允许在草稿状态和已标注状态下修改标注
            ImageMetadata imageMetadata = imageMetadataOpt.get();
            if (imageMetadata.getStatus() != null && 
                imageMetadata.getStatus() != ImageMetadata.Status.DRAFT &&
                imageMetadata.getStatus() != ImageMetadata.Status.REVIEWED) {
                // 获取状态名称用于错误消息
                String statusName = imageMetadata.getStatus().toString();
                System.out.println("图像状态为" + statusName + "，不允许修改标注: metadataId=" + metadataId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body("不允许在" + statusName + "状态下修改标注，只能在草稿或已标注状态下进行标注");
            }
            
            // 更新标注字段
            if (tagData.containsKey("tag")) {
                String tagName = (String) tagData.get("tag");
                if (tagName != null && !tagName.trim().isEmpty()) {
                    existingTag.setTag(tagName);
                }
            }
            
            // 更新坐标和尺寸
            if (tagData.containsKey("x")) {
                Double x = convertToDouble(tagData.get("x"));
                if (x != null && x >= 0 && x <= 1) {
                    existingTag.setX(x);
                }
            }
            
            if (tagData.containsKey("y")) {
                Double y = convertToDouble(tagData.get("y"));
                if (y != null && y >= 0 && y <= 1) {
                    existingTag.setY(y);
                }
            }
            
            if (tagData.containsKey("width")) {
                Double width = convertToDouble(tagData.get("width"));
                if (width != null && width > 0 && width <= 1) {
                    existingTag.setWidth(width);
                }
            }
            
            if (tagData.containsKey("height")) {
                Double height = convertToDouble(tagData.get("height"));
                if (height != null && height > 0 && height <= 1) {
                    existingTag.setHeight(height);
                }
            }
            
            // 保存更新后的标注
            Tag updatedTag = tagRepository.save(existingTag);
            System.out.println("标注更新成功: id=" + id);
            
            return ResponseEntity.ok(updatedTag);
        } catch (Exception e) {
            System.err.println("更新标注失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Failed to update tag: " + e.getMessage());
        }
    }
    
    /**
     * 工具方法：转换为Integer
     */
    private Integer convertToInteger(Object value) {
        if (value == null) {
            return null;
        }
        
        System.out.println("正在转换值为Integer: " + value + ", 类型: " + value.getClass().getName());
        
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof String) {
            try {
                // 尝试直接转换
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                // 如果无法直接转换，使用默认ID
                System.out.println("无法解析String为Integer: " + value + ", 使用默认ID 1");
                return 1;
            }
        }
        if (value instanceof Long) {
            Long longValue = (Long) value;
            // 使用固定ID，避免溢出问题
            return 1;
        }
        if (value instanceof Double) {
            return ((Double) value).intValue();
        }
        
        // 如果无法转换，使用默认ID
        System.out.println("无法转换为Integer: " + value + ", 使用默认ID 1");
        return 1;
    }
    
    /**
     * 工具方法：转换为Double
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Double) {
            return (Double) value;
        }
        if (value instanceof String) {
            return Double.parseDouble((String) value);
        }
        if (value instanceof Integer) {
            return ((Integer) value).doubleValue();
        }
        if (value instanceof Long) {
            return ((Long) value).doubleValue();
        }
        throw new NumberFormatException("Cannot convert to Double: " + value);
    }

    /**
     * 保存带标注的图像到本地指定目录
     */
    @PostMapping("/tags/save-annotated-image-to-local")
    public ResponseEntity<?> saveAnnotatedImageToLocal(@RequestBody Map<String, Object> requestData) {
        try {
            // 验证请求数据
            if (!requestData.containsKey("metadata_id") || !requestData.containsKey("image_data")) {
                return ResponseEntity.badRequest().body("缺少必要参数: metadata_id和image_data");
            }
            
            // 获取元数据ID
            Integer metadataId = convertToInteger(requestData.get("metadata_id"));
            if (metadataId == null) {
                return ResponseEntity.badRequest().body("无效的metadata_id");
            }
            
            // 获取图像数据
            String imageData = (String) requestData.get("image_data");
            if (imageData == null || !imageData.startsWith("data:image")) {
                return ResponseEntity.badRequest().body("无效的image_data，应为base64编码的图像数据");
            }
            
            // 验证图像元数据存在
            Optional<ImageMetadata> imageMetadataOpt = imageMetadataRepository.findById(TypeConverter.toLong(metadataId));
            if (!imageMetadataOpt.isPresent()) {
                System.err.println("图像元数据不存在: metadataId=" + metadataId);
                return ResponseEntity.badRequest().body("图像元数据不存在，ID: " + metadataId);
            }
            
            ImageMetadata imageMetadata = imageMetadataOpt.get();
            
            // 绘制标注框
            BufferedImage annotatedImage = null;
            
            if (imageData.isEmpty()) {
                // 如果没有现有标注，使用传入的标注数据
                annotatedImage = ImageUtil.decodeBase64Image(imageData);
            } else {
                // 使用已存在的标注绘制
                annotatedImage = ImageUtil.decodeBase64Image(imageData);
            }
            
            if (annotatedImage == null) {
                return ResponseEntity.badRequest().body("无法解码图像数据");
            }
            
            // 生成输出路径和文件名
            String originalFilename = imageMetadata.getFilename();
            String filenameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
            String extension = originalFilename.substring(originalFilename.lastIndexOf('.'));
            String uniqueFilename = FileNameGenerator.generateUniqueFileName("annotated_" + filenameWithoutExt + extension);
            
            // 保存图像到processed目录
            String processedDir = fileService.getProcessedDirectoryPath();
            String outputPath = processedDir + File.separator + uniqueFilename;
            
            // 保存图像到processed目录
            boolean saved = ImageUtil.saveImage(annotatedImage, outputPath, "jpg");
            
            if (!saved) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("保存图像失败");
            }
            
            System.out.println("带标注的图像已保存到: " + outputPath);
            
            // 构造Web访问URL
            String annotatedImageWebPath = "/medical/images/processed/" + uniqueFilename;
            
            // 先查找是否已存在ImagePair记录
            List<ImagePair> existingPairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
            
            if (existingPairs.isEmpty()) {
                // 如果不存在记录，返回错误提示
                System.err.println("未找到与图像ID关联的ImagePair记录: " + imageMetadata.getId());
                
                // 删除刚才保存的文件，因为没有对应的记录
                try {
                    Files.deleteIfExists(Paths.get(outputPath));
                    System.out.println("已删除未使用的标注图像文件: " + outputPath);
                } catch (Exception e) {
                    System.err.println("删除未使用的标注图像文件失败: " + e.getMessage());
                }
                
                return ResponseEntity.badRequest().body("未找到与该图像关联的原始记录，请先上传原始图像");
            }
            
            // 如果存在多条记录，进行清理
            ImagePair imagePair;
            if (existingPairs.size() > 1) {
                System.out.println("警告：发现重复的ImagePair记录，共 " + existingPairs.size() + " 条，进行清理...");
                
                // 按创建时间降序排序，保留最新的一条
                existingPairs.sort((a, b) -> {
                    if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
                    if (a.getCreatedAt() == null) return 1;
                    if (b.getCreatedAt() == null) return -1;
                    return b.getCreatedAt().compareTo(a.getCreatedAt());
                });
                
                // 获取最新的记录
                imagePair = existingPairs.get(0);
                System.out.println("保留最新记录，ID: " + imagePair.getId() + ", 创建时间: " + imagePair.getCreatedAt());
                
                // 删除其他记录
                for (int i = 1; i < existingPairs.size(); i++) {
                    ImagePair pair = existingPairs.get(i);
                    System.out.println("删除重复记录，ID: " + pair.getId() + ", 创建时间: " + pair.getCreatedAt());
                    imagePairRepository.deleteById(pair.getId());
                }
            } else {
                // 使用现有记录
                imagePair = existingPairs.get(0);
                System.out.println("使用现有ImagePair记录: ID=" + imagePair.getId());
            }
            
            // 设置标注后的图像路径 - 使用Web访问路径
            imagePair.setImageTwoPath(annotatedImageWebPath);
            
            // 保存ImagePair记录
            imagePairRepository.save(imagePair);
            
            System.out.println("已更新image_pairs表中的image_two_path字段: " + annotatedImageWebPath);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "标注图像已保存");
            response.put("annotated_image_path", annotatedImageWebPath);
            response.put("image_pair_id", imagePair.getId());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("保存带标注的图像失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("保存带标注的图像失败: " + e.getMessage());
        }
    }

    /**
     * 更新标注完成后的图像到文件系统 - 使用已有的image_pair_id
     */
    @PostMapping("/tags/update-image-after-annotation")
    public ResponseEntity<?> updateImageAfterAnnotation(@RequestBody Map<String, Object> requestData) {
        try {
            // 验证必要字段
            if (!requestData.containsKey("metadata_id")) {
                return ResponseEntity.badRequest().body("缺少必要字段: metadata_id");
            }
            
            // 解析元数据ID
            Long metadataId = null;
            try {
                Object idObj = requestData.get("metadata_id");
                if (idObj instanceof Integer) {
                    metadataId = ((Integer) idObj).longValue();
                } else if (idObj instanceof String) {
                    metadataId = Long.parseLong((String) idObj);
                } else if (idObj instanceof Long) {
                    metadataId = (Long) idObj;
                }
            } catch (NumberFormatException e) {
                return ResponseEntity.badRequest().body("无效的metadata_id格式");
            }
            
            // 检查ID是否有效
            if (metadataId == null) {
                return ResponseEntity.badRequest().body("无效的metadata_id");
            }
            
            // 获取当前认证用户
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = null;
            if (auth != null && auth.getPrincipal() instanceof User) {
                currentUser = (User) auth.getPrincipal();
                System.out.println("当前认证用户: " + currentUser.getName() + ", ID: " + currentUser.getId());
            } else {
                System.out.println("未找到当前认证用户，尝试从请求中获取用户信息");
                // 尝试从请求中获取用户ID
                if (requestData.containsKey("userId")) {
                    Integer userId = null;
                    try {
                        Object userIdObj = requestData.get("userId");
                        if (userIdObj instanceof Integer) {
                            userId = (Integer) userIdObj;
                        } else if (userIdObj instanceof String) {
                            userId = Integer.parseInt((String) userIdObj);
                        }
                        
                        if (userId != null) {
                            Optional<User> userOpt = userRepository.findById(userId);
                            if (userOpt.isPresent()) {
                                currentUser = userOpt.get();
                                System.out.println("从请求中获取用户: " + currentUser.getName() + ", ID: " + currentUser.getId());
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("解析用户ID失败: " + e.getMessage());
                    }
                }
            }
            
            // 获取对应的metadata记录
            Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findById(metadataId);
            if (!metadataOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            ImageMetadata imageMetadata = metadataOpt.get();
            
            // 查找现有的ImagePair记录
            List<ImagePair> existingPairs = imagePairRepository.findByMetadataId(metadataId);
            
            // 如果找到多个记录，检测重复并清理
            if (existingPairs.size() > 1) {
                System.out.println("发现多个ImagePair记录，进行清理...");
                existingPairs = cleanupDuplicateImagePairs(existingPairs);
            }
            
            ImagePair imagePair = null;
            if (!existingPairs.isEmpty()) {
                imagePair = existingPairs.get(0);
            } else {
                // 创建新的ImagePair记录
                imagePair = new ImagePair();
                imagePair.setMetadataId(metadataId);
                imagePair.setImageOnePath(imageMetadata.getPath());
                
                // 设置创建者
                if (currentUser != null) {
                    imagePair.setCreatedBy(currentUser.getId());
                    System.out.println("设置ImagePair创建者ID: " + currentUser.getId());
                } else if (imageMetadata.getUploadedBy() != null) {
                    imagePair.setCreatedBy(imageMetadata.getUploadedBy().getId());
                    System.out.println("使用图像上传者作为ImagePair创建者ID: " + imageMetadata.getUploadedBy().getId());
                }
                
                imagePair.setCreatedAt(LocalDateTime.now());
                imagePair = imagePairRepository.save(imagePair);
                System.out.println("未找到ImagePair记录，已创建新记录，ID: " + imagePair.getId());
            }
            
            // 设置标注后的图像路径
            if (requestData.containsKey("annotated_image_path")) {
                String annotatedImagePath = (String) requestData.get("annotated_image_path");
                if (annotatedImagePath != null && !annotatedImagePath.isEmpty()) {
                    imagePair.setImageTwoPath(annotatedImagePath);
                    System.out.println("设置标注图像路径: " + annotatedImagePath);
                }
            } else {
                // 如果没有提供标注图像路径，尝试生成标注图像
                try {
                    // 获取图像URL
                    String imageUrl = ImagePathUtil.getPath(imageMetadata);
                    String processedImagePath = convertUrlToFilePath(imageUrl);
                    
                    // 获取标注数据
                    List<Tag> tags = tagRepository.findByMetadataId(metadataId);
                    if (!tags.isEmpty()) {
                        // 读取原始图像
                        BufferedImage actualImage = ImageIO.read(new File(processedImagePath));
                        
                        // 准备标注数据
                        Object[][] annotations = new Object[tags.size()][5];
                        for (int i = 0; i < tags.size(); i++) {
                            Tag tag = tags.get(i);
                            
                            // 转换标注坐标
                            double normalizedX = tag.getX() != null ? tag.getX() : 0.5;
                            double normalizedY = tag.getY() != null ? tag.getY() : 0.5;
                            double normalizedWidth = tag.getWidth() != null ? tag.getWidth() : 0.3;
                            double normalizedHeight = tag.getHeight() != null ? tag.getHeight() : 0.3;
                            String label = tag.getTag() != null ? tag.getTag() : "标注";
                            
                            // 计算实际像素坐标
                            int actualWidth = actualImage.getWidth();
                            int actualHeight = actualImage.getHeight();
                            
                            // 将归一化坐标转换为像素坐标
                            int x = (int) (normalizedX * actualWidth);
                            int y = (int) (normalizedY * actualHeight);
                            int width = (int) (normalizedWidth * actualWidth);
                            int height = (int) (normalizedHeight * actualHeight);
                            
                            annotations[i] = new Object[]{x, y, width, height, label};
                        }
                        
                        // 绘制标注
                        BufferedImage annotatedImage = ImageUtil.drawMultipleAnnotations(processedImagePath, annotations);
                        
                        // 生成唯一的文件名
                        String originalFilename = imageMetadata.getFilename();
                        String filenameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
                        String extension = originalFilename.substring(originalFilename.lastIndexOf('.'));
                        String uniqueFilename = FileNameGenerator.generateUniqueFileName("annotated_" + filenameWithoutExt + extension);
                        
                        // 保存标注后的图像
                        String annotatedFilePath = fileService.saveAnnotatedImageWithName(processedImagePath, annotatedImage, uniqueFilename, false);
                        System.out.println("自动生成标注图像路径: " + annotatedFilePath);
                        
                        // 设置路径
                        imagePair.setImageTwoPath(annotatedFilePath);
                    } else {
                        System.out.println("没有找到标注数据，无法生成标注图像");
                    }
                } catch (Exception e) {
                    System.err.println("生成标注图像失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }
            
            // 更新图像状态为REVIEWED（已标注）
            if (requestData.containsKey("update_status") && Boolean.TRUE.equals(requestData.get("update_status"))) {
                imageMetadata.setStatus(ImageMetadata.Status.REVIEWED);
                imageMetadataRepository.save(imageMetadata);
                System.out.println("已更新图像状态为REVIEWED");
            }
            
            // 保存ImagePair记录
            imagePairRepository.save(imagePair);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("annotated_image_path", imageMetadata.getPath());
            response.put("message", "图像标注已更新成功");
            response.put("image_pair_id", imagePair.getId());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("更新标注图像失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Failed to update annotated image: " + e.getMessage());
        }
    }

    // 添加一个新的端点使用查询参数获取标注
    @GetMapping("/tags/image_query")
    public ResponseEntity<?> getTagsByImageQuery(@RequestParam("id") String imageId) {
        System.out.println("通过查询参数获取图像标注: id=" + imageId);
        
        try {
            // 解析图像ID为Integer类型
            Long metadataId;
            try {
                // 如果ID太长，截取后9位数字
                if (imageId.length() > 9) {
                    String shortened = imageId.substring(imageId.length() - 9);
                    metadataId = Long.valueOf(shortened);
                    System.out.println("长ID截取后9位转为Long: " + metadataId);
                } else {
                    metadataId = Long.valueOf(imageId);
                }
            } catch (NumberFormatException e) {
                System.out.println("无法解析图像ID为数字类型: " + e.getMessage());
                // 尝试通过文件名查找
                Optional<ImageMetadata> metadata = imageMetadataRepository.findByFilename(imageId);
                if (metadata.isPresent()) {
                    metadataId = metadata.get().getId();
                    System.out.println("通过文件名找到图像ID: " + metadataId);
                } else {
                    System.err.println("无法找到匹配的图像记录");
                    return ResponseEntity.ok(List.of()); // 返回空列表而不是404
                }
            }
            
            // 验证图像存在
            if (!imageMetadataRepository.existsById(metadataId)) {
                System.err.println("图像不存在，ID: " + metadataId);
                return ResponseEntity.ok(List.of()); // 返回空列表而不是404
            }
            
            System.out.println("获取图像标注: id=" + metadataId);
            List<Tag> tags = tagRepository.findByMetadataId(metadataId);
            System.out.println("找到标注数量: " + tags.size() + " 用于图像ID: " + metadataId);
            
            // 为每个标注添加用户角色信息
            List<Map<String, Object>> enrichedTags = new ArrayList<>();
            for (Tag tag : tags) {
                Map<String, Object> tagMap = new HashMap<>();
                tagMap.put("id", tag.getId());
                tagMap.put("metadata_id", tag.getMetadataId());
                tagMap.put("tag", tag.getTag());
                tagMap.put("x", tag.getX());
                tagMap.put("y", tag.getY());
                tagMap.put("width", tag.getWidth());
                tagMap.put("height", tag.getHeight());
                tagMap.put("created_by", tag.getCreatedBy());
                tagMap.put("created_at", tag.getCreatedAt());
                
                // 获取用户角色信息
                Integer createdBy = tag.getCreatedBy();
                if (createdBy != null) {
                    Optional<User> userOpt = userRepository.findById(createdBy);
                    if (userOpt.isPresent()) {
                        User user = userOpt.get();
                        tagMap.put("user_role", user.getRole().toString());
                        tagMap.put("user_name", user.getName());
                    }
                }
                
                enrichedTags.add(tagMap);
            }
            
            return ResponseEntity.ok(enrichedTags);
        } catch (Exception e) {
            System.err.println("获取图像标注失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("获取图像标注失败: " + e.getMessage());
        }
    }

    /**
     * 清理重复的ImagePair记录，保留最新的一条
     * @param pairs 可能包含重复的ImagePair列表
     * @return 清理后的ImagePair列表，只包含最新的一条记录
     */
    private List<ImagePair> cleanupDuplicateImagePairs(List<ImagePair> pairs) {
        if (pairs == null || pairs.size() <= 1) {
            return pairs;
        }
        
        System.out.println("清理重复的ImagePair记录，共 " + pairs.size() + " 条");
        
        // 按创建时间降序排序，保留最新的一条
        pairs.sort((a, b) -> {
            if (a.getCreatedAt() == null && b.getCreatedAt() == null) return 0;
            if (a.getCreatedAt() == null) return 1;
            if (b.getCreatedAt() == null) return -1;
            return b.getCreatedAt().compareTo(a.getCreatedAt());
        });
        
        // 获取最新的记录
        ImagePair latestPair = pairs.get(0);
        System.out.println("保留最新记录，ID: " + latestPair.getId() + ", 创建时间: " + latestPair.getCreatedAt());
        
        // 删除其他记录
        for (int i = 1; i < pairs.size(); i++) {
            ImagePair pair = pairs.get(i);
            System.out.println("删除重复记录，ID: " + pair.getId() + ", 创建时间: " + pair.getCreatedAt());
            imagePairRepository.deleteById(pair.getId());
        }
        
        // 返回包含最新记录的列表
        List<ImagePair> result = new ArrayList<>();
        result.add(latestPair);
        return result;
    }
} 