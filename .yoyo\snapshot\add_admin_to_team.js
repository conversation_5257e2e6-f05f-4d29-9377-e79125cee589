// A simple script to add the admin user (ID 1) to the admin team (ID 1)
// This script uses the REST API directly

const fetch = require('node-fetch');

async function addAdminToTeam() {
    console.log('Adding admin user to admin team...');
    
    try {
        // Admin user ID is 1, Admin team ID is 1
        const teamId = 1;
        const userId = 1;
        
        // Call the API to add the user to the team
        const response = await fetch(`http://localhost:8080/medical/api/teams/${teamId}/members`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ userId: userId }),
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to add user to team: ${response.status} ${response.statusText}\n${errorText}`);
        }
        
        const result = await response.json();
        console.log('Success!', result);
        
        // Also update the user's department field directly via SQL
        console.log('Note: You may need to manually update the department field in the users table:');
        console.log('UPDATE users SET department = "管理员团队" WHERE id = 1;');
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

addAdminToTeam(); 