/**
 * 提供与外部路由相关的工具函数
 */

/**
 * 获取独立审核页面URL
 * @param {Object} options 配置选项
 * @param {boolean} options.absolute 是否返回绝对URL (默认: false)
 * @returns {string} 审核页面URL
 */
export function getStandaloneReviewUrl(options = {}) {
  const path = '/review-standalone';
  
  if (options.absolute) {
    // 获取当前域名和协议
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}${path}`;
  }
  
  return path;
}

/**
 * 在新窗口中打开独立审核页面
 */
export function openStandaloneReview() {
  const url = getStandaloneReviewUrl();
  // 在同一窗口打开，而不是新窗口
  window.location.href = url;
}

export default {
  getStandaloneReviewUrl,
  openStandaloneReview
}; 