package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.service.FileService;
import com.medical.annotation.util.FileNameGenerator;
import com.medical.annotation.util.DatabaseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.File;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 直接文件上传控制器
 * 使用完全匹配的路径处理前端请求
 */
@RestController
@Primary
public class DirectFileUploadController {

    @Autowired
    private FileService fileService;
    
    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    @Autowired
    private ImagePairRepository imagePairRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Value("${app.upload.dir:F:/血管瘤辅助系统/medical_images}")
    private String uploadBaseDir;

    /**
     * 直接处理/medical/medical/api/files/upload路径
     */
    @RequestMapping(value = "/medical/medical/api/files/upload", method = RequestMethod.POST)
    public ResponseEntity<?> handleDirectUpload(@RequestParam("file") MultipartFile file,
                                              @RequestParam(value = "originalFilename", required = false) String originalFilename,
                                              @RequestParam(value = "targetPath", required = false) String targetPath,
                                              HttpServletRequest request) {
        System.out.println("直接文件上传控制器处理请求: " + request.getRequestURI());
        System.out.println("原始文件名: " + file.getOriginalFilename());
        System.out.println("提供的文件名: " + originalFilename);
        System.out.println("目标路径: " + targetPath);
        
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body("请选择文件");
            }
            
            // 处理用户提供的目标路径
            String actualTargetPath = targetPath;
            if (actualTargetPath == null || actualTargetPath.isEmpty()) {
                actualTargetPath = "medical_images/temp";
            }
            
            // 确保路径格式正确
            if (!actualTargetPath.startsWith("/") && !actualTargetPath.contains(":")) {
                // 相对路径 - 添加根目录前缀
                actualTargetPath = uploadBaseDir + File.separator + actualTargetPath;
            }
            
            // 确保目录存在
            File targetDir = new File(actualTargetPath);
            if (!targetDir.exists()) {
                boolean created = targetDir.mkdirs();
                System.out.println("创建目录: " + actualTargetPath + " - " + (created ? "成功" : "失败"));
            }
            
            // 确定文件名
            String finalFilename = originalFilename != null ? originalFilename : file.getOriginalFilename();
            String uniqueFilename = FileNameGenerator.generateUniqueFileName(finalFilename);
            
            // 文件保存路径
            File destFile = new File(targetDir, uniqueFilename);
            
            // 保存文件
            file.transferTo(destFile);
            System.out.println("文件已保存到: " + destFile.getAbsolutePath());
            
            // 构建Web访问路径
            String webPath = "/medical/images/temp/" + uniqueFilename;
            
            // 获取当前用户ID
            Integer currentUserId = getCurrentUserId(request);
            System.out.println("当前用户ID: " + currentUserId);
            
            // ===== 创建数据库记录 =====
            
            // 1. 创建ImageMetadata记录
            ImageMetadata metadata = new ImageMetadata();
            
            // 生成9位格式化ID
            Long uniqueId = DatabaseUtil.generateUniqueLongId();
            String formattedId = DatabaseUtil.formatIdToNineDigits(uniqueId);
            System.out.println("生成的唯一ID: " + uniqueId + ", 格式化ID: " + formattedId);
            
            metadata.setId(uniqueId);
            metadata.setFormattedId(formattedId);
            metadata.setFilename(uniqueFilename);
            metadata.setOriginalName(finalFilename);
            metadata.setPath(webPath);
            metadata.setMimetype(file.getContentType());
            metadata.setSize((int)file.getSize());
            metadata.setCreatedAt(LocalDateTime.now());
            
            // 读取图像尺寸
            try {
                BufferedImage bufferedImage = ImageIO.read(destFile);
                if (bufferedImage != null) {
                    metadata.setWidth(bufferedImage.getWidth());
                    metadata.setHeight(bufferedImage.getHeight());
                    System.out.println("图像尺寸: " + bufferedImage.getWidth() + "x" + bufferedImage.getHeight());
                }
            } catch (Exception e) {
                System.err.println("无法获取图像尺寸: " + e.getMessage());
            }
            
            // 设置上传用户
            Optional<User> userOpt = userRepository.findById(currentUserId);
            if (userOpt.isPresent()) {
                metadata.setUploadedBy(userOpt.get());
                metadata.setUploadedByCustomId(userOpt.get().getCustomId());
                System.out.println("设置上传用户: " + userOpt.get().getName() + " (ID: " + currentUserId + ")");
            } else {
                // 如果找不到指定用户，尝试使用默认用户
                Optional<User> defaultUser = userRepository.findById(1);
                if (defaultUser.isPresent()) {
                    metadata.setUploadedBy(defaultUser.get());
                    metadata.setUploadedByCustomId(defaultUser.get().getCustomId());
                    System.out.println("使用默认用户: " + defaultUser.get().getName());
                } else {
                    System.err.println("找不到有效用户，无法完成上传");
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("找不到有效用户，无法完成上传");
                }
            }
            
            // 设置状态为草稿
            metadata.setStatus(ImageMetadata.Status.DRAFT);
            
            // 保存元数据记录
            ImageMetadata savedMetadata = imageMetadataRepository.save(metadata);
            System.out.println("已创建图像元数据记录: ID=" + savedMetadata.getId() + ", 格式化ID=" + savedMetadata.getFormattedId());
            
            // 2. 创建ImagePair记录
            ImagePair imagePair = new ImagePair();
            imagePair.setMetadataId(savedMetadata.getId());
            imagePair.setMetadata(savedMetadata);
            imagePair.setImageOnePath(webPath);
            imagePair.setImageTwoPath(null);
            imagePair.setDescription("直接上传的图像");
            imagePair.setCreatedBy(currentUserId);
            imagePair.setCreatedAt(LocalDateTime.now());
            
            // 保存ImagePair记录
            ImagePair savedPair = imagePairRepository.save(imagePair);
            System.out.println("已创建ImagePair记录: ID=" + savedPair.getId());
            
            // 构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "文件上传成功");
            result.put("filename", uniqueFilename);
            result.put("original_name", finalFilename);
            result.put("path", webPath);
            result.put("physical_path", destFile.getAbsolutePath());
            result.put("size", file.getSize());
            result.put("id", savedMetadata.getId());
            result.put("metadataId", savedMetadata.getFormattedId());
            result.put("formattedId", savedMetadata.getFormattedId());
            result.put("imagePairId", savedPair.getId());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            System.err.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "文件上传失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 获取当前用户ID
     */
    private Integer getCurrentUserId(HttpServletRequest request) {
        // 尝试从请求头获取用户ID
        Integer userId = null;
        String userIdHeader = request.getHeader("X-User-Id");
        if (userIdHeader != null && !userIdHeader.isEmpty()) {
            try {
                userId = Integer.parseInt(userIdHeader);
                System.out.println("从请求头获取用户ID: " + userId);
            } catch (NumberFormatException e) {
                System.out.println("无法解析请求头中的用户ID: " + userIdHeader);
            }
        }
        
        // 如果仍未获取到用户ID，尝试从认证信息获取
        if (userId == null) {
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                // 这里可以解析JWT令牌获取用户信息
                System.out.println("检测到Bearer认证头");
            }
        }
        
        // 如果仍未获取到用户ID，使用默认值
        if (userId == null) {
            userId = 2; // 默认用户ID
            System.out.println("使用默认用户ID: " + userId);
        }
        
        return userId;
    }
} 