package com.medical.annotation.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;

@Service
public class FileService {

    // 使用@Qualifier注解指定要注入的Bean名称
    @Autowired
    @Qualifier("getUploadDir")
    private String uploadDir;
    
    @Autowired
    @Qualifier("getProcessedDir")
    private String processedDir;
    
    @Autowired
    @Qualifier("getAnnotatedDir")
    private String annotatedDir;
    
    @Autowired
    @Qualifier("getTempImagesDir")
    private String tempImagesDir;
    
    // 自定义临时目录，避免使用C盘
    private final String CUSTOM_TEMP_DIR = "F:/xueguan";

    // 原始图片目录
    private final String ORIGINAL_DIR = "original";
    // 临时处理图片目录
    private final String TEMP_DIR = "temp";
    // 最终处理后带标注的图片目录
    private final String PROCESSED_DIR = "processed";
    // 标注图片目录
    private final String ANNOTATE_DIR = "annotate";

    // 添加的替代根目录路径，用于修复找不到图片的问题
    private String[] ALTERNATE_ROOT_DIRS;
    
    // 构造函数初始化替代目录
    public FileService() {
        // 替代目录会在postConstruct方法中被初始化
    }
    
    @javax.annotation.PostConstruct
    public void initAlternateRootDirs() {
        ALTERNATE_ROOT_DIRS = new String[] {
            uploadDir,
            "F:/blood-vessel-tumor-annotation/medical_images",
            "F:/medical/images", 
            "F:/医学辅助系统/medical_images",
            "F:/血管瘤辅助系统/medical_images",
            "F:/xueguan/medical_images"
        };
    }

    /**
     * 初始化所有需要的目录
     */
    public void init() throws IOException {
        // 确保根目录存在
        createDirectoryIfNotExists(uploadDir);
        
        // 创建子目录
        createDirectoryIfNotExists(getOriginalDirectoryPath());
        createDirectoryIfNotExists(getTempDirectoryPath());
        createDirectoryIfNotExists(getProcessedDirectoryPath());
        createDirectoryIfNotExists(getAnnotateDirectoryPath());
        
        System.out.println("目录初始化完成: " + uploadDir);
    }

    private void createDirectoryIfNotExists(String dirPath) throws IOException {
        Path path = Paths.get(dirPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            System.out.println("创建目录: " + dirPath);
        }
    }

    /**
     * 获取原始图片目录的绝对路径
     */
    public String getOriginalDirectoryPath() {
        return uploadDir + File.separator + ORIGINAL_DIR;
    }

    /**
     * 获取临时处理图片目录的绝对路径
     */
    public String getTempDirectoryPath() {
        // 使用集中管理的临时图片目录
        return tempImagesDir;
    }

    /**
     * 获取处理后图片目录的绝对路径
     */
    public String getProcessedDirectoryPath() {
        // 使用集中管理的处理后图片目录
        return processedDir;
    }
    
    /**
     * 获取标注图片目录的绝对路径
     */
    public String getAnnotateDirectoryPath() {
        // 使用集中管理的标注后图片目录
        return annotatedDir;
    }

    /**
     * 获取上传根目录的绝对路径
     */
    public String getUploadDirectoryPath() {
        return uploadDir;
    }

    /**
     * 获取自定义临时目录的绝对路径
     */
    public String getCustomTempDirectoryPath() {
        return CUSTOM_TEMP_DIR + File.separator + "temp";
    }

    /**
     * 处理上传的图片并保存到临时处理目录
     * @param file 上传的文件
     * @param filename 文件名
     * @return 处理后的图片Web访问路径和物理路径的Map
     */
    public Map<String, String> processAndSaveImage(MultipartFile file, String filename) throws IOException {
        // 保存原始图片到原始目录
        String originalFilePath = saveOriginalImage(file, filename);
        System.out.println("原始图片已保存: " + originalFilePath);
        
        // 处理图片（调整大小）
        String processedFileName = "temp_" + filename;
        Path tempFilePath = Paths.get(getTempDirectoryPath(), processedFileName);
        
        // 读取原始图片
        BufferedImage originalImage = ImageIO.read(new File(originalFilePath));
        
        // 处理图像 - 限制最大宽高为700像素
        BufferedImage processedImage;
        int maxDimension = 700; // 最大宽度或高度
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        
        // 检查图像是否需要调整大小
        if (originalWidth > maxDimension || originalHeight > maxDimension) {
            // 计算新的尺寸，保持纵横比
            int newWidth, newHeight;
            
            if (originalWidth > originalHeight) {
                // 如果宽度大于高度，则宽度设为最大值
                newWidth = maxDimension;
                newHeight = (int) (originalHeight * ((double) maxDimension / originalWidth));
            } else {
                // 如果高度大于或等于宽度，则高度设为最大值
                newHeight = maxDimension;
                newWidth = (int) (originalWidth * ((double) maxDimension / originalHeight));
            }
            
            // 创建新的缩放后的图像
            java.awt.Image scaledImage = originalImage.getScaledInstance(newWidth, newHeight, java.awt.Image.SCALE_SMOOTH);
            processedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            
            // 将缩放后的图像绘制到新的BufferedImage中
            java.awt.Graphics2D g2d = processedImage.createGraphics();
            g2d.drawImage(scaledImage, 0, 0, null);
            g2d.dispose();
            
            System.out.println("图像已调整大小: " + originalWidth + "x" + originalHeight + " -> " + newWidth + "x" + newHeight);
        } else {
            // 如果图像尺寸已经在限制范围内，直接使用原图
            processedImage = originalImage;
            System.out.println("图像尺寸未变化: " + originalWidth + "x" + originalHeight);
        }
        
        // 保存处理后的图片到临时目录
        File tempFile = tempFilePath.toFile();
        String extension = getFileExtension(filename);
        ImageIO.write(processedImage, extension, tempFile);
        System.out.println("处理后图片已保存: " + tempFile.getAbsolutePath());
        
        // 返回包含Web访问路径和物理路径的Map
        Map<String, String> result = new HashMap<>();
        result.put("webPath", "/medical/images/temp/" + processedFileName);
        result.put("physicalPath", tempFile.getAbsolutePath());
        result.put("originalPath", originalFilePath);
        result.put("originalWebPath", "/medical/images/original/" + filename);
        
        return result;
    }

    /**
     * 保存原始图片到原始目录
     * @param file 上传的文件
     * @param filename 文件名
     * @return 保存后的文件绝对路径
     */
    public String saveOriginalImage(MultipartFile file, String filename) throws IOException {
        Path originalFilePath = Paths.get(getOriginalDirectoryPath(), filename);
        Files.copy(file.getInputStream(), originalFilePath, StandardCopyOption.REPLACE_EXISTING);
        return originalFilePath.toString();
    }

    /**
     * 保存标注后的图片到处理目录或标注目录
     * @param originalPath 原始图片路径
     * @param annotatedImage 标注后的图片
     * @param filename 文件名
     * @param useAnnotateDir 是否使用标注目录
     * @return 标注后的图片Web访问路径
     */
    public String saveAnnotatedImageWithName(String originalPath, BufferedImage annotatedImage, 
                                          String filename, boolean useAnnotateDir) throws IOException {
        String targetDir = useAnnotateDir ? getAnnotateDirectoryPath() : getProcessedDirectoryPath();
        Path processedFilePath = Paths.get(targetDir, filename);
        File processedFile = processedFilePath.toFile();
        String extension = getFileExtension(filename);
        try {
            System.out.println("[保存标注图片] 目标路径: " + processedFile.getAbsolutePath());
            ImageIO.write(annotatedImage, extension, processedFile);
            System.out.println("[保存标注图片] 保存后文件是否存在: " + processedFile.exists() + ", 大小: " + (processedFile.exists() ? processedFile.length() : 0));
        } catch (Exception e) {
            System.err.println("[保存标注图片] 保存失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
        // 返回Web访问路径
        String webDirPath = useAnnotateDir ? "/medical/images/annotate/" : "/medical/images/processed/";
        return webDirPath + filename;
    }
    
    /**
     * 保存标注后的图片到处理目录
     * @param originalPath 原始图片路径
     * @param annotatedImage 标注后的图片
     * @param filename 文件名
     * @return 标注后的图片Web访问路径
     */
    public String saveAnnotatedImageWithName(String originalPath, BufferedImage annotatedImage, String filename) throws IOException {
        return saveAnnotatedImageWithName(originalPath, annotatedImage, filename, false);
    }

    /**
     * 获取文件扩展名
     * @param filename 文件名
     * @return 扩展名（不含点）
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return filename.substring(lastDotIndex + 1).toLowerCase();
        }
        return "jpg"; // 默认扩展名
    }
    
    /**
     * 将Web路径转换为物理文件路径
     * 增强版本：支持多目录查找
     * @param webPath Web访问路径
     * @return 物理文件路径
     */
    public String convertWebPathToFilePath(String webPath) {
        if (webPath == null) return null;
        
        String filename = null;
        String subDir = null;
        
        // 处理Web路径
        if (webPath.startsWith("/medical/images/temp/")) {
            filename = webPath.substring("/medical/images/temp/".length());
            subDir = TEMP_DIR;
        } else if (webPath.startsWith("/medical/images/processed/")) {
            filename = webPath.substring("/medical/images/processed/".length());
            subDir = PROCESSED_DIR;
        } else if (webPath.startsWith("/medical/images/original/")) {
            filename = webPath.substring("/medical/images/original/".length());
            subDir = ORIGINAL_DIR;
        } else if (webPath.startsWith("/medical/images/annotate/")) {
            filename = webPath.substring("/medical/images/annotate/".length());
            subDir = ANNOTATE_DIR;
        } else if (webPath.startsWith("/medical/images/")) {
            filename = webPath.substring("/medical/images/".length());
            // 根目录中查找文件
            return findFileInMultipleRootDirs(filename, "");
        } else if (webPath.startsWith("/images/temp/")) {
            filename = webPath.substring("/images/temp/".length());
            subDir = TEMP_DIR;
        } else if (webPath.startsWith("/images/processed/")) {
            filename = webPath.substring("/images/processed/".length());
            subDir = PROCESSED_DIR;
        } else if (webPath.startsWith("/images/original/")) {
            filename = webPath.substring("/images/original/".length());
            subDir = ORIGINAL_DIR;
        } else if (webPath.startsWith("/images/annotate/")) {
            filename = webPath.substring("/images/annotate/".length());
            subDir = ANNOTATE_DIR;
        } else if (webPath.startsWith("/images/")) {
            filename = webPath.substring("/images/".length());
            // 根目录中查找文件
            return findFileInMultipleRootDirs(filename, "");
        } else if (webPath.startsWith("/temp/")) {
            filename = webPath.substring("/temp/".length());
            return getCustomTempDirectoryPath() + File.separator + filename;
        } else {
            // 默认尝试解析为一个完整路径
            File file = new File(webPath);
            if (file.exists() && file.isFile()) {
                return file.getAbsolutePath();
            }
            
            // 尝试查找相对路径
            filename = webPath;
            if (filename.startsWith("/")) {
                filename = filename.substring(1);
            }
            
            // 尝试在所有目录中查找
            return findFileInAllPossibleDirs(filename);
        }
        
        if (filename != null && subDir != null) {
            return findFileInMultipleRootDirs(filename, subDir);
        }
        
        // 如果无法解析，返回原始路径
        return webPath;
    }
    
    /**
     * 在多个可能的根目录中查找文件
     * @param filename 文件名
     * @param subDir 子目录名（可以为空）
     * @return 文件的物理路径，如果找不到则返回第一个可能的路径
     */
    private String findFileInMultipleRootDirs(String filename, String subDir) {
        String defaultPath = null;
        
        // 先尝试配置的根目录
        String path = uploadDir;
        if (subDir != null && !subDir.isEmpty()) {
            path = path + File.separator + subDir;
        }
        path = path + File.separator + filename;
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            System.out.println("文件在配置的根目录中找到: " + path);
            return path;
        }
        
        if (defaultPath == null) {
            defaultPath = path;
        }
        
        // 尝试所有可能的根目录
        for (String altRoot : ALTERNATE_ROOT_DIRS) {
            path = altRoot;
            if (subDir != null && !subDir.isEmpty()) {
                path = path + File.separator + subDir;
            }
            path = path + File.separator + filename;
            file = new File(path);
            if (file.exists() && file.isFile()) {
                System.out.println("文件在替代根目录中找到: " + path);
                return path;
            }
        }
        
        // 没有找到文件，返回默认路径
        System.out.println("文件未找到，返回默认路径: " + defaultPath);
        return defaultPath;
    }
    
    /**
     * 在所有可能的目录中查找文件
     * @param filename 文件名
     * @return 找到的文件路径，如果找不到则返回默认路径
     */
    private String findFileInAllPossibleDirs(String filename) {
        // 先检查根目录
        String defaultPath = uploadDir + File.separator + filename;
        
        // 检查所有子目录
        String[] subDirs = {TEMP_DIR, PROCESSED_DIR, ORIGINAL_DIR, ANNOTATE_DIR, ""};
        for (String subDir : subDirs) {
            String result = findFileInMultipleRootDirs(filename, subDir);
            File file = new File(result);
            if (file.exists() && file.isFile()) {
                return result;
            }
        }
        
        // 没有找到，返回默认路径
        return defaultPath;
    }
    
    /**
     * 根据Web路径删除物理文件
     * @param webPath 文件的Web访问路径
     * @return 是否成功删除
     */
    public boolean deleteFileByWebPath(String webPath) {
        if (webPath == null || webPath.isEmpty()) {
            System.out.println("无效的Web路径，跳过删除");
            return false;
        }
        
        try {
            // 增加详细日志，打印要删除的Web路径
            System.out.println("尝试删除文件，Web路径: " + webPath);
            
            // 解析Web路径得到物理路径
            String physicalPath = null;
            
            if (webPath.startsWith("/medical/images/temp/")) {
                String filename = webPath.substring("/medical/images/temp/".length());
                physicalPath = getTempDirectoryPath() + File.separator + filename;
                System.out.println("识别为temp目录文件，物理路径: " + physicalPath);
            } else if (webPath.startsWith("/medical/images/processed/")) {
                String filename = webPath.substring("/medical/images/processed/".length());
                physicalPath = getProcessedDirectoryPath() + File.separator + filename;
                System.out.println("识别为processed目录文件，物理路径: " + physicalPath);
            } else if (webPath.startsWith("/medical/images/original/")) {
                String filename = webPath.substring("/medical/images/original/".length());
                physicalPath = getOriginalDirectoryPath() + File.separator + filename;
                System.out.println("识别为original目录文件，物理路径: " + physicalPath);
            } else if (webPath.startsWith("/medical/images/annotate/")) {
                String filename = webPath.substring("/medical/images/annotate/".length());
                physicalPath = getAnnotateDirectoryPath() + File.separator + filename;
                System.out.println("识别为annotate目录文件，物理路径: " + physicalPath);
            } else if (webPath.startsWith("/temp/")) {
                String filename = webPath.substring("/temp/".length());
                physicalPath = getCustomTempDirectoryPath() + File.separator + filename;
                System.out.println("识别为自定义temp目录文件，物理路径: " + physicalPath);
            } else if (webPath.startsWith("/images/processed/")) {
                // 增加对/images/processed/路径的支持（缺少/medical前缀的情况）
                String filename = webPath.substring("/images/processed/".length());
                physicalPath = getProcessedDirectoryPath() + File.separator + filename;
                System.out.println("识别为缺少前缀的processed目录文件，物理路径: " + physicalPath);
            } else if (webPath.startsWith("/images/annotate/")) {
                // 增加对/images/annotate/路径的支持（缺少/medical前缀的情况）
                String filename = webPath.substring("/images/annotate/".length());
                physicalPath = getAnnotateDirectoryPath() + File.separator + filename;
                System.out.println("识别为缺少前缀的annotate目录文件，物理路径: " + physicalPath);
            } else if (webPath.startsWith("/images/temp/")) {
                // 增加对/images/temp/路径的支持（缺少/medical前缀的情况）
                String filename = webPath.substring("/images/temp/".length());
                physicalPath = getTempDirectoryPath() + File.separator + filename;
                System.out.println("识别为缺少前缀的temp目录文件，物理路径: " + physicalPath);
            } else {
                System.out.println("不支持的Web路径格式，尝试直接作为文件名处理: " + webPath);
                
                // 1. 尝试处理直接的文件名情况（无路径）
                String filename = webPath;
                if (webPath.contains("/")) {
                    filename = webPath.substring(webPath.lastIndexOf('/') + 1);
                }
                
                // 2. 优先在processed目录查找，因为标注图片默认在这个目录
                File processedFile = new File(getProcessedDirectoryPath(), filename);
                if (processedFile.exists()) {
                    physicalPath = processedFile.getAbsolutePath();
                    System.out.println("在processed目录找到匹配文件: " + physicalPath);
                } else {
                    // 3. 依次检查其他目录
                    File annotateFile = new File(getAnnotateDirectoryPath(), filename);
                    if (annotateFile.exists()) {
                        physicalPath = annotateFile.getAbsolutePath();
                        System.out.println("在annotate目录找到匹配文件: " + physicalPath);
                    } else {
                        File tempFile = new File(getTempDirectoryPath(), filename);
                        if (tempFile.exists()) {
                            physicalPath = tempFile.getAbsolutePath();
                            System.out.println("在temp目录找到匹配文件: " + physicalPath);
                        } else {
                            File originalFile = new File(getOriginalDirectoryPath(), filename);
                            if (originalFile.exists()) {
                                physicalPath = originalFile.getAbsolutePath();
                                System.out.println("在original目录找到匹配文件: " + physicalPath);
                            } else {
                                System.out.println("在所有目录中均未找到匹配的文件: " + filename);
                                return false;
                            }
                        }
                    }
                }
            }
            
            if (physicalPath == null) {
                System.out.println("无法解析的Web路径: " + webPath);
                return false;
            }
            
            // 添加编码处理
            String normalizedPath = physicalPath;
            try {
                if (physicalPath.contains("è") && !physicalPath.contains("血管")) {
                    normalizedPath = URLDecoder.decode(physicalPath, "UTF-8");
                    normalizedPath = normalizedPath.replace("è???????¤è???????????", "血管瘤辅助系统");
                }
            } catch (Exception e) {
                System.err.println("路径编码处理失败: " + e.getMessage());
            }
            
            // 尝试删除文件
            File fileToDelete = new File(normalizedPath);
            System.out.println("检查物理文件是否存在: " + normalizedPath + " - 存在: " + fileToDelete.exists());
            
            if (fileToDelete.exists()) {
                System.out.println("正在删除文件: " + normalizedPath);
                boolean deleted = fileToDelete.delete();
                if (deleted) {
                    System.out.println("成功删除文件: " + normalizedPath);
                } else {
                    System.out.println("删除文件失败: " + normalizedPath + "，可能是权限问题或文件被锁定");
                    
                    // 尝试使用Java 7的Files API删除
                    try {
                        Files.delete(fileToDelete.toPath());
                        System.out.println("使用Files API成功删除文件: " + normalizedPath);
                        return true;
                    } catch (Exception e) {
                        System.out.println("使用Files API删除文件也失败: " + e.getMessage());
                    }
                }
                return deleted;
            } else {
                System.out.println("要删除的文件不存在: " + normalizedPath);
                return false;
            }
        } catch (Exception e) {
            System.err.println("删除文件时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 上传文件并创建图像元数据记录
     */
    public Map<String, Object> uploadFile(MultipartFile file, String originalFilename, 
                                         Integer userId, 
                                         com.medical.annotation.util.SequentialIdGenerator idGenerator) 
            throws IOException {
        
        // 生成唯一文件名 (基于当前时间戳)
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileExtension = getFileExtension(originalFilename);
        String uniqueFilename = timestamp + "." + fileExtension;
        
        // 处理和保存图片
        Map<String, String> uploadResult = processAndSaveImage(file, uniqueFilename);
        
        // 获取图像尺寸
        File imageFile = new File(uploadResult.get("physicalPath"));
        BufferedImage img = ImageIO.read(imageFile);
        int width = img.getWidth();
        int height = img.getHeight();
        
        // 构建结果
        Map<String, Object> result = new HashMap<>();
        result.put("filename", uniqueFilename);
        result.put("original_name", originalFilename);
        result.put("path", uploadResult.get("webPath"));
        result.put("physical_path", uploadResult.get("physicalPath")); 
        result.put("mimetype", file.getContentType());
        result.put("size", file.getSize());
        result.put("width", width);
        result.put("height", height);
        result.put("uploaded_by", userId);
        
        return result;
    }

    public String convertFilePathToWebPath(String filePath) {
        // 将物理路径转回Web路径
        if (filePath.contains("血管瘤辅助系统/medical_images/temp")) {
            return "/medical/images/temp/" + new File(filePath).getName();
        }
        // 其他路径类型的处理...
        return filePath;
    }
} 