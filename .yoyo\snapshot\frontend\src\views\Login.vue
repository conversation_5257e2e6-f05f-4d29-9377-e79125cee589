<template>
  <div class="container login-container">
    <div class="card">
      <div class="card-header">
        <h3 class="mb-0">血管瘤辅助系统 - 登录</h3>
      </div>
      <div class="card-body p-4">
        <div v-if="error" class="alert alert-danger">
          {{ error }}
        </div>
        <div v-if="success" class="alert alert-success">
          {{ success }}
        </div>
        
        <form @submit.prevent="handleLogin">
          <div class="mb-3">
            <label for="email" class="form-label">邮箱</label>
            <input 
              type="email" 
              class="form-control" 
              id="email" 
              v-model="email" 
              required
              :disabled="loading"
            >
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">密码</label>
            <input 
              type="password" 
              class="form-control" 
              id="password" 
              v-model="password" 
              required
              :disabled="loading"
            >
          </div>
          <div class="mb-3 form-check">
            <input 
              type="checkbox" 
              class="form-check-input" 
              id="remember" 
              v-model="remember"
              :disabled="loading"
            >
            <label class="form-check-label" for="remember">记住我</label>
          </div>
          <div class="d-grid gap-2">
            <button 
              type="submit" 
              class="btn btn-primary" 
              :disabled="loading"
            >
              <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              登录
            </button>
          </div>
        </form>
        
        <div class="mt-3 text-center">
          <router-link to="/register" class="text-decoration-none">没有账号？点击注册</router-link>
        </div>
        
        <!-- 如果是从标注页面重定向过来的，显示提示 -->
        <div v-if="imageId" class="alert alert-info mt-3">
          检测到您之前正在标注图片(ID: {{ imageId }})，登录后将自动跳转回表单页面。
        </div>
      </div>
      <div class="card-footer text-center">
        <router-link to="/" class="btn btn-link">返回首页</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import api from '@/utils/api'  // 导入API模块

export default {
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    const email = ref('')
    const password = ref('')
    const remember = ref(false)
    const loading = ref(false)
    const error = ref('')
    const success = ref('')
    const imageId = ref(null)
    
    // 检查是否是从"保存并退出"操作过来
    onMounted(() => {
      // 先尝试清理存储的不完整用户数据
      cleanInvalidUserData();
      
      // 先尝试恢复用户会话，这对于从标注页面退出很重要
      const preservedUser = sessionStorage.getItem('preservedUser');
      if (preservedUser) {
        console.log('检测到保存的用户会话，尝试恢复');
        localStorage.setItem('user', preservedUser);
        sessionStorage.removeItem('preservedUser');
        
        // 检查是否应该重定向到工作台
        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/app/dashboard';
        console.log('恢复用户会话后将重定向到:', redirectPath);
        
        // 延迟导航以确保状态更新
        setTimeout(() => {
          router.push(redirectPath);
        }, 100);
        return;
      }
      
      // 首先检查是否有保存并退出的标记
      const isSaveAndExit = localStorage.getItem('isSaveAndExit') === 'true';
      if (isSaveAndExit) {
        console.log('登录页面检测到保存并退出标记，将自动重定向到工作台');
        // 清除标记
        localStorage.removeItem('isSaveAndExit');
        
        // 直接跳转到工作台
        router.push('/app/dashboard');
        return;
      }
      
      // 从query参数中提取imageId
      if (route.query.imageId) {
        imageId.value = route.query.imageId
        console.log(`检测到从标注页面跳转，图片ID: ${imageId.value}`)
      }
    })
    
    const handleLogin = async () => {
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        // 在登录之前清除所有与Dashboard相关的缓存
        console.log('登录前清除所有Dashboard相关缓存...');
        localStorage.removeItem('dashboardStats');
        localStorage.removeItem('tempApiData');
        localStorage.removeItem('dashboardApiResponse');
        
        // 查找和清除所有包含dashboard或stats的缓存项
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('dashboard') || key.includes('stats'))) {
            keysToRemove.push(key);
          }
        }
        
        // 删除找到的缓存项
        keysToRemove.forEach(key => {
          console.log('清除缓存项:', key);
          localStorage.removeItem(key);
        });
        
        // 执行登录
        const userData = await store.dispatch('login', {
          email: email.value,
          password: password.value
        })
        
        // 登录成功后处理用户信息
        console.log('登录成功，检查用户信息:', userData);
        
        // 特殊处理张医生账号
        if (userData && userData.customId === '200000001' && userData.role !== 'DOCTOR') {
          console.log('检测到张医生账号，修复角色为DOCTOR');
          userData.role = 'DOCTOR';
          localStorage.setItem('user', JSON.stringify(userData));
          store.commit('setUser', userData);
        }
        
        // 特殊处理李审核账号
        if (userData && userData.customId === '300000001' && (userData.role !== 'REVIEWER' || userData.name !== '李审核')) {
          console.log('检测到李审核账号，修复信息');
          userData.role = 'REVIEWER';
          userData.name = '李审核';
          localStorage.setItem('user', JSON.stringify(userData));
          store.commit('setUser', userData);
        }
        
        // 执行全局权限修复函数
        if (window.fixUserPermission) {
          const result = window.fixUserPermission();
          console.log('登录后执行权限修复:', result);
        }
        
        // 标记需要刷新工作台
        sessionStorage.setItem('forceRefreshDashboard', 'true');
        
        // 登录成功
        if (imageId.value) {
          // 如果有imageId，说明是从标注页面跳转过来的
          success.value = '登录成功，即将返回标注页面...';
          
          // 先恢复会话标识以防止再次重定向
          sessionStorage.setItem('isNavigatingAfterSave', 'true');
          
          // 保存当前进度到Vuex
          store.dispatch('saveProgress', {
            step: 2, // 病例信息填写步骤
            imageId: Number(imageId.value),
            formData: null
          })
          
          setTimeout(() => {
            // 直接跳转到结构化表单页面
            router.push({
              path: '/cases/structured-form',
              query: { imageId: imageId.value, direct: 1 }
            })
          }, 1500)
        } else {
          // 查看是否有保存的重定向路径
          const redirectPath = sessionStorage.getItem('redirectPath');
          const redirectAfterLogin = sessionStorage.getItem('redirectAfterLogin');
          
          // 清除重定向路径
          sessionStorage.removeItem('redirectPath');
          sessionStorage.removeItem('redirectAfterLogin');
          
          // 标记来自登录页面
          sessionStorage.setItem('fromLoginPage', 'true');
          
          // 普通登录流程
          if (redirectAfterLogin) {
            success.value = '登录成功，即将返回之前的页面...';
            
            setTimeout(() => {
              // 如果包含标注相关路径，添加direct参数
              if (redirectAfterLogin.includes('/annotations') || 
                  redirectAfterLogin.includes('/cases/form') || 
                  redirectAfterLogin.includes('/cases/structured-form')) {
                  
                const separator = redirectAfterLogin.includes('?') ? '&' : '?';
                window.location.href = `${redirectAfterLogin}${separator}direct=1`;
              } else {
                window.location.href = redirectAfterLogin;
              }
            }, 1500);
          } else if (redirectPath) {
            success.value = '登录成功，即将跳转到请求的页面...';
            
            setTimeout(() => {
              router.push(redirectPath);
            }, 1500);
          } else {
            // 默认登录流程
            success.value = '登录成功，即将跳转...';
        
        setTimeout(() => {
          // 在登录成功后跳转前的处理
          console.log('登录前清除所有Dashboard相关缓存...');
          localStorage.removeItem('dashboardStats');
          window.dashboardStats = null; // 清空全局缓存
          sessionStorage.removeItem('dashboardPreloaded'); // 清除预加载标记
          router.push('/app/dashboard')
        }, 1500)
          }
        }
      } catch (err) {
        console.error('Login error:', err)
        error.value = typeof err === 'string' ? err : '登录失败，请检查邮箱和密码'
      } finally {
        loading.value = false
      }
    }
    
    // 添加清理不完整用户数据的方法
    const cleanInvalidUserData = () => {
      try {
        const userStr = localStorage.getItem('user');
        if (!userStr) return; // 没有用户数据，不需要清理
        
        const user = JSON.parse(userStr);
        console.log('检查存储的用户数据:', user);
        
        // 检查是否有必要的用户字段
        const requiredFields = ['id', 'name', 'email', 'role'];
        const missingFields = requiredFields.filter(field => !user[field]);
        
        if (missingFields.length > 0) {
          console.warn(`存储的用户数据缺少必要字段: ${missingFields.join(', ')}`);
          console.log('清除不完整的用户数据');
          localStorage.removeItem('user');
          
          // 防止循环引用导致问题，也清除从会话中保存的用户
          sessionStorage.removeItem('preservedUser');
        } else {
          console.log('存储的用户数据完整，包含必要字段');
        }
      } catch (error) {
        console.error('检查用户数据时出错:', error);
        // 出错时，清除可能损坏的用户数据
        localStorage.removeItem('user');
        sessionStorage.removeItem('preservedUser');
      }
    };
    
    return {
      email,
      password,
      remember,
      loading,
      error,
      success,
      imageId,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  max-width: 450px;
  margin: 100px auto;
}

.card {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #007bff;
  color: white;
  text-align: center;
  font-weight: bold;
  padding: 1rem;
}
</style> 