<template>
  <router-view />
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100%;
}

/* 覆盖Element Plus的默认样式 */
.el-menu {
  border-right: none !important;
}

.el-menu-item.is-active {
  background-color: #1890ff !important;
}

.el-card {
  border-radius: 4px;
  border: none;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 
              0 3px 6px 0 rgba(0, 0, 0, 0.12), 
              0 5px 12px 4px rgba(0, 0, 0, 0.09) !important;
}

.el-button--primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.el-button--primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}
</style> 