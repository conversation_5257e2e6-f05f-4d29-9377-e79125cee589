version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: hemangioma-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: hemangioma_diagnosis
      MYSQL_USER: hemangioma_user
      MYSQL_PASSWORD: hemangioma_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - hemangioma-network
    command: --default-authentication-plugin=mysql_native_password

  # Spring Boot后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: hemangioma-backend
    restart: unless-stopped
    ports:
      - "8085:8085"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ***************************************************************************************************
      SPRING_DATASOURCE_USERNAME: hemangioma_user
      SPRING_DATASOURCE_PASSWORD: hemangioma_pass
      AI_SERVICE_URL: http://ai-service:8086
    depends_on:
      - mysql
    networks:
      - hemangioma-network
    volumes:
      - ./medical_images:/app/medical_images
      - ./logs:/app/logs

  # Python AI服务
  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    container_name: hemangioma-ai-service
    restart: unless-stopped
    ports:
      - "8086:8086"
    environment:
      JAVA_CALLBACK_URL: http://backend:8085/medical/api/hemangioma-diagnoses/update-recommendation
      LLM_MODEL_NAME: deepseek-r1:8b
      OLLAMA_HOST: http://ollama:11434
    networks:
      - hemangioma-network
    volumes:
      - ./resources/models:/app/models
      - ./resources/data:/app/data
      - ./medical_images:/app/medical_images
    depends_on:
      - ollama

  # Ollama LLM服务
  ollama:
    image: ollama/ollama:latest
    container_name: hemangioma-ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - hemangioma-network
    environment:
      - OLLAMA_KEEP_ALIVE=24h
    # 如果有GPU支持，取消注释以下行
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # Vue.js前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: hemangioma-frontend
    restart: unless-stopped
    ports:
      - "8080:80"
    networks:
      - hemangioma-network
    depends_on:
      - backend

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: hemangioma-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx-docker.conf:/etc/nginx/nginx.conf
      - ./medical_images:/usr/share/nginx/html/medical_images
      - ./ssl:/etc/nginx/ssl  # SSL证书目录（如果需要HTTPS）
    networks:
      - hemangioma-network
    depends_on:
      - frontend
      - backend
      - ai-service

volumes:
  mysql_data:
    driver: local
  ollama_data:
    driver: local

networks:
  hemangioma-network:
    driver: bridge
