package com.medical.annotation.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 处理独立页面的控制器
 */
@Controller
public class StandaloneController {

    /**
     * 处理标注审核独立页面请求 - 转发到主应用
     */
    @GetMapping("/review-standalone")
    public String standaloneReview() {
        return "forward:/index.html";
    }
    
    /**
     * 处理标注审核内容页面请求 - 用于iframe内部显示的纯内容页
     */
    @GetMapping("/annotation-review-content")
    public String annotationReviewContent() {
        return "forward:/annotation-review-content.html";
    }
} 