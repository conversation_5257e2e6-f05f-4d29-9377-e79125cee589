"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[222],{9222:(e,r,a)=>{a.r(r),a.d(r,{default:()=>q});var t=a(20641),s=a(90033),n=a(53751),o={class:"container login-container"},i={class:"card"},l={class:"card-body p-4"},d={key:0,class:"alert alert-danger"},c={key:1,class:"alert alert-success"},u={class:"mb-3"},m=["disabled"],v={class:"mb-3"},p=["disabled"],f={class:"mb-3 form-check"},g=["disabled"],b={class:"d-grid gap-2"},k=["disabled"],h={key:0,class:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"},L={class:"mt-3 text-center"},I={key:2,class:"alert alert-info mt-3"},w={class:"card-footer text-center"};function y(e,r,a,y,S,_){var A=(0,t.g2)("router-link");return(0,t.uX)(),(0,t.CE)("div",o,[(0,t.Lk)("div",i,[r[10]||(r[10]=(0,t.Lk)("div",{class:"card-header"},[(0,t.Lk)("h3",{class:"mb-0"},"血管瘤辅助系统 - 登录")],-1)),(0,t.Lk)("div",l,[y.error?((0,t.uX)(),(0,t.CE)("div",d,(0,s.v_)(y.error),1)):(0,t.Q3)("",!0),y.success?((0,t.uX)(),(0,t.CE)("div",c,(0,s.v_)(y.success),1)):(0,t.Q3)("",!0),(0,t.Lk)("form",{onSubmit:r[3]||(r[3]=(0,n.D$)((function(){return y.handleLogin&&y.handleLogin.apply(y,arguments)}),["prevent"]))},[(0,t.Lk)("div",u,[r[4]||(r[4]=(0,t.Lk)("label",{for:"email",class:"form-label"},"邮箱",-1)),(0,t.bo)((0,t.Lk)("input",{type:"email",class:"form-control",id:"email","onUpdate:modelValue":r[0]||(r[0]=function(e){return y.email=e}),required:"",disabled:y.loading},null,8,m),[[n.Jo,y.email]])]),(0,t.Lk)("div",v,[r[5]||(r[5]=(0,t.Lk)("label",{for:"password",class:"form-label"},"密码",-1)),(0,t.bo)((0,t.Lk)("input",{type:"password",class:"form-control",id:"password","onUpdate:modelValue":r[1]||(r[1]=function(e){return y.password=e}),required:"",disabled:y.loading},null,8,p),[[n.Jo,y.password]])]),(0,t.Lk)("div",f,[(0,t.bo)((0,t.Lk)("input",{type:"checkbox",class:"form-check-input",id:"remember","onUpdate:modelValue":r[2]||(r[2]=function(e){return y.remember=e}),disabled:y.loading},null,8,g),[[n.lH,y.remember]]),r[6]||(r[6]=(0,t.Lk)("label",{class:"form-check-label",for:"remember"},"记住我",-1))]),(0,t.Lk)("div",b,[(0,t.Lk)("button",{type:"submit",class:"btn btn-primary",disabled:y.loading},[y.loading?((0,t.uX)(),(0,t.CE)("span",h)):(0,t.Q3)("",!0),r[7]||(r[7]=(0,t.eW)(" 登录 "))],8,k)])],32),(0,t.Lk)("div",L,[(0,t.bF)(A,{to:"/register",class:"text-decoration-none"},{default:(0,t.k6)((function(){return r[8]||(r[8]=[(0,t.eW)("没有账号？点击注册")])})),_:1,__:[8]})]),y.imageId?((0,t.uX)(),(0,t.CE)("div",I," 检测到您之前正在标注图片(ID: "+(0,s.v_)(y.imageId)+")，登录后将自动跳转回表单页面。 ",1)):(0,t.Q3)("",!0)]),(0,t.Lk)("div",w,[(0,t.bF)(A,{to:"/",class:"btn btn-link"},{default:(0,t.k6)((function(){return r[9]||(r[9]=[(0,t.eW)("返回首页")])})),_:1,__:[9]})])])])}var S=a(14048),_=a(30388),A=(a(28706),a(74423),a(44114),a(2892),a(79432),a(21699),a(76031),a(50953)),x=a(40834),C=a(75220);const E={name:"Login",setup:function(){var e=(0,x.Pj)(),r=(0,C.rd)(),a=(0,C.lq)(),s=(0,A.KR)(""),n=(0,A.KR)(""),o=(0,A.KR)(!1),i=(0,A.KR)(!1),l=(0,A.KR)(""),d=(0,A.KR)(""),c=(0,A.KR)(null);(0,t.sV)((function(){var e=sessionStorage.getItem("preservedUser");if(e){localStorage.setItem("user",e),sessionStorage.removeItem("preservedUser");var t=sessionStorage.getItem("redirectAfterLogin")||"/app/dashboard";setTimeout((function(){r.push(t)}),100)}else{var s="true"===localStorage.getItem("isSaveAndExit");if(s)return localStorage.removeItem("isSaveAndExit"),void r.push("/app/dashboard");a.query.imageId&&(c.value=a.query.imageId)}}));var u=function(){var a=(0,_.A)((0,S.A)().mark((function a(){var t,o;return(0,S.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i.value=!0,l.value="",d.value="",a.prev=3,a.next=6,e.dispatch("login",{email:s.value,password:n.value});case 6:a.sent,c.value?(d.value="登录成功，即将返回标注页面...",sessionStorage.setItem("isNavigatingAfterSave","true"),e.dispatch("saveProgress",{step:2,imageId:Number(c.value),formData:null}),setTimeout((function(){r.push({path:"/cases/structured-form",query:{imageId:c.value,direct:1}})}),1500)):(t=sessionStorage.getItem("redirectPath"),o=sessionStorage.getItem("redirectAfterLogin"),sessionStorage.removeItem("redirectPath"),sessionStorage.removeItem("redirectAfterLogin"),o?(d.value="登录成功，即将返回之前的页面...",setTimeout((function(){if(o.includes("/annotations")||o.includes("/cases/form")||o.includes("/cases/structured-form")){var e=o.includes("?")?"&":"?";window.location.href="".concat(o).concat(e,"direct=1")}else window.location.href=o}),1500)):t?(d.value="登录成功，即将跳转到请求的页面...",setTimeout((function(){r.push(t)}),1500)):(d.value="登录成功，即将跳转...",setTimeout((function(){r.push("/app/dashboard")}),1500))),a.next=17;break;case 13:a.prev=13,a.t0=a["catch"](3),l.value="string"===typeof a.t0?a.t0:"登录失败，请检查邮箱和密码";case 17:return a.prev=17,i.value=!1,a.finish(17);case 20:case"end":return a.stop()}}),a,null,[[3,13,17,20]])})));return function(){return a.apply(this,arguments)}}();return{email:s,password:n,remember:o,loading:i,error:l,success:d,imageId:c,handleLogin:u}}};var K=a(66262);const R=(0,K.A)(E,[["render",y],["__scopeId","data-v-404c4d62"]]),q=R}}]);