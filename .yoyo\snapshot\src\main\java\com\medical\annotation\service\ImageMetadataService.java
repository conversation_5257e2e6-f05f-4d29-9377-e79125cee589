package com.medical.annotation.service;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.model.Tag;
import com.medical.annotation.model.User;
import com.medical.annotation.model.Team;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.repository.TeamRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.util.BasePathConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import javax.imageio.ImageIO;
import javax.annotation.PostConstruct;

@Service
public class ImageMetadataService {

    @Autowired
    private ImageMetadataRepository imageMetadataRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TeamRepository teamRepository;

    @Autowired
    private AnnotationReviewService annotationReviewService;

    @Autowired
    private ImagePairRepository imagePairRepository;
    
    @Autowired
    private BasePathConfig basePathConfig;

    private String uploadDir;
    private String processedDir;
    private String originalDir;
    
    @PostConstruct
    public void init() {
        uploadDir = basePathConfig.getUploadDir();
        processedDir = basePathConfig.getProcessedDir();
        originalDir = basePathConfig.getOriginalDir();
    }

    /**
     * 上传图像并保存元数据
     * @param file 图像文件
     * @param metadata 图像元数据
     * @param userId 上传用户ID
     * @return 保存的元数据
     */
    @Transactional
    public ImageMetadata uploadImage(MultipartFile file, ImageMetadata metadata, Integer userId) throws Exception {
        try {
            // 验证用户是否存在
            Optional<User> userOpt = userRepository.findById(userId);
            if (!userOpt.isPresent()) {
                throw new Exception("用户不存在");
            }
            
            User user = userOpt.get();

            // 获取用户所属团队
            Team team = user.getTeam();
            
            // 保存文件
            String originalFilename = file.getOriginalFilename();
            String filename = UUID.randomUUID().toString() + getFileExtension(originalFilename);
            
            Path targetLocation = Paths.get(originalDir).resolve(filename);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
            
            // 准备保存元数据
            metadata.setFilename(filename);
            metadata.setOriginalName(originalFilename);
            metadata.setMimetype(file.getContentType());
            metadata.setSize((int) file.getSize());
            
            // 计算图像尺寸
            try {
                BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
                if (bufferedImage != null) {
                    metadata.setWidth(bufferedImage.getWidth());
                    metadata.setHeight(bufferedImage.getHeight());
                }
            } catch (Exception e) {
                System.err.println("无法读取图像尺寸: " + e.getMessage());
            }
            
            // 设置上传者和团队
            metadata.setUploadedBy(user);
            metadata.setTeam(team);
            
            // 设置默认状态
            metadata.setStatus(ImageMetadata.Status.DRAFT);
            
            // 生成9位格式化ID (如果尚未设置)
            if (metadata.getFormattedId() == null || metadata.getFormattedId().isEmpty()) {
                String formattedId = generateFormattedId();
                metadata.setFormattedId(formattedId);
            }
            
            // 保存元数据
            ImageMetadata savedMetadata = imageMetadataRepository.save(metadata);
            
            // 创建ImagePair记录
            ImagePair imagePair = new ImagePair();
            imagePair.setMetadata(savedMetadata);
            imagePair.setImageOnePath(targetLocation.toString());
            imagePair.setDescription("原始图像");
            imagePair.setCreatedBy(userId);
            imagePair.setCreatedAt(LocalDateTime.now());
            imagePairRepository.save(imagePair);
            
            return savedMetadata;
        } catch (Exception e) {
            // 记录并重新抛出异常
            System.err.println("上传图像失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 更新图像元数据
     * @param metadata 更新的元数据
     * @param userId 操作用户ID
     * @return 更新后的元数据
     */
    @Transactional
    public ImageMetadata updateMetadata(ImageMetadata metadata, Integer userId) throws Exception {
        // 验证元数据是否存在
        Optional<ImageMetadata> existingMetadataOpt = imageMetadataRepository.findById(metadata.getId());
        if (!existingMetadataOpt.isPresent()) {
            throw new Exception("图像元数据不存在");
        }
        
        ImageMetadata existingMetadata = existingMetadataOpt.get();
        
        // 验证用户是否有权限
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        User user = userOpt.get();
        
        // 验证用户是否为图像的上传者或同一团队的审核医生
        boolean isUploader = existingMetadata.getUploadedBy().getId().equals(userId);
        boolean isTeamReviewer = user.getRole() == User.Role.REVIEWER && 
                                 user.getTeam() != null && 
                                 existingMetadata.getTeam() != null &&
                                 user.getTeam().getId().equals(existingMetadata.getTeam().getId());
        boolean isAdmin = user.getRole() == User.Role.ADMIN;
        
        if (!isUploader && !isTeamReviewer && !isAdmin) {
            throw new Exception("无权更新此图像的元数据");
        }
        
        // 如果是草稿或被驳回状态，只有上传者才能修改
        if ((existingMetadata.getStatus() == ImageMetadata.Status.DRAFT || 
             existingMetadata.getStatus() == ImageMetadata.Status.REJECTED) && 
            !isUploader && !isAdmin) {
            throw new Exception("只有图像上传者或管理员可以修改草稿或被驳回的标注");
        }
        
        // 如果已提交审核或已审核通过，只有审核医生或管理员才能修改
        if ((existingMetadata.getStatus() == ImageMetadata.Status.SUBMITTED || 
             existingMetadata.getStatus() == ImageMetadata.Status.APPROVED) && 
            !isTeamReviewer && !isAdmin) {
            throw new Exception("只有审核医生或管理员可以修改已提交审核或已通过的标注");
        }
        
        // 更新元数据
        existingMetadata.setPatientName(metadata.getPatientName());
        existingMetadata.setPatientAge(metadata.getPatientAge());
        existingMetadata.setPatientGender(metadata.getPatientGender());
        existingMetadata.setCaseNumber(metadata.getCaseNumber());
        existingMetadata.setLesionLocation(metadata.getLesionLocation());
        existingMetadata.setLesionSize(metadata.getLesionSize());
        existingMetadata.setLesionColor(metadata.getLesionColor());
        existingMetadata.setBorderClarity(metadata.getBorderClarity());
        existingMetadata.setBloodFlow(metadata.getBloodFlow());
        existingMetadata.setDiagnosisCategory(metadata.getDiagnosisCategory());
        existingMetadata.setDiseaseStage(metadata.getDiseaseStage());
        existingMetadata.setMorphologicalFeatures(metadata.getMorphologicalFeatures());
        existingMetadata.setSymptoms(metadata.getSymptoms());
        existingMetadata.setSymptomDetails(metadata.getSymptomDetails());
        existingMetadata.setComplications(metadata.getComplications());
        existingMetadata.setComplicationDetails(metadata.getComplicationDetails());
        existingMetadata.setDiagnosisIcdCode(metadata.getDiagnosisIcdCode());
        existingMetadata.setTreatmentPriority(metadata.getTreatmentPriority());
        existingMetadata.setRecommendedTreatment(metadata.getRecommendedTreatment());
        existingMetadata.setTreatmentPlan(metadata.getTreatmentPlan());
        existingMetadata.setContraindications(metadata.getContraindications());
        existingMetadata.setFollowUpSchedule(metadata.getFollowUpSchedule());
        existingMetadata.setPrognosisRating(metadata.getPrognosisRating());
        existingMetadata.setPatientEducation(metadata.getPatientEducation());
        existingMetadata.setAcquisitionDate(metadata.getAcquisitionDate());
        existingMetadata.setStudyDescription(metadata.getStudyDescription());
        
        return imageMetadataRepository.save(existingMetadata);
    }
    
    /**
     * 提交标注图像到审核流程
     * @param imageId 图像ID
     * @param userId 提交用户ID
     */
    @Transactional
    public void submitForReview(Long imageId, Integer userId) throws Exception {
        // 验证图像是否存在
        Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findById(imageId);
        if (!metadataOpt.isPresent()) {
            throw new Exception("图像不存在");
        }
        
        ImageMetadata metadata = metadataOpt.get();
        
        // 验证用户是否有权限
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        User user = userOpt.get();
        
        // 验证用户是否为图像的上传者
        if (!metadata.getUploadedBy().getId().equals(userId) && user.getRole() != User.Role.ADMIN) {
            throw new Exception("只有图像上传者或管理员可以提交审核");
        }
        
        // 验证图像状态是否为草稿或被驳回
        if (metadata.getStatus() != ImageMetadata.Status.DRAFT && 
            metadata.getStatus() != ImageMetadata.Status.REJECTED) {
            throw new Exception("只有草稿或被驳回的标注可以提交审核");
        }
        
        // 设置提交信息
        metadata.setSubmittedAt(LocalDateTime.now());
        metadata.setSubmittedBy(user);
        
        // 根据用户角色决定提交后的处理方式
        if (user.getRole() == User.Role.ADMIN || user.getRole() == User.Role.REVIEWER) {
            // 管理员和审核医生提交的标注自动审核通过
            metadata = annotationReviewService.autoApprove(metadata, user);
            System.out.println("管理员/审核医生提交的标注已自动审核通过：" + metadata.getId());
        } else {
            // 标注医生提交的需要审核
            // 验证是否有团队
            if (metadata.getTeam() == null) {
                // 更新状态为已提交审核
                metadata.setStatus(ImageMetadata.Status.SUBMITTED);
                System.out.println("无团队标注医生的标注已提交审核：" + metadata.getId());
            } else {
                // 验证团队中是否有审核医生
                List<User> teamReviewers = userRepository.findByTeam_IdAndRole(metadata.getTeam().getId(), User.Role.REVIEWER);
                if (teamReviewers.isEmpty()) {
                    throw new Exception("团队中没有审核医生，无法提交审核");
                }
                
                // 更新状态为已提交审核
                metadata.setStatus(ImageMetadata.Status.SUBMITTED);
                System.out.println("有团队标注医生的标注已提交审核，等待团队审核医生审核：" + metadata.getId());
            }
        }
        
        imageMetadataRepository.save(metadata);
    }
    
    /**
     * 审核标注图像
     * @param imageId 图像ID
     * @param approved 是否通过审核
     * @param reviewNotes 审核意见
     * @param reviewerId 审核医生ID
     */
    @Transactional
    public void reviewImage(Long imageId, boolean approved, String reviewNotes, Integer reviewerId) throws Exception {
        // 验证图像是否存在
        Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findById(imageId);
        if (!metadataOpt.isPresent()) {
            throw new Exception("图像不存在");
        }
        
        ImageMetadata metadata = metadataOpt.get();
        
        // 验证审核医生是否有权限
        Optional<User> reviewerOpt = userRepository.findById(reviewerId);
        if (!reviewerOpt.isPresent()) {
            throw new Exception("审核医生不存在");
        }
        
        User reviewer = reviewerOpt.get();
        
        // 验证是否为审核医生或管理员
        if (reviewer.getRole() != User.Role.REVIEWER && reviewer.getRole() != User.Role.ADMIN) {
            throw new Exception("只有审核医生或管理员可以进行审核");
        }
        
        // 对于审核医生，验证是否属于同一团队
        if (reviewer.getRole() == User.Role.REVIEWER && 
            (reviewer.getTeam() == null || 
             metadata.getTeam() == null || 
             !reviewer.getTeam().getId().equals(metadata.getTeam().getId()))) {
            throw new Exception("只能审核同一团队的标注");
        }
        
        // 验证图像状态是否为已提交审核
        if (metadata.getStatus() != ImageMetadata.Status.SUBMITTED) {
            throw new Exception("只有已提交审核的标注可以进行审核");
        }
        
        // 更新状态
        metadata.setStatus(approved ? ImageMetadata.Status.APPROVED : ImageMetadata.Status.REJECTED);
        metadata.setReviewedBy(reviewer);
        metadata.setReviewNotes(reviewNotes);
        metadata.setReviewDate(LocalDateTime.now());
        
        imageMetadataRepository.save(metadata);
    }
    
    /**
     * 保存标注图像
     * @param imageId 图像元数据ID
     * @param annotatedImageFile 标注后的图像文件
     * @param userId 操作用户ID
     * @return 保存后的文件路径
     */
    @Transactional
    public String saveAnnotatedImage(Long imageId, MultipartFile annotatedImageFile, Integer userId) throws Exception {
        try {
            // 验证图像元数据是否存在
            Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findById(imageId);
            if (!metadataOpt.isPresent()) {
                throw new Exception("图像元数据不存在");
            }
            
            ImageMetadata metadata = metadataOpt.get();
            
            // 验证用户是否有权限
            Optional<User> userOpt = userRepository.findById(userId);
            if (!userOpt.isPresent()) {
                throw new Exception("用户不存在");
            }
            
            User user = userOpt.get();
            
            // 对于草稿或被驳回状态，只有上传者或管理员才能保存标注图像
            if ((metadata.getStatus() == ImageMetadata.Status.DRAFT || 
                 metadata.getStatus() == ImageMetadata.Status.REJECTED) && 
                !metadata.getUploadedBy().getId().equals(userId) && 
                user.getRole() != User.Role.ADMIN) {
                throw new Exception("只有图像上传者或管理员可以保存标注图像");
            }
            
            // 对于已提交审核或已审核通过状态，只有审核医生或管理员才能保存标注图像
            if ((metadata.getStatus() == ImageMetadata.Status.SUBMITTED || 
                 metadata.getStatus() == ImageMetadata.Status.APPROVED) && 
                user.getRole() != User.Role.REVIEWER && 
                user.getRole() != User.Role.ADMIN) {
                throw new Exception("只有审核医生或管理员可以保存已提交或已通过的标注图像");
            }
            
            // 保存标注图像
            String originalFilename = annotatedImageFile.getOriginalFilename();
            String filename = "annotated_" + metadata.getId() + "_" + System.currentTimeMillis() + getFileExtension(originalFilename);
            
            // 创建保存目录
            File processedDirFile = new File(processedDir);
            if (!processedDirFile.exists()) {
                boolean created = processedDirFile.mkdirs();
                if (!created) {
                    throw new Exception("无法创建目录: " + processedDir);
                }
                System.out.println("已创建目录: " + processedDir);
            }
            
            // 保存文件
            Path targetPath = Paths.get(processedDir + filename);
            Files.copy(annotatedImageFile.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("已保存标注图像: " + targetPath);
            
            // 返回保存的路径
            return targetPath.toString();
        } catch (IOException ex) {
            ex.printStackTrace(); // 打印详细错误信息
            throw new Exception("标注图像保存失败: " + ex.getMessage());
        }
    }
    
    /**
     * 删除标注图像
     * @param imagePath 图像路径
     */
    public void deleteAnnotatedImage(String imagePath) throws Exception {
        try {
            Path path = Paths.get(imagePath);
            Files.deleteIfExists(path);
        } catch (IOException ex) {
            throw new Exception("标注图像删除失败: " + ex.getMessage());
        }
    }
    
    /**
     * 获取图像元数据
     * @param imageId 图像ID
     * @return 图像元数据
     */
    public ImageMetadata getImageMetadata(Long imageId) throws Exception {
        Optional<ImageMetadata> metadataOpt = imageMetadataRepository.findById(imageId);
        if (!metadataOpt.isPresent()) {
            throw new Exception("图像元数据不存在");
        }
        return metadataOpt.get();
    }
    
    /**
     * 获取图像元数据 (Integer ID版本，用于向后兼容)
     * @param imageId 图像ID (Integer类型)
     * @return 图像元数据
     */
    public ImageMetadata getImageMetadata(Integer imageId) throws Exception {
        return getImageMetadata(imageId.longValue());
    }
    
    /**
     * 获取用户上传的所有图像
     * @param userId 用户ID
     * @return 图像列表
     */
    public List<ImageMetadata> getUserImages(Integer userId) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        return imageMetadataRepository.findByUploadedBy(userOpt.get());
    }
    
    /**
     * 获取用户所在团队的所有图像
     * @param userId 用户ID
     * @return 图像列表
     */
    public List<ImageMetadata> getTeamImages(Integer userId) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        User user = userOpt.get();
        
        // 验证用户是否有团队
        if (user.getTeam() == null) {
            throw new Exception("用户没有加入团队");
        }
        
        return imageMetadataRepository.findByTeam(user.getTeam());
    }
    
    /**
     * 获取待审核的图像
     * @param reviewerId 审核医生ID
     * @return 待审核图像列表
     */
    public List<ImageMetadata> getPendingReviewImages(Integer reviewerId) throws Exception {
        // 验证审核医生是否存在
        Optional<User> reviewerOpt = userRepository.findById(reviewerId);
        if (!reviewerOpt.isPresent()) {
            throw new Exception("审核医生不存在");
        }
        
        User reviewer = reviewerOpt.get();
        
        // 验证是否为审核医生或管理员
        if (reviewer.getRole() != User.Role.REVIEWER && reviewer.getRole() != User.Role.ADMIN) {
            throw new Exception("只有审核医生或管理员可以查看待审核图像");
        }
        
        // 对于审核医生，只能查看同一团队的待审核图像
        if (reviewer.getRole() == User.Role.REVIEWER) {
            if (reviewer.getTeam() == null) {
                throw new Exception("审核医生没有加入团队");
            }
            
            return imageMetadataRepository.findByStatusAndTeam_Id(ImageMetadata.Status.SUBMITTED, reviewer.getTeam().getId());
        } else {
            // 管理员可以查看所有待审核图像
            return imageMetadataRepository.findByStatus(ImageMetadata.Status.SUBMITTED);
        }
    }
    
    /**
     * 获取已审核的图像
     * @param reviewerId 审核医生ID
     * @return 已审核图像列表
     */
    public List<ImageMetadata> getReviewedImages(Integer reviewerId) throws Exception {
        // 验证审核医生是否存在
        Optional<User> reviewerOpt = userRepository.findById(reviewerId);
        if (!reviewerOpt.isPresent()) {
            throw new Exception("审核医生不存在");
        }
        
        return imageMetadataRepository.findByReviewedBy(reviewerOpt.get());
    }
    
    /**
     * 获取文件扩展名
     * @param filename 文件名
     * @return 扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    private String generateFormattedId() {
        // Implementation of generateFormattedId method
        return UUID.randomUUID().toString().substring(0, 9);
    }
} 