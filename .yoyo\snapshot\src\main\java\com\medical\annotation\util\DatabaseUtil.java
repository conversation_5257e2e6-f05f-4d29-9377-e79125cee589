package com.medical.annotation.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.Instant;
import java.util.Random;

/**
 * 数据库工具类
 * 提供数据库操作的辅助方法
 */
public class DatabaseUtil {
    
    /**
     * 生成唯一ID，可用于数据库主键
     * 使用完整的时间戳(毫秒)作为ID，与前端保持一致
     * 
     * @return 整型ID，基于当前时间戳
     */
    public static int generateUniqueIntId() {
        // 获取当前时间戳
        long timestamp = System.currentTimeMillis();
        
        // 将时间戳转换为int (可能存在溢出风险，仅用于测试)
        // 实际使用应考虑使用完整的long类型存储
        int uniqueId = (int)(timestamp % Integer.MAX_VALUE);
        
        return uniqueId;
    }
    
    /**
     * 生成唯一ID，返回完整的长整型时间戳
     * 与前端使用的ID格式完全一致
     * 
     * @return 长整型ID，完整的毫秒时间戳
     */
    public static long generateUniqueLongId() {
        // 获取当前时间戳
        return System.currentTimeMillis();
    }
    
    /**
     * 生成带时间戳的唯一描述信息
     * 
     * @param prefix 描述前缀
     * @return 唯一描述信息
     */
    public static String generateUniqueDescription(String prefix) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        String timestamp = now.format(formatter);
        
        return prefix + "_" + timestamp;
    }
    
    // 新增：生成9位随机数ID
    public static Long generateRandom9DigitId() {
        Random random = new Random();
        // 生成范围在100,000,000到999,999,999之间的随机数
        long randomLong = 100_000_000L + random.nextInt(900_000_000);
        return randomLong;
    }
    
    // 新增：格式化ID为固定长度的字符串
    public static String formatIdToNineDigits(Long id) {
        return String.format("%09d", id);
    }
    
    // 将String ID转换为Long
    public static Long parseLongId(String id) {
        if (id == null || id.isEmpty()) {
            return null;
        }
        try {
            return Long.parseLong(id);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 