{"version": 3, "file": "js/989.bf4e9507.js", "mappings": "wMACOA,MAAM,qC,GACJA,MAAM,sB,GAEJA,MAAM,2B,GACJA,MAAM,qB,GAKJA,MAAM,iB,GAVrBC,IAAA,EAW0BD,MAAM,sB,GACjBA,MAAM,iB,GAEJA,MAAM,iB,GAdvBC,IAAA,EAiB4BD,MAAM,uB,GAKfA,MAAM,c,GACJA,MAAM,mB,EAvB3B,a,GAoCmBA,MAAM,c,GACJA,MAAM,mB,EArC3B,a,GAAAC,IAAA,EAiDuCD,MAAM,e,GAG1BA,MAAM,c,GACJA,MAAM,mB,EArD3B,a,GAAAC,IAAA,EAiE0CD,MAAM,e,GAjEhDC,IAAA,EAkE+DD,MAAM,qB,GAE9CA,MAAM,sB,GAuBVA,MAAM,c,GACJA,MAAM,mB,EA5F3B,a,GAyGmBA,MAAM,c,GACJA,MAAM,mB,EA1G3B,a,GAuHmBA,MAAM,c,GACJA,MAAM,mB,EAxH3B,a,GAoImBA,MAAM,gB,EApIzB,a,GAAAC,IAAA,EA0IuCD,MAAM,W,GAM5BA,MAAM,c,wEA/IrBE,EAAAA,EAAAA,IA2JM,MA3JNC,EA2JM,EA1JJC,EAAAA,EAAAA,IAyJM,MAzJNC,EAyJM,EAvJJD,EAAAA,EAAAA,IAsJM,MAtJNE,EAsJM,EArJJF,EAAAA,EAAAA,IAoJM,MApJNG,EAoJM,gBAnJJH,EAAAA,EAAAA,IAEE,OAFGJ,MAAM,iBAAe,EACxBI,EAAAA,EAAAA,IAAuC,OAAlCJ,MAAM,mBAAkB,UAAI,KAGnCI,EAAAA,EAAAA,IA0IM,MA1INI,EA0IM,CAzIGC,EAAAC,QAAK,WAAhBR,EAAAA,EAAAA,IAKM,MALNS,EAKM,EAJJP,EAAAA,EAAAA,IAGM,MAHNQ,EAGM,cAFJR,EAAAA,EAAAA,IAAgC,OAA3BJ,MAAM,cAAa,MAAE,KAC1BI,EAAAA,EAAAA,IAA4C,MAA5CS,GAA4CC,EAAAA,EAAAA,IAAdL,EAAAC,OAAK,SAd/CK,EAAAA,EAAAA,IAAA,OAiBmBN,EAAAO,UAAO,WAAlBd,EAAAA,EAAAA,IAEM,MAFNe,GAEMH,EAAAA,EAAAA,IADDL,EAAAO,SAAO,KAlBpBD,EAAAA,EAAAA,IAAA,QAqBQX,EAAAA,EAAAA,IAyHO,QAzHAc,SAAMC,EAAA,KAAAA,EAAA,IArBrBC,EAAAA,EAAAA,KAAA,kBAqB+BX,EAAAY,gBAAAZ,EAAAY,eAAAC,MAAAb,EAAAc,UAAc,kB,EAC/BnB,EAAAA,EAAAA,IAYM,MAZNoB,EAYM,EAXJpB,EAAAA,EAAAA,IAUA,MAVAqB,EAUA,gBATErB,EAAAA,EAAAA,IAAyB,KAAtBJ,MAAM,aAAW,oBAC1BI,EAAAA,EAAAA,IAOC,SANCsB,GAAG,OA1BjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OA2BuBlB,EAAAmB,KAAID,CAAA,GACbE,KAAK,OACCC,YAAY,UAClBC,SAAA,GACCC,SAAUvB,EAAAwB,S,OA/BzBC,GAAA,OA2BuBzB,EAAAmB,aASTxB,EAAAA,EAAAA,IAcM,MAdN+B,EAcM,EAbJ/B,EAAAA,EAAAA,IAWA,MAXAgC,EAWA,gBAVEhC,EAAAA,EAAAA,IAA0B,KAAvBJ,MAAM,cAAY,oBAC3BI,EAAAA,EAAAA,IAQC,SAPCsB,GAAG,QAxCjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAyCuBlB,EAAA4B,MAAKV,CAAA,GACdE,KAAK,QACCC,YAAY,UAClBC,SAAA,GACCC,SAAUvB,EAAAwB,QACVK,OAAInB,EAAA,KAAAA,EAAA,qBAAEV,EAAA8B,eAAA9B,EAAA8B,cAAAjB,MAAAb,EAAAc,UAAa,I,QA9ClCiB,GAAA,OAyCuB/B,EAAA4B,WAQI5B,EAAAgC,aAAU,WAArBvC,EAAAA,EAAAA,IAAiE,MAAjEwC,GAAiE5B,EAAAA,EAAAA,IAAnBL,EAAAgC,YAAU,KAjDxE1B,EAAAA,EAAAA,IAAA,UAoDcX,EAAAA,EAAAA,IAqCM,MArCNuC,EAqCM,EApCJvC,EAAAA,EAAAA,IAWA,MAXAwC,EAWA,gBAVExC,EAAAA,EAAAA,IAAyB,KAAtBJ,MAAM,aAAW,oBAC1BI,EAAAA,EAAAA,IAQC,SAPCsB,GAAG,WAxDjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAyDuBlB,EAAAoC,SAAQlB,CAAA,GACjBE,KAAK,WACCC,YAAY,QAClBC,SAAA,GACCC,SAAUvB,EAAAwB,QACVK,OAAInB,EAAA,KAAAA,EAAA,qBAAEV,EAAAqC,kBAAArC,EAAAqC,iBAAAxB,MAAAb,EAAAc,UAAgB,I,QA9DrCwB,GAAA,OAyDuBtC,EAAAoC,cAQIpC,EAAAuC,gBAAa,WAAxB9C,EAAAA,EAAAA,IAAuE,MAAvE+C,GAAuEnC,EAAAA,EAAAA,IAAtBL,EAAAuC,eAAa,KAjE9EjC,EAAAA,EAAAA,IAAA,OAkE2BN,EAAAyC,mBAAqBzC,EAAAuC,gBAAa,WAA7C9C,EAAAA,EAAAA,IAmBM,MAnBNiD,EAmBM,gBAlBJ/C,EAAAA,EAAAA,IAAuC,OAAlCJ,MAAM,kBAAiB,SAAK,KACjCI,EAAAA,EAAAA,IASM,MATNgD,EASM,EARJhD,EAAAA,EAAAA,IAOO,OANLJ,OAtEtBqD,EAAAA,EAAAA,IAAA,CAsE4B,eAAc,C,KACsC,SAAhB5C,EAAAyC,iB,OAA+E,WAAhBzC,EAAAyC,iB,OAAiF,WAAhBzC,EAAAyC,qB,WAO9K9C,EAAAA,EAAAA,IAMM,OANDJ,OA9EvBqD,EAAAA,EAAAA,IAAA,CA8E6B,gBAAwB5C,EAAAyC,qB,QAEV,SAArBzC,EAAAyC,iBAA8B,IAA6C,WAAhBzC,EAAAyC,iBAAgB,IAA6D,WAAhBzC,EAAAyC,iBAAgB,eAhF9JnC,EAAAA,EAAAA,IAAA,sBAsFgBX,EAAAA,EAAAA,IAEA,OAFKJ,MAAM,iBAAgB,0CAEjC,OAGII,EAAAA,EAAAA,IAYM,MAZNkD,EAYM,EAXJlD,EAAAA,EAAAA,IAUA,MAVAmD,EAUA,gBATEnD,EAAAA,EAAAA,IAAyB,KAAtBJ,MAAM,aAAW,oBAC1BI,EAAAA,EAAAA,IAOC,SANCsB,GAAG,kBA/FjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAgGuBlB,EAAA+C,gBAAe7B,CAAA,GACxBE,KAAK,WACCC,YAAY,UAClBC,SAAA,GACCC,SAAUvB,EAAAwB,S,OApGzBwB,GAAA,OAgGuBhD,EAAA+C,wBASTpD,EAAAA,EAAAA,IAYM,MAZNsD,EAYM,EAXJtD,EAAAA,EAAAA,IAUA,MAVAuD,EAUA,gBATEvD,EAAAA,EAAAA,IAA6B,KAA1BJ,MAAM,iBAAe,oBAC9BI,EAAAA,EAAAA,IAOC,SANCsB,GAAG,WA7GjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OA8GuBlB,EAAAmD,SAAQjC,CAAA,GACjBE,KAAK,OACCC,YAAY,UAClBC,SAAA,GACCC,SAAUvB,EAAAwB,S,OAlHzB4B,GAAA,OA8GuBpD,EAAAmD,iBASTxD,EAAAA,EAAAA,IAWM,MAXN0D,EAWM,EAVJ1D,EAAAA,EAAAA,IASA,MATA2D,EASA,gBARE3D,EAAAA,EAAAA,IAA+B,KAA5BJ,MAAM,mBAAiB,oBAChCI,EAAAA,EAAAA,IAMC,SALCsB,GAAG,aA3HjB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OA4HuBlB,EAAAuD,WAAUrC,CAAA,GACnBE,KAAK,OACCC,YAAY,UACjBE,SAAUvB,EAAAwB,S,OA/HzBgC,GAAA,OA4HuBxD,EAAAuD,mBAQT5D,EAAAA,EAAAA,IASE,MATF8D,EASE,EARJ9D,EAAAA,EAAAA,IAOS,UANPyB,KAAK,SACD7B,MAAM,eACTgC,SAAUvB,EAAAwB,WAAaxB,EAAAgC,cAAgBhC,EAAAuC,e,CAExBvC,EAAAwB,UAAO,WAAnB/B,EAAAA,EAAAA,IAA4C,OAA5CiE,KA1IlBpD,EAAAA,EAAAA,IAAA,QA2IkBX,EAAAA,EAAAA,IAA8C,aAAAU,EAAAA,EAAAA,IAArCL,EAAAwB,QAAU,SAAW,QAAd,MA3IlCmC,MAAA,KAgJYhE,EAAAA,EAAAA,IAGM,MAHNiE,EAGM,gBAFJjE,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXkE,EAAAA,EAAAA,IAA6DC,EAAA,CAAhDC,GAAG,SAASxE,MAAM,a,CAlJ7C,SAAAyE,EAAAA,EAAAA,KAkJyD,kBAAItD,EAAA,MAAAA,EAAA,MAlJ7DuD,EAAAA,EAAAA,IAkJyD,S,IAlJzDC,EAAA,EAAAC,GAAA,W,eAsJUxE,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,mBAAiB,EAC1BI,EAAAA,EAAAA,IAAa,qB,iIAazB,SACEwB,KAAM,WACNiD,MAAK,WACH,IAAMC,GAAQC,EAAAA,EAAAA,MACRC,GAASC,EAAAA,EAAAA,MAETrD,GAAOsD,EAAAA,EAAAA,IAAI,IACX7C,GAAQ6C,EAAAA,EAAAA,IAAI,IACZrC,GAAWqC,EAAAA,EAAAA,IAAI,IACf1B,GAAkB0B,EAAAA,EAAAA,IAAI,IACtBtB,GAAWsB,EAAAA,EAAAA,IAAI,IACflB,GAAakB,EAAAA,EAAAA,IAAI,IACjBjD,GAAUiD,EAAAA,EAAAA,KAAI,GACdxE,GAAQwE,EAAAA,EAAAA,IAAI,IACZlE,GAAUkE,EAAAA,EAAAA,IAAI,IACdzC,GAAayC,EAAAA,EAAAA,IAAI,IACjBlC,GAAgBkC,EAAAA,EAAAA,IAAI,IACpBhC,GAAmBgC,EAAAA,EAAAA,IAAI,IAGvB3C,EAAgB,WACpB,IAAKF,EAAM8C,MAAMC,OAEf,OADA3C,EAAW0C,MAAQ,YACZ,EAGT,IAAME,EAAa,mDACnB,OAAKA,EAAWC,KAAKjD,EAAM8C,QAK3B1C,EAAW0C,MAAQ,IACZ,IALL1C,EAAW0C,MAAQ,cACZ,EAKX,EAGMrC,EAAmB,WACvB,IAAMyC,EAAM1C,EAASsC,MAGrB,GAAII,EAAIC,OAAS,EAGf,OAFAxC,EAAcmC,MAAQ,cACtBjC,EAAiBiC,MAAQ,QAClB,EAIT,IAAMM,EAAe,QAAQH,KAAKC,GAC5BG,EAAe,QAAQJ,KAAKC,GAC5BI,EAAa,QAAQL,KAAKC,GAC1BK,EAAkB,wCAAwCN,KAAKC,GAE/DM,EAAa,CAACJ,EAAcC,EAAcC,EAAYC,GAAiBE,OAAOC,SAASP,OAGvFQ,EAAkB,CAAC,SAAU,WAAY,SAAU,WAAY,SAAU,UAC/E,OAAIA,EAAgBC,SAASV,EAAIW,gBAC/BlD,EAAcmC,MAAQ,cACtBjC,EAAiBiC,MAAQ,QAClB,GAGLU,EAAa,GACf7C,EAAcmC,MAAQ,gCACtBjC,EAAiBiC,MAAQ,QAClB,IAIU,IAAfU,GAAoBN,EAAIC,OAAS,GACnCtC,EAAiBiC,MAAQ,UACD,IAAfU,GAAoC,IAAfA,GAAoBN,EAAIC,QAAU,MAChEtC,EAAiBiC,MAAQ,UAG3BnC,EAAcmC,MAAQ,IACf,EACT,GAGAgB,EAAAA,EAAAA,IAAM9D,GAAO,WACPI,EAAW0C,QACb1C,EAAW0C,MAAQ,IAGjBzE,EAAMyE,QACRzE,EAAMyE,MAAMc,SAAS,WACrBvF,EAAMyE,MAAMc,SAAS,OACrBvF,EAAMyE,MAAMc,SAAS,UAErBvF,EAAMyE,MAAQ,GAElB,KAGAgB,EAAAA,EAAAA,IAAMtD,GAAU,WACVA,EAASsC,MACXrC,KAEAE,EAAcmC,MAAQ,GACtBjC,EAAiBiC,MAAQ,GAE7B,KAGAgB,EAAAA,EAAAA,IAAM3C,GAAiB,WACjBA,EAAgB2B,OAAStC,EAASsC,QAAU3B,EAAgB2B,MAC9DzE,EAAMyE,MAAQ,aAEdzE,EAAMyE,MAAQ,EAElB,IAEA,IAAM9D,EAAa,eAAA+E,GAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAL,EAAAA,EAAAA,KAAAM,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,UAEhBvE,IAAiB,CAAFsE,EAAAC,EAAA,eAAAD,EAAAE,EAAA,aAKfjE,IAAoB,CAAF+D,EAAAC,EAAA,eAAAD,EAAAE,EAAA,aAKnBlE,EAASsC,QAAU3B,EAAgB2B,MAAK,CAAA0B,EAAAC,EAAA,QACjB,OAAzBpG,EAAMyE,MAAQ,aAAW0B,EAAAE,EAAA,UAMV,OAFjB9E,EAAQkD,OAAQ,EAChBzE,EAAMyE,MAAQ,GACdnE,EAAQmE,MAAQ,GAAC0B,EAAAG,EAAA,EAAAH,EAAAC,EAAA,EAGMhC,EAAMmC,SAAS,WAAY,CAC9CrF,KAAMA,EAAKuD,MACX9C,MAAOA,EAAM8C,MACbtC,SAAUA,EAASsC,MACnBvB,SAAUA,EAASuB,MACnBnB,WAAYA,EAAWmB,MACvB+B,KAAM,WACP,OAPU,GAALT,EAAKI,EAAAM,EAUW,kBAAXV,KACTA,EAAOR,SAAS,WAChBQ,EAAOR,SAAS,yBAChBQ,EAAOR,SAAS,QAChBQ,EAAOR,SAAS,uBACjB,CAAAY,EAAAC,EAAA,QAC6B,OAA5BpG,EAAMyE,MAAQ,gBAAc0B,EAAAE,EAAA,UAK9B/F,EAAQmE,MAAQ,WAGhBvD,EAAKuD,MAAQ,GACb9C,EAAM8C,MAAQ,GACdtC,EAASsC,MAAQ,GACjB3B,EAAgB2B,MAAQ,GACxBvB,EAASuB,MAAQ,GACjBnB,EAAWmB,MAAQ,GACnBjC,EAAiBiC,MAAQ,GAGzBiC,YAAW,WACTpC,EAAOqC,KAAK,SACd,GAAG,KAAIR,EAAAC,EAAA,eAAAD,EAAAG,EAAA,EAAAL,EAAAE,EAAAM,EAEPG,QAAQ5G,MAAM,sBAAqBiG,GAG7BD,EAAe,SAACa,GAClB,IAAKA,EAAM,OAAO,EAClB,IAAMC,EAA2B,kBAATD,EAAqBA,EAC7BA,EAAKC,SAAW,GAChC,OAAOA,EAAQvB,SAAS,WACjBuB,EAAQvB,SAAS,yBACjBuB,EAAQvB,SAAS,QACjBuB,EAAQvB,SAAS,qBAC5B,EAGIU,EAAIc,UAAYd,EAAIc,SAASF,MAAQb,EAAaC,EAAIc,SAASF,OAExDZ,EAAIa,SAAWd,EAAaC,EAAIa,SADvC9G,EAAMyE,MAAQ,gBAIdzE,EAAMyE,MAAQ,eAClB,OAEoB,OAFpB0B,EAAAG,EAAA,EAEA/E,EAAQkD,OAAQ,EAAI0B,EAAAa,EAAA,iBAAAb,EAAAE,EAAA,MAAAP,EAAA,sBAExB,kBAnFmB,OAAAJ,EAAA9E,MAAA,KAAAC,UAAA,KAqFnB,MAAO,CACLK,KAAAA,EACAS,MAAAA,EACAQ,SAAAA,EACAW,gBAAAA,EACAI,SAAAA,EACAI,WAAAA,EACA/B,QAAAA,EACAvB,MAAAA,EACAM,QAAAA,EACAyB,WAAAA,EACAO,cAAAA,EACAE,iBAAAA,EACAX,cAAAA,EACAO,iBAAAA,EACAzB,eAAAA,EAEJ,G,eCrXF,MAAMsG,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/Register.vue", "webpack://medical-annotation-frontend/./src/views/Register.vue?157d"], "sourcesContent": ["<template>\n  <div class=\"register-page register-background\">\n    <div class=\"register-container\">\n      <!-- 右侧注册框 -->\n      <div class=\"register-form-container\">\n        <div class=\"register-form-box\">\n          <div class=\"register-tabs\">\n            <div class=\"tab-item active\">用户注册</div>\n      </div>\n          \n          <div class=\"register-form\">\n        <div v-if=\"error\" class=\"alert alert-danger\">\n          <div class=\"error-content\">\n            <div class=\"error-icon\">⚠️</div>\n            <div class=\"error-message\">{{ error }}</div>\n          </div>\n        </div>\n        <div v-if=\"success\" class=\"alert alert-success\">\n          {{ success }}\n        </div>\n        \n        <form @submit.prevent=\"handleRegister\">\n              <div class=\"form-group\">\n                <div class=\"input-with-icon\">\n                  <i class=\"icon-user\"></i>\n            <input \n              id=\"name\" \n              v-model=\"name\" \n              type=\"text\" \n                    placeholder=\"请输入您的姓名\"\n              required\n              :disabled=\"loading\"\n            >\n          </div>\n              </div>\n              \n              <div class=\"form-group\">\n                <div class=\"input-with-icon\">\n                  <i class=\"icon-email\"></i>\n            <input \n              id=\"email\" \n              v-model=\"email\" \n              type=\"email\" \n                    placeholder=\"请输入邮箱地址\"\n              required\n              :disabled=\"loading\"\n              @blur=\"validateEmail\"\n            >\n          </div>\n                <div v-if=\"emailError\" class=\"field-error\">{{ emailError }}</div>\n              </div>\n              \n              <div class=\"form-group\">\n                <div class=\"input-with-icon\">\n                  <i class=\"icon-lock\"></i>\n            <input \n              id=\"password\" \n              v-model=\"password\" \n              type=\"password\" \n                    placeholder=\"请设置密码\"\n              required\n              :disabled=\"loading\"\n              @blur=\"validatePassword\"\n            >\n          </div>\n                <div v-if=\"passwordError\" class=\"field-error\">{{ passwordError }}</div>\n                <div v-if=\"passwordStrength && !passwordError\" class=\"password-strength\">\n                  <div class=\"strength-label\">密码强度：</div>\n                  <div class=\"strength-indicator\">\n                    <div \n                      class=\"strength-bar\" \n                      :class=\"{\n                        'weak': passwordStrength === 'weak',\n                        'medium': passwordStrength === 'medium',\n                        'strong': passwordStrength === 'strong'\n                      }\"\n                    ></div>\n                  </div>\n                  <div class=\"strength-text\" :class=\"passwordStrength\">\n                    {{ \n                      passwordStrength === 'weak' ? '弱' : \n                      passwordStrength === 'medium' ? '中' : \n                      passwordStrength === 'strong' ? '强' : ''\n                    }}\n                  </div>\n                </div>\n                <div class=\"password-hint\">\n                  密码长度至少8位，必须包含大写字母、小写字母、数字和特殊符号中的至少三种\n          </div>\n              </div>\n              \n              <div class=\"form-group\">\n                <div class=\"input-with-icon\">\n                  <i class=\"icon-lock\"></i>\n            <input \n              id=\"confirmPassword\" \n              v-model=\"confirmPassword\" \n              type=\"password\" \n                    placeholder=\"请再次输入密码\"\n              required\n              :disabled=\"loading\"\n            >\n          </div>\n              </div>\n              \n              <div class=\"form-group\">\n                <div class=\"input-with-icon\">\n                  <i class=\"icon-hospital\"></i>\n            <input \n              id=\"hospital\" \n              v-model=\"hospital\" \n              type=\"text\" \n                    placeholder=\"请输入所在医院\"\n              required\n              :disabled=\"loading\"\n            >\n          </div>\n              </div>\n              \n              <div class=\"form-group\">\n                <div class=\"input-with-icon\">\n                  <i class=\"icon-department\"></i>\n            <input \n              id=\"department\" \n              v-model=\"department\" \n              type=\"text\" \n                    placeholder=\"请输入所在科室\"\n              :disabled=\"loading\"\n            >\n          </div>\n              </div>\n              \n              <div class=\"form-actions\">\n            <button \n              type=\"submit\" \n                  class=\"register-btn\" \n              :disabled=\"loading || !!emailError || !!passwordError\"\n            >\n                  <span v-if=\"loading\" class=\"spinner\"></span>\n                  <span>{{ loading ? '注册中...' : '立即注册' }}</span>\n            </button>\n          </div>\n        </form>\n        \n            <div class=\"login-link\">\n              <span>已有账号?</span>\n              <router-link to=\"/login\" class=\"login-btn\">立即登录</router-link>\n            </div>\n          </div>\n          \n          <div class=\"register-footer\">\n            <span></span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, watch } from 'vue'\nimport { useStore } from 'vuex'\nimport { useRouter } from 'vue-router'\n\nexport default {\n  name: 'Register',\n  setup() {\n    const store = useStore()\n    const router = useRouter()\n    \n    const name = ref('')\n    const email = ref('')\n    const password = ref('')\n    const confirmPassword = ref('')\n    const hospital = ref('')\n    const department = ref('')\n    const loading = ref(false)\n    const error = ref('')\n    const success = ref('')\n    const emailError = ref('')\n    const passwordError = ref('')\n    const passwordStrength = ref('')\n    \n    // 邮箱格式验证\n    const validateEmail = () => {\n      if (!email.value.trim()) {\n        emailError.value = '邮箱地址不能为空'\n        return false\n      }\n      \n      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/\n      if (!emailRegex.test(email.value)) {\n        emailError.value = '请输入有效的邮箱地址'\n        return false\n      }\n      \n      emailError.value = ''\n      return true\n    }\n    \n    // 密码强度验证\n    const validatePassword = () => {\n      const pwd = password.value\n      \n      // 检查密码长度\n      if (pwd.length < 8) {\n        passwordError.value = '密码长度必须不少于8位'\n        passwordStrength.value = 'weak'\n        return false\n      }\n      \n      // 检查密码复杂度\n      const hasUpperCase = /[A-Z]/.test(pwd)\n      const hasLowerCase = /[a-z]/.test(pwd)\n      const hasNumbers = /[0-9]/.test(pwd)\n      const hasSpecialChars = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(pwd)\n      \n      const typesCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length\n      \n      // 检查常见简单密码\n      const commonPasswords = ['123456', 'password', 'abcdef', '12345678', 'qwerty', '111111']\n      if (commonPasswords.includes(pwd.toLowerCase())) {\n        passwordError.value = '请勿使用常见的简单密码'\n        passwordStrength.value = 'weak'\n        return false\n      }\n      \n      if (typesCount < 3) {\n        passwordError.value = '密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种'\n        passwordStrength.value = 'weak'\n        return false\n      }\n      \n      // 密码强度评估\n      if (typesCount === 3 && pwd.length < 10) {\n        passwordStrength.value = 'medium'\n      } else if (typesCount === 4 || (typesCount === 3 && pwd.length >= 10)) {\n        passwordStrength.value = 'strong'\n      }\n      \n      passwordError.value = ''\n      return true\n    }\n    \n    // 监听邮箱变化，清除错误\n    watch(email, () => {\n      if (emailError.value) {\n        emailError.value = ''\n      }\n      // 清除与邮箱相关的通用错误提示\n      if (error.value && (\n        error.value.includes('邮箱已被注册') || \n        error.value.includes('邮箱') || \n        error.value.includes('已存在')\n      )) {\n        error.value = ''\n      }\n    })\n    \n    // 监听密码变化\n    watch(password, () => {\n      if (password.value) {\n        validatePassword()\n      } else {\n        passwordError.value = ''\n        passwordStrength.value = ''\n      }\n    })\n    \n    // 监听确认密码变化\n    watch(confirmPassword, () => {\n      if (confirmPassword.value && password.value !== confirmPassword.value) {\n        error.value = '两次输入的密码不一致'\n      } else {\n        error.value = ''\n      }\n    })\n    \n    const handleRegister = async () => {\n      // 邮箱验证\n      if (!validateEmail()) {\n        return\n      }\n      \n      // 密码验证\n      if (!validatePassword()) {\n        return\n      }\n      \n      // 验证密码一致性\n      if (password.value !== confirmPassword.value) {\n        error.value = '两次输入的密码不一致'\n        return\n      }\n      \n      loading.value = true\n      error.value = ''\n      success.value = ''\n      \n      try {\n        const result = await store.dispatch('register', {\n          name: name.value,\n          email: email.value,\n          password: password.value,\n          hospital: hospital.value,\n          department: department.value,\n          role: 'DOCTOR' // 默认注册为医生角色\n        })\n        \n        // 检查返回值是否为错误信息\n        if (typeof result === 'string' && (\n          result.includes('邮箱已被注册') || \n          result.includes('email already exists') || \n          result.includes('已存在') || \n          result.includes('already registered')\n        )) {\n          error.value = '该邮箱已被注册，请更换邮箱'\n          return\n        }\n        \n        // 注册成功才执行以下代码\n        success.value = '注册成功，请登录'\n        \n        // 重置表单\n        name.value = ''\n        email.value = ''\n        password.value = ''\n        confirmPassword.value = ''\n        hospital.value = ''\n        department.value = ''\n        passwordStrength.value = ''\n        \n        // 3秒后跳转到登录页\n        setTimeout(() => {\n          router.push('/login')\n        }, 3000)\n      } catch (err) {\n        console.error('Registration error:', err)\n        \n        // 检查是否是邮箱已存在的错误\n        const isEmailTaken = (data) => {\n            if (!data) return false;\n            const message = (typeof data === 'string') ? data : \n                           (data.message || '');\n            return message.includes('邮箱已被注册') || \n                   message.includes('email already exists') || \n                   message.includes('已存在') || \n                   message.includes('already registered');\n        };\n        \n        // 检查各种可能的错误响应格式\n        if (err.response && err.response.data && isEmailTaken(err.response.data)) {\n            error.value = '该邮箱已被注册，请更换邮箱';\n        } else if (err.message && isEmailTaken(err.message)) {\n            error.value = '该邮箱已被注册，请更换邮箱';\n        } else {\n            error.value = '注册失败，请检查填写信息';\n        }\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    return {\n      name,\n      email,\n      password,\n      confirmPassword,\n      hospital,\n      department,\n      loading,\n      error,\n      success,\n      emailError,\n      passwordError,\n      passwordStrength,\n      validateEmail,\n      validatePassword,\n      handleRegister\n    }\n  }\n}\n</script>\n\n<style scoped>\n.register-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  margin: 0;\n  width: 100%;\n  overflow: hidden;\n}\n\n/* 此类由全局CSS定义，这里只是备用 */\n.register-page.register-background {\n  /* background: linear-gradient(135deg, #0d47a1, #4a148c, #6a1b9a); */\n}\n\n.register-container {\n  width: 450px;\n  max-width: 90%;\n  display: flex;\n  background-color: rgba(255, 255, 255, 0.95);\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  height: auto;\n  margin: 20px;\n}\n\n/* 右侧注册框样式 */\n.register-form-container {\n  width: 100%;\n  background-color: white;\n  display: flex;\n  flex-direction: column;\n}\n\n.register-form-box {\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.register-tabs {\n  display: flex;\n  border-bottom: 1px solid #eee;\n  margin-bottom: 25px;\n  justify-content: center;\n}\n\n.tab-item {\n  padding: 10px 0;\n  margin-right: 0;\n  font-size: 18px;\n  color: #1890ff;\n  font-weight: 500;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #1890ff;\n  font-weight: 500;\n}\n\n.tab-item.active:after {\n  content: '';\n  position: absolute;\n  bottom: -1px;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background-color: #1890ff;\n}\n\n.register-form {\n  flex: 1;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.input-with-icon {\n  position: relative;\n}\n\n.input-with-icon i {\n  position: absolute;\n  left: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #bfbfbf;\n}\n\n.input-with-icon input {\n  width: 100%;\n  padding: 12px 12px 12px 40px;\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  font-size: 14px;\n  transition: all 0.3s;\n}\n\n.input-with-icon input:focus {\n  border-color: #40a9ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n  outline: none;\n}\n\n.field-error {\n  color: #ff4d4f;\n  font-size: 12px;\n  margin-top: 4px;\n  padding-left: 8px;\n}\n\n/* 密码强度相关样式 */\n.password-strength {\n  display: flex;\n  align-items: center;\n  margin-top: 8px;\n  font-size: 12px;\n}\n\n.strength-label {\n  margin-right: 8px;\n  color: #666;\n}\n\n.strength-indicator {\n  flex: 1;\n  height: 4px;\n  background-color: #f0f0f0;\n  border-radius: 2px;\n  overflow: hidden;\n  margin-right: 8px;\n}\n\n.strength-bar {\n  height: 100%;\n  width: 0;\n  transition: all 0.3s;\n}\n\n.strength-bar.weak {\n  width: 33%;\n  background-color: #ff4d4f;\n}\n\n.strength-bar.medium {\n  width: 66%;\n  background-color: #faad14;\n}\n\n.strength-bar.strong {\n  width: 100%;\n  background-color: #52c41a;\n}\n\n.strength-text {\n  font-weight: 500;\n}\n\n.strength-text.weak {\n  color: #ff4d4f;\n}\n\n.strength-text.medium {\n  color: #faad14;\n}\n\n.strength-text.strong {\n  color: #52c41a;\n}\n\n.password-hint {\n  color: #888;\n  font-size: 12px;\n  margin-top: 4px;\n  padding-left: 8px;\n  line-height: 1.4;\n}\n\n/* 错误提示相关样式 */\n.alert {\n  padding: 12px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n  font-size: 14px;\n}\n\n.alert-danger {\n  background-color: #fff2f0;\n  border: 1px solid #ffccc7;\n  color: #ff4d4f;\n}\n\n.alert-success {\n  background-color: #f6ffed;\n  border: 1px solid #b7eb8f;\n  color: #52c41a;\n}\n\n.error-content {\n  display: flex;\n  align-items: flex-start;\n}\n\n.error-icon {\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n.error-message {\n  flex: 1;\n  line-height: 1.4;\n}\n\n.error-action {\n  margin-top: 8px;\n  padding-top: 8px;\n  border-top: 1px dashed rgba(255, 77, 79, 0.3);\n  text-align: right;\n}\n\n.login-now-btn {\n  display: inline-block;\n  background-color: #ff4d4f;\n  color: white;\n  padding: 4px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  text-decoration: none;\n  transition: background-color 0.3s;\n}\n\n.login-now-btn:hover {\n  background-color: #ff7875;\n  text-decoration: none;\n}\n\n.form-actions {\n  margin-bottom: 20px;\n}\n\n.register-btn {\n  width: 100%;\n  background-color: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 24px;\n  padding: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.3s;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.register-btn:hover {\n  background-color: #40a9ff;\n}\n\n.register-btn:disabled {\n  background-color: #91d5ff;\n  cursor: not-allowed;\n}\n\n.spinner {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top-color: #fff;\n  animation: spin 0.8s linear infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n.login-link {\n  text-align: center;\n  margin-top: 16px;\n  font-size: 14px;\n}\n\n.login-link span {\n  color: #666;\n  margin-right: 5px;\n}\n\n.login-link a {\n  color: #1890ff;\n  text-decoration: none;\n  font-weight: 500;\n}\n\n.login-link a:hover {\n  text-decoration: underline;\n}\n\n.register-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: #999;\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #eee;\n}\n\n.home-link {\n  color: #1890ff;\n  text-decoration: none;\n}\n\n.home-link:hover {\n  text-decoration: underline;\n}\n\n.copyright {\n  color: #999;\n}\n\n/* 图标样式 */\n.icon-user:before {\n  content: \"👤\";\n}\n\n.icon-email:before {\n  content: \"✉️\";\n}\n\n.icon-lock:before {\n  content: \"🔒\";\n}\n\n.icon-hospital:before {\n  content: \"🏥\";\n}\n\n.icon-department:before {\n  content: \"🔬\";\n}\n\n/* 响应式调整 */\n@media (max-width: 992px) {\n  .register-container {\n    flex-direction: column;\n  }\n  \n  .register-form-container {\n    width: 100%;\n  }\n}\n\n@media (max-width: 576px) {\n  .register-container {\n    max-width: 100%;\n  }\n  \n  .register-form-box {\n    padding: 20px;\n  }\n  \n  .system-name {\n    font-size: 24px;\n  }\n  \n  .feature-list {\n    gap: 10px;\n  }\n  \n  .feature-item {\n    padding: 6px 12px;\n    font-size: 12px;\n  }\n}\n</style> ", "import { render } from \"./Register.vue?vue&type=template&id=b9aadf68&scoped=true\"\nimport script from \"./Register.vue?vue&type=script&lang=js\"\nexport * from \"./Register.vue?vue&type=script&lang=js\"\n\nimport \"./Register.vue?vue&type=style&index=0&id=b9aadf68&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-b9aadf68\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "$setup", "error", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "_createCommentVNode", "success", "_hoisted_9", "onSubmit", "_cache", "_withModifiers", "handleRegister", "apply", "arguments", "_hoisted_10", "_hoisted_11", "id", "$event", "name", "type", "placeholder", "required", "disabled", "loading", "_hoisted_12", "_hoisted_13", "_hoisted_14", "email", "onBlur", "validateEmail", "_hoisted_15", "emailError", "_hoisted_16", "_hoisted_17", "_hoisted_18", "password", "validatePassword", "_hoisted_19", "passwordError", "_hoisted_20", "passwordStrength", "_hoisted_21", "_hoisted_22", "_normalizeClass", "_hoisted_23", "_hoisted_24", "confirmPassword", "_hoisted_25", "_hoisted_26", "_hoisted_27", "hospital", "_hoisted_28", "_hoisted_29", "_hoisted_30", "department", "_hoisted_31", "_hoisted_32", "_hoisted_34", "_hoisted_33", "_hoisted_35", "_createVNode", "_component_router_link", "to", "_withCtx", "_createTextVNode", "_", "__", "setup", "store", "useStore", "router", "useRouter", "ref", "value", "trim", "emailRegex", "test", "pwd", "length", "hasUpperCase", "hasLowerCase", "hasNumbers", "hasSpecialChars", "typesCount", "filter", "Boolean", "commonPasswords", "includes", "toLowerCase", "watch", "_ref", "_asyncToGenerator", "_regenerator", "m", "_callee", "result", "isEmailTaken", "_t", "w", "_context", "n", "a", "p", "dispatch", "role", "v", "setTimeout", "push", "console", "data", "message", "response", "f", "__exports__", "render"], "sourceRoot": ""}