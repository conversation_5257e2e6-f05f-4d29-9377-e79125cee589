package com.medical.annotation.util;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.repository.ImagePairRepository;
import com.medical.annotation.service.FileService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * 图像路径工具类
 * 该类提供从ImagePair表获取路径的方法，替代之前直接从ImageMetadata获取的方式
 */
@Component
public class ImagePathUtil {

    /**
     * 获取或创建ImagePair对象
     * 集中处理ImagePair的获取和创建逻辑，防止创建重复记录
     * 
     * @param imageMetadata 图像元数据
     * @param imagePairRepository 可选，如果为null则使用SpringUtils获取
     * @return ImagePair对象，不会返回null
     */
    public static ImagePair getOrCreateImagePair(ImageMetadata imageMetadata, ImagePairRepository imagePairRepository) {
        if (imageMetadata == null || imageMetadata.getId() == null) {
            throw new IllegalArgumentException("图像元数据不能为空");
        }
        
        // 如果未提供仓库实例，从Spring上下文获取
        if (imagePairRepository == null) {
            imagePairRepository = SpringUtils.getBean(ImagePairRepository.class);
        }
        
        // 查找现有记录
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs != null && !imagePairs.isEmpty()) {
            // 返回已存在的记录
            return imagePairs.get(0);
        }
        
        // 如果不存在，创建新记录
        ImagePair newPair = new ImagePair();
        newPair.setMetadataId(imageMetadata.getId());
        newPair.setDescription("自动创建的图像对");
        
        // 保存新记录
        return imagePairRepository.save(newPair);
    }

    /**
     * 获取图像路径
     * @param imageMetadata 图像元数据
     * @return 图像路径
     */
    public static String getPath(ImageMetadata imageMetadata) {
        if (imageMetadata == null || imageMetadata.getId() == null) {
            return null;
        }
        
        ImagePairRepository imagePairRepository = SpringUtils.getBean(ImagePairRepository.class);
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs != null && !imagePairs.isEmpty()) {
            System.out.println("ImagePathUtil.getPath: 找到现有ImagePair记录, ID=" + imagePairs.get(0).getId());
            return imagePairs.get(0).getImageOnePath();
        }
        
        // 不自动创建新记录，只返回null
        System.out.println("ImagePathUtil.getPath: 未找到ImagePair记录, metadataId=" + imageMetadata.getId());
        return null;
    }

    /**
     * 设置图像路径
     * @param imageMetadata 图像元数据
     * @param path 图像路径
     * @return 是否设置成功
     */
    public static boolean setPath(ImageMetadata imageMetadata, String path) {
        if (imageMetadata == null || imageMetadata.getId() == null) {
            return false;
        }
        
        ImagePairRepository imagePairRepository = SpringUtils.getBean(ImagePairRepository.class);
        // 查找现有记录，不自动创建
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs.isEmpty()) {
            System.out.println("ImagePathUtil.setPath: 未找到ImagePair记录，无法设置路径");
            return false;
        }
        
        ImagePair imagePair = imagePairs.get(0);
        imagePair.setImageOnePath(path);
        imagePairRepository.save(imagePair);
        return true;
    }

    /**
     * 获取第二张图像路径
     * @param imageMetadata 图像元数据
     * @return 第二张图像路径
     */
    public static String getImageTwoPath(ImageMetadata imageMetadata) {
        if (imageMetadata == null || imageMetadata.getId() == null) {
            return null;
        }
        
        ImagePairRepository imagePairRepository = SpringUtils.getBean(ImagePairRepository.class);
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs != null && !imagePairs.isEmpty()) {
            System.out.println("ImagePathUtil.getImageTwoPath: 找到现有ImagePair记录, ID=" + imagePairs.get(0).getId());
            return imagePairs.get(0).getImageTwoPath();
        }
        
        // 不自动创建新记录，只返回null
        System.out.println("ImagePathUtil.getImageTwoPath: 未找到ImagePair记录, metadataId=" + imageMetadata.getId());
        return null;
    }

    /**
     * 设置第二张图像路径
     * @param imageMetadata 图像元数据
     * @param imageTwoPath 第二张图像路径
     * @return 是否设置成功
     */
    public static boolean setImageTwoPath(ImageMetadata imageMetadata, String imageTwoPath) {
        if (imageMetadata == null || imageMetadata.getId() == null) {
            return false;
        }
        
        ImagePairRepository imagePairRepository = SpringUtils.getBean(ImagePairRepository.class);
        // 查找现有记录，不自动创建
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs.isEmpty()) {
            System.out.println("ImagePathUtil.setImageTwoPath: 未找到ImagePair记录，无法设置路径");
            return false;
        }
        
        ImagePair imagePair = imagePairs.get(0);
        imagePair.setImageTwoPath(imageTwoPath);
        imagePairRepository.save(imagePair);
        return true;
    }

    /**
     * 获取图像原始路径
     * @param imageMetadata 图像元数据
     * @param imagePairRepository 图像对仓库
     * @return 原始图像路径
     */
    public static String getOriginalPath(ImageMetadata imageMetadata, ImagePairRepository imagePairRepository) {
        if (imageMetadata == null || imagePairRepository == null) {
            return null;
        }
        
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs.isEmpty()) {
            return null;
        }
        
        return imagePairs.get(0).getImageOnePath();
    }
    
    /**
     * 获取图像标注路径
     * @param imageMetadata 图像元数据
     * @param imagePairRepository 图像对仓库
     * @return 标注图像路径
     */
    public static String getAnnotatedPath(ImageMetadata imageMetadata, ImagePairRepository imagePairRepository) {
        if (imageMetadata == null || imagePairRepository == null) {
            return null;
        }
        
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs.isEmpty()) {
            return null;
        }
        
        return imagePairs.get(0).getImageTwoPath();
    }
    
    /**
     * 设置图像原始路径
     * @param imageMetadata 图像元数据
     * @param path 原始图像路径
     * @param imagePairRepository 图像对仓库
     * @return 是否设置成功
     */
    public static boolean setOriginalPath(ImageMetadata imageMetadata, String path, ImagePairRepository imagePairRepository) {
        if (imageMetadata == null || path == null || imagePairRepository == null) {
            return false;
        }
        
        // 查找现有记录，不自动创建
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs.isEmpty()) {
            System.out.println("ImagePathUtil.setOriginalPath: 未找到ImagePair记录，无法设置路径");
            return false;
        }
        
        ImagePair imagePair = imagePairs.get(0);
        imagePair.setImageOnePath(path);
        imagePairRepository.save(imagePair);
        return true;
    }
    
    /**
     * 设置图像标注路径
     * @param imageMetadata 图像元数据
     * @param path 标注图像路径
     * @param imagePairRepository 图像对仓库
     * @return 是否设置成功
     */
    public static boolean setAnnotatedPath(ImageMetadata imageMetadata, String path, ImagePairRepository imagePairRepository) {
        if (imageMetadata == null || path == null || imagePairRepository == null) {
            return false;
        }
        
        // 查找现有记录，不自动创建
        List<ImagePair> imagePairs = imagePairRepository.findByMetadataId(imageMetadata.getId());
        if (imagePairs.isEmpty()) {
            System.out.println("ImagePathUtil.setAnnotatedPath: 未找到ImagePair记录，无法设置路径");
            return false;
        }
        
        ImagePair imagePair = imagePairs.get(0);
        imagePair.setImageTwoPath(path);
        imagePairRepository.save(imagePair);
        return true;
    }
    
    /**
     * 生成标注图像路径
     * @param originalPath 原始图像路径
     * @return 标注图像路径
     */
    public static String generateAnnotatedImagePath(String originalPath) {
        if (originalPath == null) {
            return null;
        }
        
        // 获取FileService实例以访问目录路径
        FileService fileService = SpringUtils.getBean(FileService.class);
        
        // 提取文件名
        String filename;
        if (originalPath.contains("/")) {
            filename = originalPath.substring(originalPath.lastIndexOf("/") + 1);
        } else if (originalPath.contains("\\")) {
            filename = originalPath.substring(originalPath.lastIndexOf("\\") + 1);
        } else {
            filename = originalPath;
        }
        
        int lastDotIndex = filename.lastIndexOf(".");
        String baseName = (lastDotIndex > 0) ? filename.substring(0, lastDotIndex) : filename;
        String extension = (lastDotIndex > 0) ? filename.substring(lastDotIndex) : "";
        
        // 添加_annotated后缀
        String annotatedFilename = baseName + "_annotated" + extension;
        
        // 构建新路径，放在processed目录下
        return fileService.getProcessedDirectoryPath() + File.separator + annotatedFilename;
    }
    
    /**
     * 将文件路径转换为Web访问路径
     * @param path 文件系统路径
     * @return Web访问路径
     */
    public static String convertToWebPath(String path) {
        if (path == null) {
            return null;
        }
        
        // 如果已经是web路径，直接返回
        if (path.startsWith("/medical") || path.startsWith("http")) {
            return path;
        }
        
        // 转换本地路径为web路径
        String webPath = path;
        
        // 处理Windows路径
        if (path.contains("\\")) {
            // 提取文件名
            String fileName = path.substring(path.lastIndexOf("\\") + 1);
            
            // 根据路径确定子目录
            if (path.contains("\\original\\")) {
                webPath = "/medical/images/original/" + fileName;
            } else if (path.contains("\\processed\\")) {
                webPath = "/medical/images/processed/" + fileName;
            } else if (path.contains("\\annotate\\")) {
                webPath = "/medical/images/annotate/" + fileName;
            } else if (path.contains("\\temp\\")) {
                webPath = "/medical/images/temp/" + fileName;
            } else {
                // 默认为temp目录
                webPath = "/medical/images/temp/" + fileName;
            }
        }
        
        return webPath;
    }

    // 常用路径前缀
    private static final String WEB_TEMP_PREFIX = "/medical/images/temp/";
    private static final String WEB_PROCESSED_PREFIX = "/medical/images/processed/";
    private static final String WEB_ORIGINAL_PREFIX = "/medical/images/original/";
    
    // 常见的物理路径前缀
    private static final String[] PHYSICAL_PATH_PREFIXES = {
        "F:/血管瘤辅助系统/medical_images",
        "F:\\血管瘤辅助系统\\medical_images",
        "F:/医学辅助系统/medical_images",
        "F:\\医学辅助系统\\medical_images"
    };
    
    // 常见的乱码路径
    private static final List<String> COMMON_ENCODING_ISSUES = Arrays.asList(
        "è???????¤è???????????",
        "é????¡?????é????????"
    );
    
    /**
     * 获取修正后的图片路径 - 处理编码问题
     * @param path 原始路径
     * @return 修正后的路径
     */
    public static String getFixedPath(String path) {
        if (path == null) return null;
        
        String fixedPath = path;
        
        // 处理乱码问题
        for (String encodingIssue : COMMON_ENCODING_ISSUES) {
            if (path.contains(encodingIssue)) {
                fixedPath = fixedPath.replace(encodingIssue, "血管瘤辅助系统");
                System.out.println("修复路径编码问题: " + path + " -> " + fixedPath);
                break;
            }
        }
        
        // 处理斜杠方向一致性
        fixedPath = fixedPath.replace('\\', '/');
        
        // 特殊处理URL编码
        try {
            if (fixedPath.contains("%")) {
                String decodedPath = URLDecoder.decode(fixedPath, StandardCharsets.UTF_8.name());
                if (new File(decodedPath).exists()) {
                    System.out.println("URL解码路径成功: " + decodedPath);
                    return decodedPath;
                }
            }
        } catch (UnsupportedEncodingException e) {
            System.err.println("路径解码错误: " + e.getMessage());
        }
        
        return fixedPath;
    }
    
    /**
     * 将Web路径转换为物理路径
     * @param webPath Web访问路径 (如 /medical/images/temp/image.jpg)
     * @return 物理文件路径 (如 F:/血管瘤辅助系统/medical_images/temp/image.jpg)
     */
    public static String webPathToPhysical(String webPath) {
        if (webPath == null) return null;
        
        // 提取文件名
        String filename = getFilenameFromPath(webPath);
        String subdirectory = "";
        
        // 确定子目录
        if (webPath.contains("/temp/")) {
            subdirectory = "temp";
        } else if (webPath.contains("/processed/")) {
            subdirectory = "processed";
        } else if (webPath.contains("/original/")) {
            subdirectory = "original";
        } else if (webPath.contains("/annotated/")) {
            subdirectory = "annotated";
        }
        
        // 构建物理路径
        String physicalPath = PHYSICAL_PATH_PREFIXES[0] + "/" + subdirectory + "/" + filename;
        
        // 检查文件是否存在，如果不存在尝试其他路径格式
        File file = new File(physicalPath);
        if (file.exists()) {
            return physicalPath;
        }
        
        // 尝试其他路径前缀
        for (int i = 1; i < PHYSICAL_PATH_PREFIXES.length; i++) {
            String altPath = PHYSICAL_PATH_PREFIXES[i] + "/" + subdirectory + "/" + filename;
            file = new File(altPath);
            if (file.exists()) {
                return altPath;
            }
        }
        
        System.out.println("无法找到物理文件: " + physicalPath);
        return physicalPath; // 返回首选路径，即使文件不存在
    }
    
    /**
     * 将物理路径转换为Web路径
     * @param physicalPath 物理文件路径
     * @return Web访问路径
     */
    public static String physicalPathToWeb(String physicalPath) {
        if (physicalPath == null) return null;
        
        // 修正路径编码问题
        String fixedPath = getFixedPath(physicalPath);
        String filename = getFilenameFromPath(fixedPath);
        
        // 确定Web路径前缀
        String webPrefix = WEB_TEMP_PREFIX;  // 默认为temp
        if (fixedPath.contains("/processed/") || fixedPath.contains("\\processed\\")) {
            webPrefix = WEB_PROCESSED_PREFIX;
        } else if (fixedPath.contains("/original/") || fixedPath.contains("\\original\\")) {
            webPrefix = WEB_ORIGINAL_PREFIX;
        }
        
        return webPrefix + filename;
    }
    
    /**
     * 从路径中提取文件名
     * @param path 文件路径
     * @return 文件名
     */
    public static String getFilenameFromPath(String path) {
        if (path == null) return "";
        
        int lastSlash = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
        if (lastSlash >= 0 && lastSlash < path.length() - 1) {
            return path.substring(lastSlash + 1);
        }
        
        return path;
    }
    
    /**
     * 检查文件是否存在，尝试多种路径格式
     * @param path 文件路径（可以是Web路径或物理路径）
     * @return 存在的文件路径，如果找不到则返回null
     */
    public static String findExistingFile(String path) {
        if (path == null) return null;
        
        // 先尝试直接检查路径
        File file = new File(path);
        if (file.exists()) {
            return file.getAbsolutePath();
        }
        
        // 如果是Web路径，转换为物理路径再检查
        if (path.startsWith("/medical/")) {
            String physicalPath = webPathToPhysical(path);
            file = new File(physicalPath);
            if (file.exists()) {
                return file.getAbsolutePath();
            }
        }
        
        // 提取文件名，在所有可能的位置搜索
        String filename = getFilenameFromPath(path);
        for (String prefix : PHYSICAL_PATH_PREFIXES) {
            for (String subdir : new String[]{"temp", "processed", "original", "annotated"}) {
                String testPath = prefix + "/" + subdir + "/" + filename;
                file = new File(testPath);
                if (file.exists()) {
                    System.out.println("在替代位置找到文件: " + testPath);
                    return file.getAbsolutePath();
                }
            }
        }
        
        // 修正编码后再试
        String fixedPath = getFixedPath(path);
        if (!fixedPath.equals(path)) {
            file = new File(fixedPath);
            if (file.exists()) {
                return file.getAbsolutePath();
            }
        }
        
        System.out.println("无法找到文件: " + path);
        return null;
    }

    /**
     * 修复双重路径或错误格式的路径
     * 专门处理/medical/medical前缀的路径和其他路径格式问题
     * 
     * @param path 原始路径
     * @return 修复后的路径
     */
    public static String fixDuplicatePath(String path) {
        if (path == null) return null;
        
        String fixedPath = path;
        
        // 修复双重/medical/前缀问题
        if (path.contains("/medical/medical/")) {
            fixedPath = path.replace("/medical/medical/", "/medical/");
            System.out.println("修复双重路径前缀: " + path + " -> " + fixedPath);
        }
        
        // 处理编码问题
        fixedPath = getFixedPath(fixedPath);
        
        return fixedPath;
    }

    /**
     * 获取正确格式的Web访问路径
     * 确保路径符合预期格式
     * 
     * @param path 原始路径
     * @return 格式化的Web访问路径
     */
    public static String getCorrectWebPath(String path) {
        if (path == null) return null;
        
        // 首先修复双重路径问题
        String fixedPath = fixDuplicatePath(path);
        
        // 确保正确的前缀
        if (!fixedPath.startsWith("/")) {
            fixedPath = "/" + fixedPath;
        }
        
        if (!fixedPath.startsWith("/medical/")) {
            // 如果路径不以/medical/开头，但包含images子目录，添加/medical前缀
            if (fixedPath.contains("/images/")) {
                fixedPath = "/medical" + fixedPath;
            } else {
                // 如果没有包含images子目录，假设它是一个文件名，将其放到默认目录
                String filename = getFilenameFromPath(fixedPath);
                fixedPath = "/medical/images/temp/" + filename;
            }
        }
        
        // 确保不会有多余的斜杠
        fixedPath = fixedPath.replace("//", "/");
        
        return fixedPath;
    }
} 