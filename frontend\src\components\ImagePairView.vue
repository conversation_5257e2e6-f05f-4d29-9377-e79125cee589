<template>
  <div class="image-pair-container">
    <h4 v-if="title" class="image-pair-title">{{ title }}</h4>
    <div class="image-pair-content">
      <div class="image-wrapper">
        <h5>原始图像</h5>
        <div class="image-container">
          <img 
            v-if="imagePair.image_one_path" 
            :src="getImageSrc(imagePair.image_one_path)" 
            class="pair-image" 
            alt="原始图像" 
            @load="onImageLoad('原始图像加载成功', imagePair.image_one_path)" 
            @error="onImageError('原始图像加载失败', imagePair.image_one_path)" 
          />
          <div v-else class="no-image">
            <i class="el-icon-picture-outline"></i>
            <p>无原始图像</p>
          </div>
        </div>
        <!-- 调试信息 -->
        <div v-if="showDebug" class="debug-info">
          <p>原始路径: {{ imagePair.image_one_path }}</p>
          <p>处理后路径: {{ getImageSrc(imagePair.image_one_path) }}</p>
          <p>状态: {{ imageOneStatus }}</p>
        </div>
      </div>
      <div class="image-wrapper">
        <h5>标注后图像</h5>
        <div class="image-container">
          <img 
            v-if="imagePair.image_two_path" 
            :src="getImageSrc(imagePair.image_two_path)" 
            class="pair-image" 
            alt="标注后图像" 
            @load="onImageLoad('标注图像加载成功', imagePair.image_two_path)"
            @error="onImageError('标注图像加载失败', imagePair.image_two_path)"
          />
          <div v-else class="no-image">
            <i class="el-icon-picture-outline"></i>
            <p>无标注图像</p>
          </div>
        </div>
        <!-- 调试信息 -->
        <div v-if="showDebug" class="debug-info">
          <p>原始路径: {{ imagePair.image_two_path }}</p>
          <p>处理后路径: {{ getImageSrc(imagePair.image_two_path) }}</p>
          <p>状态: {{ imageTwoStatus }}</p>
        </div>
      </div>
    </div>
    <div v-if="imagePair.description" class="image-pair-description">
      <p>{{ imagePair.description }}</p>
    </div>
    
    <!-- 调试控制 -->
    <div class="debug-controls">
      <button class="debug-toggle" @click="toggleDebug">{{ showDebug ? '隐藏调试信息' : '显示调试信息' }}</button>
    </div>
  </div>
</template>

<script>
import { getImageUrl } from '../utils/imageHelper';

export default {
  name: 'ImagePairView',
  props: {
    imagePair: {
      type: Object,
      required: true,
      default: () => ({
        image_one_path: '',
        image_two_path: '',
        description: ''
      })
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showDebug: true,
      imageOneStatus: '等待加载',
      imageTwoStatus: '等待加载'
    }
  },
  mounted() {
    console.log('[ImagePairView] 组件挂载, 图像对数据:', this.imagePair);
    if (this.imagePair.image_one_path) {
      console.log('[ImagePairView] 原始图像路径:', this.imagePair.image_one_path);
      console.log('[ImagePairView] 处理后的URL:', this.getImageSrc(this.imagePair.image_one_path));
    }
    if (this.imagePair.image_two_path) {
      console.log('[ImagePairView] 标注图像路径:', this.imagePair.image_two_path);
      console.log('[ImagePairView] 处理后的URL:', this.getImageSrc(this.imagePair.image_two_path));
    }
  },
  methods: {
    getImageSrc(path) {
      // 使用辅助函数来处理图片URL
      return getImageUrl(path);
    },
    onImageLoad(message, path) {
      console.log(`[ImagePairView] ${message}:`, path);
      if (path === this.imagePair.image_one_path) {
        this.imageOneStatus = '加载成功';
      } else if (path === this.imagePair.image_two_path) {
        this.imageTwoStatus = '加载成功';
      }
    },
    onImageError(message, path) {
      console.error(`[ImagePairView] ${message}:`, path);
      if (path === this.imagePair.image_one_path) {
        this.imageOneStatus = '加载失败';
      } else if (path === this.imagePair.image_two_path) {
        this.imageTwoStatus = '加载失败';
      }
      
      // 尝试不同的路径格式
      console.log(`[ImagePairView] 尝试调整路径格式以排查问题`);
    },
    toggleDebug() {
      this.showDebug = !this.showDebug;
    }
  }
}
</script>

<style scoped>
.image-pair-container {
  margin-bottom: 20px;
}

.image-pair-title {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.image-pair-content {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.image-wrapper {
  flex: 1;
}

.image-wrapper h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.image-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f5f7fa;
}

.pair-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.no-image {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.no-image i {
  font-size: 40px;
  margin-bottom: 8px;
}

.no-image p {
  margin: 0;
}

.image-pair-description {
  color: #606266;
  font-size: 13px;
  padding: 8px 0;
}

/* 调试信息样式 */
.debug-info {
  margin-top: 10px;
  padding: 8px;
  font-size: 12px;
  background-color: #f8f9fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  color: #606266;
  word-break: break-all;
}

.debug-info p {
  margin: 4px 0;
}

.debug-controls {
  margin-top: 10px;
  text-align: right;
}

.debug-toggle {
  background-color: #f0f2f5;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
}

.debug-toggle:hover {
  background-color: #e6e8eb;
}
</style> 