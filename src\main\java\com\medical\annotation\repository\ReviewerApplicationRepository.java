package com.medical.annotation.repository;

import com.medical.annotation.model.ReviewerApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ReviewerApplicationRepository extends JpaRepository<ReviewerApplication, Integer> {
    
    // 根据用户ID查找申请
    List<ReviewerApplication> findByUserId(Integer userId);
    
    // 根据状态查找申请
    List<ReviewerApplication> findByStatus(String status);
    
    // 根据状态不等于条件查找申请
    List<ReviewerApplication> findByStatusNot(String status);
    
    // 根据用户ID和状态查找申请
    Optional<ReviewerApplication> findByUserIdAndStatus(Integer userId, String status);
} 