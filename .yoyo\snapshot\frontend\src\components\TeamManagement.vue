<template>
  <!-- No changes to template section -->
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue';

// 在setup函数中添加事件监听
onMounted(() => {
  fetchTeamMembers();
  
  // 添加全局事件监听，当团队成员更新时刷新列表
  if (window.eventBus) {
    window.eventBus.on('team-member-count-updated', () => {
      console.log('监听到团队成员更新事件，刷新成员列表');
      fetchTeamMembers();
    });
  }
  
  // 检查会话存储是否有更新标记
  const needsRefresh = sessionStorage.getItem('refreshTeamMembers');
  if (needsRefresh === 'true') {
    console.log('检测到需要刷新团队成员列表');
    fetchTeamMembers();
    sessionStorage.removeItem('refreshTeamMembers');
  }
});

// 在onUnmounted中移除事件监听
onUnmounted(() => {
  if (window.eventBus) {
    window.eventBus.off('team-member-count-updated');
  }
});
</script>

<style>
  /* No changes to style section */
</style> 