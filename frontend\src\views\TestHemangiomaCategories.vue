<template>
  <div class="test-categories">
    <h2>血管瘤分类测试页面</h2>
    
    <div class="category-selector">
      <h3>分级选择测试</h3>
      
      <!-- 第一级：主要分类 -->
      <div class="form-group">
        <label>主要分类：</label>
        <el-select 
          v-model="selectedMainCategory" 
          placeholder="请选择主要分类" 
          style="width: 300px;"
          @change="onMainCategoryChange"
        >
          <el-option label="真性血管肿瘤" value="真性血管肿瘤"></el-option>
          <el-option label="血管畸形" value="血管畸形"></el-option>
          <el-option label="血管假瘤/易混淆病变" value="血管假瘤/易混淆病变"></el-option>
        </el-select>
      </div>
      
      <!-- 第二级：具体类型 -->
      <div class="form-group">
        <label>具体类型：</label>
        <el-select 
          v-model="selectedSubCategory" 
          placeholder="请选择具体类型" 
          style="width: 300px;"
          :disabled="!selectedMainCategory"
        >
          <el-option 
            v-for="item in availableSubCategories" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      
      <!-- 显示选择结果 -->
      <div class="result" v-if="selectedMainCategory && selectedSubCategory">
        <h4>选择结果：</h4>
        <p><strong>主分类：</strong>{{ selectedMainCategory }}</p>
        <p><strong>子分类：</strong>{{ selectedSubCategory }}</p>
        <div class="color-preview" :style="{ backgroundColor: getTagColor(selectedSubCategory) }">
          颜色预览
        </div>
      </div>

      <!-- AI检测结果模拟测试 -->
      <div class="ai-test-section">
        <h4>AI检测结果模拟测试：</h4>
        <div class="test-buttons">
          <el-button @click="testAIResult('IH')" size="small">测试IH检测</el-button>
          <el-button @click="testAIResult('AVM')" size="small">测试AVM检测</el-button>
          <el-button @click="testAIResult('KHE')" size="small">测试KHE检测</el-button>
          <el-button @click="testAIResult('LM')" size="small">测试LM检测</el-button>
          <el-button @click="testAIResult('HPC')" size="small">测试HPC检测</el-button>
        </div>
      </div>
    </div>
    
    <!-- 显示所有分类 -->
    <div class="all-categories">
      <h3>所有血管瘤分类</h3>
      <div v-for="(categories, mainType) in hemangiomaCategories" :key="mainType" class="category-group">
        <h4>{{ mainType }}</h4>
        <div class="sub-categories">
          <div 
            v-for="item in categories" 
            :key="item.value" 
            class="category-item"
            :style="{ borderColor: getTagColor(item.value) }"
          >
            <span class="color-dot" :style="{ backgroundColor: getTagColor(item.value) }"></span>
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestHemangiomaCategories',
  data() {
    return {
      selectedMainCategory: '',
      selectedSubCategory: '',
      
      // 血管瘤分类数据
      hemangiomaCategories: {
        '真性血管肿瘤': [
          { label: '婴幼儿血管瘤', value: '婴幼儿血管瘤' },
          { label: '先天性快速消退型血管瘤', value: '先天性快速消退型血管瘤' },
          { label: '先天性部分消退型血管瘤', value: '先天性部分消退型血管瘤' },
          { label: '先天性不消退型血管瘤', value: '先天性不消退型血管瘤' },
          { label: '卡波西型血管内皮细胞瘤', value: '卡波西型血管内皮细胞瘤' },
          { label: '丛状血管瘤', value: '丛状血管瘤' },
          { label: '化脓性肉芽肿', value: '化脓性肉芽肿' },
          { label: '梭形细胞血管瘤', value: '梭形细胞血管瘤' },
          { label: '上皮样血管内皮瘤', value: '上皮样血管内皮瘤' },
          { label: '网状血管内皮瘤', value: '网状血管内皮瘤' },
          { label: '假肌源性血管内皮瘤', value: '假肌源性血管内皮瘤' },
          { label: '多形性血管内皮瘤', value: '多形性血管内皮瘤' },
          { label: '血管肉瘤', value: '血管肉瘤' },
          { label: '上皮样血管肉瘤', value: '上皮样血管肉瘤' },
          { label: '卡波西肉瘤', value: '卡波西肉瘤' }
        ],
        '血管畸形': [
          { label: '微静脉畸形', value: '微静脉畸形' },
          { label: '静脉畸形', value: '静脉畸形' },
          { label: '动静脉畸形', value: '动静脉畸形' },
          { label: '淋巴管畸形', value: '淋巴管畸形' },
          { label: '球细胞静脉畸形', value: '球细胞静脉畸形' },
          { label: '毛细血管-淋巴管-静脉畸形', value: '毛细血管-淋巴管-静脉畸形' },
          { label: '毛细血管-动静脉畸形', value: '毛细血管-动静脉畸形' },
          { label: '淋巴管-静脉畸形', value: '淋巴管-静脉畸形' }
        ],
        '血管假瘤/易混淆病变': [
          { label: '血管外皮细胞瘤', value: '血管外皮细胞瘤' },
          { label: '血管球瘤', value: '血管球瘤' },
          { label: '血管平滑肌瘤', value: '血管平滑肌瘤' },
          { label: '血管纤维瘤', value: '血管纤维瘤' },
          { label: '靶样含铁血黄素沉积性血管瘤', value: '靶样含铁血黄素沉积性血管瘤' },
          { label: '鞋钉样血管瘤', value: '鞋钉样血管瘤' }
        ]
      }
    };
  },
  computed: {
    // 根据选择的主要分类返回可用的子分类
    availableSubCategories() {
      if (!this.selectedMainCategory) {
        return [];
      }
      return this.hemangiomaCategories[this.selectedMainCategory] || [];
    }
  },
  methods: {
    // 处理主分类变化
    onMainCategoryChange() {
      // 清空子分类选择
      this.selectedSubCategory = '';
    },
    
    // 获取标签颜色
    getTagColor(tag) {
      const colorMap = {
        // 真性血管肿瘤 - 红色系
        '婴幼儿血管瘤': '#ff5252',
        '先天性快速消退型血管瘤': '#ff7252',
        '先天性部分消退型血管瘤': '#ff9252',
        '先天性不消退型血管瘤': '#ffb252',
        '卡波西型血管内皮细胞瘤': '#e91e63',
        '丛状血管瘤': '#f44336',
        '化脓性肉芽肿': '#ff6b6b',
        '梭形细胞血管瘤': '#ff8a80',
        '上皮样血管内皮瘤': '#ffab91',
        '网状血管内皮瘤': '#ffcc02',
        '假肌源性血管内皮瘤': '#ff9800',
        '多形性血管内皮瘤': '#ff5722',
        '血管肉瘤': '#d32f2f',
        '上皮样血管肉瘤': '#c62828',
        '卡波西肉瘤': '#b71c1c',
        
        // 血管畸形 - 蓝色系
        '微静脉畸形': '#2196f3',
        '静脉畸形': '#03a9f4',
        '动静脉畸形': '#00bcd4',
        '淋巴管畸形': '#4fc3f7',
        '球细胞静脉畸形': '#29b6f6',
        '毛细血管-淋巴管-静脉畸形': '#42a5f5',
        '毛细血管-动静脉畸形': '#1e88e5',
        '淋巴管-静脉畸形': '#1976d2',
        
        // 血管假瘤/易混淆病变 - 绿色系
        '血管外皮细胞瘤': '#4caf50',
        '血管球瘤': '#66bb6a',
        '血管平滑肌瘤': '#81c784',
        '血管纤维瘤': '#a5d6a7',
        '靶样含铁血黄素沉积性血管瘤': '#c8e6c9',
        '鞋钉样血管瘤': '#388e3c'
      };
      
      return colorMap[tag] || '#ff5252';
    },

    // 测试AI检测结果自动分类
    testAIResult(detectedType) {
      console.log('测试AI检测结果:', detectedType);
      this.autoSetCategoryFromAI(detectedType);
    },

    // 根据AI检测结果自动设置分类（与实际组件中的逻辑相同）
    autoSetCategoryFromAI(detectedType) {
      console.log('开始自动设置分类，检测类型:', detectedType);

      // AI检测结果可能是缩写形式，需要转换为完整名称
      const typeMapping = {
        'IH': '婴幼儿血管瘤',
        'RICH': '先天性快速消退型血管瘤',
        'PICH': '先天性部分消退型血管瘤',
        'NICH': '先天性不消退型血管瘤',
        'KHE': '卡波西型血管内皮细胞瘤',
        'TA': '丛状血管瘤',
        'PG': '化脓性肉芽肿',
        'MVM': '微静脉畸形',
        'VM': '静脉畸形',
        'AVM': '动静脉畸形',
        'LM': '淋巴管畸形',
        'GVM': '球细胞静脉畸形',
        'CLVM': '毛细血管-淋巴管-静脉畸形',
        'CAVM': '毛细血管-动静脉畸形',
        'LVM': '淋巴管-静脉畸形',
        'HPC': '血管外皮细胞瘤',
        'GT': '血管球瘤',
        'AL': '血管平滑肌瘤',
        'AF': '血管纤维瘤',
        'THH': '靶样含铁血黄素沉积性血管瘤',
        'HH': '鞋钉样血管瘤'
      };

      // 处理多个检测类型（用+分隔）
      const types = detectedType.split('+');
      const firstType = types[0].trim();

      // 获取完整的中文名称
      const fullTypeName = typeMapping[firstType] || firstType;
      console.log('转换后的完整类型名称:', fullTypeName);

      // 查找该类型属于哪个主分类
      let targetMainCategory = '';
      let targetSubCategory = fullTypeName;

      for (const [mainCategory, subCategories] of Object.entries(this.hemangiomaCategories)) {
        const found = subCategories.find(item => item.value === fullTypeName);
        if (found) {
          targetMainCategory = mainCategory;
          targetSubCategory = found.value;
          break;
        }
      }

      if (targetMainCategory) {
        console.log('自动设置分类:', targetMainCategory, '->', targetSubCategory);

        // 设置主分类
        this.selectedMainCategory = targetMainCategory;

        // 等待Vue更新DOM后设置子分类
        this.$nextTick(() => {
          this.selectedSubCategory = targetSubCategory;

          // 显示成功消息
          this.$message.success(`已根据AI检测结果自动设置分类：${targetSubCategory}`);
        });
      } else {
        console.log('未找到匹配的分类，使用默认设置');
        this.$message.warning(`未找到匹配的分类：${fullTypeName}`);
      }
    }
  }
};
</script>

<style scoped>
.test-categories {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.category-selector {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: inline-block;
  width: 100px;
  font-weight: bold;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 5px;
  border: 1px solid #ddd;
}

.color-preview {
  width: 100px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-top: 10px;
}

.all-categories {
  margin-top: 30px;
}

.category-group {
  margin-bottom: 25px;
}

.category-group h4 {
  color: #333;
  border-bottom: 2px solid #eee;
  padding-bottom: 5px;
}

.sub-categories {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
  margin-top: 15px;
}

.category-item {
  padding: 8px 12px;
  border: 2px solid;
  border-radius: 4px;
  background: white;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.ai-test-section {
  margin-top: 20px;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 5px;
  border: 1px solid #e0f2fe;
}

.ai-test-section h4 {
  margin-bottom: 10px;
  color: #0369a1;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style>
