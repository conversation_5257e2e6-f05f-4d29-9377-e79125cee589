// Vuex统计数据模块
import api from '@/utils/api'
import { storageUtils, asyncActionHandler } from '@/utils/storeHelpers'

const state = {
  stats: {
    totalCount: 0,
    draftCount: 0,
    reviewedCount: 0,
    submittedCount: 0,
    approvedCount: 0,
    rejectedCount: 0
  },
  loading: false,
  error: null
}

const getters = {
  getStats: state => state.stats,
  isStatsLoading: state => state.loading,
  getStatsError: state => state.error,
  // 增加兼容images模块的getter
  getImagesStats: state => state.stats
}

const mutations = {
  setStats(state, stats) {
    console.log('Vuex setStats 被调用，原始数据:', stats);
    
    // 确保所有必要的字段都存在，并转换为数字
    state.stats = {
      totalCount: parseInt(stats.totalCount) || 0,
      draftCount: parseInt(stats.draftCount) || 0,
      reviewedCount: parseInt(stats.reviewedCount || stats.pendingCount) || 0,
      submittedCount: parseInt(stats.submittedCount) || 0,
      approvedCount: parseInt(stats.approvedCount) || 0,
      rejectedCount: parseInt(stats.rejectedCount) || 0
    };
    
    console.log('Vuex stats 更新为:', state.stats);
    
    // 使用新的存储工具保存到本地存储
    storageUtils.saveToStorage('dashboardStats', state.stats);
  },
  setLoading(state, status) {
    state.loading = status
  },
  setError(state, error) {
    state.error = error
  },
  // 添加直接设置某个状态值的方法
  setStatField(state, { field, value }) {
    if (state.stats.hasOwnProperty(field)) {
      state.stats[field] = parseInt(value) || 0;
      console.log(`更新统计字段 ${field} = ${state.stats[field]}`);
    } else {
      console.error(`尝试设置未知统计字段: ${field}`);
    }
  }
}

const actions = {
  // 获取统计数据
  async fetchStats({ commit }) {
    asyncActionHandler.start(commit);
    
    try {
      console.log('===== Vuex fetchStats Action 开始执行 =====');
      
      // 从'user'对象中获取ID，只使用数字ID (user.id)
      let userId = null;
      const user = storageUtils.getFromStorage('user');
      if (user) {
        userId = user.id;
      }

      if (!userId) {
        throw new Error('未在localStorage中找到用户ID');
      }
      console.log('使用的用户ID:', userId);
      
      console.log('开始调用API获取仪表盘统计数据');
      const response = await api.stats.getDashboard(userId);
      console.log('API响应对象:', response);
      console.log('API响应状态码:', response.status);
      console.log('API响应数据类型:', typeof response.data);
      console.log('API响应数据:', response.data);
      
      if (response && response.data) {
        // 对每个字段进行详细日志记录
        const data = response.data;
        console.log('统计字段详情:');
        console.log('- totalCount:', data.totalCount, typeof data.totalCount);
        console.log('- draftCount:', data.draftCount, typeof data.draftCount);
        console.log('- reviewedCount:', data.reviewedCount, typeof data.reviewedCount);
        console.log('- submittedCount:', data.submittedCount, typeof data.submittedCount);
        console.log('- approvedCount:', data.approvedCount, typeof data.approvedCount);
        console.log('- rejectedCount:', data.rejectedCount, typeof data.rejectedCount);
        
        console.log('准备调用setStats mutation');
        commit('setStats', response.data);
        console.log('setStats mutation执行完毕');
        return response.data;
      } else {
        throw new Error('响应中没有数据');
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      
      // 使用通用错误处理
      asyncActionHandler.error(commit, error);
      
      // 使用默认值
      const defaultStats = {
        totalCount: 0, 
        draftCount: 0,
        reviewedCount: 0,
        submittedCount: 0,
        approvedCount: 0,
        rejectedCount: 0
      };
      
      console.log('由于错误，设置默认统计数据:', defaultStats);
      commit('setStats', defaultStats);
    } finally {
      console.log('===== Vuex fetchStats Action 执行完毕 =====');
      asyncActionHandler.end(commit);
    }
  },
  
  // 强制设置统计数据（用于手动更新）
  setManualStats({ commit }, statsData) {
    console.log('手动设置统计数据:', statsData);
    commit('setStats', statsData);
  }
}

export default {
  state,
  getters,
  mutations,
  actions
} 