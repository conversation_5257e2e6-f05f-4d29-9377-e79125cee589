"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[883],{2883:(e,n,t)=>{t.r(n),t.d(n,{default:()=>f});var i=t(641),a=t(33),l={class:"review-container"},o={key:0,class:"review-dialog-content"},r={class:"dialog-footer"};function u(e,n,t,u,d,s){var c=(0,i.g2)("el-table-column"),f=(0,i.g2)("el-button"),g=(0,i.g2)("el-table"),p=(0,i.g2)("el-pagination"),v=(0,i.g2)("el-tab-pane"),b=(0,i.g2)("el-tag"),w=(0,i.g2)("el-tabs"),h=(0,i.g2)("el-descriptions-item"),m=(0,i.g2)("el-descriptions"),_=(0,i.g2)("el-radio"),k=(0,i.g2)("el-radio-group"),F=(0,i.g2)("el-form-item"),C=(0,i.g2)("el-input"),D=(0,i.g2)("el-form"),R=(0,i.g2)("el-dialog"),y=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",l,[n[12]||(n[12]=(0,i.Lk)("div",{class:"page-header"},[(0,i.Lk)("h2",null,"标注审核")],-1)),(0,i.bF)(w,{modelValue:d.activeTab,"onUpdate:modelValue":n[2]||(n[2]=function(e){return d.activeTab=e})},{default:(0,i.k6)((function(){return[(0,i.bF)(v,{label:"待审核",name:"pending"},{default:(0,i.k6)((function(){return[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(g,{data:d.pendingReviews,style:{width:"100%"}},{default:(0,i.k6)((function(){return[(0,i.bF)(c,{prop:"caseId",label:"病例编号",width:"180"}),(0,i.bF)(c,{prop:"patientInfo",label:"患者信息",width:"180"}),(0,i.bF)(c,{prop:"department",label:"部位"}),(0,i.bF)(c,{prop:"type",label:"类型"}),(0,i.bF)(c,{prop:"createTime",label:"标注时间"}),(0,i.bF)(c,{prop:"annotator",label:"标注人"}),(0,i.bF)(c,{label:"操作",width:"200"},{default:(0,i.k6)((function(e){return[(0,i.bF)(f,{type:"primary",size:"small",onClick:function(n){return s.handleReview(e.row)}},{default:(0,i.k6)((function(){return n[5]||(n[5]=[(0,i.eW)(" 审核 ")])})),_:2,__:[5]},1032,["onClick"]),(0,i.bF)(f,{type:"info",size:"small",onClick:function(n){return s.handleView(e.row)}},{default:(0,i.k6)((function(){return n[6]||(n[6]=[(0,i.eW)(" 查看 ")])})),_:2,__:[6]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[y,d.loading.pending]]),(0,i.bF)(p,{background:"",layout:"prev, pager, next",total:d.pagination.pending.total,"page-size":d.pagination.pending.pageSize,onCurrentChange:n[0]||(n[0]=function(e){return s.handlePageChange(e,"pending")}),class:"pagination"},null,8,["total","page-size"])]})),_:1}),(0,i.bF)(v,{label:"已审核",name:"reviewed"},{default:(0,i.k6)((function(){return[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(g,{data:d.reviewedCases,style:{width:"100%"}},{default:(0,i.k6)((function(){return[(0,i.bF)(c,{prop:"caseId",label:"病例编号",width:"180"}),(0,i.bF)(c,{prop:"patientInfo",label:"患者信息",width:"180"}),(0,i.bF)(c,{prop:"department",label:"部位"}),(0,i.bF)(c,{prop:"type",label:"类型"}),(0,i.bF)(c,{prop:"reviewTime",label:"审核时间"}),(0,i.bF)(c,{prop:"reviewer",label:"审核人"}),(0,i.bF)(c,{prop:"status",label:"审核结果"},{default:(0,i.k6)((function(e){return[(0,i.bF)(b,{type:"已通过"===e.row.status?"success":"danger"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,a.v_)(e.row.status),1)]})),_:2},1032,["type"])]})),_:1}),(0,i.bF)(c,{label:"操作",width:"120"},{default:(0,i.k6)((function(e){return[(0,i.bF)(f,{type:"info",size:"small",onClick:function(n){return s.handleView(e.row)}},{default:(0,i.k6)((function(){return n[7]||(n[7]=[(0,i.eW)(" 查看 ")])})),_:2,__:[7]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[y,d.loading.reviewed]]),(0,i.bF)(p,{background:"",layout:"prev, pager, next",total:d.pagination.reviewed.total,"page-size":d.pagination.reviewed.pageSize,onCurrentChange:n[1]||(n[1]=function(e){return s.handlePageChange(e,"reviewed")}),class:"pagination"},null,8,["total","page-size"])]})),_:1})]})),_:1},8,["modelValue"]),(0,i.bF)(R,{title:"标注审核",visible:d.reviewDialog.visible,width:"500px",onClose:s.closeReviewDialog},{footer:(0,i.k6)((function(){return[(0,i.Lk)("span",r,[(0,i.bF)(f,{onClick:s.closeReviewDialog},{default:(0,i.k6)((function(){return n[10]||(n[10]=[(0,i.eW)("取消")])})),_:1,__:[10]},8,["onClick"]),(0,i.bF)(f,{type:"primary",onClick:s.submitReview},{default:(0,i.k6)((function(){return n[11]||(n[11]=[(0,i.eW)("提交审核")])})),_:1,__:[11]},8,["onClick"])])]})),default:(0,i.k6)((function(){return[d.reviewDialog["case"]?((0,i.uX)(),(0,i.CE)("div",o,[(0,i.bF)(m,{column:1,border:""},{default:(0,i.k6)((function(){return[(0,i.bF)(h,{label:"病例编号"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,a.v_)(d.reviewDialog["case"].caseId),1)]})),_:1}),(0,i.bF)(h,{label:"患者信息"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,a.v_)(d.reviewDialog["case"].patientInfo),1)]})),_:1}),(0,i.bF)(h,{label:"部位"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,a.v_)(d.reviewDialog["case"].department),1)]})),_:1}),(0,i.bF)(h,{label:"类型"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,a.v_)(d.reviewDialog["case"].type),1)]})),_:1}),(0,i.bF)(h,{label:"标注人"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,a.v_)(d.reviewDialog["case"].annotator),1)]})),_:1}),(0,i.bF)(h,{label:"标注时间"},{default:(0,i.k6)((function(){return[(0,i.eW)((0,a.v_)(d.reviewDialog["case"].createTime),1)]})),_:1})]})),_:1}),(0,i.bF)(D,{model:d.reviewDialog.form,"label-width":"80px",class:"review-form"},{default:(0,i.k6)((function(){return[(0,i.bF)(F,{label:"审核结果"},{default:(0,i.k6)((function(){return[(0,i.bF)(k,{modelValue:d.reviewDialog.form.result,"onUpdate:modelValue":n[3]||(n[3]=function(e){return d.reviewDialog.form.result=e})},{default:(0,i.k6)((function(){return[(0,i.bF)(_,{label:"approve"},{default:(0,i.k6)((function(){return n[8]||(n[8]=[(0,i.eW)("通过")])})),_:1,__:[8]}),(0,i.bF)(_,{label:"reject"},{default:(0,i.k6)((function(){return n[9]||(n[9]=[(0,i.eW)("驳回")])})),_:1,__:[9]})]})),_:1},8,["modelValue"])]})),_:1}),(0,i.bF)(F,{label:"备注"},{default:(0,i.k6)((function(){return[(0,i.bF)(C,{type:"textarea",modelValue:d.reviewDialog.form.comment,"onUpdate:modelValue":n[4]||(n[4]=function(e){return d.reviewDialog.form.comment=e}),rows:3,placeholder:"请输入审核意见（选填）"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model"])])):(0,i.Q3)("",!0)]})),_:1},8,["visible","onClose"])])}t(4114),t(6031);const d={name:"Review",data:function(){return{activeTab:"pending",loading:{pending:!1,reviewed:!1},pendingReviews:[],reviewedCases:[],pagination:{pending:{pageSize:10,currentPage:1,total:0},reviewed:{pageSize:10,currentPage:1,total:0}},reviewDialog:{visible:!1,case:null,form:{result:"approve",comment:""}}}},created:function(){this.fetchPendingReviews()},watch:{activeTab:function(e){"pending"===e?this.fetchPendingReviews():"reviewed"===e&&this.fetchReviewedCases()}},methods:{fetchPendingReviews:function(){var e=this;this.loading.pending=!0,setTimeout((function(){e.pendingReviews=[],e.pagination.pending.total=0,e.loading.pending=!1}),500)},fetchReviewedCases:function(){var e=this;this.loading.reviewed=!0,setTimeout((function(){e.reviewedCases=[],e.pagination.reviewed.total=0,e.loading.reviewed=!1}),500)},handlePageChange:function(e,n){this.pagination[n].currentPage=e,"pending"===n?this.fetchPendingReviews():this.fetchReviewedCases()},handleReview:function(e){this.reviewDialog.visible=!0,this.reviewDialog["case"]=e,this.reviewDialog.form={result:"approve",comment:""}},handleView:function(e){this.$router.push("/cases/view/".concat(e.id))},closeReviewDialog:function(){this.reviewDialog.visible=!1,this.reviewDialog["case"]=null,this.reviewDialog.form={result:"approve",comment:""}},submitReview:function(){this.$message.success("审核提交成功"),this.closeReviewDialog(),this.fetchPendingReviews()}}};var s=t(6262);const c=(0,s.A)(d,[["render",u],["__scopeId","data-v-7df86fb7"]]),f=c},4599:(e,n,t)=>{var i=t(6518),a=t(4576),l=t(9472),o=l(a.setTimeout,!0);i({global:!0,bind:!0,forced:a.setTimeout!==o},{setTimeout:o})},5575:(e,n,t)=>{var i=t(6518),a=t(4576),l=t(9472),o=l(a.setInterval,!0);i({global:!0,bind:!0,forced:a.setInterval!==o},{setInterval:o})},6031:(e,n,t)=>{t(5575),t(4599)},9472:(e,n,t)=>{var i=t(4576),a=t(8745),l=t(4901),o=t(4215),r=t(2839),u=t(7680),d=t(2812),s=i.Function,c=/MSIE .\./.test(r)||"BUN"===o&&function(){var e=i.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}();e.exports=function(e,n){var t=n?2:1;return c?function(i,o){var r=d(arguments.length,1)>t,c=l(i)?i:s(i),f=r?u(arguments,t):[],g=r?function(){a(c,this,f)}:c;return n?e(g,o):e(g)}:e}}}]);