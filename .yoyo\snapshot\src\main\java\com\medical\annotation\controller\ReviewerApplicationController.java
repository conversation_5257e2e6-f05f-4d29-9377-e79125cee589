package com.medical.annotation.controller;

import com.medical.annotation.model.ReviewerApplication;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.ReviewerApplicationRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.service.ReviewerApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/reviewer-applications")
public class ReviewerApplicationController {

    @Autowired
    private ReviewerApplicationRepository reviewerApplicationRepository;

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private ReviewerApplicationService reviewerApplicationService;

    // 提交审核医生申请
    @PostMapping
    public ResponseEntity<?> applyForReviewer(@RequestBody Map<String, String> requestBody) {
        try {
            String reason = requestBody.get("reason");
            Integer userId = Integer.parseInt(requestBody.getOrDefault("userId", "0"));
            
            // 如果userId为0，尝试从请求中获取当前用户
            if (userId == 0) {
                org.springframework.security.core.Authentication auth = 
                    org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
                
                String email = auth.getName();
                if (email.equals("anonymousUser")) {
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
                }
                
                Optional<User> userOpt = userRepository.findByEmail(email);
                if (!userOpt.isPresent()) {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body("用户不存在");
                }
                
                userId = userOpt.get().getId();
            }
            
            ReviewerApplication application = reviewerApplicationService.createApplication(userId, reason);
            return ResponseEntity.status(HttpStatus.CREATED).body(application);
        } catch (IllegalArgumentException e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 获取所有待处理的申请（管理员用）
    @GetMapping("/pending")
    public ResponseEntity<?> getPendingApplications() {
        try {
            List<ReviewerApplication> applications = reviewerApplicationService.getPendingApplications();
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 获取所有已处理的申请（管理员用）
    @GetMapping("/processed")
    public ResponseEntity<?> getProcessedApplications() {
        try {
            List<ReviewerApplication> applications = reviewerApplicationService.getProcessedApplications();
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 处理申请（批准或拒绝）
    @PutMapping("/{id}")
    public ResponseEntity<?> processApplication(@PathVariable Integer id, @RequestBody Map<String, Object> requestBody) {
        try {
            String status = (String) requestBody.get("status");
            String remarks = (String) requestBody.getOrDefault("remarks", "");
            Integer processorId = (Integer) requestBody.getOrDefault("processorId", null);
            
            ReviewerApplication application = reviewerApplicationService.processApplication(id, status, remarks, processorId);
            return ResponseEntity.ok(application);
        } catch (IllegalArgumentException e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 获取用户的申请历史
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserApplications(@PathVariable Integer userId) {
        try {
            List<ReviewerApplication> applications = reviewerApplicationService.getUserApplications(userId);
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    // 获取当前用户的申请历史
    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUserApplications() {
        try {
            org.springframework.security.core.Authentication auth = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            String email = auth.getName();
            if (email.equals("anonymousUser")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("用户未登录");
            }
            
            Optional<User> userOpt = userRepository.findByEmail(email);
            if (!userOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("用户不存在");
            }
            
            Integer userId = userOpt.get().getId();
            List<ReviewerApplication> applications = reviewerApplicationService.getUserApplications(userId);
            
            return ResponseEntity.ok(applications);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
} 