"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[624],{8624:(e,a,n)=>{n.r(a),n.d(a,{default:()=>v});n(4114);var t=n(641),i=n(3751),r={class:"case-form-container"},o={class:"upload-item"},l=["src"],s={class:"upload-actions"},u=["src"];function d(e,a,n,d,c,m){var g=(0,t.g2)("Plus"),f=(0,t.g2)("el-icon"),p=(0,t.g2)("ZoomIn"),h=(0,t.g2)("Delete"),v=(0,t.g2)("el-upload"),k=(0,t.g2)("el-dialog"),b=(0,t.g2)("el-form-item"),_=(0,t.g2)("el-button"),w=(0,t.g2)("el-form");return(0,t.uX)(),(0,t.CE)("div",r,[a[4]||(a[4]=(0,t.Lk)("div",{class:"page-header"},[(0,t.Lk)("h2",null,"新建标注")],-1)),(0,t.bF)(w,{ref:"form",model:c.caseData,"label-width":"100px"},{default:(0,t.k6)((function(){return[(0,t.bF)(b,{label:"医学影像",prop:"images"},{default:(0,t.k6)((function(){return[(0,t.bF)(v,{action:"#","list-type":"picture-card","auto-upload":!1,"file-list":c.imageList,"on-preview":m.handlePictureCardPreview,"on-change":m.handleImageChange,"on-remove":m.handleImageRemove,limit:5,accept:"image/*"},{file:(0,t.k6)((function(e){var a=e.file;return[(0,t.Lk)("div",o,[(0,t.Lk)("img",{class:"upload-image",src:a.url,alt:""},null,8,l),(0,t.Lk)("div",s,[(0,t.bF)(f,{onClick:(0,i.D$)((function(e){return m.handlePictureCardPreview(a)}),["stop"])},{default:(0,t.k6)((function(){return[(0,t.bF)(p)]})),_:2},1032,["onClick"]),(0,t.bF)(f,{onClick:(0,i.D$)((function(e){return m.handleImageRemove(a)}),["stop"])},{default:(0,t.k6)((function(){return[(0,t.bF)(h)]})),_:2},1032,["onClick"])])])]})),default:(0,t.k6)((function(){return[(0,t.bF)(f,null,{default:(0,t.k6)((function(){return[(0,t.bF)(g)]})),_:1})]})),_:1},8,["file-list","on-preview","on-change","on-remove"]),(0,t.bF)(k,{modelValue:c.dialogVisible,"onUpdate:modelValue":a[0]||(a[0]=function(e){return c.dialogVisible=e}),title:"预览"},{default:(0,t.k6)((function(){return[(0,t.Lk)("img",{"w-full":"",src:c.dialogImageUrl,alt:"Preview Image",style:{"max-width":"100%"}},null,8,u)]})),_:1},8,["modelValue"])]})),_:1}),(0,t.bF)(b,null,{default:(0,t.k6)((function(){return[(0,t.bF)(_,{type:"primary",onClick:m.handleNext},{default:(0,t.k6)((function(){return a[2]||(a[2]=[(0,t.eW)("下一步")])})),_:1,__:[2]},8,["onClick"]),(0,t.bF)(_,{onClick:a[1]||(a[1]=function(a){return e.$router.push("/")})},{default:(0,t.k6)((function(){return a[3]||(a[3]=[(0,t.eW)("返回首页")])})),_:1,__:[3]})]})),_:1})]})),_:1},8,["model"])])}n(8706),n(8980),n(4554),n(9089),n(739),n(2010),n(3110),n(9432);var c=n(8548),m=n(2505),g=n.n(m);const f={name:"CaseForm",components:{Plus:c.Plus,ZoomIn:c.ZoomIn,Delete:c.Delete},data:function(){return{caseData:{images:[]},imageList:[],dialogVisible:!1,dialogImageUrl:""}},methods:{handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},handleImageChange:function(e){var a=this;if(e.raw){var n=e.raw,t=new FileReader;t.readAsDataURL(n),t.onload=function(){var e=t.result;a.caseData.images.push({id:Date.now(),file:n,url:e,name:n.name,type:n.type,size:n.size})}}},handleImageRemove:function(e){var a=this.imageList.findIndex((function(a){return a.uid===e.uid}));-1!==a&&this.imageList.splice(a,1);var n=this.caseData.images.findIndex((function(a){return a.url===e.url||a.file===e.raw}));-1!==n&&this.caseData.images.splice(n,1)},handleNext:function(){var e=this;if(0!==this.caseData.images.length){var a=this.$loading({lock:!0,text:"正在上传图像...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),n=new FormData,t=this.caseData.images[0].file;n.append("file",t);var i=JSON.parse(localStorage.getItem("user")||"{}");i&&i.id&&n.append("user_id",i.id),g().post("/api/upload",n,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:function(e){var a=Math.round(100*e.loaded/e.total);console.log("上传进度："+a+"%")}}).then((function(n){if(console.log("图像上传成功:",n.data),!n.data||!n.data.id)return console.error("服务器返回的数据不完整",n.data),e.$message.error("服务器返回的数据不完整，无法继续"),void a.close();var i=n.data,r={id:i.id,url:i.url||e.caseData.images[0].url,filename:i.filename||t.name,original_name:i.original_name||t.name,path:i.path||"",server_data:i};console.log("保存到localStorage的图像数据:",r),localStorage.setItem("uploadedImages",JSON.stringify([r])),a.close(),e.$router.push("/cases/form")}))["catch"]((function(n){console.error("图像上传失败:",n);var t="上传失败";n.response?t+=": ".concat(n.response.status," - ").concat(JSON.stringify(n.response.data||"")):n.request?t+=": 未收到服务器响应":t+=": "+n.message,e.$message.error(t),a.close()}))}else this.$message.warning("请先上传医学影像")}}};var p=n(6262);const h=(0,p.A)(f,[["render",d],["__scopeId","data-v-4b3e272e"]]),v=h},9089:(e,a,n)=>{var t=n(6518),i=n(9504),r=Date,o=i(r.prototype.getTime);t({target:"Date",stat:!0},{now:function(){return o(new r)}})}}]);