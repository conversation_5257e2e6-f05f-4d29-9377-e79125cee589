<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>跳转到标注审核页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f7fa;
    }
    .container {
      text-align: center;
    }
    .redirect-message {
      margin-bottom: 20px;
    }
    .loader {
      border: 5px solid #f3f3f3;
      border-top: 5px solid #409EFF;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>正在跳转到标注审核页面...</h2>
    <div class="loader"></div>
    <p class="redirect-message">如果页面没有自动跳转，请<a href="/standalone-review">点击这里</a></p>
  </div>

  <script>
    // 页面加载后自动跳转
    window.onload = function() {
      // 检查是否有登录信息
      const userInfo = localStorage.getItem('user');
      if (!userInfo) {
        // 如果没有登录信息，先跳转到登录页面
        window.location.href = '/login?redirect=/standalone-review';
      } else {
        // 如果已登录，直接跳转到独立审核页面
        setTimeout(function() {
          window.location.href = '/standalone-review';
        }, 1000);
      }
    };
  </script>
</body>
</html> 