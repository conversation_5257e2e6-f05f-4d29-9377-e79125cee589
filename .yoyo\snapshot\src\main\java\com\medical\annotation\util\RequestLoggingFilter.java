package com.medical.annotation.util;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求日志过滤器，记录所有API请求和响应
 */
@Component
public class RequestLoggingFilter extends OncePerRequestFilter {

    private static final int MAX_PAYLOAD_LENGTH = 1000;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                    FilterChain filterChain) throws ServletException, IOException {
        
        // 检查路径，跳过静态资源
        String path = request.getRequestURI();
        if (isStaticResource(path)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        
        long startTime = System.currentTimeMillis();
        
        try {
            filterChain.doFilter(requestWrapper, responseWrapper);
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            
            // 只记录 /api 和 /medical/api 开头的请求
            if (path.contains("/api")) {
                String requestBody = getRequestBody(requestWrapper);
                String responseBody = getResponseBody(responseWrapper);
                
                logRequest(requestWrapper, requestBody, duration, responseWrapper.getStatus(), responseBody);
            }
            
            // 复制响应内容
            responseWrapper.copyBodyToResponse();
        }
    }
    
    private boolean isStaticResource(String path) {
        return path.contains("swagger") || 
               path.endsWith(".js") || 
               path.endsWith(".css") || 
               path.endsWith(".html") || 
               path.endsWith(".png") || 
               path.endsWith(".jpg") || 
               path.endsWith(".gif") || 
               path.endsWith(".ico");
    }
    
    private String getRequestBody(ContentCachingRequestWrapper request) {
        byte[] content = request.getContentAsByteArray();
        if (content.length == 0) {
            return "";
        }
        
        int length = Math.min(content.length, MAX_PAYLOAD_LENGTH);
        try {
            return new String(content, 0, length, request.getCharacterEncoding());
        } catch (UnsupportedEncodingException e) {
            return new String(content, 0, length, StandardCharsets.UTF_8);
        }
    }
    
    private String getResponseBody(ContentCachingResponseWrapper response) {
        byte[] content = response.getContentAsByteArray();
        if (content.length == 0) {
            return "";
        }
        
        int length = Math.min(content.length, MAX_PAYLOAD_LENGTH);
        try {
            return new String(content, 0, length, response.getCharacterEncoding());
        } catch (UnsupportedEncodingException e) {
            return new String(content, 0, length, StandardCharsets.UTF_8);
        }
    }
    
    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        return headers;
    }
    
    private void logRequest(ContentCachingRequestWrapper request, String requestBody, long duration,
                           int status, String responseBody) {
        StringBuilder sb = new StringBuilder();
        sb.append("\n========== API请求日志 ==========\n");
        sb.append("路径: ").append(request.getMethod()).append(" ").append(request.getRequestURI());
        
        String queryString = request.getQueryString();
        if (queryString != null) {
            sb.append("?").append(queryString);
        }
        
        sb.append("\n客户端IP: ").append(request.getRemoteAddr());
        sb.append("\n耗时: ").append(duration).append("ms");
        sb.append("\n状态码: ").append(status);
        
        // 记录请求参数
        if (!Collections.list(request.getParameterNames()).isEmpty()) {
            sb.append("\n请求参数: ");
            request.getParameterMap().forEach((key, values) -> {
                sb.append("\n  ").append(key).append("=").append(String.join(", ", values));
            });
        }
        
        // 记录请求体，但仅限于JSON等文本请求
        String contentType = request.getContentType();
        if (contentType != null && (contentType.contains("json") || contentType.contains("form"))) {
            if (requestBody != null && !requestBody.isEmpty()) {
                sb.append("\n请求体: ").append(requestBody);
                if (requestBody.length() >= MAX_PAYLOAD_LENGTH) {
                    sb.append("... [截断]");
                }
            }
        }
        
        // 记录响应体，但仅限于JSON等文本响应
        if (responseBody != null && !responseBody.isEmpty()) {
            sb.append("\n响应体: ").append(responseBody);
            if (responseBody.length() >= MAX_PAYLOAD_LENGTH) {
                sb.append("... [截断]");
            }
        }
        
        sb.append("\n===================================");
        
        // 输出日志
        System.out.println(sb.toString());
    }
} 