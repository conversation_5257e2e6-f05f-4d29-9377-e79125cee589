<template>
  <div class="image-canvas">
    <img :src="src" ref="imageRef" @click="handleCanvasClick" />
    <canvas ref="canvasRef"></canvas>
  </div>
</template>

<script>
export default {
  props: ['src'],
  methods: {
    handleCanvasClick(e) {
      const rect = this.$refs.imageRef.getBoundingClientRect()
      this.$emit('annotate', {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      })
    }
  }
}
</script>

<style scoped>
.image-canvas {
  position: relative;
  max-width: 100%;
}
canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
</style>