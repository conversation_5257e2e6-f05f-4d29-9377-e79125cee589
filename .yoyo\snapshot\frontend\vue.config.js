const { defineConfig } = require('@vue/cli-service')
const webpack = require('webpack')

/**
 * 中央API配置 - 在这里更改服务器地址将同时影响开发环境和生产环境
 * 
 * 可以通过.env文件或环境变量覆盖这些值:
 * VUE_APP_API_URL - 服务器地址和端口
 * VUE_APP_CONTEXT_PATH - 上下文路径
 * VUE_APP_API_PREFIX - API前缀
 */
const API_TARGET_URL = process.env.VUE_APP_API_URL || 'http://192.168.2.43:8085'
const API_CONTEXT_PATH = process.env.VUE_APP_CONTEXT_PATH || '/medical'
const API_PREFIX = process.env.VUE_APP_API_PREFIX || '/api'

// 将这些值写入配置文件，在前端代码创建api.config.js时可以读取
process.env.VUE_APP_API_URL = API_TARGET_URL;
process.env.VUE_APP_CONTEXT_PATH = API_CONTEXT_PATH;
process.env.VUE_APP_API_PREFIX = API_PREFIX;

module.exports = {
  lintOnSave: false,
  configureWebpack: {
    plugins: [
      // Define Vue feature flags
      new webpack.DefinePlugin({
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: JSON.stringify(false)
      })
    ],
    performance: {
      hints: process.env.NODE_ENV === 'production' ? 'warning' : false,
      maxEntrypointSize: 2000000,  // 提高限制到2MB (开发中)
      maxAssetSize: 1500000  // 提高限制到1.5MB (开发中)
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        maxInitialRequests: Infinity,
        minSize: 20000,
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name(module) {
              // 获取包名
              if (!module.context) {
                return 'npm.unknown';
              }
              const match = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/);
              const packageName = match ? match[1] : 'unknown';
              // 将包名转换为有效的文件名
              return `npm.${packageName.replace('@', '')}`;
            }
          },
          elementPlus: {
            name: 'chunk-elementplus',
            priority: 30,
            test: /[\\/]node_modules[\\/]_?element-plus(.*)/
          },
          icons: {
            name: 'chunk-icons',
            priority: 25,
            test: /[\\/]node_modules[\\/]_?@element-plus[\\/]icons-vue(.*)/
          }
        }
      }
    }
  },
  // 生产环境中移除console日志
  chainWebpack: config => {
    if (process.env.NODE_ENV === 'production') {
      config.optimization.minimizer('terser').tap(args => {
        args[0].terserOptions.compress.drop_console = true
        args[0].terserOptions.compress.drop_debugger = true
        return args
      })
    }
  },
  devServer: {
    port: 8080,
    proxy: {
      [API_PREFIX]: {
        target: API_TARGET_URL,
        changeOrigin: true,
        pathRewrite: {
          [`^${API_PREFIX}`]: `${API_CONTEXT_PATH}${API_PREFIX}`
        }
      }
    },
    host: '0.0.0.0',
    allowedHosts: 'all',
    // 添加更多配置以提高调试能力
    client: {
      logging: 'info',
      overlay: {
        errors: true,
        warnings: false
      },
      // 使用ws作为websocket传输方式
      webSocketTransport: 'ws',
      // 增加重连配置
      reconnect: 5
    }
  },
  // 禁用生产环境的Source Map
  productionSourceMap: false,
  // publicPath is the base URL for your app when deployed
  // For production, this could be a subdirectory on your server
  publicPath: '/'
} 