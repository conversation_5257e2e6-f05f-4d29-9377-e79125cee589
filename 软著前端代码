前端核心源代码 - 软件著作权申请
=========================================


// --- 文件开始: frontend/src/main.js ---
// =================================================================================

// 导入polyfills解决模块问题
import './polyfills.js';

// 导入Element Plus统一修复模块
import './element-plus-unified-fix.js';

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import axios from 'axios'
// 导入API配置
import { API_BASE_URL, API_CONTEXT_PATH, DASHBOARD_STATS_URL, DASHBOARD_STATS_WITH_PARAMS } from './config/api.config'

// 全局引入 Element Plus
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 单独引入 Element Plus 组件的方法，确保正确注册
import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'

// 覆盖ElMessage方法，使其不显示任何提示
// 替换为空函数
ElMessage.success = () => {};
ElMessage.error = () => {};
ElMessage.info = () => {};
ElMessage.warning = () => {};

// 引入自定义权限指令
import { registerPermissionDirective } from './directives/permission'

// 引入组件暴露模块
import './expose-components'

// 引入全局样式
import './assets/css/style.css'

// 引入背景样式
import '@/assets/css/background.css'

// 引入调试工具
import { enableAnnotationDebug } from './utils/debug.js';

// 添加用户权限修复工具
window.fixUserPermission = function() {
  try {
    // 获取当前用户
    const userStr = localStorage.getItem('user');
    if (!userStr) {
      return {success: false, message: '未找到已登录用户'};
    }
    
    const user = JSON.parse(userStr);
    
    // 检查用户角色
    if (!user.role) {
      // 根据用户ID模式判断角色
      if (user.customId && user.customId.startsWith('1')) {
        user.role = 'ADMIN';
      } else if (user.customId && user.customId.startsWith('3')) {
        user.role = 'REVIEWER';
      } else {
        user.role = 'DOCTOR';
      }
      
      // 保存修复后的用户数据
      localStorage.setItem('user', JSON.stringify(user));
      
      // 更新store
      store.commit('setUser', user);
      
      return {success: true, message: `已修复用户角色为: ${user.role}`};
    } else {
      // 检查角色是否与ID模式一致
      let expectedRole = null;
      
      if (user.customId && user.customId.startsWith('1')) {
        expectedRole = 'ADMIN';
      } else if (user.customId && user.customId.startsWith('3')) {
        expectedRole = 'REVIEWER';
      } else if (user.customId && user.customId.startsWith('2')) {
        expectedRole = 'DOCTOR';
      }
      
      // 如果有期望角色且与当前角色不符，进行修复
      if (expectedRole && user.role !== expectedRole) {
        user.role = expectedRole;
        localStorage.setItem('user', JSON.stringify(user));
        // 更新store
        store.commit('setUser', user);
        return {success: true, message: `已将用户角色修复为${expectedRole}`};
      }
      
      return {success: true, message: `用户角色已存在: ${user.role}`};
    }
  } catch (error) {
    return {success: false, message: `修复失败: ${error.message}`};
  }
};

// 替换localStorage监听为空函数
const originalSetItem = localStorage.setItem;
const originalRemoveItem = localStorage.removeItem;

localStorage.setItem = function(key, value) {
  originalSetItem.call(this, key, value);
};

localStorage.removeItem = function(key) {
  originalRemoveItem.call(this, key);
};

// 设置axios全局默认值
// axios.defaults.baseURL = '/api'; // 不使用baseURL，避免路径重复
axios.defaults.withCredentials = true; // 允许跨域请求发送cookies
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.timeout = 10000; // 10秒超时

// 移除所有日志输出的请求拦截器
axios.interceptors.request.use(
  config => {
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 移除所有日志输出的响应拦截器
axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    return Promise.reject(error);
  }
);

const app = createApp(App)

// 禁用全局错误处理器中的日志输出
app.config.errorHandler = function(err, vm, info) {
  // 错误处理逻辑保留，但不输出日志
};

// 禁用全局未捕获的Promise异常处理中的日志输出
window.addEventListener('unhandledrejection', event => {
  // 异常处理逻辑保留，但不输出日志
});

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局配置
app.config.globalProperties.$axios = axios

// 正确注册Element Plus的消息组件
app.config.globalProperties.$message = ElMessage
app.config.globalProperties.$notify = ElNotification
app.config.globalProperties.$msgbox = ElMessageBox
app.config.globalProperties.$alert = ElMessageBox.alert
app.config.globalProperties.$confirm = ElMessageBox.confirm
app.config.globalProperties.$prompt = ElMessageBox.prompt
app.config.globalProperties.$loading = ElLoading.service

// 注册权限指令
registerPermissionDirective(app)

// 注册Element Plus
app.use(store)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})

// 将消息组件暴露给全局，以便API响应拦截器可以使用
window.$message = ElMessage

// 定义全局的仪表盘数据刷新函数
window.refreshDashboardStats = async () => {
  console.log('全局刷新函数 window.refreshDashboardStats 被调用');
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return;
    const user = JSON.parse(userStr);
    // 只使用数字ID
    const userId = user.id;
    if (!userId) return;

    // 确保使用带个人统计参数的正确URL
    const url = DASHBOARD_STATS_WITH_PARAMS(userId);
    console.log('全局刷新函数调用的URL:', url);
    
    const response = await axios.get(url, {
      headers: { 'Cache-Control': 'no-cache' }
    });

    if (response.data) {
      store.commit('setDashboardStats', response.data);
      console.log('全局统计数据已刷新并存入store');
    }
  } catch (error) {
    console.error('全局刷新仪表盘数据失败:', error);
  }
};

// 添加禁用日志输出的函数
const disableConsoleOutput = () => {
  if (typeof window !== 'undefined') {
    window.console = {
      ...console,
      log: function() {},
      info: function() {},
      warn: function() {},
      error: function() {},
      debug: function() {}
    };
  }
};

// 初始化应用
app.mount('#app');

// 禁用控制台输出
disableConsoleOutput();

// 在应用挂载后执行数据强制更新
setTimeout(() => {
  console.log('🔄 应用挂载后尝试强制更新统计数据');
  
  try {
    // 1. 尝试从localStorage获取统计数据
    const statsData = JSON.parse(localStorage.getItem('dashboardStats') || '{}');
    if (statsData && statsData.totalCount) {
      console.log('找到统计数据:', statsData);
      
      // 2. 直接更新DOM
      const statElements = document.querySelectorAll('.stat-value');
      if (statElements && statElements.length >= 5) {
        statElements[0].textContent = statsData.totalCount || 0;
        statElements[1].textContent = statsData.draftCount || 0;
        statElements[2].textContent = statsData.reviewedCount || 0;
        statElements[3].textContent = statsData.submittedCount || 0;
        statElements[4].textContent = statsData.approvedCount || 0;
        console.log('📊 统计数据DOM元素已强制更新!');
      } else {
        console.log('未找到统计卡片DOM元素:', statElements ? statElements.length : 0);
        
        // 如果找不到元素，可能是还没渲染，再等待一小段时间
        setTimeout(() => {
          const retryElements = document.querySelectorAll('.stat-value');
          if (retryElements && retryElements.length >= 5) {
            retryElements[0].textContent = statsData.totalCount || 0;
            retryElements[1].textContent = statsData.draftCount || 0;
            retryElements[2].textContent = statsData.reviewedCount || 0;
            retryElements[3].textContent = statsData.submittedCount || 0;
            retryElements[4].textContent = statsData.approvedCount || 0;
            console.log('📊 第二次尝试：统计数据DOM元素已强制更新!');
          }
        }, 300);
      }
    } else {
      console.log('localStorage中没有找到统计数据，尝试直接获取');
      
      // 3. 如果没有localStorage数据，获取当前用户ID后请求
      const axios = require('axios').default;
      
      // 从localStorage获取当前用户ID
      try {
        const userStr = localStorage.getItem('user') || '{}';
        const user = JSON.parse(userStr);
        // 只使用数字ID
        const userId = user.id;
        
        if (!userId) {
          console.log('找不到有效的用户ID，跳过数据获取');
          return;
        }
        
        console.log('尝试获取用户', userId, '的统计数据');
        
        // 使用无认证限制的API端点
        axios.get(`${API_BASE_URL}${API_CONTEXT_PATH}/api/stats-v2/dashboard/${userId}?t=${Date.now()}&r=${Math.random()}&forcePersonalStats=true&view=personal`, {
          headers: {
            'Cache-Control': 'no-cache, no-store',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
          .then(response => {
            console.log('直接获取统计数据成功:', response.data);
            
            // 更新DOM
            const statElements = document.querySelectorAll('.stat-value');
            if (statElements && statElements.length >= 5) {
              statElements[0].textContent = response.data.totalCount || 0;
              statElements[1].textContent = response.data.draftCount || 0;
              statElements[2].textContent = response.data.reviewedCount || 0;
              statElements[3].textContent = response.data.submittedCount || 0;
              statElements[4].textContent = response.data.approvedCount || 0;
              console.log('📊 通过API获取的统计数据已更新到DOM!');
            }
          })
          .catch(error => {
            console.error('直接获取统计数据失败:', error);
          });
      } catch (error) {
        console.error('获取用户ID失败:', error);
      }
    }
  } catch (error) {
    console.error('强制更新统计数据失败:', error);
  }
}, 500);

// 在应用挂载后执行数据库清理
// console.log('应用启动，执行数据库清理...');
// cleanInvalidImagePairs(); 

// 添加window.location变更监听
const originalPushState = history.pushState;
const originalReplaceState = history.replaceState;

// 重写pushState
history.pushState = function() {
  console.log('[URL变更] history.pushState 调用:', {
    状态: arguments[0],
    标题: arguments[1],
    URL: arguments[2],
    当前时间: new Date().toLocaleTimeString(),
    调用方法: '直接调用'
  });
  
  // 获取调用堆栈
  const stack = new Error().stack;
  console.log('[URL变更] pushState调用堆栈:', stack);
  
  return originalPushState.apply(this, arguments);
};

// 重写replaceState
history.replaceState = function() {
  console.log('[URL变更] history.replaceState 调用:', {
    状态: arguments[0],
    标题: arguments[1],
    URL: arguments[2],
    当前时间: new Date().toLocaleTimeString(),
    调用方法: '直接调用'
  });
  
  // 获取调用堆栈
  const stack = new Error().stack;
  console.log('[URL变更] replaceState调用堆栈:', stack);
  
  return originalReplaceState.apply(this, arguments);
};

// 监听popstate事件
window.addEventListener('popstate', function(event) {
  console.log('[URL变更] popstate事件触发:', {
    状态: event.state,
    当前URL: window.location.href,
    当前时间: new Date().toLocaleTimeString(),
    调用方法: 'popstate事件'
  });
});

// 创建全局事件总线用于组件间通信
import mitt from 'mitt';
const eventBus = mitt();

// 全局事件总线，用于组件间通信
window.eventBus = eventBus;

// 启用标注调试模式 - 修复Vue未定义错误
app.config.globalProperties.$enableAnnotationDebug = function() {
  enableAnnotationDebug();
  app.config.globalProperties.$isAnnotationDebugEnabled = true;
};

// 在进入应用后自动启用标注调试模式（可通过控制台禁用）
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    if (window.location.href.includes('annotation') || 
        window.location.href.includes('case') ||
        localStorage.getItem('enableDebug') === 'true') {
      try {
        enableAnnotationDebug();
        console.log('标注调试模式已自动启用');
      } catch (e) {
        console.error('启用调试模式失败', e);
      }
    }
  }, 1000);
}); 


// --- 文件结束: frontend/src/main.js ---


// --- 文件开始: frontend/src/App.vue ---
// =================================================================================

<template>
  <router-view />
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100%;
}

/* 覆盖Element Plus的默认样式 */
.el-menu {
  border-right: none !important;
}

.el-menu-item.is-active {
  background-color: #1890ff !important;
}

.el-card {
  border-radius: 4px;
  border: none;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 
              0 3px 6px 0 rgba(0, 0, 0, 0.12), 
              0 5px 12px 4px rgba(0, 0, 0, 0.09) !important;
}

.el-button--primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.el-button--primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}
</style> 


// --- 文件结束: frontend/src/App.vue ---


// --- 文件开始: frontend/src/router/index.js ---
// =================================================================================

import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/components/layout/MainLayout.vue'
import SimpleLayout from '@/components/layout/SimpleLayout.vue'
import Dashboard from '@/views/Dashboard.vue'
import store from '../store'
import { canAccessRoute } from '../utils/permissions'
import { preloadDashboardData } from '@/views/Dashboard.vue'
import { API_BASE_URL, DASHBOARD_STATS_URL } from '../config/api.config'

// 导入调试页面组件
const DebugView = () => import('../views/DebugView.vue');
// 预先导入血管瘤诊断组件，避免懒加载问题
const HemangiomaDiagnosis = () => import('../views/HemangiomaDiagnosis.vue');

const routes = [
  {
    path: '/app',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/app/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { 
          keepAlive: true,
          title: '工作台'
        }
      },
      {
        path: 'cases',
        name: 'Cases',
        component: () => import('@/views/Cases.vue')
      },
      {
        path: 'cases/new',
        name: 'NewCase',
        component: () => import('@/views/CaseForm.vue')
      },
      {
        path: 'cases/edit/:id',
        name: 'EditCase',
        component: () => import('@/views/CaseForm.vue')
      },
      {
        path: 'cases/view/:id',
        name: 'ViewCase',
        component: () => import('@/views/CaseView.vue')
      },
      // {
      //   path: 'cases/form/:id',
      //   name: 'CaseDetailForm',
      //   component: () => import('@/views/CaseDetailForm.vue'),
      //   props: true
      // },
      // {
      //   path: 'cases/structured-form',
      //   name: 'CaseStructuredForm',
      //   component: () => import('@/views/CaseStructuredForm.vue')
      // },
      {
        path: 'case/:id/annotate-and-form',
        name: 'AnnotationAndForm',
        component: () => import('@/views/AnnotationAndForm.vue'),
        props: true,
        children: [
          {
            path: '',
            name: 'EmbeddedForm',
            component: () => import('@/views/CaseStructuredForm.vue'),
        props: true
          }
        ]
      },
      {
        path: 'review',
        name: 'Review',
        component: () => import('@/views/Review.vue')
      },
      {
        path: 'teams',
        name: 'Teams',
        component: () => import('@/views/Teams.vue')
      },
      {
        path: 'teams/applications',
        name: 'TeamApplications',
        component: () => import('@/components/TeamApplicationManagement.vue'),
        meta: {
          requiresAuth: true,
          title: '团队申请管理',
          accessRoles: ['ADMIN', 'REVIEWER']
        }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/Users.vue'),
        meta: { 
          requiresAdmin: true,
          title: '部门成员'
        }
      },
      {
        path: 'hemangioma-diagnosis',
        name: 'HemangiomaDiagnosis',
        component: HemangiomaDiagnosis,
        meta: {
          requiresAuth: true,
          title: '血管瘤诊断',
          accessRoles: ['ADMIN', 'DOCTOR', 'REVIEWER']
        }
      },
      {
        path: 'annotation-reviews',
        name: 'AnnotationReviews',
        component: () => import('@/views/AnnotationReview.vue'),
        meta: {
          requiresAuth: true,
          title: '标注审核',
          // 允许审核医生和管理员访问
          accessRoles: ['ADMIN', 'REVIEWER']
        }
      }
    ]
  },
  {
    path: '/',
    name: 'Root',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue')
  },
  {
    path: '/images',
    name: 'ImageList',
    component: () => import('../views/ImageList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/images/:id',
    name: 'ImageDetail',
    component: () => import('../views/ImageDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/images/upload',
    name: 'ImageUpload',
    component: () => import('../views/ImageUpload.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/annotations',
    name: 'ImageAnnotation',
    component: () => import('@/views/AnnotationAndForm.vue')
  },
  {
    path: '/cases/structured-form',
    name: 'PublicCaseStructuredForm',
    component: () => import('@/views/CaseStructuredForm.vue')
  },
  {
    path: '/tag-test',
    name: 'TagTest',
    component: () => import('../components/TagTest.vue')
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('../views/Admin.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/reviewer-applications',
    name: 'ReviewerApplications',
    component: () => import('../views/admin/ReviewerApplications.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/standalone-review/:id',
    component: SimpleLayout,
    children: [
      {
        path: '',
        name: 'ReviewStandalone',
        component: () => import('@/views/AnnotationReview.vue'),
        props: true
      }
    ],
    meta: {
      requiresAuth: true,
      title: '标注审核',
      accessRoles: ['ADMIN', 'REVIEWER']
    }
  },
  {
    path: '/app/debug',
    name: 'debug',
    component: DebugView,
    meta: { 
      requiresAuth: true,
      title: '标注调试工具'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

router.beforeEach((to, from, next) => {
  // 记录导航详情
  console.log('[路由导航] 导航开始:', {
    从: from.fullPath,
    到: to.fullPath,
    时间: new Date().toLocaleTimeString(),
    query参数: to.query,
    来源页面: document.referrer
  });
  
  // 设置应用内操作标记
  if (from.name) { // 如果from.name存在，说明是应用内导航
    sessionStorage.setItem('isAppOperation', 'true');
  }
  
  // 检查是否从表单页面保存后进行导航
  const isNavigatingAfterSave = sessionStorage.getItem('isNavigatingAfterSave') === 'true';
  const isNavigatingFromForm = sessionStorage.getItem('navigatingFromForm') === 'true';
  
  // 如果是从表单保存后导航，且目标是登录页面，则重定向到工作台
  if ((isNavigatingAfterSave || isNavigatingFromForm) && to.path === '/login') {
    console.log('检测到从表单页面保存后导航到登录页面，重定向到工作台');
    sessionStorage.removeItem('isNavigatingAfterSave');
    sessionStorage.removeItem('navigatingFromForm');
    return next('/app/dashboard');
  }
  
  // 公开页面列表 - 修复：移除需要认证的页面
  const publicPages = ['/login', '/register', '/tag-test'];
  const authRequired = !publicPages.includes(to.path) && !to.matched.some(record => publicPages.includes(record.path));
  
  // 获取用户信息
  const user = JSON.parse(localStorage.getItem('user'))
  const isAuthenticated = store.getters.isAuthenticated
  
  // 保存当前路由，用于登录后重定向
  if (to.path !== '/login' && !isAuthenticated) {
    sessionStorage.setItem('redirectPath', to.fullPath);
  }
  
  // 如果即将访问工作台页面，提前预加载数据
  if (to.path === '/app/dashboard' || to.path === '/app' || to.path === '/app/') {
    // 移除独立调用，减少请求次数
    // preloadDashboardData();
    
    // 添加：如果当前已登录，立即调用全局统计数据刷新函数
    if (user && window.refreshDashboardStats) {
      console.log('[路由导航] 检测到前往工作台，预先刷新统计数据');
      // 移除这个全局刷新函数调用，减少冗余请求
      // setTimeout(() => {
      //  window.refreshDashboardStats();
      // }, 0);
    }
  }
  
  // 1. 检查是否需要登录
  if (authRequired && !user) {
    return next('/login')
  }
  
  // 2. 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  if (requiresAuth && !isAuthenticated) {
    // 如果当前是在表单页面且设置了特殊标记
    if (from.path.includes('/cases/structured-form') && sessionStorage.getItem('allowFormOperation') === 'true') {
      console.log('表单操作中，即使未认证也允许完成此次导航');
      sessionStorage.removeItem('allowFormOperation');  // 使用后移除标记
      return next('/app/dashboard');  // 导航到工作台
    }
    
    return next('/login')
  }
  
  // 3. 检查路由是否需要特定权限
  const userRole = user ? user.role : null
  if (requiresAuth && userRole) {
    // 添加调试日志
    console.log('[路由导航] 权限检查:', {
      目标路由: to.path,
      用户角色: userRole,
      路由权限要求: to.meta.accessRoles || '无特定角色要求',
      是否需要管理员: to.matched.some(record => record.meta.requiresAdmin),
      是否需要审核医生: to.matched.some(record => record.meta.requiresReviewer),
      是否需要标注医生: to.matched.some(record => record.meta.requiresDoctor)
    });

    // 原有权限检查代码保持不变
    const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)
    const requiresReviewer = to.matched.some(record => record.meta.requiresReviewer)
    const requiresDoctor = to.matched.some(record => record.meta.requiresDoctor)
    
    if (
      (requiresAdmin && userRole !== 'ADMIN') ||
      (requiresReviewer && userRole !== 'REVIEWER' && userRole !== 'ADMIN') ||
      (requiresDoctor && userRole !== 'DOCTOR' && userRole !== 'ADMIN')
    ) {
      // 添加详细日志
      console.warn(`[路由导航] 权限检查失败: 用户角色 ${userRole} 无权访问 ${to.path}`);
      return next('/app/dashboard')
    }
    
    // 然后使用权限配置进行进一步检查
    if (!canAccessRoute(userRole, to.path)) {
      console.warn(`[路由导航] 权限配置检查失败: 用户角色 ${userRole} 无权访问 ${to.path}`);
      return next('/app/dashboard')
    }
  }
  
  // 4. 特定路由重定向
  if (to.path === '/dashboard') {
    return next('/app/dashboard')
  } else if (to.path === '/cases') {
    return next('/app/cases')
  }
  
  // 5. 通过所有检查，允许访问
  next()
})

// 添加全局后置钩子
router.afterEach((to, from) => {
  // 记录导航完成详情
  console.log('[路由导航] 导航完成:', {
    从: from.fullPath,
    到: to.fullPath,
    时间: new Date().toLocaleTimeString(),
    当前URL: window.location.href,
    导航状态: {
      isAppOperation: sessionStorage.getItem('isAppOperation'),
      isNavigatingAfterSave: sessionStorage.getItem('isNavigatingAfterSave'),
      returningToWorkbench: sessionStorage.getItem('returningToWorkbench'),
      isLogoutOperation: sessionStorage.getItem('isLogoutOperation')
    }
  });
  
  // 设置应用内操作标记
  sessionStorage.setItem('isAppOperation', 'true');
  
  // 确保不在登录页面时，设置应用内操作状态
  if (router.currentRoute.value.path !== '/login') {
    sessionStorage.setItem('isAppOperation', 'true');
  }
  
  // 添加进入Dashboard页面时的数据刷新逻辑
  if (to.path === '/app/dashboard' || to.path === '/app/' || to.path === '/app') {
    console.log('[路由导航] 检测到进入工作台页面，立即执行统计数据获取');
    
    try {
      // 获取当前用户ID
      const userStr = localStorage.getItem('user');
      if (!userStr) return;
      
      const user = JSON.parse(userStr);
      // 只使用数字ID
      const userId = user.id;
      if (!userId) return;
      
      // 使用同步XHR请求立即获取数据
      const xhr = new XMLHttpRequest();
      xhr.open('GET', `${DASHBOARD_STATS_URL}/${userId}?t=${Date.now()}&forcePersonalStats=true&view=personal`, false); // 同步请求
      xhr.send();
      
      if (xhr.status === 200) {
        console.log('[路由导航] 统计数据获取成功');
        const data = JSON.parse(xhr.responseText);
        
        // 缓存数据到全局变量和localStorage
        window.dashboardStats = {
          totalCount: parseInt(data.totalCount || 0),
          draftCount: parseInt(data.draftCount || 0),
          reviewedCount: parseInt(data.reviewedCount || 0),
          submittedCount: parseInt(data.submittedCount || 0),
          approvedCount: parseInt(data.approvedCount || 0),
          rejectedCount: parseInt(data.rejectedCount || 0),
          dataSource: "navigation_xhr",
          timestamp: Date.now()
        };
        
        // 保存到localStorage以便下次使用
        localStorage.setItem('dashboardStats', JSON.stringify(window.dashboardStats));
        
        // 如果DOM已加载，直接更新统计卡片
        setTimeout(() => {
          const statElements = document.querySelectorAll('.stat-value');
          if (statElements && statElements.length >= 5) {
            statElements[0].textContent = data.totalCount || 0;
            statElements[1].textContent = data.draftCount || 0;
            statElements[2].textContent = data.reviewedCount || 0;
            statElements[3].textContent = data.submittedCount || 0;
            statElements[4].textContent = data.approvedCount || 0;
            console.log('[路由导航] 统计数据DOM已更新');
          }
        }, 100); // 短暂延迟确保DOM加载
      }
    } catch (e) {
      console.error('[路由导航] 获取统计数据失败:', e);
    }
  }
})

export default router 


// --- 文件结束: frontend/src/router/index.js ---


// --- 文件开始: frontend/src/router/router.js ---
// =================================================================================

{
  path: '/combined-view/:caseId',
  name: 'CombinedView',
  component: () => import('@/views/CaseStructuredForm.vue'),
  props: true
}


// --- 文件结束: frontend/src/router/router.js ---


// --- 文件开始: frontend/src/store/index.js ---
// =================================================================================

import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import auth from './modules/auth'
import images from './modules/images'
import users from './modules/users'
import stats from './modules/stats'
import annotation from './modules/annotation'
import teamApplications from './modules/teamApplications' // 添加团队申请模块

export default createStore({
  state: {
    // 标注进度状态
    annotationProgress: {
      currentStep: 0, // 0: 新建标注, 1: 图像标注, 2: 病例信息填写
      imageId: null,   // 当前标注的图像ID
      formData: null,  // 表单数据
      lastUpdated: null // 最后更新时间
    }
  },
  getters: {
    isAuthenticated: state => state.auth.isAuthenticated,
    // 获取标注进度
    getAnnotationProgress: state => state.annotationProgress,
    // 检查是否有未完成的标注
    hasUnfinishedAnnotation: state => {
      return state.annotationProgress.imageId !== null && 
             state.annotationProgress.currentStep > 0;
    }
  },
  mutations: {
    // 保存标注进度
    saveAnnotationProgress(state, { step, imageId, formData }) {
      // 对formData进行处理，避免存储过大的对象
      let simplifiedFormData = null;
      if (formData) {
        // 过滤掉大型对象属性或代理对象，只保留简单数据
        simplifiedFormData = {};
        Object.keys(formData).forEach(key => {
          // 检查是否为代理对象或复杂对象
          const value = formData[key];
          if (typeof value !== 'object' || value === null) {
            simplifiedFormData[key] = value;
          } else if (Array.isArray(value)) {
            // 对于数组，保留其字符串值
            simplifiedFormData[key] = value.map(item => 
              typeof item === 'string' ? item : JSON.stringify(item)
            );
          } else {
            // 对于对象，转换为字符串
            try {
              simplifiedFormData[key] = JSON.stringify(value);
            } catch(e) {
              console.warn('无法序列化表单数据字段:', key);
            }
          }
        });
      }
      
      state.annotationProgress = {
        currentStep: step,
        imageId: imageId,
        formData: simplifiedFormData,
        lastUpdated: new Date().toISOString()
      };
      console.log('保存标注进度:', state.annotationProgress);
    },
    // 清除标注进度
    clearAnnotationProgress(state) {
      state.annotationProgress = {
        currentStep: 0,
        imageId: null,
        formData: null,
        lastUpdated: null
      };
      console.log('清除标注进度');
    }
  },
  actions: {
    // 保存当前标注进度
    saveProgress({ commit }, { step, imageId, formData }) {
      commit('saveAnnotationProgress', { step, imageId, formData });
    },
    // 完成标注并清除进度
    completeAnnotation({ commit }) {
      commit('clearAnnotationProgress');
    }
  },
  modules: {
    auth,
    images,
    users,
    stats,
    annotation,
    teamApplications // 添加团队申请模块到Vuex存储中
  },
  plugins: [
    createPersistedState({
      paths: ['auth', 'annotationProgress.currentStep', 'annotationProgress.imageId', 'annotationProgress.lastUpdated', 'teamApplications'],
      storage: {
        getItem: key => {
          try {
            return localStorage.getItem(key);
          } catch (err) {
            console.error('localStorage getItem error:', err);
            return null;
          }
        },
        setItem: (key, value) => {
          try {
            // 如果数据过大，先尝试清理一些旧数据
            if (value && value.length > 1000000) {
              console.warn('数据过大，尝试清理localStorage');
              localStorage.removeItem('vuex');
            }
            localStorage.setItem(key, value);
          } catch (err) {
            console.error('localStorage setItem error:', err);
            // 如果失败，尝试清理localStorage后再保存核心数据
            try {
              for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key !== 'user' && key !== 'token') {
                  localStorage.removeItem(key);
                }
              }
              localStorage.setItem(key, value);
            } catch (e) {
              console.error('无法保存数据到localStorage，即使在清理后:', e);
            }
          }
        },
        removeItem: key => {
          try {
            localStorage.removeItem(key);
          } catch (err) {
            console.error('localStorage removeItem error:', err);
          }
        }
      }
    })
  ]
}) 


// --- 文件结束: frontend/src/store/index.js ---


// --- 文件开始: frontend/src/views/AnnotationAndForm.vue ---
// =================================================================================

<template>
  <div class="annotation-container">
    <div class="page-header">
      <h2>图像标注</h2>
      <el-button type="primary" size="small" @click="refreshData">
        <el-icon><Refresh /></el-icon> 刷新数据
      </el-button>
    </div>

    <div class="main-content">
      <!-- 标注工具栏 -->
      <div class="toolbar">
        <div class="tool-section">
          <h3>血管瘤分类</h3>
          <!-- 第一级：主分类 -->
          <el-select
            v-model="selectedMainCategory"
            placeholder="请选择主分类"
            style="width: 100%; margin-bottom: 10px;"
            @change="onMainCategoryChange"
          >
            <el-option label="真性血管肿瘤" value="真性血管肿瘤"></el-option>
            <el-option label="血管畸形" value="血管畸形"></el-option>
            <el-option label="血管假瘤/易混淆病变" value="血管假瘤/易混淆病变"></el-option>
          </el-select>

          <!-- 第二级：具体类型 -->
          <el-select
            v-model="selectedTag"
            placeholder="请选择具体类型"
            style="width: 100%"
            :disabled="!selectedMainCategory"
          >
            <el-option
              v-for="tag in availableSubCategories"
              :key="tag.value"
              :label="tag.label"
              :value="tag.value"
            ></el-option>
          </el-select>
        </div>
        
        <div class="tool-section">
          <h3>标注工具</h3>
          <el-radio-group v-model="currentTool" style="display: block; margin-top: 10px;">
            <el-radio label="rectangle">矩形框</el-radio>
          </el-radio-group>
        </div>

        <div class="annotations-list">
          <h3>已添加标注 
            <el-button type="text" size="small" @click="reloadAnnotations" title="刷新标注">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </h3>
          <div v-if="filteredAnnotations.length === 0" class="empty-annotations">
            <p>暂无标注，请在图片上绘制矩形框添加标注</p>
          </div>
          <el-table v-else :data="filteredAnnotations" style="width: 100%">
            <el-table-column label="编号" width="60">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="tag" label="标签" width="90" />
            <el-table-column label="坐标" width="120">
              <template #default="scope">
                <div class="coordinates">
                  <small>X: {{ Math.round(scope.row.x) }}</small><br>
                  <small>Y: {{ Math.round(scope.row.y) }}</small>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="尺寸" width="120">
              <template #default="scope">
                <div class="dimensions">
                  <small>W: {{ Math.round(scope.row.width) }}</small><br>
                  <small>H: {{ Math.round(scope.row.height) }}</small>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button type="danger" size="small" @click="deleteAnnotation(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 图像标注区域 -->
      <div class="annotation-area">
        <div class="image-wrapper">
          <div ref="imageContainerInner" class="image-container">
            <!-- 固定显示的图片 -->
            <img 
              v-if="currentImage" 
              ref="annotationImage" 
              :src="getImageUrl(currentImage.url)"
              class="annotation-image"
              alt="医学影像" 
              @load="handleImageLoad"
            />
            
            <!-- 透明覆盖层，用于标注，与图片大小一致 -->
            <div 
              v-if="currentImage"
              class="annotation-overlay"
              :style="{
                width: imageWidth + 'px',
                height: imageHeight + 'px'
              }"
              @mousedown="startDrawing"
              @mousemove="onDrawing"
              @mouseup="endDrawing"
              @mouseleave="cancelDrawing"
            ></div>
            
            <!-- 标注框 -->
            <div 
              v-for="(box, index) in filteredAnnotations" 
              :key="index" 
              class="annotation-box"
              :style="{
                left: box.x + 'px',
                top: box.y + 'px',
                width: box.width + 'px',
                height: box.height + 'px',
                borderColor: getTagColor(box.tag),
                cursor: isEditingBox && editingBoxId === box.id ? 'move' : 'default'
              }"
              @mousedown.stop="startEditBox($event, box.id)"
            >
              <span class="annotation-label" :style="{ backgroundColor: getTagColor(box.tag) }">
                {{ box.tag }}
              </span>
              
              <!-- 四个角的调整大小的手柄 -->
              <div class="resize-handle top-left" @mousedown.stop="startResizeBox($event, box.id, 'top-left')"></div>
              <div class="resize-handle top-right" @mousedown.stop="startResizeBox($event, box.id, 'top-right')"></div>
              <div class="resize-handle bottom-left" @mousedown.stop="startResizeBox($event, box.id, 'bottom-left')"></div>
              <div class="resize-handle bottom-right" @mousedown.stop="startResizeBox($event, box.id, 'bottom-right')"></div>
            </div>
            
            <!-- 正在绘制的框 -->
            <div 
              v-if="isDrawing" 
              class="drawing-box"
              :style="{
                left: Math.min(drawStart.x, drawCurrent.x) + 'px',
                top: Math.min(drawStart.y, drawCurrent.y) + 'px',
                width: Math.abs(drawCurrent.x - drawStart.x) + 'px',
                height: Math.abs(drawCurrent.y - drawStart.y) + 'px'
              }"
            ></div>
          </div>
        </div>

        <div class="navigation-controls">
          <div>
            <el-button type="info" @click="goBack">返回上传图片</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="form-section">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api'
import { getImageUrl, convertToAbsoluteCoordinates, UserIdConsistencyFixer } from '../utils/imageHelper'
import { Refresh } from '@element-plus/icons-vue'
import axios from 'axios'
import { mapGetters } from 'vuex'
import { ElMessage } from 'element-plus'

// 配置对象，用于集中管理API路径和存储键名等
const CONFIG = {
  STORAGE_KEYS: {
    userInfo: 'user',
    token: 'token',
    authValid: 'authValid',
    isNavigatingAfterSave: 'isNavigatingAfterSave',
    pendingDiagnosis: 'pendingDiagnosisId',
    formDataPrefix: 'formData_backup_'
  },
  ROUTES: {
    structuredForm: '/app/cases/structured-form'
  },
  UI: {
    navigationDelay: 100
  }
};

export default {
  name: 'CaseDetailForm',
  components: {
    Refresh
  },
  data() {
    return {
      imageId: this.$route.params.id || this.$route.query.imageId || null,
      currentImage: null, // 初始化 currentImage
      selectedMainCategory: '', // 主要分类
      selectedTag: '', // 具体类型
      currentTool: 'rectangle', // 添加并初始化工具
      // 血管瘤分类数据
      hemangiomaCategories: {
        '真性血管肿瘤': [
          { label: '婴幼儿血管瘤', value: '婴幼儿血管瘤' },
          { label: '先天性快速消退型血管瘤', value: '先天性快速消退型血管瘤' },
          { label: '先天性部分消退型血管瘤', value: '先天性部分消退型血管瘤' },
          { label: '先天性不消退型血管瘤', value: '先天性不消退型血管瘤' },
          { label: '卡波西型血管内皮细胞瘤', value: '卡波西型血管内皮细胞瘤' },
          { label: '丛状血管瘤', value: '丛状血管瘤' },
          { label: '化脓性肉芽肿', value: '化脓性肉芽肿' },
          { label: '梭形细胞血管瘤', value: '梭形细胞血管瘤' },
          { label: '上皮样血管内皮瘤', value: '上皮样血管内皮瘤' },
          { label: '网状血管内皮瘤', value: '网状血管内皮瘤' },
          { label: '假肌源性血管内皮瘤', value: '假肌源性血管内皮瘤' },
          { label: '多形性血管内皮瘤', value: '多形性血管内皮瘤' },
          { label: '血管肉瘤', value: '血管肉瘤' },
          { label: '上皮样血管肉瘤', value: '上皮样血管肉瘤' },
          { label: '卡波西肉瘤', value: '卡波西肉瘤' }
        ],
        '血管畸形': [
          { label: '微静脉畸形', value: '微静脉畸形' },
          { label: '静脉畸形', value: '静脉畸形' },
          { label: '动静脉畸形', value: '动静脉畸形' },
          { label: '淋巴管畸形', value: '淋巴管畸形' },
          { label: '球细胞静脉畸形', value: '球细胞静脉畸形' },
          { label: '毛细血管-淋巴管-静脉畸形', value: '毛细血管-淋巴管-静脉畸形' },
          { label: '毛细血管-动静脉畸形', value: '毛细血管-动静脉畸形' },
          { label: '淋巴管-静脉畸形', value: '淋巴管-静脉畸形' }
        ],
        '血管假瘤/易混淆病变': [
          { label: '血管外皮细胞瘤', value: '血管外皮细胞瘤' },
          { label: '血管球瘤', value: '血管球瘤' },
          { label: '血管平滑肌瘤', value: '血管平滑肌瘤' },
          { label: '血管纤维瘤', value: '血管纤维瘤' },
          { label: '靶样含铁血黄素沉积性血管瘤', value: '靶样含铁血黄素沉积性血管瘤' },
          { label: '鞋钉样血管瘤', value: '鞋钉样血管瘤' }
        ]
      },
      formData: {
        // 表单数据结构
        diseasePart: '',
        diseaseType: '',
        patientAge: '',
        patientGender: '',
        diagnosis: '',
        treatmentPlan: '',
        notes: ''
      },
      dbAnnotations: [], // 存储从数据库获取的标注数据
      uploadedImages: [],
      currentImageIndex: 0,
      annotations: [],
      imageWidth: 0,
      imageHeight: 0,
      drawingBox: null,
      isDrawing: false,
      selectedAnnotation: null,
      isMoving: false,
      isResizing: false,
      resizeHandle: null,
      moveStartX: 0,
      moveStartY: 0,
      drawStart: { x: 0, y: 0 },
      drawCurrent: { x: 0, y: 0 },
      // annotationTool: 'rectangle', // 移除旧的、冲突的属性
      showAnnotationForm: false,
      currentAnnotation: null,
      zoomLevel: 1,
      panOffset: { x: 0, y: 0 },
      isPanning: false,
      lastPanPoint: { x: 0, y: 0 },
      annotationsLoaded: false,
      shouldSaveOriginalImage: false,
      imageLoaded: false,
      isSavingAnnotations: false,
      originalWidth: 0,
      originalHeight: 0,
      scaleX: 1,
      scaleY: 1,
      isSaving: false, // 添加保存状态变量
      processedAnnotations: false // 添加标注处理状态
    };
  },
  computed: {
    // 根据选择的主要分类返回可用的子分类
    availableSubCategories() {
      if (!this.selectedMainCategory) {
        return [];
      }
      return this.hemangiomaCategories[this.selectedMainCategory] || [];
    },
    filteredAnnotations() {
      return this.annotations.filter(a => a.imageIndex === this.currentImageIndex)
    }
  },
  watch: {
    // 关键修复：监听标注数据，当数据获取后且图像已加载时，处理标注
    dbAnnotations(newAnnotations) {
      if (this.imageLoaded && newAnnotations && newAnnotations.length > 0) {
        console.log('监听到标注数据变化且图像已加载，开始处理标注');
        this.processLoadedAnnotations();
      }
    },
    // 监听图像加载状态和物理路径，当两者都准备好时保存原始图像
    imageLoaded(newVal) {
      if (newVal && this.shouldSaveOriginalImage && this.processedFilePath) {
        this.saveOriginalImage();
      }
    }
  },
  created() {
    // 注册窗口大小改变事件监听
    window.addEventListener('resize', this.handleResize);
  },
  mounted() {
    console.log('CaseDetailForm mounted - 查询参数:', this.$route.query, '路由参数:', this.$route.params);
    
    // 获取图像ID，优先从route params中获取，然后是query参数
    let imageId = this.$route.params.id || this.$route.query.imageId || this.$route.query.diagnosisId || '';
    console.log('原始图像ID:', imageId, '类型:', typeof imageId);

    // 加载AI检测结果并自动设置分类
    this.loadAIDetectionResults();

    // 确保imageId是字符串类型
    imageId = String(imageId);
    
    // 检查是否有有效的图像ID
    if (!imageId || imageId === 'undefined' || imageId === 'null') {
      this.$message.warning('未提供有效的图像ID，请选择一个图像进行标注');
      return;
    }
    
    // 保存图像ID到组件实例
    this.imageId = imageId;
    console.log('处理后的图像ID:', this.imageId, '类型:', typeof this.imageId);
    
    // 显示加载提示
    const loading = this.$loading({
      lock: true,
      text: '正在加载图像和标注数据...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 统一加载流程
      // 标记为应用内操作
      sessionStorage.setItem('isAppOperation', 'true');
    
    // 添加重试机制，确保能正确加载图像和标注数据
    let retryCount = 0;
    const maxRetries = 3;
    
    const loadDataWithRetry = () => {
      console.log(`尝试加载页面数据，第 ${retryCount + 1} 次尝试`);
      
      // 异步加载数据
      this.loadPageData(imageId)
        .then(() => {
          console.log('页面数据加载成功');
          // 成功后加载关联的标注
          return this.loadAnnotations(imageId);
        })
        .then(() => {
          console.log('标注数据也加载成功');
          loading.close();
        })
        .catch(error => {
          console.error(`第 ${retryCount + 1} 次加载数据失败:`, error);
          
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`${retryCount * 2}秒后重试...`);
            
            // 延迟重试，每次增加等待时间
            setTimeout(loadDataWithRetry, retryCount * 2000);
          } else {
            console.error('达到最大重试次数，放弃加载');
            this.$message.error('无法加载页面数据，请返回重试');
            loading.close();
          }
        });
    };
    
    // 开始加载数据
    loadDataWithRetry();
    
    // 添加事件监听器
    document.addEventListener('mousemove', this.handleGlobalMouseMove);
    document.addEventListener('mouseup', this.handleGlobalMouseUp);
    
    // 响应窗口大小变化，更新图像尺寸
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    // 移除窗口大小改变事件监听，避免内存泄漏
    window.removeEventListener('resize', this.handleResize);
    
    // 清除导航标记
    sessionStorage.removeItem('isNavigatingAfterSave');
    sessionStorage.removeItem('returningToWorkbench');
    // 保留isAppOperation，以维持应用内操作状态
  },
  methods: {
    // 处理主分类变化
    onMainCategoryChange() {
      // 清空子分类选择
      this.selectedTag = '';
    },

    // 加载AI检测结果并自动设置分类
    async loadAIDetectionResults() {
      try {
        const diagnosisId = this.imageId;
        if (!diagnosisId) {
          console.log('没有诊断ID，跳过AI检测结果加载');
          return;
        }

        console.log('开始加载AI检测结果，诊断ID:', diagnosisId);

        // 获取血管瘤诊断数据
        const response = await axios.get(`/medical/api/hemangioma-diagnoses/${diagnosisId}`);
        const diagnosisData = response.data;

        console.log('获取到的诊断数据:', diagnosisData);

        if (diagnosisData && diagnosisData.detectedType) {
          // 将AI检测到的类型转换为完整的中文名称
          const detectedType = diagnosisData.detectedType;
          console.log('AI检测到的类型:', detectedType);

          // 自动设置分类
          this.autoSetCategoryFromAI(detectedType);
        }
      } catch (error) {
        console.error('加载AI检测结果失败:', error);
        // 不显示错误消息，因为这是自动加载，失败了用户可以手动选择
      }
    },

    // 根据AI检测结果自动设置分类
    autoSetCategoryFromAI(detectedType) {
      console.log('开始自动设置分类，检测类型:', detectedType);

      // AI检测结果可能是缩写形式，需要转换为完整名称
      const typeMapping = {
        'IH': '婴幼儿血管瘤',
        'RICH': '先天性快速消退型血管瘤',
        'PICH': '先天性部分消退型血管瘤',
        'NICH': '先天性不消退型血管瘤',
        'KHE': '卡波西型血管内皮细胞瘤',
        'TA': '丛状血管瘤',
        'PG': '化脓性肉芽肿',
        'MVM': '微静脉畸形',
        'VM': '静脉畸形',
        'AVM': '动静脉畸形',
        'LM': '淋巴管畸形',
        'GVM': '球细胞静脉畸形',
        'CLVM': '毛细血管-淋巴管-静脉畸形',
        'CAVM': '毛细血管-动静脉畸形',
        'LVM': '淋巴管-静脉畸形'
      };

      // 处理多个检测类型（用+分隔）
      const types = detectedType.split('+');
      const firstType = types[0].trim();

      // 获取完整的中文名称
      const fullTypeName = typeMapping[firstType] || firstType;
      console.log('转换后的完整类型名称:', fullTypeName);

      // 查找该类型属于哪个主分类
      let targetMainCategory = '';
      let targetSubCategory = fullTypeName;

      for (const [mainCategory, subCategories] of Object.entries(this.hemangiomaCategories)) {
        const found = subCategories.find(item => item.value === fullTypeName);
        if (found) {
          targetMainCategory = mainCategory;
          targetSubCategory = found.value;
          break;
        }
      }

      if (targetMainCategory) {
        console.log('自动设置分类:', targetMainCategory, '->', targetSubCategory);

        // 设置主分类
        this.selectedMainCategory = targetMainCategory;

        // 等待Vue更新DOM后设置子分类
        this.$nextTick(() => {
          this.selectedTag = targetSubCategory;

          // 显示成功消息
          this.$message.success(`已根据AI检测结果自动设置分类：${targetSubCategory}`);
        });
      } else {
        console.log('未找到匹配的分类，使用默认设置');
        // 如果没找到匹配的，设置为第一个分类作为默认
        this.selectedMainCategory = '真性血管肿瘤';
        this.$nextTick(() => {
          this.selectedTag = '婴幼儿血管瘤';
        });
      }
    },

    // 处理长ID，将时间戳ID转换为合适的整数
    processLongId(id) {
      if (!id) return null;

      const idStr = id.toString();
      // 如果ID长度超过9（INT范围限制），截取后9位
      if (idStr.length > 9) {
        console.log(`处理长ID: ${idStr} -> ${idStr.substring(idStr.length - 9)}`);
        return idStr.substring(idStr.length - 9);
      }
      return idStr;
    },

    // 添加防止缓存的参数
    getImageUrl(url) {
      if (!url) return '';
      
      // 如果URL已经包含完整路径(http)，直接使用
      if (url.startsWith('http')) {
        return url;
      }
      
      // 尝试不同的URL格式
      const originalUrl = url;
      let finalUrl = url;
      
      // 如果是相对路径，加上基础URL
      if (url.startsWith('/')) {
        // 获取当前域名作为基础URL
        const baseUrl = window.location.origin;
        finalUrl = baseUrl + url;
      }
      
      // 添加时间戳和随机数参数，防止缓存
      const cacheBuster = `t=${Date.now()}&r=${Math.random()}`;
      if (finalUrl.includes('?')) {
        finalUrl = `${finalUrl}&${cacheBuster}`;
      } else {
        finalUrl = `${finalUrl}?${cacheBuster}`;
      }
      
      console.log(`处理图像URL: ${originalUrl} -> ${finalUrl}`);
      return finalUrl;
    },
    
    // 修改加载图像数据的方法，添加防止缓存的头信息
    async loadImageData() {
      if (!this.imageId) {
        console.error('No image ID provided');
        return;
      }
      
      try {
        console.log(`Loading image data for ID: ${this.imageId}`);
        
        // 添加时间戳和随机数，防止缓存
        const timestamp = Date.now();
        const random = Math.random();
        
        const response = await api.get(`/images/${this.imageId}?t=${timestamp}&r=${random}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });
        
        if (response.data) {
          this.currentImage = response.data;
          console.log('Image data loaded:', this.currentImage);
          
          // 加载关联的标注
          this.loadAnnotations();
        } else {
          console.error('Failed to load image data: Response data is empty');
        }
      } catch (error) {
        console.error('Failed to load image data:', error);
      }
    },
    
    // 修改加载标注的方法，解决刷新后标注框不显示的问题
    async loadAnnotations(imageId) {
      if (!imageId) {
        console.error('无法加载标注：缺少图像ID');
        return Promise.reject('缺少图像ID');
      }
      
      const id = imageId || this.imageId;
      console.log('加载图像ID为', id, '的标注数据');
      
      // 最大重试次数
      const maxRetries = 3;
      let retries = 0;
      
      const tryLoadAnnotations = async () => {
        try {
          // 添加时间戳和随机数避免缓存
          const timestamp = new Date().getTime();
          const random = Math.random();
          
          // 获取当前用户信息
          const user = JSON.parse(localStorage.getItem('user') || '{}');
          const userId = user.customId || user.id || '';
      
          // 检查是否是血管瘤诊断ID
          const isDiagnosisId = this.$route.query.diagnosisId === id;
          
          // 构建API URL
          let apiUrl = `/medical/api/tags/image/${id}?t=${timestamp}&r=${random}&mode=view&_role=REVIEWER&userId=${userId}`;
      
          // 如果是血管瘤诊断ID，使用特殊的API端点
      if (isDiagnosisId) {
            apiUrl = `/medical/api/hemangioma-diagnoses/${id}/tags?t=${timestamp}&r=${random}`;
          }
          
          console.log(`尝试加载标注数据 (第${retries + 1}次尝试)，URL: ${apiUrl}`);
          
          // 尝试从API加载标注数据
          const response = await fetch(apiUrl);
          
          if (!response.ok) {
            throw new Error(`获取标注失败: ${response.status}`);
          }
          
          const data = await response.json();
          console.log(`获取到 ${data.length} 个标注数据:`, data);
            
            // 保存原始标注数据
          this.dbAnnotations = data || [];
            
          // 如果图像已加载，立即转换标注
              if (this.imageWidth > 0 && this.imageHeight > 0) {
            this.processLoadedAnnotations();
              } else {
            // 否则标记为待处理
                this.annotationsLoaded = true;
              }
          
          return data;
        } catch (error) {
          console.error(`加载标注数据失败 (第${retries + 1}次尝试):`, error);
          
          // 如果还有重试次数，则等待后重试
          if (retries < maxRetries) {
            retries++;
            const waitTime = retries * 1000; // 每次重试增加等待时间
            console.log(`等待 ${waitTime}ms 后重试...`);
            
            return new Promise((resolve, reject) => {
              setTimeout(async () => {
                try {
                  const result = await tryLoadAnnotations();
                  resolve(result);
                } catch (retryError) {
                  reject(retryError);
                }
              }, waitTime);
            });
          }
          
          // 尝试备用API路径
          try {
            console.log('尝试使用备用API路径...');
            const timestamp = new Date().getTime();
        const random = Math.random();
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            const userId = user.customId || user.id || '';
            
            // 检查是否是血管瘤诊断ID
            const isDiagnosisId = this.$route.query.diagnosisId === id;
            
            // 构建备用API URL
            let backupUrl = `/api/tags/image/${id}?t=${timestamp}&r=${random}&userId=${userId}`;
            
            // 如果是血管瘤诊断ID，使用特殊的API端点
            if (isDiagnosisId) {
              backupUrl = `/api/hemangioma-diagnoses/${id}/tags?t=${timestamp}&r=${random}`;
            }
            
            console.log(`尝试备用API路径: ${backupUrl}`);
            const response = await fetch(backupUrl);
            
            if (!response.ok) {
              throw new Error(`备用API也失败: ${response.status}`);
            }
            
            const data = await response.json();
            console.log(`通过备用API获取到 ${data.length} 个标注数据:`, data);
            
            // 保存原始标注数据
            this.dbAnnotations = data || [];
            
            // 如果图像已加载，立即转换标注
            if (this.imageWidth > 0 && this.imageHeight > 0) {
                this.processLoadedAnnotations();
            } else {
              // 否则标记为待处理
              this.annotationsLoaded = true;
            }
            
            return data;
          } catch (backupError) {
            console.error('所有API尝试都失败:', backupError);
            this.$message.error('加载标注数据失败，您可以创建新的标注');
            return Promise.reject(backupError);
          }
        }
      };
      
      // 开始尝试加载
      return tryLoadAnnotations();
    },
    
    // 转换血管瘤标签数据为前端标注格式
    convertHemangiomaTags(tags) {
      console.log('转换血管瘤标签数据:', tags);
      
      if (!tags || !tags.length || !this.imageWidth || !this.imageHeight) {
        console.warn('无法转换标签数据: 缺少必要信息', {
          tagsLength: tags ? tags.length : 0,
          imageWidth: this.imageWidth,
          imageHeight: this.imageHeight
        });
        return;
      }
      
      const convertedAnnotations = tags.map((tag, index) => {
        // 判断是否是相对坐标（0-1范围内）
        const isRelativeCoordinates = 
          tag.x <= 1 && tag.x >= 0 && 
          tag.y <= 1 && tag.y >= 0 && 
          tag.width <= 1 && tag.width >= 0 && 
          tag.height <= 1 && tag.height >= 0;
        
        // 根据坐标类型计算实际像素值
        const x = isRelativeCoordinates ? tag.x * this.imageWidth : tag.x;
        const y = isRelativeCoordinates ? tag.y * this.imageHeight : tag.y;
        const width = isRelativeCoordinates ? tag.width * this.imageWidth : tag.width;
        const height = isRelativeCoordinates ? tag.height * this.imageHeight : tag.height;
        
        return {
          id: `${index + 1}`,
          serverId: tag.id || null,
          tag: tag.tag || tag.tagName || 'IH-婴幼儿血管瘤',
          x: x,
          y: y,
          width: width,
          height: height,
          confidence: tag.confidence || null,
          type: 'rectangle'
        };
      });
      
      console.log('转换后的标注数据:', convertedAnnotations);
      this.annotations = convertedAnnotations;
    },
    
    // 转换普通标签数据为前端标注格式
    convertTags(tags) {
      console.log('转换普通标签数据:', tags);
          
      if (!tags || !tags.length || !this.imageWidth || !this.imageHeight) {
        console.warn('无法转换标签数据: 缺少必要信息');
        return;
          }
      
      const convertedAnnotations = tags.map((tag, index) => {
        // 判断是否是相对坐标（0-1范围内）
        const isRelativeCoordinates = 
          tag.x <= 1 && tag.x >= 0 && 
          tag.y <= 1 && tag.y >= 0 && 
          tag.width <= 1 && tag.width >= 0 && 
          tag.height <= 1 && tag.height >= 0;
        
        // 根据坐标类型计算实际像素值
        const x = isRelativeCoordinates ? tag.x * this.imageWidth : tag.x;
        const y = isRelativeCoordinates ? tag.y * this.imageHeight : tag.y;
        const width = isRelativeCoordinates ? tag.width * this.imageWidth : tag.width;
        const height = isRelativeCoordinates ? tag.height * this.imageHeight : tag.height;
        
        return {
          id: tag.id || `${index + 1}`,
          tag: tag.tag || tag.tagName || 'IH-婴幼儿血管瘤',
          x: x,
          y: y,
          width: width,
          height: height,
          confidence: tag.confidence || null,
          type: 'rectangle'
        };
      });
      
      console.log('转换后的标注数据:', convertedAnnotations);
      this.annotations = convertedAnnotations;
    },
    
    // 添加手动刷新方法
    async refreshData() {
      // 清除本地缓存
      this.dbAnnotations = [];
      this.annotations = [];
      this.annotationsLoaded = false;
      
      // 重新加载数据
      await this.loadImageData();
      
      // 显示提示
      this.$message({
        message: '数据已刷新',
        type: 'success',
        duration: 2000
      });
    },

    // 从image_pairs表加载图像
    loadImageFromPairs(imageId) {
      console.log('🔍 开始从image_pairs表加载图像，ID:', imageId);
      
      // 获取当前用户信息
      const user = JSON.parse(localStorage.getItem('user')) || {};
      const userId = user.customId || user.id;
      
      if (!userId) {
        console.warn('未找到用户ID，可能导致权限问题');
          }
      
      // 显式添加用户ID作为查询参数
      const params = { userId };
      
      // 添加时间戳避免缓存
      params.t = new Date().getTime();
      
      // 先从image_pairs表查询相关记录
      api.imagePairs.getByMetadataId(imageId)
        .then(response => {
          console.log('image_pairs查询响应:', response);
          const data = response.data;
          
          if (data && data.length > 0) {
            const imagePair = data[0];
            console.log('image_pairs第一条记录:', imagePair);
            this.imagePairId = imagePair.id;
            
            // 使用处理后的图像路径
            const processedImagePath = imagePair.imageOnePath || imagePair.image_one_path;
            console.log('获取到图像路径:', processedImagePath);
            
            // 如果路径仍为空，尝试其他字段
            if (!processedImagePath && imagePair.image_one_url) {
              console.log('尝试使用image_one_url:', imagePair.image_one_url);
              this.processedFilePath = imagePair.image_one_url;
            } else if (!processedImagePath && imagePair.url) {
              console.log('尝试使用url字段:', imagePair.url);
              this.processedFilePath = imagePair.url;
            } else {
              this.processedFilePath = processedImagePath;
            }
            
            // 如果仍然没有找到路径，直接从image_metadata表获取
            if (!this.processedFilePath) {
              console.log('image_pairs中未找到图像路径，尝试从image_metadata表获取');
              this.tryLoadImagePathFromMetadata(imageId, (path) => {
                if (path) {
                  console.log('从image_metadata表获取到图像路径:', path);
                  this.processedFilePath = path;
                  this.updateImagePairPath(imageId, path);
                  
                  // 更新上传图像数组
                  this.uploadedImages = [{
                    id: imageId,
                    url: path,
                    filename: `图像 #${imageId}`
                  }];
                  
                  this.currentImage = this.uploadedImages[0];
                  this.imageLoaded = true;
                }
              });
              return;  // 中断当前流程，等待回调处理
            }
            
            // 更新上传图像数组
            this.uploadedImages = [{
              id: imageId,
              url: this.processedFilePath,
              filename: `图像 #${imageId}`
            }];
            
            this.currentImage = this.uploadedImages[0];
            this.currentImageIndex = 0;
            this.currentImageId = imageId;
            
            // 标记图像已加载
            this.imageLoaded = true;
            
            // 加载标注框
            this.loadAnnotations(imageId);
          } else {
            console.log('image_pairs未找到数据，尝试从image_metadata表直接获取路径');
            this.tryLoadImagePathFromMetadata(imageId, (path) => {
              if (path) {
                console.log('从image_metadata表获取到图像路径:', path);
                this.processedFilePath = path;
                this.createImageRecord(path, imageId);
              } else {
                this.$message.error('未能找到图像路径，无法加载图像');
              }
            });
          }
        })
        .catch(error => {
          console.error('加载图像对失败:', error);
          // 尝试直接从image_metadata获取
          this.tryLoadImagePathFromMetadata(imageId);
        });
    },
    
    // 作为备用，尝试从image_metadata加载
    tryLoadFromMetadata(imageId) {
      console.log('从image_metadata表尝试加载图像ID:', imageId);
      
      api.images.getOne(imageId)
        .then(response => {
          console.log('从image_metadata表获取的完整响应:', response);
          const imageData = response.data;
          console.log('image_metadata表获取的图像数据:', imageData);
          
          if (!imageData || !imageData.path) {
            console.error('image_metadata表中图像路径不存在，无法加载图像');
            this.$message.error('图像路径不存在，请检查数据库');
            return;
          }
          
          // 准备图像数据
          this.uploadedImages = [{
            id: imageData.id,
            url: imageData.path,
            filename: imageData.original_name || `图像 #${imageData.id}`
          }];
          
          this.currentImage = this.uploadedImages[0];
          this.currentImageIndex = 0;
          
          // 保存处理后的图像路径
          this.processedFilePath = imageData.path;
          
          // 创建ImagePair记录 - 直接使用fetch创建，避免API调用
          this.createImagePairDirectly(imageData.id, imageData.path);
          
          // 加载标注数据
          this.loadAnnotationsFromDatabase();
          
          // 标记图像已成功加载
          this.imageLoaded = true;
          
          console.log('从image_metadata加载成功并创建image_pairs记录');
        })
        .catch(error => {
          console.error('从image_metadata加载图像失败:', error);
          this.loadingError = true;
          this.loadingErrorMessage = '无法加载图像，请检查数据库记录';
          
          // 如果有物理路径，尝试创建新的图像记录
          if (this.processedFilePath && this.currentImageId) {
            console.log('尝试使用现有路径创建图像记录');
            this.createImageRecord(this.processedFilePath, this.currentImageId);
          } else {
            this.$message.error('无法找到图像数据，请返回上一步重新上传');
          }
        });
    },
    
    // 直接创建ImagePair记录，不使用API
    createImagePairDirectly(metadataId, imagePath) {
      console.log('直接创建ImagePair记录:', { metadataId, path: imagePath });
      
      // 准备数据
      const data = {
        metadataId: metadataId.toString(),
        imageOnePath: imagePath,
        description: `图像${metadataId}`
      };
      
      // 直接使用fetch API，完全绕过axios和api.js
      fetch('/api/image-pairs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data),
        credentials: 'include'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(result => {
          console.log('成功创建ImagePair记录:', result);
          if (result && result.id) {
            this.imagePairId = result.id;
          }
          this.$message.success('图像关联信息创建成功');
        })
        .catch(error => {
          console.error('创建ImagePair记录失败:', error);
          this.$message.warning('图像关联创建失败，但您仍可继续使用');
        });
    },
    
    // 保留原有方法但不再主动调用
    loadImageById(imageId) {
      // 重定向到新的加载方法
      this.loadImageFromPairs(imageId);
    },
    
    // 创建新的图像记录
    createImageRecord(imagePath, metadataId) {
      console.log('正在创建新的图像记录:', { path: imagePath, id: metadataId });
      
      // 获取当前用户
      const user = JSON.parse(localStorage.getItem('user')) || { id: 1 };
      
      // 提取文件名
      const filename = imagePath.substring(imagePath.lastIndexOf('/') + 1);
      
      // 创建元数据
      const metadata = {
        filename: filename,
        original_name: filename,
        path: imagePath,
        mimetype: 'image/jpeg',
        size: 0,
        width: 800,
        height: 600,
        status: 'DRAFT',
        uploaded_by: user.id
      };
      
      // 保存元数据
      api.images.update(metadataId, metadata)
        .then(response => {
          console.log('成功创建/更新图像元数据:', response.data);
          
          // 创建ImagePair记录
          this.saveOriginalImage();
          
          // 重新加载页面
          this.loadImageById(metadataId);
        })
        .catch(error => {
          console.error('创建图像元数据失败:', error);
          this.$message.error('无法创建图像记录，请检查数据库连接');
        });
    },
    
    // 根据路径加载图像
    loadImageByPath(imagePath) {
      // 模拟图像加载过程
      this.uploadedImages = [{
        id: this.currentImageId || Date.now().toString(),
        url: imagePath,
        filename: imagePath.substring(imagePath.lastIndexOf('/') + 1)
      }];
      
      this.currentImage = this.uploadedImages[0];
      this.currentImageIndex = 0;
      
      // 如果没有图像ID，使用当前时间戳作为ID
      if (!this.currentImageId) {
        this.currentImageId = this.uploadedImages[0].id;
        console.log('生成临时图像ID:', this.currentImageId);
      }
      
      // 确保ImagePair记录存在
      this.checkExistingImagePair();
      
      // 标记图像已成功加载
      this.imageLoaded = true;
    },
    
    // 检查并确保图像对记录存在
    checkExistingImagePair() {
      if (!this.currentImageId || !this.processedFilePath) {
        console.log('缺少图像ID或物理路径，不检查ImagePair记录');
        return;
      }
      
      console.log('检查图像ID对应的ImagePair记录:', this.currentImageId);
      
      api.imagePairs.getByMetadataId(this.currentImageId)
        .then(response => {
          if (response.data && response.data.length > 0) {
            // 找到现有记录
            const imagePair = response.data[0];
            this.imagePairId = imagePair.id;
            console.log('找到现有ImagePair记录:', imagePair);
            
            // 如果记录中的路径与当前路径不一致，更新记录
            if (imagePair.imageOnePath !== this.processedFilePath) {
              console.log('更新ImagePair中的路径信息');
              this.saveOriginalImage();
            }
          } else {
            // 没有找到记录，创建新记录
            console.log('未找到ImagePair记录，创建新记录');
            this.saveOriginalImage();
          }
        })
        .catch(error => {
          console.error('检查ImagePair记录失败:', error);
          // 尝试创建新记录
          console.log('尝试创建新的ImagePair记录');
          this.saveOriginalImage();
        });
    },
    
    saveOriginalImage() {
      // 如果没有图像ID，则不保存
      if (!this.currentImageId) {
        console.log('缺少图像ID，不保存到ImagePair表');
        return;
      }
      
      let imagePath = '';
      
      // 首选：从currentImage中获取URL（已经从数据库加载）
      if (this.currentImage && this.currentImage.url) {
        imagePath = this.currentImage.url;
        console.log('使用从数据库加载的图像路径:', imagePath);
      } 
      // 次选：使用本地保存的processedFilePath
      else if (this.processedFilePath) {
        imagePath = this.processedFilePath;
        console.log('使用本地保存的处理后图像路径:', imagePath);
      }
      // 如果都没有，无法保存
      else {
        console.error('无法找到有效的图像路径，不保存到ImagePair表');
        return;
      }
      
      // 确保图像元数据存在
      this.ensureImageMetadataExists(this.currentImageId, imagePath, () => {
        // 获取当前用户
        const user = JSON.parse(localStorage.getItem('user')) || { id: 1 };
        
        try {
          // 准备请求数据 - 极简格式，避免任何不必要的字段
          const data = {
            metadataId: this.currentImageId.toString(), // 使用字符串格式
            imageOnePath: imagePath,
            description: `图像${this.currentImageId}` // 简化描述
          };
          
          console.log('保存到image_pairs的数据:', data);
          
          // 使用fetch API直接发送请求 - 绕过所有中间件和缓存
          const directRequest = async () => {
            try {
              // 查询image_pairs是否存在
              console.log('直接查询ImagePair是否存在:', this.currentImageId);
              const checkResponse = await fetch(`/api/image-pairs/metadata/${this.currentImageId}`, {
                method: 'GET',
                headers: {
                  'Accept': 'application/json',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache'
                },
                credentials: 'include'
              });
              
              // 解析响应
              const pairsData = await checkResponse.json();
              let existingPairId = null;
              let exists = false;
              
              if (pairsData && pairsData.length > 0 && pairsData[0].id) {
                exists = true;
                existingPairId = pairsData[0].id;
                console.log('找到现有ImagePair:', existingPairId);
              }
              
              // 如果存在，添加ID字段
              if (exists && existingPairId) {
                data.id = existingPairId;
              }
              
              // 发送创建/更新请求
              const saveResponse = await fetch('/api/image-pairs', {
                method: 'POST', // 始终使用POST
                headers: {
                  'Content-Type': 'application/json',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache'
                },
                body: JSON.stringify(data),
                credentials: 'include'
              });
              
              if (!saveResponse.ok) {
                throw new Error(`保存失败: ${saveResponse.status}`);
              }
              
              const result = await saveResponse.json();
              console.log('保存ImagePair成功:', result);
              
              // 更新本地ID
              if (result && result.id) {
                this.imagePairId = result.id;
              }
              
              this.$message.success('图像关联信息保存成功');
              
            } catch (error) {
              console.error('保存ImagePair失败:', error);
              this.$message.error('保存失败: ' + error.message);
            }
          };
          
          // 执行直接请求
          directRequest();
          
        } catch (err) {
          console.error('准备ImagePair数据时出错:', err);
          this.$message.error('保存图像对失败: ' + err.message);
        }
      });
    },
    
    // 确保图像元数据存在
    ensureImageMetadataExists(imageId, imagePath, callback) {
      // 检查图像元数据是否存在
      api.images.getOne(imageId)
        .then(() => {
          // 元数据存在，执行回调
          callback();
        })
        .catch(() => {
          // 元数据不存在，创建新的
          console.log('图像元数据不存在，创建新记录');
          this.createImageRecord(imagePath, imageId);
        });
    },
    
    // 窗口大小变化时更新图像尺寸
    handleResize() {
      // 窗口大小变化时，需要延迟一点以确保DOM已更新
      this.$nextTick(() => {
        const image = this.$refs.annotationImage;
        if (image) {
          // 获取新的图像显示尺寸
          const rect = image.getBoundingClientRect();
          const newWidth = rect.width;
          const newHeight = rect.height;
          
          // 如果尺寸有变化，更新并重新计算标注框位置
          if (newWidth !== this.imageWidth || newHeight !== this.imageHeight) {
            console.log(`窗口大小改变，图像尺寸从 ${this.imageWidth}x${this.imageHeight} 变为 ${newWidth}x${newHeight}`);
            this.imageWidth = newWidth;
            this.imageHeight = newHeight;
            
            // 更新缩放比例
            this.scaleX = this.imageWidth / this.originalWidth;
            this.scaleY = this.imageHeight / this.originalHeight;
            
            // 重新计算标注框位置
            this.recalculateAnnotationPositions();
          }
        }
      });
    }
  }
};
</script>

<style scoped>
/* 样式内容保持不变 */
</style>

// =================================================================================
// --- 文件结束: frontend/src/views/AnnotationAndForm.vue ---


// --- 文件开始: frontend/src/element-plus-unified-fix.js ---
// =================================================================================

/**
 * Element Plus 统一修复模块
 *
 * 此文件合并了所有Element Plus相关的修复功能：
 * - 解决ES模块加载问题，特别是.mjs文件找不到的问题
 * - 修复缺少的模块路径
 * - 提供通用的tryNextPath函数
 * - 模拟缺失的类型和辅助函数
 */

// 创建通用的tryNextPath函数处理模块路径解析
export function tryNextPath(paths, index) {
  if (!paths || index >= paths.length) {
    console.warn('模块路径解析失败', paths, index);
    return '';
  }
  return paths[index];
}

// 全局注入tryNextPath函数
if (typeof window !== 'undefined') {
  window.tryNextPath = tryNextPath;
}

if (typeof global !== 'undefined') {
  global.tryNextPath = tryNextPath;
}

// 修复Element Plus类型相关的模块
export const isString = (val) => typeof val === 'string';
export const isNumber = (val) => typeof val === 'number';
export const isBoolean = (val) => typeof val === 'boolean';
export const isObject = (val) => val !== null && typeof val === 'object';
export const isFunction = (val) => typeof val === 'function';
export const isArray = Array.isArray;
export const isDate = (val) => val instanceof Date;
export const isPromise = (val) => isObject(val) && isFunction(val.then) && isFunction(val.catch);

// 模拟常用的缺失模块
export const Size = {
  LARGE: 'large',
  DEFAULT: 'default',
  SMALL: 'small',
};

// 模拟运行时props帮助函数
export const buildProp = (prop, key) => prop;
export const buildProps = (props) => props;
export const definePropType = (type) => type;
export const mutable = (val) => val;

// 导出空的token对象，用于替换缺失的模块
export const TOKEN = Symbol('token');
export const TOOLTIP_INJECTION_KEY = Symbol('tooltip');

// 修复缺少的模块路径
const fixElementPlusImports = () => {
  try {
    if (typeof window !== 'undefined') {
      // 创建虚拟的utils模块以避免缺失
      window.__EP_UTILS_TYPES__ = {
        isString,
        isNumber,
        isBoolean,
        isObject,
        isFunction,
      };

      // 修复可能缺失的token和hooks
      window.__EP_VIRTUAL_MODULES__ = {};

      // 定义一个模块导入拦截器
      const originalImport = window.__webpack_require__ || (() => {});
      if (window.__webpack_require__) {
        window.__webpack_require__ = function(moduleId) {
          try {
            return originalImport(moduleId);
          } catch (e) {
            // 如果是Element Plus内部模块导入错误
            if (e.message && e.message.includes('Cannot find module')) {
              // 返回空对象避免崩溃
              console.warn('Element Plus模块加载失败:', e.message);
              return {};
            }
            throw e;
          }
        };
      }
    }
  } catch (e) {
    console.warn('Element Plus兼容性修复应用失败', e);
  }
};

// 应用修复
fixElementPlusImports();

// 导出统一的修复工具
export default {
  tryNextPath,
  isString,
  isNumber,
  isBoolean,
  isObject,
  isFunction,
  isArray,
  isDate,
  isPromise,
  Size,
  buildProp,
  buildProps,
  definePropType,
  mutable,
  TOKEN,
  TOOLTIP_INJECTION_KEY
};

// =================================================================================
// --- 文件结束: frontend/src/element-plus-unified-fix.js ---