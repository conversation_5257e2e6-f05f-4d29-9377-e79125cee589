package com.medical.annotation.config;

import com.medical.annotation.util.BasePathConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

@Configuration
public class AppConfig {

    @Value("${server.port:8085}")
    private String serverPort;

    @Value("${server.servlet.context-path:/medical}")
    private String contextPath;
    
    @Autowired
    private BasePathConfig basePathConfig;

    /**
     * 集中管理图片存储路径
     * 通过设置application.properties中的app.upload.dir可以覆盖默认值
     */
    @Value("${app.upload.dir:F:/xueguan/medical_images}")
    private String uploadDir;

    // 前端URL列表
    @Bean
    public List<String> allowedOrigins() {
        return Arrays.asList(
            "http://localhost:8080",
            "http://127.0.0.1:8080",
            "http://************:8080",
            "http://localhost:8081",
            "http://127.0.0.1:8081",
            "http://localhost:8083",
            "http://127.0.0.1:8083", 
            "http://localhost:8085",
            "http://127.0.0.1:8085",
            "http://localhost"
        );
    }

    @Bean
    public String apiBasePath() {
        return contextPath + "/api";
    }

    @Bean
    public String getServerPort() {
        return serverPort;
    }

    @Bean
    public String getContextPath() {
        return contextPath;
    }

    /**
     * 获取图片上传基本目录
     * @return 图片存储的根目录
     */
    @Bean
    public String getUploadDir() {
        return basePathConfig.getUploadDir();
    }

    /**
     * 获取处理后图片目录
     * @return 处理后的图片目录
     */
    @Bean
    public String getProcessedDir() {
        return basePathConfig.getProcessedDir();
    }

    /**
     * 获取标注后图片目录
     * @return 标注后的图片目录
     */
    @Bean
    public String getAnnotatedDir() {
        return basePathConfig.getAnnotatedDir();
    }

    /**
     * 获取临时图片目录
     * @return 临时图片目录
     */
    @Bean
    public String getTempImagesDir() {
        return basePathConfig.getTempDir();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    // 默认的 RestTemplate，用于快速的API调用（例如AI检测服务）
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder.build();
    }

    // 专门为调用大模型创建的 RestTemplate，具有更长的超时时间
    @Bean("llmRestTemplate")
    public RestTemplate llmRestTemplate(RestTemplateBuilder builder) {
        return builder
                .setConnectTimeout(Duration.ofMinutes(1)) // 设置连接超时时间为1分钟
                .setReadTimeout(Duration.ofMinutes(5))    // 设置读取超时时间为5分钟
                .build();
    }
} 