package com.medical.annotation.config;

import com.medical.annotation.filter.AuthenticationFilter;
import com.medical.annotation.service.CustomUserDetailsService;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.firewall.StrictHttpFirewall;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    private UserService userService;
    
    @Autowired
    private CustomUserDetailsService customUserDetailsService;
    
    @Autowired
    private org.springframework.security.crypto.password.PasswordEncoder passwordEncoder;

    @Autowired
    private List<String> allowedOrigins;

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        // 让Spring Security完全忽略图片路径
        return (web) -> web.ignoring().antMatchers("/images/**", "/medical/images/**");
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        // 配置身份验证
        configureAuthentication(http);
        
        http
            .csrf().disable() // 禁用CSRF保护
            .cors().configurationSource(corsConfigurationSource())
            .and()
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS) // 无状态会话
            .and()
            .authorizeRequests()
                // 允许所有OPTIONS请求
                .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                // 允许所有人访问登录和注册接口
                .antMatchers("/api/users/authenticate").permitAll()
                .antMatchers("/api/users/register").permitAll()
                .antMatchers(HttpMethod.POST, "/api/users").permitAll()
                .antMatchers("/api/users").permitAll()
                // 忘记密码相关接口
                .antMatchers("/api/users/forgot-password").permitAll()
                .antMatchers("/api/users/verify-reset-code").permitAll()
                .antMatchers("/api/users/reset-password").permitAll()
                // 新增：明确允许登录用户上传图片
                .antMatchers(HttpMethod.POST, "/medical/api/images/upload").authenticated()
                // 图片资源已由web.ignoring()处理，此处无需配置
                // 允许访问仪表盘统计数据API
                .antMatchers("/api/stats-v2/dashboard/**").permitAll()
                .antMatchers("/api/stats-v2/dashboard-unrestricted/**").permitAll()
                .antMatchers("/api/stats-v3/dashboard/**").permitAll()
                .antMatchers("/api/stats/dashboard/**").permitAll()
                // 允许访问最近标注接口
                .antMatchers("/api/images/recent-annotations/**").permitAll()
                // 允许访问血管瘤诊断接口
                .antMatchers("/api/hemangioma-diagnoses/**").permitAll()
                .antMatchers("/medical/api/hemangioma-diagnoses/**").permitAll()
                // 需要认证访问的API
                .antMatchers("/api/**").authenticated()
                // 其他请求允许访问
                .anyRequest().permitAll()
            .and()
            .addFilterBefore(customAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)
            .headers()
                .frameOptions().sameOrigin(); // 允许同源iframe
        
        return http.build();
    }
    
    private void configureAuthentication(HttpSecurity http) throws Exception {
        AuthenticationManagerBuilder authBuilder = http.getSharedObject(AuthenticationManagerBuilder.class);
        
        // 配置UserDetailsService和密码编码器
        authBuilder.userDetailsService(customUserDetailsService)
                   .passwordEncoder(passwordEncoder);
        
        // 保留内存认证作为备用
        authBuilder.inMemoryAuthentication()
                .withUser("admin")
                .password(passwordEncoder.encode("admin"))
                .roles("ADMIN");
    }
    
    @Bean
    public HttpFirewall allowUrlEncodedSlashHttpFirewall() {
        StrictHttpFirewall firewall = new StrictHttpFirewall();
        firewall.setAllowUrlEncodedSlash(true);
        firewall.setAllowSemicolon(true);
        firewall.setAllowBackSlash(true);
        return firewall;
    }
    
    @Bean
    public AuthenticationFilter customAuthenticationFilter() {
        return new AuthenticationFilter(userService);
    }
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许特定来源
        configuration.addAllowedOrigin("http://localhost:8080");
        configuration.addAllowedOrigin("http://************:8080");
        configuration.addAllowedOrigin("http://************:8080");
        
        // 动态允许所有ngrok-free.app的子域
        configuration.addAllowedOriginPattern("https://*.ngrok-free.app");
        
        // 打印允许的来源，用于调试
        System.out.println("配置的允许来源: " + allowedOrigins);
        
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "X-Requested-With", "Accept", 
            "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers", 
            "X-User-Id", "X-User-Email", "X-Authentication-Email", "X-Request-Time", "x-user-custom-id"
        ));
        configuration.setExposedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "Content-Length",
            "Access-Control-Allow-Origin", "Access-Control-Allow-Headers"
        ));
        configuration.setAllowCredentials(true); // 允许凭证
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
} 