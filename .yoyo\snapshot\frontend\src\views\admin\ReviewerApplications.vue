<template>
  <div class="reviewer-applications-container">
    <div class="page-header">
      <h1>审核医生申请管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="goBack">
          <i class="bi bi-arrow-left"></i> 返回
        </el-button>
      </div>
    </div>
    
    <el-tabs v-model="activeTab">
      <el-tab-pane label="待处理申请" name="pending">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        
        <div v-else-if="pendingApplications.length === 0" class="empty-state">
          <el-empty description="暂无待处理的申请" />
        </div>
        
        <div v-else>
          <el-table :data="pendingApplications" border style="width: 100%">
            <el-table-column prop="id" label="申请ID" width="100" />
            <el-table-column label="申请人" width="150">
              <template #default="scope">
                {{ getUserName(scope.row.userId) }}
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="申请理由" show-overflow-tooltip />
            <el-table-column prop="createdAt" label="申请时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="success" size="small" @click="handleApprove(scope.row)">
                  批准
                </el-button>
                <el-button type="danger" size="small" @click="handleReject(scope.row)">
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="已处理申请" name="processed">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        
        <div v-else-if="processedApplications.length === 0" class="empty-state">
          <el-empty description="暂无已处理的申请" />
        </div>
        
        <div v-else>
          <el-table :data="processedApplications" border style="width: 100%">
            <el-table-column prop="id" label="申请ID" width="100" />
            <el-table-column label="申请人" width="150">
              <template #default="scope">
                {{ getUserName(scope.row.userId) }}
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="申请理由" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" width="120">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'APPROVED' ? 'success' : 'danger'">
                  {{ scope.row.status === 'APPROVED' ? '已批准' : '已拒绝' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" show-overflow-tooltip />
            <el-table-column prop="processedAt" label="处理时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.processedAt) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import api from '@/utils/api'

export default {
  name: 'ReviewerApplications',
  data() {
    return {
      activeTab: 'pending',
      loading: false,
      pendingApplications: [],
      processedApplications: [],
      users: {}
    };
  },
  created() {
    this.fetchApplications();
  },
  methods: {
    // 获取申请列表
    fetchApplications() {
      this.loading = true;
      
      // 获取待处理申请
      api.users.getPendingReviewerApplications()
        .then(response => {
          this.pendingApplications = response.data || [];
          
          // 获取已处理申请
          return api.reviewer.getProcessedApplications();
        })
        .then(response => {
          this.processedApplications = response.data || [];
          
          // 获取所有用户信息
          return this.fetchUsers();
        })
        .catch(error => {
          console.error('获取申请列表失败:', error);
          this.$message.error('获取申请列表失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // 获取用户信息
    fetchUsers() {
      // 获取所有用户信息
      return api.users.getAll()
        .then(response => {
          if (response && response.data) {
            const allUsers = Array.isArray(response.data) ? response.data : [];
            
            // 将用户数组转换为以ID为键的对象
            allUsers.forEach(user => {
              this.$set(this.users, user.id, user);
            });
            
            console.log('成功获取所有用户信息:', Object.keys(this.users).length);
          }
        })
        .catch(error => {
          console.error('获取所有用户信息失败:', error);
        });
    },
    
    // 获取用户名
    getUserName(userId) {
      // 检查是否已经加载了用户信息
      if (this.users[userId] && this.users[userId].name) {
        return this.users[userId].name;
      }
      
      // 常用用户ID的备用名称映射
      const backupNames = {
        1: '管理员',
        2: '张医生',
        3: '李审核',
        4: '王医生',
        5: '赵医生',
        6: '钱医生',
        7: '孙医生',
        8: '周医生',
        9: '吴医生',
        10: '郑医生'
      };
      
      // 如果有备用名称，使用备用名称
      if (backupNames[userId]) {
        return backupNames[userId];
      }
      
      // 如果没有加载用户信息，尝试单独加载
      api.users.getUser(userId)
        .then(response => {
          if (response && response.data) {
            this.$set(this.users, userId, response.data);
          }
        })
        .catch(error => {
          console.error(`获取用户 ${userId} 信息失败:`, error);
        });
      
      // 返回临时显示内容
      return `用户 ${userId}`;
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },
    
    // 处理批准申请
    handleApprove(application) {
      this.$confirm('确定批准该用户成为审核医生吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.processApplication(application.userId, true);
      }).catch(() => {
        // 用户取消，不做处理
      });
    },
    
    // 处理拒绝申请
    handleReject(application) {
      this.$prompt('请输入拒绝理由', '拒绝申请', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入拒绝理由...'
      }).then(({ value }) => {
        this.processApplication(application.userId, false, value);
      }).catch(() => {
        // 用户取消，不做处理
      });
    },
    
    // 处理申请
    processApplication(userId, approved, remarks = '') {
      this.loading = true;
      
      // 根据用户ID查找对应的申请记录
      const application = this.pendingApplications.find(app => app.userId === userId);
      
      if (!application) {
        this.$message.error('未找到该用户的申请记录');
        this.loading = false;
        return;
      }
      
      const data = {
        status: approved ? 'APPROVED' : 'REJECTED',
        remarks: remarks || ''
      };
      
      // 使用申请ID而不是用户ID
      api.users.processReviewerApplication(application.id, data)
        .then(() => {
          this.$message.success(approved ? '已批准申请' : '已拒绝申请');
          this.fetchApplications();
        })
        .catch(error => {
          console.error('处理申请失败:', error);
          if (error.response && error.response.data && error.response.data.message) {
            this.$message.error(error.response.data.message);
          } else {
            this.$message.error('处理申请失败');
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    goBack() {
      // 返回部门成员页面
      this.$router.push('/app/users');
    }
  }
};
</script>

<style scoped>
.reviewer-applications-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  padding: 20px 0;
}

.empty-state {
  padding: 40px 0;
  display: flex;
  justify-content: center;
}
</style> 