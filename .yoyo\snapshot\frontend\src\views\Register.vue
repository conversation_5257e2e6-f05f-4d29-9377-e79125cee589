<template>
  <div class="container login-container">
    <div class="card">
      <div class="card-header">
        <h3 class="mb-0">血管瘤辅助系统 - 注册</h3>
      </div>
      <div class="card-body p-4">
        <div v-if="error" class="alert alert-danger">
          {{ error }}
        </div>
        <div v-if="success" class="alert alert-success">
          {{ success }}
        </div>
        
        <form @submit.prevent="handleRegister">
          <div class="mb-3">
            <label for="name" class="form-label">姓名</label>
            <input 
              type="text" 
              class="form-control" 
              id="name" 
              v-model="name" 
              required
              :disabled="loading"
            >
          </div>
          <div class="mb-3">
            <label for="email" class="form-label">邮箱</label>
            <input 
              type="email" 
              class="form-control" 
              id="email" 
              v-model="email" 
              required
              :disabled="loading"
            >
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">密码</label>
            <input 
              type="password" 
              class="form-control" 
              id="password" 
              v-model="password" 
              required
              :disabled="loading"
            >
          </div>
          <div class="mb-3">
            <label for="confirmPassword" class="form-label">确认密码</label>
            <input 
              type="password" 
              class="form-control" 
              id="confirmPassword" 
              v-model="confirmPassword" 
              required
              :disabled="loading"
            >
          </div>
          <div class="mb-3">
            <label for="hospital" class="form-label">医院</label>
            <input 
              type="text" 
              class="form-control" 
              id="hospital" 
              v-model="hospital" 
              required
              :disabled="loading"
            >
          </div>
          <div class="mb-3">
            <label for="department" class="form-label">科室</label>
            <input 
              type="text" 
              class="form-control" 
              id="department" 
              v-model="department" 
              :disabled="loading"
            >
          </div>
          <div class="d-grid gap-2">
            <button 
              type="submit" 
              class="btn btn-primary" 
              :disabled="loading"
            >
              <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              注册
            </button>
          </div>
        </form>
        
        <div class="mt-3 text-center">
          <router-link to="/login" class="text-decoration-none">已有账号？点击登录</router-link>
        </div>
      </div>
      <div class="card-footer text-center">
        <router-link to="/" class="btn btn-link">返回首页</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Register',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const name = ref('')
    const email = ref('')
    const password = ref('')
    const confirmPassword = ref('')
    const hospital = ref('')
    const department = ref('')
    const loading = ref(false)
    const error = ref('')
    const success = ref('')
    
    const handleRegister = async () => {
      // 验证密码
      if (password.value !== confirmPassword.value) {
        error.value = '两次输入的密码不一致'
        return
      }
      
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        await store.dispatch('register', {
          name: name.value,
          email: email.value,
          password: password.value,
          hospital: hospital.value,
          department: department.value,
          role: 'DOCTOR' // 默认注册为医生角色
        })
        
        success.value = '注册成功，请登录'
        
        // 重置表单
        name.value = ''
        email.value = ''
        password.value = ''
        confirmPassword.value = ''
        hospital.value = ''
        department.value = ''
        
        // 3秒后跳转到登录页
        setTimeout(() => {
          router.push('/login')
        }, 3000)
      } catch (err) {
        console.error('Registration error:', err)
        error.value = typeof err === 'string' ? err : '注册失败，请检查填写信息'
      } finally {
        loading.value = false
      }
    }
    
    return {
      name,
      email,
      password,
      confirmPassword,
      hospital,
      department,
      loading,
      error,
      success,
      handleRegister
    }
  }
}
</script>

<style scoped>
.login-container {
  max-width: 550px;
  margin: 60px auto;
}

.card {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #007bff;
  color: white;
  text-align: center;
  font-weight: bold;
  padding: 1rem;
}
</style> 