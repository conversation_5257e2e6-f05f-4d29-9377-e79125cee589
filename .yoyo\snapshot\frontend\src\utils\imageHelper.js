/**
 * 图像路径处理工具
 * 用于统一处理图像URL，解决路径不一致、中文编码等问题
 */

import { API_BASE_URL, API_CONTEXT_PATH } from '../config/api.config';

// 图像大小限制
const MAX_IMAGE_WIDTH = 700;
const MAX_IMAGE_HEIGHT = 700;

/**
 * 将相对路径转换为完整的图像URL
 * @param {string} path - 图像路径
 * @returns {string} 完整的图像URL
 */
export function getImageUrl(path) {
  if (!path) {
    console.warn('getImageUrl: 提供的路径为空');
    return '';
  }
  
  // 添加时间戳避免缓存问题
  const timestamp = new Date().getTime();
  
  // 防止路径为null或undefined
  const imagePath = String(path);
  console.log('getImageUrl处理路径:', {
    原始路径: imagePath,
    类型: typeof imagePath
  });
  
  // 如果已经是完整URL，直接返回并添加时间戳参数
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    console.log('getImageUrl: 检测到完整URL，添加时间戳参数');
    return imagePath + (imagePath.includes('?') ? '&' : '?') + 't=' + timestamp;
  }
  
  // 如果是data URL (base64)，直接返回
  if (imagePath.startsWith('data:')) {
    console.log('getImageUrl: 检测到Base64数据URL，直接返回');
    return imagePath;
  }
  
  // 尝试获取用户ID
  let userParam = '';
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userId = user.customId || user.id;
    if (userId) {
      userParam = `&userId=${userId}`;
    }
  } catch (e) {
    console.error('获取用户信息失败:', e);
  }

  // 关键修复：直接处理medical_images格式路径
  if (imagePath.includes('medical_images')) {
    // 用正则表达式分隔路径
    const regex = /medical_images[\/\\]([^\/\\]+)(?:[\/\\](.*))?/;
    const match = imagePath.match(regex);
    
    if (match) {
      const category = match[1]; // temp, original, processed等
      const filename = match[2] || ''; // 可能包含子目录的文件名
      
      // 构建标准URL - 添加时间戳和用户参数
      const url = `${API_CONTEXT_PATH}/images/${category}/${filename}?t=${timestamp}${userParam}`;
      const fullUrl = API_BASE_URL + url;
      console.log('getImageUrl: 将medical_images路径转换为URL:', {
        分类: category,
        文件名: filename,
        相对URL: url,
        完整URL: fullUrl
      });
      return fullUrl;
    }
  }
  
  // 对于/medical开头的路径，这些已经是后端的相对路径，只需确保添加服务器地址
  if (imagePath.startsWith(API_CONTEXT_PATH)) {
    const fullUrl = API_BASE_URL + imagePath + (imagePath.includes('?') ? '&' : '?') + 't=' + timestamp + userParam;
    console.log('使用原始后端路径并添加时间戳:', fullUrl);
    return fullUrl;
  }
  
  // 处理Windows文件系统绝对路径
  if (imagePath.match(/^[a-zA-Z]:\\/)) {
    // 提取文件名
    const fileName = imagePath.split(/[\/\\]/).pop();
    
    // 根据路径推断子目录
    let category = '';
    if (imagePath.includes('\\original\\') || imagePath.includes('/original/')) {
      category = 'original';
    } else if (imagePath.includes('\\processed\\') || imagePath.includes('/processed/')) {
      category = 'processed';
    } else if (imagePath.includes('\\annotate\\') || imagePath.includes('/annotate/')) {
      category = 'annotate';
    } else if (imagePath.includes('\\temp\\') || imagePath.includes('/temp/')) {
      category = 'temp';
    }
    
    // 构建URL
    const url = `${API_CONTEXT_PATH}/images/${category}/${fileName}`;
    console.log('将Windows路径转换为:', url);
    return API_BASE_URL + url;
  }
  
  // 确保路径以/开头
  let normalizedPath = imagePath.startsWith('/') ? imagePath : '/' + imagePath;
  
  // 添加context path前缀
  if (!normalizedPath.startsWith(API_CONTEXT_PATH)) {
    normalizedPath = API_CONTEXT_PATH + normalizedPath;
  }
  
  // 处理可能的中文字符
  try {
    // 仅编码文件名部分
    const lastSlashIndex = normalizedPath.lastIndexOf('/');
    if (lastSlashIndex !== -1) {
      const directory = normalizedPath.substring(0, lastSlashIndex + 1);
      const filename = normalizedPath.substring(lastSlashIndex + 1);
      normalizedPath = directory + encodeURIComponent(filename);
    }
  } catch (e) {
    console.error('图像路径编码失败:', e);
  }
  
  console.log('标准化后的图像路径:', normalizedPath);
  return API_BASE_URL + normalizedPath;
}

/**
 * 从图像路径中提取文件名
 * @param {string} path - 图像路径
 * @returns {string} 文件名
 */
export function getImageFilename(path) {
  if (!path) return '';
  
  const parts = path.split('/');
  return parts[parts.length - 1];
}

/**
 * 判断是否是图像文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否是图像文件
 */
export function isImageFile(filename) {
  if (!filename) return false;
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  const lowerFilename = filename.toLowerCase();
  
  return imageExtensions.some(ext => lowerFilename.endsWith(ext));
}

/**
 * 调整图像大小，确保不超过最大宽高限制
 * @param {HTMLImageElement|File} imageSource - 图像元素或文件对象
 * @param {Function} callback - 回调函数，接收处理后的图像数据(DataURL或Blob)和尺寸信息
 * @param {Object} options - 选项
 * @param {number} options.maxWidth - 最大宽度，默认700px
 * @param {number} options.maxHeight - 最大高度，默认700px
 * @param {string} options.outputFormat - 输出格式，'dataUrl'或'blob'，默认'dataUrl'
 * @param {string} options.imageType - 图像类型，默认'image/jpeg'
 * @param {number} options.quality - 压缩质量，0-1之间，默认0.9
 */
export function resizeImage(imageSource, callback, options = {}) {
  const settings = {
    maxWidth: options.maxWidth || MAX_IMAGE_WIDTH,
    maxHeight: options.maxHeight || MAX_IMAGE_HEIGHT,
    outputFormat: options.outputFormat || 'dataUrl',
    imageType: options.imageType || 'image/jpeg',
    quality: options.quality || 0.9
  };
  
  // 处理File对象
  if (imageSource instanceof File) {
    const reader = new FileReader();
    reader.onload = function(e) {
      const img = new Image();
      img.onload = function() {
        processImageResize(img, settings, callback);
      };
      img.src = e.target.result;
    };
    reader.readAsDataURL(imageSource);
    return;
  }
  
  // 处理Image对象
  if (imageSource instanceof HTMLImageElement) {
    if (imageSource.complete) {
      processImageResize(imageSource, settings, callback);
    } else {
      imageSource.onload = function() {
        processImageResize(imageSource, settings, callback);
      };
    }
    return;
  }
  
  // 处理其他情况（字符串URL或DataURL）
  if (typeof imageSource === 'string') {
    const img = new Image();
    img.onload = function() {
      processImageResize(img, settings, callback);
    };
    img.src = imageSource;
    return;
  }
  
  // 不支持的类型
  console.error('不支持的图像源类型:', imageSource);
  callback(null, { error: '不支持的图像源类型' });
}

/**
 * 处理图像大小调整的核心逻辑
 * @private
 */
function processImageResize(img, settings, callback) {
  // 获取原始尺寸
  const originalWidth = img.naturalWidth || img.width;
  const originalHeight = img.naturalHeight || img.height;
  
  // 计算调整后的尺寸，保持宽高比
  let newWidth = originalWidth;
  let newHeight = originalHeight;
  
  // 如果图像超出最大限制，按比例缩小
  if (originalWidth > settings.maxWidth || originalHeight > settings.maxHeight) {
    const widthRatio = settings.maxWidth / originalWidth;
    const heightRatio = settings.maxHeight / originalHeight;
    const ratio = Math.min(widthRatio, heightRatio);
    
    newWidth = Math.floor(originalWidth * ratio);
    newHeight = Math.floor(originalHeight * ratio);
  }
  
  // 创建Canvas绘制调整后的图像
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = newWidth;
  canvas.height = newHeight;
  
  // 绘制图像
  ctx.drawImage(img, 0, 0, newWidth, newHeight);
  
  // 输出数据
  const sizeInfo = {
    originalWidth,
    originalHeight,
    newWidth,
    newHeight,
    resized: (originalWidth !== newWidth || originalHeight !== newHeight)
  };
  
  // 根据需要的输出格式返回结果
  if (settings.outputFormat === 'blob') {
    canvas.toBlob(
      (blob) => callback(blob, sizeInfo),
      settings.imageType,
      settings.quality
    );
  } else {
    // 默认返回DataURL
    const dataUrl = canvas.toDataURL(settings.imageType, settings.quality);
    callback(dataUrl, sizeInfo);
  }
}

export default {
  getImageUrl,
  getImageFilename,
  isImageFile,
  resizeImage,
  MAX_IMAGE_WIDTH,
  MAX_IMAGE_HEIGHT
}; 