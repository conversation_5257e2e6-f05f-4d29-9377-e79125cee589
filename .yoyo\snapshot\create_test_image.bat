@echo off
setlocal enabledelayedexpansion

rem 获取当前时间戳（秒级精度）
for /f "tokens=1-3 delims=:." %%a in ("%time%") do (
    set "hour=%%a"
    set "minute=%%b"
    set "second=%%c"
)

rem 处理小时的前导零
set "hour=%hour: =0%"

rem 获取当前日期
for /f "tokens=1-3 delims=/" %%a in ("%date%") do (
    set "day=%%a"
    set "month=%%b"
    set "year=%%c"
)

rem 根据时间创建一个近似的时间戳ID (简化版本)
set "timestamp=%year%%month%%day%%hour%%minute%%second%"

echo 创建的时间戳ID: %timestamp%

rem 复制测试图像并重命名
copy "medical_images\temp\temp_IH_009_032.jpg" "medical_images\temp\test_%timestamp%.jpg"

echo 复制完成: medical_images\temp\test_%timestamp%.jpg

rem 显示SQL插入命令
echo.
echo 请执行以下SQL命令创建对应的记录:
echo.
echo INSERT INTO image_metadata 
echo (id, filename, original_name, path, mimetype, size, width, height, uploaded_by, status) 
echo VALUES 
echo (%timestamp%, 'test_%timestamp%.jpg', 'test_%timestamp%.jpg', 
echo '/medical/images/temp/test_%timestamp%.jpg', 'image/jpeg', 98856, 800, 600, 2, 'DRAFT');
echo.
echo INSERT INTO image_pairs 
echo (metadata_id, image_one_path, description, created_by) 
echo VALUES 
echo (%timestamp%, '/medical/images/temp/test_%timestamp%.jpg', 'Batch生成的测试记录', 2);

pause 