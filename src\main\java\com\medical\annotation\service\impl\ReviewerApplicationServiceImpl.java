package com.medical.annotation.service.impl;

import com.medical.annotation.model.ReviewerApplication;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.ReviewerApplicationRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.service.ReviewerApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class ReviewerApplicationServiceImpl implements ReviewerApplicationService {

    @Autowired
    private ReviewerApplicationRepository reviewerApplicationRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Override
    public ReviewerApplication createApplication(Integer userId, String reason) {
        // 检查用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        User user = userOpt.get();
        
        // 检查用户是否已经是审核医生
        if (user.getRole() != null && user.getRole() == User.Role.REVIEWER) {
            throw new IllegalArgumentException("用户已经是审核医生，无需申请");
        }
        
        // 检查是否已有待处理的申请
        Optional<ReviewerApplication> existingApplication = 
            reviewerApplicationRepository.findByUserIdAndStatus(userId, "PENDING");
        
        if (existingApplication.isPresent()) {
            throw new IllegalArgumentException("用户已有待处理的申请");
        }
        
        // 创建新的申请
        ReviewerApplication application = new ReviewerApplication();
        application.setUserId(userId);
        application.setReason(reason);
        application.setStatus("PENDING");
        
        return reviewerApplicationRepository.save(application);
    }
    
    @Override
    @Transactional
    public ReviewerApplication processApplication(Integer id, String status, String remarks, Integer processorId) {
        if (!status.equals("APPROVED") && !status.equals("REJECTED")) {
            throw new IllegalArgumentException("状态必须为 APPROVED 或 REJECTED");
        }
        
        Optional<ReviewerApplication> applicationOpt = reviewerApplicationRepository.findById(id);
        if (!applicationOpt.isPresent()) {
            throw new IllegalArgumentException("申请不存在");
        }
        
        ReviewerApplication application = applicationOpt.get();
        
        // 如果申请已经被处理，返回错误
        if (!application.getStatus().equals("PENDING")) {
            throw new IllegalArgumentException("该申请已被处理");
        }
        
        // 更新申请状态
        application.setStatus(status);
        // application.setRemarks(remarks); // 移除对remarks字段的设置，因为数据库中没有这个列
        application.setProcessedBy(processorId);
        application.setProcessedAt(new Date());
        
        // 如果批准申请，更新用户角色
        if (status.equals("APPROVED")) {
            Optional<User> userOpt = userRepository.findById(application.getUserId());
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                user.setRole(User.Role.REVIEWER);
                userRepository.save(user);
            }
        }
        
        return reviewerApplicationRepository.save(application);
    }
    
    @Override
    public List<ReviewerApplication> getUserApplications(Integer userId) {
        return reviewerApplicationRepository.findByUserId(userId);
    }
    
    @Override
    public List<ReviewerApplication> getPendingApplications() {
        return reviewerApplicationRepository.findByStatus("PENDING");
    }
    
    @Override
    public List<ReviewerApplication> getProcessedApplications() {
        return reviewerApplicationRepository.findByStatusNot("PENDING");
    }
    
    @Override
    public Optional<ReviewerApplication> getApplication(Integer id) {
        return reviewerApplicationRepository.findById(id);
    }
} 