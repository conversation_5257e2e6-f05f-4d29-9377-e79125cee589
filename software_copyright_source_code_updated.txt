// ========== File: ai_service.py ==========

from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
import numpy as np
from PIL import Image
import io
import cv2
import time
import json
import uvicorn
import os
import ollama
import httpx
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import logging
import uuid
from ultralytics import YOLO
from pathlib import Path
import re # Added for JSON fixing

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ai-service")

app = FastAPI(title="血管瘤AI分析服务", description="提供YOLO目标检测与LLM诊断建议生成功能")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，或者可以指定 ["http://localhost:8080", "http://************"]
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 全局变量存储模型
yolo_model = None
color_model = None
part_model = None

# 类别名称 - 根据您的模型调整
CLASS_NAMES = ["IH", "RICH", "PICH", "NICH", "KHE", "KH", "PG", "MVM", "VM", "AVM"]
COLOR_CLASS_NAMES = []
PART_CLASS_NAMES = []


# 新增中文名称映射
CLASS_NAMES_CHINESE = {
    "IH": "婴幼儿血管瘤",
    "RICH": "先天性快速消退型血管瘤",
    "PICH": "先天性部分消退型血管瘤",
    "NICH": "先天性不消退型血管瘤",
    "KHE": "卡波西型血管内皮细胞瘤",
    "KH": "角化型血管瘤",
    "PG": "肉芽肿性血管瘤",
    "MVM": "微静脉畸形",
    "VM": "静脉畸形",
    "AVM": "动静脉畸形"
}

# 模型配置
MODEL_PATH = "best1.onnx"  # 使用已存在的ONNX文件
COLOR_MODEL_PATH = "Color/best_color.pt"
PART_MODEL_PATH = "Part/best_part(2).pt"
CONFIDENCE_THRESHOLD = 0.25  # 置信度阈值
IOU_THRESHOLD = 0.45  # NMS IOU阈值
# 分类标签路径
COLOR_CLASSES_PATH = "Color/colors_classes_CN.txt"
PART_CLASSES_PATH = "Part/Part_classes_CN.txt"


# LLM服务配置
# The Ollama library automatically connects to the default endpoint (http://localhost:11434)
# To configure a different host, set the OLLAMA_HOST environment variable.
# Example: export OLLAMA_HOST="http://*************:11434"
LLM_MODEL_NAME = os.getenv("LLM_MODEL_NAME", "deepseek-r1:8b")
JAVA_CALLBACK_URL = os.getenv("JAVA_CALLBACK_URL", "http://localhost:8085/medical/api/hemangioma-diagnoses/update-recommendation")

# 处理后图片保存路径
PROCESSED_DIR = "medical_images/processed"

# 确保保存目录存在
os.makedirs(PROCESSED_DIR, exist_ok=True)

class DetectionBox(BaseModel):
    class_id: int
    class_name: str
    confidence: float
    bbox: List[int]
    width: int
    height: int
    orig_width: int
    orig_height: int

class PredictedClass(BaseModel):
    name: str
    confidence: float

class DetectionResult(BaseModel):
    detected: bool
    confidence: float = 0.0
    boxes: List[DetectionBox] = []
    detection_boxes: List[DetectionBox] = [] # 新增字段以兼容Java端
    processing_time: float
    image_dimensions: Dict[str, int]
    processed_image_path: str = ""
    recommendation: str = ""
    detected_type: str = ""
    predicted_color: Optional[PredictedClass] = None
    predicted_part: Optional[PredictedClass] = None

def get_vessel_texture_label(value: Optional[str]) -> str:
    if not value: return "未提供"
    labels = {
        "soft": "质软",
        "elastic": "质韧",
        "hard": "质硬",
        "cystic": "囊性",
        "compressible": "可压缩"
    }
    return labels.get(value, "未提供")

def call_llm_service(
    patient_age: Optional[int], 
    gender: Optional[str], 
    origin_type: Optional[str], 
    vessel_texture: Optional[str], 
    confidence: float,
    detected_types: str,
    predicted_color: Optional[PredictedClass],
    predicted_part: Optional[PredictedClass]
) -> str:
    """调用大模型服务获取诊断建议"""
    logger.info("开始调用大模型服务 (后台)...")
    
    # 将检测到的类型缩写转换为带中文解释的完整字符串
    if detected_types:
        type_abbrs = detected_types.split('+')
        type_full_names = [f"{abbr} ({CLASS_NAMES_CHINESE.get(abbr, '未知类型')})" for abbr in type_abbrs]
        detected_types_with_explanation = ", ".join(type_full_names)
    else:
        detected_types_with_explanation = "未检测到特定亚型"

    # 1. 定义上下文信息
    context_info = {
        "诊断疾病": "血管瘤",
        "AI初步识别亚型": detected_types_with_explanation,
        "AI识别病灶颜色": predicted_color.name if predicted_color else "未识别",
        "AI识别病灶部位": predicted_part.name if predicted_part else "未识别",
        "患者年龄": f"{patient_age} 岁" if patient_age is not None else "未知",
        "患者性别": gender if gender else "未知",
        "病变类型": origin_type if origin_type else "未知",
        "血管质地": get_vessel_texture_label(vessel_texture),
        "AI初步识别置信度": f"{confidence * 100:.1f}%" if confidence > 0 else "N/A"
    }

    # 2. 定义JSON模板
    json_template = {
      "treatment_suggestion": "",
      "precautions": "",
      "disclaimer": "本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
    }

    # 3. 构建提示
    prompt = f"""你是一名资深的血管瘤医学专家。请基于以下患者信息，生成一份个性化健康管理方案。
# 患者信息：
{json.dumps(context_info, indent=2, ensure_ascii=False)}

# 任务:
严格按照以下JSON格式填充内容并返回。不要添加任何额外的解释或说明文字，只返回JSON对象。请确保返回的是有效的JSON格式，所有键名必须与模板完全一致，不要修改键名。
{json.dumps(json_template, indent=2, ensure_ascii=False)}
"""

    messages = [{'role': 'user', 'content': prompt}]

    try:
        logger.info(f"向大模型 {LLM_MODEL_NAME} 发送请求..")
        
        response = ollama.chat(
            model=LLM_MODEL_NAME,
            messages=messages,
            stream=False,
            options={"temperature": 0.0}
        )
        
        model_output = response['message']['content']
        logger.info(f"大模型返回内容长度: {len(model_output)} 字符")
        logger.info(f"大模型返回内容前100字符: {model_output[:100]}")
        
        # 增强的JSON提取逻辑
        try:
            # 找到第一个'{' 和最后一个'}' 来提取JSON块
            json_start = model_output.find('{')
            json_end = model_output.rfind('}') + 1
            
            if json_start != -1 and json_end > 0:
                json_string = model_output[json_start:json_end]
                # 验证提取的字符串是否为有效的JSON
                try:
                    # 先尝试直接解析
                    parsed_json = json.loads(json_string)
                    logger.info("后台任务成功从大模型响应中提取并验证了JSON。")
                    
                    # 确保所有必要的字段都存在
                    required_fields = ["treatment_suggestion", "precautions", "disclaimer"]
                    missing_fields = [field for field in required_fields if field not in parsed_json]
                    
                    if missing_fields:
                        logger.warning(f"JSON中缺少必要字段: {missing_fields}")
                        # 添加缺失的字段
                        for field in missing_fields:
                            parsed_json[field] = "由于AI返回格式错误，无法提供详细建议。请咨询医生获取专业意见。"
                        
                        # 返回修复后的JSON
                        return json.dumps(parsed_json, ensure_ascii=False)
                    else:
                        # 所有字段都存在，返回原始JSON
                        return json_string
                        
                except json.JSONDecodeError:
                    # 如果JSON格式不正确，尝试修复常见错误
                    logger.warning("提取的JSON格式不正确，尝试修复...")
                    
                    # 修复可能的缺少引号
                    fixed_json = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', json_string)
                    
                    # 修复可能的多余逗号
                    fixed_json = re.sub(r',\s*}', '}', fixed_json)
                    
                    # 尝试解析修复后的JSON
                    try:
                        parsed_json = json.loads(fixed_json)
                        logger.info("成功修复并验证JSON格式。")
                        
                        # 确保所有必要的字段都存在
                        required_fields = ["treatment_suggestion", "precautions", "disclaimer"]
                        missing_fields = [field for field in required_fields if field not in parsed_json]
                        
                        if missing_fields:
                            logger.warning(f"修复后的JSON仍缺少必要字段: {missing_fields}")
                            # 添加缺失的字段
                            for field in missing_fields:
                                parsed_json[field] = "由于AI返回格式错误，无法提供详细建议。请咨询医生获取专业意见。"
                            
                            # 返回修复后的JSON
                            return json.dumps(parsed_json, ensure_ascii=False)
                        else:
                            # 所有字段都存在，返回修复后的JSON
                            return json.dumps(parsed_json, ensure_ascii=False)
                            
                    except json.JSONDecodeError as e:
                        # 如果修复失败，创建一个完整的新JSON
                        logger.error(f"无法修复JSON格式: {e}，使用默认模板。")
                        template = {
                            "treatment_suggestion": "由于AI返回格式错误，无法提供详细治疗建议。请咨询医生获取专业意见。",
                            "precautions": "如有任何不适，请立即就医。",
                            "disclaimer": "本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
                        }
                        return json.dumps(template, ensure_ascii=False)
            else:
                # 如果找不到'{' 或'}'，则认为格式无效
                logger.error("在模型输出中未找到有效的JSON对象。")
                template = {
                    "treatment_suggestion": "由于AI返回格式错误，无法提供详细治疗建议。请咨询医生获取专业意见。",
                    "precautions": "如有任何不适，请立即就医。",
                    "disclaimer": "本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
                }
                return json.dumps(template, ensure_ascii=False)

        except Exception as e:
            logger.error(f"处理大模型返回的JSON时发生未知错误: {e}")
            # 返回一个包含错误信息的JSON字符串
            template = {
                "treatment_suggestion": "由于AI返回格式错误，无法提供详细治疗建议。请咨询医生获取专业意见。",
                "precautions": "如有任何不适，请立即就医。",
                "disclaimer": "本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。"
            }
            return json.dumps(template, ensure_ascii=False)

    except Exception as e:
        error_message = str(e)
        logger.error(f"后台任务调用大模型服务失败: {error_message}")
        if "connection refused" in error_message.lower():
             logger.error("无法连接到Ollama服务。请确保Ollama正在运行。")
             return json.dumps({"error": "无法连接到Ollama服务，请确保其正在运行。"})
        return json.dumps({"error": f"调用大模型服务失败: {error_message}"})

// ========== File: src/main/java/com/medical/annotation/model/HemangiomaDiagnosis.java ==========

package com.medical.annotation.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "hemangioma_diagnoses")
public class HemangiomaDiagnosis {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private Integer patientAge;
    private String gender;
    private String originType;
    private String vesselTexture;
    private String color;
    private String bodyPart;

    @Column(length = 1000)
    private String treatmentSuggestion;

    @Column(length = 1000)
    private String precautions;

    @Column(length = 500)
    private String disclaimer;

    @Column(length = 1000)
    private String diagnosticSummary;
    
    @Column(length = 1000)
    private String reviewNotes;

    private Double confidence;
    private String detectedType;
    private String imagePath;
    private String processedImagePath;
    
    @Column(columnDefinition = "varchar(100)")
    private String formattedId;
    
    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'REJECTED') DEFAULT 'PENDING'")
    private Status status = Status.PENDING;

    private LocalDateTime createdAt;
    private LocalDateTime reviewedAt;
    
    @Column(columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isPrivate = false;

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;
    
    @ManyToOne
    @JoinColumn(name = "reviewer_id")
    private User reviewer;

    @OneToMany(mappedBy = "hemangiomaDiagnosis", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference
    private List<Tag> tags = new ArrayList<>();
    
    @Column(columnDefinition = "TEXT")
    private String annotationData;
    
    @ElementCollection
    @CollectionTable(name = "hemangioma_bounding_boxes", joinColumns = @JoinColumn(name = "hemangioma_id"))
    @Column(name = "bounding_box")
    private List<String> boundingBoxes = new ArrayList<>();
    
    // 枚举类型定义
    public enum Status {
        PENDING, PROCESSING, COMPLETED, REJECTED
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPatientAge() {
        return patientAge;
    }

    public void setPatientAge(Integer patientAge) {
        this.patientAge = patientAge;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getOriginType() {
        return originType;
    }

    public void setOriginType(String originType) {
        this.originType = originType;
    }

    public String getVesselTexture() {
        return vesselTexture;
    }

    public void setVesselTexture(String vesselTexture) {
        this.vesselTexture = vesselTexture;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getBodyPart() {
        return bodyPart;
    }

    public void setBodyPart(String bodyPart) {
        this.bodyPart = bodyPart;
    }

    public String getTreatmentSuggestion() {
        return treatmentSuggestion;
    }

    public void setTreatmentSuggestion(String treatmentSuggestion) {
        this.treatmentSuggestion = treatmentSuggestion;
    }

    public String getPrecautions() {
        return precautions;
    }

    public void setPrecautions(String precautions) {
        this.precautions = precautions;
    }

    public String getDisclaimer() {
        return disclaimer;
    }

    public void setDisclaimer(String disclaimer) {
        this.disclaimer = disclaimer;
    }

    public String getDiagnosticSummary() {
        return diagnosticSummary;
    }

    public void setDiagnosticSummary(String diagnosticSummary) {
        this.diagnosticSummary = diagnosticSummary;
    }
    
    public String getReviewNotes() {
        return reviewNotes;
    }
    
    public void setReviewNotes(String reviewNotes) {
        this.reviewNotes = reviewNotes;
    }

    public Double getConfidence() {
        return confidence;
    }

    public void setConfidence(Double confidence) {
        this.confidence = confidence;
    }

    public String getDetectedType() {
        return detectedType;
    }

    public void setDetectedType(String detectedType) {
        this.detectedType = detectedType;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getProcessedImagePath() {
        return processedImagePath;
    }

    public void setProcessedImagePath(String processedImagePath) {
        this.processedImagePath = processedImagePath;
    }
    
    public String getFormattedId() {
        return formattedId;
    }
    
    public void setFormattedId(String formattedId) {
        this.formattedId = formattedId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getReviewedAt() {
        return reviewedAt;
    }

    public void setReviewedAt(LocalDateTime reviewedAt) {
        this.reviewedAt = reviewedAt;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
    
    public User getReviewer() {
        return reviewer;
    }
    
    public void setReviewer(User reviewer) {
        this.reviewer = reviewer;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }
    
    public String getAnnotationData() {
        return annotationData;
    }
    
    public void setAnnotationData(String annotationData) {
        this.annotationData = annotationData;
    }
    
    public List<String> getBoundingBoxes() {
        return boundingBoxes;
    }
    
    public void setBoundingBoxes(List<String> boundingBoxes) {
        this.boundingBoxes = boundingBoxes;
    }
    
    public Boolean getIsPrivate() {
        return isPrivate;
    }
    
    public void setIsPrivate(Boolean isPrivate) {
        this.isPrivate = isPrivate;
    }

    // 添加/删除标签的便捷方法
    public void addTag(Tag tag) {
        tags.add(tag);
        tag.setHemangiomaDiagnosis(this);
    }

    public void removeTag(Tag tag) {
        tags.remove(tag);
        tag.setHemangiomaDiagnosis(null);
    }
}

// ========== File: frontend/src/views/CaseStructuredForm.vue ==========

<template>
  <div class="case-form-container">
    <h2>结构化病例填写</h2>
    
    <el-alert
      type="info"
      :closable="false"
      title="注意：带有 * 标记的字段为必填项，其他字段为选填项。治疗与注意事项中的四个字段必须填写。"
      show-icon
    />
    
    <el-form
      ref="caseForm"
      :model="formData"
      :rules="rules"
      label-width="140px"
      class="case-form"
    >
      <!-- 基本信息 -->
      <h3>基本信息</h3>
      <div class="adaptive-fields-container">
        <el-form-item label="患者年龄" prop="patientAge">
          <el-input-number
            v-model="formData.patientAge"
            :min="0"
            :max="120"
            placeholder="输入年龄"
          />
        </el-form-item>
        
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="formData.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="类型" prop="originType">
          <el-radio-group v-model="formData.originType">
            <el-radio label="先天性">先天性</el-radio>
            <el-radio label="后天性">后天性</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="血管质地" prop="vesselTexture">
          <el-select v-model="formData.vesselTexture" placeholder="请选择血管质地">
            <el-option label="质软" value="soft" />
            <el-option label="质韧" value="elastic" />
            <el-option label="质硬" value="hard" />
            <el-option label="囊性" value="cystic" />
            <el-option label="可压缩" value="compressible" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="病变部位" prop="bodyPart">
          <el-select v-model="formData.bodyPart" placeholder="选择部位">
            <el-option label="头部" value="头部" />
            <el-option label="颈部" value="颈部" />
            <el-option label="躯干" value="躯干" />
            <el-option label="四肢" value="四肢" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="颜色" prop="color">
          <el-select v-model="formData.color" placeholder="选择颜色">
            <el-option label="红色" value="红色" />
            <el-option label="紫色" value="紫色" />
            <el-option label="蓝色" value="蓝色" />
            <el-option label="褐色" value="褐色" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
      </div>
      
      <!-- 治疗与注意事项必填字段 -->
      <h3>治疗与注意事项</h3>
      <el-form-item label="治疗建议" prop="treatmentSuggestion">
        <el-input
          v-model="formData.treatmentSuggestion"
          type="textarea"
          :rows="3"
          placeholder="请输入治疗建议"
        />
      </el-form-item>
      
      <el-form-item label="注意事项" prop="precautions">
        <el-input
          v-model="formData.precautions"
          type="textarea"
          :rows="3"
          placeholder="请输入注意事项"
        />
      </el-form-item>
      
      <el-form-item label="免责声明" prop="disclaimer">
        <el-input
          v-model="formData.disclaimer"
          type="textarea"
          :rows="2"
          placeholder="请输入免责声明"
        />
      </el-form-item>
      
      <el-form-item label="诊断摘要" prop="diagnosticSummary">
        <el-input
          v-model="formData.diagnosticSummary"
          type="textarea"
          :rows="3"
          placeholder="请输入诊断摘要"
        />
      </el-form-item>
      
      <!-- 提交按钮 -->
      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="loading">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import axios from 'axios';
import { ElMessage } from 'element-plus';

export default {
  name: 'CaseStructuredForm',
  data() {
    return {
      formData: {
        patientAge: null,
        gender: '男',
        originType: '先天性',
        vesselTexture: '',
        bodyPart: '',
        color: '',
        treatmentSuggestion: '',
        precautions: '',
        disclaimer: '本报告由AI生成，仅供参考，不能替代专业医生的面诊。请务必咨询医生以获得最终诊断和治疗方案。',
        diagnosticSummary: '',
      },
      rules: {
        patientAge: [
          { type: 'number', required: true, message: '请输入患者年龄', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        originType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        treatmentSuggestion: [
          { required: true, message: '请输入治疗建议', trigger: 'blur' }
        ],
        precautions: [
          { required: true, message: '请输入注意事项', trigger: 'blur' }
        ],
        disclaimer: [
          { required: true, message: '请输入免责声明', trigger: 'blur' }
        ],
        diagnosticSummary: [
          { required: true, message: '请输入诊断摘要', trigger: 'blur' }
        ]
      },
      loading: false
    };
  },
  methods: {
    onSubmit() {
      this.$refs.caseForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          
          // 创建请求数据
          const payload = {
            patientAge: this.formData.patientAge,
            gender: this.formData.gender,
            originType: this.formData.originType,
            vesselTexture: this.formData.vesselTexture,
            bodyPart: this.formData.bodyPart,
            color: this.formData.color,
            treatmentSuggestion: this.formData.treatmentSuggestion,
            precautions: this.formData.precautions,
            disclaimer: this.formData.disclaimer,
            diagnosticSummary: this.formData.diagnosticSummary
          };
          
          // 发送请求
          axios.post('/medical/api/cases', payload)
            .then(response => {
              ElMessage.success('病例保存成功');
              // 重置表单
              this.resetForm();
              // 可以选择导航到新创建的病例详情页
              this.$router.push({ name: 'CaseView', params: { id: response.data.id } });
            })
            .catch(error => {
              console.error('保存病例时出错:', error);
              ElMessage.error('保存病例失败，请重试');
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          ElMessage.warning('表单验证失败，请检查必填字段');
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.caseForm.resetFields();
    }
  }
};
</script>

<style scoped>
.case-form-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  color: #303133;
}

h3 {
  margin-top: 20px;
  margin-bottom: 15px;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.adaptive-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.adaptive-fields-container .el-form-item {
  margin-right: 0;
  margin-bottom: 15px;
}

/* 响应式布局 */
@media (min-width: 1200px) {
  .adaptive-fields-container .el-form-item {
    max-width: calc(16.67% - 15px);
  }
}

@media (min-width: 900px) and (max-width: 1199px) {
  .adaptive-fields-container .el-form-item {
    max-width: calc(25% - 15px);
  }
}

@media (min-width: 600px) and (max-width: 899px) {
  .adaptive-fields-container .el-form-item {
    max-width: calc(33.33% - 15px);
  }
}

@media (max-width: 599px) {
  .adaptive-fields-container .el-form-item {
    max-width: 100%;
  }
}
</style> 

// ========== File: src/main/java/com/medical/annotation/controller/HemangiomaDiagnosisController.java ==========

package com.medical.annotation.controller;

import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.model.User;
import com.medical.annotation.model.Tag;
import com.medical.annotation.repository.HemangiomaDiagnosisRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.service.FileService;
import com.medical.annotation.util.FileNameGenerator;
import com.medical.annotation.util.BasePathConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.util.concurrent.CompletableFuture;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.medical.annotation.service.UserService;

@RestController
@RequestMapping({"/api/hemangioma-diagnoses", "/medical/api/hemangioma-diagnoses"})
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class HemangiomaDiagnosisController {

    @Autowired
    private HemangiomaDiagnosisRepository hemangiomaDiagnosisRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TagRepository tagRepository;
    
    @Autowired
    private FileService fileService;
    
    @Autowired
    private BasePathConfig basePathConfig;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private UserService userService;
    
    @Value("${ai.service.url:http://localhost:8086}")
    private String aiServiceUrl;
    
    private String tempDir;
    
    @PostConstruct
    public void init() {
        tempDir = basePathConfig.getTempDir();
        
        // 如果RestTemplate没有被Spring自动注入，手动创建一个
        if (restTemplate == null) {
            restTemplate = new RestTemplate();
        }
    }
    
    /**
     * 上传图像并创建诊断记录
     */
    @PostMapping("/upload-and-diagnose")
    public ResponseEntity<?> uploadAndDiagnose(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "patient_age", required = false) Integer patientAge,
            @RequestParam(value = "gender", required = false) String gender,
            @RequestParam(value = "origin_type", required = false) String originType,
            @RequestParam(value = "vessel_texture", required = false) String vesselTexture,
            @RequestParam(value = "user_id", required = false) Integer userId,
            @RequestParam(value = "annotationData", required = false) String annotationData,
            @RequestParam(value = "boundingBoxes", required = false) List<String> boundingBoxes,
            HttpServletRequest request) {
        try {
            // 1. 获取用户ID
            if (userId == null) {
                String userIdHeader = request.getHeader("X-User-ID");
                if (userIdHeader != null && !userIdHeader.isEmpty()) {
                    try {
                        // 处理可能包含逗号的多个ID格式（如"300000001, 3"）
                        if (userIdHeader.contains(",")) {
                            // 分割并返回去掉空格的第一个有效ID
                            String[] idParts = userIdHeader.split(",");
                            for (String idPart : idParts) {
                                String trimmedId = idPart.trim();
                                if (!trimmedId.isEmpty()) {
                                    System.out.println("从多个ID中提取第一个有效ID: " + trimmedId);
                                    userIdHeader = trimmedId;
                                    break;
                                }
                            }
                        }
                        
                        userId = Integer.parseInt(userIdHeader);
                        System.out.println("成功解析用户ID: " + userId);
                    } catch (NumberFormatException e) {
                        System.err.println("无效的用户ID格式: " + userIdHeader);
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                .body(Map.of("error", "无效的用户ID格式"));
                    }
                } else {
                    System.err.println("未提供用户ID");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(Map.of("error", "需要提供用户ID，请先登录"));
                }
            } else {
                System.out.println("使用表单参数中的用户ID: " + userId);
            }
            
            System.out.println("处理血管瘤诊断请求 - 用户ID: " + userId);
            
            // 2. 保存图像到temp目录
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String uniqueFilename = FileNameGenerator.generateUniqueFileName("hemangioma_" + fileExtension);
            
            // 确保temp目录存在
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                tempDirFile.mkdirs();
            }
            
            // 保存文件 - 使用临时文件机制
            Path targetPath = Paths.get(tempDir, uniqueFilename);
            File targetFile = targetPath.toFile();
            file.transferTo(targetFile);
            
            // 构建Web访问路径
            String webPath = "/medical/images/temp/" + uniqueFilename;
            System.out.println("图像已保存到: " + targetPath + ", Web路径: " + webPath);
            
            // 3. 创建HemangiomaDiagnosis记录 (暂时保留，但不更新AI检测结果)
            HemangiomaDiagnosis diagnosis = new HemangiomaDiagnosis();
            diagnosis.setPatientAge(patientAge);
            diagnosis.setGender(gender);
            diagnosis.setOriginType(originType);
            diagnosis.setVesselTexture(vesselTexture);
            diagnosis.setCreatedAt(LocalDateTime.now());
            diagnosis.setImagePath(webPath);
            if (annotationData != null) diagnosis.setAnnotationData(annotationData);
            if (boundingBoxes != null) diagnosis.setBoundingBoxes(new ArrayList<>(boundingBoxes));

            // 设置用户关联
            Optional<User> user = userRepository.findById(userId);
            if (user.isPresent()) {
                diagnosis.setUser(user.get());
            } else {
                // 找不到用户，返回错误
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "无法找到ID为" + userId + "的用户"));
            }
            
            // 保存诊断记录
            HemangiomaDiagnosis savedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
            System.out.println("诊断记录已保存，ID: " + savedDiagnosis.getId());
            
            // 4. 调用AI服务进行快速YOLO检测
            System.out.println("\n开始调用AI服务进行血管瘤YOLO检测..");
            Map<String, Object> yoloResult = callAiServiceOnlyYolo(targetFile, patientAge, gender, originType, vesselTexture, savedDiagnosis.getId());
            System.out.println("YOLO检测完成，返回结果: " + yoloResult);
            
            // 5. 使用YOLO返回的数据更新诊断记录
            if (yoloResult != null && !yoloResult.isEmpty()) {
                // 更新基本信息
                if (yoloResult.containsKey("detected_type")) {
                    savedDiagnosis.setDetectedType((String) yoloResult.get("detected_type"));
                }
                
                // 更新置信度字段
                if (yoloResult.containsKey("confidence")) {
                    savedDiagnosis.setConfidence(((Number) yoloResult.get("confidence")).doubleValue());
                }
                
                // 设置处理后的图像路径
                if (yoloResult.containsKey("processed_image_path")) {
                    savedDiagnosis.setProcessedImagePath((String) yoloResult.get("processed_image_path"));
                    System.out.println("设置处理后图像路径: " + yoloResult.get("processed_image_path"));
                }
                
                // 添加对颜色和部位的处理
                if (yoloResult.containsKey("predicted_color")) {
                    Map<String, Object> colorInfo = (Map<String, Object>) yoloResult.get("predicted_color");
                    if (colorInfo != null && colorInfo.containsKey("name")) {
                        String colorName = (String) colorInfo.get("name");
                        savedDiagnosis.setColor(colorName);
                        System.out.println("设置颜色: " + colorName);
                    }
                }
                
                if (yoloResult.containsKey("predicted_part")) {
                    Map<String, Object> partInfo = (Map<String, Object>) yoloResult.get("predicted_part");
                    if (partInfo != null && partInfo.containsKey("name")) {
                        String partName = (String) partInfo.get("name");
                        savedDiagnosis.setBodyPart(partName);
                        System.out.println("设置部位: " + partName);
                    }
                }
                
                // 保存更新后的诊断记录
                savedDiagnosis = hemangiomaDiagnosisRepository.save(savedDiagnosis);
                System.out.println("诊断记录更新成功，ID: " + savedDiagnosis.getId());
            }
            
            // 6. 异步处理大模型生成建议
            // 启动异步任务生成详细诊断建议
            generateRecommendationAsync(savedDiagnosis.getId(), targetFile, patientAge, gender, originType, vesselTexture);
            
            // 7. 添加延迟，确保数据库操作完成
            try {
                System.out.println("添加延迟，确保数据库操作完成...");
                Thread.sleep(1000); // 延迟1秒
                
                // 重新查询诊断记录，确保获取最新数据
                Optional<HemangiomaDiagnosis> refreshedDiagnosis = hemangiomaDiagnosisRepository.findById(savedDiagnosis.getId());
                if (refreshedDiagnosis.isPresent()) {
                    savedDiagnosis = refreshedDiagnosis.get();
                    System.out.println("已重新加载诊断记录，ID: " + savedDiagnosis.getId());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("延迟等待被中断: " + e.getMessage());
            }
            
            // 8. 立即返回YOLO检测结果
            return ResponseEntity.status(HttpStatus.CREATED).body(savedDiagnosis);
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "处理诊断请求失败: " + e.getMessage()));
        }
    }
    
    /**
     * 调用AI服务进行诊断
     */
    private Map<String, Object> callAiService(File imageFile, Integer diagnosisId, Integer patientAge, String gender, String originType, String vesselTexture) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new FileSystemResource(imageFile));
            
        // 添加诊断ID参数
        body.add("diagnosis_id", diagnosisId != null ? diagnosisId.toString() : "");
        
        // 添加所有元数据
        if (patientAge != null) body.add("patient_age", patientAge.toString());
        if (gender != null) body.add("gender", gender);
        if (originType != null) body.add("origin_type", originType);
        if (vesselTexture != null) body.add("vessel_texture", vesselTexture);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
        String url = aiServiceUrl + "/diagnose"; // 新的Python端点
        try {
            System.out.println("开始调用AI服务，URL: " + url + ", 诊断ID: " + diagnosisId);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
            System.out.println("AI服务返回状态码: " + response.getStatusCodeValue());
            return response.getBody();
        } catch (Exception e) {
            e.printStackTrace();
            // 返回一个包含错误信息的Map，以便上层可以处理
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "调用AI服务失败: " + e.getMessage());
            return errorMap;
        }
    }
    
    /**
     * 接收AI服务回调的诊断建议数据
     */
    @PostMapping("/update-recommendation")
    public ResponseEntity<?> updateRecommendation(@RequestBody Map<String, Object> payload) {
        try {
            // 从请求体中提取诊断ID和建议JSON
            Integer diagnosisId = (Integer) payload.get("diagnosisId");
            String recommendation = (String) payload.get("recommendation");
            
            System.out.println("接收到AI服务回调，更新诊断ID: " + diagnosisId);
            
            if (diagnosisId == null || recommendation == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "缺少必要参数：diagnosisId或recommendation"));
            }
            
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> optDiagnosis = hemangiomaDiagnosisRepository.findById(diagnosisId);
            if (!optDiagnosis.isPresent()) {
                System.out.println("未找到指定ID的诊断记录: " + diagnosisId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到指定ID的诊断记录"));
            }
            
            HemangiomaDiagnosis diagnosis = optDiagnosis.get();
            
            // 处理颜色信息
            if (payload.containsKey("predictedColor")) {
                Map<String, Object> colorInfo = (Map<String, Object>) payload.get("predictedColor");
                if (colorInfo != null && colorInfo.containsKey("name")) {
                    String colorName = (String) colorInfo.get("name");
                    diagnosis.setColor(colorName);
                    System.out.println("设置颜色: " + colorName);
                }
            }
            
            // 处理部位信息
            if (payload.containsKey("predictedPart")) {
                Map<String, Object> partInfo = (Map<String, Object>) payload.get("predictedPart");
                if (partInfo != null && partInfo.containsKey("name")) {
                    String partName = (String) partInfo.get("name");
                    diagnosis.setBodyPart(partName);
                    System.out.println("设置部位: " + partName);
                }
            }
            
            // 解析JSON字符串中的建议
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, String> recMap = mapper.readValue(recommendation, Map.class);
                System.out.println("成功解析建议JSON: " + recMap);
                
                // 更新诊断记录中的建议字段
                if (recMap.containsKey("treatment_suggestion")) {
                    diagnosis.setTreatmentSuggestion(recMap.get("treatment_suggestion"));
                }
                if (recMap.containsKey("precautions")) {
                    diagnosis.setPrecautions(recMap.get("precautions"));
                }
                if (recMap.containsKey("disclaimer")) {
                    diagnosis.setDisclaimer(recMap.get("disclaimer"));
                }
                
                // 保存更新后的诊断记录
                HemangiomaDiagnosis updatedDiagnosis = hemangiomaDiagnosisRepository.save(diagnosis);
                System.out.println("成功更新诊断记录的建议，ID: " + diagnosisId);
                return ResponseEntity.ok(Map.of(
                    "success", true, 
                    "message", "成功更新诊断建议", 
                    "id", updatedDiagnosis.getId()
                ));
            } catch (Exception e) {
                System.out.println("解析建议JSON失败: " + e.getMessage());
                e.printStackTrace();
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "解析建议JSON失败: " + e.getMessage()));
            }
        } catch (Exception e) {
            System.out.println("更新诊断建议时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "更新诊断建议失败: " + e.getMessage()));
        }
    }

    /**
     * 调用AI服务仅进行YOLO检测，不等待大模型处理
     */
    private Map<String, Object> callAiServiceOnlyYolo(File imageFile, Integer patientAge, String gender, String originType, String vesselTexture, Integer diagnosisId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new FileSystemResource(imageFile));
        body.add("only_yolo", "true"); // 添加参数告知AI服务只进行YOLO检测
        body.add("diagnosis_id", diagnosisId);
        
        // 添加所有元数据
        if (patientAge != null) body.add("patient_age", patientAge.toString());
        if (gender != null) body.add("gender", gender);
        if (originType != null) body.add("origin_type", originType);
        if (vesselTexture != null) body.add("vessel_texture", vesselTexture);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        String url = aiServiceUrl + "/diagnose-yolo"; // 新的仅YOLO检测端点
        try {
            System.out.println("开始调用YOLO检测服务，URL: " + url);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
            Map<String, Object> result = response.getBody();
            
            System.out.println("YOLO检测服务返回状态码: " + response.getStatusCodeValue());
            System.out.println("YOLO检测结果: " + (result != null ? result.keySet() : "null"));
            
            // 处理标注框数据
            if (result != null) {
                if (result.containsKey("detection_boxes")) {
                    List<Map<String, Object>> boxes = (List<Map<String, Object>>) result.get("detection_boxes");
                    System.out.println("YOLO检测返回了标注框数据，数量: " + boxes.size());
                    System.out.println("标注框数据示例: " + (!boxes.isEmpty() ? boxes.get(0) : "无"));
                    
                    // 保存标注框数据
                    saveDetectionBoxes(diagnosisId, result);
                } else if (result.containsKey("tags")) {
                    // 新的数据结构，使用tags字段
                    System.out.println("发现新的数据结构: 使用tags字段作为标注数据");
                    List<Map<String, Object>> tags = (List<Map<String, Object>>) result.get("tags");
                    System.out.println("YOLO检测返回了标签数据，数量: " + (tags != null ? tags.size() : 0));
                    System.out.println("标签数据示例: " + (tags != null && !tags.isEmpty() ? tags.get(0) : "无"));
                    
                    // 创建包含标签数据的新Map
                    Map<String, Object> tagsResult = new HashMap<>(result);
                    // 将tags字段映射为detection_boxes字段，保持向后兼容
                    tagsResult.put("detection_boxes", result.get("tags"));
                    
                    // 保存标注框数据
                    saveDetectionBoxes(diagnosisId, tagsResult);
                } else {
                    System.out.println("YOLO检测结果中不包含detection_boxes或tags字段");
                }
                
                if (result.containsKey("error")) {
                    System.err.println("YOLO检测服务返回错误: " + result.get("error"));
                }
            } else {
                System.out.println("YOLO检测服务返回空结果");
            }
            
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            // 返回一个包含错误信息的Map，以便上层可以处理
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "调用YOLO检测服务失败: " + e.getMessage());
            return errorMap;
        }
    }
    
    /**
     * 异步执行大模型生成建议
     */
    private void generateRecommendationAsync(final Integer diagnosisId, final File imageFile, 
                                           final Integer patientAge, final String gender, 
                                           final String originType, final String vesselTexture) {
        // 使用线程池异步执行
        CompletableFuture.runAsync(() -> {
            try {
                System.out.println("开始异步生成诊断建议，诊断ID: " + diagnosisId);
                
                // 调用完整的AI服务（包括大模型），传递诊断ID
                Map<String, Object> aiResult = callAiService(imageFile, diagnosisId, patientAge, gender, originType, 
                                                         vesselTexture);
                
                if (aiResult != null && !aiResult.containsKey("error")) {
                    // 查找诊断记录
                    Optional<HemangiomaDiagnosis> optDiagnosis = hemangiomaDiagnosisRepository.findById(diagnosisId);
                    if (optDiagnosis.isPresent()) {
                        HemangiomaDiagnosis diagnosis = optDiagnosis.get();
                        
                        // 更新LLM生成的建议
                        if (aiResult.containsKey("diagnostic_summary")) {
                            diagnosis.setDiagnosticSummary((String) aiResult.get("diagnostic_summary"));
                        }
                        if (aiResult.containsKey("treatment_suggestion")) {
                            diagnosis.setTreatmentSuggestion((String) aiResult.get("treatment_suggestion"));
                        }
                        if (aiResult.containsKey("precautions")) {
                            diagnosis.setPrecautions((String) aiResult.get("precautions"));
                        }
                        if (aiResult.containsKey("disclaimer")) {
                            diagnosis.setDisclaimer((String) aiResult.get("disclaimer"));
                        }
                        
                        // 保存更新后的诊断记录
                        hemangiomaDiagnosisRepository.save(diagnosis);
                        System.out.println("异步生成诊断建议完成，已更新诊断记录 ID: " + diagnosisId);
                    } else {
                        System.err.println("无法找到诊断记录 ID: " + diagnosisId);
                    }
                } else {
                    System.err.println("异步生成诊断建议失败: " + (aiResult != null ? aiResult.get("error") : "未知错误"));
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("异步生成诊断建议时发生错误: " + e.getMessage());
            }
        });
    }
    
    /**
     * 保存YOLO检测返回的标注框数据
     */
    private void saveDetectionBoxes(Integer diagnosisId, Map<String, Object> yoloResult) {
        try {
            System.out.println("开始保存标注框数据，诊断ID: " + diagnosisId);
            
            // 查找诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(diagnosisId);
            if (!diagnosisOpt.isPresent()) {
                System.err.println("找不到诊断记录ID: " + diagnosisId + "，无法保存标注框");
                return;
            }
            
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            System.out.println("找到诊断记录: " + diagnosis.getId());
            
            // 提取检测框数据
            Object detectionBoxesObj = yoloResult.get("detection_boxes");
            System.out.println("检测框数据类型: " + (detectionBoxesObj != null ? detectionBoxesObj.getClass().getName() : "null"));
            System.out.println("YOLO结果键集: " + yoloResult.keySet());
            
            // 如果detection_boxes为null，尝试从tags字段获取
            if (detectionBoxesObj == null && yoloResult.containsKey("tags")) {
                detectionBoxesObj = yoloResult.get("tags");
                System.out.println("从tags字段获取标注数据，类型: " + (detectionBoxesObj != null ? detectionBoxesObj.getClass().getName() : "null"));
            }
            
            // 处理不同格式的数据
            List<Map<String, Object>> detectionBoxes;
            if (detectionBoxesObj instanceof List) {
                try {
                    detectionBoxes = (List<Map<String, Object>>) detectionBoxesObj;
                } catch (ClassCastException e) {
                    System.err.println("无法转换检测框数据为List<Map>: " + e.getMessage());
                    
                    // 尝试转换每个元素
                    List<?> rawList = (List<?>) detectionBoxesObj;
                    detectionBoxes = new ArrayList<>();
                    for (Object item : rawList) {
                        if (item instanceof Map) {
                            detectionBoxes.add((Map<String, Object>) item);
                        } else {
                            System.err.println("检测框数据项类型不是Map: " + (item != null ? item.getClass().getName() : "null"));
                        }
                    }
                }
            } else {
                System.err.println("检测框数据不是List类型");
                detectionBoxes = new ArrayList<>();
            }
            
            if (detectionBoxes == null || detectionBoxes.isEmpty()) {
                System.out.println("没有检测到标注框数据");
                return;
            }
            
            System.out.println("检测到 " + detectionBoxes.size() + " 个标注框，准备保存");
            
            // 先删除现有的标签，避免重复
            List<Tag> existingTags = tagRepository.findByAnyDiagnosisId(diagnosisId);
            if (!existingTags.isEmpty()) {
                System.out.println("删除 " + existingTags.size() + " 个现有标签");
                tagRepository.deleteAll(existingTags);
            }
            
            // 创建标签对象并保存
            List<Tag> tags = new ArrayList<>();
            for (Map<String, Object> box : detectionBoxes) {
                System.out.println("处理标注框: " + box);
                
                Tag tag = new Tag();
                tag.setHemangiomaDiagnosis(diagnosis);
                tag.setHemangioma_id(diagnosis.getId().longValue()); // 同时设置hemangioma_id字段
                
                // 设置标签类型
                if (box.containsKey("class")) {
                    String className = String.valueOf(box.get("class"));
                    tag.setTagName(className);
                    System.out.println("标签类型: " + className);
                } else if (box.containsKey("class_name")) {
                    // 新数据结构使用class_name字段
                    String className = String.valueOf(box.get("class_name"));
                    tag.setTagName(className);
                    System.out.println("标签类型(新格式): " + className);
                } else {
                    tag.setTagName("未知类型");
                    System.out.println("标签类型: 未知类型 (未找到class或class_name字段)");
                }
                
                // 设置坐标和尺寸(确保是相对坐标，范围0-1)
                try {
                    // 检查是否有直接的相对坐标
                    if (box.containsKey("x") && box.containsKey("y") && box.containsKey("width") && box.containsKey("height")) {
                        // 旧格式，直接使用相对坐标
                        double x = parseNumberValue(box.get("x"));
                        double y = parseNumberValue(box.get("y"));
                        double width = parseNumberValue(box.get("width"));
                        double height = parseNumberValue(box.get("height"));
                        
                        tag.setX(roundToSixDecimals(x));
                        tag.setY(roundToSixDecimals(y));
                        tag.setWidth(roundToSixDecimals(width));
                        tag.setHeight(roundToSixDecimals(height));
                        
                        System.out.println("使用相对坐标: x=" + x + ", y=" + y + ", width=" + width + ", height=" + height);
                    } else if (box.containsKey("x_center") && box.containsKey("y_center") && 
                               box.containsKey("width") && box.containsKey("height")) {
                        // 新格式，使用中心点相对坐标
                        double centerX = parseNumberValue(box.get("x_center"));
                        double centerY = parseNumberValue(box.get("y_center"));
                        double width = parseNumberValue(box.get("width"));
                        double height = parseNumberValue(box.get("height"));
                        
                        // 设置相对坐标
                        tag.setX(roundToSixDecimals(centerX));
                        tag.setY(roundToSixDecimals(centerY));
                        tag.setWidth(roundToSixDecimals(width));
                        tag.setHeight(roundToSixDecimals(height));
                        
                        System.out.println("使用中心点相对坐标: centerX=" + centerX + ", centerY=" + centerY + 
                                          ", width=" + width + ", height=" + height);
                    } else if (box.containsKey("bbox")) {
                        // 使用绝对坐标bbox [x1, y1, x2, y2]
                        Object bboxObj = box.get("bbox");
                        
                        if (bboxObj instanceof List) {
                            List<?> bboxList = (List<?>) bboxObj;
                            if (bboxList.size() >= 4) {
                                int x1 = ((Number) bboxList.get(0)).intValue();
                                int y1 = ((Number) bboxList.get(1)).intValue();
                                int x2 = ((Number) bboxList.get(2)).intValue();
                                int y2 = ((Number) bboxList.get(3)).intValue();
                                
                                // 获取原始图像尺寸
                                int origWidth = 0;
                                int origHeight = 0;
                                
                                if (box.containsKey("orig_width") && box.containsKey("orig_height")) {
                                    origWidth = ((Number) box.get("orig_width")).intValue();
                                    origHeight = ((Number) box.get("orig_height")).intValue();
                                } else {
                                    // 如果没有原始尺寸，尝试从结果中获取
                                    Map<String, Integer> dimensions = (Map<String, Integer>) yoloResult.get("image_dimensions");
                                    if (dimensions != null) {
                                        origWidth = dimensions.get("width");
                                        origHeight = dimensions.get("height");
                                    }
                                }
                                
                                if (origWidth <= 0 || origHeight <= 0) {
                                    System.err.println("无法获取原始图像尺寸，无法计算相对坐标");
                                    continue;
                                }
                                
                                // 计算中心点坐标
                                double centerX = (x1 + x2) / 2.0 / origWidth;
                                double centerY = (y1 + y2) / 2.0 / origHeight;
                                
                                // 计算宽高
                                double width = (x2 - x1) * 1.0 / origWidth;
                                double height = (y2 - y1) * 1.0 / origHeight;
                                
                                // 设置相对坐标
                                tag.setX(roundToSixDecimals(centerX));
                                tag.setY(roundToSixDecimals(centerY));
                                tag.setWidth(roundToSixDecimals(width));
                                tag.setHeight(roundToSixDecimals(height));
                                
                                System.out.println("从bbox转换为相对坐标: centerX=" + centerX + ", centerY=" + centerY + 
                                                  ", width=" + width + ", height=" + height);
                            }
                        }
                    }
                    
                    // 设置置信度
                    if (box.containsKey("confidence")) {
                        Object confValue = box.get("confidence");
                        double confidence = parseNumberValue(confValue);
                        tag.setConfidence(roundToSixDecimals(confidence));
                        System.out.println("置信度: " + confidence + " (" + confValue.getClass().getName() + ")");
                    }
                } catch (Exception e) {
                    System.err.println("处理标注框坐标时出错: " + e.getMessage());
                    e.printStackTrace();
                }
                
                // 设置创建者ID为系统用户(1)
                tag.setCreatedBy(1L);
                
                // 验证标签数据完整性
                if (tag.getX() != null && tag.getY() != null && tag.getWidth() != null && tag.getHeight() != null) {
                    // 确保hemangioma_id字段已设置
                    if (tag.getHemangioma_id() == null && diagnosis != null) {
                        tag.setHemangioma_id(diagnosis.getId().longValue());
                    }
                    
                    // 确保tag字段已设置
                    if (tag.getTag() == null && tag.getTagName() != null) {
                        tag.setTag(tag.getTagName());
                    } else if (tag.getTagName() == null && tag.getTag() != null) {
                        tag.setTagName(tag.getTag());
                    }
                    
                    tags.add(tag);
                    System.out.println("添加有效标签: " + tag.getTagName() + " 位置: (" + tag.getX() + "," + tag.getY() + "," + tag.getWidth() + "," + tag.getHeight() + ")");
                } else {
                    System.err.println("标签数据不完整，跳过保存: X=" + tag.getX() + ", Y=" + tag.getY() + 
                                     ", Width=" + tag.getWidth() + ", Height=" + tag.getHeight());
                }
            }
            
            // 保存所有标签
            if (!tags.isEmpty()) {
                List<Tag> savedTags = tagRepository.saveAll(tags);
                System.out.println("成功保存 " + savedTags.size() + " 个标签，ID列表: " + 
                                  savedTags.stream().map(t -> t.getId().toString()).reduce("", (a, b) -> a + "," + b));
                
                // 检查是否能从数据库中查询到保存的标签
                List<Tag> verifyTags = tagRepository.findByAnyDiagnosisId(diagnosisId);
                System.out.println("验证查询: 数据库中找到 " + verifyTags.size() + " 个标签");
            } else {
                System.err.println("没有有效的标签数据可保存");
            }
            
        } catch (Exception e) {
            System.err.println("保存标注框数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析各种类型的数值
     */
    private double parseNumberValue(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            return Double.parseDouble((String) value);
        } else if (value instanceof Map) {
            // 处理JSON结构可能的数值表示
            Map<?, ?> map = (Map<?, ?>) value;
            if (map.containsKey("value")) {
                return parseNumberValue(map.get("value"));
            }
        }
        throw new IllegalArgumentException("无法解析数值: " + value);
    }
    
    private double roundToSixDecimals(double value) {
        return Math.round(value * 1_000_000.0) / 1_000_000.0;
    }
} 