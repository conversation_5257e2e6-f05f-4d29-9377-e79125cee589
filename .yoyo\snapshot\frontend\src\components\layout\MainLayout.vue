<template>
  <el-container class="layout-container">
    <el-aside width="200px" class="sidebar">
      <div class="logo">医生标注平台</div>
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        background-color="#001529"
        text-color="#fff"
        active-text-color="#409EFF"
      >
        <!-- 工作台 - 所有角色可见 -->
        <el-menu-item index="/app/dashboard" @click="$router.push('/app/dashboard')">
          <el-icon><HomeFilled /></el-icon>
          <span>工作台</span>
        </el-menu-item>
        
        <!-- 病例标注 - 所有角色可见 -->
        <el-menu-item 
          index="/app/cases" 
          @click="$router.push('/app/cases')"
        >
          <el-icon><Document /></el-icon>
          <span>病例标注</span>
        </el-menu-item>
        
        <!-- 标注审核 - 管理员和审核医生可见 -->
        <el-menu-item 
          v-if="isAdmin || isReviewer"
          index="/app/annotation-reviews" 
          @click="$router.push('/app/annotation-reviews')"
        >
          <el-icon><Check /></el-icon>
          <span>标注审核</span>
        </el-menu-item>
        
        <!-- 团队 - 所有角色可见 -->
        <el-menu-item 
          index="/app/teams" 
          @click="$router.push('/app/teams')"
        >
          <el-icon><UserFilled /></el-icon>
          <span>我的团队</span>
        </el-menu-item>
        
        <!-- 部门成员 - 只有管理员可见 -->
        <el-menu-item 
          v-if="isAdmin"
          index="/app/users" 
          @click="$router.push('/app/users')"
        >
          <el-icon><User /></el-icon>
          <span>人员管理</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    
    <el-container>
      <el-header height="60px" class="header">
        <div class="header-left">
          <el-button 
            v-if="showDashboardButton" 
            type="primary" 
            icon="el-icon-s-home" 
            @click="$router.push('/app/dashboard')">返回工作台</el-button>
          <el-button 
            v-if="showBackButton" 
            type="info" 
            icon="el-icon-back" 
            @click="goBack">返回上一步</el-button>
        </div>
        <div class="header-right">
          <el-dropdown>
            <span class="user-info">
              <el-avatar size="small" icon="el-icon-user"></el-avatar>
              {{ username }}
              <span :class="['role-tag', roleTagClass]">{{ userRoleText }}</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="isDoctor && !hasAppliedForReviewer" @click="handleApplyForReviewer">
                  申请升级权限
                </el-dropdown-item>
                <el-dropdown-item v-if="isDoctor && hasAppliedForReviewer" disabled>
                  权限申请审核中
                </el-dropdown-item>
                <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import { mapGetters } from 'vuex'
import { 
  HomeFilled, 
  Document, 
  Check, 
  User,
  UserFilled
} from '@element-plus/icons-vue'
import api from '@/utils/api'

export default {
  name: 'MainLayout',
  components: {
    HomeFilled,
    Document,
    Check,
    User,
    UserFilled
  },
  data() {
    return {
      username: '标注医生',
      showBackButton: false,
      showDashboardButton: false,
      hasAppliedForReviewer: false
    }
  },
  computed: {
    ...mapGetters({
      currentUser: 'getUser',
      isAdmin: 'isAdmin',
      isDoctor: 'isDoctor',
      isReviewer: 'isReviewer'
    }),
    // 当前激活的菜单项
    activeMenu() {
      return this.$route.path
    },
    // 用户角色文本
    userRoleText() {
      // 首先尝试使用Vuex中的角色标志
      if (this.isAdmin) return '管理员'
      if (this.isDoctor) return '标注医生'
      if (this.isReviewer) return '审核医生'
      
      // 如果Vuex中的角色判断失效，直接从localStorage获取
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (user && user.role) {
          // 根据用户角色返回对应文本
          switch(user.role) {
            case 'ADMIN': return '管理员';
            case 'DOCTOR': return '标注医生';
            case 'REVIEWER': return '审核医生';
            default: return '未知角色';
          }
        }
      } catch (err) {
        console.error('MainLayout: 获取用户角色失败:', err);
      }
      
      return '未知角色'
    },
    // 根据角色返回不同的CSS类
    roleTagClass() {
      if (this.isAdmin) return 'role-tag-admin'
      if (this.isReviewer) return 'role-tag-reviewer'
      if (this.isDoctor) return 'role-tag-doctor'
      
      // 如果Vuex中的角色判断失效，直接从localStorage获取
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (user && user.role) {
          switch(user.role) {
            case 'ADMIN': return 'role-tag-admin';
            case 'REVIEWER': return 'role-tag-reviewer';
            case 'DOCTOR': return 'role-tag-doctor';
            default: return '';
          }
        }
      } catch (err) {
        console.error('MainLayout: 获取用户角色样式失败:', err);
      }
      
      return ''
    }
  },
  watch: {
    $route(to) {
      // Only show back button on specific routes
      this.showBackButton = to.path === '/app/cases/structured-form';
      
      // Show dashboard button on all pages except dashboard
      this.showDashboardButton = to.path !== '/app/dashboard';
      
      // 如果是进入Dashboard，检查用户是否变更
      if (to.path === '/app/dashboard' || to.path === '/app' || to.path === '/app/') {
        // 获取当前用户ID
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const currentUserId = user.customId || user.id;
        
        // 如果存在lastUserId且与当前不同，说明用户已切换
        const lastUserId = localStorage.getItem('lastActiveUserId');
        if (lastUserId && lastUserId !== currentUserId) {
          console.log('MainLayout: 检测到用户ID变更，从', lastUserId, '到', currentUserId);
          
          // 强制刷新统计数据
          if (window.refreshDashboardStats) {
            console.log('MainLayout: 用户变更，强制刷新统计数据');
            setTimeout(() => window.refreshDashboardStats(), 0);
          }
        }
        
        // 更新最后活跃用户ID
        if (currentUserId) {
          localStorage.setItem('lastActiveUserId', currentUserId);
        }
      }
    },
    
    // 监听用户名变化
    username(newVal, oldVal) {
      if (newVal !== oldVal && newVal && oldVal) {
        console.log('MainLayout: 用户名变更，从', oldVal, '到', newVal);
        
        // 如果当前路径是Dashboard，强制刷新统计数据
        if (this.$route.path === '/app/dashboard' || this.$route.path === '/app' || this.$route.path === '/app/') {
          if (window.refreshDashboardStats) {
            console.log('MainLayout: 用户名变更，立即刷新统计数据');
            setTimeout(() => window.refreshDashboardStats(), 0);
          }
        }
      }
    }
  },
  created() {
    // 从localStorage获取用户信息
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    console.log('MainLayout: 从localStorage获取的用户信息:', user);
    
    // 特殊账号处理
    if (user && user.customId) {
      if (user.customId === '200000001' && (user.role !== 'DOCTOR' || user.name !== '张医生')) {
        // 修复张医生账号
        console.log('MainLayout: 检测到张医生账号，进行修复');
        user.role = 'DOCTOR';
        user.name = '张医生';
        
        // 确保张医生没有团队信息
        if (user.team) {
          console.log('MainLayout: 清除张医生的团队信息');
          delete user.team;
        }
        
        localStorage.setItem('user', JSON.stringify(user));
        this.$store.commit('setUser', user);
      }
      
      if (user.customId === '300000001' && (user.role !== 'REVIEWER' || user.name !== '李审核')) {
        // 修复李审核账号
        console.log('MainLayout: 检测到李审核账号，进行修复');
        user.role = 'REVIEWER';
        user.name = '李审核';
        
        localStorage.setItem('user', JSON.stringify(user));
        this.$store.commit('setUser', user);
      }
    }
    
    // 设置用户名，优先显示用户的真实姓名
    if (user && user.name) {
      this.username = user.name;
      console.log('MainLayout: 设置用户名为:', this.username);
    } else {
      this.username = '未登录用户';
      console.log('MainLayout: 用户未登录或无姓名信息');
    }
    
    // 从Vuex store获取当前用户信息
    if (this.currentUser && this.currentUser.name) {
      this.username = this.currentUser.name;
      console.log('MainLayout: 从Vuex获取用户名:', this.username);
    }
    
    // 调试输出当前角色状态
    console.log('MainLayout: 当前角色状态 - 管理员:', this.isAdmin, '审核医生:', this.isReviewer, '标注医生:', this.isDoctor);
    
    // 手动检查用户角色
    if (user && user.role) {
      // 如果Vuex中的角色判断不正确，但localStorage中有正确角色，手动更新store
      if ((user.role === 'ADMIN' && !this.isAdmin) || 
          (user.role === 'REVIEWER' && !this.isReviewer) || 
          (user.role === 'DOCTOR' && !this.isDoctor)) {
        console.log('MainLayout: 角色不匹配，尝试修复用户权限');
        if (window.fixUserPermission) {
          const result = window.fixUserPermission();
          console.log('MainLayout: 权限修复结果:', result);
        }
      }
    }
    
    // 监听用户信息变化
    this.$store.watch(
      state => state.auth.user,
      (newUser) => {
        if (newUser && newUser.name) {
          this.username = newUser.name;
          console.log('MainLayout: 用户信息更新, 新用户名:', this.username);
        }
      }
    );
    
    // Initialize back button visibility based on current route
    this.showBackButton = this.$route.path === '/app/cases/structured-form';
    
    // Initialize dashboard button visibility
    this.showDashboardButton = this.$route.path !== '/app/dashboard';
    
    // 检查是否已申请升级权限
    this.checkReviewerApplication();
  },
  methods: {
    handleLogout() {
      console.log('[登出操作] 用户请求登出，当前URL:', window.location.href);
      
      // 清除所有导航状态标记
      sessionStorage.removeItem('isAppOperation');
      sessionStorage.removeItem('isNavigatingAfterSave');
      sessionStorage.removeItem('returningToWorkbench');
      
      // 设置明确的注销标记，确保API拦截器可以识别这是登出操作
      sessionStorage.setItem('isLogoutOperation', 'true');
      console.log('[登出操作] 已设置登出标记，准备清除用户会话');
      
      // 清除用户会话
      localStorage.removeItem('user');
      
      // 重定向到登录页
      console.log('[登出操作] 导航到登录页');
      this.$router.push('/login').then(() => {
        console.log('[登出操作] 导航到登录页成功');
      }).catch(err => {
        console.error('[登出操作] 导航失败:', err);
        // 备用导航方案
        window.location.href = '/login';
      });
      
      // 3秒后清除登出标记
      setTimeout(() => {
        console.log('[登出操作] 清除登出标记');
        sessionStorage.removeItem('isLogoutOperation');
      }, 3000);
    },
    goBack() {
      // For all pages, ask if user wants to go back because they might lose data
      this.$confirm('返回上一页将可能丢失当前未保存的数据，是否确认返回？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // Use browser history to go back, which is more reliable
        this.$router.go(-1);
      }).catch(() => {
        // User cancelled, do nothing
      });
    },
    // 检查是否已申请升级权限
    checkReviewerApplication() {
      // 只有标注医生需要检查
      if (!this.isDoctor) {
        return;
      }
      
      // 从API获取当前用户的申请状态
      api.users.getReviewerApplicationStatus()
        .then(response => {
          console.log('获取权限申请状态:', response.data);
          this.hasAppliedForReviewer = response.data && response.data.length > 0 && 
            response.data.some(app => app.status === 'PENDING');
        })
        .catch(error => {
          console.error('获取权限申请状态失败:', error);
        });
    },
    
    // 处理申请升级权限
    handleApplyForReviewer() {
      this.$prompt('请输入申请理由', '申请成为审核医生', {
        confirmButtonText: '提交申请',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请详细说明您申请成为审核医生的理由...'
      }).then(({ value }) => {
        if (!value || value.trim() === '') {
          this.$message.warning('申请理由不能为空');
          return;
        }
        
        // 提交申请
        api.users.applyForReviewer(value)
          .then(() => {
            this.$message.success('申请已提交，请等待管理员审核');
            this.hasAppliedForReviewer = true;
          })
          .catch(error => {
            if (error.response && error.response.data && error.response.data.message) {
              this.$message.error(error.response.data.message);
            } else {
              this.$message.error('申请提交失败，请稍后重试');
            }
          });
      }).catch(() => {
        // 用户取消输入，不做处理
      });
    }
  }
}
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
}

.sidebar {
  background-color: #001529;
  color: white;
}

.logo {
  height: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
  background-color: #002140;
}

.sidebar-menu {
  border-right: none;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left .el-button--primary {
  background-color: #1890ff;
  border-color: #1890ff;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-info .el-avatar {
  margin-right: 8px;
}

.role-tag {
  font-size: 12px;
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
}

.role-tag-admin {
  background-color: #f56c6c;
  color: white;
  font-weight: bold;
}

.role-tag-reviewer {
  background-color: #e6a23c;
  color: white;
  font-weight: bold;
}

.role-tag-doctor {
  background-color: #67c23a;
  color: white;
  font-weight: bold;
}
</style> 