"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[97],{113:(t,e,n)=>{var i=n(6518),a=n(9213).find,r=n(6469),o="find",s=!0;o in[]&&Array(1)[o]((function(){s=!1})),i({target:"Array",proto:!0,forced:s},{find:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),r(o)},1240:(t,e,n)=>{var i=n(9504);t.exports=i(1..valueOf)},1278:(t,e,n)=>{var i=n(6518),a=n(3724),r=n(5031),o=n(5397),s=n(7347),c=n(4659);i({target:"Object",stat:!0,sham:!a},{getOwnPropertyDescriptors:function(t){var e,n,i=o(t),a=s.f,l=r(i),d={},h=0;while(l.length>h)n=a(i,e=l[h++]),void 0!==n&&c(d,e,n);return d}})},2097:(t,e,n)=>{n.r(e),n.d(e,{default:()=>$});var i=n(641),a=n(33),r=n(3751),o={class:"annotation-container"},s={class:"main-content"},c={class:"toolbar"},l={class:"tool-section"},d={class:"tool-section"},h={key:0,class:"annotations-list"},g={class:"annotation-area"},u={class:"image-wrapper"},f={class:"image-container",ref:"imageContainerInner"},m=["src"],p=["onMousedown"],v=["onMousedown"],x=["onMousedown"],w=["onMousedown"],I=["onMousedown"],y={class:"navigation-controls"};function b(t,e,n,b,D,B){var k=(0,i.g2)("el-option"),A=(0,i.g2)("el-select"),M=(0,i.g2)("el-radio"),C=(0,i.g2)("el-radio-group"),$=(0,i.g2)("el-table-column"),S=(0,i.g2)("el-button"),E=(0,i.g2)("el-table");return(0,i.uX)(),(0,i.CE)("div",o,[e[14]||(e[14]=(0,i.Lk)("div",{class:"page-header"},[(0,i.Lk)("h2",null,"图像标注")],-1)),(0,i.Lk)("div",s,[(0,i.Lk)("div",c,[(0,i.Lk)("div",l,[e[6]||(e[6]=(0,i.Lk)("h3",null,"标签分类",-1)),(0,i.bF)(A,{modelValue:D.selectedTag,"onUpdate:modelValue":e[0]||(e[0]=function(t){return D.selectedTag=t}),placeholder:"请选择标签分类",style:{width:"100%"}},{default:(0,i.k6)((function(){return[(0,i.bF)(k,{label:"血管瘤",value:"血管瘤"}),(0,i.bF)(k,{label:"淋巴管瘤",value:"淋巴管瘤"}),(0,i.bF)(k,{label:"混合型",value:"混合型"}),(0,i.bF)(k,{label:"其他",value:"其他"})]})),_:1},8,["modelValue"])]),(0,i.Lk)("div",d,[e[8]||(e[8]=(0,i.Lk)("h3",null,"标注工具",-1)),(0,i.bF)(C,{modelValue:D.currentTool,"onUpdate:modelValue":e[1]||(e[1]=function(t){return D.currentTool=t}),style:{display:"block","margin-top":"10px"}},{default:(0,i.k6)((function(){return[(0,i.bF)(M,{label:"rectangle"},{default:(0,i.k6)((function(){return e[7]||(e[7]=[(0,i.eW)("矩形框")])})),_:1,__:[7]})]})),_:1},8,["modelValue"])]),D.annotations.length>0?((0,i.uX)(),(0,i.CE)("div",h,[e[10]||(e[10]=(0,i.Lk)("h3",null,"已添加标注",-1)),(0,i.bF)(E,{data:B.filteredAnnotations,style:{width:"100%"}},{default:(0,i.k6)((function(){return[(0,i.bF)($,{label:"编号",width:"60"},{default:(0,i.k6)((function(t){return[(0,i.eW)((0,a.v_)(t.$index+1),1)]})),_:1}),(0,i.bF)($,{prop:"tag",label:"标签",width:"90"}),(0,i.bF)($,{label:"操作",width:"80"},{default:(0,i.k6)((function(t){return[(0,i.bF)(S,{type:"danger",size:"small",onClick:function(e){return B.deleteAnnotation(t.row.id)}},{default:(0,i.k6)((function(){return e[9]||(e[9]=[(0,i.eW)("删除")])})),_:2,__:[9]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])])):(0,i.Q3)("",!0)]),(0,i.Lk)("div",g,[(0,i.Lk)("div",u,[(0,i.Lk)("div",f,[D.currentImage?((0,i.uX)(),(0,i.CE)("img",{key:0,src:D.currentImage.url,ref:"annotationImage",class:"annotation-image",onLoad:e[2]||(e[2]=function(){return B.onImageLoad&&B.onImageLoad.apply(B,arguments)})},null,40,m)):(0,i.Q3)("",!0),D.currentImage?((0,i.uX)(),(0,i.CE)("div",{key:1,class:"annotation-overlay",style:(0,a.Tr)({width:D.imageWidth+"px",height:D.imageHeight+"px"}),onMousedown:e[3]||(e[3]=function(){return B.startDrawing&&B.startDrawing.apply(B,arguments)}),onMousemove:e[4]||(e[4]=function(){return B.drawing&&B.drawing.apply(B,arguments)}),onMouseup:e[5]||(e[5]=function(){return B.endDrawing&&B.endDrawing.apply(B,arguments)})},null,36)):(0,i.Q3)("",!0),((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(B.filteredAnnotations,(function(t,e){return(0,i.uX)(),(0,i.CE)("div",{key:e,class:"annotation-box",style:(0,a.Tr)({left:t.x+"px",top:t.y+"px",width:t.width+"px",height:t.height+"px",borderColor:B.getTagColor(t.tag),cursor:D.isEditingBox&&D.editingBoxId===t.id?"move":"default"}),onMousedown:(0,r.D$)((function(e){return B.startEditBox(e,t.id)}),["stop"])},[(0,i.Lk)("span",{class:"annotation-label",style:(0,a.Tr)({backgroundColor:B.getTagColor(t.tag)})},(0,a.v_)(t.tag),5),(0,i.Lk)("div",{class:"resize-handle top-left",onMousedown:(0,r.D$)((function(e){return B.startResizeBox(e,t.id,"top-left")}),["stop"])},null,40,v),(0,i.Lk)("div",{class:"resize-handle top-right",onMousedown:(0,r.D$)((function(e){return B.startResizeBox(e,t.id,"top-right")}),["stop"])},null,40,x),(0,i.Lk)("div",{class:"resize-handle bottom-left",onMousedown:(0,r.D$)((function(e){return B.startResizeBox(e,t.id,"bottom-left")}),["stop"])},null,40,w),(0,i.Lk)("div",{class:"resize-handle bottom-right",onMousedown:(0,r.D$)((function(e){return B.startResizeBox(e,t.id,"bottom-right")}),["stop"])},null,40,I)],44,p)})),128)),D.isDrawing?((0,i.uX)(),(0,i.CE)("div",{key:2,class:"drawing-box",style:(0,a.Tr)({left:Math.min(D.drawStart.x,D.drawCurrent.x)+"px",top:Math.min(D.drawStart.y,D.drawCurrent.y)+"px",width:Math.abs(D.drawCurrent.x-D.drawStart.x)+"px",height:Math.abs(D.drawCurrent.y-D.drawStart.y)+"px"})},null,4)):(0,i.Q3)("",!0)],512)]),(0,i.Lk)("div",y,[(0,i.Lk)("div",null,[(0,i.bF)(S,{type:"info",onClick:B.goBack},{default:(0,i.k6)((function(){return e[11]||(e[11]=[(0,i.eW)("返回上传图片")])})),_:1,__:[11]},8,["onClick"])]),(0,i.Lk)("div",null,[(0,i.bF)(S,{type:"primary",onClick:B.saveAndNext,style:{"margin-right":"10px"}},{default:(0,i.k6)((function(){return e[12]||(e[12]=[(0,i.eW)("保存并填写表单")])})),_:1,__:[12]},8,["onClick"]),(0,i.bF)(S,{type:"success",onClick:B.saveAndExit},{default:(0,i.k6)((function(){return e[13]||(e[13]=[(0,i.eW)("保存退出")])})),_:1,__:[13]},8,["onClick"])])])])])])}var D=n(4119),B=n(9201),k=(n(8706),n(2008),n(113),n(8980),n(1629),n(4346),n(4114),n(4554),n(9089),n(739),n(8111),n(2489),n(7735),n(7588),n(3110),n(9432),n(6099),n(3500),n(2505),n(653));const A={name:"CaseDetailForm",data:function(){return{uploadedImages:[],currentImageIndex:0,currentImage:null,selectedTag:"血管瘤",currentTool:"rectangle",annotations:[],isDrawing:!1,drawStart:{x:0,y:0},drawCurrent:{x:0,y:0},imageWidth:0,imageHeight:0,isEditingBox:!1,isResizingBox:!1,editingBoxId:null,resizeHandle:null,editStartPos:{x:0,y:0},originalBox:null,imagePairId:null}},computed:{filteredAnnotations:function(){var t=this;return this.annotations.filter((function(e){return e.imageIndex===t.currentImageIndex}))}},created:function(){var t=localStorage.getItem("uploadedImages");t?(this.uploadedImages=JSON.parse(t),this.uploadedImages.length>0&&(this.currentImage=this.uploadedImages[0])):(this.$message.warning("请先上传医学影像"),this.$router.push("/cases/new"))},mounted:function(){this.currentImage&&(this.loadAnnotationsFromDatabase(),this.saveOriginalImage()),window.addEventListener("mousemove",this.handleGlobalMouseMove),window.addEventListener("mouseup",this.handleGlobalMouseUp)},unmounted:function(){window.removeEventListener("mousemove",this.handleGlobalMouseMove),window.removeEventListener("mouseup",this.handleGlobalMouseUp)},methods:{onImageLoad:function(){var t=this;if(this.$refs.annotationImage){var e=this.$refs.annotationImage,n=e.getBoundingClientRect();this.imageWidth=n.width,this.imageHeight=n.height,this.$nextTick((function(){var n=e.getBoundingClientRect();t.imageWidth=n.width,t.imageHeight=n.height}))}},selectTool:function(t){this.currentTool=t},startDrawing:function(t){if(!this.isEditingBox&&!this.isResizingBox&&"rectangle"===this.currentTool){var e=this.$refs.imageContainerInner.getBoundingClientRect(),n=t.clientX-e.left,i=t.clientY-e.top;n<0||n>this.imageWidth||i<0||i>this.imageHeight||(this.isDrawing=!0,this.drawStart={x:n,y:i},this.drawCurrent={x:n,y:i})}},drawing:function(t){if(this.isDrawing){var e=this.$refs.imageContainerInner.getBoundingClientRect(),n=t.clientX-e.left,i=t.clientY-e.top,a=Math.max(0,Math.min(this.imageWidth,n)),r=Math.max(0,Math.min(this.imageHeight,i));this.drawCurrent={x:a,y:r}}},startEditBox:function(t,e){t.preventDefault();var n=this.annotations.find((function(t){return t.id===e}));if(n){this.originalBox=(0,B.A)({},n);var i=this.$refs.imageContainerInner.getBoundingClientRect();this.editStartPos={x:t.clientX-i.left,y:t.clientY-i.top},this.isEditingBox=!0,this.editingBoxId=e,this.isDrawing=!1}},startResizeBox:function(t,e,n){t.preventDefault();var i=this.annotations.find((function(t){return t.id===e}));if(i){this.originalBox=(0,B.A)({},i);var a=this.$refs.imageContainerInner.getBoundingClientRect();this.editStartPos={x:t.clientX-a.left,y:t.clientY-a.top},this.isResizingBox=!0,this.editingBoxId=e,this.resizeHandle=n,this.isDrawing=!1}},handleGlobalMouseMove:function(t){var e=this;if(this.isDrawing){var n=this.$refs.imageContainerInner.getBoundingClientRect(),i=t.clientX-n.left,a=t.clientY-n.top,r=Math.max(0,Math.min(this.imageWidth,i)),o=Math.max(0,Math.min(this.imageHeight,a));this.drawCurrent={x:r,y:o}}else{if(this.isEditingBox){var s=this.$refs.imageContainerInner.getBoundingClientRect(),c=t.clientX-s.left,l=t.clientY-s.top,d=c-this.editStartPos.x,h=l-this.editStartPos.y,g=this.annotations.findIndex((function(t){return t.id===e.editingBoxId}));if(-1===g)return;var u=this.originalBox.x+d,f=this.originalBox.y+h;return u=Math.max(0,Math.min(u,this.imageWidth-this.originalBox.width)),f=Math.max(0,Math.min(f,this.imageHeight-this.originalBox.height)),this.annotations[g].x=u,void(this.annotations[g].y=f)}if(this.isResizingBox){var m=this.$refs.imageContainerInner.getBoundingClientRect(),p=t.clientX-m.left,v=t.clientY-m.top,x=Math.max(0,Math.min(this.imageWidth,p)),w=Math.max(0,Math.min(this.imageHeight,v)),I=this.annotations.findIndex((function(t){return t.id===e.editingBoxId}));if(-1===I)return;var y=this.annotations[I],b=this.originalBox;switch(this.resizeHandle){case"top-left":y.width=b.x+b.width-x,y.height=b.y+b.height-w,y.x=x,y.y=w,y.width<10&&(y.width=10,y.x=b.x+b.width-10),y.height<10&&(y.height=10,y.y=b.y+b.height-10);break;case"top-right":y.width=x-b.x,y.height=b.y+b.height-w,y.y=w,y.width<10&&(y.width=10),y.height<10&&(y.height=10,y.y=b.y+b.height-10);break;case"bottom-left":y.width=b.x+b.width-x,y.height=w-b.y,y.x=x,y.width<10&&(y.width=10,y.x=b.x+b.width-10),y.height<10&&(y.height=10);break;case"bottom-right":y.width=x-b.x,y.height=w-b.y,y.width<10&&(y.width=10),y.height<10&&(y.height=10);break}}}},handleGlobalMouseUp:function(){var t=this;if(this.isDrawing)this.endDrawing();else if(this.isEditingBox||this.isResizingBox){var e=this.annotations.find((function(e){return e.id===t.editingBoxId}));if(e&&this.originalBox){var n=e.x!==this.originalBox.x||e.y!==this.originalBox.y||e.width!==this.originalBox.width||e.height!==this.originalBox.height;n&&(console.log("标注框已修改，更新数据库:",{原位置:"(".concat(this.originalBox.x,", ").concat(this.originalBox.y,")"),原尺寸:"".concat(this.originalBox.width," x ").concat(this.originalBox.height),新位置:"(".concat(e.x,", ").concat(e.y,")"),新尺寸:"".concat(e.width," x ").concat(e.height)}),this.updateAnnotationInDatabase(e))}this.isEditingBox=!1,this.isResizingBox=!1,this.editingBoxId=null,this.resizeHandle=null,this.originalBox=null}},endDrawing:function(){if(this.isDrawing&&(this.isDrawing=!1,Math.abs(this.drawCurrent.x-this.drawStart.x)>10&&Math.abs(this.drawCurrent.y-this.drawStart.y)>10)){var t=Math.min(this.drawStart.x,this.drawCurrent.x),e=Math.min(this.drawStart.y,this.drawCurrent.y),n=Math.abs(this.drawCurrent.x-this.drawStart.x),i=Math.abs(this.drawCurrent.y-this.drawStart.y),a={id:Date.now(),imageIndex:this.currentImageIndex,tag:this.selectedTag,type:"rectangle",x:t,y:e,width:n,height:i};this.annotations.push(a),this.saveAnnotationToDatabase(a)}},saveAnnotationToDatabase:function(t){var e=this,n=JSON.parse(localStorage.getItem("user"));if(n){var i=this.uploadedImages[this.currentImageIndex];if(i&&i.id){console.log("准备保存标注",{图像ID:i.id,标签类型:t.tag,坐标:"(".concat(t.x,", ").concat(t.y,")"),尺寸:"".concat(t.width," x ").concat(t.height),用户ID:n.id});var a={metadata_id:i.id,tag:t.tag,x:Math.round(t.x),y:Math.round(t.y),width:Math.round(t.width),height:Math.round(t.height),created_by:n.id};k.A.tags.create(a).then((function(n){console.log("标注已保存到数据库",n.data);var i=e.annotations.findIndex((function(e){return e.id===t.id}));-1!==i&&(e.annotations[i].dbId=n.data.id),e.$message.success("标注已保存")}))["catch"]((function(t){console.error("保存标注失败",t),t.response?(console.error("服务器响应:",t.response.status,t.response.data),401===t.response.status?e.$message.error("未授权，请先登录"):400===t.response.status?e.$message.error(t.response.data||"提交的标注数据无效"):e.$message.error("保存标注到数据库失败")):e.$message.error("保存标注到数据库失败，网络错误")}))}else this.$message.error("图像信息不完整，无法保存标注")}else this.$message.error("未检测到用户信息，无法保存标注")},getTagColor:function(t){var e={血管瘤:"#f56c6c",淋巴管瘤:"#409eff",混合型:"#67c23a",其他:"#909399"};return e[t]||"#409eff"},deleteAnnotation:function(t){var e=this,n=this.annotations.findIndex((function(e){return e.id===t}));if(-1!==n){var i=this.annotations[n];this.annotations.splice(n,1),i.dbId&&(console.log("正在从数据库删除标注",i.dbId),k.A.tags["delete"](i.dbId).then((function(){console.log("标注已从数据库中删除"),e.$message.success("标注已删除")}))["catch"]((function(t){console.error("删除标注失败",t),e.$message.error("从数据库删除标注失败"),e.annotations.splice(n,0,i)})))}},saveAndExit:function(){var t=this;if(0!==this.annotations.length){var e=this.$loading({lock:!0,text:"保存中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),n=this.uploadedImages[this.currentImageIndex];if(!n||!n.id)return this.$message.error("图像信息不完整，无法保存标注"),void e.close();console.log("saveAndExit: 使用真实图像ID保存标注:",n.id),k.A.tags.saveAnnotatedImage(n.id).then((function(n){console.log("标注图像保存成功",n.data),localStorage.setItem("annotations",JSON.stringify(t.annotations)),t.$message.success("标注已保存"),e.close(),t.$router.push("/cases")}))["catch"]((function(n){console.error("保存失败",n);var i="保存标注失败";n.response?(console.error("服务器响应:",n.response.status,n.response.data),"string"===typeof n.response.data?i+=": "+n.response.data:n.response.data&&n.response.data.message?i+=": "+n.response.data.message:i+=" (状态码: "+n.response.status+")"):n.message&&(i+=": "+n.message),t.$message.error(i),t.$confirm("保存失败，是否仍要返回列表页面？","提示",{confirmButtonText:"返回列表",cancelButtonText:"留在当前页面",type:"warning"}).then((function(){t.$router.push("/cases")}))["catch"]((function(){})),e.close()}))}else this.$message.warning("请至少添加一个标注框后再保存")},goBack:function(){var t=this;this.$confirm("返回上传页面可能会丢失当前未保存的标注，是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$router.push("/cases/new")}))["catch"]((function(){}))},saveAndNext:function(){var t=this;if(0!==this.annotations.length){var e=this.$loading({lock:!0,text:"保存中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),n=this.uploadedImages[this.currentImageIndex];if(!n||!n.id)return this.$message.error("图像信息不完整，无法保存标注"),void e.close();console.log("saveAndNext: 使用真实图像ID保存标注:",n.id),k.A.tags.saveAnnotatedImage(n.id).then((function(n){console.log("标注图像保存成功",n.data),localStorage.setItem("annotations",JSON.stringify(t.annotations)),t.$message.success("标注已保存"),e.close(),t.$router.push("/case-structured-form")}))["catch"]((function(n){console.error("保存失败",n);var i="保存标注失败";n.response?(console.error("服务器响应:",n.response.status,n.response.data),"string"===typeof n.response.data?i+=": "+n.response.data:n.response.data&&n.response.data.message?i+=": "+n.response.data.message:i+=" (状态码: "+n.response.status+")"):n.message&&(i+=": "+n.message),t.$message.error(i),t.$confirm("保存失败，是否仍要继续到下一步？","提示",{confirmButtonText:"继续",cancelButtonText:"留在当前页面",type:"warning"}).then((function(){t.$router.push("/case-structured-form")}))["catch"]((function(){})),e.close()}))}else this.$message.warning("请至少添加一个标注框后再保存")},saveOriginalImage:function(){var t=this,e=JSON.parse(localStorage.getItem("user"));if(!e){console.error("未检测到用户信息，无法保存原始图像");var n={id:1,name:"默认用户"};localStorage.setItem("user",JSON.stringify(n)),console.log("已创建默认用户")}var i=this.currentImage;if(i&&i.id){console.log("准备保存原始图像，图像ID信息：",{原始ID:i.id,ID类型:(0,D.A)(i.id),ID长度:String(i.id).length,图像路径:i.url});var a=i.id,r=JSON.parse(localStorage.getItem("user"))||{id:1},o=r.id||1;k.A.imagePairs.getByMetadataId(a).then((function(e){var n=e.data;if(n&&n.length>0)console.log("已存在图像对，不需要保存原始图像",n);else{var r={metadata_id:a,image_one_path:i.url,image_two_path:"",description:"图像ID:"+a,created_by:o};console.log("准备创建图像对，数据:",r),k.A.imagePairs.create(r).then((function(e){console.log("原始图像保存到图像对成功，使用真实图像ID:",a,e.data),t.imagePairId=e.data.id}))["catch"]((function(e){console.error("保存原始图像到图像对失败",e),t.createImagePairAlternative(a,i.url,o)}))}}))["catch"]((function(e){console.error("检查图像对时出错",e),t.createImagePairAlternative(a,i.url,o)}))}else console.error("图像信息不完整，无法保存原始图像")},loadAnnotationsFromDatabase:function(){var t=this,e=JSON.parse(localStorage.getItem("user"));e?this.uploadedImages.forEach((function(e,n){if(e&&e.id){console.log("准备加载图像(".concat(n,")的标注数据，ID信息:"),{原始ID:e.id,ID类型:(0,D.A)(e.id),ID长度:String(e.id).length,图像URL:e.url,图像对象:JSON.stringify(e)});var i=e.id;k.A.tags.getByImageId(i).then((function(e){console.log("成功获取图像(".concat(n,")的标注数据"),e);var i=e.data||[];Array.isArray(i)||(console.error("从服务器获取的标注数据不是数组格式",i),i=[]),i.forEach((function(e){var i={id:Date.now()+Math.random(),dbId:e.id,imageIndex:n,tag:e.tag,type:"rectangle",x:e.x,y:e.y,width:e.width,height:e.height};t.annotations.push(i)})),console.log("已加载图像".concat(n+1,"的").concat(i.length,"个标注"))}))["catch"]((function(e){console.error("加载图像".concat(n+1,"的标注失败"),e),console.error("标注加载错误详情:",{图像ID:i,错误状态:e.response?e.response.status:"网络错误",错误消息:e.response?e.response.data:e.message,请求URL:e.config?e.config.url:"未知",请求方法:e.config?e.config.method:"未知"}),e.response&&(console.error("服务器响应:",e.response.status,e.response.data),401===e.response.status?t.$message.error("请先登录后再进行标注"):404!==e.response.status&&400!==e.response.status||(console.warn("图像ID可能无效，尝试检查图像上传和保存逻辑"),t.$message.warning("无法加载图像标注，但您仍可以添加新标注")))}))}})):console.error("未检测到用户信息，无法加载标注")},createImagePairAlternative:function(t,e,n){console.log("尝试替代方法创建图像对，使用真实图像ID:",t),k.A.tags.saveAnnotatedImage(t).then((function(e){console.log("替代方法：标注图像保存成功，使用真实图像ID:",t,e.data)}))["catch"]((function(t){console.error("替代方法：保存标注图像失败",t)}))},saveAnnotatedImage:function(){var t=this,e=JSON.parse(localStorage.getItem("user"));if(!e)return console.error("未检测到用户信息，无法保存标注后的图像"),Promise.reject("未检测到用户信息");var n=this.currentImage;return n&&n.id?new Promise((function(e,i){k.A.imagePairs.getByMetadataId(n.id).then((function(n){var a=n.data;if(!a||0===a.length)return console.error("未找到图像对记录"),i("未找到图像对记录");var r=a[0];t.createAnnotatedImageDataURL().then((function(t){var e=(0,B.A)((0,B.A)({},r),{},{image_two_path:t});return k.A.imagePairs.update(r.id,e)})).then((function(t){console.log("标注后的图像保存成功",t.data),e(t.data)}))["catch"]((function(t){console.error("保存标注后的图像失败",t),i(t)}))}))["catch"]((function(t){console.error("检查图像对时出错",t),i(t)}))})):(console.error("图像信息不完整，无法保存标注后的图像"),Promise.reject("图像信息不完整"))},createAnnotatedImageDataURL:function(){var t=this;return new Promise((function(e){var n=document.createElement("canvas"),i=n.getContext("2d"),a=t.$refs.annotationImage;n.width=t.imageWidth,n.height=t.imageHeight,i.drawImage(a,0,0,t.imageWidth,t.imageHeight),t.filteredAnnotations.forEach((function(e){i.strokeStyle=t.getTagColor(e.tag),i.lineWidth=2,i.strokeRect(e.x,e.y,e.width,e.height),i.fillStyle=t.getTagColor(e.tag),i.font="12px Arial",i.fillText(e.tag,e.x,e.y-5)}));var r=n.toDataURL("image/png");e(r)}))},updateAnnotationInDatabase:function(t){var e=this,n=JSON.parse(localStorage.getItem("user"));if(n){if(!t.dbId)return console.warn("标注没有数据库ID，无法更新到数据库"),void(t.id&&this.saveAnnotationToDatabase(t));var i=this.uploadedImages[this.currentImageIndex];if(i&&i.id){console.log("准备更新标注",{标注ID:t.dbId,图像ID:i.id,标签类型:t.tag,新坐标:"(".concat(t.x,", ").concat(t.y,")"),新尺寸:"".concat(t.width," x ").concat(t.height),用户ID:n.id});var a={id:t.dbId,metadata_id:i.id,tag:t.tag,x:Math.round(t.x),y:Math.round(t.y),width:Math.round(t.width),height:Math.round(t.height),created_by:n.id};k.A.tags.update(t.dbId,a).then((function(t){console.log("标注已更新到数据库",t.data),e.$message.success("标注已更新")}))["catch"]((function(t){console.error("更新标注失败",t),t.response?(console.error("服务器响应:",t.response.status,t.response.data),401===t.response.status?e.$message.error("未授权，请先登录"):400===t.response.status?e.$message.error(t.response.data||"提交的标注数据无效"):404===t.response.status?e.$message.error("标注不存在，可能已被删除"):e.$message.error("更新标注到数据库失败")):e.$message.error("更新标注到数据库失败，网络错误")}))}else this.$message.error("图像信息不完整，无法更新标注")}else this.$message.error("未检测到用户信息，无法更新标注")}}};var M=n(6262);const C=(0,M.A)(A,[["render",b],["__scopeId","data-v-79cf7843"]]),$=C},3640:(t,e,n)=>{var i=n(8551),a=n(4270),r=TypeError;t.exports=function(t){if(i(this),"string"===t||"default"===t)t="string";else if("number"!==t)throw new r("Incorrect hint");return a(this,t)}},3802:(t,e,n)=>{var i=n(9504),a=n(7750),r=n(655),o=n(7452),s=i("".replace),c=RegExp("^["+o+"]+"),l=RegExp("(^|[^"+o+"])["+o+"]+$"),d=function(t){return function(e){var n=r(a(e));return 1&t&&(n=s(n,c,"")),2&t&&(n=s(n,l,"$1")),n}};t.exports={start:d(1),end:d(2),trim:d(3)}},3851:(t,e,n)=>{var i=n(6518),a=n(9039),r=n(5397),o=n(7347).f,s=n(3724),c=!s||a((function(){o(1)}));i({target:"Object",stat:!0,forced:c,sham:!s},{getOwnPropertyDescriptor:function(t,e){return o(r(t),e)}})},5700:(t,e,n)=>{var i=n(511),a=n(8242);i("toPrimitive"),a()},7452:t=>{t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},7735:(t,e,n)=>{var i=n(6518),a=n(9565),r=n(2652),o=n(9306),s=n(8551),c=n(1767),l=n(9539),d=n(4549),h=d("find",TypeError);i({target:"Iterator",proto:!0,real:!0,forced:h},{find:function(t){s(this);try{o(t)}catch(i){l(this,"throw",i)}if(h)return a(h,this,t);var e=c(this),n=0;return r(e,(function(e,i){if(t(e,n++))return i(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},7945:(t,e,n)=>{var i=n(6518),a=n(3724),r=n(6801).f;i({target:"Object",stat:!0,forced:Object.defineProperties!==r,sham:!a},{defineProperties:r})},8130:(t,e,n)=>{var i=n(6518),a=n(6395),r=n(3724),o=n(4576),s=n(9167),c=n(9504),l=n(2796),d=n(9297),h=n(3167),g=n(1625),u=n(757),f=n(2777),m=n(9039),p=n(8480).f,v=n(7347).f,x=n(4913).f,w=n(1240),I=n(3802).trim,y="Number",b=o[y],D=s[y],B=b.prototype,k=o.TypeError,A=c("".slice),M=c("".charCodeAt),C=function(t){var e=f(t,"number");return"bigint"==typeof e?e:$(e)},$=function(t){var e,n,i,a,r,o,s,c,l=f(t,"number");if(u(l))throw new k("Cannot convert a Symbol value to a number");if("string"==typeof l&&l.length>2)if(l=I(l),e=M(l,0),43===e||45===e){if(n=M(l,2),88===n||120===n)return NaN}else if(48===e){switch(M(l,1)){case 66:case 98:i=2,a=49;break;case 79:case 111:i=8,a=55;break;default:return+l}for(r=A(l,2),o=r.length,s=0;s<o;s++)if(c=M(r,s),c<48||c>a)return NaN;return parseInt(r,i)}return+l},S=l(y,!b(" 0o1")||!b("0b1")||b("+0x1")),E=function(t){return g(B,t)&&m((function(){w(t)}))},_=function(t){var e=arguments.length<1?0:b(C(t));return E(this)?h(Object(e),this,_):e};_.prototype=B,S&&!a&&(B.constructor=_),i({global:!0,constructor:!0,wrap:!0,forced:S},{Number:_});var T=function(t,e){for(var n,i=r?p(e):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),a=0;i.length>a;a++)d(e,n=i[a])&&!d(t,n)&&x(t,n,v(e,n))};a&&D&&T(s[y],D),(S||a)&&T(s[y],b)},9089:(t,e,n)=>{var i=n(6518),a=n(9504),r=Date,o=a(r.prototype.getTime);i({target:"Date",stat:!0},{now:function(){return o(new r)}})},9201:(t,e,n)=>{n.d(e,{A:()=>c});n(2675),n(2008),n(1629),n(4114),n(8111),n(2489),n(7588),n(7945),n(4185),n(3851),n(1278),n(9432),n(6099),n(3500);var i=n(4119);n(5700),n(6280),n(6918),n(9572),n(8130);function a(t,e){if("object"!=(0,i.A)(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,e||"default");if("object"!=(0,i.A)(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function r(t){var e=a(t,"string");return"symbol"==(0,i.A)(e)?e:e+""}function o(t,e,n){return(e=r(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach((function(e){o(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}},9572:(t,e,n)=>{var i=n(9297),a=n(6840),r=n(3640),o=n(8227),s=o("toPrimitive"),c=Date.prototype;i(c,s)||a(c,s,r)}}]);