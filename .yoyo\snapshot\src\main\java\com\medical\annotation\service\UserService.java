package com.medical.annotation.service;

import com.medical.annotation.model.Team;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.TeamRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.exception.UserNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;

@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TeamRepository teamRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;
    
    private final Random random = new Random();

    /**
     * 生成9位数随机用户ID
     * 不再将用户角色信息编码到ID中，而是生成纯粹的9位随机ID
     * @return 随机生成的9位数ID
     */
    private String generateCustomId() {
        // 生成9位随机数
        int randomNum = 100000000 + random.nextInt(900000000); // 生成9位数
        String customId = String.valueOf(randomNum);
        
        // 检查ID是否已存在，如果存在则重新生成
        while (userRepository.existsByCustomId(customId)) {
            randomNum = 100000000 + random.nextInt(900000000);
            customId = String.valueOf(randomNum);
        }
        
        return customId;
    }

    /**
     * 创建新用户
     * @param user 用户信息
     * @return 创建的用户
     */
    @Transactional
    public User createUser(User user) throws Exception {
        // 验证邮箱是否已被使用
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new Exception("邮箱已被使用");
        }

        // 设置默认角色和创建时间
        if (user.getRole() == null) {
            user.setRole(User.Role.DOCTOR);
        }

        // 生成自定义ID
        user.setCustomId(generateCustomId());

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置创建时间
        user.setCreatedAt(LocalDateTime.now());

        return userRepository.save(user);
    }

    /**
     * 更新用户信息
     * @param user 更新的用户信息
     * @return 更新后的用户
     */
    @Transactional
    public User updateUser(User user) throws Exception {
        // 验证用户是否存在
        Optional<User> existingUserOpt = userRepository.findById(user.getId());
        if (!existingUserOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User existingUser = existingUserOpt.get();

        // 更新用户信息，但保留密码和角色不变
        existingUser.setName(user.getName());
        existingUser.setDepartment(user.getDepartment());
        existingUser.setHospital(user.getHospital());

        return userRepository.save(existingUser);
    }

    /**
     * 更改用户密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 更新后的用户
     */
    @Transactional
    public User changePassword(Integer userId, String oldPassword, String newPassword) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 验证旧密码是否正确
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new Exception("旧密码不正确");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));

        return userRepository.save(user);
    }
    
    /**
     * 重置用户密码
     * @param userId 用户ID
     * @param newPassword 新密码
     * @param adminId 管理员ID
     * @return 更新后的用户
     */
    @Transactional
    public User resetPassword(Integer userId, String newPassword, Integer adminId) throws Exception {
        // 验证管理员是否有权限
        Optional<User> adminOpt = userRepository.findById(adminId);
        if (!adminOpt.isPresent() || adminOpt.get().getRole() != User.Role.ADMIN) {
            throw new Exception("无权限执行此操作");
        }
        
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));

        return userRepository.save(user);
    }

    /**
     * 申请升级为审核医生
     * @param userId 申请用户ID
     * @param reason 申请理由
     */
    @Transactional
    public void applyForReviewer(Integer userId, String reason) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 验证用户是否为标注医生
        if (user.getRole() != User.Role.DOCTOR) {
            throw new Exception("只有标注医生可以申请升级为审核医生");
        }

        // 验证用户是否已经提交过申请
        if (user.getReviewerApplicationStatus() != null && 
            user.getReviewerApplicationStatus() == User.ReviewerApplicationStatus.PENDING) {
            throw new Exception("您已提交过申请，请等待审核");
        }

        // 设置申请状态
        user.setReviewerApplicationStatus(User.ReviewerApplicationStatus.PENDING);
        user.setReviewerApplicationReason(reason);
        user.setReviewerApplicationDate(LocalDateTime.now());

        userRepository.save(user);
    }

    /**
     * 处理审核医生申请
     * @param userId 申请用户ID
     * @param approved 是否批准
     * @param adminId 处理管理员ID
     */
    @Transactional
    public void processReviewerApplication(Integer userId, boolean approved, Integer adminId) throws Exception {
        // 验证管理员是否有权限
        Optional<User> adminOpt = userRepository.findById(adminId);
        if (!adminOpt.isPresent() || adminOpt.get().getRole() != User.Role.ADMIN) {
            throw new Exception("无权限执行此操作");
        }

        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 验证用户是否有待处理的申请
        if (user.getReviewerApplicationStatus() != User.ReviewerApplicationStatus.PENDING) {
            throw new Exception("该用户没有待处理的申请");
        }

        // 更新申请状态
        if (approved) {
            user.setReviewerApplicationStatus(User.ReviewerApplicationStatus.APPROVED);
            user.setRole(User.Role.REVIEWER);
        } else {
            user.setReviewerApplicationStatus(User.ReviewerApplicationStatus.REJECTED);
        }

        user.setReviewerApplicationProcessedBy(adminOpt.get());
        user.setReviewerApplicationProcessedDate(LocalDateTime.now());

        userRepository.save(user);
    }

    /**
     * 设置用户所属团队
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param operatorId 操作者ID
     */
    @Transactional
    public void setUserTeam(Integer userId, Integer teamId, Integer operatorId) throws Exception {
        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }

        User user = userOpt.get();

        // 验证团队是否存在
        Optional<Team> teamOpt = null;
        if (teamId != null) {
            teamOpt = teamRepository.findById(teamId);
            if (!teamOpt.isPresent()) {
                throw new Exception("团队不存在");
            }
        }

        // 验证操作者是否有权限
        Optional<User> operatorOpt = userRepository.findById(operatorId);
        if (!operatorOpt.isPresent()) {
            throw new Exception("操作者不存在");
        }

        User operator = operatorOpt.get();
        if (operator.getRole() != User.Role.ADMIN && 
            (teamOpt != null && !teamOpt.get().getCreatedBy().getId().equals(operatorId)) &&
            operator.getRole() != User.Role.REVIEWER) {
            throw new Exception("无权更改用户所属团队");
        }

        // 更新用户所属团队
        user.setTeam(teamId != null ? teamOpt.get() : null);

        userRepository.save(user);
    }

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    public User getUserById(Integer userId) throws Exception {
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        return userOpt.get();
    }

    /**
     * 根据邮箱获取用户信息
     * @param email 邮箱
     * @return 用户信息
     */
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * 获取所有用户
     * @return 用户列表
     */
    @Transactional
    public List<User> getAllUsers() {
        List<User> users = userRepository.findAll();
        
        // 确保所有用户都有customId
        for (User user : users) {
            if (user.getCustomId() == null || user.getCustomId().isEmpty()) {
                user.setCustomId(generateCustomId());
                userRepository.save(user);
            }
        }
        
        return users;
    }

    /**
     * 获取所有审核医生
     * @return 审核医生列表
     */
    public List<User> getAllReviewers() {
        return userRepository.findByRole(User.Role.REVIEWER);
    }

    /**
     * 获取所有管理员
     * @return 管理员列表
     */
    public List<User> getAllAdmins() {
        return userRepository.findByRole(User.Role.ADMIN);
    }

    /**
     * 获取所有待处理的审核医生申请
     * @return 待处理申请列表
     */
    public List<User> getPendingReviewerApplications() {
        return userRepository.findByReviewerApplicationStatus(User.ReviewerApplicationStatus.PENDING);
    }

    /**
     * 获取团队中的所有用户
     * @param teamId 团队ID
     * @return 用户列表
     */
    public List<User> getUsersByTeam(Integer teamId) {
        return userRepository.findByTeam_Id(teamId);
    }

    /**
     * 获取团队中的所有审核医生
     * @param teamId 团队ID
     * @return 审核医生列表
     */
    public List<User> getReviewersByTeam(Integer teamId) {
        return userRepository.findByTeam_IdAndRole(teamId, User.Role.REVIEWER);
    }

    /**
     * 获取没有团队的用户
     * @return 无团队用户列表
     */
    public List<User> getUsersWithoutTeam() {
        return userRepository.findByTeamIsNull();
    }

    /**
     * 根据自定义ID获取用户
     * @param customId 9位数自定义用户ID
     * @return 用户信息
     */
    public Optional<User> getUserByCustomId(String customId) {
        System.out.println("通过customId查找用户: " + customId);
        
        // 先尝试使用带校对规则的查询
        Optional<User> userOpt = userRepository.findByCustomIdWithCollation(customId);
        
        if (userOpt.isPresent()) {
            System.out.println("通过校对规则查询成功找到用户: " + userOpt.get().getName());
            return userOpt;
        }
        
        // 如果没找到，尝试普通查询
        System.out.println("通过校对规则查询未找到用户，尝试普通查询");
        return userRepository.findByCustomId(customId);
    }
    
    /**
     * 根据自定义ID获取用户，如果不存在则抛出异常
     * @param customId 9位数自定义用户ID
     * @return 用户
     * @throws UserNotFoundException 如果用户不存在
     */
    public User getUserByCustomIdOrThrow(String customId) {
        return getUserByCustomId(customId)
                .orElseThrow(() -> UserNotFoundException.forCustomId(customId));
    }
} 