const state = {
  step: 0, // 0: 未开始, 1: 图像标注, 2: 病例信息填写
  imageId: null,
  formData: null,
  testMode: false,
  imagePath: null,  // 添加imagePath属性保存图片路径，支持离线模式
  lastUpdated: null
};

const getters = {
  getAnnotationProgress: state => {
    return {
      step: state.step,
      imageId: state.imageId,
      formData: state.formData,
      testMode: state.testMode,
      imagePath: state.imagePath,
      lastUpdated: state.lastUpdated
    };
  },
  hasUnfinishedAnnotation: state => {
    return state.step > 0 && state.imageId !== null;
  },
  getCurrentStep: state => state.step,
  getImageId: state => state.imageId,
  getFormData: state => state.formData,
  isTestMode: state => state.testMode,
  getImagePath: state => state.imagePath, // 获取图片路径的getter
  getStructuredFormProgress: state => {
    return {
      formData: state.formData
    };
  }
};

const actions = {
  saveProgress({ commit }, progress) {
    commit('SET_PROGRESS', progress);
  },
  completeAnnotation({ commit }) {
    commit('COMPLETE_ANNOTATION');
  },
  updateFormData({ commit }, formData) {
    commit('UPDATE_FORM_DATA', formData);
  }
};

const mutations = {
  SET_PROGRESS(state, progress) {
    state.step = progress.step || state.step;
    state.imageId = progress.imageId || state.imageId;
    
    // 保存表单数据
    if (progress.formData) {
      state.formData = progress.formData;
    }
    
    // 标记测试模式
    if (typeof progress.testMode !== 'undefined') {
      state.testMode = progress.testMode;
    }
    
    // 保存图片路径
    if (progress.imagePath) {
      state.imagePath = progress.imagePath;
    }
    
    // 离线模式也同步保存到localStorage
    if ((progress.testMode || localStorage.getItem('offlineMode') === 'true') && progress.imageId) {
      try {
        localStorage.setItem(`offline_progress_${progress.imageId}`, JSON.stringify({
          step: state.step,
          formData: state.formData,
          testMode: state.testMode,
          imagePath: state.imagePath
        }));
      } catch (e) {
        console.error('保存离线进度失败', e);
      }
    }
    
    state.lastUpdated = new Date().toISOString();
  },
  
  UPDATE_FORM_DATA(state, formData) {
    state.formData = formData;
    state.lastUpdated = new Date().toISOString();
    
    // 离线模式也同步保存表单数据
    if ((state.testMode || localStorage.getItem('offlineMode') === 'true') && state.imageId) {
      try {
        localStorage.setItem(`offline_form_${state.imageId}`, JSON.stringify(formData));
        
        // 同步更新进度
        localStorage.setItem(`offline_progress_${state.imageId}`, JSON.stringify({
          step: state.step,
          formData: formData,
          testMode: state.testMode,
          imagePath: state.imagePath
        }));
      } catch (e) {
        console.error('保存离线表单数据失败', e);
      }
    }
  },
  
  COMPLETE_ANNOTATION(state) {
    // 清除本地存储中的进度
    if (state.imageId) {
      try {
        localStorage.removeItem(`offline_progress_${state.imageId}`);
      } catch (e) {
        console.error('清除离线进度失败', e);
      }
    }
    
    // 重置状态
    state.step = 0;
    state.imageId = null;
    state.formData = null;
    state.testMode = false;
    state.imagePath = null;
    state.lastUpdated = new Date().toISOString();
  }
};

export default {
  state,
  getters,
  actions,
  mutations
}; 