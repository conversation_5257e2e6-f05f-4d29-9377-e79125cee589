"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[527],{1240:(e,t,r)=>{var n=r(9504);e.exports=n(1..valueOf)},1278:(e,t,r)=>{var n=r(6518),a=r(3724),s=r(5031),i=r(5397),c=r(7347),o=r(4659);n({target:"Object",stat:!0,sham:!a},{getOwnPropertyDescriptors:function(e){var t,r,n=i(e),a=c.f,u=s(n),l={},f=0;while(u.length>f)r=a(n,t=u[f++]),void 0!==r&&o(l,t,r);return l}})},2478:(e,t,r)=>{var n=r(9504),a=r(8981),s=Math.floor,i=n("".charAt),c=n("".replace),o=n("".slice),u=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,l=/\$([$&'`]|\d{1,2})/g;e.exports=function(e,t,r,n,f,p){var d=r+e.length,v=n.length,g=l;return void 0!==f&&(f=a(f),g=u),c(p,g,(function(a,c){var u;switch(i(c,0)){case"$":return"$";case"&":return e;case"`":return o(t,0,r);case"'":return o(t,d);case"<":u=f[o(c,1,-1)];break;default:var l=+c;if(0===l)return a;if(l>v){var p=s(l/10);return 0===p?a:p<=v?void 0===n[p-1]?i(c,1):n[p-1]+i(c,1):a}u=n[l-1]}return void 0===u?"":u}))}},3640:(e,t,r)=>{var n=r(8551),a=r(4270),s=TypeError;e.exports=function(e){if(n(this),"string"===e||"default"===e)e="string";else if("number"!==e)throw new s("Incorrect hint");return a(this,e)}},3802:(e,t,r)=>{var n=r(9504),a=r(7750),s=r(655),i=r(7452),c=n("".replace),o=RegExp("^["+i+"]+"),u=RegExp("(^|[^"+i+"])["+i+"]+$"),l=function(e){return function(t){var r=s(a(t));return 1&e&&(r=c(r,o,"")),2&e&&(r=c(r,u,"$1")),r}};e.exports={start:l(1),end:l(2),trim:l(3)}},3851:(e,t,r)=>{var n=r(6518),a=r(9039),s=r(5397),i=r(7347).f,c=r(3724),o=!c||a((function(){i(1)}));n({target:"Object",stat:!0,forced:o,sham:!c},{getOwnPropertyDescriptor:function(e,t){return i(s(e),t)}})},5440:(e,t,r)=>{var n=r(8745),a=r(9565),s=r(9504),i=r(9228),c=r(9039),o=r(8551),u=r(4901),l=r(34),f=r(1291),p=r(8014),d=r(655),v=r(7750),g=r(7829),b=r(5966),m=r(2478),k=r(6682),h=r(8227),y=h("replace"),L=Math.max,x=Math.min,E=s([].concat),w=s([].push),A=s("".indexOf),C=s("".slice),I=function(e){return void 0===e?e:String(e)},P=function(){return"$0"==="a".replace(/./,"$0")}(),O=function(){return!!/./[y]&&""===/./[y]("a","$0")}(),F=!c((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}));i("replace",(function(e,t,r){var s=O?"$":"$0";return[function(e,r){var n=v(this),s=l(e)?b(e,y):void 0;return s?a(s,e,n,r):a(t,d(n),e,r)},function(e,a){var i=o(this),c=d(e);if("string"==typeof a&&-1===A(a,s)&&-1===A(a,"$<")){var l=r(t,i,c,a);if(l.done)return l.value}var v=u(a);v||(a=d(a));var b,h=i.global;h&&(b=i.unicode,i.lastIndex=0);var y,P=[];while(1){if(y=k(i,c),null===y)break;if(w(P,y),!h)break;var O=d(y[0]);""===O&&(i.lastIndex=g(c,p(i.lastIndex),b))}for(var F="",D=0,_=0;_<P.length;_++){y=P[_];for(var N,T=d(y[0]),S=L(x(f(y.index),c.length),0),j=[],R=1;R<y.length;R++)w(j,I(y[R]));var $=y.groups;if(v){var B=E([T],j,S,c);void 0!==$&&w(B,$),N=d(n(a,void 0,B))}else N=m(T,c,S,j,$,a);S>=D&&(F+=C(c,D,S)+N,D=S+T.length)}return F+C(c,D)}]}),!F||!P||O)},5700:(e,t,r)=>{var n=r(511),a=r(8242);n("toPrimitive"),a()},6527:(e,t,r)=>{r.r(t),r.d(t,{default:()=>J});var n=r(641),a=r(3751),s=r(33),i={class:"container mt-4"},c={class:"d-flex justify-content-between align-items-center mb-4"},o={class:"card mb-4"},u={class:"card-body"},l={class:"row"},f={class:"col-md-3 mb-2"},p={class:"col-md-3 mb-2"},d={class:"col-md-3 mb-2"},v={class:"col-md-3 mb-2"},g={class:"d-flex justify-content-end mt-2"},b={key:0,class:"loader-container"},m={key:1,class:"alert alert-danger"},k={key:2,class:"text-center py-5"},h={key:3,class:"row"},y={class:"card h-100"},L={class:"image-container"},x=["src","alt"],E={class:"card-body"},w={class:"card-title"},A={class:"card-text"},C={class:"text-muted"},I={class:"card-text"},P={class:"badge bg-secondary ms-2"},O={class:"card-text"},F={class:"card-footer bg-transparent"},D={class:"d-flex justify-content-between"},_=["onClick"],N=["onClick"],T={key:4,class:"mt-4"},S={class:"pagination justify-content-center"},j=["onClick"];function R(e,t,r,R,$,B){var X=(0,n.g2)("router-link");return(0,n.uX)(),(0,n.CE)("div",i,[(0,n.Lk)("div",c,[t[9]||(t[9]=(0,n.Lk)("h2",null,"图像管理",-1)),(0,n.Lk)("div",null,[(0,n.bF)(X,{to:"/images/upload",class:"btn btn-success"},{default:(0,n.k6)((function(){return t[8]||(t[8]=[(0,n.Lk)("i",{class:"bi bi-plus-circle me-1"},null,-1),(0,n.eW)(" 上传新图像 ")])})),_:1,__:[8]})])]),(0,n.Lk)("div",o,[(0,n.Lk)("div",u,[(0,n.Lk)("div",l,[(0,n.Lk)("div",f,[t[11]||(t[11]=(0,n.Lk)("label",{for:"statusFilter",class:"form-label"},"状态",-1)),(0,n.bo)((0,n.Lk)("select",{id:"statusFilter",class:"form-select","onUpdate:modelValue":t[0]||(t[0]=function(e){return R.filters.status=e})},t[10]||(t[10]=[(0,n.Fv)('<option value="" data-v-559740d0>全部</option><option value="DRAFT" data-v-559740d0>草稿</option><option value="SUBMITTED" data-v-559740d0>待审核</option><option value="APPROVED" data-v-559740d0>已通过</option><option value="REJECTED" data-v-559740d0>未通过</option>',5)]),512),[[a.u1,R.filters.status]])]),(0,n.Lk)("div",p,[t[12]||(t[12]=(0,n.Lk)("label",{for:"patientFilter",class:"form-label"},"患者名称",-1)),(0,n.bo)((0,n.Lk)("input",{type:"text",id:"patientFilter",class:"form-control","onUpdate:modelValue":t[1]||(t[1]=function(e){return R.filters.patientName=e})},null,512),[[a.Jo,R.filters.patientName]])]),(0,n.Lk)("div",d,[t[13]||(t[13]=(0,n.Lk)("label",{for:"diagnosticFilter",class:"form-label"},"诊断类别",-1)),(0,n.bo)((0,n.Lk)("input",{type:"text",id:"diagnosticFilter",class:"form-control","onUpdate:modelValue":t[2]||(t[2]=function(e){return R.filters.diagnosisCategory=e})},null,512),[[a.Jo,R.filters.diagnosisCategory]])]),(0,n.Lk)("div",v,[t[15]||(t[15]=(0,n.Lk)("label",{for:"sortBy",class:"form-label"},"排序方式",-1)),(0,n.bo)((0,n.Lk)("select",{id:"sortBy",class:"form-select","onUpdate:modelValue":t[3]||(t[3]=function(e){return R.sortBy=e})},t[14]||(t[14]=[(0,n.Lk)("option",{value:"createdAt"},"上传时间",-1),(0,n.Lk)("option",{value:"patientName"},"患者姓名",-1),(0,n.Lk)("option",{value:"status"},"状态",-1)]),512),[[a.u1,R.sortBy]])])]),(0,n.Lk)("div",g,[(0,n.Lk)("button",{class:"btn btn-primary me-2",onClick:t[4]||(t[4]=function(){return R.applyFilters&&R.applyFilters.apply(R,arguments)})},t[16]||(t[16]=[(0,n.Lk)("i",{class:"bi bi-funnel me-1"},null,-1),(0,n.eW)(" 筛选 ")])),(0,n.Lk)("button",{class:"btn btn-outline-secondary",onClick:t[5]||(t[5]=function(){return R.resetFilters&&R.resetFilters.apply(R,arguments)})},t[17]||(t[17]=[(0,n.Lk)("i",{class:"bi bi-arrow-repeat me-1"},null,-1),(0,n.eW)(" 重置 ")]))])])]),R.loading?((0,n.uX)(),(0,n.CE)("div",b,t[18]||(t[18]=[(0,n.Lk)("div",{class:"spinner-border text-primary",role:"status"},[(0,n.Lk)("span",{class:"visually-hidden"},"加载中...")],-1)]))):R.error?((0,n.uX)(),(0,n.CE)("div",m,(0,s.v_)(R.error),1)):0===R.images.length?((0,n.uX)(),(0,n.CE)("div",k,t[19]||(t[19]=[(0,n.Lk)("div",{class:"mb-3"},[(0,n.Lk)("i",{class:"bi bi-images",style:{"font-size":"3rem"}})],-1),(0,n.Lk)("h4",null,"暂无图像数据",-1),(0,n.Lk)("p",{class:"text-muted"},'点击上方"上传新图像"按钮添加图像',-1)]))):((0,n.uX)(),(0,n.CE)("div",h,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(R.images,(function(e){return(0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"col-md-4 mb-4"},[(0,n.Lk)("div",y,[(0,n.Lk)("div",L,[(0,n.Lk)("img",{src:e.path,alt:e.filename,class:"card-img-top"},null,8,x)]),(0,n.Lk)("div",E,[(0,n.Lk)("h5",w,(0,s.v_)(e.filename),1),(0,n.Lk)("p",A,[(0,n.Lk)("small",C," 患者: "+(0,s.v_)(e.patientName||"未知")+", "+(0,s.v_)(e.patientAge||"?")+"岁, "+(0,s.v_)(e.patientGender||"未知"),1)]),(0,n.Lk)("p",I,[(0,n.Lk)("span",{class:(0,s.C4)(R.getStatusBadgeClass(e.status))},(0,s.v_)(R.getStatusText(e.status)),3),(0,n.Lk)("span",P,(0,s.v_)(e.mimetype),1)]),(0,n.Lk)("p",O,[(0,n.Lk)("small",null," 上传时间: "+(0,s.v_)(R.formatDate(e.createdAt)),1)])]),(0,n.Lk)("div",F,[(0,n.Lk)("div",D,[(0,n.bF)(X,{to:"/images/".concat(e.id),class:"btn btn-primary"},{default:(0,n.k6)((function(){return t[20]||(t[20]=[(0,n.Lk)("i",{class:"bi bi-eye me-1"},null,-1),(0,n.eW)(" 查看 ")])})),_:2,__:[20]},1032,["to"]),(0,n.Lk)("div",null,["DRAFT"===e.status?((0,n.uX)(),(0,n.CE)("button",{key:0,class:"btn btn-warning me-2",onClick:function(t){return R.submitImage(e.id)}},t[21]||(t[21]=[(0,n.Lk)("i",{class:"bi bi-send me-1"},null,-1),(0,n.eW)(" 提交 ")]),8,_)):(0,n.Q3)("",!0),(0,n.Lk)("button",{class:"btn btn-danger",onClick:function(t){return R.confirmDelete(e.id)}},t[22]||(t[22]=[(0,n.Lk)("i",{class:"bi bi-trash me-1"},null,-1),(0,n.eW)(" 删除 ")]),8,N)])])])])])})),128))])),R.images.length>0?((0,n.uX)(),(0,n.CE)("nav",T,[(0,n.Lk)("ul",S,[(0,n.Lk)("li",{class:(0,s.C4)(["page-item",{disabled:1===R.currentPage}])},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:t[6]||(t[6]=(0,a.D$)((function(e){return R.changePage(R.currentPage-1)}),["prevent"]))},"上一页")],2),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(R.totalPages,(function(e){return(0,n.uX)(),(0,n.CE)("li",{class:(0,s.C4)(["page-item",{active:e===R.currentPage}]),key:e},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:(0,a.D$)((function(t){return R.changePage(e)}),["prevent"])},(0,s.v_)(e),9,j)],2)})),128)),(0,n.Lk)("li",{class:(0,s.C4)(["page-item",{disabled:R.currentPage===R.totalPages}])},[(0,n.Lk)("a",{class:"page-link",href:"#",onClick:t[7]||(t[7]=(0,a.D$)((function(e){return R.changePage(R.currentPage+1)}),["prevent"]))},"下一页")],2)])])):(0,n.Q3)("",!0)])}var $=r(9201),B=r(4048),X=r(388),M=(r(3288),r(7495),r(5440),r(953)),V=r(6278),U=r(5220);const W={name:"ImageList",setup:function(){var e=(0,V.Pj)(),t=(0,U.lq)(),r=(0,U.rd)(),a=(0,n.EW)((function(){return e.getters.getAllImages})),s=(0,n.EW)((function(){return e.getters.isLoading})),i=(0,n.EW)((function(){return e.getters.getError})),c=(0,M.KR)(1),o=(0,M.KR)(12),u=(0,n.EW)((function(){return Math.ceil(a.value.length/o.value)})),l=(0,M.KR)({status:t.query.status||"",patientName:"",diagnosisCategory:""}),f=(0,M.KR)("createdAt");(0,n.wB)((function(){return t.query.status}),(function(e){e!==l.value.status&&(l.value.status=e||"",p())})),(0,n.sV)((0,X.A)((0,B.A)().mark((function r(){return(0,B.A)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(r.prev=0,!t.query.status){r.next=6;break}return r.next=4,e.dispatch("fetchImagesByStatus",t.query.status);case 4:r.next=8;break;case 6:return r.next=8,e.dispatch("fetchImages");case 8:r.next=13;break;case 10:r.prev=10,r.t0=r["catch"](0),console.error("Failed to load images:",r.t0);case 13:case"end":return r.stop()}}),r,null,[[0,10]])}))));var p=function(){var n=(0,X.A)((0,B.A)().mark((function n(){var a;return(0,B.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,!l.value.status){n.next=7;break}return n.next=4,e.dispatch("fetchImagesByStatus",l.value.status);case 4:r.replace({query:(0,$.A)((0,$.A)({},t.query),{},{status:l.value.status})})["catch"]((function(){})),n.next=12;break;case 7:return n.next=9,e.dispatch("fetchImages");case 9:a=(0,$.A)({},t.query),delete a.status,r.replace({query:a})["catch"]((function(){}));case 12:c.value=1,n.next=18;break;case 15:n.prev=15,n.t0=n["catch"](0),console.error("Failed to apply filters:",n.t0);case 18:case"end":return n.stop()}}),n,null,[[0,15]])})));return function(){return n.apply(this,arguments)}}(),d=function(){l.value={status:"",patientName:"",diagnosisCategory:""},f.value="createdAt",p()},v=function(e){e>=1&&e<=u.value&&(c.value=e)},g=function(){var t=(0,X.A)((0,B.A)().mark((function t(r){return(0,B.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!confirm("确定要提交此图像进行审核吗？")){t.next=9;break}return t.prev=1,t.next=4,e.dispatch("updateImageStatus",{id:r,status:"SUBMITTED"});case 4:t.next=9;break;case 6:t.prev=6,t.t0=t["catch"](1),console.error("Failed to submit image:",t.t0);case 9:case"end":return t.stop()}}),t,null,[[1,6]])})));return function(e){return t.apply(this,arguments)}}(),b=function(){var t=(0,X.A)((0,B.A)().mark((function t(r){return(0,B.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!confirm("确定要删除此图像吗？此操作不可撤销！")){t.next=9;break}return t.prev=1,t.next=4,e.dispatch("deleteImage",r);case 4:t.next=9;break;case 6:t.prev=6,t.t0=t["catch"](1),console.error("Failed to delete image:",t.t0);case 9:case"end":return t.stop()}}),t,null,[[1,6]])})));return function(e){return t.apply(this,arguments)}}(),m=function(e){switch(e){case"DRAFT":return"badge bg-primary";case"SUBMITTED":return"badge bg-warning";case"APPROVED":return"badge bg-success";case"REJECTED":return"badge bg-danger";default:return"badge bg-secondary"}},k=function(e){switch(e){case"DRAFT":return"草稿";case"SUBMITTED":return"待审核";case"APPROVED":return"已通过";case"REJECTED":return"未通过";default:return"未知"}},h=function(e){if(!e)return"未知";var t=new Date(e);return t.toLocaleString("zh-CN")};return{images:a,loading:s,error:i,currentPage:c,totalPages:u,filters:l,sortBy:f,applyFilters:p,resetFilters:d,changePage:v,submitImage:g,confirmDelete:b,getStatusBadgeClass:m,getStatusText:k,formatDate:h}}};var q=r(6262);const K=(0,q.A)(W,[["render",R],["__scopeId","data-v-559740d0"]]),J=K},6682:(e,t,r)=>{var n=r(9565),a=r(8551),s=r(4901),i=r(2195),c=r(7323),o=TypeError;e.exports=function(e,t){var r=e.exec;if(s(r)){var u=n(r,e,t);return null!==u&&a(u),u}if("RegExp"===i(e))return n(c,e,t);throw new o("RegExp#exec called on incompatible receiver")}},7452:e=>{e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},7829:(e,t,r)=>{var n=r(8183).charAt;e.exports=function(e,t,r){return t+(r?n(e,t).length:1)}},7945:(e,t,r)=>{var n=r(6518),a=r(3724),s=r(6801).f;n({target:"Object",stat:!0,forced:Object.defineProperties!==s,sham:!a},{defineProperties:s})},8130:(e,t,r)=>{var n=r(6518),a=r(6395),s=r(3724),i=r(4576),c=r(9167),o=r(9504),u=r(2796),l=r(9297),f=r(3167),p=r(1625),d=r(757),v=r(2777),g=r(9039),b=r(8480).f,m=r(7347).f,k=r(4913).f,h=r(1240),y=r(3802).trim,L="Number",x=i[L],E=c[L],w=x.prototype,A=i.TypeError,C=o("".slice),I=o("".charCodeAt),P=function(e){var t=v(e,"number");return"bigint"==typeof t?t:O(t)},O=function(e){var t,r,n,a,s,i,c,o,u=v(e,"number");if(d(u))throw new A("Cannot convert a Symbol value to a number");if("string"==typeof u&&u.length>2)if(u=y(u),t=I(u,0),43===t||45===t){if(r=I(u,2),88===r||120===r)return NaN}else if(48===t){switch(I(u,1)){case 66:case 98:n=2,a=49;break;case 79:case 111:n=8,a=55;break;default:return+u}for(s=C(u,2),i=s.length,c=0;c<i;c++)if(o=I(s,c),o<48||o>a)return NaN;return parseInt(s,n)}return+u},F=u(L,!x(" 0o1")||!x("0b1")||x("+0x1")),D=function(e){return p(w,e)&&g((function(){h(e)}))},_=function(e){var t=arguments.length<1?0:x(P(e));return D(this)?f(Object(t),this,_):t};_.prototype=w,F&&!a&&(w.constructor=_),n({global:!0,constructor:!0,wrap:!0,forced:F},{Number:_});var N=function(e,t){for(var r,n=s?b(t):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),a=0;n.length>a;a++)l(t,r=n[a])&&!l(e,r)&&k(e,r,m(t,r))};a&&E&&N(c[L],E),(F||a)&&N(c[L],x)},9201:(e,t,r)=>{r.d(t,{A:()=>o});r(2675),r(2008),r(1629),r(4114),r(8111),r(2489),r(7588),r(7945),r(4185),r(3851),r(1278),r(9432),r(6099),r(3500);var n=r(4119);r(5700),r(6280),r(6918),r(9572),r(8130);function a(e,t){if("object"!=(0,n.A)(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=(0,n.A)(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function s(e){var t=a(e,"string");return"symbol"==(0,n.A)(t)?t:t+""}function i(e,t,r){return(t=s(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}},9228:(e,t,r)=>{r(7495);var n=r(9565),a=r(6840),s=r(7323),i=r(9039),c=r(8227),o=r(6699),u=c("species"),l=RegExp.prototype;e.exports=function(e,t,r,f){var p=c(e),d=!i((function(){var t={};return t[p]=function(){return 7},7!==""[e](t)})),v=d&&!i((function(){var t=!1,r=/a/;return"split"===e&&(r={},r.constructor={},r.constructor[u]=function(){return r},r.flags="",r[p]=/./[p]),r.exec=function(){return t=!0,null},r[p](""),!t}));if(!d||!v||r){var g=/./[p],b=t(p,""[e],(function(e,t,r,a,i){var c=t.exec;return c===s||c===l.exec?d&&!i?{done:!0,value:n(g,t,r,a)}:{done:!0,value:n(e,r,t,a)}:{done:!1}}));a(String.prototype,e,b[0]),a(l,p,b[1])}f&&o(l[p],"sham",!0)}},9572:(e,t,r)=>{var n=r(9297),a=r(6840),s=r(3640),i=r(8227),c=i("toPrimitive"),o=Date.prototype;n(o,c)||a(o,c,s)}}]);