(()=>{var e={10014:(e,t,n)=>{"use strict";var a=n(88844),o=n(24059),r=n(698),s=n(23948),i=(n(16280),n(76918),n(28706),n(74423),n(59089),n(60739),n(23288),n(33110),n(5506),n(79432),n(26099),n(21699),n(11392),n(76031),n(48053));"undefined"!==typeof window&&"undefined"===typeof window.tryNextPath&&(window.tryNextPath=i.tryNextPath);n(84315),n(7452);"undefined"!==typeof window&&(window.process=window.process||{},window.process.browser=!0,window.process.env=window.process.env||{},window.Buffer=window.Buffer||n(48287).hp),"undefined"!==typeof n.g&&n.g.util&&n.g.util._extend&&(n.g.util._extend=Object.assign);var c=n(61431);function u(e,t,n,a,o,r){var s=(0,c.g2)("router-view");return(0,c.uX)(),(0,c.Wv)(s)}const l={name:"App"};var d=n(66262);const g=(0,d.A)(l,[["render",u]]),m=g;n(15086),n(62010),n(18111),n(13579),n(58940),n(47764),n(62953);var p=n(60455),f=(n(44114),{class:"header-left"}),h={class:"header-right"},v={class:"user-info"};function A(e,t,n,a,o,r){var s=(0,c.g2)("HomeFilled"),i=(0,c.g2)("el-icon"),u=(0,c.g2)("el-menu-item"),l=(0,c.g2)("Document"),d=(0,c.g2)("Picture"),g=(0,c.g2)("Check"),m=(0,c.g2)("UserFilled"),p=(0,c.g2)("User"),A=(0,c.g2)("el-menu"),I=(0,c.g2)("el-aside"),w=(0,c.g2)("el-button"),b=(0,c.g2)("el-avatar"),_=(0,c.g2)("router-link"),E=(0,c.g2)("el-dropdown-item"),S=(0,c.g2)("el-dropdown-menu"),y=(0,c.g2)("el-dropdown"),k=(0,c.g2)("el-header"),P=(0,c.g2)("router-view"),C=(0,c.g2)("el-main"),D=(0,c.g2)("el-container");return(0,c.uX)(),(0,c.Wv)(D,{class:"layout-container"},{default:(0,c.k6)((function(){return[(0,c.bF)(I,{width:"200px",class:"sidebar"},{default:(0,c.k6)((function(){return[t[12]||(t[12]=(0,c.Lk)("div",{class:"logo"},"血管瘤诊断平台",-1)),(0,c.bF)(A,{"default-active":r.activeMenu,class:"sidebar-menu","background-color":"#001529","text-color":"#fff","active-text-color":"#409EFF"},{default:(0,c.k6)((function(){return[(0,c.bF)(u,{index:"/app/dashboard",onClick:t[0]||(t[0]=function(t){return e.$router.push("/app/dashboard")})},{default:(0,c.k6)((function(){return[(0,c.bF)(i,null,{default:(0,c.k6)((function(){return[(0,c.bF)(s)]})),_:1}),t[6]||(t[6]=(0,c.Lk)("span",null,"工作台",-1))]})),_:1,__:[6]}),(0,c.bF)(u,{index:"/app/cases",onClick:t[1]||(t[1]=function(t){return e.$router.push("/app/cases")})},{default:(0,c.k6)((function(){return[(0,c.bF)(i,null,{default:(0,c.k6)((function(){return[(0,c.bF)(l)]})),_:1}),t[7]||(t[7]=(0,c.Lk)("span",null,"病例标注",-1))]})),_:1,__:[7]}),(0,c.bF)(u,{index:"/app/hemangioma-diagnosis",onClick:r.navigateToHemangiomaDiagnosis},{default:(0,c.k6)((function(){return[(0,c.bF)(i,null,{default:(0,c.k6)((function(){return[(0,c.bF)(d)]})),_:1}),t[8]||(t[8]=(0,c.Lk)("span",null,"血管瘤诊断",-1))]})),_:1,__:[8]},8,["onClick"]),e.isAdmin||e.isReviewer?((0,c.uX)(),(0,c.Wv)(u,{key:0,index:"/app/annotation-reviews",onClick:t[2]||(t[2]=function(t){return e.$router.push("/app/annotation-reviews")})},{default:(0,c.k6)((function(){return[(0,c.bF)(i,null,{default:(0,c.k6)((function(){return[(0,c.bF)(g)]})),_:1}),t[9]||(t[9]=(0,c.Lk)("span",null,"标注审核",-1))]})),_:1,__:[9]})):(0,c.Q3)("",!0),(0,c.bF)(u,{index:"/app/teams",onClick:t[3]||(t[3]=function(t){return e.$router.push("/app/teams")}),class:"team-menu-item"},{default:(0,c.k6)((function(){return[(0,c.bF)(i,null,{default:(0,c.k6)((function(){return[(0,c.bF)(m)]})),_:1}),t[10]||(t[10]=(0,c.Lk)("span",null,"我的团队",-1))]})),_:1,__:[10]}),e.isAdmin?((0,c.uX)(),(0,c.Wv)(u,{key:1,index:"/app/users",onClick:t[4]||(t[4]=function(t){return e.$router.push("/app/users")})},{default:(0,c.k6)((function(){return[(0,c.bF)(i,null,{default:(0,c.k6)((function(){return[(0,c.bF)(p)]})),_:1}),t[11]||(t[11]=(0,c.Lk)("span",null,"人员管理",-1))]})),_:1,__:[11]})):(0,c.Q3)("",!0)]})),_:1},8,["default-active"])]})),_:1,__:[12]}),(0,c.bF)(D,null,{default:(0,c.k6)((function(){return[(0,c.bF)(k,{height:"60px",class:"header"},{default:(0,c.k6)((function(){return[(0,c.Lk)("div",f,[o.showDashboardButton?((0,c.uX)(),(0,c.Wv)(w,{key:0,type:"primary",icon:"el-icon-s-home",onClick:t[5]||(t[5]=function(t){return e.$router.push("/app/dashboard")})},{default:(0,c.k6)((function(){return t[13]||(t[13]=[(0,c.eW)(" 返回工作台 ")])})),_:1,__:[13]})):(0,c.Q3)("",!0),o.showBackButton?((0,c.uX)(),(0,c.Wv)(w,{key:1,type:"info",icon:"el-icon-back",onClick:r.goBack},{default:(0,c.k6)((function(){return t[14]||(t[14]=[(0,c.eW)(" 返回上一步 ")])})),_:1,__:[14]},8,["onClick"])):(0,c.Q3)("",!0)]),(0,c.Lk)("div",h,[(0,c.bF)(y,null,{dropdown:(0,c.k6)((function(){return[(0,c.bF)(S,null,{default:(0,c.k6)((function(){return[(0,c.bF)(E,null,{default:(0,c.k6)((function(){return[(0,c.bF)(_,{to:"/app/profile",style:{"text-decoration":"none",color:"inherit"}},{default:(0,c.k6)((function(){return t[15]||(t[15]=[(0,c.eW)(" 个人中心 ")])})),_:1,__:[15]})]})),_:1}),e.isDoctor&&!o.hasAppliedForReviewer?((0,c.uX)(),(0,c.Wv)(E,{key:0,onClick:r.handleApplyForReviewer},{default:(0,c.k6)((function(){return t[16]||(t[16]=[(0,c.eW)(" 申请升级权限 ")])})),_:1,__:[16]},8,["onClick"])):(0,c.Q3)("",!0),e.isDoctor&&o.hasAppliedForReviewer?((0,c.uX)(),(0,c.Wv)(E,{key:1,disabled:""},{default:(0,c.k6)((function(){return t[17]||(t[17]=[(0,c.eW)(" 权限申请审核中 ")])})),_:1,__:[17]})):(0,c.Q3)("",!0),(0,c.bF)(E,{onClick:r.handleLogout},{default:(0,c.k6)((function(){return t[18]||(t[18]=[(0,c.eW)("退出登录")])})),_:1,__:[18]},8,["onClick"])]})),_:1})]})),default:(0,c.k6)((function(){return[(0,c.Lk)("span",v,[(0,c.bF)(b,{size:"small",icon:"el-icon-user"}),(0,c.eW)(" "+(0,c.v_)(o.username)+" ",1),(0,c.Lk)("span",{class:(0,c.C4)(["role-tag",r.roleTagClass])},(0,c.v_)(r.userRoleText),3)])]})),_:1})])]})),_:1}),(0,c.bF)(C,null,{default:(0,c.k6)((function(){return[(0,c.bF)(P)]})),_:1})]})),_:1})]})),_:1})}n(42762);var I=n(66278),w=n(48548),b=n(36149);const _={name:"MainLayout",components:{HomeFilled:w.HomeFilled,Document:w.Document,Check:w.Check,User:w.User,UserFilled:w.UserFilled,Picture:w.Picture},data:function(){return{username:"标注医生",showBackButton:!1,showDashboardButton:!1,hasAppliedForReviewer:!1,pollTimer:null}},computed:(0,a.A)((0,a.A)({},(0,I.L8)({currentUser:"getUser",isAdmin:"isAdmin",isDoctor:"isDoctor",isReviewer:"isReviewer",hasPendingApplications:"teamApplications/hasPendingApplications",pendingApplicationsCount:"teamApplications/getPendingApplicationsCount"})),{},{activeMenu:function(){return this.$route.path},userRoleText:function(){if(this.isAdmin)return"管理员";if(this.isDoctor)return"标注医生";if(this.isReviewer)return"审核医生";try{var e=JSON.parse(localStorage.getItem("user")||"{}");if(e&&e.role)switch(e.role){case"ADMIN":return"管理员";case"DOCTOR":return"标注医生";case"REVIEWER":return"审核医生";default:return"未知角色"}}catch(t){console.error("MainLayout: 获取用户角色失败:",t)}return"未知角色"},roleTagClass:function(){if(this.isAdmin)return"role-tag-admin";if(this.isReviewer)return"role-tag-reviewer";if(this.isDoctor)return"role-tag-doctor";try{var e=JSON.parse(localStorage.getItem("user")||"{}");if(e&&e.role)switch(e.role){case"ADMIN":return"role-tag-admin";case"REVIEWER":return"role-tag-reviewer";case"DOCTOR":return"role-tag-doctor";default:return""}}catch(t){console.error("MainLayout: 获取用户角色样式失败:",t)}return""}}),watch:{$route:function(e){if(this.showBackButton="/app/cases/structured-form"===e.path,this.showDashboardButton="/app/dashboard"!==e.path,"/app/dashboard"===e.path||"/app"===e.path||"/app/"===e.path){var t=JSON.parse(localStorage.getItem("user")||"{}"),n=t.customId||t.id,a=localStorage.getItem("lastActiveUserId");a&&a!==n&&(console.log("MainLayout: 检测到用户ID变更，从",a,"到",n),window.refreshDashboardStats&&(console.log("MainLayout: 用户变更，强制刷新统计数据"),setTimeout((function(){return window.refreshDashboardStats()}),0))),n&&localStorage.setItem("lastActiveUserId",n)}},username:function(e,t){e!==t&&e&&t&&(console.log("MainLayout: 用户名变更，从",t,"到",e),"/app/dashboard"!==this.$route.path&&"/app"!==this.$route.path&&"/app/"!==this.$route.path||window.refreshDashboardStats&&(console.log("MainLayout: 用户名变更，立即刷新统计数据"),setTimeout((function(){return window.refreshDashboardStats()}),0)))}},created:function(){this.loadUserInfo(),this.fetchTeamApplicationsCount()},mounted:function(){this.startPendingApplicationsPolling()},beforeUnmount:function(){this.stopPendingApplicationsPolling()},methods:{loadUserInfo:function(){var e=this;try{var t=JSON.parse(localStorage.getItem("user")||"{}");t&&t.name&&(this.username=t.name),this.isDoctor&&b["default"].users.getReviewerApplicationStatus().then((function(t){t.data&&"PENDING"===t.data.status&&(e.hasAppliedForReviewer=!0)}))["catch"]((function(e){return console.error("获取审核医生申请状态失败:",e)}))}catch(n){console.error("加载用户信息失败:",n)}},fetchTeamApplicationsCount:function(){this.$store.dispatch("teamApplications/fetchPendingApplicationsCount")},startPendingApplicationsPolling:function(){var e=this;this.pollTimer=setInterval((function(){e.fetchTeamApplicationsCount()}),3e5)},stopPendingApplicationsPolling:function(){this.pollTimer&&clearInterval(this.pollTimer)},handleLogout:function(){console.log("[登出操作] 用户请求登出，当前URL:",window.location.href),sessionStorage.removeItem("isAppOperation"),sessionStorage.removeItem("isNavigatingAfterSave"),sessionStorage.removeItem("returningToWorkbench"),sessionStorage.setItem("isLogoutOperation","true"),console.log("[登出操作] 已设置登出标记，准备清除用户会话"),localStorage.removeItem("user"),console.log("[登出操作] 导航到登录页"),this.$router.push("/login").then((function(){console.log("[登出操作] 导航到登录页成功")}))["catch"]((function(e){console.error("[登出操作] 导航失败:",e),window.location.href="/login"})),setTimeout((function(){console.log("[登出操作] 清除登出标记"),sessionStorage.removeItem("isLogoutOperation")}),3e3)},goBack:function(){var e=this;this.$confirm("返回上一页将可能丢失当前未保存的数据，是否确认返回？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$router.go(-1)}))["catch"]((function(){}))},checkReviewerApplication:function(){var e=this;this.isDoctor&&b["default"].users.getReviewerApplicationStatus().then((function(t){console.log("获取权限申请状态:",t.data),e.hasAppliedForReviewer=t.data&&t.data.length>0&&t.data.some((function(e){return"PENDING"===e.status}))}))["catch"]((function(e){console.error("获取权限申请状态失败:",e)}))},handleApplyForReviewer:function(){var e=this;this.$prompt("请输入申请理由","申请成为审核医生",{confirmButtonText:"提交申请",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"请详细说明您申请成为审核医生的理由..."}).then((function(t){var n=t.value;n&&""!==n.trim()?b["default"].users.applyForReviewer(n).then((function(){e.$message.success("申请已提交，请等待管理员审核"),e.hasAppliedForReviewer=!0}))["catch"]((function(t){t.response&&t.response.data&&t.response.data.message?e.$message.error(t.response.data.message):e.$message.error("申请提交失败，请稍后重试")})):e.$message.warning("申请理由不能为空")}))["catch"]((function(){}))},navigateToHemangiomaDiagnosis:function(){console.log("[导航] 准备导航到血管瘤诊断页面");try{this.$router.push("/app/hemangioma-diagnosis").then((function(){console.log("[导航] 成功导航到血管瘤诊断页面")}))["catch"]((function(e){console.error("[导航] 导航到血管瘤诊断页面失败:",e)}))}catch(e){console.error("[导航] 导航异常:",e)}},navigateToProfile:function(){console.log("[导航] 准备导航到个人中心页面"),window.location.href="/app/profile"}}},E=(0,d.A)(_,[["render",A]]),S=E;function y(e,t,n,a,o,r){var s=(0,c.g2)("router-view"),i=(0,c.g2)("el-main"),u=(0,c.g2)("el-container");return(0,c.uX)(),(0,c.Wv)(u,{class:"simple-layout"},{default:(0,c.k6)((function(){return[(0,c.bF)(i,null,{default:(0,c.k6)((function(){return[(0,c.bF)(s)]})),_:1})]})),_:1})}const k={name:"SimpleLayout"},P=(0,d.A)(k,[["render",y],["__scopeId","data-v-47aa494e"]]),C=P;var D={class:"dashboard"},T={class:"header-actions"},R={class:"stat-item"},O={class:"stat-value"},N={class:"stat-item"},L={class:"stat-value"},U={class:"stat-item"},F={class:"stat-value"},W={class:"stat-item"},x={class:"stat-value"},M={class:"quick-actions"},V={class:"action-content"},j={class:"action-content"};function B(e,t,n,a,o,r){var s=(0,c.g2)("Refresh"),i=(0,c.g2)("el-icon"),u=(0,c.g2)("el-button"),l=(0,c.g2)("el-col"),d=(0,c.g2)("el-card"),g=(0,c.g2)("el-row"),m=(0,c.g2)("UserFilled");return(0,c.uX)(),(0,c.CE)("div",D,[(0,c.bF)(g,{gutter:15,class:"stat-cards"},{default:(0,c.k6)((function(){return[(0,c.bF)(l,{xs:24,sm:24,md:24,lg:24,xl:24,style:{"margin-bottom":"15px"}},{default:(0,c.k6)((function(){return[(0,c.Lk)("div",T,[t[7]||(t[7]=(0,c.Lk)("h2",{style:{margin:"0"}},"工作台",-1)),(0,c.bF)(u,{type:"primary",size:"small",onClick:r.manualRefresh},{default:(0,c.k6)((function(){return[(0,c.bF)(i,null,{default:(0,c.k6)((function(){return[(0,c.bF)(s)]})),_:1}),t[6]||(t[6]=(0,c.eW)(" 刷新数据 "))]})),_:1,__:[6]},8,["onClick"])])]})),_:1}),(0,c.bF)(l,{xs:12,sm:8,md:6,lg:6,xl:6},{default:(0,c.k6)((function(){return[(0,c.bF)(d,{shadow:"hover",class:"clickable-card",onClick:t[0]||(t[0]=function(e){return r.navigateTo("/app/cases")})},{default:(0,c.k6)((function(){return[(0,c.Lk)("div",R,[t[8]||(t[8]=(0,c.Lk)("div",{class:"stat-title"},"病例总数",-1)),(0,c.Lk)("div",O,(0,c.v_)(o.dashboardStats.totalCount),1)])]})),_:1})]})),_:1}),(0,c.bF)(l,{xs:12,sm:8,md:6,lg:6,xl:6},{default:(0,c.k6)((function(){return[(0,c.bF)(d,{shadow:"hover",class:"clickable-card",onClick:t[1]||(t[1]=function(e){return r.navigateTo("/app/cases","REVIEWED")})},{default:(0,c.k6)((function(){return[(0,c.Lk)("div",N,[t[9]||(t[9]=(0,c.Lk)("div",{class:"stat-title"},"已标注",-1)),(0,c.Lk)("div",L,(0,c.v_)(o.dashboardStats.reviewedCount),1)])]})),_:1})]})),_:1}),(0,c.bF)(l,{xs:12,sm:8,md:6,lg:6,xl:6},{default:(0,c.k6)((function(){return[(0,c.bF)(d,{shadow:"hover",class:"clickable-card",onClick:t[2]||(t[2]=function(e){return r.navigateTo("/app/cases","SUBMITTED")})},{default:(0,c.k6)((function(){return[(0,c.Lk)("div",U,[t[10]||(t[10]=(0,c.Lk)("div",{class:"stat-title"},"待审核",-1)),(0,c.Lk)("div",F,(0,c.v_)(o.dashboardStats.submittedCount),1)])]})),_:1})]})),_:1}),(0,c.bF)(l,{xs:12,sm:8,md:6,lg:6,xl:6},{default:(0,c.k6)((function(){return[(0,c.bF)(d,{shadow:"hover",class:"clickable-card",onClick:t[3]||(t[3]=function(e){return r.navigateTo("/app/cases","APPROVED")})},{default:(0,c.k6)((function(){return[(0,c.Lk)("div",W,[t[11]||(t[11]=(0,c.Lk)("div",{class:"stat-title"},"已通过",-1)),(0,c.Lk)("div",x,(0,c.v_)(o.dashboardStats.approvedCount),1)])]})),_:1})]})),_:1})]})),_:1}),(0,c.Lk)("div",M,[t[16]||(t[16]=(0,c.Lk)("div",{class:"table-header"},[(0,c.Lk)("h2",null,[(0,c.Lk)("span",null,"快捷操作")])],-1)),(0,c.bF)(g,{gutter:20},{default:(0,c.k6)((function(){return[(0,c.bF)(l,{xs:24,sm:8,md:8,lg:8,xl:8},{default:(0,c.k6)((function(){return[(0,c.bF)(d,{shadow:"hover",class:"action-card",onClick:t[4]||(t[4]=function(e){return r.navigateTo("/app/hemangioma-diagnosis")})},{default:(0,c.k6)((function(){return[(0,c.Lk)("div",V,[(0,c.bF)(i,{class:"action-icon"},{default:(0,c.k6)((function(){return[(0,c.bF)(s)]})),_:1}),t[12]||(t[12]=(0,c.Lk)("div",{class:"action-title"},"血管瘤诊断",-1)),t[13]||(t[13]=(0,c.Lk)("div",{class:"action-desc"},"使用AI辅助诊断血管瘤",-1))])]})),_:1})]})),_:1}),(0,c.bF)(l,{xs:24,sm:8,md:8,lg:8,xl:8},{default:(0,c.k6)((function(){return[(0,c.bF)(d,{shadow:"hover",class:"action-card",onClick:t[5]||(t[5]=function(e){return r.navigateTo("/app/teams")})},{default:(0,c.k6)((function(){return[(0,c.Lk)("div",j,[(0,c.bF)(i,{class:"action-icon"},{default:(0,c.k6)((function(){return[(0,c.bF)(m)]})),_:1}),t[14]||(t[14]=(0,c.Lk)("div",{class:"action-title"},"团队标注",-1)),t[15]||(t[15]=(0,c.Lk)("div",{class:"action-desc"},"查看团队标注信息",-1))])]})),_:1})]})),_:1})]})),_:1})])])}var J=n(72505),X=n.n(J),q=n(81052),z={totalCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0};const $={name:"Dashboard",components:{Loading:w.Loading,Refresh:w.Refresh,Plus:w.Plus,UserFilled:w.UserFilled},data:function(){return{dashboardStats:(0,a.A)({},z)}},computed:(0,a.A)({},(0,I.L8)({currentUserId:"getUserId"})),watch:{$route:function(e,t){"/app/dashboard"===e.path&&(console.log("【路由切换】切换到 dashboard，重新加载数据"),this.manualRefresh())}},beforeRouteEnter:function(e,t,n){console.log("【路由前置】beforeRouteEnter"),n((function(e){console.log("【路由前置】进入组件，执行一次刷新"),e.manualRefresh()}))},created:function(){console.log("⭐⭐⭐ Dashboard组件 created: 组件被创建 ⭐⭐⭐"),this.dashboardStats={totalCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0}},mounted:function(){console.log("⭐⭐⭐ Dashboard组件 mounted: 组件被挂载 ⭐⭐⭐"),window.addEventListener("focus",this.handlePageFocus),console.log("焦点事件监听已设置")},activated:function(){console.log("【keep-alive】Dashboard组件被激活")},beforeUnmount:function(){console.log("⭐⭐⭐ Dashboard组件 beforeUnmount: 组件即将卸载 ⭐⭐⭐"),window.removeEventListener("focus",this.handlePageFocus),console.log("焦点事件监听已移除")},methods:(0,a.A)((0,a.A)({},(0,I.i0)(["resetState"])),{},{navigateTo:function(e,t){var n=t?{status:t}:{};localStorage.setItem("lastSelectedStatus",t||"ALL"),this.$router.push({path:e,query:n})},formatDate:function(e){return e?new Date(e).toLocaleString():""},handlePageFocus:function(){console.log("页面获得焦点，强制获取最新数据"),this.manualRefresh()},manualRefresh:function(){var e=this;return(0,r.A)((0,o.A)().m((function t(){var n,r,s,i,c,u,l,d;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:console.log("【手动刷新】开始...");try{n=e.currentUserId,n||(r=localStorage.getItem("user"),r&&(s=JSON.parse(r),n=s.id))}catch(o){console.error("解析用户信息失败:",o)}if(n){t.n=1;break}return console.error("无法获取有效的用户ID，可能用户未登录"),e.$message.error("无法获取用户ID，请重新登录"),t.a(2);case 1:return console.log("【手动刷新】使用用户ID:",n),i=e.$loading({lock:!0,text:"正在获取最新数据..."}),t.p=2,c=(0,q.XO)(n),t.n=3,fetch(c,{method:"GET",headers:{"Cache-Control":"no-cache"},credentials:"include"});case 3:if(u=t.v,u.ok){t.n=4;break}throw new Error("HTTP错误! 状态: ".concat(u.status));case 4:return t.n=5,u.json();case 5:l=t.v,console.log("【手动刷新】获取到的统计数据:",l),e.dashboardStats={totalCount:parseInt(l.totalCount||0),reviewedCount:parseInt(l.reviewedCount||0),submittedCount:parseInt(l.submittedCount||0),approvedCount:parseInt(l.approvedCount||0),rejectedCount:parseInt(l.rejectedCount||0)},z=(0,a.A)({},e.dashboardStats),e.$forceUpdate(),t.n=7;break;case 6:t.p=6,d=t.v,console.error("【手动刷新】失败:",d),e.$message.error("刷新失败: ".concat(d.message));case 7:return t.p=7,i.close(),t.f(7);case 8:return t.a(2)}}),t,null,[[2,6,7,8]])})))()}})},G=(0,d.A)($,[["render",B],["__scopeId","data-v-dcc088ae"]]),H=G;var K=n(11786),Q=(n(52675),n(89463),n(44633)),Y=n(68039),Z={user:Y.v.getFromStorage("user")||null,isAuthenticated:!!Y.v.getFromStorage("user"),users:[],currentUserDetails:null,loading:!1,error:null},ee={getUser:function(e){return e.user},getUserRole:function(e){return e.user?e.user.role:null},getUserId:function(e){return e.user?e.user.id:null},isAdmin:function(e){return e.user&&"ADMIN"===e.user.role},isDoctor:function(e){return e.user&&"DOCTOR"===e.user.role},isReviewer:function(e){return e.user&&"REVIEWER"===e.user.role},hasPermission:function(e){return function(t){var n;return(0,Q._m)(null===(n=e.user)||void 0===n?void 0:n.role,t)}},canAccessRoute:function(e){return function(t){var n;return(0,Q.kL)(null===(n=e.user)||void 0===n?void 0:n.role,t)}},canAccessResource:function(e){return function(t){return!!e.user&&(0,Q.X2)(e.user.id,t,e.user.role)}},getAllUsers:function(e){return e.users},getCurrentUserDetails:function(e){return e.currentUserDetails},isUsersLoading:function(e){return e.loading},getUsersError:function(e){return e.error}},te={setUser:function(e,t){e.user=t,e.isAuthenticated=!!t},setUsers:function(e,t){e.users=t},setCurrentUserDetails:function(e,t){e.currentUserDetails=t},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t}},ne={login:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s,i,c;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,Y.b.start(a),n.p=1,console.log("登录请求:",t),n.n=2,b["default"].auth.login(t);case 2:if(r=n.v,console.log("登录API原始响应:",r),s=r.data,console.log("登录成功，原始用户数据:",s),i={id:s.id,customId:s.customId,name:s.name,email:s.email,role:s.role,department:s.department,hospital:s.hospital},s.team)try{i.team={id:s.team.id,name:s.team.name,description:s.team.description}}catch(o){console.warn("提取team信息时出错:",o),i.team=null}else i.team=null;return console.log("处理后的用户数据:",i),console.log("用户信息详情 - ID:",i.id,"姓名:",i.name,"角色:",i.role,"customId:",i.customId),i.name||(console.warn("警告: 登录响应中缺少用户姓名!"),i.email?(i.name=i.email.split("@")[0],console.log("使用邮箱用户名作为默认名称:",i.name)):i.name="未命名用户"),i.role||(console.error("错误: 用户角色为空!"),i.customId&&i.customId.startsWith("1")?i.role="ADMIN":i.customId&&i.customId.startsWith("3")?i.role="REVIEWER":i.role="DOCTOR",console.log("已根据用户ID设置默认角色:",i.role)),Y.v.saveToStorage("user",i),a("setUser",i),n.a(2,i);case 3:return n.p=3,c=n.v,console.error("登录错误详情:",c),n.a(2,Y.b.error(a,c));case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},register:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,Y.b.start(a),n.p=1,n.n=2,b["default"].auth.register(t);case 2:return r=n.v,n.a(2,r.data);case 3:return n.p=3,s=n.v,n.a(2,Y.b.error(a,s));case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},logout:function(e){var t=e.commit;Y.v.removeFromStorage("user"),t("setUser",null)},fetchUserProfile:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,Y.b.start(a),n.p=1,n.n=2,b["default"].users.getUser(t);case 2:return r=n.v,a("setCurrentUserDetails",r.data),n.a(2,r.data);case 3:return n.p=3,s=n.v,n.a(2,Y.b.error(a,s));case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},updateUserProfile:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s,i,c;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,r=e.state,Y.b.start(a),n.p=1,n.n=2,b["default"].users.updateUser(r.user.id,t);case 2:return s=n.v,i=s.data,Y.v.saveToStorage("user",i),a("setUser",i),n.a(2,i);case 3:return n.p=3,c=n.v,n.a(2,Y.b.error(a,c));case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},fetchUsers:function(e){return(0,r.A)((0,o.A)().m((function t(){var n,a,r;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:return n=e.commit,Y.b.start(n),t.p=1,t.n=2,b["default"].users.getAll();case 2:return a=t.v,n("setUsers",a.data),t.a(2,a.data);case 3:return t.p=3,r=t.v,t.a(2,Y.b.error(n,r));case 4:return t.p=4,Y.b.end(n),t.f(4);case 5:return t.a(2)}}),t,null,[[1,3,4,5]])})))()}};const ae={state:Z,getters:ee,mutations:te,actions:ne};var oe=n(55593),re=(n(2008),n(48980),n(64346),n(34782),n(54554),n(13609),n(22489),n(16034),n(42207),n(55815),n(64979),n(79739),function(){var e=JSON.parse(localStorage.getItem("user"));return e?{Authorization:"Basic ".concat(btoa("".concat(e.email,":").concat(e.password)))}:{}}),se={images:[],currentImage:null,loading:!1,error:null},ie={getAllImages:function(e){return e.images},getCurrentImage:function(e){return e.currentImage},isImagesLoading:function(e){return e.loading},getImagesError:function(e){return e.error}},ce={setImages:function(e,t){e.images=t},setCurrentImage:function(e,t){e.currentImage=t},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t},addImage:function(e,t){e.images.unshift(t)},updateImage:function(e,t){var n=e.images.findIndex((function(e){return e.id===t.id}));-1!==n&&e.images.splice(n,1,t),e.currentImage&&e.currentImage.id===t.id&&(e.currentImage=t)},removeImage:function(e,t){e.images=e.images.filter((function(e){return e.id!==t})),e.currentImage&&e.currentImage.id===t&&(e.currentImage=null)}},ue={fetchImages:function(e){return(0,r.A)((0,o.A)().m((function t(){var n,a,r,s;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:return n=e.commit,Y.b.start(n),t.p=1,console.log("开始从API获取图像数据..."),t.n=2,X().get("".concat(q.H$,"/images"),{headers:re()});case 2:return a=t.v,console.log("API响应成功, 状态码:",a.status),r=[],Array.isArray(a.data)?(console.log("响应数据是数组，长度:",a.data.length),r=a.data,r.length>0&&console.log("数据样例:",r.slice(0,3))):"object"===(0,oe.A)(a.data)&&null!==a.data&&(console.log("响应不是数组，尝试提取数组数据"),r=Array.isArray(a.data.content)?a.data.content:Array.isArray(a.data.data)?a.data.data:Array.isArray(a.data.items)?a.data.items:Object.values(a.data).filter((function(e){return e&&"object"===(0,oe.A)(e)}))),console.log("处理后的图像数据数量:",r.length),n("setImages",r),t.a(2,r);case 3:throw t.p=3,s=t.v,console.error("获取图像数据失败:",s),Y.b.error(n,s),s;case 4:return t.p=4,Y.b.end(n),t.f(4);case 5:return t.a(2)}}),t,null,[[1,3,4,5]])})))()},fetchImagesByStatus:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s,i;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,Y.b.start(a),n.p=1,n.n=2,X().get("".concat(q.H$,"/images/status/").concat(t),{headers:re()});case 2:return r=n.v,s=Array.isArray(r.data)?r.data:[],a("setImages",s),n.a(2,s);case 3:throw n.p=3,i=n.v,Y.b.error(a,i),i;case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},fetchImageById:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,Y.b.start(a),n.p=1,n.n=2,X().get("".concat(q.H$,"/images/").concat(t),{headers:re()});case 2:return r=n.v,a("setCurrentImage",r.data),n.a(2,r.data);case 3:throw n.p=3,s=n.v,Y.b.error(a,s),s;case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},createImage:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,Y.b.start(a),n.p=1,n.n=2,X().post("".concat(q.H$,"/images"),t,{headers:re()});case 2:return r=n.v,a("addImage",r.data),n.a(2,r.data);case 3:throw n.p=3,s=n.v,Y.b.error(a,s),s;case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},updateImage:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s,i,c;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,r=t.id,s=t.imageData,Y.b.start(a),n.p=1,n.n=2,X().put("".concat(q.H$,"/images/").concat(r),s,{headers:re()});case 2:return i=n.v,a("updateImage",i.data),n.a(2,i.data);case 3:throw n.p=3,c=n.v,Y.b.error(a,c),c;case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},updateImageStatus:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s,i,c,u,l;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,r=t.id,s=t.status,i=t.reviewerId,c=t.reviewNotes,Y.b.start(a),n.p=1,n.n=2,X().put("".concat(q.H$,"/images/").concat(r,"/status"),null,{params:{status:s,reviewerId:i,reviewNotes:c},headers:re()});case 2:return n.n=3,X().get("".concat(q.H$,"/images/").concat(r),{headers:re()});case 3:return u=n.v,a("updateImage",u.data),n.a(2,u.data);case 4:throw n.p=4,l=n.v,Y.b.error(a,l),l;case 5:return n.p=5,Y.b.end(a),n.f(5);case 6:return n.a(2)}}),n,null,[[1,4,5,6]])})))()},deleteImage:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a,r,s;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.commit,Y.b.start(a),n.p=1,n.n=2,b["default"].images["delete"](t);case 2:return a("removeImage",t),n.a(2,{success:!0});case 3:return n.p=3,s=n.v,r="删除失败",s.response&&s.response.data?r="string"===typeof s.response.data?s.response.data:JSON.stringify(s.response.data):r+=": "+(s.message||"未知错误"),a("setError",r),n.a(2,{success:!1,error:r});case 4:return n.p=4,Y.b.end(a),n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},fetchStats:function(e){return(0,r.A)((0,o.A)().m((function t(){var n;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:return n=e.dispatch,e.rootState,e.commit,t.a(2,n("fetchStats",null,{root:!0}))}}),t)})))()}};const le={state:se,getters:ie,mutations:ce,actions:ue};var de={},ge={getAllUsers:function(e,t,n){return n.auth.users},getCurrentUser:function(e,t,n){return n.auth.currentUserDetails},isUsersLoading:function(e,t,n){return n.auth.loading},getUsersError:function(e,t,n){return n.auth.error}},me={},pe={fetchUsers:function(e){return(0,r.A)((0,o.A)().m((function t(){var n;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:return n=e.dispatch,t.a(2,n("auth/fetchUsers",null,{root:!0}))}}),t)})))()},fetchUser:function(e,t){return(0,r.A)((0,o.A)().m((function n(){var a;return(0,o.A)().w((function(n){while(1)switch(n.n){case 0:return a=e.dispatch,n.a(2,a("auth/fetchUserProfile",t,{root:!0}))}}),n)})))()}};const fe={state:de,getters:ge,mutations:me,actions:pe};var he={stats:{totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},loading:!1,error:null},ve={getStats:function(e){return e.stats},isStatsLoading:function(e){return e.loading},getStatsError:function(e){return e.error},getImagesStats:function(e){return e.stats}},Ae={setStats:function(e,t){console.log("Vuex setStats 被调用，原始数据:",t),e.stats={totalCount:parseInt(t.totalCount)||0,draftCount:parseInt(t.draftCount)||0,reviewedCount:parseInt(t.reviewedCount||t.pendingCount)||0,submittedCount:parseInt(t.submittedCount)||0,approvedCount:parseInt(t.approvedCount)||0,rejectedCount:parseInt(t.rejectedCount)||0},console.log("Vuex stats 更新为:",e.stats),Y.v.saveToStorage("dashboardStats",e.stats)},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t},setStatField:function(e,t){var n=t.field,a=t.value;e.stats.hasOwnProperty(n)?(e.stats[n]=parseInt(a)||0,console.log("更新统计字段 ".concat(n," = ").concat(e.stats[n]))):console.error("尝试设置未知统计字段: ".concat(n))}},Ie={fetchStats:function(e){return(0,r.A)((0,o.A)().m((function t(){var n,a,r,s,i,c,u;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:if(n=e.commit,Y.b.start(n),t.p=1,console.log("===== Vuex fetchStats Action 开始执行 ====="),a=null,r=Y.v.getFromStorage("user"),r&&(a=r.id),a){t.n=2;break}throw new Error("未在localStorage中找到用户ID");case 2:return console.log("使用的用户ID:",a),console.log("开始调用API获取仪表盘统计数据"),t.n=3,b["default"].stats.getDashboard(a);case 3:if(s=t.v,console.log("API响应对象:",s),console.log("API响应状态码:",s.status),console.log("API响应数据类型:",(0,oe.A)(s.data)),console.log("API响应数据:",s.data),!s||!s.data){t.n=4;break}return i=s.data,console.log("统计字段详情:"),console.log("- totalCount:",i.totalCount,(0,oe.A)(i.totalCount)),console.log("- draftCount:",i.draftCount,(0,oe.A)(i.draftCount)),console.log("- reviewedCount:",i.reviewedCount,(0,oe.A)(i.reviewedCount)),console.log("- submittedCount:",i.submittedCount,(0,oe.A)(i.submittedCount)),console.log("- approvedCount:",i.approvedCount,(0,oe.A)(i.approvedCount)),console.log("- rejectedCount:",i.rejectedCount,(0,oe.A)(i.rejectedCount)),console.log("准备调用setStats mutation"),n("setStats",s.data),console.log("setStats mutation执行完毕"),t.a(2,s.data);case 4:throw new Error("响应中没有数据");case 5:t.n=7;break;case 6:t.p=6,u=t.v,console.error("获取统计数据失败:",u),Y.b.error(n,u),c={totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},console.log("由于错误，设置默认统计数据:",c),n("setStats",c);case 7:return t.p=7,console.log("===== Vuex fetchStats Action 执行完毕 ====="),Y.b.end(n),t.f(7);case 8:return t.a(2)}}),t,null,[[1,6,7,8]])})))()},setManualStats:function(e,t){var n=e.commit;console.log("手动设置统计数据:",t),n("setStats",t)}};const we={state:he,getters:ve,mutations:Ae,actions:Ie};n(51629),n(62062),n(1688),n(7588),n(61701),n(23500);var be={step:0,imageId:null,formData:null,testMode:!1,imagePath:null,lastUpdated:null},_e={getAnnotationProgress:function(e){return{step:e.step,imageId:e.imageId,formData:e.formData,testMode:e.testMode,imagePath:e.imagePath,lastUpdated:e.lastUpdated}},hasUnfinishedAnnotation:function(e){return e.step>0&&null!==e.imageId},getCurrentStep:function(e){return e.step},getImageId:function(e){return e.imageId},getFormData:function(e){return e.formData},isTestMode:function(e){return e.testMode},getImagePath:function(e){return e.imagePath},getStructuredFormProgress:function(e){return{formData:e.formData}}},Ee={saveProgress:function(e,t){var n=e.commit;n("SET_PROGRESS",t)},completeAnnotation:function(e){var t=e.commit;t("COMPLETE_ANNOTATION")},updateFormData:function(e,t){var n=e.commit;n("UPDATE_FORM_DATA",t)},saveAnnotationProgress:function(e,t){var n=e.commit,a=t.step,o=t.imageId,r=t.formData;n("SET_PROGRESS",{step:a,imageId:o,formData:r})},clearAnnotationProgress:function(e){var t=e.commit;t("COMPLETE_ANNOTATION")}},Se={SET_PROGRESS:function(e,t){if(e.step=t.step||e.step,e.imageId=t.imageId||e.imageId,t.formData){var n=null;t.formData&&(n={},Object.keys(t.formData).forEach((function(e){var a=t.formData[e];if("object"!==(0,oe.A)(a)||null===a)n[e]=a;else if(Array.isArray(a))n[e]=a.map((function(e){return"string"===typeof e?e:JSON.stringify(e)}));else try{n[e]=JSON.stringify(a)}catch(o){console.warn("无法序列化表单数据字段:",e)}}))),e.formData=n||t.formData}if("undefined"!==typeof t.testMode&&(e.testMode=t.testMode),t.imagePath&&(e.imagePath=t.imagePath),(t.testMode||"true"===localStorage.getItem("offlineMode"))&&t.imageId)try{Y.v.saveToStorage("offline_progress_".concat(t.imageId),{step:e.step,formData:e.formData,testMode:e.testMode,imagePath:e.imagePath})}catch(a){console.error("保存离线进度失败",a)}e.lastUpdated=(new Date).toISOString(),console.log("保存标注进度:",e)},UPDATE_FORM_DATA:function(e,t){if(e.formData=t,e.lastUpdated=(new Date).toISOString(),(e.testMode||"true"===localStorage.getItem("offlineMode"))&&e.imageId)try{Y.v.saveToStorage("offline_form_".concat(e.imageId),t),Y.v.saveToStorage("offline_progress_".concat(e.imageId),{step:e.step,formData:t,testMode:e.testMode,imagePath:e.imagePath})}catch(n){console.error("保存离线表单数据失败",n)}},COMPLETE_ANNOTATION:function(e){if(e.imageId)try{Y.v.removeFromStorage("offline_progress_".concat(e.imageId))}catch(t){console.error("清除离线进度失败",t)}e.step=0,e.imageId=null,e.formData=null,e.testMode=!1,e.imagePath=null,e.lastUpdated=(new Date).toISOString(),console.log("清除标注进度")}};const ye={state:be,getters:_e,actions:Ee,mutations:Se};var ke={pendingCount:0,lastUpdated:null},Pe={getPendingApplicationsCount:function(e){return e.pendingCount},hasPendingApplications:function(e){return e.pendingCount>0},getLastUpdated:function(e){return e.lastUpdated}},Ce={setPendingCount:function(e,t){e.pendingCount=t,e.lastUpdated=(new Date).toISOString()},incrementPendingCount:function(e){e.pendingCount++,e.lastUpdated=(new Date).toISOString()},decrementPendingCount:function(e){e.pendingCount>0&&(e.pendingCount--,e.lastUpdated=(new Date).toISOString())},resetPendingCount:function(e){e.pendingCount=0,e.lastUpdated=(new Date).toISOString()}},De={fetchPendingApplicationsCount:function(e){return(0,r.A)((0,o.A)().m((function t(){var n,a,r,s,i,c,u;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:if(n=e.commit,a=e.rootState,t.p=1,s=a.auth.user||JSON.parse(localStorage.getItem("user")||"{}"),i=null===(r=s.team)||void 0===r?void 0:r.id,i){t.n=2;break}return console.log("用户不属于任何团队，无需获取团队申请数量"),n("resetPendingCount"),t.a(2);case 2:return t.n=3,b["default"].teams.getTeamApplications(i,"PENDING");case 3:c=t.v,c&&c.data?(n("setPendingCount",c.data.length),console.log("团队".concat(i,"有").concat(c.data.length,"条待处理申请"))):n("resetPendingCount"),t.n=5;break;case 4:t.p=4,u=t.v,console.error("获取团队申请数量失败:",u),n("resetPendingCount");case 5:return t.a(2)}}),t,null,[[1,4]])})))()},processApplication:function(e,t){var n=e.commit,a=t.action;"APPROVED"!==a&&"REJECTED"!==a||n("decrementPendingCount")}};const Te={namespaced:!0,state:ke,getters:Pe,actions:De,mutations:Ce},Re=(0,I.y$)({state:{},getters:{isAuthenticated:function(e){return e.auth.isAuthenticated}},mutations:{saveAnnotationProgress:function(e,t){},clearAnnotationProgress:function(e){}},actions:{saveProgress:function(e,t){var n=e.dispatch;return n("annotation/saveAnnotationProgress",t)},completeAnnotation:function(e){var t=e.dispatch;return t("annotation/clearAnnotationProgress")}},modules:{auth:ae,images:le,users:fe,stats:we,annotation:ye,teamApplications:Te},plugins:[(0,K.A)({paths:["auth","annotation","teamApplications"],storage:{getItem:function(e){try{return localStorage.getItem(e)}catch(t){return console.error("localStorage getItem error:",t),null}},setItem:function(e,t){try{t&&t.length>1e6&&(console.warn("数据过大，尝试清理localStorage"),localStorage.removeItem("vuex")),localStorage.setItem(e,t)}catch(o){console.error("localStorage setItem error:",o);try{for(var n=0;n<localStorage.length;n++){var a=localStorage.key(n);"user"!==a&&"token"!==a&&localStorage.removeItem(a)}localStorage.setItem(e,t)}catch(r){console.error("无法保存数据到localStorage，即使在清理后:",r)}}},removeItem:function(e){try{localStorage.removeItem(e)}catch(t){console.error("localStorage removeItem error:",t)}}}})]});var Oe=function(){return n.e(110).then(n.bind(n,86110))},Ne=function(){return n.e(763).then(n.bind(n,59763))},Le=[{path:"/app",component:S,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"Dashboard",component:H,meta:{keepAlive:!0,title:"工作台"}},{path:"cases",name:"Cases",component:function(){return n.e(52).then(n.bind(n,23052))}},{path:"cases/view/:id",name:"ViewCase",component:function(){return n.e(301).then(n.bind(n,54301))}},{path:"case/:id/annotate-and-form",name:"AnnotationAndForm",component:function(){return n.e(751).then(n.bind(n,88751))},props:!0,children:[{path:"",name:"EmbeddedForm",component:function(){return n.e(556).then(n.bind(n,44556))},props:!0}]},{path:"review",name:"Review",component:function(){return n.e(282).then(n.bind(n,90282))}},{path:"teams",name:"Teams",component:function(){return Promise.all([n.e(86),n.e(669)]).then(n.bind(n,73904))}},{path:"teams/applications",name:"TeamApplications",component:function(){return Promise.all([n.e(86),n.e(838)]).then(n.bind(n,29086))},meta:{requiresAuth:!0,title:"团队申请管理",accessRoles:["ADMIN","REVIEWER"]}},{path:"users",name:"Users",component:function(){return n.e(344).then(n.bind(n,49344))},meta:{requiresAdmin:!0,title:"部门成员"}},{path:"hemangioma-diagnosis",name:"HemangiomaDiagnosis",component:Ne,meta:{requiresAuth:!0,title:"血管瘤诊断",accessRoles:["ADMIN","DOCTOR","REVIEWER"]}},{path:"annotation-reviews",name:"AnnotationReviews",component:function(){return n.e(402).then(n.bind(n,83402))},meta:{requiresAuth:!0,title:"标注审核",accessRoles:["ADMIN","REVIEWER"]}},{path:"profile",name:"UserProfile",component:function(){return n.e(80).then(n.bind(n,7080))},meta:{requiresAuth:!0,title:"个人中心",accessRoles:["ADMIN","DOCTOR","REVIEWER"]}}]},{path:"/",name:"Root",redirect:"/login"},{path:"/login",name:"Login",component:function(){return n.e(359).then(n.bind(n,26359))}},{path:"/forgot-password",name:"ForgotPassword",component:function(){return n.e(976).then(n.bind(n,30976))}},{path:"/register",name:"Register",component:function(){return n.e(989).then(n.bind(n,7989))}},{path:"/images",name:"ImageList",component:function(){return n.e(35).then(n.bind(n,6035))},meta:{requiresAuth:!0}},{path:"/images/:id",name:"ImageDetail",component:function(){return n.e(453).then(n.bind(n,42453))},meta:{requiresAuth:!0}},{path:"/annotations",name:"ImageAnnotation",component:function(){return n.e(751).then(n.bind(n,88751))}},{path:"/cases/structured-form",name:"PublicCaseStructuredForm",component:function(){return n.e(556).then(n.bind(n,44556))}},{path:"/tag-test",name:"TagTest",component:function(){return n.e(191).then(n.bind(n,37191))}},{path:"/admin",name:"Admin",component:function(){return n.e(381).then(n.bind(n,3381))},meta:{requiresAuth:!0,requiresAdmin:!0}},{path:"/admin/reviewer-applications",name:"ReviewerApplications",component:function(){return n.e(9).then(n.bind(n,84009))},meta:{requiresAuth:!0,requiresAdmin:!0}},{path:"/standalone-review/:id",component:C,children:[{path:"",name:"ReviewStandalone",component:function(){return n.e(402).then(n.bind(n,83402))},props:!0}],meta:{requiresAuth:!0,title:"标注审核",accessRoles:["ADMIN","REVIEWER"]}},{path:"/app/debug",name:"debug",component:Oe,meta:{requiresAuth:!0,title:"标注调试工具"}},{path:"/combined-view/:caseId",name:"CombinedView",component:function(){return n.e(556).then(n.bind(n,44556))},props:!0},{path:"/:pathMatch(.*)*",redirect:"/login"}],Ue=(0,p.aE)({history:(0,p.LA)("/"),routes:Le});Ue.beforeEach((function(e,t,n){console.log("[路由导航] 导航开始:",{从:t.fullPath,到:e.fullPath,时间:(new Date).toLocaleTimeString(),query参数:e.query,来源页面:document.referrer}),t.name&&sessionStorage.setItem("isAppOperation","true");var a="true"===sessionStorage.getItem("isNavigatingAfterSave"),o="true"===sessionStorage.getItem("navigatingFromForm");if((a||o)&&"/login"===e.path)return console.log("检测到从表单页面保存后导航到登录页面，重定向到工作台"),sessionStorage.removeItem("isNavigatingAfterSave"),sessionStorage.removeItem("navigatingFromForm"),n("/app/dashboard");var r=["/login","/register","/tag-test","/forgot-password"],s=!r.includes(e.path)&&!e.matched.some((function(e){return r.includes(e.path)})),i=JSON.parse(localStorage.getItem("user")),c=Re.getters.isAuthenticated;if("/login"===e.path||c||sessionStorage.setItem("redirectPath",e.fullPath),"/app/dashboard"!==e.path&&"/app"!==e.path&&"/app/"!==e.path||i&&window.refreshDashboardStats&&console.log("[路由导航] 检测到前往工作台，预先刷新统计数据"),s&&!i)return n("/login");var u=e.matched.some((function(e){return e.meta.requiresAuth}));if(u&&!c)return t.path.includes("/cases/structured-form")&&"true"===sessionStorage.getItem("allowFormOperation")?(console.log("表单操作中，即使未认证也允许完成此次导航"),sessionStorage.removeItem("allowFormOperation"),n("/app/dashboard")):n("/login");var l=i?i.role:null;if(u&&l){console.log("[路由导航] 权限检查:",{目标路由:e.path,用户角色:l,路由权限要求:e.meta.accessRoles||"无特定角色要求",是否需要管理员:e.matched.some((function(e){return e.meta.requiresAdmin})),是否需要审核医生:e.matched.some((function(e){return e.meta.requiresReviewer})),是否需要标注医生:e.matched.some((function(e){return e.meta.requiresDoctor}))});var d=e.matched.some((function(e){return e.meta.requiresAdmin})),g=e.matched.some((function(e){return e.meta.requiresReviewer})),m=e.matched.some((function(e){return e.meta.requiresDoctor}));if(d&&"ADMIN"!==l||g&&"REVIEWER"!==l&&"ADMIN"!==l||m&&"DOCTOR"!==l&&"ADMIN"!==l)return console.warn("[路由导航] 权限检查失败: 用户角色 ".concat(l," 无权访问 ").concat(e.path,", 重定向到工作台")),n("/app/dashboard");var p=(0,Q.kL)(l,e.path);if(console.log("[路由导航] 权限配置检查: 用户角色 ".concat(l," ").concat(p?"有权":"无权","访问 ").concat(e.path)),!p)return console.warn("[路由导航] 权限配置检查失败: 用户角色 ".concat(l," 无权访问 ").concat(e.path,", 重定向到工作台")),n("/app/dashboard")}return"/dashboard"===e.path?n("/app/dashboard"):"/cases"===e.path?n("/app/cases"):void n()})),Ue.afterEach((function(e,t){if(console.log("[路由导航] 导航完成:",{从:t.fullPath,到:e.fullPath,时间:(new Date).toLocaleTimeString(),当前URL:window.location.href,导航状态:{isAppOperation:sessionStorage.getItem("isAppOperation"),isNavigatingAfterSave:sessionStorage.getItem("isNavigatingAfterSave"),returningToWorkbench:sessionStorage.getItem("returningToWorkbench"),isLogoutOperation:sessionStorage.getItem("isLogoutOperation")}}),sessionStorage.setItem("isAppOperation","true"),"/login"!==Ue.currentRoute.value.path&&sessionStorage.setItem("isAppOperation","true"),"/app/dashboard"===e.path||"/app/"===e.path||"/app"===e.path){console.log("[路由导航] 检测到进入工作台页面，立即执行统计数据获取");try{var n=localStorage.getItem("user");if(!n)return;var a=JSON.parse(n),o=a.id;if(!o)return;var r=new XMLHttpRequest;if(r.open("GET","".concat(q.fz,"/").concat(o,"?t=").concat(Date.now(),"&forcePersonalStats=true&view=personal"),!1),r.send(),200===r.status){console.log("[路由导航] 统计数据获取成功");var s=JSON.parse(r.responseText);window.dashboardStats={totalCount:parseInt(s.totalCount||0),draftCount:parseInt(s.draftCount||0),reviewedCount:parseInt(s.reviewedCount||0),submittedCount:parseInt(s.submittedCount||0),approvedCount:parseInt(s.approvedCount||0),rejectedCount:parseInt(s.rejectedCount||0),dataSource:"navigation_xhr",timestamp:Date.now()},localStorage.setItem("dashboardStats",JSON.stringify(window.dashboardStats)),setTimeout((function(){var e=document.querySelectorAll(".stat-value");e&&e.length>=5&&(e[0].textContent=s.totalCount||0,e[1].textContent=s.draftCount||0,e[2].textContent=s.reviewedCount||0,e[3].textContent=s.submittedCount||0,e[4].textContent=s.approvedCount||0,console.log("[路由导航] 统计数据DOM已更新"))}),100)}}catch(i){console.error("[路由导航] 获取统计数据失败:",i)}}}));const Fe=Ue;var We=n(59384),xe=(n(36271),n(76005)),Me=n(80142),Ve=n(93793),je=n(98351),Be=n(16653),Je=(n(88431),n(81148),{mounted:function(e,t){var n=t.value,a=t.arg,o=Re.getters.getUserRole;if(console.log("权限指令检查:",{权限值:n,参数:a,用户角色:o}),o)if("role"!==a)if(Array.isArray(n))if("all"===a){var r=n.every((function(e){return(0,Q._m)(o,e)}));r||Xe(e)}else{var s=n.some((function(e){return(0,Q._m)(o,e)}));s||Xe(e)}else{var i=(0,Q._m)(o,n);console.log("权限检查结果: ".concat(n," -> ").concat(i?"有权限":"无权限")),i||Xe(e)}else{var c=Array.isArray(n)?n:[n];c.includes(o)||Xe(e)}else Xe(e)}});function Xe(e){e.style.display="none",e.setAttribute("disabled","disabled"),e.style.pointerEvents="none",e.setAttribute("data-permission-disabled","true"),e.classList.add("permission-disabled")}function qe(e){e.directive("permission",Je)}var ze=n(25069);window.VueComponents=window.VueComponents||{},window.VueComponents.AnnotationReviewList=ze.A;ze.A;var $e=n(69018),Ge=n(49071);Me.nk.success,Me.nk.error,Me.nk.info,Me.nk.warning;Me.nk.success=function(){},Me.nk.error=function(){},Me.nk.info=function(){},Me.nk.warning=function(){},window.fixUserPermission=function(){try{var e=localStorage.getItem("user");if(!e)return{success:!1,message:"未找到已登录用户"};var t=JSON.parse(e);if(t.role){var n=null;return t.customId&&t.customId.startsWith("1")?n="ADMIN":t.customId&&t.customId.startsWith("3")?n="REVIEWER":t.customId&&t.customId.startsWith("2")&&(n="DOCTOR"),n&&t.role!==n?(t.role=n,localStorage.setItem("user",JSON.stringify(t)),Re.commit("setUser",t),{success:!0,message:"已将用户角色修复为".concat(n)}):{success:!0,message:"用户角色已存在: ".concat(t.role)}}return t.customId&&t.customId.startsWith("1")?t.role="ADMIN":t.customId&&t.customId.startsWith("3")?t.role="REVIEWER":t.role="DOCTOR",localStorage.setItem("user",JSON.stringify(t)),Re.commit("setUser",t),{success:!0,message:"已修复用户角色为: ".concat(t.role)}}catch(a){return{success:!1,message:"修复失败: ".concat(a.message)}}};var He=localStorage.setItem,Ke=localStorage.removeItem;localStorage.setItem=function(e,t){He.call(this,e,t)},localStorage.removeItem=function(e){Ke.call(this,e)},X().defaults.withCredentials=!0,X().defaults.headers.common["Content-Type"]="application/json",X().defaults.headers.common["Accept"]="application/json",X().defaults.timeout=1e4,X().interceptors.request.use((function(e){return e}),(function(e){return Promise.reject(e)})),X().interceptors.response.use((function(e){return e}),(function(e){return Promise.reject(e)}));var Qe=(0,c.Ef)(m);Qe.config.errorHandler=function(e,t,n){},window.addEventListener("unhandledrejection",(function(e){}));for(var Ye=0,Ze=Object.entries(w);Ye<Ze.length;Ye++){var et=(0,s.A)(Ze[Ye],2),tt=et[0],nt=et[1];Qe.component(tt,nt)}Qe.config.globalProperties.$axios=X(),Qe.config.globalProperties.$message=Me.nk,Qe.config.globalProperties.$notify=Ve.df,Qe.config.globalProperties.$msgbox=je.s,Qe.config.globalProperties.$alert=je.s.alert,Qe.config.globalProperties.$confirm=je.s.confirm,Qe.config.globalProperties.$prompt=je.s.prompt,Qe.config.globalProperties.$loading=Be.Ks.service,qe(Qe),Qe.use(Re),Qe.use(Fe),Qe.use(We.A,{locale:xe.A}),window.$message=Me.nk,window.refreshDashboardStats=(0,r.A)((0,o.A)().m((function e(){var t,n,a,r,s,i;return(0,o.A)().w((function(e){while(1)switch(e.n){case 0:if(console.log("全局刷新函数 window.refreshDashboardStats 被调用"),e.p=1,t=localStorage.getItem("user"),t){e.n=2;break}return e.a(2);case 2:if(n=JSON.parse(t),a=n.id,a){e.n=3;break}return e.a(2);case 3:return r=(0,q.XO)(a),console.log("全局刷新函数调用的URL:",r),e.n=4,X().get(r,{headers:{"Cache-Control":"no-cache"}});case 4:s=e.v,s.data&&(Re.commit("setDashboardStats",s.data),console.log("全局统计数据已刷新并存入store")),e.n=6;break;case 5:e.p=5,i=e.v,console.error("全局刷新仪表盘数据失败:",i);case 6:return e.a(2)}}),e,null,[[1,5]])})));var at=function(){"undefined"!==typeof window&&(window.console=(0,a.A)((0,a.A)({},console),{},{log:function(){},info:function(){},warn:function(){},error:function(){},debug:function(){}}))};Qe.mount("#app"),at(),setTimeout((function(){console.log("🔄 应用挂载后尝试强制更新统计数据");try{var e=JSON.parse(localStorage.getItem("dashboardStats")||"{}");if(e&&e.totalCount){console.log("找到统计数据:",e);var t=document.querySelectorAll(".stat-value");t&&t.length>=5?(t[0].textContent=e.totalCount||0,t[1].textContent=e.draftCount||0,t[2].textContent=e.reviewedCount||0,t[3].textContent=e.submittedCount||0,t[4].textContent=e.approvedCount||0,console.log("📊 统计数据DOM元素已强制更新!")):(console.log("未找到统计卡片DOM元素:",t?t.length:0),setTimeout((function(){var t=document.querySelectorAll(".stat-value");t&&t.length>=5&&(t[0].textContent=e.totalCount||0,t[1].textContent=e.draftCount||0,t[2].textContent=e.reviewedCount||0,t[3].textContent=e.submittedCount||0,t[4].textContent=e.approvedCount||0,console.log("📊 第二次尝试：统计数据DOM元素已强制更新!"))}),300))}else{console.log("localStorage中没有找到统计数据，尝试直接获取");var a=n(72505)["default"];try{var o=localStorage.getItem("user")||"{}",r=JSON.parse(o),s=r.id;if(!s)return void console.log("找不到有效的用户ID，跳过数据获取");console.log("尝试获取用户",s,"的统计数据"),a.get("".concat(q.JR).concat(q.T_,"/api/stats-v2/dashboard/").concat(s,"?t=").concat(Date.now(),"&r=").concat(Math.random(),"&forcePersonalStats=true&view=personal"),{headers:{"Cache-Control":"no-cache, no-store",Pragma:"no-cache",Expires:"0"}}).then((function(e){console.log("直接获取统计数据成功:",e.data);var t=document.querySelectorAll(".stat-value");t&&t.length>=5&&(t[0].textContent=e.data.totalCount||0,t[1].textContent=e.data.draftCount||0,t[2].textContent=e.data.reviewedCount||0,t[3].textContent=e.data.submittedCount||0,t[4].textContent=e.data.approvedCount||0,console.log("📊 通过API获取的统计数据已更新到DOM!"))}))["catch"]((function(e){console.error("直接获取统计数据失败:",e)}))}catch(i){console.error("获取用户ID失败:",i)}}}catch(i){console.error("强制更新统计数据失败:",i)}}),500);var ot=history.pushState,rt=history.replaceState;history.pushState=function(){console.log("[URL变更] history.pushState 调用:",{状态:arguments[0],标题:arguments[1],URL:arguments[2],当前时间:(new Date).toLocaleTimeString(),调用方法:"直接调用"});var e=(new Error).stack;return console.log("[URL变更] pushState调用堆栈:",e),ot.apply(this,arguments)},history.replaceState=function(){console.log("[URL变更] history.replaceState 调用:",{状态:arguments[0],标题:arguments[1],URL:arguments[2],当前时间:(new Date).toLocaleTimeString(),调用方法:"直接调用"});var e=(new Error).stack;return console.log("[URL变更] replaceState调用堆栈:",e),rt.apply(this,arguments)},window.addEventListener("popstate",(function(e){console.log("[URL变更] popstate事件触发:",{状态:e.state,当前URL:window.location.href,当前时间:(new Date).toLocaleTimeString(),调用方法:"popstate事件"})}));var st=(0,Ge.A)();window.eventBus=st,Qe.config.globalProperties.$enableAnnotationDebug=function(){(0,$e.AW)(),Qe.config.globalProperties.$isAnnotationDebugEnabled=!0},document.addEventListener("DOMContentLoaded",(function(){setTimeout((function(){if(window.location.href.includes("annotation")||window.location.href.includes("case")||"true"===localStorage.getItem("enableDebug"))try{(0,$e.AW)(),console.log("标注调试模式已自动启用")}catch(e){console.error("启用调试模式失败",e)}}),1e3)}))},25069:(e,t,n)=>{"use strict";n.d(t,{A:()=>B});var a=n(55593),o=(n(62010),n(61431)),r={key:0},s={key:0,style:{"margin-left":"10px"}},i={class:"filters"},c={key:0},u={key:1},l={key:2},d={key:3},g={key:4},m={class:"pagination-container"},p={key:1,class:"annotation-detail-view"},f={class:"top-actions"},h={key:0,class:"annotation-detail"},v={class:"section"},A={class:"image-container"},I={class:"image-error"},w={key:1,class:"no-image"},b={class:"section"},_={class:"section"},E={class:"section"},S={class:"section"},y={key:0,class:"approved-notice"},k={key:1,class:"review-notes"},P={class:"action-buttons"},C={class:"dialog-footer"};function D(e,t,n,D,T,R){var O=(0,o.g2)("el-tag"),N=(0,o.g2)("el-alert"),L=(0,o.g2)("el-input"),U=(0,o.g2)("el-option"),F=(0,o.g2)("el-select"),W=(0,o.g2)("el-table-column"),x=(0,o.g2)("el-tooltip"),M=(0,o.g2)("el-button"),V=(0,o.g2)("el-table"),j=(0,o.g2)("el-pagination"),B=(0,o.g2)("el-image"),J=(0,o.g2)("el-descriptions-item"),X=(0,o.g2)("el-descriptions"),q=(0,o.g2)("el-form-item"),z=(0,o.g2)("el-form"),$=(0,o.g2)("el-dialog"),G=(0,o.gN)("loading");return(0,o.uX)(),(0,o.CE)("div",{class:(0,o.C4)(["annotation-review-list",{"standalone-mode":D.isStandalone}])},[D.isViewingDetail?((0,o.uX)(),(0,o.CE)("div",p,[(0,o.Lk)("div",f,[(0,o.bF)(M,{type:"primary",size:"medium",class:"back-button",onClick:D.backToList},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.isStandalone?"返回列表":"返回标注列表"),1)]})),_:1},8,["onClick"])]),t[26]||(t[26]=(0,o.Lk)("div",{class:"detail-title"},[(0,o.Lk)("h2",null,"病例详情")],-1)),D.selectedAnnotation?((0,o.uX)(),(0,o.CE)("div",h,[(0,o.Lk)("div",v,[t[15]||(t[15]=(0,o.Lk)("h3",null,"病变标注图像",-1)),(0,o.Lk)("div",A,[D.selectedAnnotation&&(D.selectedAnnotation.image_two_path||D.selectedAnnotation.processedImagePath||D.selectedAnnotation.imagePath)?((0,o.uX)(),(0,o.Wv)(B,{key:0,src:D.getFullImageUrl(D.selectedAnnotation.image_two_path||D.selectedAnnotation.processedImagePath||D.selectedAnnotation.imagePath),"preview-src-list":[],fit:"contain",style:{"max-width":"350px","max-height":"350px",width:"auto",height:"auto"},"z-index":9999,"preview-teleported":!1,"initial-index":0,"hide-on-click-modal":""},{error:(0,o.k6)((function(){return[(0,o.Lk)("div",I,[t[12]||(t[12]=(0,o.Lk)("i",{class:"el-icon-picture-outline"},null,-1)),t[13]||(t[13]=(0,o.Lk)("p",null,"无法加载图像",-1)),(0,o.Lk)("small",null,"路径: "+(0,o.v_)(D.selectedAnnotation.image_two_path||D.selectedAnnotation.processedImagePath||D.selectedAnnotation.imagePath),1)])]})),_:1},8,["src"])):((0,o.uX)(),(0,o.CE)("div",w,t[14]||(t[14]=[(0,o.Lk)("i",{class:"el-icon-picture-outline"},null,-1),(0,o.Lk)("p",null,"该标注无图像",-1)])))])]),(0,o.Lk)("div",b,[t[17]||(t[17]=(0,o.Lk)("h3",null,"基本信息",-1)),(0,o.bF)(X,{column:2,border:""},{default:(0,o.k6)((function(){return[(0,o.bF)(J,{label:"ID"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.formattedId||D.selectedAnnotation.id),1)]})),_:1}),(0,o.bF)(J,{label:"标注状态"},{default:(0,o.k6)((function(){return[(0,o.bF)(O,{type:D.getStatusTagType(D.selectedAnnotation.status)},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.getStatusDisplayText(D.selectedAnnotation.status)),1)]})),_:1},8,["type"])]})),_:1}),(0,o.bF)(J,{label:"标注医生"},{default:(0,o.k6)((function(){var e;return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.user&&D.selectedAnnotation.user.name||D.selectedAnnotation.submittedBy&&D.selectedAnnotation.submittedBy.name||D.getDoctorNameById((null===(e=D.selectedAnnotation.user)||void 0===e?void 0:e.id)||D.selectedAnnotation.submittedBy)||"未知"),1)]})),_:1}),(0,o.bF)(J,{label:"所属团队"},{default:(0,o.k6)((function(){return[D.selectedAnnotation.user&&D.selectedAnnotation.user.team&&D.selectedAnnotation.user.team.name?((0,o.uX)(),(0,o.Wv)(O,{key:0,type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.user.team.name),1)]})),_:1})):D.selectedAnnotation.user&&D.selectedAnnotation.user.team_id?((0,o.uX)(),(0,o.Wv)(O,{key:1,type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.getTeamNameById(D.selectedAnnotation.user.team_id)),1)]})),_:1})):D.selectedAnnotation.team&&D.selectedAnnotation.team.name?((0,o.uX)(),(0,o.Wv)(O,{key:2,type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.team.name),1)]})),_:1})):D.selectedAnnotation.team_id?((0,o.uX)(),(0,o.Wv)(O,{key:3,type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.getTeamNameById(D.selectedAnnotation.team_id)),1)]})),_:1})):((0,o.uX)(),(0,o.Wv)(O,{key:4,type:"warning"},{default:(0,o.k6)((function(){return t[16]||(t[16]=[(0,o.eW)("无团队")])})),_:1,__:[16]}))]})),_:1}),(0,o.bF)(J,{label:"创建时间"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.formatDateTime(D.selectedAnnotation.createdAt)),1)]})),_:1}),(0,o.bF)(J,{label:"更新时间"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.formatDateTime(D.selectedAnnotation.updatedAt)),1)]})),_:1}),(0,o.bF)(J,{label:"病例编号"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.caseNumber||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"血管瘤类型"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.detectedType||"-"),1)]})),_:1})]})),_:1})]),(0,o.Lk)("div",_,[t[18]||(t[18]=(0,o.Lk)("h3",null,"患者信息",-1)),(0,o.bF)(X,{column:3,border:""},{default:(0,o.k6)((function(){return[(0,o.bF)(J,{label:"姓名"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.patientName||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"年龄"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.patientAge||"-")+"岁",1)]})),_:1}),(0,o.bF)(J,{label:"性别"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.gender||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"病例编号"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.caseNumber||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"病灶位置",span:2},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.bodyPart||D.selectedAnnotation.lesionLocation||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"颜色"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.color||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"血管质地"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.vesselTexture||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"症状描述"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.symptomDescription||"-"),1)]})),_:1})]})),_:1})]),(0,o.Lk)("div",E,[t[19]||(t[19]=(0,o.Lk)("h3",null,"诊断信息",-1)),(0,o.bF)(X,{column:1,border:""},{default:(0,o.k6)((function(){return[(0,o.bF)(J,{label:"诊断摘要"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.diagnosticSummary||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"治疗建议"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.treatmentSuggestion||"-"),1)]})),_:1}),(0,o.bF)(J,{label:"注意事项"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.selectedAnnotation.precautions||"-"),1)]})),_:1})]})),_:1})]),(0,o.Lk)("div",S,[t[22]||(t[22]=(0,o.Lk)("h3",null,"审核意见",-1)),(0,o.bF)(L,{modelValue:D.reviewNotes,"onUpdate:modelValue":t[2]||(t[2]=function(e){return D.reviewNotes=e}),type:"textarea",rows:4,placeholder:"请输入审核意见...",disabled:"SUBMITTED"!==D.selectedAnnotation.status},null,8,["modelValue","disabled"]),"SUBMITTED"!==D.selectedAnnotation.status?((0,o.uX)(),(0,o.CE)("div",y,[t[20]||(t[20]=(0,o.Lk)("i",{class:"el-icon-warning"},null,-1)),(0,o.eW)(" "+(0,o.v_)("APPROVED"===D.selectedAnnotation.status?"该标注已通过审核，无法修改审核意见":"REJECTED"===D.selectedAnnotation.status?"该标注已被拒绝，无法修改审核意见":"该标注不是待审核状态，无法进行审核操作"),1)])):(0,o.Q3)("",!0),!D.selectedAnnotation.reviewNotes||"APPROVED"!==D.selectedAnnotation.status&&"REJECTED"!==D.selectedAnnotation.status?(0,o.Q3)("",!0):((0,o.uX)(),(0,o.CE)("div",k,[t[21]||(t[21]=(0,o.Lk)("strong",null,"审核备注:",-1)),(0,o.eW)(" "+(0,o.v_)(D.selectedAnnotation.reviewNotes),1)]))]),(0,o.Lk)("div",P,[(0,o.bF)(M,{plain:"",onClick:D.backToList},{default:(0,o.k6)((function(){return t[23]||(t[23]=[(0,o.eW)("返回")])})),_:1,__:[23]},8,["onClick"]),"SUBMITTED"===D.selectedAnnotation.status?((0,o.uX)(),(0,o.CE)(o.FK,{key:0},[(0,o.bF)(M,{type:"success",plain:"",onClick:t[3]||(t[3]=function(e){return D.approveAnnotation(D.selectedAnnotation)})},{default:(0,o.k6)((function(){return t[24]||(t[24]=[(0,o.eW)(" 批准 ")])})),_:1,__:[24]}),(0,o.bF)(M,{type:"danger",plain:"",onClick:t[4]||(t[4]=function(e){return D.showRejectDialog(D.selectedAnnotation)})},{default:(0,o.k6)((function(){return t[25]||(t[25]=[(0,o.eW)(" 拒绝 ")])})),_:1,__:[25]})],64)):(0,o.Q3)("",!0)])])):(0,o.Q3)("",!0)])):((0,o.uX)(),(0,o.CE)("div",r,[t[11]||(t[11]=(0,o.Lk)("h2",null,"标注审核",-1)),D.currentUser?((0,o.uX)(),(0,o.Wv)(N,{key:0,type:"info",closable:!1},{title:(0,o.k6)((function(){return[(0,o.Lk)("span",null,[t[8]||(t[8]=(0,o.eW)("当前审核人: ")),(0,o.Lk)("strong",null,(0,o.v_)(D.currentUser.name),1)]),(0,o.bF)(O,{size:"small",type:"ADMIN"===D.currentUser.role?"danger":"warning",style:{"margin-left":"10px"}},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)("ADMIN"===D.currentUser.role?"管理员":"审核医生"),1)]})),_:1},8,["type"]),D.currentUser.team?((0,o.uX)(),(0,o.CE)("span",s,[t[9]||(t[9]=(0,o.eW)(" 团队: ")),(0,o.bF)(O,{size:"small",type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.currentUser.team.name),1)]})),_:1})])):(0,o.Q3)("",!0)]})),_:1})):(0,o.Q3)("",!0),(0,o.Lk)("div",i,[(0,o.bF)(L,{modelValue:D.searchQuery,"onUpdate:modelValue":t[0]||(t[0]=function(e){return D.searchQuery=e}),placeholder:"搜索标注","prefix-icon":"el-icon-search",clearable:"",class:"search-input",onChange:D.loadAnnotations},null,8,["modelValue","onChange"]),(0,o.bF)(F,{modelValue:D.filters.team,"onUpdate:modelValue":t[1]||(t[1]=function(e){return D.filters.team=e}),placeholder:"按团队筛选",clearable:"",onChange:D.loadAnnotations},{default:(0,o.k6)((function(){return[((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(D.teams,(function(e){return(0,o.uX)(),(0,o.Wv)(U,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])})),128))]})),_:1},8,["modelValue","onChange"])]),(0,o.bo)(((0,o.uX)(),(0,o.Wv)(V,{data:D.annotations,"empty-text":D.loading?"加载中...":"暂无待审核的标注",style:{width:"100%"},border:"",stripe:"","highlight-current-row":"","row-class-name":D.getRowClassName,onRowClick:D.showAnnotationDetail},{default:(0,o.k6)((function(){return[(0,o.bF)(W,{prop:"id",label:"ID",width:"70"}),(0,o.bF)(W,{label:"提交时间",width:"160"},{default:(0,o.k6)((function(e){return[(0,o.eW)((0,o.v_)(D.formatDateTime(e.row.submittedAt)),1)]})),_:1}),(0,o.bF)(W,{label:"标注医生",width:"150"},{default:(0,o.k6)((function(e){return[e.row.submittedBy&&"object"===(0,a.A)(e.row.submittedBy)?((0,o.uX)(),(0,o.CE)("span",c,(0,o.v_)(e.row.submittedBy.name||"未知"),1)):e.row.submittedBy?((0,o.uX)(),(0,o.CE)("span",u,(0,o.v_)(D.getDoctorNameById(e.row.submittedBy)||"医生"+e.row.submittedBy),1)):e.row.uploadedBy&&"object"===(0,a.A)(e.row.uploadedBy)?((0,o.uX)(),(0,o.CE)("span",l,(0,o.v_)(e.row.uploadedBy.name||"未知"),1)):e.row.uploadedBy?((0,o.uX)(),(0,o.CE)("span",d,(0,o.v_)(D.getDoctorNameById(e.row.uploadedBy)||"医生"+e.row.uploadedBy),1)):((0,o.uX)(),(0,o.CE)("span",g," 未知 "))]})),_:1}),(0,o.bF)(W,{label:"所属团队",width:"180"},{default:(0,o.k6)((function(e){return[(0,o.Lk)("div",null,[(0,o.Q3)("",!0),e.row.user&&e.row.user.team&&e.row.user.team.name?((0,o.uX)(),(0,o.Wv)(O,{key:1,type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(e.row.user.team.name),1)]})),_:2},1024)):e.row.user&&e.row.user.team_id?((0,o.uX)(),(0,o.Wv)(O,{key:2,type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.getTeamNameById(e.row.user.team_id)),1)]})),_:2},1024)):e.row.team&&e.row.team.name?((0,o.uX)(),(0,o.Wv)(O,{key:3,type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(e.row.team.name),1)]})),_:2},1024)):e.row.team_id?((0,o.uX)(),(0,o.Wv)(O,{key:4,type:"success"},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.getTeamNameById(e.row.team_id)),1)]})),_:2},1024)):((0,o.uX)(),(0,o.Wv)(O,{key:5,type:"warning",effect:"dark"},{default:(0,o.k6)((function(){return[(0,o.bF)(x,{content:"无团队标注，所有审核医生可审核",placement:"top"},{default:(0,o.k6)((function(){return t[10]||(t[10]=[(0,o.Lk)("span",null,[(0,o.Lk)("i",{class:"el-icon-warning-outline"}),(0,o.eW)(" 无团队")],-1)])})),_:1,__:[10]})]})),_:1}))])]})),_:1}),(0,o.bF)(W,{prop:"patientName",label:"患者姓名",width:"120"}),(0,o.bF)(W,{prop:"diagnosis",label:"诊断"}),(0,o.bF)(W,{label:"状态",width:"100"},{default:(0,o.k6)((function(e){return[(0,o.bF)(O,{type:D.getStatusTagType(e.row.status)},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)(D.getStatusDisplayText(e.row.status)),1)]})),_:2},1032,["type"])]})),_:1}),(0,o.bF)(W,{fixed:"right",label:"操作",width:"120"},{default:(0,o.k6)((function(e){return[(0,o.bF)(M,{size:"small",type:"APPROVED"===e.row.status?"info":"primary",onClick:(0,o.D$)((function(t){return D.showAnnotationDetail(e.row)}),["stop"])},{default:(0,o.k6)((function(){return[(0,o.eW)((0,o.v_)("APPROVED"===e.row.status?"查看":"批阅"),1)]})),_:2},1032,["type","onClick"])]})),_:1})]})),_:1},8,["data","empty-text","row-class-name","onRowClick"])),[[G,D.loading]]),(0,o.Lk)("div",m,[(0,o.bF)(j,{background:"",layout:"total, sizes, prev, pager, next, jumper","page-sizes":[10,20,50,100],"page-size":D.pageSize,total:D.total,"current-page":D.currentPage,onSizeChange:D.handleSizeChange,onCurrentChange:D.handleCurrentChange},null,8,["page-size","total","current-page","onSizeChange","onCurrentChange"])])])),(0,o.bF)($,{modelValue:D.rejectDialogVisible,"onUpdate:modelValue":t[7]||(t[7]=function(e){return D.rejectDialogVisible=e}),title:"拒绝标注",width:"400px"},{footer:(0,o.k6)((function(){return[(0,o.Lk)("span",C,[(0,o.bF)(M,{onClick:t[6]||(t[6]=function(e){return D.rejectDialogVisible=!1})},{default:(0,o.k6)((function(){return t[27]||(t[27]=[(0,o.eW)("取消")])})),_:1,__:[27]}),(0,o.bF)(M,{type:"danger",loading:D.submitting,onClick:D.rejectAnnotation},{default:(0,o.k6)((function(){return t[28]||(t[28]=[(0,o.eW)("确认拒绝")])})),_:1,__:[28]},8,["loading","onClick"])])]})),default:(0,o.k6)((function(){return[(0,o.bF)(z,null,{default:(0,o.k6)((function(){return[(0,o.bF)(q,{label:"拒绝原因"},{default:(0,o.k6)((function(){return[(0,o.bF)(L,{modelValue:D.reviewNotes,"onUpdate:modelValue":t[5]||(t[5]=function(e){return D.reviewNotes=e}),type:"textarea",rows:"4",placeholder:"请输入拒绝原因..."},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1},8,["modelValue"])],2)}var T=n(88844),R=n(24059),O=n(698),N=(n(28706),n(50113),n(74423),n(62062),n(60739),n(23288),n(18111),n(20116),n(61701),n(33110),n(79432),n(26099),n(21699),n(11392),n(42762),n(80142)),L=n(98351),U=n(36149),F=n(72505),W=n.n(F),x=n(60455);const M={name:"AnnotationReviewList",props:{standaloneMode:{type:Boolean,default:!1}},setup:function(e){var t=(0,o.KR)([]),n=(0,o.KR)(!1),a=(0,o.KR)(!1),r=(0,o.KR)(1),s=(0,o.KR)(20),i=(0,o.KR)(0),c=(0,o.KR)(""),u=(0,o.KR)([]),l=(0,o.KR)(null),d=(0,o.Kh)({team:null}),g=(0,o.KR)(!1),m=(0,o.KR)(null),p=(0,o.KR)(""),f=(0,o.KR)(!1),h=((0,x.rd)(),(0,x.lq)()),v=(0,o.EW)((function(){return e.standaloneMode||"/standalone-review"===h.path})),A=function(){var e=(0,O.A)((0,R.A)().m((function e(){var t,n,a;return(0,R.A)().w((function(e){while(1)switch(e.n){case 0:if(e.p=0,t=localStorage.getItem("user"),!t){e.n=1;break}l.value=JSON.parse(t),e.n=3;break;case 1:return e.n=2,U["default"].user.getCurrentUser();case 2:n=e.v,l.value=n.data,localStorage.setItem("user",JSON.stringify(l.value));case 3:e.n=5;break;case 4:e.p=4,a=e.v,console.error("获取当前用户信息失败",a),N.nk.error("获取用户信息失败，请重新登录");case 5:return e.a(2)}}),e,null,[[0,4]])})));return function(){return e.apply(this,arguments)}}(),I=function(){var e=(0,O.A)((0,R.A)().m((function e(){var t,n;return(0,R.A)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,U["default"].teams.getAllTeams();case 1:t=e.v,u.value=t.data,e.n=3;break;case 2:e.p=2,n=e.v,console.error("获取团队列表失败",n),N.nk.warning("获取团队列表失败");case 3:return e.a(2)}}),e,null,[[0,2]])})));return function(){return e.apply(this,arguments)}}(),w=function(){var e=(0,O.A)((0,R.A)().m((function e(){var a,o,l,g;return(0,R.A)().w((function(e){while(1)switch(e.n){case 0:return n.value=!0,e.p=1,a=JSON.parse(localStorage.getItem("user")||"{}"),o={page:r.value-1,size:s.value,sort:"created_at,desc",search:c.value.trim(),teamId:d.team},"ADMIN"!==a.role&&(o.userId=a.id||a.customId),console.log("加载待审核的血管瘤诊断记录，参数:",o),e.n=2,W().get("/medical/api/hemangioma-diagnoses/pending",{params:o});case 2:l=e.v,l.data&&l.data.content?(t.value=l.data.content.map((function(e){var t=e.user||{};if(t&&!t.team&&t.team_id){var n=u.value.find((function(e){return e.id===t.team_id}));n&&(t.team=n)}return(0,T.A)((0,T.A)({},e),{},{submittedAt:e.updatedAt,submittedBy:t,user:t})})),i.value=l.data.totalElements):(t.value=[],i.value=0),e.n=4;break;case 3:e.p=3,g=e.v,console.error("加载待审核标注失败:",g),N.nk.error("加载待审核列表失败，请稍后重试"),t.value=[],i.value=0;case 4:return e.p=4,n.value=!1,e.f(4);case 5:return e.a(2)}}),e,null,[[1,3,4,5]])})));return function(){return e.apply(this,arguments)}}(),b=function(e){s.value=e,r.value=1,w()},_=function(e){r.value=e,w()},E=function(e){if(!l.value)return!1;if("ADMIN"===l.value.role)return!0;if("REVIEWER"!==l.value.role)return!1;if(!e.team&&!e.team_id)return!0;var t=e.team&&e.team.id||e.team_id,n=l.value.team&&l.value.team.id||l.value.team_id;return n&&n===t},S=function(e){var t=e.row;return t.team?"":"no-team-row"},y=function(){var e=(0,O.A)((0,R.A)().m((function e(t){var a,o,r,s,i,c;return(0,R.A)().w((function(e){while(1)switch(e.n){case 0:if(console.log("查看标注详情",t),e.p=1,m.value=t,f.value=!0,p.value="",a=t.id,!a){e.n=6;break}return e.p=2,console.log("加载完整的诊断记录，ID: ".concat(a)),n.value=!0,e.n=3,W().get("/medical/api/hemangioma-diagnoses/".concat(a));case 3:o=e.v,o.data&&(console.log("获取到完整的诊断记录:",o.data),r=o.data.user||m.value.user||m.value.submittedBy||{},r&&!r.team&&r.team_id&&(s=u.value.find((function(e){return e.id===r.team_id})),s&&(r.team=s)),m.value=(0,T.A)((0,T.A)((0,T.A)({},m.value),o.data),{},{user:r,submittedBy:r}),console.log("合并后的完整诊断记录:",m.value)),e.n=5;break;case 4:e.p=4,i=e.v,console.error("获取完整诊断记录失败:",i),N.nk.error("获取诊断详情失败，请稍后重试");case 5:return e.p=5,n.value=!1,e.f(5);case 6:e.n=8;break;case 7:e.p=7,c=e.v,console.error("查看标注详情失败:",c),N.nk.error("加载标注详情失败");case 8:return e.a(2)}}),e,null,[[2,4,5,6],[1,7]])})));return function(t){return e.apply(this,arguments)}}(),k=function(){f.value=!1,m.value=null},P=function(){var e=(0,O.A)((0,R.A)().m((function e(t){var n,o,r,s,i,c,u,l,d,g,m;return(0,R.A)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,L.s.confirm("确认批准此标注？","确认批准",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"});case 1:return a.value=!0,n=JSON.parse(localStorage.getItem("user")||"{}"),o=n.customId||n.id||"",r=n.role||"REVIEWER",console.log("使用认证信息:",o,r),e.p=2,e.n=3,U["default"].reviews.approveReview(t.id,{reviewNotes:p.value||"审核通过"});case 3:s=e.v,console.log("审核成功响应:",s.data),t.status="APPROVED",N.nk.success("标注已批准，状态已更新为已通过"),k(),w(),e.n=8;break;case 4:return e.p=4,d=e.v,console.error("API调用失败:",d),console.log("错误详情:",null===(i=d.response)||void 0===i?void 0:i.data),N.nk.error("审核失败: ".concat((null===(c=d.response)||void 0===c||null===(c=c.data)||void 0===c?void 0:c.message)||d.message)),e.p=5,console.log("尝试使用备用方法..."),e.n=6,W().put("/medical/api/hemangioma-diagnoses/".concat(t.id),{status:"APPROVED",reviewNotes:p.value||"审核通过"});case 6:u=e.v,console.log("备用方法成功:",u.data),t.status="APPROVED",N.nk.success("标注已批准（备用方法），状态已更新为已通过"),k(),w(),e.n=8;break;case 7:e.p=7,g=e.v,console.error("备用方法也失败:",g),N.nk.error("所有审核方法均失败，请稍后重试");case 8:e.n=10;break;case 9:e.p=9,m=e.v,"cancel"!==m&&(console.error("批准标注失败",m),N.nk.error((null===(l=m.response)||void 0===l?void 0:l.data)||"批准标注失败"));case 10:return e.p=10,a.value=!1,e.f(10);case 11:return e.a(2)}}),e,null,[[5,7],[2,4],[0,9,10,11]])})));return function(t){return e.apply(this,arguments)}}(),C=function(e){m.value=e,g.value=!0},D=function(){var e=(0,O.A)((0,R.A)().m((function e(){var t,n,o,r,s,i,c,u,l,d,h;return(0,R.A)().w((function(e){while(1)switch(e.n){case 0:if(p.value){e.n=1;break}return N.nk.warning("请输入拒绝原因"),e.a(2);case 1:return e.p=1,a.value=!0,t=JSON.parse(localStorage.getItem("user")||"{}"),n=t.customId||t.id||"",o=t.role||"REVIEWER",console.log("使用认证信息:",n,o),e.p=2,e.n=3,U["default"].reviews.rejectReview(m.value.id,{reviewNotes:p.value});case 3:r=e.v,console.log("拒绝成功响应:",r.data),g.value=!1,m.value.status="REJECTED",N.nk.success("标注已拒绝，状态已更新为已拒绝"),f.value&&k(),w(),e.n=8;break;case 4:return e.p=4,l=e.v,console.error("API调用失败:",l),console.log("错误详情:",null===(s=l.response)||void 0===s?void 0:s.data),N.nk.error("拒绝失败: ".concat((null===(i=l.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.message)||l.message)),e.p=5,console.log("尝试使用备用方法..."),e.n=6,U["default"].reviews.rejectReviewBackup(m.value.id,p.value);case 6:c=e.v,console.log("备用方法成功:",c),g.value=!1,m.value.status="REJECTED",N.nk.success("标注已拒绝（备用方法），状态已更新为已拒绝"),f.value&&k(),w(),e.n=8;break;case 7:e.p=7,d=e.v,console.error("备用方法也失败:",d),N.nk.error("所有拒绝方法均失败，请稍后重试");case 8:e.n=10;break;case 9:e.p=9,h=e.v,console.error("拒绝标注失败",h),N.nk.error((null===(u=h.response)||void 0===u?void 0:u.data)||"拒绝标注失败");case 10:return e.p=10,a.value=!1,e.f(10);case 11:return e.a(2)}}),e,null,[[5,7],[2,4],[1,9,10,11]])})));return function(){return e.apply(this,arguments)}}(),F=function(e){if(!e)return"-";try{var t=new Date(e);return isNaN(t.getTime())?"-":new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}).format(t)}catch(n){return console.warn("日期格式化错误:",e,n),"-"}},M=function(e){var t,n;if(!e)return null;if(console.log("获取图像路径, annotation对象:",e),e.image_two_path)return console.log("直接使用annotation.image_two_path:",e.image_two_path),e.image_two_path;if(e.imagePair&&e.imagePair.image_two_path)return console.log("使用annotation.imagePair.image_two_path:",e.imagePair.image_two_path),e.imagePair.image_two_path;for(var a=[e.image_two_path,e.imageTwoPath,null===(t=e.imagePair)||void 0===t?void 0:t.image_two_path,null===(n=e.imagePair)||void 0===n?void 0:n.imageTwoPath,e.annotated_image_path,e.annotatedImagePath,e.image_path,e.imagePath,e.path],o=0,r=a;o<r.length;o++){var s=r[o];if(s)return console.log("找到图像路径:",s),s}return console.log("未找到任何图像路径"),null},V=function(e){if(!e)return"";if(e.startsWith("http://")||e.startsWith("https://"))return e;var t;try{var n=window.apiConfig||{};t=n.API_BASE_URL}catch(i){console.log("无法从配置获取API基础URL，使用当前窗口位置")}t||(t=window.location.origin,console.log("使用当前窗口origin作为基础URL:",t));var a=e.startsWith("/")?e:"/".concat(e),o=JSON.parse(localStorage.getItem("user")||"{}"),r=o.customId||o.id||"",s=a.includes("?")?"&":"?";return a="".concat(a).concat(s,"userId=").concat(r,"&t=").concat((new Date).getTime()),"".concat(t).concat(a)};(0,o.sV)((function(){console.log("========== 页面组件挂载完成 =========="),console.log("当前环境:","production"),console.log("baseURL:",W().defaults.baseURL),window.debugAnnotationReview={loadAnnotations:w,getUserInfo:function(){var e=localStorage.getItem("user");return e?JSON.parse(e):null},directFetchApi:function(){var e=(0,O.A)((0,R.A)().m((function e(){var t,n,a,o,r,s;return(0,R.A)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,n=JSON.parse(localStorage.getItem("user")||"{}"),a=n.customId||n.id,e.n=1,fetch("/api/reviews/pending?userId=".concat(a,"&page=0&size=20"));case 1:return o=e.v,e.n=2,o.json();case 2:return r=e.v,console.log("直接API调用结果:",r),console.log("找到".concat(r.totalElements,"条记录，当前页").concat((null===(t=r.content)||void 0===t?void 0:t.length)||0,"条数据")),e.a(2,r);case 3:e.p=3,s=e.v,console.error("直接API调用失败:",s);case 4:return e.a(2)}}),e,null,[[0,3]])})));function t(){return e.apply(this,arguments)}return t}()},A(),I(),w()}));var j=function(e){if(!e)return"无团队";var t=u.value.find((function(t){return t.id===e}));return t?t.name:"团队".concat(e)},B=function(e){return e?!l.value||l.value.id!==e&&l.value.customId!==e?"医生".concat(e):l.value.name:"未知"},J=function(e){if(!e)return"未知";var t={DRAFT:"草稿",SUBMITTED:"待审核",REVIEWED:"已审阅",REJECTED:"已拒绝",APPROVED:"已通过"};return t[e]||e},X=function(e){if(!e)return"info";var t={DRAFT:"info",SUBMITTED:"warning",REVIEWED:"success",REJECTED:"danger",APPROVED:"success"};return t[e]||"info"};return{annotations:t,loading:n,submitting:a,currentPage:r,pageSize:s,total:i,searchQuery:c,filters:d,teams:u,rejectDialogVisible:g,selectedAnnotation:m,isViewingDetail:f,loadAnnotations:w,handleSizeChange:b,handleCurrentChange:_,canReview:E,showAnnotationDetail:y,backToList:k,approveAnnotation:P,showRejectDialog:C,rejectAnnotation:D,formatDateTime:F,getRowClassName:S,currentUser:l,getTeamNameById:j,getDoctorNameById:B,getFullImageUrl:V,getImagePath:M,isStandalone:v,reviewNotes:p,getStatusDisplayText:J,getStatusTagType:X}}};var V=n(66262);const j=(0,V.A)(M,[["render",D],["__scopeId","data-v-7f3ba300"]]),B=j},36149:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>I});var a,o=n(50139),r=n(55593),s=n(59158),i=n(88844),c=(n(16280),n(76918),n(30067),n(28706),n(2008),n(50113),n(51629),n(74423),n(64346),n(48598),n(62062),n(59089),n(60739),n(23288),n(62010),n(18111),n(22489),n(20116),n(7588),n(61701),n(33110),n(79432),n(26099),n(16034),n(58940),n(93518),n(84864),n(57465),n(27495),n(87745),n(38781),n(21699),n(47764),n(25440),n(5746),n(11392),n(42207),n(23500),n(62953),n(55815),n(64979),n(79739),n(48408),n(14603),n(47566),n(98721),n(72505)),u=n.n(c),l=u().create({baseURL:{NODE_ENV:"production",VUE_APP_API_PREFIX:"/api",VUE_APP_API_URL:"",VUE_APP_CONTEXT_PATH:"/medical",BASE_URL:"/"}.VUE_APP_API_BASE_URL||"/medical/api",timeout:3e4,withCredentials:!0});l.interceptors.request.use((function(e){if(e.url=f(e.url),console.log("[API] ".concat(e.method.toUpperCase()," ").concat(e.baseURL).concat(e.url)),e.url.includes("/users/me")||e.url.includes("/session-check")){console.log("[API] 检测到会话检查请求，使用简化认证"),e.headers={"Content-Type":"application/json",Accept:"application/json"};var t=e.url.includes("?")?"&":"?";e.url+="".concat(t,"_t=").concat(Date.now());try{var n=localStorage.getItem("user");if(n){var a=JSON.parse(n);if(a&&(a.id||a.customId)){var o=a.customId||a.id;e.headers["Authorization"]="Bearer user_".concat(o);var r=e.url.includes("?")?"&":"?";e.url+="".concat(r,"userId=").concat(o)}}}catch(I){console.warn("[API] 读取用户信息失败:",I)}return e.headers["X-User-ID"]=userId,e}var c;try{var u=localStorage.getItem("user");if(u&&(c=JSON.parse(u)),!c||!c.id){var l=sessionStorage.getItem("preservedUser");l&&(console.log("[API] 从会话存储恢复用户信息"),c=JSON.parse(l),localStorage.setItem("user",l))}if(c&&(!c.email||!c.role)){var d=localStorage.getItem("userProfile");if(d){var g=JSON.parse(d);c=(0,i.A)((0,i.A)({},c),g),console.log("[API] 从用户配置文件合并额外信息")}}}catch(I){console.error("解析用户信息失败:",I)}if(c){var m=c.id||c.customId||"";if(e.headers["Authorization"]="Bearer user_".concat(m),e.headers["X-User-ID"]=m,c.role){var p=new URLSearchParams(e.params||{});p.has("_role")||p.set("_role",c.role),e.params=p}var h=new URLSearchParams(e.params||{});if(h.has("userId")||h.set("userId",m),e.params=h,c.team&&c.team.id&&(e.url.includes("/teams/")||e.url.includes("/team-applications")||e.url.includes("/images/team-"))){var v=new URLSearchParams(e.params);v.has("_teamId")||v.set("_teamId",c.team.id),e.params=v}}else console.warn("[API] 未找到用户信息，请求可能未认证");e.method&&"options"===e.method.toLowerCase()&&(e.headers["Access-Control-Request-Headers"]="authorization, content-type",e.headers["Access-Control-Request-Method"]="GET, POST, PUT, DELETE"),e.headers["Cache-Control"]||(e.headers["Cache-Control"]="no-cache");var A="_t=".concat(Date.now());return e.url.includes("?")?e.url+="&".concat(A):e.url+="?".concat(A),e.transformResponse&&Array.isArray(e.transformResponse)?e.transformResponse=(0,s.A)(e.transformResponse):e.transformResponse=[function(e){if("string"===typeof e)try{return JSON.parse(e)}catch(I){return console.warn("JSON解析失败:",I),e}return e}],console.log("[API完整请求] ".concat(e.method.toUpperCase()," ").concat(e.baseURL).concat(e.url),{参数:e.params,头部:e.headers}),e}),(function(e){return console.error("[API Request Error]",e),Promise.reject(e)})),l.interceptors.response.use((function(e){return console.log("[API响应] ".concat(e.status," ").concat(e.config.url),{数据:e.data}),e}),(function(e){return console.error("[API响应错误]",{请求URL:e.config?e.config.url:"未知",请求方法:e.config?e.config.method:"未知",状态码:e.response?e.response.status:"网络错误",响应数据:e.response?e.response.data:null}),Promise.reject(e)}));var d=u().create({baseURL:{NODE_ENV:"production",VUE_APP_API_PREFIX:"/api",VUE_APP_API_URL:"",VUE_APP_CONTEXT_PATH:"/medical",BASE_URL:"/"}.VUE_APP_API_BASE_URL||"/medical/api",headers:{"Content-Type":"multipart/form-data",Accept:"application/json"},timeout:15e3,withCredentials:!0}),g=u().create({baseURL:{NODE_ENV:"production",VUE_APP_API_PREFIX:"/api",VUE_APP_API_URL:"",VUE_APP_CONTEXT_PATH:"/medical",BASE_URL:"/"}.VUE_APP_API_BASE_URL||"/medical/api",headers:{"Content-Type":"application/json",Accept:"application/json","X-Debug":"true"},timeout:1e4,withCredentials:!0});function m(){try{var e=localStorage.getItem("user");if(!e)return!1;var t=JSON.parse(e),n=sessionStorage.getItem("authData");if(n){var a=JSON.parse(n);if(a.token)return t.token=a.token,localStorage.setItem("user",JSON.stringify(t)),console.log("已从会话存储恢复认证令牌"),!0}return!(!t.email||!t.id&&!t.customId)}catch(o){return console.error("自动修复尝试失败:",o),!1}}function p(){try{console.log("检查页面是否需要恢复会话...");var e=localStorage.getItem("user");if(!e)return console.log("本地存储中没有用户信息，无需恢复会话"),!1;var t=JSON.parse(e);if(!t||!t.id&&!t.customId)return console.log("用户信息不完整，无法恢复会话"),!1;var n=t.customId||t.id,a="/medical/api/users/me",o={_t:(new Date).getTime(),_refresh:!0},r={"Content-Type":"application/json",Accept:"application/json","Cache-Control":"no-cache, no-store"};n&&(r["Authorization"]="Bearer user_".concat(n));var s=Object.keys(o).map((function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(o[e]))})).join("&"),i="".concat(a,"?").concat(s);return console.log("发送简化的会话恢复请求..."),fetch(i,{method:"GET",headers:r,credentials:"include"}).then((function(e){if(!e.ok)throw new Error("会话恢复请求失败: ".concat(e.status));return e.json()})).then((function(e){return console.log("会话恢复成功:",e),e&&e.user&&(localStorage.setItem("user",JSON.stringify(e.user)),console.log("用户信息已更新")),!0}))["catch"]((function(e){return console.warn("会话恢复失败:",e),!1}))}catch(c){return console.error("会话恢复过程出错:",c),!1}}function f(e){if(!e)return e;var t=e,n=e;while(n.startsWith("//"))n=n.substring(1);for(var a=[{find:"medical/api/medical/api/",replace:"medical/api/"},{find:"api/medical/api/",replace:"medical/api/"},{find:"medical/api/api/",replace:"medical/api/"},{find:"medical/medical/",replace:"medical/"},{find:"api/api/",replace:"api/"}],o=0,r=a;o<r.length;o++){var s=r[o];n.includes(s.find)&&(n=n.replace(new RegExp(s.find,"g"),s.replace),console.log("[normalizePath] 路径修正: 将 ".concat(s.find," 替换为 ").concat(s.replace)),!0)}return n.startsWith("/")||(n="/"+n),n.startsWith("/api/")&&l&&"/api"===l.defaults.baseURL&&(n=n.replace(/^\/api\//,"/"),console.log("[normalizePath] 检测到路径已有/api前缀，移除重复: ".concat(n))),t!==n&&console.log("[normalizePath] URL路径已规范化: ".concat(t," -> ").concat(n)),n}function h(e){try{var t=localStorage.getItem("user");if(!t)return Promise.reject(new Error("无可用认证信息"));var n=JSON.parse(t),a=(0,i.A)({},e);if(a.headers=(0,i.A)({},e.headers),a.headers["Authorization"]="Bearer user_".concat(n.customId||n.id),n.email){var o=btoa(n.email);a.headers["Authorization"]="".concat(a.headers["Authorization"],":").concat(o)}return delete a.headers["X-User-Id"],delete a.headers["X-User-Email"],delete a.headers["X-Authentication-Email"],delete a.headers["X-User-Role"],delete a.headers["X-User-Team-Id"],a.url=f(a.url),a._retry=!0,console.log("使用标准认证头重试请求，URL:",a.url),l(a)}catch(r){return console.error("重试请求失败:",r),Promise.reject(r)}}g.interceptors.request.use((function(e){e.url=f(e.url),console.log("[Debug API] 请求: ".concat(e.method.toUpperCase()," ").concat(e.baseURL).concat(e.url));try{var t=localStorage.getItem("user");if(t){var n=JSON.parse(t);if(n){if(e.headers["Authorization"]="Bearer user_".concat(n.customId||n.id||""),n.email){var a=btoa(n.email);e.headers["Authorization"]="".concat(e.headers["Authorization"],":").concat(a)}if(n.role){var o=e.url.includes("?")?"&":"?";e.url+="".concat(o,"_role=").concat(n.role)}}}}catch(r){console.error("[Debug API] 读取用户信息失败:",r)}return console.log("请求头:",e.headers),e}),(function(e){return console.error("[Debug API] 请求错误:",e),Promise.reject(e)})),g.interceptors.response.use((function(e){return console.log("[Debug API] 响应状态: ".concat(e.status)),console.log("响应数据:",e.data),e}),(function(e){return console.error("[Debug API] 响应错误:",e.response||e),Promise.reject(e)})),d.interceptors.request.use((function(e){e.url=f(e.url),console.log("[FileAPI] ".concat(e.method.toUpperCase()," ").concat(e.url));try{var t=localStorage.getItem("user");if(t){var n=JSON.parse(t);if(n&&(e.headers["Authorization"]="Bearer user_".concat(n.customId||n.id),n.email)){var a=btoa(n.email);e.headers["Authorization"]="".concat(e.headers["Authorization"],":").concat(a)}}}catch(o){console.error("[FileAPI] 读取用户信息失败:",o)}return e}),(function(e){return console.error("[FileAPI Request Error]",e),Promise.reject(e)})),d.interceptors.response.use((function(e){return console.log("[FileAPI] Response: ".concat(e.status)),e}),(function(e){return e.response?console.error("[FileAPI] Error: ".concat(e.response.status),e.response.data):console.error("[FileAPI] Network Error:",e.message),Promise.reject(e)})),l.interceptors.response.use((function(e){console.log("[API] Response: ".concat(e.status));try{var t=e.config.url;if(t.includes("/teams/")&&t.includes("/members")){if(console.log("[API Debug] 团队成员API返回数据:",(0,r.A)(e.data),Array.isArray(e.data)?"是数组":"不是数组",e.data?"非空":"为空"),e.data&&!Array.isArray(e.data))if(console.warn("[API Debug] 团队成员API返回非数组数据，尝试修复"),"object"===(0,r.A)(e.data)){var n=e.data.members||e.data.data||e.data.content||e.data.users;if(Array.isArray(n))console.log("[API Debug] 从对象中提取到数组数据"),e.data=n;else if(e.data.content&&"object"===(0,r.A)(e.data.content)){var a=e.data.content.members||e.data.content.users;Array.isArray(a)?(console.log("[API Debug] 从嵌套对象中提取到数组数据"),e.data=a):(console.log("[API Debug] 将对象放入数组中"),e.data=[e.data])}else{var o=e.data.id&&(e.data.name||e.data.email);if(o)console.log("[API Debug] 检测到单个团队成员对象"),e.data=[e.data];else{var s=Object.values(e.data).filter((function(e){return"object"===(0,r.A)(e)&&null!==e&&(e.id||e.userId||e.user_id)}));s.length>0?(console.log("[API Debug] 从对象键值中提取成员"),e.data=s):(console.log("[API Debug] 将对象放入数组中"),e.data=[e.data])}}}else console.warn("[API Debug] 无法解析团队成员数据，返回空数组"),e.data=[];e.data||(console.warn("[API Debug] 团队成员API返回空数据，初始化为空数组"),e.data=[]),Array.isArray(e.data)&&(e.data=e.data.map((function(e){return!e.id&&e.user_id&&(e.id=e.user_id),e.name||(e.username?e.name=e.username:e.user&&e.user.name&&(e.name=e.user.name)),e}))),console.log("[API Debug] 处理后的团队成员数据，成员数量:",Array.isArray(e.data)?e.data.length:0)}if(t.includes("/users")&&(console.log("[API Debug] 用户API返回数据结构:",(0,r.A)(e.data),Array.isArray(e.data)?"是数组":"不是数组",e.data?"非空":"为空"),e.data&&"object"===(0,r.A)(e.data)&&!Array.isArray(e.data)&&console.log("[API Debug] 对象键:",Object.keys(e.data))),t.includes("/images/")||t.includes("/api/images")){if(console.log("[API Debug] 图像API返回数据:",(0,r.A)(e.data),Array.isArray(e.data)?"是数组[".concat(e.data.length,"]"):"不是数组",e.data?"非空":"为空"),e.data||!t.includes("my-images")&&!t.includes("team-images")||(console.warn("[API Debug] 病例列表返回空数据，初始化为空数组"),e.data=[]),e.data&&!Array.isArray(e.data)&&(t.includes("my-images")||t.includes("team-images"))&&(console.warn("[API Debug] 病例列表返回非数组数据，尝试修复"),"object"===(0,r.A)(e.data))){var i=e.data.content||e.data.images||e.data.data||e.data.records||e.data.items||e.data.list;if(Array.isArray(i))console.log("[API Debug] 从对象中提取到病例数组数据"),e.data=i;else if(e.data.page&&"object"===(0,r.A)(e.data.page)){var c=e.data.page.content||e.data.page.data||e.data.page.list;Array.isArray(c)?(console.log("[API Debug] 从分页对象中提取病例数组"),e.data=c):(console.log("[API Debug] 将单个病例对象放入数组"),e.data=[e.data])}else if(Object.keys(e.data).length>0&&(e.data.id||e.data.imageId||e.data.metadata_id))console.log("[API Debug] 检测到单个病例对象，转换为数组"),e.data=[e.data];else{var u=Object.values(e.data).filter((function(e){return Array.isArray(e)&&e.length>0&&"object"===(0,r.A)(e[0])&&(e[0].id||e[0].imageId||e[0].metadata_id)}));u.length>0?(console.log("[API Debug] 从对象值中提取病例数组"),e.data=u[0]):(console.log("[API Debug] 将对象放入数组"),e.data=[e.data])}}Array.isArray(e.data)&&(t.includes("my-images")||t.includes("team-images"))&&(e.data=e.data.map((function(e){return!e.id&&e.imageId?e.id=e.imageId:!e.id&&e.metadata_id&&(e.id=e.metadata_id),!e.status&&e.imageStatus&&(e.status=e.imageStatus),!e.title&&e.name&&(e.title=e.name),e})),console.log("[API Debug] 处理后的病例列表数量: ".concat(e.data.length)))}}catch(l){console.error("[API Debug] 日志错误:",l)}return e}),(function(e){if(e.response){if(console.error("[API] Error: ".concat(e.response.status),e.response.data),401===e.response.status){console.warn("未授权，可能需要重新登录");var t=e.config.url&&(e.config.url.includes("/team-applications")||e.config.url.includes("/teams/")&&e.config.url.includes("/applications")),n=e.config.url&&(e.config.url.includes("/annotations")||e.config.url.includes("/tags")||e.config.url.includes("/image-after-annotation")||e.config.url.includes("/images/")||e.config.url.includes("/structured-form")),a=n||window.location.pathname.includes("/annotations")||window.location.pathname.includes("/cases/structured-form")||window.location.pathname.includes("/cases/form")||sessionStorage.getItem("isNavigatingAfterSave"),o=sessionStorage.getItem("isNavigatingAfterSave"),r=localStorage.getItem("skipAuthCheck"),s=window.location.pathname.includes("/teams"),i=e.config.url&&(e.config.url.includes("/images/my-images")||e.config.url.includes("/images/team-images")),c=window.location.pathname.includes("/cases");if(t||s||i||c){console.log("重要操作检测到认证问题，显示用户提示而非重定向");var u=m();if(i){console.log("病例列表API认证失败，返回空数组");var d={status:200,statusText:"OK (Mock)",data:[]};return Promise.resolve(d)}return u?h(e.config):Promise.reject({teamAuthError:!0,needsRelogin:!0,message:"会话已过期，请重新登录后再操作"})}if(o||r||a){console.log("检测到标注流程相关请求或特殊设置，不执行自动重定向"),window.$message&&window.$message.warning("您的登录已过期，但可以继续当前操作");var g=sessionStorage.getItem("preservedUser");if(g){console.log("尝试使用保存的用户信息恢复会话"),localStorage.setItem("user",g);var p=e.config;p._retry=!0;var f=JSON.parse(g);if(f&&(p.headers["Authorization"]="Bearer user_".concat(f.customId||f.id),f.email)){var v=btoa(f.email);p.headers["Authorization"]="".concat(p.headers["Authorization"],":").concat(v)}return l(p)}return Promise.reject(new Error("会话已过期，请重新登录后再操作"))}if(localStorage.removeItem("user"),"/login"!==window.location.pathname){var A=window.location.pathname+window.location.search;sessionStorage.setItem("redirectAfterLogin",A),window.location.href="/login"}}}else if(e.request){var I;console.error("[API] 网络错误:",e.message);var w=(null===(I=e.config)||void 0===I?void 0:I.url)||"";if(w.includes("/images/my-images")||w.includes("/images/team-images")||w.includes("/teams")&&!w.includes("/applications")){console.log("关键列表API网络错误，返回空数组以避免UI崩溃");var b={status:200,statusText:"OK (Mock due to network error)",data:[]};return Promise.resolve(b)}}else console.error("[API] 请求配置错误:",e.message);return Promise.reject(e)})),"undefined"!==typeof window&&window.addEventListener("DOMContentLoaded",(function(){p()}));var v=function(e){return e&&"object"===(0,r.A)(e)?e.customId||e.id||null:e};var A={auth:{login:function(e){return l.post("/users/authenticate",e)},register:function(e){return l.post("/users",e)}},stats:{getDashboard:function(e){if(console.log("[调试] getDashboard 被调用，传入的 userId:",e,"类型:",(0,r.A)(e)),!e){var t=localStorage.getItem("user");if(t){var n=JSON.parse(t);e=n.id,console.log("从localStorage中获取用户ID:",e)}}if(!e)return console.error("获取仪表盘统计数据时用户ID为空"),Promise.reject(new Error("用户ID不能为空"));var a=Date.now(),o=Math.random(),s="/stats-v2/dashboard/".concat(e,"?t=").concat(a,"&r=").concat(o,"&forcePersonalStats=true&view=personal");console.log("获取用户[".concat(e,"]的仪表盘统计数据, 完整URL: ").concat(l.defaults.baseURL).concat(s));var i={headers:{"X-User-Id":e},timeout:15e3,withCredentials:!0};return l.get(s,i)["catch"]((function(t){return console.error("获取用户[".concat(e,"]的仪表盘统计数据失败:"),t.message||t),t.response?(console.error("服务器响应状态码:",t.response.status),console.error("响应数据:",t.response.data)):t.request&&console.error("请求已发送但未收到响应，可能是网络问题"),{data:{totalCount:0,draftCount:0,reviewedCount:0,submittedCount:0,approvedCount:0,rejectedCount:0,dataSource:"error_fallback",userId:e,error:t.message||"获取统计数据失败"}}}))}},users:(0,o.A)((0,o.A)({getCurrentUser:function(){return l.get("/users/me")},updateProfile:function(e){return l.put("/users/profile",e)},changePassword:function(e){return l.put("/users/password",e)},uploadAvatar:function(e,t){return l.post("/users/".concat(e,"/avatar"),t,{headers:{"Content-Type":"multipart/form-data"}})},getReviewerApplicationStatus:function(){return l.get("/reviewer-applications/me")},applyForReviewer:function(e){return l.post("/reviewer-applications",{reason:e})},getAll:function(){return l.get("/users")},getUser:function(e){return l.get("/users/".concat(e))},createUser:function(e){return l.post("/users",e)},updateUser:function(e,t){return l.put("/users/".concat(e),t)},deleteUser:function(e){return l["delete"]("/users/".concat(e))},resetPassword:function(e,t){return l.post("/users/".concat(e,"/reset-password"),{newPassword:t})},getAllReviewers:function(){return l.get("/users/reviewers")},getPendingReviewerApplications:function(){return l.get("/reviewer-applications/pending")},processReviewerApplication:function(e,t){return l.put("/reviewer-applications/".concat(e),t)},getUsersWithoutTeam:function(){return l.get("/users/without-team")},sendResetPasswordEmail:function(e){return l.post("/users/forgot-password",{email:e})},verifyResetCode:function(e,t){return l.post("/users/verify-reset-code",{email:e,code:t})}},"resetPassword",(function(e,t,n){return l.post("/users/reset-password",{email:e,code:t,newPassword:n})})),"getUserAvatar",(function(e){return l.get("/users/".concat(e,"/avatar"))})),teams:{create:function(e){return l.post("/teams",e)},getAll:function(){return l.get("/teams")},getOne:function(e){return l.get("/teams/".concat(e))},joinTeam:function(e,t){return l.post("/teams/".concat(e,"/members"),{userId:t})},applyToJoinTeam:function(e,t){var n=JSON.parse(localStorage.getItem("user")||"{}"),a=n.customId||n.id;if(console.log("申请加入团队，使用用户ID:",a,"类型:",(0,r.A)(a)),!a)return console.error("无法获取有效的用户ID，可能用户未登录或会话已过期"),Promise.reject(new Error("无法获取用户ID，请重新登录"));var o={teamId:parseInt(e,10)||e,reason:t,status:"PENDING"};return console.log("团队申请请求数据:",JSON.stringify(o)),l.post("/team-applications",o)},getTeamApplications:function(e,t){var n="/team-applications/team/".concat(e);return t&&(n+="?status=".concat(t)),l.get(n)},processTeamApplication:function(e,t,n){return l.put("/team-applications/".concat(e),{action:t,reason:n||""})},getUserApplications:function(e){var t=v(e);return l.get("/team-applications/user/".concat(t))},getTeamMembers:function(e){return l.get("/teams/".concat(e,"/members"))},getTeamAnnotations:function(e){return l.get("/teams/".concat(e,"/annotations/approved"))},getAnnotationDetail:function(e){return l.get("/annotations/".concat(e))},removeTeamMember:function(e,t){return l["delete"]("/teams/".concat(e,"/members/").concat(t))},checkUserInTeam:function(e,t){return l.get("/teams/".concat(t,"/members/check/").concat(e))["catch"]((function(e){if(e.response)return{data:{isMember:!1}};throw e}))},deleteTeam:function(e){var t=localStorage.getItem("user"),n=t?JSON.parse(t):null,a={};return n&&n.id&&(a["X-User-ID"]=n.id),l["delete"]("/teams/".concat(e),{headers:a})},transferOwnership:function(e,t){var n=localStorage.getItem("user"),a=n?JSON.parse(n):null,o={};return a&&a.id&&(o["X-User-ID"]=a.id),l.post("/teams/".concat(e,"/transfer-ownership"),{newOwnerId:t},{headers:o})}},reviewer:{getProcessedApplications:function(){return l.get("/reviewer-applications/processed")}},images:(a={getAll:function(){return l.get("/images")},getOne:function(e){return l.get("/images/".concat(e))},upload:function(e){var t=new FormData;t.append("file",e);var n=JSON.parse(localStorage.getItem("user")||"{}");n.id&&t.append("user_id",n.id),e.name&&t.append("name",e.name);var a=15e3;console.log("上传文件中...",{文件名:e.name,文件大小:e.size,文件类型:e.type,用户ID:n.id});var o={timeout:a};return d.post("/images/upload",t,o)},saveToPath:function(e){return d.post("/images/save-to-path",e)},saveAnnotatedImage:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(console.log("[API] 保存标注图像，图像ID: ".concat(e)),!e)return console.error("[API] 保存标注图像失败: 缺少图像ID"),Promise.reject(new Error("缺少图像ID"));if(t){console.log("[API] 检测到图像文件，使用文件上传方式");var n=new FormData;return n.append("file",t),d.post("/images/".concat(e,"/annotate"),n,{headers:{"Content-Type":"multipart/form-data"}})["catch"]((function(e){return console.error("[API] 文件上传方式保存标注失败:",e),Promise.reject(e)}))}var a=Date.now(),o={metadata_id:e,t:a};return console.log("[API] 调用标注保存API，参数:",o),Promise.any([l.get("/tags/generate-annotated-image/".concat(e),{params:(0,i.A)((0,i.A)({},o),{},{mode:"edit"}),timeout:1e4}).then((function(e){return console.log("[API] generate-annotated-image成功:",e.data),e}))["catch"]((function(e){return console.error("[API] generate-annotated-image失败:",e),Promise.reject(e)})),l.get("/tags/save-image-after-annotation/".concat(e),{params:o,timeout:1e4}).then((function(e){return console.log("[API] save-image-after-annotation成功:",e.data),e}))["catch"]((function(e){return console.error("[API] save-image-after-annotation失败:",e),Promise.reject(e)})),l.post("/tags/save-annotated-image",{metadata_id:e,timestamp:a},{timeout:1e4}).then((function(e){return console.log("[API] 直接保存标注成功:",e.data),e}))["catch"]((function(e){return console.error("[API] 直接保存标注失败:",e),Promise.reject(e)}))])["catch"]((function(e){return console.error("[API] 所有保存标注方法均失败:",e),{data:{success:!1,message:"标注保存失败，但允许继续",error:e.message||"所有API调用均失败"}}}))},delete:function(e){return console.log("删除图像ID: ".concat(e)),l["delete"]("/hemangioma-diagnoses/".concat(e))},update:function(e,t){return l.put("/images/".concat(e),t)},submitForReview:function(e){return l.post("/images/".concat(e,"/submit"))},reviewImage:function(e,t,n){return l.post("/images/".concat(e,"/review"),{approved:t,reviewNotes:n})}},(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)(a,"saveAnnotatedImage",(function(e){if(console.log("[API] 保存标注图像，图像ID: ".concat(e)),!e)return console.error("[API] 保存标注图像失败: 缺少图像ID"),Promise.reject(new Error("缺少图像ID"));var t=Date.now(),n={metadata_id:e,t};return console.log("[API] 调用标注保存API，参数:",n),l.get("/tags/generate-annotated-image/".concat(e),{params:(0,i.A)((0,i.A)({},n),{},{mode:"edit"})})["catch"]((function(t){return console.error("[API] 调用generate-annotated-image失败:",t),console.log("[API] 尝试备用端点 save-image-after-annotation"),l.get("/tags/save-image-after-annotation/".concat(e),{params:n})}))})),"deleteAnnotatedImage",(function(e){return l["delete"]("/images/annotated",{params:{path:e}})})),"getUserImages",(function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(n)e=n,console.log("getUserImages: 使用传入的用户ID:",e);else{var a=JSON.parse(localStorage.getItem("user")||"{}");e=a.customId||a.id,console.log("getUserImages: 从localStorage获取用户ID:",e)}var o={userId:e};t&&(o.status=t),console.log("获取用户图像，用户ID:",e,"状态:",t||"全部");var r="/medical/api/images/my-images";console.log("getUserImages使用完整路径:",r,"参数:",o);try{return u().get(r,{params:o})}catch(s){return console.error("获取用户图像失败:",s),Promise.reject(s)}})),"getTeamImages",(function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(t)e=t,console.log("getTeamImages: 使用传入的用户ID:",e);else{var n=JSON.parse(localStorage.getItem("user")||"{}");e=n.customId||n.id,console.log("getTeamImages: 从localStorage获取用户ID:",e)}var a={userId:e};console.log("获取团队图像，用户ID:",e);var o="/medical/api/images/team-images";console.log("getTeamImages使用完整路径:",o,"参数:",a);try{return u().get(o,{params:a})}catch(r){return console.error("获取团队图像失败:",r),Promise.reject(r)}})),"getPendingReviewImages",(function(){return l.get("/images/pending-review")})),"getReviewedImages",(function(){return l.get("/images/reviewed")})),"saveStructuredFormData",(function(e,t){var n=this;console.log("保存结构化表单数据，图像ID:",e);var a=t.annotations;a&&(console.log("表单包含标注数据:",a.length,"个标注"),delete t.annotations);var o=new URLSearchParams;return Object.keys(t).forEach((function(e){var n=t[e];Array.isArray(n)&&(n=n.join(",")),o.append(e,null==n?"":n)})),l.post("/images/".concat(e,"/structured-form"),o,{headers:{"Content-Type":"application/x-www-form-urlencoded"}}).then((function(t){if(console.log("表单数据保存成功:",t.data),a&&a.length>0){console.log("开始保存标注数据...");var o=JSON.parse(localStorage.getItem("user")||"{}"),r=a.map((function(t){var a={metadata_id:parseInt(e,10),tag:t.tag||t.name,x:t.x,y:t.y,width:t.width,height:t.height,created_by:o.id||3};return console.log("保存标注数据:",a),n.saveTag(a)}));return Promise.all(r).then((function(e){return console.log("所有标注保存成功:",e),t}))["catch"]((function(e){return console.error("保存标注数据失败:",e),t}))}return t}))})),"saveTag",(function(e){return console.log("保存单个标注:",e),l.post("/tags",e,{headers:{"Content-Type":"application/json"}})})),"updateStructuredFormData",(function(e,t){return console.log("API调用: 更新结构化表单数据，ID:",e,"数据:",t),l.put("/images/".concat(e,"/structured-form"),t)})),"markAsAnnotated",(function(e,t){console.log("API调用: 将图像标记为已标注，ID:",e,t?"时间戳:"+t:"");var n={};t&&(n.reviewTimestamp=t);var a="mark_annotated_".concat(e,"_").concat((new Date).getTime());if(window.pendingRequests&&window.pendingRequests[a])return console.warn("已有相同请求正在处理中，避免重复提交"),window.pendingRequests[a];var o=l.put("/images/".concat(e,"/mark-annotated"),null,{params:n}).then((function(e){return window.pendingRequests&&delete window.pendingRequests[a],e}))["catch"]((function(e){throw window.pendingRequests&&delete window.pendingRequests[a],e}));return window.pendingRequests||(window.pendingRequests={}),window.pendingRequests[a]=o,o})),(0,o.A)((0,o.A)((0,o.A)((0,o.A)(a,"updateStatus",(function(e,t){return console.log("API调用: 更新图像状态，ID:",e,"状态:",t),l.put("/images/".concat(e,"/status"),null,{params:{status:t}})})),"getFilteredImages",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=JSON.parse(localStorage.getItem("user")||"{}"),n=t.customId||t.id;if(!n)return console.error("获取筛选图像列表失败：未找到用户ID"),Promise.reject(new Error("用户未登录或ID不可用"));var a="true"===localStorage.getItem("directNavigation");if(a){localStorage.removeItem("directNavigation");var o=localStorage.getItem("lastSelectedStatus");o&&!e&&(e=o,console.log("使用保存的状态值:",e))}var r={userId:n};e&&(r.status=e),console.log("获取筛选图像列表，用户ID:",n,"状态:",e||"全部");var s="/medical/api/images/my-images";console.log("getFilteredImages使用路径:",s,"参数:",r);try{return u().get(s,{params:r}).then((function(e){return console.log("获取筛选图像列表成功，数量:",e.data?e.data.length:0),e}))}catch(i){return console.error("获取筛选图像列表失败:",i),Promise.reject(i)}})),"saveAnnotatedImage",(function(e){return console.log("保存标注图像，图像ID: ".concat(e)),e?l.get("/tags/save-image-after-annotation/".concat(e),{params:{metadata_id:e,t:Date.now()}}):Promise.reject(new Error("缺少图像ID"))})),"saveAnnotatedImage",(function(e){return console.log("保存标注图像，图像ID: ".concat(e)),e?l.get("/tags/generate-annotated-image/".concat(e),{params:{t:Date.now(),mode:"edit"}})["catch"]((function(t){return console.error("生成标注图像失败:",t),l.get("/tags/save-annotated-image/".concat(e),{params:{metadata_id:e,t:Date.now()}})})):Promise.reject(new Error("缺少图像ID"))}))),tags:{getByImageId:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"view";return console.log("获取图像标签，ID: ".concat(e,", 模式: ").concat(t)),l.get("/tags/image/".concat(e),{params:{mode:t}})},getById:function(e){return l.get("/tags/".concat(e))},create:function(e){console.log("创建新标签:",e);var t=(0,i.A)((0,i.A)({},e),{},{metadata_id:parseInt(e.metadata_id,10),created_by:parseInt(e.created_by,10)});return l.post("/tags",t)},update:function(e,t){return l.put("/tags/".concat(e),t)},delete:function(e){var t=JSON.parse(localStorage.getItem("user")||"{}"),n={};return t&&t.id&&(n.userId=t.id),t&&t.customId&&(n.customUserId=t.customId),console.log("删除标注，传递用户信息:",{标注ID:e,用户ID:n.userId,自定义ID:n.customUserId}),l["delete"]("/tags/".concat(e),{params:n})},deleteByImageId:function(e){return l["delete"]("/tags/image/".concat(e))},getByUserId:function(e){return l.get("/tags/user/".concat(e))},saveAnnotatedImage:function(e){return console.log("保存标注图像，图像ID: ".concat(e)),e?l.get("/tags/save-image-after-annotation/".concat(e),{params:{metadata_id:e,t:Date.now()}}):Promise.reject(new Error("缺少图像ID"))},getAnnotationDetail:function(e){return l.get("/annotations/".concat(e))}},annotations:{getAnnotationDetail:function(e){return l.get("/annotations/".concat(e))},saveAnnotatedImage:function(e){return console.log("使用统一的标注图像生成方法"),A.tags.saveAnnotatedImage(e)},updateAnnotatedImage:function(e,t){var n=JSON.parse(localStorage.getItem("user")||"{}"),a={_t:(new Date).getTime(),userId:n.customId||n.id||""};return n.role&&(a._role=n.role),l.put("/annotations/".concat(e,"/").concat(t),null,{params:a})},generateAnnotatedImage:function(e){return console.log("重定向到统一的标注图像生成方法"),A.tags.saveAnnotatedImage(e)}},imagePairs:{getAll:function(){return l.get("/image-pairs")},getByMetadataId:function(e){var t;console.log("获取图像对 - 认证调试:");try{var n=localStorage.getItem("user");n?(t=JSON.parse(n),console.log("用户信息:",{id:t.id,customId:t.customId,email:t.email,role:t.role})):console.warn("本地存储中没有用户信息")}catch(s){console.error("解析用户信息失败:",s)}if(e&&e.toString().length>9){var a=e.toString();return console.log("使用完整ID参数请求: ".concat(a)),l.get("/api/image-pairs/by-metadata?id=".concat(a))}var o="/api/images/".concat(e,"/image-pairs");console.log("尝试使用图像控制器API:",o);var r={};return t&&(r.params={_role:t.role||"USER",userId:t.customId||t.id,_t:(new Date).getTime()},console.log("添加查询参数:",r.params)),u().get(o,r)["catch"]((function(e){return console.error("获取图像对失败:",e),{data:[]}}))},getOne:function(e){return l.get("/image-pairs/".concat(e))},create:function(e){return l.post("/image-pairs",e)},update:function(e,t){return l.put("/image-pairs/".concat(e),t)},delete:function(e){return l["delete"]("/image-pairs/".concat(e))},deleteByMetadataId:function(e){return l["delete"]("/image-pairs/metadata/".concat(e))},deleteAnnotatedImage:function(e){return l["delete"]("/annotations/".concat(e))},upload:function(e){return d.post("/images/upload",e)},saveToPath:function(e){return d.post("/images/save-to-path",e)}},teamApplications:{getPendingApplications:function(){return l.get("/team-applications/pending")},getProcessedApplications:function(e){return e?l.get("/team-applications/processed?status=".concat(e)):l.get("/team-applications/processed")},processApplication:function(e,t){return console.log("调用处理申请API: ID=".concat(e,", 数据="),t),l.put("/team-applications/".concat(e),t)},getTeamApplications:function(e,t){var n="/team-applications/team/".concat(e);return t&&(n+="?status=".concat(t)),l.get(n)},getUserApplications:function(e){return l.get("/team-applications/user/".concat(e))},applyToJoinTeam:function(e,t){return l.post("/team-applications",{teamId:e,reason:t})}},imageLogs:{logOperation:function(e){return console.log("记录图像操作日志:",e),l.post("/image-logs/log",e)},getTimezoneInfo:function(){return l.get("/image-logs/timezone-info")}}};const I=A},44633:(e,t,n)=>{"use strict";n.d(t,{X2:()=>c,_m:()=>s,kL:()=>i});n(28706),n(74423),n(21699),n(11392);var a={MANAGE_USERS:"manage_users",MANAGE_TEAMS:"manage_teams",VIEW_STATS:"view_stats",VIEW_TEAM:"view_team",JOIN_TEAM:"join_team",LEAVE_TEAM:"leave_team",CREATE_CASE:"create_case",EDIT_CASE:"edit_case",DELETE_CASE:"delete_case",VIEW_ALL_CASES:"view_all_cases",VIEW_OWN_CASES:"view_own_cases",REVIEW_CASES:"review_cases",APPROVE_CASES:"approve_cases",ANNOTATE_IMAGES:"annotate_images",VIEW_OWN_ANNOTATIONS:"view_own_annotations",EDIT_OWN_ANNOTATIONS:"edit_own_annotations",UPLOAD_IMAGES:"upload_images",DELETE_IMAGES:"delete_images"},o={ADMIN:[a.MANAGE_USERS,a.MANAGE_TEAMS,a.VIEW_STATS,a.VIEW_TEAM,a.JOIN_TEAM,a.LEAVE_TEAM,a.CREATE_CASE,a.EDIT_CASE,a.DELETE_CASE,a.VIEW_ALL_CASES,a.VIEW_OWN_CASES,a.REVIEW_CASES,a.APPROVE_CASES,a.ANNOTATE_IMAGES,a.VIEW_OWN_ANNOTATIONS,a.EDIT_OWN_ANNOTATIONS,a.UPLOAD_IMAGES,a.DELETE_IMAGES],DOCTOR:[a.CREATE_CASE,a.EDIT_CASE,a.VIEW_OWN_CASES,a.ANNOTATE_IMAGES,a.VIEW_OWN_ANNOTATIONS,a.EDIT_OWN_ANNOTATIONS,a.UPLOAD_IMAGES,a.VIEW_TEAM,a.JOIN_TEAM,a.LEAVE_TEAM],REVIEWER:[a.VIEW_ALL_CASES,a.VIEW_OWN_CASES,a.REVIEW_CASES,a.APPROVE_CASES,a.ANNOTATE_IMAGES,a.VIEW_OWN_ANNOTATIONS,a.EDIT_OWN_ANNOTATIONS,a.CREATE_CASE,a.EDIT_CASE,a.VIEW_TEAM,a.JOIN_TEAM,a.LEAVE_TEAM]},r={"/app/dashboard":["ADMIN","DOCTOR","REVIEWER"],"/app/users":["ADMIN"],"/app/teams":["ADMIN","DOCTOR","REVIEWER"],"/app/teams/join":["ADMIN","DOCTOR","REVIEWER"],"/app/teams/view":["ADMIN","DOCTOR","REVIEWER"],"/app/cases":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/new":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/edit":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/view":["ADMIN","DOCTOR","REVIEWER"],"/app/cases/form":["ADMIN","DOCTOR","REVIEWER"],"/app/case":["ADMIN","DOCTOR","REVIEWER"],"/app/review":["ADMIN","REVIEWER"],"/app/annotation-reviews":["ADMIN","REVIEWER"],"/app/images/upload":["ADMIN","DOCTOR","REVIEWER"],"/app/hemangioma-diagnosis":["ADMIN","DOCTOR","REVIEWER"],"/app/profile":["ADMIN","DOCTOR","REVIEWER"],"/admin":["ADMIN"]};function s(e,t){var n;return e&&(null===(n=o[e])||void 0===n?void 0:n.includes(t))||!1}function i(e,t){if(!e||!t)return console.log("[权限检查] 缺少用户角色或路由路径: 角色=".concat(e,", 路径=").concat(t)),!1;if(console.log("[权限检查] 检查用户角色 ".concat(e," 是否可以访问路径 ").concat(t)),r[t]){var n=r[t].includes(e);return console.log("[权限检查] 精确匹配结果: ".concat(n?"有权访问":"无权访问")),n}for(var a in r)if(t.startsWith(a)){var o=r[a].includes(e);if(console.log("[权限检查] 模糊匹配 ".concat(a,": ").concat(o?"有权访问":"无权访问")),o)return!0}return console.log("[权限检查] 未找到匹配的路由权限配置，默认无权访问"),!1}function c(e,t,n){return e===t||("ADMIN"===n||!("REVIEWER"!==n||!s(n,a.VIEW_ALL_CASES)))}},48053:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Size:()=>m,TOKEN:()=>A,TOOLTIP_INJECTION_KEY:()=>I,buildProp:()=>p,buildProps:()=>f,default:()=>b,definePropType:()=>h,isArray:()=>l,isBoolean:()=>i,isDate:()=>d,isFunction:()=>u,isNumber:()=>s,isObject:()=>c,isPromise:()=>g,isString:()=>r,mutable:()=>v,tryNextPath:()=>o});var a=n(55593);n(52675),n(89463),n(74423),n(64346),n(23288),n(26099),n(21699);function o(e,t){return!e||t>=e.length?(console.warn("模块路径解析失败",e,t),""):e[t]}"undefined"!==typeof window&&(window.tryNextPath=o),"undefined"!==typeof n.g&&(n.g.tryNextPath=o);var r=function(e){return"string"===typeof e},s=function(e){return"number"===typeof e},i=function(e){return"boolean"===typeof e},c=function(e){return null!==e&&"object"===(0,a.A)(e)},u=function(e){return"function"===typeof e},l=Array.isArray,d=function(e){return e instanceof Date},g=function(e){return c(e)&&u(e.then)&&u(e["catch"])},m={LARGE:"large",DEFAULT:"default",SMALL:"small"},p=function(e,t){return e},f=function(e){return e},h=function(e){return e},v=function(e){return e},A=Symbol("token"),I=Symbol("tooltip"),w=function(){try{if("undefined"!==typeof window){window.__EP_UTILS_TYPES__={isString:r,isNumber:s,isBoolean:i,isObject:c,isFunction:u},window.__EP_VIRTUAL_MODULES__={};var e=window.__webpack_require__||function(){};window.__webpack_require__&&(window.__webpack_require__=function(t){try{return e(t)}catch(n){if(n.message&&n.message.includes("Cannot find module"))return console.warn("Element Plus模块加载失败:",n.message),{};throw n}})}}catch(t){console.warn("Element Plus兼容性修复应用失败",t)}};w();const b={tryNextPath:o,isString:r,isNumber:s,isBoolean:i,isObject:c,isFunction:u,isArray:l,isDate:d,isPromise:g,Size:m,buildProp:p,buildProps:f,definePropType:h,mutable:v,TOKEN:A,TOOLTIP_INJECTION_KEY:I}},68039:(e,t,n)=>{"use strict";n.d(t,{b:()=>o,v:()=>a});n(60739),n(33110),n(79432);var a={saveToStorage:function(e,t){try{return localStorage.setItem(e,JSON.stringify(t)),!0}catch(n){return console.error("保存数据到本地存储失败: ".concat(e),n),!1}},getFromStorage:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{var n=localStorage.getItem(e);return n?JSON.parse(n):t}catch(a){return console.error("从本地存储获取数据失败: ".concat(e),a),t}},removeFromStorage:function(e){try{return localStorage.removeItem(e),!0}catch(t){return console.error("从本地存储中移除数据失败: ".concat(e),t),!1}}},o={start:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"setLoading",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"setError";e(t,!0),n&&e(n,null)},end:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"setLoading";e(t,!1)},error:function(e,t){var n,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"setError",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"setLoading";if(t.response){var r=t.response.data;n="string"===typeof r?r:r&&r.message?r.message:r&&r.error?r.error:t.response.statusText}else n=t.message?t.message:"未知错误";return e(a,n),e(o,!1),console.error("操作失败:",t),t.response&&console.error("错误响应:",t.response.status,t.response.data),n}}},69018:(e,t,n)=>{"use strict";n.d(t,{AW:()=>r,h3:()=>s});n(28706),n(74423),n(79432),n(26099),n(21699);var a=window.fetch,o=window.XMLHttpRequest,r=function(){return window.ANNOTATION_DEBUG_ENABLED=!0,console.log("%c标注调试模式已启用","background: #4CAF50; color: white; font-size: 16px; padding: 3px 8px; border-radius: 4px;"),window.fetch=function(e,t){if("string"===typeof e&&(e.includes("/api/tags")||e.includes("/api/api"))){if(console.group("%c[标注调试] Fetch 请求","background: #2196F3; color: white; font-size: 14px; padding: 2px 5px;"),console.log("URL:",e),console.log("选项:",t),t&&t.body)try{var n=JSON.parse(t.body);console.log("请求体:",n)}catch(o){console.log("请求体 (无法解析):",t.body)}console.groupEnd()}return a.apply(this,arguments).then((function(t){if("string"===typeof e&&(e.includes("/api/tags")||e.includes("/api/api"))){console.group("%c[标注调试] Fetch 响应","background: #4CAF50; color: white; font-size: 14px; padding: 2px 5px;"),console.log("状态:",t.status,t.statusText),console.log("响应头:",t.headers);var n=t.clone();n.json().then((function(e){console.log("响应数据:",e),console.groupEnd()}))["catch"]((function(){console.log("响应数据: 无法解析为JSON"),console.groupEnd()}))}return t}))["catch"]((function(t){throw"string"===typeof e&&(e.includes("/api/tags")||e.includes("/api/api"))&&(console.group("%c[标注调试] Fetch 错误","background: #F44336; color: white; font-size: 14px; padding: 2px 5px;"),console.error("错误信息:",t),console.groupEnd()),t}))},window.XMLHttpRequest=function(){var e=new o,t=e.open,n=e.send,a="",r="";return e.open=function(e,n){return a=n,r=e,"string"===typeof n&&(n.includes("/api/tags")||n.includes("/api/api"))&&(console.group("%c[标注调试] XHR 请求","background: #673AB7; color: white; font-size: 14px; padding: 2px 5px;"),console.log("URL:",n),console.log("方法:",e)),t.apply(this,arguments)},e.send=function(e){if("string"===typeof a&&(a.includes("/api/tags")||a.includes("/api/api"))&&e)try{var t=JSON.parse(e);console.log("请求体:",t)}catch(o){console.log("请求体 (无法解析):",e)}return n.apply(this,arguments)},e.addEventListener("load",(function(){if("string"===typeof a&&(a.includes("/api/tags")||a.includes("/api/api"))){if(console.log("状态:",e.status,e.statusText),e.responseText)try{var t=JSON.parse(e.responseText);console.log("响应数据:",t)}catch(n){console.log("响应数据 (无法解析):",e.responseText)}console.groupEnd()}})),e.addEventListener("error",(function(){"string"===typeof a&&(a.includes("/api/tags")||a.includes("/api/api"))&&(console.group("%c[标注调试] XHR 错误","background: #F44336; color: white; font-size: 14px; padding: 2px 5px;"),console.error("请求失败:",{url:a,method:r,status:e.status,statusText:e.statusText,responseText:e.responseText}),console.groupEnd())})),e},!0},s=function(e){return console.group("%c标注数据详情","background: #FF9800; color: white; font-size: 14px; padding: 3px 8px;"),console.log("标签类型:",e.tag),console.log("坐标:","(".concat(e.x,", ").concat(e.y,")")),console.log("尺寸:","".concat(e.width," x ").concat(e.height)),console.log("图像ID:",e.metadata_id),console.log("创建者:",e.created_by),console.groupEnd(),{标签类型:e.tag,中心点X坐标:e.x,中心点Y坐标:e.y,宽度:e.width,高度:e.height,图像ID:e.metadata_id,创建者ID:e.created_by}}},72068:(e,t,n)=>{n(23792),n(3362),n(69085),n(9391),console.log("正在启动应用，应用模块修复..."),n(48053),n(10014)},81052:(e,t,n)=>{"use strict";n.d(t,{H$:()=>s,JR:()=>a,M8:()=>u,T_:()=>o,XO:()=>c,fz:()=>i});n(28706),n(59089);var a="",o="/medical",r="/api",s="".concat(a).concat(o).concat(r),i="".concat(o,"/api/stats-v2/dashboard"),c=function(e){var t=Date.now(),n=Math.random();return"".concat(i,"/").concat(e,"?t=").concat(t,"&r=").concat(n,"&forcePersonalStats=true&view=personal")},u=function(){return!0}}},t={};function n(a){var o=t[a];if(void 0!==o)return o.exports;var r=t[a]={exports:{}};return e[a].call(r.exports,r,r.exports,n),r.exports}n.m=e,(()=>{var e=[];n.O=(t,a,o,r)=>{if(!a){var s=1/0;for(l=0;l<e.length;l++){for(var[a,o,r]=e[l],i=!0,c=0;c<a.length;c++)(!1&r||s>=r)&&Object.keys(n.O).every((e=>n.O[e](a[c])))?a.splice(c--,1):(i=!1,r<s&&(s=r));if(i){e.splice(l--,1);var u=o();void 0!==u&&(t=u)}}return t}r=r||0;for(var l=e.length;l>0&&e[l-1][2]>r;l--)e[l]=e[l-1];e[l]=[a,o,r]}})(),(()=>{n.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return n.d(t,{a:t}),t}})(),(()=>{n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}})(),(()=>{n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,a)=>(n.f[a](e,t),t)),[]))})(),(()=>{n.u=e=>"js/"+e+"."+{9:"a8cdb7fa",35:"0d08ecdb",52:"8a412771",80:"7320b977",86:"01342c10",110:"9a6803eb",191:"7d6c1f07",282:"8b1cf9e0",301:"13bfe164",344:"e01b8cd7",359:"7209d510",381:"cd9492de",402:"78ca37b4",453:"aad7fb15",556:"eb2b18ef",669:"8db92e4a",751:"e1cc22ae",763:"c78ee3c3",976:"e6e99f46",989:"bf4e9507"}[e]+".js"})(),(()=>{n.miniCssF=e=>"css/"+e+"."+{9:"31434080",35:"7abf8db7",52:"bb541bc8",80:"50e3f2d8",110:"308ad403",191:"63574b3d",282:"99991a56",301:"92c0830f",344:"99c5710a",359:"e86ff1d2",381:"02384c7a",402:"4008058f",453:"44d27529",556:"2974cbd2",669:"6fb84a2d",751:"9a68c00b",763:"0d375eec",838:"53b09eb3",976:"f143d9f0",989:"12cb3ed1"}[e]+".css"})(),(()=>{n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="medical-annotation-frontend:";n.l=(a,o,r,s)=>{if(e[a])e[a].push(o);else{var i,c;if(void 0!==r)for(var u=document.getElementsByTagName("script"),l=0;l<u.length;l++){var d=u[l];if(d.getAttribute("src")==a||d.getAttribute("data-webpack")==t+r){i=d;break}}i||(c=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+r),i.src=a),e[a]=[o];var g=(t,n)=>{i.onerror=i.onload=null,clearTimeout(m);var o=e[a];if(delete e[a],i.parentNode&&i.parentNode.removeChild(i),o&&o.forEach((e=>e(n))),t)return t(n)},m=setTimeout(g.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=g.bind(null,i.onerror),i.onload=g.bind(null,i.onload),c&&document.head.appendChild(i)}}})(),(()=>{n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{n.p="/"})(),(()=>{if("undefined"!==typeof document){var e=(e,t,a,o,r)=>{var s=document.createElement("link");s.rel="stylesheet",s.type="text/css",n.nc&&(s.nonce=n.nc);var i=n=>{if(s.onerror=s.onload=null,"load"===n.type)o();else{var a=n&&n.type,i=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+i+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=a,c.request=i,s.parentNode&&s.parentNode.removeChild(s),r(c)}};return s.onerror=s.onload=i,s.href=t,a?a.parentNode.insertBefore(s,a.nextSibling):document.head.appendChild(s),s},t=(e,t)=>{for(var n=document.getElementsByTagName("link"),a=0;a<n.length;a++){var o=n[a],r=o.getAttribute("data-href")||o.getAttribute("href");if("stylesheet"===o.rel&&(r===e||r===t))return o}var s=document.getElementsByTagName("style");for(a=0;a<s.length;a++){o=s[a],r=o.getAttribute("data-href");if(r===e||r===t)return o}},a=a=>new Promise(((o,r)=>{var s=n.miniCssF(a),i=n.p+s;if(t(s,i))return o();e(a,i,null,o,r)})),o={524:0};n.f.miniCss=(e,t)=>{var n={9:1,35:1,52:1,80:1,110:1,191:1,282:1,301:1,344:1,359:1,381:1,402:1,453:1,556:1,669:1,751:1,763:1,838:1,976:1,989:1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=a(e).then((()=>{o[e]=0}),(t=>{throw delete o[e],t})))}}})(),(()=>{var e={524:0};n.f.j=(t,a)=>{var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)a.push(o[2]);else if(838!=t){var r=new Promise(((n,a)=>o=e[t]=[n,a]));a.push(o[2]=r);var s=n.p+n.u(t),i=new Error,c=a=>{if(n.o(e,t)&&(o=e[t],0!==o&&(e[t]=void 0),o)){var r=a&&("load"===a.type?"missing":a.type),s=a&&a.target&&a.target.src;i.message="Loading chunk "+t+" failed.\n("+r+": "+s+")",i.name="ChunkLoadError",i.type=r,i.request=s,o[1](i)}};n.l(s,c,"chunk-"+t,t)}else e[t]=0},n.O.j=t=>0===e[t];var t=(t,a)=>{var o,r,[s,i,c]=a,u=0;if(s.some((t=>0!==e[t]))){for(o in i)n.o(i,o)&&(n.m[o]=i[o]);if(c)var l=c(n)}for(t&&t(a);u<s.length;u++)r=s[u],n.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return n.O(l)},a=self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var a=n.O(void 0,[504],(()=>n(72068)));a=n.O(a)})();
//# sourceMappingURL=app.a7d91276.js.map