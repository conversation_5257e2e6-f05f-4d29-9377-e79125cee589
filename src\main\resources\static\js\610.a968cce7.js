"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[610],{16610:(e,a,t)=>{t.r(a),t.d(a,{default:()=>S});var i=t(20641),n=t(90033),s=t(53751),r={class:"case-form-container"},l={class:"upload-section"},o={key:0,class:"upload-placeholder"},c={key:1,class:"image-preview-wrapper"},d=["src"],u={class:"image-info"},g={class:"file-name"},p={class:"file-size"},m={class:"image-overlay"},h={class:"overlay-content"},f={key:0,class:"action-bar"},v={class:"processing-content"};function F(e,a,t,F,I,P){var k=(0,i.g2)("el-button"),w=(0,i.g2)("el-progress"),b=(0,i.g2)("el-form-item"),_=(0,i.g2)("el-form"),y=(0,i.g2)("el-dialog");return(0,i.uX)(),(0,i.CE)("div",r,[a[9]||(a[9]=(0,i.Lk)("div",{class:"page-header"},[(0,i.Lk)("h2",null,"新建标注")],-1)),(0,i.bF)(_,{ref:"form",model:I.caseData,"label-width":"100px"},{default:(0,i.k6)((function(){return[(0,i.bF)(b,{label:"医学影像",prop:"images"},{default:(0,i.k6)((function(){return[(0,i.Lk)("div",l,[(0,i.Lk)("div",{class:(0,n.C4)(["upload-box",{"has-image":I.imagePreview}]),onClick:a[1]||(a[1]=function(){return P.triggerFileInput&&P.triggerFileInput.apply(P,arguments)})},[(0,i.Lk)("input",{type:"file",id:"standardFileInput",accept:"image/*",onChange:a[0]||(a[0]=function(){return P.handleFileSelect&&P.handleFileSelect.apply(P,arguments)}),style:{display:"none"}},null,32),I.imagePreview?((0,i.uX)(),(0,i.CE)("div",c,[(0,i.Lk)("img",{src:I.imagePreview,class:"preview-image"},null,8,d),(0,i.Lk)("div",u,[(0,i.Lk)("div",g,(0,n.v_)(I.selectedFileName),1),(0,i.Lk)("div",p,(0,n.v_)(P.formatFileSize(I.selectedFileSize)),1)]),(0,i.Lk)("div",m,[(0,i.Lk)("div",h,[(0,i.bF)(k,{type:"text",icon:"el-icon-refresh-right",onClick:(0,s.D$)(P.triggerFileInput,["stop"])},{default:(0,i.k6)((function(){return a[3]||(a[3]=[(0,i.eW)("重新选择")])})),_:1,__:[3]},8,["onClick"])])])])):((0,i.uX)(),(0,i.CE)("div",o,a[2]||(a[2]=[(0,i.Lk)("i",{class:"el-icon-upload"},null,-1),(0,i.Lk)("div",{class:"upload-text"},"点击选择图片或拖拽图片到此处",-1),(0,i.Lk)("div",{class:"upload-hint"},"支持JPG、PNG格式，图片将自动调整至最佳大小",-1)])))],2),I.imagePreview?((0,i.uX)(),(0,i.CE)("div",f,[(0,i.bF)(k,{type:"primary",onClick:P.handleNext,disabled:I.isUploading,class:"action-button"},{default:(0,i.k6)((function(){return a[4]||(a[4]=[(0,i.Lk)("i",{class:"el-icon-right"},null,-1),(0,i.eW)(" 下一步 ")])})),_:1,__:[4]},8,["onClick","disabled"]),(0,i.bF)(k,{onClick:P.clearSelectedImage,disabled:I.isUploading,class:"action-button"},{default:(0,i.k6)((function(){return a[5]||(a[5]=[(0,i.Lk)("i",{class:"el-icon-delete"},null,-1),(0,i.eW)(" 清除 ")])})),_:1,__:[5]},8,["onClick","disabled"])])):(0,i.Q3)("",!0),I.uploadProgress>0&&I.uploadProgress<100?((0,i.uX)(),(0,i.Wv)(w,{key:1,percentage:I.uploadProgress,"stroke-width":6,status:"success",class:"upload-progress"},null,8,["percentage"])):(0,i.Q3)("",!0)])]})),_:1}),(0,i.bF)(b,null,{default:(0,i.k6)((function(){return[(0,i.bF)(k,{onClick:P.handleReturn},{default:(0,i.k6)((function(){return a[6]||(a[6]=[(0,i.eW)("返回首页")])})),_:1,__:[6]},8,["onClick"])]})),_:1})]})),_:1},8,["model"]),(0,i.bF)(y,{title:"图片处理中",visible:I.processingDialog,"close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1,width:"400px",center:""},{default:(0,i.k6)((function(){return[(0,i.Lk)("div",v,[a[7]||(a[7]=(0,i.Lk)("i",{class:"el-icon-loading loading-icon"},null,-1)),a[8]||(a[8]=(0,i.Lk)("p",{class:"processing-text"},"正在处理图片，请稍候...",-1)),(0,i.bF)(w,{percentage:I.processingProgress,"stroke-width":6},null,8,["percentage"])])]})),_:1},8,["visible"])])}var I=t(14048),P=t(30388),k=t(41034),w=(t(16280),t(76918),t(28706),t(51629),t(44114),t(59089),t(60739),t(23288),t(62010),t(33110),t(9868),t(26099),t(27495),t(38781),t(47764),t(71761),t(62953),t(76031),t(3296),t(27208),t(48408),t(14603),t(47566),t(98721),t(653)),b=t(40834);t(72505);const _={name:"CaseForm",data:function(){return{caseData:{images:[]},dialogVisible:!1,dialogImageUrl:"",uploadedImageId:null,imagePreview:null,selectedFileName:"",selectedFileSize:0,selectedFile:null,isUploading:!1,uploadProgress:0,processingDialog:!1,processingProgress:0,maxImageWidth:700,maxImageHeight:700,processedFilePath:null,lastServerResponse:null}},mounted:function(){var e=this,a=document.querySelector(".upload-box");a&&(["dragenter","dragover","dragleave","drop"].forEach((function(t){a.addEventListener(t,e.preventDefaults,!1)})),["dragenter","dragover"].forEach((function(t){a.addEventListener(t,e.highlight,!1)})),["dragleave","drop"].forEach((function(t){a.addEventListener(t,e.unhighlight,!1)})),a.addEventListener("drop",this.handleDrop,!1))},methods:(0,k.A)((0,k.A)({},(0,b.i0)(["saveProgress"])),{},{preventDefaults:function(e){e.preventDefault(),e.stopPropagation()},highlight:function(){document.querySelector(".upload-box").classList.add("highlight")},unhighlight:function(){document.querySelector(".upload-box").classList.remove("highlight")},handleDrop:function(e){var a=e.dataTransfer,t=a.files;t&&t.length&&this.handleFiles(t[0])},handleFiles:function(e){e&&(e.type.match("image.*")?(this.selectedFile=e,this.selectedFileName=e.name,this.selectedFileSize=e.size,this.processImage(e)):this.$message.error("请选择图片文件"))},triggerFileInput:function(){document.getElementById("standardFileInput").click()},handleFileSelect:function(e){var a=e.target.files[0];a&&this.handleFiles(a)},clearSelectedImage:function(){this.imagePreview=null,this.selectedFile=null,this.selectedFileName="",this.selectedFileSize=0,document.getElementById("standardFileInput").value=""},processImage:function(e){var a=this,t=new FileReader;t.onload=function(t){var i=new Image;i.onload=function(){var n=i.width,s=i.height;n>a.maxImageWidth||s>a.maxImageHeight?(a.processingDialog=!0,a.processingProgress=10,setTimeout((function(){a.processingProgress=30;var t=document.createElement("canvas"),r=t.getContext("2d"),l=Math.min(a.maxImageWidth/n,a.maxImageHeight/s),o=n*l,c=s*l;t.width=o,t.height=c,a.processingProgress=60,r.drawImage(i,0,0,o,c),a.processingProgress=90;var d=t.toDataURL(e.type);a.imagePreview=d,t.toBlob((function(t){a.selectedFile=new File([t],e.name,{type:e.type,lastModified:(new Date).getTime()}),a.processingProgress=100,a.processingDialog=!1}),e.type)}),300)):a.imagePreview=t.target.result},i.src=t.target.result},t.onerror=function(e){a.$message.error("读取文件失败")},t.readAsDataURL(e)},formatFileSize:function(e){return e<1024?e+" bytes":e<1048576?(e/1024).toFixed(2)+" KB":(e/1048576).toFixed(2)+" MB"},processImageFile:function(e){var a=this;return(0,P.A)((0,I.A)().mark((function t(){return(0,I.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,i){var n=new FileReader;n.onload=function(n){var s=new Image;s.onload=function(){try{var n=s.width,r=s.height,l=700,o=700;if(n>l||r>o){var c=Math.min(l/n,o/r),d=Math.floor(n*c),u=Math.floor(r*c),g=document.createElement("canvas"),p=g.getContext("2d");g.width=d,g.height=u,p.drawImage(s,0,0,d,u);var m=.85;g.toBlob((function(n){if(n){var s=new File([n],"temp_".concat(e.name),{type:e.type||"image/jpeg",lastModified:(new Date).getTime()});s.size>3145728?g.toBlob((function(n){var s=new File([n],"temp_".concat(e.name),{type:"image/jpeg",lastModified:(new Date).getTime()});a.saveProcessedFile(s,t,i)}),"image/jpeg",.7):a.saveProcessedFile(s,t,i)}else i(new Error("图像处理失败"))}),e.type||"image/jpeg",m)}else a.saveProcessedFile(e,t,i)}catch(h){i(h)}},s.onerror=function(){i(new Error("图片加载失败"))},s.src=n.target.result},n.onerror=function(){i(new Error("读取文件失败"))},n.readAsDataURL(e)})));case 1:case"end":return t.stop()}}),t)})))()},saveProcessedFile:function(e,a,t){var i=this,n=new FormData;n.append("file",e),n.append("targetPath","F:/血管瘤辅助系统/medical_images/temp"),w["default"].images.saveToPath(n).then((function(t){i.processedFilePath=t.data.filePath||"F:\\血管瘤辅助系统\\medical_images\\temp\\temp_".concat(e.name),i.lastServerResponse=t.data,t.data.metadataId,a(e)}))["catch"]((function(t){a(e)}))},handleNext:function(){var e=this;return(0,P.A)((0,I.A)().mark((function a(){var t,i,n,s,r,l,o;return(0,I.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,e.selectedFile){a.next=4;break}return e.$message.warning("请先选择图片"),a.abrupt("return");case 4:return e.isUploading=!0,e.uploadProgress=0,a.next=8,e.processImageFile(e.selectedFile);case 8:t=a.sent,e.processedFilePath&&localStorage.setItem("processedFilePath",e.processedFilePath),i={id:null,metadataId:null,imagePairId:null,path:null,filePath:null},e.lastServerResponse&&e.lastServerResponse.metadataId?i={id:e.lastServerResponse.id||e.lastServerResponse.metadataId,metadataId:e.lastServerResponse.metadataId,imagePairId:e.lastServerResponse.imagePairId,path:e.lastServerResponse.webPath,filePath:e.lastServerResponse.filePath}:(n=Date.now().toString(),i={id:n,metadataId:n,path:URL.createObjectURL(t),filePath:e.processedFilePath||"F:\\血管瘤辅助系统\\medical_images\\temp\\temp_".concat(e.selectedFileName)});try{s=2,r=1024*s*1024,t.size>r?(localStorage.setItem("offline_image_info_".concat(i.metadataId),JSON.stringify({size:t.size,name:t.name,type:t.type,useTempUrl:!0,serverMetadataId:i.metadataId})),e.uploadProgress=100):(l=new FileReader,l.onload=function(a){try{localStorage.setItem("offline_image_".concat(i.metadataId),a.target.result)}catch(t){localStorage.removeItem("offline_image_".concat(i.metadataId))}e.uploadProgress=100},l.readAsDataURL(t))}catch(c){e.uploadProgress=100}o=i.metadataId,e.uploadedImageId=o,localStorage.setItem("lastUploadedImageId",o),e.saveProgress({step:1,imageId:o,formData:null,testMode:!1,imagePath:i.path,filePath:i.filePath}),e.$message.success("图像处理成功，正在进入标注界面..."),setTimeout((function(){e.$router.push({path:"/app/cases/form",query:{imageId:o,testMode:"false",filePath:encodeURIComponent(i.filePath),ts:(new Date).getTime()}})}),500),a.next=27;break;case 22:a.prev=22,a.t0=a["catch"](0),e.$message.error("处理失败，请重试: "+(a.t0.message||"未知错误")),e.isUploading=!1;case 27:case"end":return a.stop()}}),a,null,[[0,22]])})))()},handleReturn:function(){this.$router.push("/app/dashboard")}})};var y=t(66262);const L=(0,y.A)(_,[["render",F],["__scopeId","data-v-1491d399"]]),S=L}}]);