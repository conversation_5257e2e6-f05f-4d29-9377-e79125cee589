package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.ImageMetadataRepository;
import com.medical.annotation.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 图像控制器 - 用于记录图像操作日志和图像查询
 */
@RestController
@RequestMapping("/api")
public class ImageController {

    @Autowired
    private ImageMetadataRepository imageMetadataRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 记录图像操作日志
     */
    @PostMapping("/image-logs/log")
    public ResponseEntity<?> logImageOperation(@RequestBody Map<String, Object> logData) {
        try {
            // 获取当前中国时间
            ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
            String formattedTime = chinaTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            // 记录请求数据
            System.out.println("===== 图像操作日志 [" + formattedTime + "] =====");
            System.out.println("操作类型: " + logData.getOrDefault("operation", "未知操作"));
            System.out.println("用户ID: " + logData.getOrDefault("userId", "未知用户"));
            System.out.println("用户自定义ID: " + logData.getOrDefault("userCustomId", "未知"));
            System.out.println("图像ID: " + logData.getOrDefault("imageId", "未知"));
            System.out.println("操作详情: " + logData.getOrDefault("details", "无"));
            
            // 验证用户信息
            String userCustomId = (String) logData.get("userCustomId");
            if (userCustomId != null && !userCustomId.isEmpty()) {
                Optional<User> userOptional = userRepository.findByCustomId(userCustomId);
                User user = userOptional.orElse(null);
                if (user != null) {
                    System.out.println("验证用户: " + user.getName() + " (ID: " + user.getId() + ", 自定义ID: " + user.getCustomId() + ")");
                } else {
                    System.out.println("警告: 未找到自定义ID为 " + userCustomId + " 的用户");
                }
            }
            
            // 验证图像信息
            Long imageId = null;
            try {
                if (logData.get("imageId") != null) {
                    imageId = Long.parseLong(logData.get("imageId").toString());
                }
            } catch (NumberFormatException e) {
                System.out.println("警告: 图像ID格式无效: " + logData.get("imageId"));
            }
            
            if (imageId != null) {
                Optional<ImageMetadata> imageOptional = imageMetadataRepository.findById(imageId);
                ImageMetadata image = imageOptional.orElse(null);
                if (image != null) {
                    System.out.println("验证图像: ID=" + image.getId() + ", 文件名=" + image.getFilename());
                    System.out.println("图像上传者: " + (image.getUploadedBy() != null ? image.getUploadedBy().getName() : "未知") + 
                                     " (ID: " + (image.getUploadedBy() != null ? image.getUploadedBy().getId() : "未知") + 
                                     ", 自定义ID: " + (image.getUploadedBy() != null ? image.getUploadedBy().getCustomId() : "未知") + ")");
                    System.out.println("图像创建时间: " + image.getCreatedAt());
                    System.out.println("图像更新时间: " + image.getUpdatedAt());
                } else {
                    System.out.println("警告: 未找到ID为 " + imageId + " 的图像");
                }
            }
            
            // 返回成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("timestamp", formattedTime);
            response.put("message", "日志记录成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("记录图像操作日志失败: " + e.getMessage());
            e.printStackTrace();
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "日志记录失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
    
    /**
     * 获取图像时区信息
     */
    @GetMapping("/image-logs/timezone-info")
    public ResponseEntity<?> getTimezoneInfo() {
        Map<String, Object> response = new HashMap<>();
        
        // 获取当前中国时间
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        String formattedTime = chinaTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        // 获取UTC时间
        ZonedDateTime utcTime = ZonedDateTime.now(ZoneId.of("UTC"));
        String formattedUtcTime = utcTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        response.put("chinaTime", formattedTime);
        response.put("utcTime", formattedUtcTime);
        response.put("timeZone", "Asia/Shanghai");
        response.put("offset", "+08:00");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 通过格式化ID获取图像
     */
    @GetMapping("/images/by-formatted-id/{formattedId}")
    public ResponseEntity<?> getImageByFormattedId(@PathVariable String formattedId) {
        try {
            System.out.println("通过格式化ID查询图像: " + formattedId);
            
            // 查询数据库
            Optional<ImageMetadata> imageOpt = imageMetadataRepository.findByFormattedId(formattedId);
            
            // 如果找到图像，返回图像数据
            if (imageOpt.isPresent()) {
                ImageMetadata image = imageOpt.get();
                System.out.println("找到格式化ID为 " + formattedId + " 的图像, ID: " + image.getId());
                return ResponseEntity.ok(image);
            } else {
                System.out.println("未找到格式化ID为 " + formattedId + " 的图像");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.err.println("通过格式化ID查询图像失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 通过ID获取图像 - 备用API
     */
    @GetMapping("/image-metadata/{id}")
    public ResponseEntity<?> getImageMetadata(@PathVariable Long id) {
        try {
            System.out.println("通过ID查询图像元数据: " + id);
            
            // 查询数据库
            Optional<ImageMetadata> imageOpt = imageMetadataRepository.findById(id);
            
            // 如果找到图像，返回图像数据
            if (imageOpt.isPresent()) {
                ImageMetadata image = imageOpt.get();
                System.out.println("找到ID为 " + id + " 的图像元数据");
                return ResponseEntity.ok(image);
            } else {
                System.out.println("未找到ID为 " + id + " 的图像元数据");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.err.println("查询图像元数据失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body("查询失败: " + e.getMessage());
        }
    }
} 