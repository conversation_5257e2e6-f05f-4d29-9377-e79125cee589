{"version": 3, "file": "js/282.8b1cf9e0.js", "mappings": "gMACOA,MAAM,oB,GADbC,IAAA,EA+EoCD,MAAM,yB,GA4B5BA,MAAM,iB,ucA1GlBE,EAAAA,EAAAA,IAgHM,MAhHNC,EAgHM,gBA/GJC,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,eAAa,EACtBI,EAAAA,EAAAA,IAAa,UAAT,UAAI,KAGVC,EAAAA,EAAAA,IAgEUC,EAAA,CAtEdC,WAMsBC,EAAAC,UANtB,sBAAAC,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAMsBH,EAAAC,UAASE,CAAA,I,CAN/B,SAAAC,EAAAA,EAAAA,KAOM,iBA4Bc,EA5BdP,EAAAA,EAAAA,IA4BcQ,EAAA,CA5BDC,MAAM,MAAMC,KAAK,W,CAPpC,SAAAH,EAAAA,EAAAA,KAQQ,iBAiBW,uBAjBXI,EAAAA,EAAAA,IAiBWC,EAAA,CAjB4BC,KAAMV,EAAAW,eAAgBC,MAAA,gB,CARrE,SAAAR,EAAAA,EAAAA,KASU,iBAA0D,EAA1DP,EAAAA,EAAAA,IAA0DgB,EAAA,CAAzCC,KAAK,SAASR,MAAM,OAAOS,MAAM,SAClDlB,EAAAA,EAAAA,IAA+DgB,EAAA,CAA9CC,KAAK,cAAcR,MAAM,OAAOS,MAAM,SACvDlB,EAAAA,EAAAA,IAAgDgB,EAAA,CAA/BC,KAAK,aAAaR,MAAM,QACzCT,EAAAA,EAAAA,IAA0CgB,EAAA,CAAzBC,KAAK,OAAOR,MAAM,QACnCT,EAAAA,EAAAA,IAAkDgB,EAAA,CAAjCC,KAAK,aAAaR,MAAM,UACzCT,EAAAA,EAAAA,IAAgDgB,EAAA,CAA/BC,KAAK,YAAYR,MAAM,SACxCT,EAAAA,EAAAA,IASkBgB,EAAA,CATDP,MAAM,KAAKS,MAAM,O,CACrBC,SAAOZ,EAAAA,EAAAA,KAChB,SAEYa,GAHW,QACvBpB,EAAAA,EAAAA,IAEYqB,EAAA,CAFDC,KAAK,UAAUC,KAAK,QAASC,QAAK,SAAAlB,GAAA,OAAEmB,EAAAC,aAAaN,EAAMO,IAAG,G,CAjBnF,SAAApB,EAAAA,EAAAA,KAiBsF,kBAExEF,EAAA,KAAAA,EAAA,KAnBduB,EAAAA,EAAAA,IAiBsF,S,IAjBtFC,EAAA,EAAAC,GAAA,K,mBAoBc9B,EAAAA,EAAAA,IAEYqB,EAAA,CAFDC,KAAK,OAAOC,KAAK,QAASC,QAAK,SAAAlB,GAAA,OAAEmB,EAAAM,WAAWX,EAAMO,IAAG,G,CApB9E,SAAApB,EAAAA,EAAAA,KAoBiF,kBAEnEF,EAAA,KAAAA,EAAA,KAtBduB,EAAAA,EAAAA,IAoBiF,S,IApBjFC,EAAA,EAAAC,GAAA,K,sBAAAD,EAAA,I,IAAAA,EAAA,G,iBAQ6B1B,EAAA6B,QAAQC,YAmB7BjC,EAAAA,EAAAA,IAOEkC,EAAA,CANAC,WAAA,GACAC,OAAO,oBACNC,MAAOlC,EAAAmC,WAAWL,QAAQI,MAC1B,YAAWlC,EAAAmC,WAAWL,QAAQM,SAC/B5C,MAAM,aACL6C,gBAAcnC,EAAA,KAAAA,EAAA,YAAGoC,GAAI,OAAKhB,EAAAiB,iBAAiBD,EAAM,UAAF,I,kCAjC1DZ,EAAA,KAqCM7B,EAAAA,EAAAA,IAgCcQ,EAAA,CAhCDC,MAAM,MAAMC,KAAK,Y,CArCpC,SAAAH,EAAAA,EAAAA,KAsCQ,iBAqBW,uBArBXI,EAAAA,EAAAA,IAqBWC,EAAA,CArB6BC,KAAMV,EAAAwC,cAAe5B,MAAA,gB,CAtCrE,SAAAR,EAAAA,EAAAA,KAuCU,iBAA0D,EAA1DP,EAAAA,EAAAA,IAA0DgB,EAAA,CAAzCC,KAAK,SAASR,MAAM,OAAOS,MAAM,SAClDlB,EAAAA,EAAAA,IAA+DgB,EAAA,CAA9CC,KAAK,cAAcR,MAAM,OAAOS,MAAM,SACvDlB,EAAAA,EAAAA,IAAgDgB,EAAA,CAA/BC,KAAK,aAAaR,MAAM,QACzCT,EAAAA,EAAAA,IAA0CgB,EAAA,CAAzBC,KAAK,OAAOR,MAAM,QACnCT,EAAAA,EAAAA,IAAkDgB,EAAA,CAAjCC,KAAK,aAAaR,MAAM,UACzCT,EAAAA,EAAAA,IAA+CgB,EAAA,CAA9BC,KAAK,WAAWR,MAAM,SACvCT,EAAAA,EAAAA,IAMkBgB,EAAA,CANDC,KAAK,SAASR,MAAM,Q,CACxBU,SAAOZ,EAAAA,EAAAA,KAChB,SAESa,GAHc,QACvBpB,EAAAA,EAAAA,IAES4C,EAAA,CAFAtB,KAA2B,QAArBF,EAAMO,IAAIkB,OAAmB,UAAY,U,CA/CtE,SAAAtC,EAAAA,EAAAA,KAgDgB,iBAAsB,EAhDtCqB,EAAAA,EAAAA,KAAAkB,EAAAA,EAAAA,IAgDmB1B,EAAMO,IAAIkB,QAAM,G,IAhDnChB,EAAA,G,mBAAAA,EAAA,KAoDU7B,EAAAA,EAAAA,IAMkBgB,EAAA,CANDP,MAAM,KAAKS,MAAM,O,CACrBC,SAAOZ,EAAAA,EAAAA,KAChB,SAEYa,GAHW,QACvBpB,EAAAA,EAAAA,IAEYqB,EAAA,CAFDC,KAAK,OAAOC,KAAK,QAASC,QAAK,SAAAlB,GAAA,OAAEmB,EAAAM,WAAWX,EAAMO,IAAG,G,CAtD9E,SAAApB,EAAAA,EAAAA,KAsDiF,kBAEnEF,EAAA,KAAAA,EAAA,KAxDduB,EAAAA,EAAAA,IAsDiF,S,IAtDjFC,EAAA,EAAAC,GAAA,K,sBAAAD,EAAA,I,IAAAA,EAAA,G,iBAsC6B1B,EAAA6B,QAAQe,aAuB7B/C,EAAAA,EAAAA,IAOEkC,EAAA,CANAC,WAAA,GACAC,OAAO,oBACNC,MAAOlC,EAAAmC,WAAWS,SAASV,MAC3B,YAAWlC,EAAAmC,WAAWS,SAASR,SAChC5C,MAAM,aACL6C,gBAAcnC,EAAA,KAAAA,EAAA,YAAGoC,GAAI,OAAKhB,EAAAiB,iBAAiBD,EAAM,WAAF,I,kCAnE1DZ,EAAA,I,IAAAA,EAAA,G,mBAyEI7B,EAAAA,EAAAA,IAuCYgD,EAAA,CAtCFC,QAAS9C,EAAA+C,aAAaD,QA1EpC,mBAAA5C,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA0EuBH,EAAA+C,aAAaD,QAAO3C,CAAA,GACrC6C,MAAM,OACNjC,MAAM,QACLkC,QAAO3B,EAAA4B,mB,CA6BGC,QAAM/C,EAAAA,EAAAA,KACf,iBAGO,EAHPR,EAAAA,EAAAA,IAGO,OAHPwD,EAGO,EAFLvD,EAAAA,EAAAA,IAAoDqB,EAAA,CAAxCG,QAAOC,EAAA4B,mBAAiB,CA5G9C,SAAA9C,EAAAA,EAAAA,KA4GgD,kBAAEF,EAAA,MAAAA,EAAA,MA5GlDuB,EAAAA,EAAAA,IA4GgD,O,IA5GhDC,EAAA,EAAAC,GAAA,M,gBA6GU9B,EAAAA,EAAAA,IAAgEqB,EAAA,CAArDC,KAAK,UAAWE,QAAOC,EAAA+B,c,CA7G5C,SAAAjD,EAAAA,EAAAA,KA6G0D,kBAAIF,EAAA,MAAAA,EAAA,MA7G9DuB,EAAAA,EAAAA,IA6G0D,S,IA7G1DC,EAAA,EAAAC,GAAA,M,qBAAA,SAAAvB,EAAAA,EAAAA,KAiGK,iBA2DH,CA7EeJ,EAAA+C,aAAY,UAAK,WAA5BrD,EAAAA,EAAAA,IA0BM,MA1BN4D,EA0BM,EAzBJzD,EAAAA,EAAAA,IAOkB0D,EAAA,CAPAC,OAAQ,EAAGC,OAAA,I,CAhFrC,SAAArD,EAAAA,EAAAA,KAiFU,iBAAwF,EAAxFP,EAAAA,EAAAA,IAAwF6D,EAAA,CAAlEpD,MAAM,QAAM,CAjF5C,SAAAF,EAAAA,EAAAA,KAiF6C,iBAA8B,EAjF3EqB,EAAAA,EAAAA,KAAAkB,EAAAA,EAAAA,IAiFgD3C,EAAA+C,aAAY,QAAMY,QAAM,G,IAjFxEjC,EAAA,KAkFU7B,EAAAA,EAAAA,IAA6F6D,EAAA,CAAvEpD,MAAM,QAAM,CAlF5C,SAAAF,EAAAA,EAAAA,KAkF6C,iBAAmC,EAlFhFqB,EAAAA,EAAAA,KAAAkB,EAAAA,EAAAA,IAkFgD3C,EAAA+C,aAAY,QAAMa,aAAW,G,IAlF7ElC,EAAA,KAmFU7B,EAAAA,EAAAA,IAA0F6D,EAAA,CAApEpD,MAAM,MAAI,CAnF1C,SAAAF,EAAAA,EAAAA,KAmF2C,iBAAkC,EAnF7EqB,EAAAA,EAAAA,KAAAkB,EAAAA,EAAAA,IAmF8C3C,EAAA+C,aAAY,QAAMc,YAAU,G,IAnF1EnC,EAAA,KAoFU7B,EAAAA,EAAAA,IAAoF6D,EAAA,CAA9DpD,MAAM,MAAI,CApF1C,SAAAF,EAAAA,EAAAA,KAoF2C,iBAA4B,EApFvEqB,EAAAA,EAAAA,KAAAkB,EAAAA,EAAAA,IAoF8C3C,EAAA+C,aAAY,QAAM5B,MAAI,G,IApFpEO,EAAA,KAqFU7B,EAAAA,EAAAA,IAA0F6D,EAAA,CAApEpD,MAAM,OAAK,CArF3C,SAAAF,EAAAA,EAAAA,KAqF4C,iBAAiC,EArF7EqB,EAAAA,EAAAA,KAAAkB,EAAAA,EAAAA,IAqF+C3C,EAAA+C,aAAY,QAAMe,WAAS,G,IArF1EpC,EAAA,KAsFU7B,EAAAA,EAAAA,IAA4F6D,EAAA,CAAtEpD,MAAM,QAAM,CAtF5C,SAAAF,EAAAA,EAAAA,KAsF6C,iBAAkC,EAtF/EqB,EAAAA,EAAAA,KAAAkB,EAAAA,EAAAA,IAsFgD3C,EAAA+C,aAAY,QAAMgB,YAAU,G,IAtF5ErC,EAAA,I,IAAAA,EAAA,KAyFQ7B,EAAAA,EAAAA,IAeUmE,EAAA,CAfAC,MAAOjE,EAAA+C,aAAamB,KAAM,cAAY,OAAO1E,MAAM,e,CAzFrE,SAAAY,EAAAA,EAAAA,KA0FU,iBAKe,EALfP,EAAAA,EAAAA,IAKesE,EAAA,CALD7D,MAAM,QAAM,CA1FpC,SAAAF,EAAAA,EAAAA,KA2FY,iBAGiB,EAHjBP,EAAAA,EAAAA,IAGiBuE,EAAA,CA9F7BrE,WA2FqCC,EAAA+C,aAAamB,KAAKG,OA3FvD,sBAAAnE,EAAA,KAAAA,EAAA,YAAAC,GAAA,OA2FqCH,EAAA+C,aAAamB,KAAKG,OAAMlE,CAAA,I,CA3F7D,SAAAC,EAAAA,EAAAA,KA4Fc,iBAAuC,EAAvCP,EAAAA,EAAAA,IAAuCyE,EAAA,CAA7BhE,MAAM,WAAS,CA5FvC,SAAAF,EAAAA,EAAAA,KA4FwC,kBAAEF,EAAA,KAAAA,EAAA,KA5F1CuB,EAAAA,EAAAA,IA4FwC,O,IA5FxCC,EAAA,EAAAC,GAAA,OA6Fc9B,EAAAA,EAAAA,IAAsCyE,EAAA,CAA5BhE,MAAM,UAAQ,CA7FtC,SAAAF,EAAAA,EAAAA,KA6FuC,kBAAEF,EAAA,MAAAA,EAAA,MA7FzCuB,EAAAA,EAAAA,IA6FuC,O,IA7FvCC,EAAA,EAAAC,GAAA,O,IAAAD,EAAA,G,sBAAAA,EAAA,KAgGU7B,EAAAA,EAAAA,IAOesE,EAAA,CAPD7D,MAAM,MAAI,CAhGlC,SAAAF,EAAAA,EAAAA,KAiGY,iBAKY,EALZP,EAAAA,EAAAA,IAKY0E,EAAA,CAtGxBxE,WAkGuBC,EAAA+C,aAAamB,KAAKM,QAlGzC,sBAAAtE,EAAA,KAAAA,EAAA,YAAAC,GAAA,OAkGuBH,EAAA+C,aAAamB,KAAKM,QAAOrE,CAAA,GAClCgB,KAAK,WACJsD,KAAM,EACPC,YAAY,e,2BArG1BhD,EAAA,I,IAAAA,EAAA,G,iBAAAiD,EAAAA,EAAAA,IAAA,O,IAAAjD,EAAA,G,8EAuHA,SACEnB,KAAM,SACNG,KAAI,WACF,MAAO,CACLT,UAAW,UACX4B,QAAS,CACPC,SAAS,EACTc,UAAU,GAEZjC,eAAgB,GAChB6B,cAAe,GACfL,WAAY,CACVL,QAAS,CACPM,SAAU,GACVwC,YAAa,EACb1C,MAAO,GAETU,SAAU,CACRR,SAAU,GACVwC,YAAa,EACb1C,MAAO,IAGXa,aAAc,CACZD,SAAS,EACT+B,KAAM,KACNX,KAAM,CACJG,OAAQ,UACRG,QAAS,KAIjB,EACAM,MAAO,CACL7E,UAAS,SAAC8E,GACI,YAARA,EACFC,KAAKC,sBACY,aAARF,GACTC,KAAKE,oBAET,GAEFC,QAAO,WACLH,KAAKC,qBACP,EACAG,QAAS,CACPH,oBAAmB,WAAG,IAAAI,EAAA,KACpBL,KAAKnD,QAAQC,SAAU,EAEvBwD,YAAW,WACTD,EAAK1E,eAAiB,GACtB0E,EAAKlD,WAAWL,QAAQI,MAAQ,EAChCmD,EAAKxD,QAAQC,SAAU,CACzB,GAAG,IACL,EACAoD,mBAAkB,WAAG,IAAAK,EAAA,KACnBP,KAAKnD,QAAQe,UAAW,EAExB0C,YAAW,WACTC,EAAK/C,cAAgB,GACrB+C,EAAKpD,WAAWS,SAASV,MAAQ,EACjCqD,EAAK1D,QAAQe,UAAW,CAC1B,GAAG,IACL,EACAL,iBAAgB,SAACD,EAAMnB,GACrB6D,KAAK7C,WAAWhB,GAAMyD,YAActC,EACvB,YAATnB,EACF6D,KAAKC,sBAELD,KAAKE,oBAET,EACA3D,aAAY,SAACC,GACXwD,KAAKjC,aAAaD,SAAU,EAC5BkC,KAAKjC,aAAY,QAAQvB,EACzBwD,KAAKjC,aAAamB,KAAO,CACvBG,OAAQ,UACRG,QAAS,GAEb,EACA5C,WAAU,SAACJ,GACTwD,KAAKQ,QAAQC,KAAK,eAADC,OAAgBlE,EAAImE,IACvC,EACAzC,kBAAiB,WACf8B,KAAKjC,aAAaD,SAAU,EAC5BkC,KAAKjC,aAAY,QAAQ,KACzBiC,KAAKjC,aAAamB,KAAO,CACvBG,OAAQ,UACRG,QAAS,GAEb,EACAnB,aAAY,WAAG,IAAAuC,EAAA,KACb,GAAKZ,KAAKjC,aAAY,QAAtB,CAKA,IAAMY,EAASqB,KAAKjC,aAAY,QAAM4C,GAChCE,EAA+C,YAAlCb,KAAKjC,aAAamB,KAAKG,OACpCyB,EAAcd,KAAKjC,aAAamB,KAAKM,QAGrC3C,EAAUmD,KAAKe,SAAS,CAC5BC,MAAM,EACNC,KAAM,YACNC,QAAS,kBACTlE,WAAY,uBAIRmE,EAAgBN,EAClBO,EAAAA,WAAIC,OAAOC,eAAe3C,EAAQmC,GAClCM,EAAAA,WAAIC,OAAOE,eAAe5C,EAAQmC,GAEtCU,QAAQC,IAAI,WAADf,OAAY/B,EAAM,UAAA+B,OAASG,EAAa,kBAAoB,oBAEvEM,EACGO,MAAK,WACJF,QAAQC,IAAI,WAADf,OAAY/B,EAAM,UAAA+B,OAASG,EAAa,kBAAoB,oBACvED,EAAKe,SAASC,QAAQ,KAADlB,OAAMG,EAAa,KAAO,KAAI,OACnDD,EAAK1C,oBAGkB,YAAnB0C,EAAK3F,UACP2F,EAAKX,sBAELW,EAAKV,oBAET,IAAC,UACM,SAAA2B,GAAS,IAAAC,EACdN,QAAQK,MAAM,SAAUA,GACxBjB,EAAKe,SAASE,MAAM,aAA4B,QAAdC,EAAAD,EAAME,gBAAQ,IAAAD,OAAA,EAAdA,EAAgBpG,OAAQmG,EAAMG,SAAW,QAC7E,IAAC,YACQ,WACPnF,EAAQoF,OACV,GAxCF,MAFEjC,KAAK2B,SAASO,QAAQ,YA2C1B,I,eCxPJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/Review.vue", "webpack://medical-annotation-frontend/./src/views/Review.vue?75cc"], "sourcesContent": ["<template>\n  <div class=\"review-container\">\n    <div class=\"page-header\">\n      <h2>标注审核</h2>\n    </div>\n\n    <el-tabs v-model=\"activeTab\">\n      <el-tab-pane label=\"待审核\" name=\"pending\">\n        <el-table v-loading=\"loading.pending\" :data=\"pendingReviews\" style=\"width: 100%\">\n          <el-table-column prop=\"caseId\" label=\"病例编号\" width=\"180\" />\n          <el-table-column prop=\"patientInfo\" label=\"患者信息\" width=\"180\" />\n          <el-table-column prop=\"department\" label=\"部位\" />\n          <el-table-column prop=\"type\" label=\"类型\" />\n          <el-table-column prop=\"createTime\" label=\"标注时间\" />\n          <el-table-column prop=\"annotator\" label=\"标注人\" />\n          <el-table-column label=\"操作\" width=\"200\">\n            <template #default=\"scope\">\n              <el-button type=\"primary\" size=\"small\" @click=\"handleReview(scope.row)\">\n                审核\n              </el-button>\n              <el-button type=\"info\" size=\"small\" @click=\"handleView(scope.row)\">\n                查看\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n\n        <el-pagination\n          background\n          layout=\"prev, pager, next\"\n          :total=\"pagination.pending.total\"\n          :page-size=\"pagination.pending.pageSize\"\n          class=\"pagination\"\n          @current-change=\"(page) => handlePageChange(page, 'pending')\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"已审核\" name=\"reviewed\">\n        <el-table v-loading=\"loading.reviewed\" :data=\"reviewedCases\" style=\"width: 100%\">\n          <el-table-column prop=\"caseId\" label=\"病例编号\" width=\"180\" />\n          <el-table-column prop=\"patientInfo\" label=\"患者信息\" width=\"180\" />\n          <el-table-column prop=\"department\" label=\"部位\" />\n          <el-table-column prop=\"type\" label=\"类型\" />\n          <el-table-column prop=\"reviewTime\" label=\"审核时间\" />\n          <el-table-column prop=\"reviewer\" label=\"审核人\" />\n          <el-table-column prop=\"status\" label=\"审核结果\">\n            <template #default=\"scope\">\n              <el-tag :type=\"scope.row.status === '已通过' ? 'success' : 'danger'\">\n                {{ scope.row.status }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"120\">\n            <template #default=\"scope\">\n              <el-button type=\"info\" size=\"small\" @click=\"handleView(scope.row)\">\n                查看\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n\n        <el-pagination\n          background\n          layout=\"prev, pager, next\"\n          :total=\"pagination.reviewed.total\"\n          :page-size=\"pagination.reviewed.pageSize\"\n          class=\"pagination\"\n          @current-change=\"(page) => handlePageChange(page, 'reviewed')\"\n        />\n      </el-tab-pane>\n    </el-tabs>\n\n    <!-- 审核对话框 -->\n    <el-dialog\n      v-model:visible=\"reviewDialog.visible\"\n      title=\"标注审核\"\n      width=\"500px\"\n      @close=\"closeReviewDialog\"\n    >\n      <div v-if=\"reviewDialog.case\" class=\"review-dialog-content\">\n        <el-descriptions :column=\"1\" border>\n          <el-descriptions-item label=\"病例编号\">{{ reviewDialog.case.caseId }}</el-descriptions-item>\n          <el-descriptions-item label=\"患者信息\">{{ reviewDialog.case.patientInfo }}</el-descriptions-item>\n          <el-descriptions-item label=\"部位\">{{ reviewDialog.case.department }}</el-descriptions-item>\n          <el-descriptions-item label=\"类型\">{{ reviewDialog.case.type }}</el-descriptions-item>\n          <el-descriptions-item label=\"标注人\">{{ reviewDialog.case.annotator }}</el-descriptions-item>\n          <el-descriptions-item label=\"标注时间\">{{ reviewDialog.case.createTime }}</el-descriptions-item>\n        </el-descriptions>\n\n        <el-form :model=\"reviewDialog.form\" label-width=\"80px\" class=\"review-form\">\n          <el-form-item label=\"审核结果\">\n            <el-radio-group v-model=\"reviewDialog.form.result\">\n              <el-radio label=\"approve\">通过</el-radio>\n              <el-radio label=\"reject\">驳回</el-radio>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"备注\">\n            <el-input\n              v-model=\"reviewDialog.form.comment\"\n              type=\"textarea\"\n              :rows=\"3\"\n              placeholder=\"请输入审核意见（选填）\"\n            ></el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"closeReviewDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitReview\">提交审核</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport api from '../utils/api'\n\nexport default {\n  name: 'Review',\n  data() {\n    return {\n      activeTab: 'pending',\n      loading: {\n        pending: false,\n        reviewed: false\n      },\n      pendingReviews: [],\n      reviewedCases: [],\n      pagination: {\n        pending: {\n          pageSize: 10,\n          currentPage: 1,\n          total: 0\n        },\n        reviewed: {\n          pageSize: 10,\n          currentPage: 1,\n          total: 0\n        }\n      },\n      reviewDialog: {\n        visible: false,\n        case: null,\n        form: {\n          result: 'approve',\n          comment: ''\n        }\n      }\n    }\n  },\n  watch: {\n    activeTab(val) {\n      if (val === 'pending') {\n        this.fetchPendingReviews()\n      } else if (val === 'reviewed') {\n        this.fetchReviewedCases()\n      }\n    }\n  },\n  created() {\n    this.fetchPendingReviews()\n  },\n  methods: {\n    fetchPendingReviews() {\n      this.loading.pending = true\n      // TODO: 实际API调用获取待审核列表\n      setTimeout(() => {\n        this.pendingReviews = []\n        this.pagination.pending.total = 0\n        this.loading.pending = false\n      }, 500)\n    },\n    fetchReviewedCases() {\n      this.loading.reviewed = true\n      // TODO: 实际API调用获取已审核列表\n      setTimeout(() => {\n        this.reviewedCases = []\n        this.pagination.reviewed.total = 0\n        this.loading.reviewed = false\n      }, 500)\n    },\n    handlePageChange(page, type) {\n      this.pagination[type].currentPage = page\n      if (type === 'pending') {\n        this.fetchPendingReviews()\n      } else {\n        this.fetchReviewedCases()\n      }\n    },\n    handleReview(row) {\n      this.reviewDialog.visible = true\n      this.reviewDialog.case = row\n      this.reviewDialog.form = {\n        result: 'approve',\n        comment: ''\n      }\n    },\n    handleView(row) {\n      this.$router.push(`/cases/view/${row.id}`)\n    },\n    closeReviewDialog() {\n      this.reviewDialog.visible = false\n      this.reviewDialog.case = null\n      this.reviewDialog.form = {\n        result: 'approve',\n        comment: ''\n      }\n    },\n    submitReview() {\n      if (!this.reviewDialog.case) {\n        this.$message.warning('没有选择审核的病例');\n        return;\n      }\n      \n      const caseId = this.reviewDialog.case.id;\n      const isApproved = this.reviewDialog.form.result === 'approve';\n      const reviewNotes = this.reviewDialog.form.comment;\n      \n      // 显示加载状态\n      const loading = this.$loading({\n        lock: true,\n        text: '提交审核结果...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      });\n      \n      // 使用新API更新状态\n      const updatePromise = isApproved \n        ? api.images.markAsApproved(caseId, reviewNotes) \n        : api.images.markAsRejected(caseId, reviewNotes);\n      \n      console.log(`开始将图像ID ${caseId} 状态更新为${isApproved ? '\"已通过\"(APPROVED)' : '\"已驳回\"(REJECTED)'}`);\n      \n      updatePromise\n        .then(() => {\n          console.log(`成功将图像ID ${caseId} 状态更新为${isApproved ? '\"已通过\"(APPROVED)' : '\"已驳回\"(REJECTED)'}`);\n          this.$message.success(`审核${isApproved ? '通过' : '驳回'}成功`);\n          this.closeReviewDialog();\n          \n          // 重新加载数据\n          if (this.activeTab === 'pending') {\n            this.fetchPendingReviews();\n          } else {\n            this.fetchReviewedCases();\n          }\n        })\n        .catch(error => {\n          console.error('审核提交失败', error);\n          this.$message.error('审核提交失败: ' + (error.response?.data || error.message || '未知错误'));\n        })\n        .finally(() => {\n          loading.close();\n        });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.review-container {\n  padding: 20px;\n}\n\n.page-header {\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  font-size: 20px;\n  color: #333;\n}\n\n.pagination {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.review-dialog-content {\n  padding: 10px;\n}\n\n.review-form {\n  margin-top: 20px;\n}\n</style> ", "import { render } from \"./Review.vue?vue&type=template&id=5de22d39&scoped=true\"\nimport script from \"./Review.vue?vue&type=script&lang=js\"\nexport * from \"./Review.vue?vue&type=script&lang=js\"\n\nimport \"./Review.vue?vue&type=style&index=0&id=5de22d39&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-5de22d39\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createVNode", "_component_el_tabs", "modelValue", "$data", "activeTab", "_cache", "$event", "_withCtx", "_component_el_tab_pane", "label", "name", "_createBlock", "_component_el_table", "data", "pendingReviews", "style", "_component_el_table_column", "prop", "width", "default", "scope", "_component_el_button", "type", "size", "onClick", "$options", "handleReview", "row", "_createTextVNode", "_", "__", "handleView", "loading", "pending", "_component_el_pagination", "background", "layout", "total", "pagination", "pageSize", "onCurrentChange", "page", "handlePageChange", "reviewedCases", "_component_el_tag", "status", "_toDisplayString", "reviewed", "_component_el_dialog", "visible", "reviewDialog", "title", "onClose", "closeReviewDialog", "footer", "_hoisted_3", "submitReview", "_hoisted_2", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "caseId", "patientInfo", "department", "annotator", "createTime", "_component_el_form", "model", "form", "_component_el_form_item", "_component_el_radio_group", "result", "_component_el_radio", "_component_el_input", "comment", "rows", "placeholder", "_createCommentVNode", "currentPage", "case", "watch", "val", "this", "fetchPendingReviews", "fetchReviewedCases", "created", "methods", "_this", "setTimeout", "_this2", "$router", "push", "concat", "id", "_this3", "isApproved", "reviewNotes", "$loading", "lock", "text", "spinner", "updatePromise", "api", "images", "markAsApproved", "markAsRejected", "console", "log", "then", "$message", "success", "error", "_error$response", "response", "message", "close", "warning", "__exports__", "render"], "sourceRoot": ""}