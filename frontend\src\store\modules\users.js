// 简化的Vuex用户模块 - 调用auth模块来保持向后兼容性

const state = {
  // 保持状态为空，实际使用auth模块的状态
}

const getters = {
  // 所有getter重定向到auth模块
  getAllUsers: (state, getters, rootState) => rootState.auth.users,
  getCurrentUser: (state, getters, rootState) => rootState.auth.currentUserDetails,
  isUsersLoading: (state, getters, rootState) => rootState.auth.loading,
  getUsersError: (state, getters, rootState) => rootState.auth.error
}

const mutations = {
  // 不需要本地mutation，使用auth模块的mutation
}

const actions = {
  // 获取用户列表 - 调用auth模块
  async fetchUsers({ dispatch }) {
    return dispatch('auth/fetchUsers', null, { root: true });
  },
  
  // 获取单个用户 - 调用auth模块
  async fetchUser({ dispatch }, userId) {
    return dispatch('auth/fetchUserProfile', userId, { root: true });
  }
}

export default {
  state,
  getters,
  mutations,
  actions
} 