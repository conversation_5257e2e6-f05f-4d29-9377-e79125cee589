@echo off
chcp 65001
echo ========================================
echo 血管瘤AI智能诊断平台 - 开发环境启动脚本
echo ========================================

:: 设置颜色
color 0A

:: 检查Java环境
echo [1/6] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置环境变量
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Node.js环境
echo [2/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未配置环境变量
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查Python环境
echo [3/6] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未配置环境变量
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 创建必要的目录
echo [4/6] 创建必要的目录...
if not exist "medical_images\original" mkdir "medical_images\original"
if not exist "medical_images\processed" mkdir "medical_images\processed"
if not exist "medical_images\annotated" mkdir "medical_images\annotated"
if not exist "logs" mkdir "logs"
echo ✅ 目录创建完成

:: 启动后端服务
echo [5/6] 启动Spring Boot后端服务...
start "血管瘤诊断平台-后端" cmd /k "echo 启动后端服务... && mvn spring-boot:run -Dspring-boot.run.profiles=dev"

:: 等待后端启动
echo 等待后端服务启动...
timeout /t 30 /nobreak >nul

:: 启动AI服务
echo 启动AI服务...
start "血管瘤诊断平台-AI服务" cmd /k "cd ai-service && echo 启动AI服务... && python ai_service.py"

:: 等待AI服务启动
echo 等待AI服务启动...
timeout /t 15 /nobreak >nul

:: 启动前端服务
echo [6/6] 启动Vue.js前端服务...
start "血管瘤诊断平台-前端" cmd /k "cd frontend && echo 启动前端服务... && npm run serve"

:: 等待前端启动
echo 等待前端服务启动...
timeout /t 20 /nobreak >nul

echo.
echo ========================================
echo 🎉 所有服务启动完成！
echo ========================================
echo.
echo 📱 前端应用: http://localhost:8080
echo 🔧 后端API: http://localhost:8085
echo 🤖 AI服务: http://localhost:8086
echo 📚 API文档: http://localhost:8086/docs
echo.
echo 💡 提示：
echo - 首次启动可能需要下载依赖，请耐心等待
echo - 如果服务启动失败，请检查端口是否被占用
echo - 按任意键退出此窗口（不会关闭服务）
echo.
pause
