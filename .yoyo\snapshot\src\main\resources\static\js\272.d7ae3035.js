"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[272],{4272:(e,a,r)=>{r.r(a),r.d(a,{default:()=>X});r(2010);var t=r(641),n=r(33),l=r(3751),s={class:"container login-container"},o={class:"card"},i={class:"card-body p-4"},d={key:0,class:"alert alert-danger"},u={key:1,class:"alert alert-success"},c={class:"mb-3"},p=["disabled"],m={class:"mb-3"},b=["disabled"],v={class:"mb-3"},f=["disabled"],k={class:"mb-3"},g=["disabled"],L={class:"mb-3"},h=["disabled"],w={class:"mb-3"},y=["disabled"],R={class:"d-grid gap-2"},_=["disabled"],x={key:0,class:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"},K={class:"mt-3 text-center"},C={class:"card-footer text-center"};function U(e,a,r,U,J,P){var V=(0,t.g2)("router-link");return(0,t.uX)(),(0,t.CE)("div",s,[(0,t.Lk)("div",o,[a[16]||(a[16]=(0,t.Lk)("div",{class:"card-header"},[(0,t.Lk)("h3",{class:"mb-0"},"血管瘤辅助系统 - 注册")],-1)),(0,t.Lk)("div",i,[U.error?((0,t.uX)(),(0,t.CE)("div",d,(0,n.v_)(U.error),1)):(0,t.Q3)("",!0),U.success?((0,t.uX)(),(0,t.CE)("div",u,(0,n.v_)(U.success),1)):(0,t.Q3)("",!0),(0,t.Lk)("form",{onSubmit:a[6]||(a[6]=(0,l.D$)((function(){return U.handleRegister&&U.handleRegister.apply(U,arguments)}),["prevent"]))},[(0,t.Lk)("div",c,[a[7]||(a[7]=(0,t.Lk)("label",{for:"name",class:"form-label"},"姓名",-1)),(0,t.bo)((0,t.Lk)("input",{type:"text",class:"form-control",id:"name","onUpdate:modelValue":a[0]||(a[0]=function(e){return U.name=e}),required:"",disabled:U.loading},null,8,p),[[l.Jo,U.name]])]),(0,t.Lk)("div",m,[a[8]||(a[8]=(0,t.Lk)("label",{for:"email",class:"form-label"},"邮箱",-1)),(0,t.bo)((0,t.Lk)("input",{type:"email",class:"form-control",id:"email","onUpdate:modelValue":a[1]||(a[1]=function(e){return U.email=e}),required:"",disabled:U.loading},null,8,b),[[l.Jo,U.email]])]),(0,t.Lk)("div",v,[a[9]||(a[9]=(0,t.Lk)("label",{for:"password",class:"form-label"},"密码",-1)),(0,t.bo)((0,t.Lk)("input",{type:"password",class:"form-control",id:"password","onUpdate:modelValue":a[2]||(a[2]=function(e){return U.password=e}),required:"",disabled:U.loading},null,8,f),[[l.Jo,U.password]])]),(0,t.Lk)("div",k,[a[10]||(a[10]=(0,t.Lk)("label",{for:"confirmPassword",class:"form-label"},"确认密码",-1)),(0,t.bo)((0,t.Lk)("input",{type:"password",class:"form-control",id:"confirmPassword","onUpdate:modelValue":a[3]||(a[3]=function(e){return U.confirmPassword=e}),required:"",disabled:U.loading},null,8,g),[[l.Jo,U.confirmPassword]])]),(0,t.Lk)("div",L,[a[11]||(a[11]=(0,t.Lk)("label",{for:"hospital",class:"form-label"},"医院",-1)),(0,t.bo)((0,t.Lk)("input",{type:"text",class:"form-control",id:"hospital","onUpdate:modelValue":a[4]||(a[4]=function(e){return U.hospital=e}),required:"",disabled:U.loading},null,8,h),[[l.Jo,U.hospital]])]),(0,t.Lk)("div",w,[a[12]||(a[12]=(0,t.Lk)("label",{for:"department",class:"form-label"},"科室",-1)),(0,t.bo)((0,t.Lk)("input",{type:"text",class:"form-control",id:"department","onUpdate:modelValue":a[5]||(a[5]=function(e){return U.department=e}),disabled:U.loading},null,8,y),[[l.Jo,U.department]])]),(0,t.Lk)("div",R,[(0,t.Lk)("button",{type:"submit",class:"btn btn-primary",disabled:U.loading},[U.loading?((0,t.uX)(),(0,t.CE)("span",x)):(0,t.Q3)("",!0),a[13]||(a[13]=(0,t.eW)(" 注册 "))],8,_)])],32),(0,t.Lk)("div",K,[(0,t.bF)(V,{to:"/login",class:"text-decoration-none"},{default:(0,t.k6)((function(){return a[14]||(a[14]=[(0,t.eW)("已有账号？点击登录")])})),_:1,__:[14]})])]),(0,t.Lk)("div",C,[(0,t.bF)(V,{to:"/",class:"btn btn-link"},{default:(0,t.k6)((function(){return a[15]||(a[15]=[(0,t.eW)("返回首页")])})),_:1,__:[15]})])])])}var J=r(4048),P=r(388),V=(r(4114),r(6031),r(953)),q=r(6278),E=r(5220);const I={name:"Register",setup:function(){var e=(0,q.Pj)(),a=(0,E.rd)(),r=(0,V.KR)(""),t=(0,V.KR)(""),n=(0,V.KR)(""),l=(0,V.KR)(""),s=(0,V.KR)(""),o=(0,V.KR)(""),i=(0,V.KR)(!1),d=(0,V.KR)(""),u=(0,V.KR)(""),c=function(){var c=(0,P.A)((0,J.A)().mark((function c(){return(0,J.A)().wrap((function(c){while(1)switch(c.prev=c.next){case 0:if(n.value===l.value){c.next=3;break}return d.value="两次输入的密码不一致",c.abrupt("return");case 3:return i.value=!0,d.value="",u.value="",c.prev=6,c.next=9,e.dispatch("register",{name:r.value,email:t.value,password:n.value,hospital:s.value,department:o.value,role:"DOCTOR"});case 9:u.value="注册成功，请登录",r.value="",t.value="",n.value="",l.value="",s.value="",o.value="",setTimeout((function(){a.push("/login")}),3e3),c.next=23;break;case 19:c.prev=19,c.t0=c["catch"](6),console.error("Registration error:",c.t0),d.value="string"===typeof c.t0?c.t0:"注册失败，请检查填写信息";case 23:return c.prev=23,i.value=!1,c.finish(23);case 26:case"end":return c.stop()}}),c,null,[[6,19,23,26]])})));return function(){return c.apply(this,arguments)}}();return{name:r,email:t,password:n,confirmPassword:l,hospital:s,department:o,loading:i,error:d,success:u,handleRegister:c}}};var T=r(6262);const A=(0,T.A)(I,[["render",U],["__scopeId","data-v-3692b039"]]),X=A},4599:(e,a,r)=>{var t=r(6518),n=r(4576),l=r(9472),s=l(n.setTimeout,!0);t({global:!0,bind:!0,forced:n.setTimeout!==s},{setTimeout:s})},5575:(e,a,r)=>{var t=r(6518),n=r(4576),l=r(9472),s=l(n.setInterval,!0);t({global:!0,bind:!0,forced:n.setInterval!==s},{setInterval:s})},6031:(e,a,r)=>{r(5575),r(4599)},9472:(e,a,r)=>{var t=r(4576),n=r(8745),l=r(4901),s=r(4215),o=r(2839),i=r(7680),d=r(2812),u=t.Function,c=/MSIE .\./.test(o)||"BUN"===s&&function(){var e=t.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}();e.exports=function(e,a){var r=a?2:1;return c?function(t,s){var o=d(arguments.length,1)>r,c=l(t)?t:u(t),p=o?i(arguments,r):[],m=o?function(){n(c,this,p)}:c;return a?e(m,s):e(m)}:e}}}]);