"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[362],{37620:(e,t,a)=>{a.d(t,{VG:()=>r});a(54119),a(28706),a(74423),a(8921),a(15086),a(26099),a(27495),a(99449),a(21699),a(71761),a(90744),a(11392);var n="http://localhost:8085";function r(e){if(!e)return"";var t=String(e);if(t.startsWith("http://")||t.startsWith("https://"))return t;if(t.startsWith("data:"))return t;if(t.includes("medical_images")){var a=/medical_images[\/\\]([^\/\\]+)(?:[\/\\](.*))?/,r=t.match(a);if(r){var i=r[1],s=r[2]||"",l="/medical/images/".concat(i,"/").concat(s),o=n+l;return o}}if(t.startsWith("/medical/"))return n+t;if(t.match(/^[a-zA-Z]:\\/)){var c=t.split(/[\/\\]/).pop(),u="";t.includes("\\original\\")||t.includes("/original/")?u="original":t.includes("\\processed\\")||t.includes("/processed/")?u="processed":t.includes("\\annotate\\")||t.includes("/annotate/")?u="annotate":(t.includes("\\temp\\")||t.includes("/temp/"))&&(u="temp");var d="/medical/images/".concat(u,"/").concat(c);return n+d}var f=t.startsWith("/")?t:"/"+t;f.startsWith("/medical")||(f="/medical"+f);try{var m=f.lastIndexOf("/");if(-1!==m){var h=f.substring(0,m+1),p=f.substring(m+1);f=h+encodeURIComponent(p)}}catch(g){}return n+f}},90362:(e,t,a)=>{a.r(t),a.d(t,{default:()=>E});var n=a(20641),r=a(90033),i={class:"case-view-container"},s={class:"page-header"},l={class:"header-actions"},o={key:1,class:"annotated-image-section"},c={class:"section-header"},u={key:0,class:"refresh-info"},d={class:"annotated-image-container"},f={class:"image-error"},m={key:0,class:"debug-info"},h={key:2,class:"no-image-message"},p={class:"card-header"},g={class:"case-info"},v={class:"detailed-case-info"},k={class:"section-header"},D={key:0,class:"version-info"};function _(e,t,a,_,b,y){var I=(0,n.g2)("el-button"),W=(0,n.g2)("el-tooltip"),w=(0,n.g2)("el-alert"),U=(0,n.g2)("el-image"),C=(0,n.g2)("el-tag"),E=(0,n.g2)("el-descriptions-item"),T=(0,n.g2)("el-descriptions"),P=(0,n.g2)("el-card"),A=(0,n.gN)("loading");return(0,n.uX)(),(0,n.CE)("div",i,[(0,n.Lk)("div",s,[t[3]||(t[3]=(0,n.Lk)("h2",null,"病例详情",-1)),(0,n.Lk)("div",l,[b.hasPermission?((0,n.uX)(),(0,n.Wv)(I,{key:0,type:"primary",size:"small",onClick:y.redirectToAnnotate},{default:(0,n.k6)((function(){return t[1]||(t[1]=[(0,n.eW)("编辑")])})),_:1,__:[1]},8,["onClick"])):(0,n.Q3)("",!0),(0,n.bF)(W,{content:"刷新病例数据",placement:"top"},{default:(0,n.k6)((function(){return[(0,n.bF)(I,{icon:"el-icon-refresh",size:"small",circle:"",onClick:y.refreshData,loading:b.loading},null,8,["onClick","loading"])]})),_:1}),(0,n.bF)(I,{link:"",onClick:t[0]||(t[0]=function(t){return e.$router.back()})},{default:(0,n.k6)((function(){return t[2]||(t[2]=[(0,n.eW)("返回")])})),_:1,__:[2]})])]),(0,n.bo)(((0,n.uX)(),(0,n.Wv)(P,null,{header:(0,n.k6)((function(){return[(0,n.Lk)("div",p,[t[12]||(t[12]=(0,n.Lk)("span",null,"基本信息",-1)),(0,n.bF)(C,{type:y.getStatusType(b.caseDetail.status)},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.status),1)]})),_:1},8,["type"])])]})),default:(0,n.k6)((function(){return[!b.hasPermission&&b.currentUser?((0,n.uX)(),(0,n.Wv)(w,{key:0,title:"权限受限",type:"warning",description:"您只能查看此病例的基本信息，编辑功能已禁用。","show-icon":"",closable:!1,style:{"margin-bottom":"15px"}})):(0,n.Q3)("",!0),b.annotatedImageUrl?((0,n.uX)(),(0,n.CE)("div",o,[(0,n.Lk)("div",c,[t[4]||(t[4]=(0,n.Lk)("h3",null,"病变标注图像",-1)),b.lastRefreshTime?((0,n.uX)(),(0,n.CE)("small",u,"最后更新: "+(0,r.v_)(y.formatTime(b.lastRefreshTime)),1)):(0,n.Q3)("",!0)]),(0,n.Lk)("div",d,[(0,n.bF)(U,{src:b.annotatedImageUrl,fit:"contain",class:"annotated-image","preview-src-list":[b.annotatedImageUrl],onError:y.handleImageError},{error:(0,n.k6)((function(){return[(0,n.Lk)("div",f,[t[9]||(t[9]=(0,n.Lk)("i",{class:"el-icon-picture-outline"},null,-1)),t[10]||(t[10]=(0,n.Lk)("p",null,"无法加载标注图像",-1)),b.imageLoadError?((0,n.uX)(),(0,n.CE)("div",m,[t[7]||(t[7]=(0,n.Lk)("h4",null,"图片调试信息",-1)),(0,n.Lk)("p",null,[t[5]||(t[5]=(0,n.Lk)("b",null,"数据库图片路径:",-1)),(0,n.eW)(" "+(0,r.v_)(b.debugInfo.rawPath),1)]),(0,n.Lk)("p",null,[t[6]||(t[6]=(0,n.Lk)("b",null,"完整访问URL:",-1)),(0,n.eW)(" "+(0,r.v_)(b.debugInfo.fullUrl),1)]),t[8]||(t[8]=(0,n.Lk)("small",null,"请检查此路径是否正确，以及后端是否提供了静态文件访问",-1))])):(0,n.Q3)("",!0)])]})),_:1},8,["src","preview-src-list","onError"])])])):((0,n.uX)(),(0,n.CE)("div",h,t[11]||(t[11]=[(0,n.Lk)("i",{class:"el-icon-picture-outline"},null,-1),(0,n.Lk)("p",null,"此病例暂无标注图像",-1)]))),(0,n.Lk)("div",g,[(0,n.bF)(T,{column:4,border:""},{default:(0,n.k6)((function(){return[(0,n.bF)(E,{label:"病例编号"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.caseId||"无"),1)]})),_:1}),b.caseDetail.patientInfo?((0,n.uX)(),(0,n.Wv)(E,{key:0,label:"患者信息"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.patientInfo),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.department?((0,n.uX)(),(0,n.Wv)(E,{key:1,label:"部位"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.department),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.type?((0,n.uX)(),(0,n.Wv)(E,{key:2,label:"类型"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.type),1)]})),_:1})):(0,n.Q3)("",!0),(0,n.bF)(E,{label:"创建时间"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.createTime||"无"),1)]})),_:1}),(0,n.bF)(E,{label:"更新时间"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.updateTime||"无"),1)]})),_:1}),b.caseDetail.note?((0,n.uX)(),(0,n.Wv)(E,{key:3,label:"备注",span:4},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.note),1)]})),_:1})):(0,n.Q3)("",!0)]})),_:1})]),(0,n.Lk)("div",v,[(0,n.Lk)("div",k,[t[13]||(t[13]=(0,n.Lk)("h3",null,"详细信息",-1)),b.dataVersion>0?((0,n.uX)(),(0,n.CE)("small",D,"版本: "+(0,r.v_)(b.dataVersion),1)):(0,n.Q3)("",!0)]),(0,n.bF)(T,{column:3,border:""},{default:(0,n.k6)((function(){return[b.caseDetail.lesionSize?((0,n.uX)(),(0,n.Wv)(E,{key:0,label:"病变大小"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.lesionSize),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.lesionColor?((0,n.uX)(),(0,n.Wv)(E,{key:1,label:"病变颜色"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.lesionColor),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.bloodFlow?((0,n.uX)(),(0,n.Wv)(E,{key:2,label:"血流信号"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.bloodFlow),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.borderClarity?((0,n.uX)(),(0,n.Wv)(E,{key:3,label:"边界清晰度"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.borderClarity),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.diseaseStage?((0,n.uX)(),(0,n.Wv)(E,{key:4,label:"病程阶段"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.diseaseStage),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.morphologicalFeatures?((0,n.uX)(),(0,n.Wv)(E,{key:5,label:"形态特征"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.morphologicalFeatures),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.symptoms?((0,n.uX)(),(0,n.Wv)(E,{key:6,label:"症状表现"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.symptoms),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.symptomDetails?((0,n.uX)(),(0,n.Wv)(E,{key:7,label:"症状详情"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.symptomDetails),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.complications?((0,n.uX)(),(0,n.Wv)(E,{key:8,label:"并发症"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.complications),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.complicationDetails?((0,n.uX)(),(0,n.Wv)(E,{key:9,label:"并发症详情"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.complicationDetails),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.diagnosisCode?((0,n.uX)(),(0,n.Wv)(E,{key:10,label:"诊断编码"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.diagnosisCode),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.treatmentPriority?((0,n.uX)(),(0,n.Wv)(E,{key:11,label:"治疗优先级"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.treatmentPriority),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.treatmentPlan?((0,n.uX)(),(0,n.Wv)(E,{key:12,label:"治疗方案"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.treatmentPlan),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.recommendedTreatment?((0,n.uX)(),(0,n.Wv)(E,{key:13,label:"推荐治疗"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.recommendedTreatment),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.contraindications?((0,n.uX)(),(0,n.Wv)(E,{key:14,label:"禁忌症"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.contraindications),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.followUpSchedule?((0,n.uX)(),(0,n.Wv)(E,{key:15,label:"随访周期"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.followUpSchedule),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.prognosisRating?((0,n.uX)(),(0,n.Wv)(E,{key:16,label:"预后评级"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.prognosisRating),1)]})),_:1})):(0,n.Q3)("",!0),b.caseDetail.patientEducation?((0,n.uX)(),(0,n.Wv)(E,{key:17,label:"患者教育重点"},{default:(0,n.k6)((function(){return[(0,n.eW)((0,r.v_)(b.caseDetail.patientEducation),1)]})),_:1})):(0,n.Q3)("",!0)]})),_:1})])]})),_:1})),[[A,b.loading]])])}var b=a(14048),y=a(30388),I=(a(28706),a(74423),a(44114),a(59089),a(23288),a(79432),a(27495),a(21699),a(25440),a(11392),a(653)),W=a(37620);const w={name:"CaseView",data:function(){return{caseId:this.$route.params.id,loading:!0,caseDetail:{caseId:"",patientInfo:"",department:"",type:"",status:"",note:"",createTime:"",updateTime:"",lesionSize:"",lesionColor:"",bloodFlow:"",borderClarity:"",diseaseStage:"",morphologicalFeatures:"",symptoms:"",symptomDetails:"",complications:"",complicationDetails:"",diagnosisCode:"",treatmentPriority:"",treatmentPlan:"",recommendedTreatment:"",contraindications:"",followUpSchedule:"",prognosisRating:"",patientEducation:"",createdBy:"",teamId:""},tags:[],annotatedImageUrl:null,imageLoadError:!1,debugInfo:{rawPath:"",fullUrl:""},currentUser:null,lastRefreshTime:null,refreshInterval:6e4,hasPermission:!1,dataVersion:0}},created:function(){this.getCurrentUser(),this.fetchCaseDetail(),window.addEventListener("case-data-updated",this.handleDataUpdate)},beforeDestroy:function(){window.removeEventListener("case-data-updated",this.handleDataUpdate)},methods:{getCurrentUser:function(){try{var e=localStorage.getItem("user");e&&(this.currentUser=JSON.parse(e))}catch(t){}},checkPermission:function(){if(!this.currentUser)return!1;var e="ADMIN"===this.currentUser.role,t="ANNOTATOR"===this.currentUser.role||"DOCTOR"===this.currentUser.role,a=this.caseDetail.createdBy===this.currentUser.id||this.caseDetail.createdBy===this.currentUser.customId,n=this.caseDetail.teamId&&this.currentUser.teamId&&this.caseDetail.teamId===this.currentUser.teamId;return this.hasPermission=e||t&&(a||n),this.hasPermission},handleDataUpdate:function(e){var t,a;if(e&&e.detail&&e.detail.caseId===this.caseId&&(!e.detail.userId||e.detail.userId===(null===(t=this.currentUser)||void 0===t?void 0:t.id)||e.detail.userId===(null===(a=this.currentUser)||void 0===a?void 0:a.customId))){this.dataVersion++;var n=!0===e.detail.immediate||Date.now()-this.lastRefreshTime>this.refreshInterval;n&&this.fetchCaseDetail()}},shouldRefreshData:function(){if(!this.lastRefreshTime)return!0;var e=Date.now(),t=e-this.lastRefreshTime;return t>this.refreshInterval},updateRefreshTime:function(){this.lastRefreshTime=Date.now()},redirectToAnnotate:function(){localStorage.setItem("isEditingCase","true"),this.$router.push({path:"/cases/form",query:{imageId:this.caseId,edit:"true"}})},getStatusType:function(e){var t={待标注:"info",已标注:"success",审核中:"warning",已通过:"success",已驳回:"danger"};return t[e]||"info"},fetchCaseDetail:function(){var e=this;return(0,y.A)((0,b.A)().mark((function t(){var a,n;return(0,b.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.shouldRefreshData()||!e.annotatedImageUrl){t.next=3;break}return t.abrupt("return");case 3:return e.loading=!0,t.prev=4,t.next=7,I["default"].images.getOne(e.caseId);case 7:if(a=t.sent,n=a.data,!n){t.next=17;break}return e.caseDetail={caseId:"CASE-".concat(n.id),patientInfo:n.patientName||"",department:n.lesionLocation||"",type:n.diagnosisCategory||"",status:e.getStatusText(n.status),note:n.reviewNotes||"",createTime:e.formatDate(n.createdAt),updateTime:e.formatDate(n.updatedAt),lesionSize:n.lesionSize||"",lesionColor:n.lesionColor||"",bloodFlow:n.bloodFlow||"",borderClarity:n.borderClarity||"",diseaseStage:n.diseaseStage||"",morphologicalFeatures:n.morphologicalFeatures||"",symptoms:n.symptoms||"",symptomDetails:n.symptomDetails||"",complications:n.complications||"",complicationDetails:n.complicationDetails||"",diagnosisCode:n.diagnosisIcdCode||"",treatmentPriority:n.treatmentPriority||"",treatmentPlan:n.treatmentPlan||"",recommendedTreatment:n.recommendedTreatment||"",contraindications:n.contraindications||"",followUpSchedule:n.followUpSchedule||"",prognosisRating:n.prognosisRating?n.prognosisRating+"分":"",patientEducation:n.patientEducation||"",createdBy:n.createdBy||n.userId,teamId:n.teamId},e.checkPermission(),t.next=14,e.fetchAnnotations();case 14:return t.next=16,e.fetchImagePairs();case 16:e.updateRefreshTime();case 17:t.next=23;break;case 19:t.prev=19,t.t0=t["catch"](4),e.$message.error("获取病例详情失败");case 23:return t.prev=23,e.loading=!1,t.finish(23);case 26:case"end":return t.stop()}}),t,null,[[4,19,23,26]])})))()},getStatusText:function(e){var t={DRAFT:"待标注",SUBMITTED:"已标注",PENDING:"审核中",APPROVED:"已通过",REJECTED:"已驳回"};return t[e]||"未知"},fetchAnnotations:function(){var e=this;return(0,y.A)((0,b.A)().mark((function t(){var a;return(0,b.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,I["default"].tags.getByImageId(e.caseId);case 3:a=t.sent,e.tags=a.data,t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},fetchImagePairs:function(){var e=this;return(0,y.A)((0,b.A)().mark((function t(){var a,n,r,i,s,l,o;return(0,b.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,I["default"].imagePairs.getByMetadataId(e.caseId);case 4:if(a=t.sent,n=a.data,!(n&&n.length>0)){t.next=15;break}r=n[0],i=r.image_two_path,e.debugInfo.rawPath=i||"null",i?(s={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_BACKEND_PORT||"8085",l=i.startsWith("/")?i:"/"+i,o="http://localhost:".concat(s).concat(l),e.debugInfo.fullUrl=o,e.annotatedImageUrl=o):e.annotatedImageUrl=null,t.next=19;break;case 15:return e.annotatedImageUrl=null,t.next=19,e.fallbackToImageMetadata();case 19:t.next=27;break;case 21:return t.prev=21,t.t0=t["catch"](0),t.next=27,e.fallbackToImageMetadata();case 27:case"end":return t.stop()}}),t,null,[[0,21]])})))()},fallbackToImageMetadata:function(){var e=this;return(0,y.A)((0,b.A)().mark((function t(){var a,n,r,i,s,l,o,c,u,d;return(0,b.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,I["default"].images.getOne(e.caseId);case 4:a=t.sent,n=a.data,n&&n.image_two_path?(r=n.image_two_path,i={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_BACKEND_PORT||"8085",s=r.startsWith("/")?r:"/"+r,l="http://localhost:".concat(i).concat(s),e.debugInfo.rawPath=r,e.debugInfo.fullUrl=l,e.annotatedImageUrl=l):n&&n.path&&(o=n.path,c=o,o.includes("/original/")&&(c=o.replace("/original/","/processed/")),u={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_BACKEND_PORT||"8085",d="http://localhost:".concat(u).concat(c),e.debugInfo.rawPath=c,e.debugInfo.fullUrl=d,e.annotatedImageUrl=d),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](0),e.annotatedImageUrl=null;case 14:case"end":return t.stop()}}),t,null,[[0,10]])})))()},formatDate:function(e){if(!e)return"无";var t=new Date(e);return t.toLocaleString("zh-CN")},handleImageError:function(){this.imageLoadError=!0},getImageUrl:W.VG,refreshData:function(){this.fetchCaseDetail()},formatTime:function(e){if(!e)return"";var t=new Date(e);return t.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"})}}};var U=a(66262);const C=(0,U.A)(w,[["render",_],["__scopeId","data-v-64caaa50"]]),E=C}}]);