-- 血管瘤AI智能诊断平台数据库初始化脚本
-- 创建时间: 2024-01-01
-- 版本: V2.0

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `role` varchar(20) NOT NULL DEFAULT 'USER' COMMENT '角色：ADMIN, DOCTOR, USER',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 血管瘤诊断记录表
-- ----------------------------
DROP TABLE IF EXISTS `hemangioma_diagnoses`;
CREATE TABLE `hemangioma_diagnoses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '诊断ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `image_path` varchar(500) NOT NULL COMMENT '图像路径',
  `original_filename` varchar(255) DEFAULT NULL COMMENT '原始文件名',
  `image_size` varchar(50) DEFAULT NULL COMMENT '图像尺寸',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  
  -- AI检测结果
  `ai_detection_result` json DEFAULT NULL COMMENT 'AI检测结果JSON',
  `detected_types` text DEFAULT NULL COMMENT '检测到的类型列表',
  `confidence_scores` text DEFAULT NULL COMMENT '置信度分数',
  `bounding_boxes` json DEFAULT NULL COMMENT '边界框坐标',
  
  -- 分类信息
  `primary_category` varchar(50) DEFAULT NULL COMMENT '主要分类',
  `secondary_category` varchar(100) DEFAULT NULL COMMENT '次要分类',
  `hemangioma_type` varchar(100) DEFAULT NULL COMMENT '血管瘤类型',
  
  -- LLM诊断建议
  `llm_recommendation` json DEFAULT NULL COMMENT 'LLM诊断建议JSON',
  `treatment_suggestion` text DEFAULT NULL COMMENT '治疗建议',
  `precautions` text DEFAULT NULL COMMENT '注意事项',
  `disclaimer` text DEFAULT NULL COMMENT '免责声明',
  
  -- 医生标注
  `doctor_diagnosis` text DEFAULT NULL COMMENT '医生诊断',
  `doctor_notes` text DEFAULT NULL COMMENT '医生备注',
  `is_confirmed` tinyint(1) DEFAULT '0' COMMENT '是否已确认：1-是，0-否',
  `confirmed_by` bigint(20) DEFAULT NULL COMMENT '确认医生ID',
  `confirmed_at` timestamp NULL DEFAULT NULL COMMENT '确认时间',
  
  -- 状态信息
  `processing_status` varchar(20) DEFAULT 'PENDING' COMMENT '处理状态：PENDING, PROCESSING, COMPLETED, FAILED',
  `ai_processing_time` int(11) DEFAULT NULL COMMENT 'AI处理时间（毫秒）',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_hemangioma_type` (`hemangioma_type`),
  KEY `idx_processing_status` (`processing_status`),
  CONSTRAINT `fk_hemangioma_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='血管瘤诊断记录表';

-- ----------------------------
-- 血管瘤类型字典表
-- ----------------------------
DROP TABLE IF EXISTS `hemangioma_types`;
CREATE TABLE `hemangioma_types` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `category` varchar(50) NOT NULL COMMENT '大类别',
  `type_code` varchar(20) NOT NULL COMMENT '类型代码',
  `type_name` varchar(100) NOT NULL COMMENT '类型名称',
  `description` text DEFAULT NULL COMMENT '描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_code` (`type_code`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='血管瘤类型字典表';

-- ----------------------------
-- 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text DEFAULT NULL COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'STRING' COMMENT '配置类型：STRING, NUMBER, BOOLEAN, JSON',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统配置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ----------------------------
-- 操作日志表
-- ----------------------------
DROP TABLE IF EXISTS `operation_logs`;
CREATE TABLE `operation_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `operation` varchar(100) NOT NULL COMMENT '操作类型',
  `resource_type` varchar(50) DEFAULT NULL COMMENT '资源类型',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '资源ID',
  `details` json DEFAULT NULL COMMENT '操作详情',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation` (`operation`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- ----------------------------
-- 插入初始数据
-- ----------------------------

-- 插入默认管理员用户
INSERT INTO `users` (`username`, `password`, `email`, `real_name`, `role`, `status`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiE.rV8NtjjAaEKqhJGJRdqKRi', '<EMAIL>', '系统管理员', 'ADMIN', 1);

-- 插入血管瘤类型数据
INSERT INTO `hemangioma_types` (`category`, `type_code`, `type_name`, `description`, `sort_order`) VALUES
-- 真性血管肿瘤 (15种)
('真性血管肿瘤', 'IH', '婴幼儿血管瘤', '最常见的婴幼儿良性血管肿瘤', 1),
('真性血管肿瘤', 'CH', '先天性血管瘤', '出生时即存在的血管瘤', 2),
('真性血管肿瘤', 'PG', '化脓性肉芽肿', '快速生长的血管性病变', 3),
('真性血管肿瘤', 'Tufted', '簇状血管瘤', '罕见的血管肿瘤', 4),
('真性血管肿瘤', 'KHE', '卡波西样血管内皮瘤', '中等恶性血管肿瘤', 5),
('真性血管肿瘤', 'Spindle', '梭形细胞血管瘤', '良性血管肿瘤', 6),
('真性血管肿瘤', 'Epithelioid', '上皮样血管瘤', '良性血管肿瘤', 7),
('真性血管肿瘤', 'Papillary', '乳头状血管内皮瘤', '良性血管肿瘤', 8),
('真性血管肿瘤', 'Retiform', '网状血管瘤', '中等恶性血管肿瘤', 9),
('真性血管肿瘤', 'Composite', '复合性血管瘤', '混合型血管肿瘤', 10),
('真性血管肿瘤', 'Kaposiform', '卡波西样血管瘤', '中等恶性血管肿瘤', 11),
('真性血管肿瘤', 'Hobnail', '鞋钉样血管瘤', '良性血管肿瘤', 12),
('真性血管肿瘤', 'Microvenular', '微静脉血管瘤', '良性血管肿瘤', 13),
('真性血管肿瘤', 'Anastomosing', '吻合性血管瘤', '良性血管肿瘤', 14),
('真性血管肿瘤', 'Glomeruloid', '肾小球样血管瘤', '良性血管肿瘤', 15),

-- 血管畸形 (8种)
('血管畸形', 'CM', '毛细血管畸形', '先天性毛细血管扩张', 16),
('血管畸形', 'VM', '静脉畸形', '静脉发育异常', 17),
('血管畸形', 'LM', '淋巴管畸形', '淋巴管发育异常', 18),
('血管畸形', 'AVM', '动静脉畸形', '动静脉直接连接', 19),
('血管畸形', 'AVF', '动静脉瘘', '动静脉异常连接', 20),
('血管畸形', 'PTEN', 'PTEN错构瘤综合征', '遗传性血管畸形', 21),
('血管畸形', 'CLOVES', 'CLOVES综合征', '复杂性血管畸形', 22),
('血管畸形', 'PROS', 'PROS综合征', 'PIK3CA相关过度生长综合征', 23),

-- 血管假瘤/易混淆病变 (6种)
('血管假瘤/易混淆病变', 'Bacillary', '杆菌性血管瘤病', '感染性血管增生', 24),
('血管假瘤/易混淆病变', 'Targetoid', '靶样血管瘤', '反应性血管增生', 25),
('血管假瘤/易混淆病变', 'Intravascular', '血管内乳头状内皮增生', '血管内增生性病变', 26),
('血管假瘤/易混淆病变', 'Reactive', '反应性血管增生', '炎症后血管增生', 27),
('血管假瘤/易混淆病变', 'Pseudoangiomatous', '假血管瘤样增生', '非真性血管病变', 28),
('血管假瘤/易混淆病变', 'Granulation', '肉芽组织增生', '创伤后血管增生', 29);

-- 插入系统配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `is_system`) VALUES
('ai.model.path', '/app/models/yolo_hemangioma.pt', 'STRING', 'YOLO模型文件路径', 1),
('ai.confidence.threshold', '0.5', 'NUMBER', 'AI检测置信度阈值', 1),
('ai.max.detections', '10', 'NUMBER', '最大检测数量', 1),
('llm.model.name', 'deepseek-r1:8b', 'STRING', 'LLM模型名称', 1),
('llm.timeout', '60', 'NUMBER', 'LLM请求超时时间（秒）', 1),
('upload.max.size', '10485760', 'NUMBER', '最大上传文件大小（字节）', 1),
('upload.allowed.types', 'jpg,jpeg,png,bmp,tiff', 'STRING', '允许上传的文件类型', 1);

SET FOREIGN_KEY_CHECKS = 1;
