package com.medical.annotation.model;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@Entity
@Table(name = "image_pairs")
public class ImagePair {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "image_one_path", nullable = false)
    private String imageOnePath;
    
    @Column(name = "image_two_path")
    private String imageTwoPath;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "created_by")
    private Integer createdBy;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "diagnosis_id")
    private Integer diagnosisId;
    
    // 构造函数
    public ImagePair() {
        // 设置创建时间和更新时间
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        this.createdAt = chinaTime.toLocalDateTime();
        this.updatedAt = chinaTime.toLocalDateTime();
    }
    
    @PrePersist
    protected void onCreate() {
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        if (createdAt == null) {
            createdAt = chinaTime.toLocalDateTime();
        }
        updatedAt = chinaTime.toLocalDateTime();
    }
    
    @PreUpdate
    protected void onUpdate() {
        ZonedDateTime chinaTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        updatedAt = chinaTime.toLocalDateTime();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    // 兼容Integer类型的id设置
    public void setId(Integer id) {
        this.id = id != null ? id.longValue() : null;
    }
    
    public String getImageOnePath() {
        return imageOnePath;
    }
    
    public void setImageOnePath(String imageOnePath) {
        this.imageOnePath = imageOnePath;
    }
    
    public String getImageTwoPath() {
        return imageTwoPath;
    }
    
    public void setImageTwoPath(String imageTwoPath) {
        this.imageTwoPath = imageTwoPath;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Integer getDiagnosisId() {
        return diagnosisId;
    }
    
    public void setDiagnosisId(Integer diagnosisId) {
        this.diagnosisId = diagnosisId;
    }
} 