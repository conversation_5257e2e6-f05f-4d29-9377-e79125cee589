package com.medical.annotation.service;

import com.medical.annotation.model.ReviewerApplication;
import java.util.List;
import java.util.Optional;

public interface ReviewerApplicationService {
    
    // 创建申请
    ReviewerApplication createApplication(Integer userId, String reason);
    
    // 处理申请
    ReviewerApplication processApplication(Integer id, String status, String remarks, Integer processorId);
    
    // 获取用户的申请历史
    List<ReviewerApplication> getUserApplications(Integer userId);
    
    // 获取待处理的申请
    List<ReviewerApplication> getPendingApplications();
    
    // 获取已处理的申请
    List<ReviewerApplication> getProcessedApplications();
    
    // 获取单个申请
    Optional<ReviewerApplication> getApplication(Integer id);
} 