<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="15" class="stat-cards">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" style="margin-bottom: 15px;">
        <div class="header-actions">
          <h2 style="margin: 0;">工作台</h2>
          <el-button type="primary" size="small" @click="manualRefresh">
            <el-icon><Refresh /></el-icon> 刷新数据
          </el-button>
        </div>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="clickable-card" @click="navigateTo('/app/cases')">
          <div class="stat-item">
            <div class="stat-title">病例总数</div>
            <div class="stat-value">{{ dashboardStats.totalCount }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="clickable-card" @click="navigateTo('/app/cases', 'REVIEWED')">
          <div class="stat-item">
            <div class="stat-title">已标注</div>
            <div class="stat-value">{{ dashboardStats.reviewedCount }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="clickable-card" @click="navigateTo('/app/cases', 'SUBMITTED')">
          <div class="stat-item">
            <div class="stat-title">待审核</div>
            <div class="stat-value">{{ dashboardStats.submittedCount }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="clickable-card" @click="navigateTo('/app/cases', 'APPROVED')">
          <div class="stat-item">
            <div class="stat-title">已通过</div>
            <div class="stat-value">{{ dashboardStats.approvedCount }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作按钮 -->
    <div class="quick-actions">
      <div class="table-header">
        <h2><span>快捷操作</span></h2>
      </div>
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-card shadow="hover" class="action-card" @click="navigateTo('/app/hemangioma-diagnosis')">
            <div class="action-content">
              <el-icon class="action-icon"><Refresh /></el-icon>
              <div class="action-title">血管瘤诊断</div>
              <div class="action-desc">使用AI辅助诊断血管瘤</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-card shadow="hover" class="action-card" @click="navigateTo('/app/teams')">
            <div class="action-content">
              <el-icon class="action-icon"><UserFilled /></el-icon>
              <div class="action-title">团队标注</div>
              <div class="action-desc">查看团队标注信息</div>
      </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import axios from 'axios'
import { Loading, Refresh, Plus, UserFilled } from '@element-plus/icons-vue'
import { DASHBOARD_STATS_WITH_PARAMS } from '../config/api.config'

// 全局缓存，保证统计数据可以即时显示
let globalStatsCache = {
  totalCount: 0,
  reviewedCount: 0,
  submittedCount: 0,
  approvedCount: 0,
  rejectedCount: 0
};

// 恢复预加载函数，但修复为使用数字ID
export function preloadDashboardData() {
  console.log('预加载Dashboard数据...');
  try {
    const userStr = localStorage.getItem('user') || '{}';
    const user = JSON.parse(userStr);
    // 只使用数字ID
    const userId = user.id;
    
    if (!userId) {
      console.warn('未获取到用户ID，跳过预加载');
      return;
    }
    
    const url = DASHBOARD_STATS_WITH_PARAMS(userId);
    console.log('预加载仪表盘数据，URL:', url);
    
    axios.get(url, {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    .then(response => {
      if (response.data) {
        globalStatsCache = {
          totalCount: parseInt(response.data.totalCount || 0),
          reviewedCount: parseInt(response.data.reviewedCount || 0),
          submittedCount: parseInt(response.data.submittedCount || 0),
          approvedCount: parseInt(response.data.approvedCount || 0),
          rejectedCount: parseInt(response.data.rejectedCount || 0)
        };
        console.log('预加载数据更新完成');
      }
    })
    .catch(err => console.error('预加载数据失败:', err));
  } catch (e) {
    console.error('预加载过程出错:', e);
  }
}

export default {
  name: 'Dashboard',
  components: {
    Loading,
    Refresh,
    Plus,
    UserFilled
  },
  data() {
    return {
      dashboardStats: { ...globalStatsCache }
    }
  },
  computed: {
    ...mapGetters({
      currentUserId: 'getUserId'
    }),
  },
  watch: {
    $route(to, from) {
      if (to.path === '/app/dashboard') {
        console.log('【路由切换】切换到 dashboard，重新加载数据');
        this.manualRefresh();
      }
    }
  },

  // --- Start of Lifecycle Hooks ---
  
  beforeRouteEnter(to, from, next) {
    // 简化路由钩子，移除预加载调用
    console.log('【路由前置】beforeRouteEnter');
    next(vm => {
      // 只调用一次刷新
      console.log('【路由前置】进入组件，执行一次刷新');
      vm.manualRefresh();
    });
  },

  created() {
    console.log('⭐⭐⭐ Dashboard组件 created: 组件被创建 ⭐⭐⭐');
    this.dashboardStats = { totalCount: 0, reviewedCount: 0, submittedCount: 0, approvedCount: 0, rejectedCount: 0 };
  },

  mounted() {
    console.log('⭐⭐⭐ Dashboard组件 mounted: 组件被挂载 ⭐⭐⭐');
    // 移除这里的刷新调用，避免重复
    // this.manualRefresh(); 
    
    // 只保留页面获得焦点时的刷新，移除定时器
    window.addEventListener('focus', this.handlePageFocus); // 页面获得焦点时刷新
    console.log('焦点事件监听已设置');
  },

  activated() {
    console.log('【keep-alive】Dashboard组件被激活');
    // 组件被重新激活时不立即刷新，避免和beforeRouteEnter重复
    // this.manualRefresh();
  },

  beforeUnmount() {
    console.log('⭐⭐⭐ Dashboard组件 beforeUnmount: 组件即将卸载 ⭐⭐⭐');
    // 清除所有事件监听，防止内存泄漏
    window.removeEventListener('focus', this.handlePageFocus);
    console.log('焦点事件监听已移除');
  },

  // --- End of Lifecycle Hooks ---

  methods: {
    ...mapActions(['resetState']),

    navigateTo(path, status) {
      const query = status ? { status } : {};
      localStorage.setItem('lastSelectedStatus', status || 'ALL');
      this.$router.push({ path, query });
    },
    
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleString();
    },

    handlePageFocus() {
      console.log('页面获得焦点，强制获取最新数据');
      this.manualRefresh();
    },

    async manualRefresh() {
      console.log('【手动刷新】开始...');
      
      // 修改为只获取数字ID
      let userId;
      try {
        // 先尝试从Vuex获取，这会返回数字ID
        userId = this.currentUserId;
        
        // 如果Vuex中没有，则从localStorage获取
        if (!userId) {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const user = JSON.parse(userStr);
            // 只使用id（数字ID）
            userId = user.id;
          }
        }
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
      
      if (!userId) {
        console.error('无法获取有效的用户ID，可能用户未登录');
        this.$message.error('无法获取用户ID，请重新登录');
        return;
      }
      
      console.log('【手动刷新】使用用户ID:', userId);

      const loading = this.$loading({ lock: true, text: '正在获取最新数据...' });
      try {
        const url = DASHBOARD_STATS_WITH_PARAMS(userId);
        const response = await fetch(url, {
          method: 'GET',
          headers: { 'Cache-Control': 'no-cache' },
          credentials: 'include'
        });

        if (!response.ok) throw new Error(`HTTP错误! 状态: ${response.status}`);
        
        const data = await response.json();
        console.log('【手动刷新】获取到的统计数据:', data);
        
        this.dashboardStats = {
          totalCount: parseInt(data.totalCount || 0),
          reviewedCount: parseInt(data.reviewedCount || 0),
          submittedCount: parseInt(data.submittedCount || 0),
          approvedCount: parseInt(data.approvedCount || 0),
          rejectedCount: parseInt(data.rejectedCount || 0)
        };
        globalStatsCache = { ...this.dashboardStats };
        this.$forceUpdate();
        
      } catch (error) {
        console.error('【手动刷新】失败:', error);
        this.$message.error(`刷新失败: ${error.message}`);
      } finally {
        loading.close();
      }
    },
  },
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}
.stat-cards {
  margin-bottom: 24px;
  width: 100%;
}
.clickable-card {
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  margin-bottom: 15px;
}
.clickable-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
.stat-item {
  text-align: center;
}
.stat-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 10px;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}
.quick-actions {
  background: #fff;
  padding: 24px;
  border-radius: 4px;
  margin-bottom: 20px;
}
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.table-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}
.action-card {
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  margin-bottom: 15px;
}
.action-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
.action-content {
  text-align: center;
  padding: 20px 0;
}
.action-icon {
  font-size: 36px;
  color: #409EFF;
  margin-bottom: 10px;
}
.action-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}
.action-desc {
  font-size: 14px;
  color: #606266;
}
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style> 