"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[843],{14278:(e,t,n)=>{n.d(t,{DD:()=>v,GM:()=>D,Mn:()=>r,OM:()=>f,Ol:()=>m,R9:()=>l,WY:()=>u,_N:()=>p,ir:()=>h,kb:()=>a,ni:()=>c,pG:()=>i,qZ:()=>s,sQ:()=>o,xf:()=>d});var r="top",o="bottom",i="right",a="left",s="auto",f=[r,o,i,a],c="start",p="end",u="clippingParents",l="viewport",d="popper",h="reference",m=f.reduce((function(e,t){return e.concat([t+"-"+c,t+"-"+p])}),[]),v=[].concat(f,[s]).reduce((function(e,t){return e.concat([t,t+"-"+c,t+"-"+p])}),[]),g="beforeRead",b="read",y="afterRead",x="beforeMain",w="main",O="afterMain",k="beforeWrite",M="write",j="afterWrite",D=[g,b,y,x,w,O,k,M,j]},40485:(e,t,n)=>{function r(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function o(e){var t=r(e).Element;return e instanceof t||e instanceof Element}function i(e){var t=r(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function a(e){if("undefined"===typeof ShadowRoot)return!1;var t=r(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}n.d(t,{n4:()=>Qe});var s=Math.max,f=Math.min,c=Math.round;function p(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function u(){return!/^((?!chrome|android).)*safari/i.test(p())}function l(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var a=e.getBoundingClientRect(),s=1,f=1;t&&i(e)&&(s=e.offsetWidth>0&&c(a.width)/e.offsetWidth||1,f=e.offsetHeight>0&&c(a.height)/e.offsetHeight||1);var p=o(e)?r(e):window,l=p.visualViewport,d=!u()&&n,h=(a.left+(d&&l?l.offsetLeft:0))/s,m=(a.top+(d&&l?l.offsetTop:0))/f,v=a.width/s,g=a.height/f;return{width:v,height:g,top:m,right:h+v,bottom:m+g,left:h,x:h,y:m}}function d(e){var t=r(e),n=t.pageXOffset,o=t.pageYOffset;return{scrollLeft:n,scrollTop:o}}function h(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function m(e){return e!==r(e)&&i(e)?h(e):d(e)}function v(e){return e?(e.nodeName||"").toLowerCase():null}function g(e){return((o(e)?e.ownerDocument:e.document)||window.document).documentElement}function b(e){return l(g(e)).left+d(e).scrollLeft}function y(e){return r(e).getComputedStyle(e)}function x(e){var t=y(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function w(e){var t=e.getBoundingClientRect(),n=c(t.width)/e.offsetWidth||1,r=c(t.height)/e.offsetHeight||1;return 1!==n||1!==r}function O(e,t,n){void 0===n&&(n=!1);var r=i(t),o=i(t)&&w(t),a=g(t),s=l(e,o,n),f={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(r||!r&&!n)&&(("body"!==v(t)||x(a))&&(f=m(t)),i(t)?(c=l(t,!0),c.x+=t.clientLeft,c.y+=t.clientTop):a&&(c.x=b(a))),{x:s.left+f.scrollLeft-c.x,y:s.top+f.scrollTop-c.y,width:s.width,height:s.height}}function k(e){var t=l(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function M(e){return"html"===v(e)?e:e.assignedSlot||e.parentNode||(a(e)?e.host:null)||g(e)}function j(e){return["html","body","#document"].indexOf(v(e))>=0?e.ownerDocument.body:i(e)&&x(e)?e:j(M(e))}function D(e,t){var n;void 0===t&&(t=[]);var o=j(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),a=r(o),s=i?[a].concat(a.visualViewport||[],x(o)?o:[]):o,f=t.concat(s);return i?f:f.concat(D(M(s)))}function E(e){return["table","td","th"].indexOf(v(e))>=0}function A(e){return i(e)&&"fixed"!==y(e).position?e.offsetParent:null}function W(e){var t=/firefox/i.test(p()),n=/Trident/i.test(p());if(n&&i(e)){var r=y(e);if("fixed"===r.position)return null}var o=M(e);a(o)&&(o=o.host);while(i(o)&&["html","body"].indexOf(v(o))<0){var s=y(o);if("none"!==s.transform||"none"!==s.perspective||"paint"===s.contain||-1!==["transform","perspective"].indexOf(s.willChange)||t&&"filter"===s.willChange||t&&s.filter&&"none"!==s.filter)return o;o=o.parentNode}return null}function L(e){var t=r(e),n=A(e);while(n&&E(n)&&"static"===y(n).position)n=A(n);return n&&("html"===v(n)||"body"===v(n)&&"static"===y(n).position)?t:n||W(e)||t}var P=n(14278);function R(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name);var i=[].concat(e.requires||[],e.requiresIfExists||[]);i.forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}function B(e){var t=R(e);return P.GM.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}function H(e){var t;return function(){return t||(t=new Promise((function(n){Promise.resolve().then((function(){t=void 0,n(e())}))}))),t}}function T(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}var G={placement:"bottom",modifiers:[],strategy:"absolute"};function q(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"===typeof e.getBoundingClientRect)}))}function C(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,a=void 0===i?G:i;return function(e,t,n){void 0===n&&(n=a);var i={placement:"bottom",orderedModifiers:[],options:Object.assign({},G,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},s=[],f=!1,c={state:i,setOptions:function(n){var s="function"===typeof n?n(i.options):n;u(),i.options=Object.assign({},a,i.options,s),i.scrollParents={reference:o(e)?D(e):e.contextElement?D(e.contextElement):[],popper:D(t)};var f=B(T([].concat(r,i.options.modifiers)));return i.orderedModifiers=f.filter((function(e){return e.enabled})),p(),c.update()},forceUpdate:function(){if(!f){var e=i.elements,t=e.reference,n=e.popper;if(q(t,n)){i.rects={reference:O(t,L(n),"fixed"===i.options.strategy),popper:k(n)},i.reset=!1,i.placement=i.options.placement,i.orderedModifiers.forEach((function(e){return i.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<i.orderedModifiers.length;r++)if(!0!==i.reset){var o=i.orderedModifiers[r],a=o.fn,s=o.options,p=void 0===s?{}:s,u=o.name;"function"===typeof a&&(i=a({state:i,options:p,name:u,instance:c})||i)}else i.reset=!1,r=-1}}},update:H((function(){return new Promise((function(e){c.forceUpdate(),e(i)}))})),destroy:function(){u(),f=!0}};if(!q(e,t))return c;function p(){i.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,o=e.effect;if("function"===typeof o){var a=o({state:i,name:t,instance:c,options:r}),f=function(){};s.push(a||f)}}))}function u(){s.forEach((function(e){return e()})),s=[]}return c.setOptions(n).then((function(e){!f&&n.onFirstUpdate&&n.onFirstUpdate(e)})),c}}var Q={passive:!0};function S(e){var t=e.state,n=e.instance,o=e.options,i=o.scroll,a=void 0===i||i,s=o.resize,f=void 0===s||s,c=r(t.elements.popper),p=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&p.forEach((function(e){e.addEventListener("scroll",n.update,Q)})),f&&c.addEventListener("resize",n.update,Q),function(){a&&p.forEach((function(e){e.removeEventListener("scroll",n.update,Q)})),f&&c.removeEventListener("resize",n.update,Q)}}const N={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:S,data:{}};function V(e){return e.split("-")[0]}function _(e){return e.split("-")[1]}function I(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function F(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?V(o):null,a=o?_(o):null,s=n.x+n.width/2-r.width/2,f=n.y+n.height/2-r.height/2;switch(i){case P.Mn:t={x:s,y:n.y-r.height};break;case P.sQ:t={x:s,y:n.y+n.height};break;case P.pG:t={x:n.x+n.width,y:f};break;case P.kb:t={x:n.x-r.width,y:f};break;default:t={x:n.x,y:n.y}}var c=i?I(i):null;if(null!=c){var p="y"===c?"height":"width";switch(a){case P.ni:t[c]=t[c]-(n[p]/2-r[p]/2);break;case P._N:t[c]=t[c]+(n[p]/2-r[p]/2);break;default:}}return t}function U(e){var t=e.state,n=e.name;t.modifiersData[n]=F({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}const Y={name:"popperOffsets",enabled:!0,phase:"read",fn:U,data:{}};var z={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Z(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:c(n*o)/o||0,y:c(r*o)/o||0}}function X(e){var t,n=e.popper,o=e.popperRect,i=e.placement,a=e.variation,s=e.offsets,f=e.position,c=e.gpuAcceleration,p=e.adaptive,u=e.roundOffsets,l=e.isFixed,d=s.x,h=void 0===d?0:d,m=s.y,v=void 0===m?0:m,b="function"===typeof u?u({x:h,y:v}):{x:h,y:v};h=b.x,v=b.y;var x=s.hasOwnProperty("x"),w=s.hasOwnProperty("y"),O=P.kb,k=P.Mn,M=window;if(p){var j=L(n),D="clientHeight",E="clientWidth";if(j===r(n)&&(j=g(n),"static"!==y(j).position&&"absolute"===f&&(D="scrollHeight",E="scrollWidth")),i===P.Mn||(i===P.kb||i===P.pG)&&a===P._N){k=P.sQ;var A=l&&j===M&&M.visualViewport?M.visualViewport.height:j[D];v-=A-o.height,v*=c?1:-1}if(i===P.kb||(i===P.Mn||i===P.sQ)&&a===P._N){O=P.pG;var W=l&&j===M&&M.visualViewport?M.visualViewport.width:j[E];h-=W-o.width,h*=c?1:-1}}var R,B=Object.assign({position:f},p&&z),H=!0===u?Z({x:h,y:v},r(n)):{x:h,y:v};return h=H.x,v=H.y,c?Object.assign({},B,(R={},R[k]=w?"0":"",R[O]=x?"0":"",R.transform=(M.devicePixelRatio||1)<=1?"translate("+h+"px, "+v+"px)":"translate3d("+h+"px, "+v+"px, 0)",R)):Object.assign({},B,(t={},t[k]=w?v+"px":"",t[O]=x?h+"px":"",t.transform="",t))}function J(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,i=n.adaptive,a=void 0===i||i,s=n.roundOffsets,f=void 0===s||s,c={placement:V(t.placement),variation:_(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,X(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:f})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,X(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const K={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:J,data:{}};function $(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];i(o)&&v(o)&&(Object.assign(o.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))}function ee(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],o=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]),s=a.reduce((function(e,t){return e[t]="",e}),{});i(r)&&v(r)&&(Object.assign(r.style,s),Object.keys(o).forEach((function(e){r.removeAttribute(e)})))}))}}const te={name:"applyStyles",enabled:!0,phase:"write",fn:$,effect:ee,requires:["computeStyles"]};function ne(e,t,n){var r=V(e),o=[P.kb,P.Mn].indexOf(r)>=0?-1:1,i="function"===typeof n?n(Object.assign({},t,{placement:e})):n,a=i[0],s=i[1];return a=a||0,s=(s||0)*o,[P.kb,P.pG].indexOf(r)>=0?{x:s,y:a}:{x:a,y:s}}function re(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=P.DD.reduce((function(e,n){return e[n]=ne(n,t.rects,i),e}),{}),s=a[t.placement],f=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=a}const oe={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:re};var ie={left:"right",right:"left",bottom:"top",top:"bottom"};function ae(e){return e.replace(/left|right|bottom|top/g,(function(e){return ie[e]}))}var se={start:"end",end:"start"};function fe(e){return e.replace(/start|end/g,(function(e){return se[e]}))}function ce(e,t){var n=r(e),o=g(e),i=n.visualViewport,a=o.clientWidth,s=o.clientHeight,f=0,c=0;if(i){a=i.width,s=i.height;var p=u();(p||!p&&"fixed"===t)&&(f=i.offsetLeft,c=i.offsetTop)}return{width:a,height:s,x:f+b(e),y:c}}function pe(e){var t,n=g(e),r=d(e),o=null==(t=e.ownerDocument)?void 0:t.body,i=s(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),a=s(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),f=-r.scrollLeft+b(e),c=-r.scrollTop;return"rtl"===y(o||n).direction&&(f+=s(n.clientWidth,o?o.clientWidth:0)-i),{width:i,height:a,x:f,y:c}}function ue(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&a(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function le(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function de(e,t){var n=l(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}function he(e,t,n){return t===P.R9?le(ce(e,n)):o(t)?de(t,n):le(pe(g(e)))}function me(e){var t=D(M(e)),n=["absolute","fixed"].indexOf(y(e).position)>=0,r=n&&i(e)?L(e):e;return o(r)?t.filter((function(e){return o(e)&&ue(e,r)&&"body"!==v(e)})):[]}function ve(e,t,n,r){var o="clippingParents"===t?me(e):[].concat(t),i=[].concat(o,[n]),a=i[0],c=i.reduce((function(t,n){var o=he(e,n,r);return t.top=s(o.top,t.top),t.right=f(o.right,t.right),t.bottom=f(o.bottom,t.bottom),t.left=s(o.left,t.left),t}),he(e,a,r));return c.width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c}function ge(){return{top:0,right:0,bottom:0,left:0}}function be(e){return Object.assign({},ge(),e)}function ye(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function xe(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=void 0===r?e.placement:r,a=n.strategy,s=void 0===a?e.strategy:a,f=n.boundary,c=void 0===f?P.WY:f,p=n.rootBoundary,u=void 0===p?P.R9:p,d=n.elementContext,h=void 0===d?P.xf:d,m=n.altBoundary,v=void 0!==m&&m,b=n.padding,y=void 0===b?0:b,x=be("number"!==typeof y?y:ye(y,P.OM)),w=h===P.xf?P.ir:P.xf,O=e.rects.popper,k=e.elements[v?w:h],M=ve(o(k)?k:k.contextElement||g(e.elements.popper),c,u,s),j=l(e.elements.reference),D=F({reference:j,element:O,strategy:"absolute",placement:i}),E=le(Object.assign({},O,D)),A=h===P.xf?E:j,W={top:M.top-A.top+x.top,bottom:A.bottom-M.bottom+x.bottom,left:M.left-A.left+x.left,right:A.right-M.right+x.right},L=e.modifiersData.offset;if(h===P.xf&&L){var R=L[i];Object.keys(W).forEach((function(e){var t=[P.pG,P.sQ].indexOf(e)>=0?1:-1,n=[P.Mn,P.sQ].indexOf(e)>=0?"y":"x";W[e]+=R[n]*t}))}return W}function we(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,i=n.rootBoundary,a=n.padding,s=n.flipVariations,f=n.allowedAutoPlacements,c=void 0===f?P.DD:f,p=_(r),u=p?s?P.Ol:P.Ol.filter((function(e){return _(e)===p})):P.OM,l=u.filter((function(e){return c.indexOf(e)>=0}));0===l.length&&(l=u);var d=l.reduce((function(t,n){return t[n]=xe(e,{placement:n,boundary:o,rootBoundary:i,padding:a})[V(n)],t}),{});return Object.keys(d).sort((function(e,t){return d[e]-d[t]}))}function Oe(e){if(V(e)===P.qZ)return[];var t=ae(e);return[fe(e),t,fe(t)]}function ke(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,f=n.fallbackPlacements,c=n.padding,p=n.boundary,u=n.rootBoundary,l=n.altBoundary,d=n.flipVariations,h=void 0===d||d,m=n.allowedAutoPlacements,v=t.options.placement,g=V(v),b=g===v,y=f||(b||!h?[ae(v)]:Oe(v)),x=[v].concat(y).reduce((function(e,n){return e.concat(V(n)===P.qZ?we(t,{placement:n,boundary:p,rootBoundary:u,padding:c,flipVariations:h,allowedAutoPlacements:m}):n)}),[]),w=t.rects.reference,O=t.rects.popper,k=new Map,M=!0,j=x[0],D=0;D<x.length;D++){var E=x[D],A=V(E),W=_(E)===P.ni,L=[P.Mn,P.sQ].indexOf(A)>=0,R=L?"width":"height",B=xe(t,{placement:E,boundary:p,rootBoundary:u,altBoundary:l,padding:c}),H=L?W?P.pG:P.kb:W?P.sQ:P.Mn;w[R]>O[R]&&(H=ae(H));var T=ae(H),G=[];if(i&&G.push(B[A]<=0),s&&G.push(B[H]<=0,B[T]<=0),G.every((function(e){return e}))){j=E,M=!1;break}k.set(E,G)}if(M)for(var q=h?3:1,C=function(e){var t=x.find((function(t){var n=k.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return j=t,"break"},Q=q;Q>0;Q--){var S=C(Q);if("break"===S)break}t.placement!==j&&(t.modifiersData[r]._skip=!0,t.placement=j,t.reset=!0)}}const Me={name:"flip",enabled:!0,phase:"main",fn:ke,requiresIfExists:["offset"],data:{_skip:!1}};function je(e){return"x"===e?"y":"x"}function De(e,t,n){return s(e,f(t,n))}function Ee(e,t,n){var r=De(e,t,n);return r>n?n:r}function Ae(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=void 0===o||o,a=n.altAxis,c=void 0!==a&&a,p=n.boundary,u=n.rootBoundary,l=n.altBoundary,d=n.padding,h=n.tether,m=void 0===h||h,v=n.tetherOffset,g=void 0===v?0:v,b=xe(t,{boundary:p,rootBoundary:u,padding:d,altBoundary:l}),y=V(t.placement),x=_(t.placement),w=!x,O=I(y),M=je(O),j=t.modifiersData.popperOffsets,D=t.rects.reference,E=t.rects.popper,A="function"===typeof g?g(Object.assign({},t.rects,{placement:t.placement})):g,W="number"===typeof A?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),R=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,B={x:0,y:0};if(j){if(i){var H,T="y"===O?P.Mn:P.kb,G="y"===O?P.sQ:P.pG,q="y"===O?"height":"width",C=j[O],Q=C+b[T],S=C-b[G],N=m?-E[q]/2:0,F=x===P.ni?D[q]:E[q],U=x===P.ni?-E[q]:-D[q],Y=t.elements.arrow,z=m&&Y?k(Y):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:ge(),X=Z[T],J=Z[G],K=De(0,D[q],z[q]),$=w?D[q]/2-N-K-X-W.mainAxis:F-K-X-W.mainAxis,ee=w?-D[q]/2+N+K+J+W.mainAxis:U+K+J+W.mainAxis,te=t.elements.arrow&&L(t.elements.arrow),ne=te?"y"===O?te.clientTop||0:te.clientLeft||0:0,re=null!=(H=null==R?void 0:R[O])?H:0,oe=C+$-re-ne,ie=C+ee-re,ae=De(m?f(Q,oe):Q,C,m?s(S,ie):S);j[O]=ae,B[O]=ae-C}if(c){var se,fe="x"===O?P.Mn:P.kb,ce="x"===O?P.sQ:P.pG,pe=j[M],ue="y"===M?"height":"width",le=pe+b[fe],de=pe-b[ce],he=-1!==[P.Mn,P.kb].indexOf(y),me=null!=(se=null==R?void 0:R[M])?se:0,ve=he?le:pe-D[ue]-E[ue]-me+W.altAxis,be=he?pe+D[ue]+E[ue]-me-W.altAxis:de,ye=m&&he?Ee(ve,pe,be):De(m?ve:le,pe,m?be:de);j[M]=ye,B[M]=ye-pe}t.modifiersData[r]=B}}const We={name:"preventOverflow",enabled:!0,phase:"main",fn:Ae,requiresIfExists:["offset"]};var Le=function(e,t){return e="function"===typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e,be("number"!==typeof e?e:ye(e,P.OM))};function Pe(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=V(n.placement),f=I(s),c=[P.kb,P.pG].indexOf(s)>=0,p=c?"height":"width";if(i&&a){var u=Le(o.padding,n),l=k(i),d="y"===f?P.Mn:P.kb,h="y"===f?P.sQ:P.pG,m=n.rects.reference[p]+n.rects.reference[f]-a[f]-n.rects.popper[p],v=a[f]-n.rects.reference[f],g=L(i),b=g?"y"===f?g.clientHeight||0:g.clientWidth||0:0,y=m/2-v/2,x=u[d],w=b-l[p]-u[h],O=b/2-l[p]/2+y,M=De(x,O,w),j=f;n.modifiersData[r]=(t={},t[j]=M,t.centerOffset=M-O,t)}}function Re(e){var t=e.state,n=e.options,r=n.element,o=void 0===r?"[data-popper-arrow]":r;null!=o&&("string"!==typeof o||(o=t.elements.popper.querySelector(o),o))&&ue(t.elements.popper,o)&&(t.elements.arrow=o)}const Be={name:"arrow",enabled:!0,phase:"main",fn:Pe,effect:Re,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function He(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Te(e){return[P.Mn,P.pG,P.sQ,P.kb].some((function(t){return e[t]>=0}))}function Ge(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=xe(t,{elementContext:"reference"}),s=xe(t,{altBoundary:!0}),f=He(a,r),c=He(s,o,i),p=Te(f),u=Te(c);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:p,hasPopperEscaped:u},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":u})}const qe={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ge};var Ce=[N,Y,K,te,oe,Me,We,Be,qe],Qe=C({defaultModifiers:Ce})}}]);