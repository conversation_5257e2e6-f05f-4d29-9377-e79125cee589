"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[356],{5356:(e,t,n)=>{n.r(t),n.d(t,{default:()=>d});n(4114);var a=n(641),s=n(33),r={class:"cases-container"},i={class:"page-header"};function u(e,t,n,u,o,l){var c=(0,a.g2)("el-button"),d=(0,a.g2)("el-table-column"),p=(0,a.g2)("el-tag"),f=(0,a.g2)("el-table"),h=(0,a.g2)("el-pagination");return(0,a.uX)(),(0,a.CE)("div",r,[(0,a.Lk)("div",i,[t[2]||(t[2]=(0,a.Lk)("h2",null,"病例标注",-1)),(0,a.bF)(c,{type:"primary",onClick:t[0]||(t[0]=function(t){return e.$router.push("/cases/new")})},{default:(0,a.k6)((function(){return t[1]||(t[1]=[(0,a.eW)("新建标注")])})),_:1,__:[1]})]),(0,a.bF)(f,{data:o.casesList,style:{width:"100%"}},{default:(0,a.k6)((function(){return[(0,a.bF)(d,{prop:"caseId",label:"病例编号",width:"180"}),(0,a.bF)(d,{prop:"patientInfo",label:"患者信息",width:"180"}),(0,a.bF)(d,{prop:"department",label:"部位"}),(0,a.bF)(d,{prop:"type",label:"类型"}),(0,a.bF)(d,{prop:"status",label:"状态"},{default:(0,a.k6)((function(e){return[(0,a.bF)(p,{type:l.getStatusType(e.row.status)},{default:(0,a.k6)((function(){return[(0,a.eW)((0,s.v_)(e.row.status),1)]})),_:2},1032,["type"])]})),_:1}),(0,a.bF)(d,{prop:"createTime",label:"创建时间"}),(0,a.bF)(d,{label:"操作",width:"150"},{default:(0,a.k6)((function(e){return[(0,a.bF)(c,{type:"text",size:"small",onClick:function(t){return l.handleEdit(e.row)}},{default:(0,a.k6)((function(){return t[3]||(t[3]=[(0,a.eW)(" 编辑 ")])})),_:2,__:[3]},1032,["onClick"]),(0,a.bF)(c,{type:"text",size:"small",onClick:function(t){return l.handleView(e.row)}},{default:(0,a.k6)((function(){return t[4]||(t[4]=[(0,a.eW)(" 查看 ")])})),_:2,__:[4]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"]),(0,a.bF)(h,{background:"",layout:"prev, pager, next",total:o.totalItems,"page-size":o.pageSize,onCurrentChange:l.handlePageChange},null,8,["total","page-size","onCurrentChange"])])}const o={name:"CasesList",data:function(){return{casesList:[],pageSize:10,currentPage:1,totalItems:0}},methods:{getStatusType:function(e){var t={待标注:"info",已标注:"success",审核中:"warning",已通过:"success",已驳回:"danger"};return t[e]||"info"},handleEdit:function(e){this.$router.push("/cases/edit/".concat(e.id))},handleView:function(e){this.$router.push("/cases/view/".concat(e.id))},handlePageChange:function(e){this.currentPage=e,this.fetchCases()},fetchCases:function(){this.casesList=[],this.totalItems=0}},created:function(){this.fetchCases()}};var l=n(6262);const c=(0,l.A)(o,[["render",u],["__scopeId","data-v-3409e332"]]),d=c}}]);