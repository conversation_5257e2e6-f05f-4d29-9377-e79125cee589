import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import java.sql.Timestamp;
import java.time.LocalDateTime;

public class AddAdminToTeam {
    public static void main(String[] args) {
        System.out.println("Adding admin user to admin team...");

        // Database connection settings
        String url = "***********************************************";
        String username = "root";
        String password = ""; // Enter your MySQL password here

        // Create data source and JDBC template
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);

        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        
        try {
            // 1. Check if admin (ID 1) already has a department set
            String checkSql = "SELECT id, name, department FROM users WHERE id = 1";
            jdbcTemplate.query(checkSql, (rs, rowNum) -> {
                System.out.println("User ID: " + rs.getInt("id"));
                System.out.println("Name: " + rs.getString("name"));
                System.out.println("Current Department: " + rs.getString("department"));
                return null;
            });
            
            // 2. Add admin user (ID 1) to admin team (ID 1) in team_member_logs
            String insertSql = "INSERT INTO team_member_logs (user_id, team_id, action, performed_by, performed_at) VALUES (?, ?, ?, ?, ?)";
            int result = jdbcTemplate.update(insertSql, 1, 1, "ADD", 1, Timestamp.valueOf(LocalDateTime.now()));
            System.out.println("Insert result: " + result + " row(s) affected");
            
            // 3. Update the user's department field
            String updateSql = "UPDATE users SET department = ? WHERE id = 1";
            int updateResult = jdbcTemplate.update(updateSql, "管理员团队");
            System.out.println("Update result: " + updateResult + " row(s) affected");
            
            // 4. Verify the changes
            System.out.println("\nVerifying changes:");
            jdbcTemplate.query("SELECT id, name, department FROM users WHERE id = 1", (rs, rowNum) -> {
                System.out.println("User ID: " + rs.getInt("id"));
                System.out.println("Name: " + rs.getString("name"));
                System.out.println("Updated Department: " + rs.getString("department"));
                return null;
            });
            
            jdbcTemplate.query("SELECT * FROM team_member_logs", (rs, rowNum) -> {
                System.out.println("Log ID: " + rs.getInt("id"));
                System.out.println("User ID: " + rs.getInt("user_id"));
                System.out.println("Team ID: " + rs.getInt("team_id"));
                System.out.println("Action: " + rs.getString("action"));
                System.out.println("Performed At: " + rs.getTimestamp("performed_at"));
                System.out.println("----------------------");
                return null;
            });
            
            System.out.println("Admin user successfully added to admin team!");
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 