package com.medical.annotation.config;

import com.medical.annotation.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

/**
 * 文件存储配置类，负责在应用启动时初始化文件目录
 */
@Configuration
public class FileStorageConfig {

    @Autowired
    private FileService fileService;
    
    /**
     * 应用启动时初始化文件目录
     */
    @EventListener
    public void onApplicationEvent(ContextRefreshedEvent event) {
        try {
            fileService.init();
        } catch (Exception e) {
            // 记录异常但允许应用继续启动
            System.err.println("文件系统初始化错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 