package com.medical.annotation.model;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "users")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class User {
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", customId='" + customId + '\'' +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", role=" + role +
                ", department='" + department + '\'' +
                ", hospital='" + hospital + '\'' +
                ", avatarPath='" + avatarPath + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }

    public enum Role {
        ADMIN,        // 管理员
        DOCTOR,       // 标注医生
        REVIEWER,     // 审核医生
        USER          // 用户
    }
    
    public enum ReviewerApplicationStatus {
        PENDING,      // 审核中
        APPROVED,     // 已通过
        REJECTED      // 已拒绝
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "custom_id", unique = true)
    private String customId;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "email", nullable = false, unique = true)
    private String email;
    
    @Column(name = "password", nullable = false)
    private String password;
    
    @Column(name = "role", nullable = false, columnDefinition = "ENUM('USER', 'ADMIN', 'REVIEWER')")
    @Enumerated(EnumType.STRING)
    private Role role = Role.USER;
    
    @Column(name = "department")
    private String department;
    
    @Column(name = "hospital")
    private String hospital;
    
    // 添加头像路径字段
    @Column(name = "avatar_path")
    private String avatarPath;
    
    // 添加团队关联
    @ManyToOne
    @JoinColumn(name = "team_id")
    private Team team;
    
    // 添加审核医生申请状态
    @Column(name = "reviewer_application_status")
    @Enumerated(EnumType.STRING)
    private ReviewerApplicationStatus reviewerApplicationStatus;
    
    // 审核申请理由
    @Column(name = "reviewer_application_reason", columnDefinition = "TEXT")
    private String reviewerApplicationReason;
    
    // 审核申请时间
    @Column(name = "reviewer_application_date")
    private LocalDateTime reviewerApplicationDate;
    
    // 审核申请处理人
    @ManyToOne
    @JoinColumn(name = "reviewer_application_processed_by")
    private User reviewerApplicationProcessedBy;
    
    // 审核申请处理时间
    @Column(name = "reviewer_application_processed_date")
    private LocalDateTime reviewerApplicationProcessedDate;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "reset_password_token")
    private String resetPasswordToken;
    
    @Column(name = "reset_password_expire")
    private LocalDateTime resetPasswordExpire;
    
    // 构造函数
    public User() {
        this.createdAt = LocalDateTime.now();
        this.role = Role.DOCTOR;  // 默认为标注医生
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getCustomId() {
        return customId;
    }

    public void setCustomId(String customId) {
        this.customId = customId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public Role getRole() {
        return role;
    }
    
    public void setRole(Role role) {
        this.role = role;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public String getHospital() {
        return hospital;
    }
    
    public void setHospital(String hospital) {
        this.hospital = hospital;
    }
    
    // 头像路径的getter和setter
    public String getAvatarPath() {
        return avatarPath;
    }
    
    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getResetPasswordToken() {
        return resetPasswordToken;
    }
    
    public void setResetPasswordToken(String resetPasswordToken) {
        this.resetPasswordToken = resetPasswordToken;
    }
    
    public LocalDateTime getResetPasswordExpire() {
        return resetPasswordExpire;
    }
    
    public void setResetPasswordExpire(LocalDateTime resetPasswordExpire) {
        this.resetPasswordExpire = resetPasswordExpire;
    }
    
    // 新增的getter和setter
    public Team getTeam() {
        return team;
    }
    
    public void setTeam(Team team) {
        this.team = team;
    }
    
    public ReviewerApplicationStatus getReviewerApplicationStatus() {
        return reviewerApplicationStatus;
    }
    
    public void setReviewerApplicationStatus(ReviewerApplicationStatus reviewerApplicationStatus) {
        this.reviewerApplicationStatus = reviewerApplicationStatus;
    }
    
    public String getReviewerApplicationReason() {
        return reviewerApplicationReason;
    }
    
    public void setReviewerApplicationReason(String reviewerApplicationReason) {
        this.reviewerApplicationReason = reviewerApplicationReason;
    }
    
    public LocalDateTime getReviewerApplicationDate() {
        return reviewerApplicationDate;
    }
    
    public void setReviewerApplicationDate(LocalDateTime reviewerApplicationDate) {
        this.reviewerApplicationDate = reviewerApplicationDate;
    }
    
    public User getReviewerApplicationProcessedBy() {
        return reviewerApplicationProcessedBy;
    }
    
    public void setReviewerApplicationProcessedBy(User reviewerApplicationProcessedBy) {
        this.reviewerApplicationProcessedBy = reviewerApplicationProcessedBy;
    }
    
    public LocalDateTime getReviewerApplicationProcessedDate() {
        return reviewerApplicationProcessedDate;
    }
    
    public void setReviewerApplicationProcessedDate(LocalDateTime reviewerApplicationProcessedDate) {
        this.reviewerApplicationProcessedDate = reviewerApplicationProcessedDate;
    }
} 