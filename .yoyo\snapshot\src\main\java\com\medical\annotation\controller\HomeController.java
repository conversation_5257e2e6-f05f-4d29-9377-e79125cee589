package com.medical.annotation.controller;

import com.medical.annotation.model.ImageMetadata;
import com.medical.annotation.repository.ImageMetadataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HomeController {

    @Autowired
    private ImageMetadataRepository imageMetadataRepository;

    @GetMapping("/")
    public String home(Model model) {
        model.addAttribute("draftCount", imageMetadataRepository.findByStatus(ImageMetadata.Status.DRAFT).size());
        model.addAttribute("submittedCount", imageMetadataRepository.findByStatus(ImageMetadata.Status.SUBMITTED).size());
        model.addAttribute("approvedCount", imageMetadataRepository.findByStatus(ImageMetadata.Status.APPROVED).size());
        model.addAttribute("rejectedCount", imageMetadataRepository.findByStatus(ImageMetadata.Status.REJECTED).size());
        model.addAttribute("totalCount", imageMetadataRepository.count());
        return "index";
    }
} 