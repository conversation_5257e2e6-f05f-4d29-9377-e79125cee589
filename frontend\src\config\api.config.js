/**
 * API 配置文件
 * 统一管理API基础URL和上下文路径
 */

// 获取环境变量或使用默认值 - 修改为使用相对路径
// 在开发环境下，如果是通过内网穿透访问，我们应该使用相对路径
// 这样请求会发送到当前域名，而不是硬编码的IP地址
const API_BASE_URL = ''; // 使用空字符串表示相对于当前域名
const API_CONTEXT_PATH = process.env.VUE_APP_CONTEXT_PATH || '/medical';
const API_PREFIX = process.env.VUE_APP_API_PREFIX || '/api';

// 构建完整的API URL
const API_URL = `${API_BASE_URL}${API_CONTEXT_PATH}${API_PREFIX}`;

// 常用URL - 修改为标准仪表盘API，不再使用无限制版本
const DASHBOARD_STATS_URL = `${API_CONTEXT_PATH}/api/stats-v2/dashboard`;
// 确保仪表盘使用标准API且添加缓存控制参数
const DASHBOARD_STATS_WITH_PARAMS = (userId) => {
  const timestamp = Date.now();
  const random = Math.random();
  // 注意: userId必须为数字类型ID (如3)，不能使用自定义ID (如300000001)
  return `${DASHBOARD_STATS_URL}/${userId}?t=${timestamp}&r=${random}&forcePersonalStats=true&view=personal`;
};

// 添加缺失的isLocalhost函数，并修改为始终返回true，以便在任何环境下都使用相对路径
const isLocalhost = () => {
  return true; // 始终返回true，确保imageHelper使用相对路径
};

// 导出配置
export {
  API_BASE_URL,
  API_CONTEXT_PATH,
  API_PREFIX,
  API_URL,
  DASHBOARD_STATS_URL,
  DASHBOARD_STATS_WITH_PARAMS,
  isLocalhost
}; 