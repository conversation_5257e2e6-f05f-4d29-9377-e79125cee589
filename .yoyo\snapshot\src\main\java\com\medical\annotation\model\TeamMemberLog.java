package com.medical.annotation.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 团队成员日志实体类
 */
@Entity
@Table(name = "team_member_logs")
public class TeamMemberLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "custom_id")
    private String customId;
    
    @Column(name = "user_id")
    private Integer userId;
    
    @Column(name = "team_id")
    private Integer teamId;
    
    @Column(name = "action")
    private String action;
    
    @Column(name = "performed_by")
    private Integer performedBy;
    
    @Column(name = "performed_at")
    private Date performedAt;
    
    // 默认构造函数
    public TeamMemberLog() {}
    
    // 添加成员的构造函数
    public TeamMemberLog(Integer userId, Integer teamId, String action, Integer performedBy) {
        this.userId = userId;
        this.teamId = teamId;
        this.action = action;
        this.performedBy = performedBy;
        this.performedAt = new Date();
    }
    
    // 添加成员的构造函数 (包含customId)
    public TeamMemberLog(String customId, Integer userId, Integer teamId, String action, Integer performedBy) {
        this.customId = customId;
        this.userId = userId;
        this.teamId = teamId;
        this.action = action;
        this.performedBy = performedBy;
        this.performedAt = new Date();
    }
    
    // Getters and Setters
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getCustomId() {
        return customId;
    }
    
    public void setCustomId(String customId) {
        this.customId = customId;
    }
    
    public Integer getUserId() {
        return userId;
    }
    
    public void setUserId(Integer userId) {
        this.userId = userId;
    }
    
    public Integer getTeamId() {
        return teamId;
    }
    
    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public Integer getPerformedBy() {
        return performedBy;
    }
    
    public void setPerformedBy(Integer performedBy) {
        this.performedBy = performedBy;
    }
    
    public Date getPerformedAt() {
        return performedAt;
    }
    
    public void setPerformedAt(Date performedAt) {
        this.performedAt = performedAt;
    }
} 