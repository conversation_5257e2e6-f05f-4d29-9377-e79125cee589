package com.medical.annotation.service;

import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.model.Team;
import com.medical.annotation.model.TeamMemberLog;
import com.medical.annotation.model.User;
import com.medical.annotation.repository.TeamMemberLogRepository;
import com.medical.annotation.repository.TeamRepository;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.HemangiomaDiagnosisRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class TeamService {

    @Autowired
    private TeamRepository teamRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TeamMemberLogRepository teamMemberLogRepository;
    
    @Autowired
    private HemangiomaDiagnosisRepository hemangiomaDiagnosisRepository;
    
    /**
     * 创建新团队
     * @param team 团队信息
     * @param creatorId 创建者ID
     * @return 创建的团队
     */
    @Transactional
    public Team createTeam(Team team, Integer creatorId) throws Exception {
        try {
            System.out.println("=== 开始创建团队 ===");
        // 验证团队名是否已存在
        if (teamRepository.existsByName(team.getName())) {
            throw new Exception("团队名称已存在");
        }
        
        // 验证创建者是否存在
        Optional<User> creatorOpt = userRepository.findById(creatorId);
        if (!creatorOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        User creator = creatorOpt.get();
            System.out.println("创建者信息: ID=" + creator.getId() + ", 名称=" + creator.getName());
        
        // 设置创建者和时间
        team.setCreatedBy(creator);
            team.setOwner(creator); // 将创建者同时设置为群主
        team.setCreatedAt(LocalDateTime.now());
        
            System.out.println("准备保存团队: " + team.getName());
        // 保存团队
            Team savedTeam;
            try {
                savedTeam = teamRepository.save(team);
                System.out.println("团队保存成功, ID: " + savedTeam.getId());
            } catch (Exception e) {
                System.err.println("保存团队时出错: " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
            
            // 将创建者自动添加为团队成员
            try {
            creator.setTeam(savedTeam);
            userRepository.save(creator);
                System.out.println("已将创建者设置为团队成员");
            } catch (Exception e) {
                System.err.println("将创建者添加到团队时出错: " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
            
            // 记录团队成员添加日志
            try {
            addTeamMemberLog(creator.getId(), savedTeam.getId(), creator.getId());
                System.out.println("已记录团队成员添加日志");
            } catch (Exception e) {
                System.err.println("记录团队成员日志时出错: " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
            
            System.out.println("自动将创建者 " + creator.getName() + " (ID: " + creator.getId() + ") 添加为团队 " + savedTeam.getName() + " (ID: " + savedTeam.getId() + ") 的成员");
            System.out.println("=== 团队创建完成 ===");
            
            return savedTeam;
        } catch (Exception e) {
            System.err.println("创建团队过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 添加团队成员日志
     */
    private void addTeamMemberLog(Integer userId, Integer teamId, Integer performedBy) {
        addTeamMemberLog(userId, teamId, performedBy, "ADD");
    }

    /**
     * 添加团队成员日志，支持指定动作
     */
    private void addTeamMemberLog(Integer userId, Integer teamId, Integer performedBy, String action) {
            try {
            // 创建并保存团队成员日志
            TeamMemberLog log = new TeamMemberLog(userId, teamId, action, performedBy);
            System.out.println("创建团队成员日志: userId=" + userId + ", teamId=" + teamId + ", action=" + action);
            teamMemberLogRepository.save(log);
            System.out.println("团队成员日志保存成功");
        } catch (Exception e) {
            System.err.println("添加团队成员日志失败: " + e.getMessage());
            e.printStackTrace();
            throw e; // 重新抛出异常，让上层方法知道出错了
        }
    }
    
    /**
     * 更新团队信息
     * @param team 更新的团队信息
     * @param userId 操作用户ID
     * @return 更新后的团队
     */
    @Transactional
    public Team updateTeam(Team team, Integer userId) throws Exception {
        // 验证团队是否存在
        Optional<Team> existingTeamOpt = teamRepository.findById(team.getId());
        if (!existingTeamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        
        // 验证用户是否有权限（只有管理员、团队创建者或审核医生可以更新团队）
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        User user = userOpt.get();
        Team existingTeam = existingTeamOpt.get();
        
        if (user.getRole() != User.Role.ADMIN && 
            !existingTeam.getCreatedBy().getId().equals(userId) && 
            user.getRole() != User.Role.REVIEWER) {
            throw new Exception("无权更新团队");
        }
        
        // 更新团队信息
        existingTeam.setName(team.getName());
        existingTeam.setDescription(team.getDescription());
        existingTeam.setUpdatedAt(LocalDateTime.now());
        
        return teamRepository.save(existingTeam);
    }
    
    @Transactional
    public void deleteTeam(Integer teamId, Integer operatorId) throws Exception {
        Team team = teamRepository.findById(teamId)
                .orElseThrow(() -> new Exception("团队不存在"));
        
        User operator = userRepository.findById(operatorId)
                .orElseThrow(() -> new Exception("操作用户不存在"));

        // 权限校验：只有团队所有者(群主)或管理员才能删除团队
        boolean isOwner = team.getOwner() != null && team.getOwner().getId().equals(operator.getId());
        boolean isAdmin = operator.getRole() == User.Role.ADMIN;

        if (!isOwner && !isAdmin) {
            throw new Exception("只有团队所有者或管理员才能解散团队");
        }
        
        // 步骤1: 删除与该团队相关的所有团队成员日志
        teamMemberLogRepository.deleteByTeamId(teamId);

        // 步骤2: 找出所有团队成员
        List<User> members = userRepository.findByTeam_Id(teamId);

        // 步骤3: 将所有成员的 team 关联设置为 null
        for (User member : members) {
            member.setTeam(null);
        }
        if (!members.isEmpty()) {
            userRepository.saveAllAndFlush(members); // 批量更新用户信息并立即刷新
        }
        
        // 步骤4: 删除团队
        teamRepository.delete(team);
        }
        
    @Transactional
    public void transferOwnership(Integer teamId, Integer newOwnerId, Integer currentOwnerId) throws Exception {
        Team team = teamRepository.findById(teamId)
                .orElseThrow(() -> new Exception("团队不存在"));
        
        User currentOwner = userRepository.findById(currentOwnerId)
                .orElseThrow(() -> new Exception("当前操作用户不存在"));

        // 权限校验：只有当前的群主才能转让所有权
        if (!team.getOwner().getId().equals(currentOwner.getId())) {
            throw new Exception("只有团队所有者才能转让所有权");
        }
        
        User newOwner = userRepository.findById(newOwnerId)
                .orElseThrow(() -> new Exception("指定的新所有者用户不存在"));

        // 验证新所有者是否是团队成员
        if (newOwner.getTeam() == null || !newOwner.getTeam().getId().equals(teamId)) {
            throw new Exception("新所有者必须是该团队的成员");
        }
        
        // 更新所有者
        team.setOwner(newOwner);
        teamRepository.save(team);
    }
    
    /**
     * 将用户添加到团队
     * @param teamId 团队ID
     * @param userId 要添加的用户ID
     * @param operatorId 操作者ID
     */
    @Transactional
    public void addUserToTeam(Integer teamId, Integer userId, Integer operatorId) throws Exception {
        // 验证团队是否存在
        Optional<Team> teamOpt = teamRepository.findById(teamId);
        if (!teamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        
        // 验证要添加的用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        // 验证操作者是否有权限
        Optional<User> operatorOpt = userRepository.findById(operatorId);
        if (!operatorOpt.isPresent()) {
            throw new Exception("操作者不存在");
        }
        
        User operator = operatorOpt.get();
        Team team = teamOpt.get();
        
        if (operator.getRole() != User.Role.ADMIN && 
            !team.getCreatedBy().getId().equals(operatorId) &&
            operator.getRole() != User.Role.REVIEWER) {
            throw new Exception("无权添加团队成员");
        }
        
        // 将用户添加到团队
        User user = userOpt.get();
        user.setTeam(team);
        userRepository.save(user);
        
        // 移除团队成员日志记录，简化流程
        // addTeamMemberLog(userId, teamId, operatorId);
    }
    
    /**
     * 将用户从团队中移除
     * @param teamId 团队ID
     * @param userId 要移除的用户ID
     * @param operatorId 操作者ID
     */
    @Transactional
    public void removeUserFromTeam(Integer teamId, Integer userId, Integer operatorId) throws Exception {
        // 验证团队是否存在
        Optional<Team> teamOpt = teamRepository.findById(teamId);
        if (!teamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        
        // 验证要移除的用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new Exception("用户不存在");
        }
        
        // 验证操作者是否有权限
        Optional<User> operatorOpt = userRepository.findById(operatorId);
        if (!operatorOpt.isPresent()) {
            throw new Exception("操作者不存在");
        }
        
        User operator = operatorOpt.get();
        Team team = teamOpt.get();
        User user = userOpt.get();
        
        if (operator.getRole() != User.Role.ADMIN && 
            !team.getCreatedBy().getId().equals(operatorId) &&
            operator.getRole() != User.Role.REVIEWER) {
            throw new Exception("无权移除团队成员");
        }
        
        // 验证用户是否在该团队中
        if (user.getTeam() == null || !user.getTeam().getId().equals(teamId)) {
            throw new Exception("用户不在该团队中");
        }
        
        // 将用户从团队中移除
        user.setTeam(null);
        userRepository.save(user);
        
        // 记录团队成员移除日志
        addTeamMemberLog(userId, teamId, operatorId, "REMOVE");
    }
    
    /**
     * 获取团队信息
     * @param teamId 团队ID
     * @return 团队信息
     */
    public Team getTeamById(Integer teamId) throws Exception {
        Optional<Team> teamOpt = teamRepository.findById(teamId);
        if (!teamOpt.isPresent()) {
            throw new Exception("团队不存在");
        }
        return teamOpt.get();
    }
    
    /**
     * 获取团队成员
     * @param teamId 团队ID
     * @return 团队成员列表
     */
    public List<User> getTeamMembers(Integer teamId) throws Exception {
        // 验证团队是否存在
        if (!teamRepository.existsById(teamId)) {
            throw new Exception("团队不存在");
        }
        
        return userRepository.findByTeam_Id(teamId);
    }
    
    /**
     * 获取所有团队
     * @return 团队列表
     */
    public List<Team> getAllTeams() {
        return teamRepository.findAll();
    }
    
    /**
     * 搜索团队
     * @param keyword 关键词
     * @return 团队列表
     */
    public List<Team> searchTeams(String keyword) {
        return teamRepository.findByNameContainingIgnoreCase(keyword);
    }
    
    /**
     * 获取团队已批准的诊断记录列表。
     * @param teamId 团队ID
     * @return 团队已批准的诊断记录列表
     * @throws Exception 如果团队不存在
     */
    @Transactional(readOnly = true)
    public List<HemangiomaDiagnosis> getTeamApprovedAnnotations(Integer teamId) throws Exception {
        Team team = teamRepository.findById(teamId)
                .orElseThrow(() -> new Exception("团队不存在"));
        
        // 注意: 这里的查询逻辑假设 HemangiomaDiagnosis 与 User 关联, User 与 Team 关联
        return hemangiomaDiagnosisRepository.findByStatusAndUser_Team(HemangiomaDiagnosis.Status.APPROVED, team);
    }
} 