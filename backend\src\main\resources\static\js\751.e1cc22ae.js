"use strict";(self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[]).push([[751],{1573:(e,t,a)=>{a.d(t,{VG:()=>i});a(88844),a(28706),a(15086),a(26099),a(27495),a(90906),a(99449),a(11392);var n=a(81052);a(68039),a(55593),a(51629),a(74423),a(59089),a(18111),a(7588),a(58940),a(21699),a(25440),a(23500);function o(e){var t="_t=".concat(Date.now());return e.includes("?")?"".concat(e,"&").concat(t):"".concat(e,"?").concat(t)}function i(e){if(!e)return"";if(console.log("[ImageHelper] 处理图像路径:",e),/^[a-zA-Z]:\\/.test(e)){console.log("[ImageHelper] 检测到文件系统路径");var t=encodeURIComponent(e),a=n.M8?"".concat(n.JR):"";return o("".concat(a,"/medical/image/system-path?path=").concat(t))}if(e.startsWith("http://")||e.startsWith("https://"))return o(e);if(e.startsWith("data:"))return e;var i=e;return e.startsWith("/medical")?(console.log("[ImageHelper] 检测到相对路径，添加后端服务器地址"),i=n.M8?"".concat(n.JR).concat(e):e):e.startsWith("/")&&(console.log("[ImageHelper] 检测到其他相对路径，添加后端服务器地址和上下文路径"),i=n.M8?"".concat(n.JR).concat(n.T_).concat(e):"".concat(n.T_).concat(e)),console.log("[ImageHelper] 处理后的URL:",i),o(i)}},88751:(e,t,a)=>{a.r(t),a.d(t,{default:()=>N});var n=a(61431),o={class:"annotation-container"},i={class:"page-header"},r={class:"main-content"},s={class:"toolbar"},c={class:"tool-section"},l={class:"tool-section"},d={class:"annotations-list"},g={key:0,class:"empty-annotations"},h={class:"coordinates"},u={class:"dimensions"},m={class:"annotation-area"},f={class:"image-wrapper"},I={ref:"imageContainerInner",class:"image-container"},p=["src"],v=["onMousedown"],w=["onMousedown"],x=["onMousedown"],A=["onMousedown"],b=["onMousedown"],y={class:"form-section"};function k(e,t,a,k,D,S){var P=(0,n.g2)("Refresh"),$=(0,n.g2)("el-icon"),F=(0,n.g2)("el-button"),M=(0,n.g2)("el-option"),H=(0,n.g2)("el-select"),T=(0,n.g2)("el-radio"),_=(0,n.g2)("el-radio-group"),W=(0,n.g2)("el-table-column"),z=(0,n.g2)("el-table"),C=(0,n.g2)("router-view");return(0,n.uX)(),(0,n.CE)("div",o,[(0,n.Lk)("div",i,[t[8]||(t[8]=(0,n.Lk)("h2",null,"图像标注",-1)),(0,n.bF)(F,{type:"primary",size:"small",onClick:S.refreshData},{default:(0,n.k6)((function(){return[(0,n.bF)($,null,{default:(0,n.k6)((function(){return[(0,n.bF)(P)]})),_:1}),t[7]||(t[7]=(0,n.eW)(" 刷新数据 "))]})),_:1,__:[7]},8,["onClick"])]),(0,n.Lk)("div",r,[(0,n.Lk)("div",s,[(0,n.Lk)("div",c,[t[9]||(t[9]=(0,n.Lk)("h3",null,"标签分类",-1)),(0,n.bF)(H,{modelValue:D.selectedTag,"onUpdate:modelValue":t[0]||(t[0]=function(e){return D.selectedTag=e}),placeholder:"请选择标签分类",style:{width:"100%"}},{default:(0,n.k6)((function(){return[(0,n.bF)(M,{label:"IH-婴幼儿血管瘤",value:"IH-婴幼儿血管瘤"}),(0,n.bF)(M,{label:"RICH-先天性快速消退型血管瘤",value:"RICH-先天性快速消退型血管瘤"}),(0,n.bF)(M,{label:"PICH-先天性部分消退型血管瘤",value:"PICH-先天性部分消退型血管瘤"}),(0,n.bF)(M,{label:"NICH-先天性不消退型血管瘤",value:"NICH-先天性不消退型血管瘤"}),(0,n.bF)(M,{label:"KHE-卡泊西型血管内皮细胞瘤",value:"KHE-卡泊西型血管内皮细胞瘤"}),(0,n.bF)(M,{label:"KH-角化型血管瘤",value:"KH-角化型血管瘤"}),(0,n.bF)(M,{label:"PG-肉芽肿性血管瘤",value:"PG-肉芽肿性血管瘤"}),(0,n.bF)(M,{label:"MVM-微静脉畸形",value:"MVM-微静脉畸形"}),(0,n.bF)(M,{label:"VM-静脉畸形",value:"VM-静脉畸形"}),(0,n.bF)(M,{label:"AVM-动静脉畸形",value:"AVM-动静脉畸形"})]})),_:1},8,["modelValue"])]),(0,n.Lk)("div",l,[t[11]||(t[11]=(0,n.Lk)("h3",null,"标注工具",-1)),(0,n.bF)(_,{modelValue:D.currentTool,"onUpdate:modelValue":t[1]||(t[1]=function(e){return D.currentTool=e}),style:{display:"block","margin-top":"10px"}},{default:(0,n.k6)((function(){return[(0,n.bF)(T,{label:"rectangle"},{default:(0,n.k6)((function(){return t[10]||(t[10]=[(0,n.eW)("矩形框")])})),_:1,__:[10]})]})),_:1},8,["modelValue"])]),(0,n.Lk)("div",d,[(0,n.Lk)("h3",null,[t[12]||(t[12]=(0,n.eW)("已添加标注 ")),(0,n.bF)(F,{type:"text",size:"small",onClick:S.reloadAnnotations,title:"刷新标注"},{default:(0,n.k6)((function(){return[(0,n.bF)($,null,{default:(0,n.k6)((function(){return[(0,n.bF)(P)]})),_:1})]})),_:1},8,["onClick"])]),0===S.filteredAnnotations.length?((0,n.uX)(),(0,n.CE)("div",g,t[13]||(t[13]=[(0,n.Lk)("p",null,"暂无标注，请在图片上绘制矩形框添加标注",-1)]))):((0,n.uX)(),(0,n.Wv)(z,{key:1,data:S.filteredAnnotations,style:{width:"100%"}},{default:(0,n.k6)((function(){return[(0,n.bF)(W,{label:"编号",width:"60"},{default:(0,n.k6)((function(e){return[(0,n.eW)((0,n.v_)(e.$index+1),1)]})),_:1}),(0,n.bF)(W,{prop:"tag",label:"标签",width:"90"}),(0,n.bF)(W,{label:"坐标",width:"120"},{default:(0,n.k6)((function(e){return[(0,n.Lk)("div",h,[(0,n.Lk)("small",null,"X: "+(0,n.v_)(Math.round(e.row.x)),1),t[14]||(t[14]=(0,n.Lk)("br",null,null,-1)),(0,n.Lk)("small",null,"Y: "+(0,n.v_)(Math.round(e.row.y)),1)])]})),_:1}),(0,n.bF)(W,{label:"尺寸",width:"120"},{default:(0,n.k6)((function(e){return[(0,n.Lk)("div",u,[(0,n.Lk)("small",null,"W: "+(0,n.v_)(Math.round(e.row.width)),1),t[15]||(t[15]=(0,n.Lk)("br",null,null,-1)),(0,n.Lk)("small",null,"H: "+(0,n.v_)(Math.round(e.row.height)),1)])]})),_:1}),(0,n.bF)(W,{label:"操作",width:"80"},{default:(0,n.k6)((function(e){return[(0,n.bF)(F,{type:"danger",size:"small",onClick:function(t){return S.deleteAnnotation(e.row.id)}},{default:(0,n.k6)((function(){return t[16]||(t[16]=[(0,n.eW)("删除")])})),_:2,__:[16]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"]))])]),(0,n.Lk)("div",m,[(0,n.Lk)("div",f,[(0,n.Lk)("div",I,[D.currentImage?((0,n.uX)(),(0,n.CE)("img",{key:0,ref:"annotationImage",src:S.getImageUrl(D.currentImage.url),class:"annotation-image",alt:"医学影像",onLoad:t[2]||(t[2]=function(){return S.handleImageLoad&&S.handleImageLoad.apply(S,arguments)})},null,40,p)):(0,n.Q3)("",!0),D.currentImage?((0,n.uX)(),(0,n.CE)("div",{key:1,class:"annotation-overlay",style:(0,n.Tr)({width:D.imageWidth+"px",height:D.imageHeight+"px"}),onMousedown:t[3]||(t[3]=function(){return S.startDrawing&&S.startDrawing.apply(S,arguments)}),onMousemove:t[4]||(t[4]=function(){return S.onDrawing&&S.onDrawing.apply(S,arguments)}),onMouseup:t[5]||(t[5]=function(){return S.endDrawing&&S.endDrawing.apply(S,arguments)}),onMouseleave:t[6]||(t[6]=function(){return S.cancelDrawing&&S.cancelDrawing.apply(S,arguments)})},null,36)):(0,n.Q3)("",!0),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(S.filteredAnnotations,(function(t,a){return(0,n.uX)(),(0,n.CE)("div",{key:a,class:"annotation-box",style:(0,n.Tr)({left:t.x+"px",top:t.y+"px",width:t.width+"px",height:t.height+"px",borderColor:S.getTagColor(t.tag),cursor:e.isEditingBox&&e.editingBoxId===t.id?"move":"default"}),onMousedown:(0,n.D$)((function(e){return S.startEditBox(e,t.id)}),["stop"])},[(0,n.Lk)("span",{class:"annotation-label",style:(0,n.Tr)({backgroundColor:S.getTagColor(t.tag)})},(0,n.v_)(t.tag),5),(0,n.Lk)("div",{class:"resize-handle top-left",onMousedown:(0,n.D$)((function(e){return S.startResizeBox(e,t.id,"top-left")}),["stop"])},null,40,w),(0,n.Lk)("div",{class:"resize-handle top-right",onMousedown:(0,n.D$)((function(e){return S.startResizeBox(e,t.id,"top-right")}),["stop"])},null,40,x),(0,n.Lk)("div",{class:"resize-handle bottom-left",onMousedown:(0,n.D$)((function(e){return S.startResizeBox(e,t.id,"bottom-left")}),["stop"])},null,40,A),(0,n.Lk)("div",{class:"resize-handle bottom-right",onMousedown:(0,n.D$)((function(e){return S.startResizeBox(e,t.id,"bottom-right")}),["stop"])},null,40,b)],44,v)})),128)),D.isDrawing?((0,n.uX)(),(0,n.CE)("div",{key:2,class:"drawing-box",style:(0,n.Tr)({left:Math.min(D.drawStart.x,D.drawCurrent.x)+"px",top:Math.min(D.drawStart.y,D.drawCurrent.y)+"px",width:Math.abs(D.drawCurrent.x-D.drawStart.x)+"px",height:Math.abs(D.drawCurrent.y-D.drawStart.y)+"px"})},null,4)):(0,n.Q3)("",!0)],512)])])]),(0,n.Lk)("div",y,[(0,n.bF)(C)])])}var D,S=a(50139),P=a(88844),$=a(24059),F=a(698),M=a(55593),H=(a(16280),a(76918),a(28706),a(2008),a(50113),a(48980),a(51629),a(74423),a(64346),a(8921),a(62062),a(44114),a(54554),a(59089),a(1688),a(60739),a(23288),a(18111),a(22489),a(20116),a(7588),a(61701),a(33110),a(36033),a(2892),a(9868),a(79432),a(26099),a(78459),a(58940),a(27495),a(38781),a(21699),a(47764),a(25440),a(11392),a(23500),a(62953),a(76031),a(36149)),T=(a(1573),a(48548)),_=a(72505),W=a.n(_),z={STORAGE_KEYS:{userInfo:"user",token:"token",authValid:"authValid",isNavigatingAfterSave:"isNavigatingAfterSave",pendingDiagnosis:"pendingDiagnosisId",formDataPrefix:"formData_backup_"},ROUTES:{structuredForm:"/app/cases/structured-form"},UI:{navigationDelay:100}};const C={name:"CaseDetailForm",components:{Refresh:T.Refresh},data:function(){return{imageId:this.$route.params.id||this.$route.query.imageId||null,currentImage:null,selectedTag:"IH-婴幼儿血管瘤",currentTool:"rectangle",formData:{diseasePart:"",diseaseType:"",patientAge:"",patientGender:"",diagnosis:"",treatmentPlan:"",notes:""},dbAnnotations:[],uploadedImages:[],currentImageIndex:0,annotations:[],imageWidth:0,imageHeight:0,drawingBox:null,isDrawing:!1,selectedAnnotation:null,isMoving:!1,isResizing:!1,resizeHandle:null,moveStartX:0,moveStartY:0,drawStart:{x:0,y:0},drawCurrent:{x:0,y:0},showAnnotationForm:!1,currentAnnotation:null,zoomLevel:1,panOffset:{x:0,y:0},isPanning:!1,lastPanPoint:{x:0,y:0},annotationsLoaded:!1,shouldSaveOriginalImage:!1,imageLoaded:!1,isSavingAnnotations:!1,originalWidth:0,originalHeight:0,scaleX:1,scaleY:1,isSaving:!1,processedAnnotations:!1,saveImageTimer:null}},computed:{filteredAnnotations:function(){var e=this;return this.annotations.filter((function(t){return t.imageIndex===e.currentImageIndex}))}},watch:{dbAnnotations:{handler:function(e){this.imageLoaded&&e&&e.length>0&&!this.processedAnnotations&&(console.log("监听到标注数据变化且图像已加载，开始处理标注"),this.processLoadedAnnotations())},deep:!0,immediate:!0},imageLoaded:{handler:function(e){e&&this.dbAnnotations&&this.dbAnnotations.length>0&&!this.processedAnnotations&&(console.log("监听到图像加载完成且标注数据已存在，开始处理标注"),this.processLoadedAnnotations())},immediate:!0}},created:function(){window.addEventListener("resize",this.handleResize)},mounted:function(){var e=this;console.log("CaseDetailForm mounted - 查询参数:",this.$route.query,"路由参数:",this.$route.params);var t=this.$route.params.id||this.$route.query.imageId||this.$route.query.diagnosisId||"";if(console.log("原始图像ID:",t,"类型:",(0,M.A)(t)),t=String(t),t&&"undefined"!==t&&"null"!==t){this.imageId=t,console.log("处理后的图像ID:",this.imageId,"类型:",(0,M.A)(this.imageId));var a=this.$loading({lock:!0,text:"正在加载图像和标注数据...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});sessionStorage.setItem("isAppOperation","true");var n=0,o=3,i=function(){console.log("尝试加载页面数据，第 ".concat(n+1," 次尝试")),e.loadPageData(t).then((function(){return console.log("页面数据加载成功"),e.loadAnnotations(t)})).then((function(){console.log("标注数据也加载成功"),a.close()}))["catch"]((function(t){console.error("第 ".concat(n+1," 次加载数据失败:"),t),n<o?(n++,console.log("".concat(2*n,"秒后重试...")),setTimeout(i,2e3*n)):(console.error("达到最大重试次数，放弃加载"),e.$message.error("无法加载页面数据，请返回重试"),a.close())}))};i(),document.addEventListener("mousemove",this.handleGlobalMouseMove),document.addEventListener("mouseup",this.handleGlobalMouseUp),window.addEventListener("resize",this.handleResize)}else this.$message.warning("未提供有效的图像ID，请选择一个图像进行标注")},beforeUnmount:function(){window.removeEventListener("resize",this.handleResize),sessionStorage.removeItem("isNavigatingAfterSave"),sessionStorage.removeItem("returningToWorkbench")},methods:(D={processLongId:function(e){if(!e)return null;var t=e.toString();return t.length>9?(console.log("处理长ID: ".concat(t," -> ").concat(t.substring(t.length-9))),t.substring(t.length-9)):t},getImageUrl:function(e){if(!e)return"";if(e.startsWith("http"))return e;var t=e,a=e;if(e.startsWith("/")){var n=window.location.origin;a=n+e}var o="t=".concat(Date.now(),"&r=").concat(Math.random());return a=a.includes("?")?"".concat(a,"&").concat(o):"".concat(a,"?").concat(o),console.log("处理图像URL: ".concat(t," -> ").concat(a)),a},loadImageData:function(){var e=this;return(0,F.A)((0,$.A)().m((function t(){var a,n,o,i;return(0,$.A)().w((function(t){while(1)switch(t.n){case 0:if(e.imageId){t.n=1;break}return console.error("No image ID provided"),t.a(2);case 1:return t.p=1,console.log("Loading image data for ID: ".concat(e.imageId)),a=Date.now(),n=Math.random(),t.n=2,H["default"].get("/images/".concat(e.imageId,"?t=").concat(a,"&r=").concat(n),{headers:{"Cache-Control":"no-cache, no-store",Pragma:"no-cache",Expires:"0"}});case 2:o=t.v,o.data?(e.currentImage=o.data,console.log("Image data loaded:",e.currentImage),e.loadAnnotations()):console.error("Failed to load image data: Response data is empty"),t.n=4;break;case 3:t.p=3,i=t.v,console.error("Failed to load image data:",i);case 4:return t.a(2)}}),t,null,[[1,3]])})))()},loadAnnotations:function(e){var t=this;return(0,F.A)((0,$.A)().m((function a(){var n,o,i,r;return(0,$.A)().w((function(a){while(1)switch(a.n){case 0:if(e){a.n=1;break}return console.error("无法加载标注：缺少图像ID"),a.a(2,Promise.reject("缺少图像ID"));case 1:return n=e||t.imageId,console.log("加载图像ID为",n,"的标注数据"),o=3,i=0,r=function(){var e=(0,F.A)((0,$.A)().m((function e(){var a,s,c,l,d,g,h,u,m,f,I,p,v,w,x,A,b,y,k;return(0,$.A)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,a=(new Date).getTime(),s=Math.random(),c=JSON.parse(localStorage.getItem("user")||"{}"),l=c.customId||c.id||"",d=t.$route.query.diagnosisId===n,g="/medical/api/tags/image/".concat(n,"?t=").concat(a,"&r=").concat(s,"&mode=view&_role=REVIEWER&userId=").concat(l),d&&(g="/medical/api/hemangioma-diagnoses/".concat(n,"/tags?t=").concat(a,"&r=").concat(s)),console.log("尝试加载标注数据 (第".concat(i+1,"次尝试)，URL: ").concat(g)),e.n=1,fetch(g);case 1:if(h=e.v,h.ok){e.n=2;break}throw new Error("获取标注失败: ".concat(h.status));case 2:return e.n=3,h.json();case 3:return u=e.v,console.log("获取到 ".concat(u.length," 个标注数据:"),u),t.dbAnnotations=u||[],t.imageWidth>0&&t.imageHeight>0?t.processLoadedAnnotations():t.annotationsLoaded=!0,e.a(2,u);case 4:if(e.p=4,y=e.v,console.error("加载标注数据失败 (第".concat(i+1,"次尝试):"),y),!(i<o)){e.n=5;break}return i++,m=1e3*i,console.log("等待 ".concat(m,"ms 后重试...")),e.a(2,new Promise((function(e,t){setTimeout((0,F.A)((0,$.A)().m((function a(){var n,o;return(0,$.A)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,r();case 1:n=a.v,e(n),a.n=3;break;case 2:a.p=2,o=a.v,t(o);case 3:return a.a(2)}}),a,null,[[0,2]])}))),m)})));case 5:return e.p=5,console.log("尝试使用备用API路径..."),f=(new Date).getTime(),I=Math.random(),p=JSON.parse(localStorage.getItem("user")||"{}"),v=p.customId||p.id||"",w=t.$route.query.diagnosisId===n,x="/api/tags/image/".concat(n,"?t=").concat(f,"&r=").concat(I,"&userId=").concat(v),w&&(x="/api/hemangioma-diagnoses/".concat(n,"/tags?t=").concat(f,"&r=").concat(I)),console.log("尝试备用API路径: ".concat(x)),e.n=6,fetch(x);case 6:if(A=e.v,A.ok){e.n=7;break}throw new Error("备用API也失败: ".concat(A.status));case 7:return e.n=8,A.json();case 8:return b=e.v,console.log("通过备用API获取到 ".concat(b.length," 个标注数据:"),b),t.dbAnnotations=b||[],t.imageWidth>0&&t.imageHeight>0?t.processLoadedAnnotations():t.annotationsLoaded=!0,e.a(2,b);case 9:return e.p=9,k=e.v,console.error("所有API尝试都失败:",k),t.$message.error("加载标注数据失败，您可以创建新的标注"),e.a(2,Promise.reject(k))}}),e,null,[[5,9],[0,4]])})));return function(){return e.apply(this,arguments)}}(),a.a(2,r())}}),a)})))()},convertHemangiomaTags:function(e){var t=this;if(console.log("转换血管瘤标签数据:",e),e&&e.length&&this.imageWidth&&this.imageHeight){var a=e.map((function(e,a){var n=e.x<=1&&e.x>=0&&e.y<=1&&e.y>=0&&e.width<=1&&e.width>=0&&e.height<=1&&e.height>=0,o=n?e.x*t.imageWidth:e.x,i=n?e.y*t.imageHeight:e.y,r=n?e.width*t.imageWidth:e.width,s=n?e.height*t.imageHeight:e.height;return{id:"".concat(a+1),serverId:e.id||null,tag:e.tag||e.tagName||"IH-婴幼儿血管瘤",x:o,y:i,width:r,height:s,confidence:e.confidence||null,type:"rectangle"}}));console.log("转换后的标注数据:",a),this.annotations=a}else console.warn("无法转换标签数据: 缺少必要信息",{tagsLength:e?e.length:0,imageWidth:this.imageWidth,imageHeight:this.imageHeight})},convertTags:function(e){var t=this;if(console.log("转换普通标签数据:",e),e&&e.length&&this.imageWidth&&this.imageHeight){var a=e.map((function(e,a){var n=e.x<=1&&e.x>=0&&e.y<=1&&e.y>=0&&e.width<=1&&e.width>=0&&e.height<=1&&e.height>=0,o=n?e.x*t.imageWidth:e.x,i=n?e.y*t.imageHeight:e.y,r=n?e.width*t.imageWidth:e.width,s=n?e.height*t.imageHeight:e.height;return{id:e.id||"".concat(a+1),tag:e.tag||e.tagName||"IH-婴幼儿血管瘤",x:o,y:i,width:r,height:s,confidence:e.confidence||null,type:"rectangle"}}));console.log("转换后的标注数据:",a),this.annotations=a}else console.warn("无法转换标签数据: 缺少必要信息")},refreshData:function(){var e=this;return(0,F.A)((0,$.A)().m((function t(){return(0,$.A)().w((function(t){while(1)switch(t.n){case 0:return e.dbAnnotations=[],e.annotations=[],e.annotationsLoaded=!1,t.n=1,e.loadImageData();case 1:e.$message({message:"数据已刷新",type:"success",duration:2e3});case 2:return t.a(2)}}),t)})))()},loadImageFromPairs:function(e){var t=this;console.log("🔍 开始从image_pairs表加载图像，ID:",e);var a=JSON.parse(localStorage.getItem("user"))||{},n=a.customId||a.id;n||console.warn("未找到用户ID，可能导致权限问题");var o={userId:n};o.t=(new Date).getTime(),H["default"].imagePairs.getByMetadataId(e).then((function(a){console.log("image_pairs查询响应:",a);var n=a.data;if(n&&n.length>0){var o=n[0];console.log("image_pairs第一条记录:",o),t.imagePairId=o.id;var i=o.imageOnePath||o.image_one_path;if(console.log("获取到图像路径:",i),!i&&o.image_one_url?(console.log("尝试使用image_one_url:",o.image_one_url),t.processedFilePath=o.image_one_url):!i&&o.url?(console.log("尝试使用url字段:",o.url),t.processedFilePath=o.url):t.processedFilePath=i,!t.processedFilePath)return console.log("image_pairs中未找到图像路径，尝试从image_metadata表获取"),void t.tryLoadImagePathFromMetadata(e,(function(a){a&&(console.log("从image_metadata表获取到图像路径:",a),t.processedFilePath=a,t.updateImagePairPath(e,a),t.uploadedImages=[{id:e,url:a,filename:"图像 #".concat(e)}],t.currentImage=t.uploadedImages[0],t.imageLoaded=!0)}));t.uploadedImages=[{id:e,url:t.processedFilePath,filename:"图像 #".concat(e)}],t.currentImage=t.uploadedImages[0],t.currentImageIndex=0,t.currentImageId=e,t.imageLoaded=!0,t.loadAnnotations(e)}else console.log("image_pairs未找到数据，尝试从image_metadata表直接获取路径"),t.tryLoadImagePathFromMetadata(e,(function(a){a?(console.log("从image_metadata表获取到图像路径:",a),t.processedFilePath=a,t.createImageRecord(a,e)):t.$message.error("未能找到图像路径，无法加载图像")}))}))["catch"]((function(a){console.error("加载图像对失败:",a),t.tryLoadImagePathFromMetadata(e)}))},tryLoadFromMetadata:function(e){var t=this;console.log("从image_metadata表尝试加载图像ID:",e),H["default"].images.getOne(e).then((function(e){console.log("从image_metadata表获取的完整响应:",e);var a=e.data;if(console.log("image_metadata表获取的图像数据:",a),!a||!a.path)return console.error("image_metadata表中图像路径不存在，无法加载图像"),void t.$message.error("图像路径不存在，请检查数据库");t.uploadedImages=[{id:a.id,url:a.path,filename:a.original_name||"图像 #".concat(a.id)}],t.currentImage=t.uploadedImages[0],t.currentImageIndex=0,t.processedFilePath=a.path,t.createImagePairDirectly(a.id,a.path),t.loadAnnotationsFromDatabase(),t.imageLoaded=!0,console.log("从image_metadata加载成功并创建image_pairs记录")}))["catch"]((function(e){console.error("从image_metadata加载图像失败:",e),t.loadingError=!0,t.loadingErrorMessage="无法加载图像，请检查数据库记录",t.processedFilePath&&t.currentImageId?(console.log("尝试使用现有路径创建图像记录"),t.createImageRecord(t.processedFilePath,t.currentImageId)):t.$message.error("无法找到图像数据，请返回上一步重新上传")}))},createImagePairDirectly:function(e,t){var a=this;console.log("直接创建ImagePair记录:",{metadataId:e,path:t});var n={metadataId:e.toString(),imageOnePath:t,description:"图像".concat(e)};fetch("/api/image-pairs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n),credentials:"include"}).then((function(e){if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return e.json()})).then((function(e){console.log("成功创建ImagePair记录:",e),e&&e.id&&(a.imagePairId=e.id),a.$message.success("图像关联信息创建成功")}))["catch"]((function(e){console.error("创建ImagePair记录失败:",e),a.$message.warning("图像关联创建失败，但您仍可继续使用")}))},loadImageById:function(e){this.loadImageFromPairs(e)},createImageRecord:function(e,t){var a=this;console.log("正在创建新的图像记录:",{path:e,id:t});var n=JSON.parse(localStorage.getItem("user"))||{id:1},o=e.substring(e.lastIndexOf("/")+1),i={filename:o,original_name:o,path:e,mimetype:"image/jpeg",size:0,width:800,height:600,status:"DRAFT",uploaded_by:n.id};H["default"].images.update(t,i).then((function(e){console.log("成功创建/更新图像元数据:",e.data),a.saveOriginalImage(),a.loadImageById(t)}))["catch"]((function(e){console.error("创建图像元数据失败:",e),a.$message.error("无法创建图像记录，请检查数据库连接")}))},loadImageByPath:function(e){this.uploadedImages=[{id:this.currentImageId||Date.now().toString(),url:e,filename:e.substring(e.lastIndexOf("/")+1)}],this.currentImage=this.uploadedImages[0],this.currentImageIndex=0,this.currentImageId||(this.currentImageId=this.uploadedImages[0].id,console.log("生成临时图像ID:",this.currentImageId)),this.checkExistingImagePair(),this.imageLoaded=!0},checkExistingImagePair:function(){var e=this;this.currentImageId&&this.processedFilePath?(console.log("检查图像ID对应的ImagePair记录:",this.currentImageId),H["default"].imagePairs.getByMetadataId(this.currentImageId).then((function(t){if(t.data&&t.data.length>0){var a=t.data[0];e.imagePairId=a.id,console.log("找到现有ImagePair记录:",a),a.imageOnePath!==e.processedFilePath&&(console.log("更新ImagePair中的路径信息"),e.saveOriginalImage())}else console.log("未找到ImagePair记录，创建新记录"),e.saveOriginalImage()}))["catch"]((function(t){console.error("检查ImagePair记录失败:",t),console.log("尝试创建新的ImagePair记录"),e.saveOriginalImage()}))):console.log("缺少图像ID或物理路径，不检查ImagePair记录")},saveOriginalImage:function(){var e=this;if(this.currentImageId){var t="";if(this.currentImage&&this.currentImage.url)t=this.currentImage.url,console.log("使用从数据库加载的图像路径:",t);else{if(!this.processedFilePath)return void console.error("无法找到有效的图像路径，不保存到ImagePair表");t=this.processedFilePath,console.log("使用本地保存的处理后图像路径:",t)}this.ensureImageMetadataExists(this.currentImageId,t,(function(){JSON.parse(localStorage.getItem("user"));try{var a={metadataId:e.currentImageId.toString(),imageOnePath:t,description:"图像".concat(e.currentImageId)};console.log("保存到image_pairs的数据:",a);var n=function(){var t=(0,F.A)((0,$.A)().m((function t(){var n,o,i,r,s,c,l;return(0,$.A)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,console.log("直接查询ImagePair是否存在:",e.currentImageId),t.n=1,fetch("/api/image-pairs/metadata/".concat(e.currentImageId),{method:"GET",headers:{Accept:"application/json","Cache-Control":"no-cache",Pragma:"no-cache"},credentials:"include"});case 1:return n=t.v,t.n=2,n.json();case 2:return o=t.v,i=null,r=!1,o&&o.length>0&&o[0].id&&(r=!0,i=o[0].id,console.log("找到现有ImagePair:",i)),r&&i&&(a.id=i),t.n=3,fetch("/api/image-pairs",{method:"POST",headers:{"Content-Type":"application/json","Cache-Control":"no-cache",Pragma:"no-cache"},body:JSON.stringify(a),credentials:"include"});case 3:if(s=t.v,s.ok){t.n=4;break}throw new Error("保存失败: ".concat(s.status));case 4:return t.n=5,s.json();case 5:c=t.v,console.log("保存ImagePair成功:",c),c&&c.id&&(e.imagePairId=c.id),e.$message.success("图像关联信息保存成功"),t.n=7;break;case 6:t.p=6,l=t.v,console.error("保存ImagePair失败:",l),e.$message.error("保存失败: "+l.message);case 7:return t.a(2)}}),t,null,[[0,6]])})));return function(){return t.apply(this,arguments)}}();n()}catch(o){console.error("准备ImagePair数据时出错:",o),e.$message.error("保存图像对失败: "+o.message)}}))}else console.log("缺少图像ID，不保存到ImagePair表")},ensureImageMetadataExists:function(e,t,a){var n=this;H["default"].images.getOne(e).then((function(){a()}))["catch"]((function(){console.log("图像元数据不存在，创建新记录"),n.createImageRecord(t,e)}))},handleResize:function(){var e=this;this.$nextTick((function(){var t=e.$refs.annotationImage;if(t){var a=t.getBoundingClientRect(),n=a.width,o=a.height;n===e.imageWidth&&o===e.imageHeight||(console.log("窗口大小改变，图像尺寸从 ".concat(e.imageWidth,"x").concat(e.imageHeight," 变为 ").concat(n,"x").concat(o)),e.imageWidth=n,e.imageHeight=o,e.scaleX=e.imageWidth/e.originalWidth,e.scaleY=e.imageHeight/e.originalHeight,e.recalculateAnnotationPositions())}}))},initTools:function(){console.log("初始化标注工具")},handleImageLoad:function(e){console.log("图像加载完成");var t=e.target,a=t.getBoundingClientRect();this.imageWidth=a.width,this.imageHeight=a.height,this.originalWidth=t.naturalWidth,this.originalHeight=t.naturalHeight,console.log("图像尺寸:",{width:this.imageWidth,height:this.imageHeight,originalWidth:this.originalWidth,originalHeight:this.originalHeight}),this.imageLoaded=!0,this.annotationsLoaded&&this.dbAnnotations.length>0&&(console.log("图像加载完成，处理待转换的标注数据"),this.processLoadedAnnotations()),this.$emit("image-loaded",{width:this.imageWidth,height:this.imageHeight})},recalculateAnnotationPositions:function(){var e=this;this.annotations&&this.annotations.length&&(console.log("重新计算标注位置，图像尺寸:",this.imageWidth,"x",this.imageHeight),this.imageWidth<=0||this.imageHeight<=0?console.error("图像尺寸无效，无法重新计算标注位置"):(this.annotations=this.annotations.map((function(t){if(t.isYoloFormat){var a=t.normalizedX,n=t.normalizedY,o=t.normalizedWidth,i=t.normalizedHeight,r=Math.round((a-o/2)*e.imageWidth),s=Math.round((n-i/2)*e.imageHeight),c=Math.round(o*e.imageWidth),l=Math.round(i*e.imageHeight);return console.log("重新计算YOLO标注 ".concat(t.id,": 中心点(").concat(a.toFixed(4),", ").concat(n.toFixed(4),") -> 左上角(").concat(r,", ").concat(s,"), 尺寸: ").concat(c," x ").concat(l)),(0,P.A)((0,P.A)({},t),{},{x:r,y:s,width:c,height:l})}if(void 0!==t.normalizedX){var d=Math.round(t.normalizedX*e.imageWidth),g=Math.round(t.normalizedY*e.imageHeight),h=Math.round(t.normalizedWidth*e.imageWidth),u=Math.round(t.normalizedHeight*e.imageHeight);return console.log("重新计算普通标注 ".concat(t.id,": 归一化(").concat(t.normalizedX.toFixed(4),", ").concat(t.normalizedY.toFixed(4),") -> 像素(").concat(d,", ").concat(g,"), 尺寸: ").concat(h," x ").concat(u)),(0,P.A)((0,P.A)({},t),{},{x:d,y:g,width:h,height:u})}if(t.x<=1&&t.y<=1&&t.width<=1&&t.height<=1){var m=Math.round(t.x*e.imageWidth),f=Math.round(t.y*e.imageHeight),I=Math.round(t.width*e.imageWidth),p=Math.round(t.height*e.imageHeight);return console.log("重新计算旧式归一化标注 ".concat(t.id,": (").concat(t.x.toFixed(4),", ").concat(t.y.toFixed(4),") -> (").concat(m,", ").concat(f,"), 尺寸: ").concat(I," x ").concat(p)),(0,P.A)((0,P.A)({},t),{},{x:m,y:f,width:I,height:p,normalizedX:t.x,normalizedY:t.y,normalizedWidth:t.width,normalizedHeight:t.height})}var v=e.imageWidth/e.originalWidth||1,w=e.imageHeight/e.originalHeight||1;if(Math.abs(v-1)<.01&&Math.abs(w-1)<.01)return t;var x=Math.round(t.x*v),A=Math.round(t.y*w),b=Math.round(t.width*v),y=Math.round(t.height*w);return console.log("按比例缩放标注 ".concat(t.id,": 比例(").concat(v.toFixed(2),", ").concat(w.toFixed(2),"), (").concat(t.x,", ").concat(t.y,") -> (").concat(x,", ").concat(A,"), 尺寸: ").concat(b," x ").concat(y)),(0,P.A)((0,P.A)({},t),{},{x,y:A,width:b,height:y})})),console.log("标注位置重新计算完成，共",this.annotations.length,"个标注")))},selectTool:function(e){this.currentTool=e},startDrawing:function(e){if("rectangle"===this.currentTool){this.isDrawing=!0;var t=this.$refs.imageContainerInner.getBoundingClientRect();this.drawStart.x=e.clientX-t.left,this.drawStart.y=e.clientY-t.top,this.drawCurrent.x=this.drawStart.x,this.drawCurrent.y=this.drawStart.y}},onDrawing:function(e){if(this.isDrawing){var t=this.$refs.imageContainerInner.getBoundingClientRect();this.drawCurrent.x=e.clientX-t.left,this.drawCurrent.y=e.clientY-t.top}},endDrawing:function(){if(this.isDrawing&&(this.isDrawing=!1,Math.abs(this.drawCurrent.x-this.drawStart.x)>10&&Math.abs(this.drawCurrent.y-this.drawStart.y)>10)){var e=Math.min(this.drawStart.x,this.drawCurrent.x),t=Math.min(this.drawStart.y,this.drawCurrent.y),a=Math.abs(this.drawCurrent.x-this.drawStart.x),n=Math.abs(this.drawCurrent.y-this.drawStart.y),o=e/this.imageWidth,i=t/this.imageHeight,r=a/this.imageWidth,s=n/this.imageHeight,c={id:Date.now(),imageIndex:this.currentImageIndex,tag:this.selectedTag,type:"rectangle",x:e,y:t,width:a,height:n,normalizedX:o,normalizedY:i,normalizedWidth:r,normalizedHeight:s};this.annotations.push(c),this.saveAnnotationToDatabase(c)}},cancelDrawing:function(){this.isDrawing&&(this.isDrawing=!1)},startEditBox:function(e,t){e.preventDefault();var a=this.annotations.find((function(e){return e.id===t}));if(a){this.originalBox=(0,P.A)({},a);var n=this.$refs.imageContainerInner.getBoundingClientRect();this.editStartPos={x:e.clientX-n.left,y:e.clientY-n.top},this.isEditingBox=!0,this.editingBoxId=t,this.isDrawing=!1}},startResizeBox:function(e,t,a){e.preventDefault();var n=this.annotations.find((function(e){return e.id===t}));if(n){this.originalBox=(0,P.A)({},n);var o=this.$refs.imageContainerInner.getBoundingClientRect();this.editStartPos={x:e.clientX-o.left,y:e.clientY-o.top},this.isResizingBox=!0,this.editingBoxId=t,this.resizeHandle=a,this.isDrawing=!1}},handleGlobalMouseMove:function(e){var t=this;if(this.isDrawing){var a=this.$refs.imageContainerInner.getBoundingClientRect(),n=e.clientX-a.left,o=e.clientY-a.top,i=Math.max(0,Math.min(this.imageWidth,n)),r=Math.max(0,Math.min(this.imageHeight,o));this.drawCurrent={x:i,y:r}}else{if(this.isEditingBox){var s=this.$refs.imageContainerInner.getBoundingClientRect(),c=e.clientX-s.left,l=e.clientY-s.top,d=c-this.editStartPos.x,g=l-this.editStartPos.y,h=this.annotations.findIndex((function(e){return e.id===t.editingBoxId}));if(-1===h)return;var u=this.originalBox.x+d,m=this.originalBox.y+g;return u=Math.max(0,Math.min(u,this.imageWidth-this.originalBox.width)),m=Math.max(0,Math.min(m,this.imageHeight-this.originalBox.height)),this.annotations[h].x=u,void(this.annotations[h].y=m)}if(this.isResizingBox){var f=this.$refs.imageContainerInner.getBoundingClientRect(),I=e.clientX-f.left,p=e.clientY-f.top,v=Math.max(0,Math.min(this.imageWidth,I)),w=Math.max(0,Math.min(this.imageHeight,p)),x=this.annotations.findIndex((function(e){return e.id===t.editingBoxId}));if(-1===x)return;var A=this.annotations[x],b=this.originalBox;switch(this.resizeHandle){case"top-left":A.width=b.x+b.width-v,A.height=b.y+b.height-w,A.x=v,A.y=w,A.width<10&&(A.width=10,A.x=b.x+b.width-10),A.height<10&&(A.height=10,A.y=b.y+b.height-10);break;case"top-right":A.width=v-b.x,A.height=b.y+b.height-w,A.y=w,A.width<10&&(A.width=10),A.height<10&&(A.height=10,A.y=b.y+b.height-10);break;case"bottom-left":A.width=b.x+b.width-v,A.height=w-b.y,A.x=v,A.width<10&&(A.width=10,A.x=b.x+b.width-10),A.height<10&&(A.height=10);break;case"bottom-right":A.width=v-b.x,A.height=w-b.y,A.width<10&&(A.width=10),A.height<10&&(A.height=10);break}}}},handleGlobalMouseUp:function(){var e=this;if(this.isDrawing)this.endDrawing();else if(this.isEditingBox||this.isResizingBox){var t=this.annotations.find((function(t){return t.id===e.editingBoxId}));if(t&&this.originalBox){var a=t.x!==this.originalBox.x||t.y!==this.originalBox.y||t.width!==this.originalBox.width||t.height!==this.originalBox.height;a&&(console.log("标注框已修改，更新数据库:",{原位置:"(".concat(this.originalBox.x,", ").concat(this.originalBox.y,")"),原尺寸:"".concat(this.originalBox.width," x ").concat(this.originalBox.height),新位置:"(".concat(t.x,", ").concat(t.y,")"),新尺寸:"".concat(t.width," x ").concat(t.height)}),t.normalizedX=t.x/this.imageWidth,t.normalizedY=t.y/this.imageHeight,t.normalizedWidth=t.width/this.imageWidth,t.normalizedHeight=t.height/this.imageHeight,this.updateAnnotationInDatabase(t))}this.isEditingBox=!1,this.isResizingBox=!1,this.editingBoxId=null,this.resizeHandle=null,this.originalBox=null}}},(0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)(D,"endDrawing",(function(){if(this.isDrawing&&(this.isDrawing=!1,Math.abs(this.drawCurrent.x-this.drawStart.x)>10&&Math.abs(this.drawCurrent.y-this.drawStart.y)>10)){var e=Math.min(this.drawStart.x,this.drawCurrent.x),t=Math.min(this.drawStart.y,this.drawCurrent.y),a=Math.abs(this.drawCurrent.x-this.drawStart.x),n=Math.abs(this.drawCurrent.y-this.drawStart.y),o=e/this.imageWidth,i=t/this.imageHeight,r=a/this.imageWidth,s=n/this.imageHeight,c={id:Date.now(),imageIndex:this.currentImageIndex,tag:this.selectedTag,type:"rectangle",x:e,y:t,width:a,height:n,normalizedX:o,normalizedY:i,normalizedWidth:r,normalizedHeight:s};this.annotations.push(c),this.saveAnnotationToDatabase(c)}})),"saveAnnotationToDatabase",(function(e){var t=this;return(0,F.A)((0,$.A)().m((function a(){var n,o,i,r,s,c,l;return(0,$.A)().w((function(a){while(1)switch(a.n){case 0:if(console.log("保存标注到数据库",e),t.imageId){a.n=1;break}return console.error("缺少图像ID，无法保存标注"),a.a(2);case 1:if(t.imageWidth&&t.imageHeight){a.n=2;break}return console.error("图像尺寸无效，无法计算归一化坐标"),a.a(2);case 2:return a.p=2,e.isYoloFormat?(i=e.width/t.imageWidth,r=e.height/t.imageHeight,n=e.x/t.imageWidth+i/2,o=e.y/t.imageHeight+r/2,console.log("保存YOLO格式标注，转换为中心点坐标：",{左上角像素:"(".concat(e.x,", ").concat(e.y,")"),像素宽高:"".concat(e.width," x ").concat(e.height),归一化中心点:"(".concat(n.toFixed(4),", ").concat(o.toFixed(4),")"),归一化宽高:"".concat(i.toFixed(4)," x ").concat(r.toFixed(4))})):(n=e.x/t.imageWidth,o=e.y/t.imageHeight,i=e.width/t.imageWidth,r=e.height/t.imageHeight,console.log("保存普通格式标注，使用左上角坐标：",{像素坐标:"(".concat(e.x,", ").concat(e.y,")"),像素宽高:"".concat(e.width," x ").concat(e.height),归一化坐标:"(".concat(n.toFixed(4),", ").concat(o.toFixed(4),")"),归一化宽高:"".concat(i.toFixed(4)," x ").concat(r.toFixed(4))})),n=Math.max(0,Math.min(1,n)),o=Math.max(0,Math.min(1,o)),i=Math.max(.001,Math.min(1,i)),r=Math.max(.001,Math.min(1,r)),s={metadata_id:parseInt(t.imageId,10),tag:e.tag,x:Number(n.toFixed(6)),y:Number(o.toFixed(6)),width:Number(i.toFixed(6)),height:Number(r.toFixed(6)),coord_system:e.isYoloFormat?"yolo_center":"top_left"},console.log("保存标注数据:",s),a.n=3,H["default"].tags.create(s);case 3:return c=a.v,console.log("标注保存成功:",c.data),e.dbId=c.data.id,e.normalizedX=n,e.normalizedY=o,e.normalizedWidth=i,e.normalizedHeight=r,t.$message.success("标注保存成功"),a.a(2,c.data);case 4:throw a.p=4,l=a.v,console.error("保存标注失败:",l),t.$message.error("保存标注失败: "+(l.message||"未知错误")),l;case 5:return a.a(2)}}),a,null,[[2,4]])})))()})),"saveAnnotationToDatabase",(function(e){var t=this;console.clear(),console.log("%c==================== 开始保存标注到数据库 ====================","background: #222; color: #bada55; font-size: 16px;");var a=JSON.parse(localStorage.getItem("user"));if(!a||!a.id)return this.$message.error("未检测到有效用户信息(缺少ID)，无法保存标注"),void console.error("未检测到有效用户信息(缺少ID)，无法保存标注",a);var n=this.uploadedImages[this.currentImageIndex];if(!n||!n.id)return this.$message.error("图像信息不完整，无法保存标注"),void console.error("图像信息不完整，无法保存标注, currentImage:",n);var o,i,r,s,c={图像ID:n.id,图像ID类型:(0,M.A)(n.id),标签类型:e.tag,坐标:"(".concat(e.x,", ").concat(e.y,")"),尺寸:"".concat(e.width," x ").concat(e.height),用户ID:a.id||a.customId,用户ID类型:(0,M.A)(a.id||a.customId),用户角色:a.role};if(console.log("%c标注数据:","color: #47B881; font-weight: bold;",c),void 0!==e.normalizedX)o=e.normalizedX,i=e.normalizedY,r=e.normalizedWidth,s=e.normalizedHeight,console.log("使用已保存的归一化坐标:",{x:o,y:i,width:r,height:s});else{if(!this.imageWidth||!this.imageHeight)return void this.$message.error("图片尺寸无效，无法计算标注坐标");o=e.x/this.imageWidth,i=e.y/this.imageHeight,r=e.width/this.imageWidth,s=e.height/this.imageHeight,console.log("计算新的归一化坐标:",{x:o,y:i,width:r,height:s})}o=Math.max(0,Math.min(1,o)),i=Math.max(0,Math.min(1,i)),r=Math.max(.001,Math.min(1,r)),s=Math.max(.001,Math.min(1,s)),o=Number(o.toFixed(4)),i=Number(i.toFixed(4)),r=Number(r.toFixed(4)),s=Number(s.toFixed(4));var l={tag:e.tag,tagName:e.tag,x:o,y:i,width:r,height:s,metadata_id:parseInt(n.id,10),created_by:parseInt(a.id,10)};console.log("发送标注数据:",l,{metadata_id类型:(0,M.A)(l.metadata_id),created_by类型:(0,M.A)(l.created_by)});var d=this.$loading({lock:!0,text:"正在保存标注...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),g=localStorage.getItem("token")||"",h=a.id||a.customId||"",u=a.role||"USER",m={"Content-Type":"application/json",Accept:"application/json"};g&&(m["Authorization"]="Bearer ".concat(g)),m["X-User-Id"]=h,m["X-User-Role"]=u,console.log("使用认证头信息:",{Authorization:g?"Bearer [已设置]":"[未设置]","X-User-Id":h,"X-User-Role":u}),fetch("/medical/api/tags",{method:"POST",headers:m,body:JSON.stringify(l),credentials:"include"}).then((function(e){if(console.log("保存标注响应状态:",e.status),!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return e.json()})).then((function(a){d.close(),console.log("标注保存成功:",a),t.$message.success("标注保存成功"),e.dbId=a.id,e.normalizedX=o,e.normalizedY=i,e.normalizedWidth=r,e.normalizedHeight=s,t.loadAnnotationsFromDatabase().then((function(){t.processLoadedAnnotations()}))}))["catch"]((function(a){console.error("保存标注失败:",a),console.log("尝试备用API路径..."),fetch("/api/tags",{method:"POST",headers:m,body:JSON.stringify(l),credentials:"include"}).then((function(e){if(!e.ok)throw new Error("备用路径也失败! status: ".concat(e.status));return e.json()})).then((function(a){d.close(),console.log("通过备用路径保存标注成功:",a),t.$message.success("标注保存成功"),e.dbId=a.id,e.normalizedX=o,e.normalizedY=i,e.normalizedWidth=r,e.normalizedHeight=s,t.loadAnnotationsFromDatabase().then((function(){t.processLoadedAnnotations()}))}))["catch"]((function(a){d.close(),console.error("所有尝试都失败:",a),t.$message.warning("无法保存到服务器，但已添加到本地标注列表"),e.normalizedX=o,e.normalizedY=i,e.normalizedWidth=r,e.normalizedHeight=s}))}))})),"getTagColor",(function(e){var t={"IH-婴幼儿血管瘤":"#ff5252","RICH-先天性快速消退型血管瘤":"#ff7252","PICH-先天性部分消退型血管瘤":"#ff9252","NICH-先天性不消退型血管瘤":"#ffb252","KHE-卡泊西型血管内皮细胞瘤":"#4caf50","KH-角化型血管瘤":"#8bc34a","PG-肉芽肿性血管瘤":"#cddc39","MVM-微静脉畸形":"#2196f3","VM-静脉畸形":"#03a9f4","AVM-动静脉畸形":"#00bcd4",血管瘤:"#ff5252",淋巴管瘤:"#2196f3",混合型:"#9c27b0",其他:"#607d8b"};return t[e]||"#ff5252"})),"deleteAnnotation",(function(e){var t=this,a=this.annotations.length;console.log("删除标注开始，当前标注数量: ".concat(a,", 要删除的ID: ").concat(e));var n=this.annotations.findIndex((function(t){return t.id===e}));if(-1!==n){var o=this.annotations[n],i=this.$loading({lock:!0,text:"正在删除标注...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});if(this.annotations.splice(n,1),console.log("已从本地数组中删除标注，剩余标注数量: ".concat(this.annotations.length)),o.dbId){console.log("正在从数据库删除标注，ID:",o.dbId);var r=(0,P.A)({},o);H["default"].tags["delete"](o.dbId).then((function(){console.log("标注已从数据库中删除"),t.$message.success("标注已删除"),i.close(),console.log("删除成功后标注数量: ".concat(t.annotations.length))}))["catch"]((function(e){console.error("删除标注失败",e),e.response?(console.error("服务器响应:",e.response.status,e.response.data),403===e.response.status?t.$message.error("您没有权限删除此标注"):404===e.response.status?t.$message.error("标注不存在或已被删除"):t.$message.error("删除失败: ".concat(e.response.data||"服务器错误"))):e.request?t.$message.error("网络错误，无法连接到服务器"):t.$message.error("删除标注失败: "+e.message),i.close(),console.log("恢复标注到索引 ".concat(n,"，恢复前标注数量: ").concat(t.annotations.length)),t.annotations.splice(n,0,r),console.log("恢复后标注数量: ".concat(t.annotations.length)),t.checkForDuplicateAnnotations()}))}else this.$message.success("标注已删除"),i.close(),console.log("本地标注删除完成，当前标注数量: ".concat(this.annotations.length))}else console.error("未找到ID为 ".concat(e," 的标注")),this.$message.warning("未找到要删除的标注")})),"checkForDuplicateAnnotations",(function(){console.log("检查是否有重复标注...");var e=new Map,t=[];if(this.annotations.forEach((function(a,n){e.has(a.id)?t.push(n):e.set(a.id,n)})),t.length>0){console.warn("发现 ".concat(t.length," 个重复标注，将被移除"));for(var a=t.length-1;a>=0;a--)this.annotations.splice(t[a],1);this.$message.warning("已自动移除 ".concat(t.length," 个重复标注"))}})),"saveAndNext",(function(){var e=this;return(0,F.A)((0,$.A)().m((function t(){return(0,$.A)().w((function(t){while(1)switch(t.n){case 0:return t.a(2,new Promise(function(){var t=(0,F.A)((0,$.A)().m((function t(a,n){var o,i,r,s,c,l,d,g,h,u,m,f,I,p,v,w,x,A,b,y;return(0,$.A)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,e.isSaving=!0,console.log("[saveAndNext] 开始保存标注并跳转"),console.log("[saveAndNext] 当前标注数量:",e.annotations.length),t.p=1,t.n=2,e.$confirm("是否继续填写表单？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});case 2:console.log("[saveAndNext] 用户确认继续"),t.n=4;break;case 3:return t.p=3,t.v,console.log("[saveAndNext] 用户取消了操作"),e.isSaving=!1,a(!1),t.a(2);case 4:if(console.log("[导航跟踪] 准备跳转到表单页，当前URL:",window.location.href),o=JSON.parse(localStorage.getItem(z.STORAGE_KEYS.userInfo)||"{}"),i=o.id||o.customId||"",r=o.role||"USER",localStorage.getItem(z.STORAGE_KEYS.token),sessionStorage.setItem(z.STORAGE_KEYS.authValid,"true"),sessionStorage.setItem(z.STORAGE_KEYS.isNavigatingAfterSave,"true"),console.log("[认证状态] 用户ID:",i,"角色:",r),s=e.diagnosisId,s||(s=e.$route.query.diagnosisId||e.$route.params.id),!s&&e.imageId&&(s=e.imageId,console.log("[诊断ID] 使用imageId作为诊断ID:",s)),c=e.uploadedImages&&e.uploadedImages.length>0?e.uploadedImages[e.currentImageIndex]:null,c&&c.id?console.log("[saveAndNext] 使用图像信息:",{id:c.id,url:c.url}):(console.error("[saveAndNext] 无法获取当前图像信息:",{hasUploadedImages:!!(e.uploadedImages&&e.uploadedImages.length>0),currentImageIndex:e.currentImageIndex}),e.$message.warning("无法获取图像信息，将尝试使用备用方法保存标注")),l=(null===c||void 0===c?void 0:c.id)||s||e.imageId,l){t.n=5;break}return e.$message.error("无法获取有效的图像ID，无法保存标注"),e.isSaving=!1,a(!1),t.a(2);case 5:if(console.log("[saveAndNext] 使用的图像ID:",l),!(e.annotations&&e.annotations.length>0)){t.n=23;break}t.p=6,d=e.$loading({lock:!0,text:"保存标注并生成图像...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),g=3,h=0,u=!1,m="";case 7:if(!(h<g)||u){t.n=13;break}return t.p=8,console.log("[saveAndNext] 尝试保存标注，第 ".concat(h+1," 次尝试")),t.n=9,e.saveAnnotatedImageAfterEdit(l,!0);case 9:console.log("[saveAndNext] 标注图像保存成功"),u=!0,localStorage.setItem("annotations",JSON.stringify(e.annotations)),e.$message.success("标注已保存"),t.n=12;break;case 10:return t.p=10,w=t.v,console.error("[saveAndNext] 第 ".concat(h+1," 次保存标注失败:"),w),m=w.response?"服务器错误 (".concat(w.response.status,"): ").concat("string"===typeof w.response.data?w.response.data:(null===(f=w.response.data)||void 0===f?void 0:f.error)||(null===(I=w.response.data)||void 0===I?void 0:I.message)||JSON.stringify(w.response.data)):w.request?"服务器未响应":w.message||"未知错误",t.n=11,new Promise((function(e){return setTimeout(e,1e3*(h+1))}));case 11:h++;case 12:t.n=7;break;case 13:if(d.close(),u){t.n=17;break}return console.error("[saveAndNext] 所有保存尝试均失败"),e.$message.error("保存标注失败: ".concat(m)),t.p=14,t.n=15,e.$confirm("保存标注失败，是否仍要继续到表单页面？","提示",{confirmButtonText:"继续",cancelButtonText:"留在当前页面",type:"warning"});case 15:console.log("[saveAndNext] 用户选择继续到表单页面"),t.n=17;break;case 16:return t.p=16,t.v,console.log("[saveAndNext] 用户选择留在当前页面"),e.isSaving=!1,a(!1),t.a(2);case 17:t.n=22;break;case 18:return t.p=18,x=t.v,console.error("[saveAndNext] 保存标注图像过程中出错:",x),e.$message.error("保存标注过程中出错: "+(x.message||"未知错误")),t.p=19,t.n=20,e.$confirm("保存标注失败，是否仍要继续到表单页面？","提示",{confirmButtonText:"继续",cancelButtonText:"留在当前页面",type:"warning"});case 20:t.n=22;break;case 21:return t.p=21,t.v,e.isSaving=!1,a(!1),t.a(2);case 22:t.n=24;break;case 23:console.log("[saveAndNext] 没有标注数据，跳过保存步骤");case 24:return t.n=25,new Promise((function(e){return setTimeout(e,z.UI.navigationDelay)}));case 25:if(!s&&e.currentImageIndex>=0&&e.uploadedImages&&e.uploadedImages.length>0&&(p=e.uploadedImages[e.currentImageIndex],p&&p.id&&(s=p.id,console.log("[诊断ID] 使用当前图像ID作为诊断ID:",s))),s){t.n=26;break}return e.$message.error("未找到诊断ID，无法继续"),console.error("[导航错误] 未找到有效的诊断ID"),console.log("[诊断状态]",{this_diagnosisId:e.diagnosisId,route_query:e.$route.query.diagnosisId,route_params:e.$route.params.id,imageId:e.imageId,currentImageIndex:e.currentImageIndex,hasUploadedImages:!!(e.uploadedImages&&e.uploadedImages.length>0)}),e.isSaving=!1,a(!1),t.a(2);case 26:return console.log("[诊断ID] 最终使用的诊断ID:",s),v="".concat(z.ROUTES.structuredForm,"?diagnosisId=").concat(s),console.log("[导航] 即将导航到:",v),t.p=27,t.n=28,e.$router.push(v);case 28:console.log("[导航] 导航完成"),a(!0),t.n=33;break;case 29:return t.p=29,A=t.v,console.error("[导航] 路由导航失败:",A),t.p=30,t.n=31,e.$router.replace(v);case 31:console.log("[导航] 备用导航完成"),a(!0),t.n=33;break;case 32:t.p=32,b=t.v,console.error("[导航] 备用导航也失败:",b),e.$message.error("导航失败，请手动访问结构化表单页面"),e.isSaving=!1,sessionStorage.setItem(z.STORAGE_KEYS.pendingDiagnosis,s),setTimeout((function(){window.location.href=v}),200),a(!1);case 33:t.n=35;break;case 34:t.p=34,y=t.v,console.error("[saveAndNext] 操作失败:",y),e.$message.error("操作失败，请重试"),n(y);case 35:return t.p=35,setTimeout((function(){e.isSaving=!1}),1e3),t.f(35);case 36:return t.a(2)}}),t,null,[[30,32],[27,29],[19,21],[14,16],[8,10],[6,18],[1,3],[0,34,35,36]])})));return function(e,a){return t.apply(this,arguments)}}()))}}),t)})))()})),"confirmContinue",(function(){var e=this;return(0,F.A)((0,$.A)().m((function t(){return(0,$.A)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,t.n=1,e.$confirm("保存标注失败，是否仍要继续到表单页面？","提示",{confirmButtonText:"继续",cancelButtonText:"留在当前页面",type:"warning"});case 1:return t.a(2,!0);case 2:return t.p=2,t.v,t.a(2,!1)}}),t,null,[[0,2]])})))()})),"saveAnnotations",(function(){var e=arguments,t=this;return(0,F.A)((0,$.A)().m((function a(){var n,o,i,r,s,c,l,d,g,h,u,m,f,I;return(0,$.A)().w((function(a){while(1)switch(a.n){case 0:if(n=e.length>0&&void 0!==e[0]?e[0]:"保存中...",o=t.uploadedImages[t.currentImageIndex],o&&o.id){a.n=1;break}return t.$message.error("无效的图像，无法保存标注"),a.a(2,!1);case 1:if(!t.isSavingAnnotations){a.n=2;break}return console.warn("正在保存标注，请勿重复操作"),a.a(2,!1);case 2:return t.isSavingAnnotations=!0,i=t.$loading({lock:!0,text:n,spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),a.p=3,console.log("保存标注，图像ID:",o.id),a.p=4,a.n=5,H["default"].tags.saveAnnotatedImage(o.id);case 5:r=a.v,console.log("标注图像保存成功:",r.data),a.n=12;break;case 6:if(a.p=6,h=a.v,console.error("保存标注图像失败:",h),!(h.response&&h.response.status>=500)){a.n=11;break}return a.p=7,console.log("尝试使用另一种方法更新标注图像"),a.n=8,H["default"].tags.updateImageAfterAnnotation({metadata_id:o.id});case 8:console.log("更新标注图像成功"),a.n=10;break;case 9:throw a.p=9,u=a.v,console.error("更新标注图像也失败:",u),u;case 10:a.n=12;break;case 11:throw h;case 12:return a.p=12,s=new Date,c=new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1,timeZone:"Asia/Shanghai"}).format(s),l=s.toISOString(),console.log("当前中国时间:",c,"，ISO时间:",l),a.p=13,a.n=14,H["default"].images.markAsAnnotated(o.id,l);case 14:console.log('图像状态已成功更新为"已标注"(REVIEWED)'),a.n=18;break;case 15:if(a.p=15,m=a.v,console.error("标记为已标注失败:",m),!(m.response&&m.response.data&&m.response.data.includes("重复"))){a.n=16;break}console.log("检测到重复标记消息，不再重试"),a.n=18;break;case 16:return a.n=17,H["default"].images.updateStatus(o.id,"REVIEWED");case 17:console.log('使用updateStatus成功更新状态为"已标注"');case 18:a.n=20;break;case 19:a.p=19,f=a.v,console.error("所有更新图像状态的尝试都失败:",f);case 20:return t.$message.success("标注已保存"),i.close(),t.isSavingAnnotations=!1,a.a(2,!0);case 21:return a.p=21,I=a.v,console.error("保存标注图片失败",I),d="保存标注失败",I.response?(console.error("服务器响应:",I.response.status,I.response.data),"string"===typeof I.response.data?d+=": "+I.response.data:I.response.data&&I.response.data.message?d+=": "+I.response.data.message:d+=" (状态码: "+I.response.status+")"):I.message&&(d+=": "+I.message),t.$message.error(d),i.close(),t.isSavingAnnotations=!1,a.p=22,a.n=23,t.$confirm("保存标注失败，但已在本地保存标注数据。是否继续?","警告",{confirmButtonText:"继续",cancelButtonText:"留在当前页面",type:"warning"});case 23:if(g=a.v,"confirm"!==g){a.n=24;break}return localStorage.setItem("annotations",JSON.stringify(t.annotations)),localStorage.setItem("pendingImageId",o.id.toString()),a.a(2,!0);case 24:return a.a(2,!1);case 25:return a.p=25,a.v,a.a(2,!1)}}),a,null,[[22,25],[13,15],[12,19],[7,9],[4,6],[3,21]])})))()})),"loadAnnotationsFromDatabase",(function(){var e=this;return console.log("从数据库加载标注数据，图像ID:",this.imageId),new Promise((function(t,a){var n=JSON.parse(localStorage.getItem("user")||"{}"),o=localStorage.getItem("token")||"",i=n.id||n.customId||"",r=n.role||"USER",s={Accept:"application/json","Cache-Control":"no-cache","Content-Type":"application/json"};o&&(s["Authorization"]="Bearer ".concat(o)),s["X-User-Id"]=i,s["X-User-Role"]=r,console.log("加载标注数据 - 使用认证头:",{Authorization:o?"Bearer [已设置]":"[未设置]","X-User-Id":i,"X-User-Role":r});var c=Date.now(),l=Math.random(),d="t=".concat(c,"&r=").concat(l),g="/medical/api/tags/image/".concat(e.imageId,"?").concat(d,"&userId=").concat(i);fetch(g,{method:"GET",headers:s,credentials:"include"}).then((function(e){if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return e.json()})).then((function(a){console.log("从数据库获取到标注数据:",a),e.dbAnnotations=a.map((function(e){return{id:e.id,tag:e.tagName||e.tag,name:e.tagName||e.tag,x:e.x,y:e.y,width:e.width,height:e.height,created_by:e.created_by}})),console.log("处理后的标注数据:",e.dbAnnotations),t(e.dbAnnotations)}))["catch"]((function(n){console.error("获取标注数据失败:",n),console.log("尝试备用API路径...");var o="/api/tags/image/".concat(e.imageId,"?").concat(d,"&userId=").concat(i);fetch(o,{method:"GET",headers:s,credentials:"include"}).then((function(e){if(!e.ok)throw new Error("备用路径也失败! status: ".concat(e.status));return e.json()})).then((function(a){console.log("通过备用路径获取到标注数据:",a),e.dbAnnotations=a.map((function(e){return{id:e.id,tag:e.tagName||e.tag,name:e.tagName||e.tag,x:e.x,y:e.y,width:e.width,height:e.height,created_by:e.created_by}})),console.log("处理后的标注数据:",e.dbAnnotations),t(e.dbAnnotations)}))["catch"]((function(n){console.error("所有尝试都失败:",n);var o=localStorage.getItem("annotations_".concat(e.imageId));if(o)try{var i=JSON.parse(o);console.log("找到本地保存的标注数据:",i),e.dbAnnotations=i,t(e.dbAnnotations)}catch(r){console.error("解析本地标注数据失败:",r),a(n)}else a(n)}))}))}))})),(0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)(D,"updateAnnotationInDatabase",(function(e){var t=this,a=JSON.parse(localStorage.getItem("user")||"{}");if(a&&(a.id||a.customId)){var n=a.id||a.customId,o=a.role||"USER";if(e.dbId)if(this.imageWidth&&this.imageHeight){var i,r,s,c;e.isYoloFormat?(s=e.width/this.imageWidth,c=e.height/this.imageHeight,i=e.x/this.imageWidth+s/2,r=e.y/this.imageHeight+c/2):(i=e.x/this.imageWidth,r=e.y/this.imageHeight,s=e.width/this.imageWidth,c=e.height/this.imageHeight),i=Math.max(0,Math.min(1,i)),r=Math.max(0,Math.min(1,r)),s=Math.max(.001,Math.min(1,s)),c=Math.max(.001,Math.min(1,c)),i=Number(i.toFixed(4)),r=Number(r.toFixed(4)),s=Number(s.toFixed(4)),c=Number(c.toFixed(4));var l={tag:e.tag,x:i,y:r,width:s,height:c,updated_by:n,coord_system:e.isYoloFormat?"yolo_center":"top_left"};console.log("更新标注数据:",{id:e.dbId,归一化坐标:"(".concat(i,", ").concat(r,")"),归一化尺寸:"".concat(s," x ").concat(c),坐标系统:e.isYoloFormat?"YOLO中心点":"左上角"});var d=this.$loading({lock:!0,text:"正在更新标注...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),g={"Content-Type":"application/json",Accept:"application/json","X-User-Id":n,"X-User-Role":o,"Cache-Control":"no-cache"},h=Date.now();fetch("/medical/api/tags/".concat(e.dbId,"?t=").concat(h,"&userId=").concat(n),{method:"PUT",headers:g,body:JSON.stringify(l),credentials:"include"}).then((function(e){if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return e.json()})).then((function(a){d.close(),console.log("标注已更新",a),t.$message.success("标注已更新"),e.normalizedX=i,e.normalizedY=r,e.normalizedWidth=s,e.normalizedHeight=c,t.scheduleSaveAnnotatedImage(t.imageId)}))["catch"]((function(a){console.error("更新标注失败",a),d.close(),console.log("尝试备用API路径..."),fetch("/api/tags/".concat(e.dbId,"?t=").concat(h,"&userId=").concat(n),{method:"PUT",headers:g,body:JSON.stringify(l),credentials:"include"}).then((function(e){if(!e.ok)throw new Error("备用路径也失败! status: ".concat(e.status));return e.json()})).then((function(a){console.log("通过备用路径更新标注成功:",a),t.$message.success("标注已更新"),e.normalizedX=i,e.normalizedY=r,e.normalizedWidth=s,e.normalizedHeight=c,t.scheduleSaveAnnotatedImage(t.imageId)}))["catch"]((function(e){console.error("所有尝试都失败:",e),t.$message.error("更新标注失败，请稍后重试")}))}))}else this.$message.error("图片尺寸无效，无法计算标注坐标");else console.warn("标注没有数据库ID，无法更新:",e)}else this.$message.error("未检测到有效用户信息，无法更新标注")})),"saveAnnotatedImageAfterEdit",(function(e){var t=arguments,a=this;return(0,F.A)((0,$.A)().m((function n(){var o,i,r,s,c,l;return(0,$.A)().w((function(n){while(1)switch(n.n){case 0:if(o=t.length>1&&void 0!==t[1]&&t[1],e){n.n=1;break}return console.error("保存标注图像失败：缺少图像ID"),a.$message.error("保存标注图像失败：缺少图像ID"),n.a(2);case 1:if(o||!a.saveImageTimer){n.n=2;break}return console.log("已经有延迟保存计划，不重复保存"),n.a(2);case 2:return a.saveImageTimer&&(clearTimeout(a.saveImageTimer),a.saveImageTimer=null),console.log("编辑标注完成，正在保存标注图像，图像ID:",e),i=a.$loading({lock:!0,text:"正在保存标注图像...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),n.p=3,n.n=4,H["default"].tags.saveAnnotatedImage(e);case 4:r=n.v,console.log("标注图像保存成功:",r.data),r.data&&r.data.processedPath?(console.log("更新处理后的图像路径:",r.data.processedPath),s=Date.now(),c="".concat(r.data.processedPath,"?t=").concat(s),a.currentImage&&(a.processedFilePath=c),a.$message.success("标注图像已保存")):a.$message.warning("标注图像已保存，但未返回新图像路径"),i.close(),n.n=6;break;case 5:n.p=5,l=n.v,console.error("保存标注图像失败:",l),a.$message.error("保存标注图像失败，请稍后重试"),i.close();case 6:return n.a(2)}}),n,null,[[3,5]])})))()})),"processLoadedAnnotations",(function(){var e=this;if(this.processedAnnotations)console.warn("标注已处理过，跳过重复渲染，防止生成重复的标注框。");else if(this.imageWidth<=0||this.imageHeight<=0)console.warn("图像尺寸无效，无法处理标注",{w:this.imageWidth,h:this.imageHeight});else{if(!this.dbAnnotations||0===this.dbAnnotations.length)return console.log("没有标注数据需要处理"),void(this.annotations=[]);console.log("开始处理已加载的标注数据，图像尺寸:","显示: ".concat(this.imageWidth,"x").concat(this.imageHeight),"原始: ".concat(this.originalWidth,"x").concat(this.originalHeight),"标注数量:",this.dbAnnotations.length);var t=[];this.dbAnnotations.forEach((function(a){var n=a.tagName||a.tag||"IH-婴幼儿血管瘤",o=parseFloat(a.x)||0,i=parseFloat(a.y)||0,r=parseFloat(a.width)||0,s=parseFloat(a.height)||0;if(r<=0||s<=0)console.warn("标注 ".concat(a.id," 的尺寸无效，跳过该标注"));else{var c=r*e.imageWidth,l=s*e.imageHeight,d=o*e.imageWidth,g=i*e.imageHeight,h=d-c/2,u=g-l/2;console.log("处理标注 ".concat(a.id,": 中心点(").concat(d.toFixed(2),", ").concat(g.toFixed(2),") -> 左上角(").concat(h.toFixed(2),", ").concat(u.toFixed(2),")"));var m={id:a.id,dbId:a.id,tag:n,x:h,y:u,width:c,height:l,normalizedX:o,normalizedY:i,normalizedWidth:r,normalizedHeight:s,imageIndex:e.currentImageIndex,type:"rectangle",isYoloFormat:!0};t.push(m)}})),this.annotations=t,this.processedAnnotations=!0,this.annotationsLoaded=!0}})),"goBack",(function(){var e=this;console.log("[导航跟踪] 点击返回按钮，当前URL:",window.location.href),this.$confirm("返回上传页面可能会丢失当前未保存的标注，是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(sessionStorage.setItem("returningToWorkbench","true"),console.log("[导航跟踪] 用户确认返回，准备导航到: /app/cases/new"),e.imagePairId){var t=e.$loading({lock:!0,text:"正在处理...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});H["default"].imagePairs.deleteAnnotatedImage(e.imagePairId).then((function(a){console.log("标注图片删除结果:",a.data),t.close(),console.log("[导航跟踪] 导航到: /app/cases/new (API调用后)");var n=window.location.href;e.$router.push("/app/cases/new").then((function(){console.log("[导航跟踪] 导航成功，从",n,"到",window.location.href)}))["catch"]((function(e){console.error("[导航跟踪] 导航失败:",e),window.location.href="/app/cases/new"}))}))["catch"]((function(a){console.error("删除标注图片失败",a),t.close(),e.$message.error("删除标注图片失败，但仍将返回上一页"),console.log("[导航跟踪] 导航到: /app/cases/new (API调用失败后)");var n=window.location.href;e.$router.push("/app/cases/new").then((function(){console.log("[导航跟踪] 导航成功，从",n,"到",window.location.href)}))["catch"]((function(e){console.error("[导航跟踪] 导航失败:",e),window.location.href="/app/cases/new"}))}))}else{console.log("[导航跟踪] 导航到: /app/cases/new (无图像对ID)");var a=window.location.href;e.$router.push("/app/cases/new").then((function(){console.log("[导航跟踪] 导航成功，从",a,"到",window.location.href)}))["catch"]((function(e){console.error("[导航跟踪] 导航失败:",e),window.location.href="/app/cases/new"}))}}))["catch"]((function(){console.log("[导航跟踪] 用户取消返回操作")}))})),"tryLoadImagePathFromMetadata",(function(e,t){console.log("从image_metadata表获取图像路径:",e),H["default"].images.getOne(e).then((function(e){e.data&&e.data.path?(console.log("成功从image_metadata获取图像路径:",e.data.path),t(e.data.path)):(console.error("image_metadata中图像路径不存在"),t(null))}))["catch"]((function(e){console.error("从image_metadata获取图像路径失败:",e),t(null)}))})),"updateImagePairPath",(function(e,t){if(this.imagePairId&&t){console.log("更新image_pairs表中的图像路径:",{imagePairId:this.imagePairId,metadataId:e,path:t});var a={id:this.imagePairId,metadataId:e.toString(),imageOnePath:t};fetch("/api/image-pairs",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(a),credentials:"include"}).then((function(e){if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return e.json()})).then((function(e){console.log("成功更新image_pairs中的图像路径:",e)}))["catch"]((function(e){console.error("更新image_pairs中的图像路径失败:",e)}))}else console.error("缺少imagePairId或path，无法更新")})),"loadAnnotations",(function(e){var t=this;if(e){this.annotationsLoaded=!1,console.log("开始加载图像标注，ID:",e);var a=JSON.parse(localStorage.getItem("user"))||{},n=a.customId||a.id;n||console.warn("未找到用户ID，标注加载可能存在权限问题"),this.$message.info("正在加载标注数据...");var o=(new Date).getTime();console.log("请求时间戳: ".concat(o,", 使用edit模式加载标注")),H["default"].tags.getByImageId(e,"edit").then((function(e){console.log("成功获取图像标注数据:",e.data),t.dbAnnotations=e.data||[],Array.isArray(t.dbAnnotations)||(console.error("从服务器获取的标注数据不是数组格式:",t.dbAnnotations),t.dbAnnotations=[]),console.log("成功获取到 ".concat(t.dbAnnotations.length," 个标注")),t.dbAnnotations.length>0?t.$message.success("已加载 ".concat(t.dbAnnotations.length," 个标注")):t.$message.info("没有找到标注数据，您可以添加新标注"),t.imageWidth>0&&t.imageHeight>0?t.processLoadedAnnotations():console.log("图像尺寸尚未加载，将在图像加载后处理标注")}))["catch"]((function(e){console.error("加载标注数据失败:",e),t.dbAnnotations=[],t.annotationsLoaded=!0,t.$message.error("加载标注失败，但您仍可以添加新标注")}))}else console.error("loadAnnotations: 图像ID无效，无法加载标注")})),"refreshAuthentication",(function(){var e=JSON.parse(localStorage.getItem("user")||"{}");(e.id||e.customId)&&console.log("尝试刷新用户认证信息")})),"tryLoadOfflineData",(function(e){console.log("尝试从localStorage加载离线数据...");var t=localStorage.getItem("offline_image_".concat(e));t?(console.log("找到离线图像数据"),this.imageData={id:e,path:t},this.imagePair={id:Date.now(),metadataId:e,imageOnePath:t},this.loadImage()):console.warn("未找到离线图像数据")})),"submitForm",(function(){var e=this;this.$refs.structuredForm.validate((function(t){if(!t)return console.error("表单验证失败"),e.$message.error("表单验证失败，请检查输入"),!1;console.log("表单验证通过，准备提交数据"),e.loadAnnotationsFromDatabase().then((function(){var t=e.$loading({lock:!0,text:"正在保存表单数据...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.7)"}),a=(0,P.A)({},e.formData);a.annotations=e.dbAnnotations,console.log("提交表单数据:",a),H["default"].images.saveStructuredFormData(e.imageId,a).then((function(a){console.log("表单数据保存成功:",a.data),t.close(),e.$message.success("表单数据保存成功"),e.$router.push("/app/cases")}))["catch"]((function(a){console.error("表单数据保存失败:",a),t.close(),e.$message.error("表单数据保存失败: "+(a.message||"未知错误"))}))}))["catch"]((function(t){console.error("获取标注数据失败:",t),e.$message.warning("无法获取最新标注数据，将只保存表单信息"),e.submitFormOnly()}))}))})),(0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)(D,"submitFormOnly",(function(){var e=this,t=this.$loading({lock:!0,text:"正在保存表单数据...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.7)"}),a=(0,P.A)({},this.formData);console.log("提交表单数据:",a),H["default"].images.saveStructuredFormData(this.imageId,a).then((function(a){console.log("表单数据保存成功:",a.data),t.close(),e.$message.success("表单数据保存成功"),e.$router.push("/app/cases")}))["catch"]((function(a){console.error("表单数据保存失败:",a),t.close(),e.$message.error("表单数据保存失败: "+(a.message||"未知错误"))}))})),"loadFormData",(function(){var e=this;console.log("加载表单数据，图像ID:",this.imageId),H["default"].images.getOne(this.imageId).then((function(t){console.log("获取图像元数据成功:",t.data);var a=t.data;a.structuredForm?(e.formData=(0,P.A)((0,P.A)({},e.formData),a.structuredForm),console.log("已加载结构化表单数据:",e.formData)):console.log("图像没有结构化表单数据")}))["catch"]((function(t){console.error("加载表单数据失败:",t),e.$message.warning("加载表单数据失败，将使用空表单")}))})),"loadPageData",(function(e){var t=this;return(0,F.A)((0,$.A)().m((function a(){var n,o,i,r,s,c,l,d;return(0,$.A)().w((function(a){while(1)switch(a.n){case 0:if(e){a.n=1;break}return a.a(2,Promise.reject("未提供诊断ID"));case 1:return console.log("开始为ID: ".concat(e," 加载页面核心数据")),a.p=2,a.p=3,a.n=4,W().get("/medical/api/hemangioma-diagnoses/".concat(e));case 4:if(n=a.v,o=n.data,console.log("从血管瘤诊断API获取到的核心数据:",o),!o||!o.imagePath){a.n=5;break}return i=o.imagePath,t.uploadedImages=[{id:e,url:i,filename:"诊断图像 #".concat(e)}],t.currentImage=t.uploadedImages[0],t.processedFilePath=i,console.log("图片信息设置成功，URL:",i),a.a(2,Promise.resolve());case 5:a.n=7;break;case 6:a.p=6,l=a.v,console.log("从血管瘤诊断API获取数据失败，尝试普通图像API:",l.message);case 7:return console.log("尝试从普通图像API获取数据，ID:",e),a.n=8,W().get("/api/images/".concat(e),{headers:{"Cache-Control":"no-cache, no-store",Pragma:"no-cache",Expires:"0"}});case 8:if(r=a.v,s=r.data,console.log("从普通图像API获取到的数据:",s),!s||!s.path&&!s.url){a.n=9;break}return c=s.path||s.url,t.uploadedImages=[{id:e,url:c,filename:s.original_name||"图像 #".concat(e)}],t.currentImage=t.uploadedImages[0],t.processedFilePath=c,console.log("图片信息设置成功，URL:",c),a.a(2,Promise.resolve());case 9:throw new Error("响应数据中缺少图像路径");case 10:a.n=12;break;case 11:return a.p=11,d=a.v,console.error("加载页面数据失败 (ID: ".concat(e,"):"),d),t.$message.error("加载图像信息失败: ".concat(d.message)),a.a(2,Promise.reject(d));case 12:return a.a(2)}}),a,null,[[3,6],[2,11]])})))()})),"reloadAnnotations",(function(){var e=this;console.log("手动刷新标注数据"),this.annotations=[],this.dbAnnotations=[],this.annotationsLoaded=!1,this.$message.info("正在重新加载标注数据..."),this.loadAnnotationsFromDatabase().then((function(){e.imageWidth>0&&e.imageHeight>0&&e.processLoadedAnnotations(),e.$message.success("已加载 ".concat(e.dbAnnotations.length," 个标注"))}))["catch"]((function(t){console.error("重新加载标注失败:",t),e.$message.error("重新加载标注失败，请刷新页面重试")}))})),"handleNextButtonClick",(function(e){var t=this;console.log("下一步按钮被点击",e),e&&(e.stopPropagation(),e.preventDefault()),console.log("当前组件状态:",{imageId:this.imageId,hasAnnotations:this.annotations.length>0,isSaving:this.isSaving,currentImageIndex:this.currentImageIndex,hasUploadedImages:!!(this.uploadedImages&&this.uploadedImages.length>0)});var a=setTimeout((function(){console.log("[handleNextButtonClick] 导航超时，使用备用导航"),t.directNavigateToForm()}),5e3);this.saveAndNext()["finally"]((function(){clearTimeout(a)}))})),"directNavigateToForm",(function(){try{var e=this.diagnosisId||this.$route.query.diagnosisId||this.$route.params.id||this.imageId;if(!e)return console.error("[directNavigateToForm] 无法获取诊断ID"),this.$message.error("无法获取诊断ID，无法导航"),!1;var t="".concat(z.ROUTES.structuredForm,"?diagnosisId=").concat(e);return console.log("[directNavigateToForm] 直接导航到:",t),window.location.href=t,!0}catch(a){return console.error("[directNavigateToForm] 导航失败:",a),!1}})),"scheduleSaveAnnotatedImage",(function(e){var t=this;console.log("安排延迟保存标注图像，10秒后执行..."),this.saveImageTimer&&(console.log("清除之前的保存计时器"),clearTimeout(this.saveImageTimer)),this.saveImageTimer=setTimeout((function(){console.log("10秒延迟后执行保存标注图像"),t.saveAnnotatedImageAfterEdit(e),t.saveImageTimer=null}),1e4)})),"saveAndExit",(function(){var e=this;return(0,F.A)((0,$.A)().m((function t(){var a,n,o;return(0,$.A)().w((function(t){while(1)switch(t.n){case 0:if(0!==e.annotations.length){t.n=1;break}return e.$router.push("/app/cases"),t.a(2);case 1:if(a=e.$loading({lock:!0,text:"保存中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),n=e.uploadedImages[e.currentImageIndex],n&&n.id){t.n=2;break}return e.$message.error("图像信息不完整，无法保存标注"),a.close(),t.a(2);case 2:return t.p=2,console.log("saveAndExit: 使用真实图像ID保存标注:",n.id),t.n=3,e.saveAnnotatedImageAfterEdit(n.id,!0);case 3:localStorage.setItem("annotations",JSON.stringify(e.annotations)),e.$message.success("标注已保存"),a.close(),localStorage.setItem("isSaveAndExit","true"),e.$router.push("/app/cases"),t.n=5;break;case 4:t.p=4,o=t.v,console.error("保存标注失败:",o),e.$message.error("保存标注失败: "+(o.message||"未知错误")),a.close();case 5:return t.a(2)}}),t,null,[[2,4]])})))()})))};var L=a(66262);const E=(0,L.A)(C,[["render",k],["__scopeId","data-v-46ae9090"]]),N=E}}]);
//# sourceMappingURL=751.e1cc22ae.js.map