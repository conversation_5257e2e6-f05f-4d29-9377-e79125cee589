<template>
  <div class="teams-container">
    <div class="page-header">
      <h2>我的团队</h2>
      <div class="action-buttons">
        <el-button 
          v-if="(!currentTeam && isAdmin) || (!currentTeam && isReviewer)"
          type="success" 
          @click="showCreateTeamDialog = true"
        >
          创建团队
        </el-button>
        <el-button 
          v-if="!currentTeam"
          type="primary" 
          @click="showJoinTeamDialog = true"
        >
          加入团队
        </el-button>
      </div>
    </div>

    <!-- 当前用户所在的团队 -->
    <el-card v-if="currentTeam" class="current-team-card">
      <template #header>
        <div class="card-header">
          <span>当前团队</span>
          <div class="team-header-actions">
            <el-button 
              v-if="isTeamOwner || isAdmin"
              type="danger" 
              size="small"
              @click="handleDisbandTeam"
            >
              解散团队
            </el-button>
            <!-- 添加团队申请管理按钮，仅对团队管理员和审核医生可见 -->
            <el-button 
              v-if="isAdmin || isReviewer"
              type="primary" 
              size="small"
              @click="() => $router.push({ name: 'TeamApplications' })"
              class="team-app-button"
            >
              团队申请管理
              <!-- 添加团队申请数量徽章 -->
              <el-badge
                v-if="pendingApplicationsCount > 0"
                :value="pendingApplicationsCount"
                class="team-app-button-badge"
                type="danger"
                :max="99"
              />
            </el-button>
            <el-button 
              type="danger" 
              text 
              @click="handleLeaveTeam"
            >
              退出团队
            </el-button>
          </div>
        </div>
      </template>
      <div class="team-info">
        <h3>{{ currentTeam.name }}</h3>
        <p class="team-description">{{ currentTeam.description || '暂无团队介绍' }}</p>
        <div class="team-stats">
          <div class="stat-item">
            <div class="stat-label">成员数</div>
            <div class="stat-value">{{ currentTeam.memberCount || 0 }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">病例数</div>
            <div class="stat-value">{{ currentTeam.caseCount || 0 }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">创建时间</div>
            <div class="stat-value">{{ formatDate(currentTeam.createdAt) }}</div>
          </div>
        </div>
        <div class="team-actions">
          <el-button type="primary" @click="showTeamMembersDialog = true">查看团队成员</el-button>
        </div>
      </div>
    </el-card>

    <div v-else class="no-team-message">
      <el-empty description="您当前不属于任何团队" />
      <el-button type="primary" @click="showJoinTeamDialog = true">加入团队</el-button>
    </div>

    <!-- 添加团队已通过标注列表 -->
    <div v-if="currentTeam" class="team-annotations-section">
      <div class="section-header">
        <h3>团队已通过标注</h3>
        <div class="section-actions">
          <el-input
            v-model="searchQuery"
            placeholder="搜索病例"
            prefix-icon="el-icon-search"
            clearable
            style="width: 200px; margin-right: 10px;"
          />
          <el-select v-model="filterType" placeholder="标注类型" clearable style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="血管瘤" value="血管瘤" />
            <el-option label="其他" value="其他" />
          </el-select>
        </div>
      </div>
      
      <div v-loading="annotationsLoading">
        <el-table 
          v-if="teamAnnotations.length > 0" 
          :data="filteredAnnotations" 
          stripe 
          style="width: 100%"
          @row-click="handleViewAnnotation"
        >
          <el-table-column prop="caseNumber" label="病例号" width="120" />
          <el-table-column prop="uploadedByName" label="标注医生" width="120" />
          <el-table-column prop="lesionLocation" label="部位" width="120" />
          <el-table-column prop="tags" label="类型" width="120" />
          <el-table-column prop="formattedReviewDate" label="审核时间" width="180" />
          <el-table-column fixed="right" label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" @click.stop="handleViewAnnotation(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-else description="暂无已通过标注" />
        
        <div v-if="teamAnnotations.length > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            background
            layout="prev, pager, next"
            :total="filteredAnnotations.length"
            :page-size="10"
          />
        </div>
      </div>
    </div>

    <!-- 加入团队对话框 -->
    <el-dialog
      v-model="showJoinTeamDialog"
      title="加入团队"
      width="500px"
    >
      <el-form ref="joinTeamFormRef" :model="joinTeamForm" label-width="80px" :rules="joinTeamRules">
        <el-form-item label="团队代码" prop="teamCode">
          <el-select
            v-model="joinTeamForm.teamCode"
            filterable
            placeholder="请选择要加入的团队"
            style="width: 100%"
          >
            <el-option
              v-for="team in teamOptions"
              :key="team.code"
              :label="team.name"
              :value="team.code"
            >
              <div style="display: flex; flex-direction: column; gap: 4px; width: 100%">
                <div style="display: flex; justify-content: space-between; align-items: center">
                  <span style="font-weight: bold;">{{ team.name }}</span>
                  <span style="color: #8492a6; font-size: 13px">ID: {{ team.code }}</span>
                </div>
                <div style="font-size: 12px; color: #606266; white-space: normal; line-height: 1.3">
                  {{ team.description || '暂无描述' }}
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="加入理由" prop="reason">
          <el-input
            v-model="joinTeamForm.reason"
            type="textarea"
            placeholder="请简要说明加入原因"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showJoinTeamDialog = false">取消</el-button>
          <el-button type="primary" :loading="joinTeamLoading" @click="handleJoinTeam">
            提交申请
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建团队对话框 -->
    <el-dialog
      v-model="showCreateTeamDialog"
      title="创建团队"
      width="500px"
    >
      <el-form ref="createTeamFormRef" :model="createTeamForm" label-width="80px" :rules="createTeamRules">
        <el-form-item label="团队名称" prop="name">
          <el-input v-model="createTeamForm.name" placeholder="请输入团队名称"></el-input>
        </el-form-item>
        <el-form-item label="团队描述" prop="description">
          <el-input
            v-model="createTeamForm.description"
            type="textarea"
            placeholder="请输入团队描述"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateTeamDialog = false">取消</el-button>
          <el-button type="primary" :loading="createTeamLoading" @click="handleCreateTeam">
            创建团队
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 团队申请管理对话框 -->

    <!-- 添加团队成员对话框 -->
    <el-dialog
      v-model="showTeamMembersDialog"
      title="团队成员"
      width="800px"
      destroy-on-close
      :append-to-body="true"
      @open="fetchTeamMembers"
    >
      <div v-loading="membersLoading">
        <div class="debug-info" style="margin-bottom: 15px; padding: 10px; background-color: #f0f9eb; border-radius: 4px;">
          <p><strong>调试信息:</strong></p>
          <p>当前用户ID: {{ currentUser?.id }}</p>
          <p>团队所有者: {{ typeof currentTeam?.owner === 'object' ? currentTeam?.owner?.id : currentTeam?.owner }}</p>
          <p>是否为团队所有者: {{ isTeamOwner ? '是' : '否' }}</p>
        </div>
        <el-table v-if="teamMembers.length > 0" :data="teamMembers" stripe style="width: 100%">
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="role" label="角色">
            <template #default="scope">
              <el-tag :type="getRoleType(scope.row.role)">
                {{ getRoleName(scope.row.role) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="department" label="部门" />
          <el-table-column prop="hospital" label="医院" />
          <el-table-column label="操作" fixed="right" width="150">
            <template #default="scope">
              <el-button
                v-if="isTeamOwner && scope.row.id !== currentUser.id"
                type="primary"
                size="small"
                @click="handleTransferOwnership(scope.row)"
              >
                设为群主
              </el-button>
              <el-button
                v-if="isTeamOwner && scope.row.id !== currentUser.id"
                type="danger"
                size="small"
                @click="handleRemoveMember(scope.row.id)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-else description="暂无团队成员" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTeamMembersDialog = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import api from '@/utils/api'
import { useStore } from 'vuex'
import TeamApplicationManagement from '@/components/TeamApplicationManagement.vue'
import { useRouter } from 'vue-router'

export default {
  name: 'TeamsView',
  components: {
    TeamApplicationManagement
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // 当前用户角色
    const isAdmin = computed(() => store.getters.isAdmin)
    const isReviewer = computed(() => store.getters.isReviewer)
    const currentUser = computed(() => store.getters.getUser)
    
    // 获取申请数量
    const pendingApplicationsCount = computed(() => store.getters['teamApplications/getPendingApplicationsCount'])
    
    // 当前用户所在的团队
    const currentTeam = ref(null)
    // 团队成员列表
    const teamMembers = ref([])
    // 加入团队对话框可见性
    const showJoinTeamDialog = ref(false)
    // 加入团队表单
    const joinTeamForm = reactive({
      teamCode: '',
      reason: ''
    })
    // 表单校验规则
    const joinTeamRules = {
      teamCode: [
        { required: true, message: '请选择要加入的团队', trigger: 'change' }
      ],
      reason: [
        { required: true, message: '请输入加入理由', trigger: 'blur' },
        { min: 5, max: 200, message: '请输入5-200个字符', trigger: 'blur' }
      ]
    }
    // 表单引用
    const joinTeamFormRef = ref(null)
    // 加入团队加载状态
    const joinTeamLoading = ref(false)
    // 团队选项
    const teamOptions = ref([])
    // 搜索加载状态
    const loading = ref(false)
    // 创建团队对话框可见性
    const showCreateTeamDialog = ref(false)
    // 创建团队表单
    const createTeamForm = reactive({
      name: '',
      description: ''
    })
    // 表单校验规则
    const createTeamRules = {
      name: [
        { required: true, message: '请输入团队名称', trigger: 'blur' },
        { min: 2, max: 50, message: '团队名称长度在2-50个字符之间', trigger: 'blur' }
      ],
      description: [
        { required: true, message: '请输入团队描述', trigger: 'blur' },
        { min: 5, max: 200, message: '请输入5-200个字符', trigger: 'blur' }
      ]
    }
    // 表单引用
    const createTeamFormRef = ref(null)
    // 创建团队加载状态
    const createTeamLoading = ref(false)
    // 团队申请管理对话框
    const showTeamApplicationDialog = ref(false);
    // 添加团队成员对话框可见性
    const showTeamMembersDialog = ref(false);
    // 团队成员加载状态
    const membersLoading = ref(false);
    // 搜索查询
    const searchQuery = ref('');
    // 过滤类型
    const filterType = ref('');
    // 团队已通过标注列表
    const teamAnnotations = ref([]);
    // 已通过标注加载状态
    const annotationsLoading = ref(false);
    // 当前页码
    const currentPage = ref(1);

    const isTeamOwner = computed(() => {
      if (!currentUser.value || !currentTeam.value) {
        return false;
      }
      
      // 处理owner可能是对象或ID的情况
      if (currentTeam.value.owner) {
        if (typeof currentTeam.value.owner === 'object') {
          // owner是对象的情况
          return currentUser.value.id === currentTeam.value.owner.id;
        } else {
          // owner是ID的情况
          return currentUser.value.id === currentTeam.value.owner;
        }
      }
      
      return false;
    })

    // 获取当前用户的团队信息
    const fetchUserTeam = async () => {
      try {
        // 从localStorage获取用户信息
        const userStr = localStorage.getItem('user');
        if (!userStr) {
          console.log('未找到用户信息');
          currentTeam.value = null;
          return;
        }
        
        const user = JSON.parse(userStr);
        console.log('当前用户:', user);
        
        if (user && user.id) {
          try {
            // 先尝试通过API获取最新的用户信息（包括团队）
            console.log('尝试从API获取用户的最新团队信息');
            const response = await api.users.getUser(user.id);
            
            if (response && response.data && response.data.team) {
              console.log('成功从API获取用户团队信息:', response.data.team);
              console.log('团队owner数据类型:', typeof response.data.team.owner);
              console.log('团队owner值:', response.data.team.owner);
              
              // 检查是否有循环引用标记，如果有则进一步请求团队详情
              if (response.data.team._isCircularRef || response.data.team._simplified) {
                console.log('团队数据被简化处理，尝试获取完整团队信息');
                try {
                  const teamResponse = await api.teams.getOne(response.data.team.id);
                  if (teamResponse && teamResponse.data) {
                    // 设置完整的团队信息
                    currentTeam.value = {
                      ...teamResponse.data
                    };
                  } else {
                    // 使用简化版本
                    currentTeam.value = {
                      ...response.data.team
                    };
                  }
                } catch (teamError) {
                  console.error('获取完整团队信息失败:', teamError);
                  // 使用简化版本
                  currentTeam.value = {
                    ...response.data.team
                  };
                }
              } else {
                // 直接使用响应中的团队信息
                currentTeam.value = {
                  ...response.data.team
                };
              }
              
          // 获取团队成员
          fetchTeamMembers();
        } else {
          // 用户没有团队，这是正常情况，不显示错误
          console.log('用户当前不属于任何团队');
              currentTeam.value = null;
              
              // 确保清除localStorage中的团队信息
              if (user.team) {
                console.log('清除localStorage中的团队信息');
                delete user.team;
                localStorage.setItem('user', JSON.stringify(user));
              }
            }
          } catch (error) {
            console.error('API获取用户团队信息失败:', error);
            // 由于API调用失败，我们不能确定用户是否有团队，设为null
            currentTeam.value = null;
          }
        } else {
          console.log('用户数据不完整，无法获取团队信息');
          currentTeam.value = null;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        currentTeam.value = null;
      }
    }

    // 获取团队成员列表
    const fetchTeamMembers = async () => {
      if (!currentTeam.value || !currentTeam.value.id) return;
      
      try {
        membersLoading.value = true;
        console.log('获取团队成员，团队ID:', currentTeam.value.id);
        const response = await api.teams.getTeamMembers(currentTeam.value.id);
        console.log('团队成员API返回数据:', response);
        
        // 检查返回数据是否为数组
        if (response.data && Array.isArray(response.data)) {
          // 使用数据库返回的真实团队成员数据
          console.log('使用数据库返回的真实团队成员数据，成员数量:', response.data.length);
          
          // 处理返回的有效数组数据
          teamMembers.value = response.data.map(member => ({
            ...member,
            joinDate: formatDate(member.joinDate)
          })).sort((a, b) => {
            // 定义角色优先级：ADMIN > REVIEWER > DOCTOR
            const rolePriority = {
              'ADMIN': 1,
              'REVIEWER': 2,
              'DOCTOR': 3
            };
            
            // 按角色优先级排序
            return (rolePriority[a.role] || 99) - (rolePriority[b.role] || 99);
          });
          
          // 更新团队成员数量
          if (currentTeam.value) {
            currentTeam.value.memberCount = teamMembers.value.length;
            console.log('更新团队成员数量:', currentTeam.value.memberCount);
          }
        } else {
          // 处理非数组响应
          console.error('团队成员数据不是数组:', response.data);
          teamMembers.value = [];
          
          // 更新团队成员数量为0
          if (currentTeam.value) {
            currentTeam.value.memberCount = 0;
          }
        }
      } catch (error) {
        console.error('获取团队成员失败:', error);
        teamMembers.value = [];
        ElMessage.error('获取团队成员失败，请稍后重试');
        
        // 更新团队成员数量为0
        if (currentTeam.value) {
          currentTeam.value.memberCount = 0;
        }
      } finally {
        membersLoading.value = false;
      }
    }

    // 处理加入团队
    const handleJoinTeam = async () => {
      // 表单验证
      if (!joinTeamFormRef.value) return;
      
      try {
        await joinTeamFormRef.value.validate();
        
        joinTeamLoading.value = true;
        
        // 获取选中的团队ID
        const selectedTeamId = joinTeamForm.teamCode;
        
        console.log(`尝试加入团队ID: ${selectedTeamId}`);
        
        try {
          // 从localStorage获取用户信息
          const userStr = localStorage.getItem('user');
          if (!userStr) {
            console.warn('未找到用户信息');
            ElMessage.error('未找到用户信息，请重新登录');
            joinTeamLoading.value = false;
            return;
          }
          
          const user = JSON.parse(userStr);
          
          if (user) {
            // 尝试提交加入申请
            await api.teams.applyToJoinTeam(
              selectedTeamId, 
              joinTeamForm.reason
            );
            
            // 申请成功
            ElMessage.success('已成功提交加入申请，请等待审核');
            joinTeamForm.teamCode = '';
            joinTeamForm.reason = '';
            showJoinTeamDialog.value = false;
            
            // 刷新用户团队信息
            fetchUserTeam();
          } else {
            ElMessage.error('用户信息不完整，请重新登录');
          }
        } catch (error) {
          console.error('申请加入团队失败:', error);
          ElMessage.error(error.response?.data?.message || '申请加入团队失败');
        } finally {
          joinTeamLoading.value = false;
        }
      } catch (error) {
        console.error('表单验证失败:', error);
        joinTeamLoading.value = false;
      }
    }

    // 处理退出团队
    const handleLeaveTeam = () => {
      ElMessageBox.confirm(
        '确定要退出当前团队吗？退出后需要重新申请加入。',
        '退出团队',
        {
          confirmButtonText: '确定退出',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          // 从localStorage获取用户信息
          const userStr = localStorage.getItem('user');
          if (!userStr) {
            console.warn('未找到用户信息');
            ElMessage.error('未找到用户信息，请重新登录');
            return;
          }
          
          const user = JSON.parse(userStr);
          
          if (user && user.team && currentTeam.value) {
            await api.teams.removeTeamMember(currentTeam.value.id, user.id);
            ElMessage.success('已成功退出团队');
            currentTeam.value = null;
            teamMembers.value = [];
          } else {
            ElMessage.error('您当前不在任何团队中');
          }
        } catch (error) {
          console.error('退出团队失败:', error);
          ElMessage.error('退出团队失败: ' + (error.response?.data?.message || error.message));
        }
      }).catch(() => {
        // 用户取消操作
      });
    }

    // 获取角色类型（样式）
    const getRoleType = (role) => {
      const types = {
        'ADMIN': 'danger',
        'DOCTOR': 'primary',
        'REVIEWER': 'success'
      }
      return types[role] || 'info'
    }

    // 获取角色名称
    const getRoleName = (role) => {
      const names = {
        'ADMIN': '管理员',
        'DOCTOR': '标注医生',
        'REVIEWER': '审核医生'
      }
      return names[role] || '未知'
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      try {
        const date = new Date(dateString)
        if (isNaN(date.getTime())) return '未知'
        return date.toLocaleString('zh-CN')
      } catch (e) {
        return '未知'
      }
    }

    // 搜索团队
    const searchTeams = async () => {
      try {
        loading.value = true;
        // 尝试使用API获取团队列表
        try {
          const response = await api.teams.getAll();
          
          if (response && response.data && response.data.length > 0) {
            teamOptions.value = response.data.map(team => ({
              code: team.id.toString(),
              name: team.name,
              description: team.description || '暂无描述'
            }));
            console.log('成功获取到团队列表:', teamOptions.value);
            return;
          }
        } catch (error) {
          console.error('API获取团队列表失败，将使用模拟数据:', error);
        }
        
        // 如果API调用失败，使用模拟数据
        console.log('使用模拟团队数据');
        teamOptions.value = [
          { code: '1', name: '管理员团队', description: '系统管理团队' },
          { code: '5', name: '第一测试', description: '仅限于测试' },
          { code: '6', name: '第二测试团队', description: '仅限于测试' }
        ];
      } catch (error) {
        console.error('获取团队列表完全失败:', error);
        ElMessage.error('获取团队列表失败，已使用默认数据');
        
        // 确保即使在错误情况下也有一些选项
        teamOptions.value = [
          { code: '1', name: '默认团队', description: '系统默认团队' }
        ];
      } finally {
        loading.value = false;
      }
    };

    // 监听对话框打开，加载团队数据
    watch(showJoinTeamDialog, (newVal) => {
      if (newVal) {
        searchTeams().catch(err => {
          console.error('加载团队列表失败:', err);
          // 提供空的团队列表，防止界面崩溃
          teamOptions.value = [];
        });
      }
    });

    // 组件挂载时获取团队信息
    onMounted(() => {
      // 尝试获取用户团队信息，但不会在失败时显示错误
      fetchUserTeam().then(() => {
        // 如果有团队，获取团队已通过标注
        if (currentTeam.value && currentTeam.value.id) {
          fetchTeamAnnotations();
        }
      }).catch(err => {
        console.error('获取用户团队信息失败，但不影响界面显示:', err);
      });
    });

    // 处理创建团队
    const handleCreateTeam = async () => {
      // 表单验证
      if (!createTeamFormRef.value) return;
      
      try {
        await createTeamFormRef.value.validate();
        
        createTeamLoading.value = true;
        
        // 确保用户已登录
        const userStr = localStorage.getItem('user');
        if (!userStr) {
          console.warn('未找到用户信息');
          ElMessage.error('未找到用户信息，请重新登录');
          createTeamLoading.value = false;
          return;
        }
        
        const user = JSON.parse(userStr);
        
        // 提交创建团队请求
        await api.teams.create({
          name: createTeamForm.name, 
          description: createTeamForm.description
        });
        
        // 创建成功后，重置表单并关闭对话框
        createTeamForm.name = '';
        createTeamForm.description = '';
        showCreateTeamDialog.value = false;
        ElMessage.success('团队创建成功');
        
        // 刷新用户团队信息
        fetchUserTeam();
      } catch (error) {
        if (error.name === 'ValidationError') {
          // 表单验证失败
          return;
        }
        
        console.error('创建团队失败:', error);
        ElMessage.error('创建团队失败: ' + (error.response?.data?.message || error.message));
      } finally {
        createTeamLoading.value = false;
      }
    };

    // 显示团队申请管理
    const showTeamApplications = () => {
      if (!currentTeam.value || !currentTeam.value.id) {
        ElMessage.warning('请先选择一个团队');
        return;
      }
      
      // 检查用户登录状态
      const userStr = localStorage.getItem('user');
      if (!userStr) {
        ElMessage.warning('请先登录后再操作');
        // 保存当前路径，以便登录后返回
        sessionStorage.setItem('redirectAfterLogin', window.location.pathname);
        // 跳转到登录页面
        window.location.href = '/login';
        return;
      }
      
      try {
        // 刷新用户信息，确保当前会话有效
        const user = JSON.parse(userStr);
        console.log('当前用户:', user);
        
        // 确认用户角色
        if (!(isAdmin.value || isReviewer.value)) {
          ElMessage.warning('只有管理员和审核医生可以管理团队申请');
          return;
        }
        
        showTeamApplicationDialog.value = true;
      } catch (e) {
        console.error('解析用户信息失败:', e);
        ElMessage.error('获取用户信息失败，请重新登录');
      }
    };

    // 获取已通过标注列表
    const fetchTeamAnnotations = async () => {
      if (!currentTeam.value || !currentTeam.value.id) return;
      
      try {
        annotationsLoading.value = true;
        console.log('获取团队已通过标注，团队ID:', currentTeam.value.id);
        const response = await api.teams.getTeamAnnotations(currentTeam.value.id);
        console.log('团队已通过标注API返回数据:', response);
        
        if (response && response.data && Array.isArray(response.data)) {
          teamAnnotations.value = response.data.map(annotation => {
            // 调试审核时间
            console.log(`标注ID ${annotation.id} 的审核时间:`, annotation.reviewDate);
            
            return {
              ...annotation,
              formattedReviewDate: formatDate(annotation.reviewDate)
            };
          });
          console.log('成功获取到团队已通过标注:', teamAnnotations.value);
        } else {
          console.error('团队已通过标注数据不是数组:', response.data);
          teamAnnotations.value = [];
        }
      } catch (error) {
        console.error('获取团队已通过标注失败:', error);
        teamAnnotations.value = [];
        ElMessage.error('获取团队已通过标注失败，请稍后重试');
      } finally {
        annotationsLoading.value = false;
      }
    };

    // 过滤已通过标注列表
    const filteredAnnotations = computed(() => {
      if (!teamAnnotations.value) {
        return [];
      }
      
      let filtered = teamAnnotations.value;

      if (searchQuery.value) {
        filtered = filtered.filter(annotation => {
          const searchTerm = searchQuery.value.toLowerCase();
          return (
            annotation.caseNumber?.toLowerCase().includes(searchTerm) ||
            annotation.uploadedByName?.toLowerCase().includes(searchTerm) ||
            annotation.lesionLocation?.toLowerCase().includes(searchTerm) ||
            annotation.tags?.toLowerCase().includes(searchTerm)
          );
        });
      }

      if (filterType.value) {
        filtered = filtered.filter(annotation => 
          annotation.tags?.includes(filterType.value)
        );
      }
      
      return filtered;
    });

    // 查看已通过标注
    const handleViewAnnotation = (annotation) => {
      if (annotation && annotation.id) {
        console.log('导航到病例详情页, ID:', annotation.id);
        router.push({ name: 'ViewCase', params: { id: annotation.id } });
      } else {
        console.error('无法查看标注，缺少有效ID', annotation);
        ElMessage.error('无法查看病例，缺少有效ID');
      }
    };

    // 根据状态返回标签类型
    const getStatusType = (status) => {
      const types = {
        '待标注': 'info',
        '已标注': 'success',
        '审核中': 'warning',
        '已通过': 'success',
        '已驳回': 'danger'
      }
      return types[status] || 'info'
    };

    const handleDisbandTeam = () => {
      ElMessageBox.confirm(
        '此操作将永久解散该团队，所有成员将被移除。请确认所有重要数据已备份。是否继续?',
        '警告：解散团队',
        {
          confirmButtonText: '确认解散',
          cancelButtonText: '取消',
          type: 'error',
        }
      ).then(async () => {
        try {
          await api.teams.deleteTeam(currentTeam.value.id)
          ElMessage.success('团队已成功解散')
          fetchUserTeam() // 重新获取团队信息，此时应为空
        } catch (error) {
          ElMessage.error(error.response?.data?.message || '解散团队失败')
        }
      }).catch(() => {
        // 用户取消操作
      })
    }

    const handleTransferOwnership = (newOwner) => {
      ElMessageBox.confirm(
        `您确定要将团队所有权转让给【${newOwner.name}】吗？此操作不可撤销。`,
        '警告：转让所有权',
        {
          confirmButtonText: '确认转让',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        try {
          await api.teams.transferOwnership(currentTeam.value.id, newOwner.id)
          ElMessage.success('团队所有权已成功转让')
          fetchUserTeam() // 重新获取团队信息以更新owner
          fetchTeamMembers() // 重新获取成员列表以更新UI
        } catch (error) {
          ElMessage.error(error.response?.data?.message || '转让所有权失败')
        }
      }).catch(() => {
        // 用户取消操作
      })
    }

    return {
      currentTeam,
      teamMembers,
      showJoinTeamDialog,
      joinTeamForm,
      joinTeamRules,
      joinTeamFormRef,
      joinTeamLoading,
      handleJoinTeam,
      handleLeaveTeam,
      getRoleType,
      getRoleName,
      formatDate,
      teamOptions,
      loading,
      searchTeams,
      showCreateTeamDialog,
      createTeamForm,
      createTeamRules,
      createTeamFormRef,
      createTeamLoading,
      handleCreateTeam,
      isAdmin,
      isReviewer,
      showTeamApplicationDialog,
      showTeamApplications,
      showTeamMembersDialog,
      membersLoading,
      fetchTeamMembers,
      searchQuery,
      filterType,
      teamAnnotations,
      annotationsLoading,
      currentPage,
      filteredAnnotations,
      handleViewAnnotation,
      getStatusType,
      isTeamOwner,
      handleDisbandTeam,
      handleTransferOwnership,
      pendingApplicationsCount,
    }
  }
}
</script>

<style scoped>
.teams-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.current-team-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.team-header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-right: 10px;
  justify-content: flex-end;
}

.team-info {
  padding: 10px 0;
}

.team-info h3 {
  margin-top: 0;
  color: #303133;
}

.team-description {
  color: #606266;
  margin-bottom: 20px;
}

.team-stats {
  display: flex;
  justify-content: space-between;
  max-width: 500px;
}

.stat-item {
  text-align: center;
  padding: 0 20px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.team-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}

.no-team-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.no-team-message .el-button {
  margin-top: 20px;
}

.team-annotations-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.case-view-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;
}

.annotated-image-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.refresh-info {
  color: #909399;
  font-size: 12px;
}

.annotated-image-container {
  width: 100%;
  max-width: 300px;
  height: auto;
  overflow: visible;
  border-radius: 4px;
  border: 1px solid #eee;
  margin: 0 auto;
  box-shadow: 0 2px 4px rgba(0,0,0,.05);
  padding-bottom: 10px;
}

.annotated-image {
  width: 100%;
  max-width: 300px;
  height: auto;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.detailed-info-card {
  margin-top: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.case-info {
  margin-bottom: 10px;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 150px;
  background-color: #f5f7fa;
  color: #909399;
}

.image-error i {
  font-size: 48px;
  color: #909399;
  margin-bottom: 10px;
}

.image-error p {
  font-size: 14px;
  color: #909399;
}

.team-app-button {
  position: relative;
  padding-right: 15px;
}

.team-app-button-badge {
  position: absolute;
  top: -8px;
  right: -8px;
}

.team-app-button-badge .el-badge__content {
  height: 18px;
  min-width: 18px;
  line-height: 18px;
  padding: 0 6px;
  border-radius: 9px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 0 0 1px #fff;
}
</style> 

