<!DOCTYPE html>
<html>
<head>
    <title>API测试工具</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .result {
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            background-color: #f5f5f5;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        input[type="text"] {
            padding: 8px;
            width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API测试工具</h1>
        
        <div>
            <h2>获取图像元数据</h2>
            <input type="text" id="imageId" value="941231575" placeholder="输入图像ID">
            <button onclick="getImageMetadata()">测试</button>
            <div class="result" id="metadataResult"></div>
        </div>

        <div>
            <h2>获取图像对</h2>
            <input type="text" id="imagePairId" value="941231575" placeholder="输入图像ID">
            <button onclick="getImagePair()">测试</button>
            <div class="result" id="pairResult"></div>
        </div>
        
        <div>
            <h2>获取图像</h2>
            <input type="text" id="imagePath" value="/medical/images/temp/temp_IH_009_032.jpg" placeholder="输入图像路径">
            <button onclick="testImageAccess()">测试</button>
            <div>
                <img id="resultImage" style="max-width:100%; margin-top:10px; display:none;">
            </div>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8085'; // 替换为实际的服务器URL
        
        async function getImageMetadata() {
            const imageId = document.getElementById('imageId').value;
            const resultDiv = document.getElementById('metadataResult');
            resultDiv.innerHTML = '加载中...';
            
            try {
                const response = await fetch(`${baseUrl}/medical/api/images/${imageId}`);
                const data = await response.json();
                resultDiv.innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.innerHTML = `错误: ${error.message}`;
            }
        }
        
        async function getImagePair() {
            const imageId = document.getElementById('imagePairId').value;
            const resultDiv = document.getElementById('pairResult');
            resultDiv.innerHTML = '加载中...';
            
            try {
                const response = await fetch(`${baseUrl}/medical/api/image-pairs/metadata/${imageId}`);
                const data = await response.json();
                resultDiv.innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.innerHTML = `错误: ${error.message}`;
            }
        }
        
        async function testImageAccess() {
            const imagePath = document.getElementById('imagePath').value;
            const imgElement = document.getElementById('resultImage');
            
            // 添加时间戳防止缓存
            const url = baseUrl + imagePath + '?t=' + new Date().getTime();
            imgElement.src = url;
            imgElement.style.display = 'block';
            
            // 监听加载事件
            imgElement.onload = function() {
                console.log('图像加载成功');
            };
            
            imgElement.onerror = function() {
                console.error('图像加载失败');
                alert('图像加载失败，请检查路径');
            };
        }
    </script>
</body>
</html> 