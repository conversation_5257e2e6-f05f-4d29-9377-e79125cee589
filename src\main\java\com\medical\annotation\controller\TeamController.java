package com.medical.annotation.controller;

import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.model.ImagePair;
import com.medical.annotation.model.Team;
import com.medical.annotation.model.User;
import com.medical.annotation.model.Tag;
import com.medical.annotation.repository.UserRepository;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.service.TeamService;
import com.medical.annotation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/teams")
public class TeamController {

    @Autowired
    private TeamService teamService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TagRepository tagRepository;
    
    /**
     * 创建团队
     */
    @PostMapping
    public ResponseEntity<?> createTeam(
            @RequestBody Team team,
            @RequestParam(value = "userId", required = false) Integer userIdParam) {
        try {
            Integer userId = userIdParam; // 首先使用请求参数中的userId
            
            // 如果请求参数中没有userId，尝试从SecurityContext获取当前用户
            if (userId == null) {
            try {
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                if (auth != null && auth.getName() != null && !auth.getName().equals("anonymousUser")) {
                    User currentUser = userService.getUserByEmail(auth.getName()).orElse(null);
                    if (currentUser != null) {
                        userId = currentUser.getId();
                    }
                }
            } catch (Exception e) {
                System.out.println("无法从认证上下文获取用户: " + e.getMessage());
                }
            }
            
            // 如果无法获取用户ID，使用默认管理员ID
            if (userId == null) {
                // 尝试查找系统中的第一个管理员用户
                List<User> admins = userRepository.findByRole(User.Role.ADMIN);
                if (!admins.isEmpty()) {
                    userId = admins.get(0).getId();
                } else {
                    // 如果没有管理员，使用ID为1的用户作为备选
                    userId = 1;
                }
            }
            
            System.out.println("创建团队使用的用户ID: " + userId);
            Team createdTeam = teamService.createTeam(team, userId);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(createdTeam);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 更新团队信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateTeam(@PathVariable("id") Integer id, @RequestBody Team team) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            team.setId(id);
            Team updatedTeam = teamService.updateTeam(team, currentUser.getId());
            
            return ResponseEntity.ok(updatedTeam);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 删除团队
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteTeam(@PathVariable Integer id, @RequestHeader("X-User-ID") Integer operatorId) {
        try {
            teamService.deleteTeam(id, operatorId);
            return ResponseEntity.ok().body(Map.of("message", "团队已成功解散"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        }
    }
    
    /**
     * 获取团队信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getTeam(@PathVariable("id") Integer id) {
        try {
            Team team = teamService.getTeamById(id);
            return ResponseEntity.ok(team);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取所有团队
     */
    @GetMapping
    public ResponseEntity<?> getAllTeams(@RequestParam(value = "keyword", required = false) String keyword) {
        try {
            List<Team> teams;
            if (keyword != null && !keyword.isEmpty()) {
                teams = teamService.searchTeams(keyword);
            } else {
                teams = teamService.getAllTeams();
            }
            
            // 转换为简单格式
            List<Map<String, Object>> result = teams.stream().map(team -> {
                Map<String, Object> teamMap = new HashMap<>();
                teamMap.put("id", team.getId());
                teamMap.put("name", team.getName());
                teamMap.put("description", team.getDescription());
                return teamMap;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取团队成员
     */
    @GetMapping("/{id}/members")
    public ResponseEntity<?> getTeamMembers(@PathVariable("id") Integer id) {
        try {
            List<User> members = teamService.getTeamMembers(id);
            return ResponseEntity.ok(members);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 添加团队成员（新接口，支持通过请求体传递用户ID）
     */
    @PostMapping("/{id}/members")
    public ResponseEntity<?> addTeamMemberByBody(
            @PathVariable("id") Integer id,
            @RequestBody Map<String, Object> requestBody) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            // 从请求体中获取userId
            Object userIdObj = requestBody.get("userId");
            if (userIdObj == null) {
                throw new Exception("用户ID不能为空");
            }
            
            Integer userId;
            try {
                userId = Integer.valueOf(userIdObj.toString());
            } catch (NumberFormatException e) {
                throw new Exception("无效的用户ID格式");
            }
            
            teamService.addUserToTeam(id, userId, currentUser.getId());
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "用户已成功添加到团队");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 移除团队成员
     */
    @DeleteMapping("/{id}/members/{userId}")
    public ResponseEntity<?> removeTeamMember(@PathVariable("id") Integer id, @PathVariable("userId") Integer userId) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(auth.getName()).orElseThrow(() -> new Exception("用户不存在"));
            
            teamService.removeUserFromTeam(id, userId, currentUser.getId());
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "用户已成功从团队移除");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 搜索团队
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchTeams(@RequestParam("query") String query) {
        try {
            if (query == null || query.trim().isEmpty()) {
                return ResponseEntity.ok(teamService.getAllTeams());
            }
            
            List<Team> teams = teamService.searchTeams(query.trim());
            
            // 转换为前端需要的格式
            List<Map<String, Object>> result = teams.stream().map(team -> {
                Map<String, Object> teamMap = new HashMap<>();
                teamMap.put("id", team.getId());
                teamMap.put("name", team.getName());
                teamMap.put("code", team.getId().toString()); // 使用ID作为团队代码
                teamMap.put("description", team.getDescription());
                teamMap.put("memberCount", userRepository.countByTeam_Id(team.getId()));
                return teamMap;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 获取团队已通过的诊断记录
     */
    @GetMapping("/{id}/annotations/approved")
    public ResponseEntity<?> getTeamApprovedAnnotations(@PathVariable("id") Integer id) {
        try {
            List<HemangiomaDiagnosis> approvedDiagnoses = teamService.getTeamApprovedAnnotations(id);
            
            System.out.println("获取到已通过诊断数量: " + approvedDiagnoses.size());
            
            // 转换为包含必要信息的格式
            List<Map<String, Object>> result = approvedDiagnoses.stream().map(diagnosis -> {
                Map<String, Object> item = new HashMap<>();
                item.put("id", diagnosis.getId());
                
                // 生成病例号
                item.put("caseNumber", "CASE-" + diagnosis.getId());
                
                // 上传者信息
                item.put("uploadedByName", diagnosis.getUser() != null ? diagnosis.getUser().getName() : "未知用户");
                
                // 部位
                item.put("lesionLocation", diagnosis.getBodyPart());
                
                // 获取并组合标签
                List<Tag> tags = diagnosis.getTags();
                String tagNames = tags.stream().map(Tag::getTagName).collect(Collectors.joining(", "));
                item.put("tags", tagNames);
                
                // 使用创建时间作为审核时间
                if (diagnosis.getCreatedAt() != null) {
                    item.put("reviewDate", diagnosis.getCreatedAt().toString());
                    item.put("formattedReviewDate", diagnosis.getCreatedAt().toString());
                } else {
                    item.put("reviewDate", "");
                    item.put("formattedReviewDate", "");
                }
                
                // 获取图像路径
                item.put("imagePath", diagnosis.getImagePath());
                item.put("imageTwoPath", ""); // The concept of a second image is removed with ImagePair
                
                return item;
            }).collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    @PostMapping("/{id}/transfer-ownership")
    public ResponseEntity<?> transferOwnership(@PathVariable Integer id, @RequestBody Map<String, Integer> payload, @RequestHeader("X-User-ID") Integer operatorId) {
        try {
            Integer newOwnerId = payload.get("newOwnerId");
            if (newOwnerId == null) {
                return ResponseEntity.badRequest().body(Map.of("message", "必须提供新所有者的ID"));
            }
            teamService.transferOwnership(id, newOwnerId, operatorId);
            return ResponseEntity.ok().body(Map.of("message", "团队所有权已成功转让"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        }
    }
} 