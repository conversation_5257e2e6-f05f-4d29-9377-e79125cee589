package com.medical.annotation.controller;

import com.medical.annotation.model.Tag;
import com.medical.annotation.model.HemangiomaDiagnosis;
import com.medical.annotation.repository.TagRepository;
import com.medical.annotation.repository.HemangiomaDiagnosisRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.File;
import com.medical.annotation.service.FileService;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.awt.Graphics2D;
import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import javax.imageio.ImageIO;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping({"/api/tags", "/medical/api/tags", "/tags"})
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class TagController {

    @Autowired
    private TagRepository tagRepository;
    
    @Autowired
    private HemangiomaDiagnosisRepository hemangiomaDiagnosisRepository;

    @Autowired
    private FileService fileService;

    /**
     * 获取指定诊断ID的标签信息
     */
    @GetMapping({"/image/{id}", "image/{id}"})
    public ResponseEntity<?> getTagsForImage(@PathVariable Integer id) {
        try {
            System.out.println("查询图像ID " + id + " 的标签");
            List<Tag> tags = new ArrayList<>();
            
            // 首先按照hemangioma_diagnosis_id查询
            List<Tag> diagnosisTags = tagRepository.findByHemangiomaDiagnosisId(id);
            if (diagnosisTags != null && !diagnosisTags.isEmpty()) {
                tags.addAll(diagnosisTags);
                System.out.println("通过hemangiomaDiagnosisId找到 " + diagnosisTags.size() + " 个标签");
            }
            
            // 然后按照hemangioma_id查询
            List<Tag> hemangiomaTags = tagRepository.findByHemangioma_id(id.longValue());
            if (hemangiomaTags != null && !hemangiomaTags.isEmpty()) {
                // 关键修复：添加前进行去重
                List<Tag> uniqueHemangiomaTags = hemangiomaTags.stream()
                    .filter(hTag -> tags.stream().noneMatch(t -> t.getId().equals(hTag.getId())))
                    .collect(Collectors.toList());
                tags.addAll(uniqueHemangiomaTags);
                System.out.println("通过hemangioma_id找到 " + uniqueHemangiomaTags.size() + " 个独特标签");
            }
            
            // 最后按照metadata_id查询
            List<Tag> metadataTags = tagRepository.findByMetadataId(id);
            if (metadataTags != null && !metadataTags.isEmpty()) {
                // 过滤掉可能已经通过其他方式查询到的相同标签
                List<Tag> uniqueMetadataTags = metadataTags.stream()
                    .filter(mTag -> tags.stream().noneMatch(t -> t.getId().equals(mTag.getId())))
                    .collect(Collectors.toList());
                tags.addAll(uniqueMetadataTags);
                System.out.println("通过metadata_id找到 " + uniqueMetadataTags.size() + " 个独特标签");
            }
            
            System.out.println("共找到 " + tags.size() + " 个标签");
            return ResponseEntity.ok(tags);
        } catch (Exception e) {
            System.err.println("获取标签时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("获取标签失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建新标签
     */
    @PostMapping(value = {"", "/"}, consumes = {MediaType.APPLICATION_JSON_VALUE, "application/json;charset=UTF-8"})
    public ResponseEntity<?> createTag(@RequestBody Map<String, Object> requestBody, HttpServletRequest request) {
        try {
            System.out.println("开始创建新标签，原始请求数据: " + requestBody);
            
            // 将请求数据转换为Tag对象
            Tag tag = new Tag();
            
            // 设置基本属性
            if (requestBody.containsKey("tag")) {
                tag.setTag((String) requestBody.get("tag"));
            }
            
            // 处理坐标数据
            if (requestBody.containsKey("x")) {
                tag.setX(parseDouble(requestBody.get("x")));
            }
            
            if (requestBody.containsKey("y")) {
                tag.setY(parseDouble(requestBody.get("y")));
            }
            
            if (requestBody.containsKey("width")) {
                tag.setWidth(parseDouble(requestBody.get("width")));
            }
            
            if (requestBody.containsKey("height")) {
                tag.setHeight(parseDouble(requestBody.get("height")));
            }
            
            // 处理metadata_id字段
            if (requestBody.containsKey("metadata_id")) {
                Integer metadataId = parseInteger(requestBody.get("metadata_id"));
                tag.setMetadata_id(metadataId);
                
                // 同时设置hemangioma_id
                if (metadataId != null) {
                    tag.setHemangioma_id(metadataId.longValue());
                    System.out.println("已将hemangioma_id设置为与metadata_id相同的值: " + metadataId);
                }
            }
            
            // 处理created_by字段
            Long createdBy = null;
            
            // 1. 先从请求体获取
            if (requestBody.containsKey("created_by")) {
                createdBy = parseLong(requestBody.get("created_by"));
                System.out.println("从请求体中获取created_by: " + createdBy);
            }
            
            // 2. 如果请求体中没有，则从请求头获取
            if (createdBy == null) {
                String userIdHeader = request.getHeader("X-User-Id");
                if (userIdHeader != null && !userIdHeader.isEmpty()) {
                    try {
                        createdBy = Long.parseLong(userIdHeader);
                        System.out.println("从请求头获取用户ID: " + createdBy);
                    } catch (NumberFormatException e) {
                        System.out.println("请求头中的用户ID无法解析为Long: " + userIdHeader);
                    }
                }
            }
            
            // 设置创建者ID
            if (createdBy != null) {
                tag.setCreatedBy(createdBy);
                System.out.println("最终设置createdBy = " + createdBy);
            } else {
                System.out.println("警告: 未能获取到有效的创建者ID");
            }
            
            // 设置创建时间
            tag.setCreatedAt(LocalDateTime.now());
            
            // 打印最终标签数据，用于调试
            System.out.println("准备保存的标签数据: " + 
                             "tag=" + tag.getTag() + 
                             ", metadata_id=" + tag.getMetadata_id() + 
                             ", hemangioma_id=" + tag.getHemangioma_id() + 
                             ", createdBy=" + tag.getCreatedBy());
            
            // 保存标签
            Tag savedTag = tagRepository.save(tag);
            System.out.println("标签保存成功，ID: " + savedTag.getId() + 
                              ", createdBy: " + savedTag.getCreatedBy() + 
                              ", hemangioma_id: " + savedTag.getHemangioma_id());
            
            return ResponseEntity.status(HttpStatus.CREATED).body(savedTag);
        } catch (Exception e) {
            System.err.println("创建标签时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("创建标签失败: " + e.getMessage());
        }
    }
    
    // 辅助方法：安全解析Double
    private Double parseDouble(Object value) {
        if (value == null) return null;
        if (value instanceof Double) return (Double) value;
        if (value instanceof Number) return ((Number) value).doubleValue();
        if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    // 辅助方法：安全解析Integer
    private Integer parseInteger(Object value) {
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Number) return ((Number) value).intValue();
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    // 辅助方法：安全解析Long
    private Long parseLong(Object value) {
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Number) return ((Number) value).longValue();
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 更新标签
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateTag(@PathVariable Long id, @RequestBody Tag tagUpdate) {
        try {
            System.out.println("更新标签，ID: " + id);
            
            Optional<Tag> existingTagOpt = tagRepository.findById(id);
            if (!existingTagOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            Tag existingTag = existingTagOpt.get();
            
            // 更新字段
            if (tagUpdate.getTagName() != null) {
                existingTag.setTagName(tagUpdate.getTagName());
                existingTag.setTag(tagUpdate.getTagName()); // 同步更新旧字段
            }
            
            if (tagUpdate.getX() != null) existingTag.setX(tagUpdate.getX());
            if (tagUpdate.getY() != null) existingTag.setY(tagUpdate.getY());
            if (tagUpdate.getWidth() != null) existingTag.setWidth(tagUpdate.getWidth());
            if (tagUpdate.getHeight() != null) existingTag.setHeight(tagUpdate.getHeight());
            
            // 设置更新时间
            existingTag.setUpdatedAt(LocalDateTime.now());
            
            // 保存更新
            Tag updatedTag = tagRepository.save(existingTag);
            System.out.println("标签更新成功");
            
            return ResponseEntity.ok(updatedTag);
        } catch (Exception e) {
            System.err.println("更新标签时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("更新标签失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除标签
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteTag(@PathVariable Long id) {
        try {
            System.out.println("删除标签，ID: " + id);
            
            if (!tagRepository.existsById(id)) {
                return ResponseEntity.notFound().build();
            }
            
            tagRepository.deleteById(id);
            System.out.println("标签删除成功");
            
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            System.err.println("删除标签时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("删除标签失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存标注后的图像
     */
    @PostMapping("/annotated-image/{id}")
    public ResponseEntity<?> saveAnnotatedImage(@PathVariable Integer id) {
        try {
            System.out.println("保存标注后的图像，ID: " + id);
            
            // TODO: 实现图像处理逻辑
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "标注图像保存成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("保存标注图像时发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("保存标注图像失败: " + e.getMessage());
        }
    }

    /**
     * 生成并保存带标注的图像
     * 此端点根据存储的标注数据生成图像并更新HemangiomaDiagnosis中的processedImagePath
     */
    @GetMapping("/generate-annotated-image/{id}")
    public ResponseEntity<?> generateAnnotatedImage(@PathVariable Integer id, 
                                                  @RequestParam(required = false) String mode) {
        try {
            System.out.println("生成标注图像，诊断ID: " + id);
            
            // 1. 获取诊断记录
            Optional<HemangiomaDiagnosis> diagnosisOpt = hemangiomaDiagnosisRepository.findById(id);
            if (!diagnosisOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "未找到诊断记录: " + id));
            }
            
            HemangiomaDiagnosis diagnosis = diagnosisOpt.get();
            
            // 2. 获取原始图像路径
            String originalImagePath = diagnosis.getImagePath();
            if (originalImagePath == null || originalImagePath.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "诊断记录中没有图像路径"));
            }
            
            // 3. 获取图像的所有标注
            List<Tag> tags = tagRepository.findByHemangiomaDiagnosisId(id);
            if (tags.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "没有找到标注数据"));
            }
            
            // 4. 读取原始图像
            String actualFilePath = fileService.getActualFilePath(originalImagePath);
            BufferedImage originalImage = fileService.readImage(actualFilePath);
            if (originalImage == null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "无法读取原始图像: " + actualFilePath));
            }
            
            // 5. 在图像上绘制标注框
            BufferedImage annotatedImage = drawAnnotationsOnImage(originalImage, tags);
            
            // 6. 生成新的文件名和保存路径
            String originalFileName = originalImagePath.substring(originalImagePath.lastIndexOf('/') + 1);
            String fileNameWithoutExt = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
            String extension = originalFileName.substring(originalFileName.lastIndexOf('.'));
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String newFileName = fileNameWithoutExt + "_annotated_" + timestamp + extension;
            
            // 修改：使用FileService的saveAnnotatedImageWithName方法保存到processed目录
            // 这会自动将图像保存到正确的目录
            String newImageWebPath = fileService.saveAnnotatedImageWithName(originalImagePath, annotatedImage, newFileName);
            System.out.println("保存标注图像到新路径: " + newImageWebPath);
            
            // 8. 更新诊断记录中的processedImagePath
            diagnosis.setProcessedImagePath(newImageWebPath);
            hemangiomaDiagnosisRepository.save(diagnosis);
            
            // 9. 返回成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "标注图像已生成并保存");
            response.put("originalPath", originalImagePath);
            response.put("processedPath", newImageWebPath);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "生成标注图像失败: " + e.getMessage()));
        }
    }
    
    /**
     * 在图像上绘制标注框
     */
    private BufferedImage drawAnnotationsOnImage(BufferedImage originalImage, List<Tag> tags) {
        // 创建图像副本，避免修改原始图像
        BufferedImage annotatedImage = new BufferedImage(
                originalImage.getWidth(), 
                originalImage.getHeight(), 
                BufferedImage.TYPE_INT_RGB);
        
        // 复制原始图像内容
        Graphics2D g2d = annotatedImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);
        
        // 设置绘图属性
        g2d.setStroke(new BasicStroke(3)); // 线宽
        
        // 为不同标签设置不同颜色
        Map<String, Color> tagColors = new HashMap<>();
        tagColors.put("IH", new Color(0, 255, 0)); // 红色
        tagColors.put("VM", new Color(0, 255, 0)); // 绿色 (原来是蓝色，现在改为绿色)
        tagColors.put("AVM", new Color(0, 255, 0)); // 绿色
        tagColors.put("MVM", new Color(0, 255, 0)); // 橙色
        tagColors.put("KHE", new Color(0, 255, 0)); // 紫色
        tagColors.put("NICH", new Color(0, 255, 0)); // 黄色
        tagColors.put("RICH", new Color(0, 255, 0)); // 青色
        tagColors.put("PICH", new Color(0, 255, 0)); // 品红色
        
        // 默认颜色
        Color defaultColor = new Color(0, 255, 0); // 绿色
        
        // 图像尺寸
        int imgWidth = originalImage.getWidth();
        int imgHeight = originalImage.getHeight();
        
        // 绘制每个标注框
        for (Tag tag : tags) {
            try {
                // 获取标签对应的颜色，如果没有预定义则使用默认颜色
                String tagName = tag.getTag() != null ? tag.getTag() : (tag.getTagName() != null ? tag.getTagName() : "IH");
                Color boxColor = tagColors.getOrDefault(tagName, defaultColor);
                g2d.setColor(boxColor);
                
                // 标注数据中的坐标可能是中心点坐标或左上角坐标，需要正确解析
                double normalizedX = tag.getX();
                double normalizedY = tag.getY();
                double normalizedWidth = tag.getWidth();
                double normalizedHeight = tag.getHeight();
                
                // 确定坐标系统类型 - 通过检查坐标值来判断
                // 如果x+width > 1 或 y+height > 1，则可能是像素坐标而非归一化坐标
                boolean isPixelCoordinates = (normalizedX + normalizedWidth > 1.1) || (normalizedY + normalizedHeight > 1.1);
                
                // 如果是像素坐标，转换为归一化坐标
                if (isPixelCoordinates) {
                    System.out.println("检测到像素坐标，转换为归一化坐标");
                    normalizedX = normalizedX / imgWidth;
                    normalizedY = normalizedY / imgHeight;
                    normalizedWidth = normalizedWidth / imgWidth;
                    normalizedHeight = normalizedHeight / imgHeight;
                }
                
                // 检查是否是中心点坐标系统
                // 如果x < width/2 或 y < height/2，则可能是中心点坐标
                boolean isCenterCoordinates = false;
                
                // 如果confidence大于0，通常表示是AI检测结果，使用中心点坐标
                if (tag.getConfidence() != null && tag.getConfidence() > 0) {
                    isCenterCoordinates = true;
                }
                
                // 计算像素坐标
                int x, y, width, height;
                
                if (isCenterCoordinates) {
                    // 中心点坐标系统
                    width = (int) (normalizedWidth * imgWidth);
                    height = (int) (normalizedHeight * imgHeight);
                    x = (int) (normalizedX * imgWidth - width / 2);
                    y = (int) (normalizedY * imgHeight - height / 2);
                    System.out.println("使用中心点坐标系统");
                } else {
                    // 左上角坐标系统
                    x = (int) (normalizedX * imgWidth);
                    y = (int) (normalizedY * imgHeight);
                    width = (int) (normalizedWidth * imgWidth);
                    height = (int) (normalizedHeight * imgHeight);
                    System.out.println("使用左上角坐标系统");
                }
                
                // 确保坐标不为负
                x = Math.max(0, x);
                y = Math.max(0, y);
                
                // 确保宽高不超出图像边界
                width = Math.min(width, imgWidth - x);
                height = Math.min(height, imgHeight - y);
                
                // 打印调试信息
                System.out.println("绘制标注框: " + tagName + 
                                  " 归一化坐标: (" + normalizedX + "," + normalizedY + ")" +
                                  " 像素坐标: (" + x + "," + y + ")" + 
                                  " 尺寸: " + width + "x" + height);
                
                // 绘制矩形框
                g2d.drawRect(x, y, width, height);
                
                // 绘制标签文本
                g2d.setFont(new Font("Arial", Font.BOLD, 14));
                g2d.drawString(tagName, x, y - 5);
            } catch (Exception e) {
                System.err.println("绘制标注框时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        g2d.dispose();
        return annotatedImage;
    }

    /**
     * 保存标注后的图像
     * 此端点用于标注修改后重新生成图像
     */
    @GetMapping("/save-image-after-annotation/{id}")
    public ResponseEntity<?> saveImageAfterAnnotation(@PathVariable Integer id,
                                                     @RequestParam(required = false) Integer metadata_id) {
        try {
            System.out.println("==== 保存标注后的图像请求开始 ====");
            System.out.println("路径参数ID: " + id);
            System.out.println("查询参数metadata_id: " + metadata_id);
            
            // 打印堆栈跟踪，帮助调试
            System.out.println("调用堆栈:");
            Thread.dumpStack();
            
            // 如果提供了metadata_id，优先使用它
            Integer imageId = metadata_id != null ? metadata_id : id;
            System.out.println("最终使用的图像ID: " + imageId);
            
            // 调用生成标注图像的方法
            ResponseEntity<?> result = generateAnnotatedImage(imageId, null);
            System.out.println("调用生成标注图像方法完成，状态码: " + result.getStatusCode());
            
            System.out.println("==== 保存标注后的图像请求结束 ====");
            return result;
        } catch (Exception e) {
            System.out.println("==== 保存标注后的图像请求异常 ====");
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "保存标注图像失败: " + e.getMessage()));
        }
    }
} 