package com.medical.annotation.util;

/**
 * 类型转换工具类，用于数据库迁移过程中处理Integer到Long的转换
 */
public class TypeConverter {
    
    /**
     * 将Integer安全地转换为Long
     * @param intValue Integer值
     * @return Long值，如果输入为null则返回null
     */
    public static Long toLong(Integer intValue) {
        return intValue != null ? intValue.longValue() : null;
    }
    
    /**
     * 将int安全地转换为Long
     * @param intValue int值
     * @return Long值
     */
    public static Long toLong(int intValue) {
        return Long.valueOf(intValue);
    }
    
    /**
     * 将Long安全地转换为Integer
     * @param longValue Long值
     * @return Integer值，如果输入为null则返回null
     */
    public static Integer toInteger(Long longValue) {
        return longValue != null ? longValue.intValue() : null;
    }
    
    /**
     * 将字符串解析为Long
     * @param strValue 字符串值
     * @return Long值，如果解析失败则返回null
     */
    public static Long parseLong(String strValue) {
        try {
            return Long.valueOf(strValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 将字符串解析为Integer
     * @param strValue 字符串值
     * @return Integer值，如果解析失败则返回null
     */
    public static Integer parseInt(String strValue) {
        try {
            return Integer.valueOf(strValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 