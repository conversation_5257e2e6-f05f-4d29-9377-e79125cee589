{"version": 3, "file": "js/976.e6e99f46.js", "mappings": "gMACOA,MAAM,mD,GACJA,MAAM,6B,GACJA,MAAM,kC,GACJA,MAAM,4B,GAKJA,MAAM,wB,GATrBC,IAAA,EAU8BD,MAAM,sB,GAVpCC,IAAA,EAagCD,MAAM,uB,GAbtCC,IAAA,G,GAqBqBD,MAAM,c,GACJA,MAAM,mB,EAtB7B,a,GAmCqBA,MAAM,gB,EAnC3B,a,GAAAC,IAAA,EAyCyCD,MAAM,W,GAzC/CC,IAAA,G,GAoDqBD,MAAM,c,GACJA,MAAM,mB,EArD7B,a,GAkEqBA,MAAM,gB,EAlE3B,a,GAAAC,IAAA,EAwEyCD,MAAM,W,GAK1BA,MAAM,e,GA7E3BC,IAAA,G,GAAAA,IAAA,G,GAwFqBD,MAAM,c,GACJA,MAAM,mB,EAzF7B,a,GAAAC,IAAA,EAqG4CD,MAAM,e,GArGlDC,IAAA,EAsGiED,MAAM,qB,GAE9CA,MAAM,sB,GAuBVA,MAAM,c,GACJA,MAAM,mB,EAhI7B,a,GA6IqBA,MAAM,gB,EA7I3B,a,GAAAC,IAAA,EAmJyCD,MAAM,W,GAO9BA,MAAM,c,wEAzJrBE,EAAAA,EAAAA,IAiKM,MAjKNC,EAiKM,EAhKJC,EAAAA,EAAAA,IA+JM,MA/JNC,EA+JM,EA9JJD,EAAAA,EAAAA,IA6JM,MA7JNE,EA6JM,EA5JJF,EAAAA,EAAAA,IA2JM,MA3JNG,EA2JM,gBA1JJH,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,wBAAsB,EAC/BI,EAAAA,EAAAA,IAAuC,OAAlCJ,MAAM,mBAAkB,UAAI,KAGnCI,EAAAA,EAAAA,IAqJM,MArJNI,EAqJM,CApJOC,EAAAC,QAAK,WAAhBR,EAAAA,EAAAA,IAEM,MAFNS,GAEMC,EAAAA,EAAAA,IADDH,EAAAC,OAAK,KAXtBG,EAAAA,EAAAA,IAAA,OAauBJ,EAAAK,UAAO,WAAlBZ,EAAAA,EAAAA,IAEM,MAFNa,GAEMH,EAAAA,EAAAA,IADDH,EAAAK,SAAO,KAdxBD,EAAAA,EAAAA,IAAA,OAkBgC,IAATJ,EAAAO,OAAI,WAAfd,EAAAA,EAAAA,IA4BM,MA9ClBe,EAAA,gBAmBcb,EAAAA,EAAAA,IAA2D,KAAxDJ,MAAM,oBAAmB,+BAA2B,KACvDI,EAAAA,EAAAA,IAyBO,QAzBAc,SAAMC,EAAA,KAAAA,EAAA,IApB3BC,EAAAA,EAAAA,KAAA,kBAoBqCX,EAAAY,4BAAAZ,EAAAY,2BAAAC,MAAAb,EAAAc,UAA0B,kB,EAC/CnB,EAAAA,EAAAA,IAYM,MAZNoB,EAYM,EAXJpB,EAAAA,EAAAA,IAUM,MAVNqB,EAUM,cATJrB,EAAAA,EAAAA,IAA0B,KAAvBJ,MAAM,cAAY,oBACrBI,EAAAA,EAAAA,IAOC,SANCsB,GAAG,QAzBzB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OA0B+BlB,EAAAmB,MAAKD,CAAA,GACdE,KAAK,QACLC,YAAY,UACZC,SAAA,GACCC,SAAUvB,EAAAwB,S,OA9BjCC,GAAA,OA0B+BzB,EAAAmB,cASfxB,EAAAA,EAAAA,IASM,MATN+B,EASM,EARJ/B,EAAAA,EAAAA,IAOS,UANPyB,KAAK,SACL7B,MAAM,aACLgC,SAAUvB,EAAAwB,S,CAECxB,EAAAwB,UAAO,WAAnB/B,EAAAA,EAAAA,IAA4C,OAA5CkC,KAzCpBvB,EAAAA,EAAAA,IAAA,QA0CoBT,EAAAA,EAAAA,IAAgD,aAAAQ,EAAAA,EAAAA,IAAvCH,EAAAwB,QAAU,SAAW,UAAd,MA1CpCI,MAAA,QAAAxB,EAAAA,EAAAA,IAAA,OAiDgC,IAATJ,EAAAO,OAAI,WAAfd,EAAAA,EAAAA,IAiCM,MAlFlBoC,EAAA,gBAkDclC,EAAAA,EAAAA,IAAmD,KAAhDJ,MAAM,oBAAmB,uBAAmB,KAC/CI,EAAAA,EAAAA,IA8BO,QA9BAc,SAAMC,EAAA,KAAAA,EAAA,IAnD3BC,EAAAA,EAAAA,KAAA,kBAmDqCX,EAAA8B,kBAAA9B,EAAA8B,iBAAAjB,MAAAb,EAAAc,UAAgB,kB,EACrCnB,EAAAA,EAAAA,IAYM,MAZNoC,EAYM,EAXJpC,EAAAA,EAAAA,IAUM,MAVNqC,EAUM,gBATJrC,EAAAA,EAAAA,IAAyB,KAAtBJ,MAAM,aAAW,oBACpBI,EAAAA,EAAAA,IAOC,SANCsB,GAAG,mBAxDzB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAyD+BlB,EAAAiC,iBAAgBf,CAAA,GACzBE,KAAK,OACLC,YAAY,SACZC,SAAA,GACCC,SAAUvB,EAAAwB,S,OA7DjCU,GAAA,OAyD+BlC,EAAAiC,yBASftC,EAAAA,EAAAA,IASM,MATNwC,EASM,EARJxC,EAAAA,EAAAA,IAOS,UANPyB,KAAK,SACL7B,MAAM,aACLgC,SAAUvB,EAAAwB,S,CAECxB,EAAAwB,UAAO,WAAnB/B,EAAAA,EAAAA,IAA4C,OAA5C2C,KAxEpBhC,EAAAA,EAAAA,IAAA,QAyEoBT,EAAAA,EAAAA,IAA4C,aAAAQ,EAAAA,EAAAA,IAAnCH,EAAAwB,QAAU,SAAW,MAAd,MAzEpCa,MA6EgB1C,EAAAA,EAAAA,IAGM,MAHN2C,EAGM,CAFQtC,EAAAuC,UAAY,IAAH,WAArB9C,EAAAA,EAAAA,IAAwD,OA9E1E+C,GAAArC,EAAAA,EAAAA,IA8EgDH,EAAAuC,WAAY,UAAO,iBACjD9C,EAAAA,EAAAA,IAA0E,KA/E5FD,IAAA,EA+EqBiD,KAAK,IAAYC,QAAKhC,EAAA,KAAAA,EAAA,IA/E3CC,EAAAA,EAAAA,KAAA,kBA+EqDX,EAAAY,4BAAAZ,EAAAY,2BAAAC,MAAAb,EAAAc,UAA0B,kBAAE,eAAO,QA/ExFV,EAAAA,EAAAA,IAAA,OAqFgC,IAATJ,EAAAO,OAAI,WAAfd,EAAAA,EAAAA,IAmEM,MAxJlBkD,EAAA,gBAsFchD,EAAAA,EAAAA,IAAsC,KAAnCJ,MAAM,oBAAmB,UAAM,KAClCI,EAAAA,EAAAA,IAgEO,QAhEAc,SAAMC,EAAA,KAAAA,EAAA,IAvF3BC,EAAAA,EAAAA,KAAA,kBAuFqCX,EAAA4C,qBAAA5C,EAAA4C,oBAAA/B,MAAAb,EAAAc,UAAmB,kB,EACxCnB,EAAAA,EAAAA,IAqCM,MArCNkD,EAqCM,EApCJlD,EAAAA,EAAAA,IAWM,MAXNmD,EAWM,gBAVJnD,EAAAA,EAAAA,IAAyB,KAAtBJ,MAAM,aAAW,oBACpBI,EAAAA,EAAAA,IAQC,SAPCsB,GAAG,cA5FzB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OA6F+BlB,EAAA+C,YAAW7B,CAAA,GACpBE,KAAK,WACLC,YAAY,SACZC,SAAA,GACCC,SAAUvB,EAAAwB,QACVwB,OAAItC,EAAA,KAAAA,EAAA,qBAAEV,EAAAiD,kBAAAjD,EAAAiD,iBAAApC,MAAAb,EAAAc,UAAgB,I,QAlG7CoC,GAAA,OA6F+BlD,EAAA+C,iBAQF/C,EAAAmD,gBAAa,WAAxB1D,EAAAA,EAAAA,IAAuE,MAAvE2D,GAAuEjD,EAAAA,EAAAA,IAAtBH,EAAAmD,eAAa,KArGhF/C,EAAAA,EAAAA,IAAA,OAsG6BJ,EAAAqD,mBAAqBrD,EAAAmD,gBAAa,WAA7C1D,EAAAA,EAAAA,IAmBM,MAnBN6D,EAmBM,gBAlBJ3D,EAAAA,EAAAA,IAAuC,OAAlCJ,MAAM,kBAAiB,SAAK,KACjCI,EAAAA,EAAAA,IASM,MATN4D,EASM,EARJ5D,EAAAA,EAAAA,IAOO,OANLJ,OA1GxBiE,EAAAA,EAAAA,IAAA,CA0G8B,eAAc,C,KACyC,SAAhBxD,EAAAqD,iB,OAAkF,WAAhBrD,EAAAqD,iB,OAAoF,WAAhBrD,EAAAqD,qB,WAOvL1D,EAAAA,EAAAA,IAMM,OANDJ,OAlHzBiE,EAAAA,EAAAA,IAAA,CAkH+B,gBAAwBxD,EAAAqD,qB,QAEV,SAArBrD,EAAAqD,iBAA8B,IAAgD,WAAhBrD,EAAAqD,iBAAgB,IAAgE,WAAhBrD,EAAAqD,iBAAgB,eApHtKjD,EAAAA,EAAAA,IAAA,sBA0HkBT,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,iBAAgB,0CAE3B,OAGFI,EAAAA,EAAAA,IAYM,MAZN8D,EAYM,EAXJ9D,EAAAA,EAAAA,IAUM,MAVN+D,EAUM,gBATJ/D,EAAAA,EAAAA,IAAyB,KAAtBJ,MAAM,aAAW,oBACpBI,EAAAA,EAAAA,IAOC,SANCsB,GAAG,kBAnIzB,sBAAAP,EAAA,KAAAA,EAAA,YAAAQ,GAAA,OAoI+BlB,EAAA2D,gBAAezC,CAAA,GACxBE,KAAK,WACLC,YAAY,SACZC,SAAA,GACCC,SAAUvB,EAAAwB,S,OAxIjCoC,GAAA,OAoI+B5D,EAAA2D,wBASfhE,EAAAA,EAAAA,IASM,MATNkE,EASM,EARJlE,EAAAA,EAAAA,IAOS,UANPyB,KAAK,SACL7B,MAAM,aACLgC,SAAUvB,EAAAwB,WAAaxB,EAAAmD,e,CAEZnD,EAAAwB,UAAO,WAAnB/B,EAAAA,EAAAA,IAA4C,OAA5CqE,KAnJpB1D,EAAAA,EAAAA,IAAA,QAoJoBT,EAAAA,EAAAA,IAA8C,aAAAQ,EAAAA,EAAAA,IAArCH,EAAAwB,QAAU,SAAW,QAAd,MApJpCuC,MAAA,QAAA3D,EAAAA,EAAAA,IAAA,QA0JYT,EAAAA,EAAAA,IAGM,MAHNqE,EAGM,gBAFJrE,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZsE,EAAAA,EAAAA,IAA6DC,EAAA,CAAhDC,GAAG,SAAS5E,MAAM,a,CA5J7C,SAAA6E,EAAAA,EAAAA,KA4JyD,kBAAI1D,EAAA,MAAAA,EAAA,MA5J7D2D,EAAAA,EAAAA,IA4JyD,S,IA5JzDC,EAAA,EAAAC,GAAA,kB,+GA0KA,SACEC,KAAM,iBACNC,MAAK,WACH,IAAMC,GAASC,EAAAA,EAAAA,MAGTxD,GAAQyD,EAAAA,EAAAA,IAAI,IACZ3C,GAAmB2C,EAAAA,EAAAA,IAAI,IACvB7B,GAAc6B,EAAAA,EAAAA,IAAI,IAClBjB,GAAkBiB,EAAAA,EAAAA,IAAI,IAGtBpD,GAAUoD,EAAAA,EAAAA,KAAI,GACd3E,GAAQ2E,EAAAA,EAAAA,IAAI,IACZvE,GAAUuE,EAAAA,EAAAA,IAAI,IACdrE,GAAOqE,EAAAA,EAAAA,IAAI,GACXrC,GAAYqC,EAAAA,EAAAA,IAAI,GAClBC,EAAiB,KACf1B,GAAgByB,EAAAA,EAAAA,IAAI,IACpBvB,GAAmBuB,EAAAA,EAAAA,IAAI,KAG7BE,EAAAA,EAAAA,KAAY,WACND,GACFE,cAAcF,EAElB,IAGA,IAAMG,EAAiB,WACrBzC,EAAU0C,MAAQ,GAClBJ,EAAiBK,aAAY,WAC3B3C,EAAU0C,QACN1C,EAAU0C,OAAS,GACrBF,cAAcF,EAElB,GAAG,IACL,EAGM5B,EAAmB,WACvB,IAAMkC,EAAMpC,EAAYkC,MAGxB,GAAIE,EAAIC,OAAS,EAGf,OAFAjC,EAAc8B,MAAQ,cACtB5B,EAAiB4B,MAAQ,QAClB,EAIT,IAAMI,EAAe,QAAQC,KAAKH,GAC5BI,EAAe,QAAQD,KAAKH,GAC5BK,EAAa,QAAQF,KAAKH,GAC1BM,EAAkB,wCAAwCH,KAAKH,GAE/DO,EAAa,CAACL,EAAcE,EAAcC,EAAYC,GAAiBE,OAAOC,SAASR,OAGvFS,EAAkB,CAAC,SAAU,WAAY,SAAU,WAAY,SAAU,UAC/E,OAAIA,EAAgBC,SAASX,EAAIY,gBAC/B5C,EAAc8B,MAAQ,cACtB5B,EAAiB4B,MAAQ,QAClB,GAGLS,EAAa,GACfvC,EAAc8B,MAAQ,gCACtB5B,EAAiB4B,MAAQ,QAClB,IAIU,IAAfS,GAAoBP,EAAIC,OAAS,GACnC/B,EAAiB4B,MAAQ,UACD,IAAfS,GAAoC,IAAfA,GAAoBP,EAAIC,QAAU,MAChE/B,EAAiB4B,MAAQ,UAG3B9B,EAAc8B,MAAQ,IACf,EACT,GAGAe,EAAAA,EAAAA,IAAMjD,GAAa,WACbA,EAAYkC,MACdhC,KAEAE,EAAc8B,MAAQ,GACtB5B,EAAiB4B,MAAQ,GAE7B,IAGA,IAAMrE,EAAyB,eAAAqF,GAAAC,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAC,IAAA,IAAAC,EAAA,OAAAH,EAAAA,EAAAA,KAAAI,GAAA,SAAAC,GAAA,eAAAA,EAAAC,GAAA,UAC5BtF,EAAM8D,MAAO,CAAFuB,EAAAC,EAAA,QACQ,OAAtBxG,EAAMgF,MAAQ,UAAQuB,EAAAE,EAAA,UAMP,OAFjBlF,EAAQyD,OAAQ,EAChBhF,EAAMgF,MAAQ,GACd5E,EAAQ4E,MAAQ,GAACuB,EAAAG,EAAA,EAAAH,EAAAC,EAAA,EAGTG,EAAAA,WAAIC,MAAMC,uBAAuB3F,EAAM8D,OAAK,OAClD5E,EAAQ4E,MAAQ,gBAChB1E,EAAK0E,MAAQ,EACbD,IAAewB,EAAAC,EAAA,eAAAD,EAAAG,EAAA,EAAAL,EAAAE,EAAAO,EAEfC,QAAQ/G,MAAM,WAAUqG,GACxBrG,EAAMgF,MAAuB,kBAAfqB,EAAsBA,EAAU,oBAAkB,OAE5C,OAF4CE,EAAAG,EAAA,EAEhEnF,EAAQyD,OAAQ,EAAIuB,EAAAS,EAAA,iBAAAT,EAAAE,EAAA,MAAAL,EAAA,sBAExB,kBArB+B,OAAAJ,EAAApF,MAAA,KAAAC,UAAA,KAwBzBgB,EAAe,eAAAoF,GAAAhB,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAe,IAAA,IAAAC,EAAA,OAAAjB,EAAAA,EAAAA,KAAAI,GAAA,SAAAc,GAAA,eAAAA,EAAAZ,GAAA,UAClBxE,EAAiBgD,MAAO,CAAFoC,EAAAZ,EAAA,QACJ,OAArBxG,EAAMgF,MAAQ,SAAOoC,EAAAX,EAAA,UAMN,OAFjBlF,EAAQyD,OAAQ,EAChBhF,EAAMgF,MAAQ,GACd5E,EAAQ4E,MAAQ,GAACoC,EAAAV,EAAA,EAAAU,EAAAZ,EAAA,EAGTG,EAAAA,WAAIC,MAAMS,gBAAgBnG,EAAM8D,MAAOhD,EAAiBgD,OAAK,OACnE5E,EAAQ4E,MAAQ,cAChB1E,EAAK0E,MAAQ,EAAAoC,EAAAZ,EAAA,eAAAY,EAAAV,EAAA,EAAAS,EAAAC,EAAAN,EAEbC,QAAQ/G,MAAM,WAAUmH,GACxBnH,EAAMgF,MAAuB,kBAAfmC,EAAsBA,EAAU,YAAU,OAEpC,OAFoCC,EAAAV,EAAA,EAExDnF,EAAQyD,OAAQ,EAAIoC,EAAAJ,EAAA,iBAAAI,EAAAX,EAAA,MAAAS,EAAA,sBAExB,kBApBqB,OAAAD,EAAArG,MAAA,KAAAC,UAAA,KAuBf8B,EAAkB,eAAA2E,GAAArB,EAAAA,EAAAA,IAAAC,EAAAA,EAAAA,KAAAC,GAAI,SAAAoB,IAAA,IAAAC,EAAA,OAAAtB,EAAAA,EAAAA,KAAAI,GAAA,SAAAmB,GAAA,eAAAA,EAAAjB,GAAA,UACrB1D,EAAYkC,MAAO,CAAFyC,EAAAjB,EAAA,QACC,OAArBxG,EAAMgF,MAAQ,SAAOyC,EAAAhB,EAAA,aAIlBzD,IAAoB,CAAFyE,EAAAjB,EAAA,eAAAiB,EAAAhB,EAAA,aAInB3D,EAAYkC,QAAUtB,EAAgBsB,MAAK,CAAAyC,EAAAjB,EAAA,QACpB,OAAzBxG,EAAMgF,MAAQ,aAAWyC,EAAAhB,EAAA,UAMV,OAFjBlF,EAAQyD,OAAQ,EAChBhF,EAAMgF,MAAQ,GACd5E,EAAQ4E,MAAQ,GAACyC,EAAAf,EAAA,EAAAe,EAAAjB,EAAA,EAGTG,EAAAA,WAAIC,MAAMc,cAAcxG,EAAM8D,MAAOhD,EAAiBgD,MAAOlC,EAAYkC,OAAK,OACpF5E,EAAQ4E,MAAQ,mBAGhB2C,YAAW,WACTlD,EAAOmD,KAAK,SACd,GAAG,KAAIH,EAAAjB,EAAA,eAAAiB,EAAAf,EAAA,EAAAc,EAAAC,EAAAX,EAEPC,QAAQ/G,MAAM,UAASwH,GACvBxH,EAAMgF,MAAuB,kBAAfwC,EAAsBA,EAAU,aAAW,OAErC,OAFqCC,EAAAf,EAAA,EAEzDnF,EAAQyD,OAAQ,EAAIyC,EAAAT,EAAA,iBAAAS,EAAAhB,EAAA,MAAAc,EAAA,sBAExB,kBAjCwB,OAAAD,EAAA1G,MAAA,KAAAC,UAAA,KAmCxB,MAAO,CACLK,MAAAA,EACAc,iBAAAA,EACAc,YAAAA,EACAY,gBAAAA,EACAnC,QAAAA,EACAvB,MAAAA,EACAI,QAAAA,EACAE,KAAAA,EACAgC,UAAAA,EACAY,cAAAA,EACAE,iBAAAA,EACAzC,2BAAAA,EACAkB,iBAAAA,EACAc,oBAAAA,EACAK,iBAAAA,EAEJ,G,eCpWF,MAAM6E,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://medical-annotation-frontend/./src/views/ForgotPassword.vue", "webpack://medical-annotation-frontend/./src/views/ForgotPassword.vue?1314"], "sourcesContent": ["<template>\r\n  <div class=\"forgot-password-page forgot-password-background\">\r\n    <div class=\"forgot-password-container\">\r\n      <div class=\"forgot-password-form-container\">\r\n        <div class=\"forgot-password-form-box\">\r\n          <div class=\"forgot-password-tabs\">\r\n            <div class=\"tab-item active\">密码找回</div>\r\n          </div>\r\n          \r\n          <div class=\"forgot-password-form\">\r\n            <div v-if=\"error\" class=\"alert alert-danger\">\r\n              {{ error }}\r\n            </div>\r\n            <div v-if=\"success\" class=\"alert alert-success\">\r\n              {{ success }}\r\n            </div>\r\n            \r\n            <!-- 步骤1: 输入邮箱 -->\r\n            <div v-if=\"step === 1\">\r\n              <p class=\"form-description\">请输入您注册时使用的电子邮箱，我们将向该邮箱发送验证码</p>\r\n              <form @submit.prevent=\"handleSendVerificationCode\">\r\n                <div class=\"form-group\">\r\n                  <div class=\"input-with-icon\">\r\n                    <i class=\"icon-email\"></i>\r\n                    <input \r\n                      id=\"email\" \r\n                      v-model=\"email\" \r\n                      type=\"email\" \r\n                      placeholder=\"请输入邮箱地址\"\r\n                      required\r\n                      :disabled=\"loading\"\r\n                    >\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"form-actions\">\r\n                  <button \r\n                    type=\"submit\" \r\n                    class=\"submit-btn\" \r\n                    :disabled=\"loading\"\r\n                  >\r\n                    <span v-if=\"loading\" class=\"spinner\"></span>\r\n                    <span>{{ loading ? '发送中...' : '发送验证邮件' }}</span>\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n            \r\n            <!-- 步骤2: 输入验证码 -->\r\n            <div v-if=\"step === 2\">\r\n              <p class=\"form-description\">验证邮件已发送，请查收邮箱并输入验证码</p>\r\n              <form @submit.prevent=\"handleVerifyCode\">\r\n                <div class=\"form-group\">\r\n                  <div class=\"input-with-icon\">\r\n                    <i class=\"icon-lock\"></i>\r\n                    <input \r\n                      id=\"verificationCode\" \r\n                      v-model=\"verificationCode\" \r\n                      type=\"text\" \r\n                      placeholder=\"请输入验证码\"\r\n                      required\r\n                      :disabled=\"loading\"\r\n                    >\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"form-actions\">\r\n                  <button \r\n                    type=\"submit\" \r\n                    class=\"submit-btn\" \r\n                    :disabled=\"loading\"\r\n                  >\r\n                    <span v-if=\"loading\" class=\"spinner\"></span>\r\n                    <span>{{ loading ? '验证中...' : '验证' }}</span>\r\n                  </button>\r\n                </div>\r\n                \r\n                <div class=\"resend-code\">\r\n                  <span v-if=\"countdown > 0\">{{ countdown }}秒后可重新发送</span>\r\n                  <a href=\"#\" v-else @click.prevent=\"handleSendVerificationCode\">重新发送验证码</a>\r\n                </div>\r\n              </form>\r\n            </div>\r\n            \r\n            <!-- 步骤3: 设置新密码 -->\r\n            <div v-if=\"step === 3\">\r\n              <p class=\"form-description\">请设置新密码</p>\r\n              <form @submit.prevent=\"handleResetPassword\">\r\n                <div class=\"form-group\">\r\n                  <div class=\"input-with-icon\">\r\n                    <i class=\"icon-lock\"></i>\r\n                    <input \r\n                      id=\"newPassword\" \r\n                      v-model=\"newPassword\" \r\n                      type=\"password\" \r\n                      placeholder=\"请设置新密码\"\r\n                      required\r\n                      :disabled=\"loading\"\r\n                      @blur=\"validatePassword\"\r\n                    >\r\n                  </div>\r\n                  <div v-if=\"passwordError\" class=\"field-error\">{{ passwordError }}</div>\r\n                  <div v-if=\"passwordStrength && !passwordError\" class=\"password-strength\">\r\n                    <div class=\"strength-label\">密码强度：</div>\r\n                    <div class=\"strength-indicator\">\r\n                      <div \r\n                        class=\"strength-bar\" \r\n                        :class=\"{\r\n                          'weak': passwordStrength === 'weak',\r\n                          'medium': passwordStrength === 'medium',\r\n                          'strong': passwordStrength === 'strong'\r\n                        }\"\r\n                      ></div>\r\n                    </div>\r\n                    <div class=\"strength-text\" :class=\"passwordStrength\">\r\n                      {{ \r\n                        passwordStrength === 'weak' ? '弱' : \r\n                        passwordStrength === 'medium' ? '中' : \r\n                        passwordStrength === 'strong' ? '强' : ''\r\n                      }}\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"password-hint\">\r\n                    密码长度至少8位，必须包含大写字母、小写字母、数字和特殊符号中的至少三种\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"form-group\">\r\n                  <div class=\"input-with-icon\">\r\n                    <i class=\"icon-lock\"></i>\r\n                    <input \r\n                      id=\"confirmPassword\" \r\n                      v-model=\"confirmPassword\" \r\n                      type=\"password\" \r\n                      placeholder=\"请确认新密码\"\r\n                      required\r\n                      :disabled=\"loading\"\r\n                    >\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"form-actions\">\r\n                  <button \r\n                    type=\"submit\" \r\n                    class=\"submit-btn\" \r\n                    :disabled=\"loading || !!passwordError\"\r\n                  >\r\n                    <span v-if=\"loading\" class=\"spinner\"></span>\r\n                    <span>{{ loading ? '重置中...' : '重置密码' }}</span>\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n            \r\n            <div class=\"login-link\">\r\n              <span>记起密码了?</span>\r\n              <router-link to=\"/login\" class=\"login-btn\">返回登录</router-link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, onUnmounted, watch } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport api from '@/utils/api'\r\n\r\nexport default {\r\n  name: 'ForgotPassword',\r\n  setup() {\r\n    const router = useRouter()\r\n    \r\n    // 表单数据\r\n    const email = ref('')\r\n    const verificationCode = ref('')\r\n    const newPassword = ref('')\r\n    const confirmPassword = ref('')\r\n    \r\n    // 状态管理\r\n    const loading = ref(false)\r\n    const error = ref('')\r\n    const success = ref('')\r\n    const step = ref(1)\r\n    const countdown = ref(0)\r\n    let countdownTimer = null\r\n    const passwordError = ref('')\r\n    const passwordStrength = ref('')\r\n    \r\n    // 清理定时器\r\n    onUnmounted(() => {\r\n      if (countdownTimer) {\r\n        clearInterval(countdownTimer)\r\n      }\r\n    })\r\n    \r\n    // 开始倒计时\r\n    const startCountdown = () => {\r\n      countdown.value = 60\r\n      countdownTimer = setInterval(() => {\r\n        countdown.value--\r\n        if (countdown.value <= 0) {\r\n          clearInterval(countdownTimer)\r\n        }\r\n      }, 1000)\r\n    }\r\n    \r\n    // 密码强度验证\r\n    const validatePassword = () => {\r\n      const pwd = newPassword.value\r\n      \r\n      // 检查密码长度\r\n      if (pwd.length < 8) {\r\n        passwordError.value = '密码长度必须不少于8位'\r\n        passwordStrength.value = 'weak'\r\n        return false\r\n      }\r\n      \r\n      // 检查密码复杂度\r\n      const hasUpperCase = /[A-Z]/.test(pwd)\r\n      const hasLowerCase = /[a-z]/.test(pwd)\r\n      const hasNumbers = /[0-9]/.test(pwd)\r\n      const hasSpecialChars = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(pwd)\r\n      \r\n      const typesCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length\r\n      \r\n      // 检查常见简单密码\r\n      const commonPasswords = ['123456', 'password', 'abcdef', '12345678', 'qwerty', '111111']\r\n      if (commonPasswords.includes(pwd.toLowerCase())) {\r\n        passwordError.value = '请勿使用常见的简单密码'\r\n        passwordStrength.value = 'weak'\r\n        return false\r\n      }\r\n      \r\n      if (typesCount < 3) {\r\n        passwordError.value = '密码必须包含大写字母、小写字母、数字和特殊符号中的至少三种'\r\n        passwordStrength.value = 'weak'\r\n        return false\r\n      }\r\n      \r\n      // 密码强度评估\r\n      if (typesCount === 3 && pwd.length < 10) {\r\n        passwordStrength.value = 'medium'\r\n      } else if (typesCount === 4 || (typesCount === 3 && pwd.length >= 10)) {\r\n        passwordStrength.value = 'strong'\r\n      }\r\n      \r\n      passwordError.value = ''\r\n      return true\r\n    }\r\n    \r\n    // 监听密码变化\r\n    watch(newPassword, () => {\r\n      if (newPassword.value) {\r\n        validatePassword()\r\n      } else {\r\n        passwordError.value = ''\r\n        passwordStrength.value = ''\r\n      }\r\n    })\r\n    \r\n    // 发送验证码\r\n    const handleSendVerificationCode = async () => {\r\n      if (!email.value) {\r\n        error.value = '请输入邮箱地址'\r\n        return\r\n      }\r\n      \r\n      loading.value = true\r\n      error.value = ''\r\n      success.value = ''\r\n      \r\n      try {\r\n        await api.users.sendResetPasswordEmail(email.value)\r\n        success.value = '验证邮件已发送，请注意查收'\r\n        step.value = 2\r\n        startCountdown()\r\n      } catch (err) {\r\n        console.error('发送验证码错误:', err)\r\n        error.value = typeof err === 'string' ? err : '发送验证码失败，请检查邮箱是否正确'\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 验证验证码\r\n    const handleVerifyCode = async () => {\r\n      if (!verificationCode.value) {\r\n        error.value = '请输入验证码'\r\n        return\r\n      }\r\n      \r\n      loading.value = true\r\n      error.value = ''\r\n      success.value = ''\r\n      \r\n      try {\r\n        await api.users.verifyResetCode(email.value, verificationCode.value)\r\n        success.value = '验证成功，请设置新密码'\r\n        step.value = 3\r\n      } catch (err) {\r\n        console.error('验证码验证错误:', err)\r\n        error.value = typeof err === 'string' ? err : '验证码错误或已过期'\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 重置密码\r\n    const handleResetPassword = async () => {\r\n      if (!newPassword.value) {\r\n        error.value = '请输入新密码'\r\n        return\r\n      }\r\n      \r\n      if (!validatePassword()) {\r\n        return\r\n      }\r\n      \r\n      if (newPassword.value !== confirmPassword.value) {\r\n        error.value = '两次输入的密码不一致'\r\n        return\r\n      }\r\n      \r\n      loading.value = true\r\n      error.value = ''\r\n      success.value = ''\r\n      \r\n      try {\r\n        await api.users.resetPassword(email.value, verificationCode.value, newPassword.value)\r\n        success.value = '密码重置成功，即将跳转到登录页面'\r\n        \r\n        // 3秒后跳转到登录页\r\n        setTimeout(() => {\r\n          router.push('/login')\r\n        }, 3000)\r\n      } catch (err) {\r\n        console.error('重置密码错误:', err)\r\n        error.value = typeof err === 'string' ? err : '重置密码失败，请重试'\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    return {\r\n      email,\r\n      verificationCode,\r\n      newPassword,\r\n      confirmPassword,\r\n      loading,\r\n      error,\r\n      success,\r\n      step,\r\n      countdown,\r\n      passwordError,\r\n      passwordStrength,\r\n      handleSendVerificationCode,\r\n      handleVerifyCode,\r\n      handleResetPassword,\r\n      validatePassword\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.forgot-password-page {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0;\r\n  margin: 0;\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.forgot-password-page.forgot-password-background {\r\n  /* 使用全局CSS定义的背景 */\r\n}\r\n\r\n.forgot-password-container {\r\n  width: 450px;\r\n  max-width: 90%;\r\n  display: flex;\r\n  background-color: rgba(255, 255, 255, 0.95);\r\n  border-radius: 12px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n  height: auto;\r\n  margin: 20px;\r\n}\r\n\r\n.forgot-password-form-container {\r\n  width: 100%;\r\n  background-color: white;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.forgot-password-form-box {\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  overflow-y: auto;\r\n}\r\n\r\n.forgot-password-tabs {\r\n  display: flex;\r\n  border-bottom: 1px solid #eee;\r\n  margin-bottom: 25px;\r\n  justify-content: center;\r\n}\r\n\r\n.tab-item {\r\n  padding: 10px 0;\r\n  margin-right: 0;\r\n  font-size: 18px;\r\n  color: #1890ff;\r\n  font-weight: 500;\r\n  position: relative;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #1890ff;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-item.active:after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -1px;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background-color: #1890ff;\r\n}\r\n\r\n.forgot-password-form {\r\n  flex: 1;\r\n}\r\n\r\n.form-description {\r\n  margin-bottom: 20px;\r\n  color: #666;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.input-with-icon {\r\n  position: relative;\r\n}\r\n\r\n.input-with-icon i {\r\n  position: absolute;\r\n  left: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #bfbfbf;\r\n}\r\n\r\n.input-with-icon input {\r\n  width: 100%;\r\n  padding: 12px 12px 12px 40px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.input-with-icon input:focus {\r\n  border-color: #40a9ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n  outline: none;\r\n}\r\n\r\n.form-actions {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.field-error {\r\n  color: #ff4d4f;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n  padding-left: 8px;\r\n}\r\n\r\n/* 密码强度相关样式 */\r\n.password-strength {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.strength-label {\r\n  margin-right: 8px;\r\n  color: #666;\r\n}\r\n\r\n.strength-indicator {\r\n  flex: 1;\r\n  height: 4px;\r\n  background-color: #f0f0f0;\r\n  border-radius: 2px;\r\n  overflow: hidden;\r\n  margin-right: 8px;\r\n}\r\n\r\n.strength-bar {\r\n  height: 100%;\r\n  width: 0;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.strength-bar.weak {\r\n  width: 33%;\r\n  background-color: #ff4d4f;\r\n}\r\n\r\n.strength-bar.medium {\r\n  width: 66%;\r\n  background-color: #faad14;\r\n}\r\n\r\n.strength-bar.strong {\r\n  width: 100%;\r\n  background-color: #52c41a;\r\n}\r\n\r\n.strength-text {\r\n  font-weight: 500;\r\n}\r\n\r\n.strength-text.weak {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.strength-text.medium {\r\n  color: #faad14;\r\n}\r\n\r\n.strength-text.strong {\r\n  color: #52c41a;\r\n}\r\n\r\n.password-hint {\r\n  color: #888;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n  padding-left: 8px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.submit-btn {\r\n  width: 100%;\r\n  background-color: #1890ff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 24px;\r\n  padding: 12px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background-color: #40a9ff;\r\n}\r\n\r\n.submit-btn:disabled {\r\n  background-color: #91d5ff;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.spinner {\r\n  display: inline-block;\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-right: 8px;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  border-top-color: #fff;\r\n  animation: spin 0.8s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.resend-code {\r\n  text-align: center;\r\n  margin-top: 10px;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.resend-code a {\r\n  color: #1890ff;\r\n  text-decoration: none;\r\n}\r\n\r\n.resend-code a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.login-link {\r\n  text-align: center;\r\n  margin-top: 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-link span {\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.login-link a {\r\n  color: #1890ff;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.login-link a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.alert {\r\n  padding: 12px;\r\n  border-radius: 6px;\r\n  margin-bottom: 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n.alert-danger {\r\n  background-color: #fff2f0;\r\n  border: 1px solid #ffccc7;\r\n  color: #ff4d4f;\r\n}\r\n\r\n.alert-success {\r\n  background-color: #f6ffed;\r\n  border: 1px solid #b7eb8f;\r\n  color: #52c41a;\r\n}\r\n\r\n/* 图标样式 */\r\n.icon-email:before {\r\n  content: \"✉️\";\r\n}\r\n\r\n.icon-lock:before {\r\n  content: \"🔒\";\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 576px) {\r\n  .forgot-password-container {\r\n    max-width: 100%;\r\n  }\r\n  \r\n  .forgot-password-form-box {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style> ", "import { render } from \"./ForgotPassword.vue?vue&type=template&id=0220b3a4&scoped=true\"\nimport script from \"./ForgotPassword.vue?vue&type=script&lang=js\"\nexport * from \"./ForgotPassword.vue?vue&type=script&lang=js\"\n\nimport \"./ForgotPassword.vue?vue&type=style&index=0&id=0220b3a4&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0220b3a4\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "$setup", "error", "_hoisted_6", "_toDisplayString", "_createCommentVNode", "success", "_hoisted_7", "step", "_hoisted_8", "onSubmit", "_cache", "_withModifiers", "handleSendVerificationCode", "apply", "arguments", "_hoisted_9", "_hoisted_10", "id", "$event", "email", "type", "placeholder", "required", "disabled", "loading", "_hoisted_11", "_hoisted_12", "_hoisted_14", "_hoisted_13", "_hoisted_15", "handleVerifyCode", "_hoisted_16", "_hoisted_17", "verificationCode", "_hoisted_18", "_hoisted_19", "_hoisted_21", "_hoisted_20", "_hoisted_22", "countdown", "_hoisted_23", "href", "onClick", "_hoisted_24", "handleResetPassword", "_hoisted_25", "_hoisted_26", "newPassword", "onBlur", "validatePassword", "_hoisted_27", "passwordError", "_hoisted_28", "passwordStrength", "_hoisted_29", "_hoisted_30", "_normalizeClass", "_hoisted_31", "_hoisted_32", "confirmPassword", "_hoisted_33", "_hoisted_34", "_hoisted_36", "_hoisted_35", "_hoisted_37", "_createVNode", "_component_router_link", "to", "_withCtx", "_createTextVNode", "_", "__", "name", "setup", "router", "useRouter", "ref", "countdownTimer", "onUnmounted", "clearInterval", "startCountdown", "value", "setInterval", "pwd", "length", "hasUpperCase", "test", "hasLowerCase", "hasNumbers", "hasSpecialChars", "typesCount", "filter", "Boolean", "commonPasswords", "includes", "toLowerCase", "watch", "_ref", "_asyncToGenerator", "_regenerator", "m", "_callee", "_t", "w", "_context", "n", "a", "p", "api", "users", "sendResetPasswordEmail", "v", "console", "f", "_ref2", "_callee2", "_t2", "_context2", "verifyResetCode", "_ref3", "_callee3", "_t3", "_context3", "resetPassword", "setTimeout", "push", "__exports__", "render"], "sourceRoot": ""}