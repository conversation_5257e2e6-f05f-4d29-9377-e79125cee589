/**
 * API客户端适配器
 * 
 * 这个文件用于解决api/index.js和utils/api.js之间的冗余问题
 * 它将api/index.js中的方法重定向到utils/api.js，以便逐步迁移
 */

// 导入utils/api.js中的API客户端
import utilsApi from '../utils/api';

// 创建一个适配器对象，模拟api/index.js的结构但使用utils/api.js的实现
const apiAdapter = {
  // 认证相关API
  auth: {
    login: (credentials) => utilsApi.auth.login(credentials),
    register: (userData) => utilsApi.auth.register(userData),
    logout: () => utilsApi.auth.logout()
  },
  
  // 用户相关API
  user: {
    getCurrentUser: () => utilsApi.users.getCurrentUser(),
    getUserByEmail: (email) => utilsApi.users.getUserByEmail(email),
    getUserById: (id) => utilsApi.users.getUser(id),
    getAllUsers: () => utilsApi.users.getAll()
  },
  
  // 团队相关API
  teams: {
    getAllTeams: () => utilsApi.teams.getAll(),
    getTeamById: (id) => utilsApi.teams.getOne(id),
    createTeam: (teamData) => utilsApi.teams.create(teamData),
    updateTeam: (id, teamData) => utilsApi.teams.update(id, teamData),
    getTeamMembers: (teamId) => utilsApi.teams.getTeamMembers(teamId)
  },
  
  // 图像相关API
  image: {
    uploadImage: (formData) => utilsApi.images.upload(formData),
    getMetadata: (id) => utilsApi.images.getOne(id),
    updateMetadata: (id, data) => utilsApi.images.update(id, data),
    getUserAnnotations: () => utilsApi.annotations.getByUserId(),
    getTeamAnnotations: (teamId) => utilsApi.teams.getTeamAnnotations(teamId)
  },
  
  // 标签相关API
  tags: {
    getAllTags: () => utilsApi.tags.getAll(),
    createTag: (tagData) => utilsApi.tags.create(tagData)
  },
  
  // 图像对相关API
  imagePairs: {
    createImagePair: (data) => utilsApi.imagePairs.create(data),
    getImagePairsByMetadata: (metadataId) => utilsApi.imagePairs.getByMetadataId(metadataId),
    getByMetadataId: (metadataId) => utilsApi.imagePairs.getByMetadataId(metadataId)
  },
  
  // 申请相关API
  applications: {
    getUserApplications: () => utilsApi.teamApplications.getUserApplications(),
    createApplication: (data) => utilsApi.teamApplications.applyToJoinTeam(data.teamId, data.reason),
    approveApplication: (id) => utilsApi.teamApplications.processApplication(id, { action: 'APPROVED' }),
    rejectApplication: (id, reason) => utilsApi.teamApplications.processApplication(id, { action: 'REJECTED', reason })
  },
  
  // 审核相关API
  reviews: {
    getPendingApplications: () => utilsApi.teamApplications.getPendingApplications(),
    getPendingReviews: (params) => utilsApi.reviews.getPendingApplications(params),
    getProcessedReviews: (params) => utilsApi.reviews.getProcessedApplications(params.status),
    approveReview: (reviewId, data) => utilsApi.reviews.processApplication(reviewId, { ...data, action: 'APPROVED' }),
    rejectReview: (reviewId, data) => utilsApi.reviews.processApplication(reviewId, { ...data, action: 'REJECTED' })
  }
};

// 导出适配器作为默认导出
export default apiAdapter; 