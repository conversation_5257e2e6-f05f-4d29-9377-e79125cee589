(()=>{"use strict";var e={653:(e,t,n)=>{n.d(t,{A:()=>u});n(8706),n(9432),n(6099);var r=n(2505),a=n.n(r),s=a().create({baseURL:"/api",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:1e4,withCredentials:!0});s.interceptors.request.use((function(e){console.log("[API] ".concat(e.method.toUpperCase()," ").concat(e.url));var t=JSON.parse(localStorage.getItem("user"));return t&&(e.headers["X-User-Id"]=t.id,e.headers["X-User-Email"]=t.email),e}),(function(e){return console.error("[API Request Error]",e),Promise.reject(e)})),s.interceptors.response.use((function(e){return console.log("[API] Response: ".concat(e.status)),e}),(function(e){return e.response?(console.error("[API] Error: ".concat(e.response.status),e.response.data),401===e.response.status&&(console.warn("未授权，可能需要重新登录"),localStorage.removeItem("user"),"/login"!==window.location.pathname&&(window.location.href="/login"))):console.error("[API] Network Error:",e.message),Promise.reject(e)}));var o={auth:{login:function(e){return s.post("/users/authenticate",e)},register:function(e){return s.post("/users",e)}},users:{getUser:function(e){return s.get("/users/".concat(e))},updateUser:function(e,t){return s.put("/users/".concat(e),t)}},images:{getAll:function(){return s.get("/images")},getOne:function(e){return s.get("/images/".concat(e))},create:function(e){return s.post("/images",e)},update:function(e,t){return s.put("/images/".concat(e),t)},delete:function(e){return s["delete"]("/images/".concat(e))}},tags:{getByImageId:function(e){return s.get("/tags/image/".concat(e))},create:function(e){return s.post("/tags",e)},update:function(e,t){return s.put("/tags/".concat(e),t)},delete:function(e){return s["delete"]("/tags/".concat(e))},getByUserId:function(e){return s.get("/tags/user/".concat(e))},saveAnnotatedImage:function(e){return console.log("API调用: 保存标注后的图像，ID:",e),s.post("/tags/save-image-after-annotation",{metadata_id:e})}},imagePairs:{getByMetadataId:function(e){return s.get("/image-pairs/metadata/".concat(e))},getOne:function(e){return s.get("/image-pairs/".concat(e))},create:function(e){return s.post("/image-pairs",e)},update:function(e,t){return s.put("/image-pairs/".concat(e),t)},delete:function(e){return s["delete"]("/image-pairs/".concat(e))},deleteByMetadataId:function(e){return s["delete"]("/image-pairs/metadata/".concat(e))}}};const u=o},1391:(e,t,n)=>{var r=n(3644),a=(n(3792),n(3362),n(9085),n(9391),n(5506),n(6099),n(3751)),s=n(641);function o(e,t,n,r,a,o){var u=(0,s.g2)("router-view"),c=(0,s.g2)("stagewise-toolbar");return(0,s.uX)(),(0,s.CE)(s.FK,null,[(0,s.bF)(u),a.isDevelopment?((0,s.uX)(),(0,s.Wv)(c,{key:0,config:a.stagewiseConfig},null,8,["config"])):(0,s.Q3)("",!0)],64)}var u=n(3421);const c={name:"App",components:{StagewiseToolbar:u.G},data:function(){return{isDevelopment:!1,stagewiseConfig:{plugins:[]}}}};var i=n(6262);const l=(0,i.A)(c,[["render",o]]),d=l;n(4423),n(5086),n(8111),n(3579),n(7764),n(2953);var p=n(5220),f=(n(4114),n(33)),m={class:"header-right"},g={class:"user-info"};function h(e,t,n,r,a,o){var u=(0,s.g2)("el-menu-item"),c=(0,s.g2)("el-menu"),i=(0,s.g2)("el-aside"),l=(0,s.g2)("el-avatar"),d=(0,s.g2)("el-dropdown-item"),p=(0,s.g2)("el-dropdown-menu"),h=(0,s.g2)("el-dropdown"),v=(0,s.g2)("el-header"),b=(0,s.g2)("router-view"),w=(0,s.g2)("el-main"),k=(0,s.g2)("el-container");return(0,s.uX)(),(0,s.Wv)(k,{class:"layout-container"},{default:(0,s.k6)((function(){return[(0,s.bF)(i,{width:"200px",class:"sidebar"},{default:(0,s.k6)((function(){return[t[8]||(t[8]=(0,s.Lk)("div",{class:"logo"},"医生标注平台",-1)),(0,s.bF)(c,{"default-active":"1",class:"sidebar-menu","background-color":"#001529","text-color":"#fff","active-text-color":"#409EFF"},{default:(0,s.k6)((function(){return[(0,s.bF)(u,{index:"1",onClick:t[0]||(t[0]=function(t){return e.$router.push("/dashboard")})},{default:(0,s.k6)((function(){return t[4]||(t[4]=[(0,s.Lk)("i",{class:"el-icon-s-home"},null,-1),(0,s.Lk)("span",null,"工作台",-1)])})),_:1,__:[4]}),(0,s.bF)(u,{index:"2",onClick:t[1]||(t[1]=function(t){return e.$router.push("/cases")})},{default:(0,s.k6)((function(){return t[5]||(t[5]=[(0,s.Lk)("i",{class:"el-icon-document"},null,-1),(0,s.Lk)("span",null,"病例标注",-1)])})),_:1,__:[5]}),(0,s.bF)(u,{index:"3",onClick:t[2]||(t[2]=function(t){return e.$router.push("/review")})},{default:(0,s.k6)((function(){return t[6]||(t[6]=[(0,s.Lk)("i",{class:"el-icon-s-check"},null,-1),(0,s.Lk)("span",null,"标注审核",-1)])})),_:1,__:[6]}),(0,s.bF)(u,{index:"4",onClick:t[3]||(t[3]=function(t){return e.$router.push("/users")})},{default:(0,s.k6)((function(){return t[7]||(t[7]=[(0,s.Lk)("i",{class:"el-icon-user"},null,-1),(0,s.Lk)("span",null,"用户管理",-1)])})),_:1,__:[7]})]})),_:1})]})),_:1,__:[8]}),(0,s.bF)(k,null,{default:(0,s.k6)((function(){return[(0,s.bF)(v,{height:"60px",class:"header"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",m,[(0,s.bF)(h,null,{dropdown:(0,s.k6)((function(){return[(0,s.bF)(p,null,{default:(0,s.k6)((function(){return[(0,s.bF)(d,{onClick:o.handleLogout},{default:(0,s.k6)((function(){return t[9]||(t[9]=[(0,s.eW)("退出登录")])})),_:1,__:[9]},8,["onClick"])]})),_:1})]})),default:(0,s.k6)((function(){return[(0,s.Lk)("span",g,[(0,s.bF)(l,{size:"small",icon:"el-icon-user"}),(0,s.eW)(" "+(0,f.v_)(a.username),1)])]})),_:1})])]})),_:1}),(0,s.bF)(w,null,{default:(0,s.k6)((function(){return[(0,s.bF)(b)]})),_:1})]})),_:1})]})),_:1})}const v={name:"MainLayout",data:function(){return{username:"标注医生"}},methods:{handleLogout:function(){this.$router.push("/login")}}},b=(0,i.A)(v,[["render",h],["__scopeId","data-v-20dbdc36"]]),w=b;var k={class:"dashboard"},A={class:"stat-item"},_={class:"stat-value"},I={class:"stat-item"},L={class:"stat-value"},y={class:"stat-item"},C={class:"stat-value"},x={class:"stat-item"},F={class:"stat-value"},E={class:"recent-tasks"},S={class:"table-header"};function N(e,t,n,r,a,o){var u=(0,s.g2)("el-card"),c=(0,s.g2)("el-col"),i=(0,s.g2)("el-row"),l=(0,s.g2)("el-button"),d=(0,s.g2)("el-table-column"),p=(0,s.g2)("el-tag"),m=(0,s.g2)("el-table");return(0,s.uX)(),(0,s.CE)("div",k,[(0,s.bF)(i,{gutter:20,class:"stat-cards"},{default:(0,s.k6)((function(){return[(0,s.bF)(c,{span:6},{default:(0,s.k6)((function(){return[(0,s.bF)(u,{shadow:"hover"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",A,[t[0]||(t[0]=(0,s.Lk)("div",{class:"stat-title"},"病例总数",-1)),(0,s.Lk)("div",_,(0,f.v_)(a.stats.total||"N/A"),1)])]})),_:1})]})),_:1}),(0,s.bF)(c,{span:6},{default:(0,s.k6)((function(){return[(0,s.bF)(u,{shadow:"hover"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",I,[t[1]||(t[1]=(0,s.Lk)("div",{class:"stat-title"},"已标注",-1)),(0,s.Lk)("div",L,(0,f.v_)(a.stats.annotated||"N/A"),1)])]})),_:1})]})),_:1}),(0,s.bF)(c,{span:6},{default:(0,s.k6)((function(){return[(0,s.bF)(u,{shadow:"hover"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",y,[t[2]||(t[2]=(0,s.Lk)("div",{class:"stat-title"},"待审核",-1)),(0,s.Lk)("div",C,(0,f.v_)(a.stats.pending||"N/A"),1)])]})),_:1})]})),_:1}),(0,s.bF)(c,{span:6},{default:(0,s.k6)((function(){return[(0,s.bF)(u,{shadow:"hover"},{default:(0,s.k6)((function(){return[(0,s.Lk)("div",x,[t[3]||(t[3]=(0,s.Lk)("div",{class:"stat-title"},"审核通过",-1)),(0,s.Lk)("div",F,(0,f.v_)(a.stats.approved||"N/A"),1)])]})),_:1})]})),_:1})]})),_:1}),(0,s.Lk)("div",E,[(0,s.Lk)("div",S,[t[5]||(t[5]=(0,s.Lk)("h2",null,"最近标注任务",-1)),(0,s.bF)(l,{type:"primary",onClick:o.handleNewAnnotation},{default:(0,s.k6)((function(){return t[4]||(t[4]=[(0,s.eW)("新建标注")])})),_:1,__:[4]},8,["onClick"])]),(0,s.bF)(m,{data:a.recentTasks,style:{width:"100%"}},{default:(0,s.k6)((function(){return[(0,s.bF)(d,{prop:"caseId",label:"病例编号",width:"180"}),(0,s.bF)(d,{prop:"patientInfo",label:"患者信息",width:"180"}),(0,s.bF)(d,{prop:"department",label:"部位"}),(0,s.bF)(d,{prop:"type",label:"类型"}),(0,s.bF)(d,{prop:"status",label:"状态"},{default:(0,s.k6)((function(e){return[(0,s.bF)(p,{type:o.getStatusType(e.row.status)},{default:(0,s.k6)((function(){return[(0,s.eW)((0,f.v_)(e.row.status),1)]})),_:2},1032,["type"])]})),_:1}),(0,s.bF)(d,{prop:"createTime",label:"创建时间"}),(0,s.bF)(d,{label:"操作",width:"150"},{default:(0,s.k6)((function(e){return[(0,s.bF)(l,{type:"text",size:"small",onClick:function(t){return o.handleEdit(e.row)}},{default:(0,s.k6)((function(){return t[6]||(t[6]=[(0,s.eW)(" 编辑 ")])})),_:2,__:[6]},1032,["onClick"]),(0,s.bF)(l,{type:"text",size:"small",onClick:function(t){return o.handleView(e.row)}},{default:(0,s.k6)((function(){return t[7]||(t[7]=[(0,s.eW)(" 查看 ")])})),_:2,__:[7]},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])])])}var O=n(4048),j=n(388);const P={name:"Dashboard",data:function(){return{stats:{total:"N/A",annotated:"N/A",pending:"N/A",approved:"N/A"},recentTasks:[]}},methods:{handleNewAnnotation:function(){this.$router.push("/cases/new")},handleEdit:function(e){this.$router.push("/cases/edit/".concat(e.id))},handleView:function(e){this.$router.push("/cases/view/".concat(e.id))},getStatusType:function(e){var t={待标注:"info",已标注:"success",审核中:"warning",已通过:"success",已驳回:"danger"};return t[e]||"info"},fetchDashboardData:function(){return(0,j.A)((0,O.A)().mark((function e(){return(0,O.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case 1:case"end":return e.stop()}}),e)})))()}},created:function(){this.fetchDashboardData()}},U=(0,i.A)(P,[["render",N],["__scopeId","data-v-0d4c7975"]]),T=U;var q=n(6278),D=(n(739),n(3110),n(9432),n(653)),B={user:JSON.parse(localStorage.getItem("user"))||null,isAuthenticated:!!localStorage.getItem("user")},$={getUser:function(e){return e.user}},W={setUser:function(e,t){e.user=t,e.isAuthenticated=!!t}},M={login:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r,a,s;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,n.prev=1,console.log("登录请求:",t),n.next=5,D.A.auth.login(t);case 5:return a=n.sent,s=a.data,console.log("登录成功，用户数据:",s),localStorage.setItem("user",JSON.stringify(s)),r("setUser",s),n.abrupt("return",s);case 13:if(n.prev=13,n.t0=n["catch"](1),console.error("登录错误详情:",n.t0),!n.t0.response){n.next=21;break}throw console.error("服务器响应:",n.t0.response.status,n.t0.response.data),n.t0.response.data||"登录失败，服务器错误";case 21:if(!n.t0.request){n.next=26;break}throw console.error("未收到响应:",n.t0.request),"登录失败，无法连接到服务器";case 26:throw console.error("请求配置错误:",n.t0.message),n.t0.message||"登录失败，请求配置错误";case 28:case"end":return n.stop()}}),n,null,[[1,13]])})))()},register:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.commit,n.prev=1,n.next=4,D.A.auth.register(t);case 4:return r=n.sent,n.abrupt("return",r.data);case 8:throw n.prev=8,n.t0=n["catch"](1),n.t0.response?n.t0.response.data:n.t0.message;case 11:case"end":return n.stop()}}),n,null,[[1,8]])})))()},logout:function(e){var t=e.commit;localStorage.removeItem("user"),t("setUser",null)},fetchUserProfile:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.commit,n.prev=1,n.next=4,D.A.users.getUser(t);case 4:return r=n.sent,n.abrupt("return",r.data);case 8:throw n.prev=8,n.t0=n["catch"](1),n.t0.response?n.t0.response.data:n.t0.message;case 11:case"end":return n.stop()}}),n,null,[[1,8]])})))()},updateUserProfile:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r,a,s,o;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,a=e.state,n.prev=1,n.next=4,D.A.users.updateUser(a.user.id,t);case 4:return s=n.sent,o=s.data,localStorage.setItem("user",JSON.stringify(o)),r("setUser",o),n.abrupt("return",o);case 11:throw n.prev=11,n.t0=n["catch"](1),n.t0.response?n.t0.response.data:n.t0.message;case 14:case"end":return n.stop()}}),n,null,[[1,11]])})))()}};const X={state:B,getters:$,mutations:W,actions:M};n(6918),n(8706),n(2008),n(8980),n(4554),n(3609),n(2489),n(2207),n(5815),n(4979),n(9739);var J=n(2505),R=n.n(J),z="http://localhost:8080/medical/api",V=function(){var e=JSON.parse(localStorage.getItem("user"));return e?{Authorization:"Basic ".concat(btoa("".concat(e.email,":").concat(e.password)))}:{}},K={images:[],currentImage:null,stats:{totalCount:0,draftCount:0,submittedCount:0,approvedCount:0,rejectedCount:0},loading:!1,error:null},G={getAllImages:function(e){return e.images},getCurrentImage:function(e){return e.currentImage},getStats:function(e){return e.stats},isLoading:function(e){return e.loading},getError:function(e){return e.error}},H={setImages:function(e,t){e.images=t},setCurrentImage:function(e,t){e.currentImage=t},setStats:function(e,t){e.stats=t},setLoading:function(e,t){e.loading=t},setError:function(e,t){e.error=t},addImage:function(e,t){e.images.unshift(t)},updateImage:function(e,t){var n=e.images.findIndex((function(e){return e.id===t.id}));-1!==n&&e.images.splice(n,1,t),e.currentImage&&e.currentImage.id===t.id&&(e.currentImage=t)},removeImage:function(e,t){e.images=e.images.filter((function(e){return e.id!==t})),e.currentImage&&e.currentImage.id===t&&(e.currentImage=null)}},Q={fetchImages:function(e){return(0,j.A)((0,O.A)().mark((function t(){var n,r;return(0,O.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.commit,n("setLoading",!0),n("setError",null),t.prev=3,t.next=6,R().get("".concat(z,"/images"),{headers:V()});case 6:return r=t.sent,n("setImages",r.data),t.abrupt("return",r.data);case 11:throw t.prev=11,t.t0=t["catch"](3),n("setError",t.t0.response?t.t0.response.data:t.t0.message),t.t0;case 15:return t.prev=15,n("setLoading",!1),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[3,11,15,18]])})))()},fetchImagesByStatus:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r,a;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,R().get("".concat(z,"/images/status/").concat(t),{headers:V()});case 6:return a=n.sent,r("setImages",a.data),n.abrupt("return",a.data);case 11:throw n.prev=11,n.t0=n["catch"](3),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 15:return n.prev=15,r("setLoading",!1),n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[3,11,15,18]])})))()},fetchImageById:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r,a;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,R().get("".concat(z,"/images/").concat(t),{headers:V()});case 6:return a=n.sent,r("setCurrentImage",a.data),n.abrupt("return",a.data);case 11:throw n.prev=11,n.t0=n["catch"](3),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 15:return n.prev=15,r("setLoading",!1),n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[3,11,15,18]])})))()},createImage:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r,a;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,R().post("".concat(z,"/images"),t,{headers:V()});case 6:return a=n.sent,r("addImage",a.data),n.abrupt("return",a.data);case 11:throw n.prev=11,n.t0=n["catch"](3),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 15:return n.prev=15,r("setLoading",!1),n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[3,11,15,18]])})))()},updateImage:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r,a,s,o;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,a=t.id,s=t.imageData,r("setLoading",!0),r("setError",null),n.prev=4,n.next=7,R().put("".concat(z,"/images/").concat(a),s,{headers:V()});case 7:return o=n.sent,r("updateImage",o.data),n.abrupt("return",o.data);case 12:throw n.prev=12,n.t0=n["catch"](4),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 16:return n.prev=16,r("setLoading",!1),n.finish(16);case 19:case"end":return n.stop()}}),n,null,[[4,12,16,19]])})))()},updateImageStatus:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r,a,s,o,u,c;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,a=t.id,s=t.status,o=t.reviewerId,u=t.reviewNotes,r("setLoading",!0),r("setError",null),n.prev=4,n.next=7,R().put("".concat(z,"/images/").concat(a,"/status"),null,{params:{status:s,reviewerId:o,reviewNotes:u},headers:V()});case 7:return n.next=9,R().get("".concat(z,"/images/").concat(a),{headers:V()});case 9:return c=n.sent,r("updateImage",c.data),n.abrupt("return",c.data);case 14:throw n.prev=14,n.t0=n["catch"](4),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 18:return n.prev=18,r("setLoading",!1),n.finish(18);case 21:case"end":return n.stop()}}),n,null,[[4,14,18,21]])})))()},deleteImage:function(e,t){return(0,j.A)((0,O.A)().mark((function n(){var r;return(0,O.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.commit,r("setLoading",!0),r("setError",null),n.prev=3,n.next=6,R()["delete"]("".concat(z,"/images/").concat(t),{headers:V()});case 6:return r("removeImage",t),n.abrupt("return",!0);case 10:throw n.prev=10,n.t0=n["catch"](3),r("setError",n.t0.response?n.t0.response.data:n.t0.message),n.t0;case 14:return n.prev=14,r("setLoading",!1),n.finish(14);case 17:case"end":return n.stop()}}),n,null,[[3,10,14,17]])})))()},fetchStats:function(e){return(0,j.A)((0,O.A)().mark((function t(){var n,r;return(0,O.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.commit,n("setLoading",!0),n("setError",null),t.prev=3,t.next=6,R().get("".concat(z,"/stats"),{headers:V()});case 6:return r=t.sent,n("setStats",r.data),t.abrupt("return",r.data);case 11:throw t.prev=11,t.t0=t["catch"](3),n("setError",t.t0.response?t.t0.response.data:t.t0.message),t.t0;case 15:return t.prev=15,n("setLoading",!1),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[3,11,15,18]])})))()}};const Y={state:K,getters:G,mutations:H,actions:Q},Z=(0,q.y$)({state:{},getters:{isAuthenticated:function(e){return e.auth.isAuthenticated},isAdmin:function(e){return e.auth.user&&"ADMIN"===e.auth.user.role}},mutations:{},actions:{},modules:{auth:X,images:Y}});var ee=[{path:"/",component:w,children:[{path:"",redirect:"/dashboard"},{path:"/dashboard",name:"Dashboard",component:T},{path:"/cases",name:"Cases",component:function(){return n.e(356).then(n.bind(n,5356))}},{path:"/cases/new",name:"NewCase",component:function(){return n.e(624).then(n.bind(n,8624))}},{path:"/cases/edit/:id",name:"EditCase",component:function(){return n.e(624).then(n.bind(n,8624))}},{path:"/cases/view/:id",name:"ViewCase",component:function(){return n.e(33).then(n.bind(n,9033))}},{path:"/cases/form",name:"CaseDetailForm",component:function(){return n.e(143).then(n.bind(n,3143))}},{path:"/case-structured-form",name:"CaseStructuredForm",component:function(){return n.e(280).then(n.bind(n,3280))}},{path:"/review",name:"Review",component:function(){return n.e(883).then(n.bind(n,2883))}},{path:"/users",name:"Users",component:function(){return n.e(176).then(n.bind(n,176))}}]},{path:"/login",name:"Login",component:function(){return n.e(533).then(n.bind(n,533))}},{path:"/register",name:"Register",component:function(){return n.e(272).then(n.bind(n,4272))}},{path:"/images",name:"ImageList",component:function(){return n.e(527).then(n.bind(n,6527))},meta:{requiresAuth:!0}},{path:"/images/:id",name:"ImageDetail",component:function(){return n.e(35).then(n.bind(n,7035))},meta:{requiresAuth:!0}},{path:"/images/upload",name:"ImageUpload",component:function(){return n.e(587).then(n.bind(n,1587))},meta:{requiresAuth:!0}},{path:"/admin",name:"Admin",component:function(){return n.e(236).then(n.bind(n,5236))},meta:{requiresAuth:!0,requiresAdmin:!0}}],te=(0,p.aE)({history:(0,p.LA)("/"),routes:ee});te.beforeEach((function(e,t,n){var r=["/login"],a=!r.includes(e.path),s=localStorage.getItem("user");if(a&&!s)return n("/login");var o=Z.getters.isAuthenticated,u=e.matched.some((function(e){return e.meta.requiresAuth})),c=e.matched.some((function(e){return e.meta.requiresAdmin}));u&&!o?n("/login"):c&&!Z.getters.isAdmin?n("/"):n()}));const ne=te;var re=n(116),ae=n(8548),se=(n(4188),n(7487));n(8736);R().defaults.withCredentials=!0,R().defaults.headers.common["Content-Type"]="application/json",R().defaults.headers.common["Accept"]="application/json",R().defaults.timeout=1e4,R().interceptors.request.use((function(e){return console.log("发送请求:",e.url),e}),(function(e){return console.error("请求错误:",e),Promise.reject(e)})),R().interceptors.response.use((function(e){return console.log("收到响应:",e.status),e}),(function(e){return console.error("响应错误:",e),e.response?console.error("服务器响应:",e.response.status,e.response.data):e.request?console.error("未收到响应:",e.request):console.error("请求配置错误:",e.message),Promise.reject(e)}));for(var oe=(0,a.Ef)(d),ue=0,ce=Object.entries(ae);ue<ce.length;ue++){var ie=(0,r.A)(ce[ue],2),le=ie[0],de=ie[1];oe.component(le,de)}oe.config.globalProperties.$axios=R(),oe.use(Z),oe.use(ne),oe.use(re.A,{locale:se.A}),oe.mount("#app")}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var s=t[r]={exports:{}};return e[r].call(s.exports,s,s.exports,n),s.exports}n.m=e,(()=>{var e=[];n.O=(t,r,a,s)=>{if(!r){var o=1/0;for(l=0;l<e.length;l++){for(var[r,a,s]=e[l],u=!0,c=0;c<r.length;c++)(!1&s||o>=s)&&Object.keys(n.O).every((e=>n.O[e](r[c])))?r.splice(c--,1):(u=!1,s<o&&(o=s));if(u){e.splice(l--,1);var i=a();void 0!==i&&(t=i)}}return t}s=s||0;for(var l=e.length;l>0&&e[l-1][2]>s;l--)e[l]=e[l-1];e[l]=[r,a,s]}})(),(()=>{n.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return n.d(t,{a:t}),t}})(),(()=>{n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}})(),(()=>{n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[]))})(),(()=>{n.u=e=>"js/"+e+"."+{33:"919fe087",35:"a1785f1b",143:"53d24719",176:"5682885e",236:"5df39630",272:"d7ae3035",280:"c0836fb1",356:"94572d5e",527:"0892fff3",533:"41254223",587:"796a232e",624:"5e783a1f",883:"baa806e9"}[e]+".js"})(),(()=>{n.miniCssF=e=>"css/"+e+"."+{33:"bac590d2",35:"da550125",143:"8b2caf9e",176:"c5848730",236:"f6967544",272:"0d8c01b5",280:"f79a1a12",356:"d4acbfbe",527:"5b2387da",533:"bc07e690",587:"98e3d387",624:"4cb4938b",883:"efce384b"}[e]+".css"})(),(()=>{n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="medical-annotation-frontend:";n.l=(r,a,s,o)=>{if(e[r])e[r].push(a);else{var u,c;if(void 0!==s)for(var i=document.getElementsByTagName("script"),l=0;l<i.length;l++){var d=i[l];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+s){u=d;break}}u||(c=!0,u=document.createElement("script"),u.charset="utf-8",u.timeout=120,n.nc&&u.setAttribute("nonce",n.nc),u.setAttribute("data-webpack",t+s),u.src=r),e[r]=[a];var p=(t,n)=>{u.onerror=u.onload=null,clearTimeout(f);var a=e[r];if(delete e[r],u.parentNode&&u.parentNode.removeChild(u),a&&a.forEach((e=>e(n))),t)return t(n)},f=setTimeout(p.bind(null,void 0,{type:"timeout",target:u}),12e4);u.onerror=p.bind(null,u.onerror),u.onload=p.bind(null,u.onload),c&&document.head.appendChild(u)}}})(),(()=>{n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{n.p="/"})(),(()=>{if("undefined"!==typeof document){var e=(e,t,r,a,s)=>{var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",n.nc&&(o.nonce=n.nc);var u=n=>{if(o.onerror=o.onload=null,"load"===n.type)a();else{var r=n&&n.type,u=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+r+": "+u+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=r,c.request=u,o.parentNode&&o.parentNode.removeChild(o),s(c)}};return o.onerror=o.onload=u,o.href=t,r?r.parentNode.insertBefore(o,r.nextSibling):document.head.appendChild(o),o},t=(e,t)=>{for(var n=document.getElementsByTagName("link"),r=0;r<n.length;r++){var a=n[r],s=a.getAttribute("data-href")||a.getAttribute("href");if("stylesheet"===a.rel&&(s===e||s===t))return a}var o=document.getElementsByTagName("style");for(r=0;r<o.length;r++){a=o[r],s=a.getAttribute("data-href");if(s===e||s===t)return a}},r=r=>new Promise(((a,s)=>{var o=n.miniCssF(r),u=n.p+o;if(t(o,u))return a();e(r,u,null,a,s)})),a={524:0};n.f.miniCss=(e,t)=>{var n={33:1,35:1,143:1,176:1,236:1,272:1,280:1,356:1,527:1,533:1,587:1,624:1,883:1};a[e]?t.push(a[e]):0!==a[e]&&n[e]&&t.push(a[e]=r(e).then((()=>{a[e]=0}),(t=>{throw delete a[e],t})))}}})(),(()=>{var e={524:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var s=new Promise(((n,r)=>a=e[t]=[n,r]));r.push(a[2]=s);var o=n.p+n.u(t),u=new Error,c=r=>{if(n.o(e,t)&&(a=e[t],0!==a&&(e[t]=void 0),a)){var s=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;u.message="Loading chunk "+t+" failed.\n("+s+": "+o+")",u.name="ChunkLoadError",u.type=s,u.request=o,a[1](u)}};n.l(o,c,"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,r)=>{var a,s,[o,u,c]=r,i=0;if(o.some((t=>0!==e[t]))){for(a in u)n.o(u,a)&&(n.m[a]=u[a]);if(c)var l=c(n)}for(t&&t(r);i<o.length;i++)s=o[i],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return n.O(l)},r=self["webpackChunkmedical_annotation_frontend"]=self["webpackChunkmedical_annotation_frontend"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r=n.O(void 0,[504],(()=>n(1391)));r=n.O(r)})();