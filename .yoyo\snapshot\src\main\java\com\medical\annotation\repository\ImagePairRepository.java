package com.medical.annotation.repository;

import com.medical.annotation.model.ImagePair;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ImagePairRepository extends JpaRepository<ImagePair, Long> {
    List<ImagePair> findByMetadataId(Long metadataId);
    void deleteByMetadataId(Long metadataId);
    List<ImagePair> findByImageOnePathContaining(String pathFragment);
    
    // 兼容Integer类型的metadataId查询
    default List<ImagePair> findByMetadataId(Integer metadataId) {
        return findByMetadataId(metadataId.longValue());
    }
    
    // 兼容Integer类型的metadataId删除
    default void deleteByMetadataId(Integer metadataId) {
        deleteByMetadataId(metadataId.longValue());
    }
} 