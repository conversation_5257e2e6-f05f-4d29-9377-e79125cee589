/**
 * 图像路径处理工具
 * 用于统一处理图像URL，解决路径不一致、中文编码等问题
 */

import { API_BASE_URL, API_CONTEXT_PATH, isLocalhost } from '../config/api.config';
import { storageUtils } from './storeHelpers';
import { addTimestamp } from './apiHelpers';

// 图像大小限制
const MAX_IMAGE_WIDTH = 700;
const MAX_IMAGE_HEIGHT = 700;

/**
 * 获取图像URL，添加防缓存参数并处理不同的路径格式
 * @param {string} url 原始图像URL
 * @returns {string} 处理后的URL
 */
export function getImageUrl(url) {
  if (!url) return '';
  
  console.log('[ImageHelper] 处理图像路径:', url);
  
  // 处理文件系统路径
  if (/^[a-zA-Z]:\\/.test(url)) {
    console.log('[ImageHelper] 检测到文件系统路径');
    const encodedPath = encodeURIComponent(url);
    
    // 使用当前域名而非硬编码地址
    const baseUrl = isLocalhost ? `${API_BASE_URL}` : '';
    return addTimestamp(`${baseUrl}/medical/image/system-path?path=${encodedPath}`);
  }
  
  // 如果URL已经是完整的HTTP(S)地址，直接使用
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return addTimestamp(url);
  }
  
  // 如果URL是data:开头的数据URI，直接返回
  if (url.startsWith('data:')) {
    return url;
  }
  
  // 处理相对路径
  let finalUrl = url;
  
  if (url.startsWith('/medical')) {
    console.log('[ImageHelper] 检测到相对路径，添加后端服务器地址');
    // 根据环境使用适当的基础URL
    finalUrl = isLocalhost ? `${API_BASE_URL}${url}` : url;
  } else if (url.startsWith('/')) {
    console.log('[ImageHelper] 检测到其他相对路径，添加后端服务器地址和上下文路径');
    // 根据环境使用适当的基础URL
    finalUrl = isLocalhost ? `${API_BASE_URL}${API_CONTEXT_PATH}${url}` : `${API_CONTEXT_PATH}${url}`;
  }
  
  console.log('[ImageHelper] 处理后的URL:', finalUrl);
  return addTimestamp(finalUrl);
}

/**
 * 从图像路径中提取文件名
 * @param {string} path - 图像路径
 * @returns {string} 文件名
 */
export function getImageFilename(path) {
  if (!path) return '';
  
  const parts = path.split('/');
  return parts[parts.length - 1];
}

/**
 * 判断是否是图像文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否是图像文件
 */
export function isImageFile(filename) {
  if (!filename) return false;
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  const lowerFilename = filename.toLowerCase();
  
  return imageExtensions.some(ext => lowerFilename.endsWith(ext));
}

/**
 * 调整图像大小，确保不超过最大宽高限制
 * @param {HTMLImageElement|File} imageSource - 图像元素或文件对象
 * @param {Function} callback - 回调函数，接收处理后的图像数据(DataURL或Blob)和尺寸信息
 * @param {Object} options - 选项
 * @param {number} options.maxWidth - 最大宽度，默认700px
 * @param {number} options.maxHeight - 最大高度，默认700px
 * @param {string} options.outputFormat - 输出格式，'dataUrl'或'blob'，默认'dataUrl'
 * @param {string} options.imageType - 图像类型，默认'image/jpeg'
 * @param {number} options.quality - 压缩质量，0-1之间，默认0.9
 */
export function resizeImage(imageSource, callback, options = {}) {
  const settings = {
    maxWidth: options.maxWidth || MAX_IMAGE_WIDTH,
    maxHeight: options.maxHeight || MAX_IMAGE_HEIGHT,
    outputFormat: options.outputFormat || 'dataUrl',
    imageType: options.imageType || 'image/jpeg',
    quality: options.quality || 0.9
  };
  
  // 处理File对象
  if (imageSource instanceof File) {
    const reader = new FileReader();
    reader.onload = function(e) {
      const img = new Image();
      img.onload = function() {
        processImageResize(img, settings, callback);
      };
      img.src = e.target.result;
    };
    reader.readAsDataURL(imageSource);
    return;
  }
  
  // 处理Image对象
  if (imageSource instanceof HTMLImageElement) {
    if (imageSource.complete) {
      processImageResize(imageSource, settings, callback);
    } else {
      imageSource.onload = function() {
        processImageResize(imageSource, settings, callback);
      };
    }
    return;
  }
  
  // 处理其他情况（字符串URL或DataURL）
  if (typeof imageSource === 'string') {
    const img = new Image();
    img.onload = function() {
      processImageResize(img, settings, callback);
    };
    img.src = imageSource;
    return;
  }
  
  // 不支持的类型
  console.error('不支持的图像源类型:', imageSource);
  callback(null, { error: '不支持的图像源类型' });
}

/**
 * 处理图像大小调整的核心逻辑
 * @private
 */
function processImageResize(img, settings, callback) {
  // 获取原始尺寸
  const originalWidth = img.naturalWidth || img.width;
  const originalHeight = img.naturalHeight || img.height;
  
  // 计算调整后的尺寸，保持宽高比
  let newWidth = originalWidth;
  let newHeight = originalHeight;
  
  // 如果图像超出最大限制，按比例缩小
  if (originalWidth > settings.maxWidth || originalHeight > settings.maxHeight) {
    const widthRatio = settings.maxWidth / originalWidth;
    const heightRatio = settings.maxHeight / originalHeight;
    const ratio = Math.min(widthRatio, heightRatio);
    
    newWidth = Math.floor(originalWidth * ratio);
    newHeight = Math.floor(originalHeight * ratio);
  }
  
  // 创建Canvas绘制调整后的图像
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = newWidth;
  canvas.height = newHeight;
  
  // 绘制图像
  ctx.drawImage(img, 0, 0, newWidth, newHeight);
  
  // 输出数据
  const sizeInfo = {
    originalWidth,
    originalHeight,
    newWidth,
    newHeight,
    resized: (originalWidth !== newWidth || originalHeight !== newHeight)
  };
  
  // 根据需要的输出格式返回结果
  if (settings.outputFormat === 'blob') {
    canvas.toBlob(
      (blob) => callback(blob, sizeInfo),
      settings.imageType,
      settings.quality
    );
  } else {
    // 默认返回DataURL
    const dataUrl = canvas.toDataURL(settings.imageType, settings.quality);
    callback(dataUrl, sizeInfo);
  }
}

/**
 * 转换相对坐标到绝对坐标
 * @param {Object} annotation 标注数据
 * @param {number} imageWidth 图像宽度
 * @param {number} imageHeight 图像高度
 * @returns {Object} 转换后的标注数据
 */
export function convertToAbsoluteCoordinates(annotation, imageWidth, imageHeight) {
  // 判断是否是相对坐标（0-1范围内）
  const isRelativeCoordinates = 
    annotation.x <= 1 && annotation.x >= 0 && 
    annotation.y <= 1 && annotation.y >= 0 && 
    annotation.width <= 1 && annotation.width >= 0 && 
    annotation.height <= 1 && annotation.height >= 0;
  
  if (!isRelativeCoordinates) {
    return annotation; // 已经是绝对坐标，直接返回
  }
  
  // 计算绝对坐标
  const x = Math.round(annotation.x * imageWidth);
  const y = Math.round(annotation.y * imageHeight);
  const width = Math.round(annotation.width * imageWidth);
  const height = Math.round(annotation.height * imageHeight);
  
  return {
    ...annotation,
    x,
    y,
    width,
    height,
    // 保存原始的相对坐标
    normalizedX: annotation.x,
    normalizedY: annotation.y,
    normalizedWidth: annotation.width,
    normalizedHeight: annotation.height
  };
}

/**
 * 创建用户ID一致性处理工具
 */
export const UserIdConsistencyFixer = {
  /**
   * 获取一致的用户ID，优先使用customId
   * @param {Object} user 用户对象
   * @returns {string|number} 用户ID
   */
  getConsistentUserId(user) {
    if (!user) return null;
    return user.customId || user.id || null;
  },
  
  /**
   * 获取用户对象，确保包含一致的ID
   * @returns {Object} 用户对象
   */
  getCurrentUser() {
    const user = storageUtils.getFromStorage('user', {});
      return {
        ...user,
        consistentId: this.getConsistentUserId(user)
      };
  }
};

export default {
  getImageUrl,
  getImageFilename,
  isImageFile,
  resizeImage,
  MAX_IMAGE_WIDTH,
  MAX_IMAGE_HEIGHT
}; 